#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/23 10:47
# <AUTHOR> wangf10
# @File     : keyword_result_service.py
import json
import re
import hashlib
from collections import defaultdict

######################################################
from base import repository
from dmplib.hug import debugger
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
# from components.global_utils import DecimalEncoder
from dmplib.redis import ObjectEncoder
from keywords.models import (
    KeywordModel,
)
from base.enums import (
    KeywordType,
    KeywordDataType,
)
from keywords.repositories import keyword_repositories
from keywords.utils.keyword_utils import get_data_by_keyword
from data_source.services.data_source_client import query_data_of_sql_by_data_source
from json import dumps
from base.dmp_constant import KEYWORD_FLAG

_debugger = debugger.Debug(__name__)


# KEYWORD_FLAG = '"@{keyword_value}"'


def get_keyword_result_by_id(keyword_id: str):
    """
    获取关键字结果
    @param keyword_id:
    @return:
    """
    if not keyword_id:
        raise UserError(message='关键字id不能为空')
    data = repository.get_one(KeywordModel.__table__, {"id": keyword_id})
    if not data:
        return None
    model = KeywordModel(**data)
    return analysis_keyword(model)


def analysis_keyword(model: KeywordModel, keyword_list: list = None):
    """
    关键字解析
    @param model:
    @param keyword_list:
    @return:
    """
    match = r'(?<=\[key:)[^key:\]]+(?=\])'
    keyword_list = [] if not keyword_list else keyword_list
    # 判断关键字的类型
    if model.keyword_type == KeywordType.Sql.value:
        # SQL关键字则获取SQL，并判断是否SQL中含有关键字
        match_list = list(set(re.findall(match, model.sql_text)))
        if not match_list:
            return get_sql_keyword_result(model)
        else:
            keyword_list.append(model.keyword_name)
            keyword_list = list(set(keyword_list))
            data = keyword_repositories.get_keyword_by_name_list(match_list, model.datasource_id)
            data = adapt_keyword_data(data)
            for item in data:
                child_model = KeywordModel(**item)
                if child_model.keyword_name in keyword_list:
                    raise UserError(message='关键字定义中出现重复嵌套的关键字，请修改后重试')
                result = analysis_keyword(child_model, keyword_list)
                result = keyword_result_format(result, model)
                replace_str = """[key:{keyword_name}]""".format(keyword_name=item.get('keyword_name'))
                model.sql_text = model.sql_text.replace(replace_str, result)
            return get_sql_keyword_result(model)
    else:
        # 程序解析类型关键字
        return get_sql_keyword_result(model)


def adapt_keyword_data(data: list) -> list:
    """
    场景：
    关键字A定义: select [key:B]
    关键字B定义: select [key:C]
    使用A时，当关键字B出现重复两次及以上时候就会报错，关键字定义中出现重复嵌套的关键字
    这里当重复的关键字只选择一个出来，优先选择用户定义的关键字，其次再选系统定义的关键字
    """
    if not data:
        return []
    name_keyword_map = defaultdict(list)
    result = []
    for keyword in data:
        name_keyword_map[keyword.get('name', '')].append(keyword)
    for name, keywords in name_keyword_map.items():
        if len(keywords) > 1:
            sorted_keywords = list(sorted(keywords, key=lambda x: x.get('is_system', 0)))
            result.append(sorted_keywords[0])
            _debugger.log({'选择取的关键字': sorted_keywords[0], '同名的关键字': sorted_keywords})
        elif len(keywords) == 1:
            result.append(keywords[0])
    return result


def get_sql_keyword_result(model: KeywordModel):
    """
    获取关键字的数据内容
    @param model:
    @return:
    """
    if model.keyword_type == KeywordType.Sql.value and model.sql_text:
        cache = RedisCache()
        key = '''{}{}'''.format(model.datasource_id, model.sql_text)
        result_key = hashlib.md5(key.encode("utf-8")).hexdigest()
        cache_key = """keyword_result:{}""".format(result_key)
        result = cache.get_data(cache_key)
        if result is None:
            result = query_data_of_sql_by_data_source(model.datasource_id, model.sql_text)
            cache.set(cache_key, json.dumps(result, ensure_ascii=False, cls=ObjectEncoder), 300)
    elif model.keyword_type == KeywordType.Sql.value and not model.sql_text:
        raise UserError(message="关键字SQL定义不能为空")
    else:
        # 程序解析类型关键字
        result = [get_data_by_keyword(model.keyword_name)]
    return result


def keyword_result_format(result: list, model: KeywordModel):
    """
    关键字结果格式化
    @param result:
    @param model
    @return:
    """
    if not result or result == [None]:
        return 'Null'
    if len(result) > 1 and model.data_type == KeywordDataType.Number.value:
        data = ','.join(result)
    elif len(result) > 1 and model.data_type != KeywordDataType.Number.value:
        data = "'" + "','".join(result) + "'"
    else:
        data = result[0] if model.data_type == KeywordDataType.Number.value else "'{}'".format(result[0])
    return data


def get_keyword_result_format_for_var(var: dict):  # NOSONAR
    keyword_list = var.get('keyword_list', list())
    keyword_result_list = list()
    if var.get('default_value_type', 2) == 1 and keyword_list:
        for item in keyword_list:
            keyword_result_list += get_keyword_result_by_id(item.get('keyword_id'))
    if keyword_result_list and len(keyword_result_list) > 1:
        return dumps(keyword_result_list, cls=ObjectEncoder)
    elif keyword_result_list and len(keyword_result_list) == 1:
        if var.get('value_type') == 2:
            if isinstance(keyword_result_list[0], str):
                return keyword_result_list[0]
            else:
                return dumps(keyword_result_list[0], cls=ObjectEncoder)
        else:
            return dumps(keyword_result_list, cls=ObjectEncoder)
    else:
        return var.get('default_value')


def get_keyword_result_format_special_flag_for_var(var: dict):
    "b85ea54f-28dc-11ed-985f-4189017ae4a9"
    if var.get('default_value_type', 2) == 1:
        return KEYWORD_FLAG
    else:
        return var.get('default_value')


def get_formatted_keyword_result_by_id(keyword_id):
    result = get_keyword_result_by_id(keyword_id)
    return json.loads(json.dumps(result, cls=ObjectEncoder))
