#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/17.
"""
import json
import re
import xmltodict
from urllib.parse import urlparse

from base.enums import DataSourceType, MysoftNewERPConfigType, MysoftNewERPDataBaseType, \
    MysoftNewERPDataFromType, MysoftNewERPCloudType
from base.models import BaseModel, QueryBaseModel
from dmplib.utils.crypt import AESCrypt
from dmplib.utils.errors import UserError
from dmplib.saas.project import get_mysoft_erp

from typing import Dict, List, Tuple, Union


class DataSourceModel(BaseModel):
    __slots__ = [
        'id',
        'name',
        'code',
        'description',
        'type',
        'conn_str',
        'is_buildin',
        'icon',
        'inspect_api',
        'config_type',
        'config_xml',
        'app_code',
        'db_type',
        'app_level_code',
        'data_source_permission_type',
        'data_from',
        'third_party_id',
    ]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.name = None
        self.code = None
        self.description = None
        self.type = None
        self.conn_str = None
        self.is_buildin = 0
        self.icon = None
        self.inspect_api = ""
        self.user_source_id = ""
        # 以下都是MysoftNewERP类型数据源专有属性
        self.config_type = None
        self.config_xml = ""
        self.app_code = ""
        self.db_type = None
        self.app_level_code = ""
        self.data_source_permission_type = 1
        self.data_from = ""
        self.third_party_id = None

        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 254}))
        rules.append(('code', 'string', {'max': 254}))
        rules.append(('description', 'string', {'max': 1000, 'required': False}))
        rules.append(('type', 'in_range', {'range': [e.value for e in DataSourceType.__members__.values()]}))
        rules.append(('conn_str',))
        # 只允许添加或修改内置数据源
        rules.append(('is_buildin', 'in_range', {'range': [0]}))
        return rules

    def conn_str_to_model(self) -> None:
        if not isinstance(self.conn_str, dict):
            return
        if self.type == DataSourceType.Mysql.value:
            model = MysqlConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.ODPS.value:
            model = ODPSConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.SaaS.value:
            model = SaaSConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.MysoftERP.value:
            if self.conn_str.get('is_config_center', 0) == 1:
                model = MysoftERPConfigCenterModel(**self.conn_str)
            elif self.conn_str.get('db_type', None):
                model = MysoftERPDbConnStrModel(**self.conn_str)
            else:
                model = MysoftERPConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.API.value:
            model = APIConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.DataHub.value:
            model = DataHubConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.PostgreSQL.value:
            model = PostgreSQLConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.HighData.value:
            model = HighDataConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.ADS.value:
            model = ADSDataConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.MysoftNewERP.value:
            model = MysoftNewERPConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.Presto.value:
            model = PrestoConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.MSSQL.value:
            model = MsSqlConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.MysoftShuXin.value:
            model = MysoftShuXinConnStrModel(**self.conn_str)
        elif self.type == DataSourceType.MysoftShuXin15.value:
            model = MysoftShuXin15ConnStrModel(**self.conn_str)
        else:
            raise UserError(message='未知数据源类型')
        model.validate()
        self.conn_str = model


class ConnStrModel(BaseModel):
    def encrypt(self):
        """
        将敏感信息加密
        :return:
        """

    def decrypt(self):
        """
        解密敏感信息
        :return:
        """

    def clear_safe_attributes(self):
        pass


class MysqlConnStrModel(ConnStrModel):
    __slots__ = [
        'host',
        'port',
        'database',
        'user',
        'password',
        'use_ssh',
        'ssh_host',
        'ssh_port',
        'ssh_user',
        'ssh_password',
        'table_name_prefix',
        'db_type',
        'engine',
    ]

    def __init__(self, **kwargs) -> None:
        self.host = None
        self.port = None
        self.database = None
        self.user = None
        self.password = None
        self.use_ssh = 0
        self.ssh_host = None
        self.ssh_port = None
        self.ssh_user = None
        self.ssh_password = None
        # 数据表前缀限制，默认为空，多个以逗号分隔
        self.table_name_prefix = None
        self.db_type = 'mysql'
        self.engine = 'pymysql'
        super().__init__(**kwargs)

    def rules(self) -> List[Union[Tuple[List[str]], Tuple[str, str, Dict[str, List[int]]]]]:
        rules = super().rules()
        rules.append((['host', 'port', 'database', 'user', 'password'],))
        rules.append(('use_ssh', 'in_range', {'range': [0, 1]}))
        if self.use_ssh:
            rules.append((['ssh_host', 'ssh_port', 'ssh_user', 'ssh_password'],))
        return rules

    def encrypt(self):
        crypt = AESCrypt()
        if self.password:
            self.password = crypt.encrypt(self.password)
        if self.ssh_password:
            self.ssh_password = crypt.encrypt(self.ssh_password)

    def decrypt(self) -> None:
        crypt = AESCrypt()
        if self.password:
            self.password = crypt.decrypt(self.password)
        if self.ssh_password:
            self.ssh_password = crypt.decrypt(self.ssh_password)

    def clear_safe_attributes(self) -> None:
        self.password = None
        self.ssh_password = None


class PostgreSQLConnStrModel(MysqlConnStrModel):
    # pylint: disable=W0235
    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)


class MsSqlConnStrModel(MysqlConnStrModel):
    # pylint: disable=W0235
    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)
        self.db_type = 'mssql'
        self.engine = 'pymssql'

    def rules(self) -> List[Union[Tuple[List[str]], Tuple[str, str, Dict[str, List[int]]]]]:
        rules = list()
        rules.append((['host', 'database', 'user', 'password'],))
        return rules


class ODPSConnStrModel(ConnStrModel):
    __slots__ = ['project_name', 'access_id', 'access_key', 'table_name_prefix']

    def __init__(self, **kwargs):
        self.project_name = None
        self.access_id = None
        self.access_key = None
        # 数据表前缀限制，默认为空，多个以逗号分隔
        self.table_name_prefix = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['project_name', 'access_id', 'access_key'],))
        return rules

    def encrypt(self):
        crypt = AESCrypt()
        if self.access_key:
            self.access_key = crypt.encrypt(self.access_key)

    def decrypt(self):
        crypt = AESCrypt()
        if self.access_key:
            self.access_key = crypt.decrypt(self.access_key)

    def clear_safe_attributes(self):
        self.access_key = None


class SaaSConnStrModel(ConnStrModel):
    __slots__ = [
        'tenant_db_host',
        'tenant_db_port',
        'tenant_db_name',
        'tenant_db_user',
        'tenant_db_password',
        'tenant_list_sql',
        'standard_db_host',
        'standard_db_port',
        'standard_db_name',
        'standard_db_user',
        'standard_db_password',
        'table_name_prefix',
    ]

    def __init__(self, **kwargs):
        """
        SaaS连接信息
        :param kwargs:
        """
        # 租户清单配置
        self.tenant_db_host = None
        self.tenant_db_port = None
        self.tenant_db_name = None
        # 若租列表数据表、视图中未配置，则默认采用该密码
        self.tenant_db_user = None
        self.tenant_db_password = None
        # 租户列表数据表、视图 字段必须包括：database,host,port, 可选择字段 user,password
        self.tenant_list_sql = None

        # 租户标准库配置
        self.standard_db_host = None
        self.standard_db_port = None
        self.standard_db_name = None
        self.standard_db_user = None
        self.standard_db_password = None

        # 数据表前缀限制，默认为空，多个以逗号分隔
        self.table_name_prefix = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(
            (
                [
                    'tenant_db_host',
                    'tenant_db_port',
                    'tenant_db_name',
                    'tenant_db_user',
                    'tenant_db_password',
                    'tenant_list_sql',
                    'standard_db_host',
                    'standard_db_port',
                    'standard_db_name',
                    'standard_db_user',
                    'standard_db_password',
                ],
            )
        )
        rules.append(('tenant_list_sql', 'validate_sql'))
        return rules

    @staticmethod
    def validate_sql(config, attr_name, value):
        """
        校验SQL关键字
        :param config:
        :param attr_name:
        :param value:
        :return:
        """
        sql_key_word = [
            'truncate',
            'create',
            'add',
            'alert',
            'modify',
            'delete',
            'insert',
            'update',
            'drop',
            'set',
            'goto',
            'show',
            'function',
            'procedure',
            'index',
            'prepare',
            'execute',
            'deallocate',
            'begin',
            'if',
            'call',
            'return',
        ]
        pattern = r'(\b' + r'\b)|(\b'.join(sql_key_word) + r'\b)'
        match = re.compile(pattern, re.IGNORECASE).search(value)
        if match:
            msg = config.get('msg')
            raise UserError(message=attr_name + '禁止使用关键字：' + match.group() if not msg else msg)
        return True

    def encrypt(self):
        crypt = AESCrypt()
        if self.tenant_db_password:
            self.tenant_db_password = crypt.encrypt(self.tenant_db_password)
        if self.standard_db_password:
            self.standard_db_password = crypt.encrypt(self.standard_db_password)

    def decrypt(self):
        crypt = AESCrypt()
        if self.tenant_db_password:
            self.tenant_db_password = crypt.decrypt(self.tenant_db_password)
        if self.standard_db_password:
            self.standard_db_password = crypt.decrypt(self.standard_db_password)

    def clear_safe_attributes(self):
        self.tenant_db_password = None
        self.standard_db_password = None


class MysoftERPConnStrModel(ConnStrModel):
    __slots__ = ['host', 'app_name', 'access_id', 'access_secret', 'table_name_prefix']

    def __init__(self, **kwargs) -> None:
        self.host = None
        self.app_name = None
        self.access_id = None
        self.access_secret = None
        # 数据表前缀限制，默认为空，多个以逗号分隔
        self.table_name_prefix = None
        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[List[str]]]:
        rules = super().rules()
        rules.append((['host', 'app_name', 'access_id', 'access_secret'],))
        return rules

    def encrypt(self):
        crypt = AESCrypt()
        if self.access_secret:
            self.access_secret = crypt.encrypt(self.access_secret)

    def decrypt(self) -> None:
        crypt = AESCrypt()
        if self.access_secret:
            self.access_secret = crypt.decrypt(self.access_secret)

    def clear_safe_attributes(self):
        self.access_secret = None


class MysoftERPDbConnStrModel(ConnStrModel):
    __slots__ = [
        'db_host',
        'port',
        'database',
        'user',
        'password',
        'db_type',
        'service_name',
        'host',  # erp_host
        'app_name',
        'access_id',
        'access_secret',
        'db_str',
        'DBAPrivilege',
        'table_name_prefix'
    ]

    def __init__(self, **kwargs) -> None:
        self.host = None
        self.port = None
        self.database = None
        self.user = None
        self.password = None
        self.db_type = None
        self.service_name = None
        self.app_name = None
        self.access_id = None
        self.access_secret = None
        self.db_host = None
        self.DBAPrivilege = None
        self.db_str = None
        self.table_name_prefix = None
        super().__init__(**kwargs)
        self.get_erp_info()
        self.encrypt_db_str()

    def rules(self) -> List[Tuple[List[str]]]:
        rules = super().rules()
        rules.append((['db_host', 'user', 'password'],))
        return rules

    def encrypt(self):
        self.encrypt_db_str()
        crypt = AESCrypt()
        if self.password:
            self.password = crypt.encrypt(self.password)

    def decrypt(self) -> None:
        crypt = AESCrypt()
        if self.password:
            self.password = crypt.decrypt(self.password)
        self.encrypt_db_str()

    def encrypt_db_str(self):
        secret = 'MysoftShuJian!@#'
        db_config = {
            'db_type': self.db_type, 'host': self.db_host, 'port': self.port, 'user': self.user,
            'database': self.database, 'password': self.password, 'dba_privilege': self.DBAPrivilege,
            'service_name': self.service_name
        }
        db_str = json.dumps(db_config)
        crypt = AESCrypt(secret)
        self.db_str = crypt.encrypt(db_str)

    def get_erp_info(self):
        erp_info = get_mysoft_erp()
        host = erp_info.get('erpapi_host')
        _url = urlparse(host)
        self.host = _url.scheme + '://' + _url.netloc
        self.app_name = 'mybigdata'
        self.access_id = erp_info.get('erpapi_access_id')
        self.access_secret = erp_info.get('erpapi_access_secret')

    def clear_safe_attributes(self):
        self.password = None
        self.access_secret = None


class MysoftERPConfigCenterModel(ConnStrModel):
    __slots__ = [
        'AppCode',
        'AppLevelCode',
        'ConfigType',
        'DbType',
        'Server',
        'Database',
        'Uid',
        'Pwd',
        'Port',
        'IsErp',
        'AppId',
        'EnvironmentId',
        'SiteGroupKey',
        'FirstLevelCode',
        'DbRole',
        'DataFrom',
        'SaasLinkType',
        'IsShuXin',
        'ProjectCode'
    ]

    def __init__(self, **kwargs) -> None:
        self.AppCode = None
        self.AppLevelCode = None
        self.ConfigType = None
        self.DbType = ""
        self.Server = ""
        self.Database = ""
        self.Uid = ""
        self.Pwd = ""
        self.Port = None
        self.DbRole = ""
        self.IsErp = 0
        self.AppId = ""
        self.EnvironmentId = ""
        self.SiteGroupKey = ""
        self.FirstLevelCode = ""
        self.DataFrom = ""
        self.SaasLinkType = None
        self.host = None
        self.app_name = None
        self.access_id = None
        self.access_secret = None
        self.is_config_center = ""
        # 数据表前缀限制，默认为空，多个以逗号分隔
        self.table_name_prefix = None
        self.db_str = None

        # 数芯1.5数据源替换数据服务中心数据源
        self.IsShuXin = 0
        self.ProjectCode = ""

        super().__init__(**kwargs)
        self.get_erp_info()
        self.encrypt_db_str()

    def rules(self) -> List[Tuple[List[str]]]:
        rules = super().rules()
        rules.append((['db_host', 'user', 'password'],))
        return rules

    def encrypt(self):
        self.encrypt_db_str()
        crypt = AESCrypt()
        if self.Pwd:
            self.Pwd = crypt.encrypt(self.Pwd)

    def decrypt(self) -> None:
        crypt = AESCrypt()
        if self.Pwd:
            self.Pwd = crypt.decrypt(self.Pwd)
        self.encrypt_db_str()

    def encrypt_db_str(self):
        secret = 'MysoftShuJian!@#'
        if self.ConfigType == MysoftNewERPConfigType.CONFIG_CENTER.value and self.AppId and self.SiteGroupKey:
            db_config = {
                "db_type": "configcenter", "appid": self.AppId,
                "environmentid": self.EnvironmentId,
                "sitegroupkey": self.SiteGroupKey, "appcode": self.AppLevelCode
            }
        elif self.ConfigType == MysoftNewERPConfigType.MANUAL_INPUT.value and self.DbType.lower() in [
            MysoftNewERPDataBaseType.SQL_Server.value.lower()]:
            db_config = {
                'db_type': self.DbType, 'host': self.Server, 'port': self.Port, 'user': self.Uid,
                'database': self.Database, 'password': self.Pwd
            }
        db_str = json.dumps(db_config)
        crypt = AESCrypt(secret)
        self.db_str = crypt.encrypt(db_str)

    def get_erp_info(self):
        erp_info = get_mysoft_erp()
        host = erp_info.get('erpapi_host')
        _url = urlparse(host)
        self.host = _url.scheme + '://' + _url.netloc
        self.app_name = 'mybigdata'
        self.access_id = erp_info.get('erpapi_access_id')
        self.access_secret = erp_info.get('erpapi_access_secret')

    def clear_safe_attributes(self):
        self.Pwd = None


class DataHubConnStrModel(ConnStrModel):
    __slots__ = ['db_code', 'data_base_type', 'host', 'access_secret']

    def __init__(self, **kwargs):
        self.db_code = None
        self.data_base_type = None
        self.host = None
        self.access_secret = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['host', 'access_secret'],))
        return rules

    def encrypt(self):
        crypt = AESCrypt()
        if self.access_secret:
            self.access_secret = crypt.encrypt(self.access_secret)

    def decrypt(self):
        crypt = AESCrypt()
        if self.access_secret:
            self.access_secret = crypt.decrypt(self.access_secret)

    def clear_safe_attributes(self):
        self.access_secret = None


class APIConnStrModel(ConnStrModel):
    __slots__ = ['host', 'access_secret', 'params', 'cookie', 'param_key', 'tenant_code', 'third_party_id', 'check_sr']

    def __init__(self, **kwargs) -> None:
        self.host = None
        self.access_secret = None
        self.param_key = None
        self.params = []
        self.cookie = None
        self.tenant_code = None
        self.third_party_id = None
        self.check_sr = 0

        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[List[str]]]:
        rules = super().rules()
        rules.append((['host', 'access_secret'],))
        return rules

    def encrypt(self):
        crypt = AESCrypt()
        if self.access_secret:
            self.access_secret = crypt.encrypt(self.access_secret)

    def decrypt(self):
        crypt = AESCrypt()
        if self.access_secret:
            self.access_secret = crypt.decrypt(self.access_secret)
        return self.access_secret

    def clear_safe_attributes(self) -> None:
        self.access_secret = None


class HighDataConnStrModel(ConnStrModel):
    __slots__ = ['oss_url', 'access_key_id', 'access_key_secret']

    def __init__(self, **kwargs):
        self.oss_url = None
        self.access_key_id = None
        self.access_key_secret = None
        super().__init__(**kwargs)

    def encrypt(self):
        crypt = AESCrypt()
        if self.access_key_secret:
            self.access_key_secret = crypt.encrypt(self.access_key_secret)

    def decrypt(self):
        crypt = AESCrypt()
        if self.access_key_secret:
            self.access_key_secret = crypt.decrypt(self.access_key_secret)

    def clear_safe_attributes(self):
        self.access_key_secret = None


class ADSDataConnStrModel(ConnStrModel):
    __slots__ = ['host', 'port', 'access_key_id', 'access_key_secret', 'database', 'version']

    def __init__(self, **kwargs):
        self.host = None
        self.port = None
        self.access_key_id = None
        self.access_key_secret = None
        self.database = None
        self.version = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['host', 'access_key_id', 'access_key_secret', 'database'], 'string'))
        rules.append((['port'],))
        return rules

    def encrypt(self):
        crypt = AESCrypt()
        if self.access_key_secret:
            self.access_key_secret = crypt.encrypt(self.access_key_secret)

    def decrypt(self):
        crypt = AESCrypt()
        if self.access_key_secret:
            self.access_key_secret = crypt.decrypt(self.access_key_secret)

    def clear_safe_attributes(self):
        self.access_key_secret = None


class MysoftNewERPConnStrModel(ConnStrModel):
    """
    MysoftNewERP 通过接口管家连接数据服务中心
    """

    __slots__ = [
        'AppCode',
        'AppLevelCode',
        'ConfigType',
        'DbType',
        'Server',
        'Database',
        'Uid',
        'Pwd',
        'Port',
        'IsErp',
        'AppId',
        'EnvironmentId',
        'SiteGroupKey',
        'FirstLevelCode',
        'DbRole',
        'DataFrom',
        'SaasLinkType',
        'is_master_local_db',
        'erp_api_info_id',
        'IsShuXin',
        'ProjectCode'
    ]

    def __init__(self, **kwargs) -> None:
        self.AppCode = None
        self.AppLevelCode = None
        self.ConfigType = None
        self.DbType = ""
        self.Server = ""
        self.Database = ""
        self.Uid = ""
        self.Pwd = ""
        self.Port = None
        self.DbRole = ""
        self.IsErp = 0
        self.AppId = ""
        self.EnvironmentId = ""
        self.SiteGroupKey = ""
        self.FirstLevelCode = ""
        self.DataFrom = ""
        self.is_master_local_db = None
        # 三云链接方式，1：api，2：数据库，默认为1
        self.SaasLinkType = None
        self.erp_api_info_id = None

        # 数芯1.5数据源替换数据服务中心数据源
        self.IsShuXin = 0
        self.ProjectCode = ""

        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[List[str]]]:
        rules = super().rules()
        rules.append((['ConfigType'],))
        return rules

    def clear_safe_attributes(self):
        # 三云api模式连接，返回企业code和秘钥
        if self.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value \
                and self.SaasLinkType == MysoftNewERPCloudType.CLOUD_API.value:
            self.decrypt()
        else:
            self.Pwd = None

    def encrypt(self):
        crypt = AESCrypt()
        if self.Pwd:
            self.Pwd = crypt.encrypt(self.Pwd)

    def decrypt(self):
        crypt = AESCrypt()
        if self.Pwd:
            self.Pwd = crypt.decrypt(self.Pwd)

    def get_config_xml(self):
        """
        获取xml配置
        """
        xml = ""
        # 构建xml时对pwd进行加密，因外部还会加密一次，所以重新赋值pwd为明文
        pwd = self.Pwd
        self.encrypt()
        xml_dict = link_config = dict()
        if self.DataFrom == MysoftNewERPDataFromType.MYSOFT_ERP.value:
            if self.ConfigType == MysoftNewERPConfigType.CONFIG_CENTER.value and self.AppId and self.SiteGroupKey:
                # 存在值，才构建xml
                link_config = {
                    "AppId": self.AppId,
                    "EnvironmentId": self.EnvironmentId,
                    "SiteGroupKey": self.SiteGroupKey,
                }
            if self.ConfigType == MysoftNewERPConfigType.MANUAL_INPUT.value and self.DbType.lower() in [
                MysoftNewERPDataBaseType.SQL_Server.value.lower()
            ]:
                link_config = {
                    "Server": self.Server,
                    "Database": self.Database,
                    "Uid": self.Uid,
                    "Pwd": self.Pwd,
                    "Port": self.Port,
                }
            if link_config:
                xml_dict = {"Config": link_config}
        elif self.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
            link_config = {
                "Server": self.Server,
                "Database": self.Database,
                "Uid": self.Uid,
                "Pwd": self.Pwd,
                "Port": self.Port,
            }
            xml_dict = {"Config": link_config}
        elif self.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
            # 三云的xml构建
            link_config = {
                "serverUrl": self.Server,
                "tenantCode": self.Database,
                "corpSecret": self.Pwd,
                "appCode": self.AppCode,
                "LevelCode": self.AppLevelCode,
            }
            xml_dict = {"CloudMysqlServerInfo": link_config}
        if xml_dict:
            xml = xmltodict.unparse(xml_dict)
        # 恢复密码为明文，外部还会再次加密
        self.Pwd = pwd
        return xml


class MysoftShuXinConnStrModel(ConnStrModel):
    __slots__ = [
        'api_host',
        'app_id',
        'app_secret',
        'env_code',
        'project_code',
        'url',
        'key',
        'app_key',
        'engine'
    ]

    def __init__(self, **kwargs) -> None:
        self.api_host = None
        self.app_id = None
        self.app_secret = None
        self.env_code = ""
        self.project_code = ""
        self.url = ""
        self.key = ""
        self.app_key = ""
        self.engine = ""
        super().__init__(**kwargs)

    # def encrypt(self):
    #     crypt = AESCrypt()
    #     if self.app_secret:
    #         self.app_secret = crypt.encrypt(self.app_secret)
    #
    # def decrypt(self):
    #     crypt = AESCrypt()
    #     if self.app_secret:
    #         self.app_secret = crypt.decrypt(self.app_secret)


class MysoftShuXin15ConnStrModel(ConnStrModel):
    __slots__ = [
        'api_host',
        'app_id',
        'app_secret',
        'env_code',
        'project_code',
        'url',
        'key',
        'app_key',
        'engine'
    ]

    def __init__(self, **kwargs) -> None:
        self.api_host = None
        self.app_id = None
        self.app_secret = None
        self.env_code = ""
        self.project_code = ""
        self.url = ""
        self.key = ""
        self.app_key = ""
        self.engine = ""
        super().__init__(**kwargs)


class DataSourceQueryModel(QueryBaseModel):
    __slots__ = ['type', 'is_new', 'is_buildin']

    def __init__(self, **kwargs) -> None:
        self.type = None
        self.is_new = None
        self.is_buildin = None
        super().__init__(**kwargs)


class TableQueryModel(QueryBaseModel):
    __slots__ = ['id', 'data_source']

    def __init__(self, **kwargs) -> None:
        # 数据源id
        self.id = None
        self.data_source = None
        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[str, str, Dict[str, int]]]:
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        return rules


class ColumnQueryModel(TableQueryModel):
    __slots__ = ['table_name']

    def __init__(self, **kwargs):
        # 数据表名
        self.table_name = None
        self.var_content = None
        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(('table_name', 'string', {'max': 255}))
        return rules


class ColumnValueQueryModel(TableQueryModel):
    __slots__ = ['table_name', 'column_name']

    def __init__(self, **kwargs):
        # 数据表名
        self.table_name = None
        self.column_name = None
        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(('table_name', 'string', {'max': 255}))
        rules.append(('column_name', 'string', {'max': 255}))
        return rules


class CreateTableModel(TableQueryModel):
    __slots__ = ['create_sql']

    def __init__(self, **kwargs):
        self.create_sql = None
        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(('create_sql', 'validate_sql'))
        return rules

    @staticmethod
    def validate_sql(config, attr_name, value):
        """
        校验SQL关键字
        :param config:
        :param attr_name:
        :param value:
        :return:
        """
        sql_key_word = [
            'truncate',
            'select',
            'add',
            'alert',
            'modify',
            'delete',
            'insert',
            'update',
            'drop',
            'set',
            'goto',
            'show',
            'function',
            'procedure',
            'index',
            'prepare',
            'execute',
            'deallocate',
            'begin',
            'end',
            'call',
            'return',
        ]
        pattern = r'(\b' + r'\b)|(\b'.join(sql_key_word) + r'\b)'
        match = re.compile(pattern, re.IGNORECASE).search(value)
        if match:
            msg = config.get('msg')
            raise UserError(message=attr_name + '禁止使用关键字：' + match.group() if not msg else msg)
        return True


class ApiParamModel(BaseModel):
    __slots__ = ['key', 'value', 'description']

    def __init__(self, **kwargs) -> None:
        self.key = None
        self.value = None
        self.description = None
        super().__init__(**kwargs)


class PrestoConnStrModel(ConnStrModel):
    __slots__ = [
        'host',
        'port',
        'catalog',
        'database',
        'user',
        'password',
    ]

    def __init__(self, **kwargs) -> None:
        self.host = None
        self.port = None
        self.database = None
        self.user = None
        self.password = None
        self.catalog = None
        # 数据表前缀限制，默认为空，多个以逗号分隔
        self.table_name_prefix = None
        super().__init__(**kwargs)


if __name__ == "__main__":
    from dmplib.utils.crypt import AESCrypt

    crypt = AESCrypt()
    access_key_secret = crypt.decrypt("09216595f48b730754ab51ff71db009a")
    print(access_key_secret)
