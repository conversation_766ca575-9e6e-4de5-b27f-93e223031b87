#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by wangf10 on 2021/10/12
"""
from sqlalchemy import text

from components.orm import OrmDb, OrmConnection


def get_tables(query_model):
    """
    获取数据表
    :param data_source.models.TableQueryModel query_model:
    :return:
    """
    sql = """SELECT obj.name AS [name],CONVERT(VARCHAR,pro.value) AS [comment],
ROW_NUMBER() OVER(Order by obj.name ) AS RowId 
FROM sys.objects obj LEFT JOIN sys.extended_properties pro ON obj.object_id=pro.major_id 
WHERE obj.type='u' """
    params = {}
    if query_model.keyword:
        sql += """ and (obj.name like :keywords or CONVERT(VARCHAR,pro.value) like :keywords)"""
        params['keywords'] = '%' + query_model.keyword + '%'
    execute_sql = text("""select name, comment from ({}) as b where RowId between :start and :end""".format(sql))
    params['start'] = int(query_model.skip)
    params['end'] = int(query_model.page_size + query_model.skip)
    with get_mssql_db(query_model.data_source) as db:
        data = db.execute(execute_sql, params)
        query_model.items = OrmDb.data_format(data)
        query_model.total = db.execute(text("select count(*) as total from sys.objects WHERE type='u';")).scalar()
        return query_model


def get_table_columns(query_model):
    """
    获取数据表字段
    :param data_source.models.ColumnQueryModel query_model:
    :return:
    """
    sql = '''
        SELECT
        a.name,
        COLUMNPROPERTY(a.id,a.name,'PRECISION') as length,
        b.name as type,
        g.[value] AS comment
        FROM syscolumns a left join systypes b
        on a.xtype=b.xusertype
        inner join sysobjects d
        on a.id=d.id and d.xtype='U'
        left join sys.extended_properties g
        on a.id=g.major_id AND a.colid = g.minor_id 
        WHERE d.[name] =:name
    '''
    with get_mssql_db(query_model.data_source) as db:
        data = db.execute(text(sql), {'name': query_model.table_name})
        query_model.items = OrmDb.data_format(data)
        for row in query_model.items:
            if row.get('type') in ['nvarchar', 'varchar', 'char']:
                if row['length'] < 0:
                    row['type'] = 'text'
                else:
                    row['type'] = '{}({})'.format(row['type'], row['length'])
        query_model.total = len(query_model.items)
        return query_model


def create_table(model):
    """
    创建数据表
    :param data_source.models.CreateTableModel model:
    :return:
    """
    with get_mssql_db(model.data_source) as db:
        return db.execute(text(model.create_sql))


def get_result_data(model, sql):
    """
    获取SQL返回的结果
    :param data_source.models.DataSourceModel model:
    :param sql
    :return:
    """
    with get_mssql_db(model) as db:
        data = db.execute(text(sql))
        return OrmDb.data_format(data)


def get_mssql_db(model):
    """
    获取mssql数据db
    :param data_source.models.DataSourceModel model:
    :return:
    """
    conn_str = model.conn_str
    connect = OrmConnection(**conn_str.get_dict())
    orm = OrmDb(connect)
    return orm.get_db()
