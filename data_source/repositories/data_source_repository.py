#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2017/3/21.
"""
from dmplib.saas.project import get_db
from base import repository
from base.dmp_constant import DATA_SOURCE_DATASET_GUID

from data_source.models import DataSourceQueryModel


def get_data_source_list(model: DataSourceQueryModel) -> DataSourceQueryModel:
    """
    获取数据源列表
    :param data_source.models.DataSourceQueryModel model:
    :return tuple:
    """
    fields = [
        'id',
        'name',
        'code',
        'description',
        'type',
        'is_buildin',
        'icon',
        'modified_on',
        'conn_str',
        'created_by',
        'config_type',
        'db_type',
        'app_code',
        'app_level_code',
        'data_from'
    ]
    sql = 'SELECT `' + '`,`'.join(fields) + '` FROM data_source '

    # 添加数据集数据源
    if model.is_new and model.is_new == '1':
        sql = 'SELECT * FROM( select `' + '`,`'.join(fields) + '` FROM data_source \n'
        sql += ' UNION all \n'
        sql += f"SELECT '{DATA_SOURCE_DATASET_GUID}' as `{fields[0]}`,'数见数据集' as `{fields[1]}`,"
        sql += f"'proj_dataset' as `{fields[2]}`,'数见数据集分类' as `{fields[3]}`,'DataSet' as `{fields[4]}`,"
        sql += f"'1' as `{fields[5]}`,'' as `{fields[6]}`,'2023-08-30 00:00:00' as `{fields[7]}`,NULL as `{fields[8]}`,"
        sql += f"'System' as `{fields[9]}`,0 as `{fields[10]}`,'' as `{fields[11]}`,'' as `{fields[12]}`,'' as `{fields[13]}`,"
        sql += f"'' as `{fields[14]}`) as t "
    params = {}
    wheres = []
    if model.keyword:
        wheres.append('( `name` LIKE %(keyword)s  OR  `description` LIKE %(keyword)s)')
        params['keyword'] = '%' + model.keyword_escape + '%'
    if model.type:
        if isinstance(model.type, list):
            types = model.type
        else:
            types = model.type.split(',')
        condition = []
        for data_source_type in types:
            condition.append('`type` = %(' + data_source_type + ')s')
            params[data_source_type] = data_source_type
        wheres.append(' or '.join(condition))

    if model.is_buildin:
        wheres.append('`is_buildin` = %(is_buildin)s')
        params['is_buildin'] = model.is_buildin
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    if model.is_new and model.is_new == '1':  # 编辑界面需要有这个排序，其他界面没有这个排序
        sql += ' ORDER BY  case when Type =\'MysoftShuXin15\' then 0 when type =\'MysoftNewERP\'  then 1 when type =\'DataSet\'  then 90 when `code` =\'proj_config\'  then 91 when `code` =\'proj_data\'  then 92 else 50 end ,   `modified_on` DESC'
    else:
        sql += ' ORDER BY `modified_on` DESC'
    with get_db() as db:
        model.total = repository.get_total(sql,  params, db)
        # 数据权限问题，page_size暂和应用门户一样处理改成1000
        sql += ' LIMIT ' + str(model.skip) + ',' + str(1000)
        model.items = db.query(sql, params)
    return model


def query_reference(data_source_id):
    """
    查询数据源的依赖
    :param data_source_id:
    :return:
    """
    content = '"data_source_id": "{}"'.format(data_source_id)
    sql = """
        select `id` from dataset where content like '%{}%'
    """.format(
        content
    )
    with get_db() as db:
        return db.query_columns(sql)


def get_data_source_name_by_ids(ids):
    return repository.get_dict("data_source", {"id": ids}, "id,name")


def get_data_source_by_id(id_: str):
    return repository.get_data("data_source", {"id": id_})
