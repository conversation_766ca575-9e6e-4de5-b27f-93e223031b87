#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/21.
"""

from dmplib.db.mysql_wrapper import SimpleMysql
from base import repository


def get_tables(query_model):
    """
    获取数据表
    :param data_source.models.TableQueryModel query_model:
    :return:
    """
    with get_mysql_db(query_model.data_source) as db:
        sql = 'SELECT TABLE_NAME AS `name`,TABLE_COMMENT AS `comment` ' 'FROM information_schema.TABLES ' 'WHERE TABLE_TYPE=\'BASE TABLE\' ' 'AND TABLE_SCHEMA =DATABASE() '
        params = {}
        if query_model.keyword:
            sql += ' AND (TABLE_NAME LIKE %(keyword)s or TABLE_COMMENT LIKE %(keyword)s) '
            params['keyword'] = '%' + query_model.keyword_escape + '%'
        if query_model.data_source.conn_str.table_name_prefix:
            table_name_prefix = query_model.data_source.conn_str.table_name_prefix.split(',')
            for i in range(len(table_name_prefix)):
                if not table_name_prefix[i]:
                    continue
                sql += ' AND TABLE_NAME LIKE %(prefix' + str(i) + ')s '
                params['prefix' + str(i)] = table_name_prefix[i].replace('_', '\\_') + '%'

        query_model.total = repository.get_total(sql, params, db)
        sql += 'LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
        return query_model


def get_table_columns(query_model):
    """
    获取数据表字段
    :param data_source.models.ColumnQueryModel query_model:
    :return:
    """
    sql = 'SELECT COLUMN_NAME AS `name`,COLUMN_TYPE AS `type` ,COLUMN_COMMENT AS `comment` ' 'FROM information_schema.COLUMNS ' 'WHERE TABLE_SCHEMA =DATABASE() AND TABLE_NAME=%(table_name)s ' 'ORDER BY ORDINAL_POSITION'
    with get_mysql_db(query_model.data_source) as db:
        query_model.items = db.query(sql, {'table_name': query_model.table_name})
        query_model.total = len(query_model.items)
        return query_model


def create_table(model):
    """
    创建数据表
    :param data_source.models.CreateTableModel model:
    :return:
    """
    with get_mysql_db(model.data_source) as db:
        return db.exec_sql(model.create_sql)


def get_mysql_db(model):
    """
    获取mysql数据db
    :param data_source.models.DataSourceModel model:
    :return:
    """
    conn_str = model.conn_str
    return SimpleMysql(
        host=conn_str.host,
        port=int(conn_str.port),
        db=conn_str.database,
        user=conn_str.user,
        passwd=conn_str.password,
        use_ssh=conn_str.use_ssh,
        ssh_host=conn_str.ssh_host,
        ssh_port=conn_str.ssh_port,
        ssh_user=conn_str.ssh_user,
        ssh_password=conn_str.ssh_password,
    )
