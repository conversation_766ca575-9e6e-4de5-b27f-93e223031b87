#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pymysql import OperationalError

from dmplib.db.mysql_wrapper import SimpleMysql
from data_source.models import ADSDataConnStrModel, TableQueryModel, ColumnQueryModel


def get_ads_db(model: ADSDataConnStrModel) -> SimpleMysql:
    return SimpleMysql(
        host=model.host,
        port=int(model.port),
        db=model.database,
        user=model.access_key_id,
        passwd=model.access_key_secret,
    )


def get_ads_version(model: ADSDataConnStrModel) -> str:
    with get_ads_db(model) as db:
        sql = 'select adb_version()'
        try:
            db.query_scalar(sql)
        except OperationalError:
            return '2'
        else:
            return '3'


def get_table_list(query_model: TableQueryModel):
    if query_model.data_source.conn_str.version == '3':
        return get_table_list30(query_model)
    else:
        return get_table_list20(query_model)


def get_table_list30(query_model: TableQueryModel):
    sql = 'select TABLE_NAME, TABLE_COMMENT as COMMENTS from information_schema.tables'
    count_sql = 'select count(*) from information_schema.tables'
    params = {'database': query_model.data_source.conn_str.database}
    where_condition = ' where TABLE_SCHEMA = %(database)s '

    if query_model.keyword:
        where_condition += ' and (`TABLE_NAME` like %(keyword)s or `TABLE_COMMENT` like %(keyword)s) '
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += where_condition
    count_sql += where_condition
    sql += ' ORDER BY ' + (','.join(sorts) if sorts else '`TABLE_NAME` ASC')
    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)

    with get_ads_db(query_model.data_source.conn_str) as db:
        query_model.items = db.query(sql, params)
        query_model.total = db.query_scalar(count_sql, params)
    return query_model.get_result_dict()


def get_table_list20(query_model: TableQueryModel):
    sql = '''select `TABLE_NAME`, `COMMENTS`, `PARTITION_COLUMN` from information_schema.tables'''
    count_sql = '''select count(*) from information_schema.tables'''
    params = {}
    where_condition = ''
    if query_model.keyword:
        where_condition = ' where `TABLE_NAME` like %(keyword)s or `COMMENTS` like %(keyword)s '
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += where_condition
    count_sql += where_condition
    sql += ' order by ' + (','.join(sorts) if sorts else '`table_name` asc')
    sql += ' limit ' + str(query_model.skip) + ',' + str(query_model.page_size)

    with get_ads_db(query_model.data_source.conn_str) as db:
        query_model.items = db.query(sql, params)
        query_model.total = db.query_scalar(count_sql, params)
    return query_model.get_result_dict()


def get_table_partition_column(query_model: ColumnQueryModel):
    if query_model.data_source.conn_str.version == '3':
        return get_table_partition_column30(query_model)
    else:
        return get_table_partition_column20(query_model)


def get_table_partition_column30(query_model: ColumnQueryModel):
    sql = 'select DISTRIBUTE_COLUMN as PARTITION_COLUMN from information_schema.kepler_meta_tables'

    params = {'table_name': query_model.table_name, 'database': query_model.data_source.conn_str.database}
    where_condition = ' where TABLE_SCHEMA = %(database)s and TABLE_NAME = %(table_name)s'

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += where_condition
    sql += ' order by ' + (','.join(sorts) if sorts else '`table_name` asc')

    with get_ads_db(query_model.data_source.conn_str) as db:
        items = db.query_columns(sql, params)
    return items


def get_table_partition_column20(query_model: ColumnQueryModel):
    sql = 'select PARTITION_COLUMN from information_schema.tables'

    params = {'table_name': query_model.table_name}
    where_condition = ' where TABLE_NAME = %(table_name)s'

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += where_condition
    sql += ' order by ' + (','.join(sorts) if sorts else '`table_name` asc')

    with get_ads_db(query_model.data_source.conn_str) as db:
        items = db.query_columns(sql, params)
    return items


def get_table_columns(query_model: ColumnQueryModel):
    if query_model.data_source.conn_str.version == '3':
        return get_table_columns30(query_model)
    else:
        return get_table_columns20(query_model)


def get_table_columns30(query_model: ColumnQueryModel):
    sql = '''select `COLUMN_NAME`, `COLUMN_COMMENT`, `COLUMN_TYPE` from information_schema.columns'''
    params = {'table_name': query_model.table_name, 'database': query_model.data_source.conn_str.database}
    where_condition = ' where TABLE_SCHEMA = %(database)s and TABLE_NAME = %(table_name)s'
    if query_model.keyword:
        where_condition += ' and `COLUMN_NAME` like %(keyword)s'
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += where_condition
    sql += ' order by ' + (','.join(sorts) if sorts else '`ordinal_position` asc')

    with get_ads_db(query_model.data_source.conn_str) as db:
        query_model.items = db.query(sql, params)
        query_model.total = len(query_model.items)
    return query_model.get_result_dict()


def get_table_columns20(query_model: ColumnQueryModel):
    sql = '''select `COLUMN_NAME`, `COLUMN_COMMENT`, `COLUMN_TYPE` from information_schema.columns'''
    params = {'table_name': query_model.table_name}
    where_condition = ' where TABLE_NAME = %(table_name)s'
    if query_model.keyword:
        where_condition += ' and `COLUMN_NAME` like %(keyword)s'
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += where_condition
    sql += ' order by ' + (','.join(sorts) if sorts else '`ordinal_position` asc')

    with get_ads_db(query_model.data_source.conn_str) as db:
        query_model.items = db.query(sql, params)
        query_model.total = len(query_model.items)
    return query_model.get_result_dict()


def create_table(model):
    """
    创建数据表
    :param data_source.models.CreateTableModel model:
    :return:
    """
    with get_ads_db(model.data_source.conn_str) as db:
        return db.exec_sql(model.create_sql)
