#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2017/3/18.
"""
import json
import os
from copy import deepcopy

import pickle
import pymysql
import xmltodict
from base import repository
from base.enums import DataSourceType, DataBaseType, MysoftNewERPConfigType, MysoftNewERPDataBaseType, \
    MysoftNewERPDataFromType, MysoftNewERPCloudType, ErpApiId, ErpApiType, MysoftShuXinTableType
from base import event
from components import db_engine_transform
from components.kong import KongAPI
from components.storage_setting import get_dmp_env_sign, get_storage_type
from components.pular_api import PulsarApi
from dataset.repositories import dataset_repository
from dmplib.redis import RedisCache
from data_source.cache import data_source_meta_cache
from data_source.models import ConnStrModel, DataSourceModel, ODPSConnStrModel, \
    MysqlConnStrModel, ADSDataConnStrModel, MysoftNewERPConnStrModel, MysoftShuXinConnStrModel
from data_source.repositories import data_source_repository
from data_source.repositories.ads_data_source_repository import get_ads_version
from data_source.services.data_source_client import get_client, DataHubDataSource
from data_source.services.external_data_source_service import get_app_code, get_mysoft_saas_server_url
from dmplib import config
from dmplib.constants import DATA_SOURCE_ODPS_ID, DATA_SOURCE_RDS_ID
from dmplib.saas.project import get_odps_config, get_data_db_config, get_db_config
from dmplib.utils.crypt import AESCrypt
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.redis import conn as redis_conn
from rbac.services.data_permissions import data_permission_edit_filter, data_permission_filter

from data_source.models import DataSourceModel, DataSourceQueryModel, TableQueryModel
from datetime import datetime
from typing import Dict, List, Optional, Union
from keywords.external_service import delete_keyword_by_datasource_id
from dmplib.hug import g
import logging

logger = logging.getLogger(__name__)
logger.setLevel("INFO")
DATA_SOURCE_DMP_ID = "00000000-1111-1111-4444-000000000000"


def get_params_of_pulsar(model: MysoftShuXinConnStrModel):
    """
    获取数芯参数
    :param model:
    :return:
    """
    key = f"{model.project_code}:pulsar_api{model.app_id}:{model.app_key}:token"
    key_no_code = f":pulsar_api{model.app_id}:{model.app_key}:token"
    cache = RedisCache(key_prefix="indicator_model")
    cache.delete(key)
    cache.delete(key_no_code)

    api = PulsarApi(
        _from='test_connect',
        host=model.api_host,
        app_key=model.app_id,
        app_secret=model.app_secret,
        pulsar_key=model.app_key,
        pulsar_code=model.project_code,
        set_token_cache=False
    )
    code_list = api.get_pulsar_code_list()
    # 新增的场景前端会调用两次接口，传project_code时才获取engine_list
    if model.project_code:
        engine_list = api.get_engine_list()
    else:
        engine_list = []
    return {
        "code_list": code_list,
        "engine_list": engine_list
    }


def test_connection(model: DataSourceModel):
    """
    测试连接数据源
    :param data_source.models.DataSourceModel model:
    :return bool:
    """

    adapter = get_client(model.type)
    return adapter.test_connection(model.conn_str)


@data_permission_edit_filter('data_source-edit')
def check_can_edit_data_sourc(model: DataSourceModel) -> str:
    """
    检测是否可以编辑该数据源
    :param model:
    :return:
    """
    return model.id


def send_config(request, model):
    # 修改host
    current_domain = config.get('Domain.dmp_openapi')
    # _url.scheme 拿到的是http, 暂时先统一成https
    if not current_domain:
        host = str(request.host).replace('dmp', 'dmp-openapi', 1)
        current_domain = '{}://{}'.format('https', host)

    # 获取 dmp_openapi_key
    kong_api = KongAPI()
    success, result = kong_api.list_consumers()
    dmp_openapi_key_id = ''
    if success:
        consumers = result.get('data')
        for consumer in consumers:
            if consumer.get('username') in ['dmp', 'DMP']:
                dmp_openapi_key_id = consumer.get('id')
                break
        # 如果没有找到dmp openapi key，选择第一个
        if not dmp_openapi_key_id and consumers:
            dmp_openapi_key_id = consumers[0].get('id')
    # 通过 dmp_openapi_key_id 获取 dmp_openapi_key
    success, result = kong_api.list_keys(dmp_openapi_key_id)
    dmp_openapi_key = ''
    if success:
        data = result.get('data')
        for item in data:
            # 这里对0.11和2.0版本的list_keys做兼容
            # 0.11版本在data中返回consumer_id
            if item.get('consumer_id') == dmp_openapi_key_id:
                dmp_openapi_key = item.get('key')
                break
            # 2.0版本在data中返回consumer, 在consumer中附带id
            if item.get('consumer') and item.get('consumer').get('id') == dmp_openapi_key_id:
                dmp_openapi_key = item.get('key')
                break
        if not dmp_openapi_key and data:
            dmp_openapi_key = data[0].get('key')

    # 加密敏感信息

    cryptor_key = config.get('Security.collect_crypt_key')
    if not cryptor_key or cryptor_key.lower() == "disable":
        access_key_secret = config.get('OSS.access_key_secret')
        access_key_id = config.get('OSS.access_key_id')
        cryptor_key = ""
    else:
        try:
            cryptor = get_cryptor(cryptor_key)
        except Exception as e:
            logger.error(str(e))
            raise UserError(code=500, message="获取加密实例失败, 请联系管理员!")
        access_key_secret = cryptor.encrypt(config.get('OSS.access_key_secret'))
        access_key_id = cryptor.encrypt(config.get('OSS.access_key_id'))
        dmp_openapi_key = cryptor.encrypt(dmp_openapi_key)

    # 下发配置
    _data = {
        "api": "api.sync.configuration",
        "oss": {
            "bucket": config.get('OSS.bucket'),
            "endpoint": config.get('OSS.endpoint'),
            "access_key_secret": access_key_secret,
            "access_key_id": access_key_id,
        },
        "api_mq_send": current_domain + '/openapi/send_message',
        "api_check_flow": current_domain + '/openapi/check_flow',
        "dmp_openapi_key": dmp_openapi_key,
        "env_code": os.environ.get('CONFIG_AGENT_CLIENT_CODE'),
        "key": cryptor_key,
    }
    data_log = json.dumps(_data).encode("utf-8")
    msg = "oss 下发配置信息: {}".format(data_log)
    logger.info(msg)
    DataHubDataSource.send_config(model.conn_str, _data)


def get_cryptor(cryptor_key):
    return AESCrypt(key=get_confused_key(cryptor_key))


def get_confused_key(cryptor_key):
    if not cryptor_key:
        return ""
    a = ''
    b = ''
    for x in range(len(cryptor_key)):
        if x % 2 == 0:
            a += cryptor_key[x]
        else:
            b += cryptor_key[x]
    key = a + b
    return key


def get_params(model: DataSourceModel):
    """
    获取数据源接口参数值
    :param data_source.models.DataSourceModel model:
    :return bool:
    """
    if model.type not in [DataSourceType.DataHub.value, DataSourceType.API.value]:
        raise UserError(message="只支持api、datahub数据源")

    adapter = get_client(model.type)
    result = adapter.get_params(model.conn_str)

    if model.type == DataSourceType.DataHub.value:
        datahub_params = []
        if result:
            for data in result:
                if "oracle" == data.get("driver") or "goracle" == data.get("driver"):
                    data_base_type = DataBaseType.Oracle.value
                elif "mysql" == data.get("driver"):
                    data_base_type = DataBaseType.Mysql.value
                else:
                    data_base_type = DataBaseType.SQL_Server.value
                datahub_params.append({"data_base_type": data_base_type, "db_code": data.get("db_code")})
        return datahub_params
    else:
        return result


def add_data_source(model, uncheck_connection=False):
    """
    添加数据源
    :param data_source.models.DataSourceModel model:
    :param uncheck_connection:
    :return bool:
    """
    model.id = seq_id()
    model.validate()
    model.conn_str_to_model()
    if not uncheck_connection:
        test_connection(model)
    check_data_source_code(model.code)

    fields = ['id', 'name', 'code', 'description', 'type', 'conn_str', 'is_buildin', 'icon', 'inspect_api',
              'user_source_id']
    if model.type == DataSourceType.MysoftNewERP.value:
        fields, model = format_mysoft_new_erp_model(fields, model)
        # 异构系统，不用校验app_level_code
        if model.data_from != MysoftNewERPDataFromType.NO_MYSOFT.value and model.conn_str.AppLevelCode:
            check_data_source_app_level_code(model.app_level_code)

    if isinstance(model.conn_str, ConnStrModel):
        if isinstance(model.conn_str, ADSDataConnStrModel):
            model.conn_str.version = get_ads_version(model.conn_str)
        model.conn_str.encrypt()
        model.conn_str = json.dumps(model.conn_str.get_dict())

    if model.type == DataSourceType.MysoftShuXin.value:
        shuxin_source = repository.get_one('data_source', {"type": DataSourceType.MysoftShuXin.value})
        if shuxin_source:
            raise UserError(message=f'{DataSourceType.MysoftShuXin.value}数据源已存在')
    if model.type == DataSourceType.MysoftShuXin15.value:
        shuxin15_source = repository.get_one('data_source', {"type": DataSourceType.MysoftShuXin15.value})
        if shuxin15_source:
            raise UserError(message=f'{DataSourceType.MysoftShuXin15.value}数据源已存在')

    try:
        result = repository.add_model('data_source', model, fields)

        # 指标模型数据源
        # 立即调一次同步
        # 发送新增事件
        # 添加project_to_shuxin记录
        if model.type == DataSourceType.MysoftShuXin.value:
            import app_celery

            PulsarApi().indicator_event_notify(event=0)
            shuxin_code = repository.get_data_scalar('project_to_shuxin', {"code": g.code}, "shuxin_code",
                                                     from_config_db=True)
            conn_str = json.loads(model.conn_str)
            if not shuxin_code:
                from dmplib.db.mysql_wrapper import get_db
                with get_db() as db:
                    db.insert("project_to_shuxin", data={'code': g.code, 'shuxin_code': conn_str.get('project_code')})
            elif shuxin_code and shuxin_code != conn_str.get("project_code"):
                raise UserError(message="暂不支持变更租户code")

            # 创建实例
            from dataset.services import indicator_service

            instance_id = indicator_service.create_flow_instance()
            # 触发异步任务
            app_celery.sync_indicator_model.apply_async(
                kwargs={'code': g.code, 'instance_id': instance_id}, queue='celery-slow'
            )
            logger.info("开始异步同步指标")

    except Exception as e:
        repository.delete_data("data_source", {'id': model.id})
        raise UserError(message=f"添加数据源失败:{str(e)}")
    event.trigger(event.AddDataSourceEvent, model)
    # 设置缓存
    data_source_meta_cache.set_data_source_cache(model.id, model.get_dict())
    return result


def format_mysoft_new_erp_model(fields: dict, model: DataSourceModel):
    """
    MysoftNewERP 类型的数据源model对象格式化
    :param fields:
    :param model:
    """
    # MysoftNewERP数据源字段格式化
    fields = format_mysoft_new_erp_fields(fields)
    if model.data_from != MysoftNewERPDataFromType.NO_MYSOFT.value and not model.conn_str.AppLevelCode:
        raise UserError(message='参数异常，子系统不能为空')

    # 三云获取api地址
    if model.conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
        # api连接方式
        if model.conn_str.SaasLinkType == MysoftNewERPCloudType.CLOUD_API.value:
            app_code = get_app_code(model.conn_str.AppLevelCode)
            server_url = get_mysoft_saas_server_url(app_code)
            if not server_url:
                raise UserError(message='没有配置对应移动产品的请求数据调用的api地址')
            model.conn_str.Server = server_url
            model.conn_str.DbType = MysoftNewERPDataBaseType.Cloud_Mysql.value
            model.conn_str.ConfigType = MysoftNewERPConfigType.CLOUD_MYSQL.value
        else:
            # 数据库连接方式
            model.conn_str.DbType = MysoftNewERPDataBaseType.Mysql.value
            model.conn_str.ConfigType = MysoftNewERPConfigType.MANUAL_INPUT.value

    # 异构系统默认的app_level_code设置
    if model.conn_str.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
        model.conn_str.AppLevelCode = "9000"
        model.conn_str.ConfigType = MysoftNewERPConfigType.MANUAL_INPUT.value

    # 默认值处理
    if model.conn_str.DbType == MysoftNewERPDataBaseType.SQL_Server.value and model.conn_str.Port is None:
        model.conn_str.Port = 1433
    if model.conn_str.AppId is None:
        model.conn_str.AppId = 'erp60'
    if model.conn_str.EnvironmentId is None:
        model.conn_str.EnvironmentId = 'test'
    if model.conn_str.FirstLevelCode == '1000' \
            and model.conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_ERP.value:
        model.conn_str.ConfigType = MysoftNewERPConfigType.CONFIG_CENTER.value

    # 默认为明源erp产品类型
    if not model.conn_str.DataFrom:
        model.conn_str.DataFrom = MysoftNewERPDataFromType.MYSOFT_ERP.value
    model.data_from = model.conn_str.DataFrom
    model.config_type = model.conn_str.ConfigType
    model.db_type = model.conn_str.DbType.lower()
    model.app_level_code = model.conn_str.AppLevelCode
    # 赋值Appcode
    if model.app_level_code.find('.') > -1:
        app_level_code = model.app_level_code.split('.')
        app_code = app_level_code[1]
    else:
        app_code = model.app_level_code
    model.app_code = model.conn_str.AppCode = app_code
    model.config_xml = model.conn_str.get_config_xml()
    return fields, model


def format_mysoft_new_erp_fields(fields: dict):
    """
    MysoftNewERP数据源字段格式化
    :param fields:
    """
    fields.extend(['config_type', 'config_xml', 'app_code', 'db_type', 'app_level_code',
                   'data_source_permission_type', 'data_from'])
    return fields


@data_permission_edit_filter('data_source-edit')
def update_data_source(model, uncheck_connection=False):
    """
    修改数据源
    :param data_source.models.DataSourceModel model:
    :param uncheck_connection:
    :return bool:
    """
    model.validate()
    model.conn_str_to_model()
    if not uncheck_connection:
        test_connection(model)
    check_data_source_code(model.code, model.id)

    fields = ['name', 'code', 'description', 'conn_str', 'icon', 'inspect_api', 'third_party_id']
    if model.type == DataSourceType.MysoftNewERP.value:
        fields, model = format_mysoft_new_erp_model(fields, model)
        # 异构系统，不用校验app_level_code
        if model.data_from != MysoftNewERPDataFromType.NO_MYSOFT.value and model.app_level_code:
            check_data_source_app_level_code(model.app_level_code, model.id)
        if model.app_level_code == '1000.1401' and model.conn_str.is_master_local_db == 1:
            sync_erp_info_to_project(model.conn_str.erp_api_info_id)

    if model.type == DataSourceType.MysoftShuXin.value:
        try:
            conn_str = repository.get_data_scalar('data_source', {"id": model.id}, col_name='conn_str')
            if conn_str:
                conn_str = json.loads(conn_str)
                origin_code = conn_str.get('project_code')
                if origin_code != model.conn_str.project_code:
                    repository.delete_data("project_to_shuxin", {"code": g.code}, from_config_db=True)
                    # # 取消原绑定的回调
                    # PulsarApi().indicator_event_notify(event=1)
                    # 添加新的回调
                    add_indicator_event_notify(model)
                else:
                    data = repository.get_one(
                        'project_to_shuxin',
                        {'shuxin_code': model.conn_str.project_code, 'code': g.code},
                        from_config_db=True
                    )
                    add_indicator_event_notify(model, is_need=not bool(data))
            # 清空数芯数据源缓存
            RedisCache().delete(key="MysoftShuXin:data_source")

        except Exception as e:
            logger.error(f"update mysoftshuxin datasource error: {e}")
            raise UserError(message=f'更新数芯数据源失败：{e}')
    if model.type == DataSourceType.MysoftShuXin15.value:
        # 清空数芯数据源缓存
        RedisCache().delete(key="MysoftShuXin15:data_source")
    if isinstance(model.conn_str, ConnStrModel):
        if isinstance(model.conn_str, ADSDataConnStrModel):
            model.conn_str.version = get_ads_version(model.conn_str)
        model.conn_str.encrypt()
        model.conn_str = json.dumps(model.conn_str.get_dict())
    if model.type == DataSourceType.API.value:
        fields.append('db_type')
    result = repository.update_model('data_source', model, {'id': model.id}, fields)
    # 设置缓存
    data_source_meta_cache.del_data_source_cache(model.id)
    data_source_meta_cache.get_data_source_cache(model.id)
    event.trigger(event.UpdateDataSourceEvent, model)
    return result, model.name


def add_indicator_event_notify(model: DataSourceModel, is_need=True):
    # 添加新的回调
    PulsarApi(
        _from='test_connect',
        app_key=model.conn_str.app_id,
        app_secret=model.conn_str.app_secret,
        host=model.conn_str.api_host,
        pulsar_key=model.conn_str.app_key,
        pulsar_code=model.conn_str.project_code
    ).indicator_event_notify(event=0)
    if is_need:
        repository.add_data(
            "project_to_shuxin",
            {'shuxin_code': model.conn_str.project_code, 'code': g.code},
            from_config_db=True
        )


@data_permission_edit_filter('data_source-edit')
def delete_data_source(data_source_id):
    """
    删除数据源
    :param data_source_id:
    :return:
    """
    reference = data_source_repository.query_reference(data_source_id)
    data_source = get_data_source(data_source_id, safe_mode=True)
    if not data_source:
        raise UserError(message='数据源不存在')
    elif int(data_source.is_buildin) == 1:
        raise UserError(message='内置数据源不允许删除')
    elif reference:
        dataset_names = ','.join(reference)
        raise UserError(message='数据源被数据集：{}使用，不允许删除'.format(dataset_names))
    else:
        result = repository.delete_data('data_source', {'id': data_source_id})
        if data_source.type == DataSourceType.MysoftNewERP.value:
            delete_keyword_by_datasource_id(data_source_id)
        if data_source.type == DataSourceType.MysoftShuXin.value:
            from components.pular_api import PulsarApi

            repository.delete_data("project_to_shuxin", {"code": g.code}, from_config_db=True)
            if not repository.get_data("project_to_shuxin", {'shuxin_code': data_source.conn_str.project_ocde}):
                # 取消回调
                PulsarApi(
                    _from='del_source',
                    app_key=data_source.conn_str.api_id,
                    app_secret=data_source.conn_str.app_secret,
                    host=data_source.conn_str.api_host,
                    pulsar_key=data_source.conn_str.app_key,
                    pulsar_code=data_source.conn_str.project_ocde
                ).indicator_event_notify(event=1)
        # 删除缓存
        data_source_meta_cache.del_data_source_cache(data_source_id)
        event.trigger(event.DelDataSourceEvent, data_source_id)
        return result, data_source.name


def get_data_source(
        data_source_id: str, data_source_code: None = None, safe_mode: Optional[bool] = None
) -> DataSourceModel:
    """
    获取数据源
    :param str data_source_id:
    :param str data_source_code:
    :param bool safe_mode: 安全模式下所有密码属性为空
    :return data_source.models.DataSourceModel:
    """
    fields = [
        'id', 'name', 'code', 'description', 'type', 'conn_str', 'is_buildin', 'icon',
        'inspect_api', 'user_source_id', 'third_party_id'
    ]
    fields = format_mysoft_new_erp_fields(fields)
    data = repository.get_data(
        'data_source', {'code': data_source_code} if data_source_code else {'id': data_source_id}, fields
    )
    if not data:
        return None
    model = DataSourceModel(**data)
    load_data_source_conn_str(model, safe_mode)
    return model


def get_data_source_list_by_type(type: None = None, safe_mode: Optional[bool] = None) -> DataSourceModel:
    """
    获取数据源
    :param str type:
    :param bool safe_mode:
    :return data_source.models.DataSourceModel:
    """
    fields = [
        'id', 'name', 'code', 'description', 'type', 'conn_str', 'is_buildin', 'icon',
        'inspect_api', 'user_source_id', 'third_party_id'
    ]
    fields = format_mysoft_new_erp_fields(fields)
    datas = repository.get_list('data_source', {'type': type})
    if not datas:
        return None

    data_source_list = []
    for data in datas:
        model = DataSourceModel(**data)
        load_data_source_conn_str(model, safe_mode)
        data_source_list.append(model)
    return data_source_list


def get_data_source_by_id(data_source_id: str) -> dict:
    return repository.get_data("data_source", {"id": data_source_id})


def load_data_source_conn_str(model: DataSourceModel, safe_mode: Optional[bool] = None) -> None:
    """
    加载数据源连接信息（包括系统内置数据源）
    :param data_source.models.DataSourceModel model:
    :param bool safe_mode: 安全模式下所有密码属性为空
    :return:
    """
    is_buildin = int(model.is_buildin) == 1
    if is_buildin:
        if model.id == DATA_SOURCE_ODPS_ID:
            model.conn_str = ODPSConnStrModel(**get_odps_config())
        elif model.id == DATA_SOURCE_RDS_ID:
            model.conn_str = MysqlConnStrModel(**get_data_db_config())
        elif model.id == DATA_SOURCE_DMP_ID:
            model.conn_str = MysqlConnStrModel(**get_db_config())
        else:
            model.conn_str = ConnStrModel()
    else:
        # 内置MysoftNewERP数据源，连接信息为空。通过表字段构建为 MysoftNewERPConnStrModel
        if model.type == DataSourceType.MysoftNewERP.value:
            if model.conn_str == '':
                model = mysoft_new_erp_conn_str_is_none(model)
            else:
                # DataFrom 字段后来添加，需要手动加入conn_str
                conn_str_dict = json.loads(model.conn_str)
                if "DataFrom" not in conn_str_dict or conn_str_dict["DataFrom"] == '':
                    conn_str_dict["DataFrom"] = model.data_from
                model.conn_str = conn_str_dict
                model.conn_str_to_model()
        else:
            model.conn_str = json.loads(model.conn_str)
            model.conn_str_to_model()
    # 内置数据源已经实现加密，这里的 not is_buildin 条件则可以移除
    if not is_buildin and not safe_mode:
        model.conn_str.decrypt()
    if hasattr(model.conn_str, 'clear_safe_attributes') and safe_mode:
        model.conn_str.clear_safe_attributes()
    model.config_xml = None


def mysoft_new_erp_conn_str_is_none(model: DataSourceModel):
    """
    如果MysoftNewERP 数据源的conn_str为空，则构建
    :param model:
    :return:
    """
    if model.type == DataSourceType.MysoftNewERP.value and model.conn_str == '':
        if model.conn_str == '':
            app_level_code = model.app_level_code
        first_level_code = ""
        if app_level_code.find('.') > -1:
            codes = app_level_code.split('.')
            first_level_code = codes[0]
        config_xml = model.config_xml
        config_dict = {}
        if config_xml:
            config_xml_dict = xmltodict.parse(config_xml)
            config_dict = config_xml_dict.get("Config")

        new_erp_dict = {"AppCode": model.app_code, "AppLevelCode": model.app_level_code,
                        "ConfigType": model.config_type, "DbType": model.db_type,
                        "FirstLevelCode": first_level_code, "DataFrom": model.data_from}
        # 手工录入
        if model.config_type == MysoftNewERPConfigType.MANUAL_INPUT.value:
            new_erp_dict['Server'] = config_dict.get("Server")
            new_erp_dict['Database'] = config_dict.get("Database")
            new_erp_dict['Uid'] = config_dict.get("Uid")
            new_erp_dict['Pwd'] = config_dict.get("Pwd")
        elif model.config_type == MysoftNewERPConfigType.CONFIG_CENTER.value:
            # 配置中心
            new_erp_dict['AppId'] = config_dict.get("AppId")
            new_erp_dict['SiteGroupKey'] = config_dict.get("SiteGroupKey")
            new_erp_dict['EnvironmentId'] = config_dict.get("EnvironmentId")
        model.conn_str = MysoftNewERPConnStrModel(**new_erp_dict)
    return model


@data_permission_filter('data_source-view')
def get_data_source_list(
        model: DataSourceQueryModel,
) -> Dict[
    str, Union[int, List[Union[Dict[str, Union[str, int, datetime]], Dict[str, Union[str, None, int, datetime]]]]]
]:
    """
    获取数据源列表
    :param data_source.models.DataSourceQueryModel model:
    :return tuple:
    """
    source_list = data_source_repository.get_data_source_list(model).get_result_dict()
    dataset_edit_config = repository.get_data(
        'dataset_edit_config',
        {'enable_mysql_tenantS like': f'%{g.code}%'}, ['map_datasource_ids'],
        from_config_db=True
    )
    if dataset_edit_config:
        map_datasource_ids = json.loads(dataset_edit_config.get('map_datasource_ids'))
        ids = list(map_datasource_ids.keys()) + list(map_datasource_ids.values())
        items = source_list.get('items') or []
        relation_list = deepcopy([i for i in items if i.get('id') in ids])
        for item in items:
            if item.get('id') in ids:
                item['relation_list'] = relation_list

    for source in source_list.get("items"):
        # 数芯数据源 数据服务中心数据源 数据集为is_tree
        source['is_tree'] = True if source.get("type") == "DataSet" or (
                source.get("type") == "MysoftNewERP" and source.get("app_code") == "1401") \
                                    or source.get("type") == "MysoftShuXin15" else False
    return source_list


def check_data_source_code(code, data_source_id=None):
    """
    检查code
    :param code:
    :param data_source_id:
    :return bool:
    """
    is_exists = repository.data_is_exists(
        'data_source', {'code': code}, {'id': data_source_id} if data_source_id else None
    )
    if is_exists:
        raise UserError(message='数据源编码“' + code + '”已经存在')
    return True


def check_data_source_app_level_code(app_level_code, data_source_id=None):
    """
    MysoftNewERP 检测产品对应数据源是否已存在
    :param app_level_code:
    :param data_source_id:
    :return bool:
    """
    return True
    # if app_level_code:
    #     is_exists = repository.data_is_exists(
    #         'data_source', {'app_level_code': app_level_code}, {'id': data_source_id} if data_source_id else None
    #     )
    #     if is_exists:
    #         raise UserError(message='该产品(' + app_level_code + ')对应的数据源已经存在')
    # return True


def get_tables(query_model: TableQueryModel):
    """
    获取数据表
    :param data_source.models.TableQueryModel query_model:
    :return:
    """
    if query_model.id in [DATA_SOURCE_DMP_ID, DATA_SOURCE_RDS_ID]:
        return query_model
    query_model.validate()
    init_table_query_model(query_model)
    need_cache, cache_data = first_table_cache(query_model)
    if cache_data and need_cache:
        return cache_data
    return get_client(query_model.data_source.type).get_tables(query_model)


def first_table_cache(query_model: TableQueryModel):
    need_cache = False
    result = None
    if query_model.page == 1 and not query_model.keyword and query_model.page_size:
        need_cache = True
        cache = redis_conn()
        key = "table_list_cache:data_source_id:{}:page_size:{}".format(query_model.id, query_model.page_size)
        result = cache.get(key) or ''
        if result:
            result = pickle.loads(result)
        else:
            result = get_client(query_model.data_source.type).get_tables(query_model) or ''
            data = pickle.dumps(result)
            cache.set(key, data, 300)  # 缓存5分钟 不可能等一个小时
    return need_cache, result


def get_table_columns(query_model):
    """
    获取数据表字段
    :param data_source.models.ColumnQueryModel query_model:
    :return:
    """
    query_model.validate()
    init_table_query_model(query_model)
    query_model = get_client(query_model.data_source.type).get_table_columns(query_model)
    # 后端统一处理 字段类型 和分组信息
    if query_model.items:
        for item in query_model.items:
            item['group_name'] = item.get('group_name', '')
            dim_type = item.get('dim_type', None)
            if dim_type:
                item['field_group'] = '度量' if dim_type == 'indicator' else '维度'
                continue
            col_type = (
                item.get("type")[0: item.get("type").find("(")]
                if item.get("type") and item.get("type").find("(") != -1
                else item.get("type")
            )

            item["field_group"] = db_engine_transform.get_dmp_field_group(col_type)
    return query_model


def get_table_column_values(query_model):
    """
    获取数据表字段值
    :param data_source.models.ColumnValueQueryModel query_model:
    :return:
    """
    query_model.validate()
    init_table_query_model(query_model)
    return get_client(query_model.data_source.type).get_table_column_values(query_model)


def create_table(model):
    """
    创建数据表
    :param data_source.models.CreateTableModel model:
    :return:
    """
    model.validate()
    init_table_query_model(model)
    return get_client(model.data_source.type).create_table(model)


def test_mysql_connection(**kwargs):
    """
    测试mysql数据源
    :param kwargs:
    :return:
    """
    conn = None
    try:
        conn = pymysql.connect(**kwargs)
    except pymysql.Error as e:
        raise UserError(message=kwargs.get('db', '') + ' db connection failed' + str(e))
    finally:
        if conn:
            conn.close()
    return True


def init_table_query_model(query_model: TableQueryModel) -> None:
    """
    初始数据表查询Model
    :param data_source.models.TableQueryModel query_model:
    :return:
    """
    model = get_data_source(query_model.id, safe_mode=False)
    if not model:
        raise UserError(message='数据源不存在')
    query_model.data_source = model
    query_model.data_source.conn_str_to_model()


def get_project_odps_instance():
    """
    获取当前项目ODPS API 实例
    :return odps.core.ODPS:
    """
    return get_client(DataSourceType.ODPS.value).get_odps(ODPSConnStrModel(**get_odps_config()))


def check_high_data_source_exists():
    record = repository.get_data('data_source', {'type': DataSourceType.HighData.value}, multi_row=True)
    return bool(record)


def is_api_datasource(subject_id: str) -> bool:
    """
    是否是api数据源
    :param subject_id:
    :return:
    """
    item = repository.data_is_exists("data_source", {"id": subject_id, "type": "API"})
    if item:
        return True
    return False


def get_reference_datasets(data_source_id: str) -> list:
    """
    获取依赖数据源的数据集
    :param data_source_id:
    :return:
    """
    return data_source_repository.query_reference(data_source_id)


def get_erp_apps(data_source_type, erp_api_info_id=''):
    """
    获取erp的子系统产品列表，增加FirstLevelCode节点
    :return:
    """
    adapter = get_client(data_source_type)
    apps_list = adapter.get_erp_app_list(erp_api_info_id=erp_api_info_id)
    for item in apps_list:
        app_level_code = item.get('LevelCode')
        first_level_code = app_level_code
        if app_level_code.find('.') > -1:
            codes = app_level_code.split('.')
            first_level_code = codes[0]
        item['FirstLevelCode'] = first_level_code
    return apps_list


def get_erp_env(data_source_type, erp_api_info_id=''):
    """
    获取erp的环境标识
    :return dict:
    """
    adapter = get_client(data_source_type)
    return adapter.get_erp_env(erp_api_info_id=erp_api_info_id)


def get_saas_orgcode_and_secret(model: DataSourceModel):
    """
    获取MysoftNewERP数据源三云MysoftSaaS的企业代码和秘钥
    :param model:
    :return:
    """
    adapter = get_client(DataSourceType.MysoftNewERP.value)
    if hasattr(adapter, "get_saas_orgcode_and_secret"):
        return adapter.get_saas_orgcode_and_secret(model.conn_str)


def get_data_soruce_show_config():
    """
    获取当前租户数据源显示配置
    :return:
    """
    env_sign = get_dmp_env_sign(project_code=g.code)
    storage_type = get_storage_type(project_code=g.code)
    data_source_show_config = repository.get_list('data_source_show_config',
                                                  {"env_sign_code": env_sign, "storage_type": storage_type},
                                                  order_by="order_num asc", from_config_db=True)
    return data_source_show_config
    # result = {}
    # if data_source_show_config:
    #     result['env_sign_code'] = data_source_show_config[0].get('env_sign_code')
    #     result['env_sign_name'] = data_source_show_config[0].get('env_sign_name')
    #     result['storage_type'] = data_source_show_config[0].get('storage_type')
    #     data_source_group = dict()
    #     for config in data_source_show_config:
    #         if config.get('data_source_group') in data_source_group.keys():
    #             data_source_group[config.get('data_source_group')].append({"data_source_type_code":})


def edit_code_of_data_source(data_source_id: str, tenant_code: str):
    """
    为datasource.conn_str添加tenant_code
    :param data_source_id:
    :param tenant_code:
    :return:
    """
    # 更新data_source.conn_str.tenant_code
    data_source = repository.get_data("data_source", {"id": data_source_id}, fields=["conn_str", "type"])
    if not data_source:
        raise UserError(message="数据源不存在")
    if data_source.get("type") != DataSourceType.API.value:
        raise UserError(message="只能为api数据源添加code")
    conn_str = json.loads(data_source.get("conn_str"))
    conn_str["tenant_code"] = tenant_code
    repository.update("data_source", {"conn_str": json.dumps(conn_str)}, {"id": data_source_id})
    # 设置数据源缓存
    data_source_meta_cache.del_data_source_cache(data_source_id)
    data_source_meta_cache.get_data_source_cache(data_source_id)


def get_datacenter_list():
    """
    获取数据服务中心数据源列表
    :return:
    """
    return repository.get_list(
        'data_source', {'app_level_code': '1000.1401'}, ['id', 'name']
    )


def get_erp_api_info():
    fields = ['id', 'erp_api_host', 'erp_api_access_id', 'erp_api_access_secret', 'erp_api_type']
    info_list = repository.get_list("erp_api_info", {}, fields=fields) or []
    erp_info_list = []
    for i in info_list:
        if i.get("erp_api_host"):
            erp_info_list.append(i)
    return erp_info_list


def handel_history_erp_api():
    """
    处理租户历史的接口管家配置
    """
    fields = ['id', 'code', 'erpapi_host', 'erpapi_access_id', 'erpapi_access_secret',
              'local_dataset_clean_max_time_type']
    project = repository.get_one('project', {'code': g.code}, fields, from_config_db=True)
    data = get_erp_api_info()
    if data:
        return
    if not project.get('erpapi_host'):
        return
    data_source = repository.get_one('data_source', {'app_level_code': '1000.1401'}, ['id', 'db_type', 'conn_str'])
    if not data_source:
        return
    db_type = data_source.get('db_type')
    source_id = data_source.get('id')
    conn_str = data_source.get('conn_str')
    conn_str = json.loads(data_source.get('conn_str')) if conn_str else {}
    erp_api_id = ErpApiId.SAAS.value if db_type.lower() == 'mysql' else ErpApiId.OP.value
    erp_api_info = {
        'id': erp_api_id, 'erp_api_host': project.get('erpapi_host'),
        'erp_api_access_id': project.get('erpapi_access_id') or '',
        'erp_api_access_secret': project.get('erpapi_access_secret') or '',
        'erp_api_type': ErpApiType.SAAS.value if db_type.lower() == 'mysql' else ErpApiType.OP.value,
        'local_dataset_clean_max_time_type': project.get('local_dataset_clean_max_time_type')
    }
    if conn_str:
        conn_str['erp_api_info_id'] = erp_api_id
        conn_str['is_master_local_db'] = 1
    conn_str = json.dumps(conn_str, ensure_ascii=False)
    repository.update('data_source', {'conn_str': conn_str}, {'id': source_id})
    repository.replace_list_data('erp_api_info', [erp_api_info], list(erp_api_info.keys()))
    # 清除数据源缓存
    key = '%s:%s' % ('data_source:', source_id)
    cache = redis_conn()
    cache.del_data(key)
    # 绑定所有的MysoftNewERP数据源
    handel_history_data_source(erp_api_id)


def handel_history_data_source(erp_info_id):
    data_source_list = repository.get_list('data_source', {'type': 'MysoftNewERP', 'app_level_code!=': '1000.1401'},
                                           ['id', 'conn_str'])
    for data_source in data_source_list:
        if data_source.get('conn_str'):
            source_id = data_source.get('id')
            conn_str = json.loads(data_source.get('conn_str'))
            conn_str['erp_api_info_id'] = erp_info_id
            conn_str = json.dumps(conn_str, ensure_ascii=False)
            repository.update('data_source', {'conn_str': conn_str}, {'id': source_id})
            # 清除数据源缓存
            key = '%s:%s' % ('data_source:', source_id)
            cache = redis_conn()
            cache.del_data(key)


def sync_erp_info_to_project(erp_api_id: str):
    if not erp_api_id:
        return
    from dmplib.saas.project import get_db
    with get_db() as project_db:
        sql = """select erp_api_host as erpapi_host, erp_api_access_id as erpapi_access_id,
         erp_api_access_secret as erpapi_access_secret from erp_api_info where id = '{}'""".format(erp_api_id)
        data = project_db.query_one(sql)
        if data:
            from dmplib.db import mysql_wrapper
            with mysql_wrapper.get_db() as db:
                db.update('project', data, {'code': g.code})


def import_tables(model: TableQueryModel):
    table_list = get_tables(model).get_result_dict()
    if table_list['items']:
        used_table = get_imported_table(model.id)
        if model.data_source.type == DataSourceType.MysoftNewERP.value and model.data_source.app_code == '1401':
            return trans_import_tables_mysoftnewerp(table_list.get('items', []), used_table)
        if model.data_source.type == DataSourceType.MysoftShuXin15.value:
            return trans_import_tables_shuxin(table_list.get('items', []), used_table, filter_view=True)
        items = table_list.get('items', [])
        for idx, item in enumerate(items):
            if item['name'] in used_table:
                item['is_used'] = True
            else:
                item['is_used'] = False
            item['level_type'] = 'table'
            item['id'] = item['name']
            item['parent_id'] = ''
        return items
    return table_list


def trans_import_tables_shuxin(items, used_table, code='', filter_view=False):
    new_list = []
    for idx, item in enumerate(items):
        if item['name'] and item['name'] in used_table:
            item['is_used'] = True
        else:
            item['is_used'] = False
        item['name'] = item['name_cn'] if not item['name'] else item['name']
        item['level_type'] = 'table' if item['type'] in ('dim', 'dwd', 'dws', 'dws_view', 'ods') else 'folder'
        item['id'] = item['code']
        item['parent_id'] = code
        if filter_view and item['type'].upper() == "DWS_VIEW".upper():  # 过滤dws 指标视图的引入
            continue
        item['table_type'] = MysoftShuXinTableType[item['type'].upper()].value if item[
                                                                                      'type'].upper() in MysoftShuXinTableType.__members__ else \
        item['type'].upper()

        item['comment'] = item['name_cn']
        children = item.get('children', [])
        del item['children']
        new_list.append(item)
        if len(children) > 0:
            new_list.extend(trans_import_tables_shuxin(children, used_table, item['code'], filter_view))
    return new_list


def trans_import_tables_mysoftnewerp(items, used_table):
    new_list = []
    add_folder = []
    for idx, item in enumerate(items):
        if not item.get('table_type'):  # 移除基础表 只保留宽表
            continue
        if item['name'] in used_table:
            item['is_used'] = True
        else:
            item['is_used'] = False
        item['level_type'] = 'table'
        item['id'] = item['name']
        item['parent_id'] = item.get('tablebusinesstypename', '') + item.get('table_type', '')
        if item['parent_id'] not in [e.get('id') for e in add_folder]:
            add_folder.append(
                {'id': item['parent_id'], 'parent_id': item.get('tablebusinesstypename', ''),
                 'name': item.get('table_type', ''), 'level_type': 'folder', 'comment': ''})
            if item.get('tablebusinesstypename', '') not in [e.get('id') for e in add_folder]:
                add_folder.append(
                    {'id': item.get('tablebusinesstypename', ''), 'parent_id': '',
                     'name': item.get('tablebusinesstypename', ''), 'level_type': 'folder', 'comment': ''})
        new_list.append(item)
    new_list.extend(add_folder)
    return new_list


def get_imported_table(data_source_id: str):
    list_data = dataset_repository.get_dataset_by_source(data_source_id)
    table_name = []
    for dataset in list_data:
        if dataset.get('is_import_table'):
            table_name.append(dataset.get('import_table_name'))
    return table_name
