import requests
import json
from dmplib.hug import g
from base.errors import UserError
from base import repository
from data_source.models import DataSourceModel


def get_env_data_source(env_url, code):
    if not code:
        raise UserError(message='租户code不能为空')
    if not env_url:
        raise UserError(message='环境地址不能为空')
    data_source = env_data_source(env_url, {'code': code})
    return data_source


def env_data_source(host, params: dict):
    """
    @param host:
    @param params:
    @return:
    """
    host = host[: len(host) - 1] if host.endswith('/') else host
    url = '%s/%s/%s' % (host, 'api', 'data_source/get_data_source_by_code')
    response = requests.post(url, json=params, timeout=60)
    if response.status_code != 200:
        raise UserError(message=' 状态：' + str(response.status_code) + ' , ' + response.reason)
    try:
        data = json.loads(response.text)
    except Exception as e:
        raise UserError(message=f'json loads error: {str(e)}, response.status: {response.status_code}, response.text: {response.text}')
    if data.get('result'):
        return data.get('data')
    else:
        raise UserError(message=data.get('msg'))


def get_data_source_by_url(url: str, code: str):
    url = '''{}{}'''.format(url.rstrip('/'), '/api/data_source/get_data_source_list_by_code')
    params = {'code': code}
    response = requests.post(url, json=params, timeout=60)
    if response.status_code == 200:
        result = response.json() if response.text else {'result': 0, 'msg': '接口数据返回为空'}
        if not result.get('result'):
            raise UserError(message=result.get('msg'))
        return result.get('data')
    return '调用对应环境接口失败，请重试'


def get_data_source():
    return repository.get_list('data_source', {})


def import_data_source_model(model: DataSourceModel, code=None):
    if model.conn_str:
        conn_dict = json.loads(model.conn_str)
        if conn_dict:
            conn_dict['tenant_code'] = code
        model.conn_str = json.dumps(conn_dict)
    data = model.get_dict()
    fields = list(data.keys())
    return repository.replace_list_data('data_source', [data], fields)
