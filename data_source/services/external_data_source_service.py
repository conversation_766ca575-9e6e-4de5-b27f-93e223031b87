import json
from data_source.models import MysqlConnStrModel, DataSourceModel
from base.errors import UserError
from base.enums import DataSourceType
from base import repository
from dmplib.db.mysql_wrapper import get_db as get_config_db, ConnectException
from dmplib.hug import g
from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id


def update_mysql_data_source_conn_str(data: dict):
    if not isinstance(data, dict):
        raise UserError(message='数据格式错误:参数为应该为json对象类型，租户->租户更新信息(json对象类型)')

    validate_input_data(data)

    with get_config_db() as db:
        project_codes = db.query_columns('SELECT code from project')
    project_codes = set(project_codes or [])

    result = {}
    for tenant_code, update_data in data.items():
        if tenant_code not in project_codes:
            result[tenant_code] = {'result': False, 'msg': '该租户不存在'}
        else:
            result[tenant_code] = update_tenant_data_source_conn_str(tenant_code, update_data)
    return result


def update_tenant_data_source_conn_str(tenant_code: str, update_data: dict):
    try:
        with get_db(tenant_code) as db:
            records = db.query('select * from data_source')
            data_sources_mapping = {r['code']: r for r in records}

            result = []
            for data_source_code, data in update_data.items():
                try:
                    name, description = data.pop('name'), data.pop('description', '')
                    conn_str_model = MysqlConnStrModel(**data)
                    conn_str_model.validate()
                    conn_str_model.encrypt()

                    replace_data = {'name': name, 'conn_str': json.dumps(conn_str_model.get_dict())}
                    if description:
                        replace_data['description'] = description

                    if data_source_code in data_sources_mapping:
                        if data_sources_mapping[data_source_code]['type'] == DataSourceType.Mysql.value:
                            db.update('data_source', replace_data, condition={'code': data_source_code})
                            result.append({'result': '更新成功', 'code': data_source_code})
                        else:
                            result.append({'result': '失败：数据源编码已存在，但不是MySQL类型，不更新', 'code': data_source_code})
                    else:
                        replace_data.update(
                            {'id': seq_id(), 'code': data_source_code, 'type': DataSourceType.Mysql.value}
                        )
                        db.insert('data_source', replace_data)
                        result.append({'result': '新增成功', 'code': data_source_code})
                except Exception:
                    result.append({'result': '失败', 'code': data_source_code})

            return {'result': result, 'msg': '成功'}

    except ConnectException:
        return {'result': False, 'msg': '连接租户库DB失败'}

    except Exception:
        return {'result': False, 'msg': '更新失败，未知错误'}


def validate_input_data(data):
    required_fields = ['name', 'database', 'host', 'user', 'password', 'port']
    for tenant_code, update_data in data.items():
        if not isinstance(update_data, dict):
            raise UserError(message='数据格式错误:单个租户相应的更新信息应该json对象类型，数据源编码->数据源连接信息(json对象类型)')

        for data_source_code, source_data in update_data.items():
            if not isinstance(source_data, dict):
                raise UserError(message='数据源连接信息应为json对象类型')
            for field in required_fields:
                if field not in source_data:
                    raise UserError(message='数据源信息：name,database,host,user,password,port必填')


def replace_into_mysql_data_source(data: dict):
    """
    替换 或 新增MYSQL数据源的连接信息
    :param data:
    :return:
    """
    tenant_code = data.get("tenant_code")
    if not tenant_code:
        raise UserError(message="企业代码不能为空")

    with get_config_db() as cdb:
        sql = "SELECT code FROM project WHERE code=%s"
        project_code = cdb.query_scalar(sql, (tenant_code,))
        if not project_code:
            raise UserError(message='企业代码无效')
    g.code = tenant_code
    data_source = data.get("data_source")
    if not data_source:
        raise UserError(message='数据源信息不能为空')
    required_fields = ["id", "code", "name", "database", "host", "user", "password", "port"]
    for required_field in required_fields:
        if not data_source.get(required_field):
            raise UserError(message='数据源信息{}不能为空'.format(required_field))
    if len(data_source.get("id")) > 36:
        raise UserError(message='数据源信息id长度不能超过36')
    with get_db(tenant_code) as db:
        old_data = db.query("select id, code from data_source where code =%(code)s ", {"code": data_source.get("code")})
        if old_data:
            for old_data in old_data:
                if old_data.get("code") == data_source.get("code") and old_data.get("id") != data_source.get("id"):
                    raise UserError(message='数据源编码已存在')
        conn_str_model = MysqlConnStrModel(**data_source)
        conn_str_model.validate()
        conn_str_model.encrypt()
        data_source_model = DataSourceModel(**data_source)
        data_source_model.type = DataSourceType.Mysql.value
        data_source_model.conn_str = json.dumps(conn_str_model.get_dict())
        data_source_model.validate()
        replace_data = data_source_model.get_dict()
        fields = list(replace_data.keys())
        return db.replace_multi_data("data_source", [replace_data], fields)


def get_app_code(app_level_code: str):
    """
    获取app_level_code的后面一个值
    :param app_level_code:
    :return:
    """
    if app_level_code and app_level_code.find(".") != -1:
        code_list = app_level_code.split('.')
        return code_list[1]
    return app_level_code


def get_mysoft_saas_server_url(app_code):
    """
    获取明源移动产品的api地址
    :param app_code:
    :return:
    """
    server_url = ""
    mysoft_saas_info = repository.get_one('cloudmysql_app_serverinfo', {"app_code": app_code}, fields=['server_url'],
                                          from_config_db=True)
    if mysoft_saas_info:
        server_url = mysoft_saas_info.get('server_url')
    return server_url