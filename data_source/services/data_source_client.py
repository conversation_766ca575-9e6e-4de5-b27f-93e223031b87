#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2017/3/27.
"""
import json
import re
import traceback
from loguru import logger

import psycopg2
import pymysql
from pyhive import trino
from urllib.parse import urlparse
from odps.core import ODPS
from odps.errors import ODPSError
from odps.models.table import TableSchema
from psycopg2.extras import RealDictCursor
from sshtunnel import SSHTunnelForwarder, BaseSSHTunnelForwarderError

from base.enums import (
    DataSourceType,
    DBEngine,
    MysoftNewERPDataFromType,
    MysoftNewERPDataBaseType,
    MysoftNewERPCloudType,
    MysoftNewERPConfigType, DatasetVarVarType, DatasetVarValueType,
)
from base import repository
from components.data_hub import CollectAPI
from components.global_utils import validate_key_word
from components.mysoft import CloudAPI
from components.external_api import ExternalAPI
from components.oss import OSSFileProxy
from components import db_engine_transform, auth_util
from components.pular15_api import Pulsar15Api
from components.query_models import QueryStructure
from components.remove_comment import remove_comment
from components.orm import OrmDb, OrmConnection
from data_source.repositories import presto_data_source_repository
from components.data_center_api import request_data_center, get_data_by_sql
from components.erpapi_manager_api import get_corpcode
from data_source.repositories import mysql_data_source_repository, saas_data_source_repository
from data_source.repositories import postgresql_data_source_repository, mssql_data_source_repository
from data_source.repositories import highdata_data_source_repository, ads_data_source_repository
from data_source.enums.mysoft_new_erp_enums import MysoftNewERPAPI, MysoftNewERPTableSql
from data_source.services.external_data_source_service import get_app_code, get_mysoft_saas_server_url
from dmplib.constants import LOOPBACK_ADDRESS
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from data_source.models import PrestoConnStrModel
from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib.saas.project import get_mysoft_erp
from dmplib.redis import RedisCache

from data_source.models import HighDataConnStrModel, ADSDataConnStrModel, MysoftNewERPConnStrModel

from data_source.models import APIConnStrModel, TableQueryModel, ColumnQueryModel, DataSourceModel, \
    MysoftShuXinConnStrModel
from typing import Union, List
from components.pular_api import PulsarApi
from dmplib.utils.strings import seq_id


class DataSourceClient:

    @staticmethod
    def test_connection(model):
        """
        :param data_source.models model:
        :return:
        """
        pass

    @staticmethod
    def exec_sql(model, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        pass

    @staticmethod
    def get_tables(query_model):
        """
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        pass

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        pass

    @staticmethod
    def get_table_column_values(query_model):
        """
        获取数据表字段值
        :param data_source.models.ColumnValueQueryModel query_model:
        :return:
        """

    @staticmethod
    def create_table(model):
        """
        获取数据表字段值
        :param data_source.models.CreateTableModel model:
        :return:
        """


class MSSQLDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model):
        """
        测试数据库连接
        :param data_source.models.MsSqlConnStrModel model:
        :return bool:
        """
        model.host = model.host.replace(',', ':')
        connection = OrmConnection(**model.get_dict())
        orm_db = OrmDb(connection)
        orm_db.test_connection()

    @staticmethod
    def get_db(model):
        model.host = model.host.replace(',', ':')
        connection = OrmConnection(**model.get_dict())
        orm_db = OrmDb(connection)
        return orm_db.get_db()

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        with MSSQLDataSource.get_db(model.conn_str) as db:
            return OrmDb.data_format(db.execute(sql))

    @staticmethod
    def create_table(model):
        """
        创建数据表
        :param data_source.models.CreateTableModel model:
        :return:
        """
        try:
            return mysql_data_source_repository.create_table(model)
        except pymysql.Error as e:
            raise UserError(message='创建表错误：' + str(e))

    @staticmethod
    def get_tables(query_model):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        try:
            return mssql_data_source_repository.get_tables(query_model)
        except Exception as e:
            raise UserError(message='获取数据表错误：' + str(e))

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        try:
            query_model = mssql_data_source_repository.get_table_columns(query_model)
            return handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.MSSQL.value)
        except Exception as be:
            raise UserError(message='获取数据表字段错误：' + str(be))

    @staticmethod
    def data_format(data):
        return OrmDb.data_format(data)


class MySQLDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model):
        """
        测试mysql数据库连接
        :param data_source.models.MysqlConnStrModel model:
        :return bool:
        """
        ssh = None
        conn = None

        # 若使用gunicorn  gevent模式，MySQLdb 使用ssh 通道不行，改用pymysql方式

        if model.use_ssh:
            try:
                ssh = SSHTunnelForwarder(
                    (model.ssh_host, int(model.ssh_port)),
                    ssh_username=model.ssh_user,
                    ssh_password=model.ssh_password,
                    remote_bind_address=(model.host, int(model.port)),
                )
                ssh.start()
            except BaseSSHTunnelForwarderError as e:
                raise UserError(message='ssh connection failed ' + str(e))

        try:
            conn = pymysql.connect(
                db=model.database,
                host=model.host if not ssh else LOOPBACK_ADDRESS,
                port=int(model.port) if not ssh else int(ssh.local_bind_port),
                user=model.user,
                passwd=model.password,
                charset='utf8',
                connect_timeout=4,
                cursorclass=pymysql.cursors.DictCursor,
            )
        # pylint:disable=E1101
        except pymysql.DatabaseError as e:
            raise UserError(message='数据库连接失败：' + str(e))
        finally:
            if conn:
                conn.close()
            if ssh:
                ssh.close()

        return True

    @staticmethod
    def get_mysql_db(conn_str):
        """
        获取mysql数据db
        :param data_source.models.DataSourceModel model:
        :return:
        """
        return SimpleMysql(
            host=conn_str.host,
            port=int(conn_str.port),
            db=conn_str.database,
            user=conn_str.user,
            passwd=conn_str.password,
            use_ssh=conn_str.use_ssh,
            ssh_host=conn_str.ssh_host,
            ssh_port=conn_str.ssh_port,
            ssh_user=conn_str.ssh_user,
            ssh_password=conn_str.ssh_password,
        )

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        with MySQLDataSource.get_mysql_db(model.conn_str) as db:
            return db.query(sql)

    @staticmethod
    def get_tables(query_model):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        try:
            return mysql_data_source_repository.get_tables(query_model)
        except pymysql.Error as e:
            raise UserError(message='获取数据表错误：' + str(e))
        except Exception as be:
            raise UserError(message='获取数据表错误：' + str(be))

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        try:
            query_model = mysql_data_source_repository.get_table_columns(query_model)
            return handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.RDS.value)
        except pymysql.Error as e:
            raise UserError(message='获取数据表字段错误：' + str(e))
        except Exception as be:
            raise UserError(message='获取数据表字段错误：' + str(be))

    @staticmethod
    def create_table(model):
        """
        创建数据表
        :param data_source.models.CreateTableModel model:
        :return:
        """
        try:
            return mysql_data_source_repository.create_table(model)
        except pymysql.Error as e:
            raise UserError(message='创建表错误：' + str(e))


class PostgreSQLDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model):
        """
        测试postgre数据库连接
        :param data_source.models.PostgreSQLConnStrModel model:
        :return bool:
        """
        ssh = None
        conn = None

        try:
            conn = psycopg2.connect(
                database=model.database,
                user=model.user,
                password=model.password,
                host=model.host,
                port=int(model.port),
                cursor_factory=RealDictCursor,
                connect_timeout=4,
            )
        except (psycopg2.OperationalError, psycopg2.ProgrammingError) as e:
            raise UserError(message='数据库连接失败：' + str(e))
        finally:
            if conn:
                conn.close()
            if ssh:
                ssh.close()

        return True

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        with postgresql_data_source_repository.get_postgresql_db(model) as db:
            return db.query(sql)

    @staticmethod
    def get_tables(query_model: TableQueryModel):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        try:
            return postgresql_data_source_repository.get_tables(query_model)
        except pymysql.Error as e:
            raise UserError(message='获取数据表错误：' + str(e))
        except Exception as be:
            raise UserError(message='获取数据表错误：' + str(be))

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        try:
            query_model = postgresql_data_source_repository.get_table_columns(query_model)
            return handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.PG.value)
        except pymysql.Error as e:
            raise UserError(message='获取数据表字段错误：' + str(e))
        except Exception as be:
            raise UserError(message='获取数据表字段错误：' + str(be))

    @staticmethod
    def create_table(model):
        """
        创建数据表
        :param data_source.models.CreateTableModel model:
        :return:
        """
        try:
            return postgresql_data_source_repository.create_table(model)
        except pymysql.Error as e:
            raise UserError(message='创建表错误：' + str(e))


class ODPSDataSource(DataSourceClient):
    @staticmethod
    def get_odps(model):
        """
        获取ODPS实例
        :param data_source.models.ODPSConnStrModel model:
        :return:
        """
        return ODPS(model.access_id, model.access_key, model.project_name, endpoint=config.get('ODPS.endpoint'))

    @staticmethod
    def test_connection(model):
        """
        测试ODPS配置
        :param data_source.models.ODPSConnStrModel model:
        :return bool:
        """
        try:
            return ODPSDataSource.get_odps(model).exist_project(model.project_name)
        except ODPSError as e:
            raise UserError(message='ODPS config error ' + str(e))

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        odps = ODPSDataSource.get_odps(model.conn_str)
        return odps.execute_sql(sql)

    @staticmethod
    def get_tables(query_model):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        try:
            tables = ODPSDataSource.get_odps(query_model.data_source.conn_str).list_tables()
            query_model.total = 0
            skip = query_model.skip
            items = []
            page_size = int(query_model.page_size)
            table_name_prefix = (
                query_model.data_source.conn_str.table_name_prefix.split(',')
                if query_model.data_source.conn_str.table_name_prefix
                else []
            )
            for table in tables:
                if query_model.keyword and query_model.keyword not in table.name:
                    continue
                if (
                        table_name_prefix
                        and len(
                    [prefix for prefix in table_name_prefix if prefix and table.name.find(prefix) == 0]) == 0
                ):
                    continue
                query_model.total += 1
                if query_model.total > skip and page_size > 0:
                    items.append({'name': table.name})
                    page_size -= 1
            query_model.items = items
            return query_model
        except ODPSError as e:
            raise UserError(message='获取数据表错误 ' + str(e))

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        try:
            odps = ODPSDataSource.get_odps(query_model.data_source.conn_str)
            if not odps.exist_table(query_model.table_name):
                raise UserError(message=query_model.table_name + '数据表不存在')
            table = odps.get_table(query_model.table_name)
            query_model.items = [
                {
                    'name': column.name,
                    'type': str(column.type),
                    'comment': column.comment,
                    'is_partition': isinstance(column, TableSchema.TablePartition),
                }
                for column in table.schema.columns
            ]
            query_model.total = len(query_model.items)
            return query_model
        except ODPSError as e:
            raise UserError(message='获取数据表字段错误 ' + str(e))

    @staticmethod
    def get_table_column_values(query_model):
        """
        获取数据表字段值
        :param data_source.models.ColumnValueQueryModel query_model:
        :return:
        """
        try:
            odps = ODPSDataSource.get_odps(query_model.data_source.conn_str)
            if not odps.exist_table(query_model.table_name):
                raise UserError(message=query_model.table_name + '数据表不存在')
            table = odps.get_table(query_model.table_name)
            if query_model.column_name not in [c.name for c in table.schema.columns]:
                raise UserError(message=query_model.column_name + '字段不存在')
            sql = (
                'SELECT (CASE WHEN {col} IS NULL THEN \'<null>\' ELSE  {col} END )AS value,'
                ' count(1) AS total_record FROM {table} '
                'GROUP BY {col}  LIMIT 150'.format(table=query_model.table_name, col=query_model.column_name)
            )
            instance = odps.execute_sql(sql)
            query_model.items = []
            with instance.open_reader() as reader:
                for record in reader:
                    query_model.items.append({'value': record.get(0), 'total_record': record.get(1)})
            return query_model
        except ODPSError as e:
            raise UserError(message='获取数据表字段值错误 ' + str(e))


class SaaSDataSource(DataSourceClient):
    @staticmethod
    def _test_tenant_db_connection(model):
        """
        测试SaaS租户库连接
        :param data_source.models.SaaSConnStrModel model:
        :return:
        """
        try:
            tenant_db_list = saas_data_source_repository.get_tenant_db_list(model)
        # pylint:disable=E1101
        except (pymysql.OperationalError, pymysql.ProgrammingError) as e:
            raise UserError(message='获取租户列表错误：' + str(e))
        if tenant_db_list:
            first_db_config = tenant_db_list[0]
            if not isinstance(first_db_config, dict):
                raise UserError(message='租户库返回数据格式错误')
            keys = first_db_config.keys()
            if 'database' not in keys or 'host' not in keys or 'port' not in keys:
                raise UserError(
                    message='租户列表获取SQL,返回所有租户库连接的关键信息必须返回：database(数据库名称),host(数据库主机名或IP地址),port(端口)')
            for tenant_db in tenant_db_list:
                user = tenant_db.get('user')
                password = tenant_db.get('password')
                SaaSDataSource._test_mysql_connection(
                    db=tenant_db.get('database'),
                    host=tenant_db.get('host'),
                    port=int(tenant_db.get('port')),
                    user=user if user else model.tenant_db_user,
                    passwd=password if password else model.tenant_db_password,
                    charset='utf8',
                    connect_timeout=4,
                )
        return True

    @staticmethod
    def _test_mysql_connection(**kwargs):
        """
        测试mysql数据源
        :param kwargs:
        :return:
        """
        conn = None
        try:
            conn = pymysql.connect(**kwargs)
        except pymysql.Error as e:
            raise UserError(message=kwargs.get('db', '') + ' db connection failed' + str(e))
        finally:
            if conn:
                conn.close()
        return True

    @staticmethod
    def test_connection(model):
        """
        测试SaaS数据库连接
        :param data_source.models.SaaSConnStrModel model:
        :return bool:
        """
        SaaSDataSource._test_tenant_db_connection(model)
        SaaSDataSource._test_mysql_connection(
            db=model.standard_db_name,
            host=model.standard_db_host,
            port=int(model.standard_db_port),
            user=model.standard_db_user,
            passwd=model.standard_db_password,
            charset='utf8',
            connect_timeout=4,
        )

    @staticmethod
    def get_tables(query_model):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        try:
            return saas_data_source_repository.get_tables(query_model)
        except pymysql.Error as e:
            raise UserError(message='获取数据表错误：' + str(e))

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        try:
            return saas_data_source_repository.get_table_columns(query_model)
        except pymysql.Error as e:
            raise UserError(message='获取数据表字段错误：' + str(e))


class MysoftDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model):
        """
        测试mysql数据库连接
        :param data_source.models.MysoftERPConnStrModel model:
        :return bool:
        """
        if CloudAPI(model.host, model.access_id, model.access_secret,
                    model.app_name, getattr(model, 'db_str', None)).ping():
            return True
        else:
            raise UserError(message='连接失败')

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        conn_model = model.conn_str
        api = CloudAPI(conn_model.host, conn_model.access_id, conn_model.access_secret,
                       conn_model.app_name, getattr(conn_model, 'db_str', None))
        return api.get_sql_list(sql)

    @staticmethod
    def get_tables(query_model: TableQueryModel):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        result = CloudAPI(conn_str.host, conn_str.access_id, conn_str.access_secret, conn_str.app_name,
                          getattr(conn_str, 'db_str', None)).get_tables(
            query_model.keyword, query_model.page, query_model.page_size, conn_str.table_name_prefix
        )
        if result.get('errmsg'):
            raise UserError(message=result.get('errmsg'))
        else:
            query_model.items = result.get('data').get('list')
            query_model.total = result.get('data').get('count')
        return query_model

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        result = CloudAPI(
            conn_str.host, conn_str.access_id, conn_str.access_secret, conn_str.app_name,
            getattr(conn_str, 'db_str', None)
        ).get_table_columns(query_model.table_name)
        if result.get('errmsg'):
            raise UserError(message=result.get('errmsg'))
        else:
            query_model.items = result.get('data') if result.get("data") else []
            query_model = handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.MSSQL.value)
            query_model.total = len(query_model.items)
        return query_model

    @staticmethod
    def get_erp_app_list(erp_api_info_id=None):
        """
        获取erp的子系统列表
        :return list:
        """
        erp_app = repository.get_list("erp_app", {}, order_by=" level_code asc ", from_config_db=True)
        result = []
        for app in erp_app:
            item = {"AppCode": app.get("app_code"),
                    "ApplicationName": app.get("application_name"),
                    "IsEnd": app.get("is_end"),
                    "IsErp": app.get("is_erp"),
                    "IsSaaS": app.get("is_saas"),
                    "LevelCode": app.get("level_code")}
            result.append(item)
        return result

    @staticmethod
    def get_erp_env(erp_api_info_id=None):
        """
        获取erp的环境标识
        :return list:
        """
        erp_site = get_mysoft_erp(erp_api_info_id=erp_api_info_id)
        if erp_site['erpapi_host'] is None:
            raise UserError(message='未配置接口管家地址!')
        result = CloudAPI(erp_site.get('erpapi_host', ""), erp_site.get('erpapi_access_id', ""),
                          erp_site.get('erpapi_access_secret', ""), "",
                          "").get_environment_id()
        return result.get('data')


class MysoftNewDataSource(DataSourceClient):
    """
    MysoftNewERP 类型数据源获取对应数据服务中心环境变量，以及业务数据库测试连接接口实现
    """

    @staticmethod
    def test_connection(model):
        """
        测试数据源指定的业务系统数据库连接
        :param data_source.models.MysoftNewERPConnStrModel model:
        :return bool:
        """
        # 数据源测试接口
        # 如果是三云则需要获取server url
        model = MysoftNewDataSource.get_mysoft_saas_info(model)
        params = {"Content": model.get_dict()}
        rs = request_data_center(MysoftNewERPAPI.TestConnection.value, params, erp_api_info_id=model.erp_api_info_id)
        if rs.get('IsCanConnect'):
            return True
        else:
            reason = ''
            if 'Reason' in rs:
                reason = rs.get('Reason')
            raise UserError(message='连接失败：' + reason)

    @staticmethod
    def get_mysoft_saas_info(conn_str: MysoftNewERPConnStrModel):
        if conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
            # api连接方式
            if conn_str.SaasLinkType == MysoftNewERPCloudType.CLOUD_API.value:
                app_code = get_app_code(conn_str.AppLevelCode)
                server_url = get_mysoft_saas_server_url(app_code)
                if not server_url:
                    raise UserError(message='没有配置对应移动产品的请求数据调用的api地址')
                conn_str.Server = server_url
                conn_str.DbType = MysoftNewERPDataBaseType.Cloud_Mysql.value
                conn_str.ConfigType = MysoftNewERPConfigType.CLOUD_MYSQL.value
            else:
                # 数据库连接方式
                conn_str.DbType = MysoftNewERPDataBaseType.Mysql.value
                conn_str.ConfigType = MysoftNewERPConfigType.MANUAL_INPUT.value

        # 异构系统默认的app_level_code设置
        if conn_str.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
            conn_str.AppLevelCode = "9000"
            conn_str.ConfigType = MysoftNewERPConfigType.MANUAL_INPUT.value
        return conn_str

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        rs = get_data_by_sql(sql=sql, data_source_model=model)
        return rs.get('Data')

    @staticmethod
    def get_erp_env(erp_api_info_id=None):
        """
        获取erp的环境标识
        :return list:
        """
        return request_data_center(MysoftNewERPAPI.GetEnvironmentId.value, erp_api_info_id=erp_api_info_id)

    @staticmethod
    def get_erp_app_list(erp_api_info_id=None):
        """
        获取erp的子系统列表
        :return list:
        """
        return request_data_center(MysoftNewERPAPI.GetApps.value, erp_api_info_id=erp_api_info_id)

    @staticmethod
    def get_tables(query_model: TableQueryModel):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        # 明源erp类别获取表
        if conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_ERP.value:
            if query_model.data_source.db_type.lower() == MysoftNewERPDataBaseType.Mysql.value.lower() \
                    or query_model.data_source.db_type.lower() == MysoftNewERPDataBaseType.DM.value.lower():
                return MysoftNewDataSource._get_no_mysoft_tables(query_model)
            return MysoftNewDataSource._get_mysoft_erp_tables(query_model)
        # 异构系统获取表信息
        elif conn_str.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
            return MysoftNewDataSource._get_no_mysoft_tables(query_model)
        elif conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
            # 三云的返回空，暂不需要显示提示信息。以后可能优化。
            query_model.items = []
            query_model.total = 0
            return query_model

    @staticmethod
    def _get_tables_rs(sql: str, count_sql: str, query_model: TableQueryModel):
        """
        获取表列表的数据和总数
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        # 表列表数据
        table_list = []
        if sql:
            rs = get_data_by_sql(sql, query_model.data_source, dataset_id=query_model.id)
            if 'Data' in rs:
                table_list = rs['Data']
        query_model.items = table_list
        # 总记录数
        total_count = 0
        if count_sql:
            count_rs = get_data_by_sql(count_sql, query_model.data_source, dataset_id=query_model.id)
            if 'Data' in count_rs and 'c' in count_rs['Data'][0]:
                total_count = count_rs['Data'][0].get('c')
        query_model.total = total_count
        return query_model

    @staticmethod
    def _get_mysoft_erp_tables(query_model: TableQueryModel):
        """
        明源erp类别获取表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        if str(conn_str.AppCode) == "1401":
            sql = MysoftNewERPTableSql.GET_TABLE_MDC_SQL.value
            count_sql = MysoftNewERPTableSql.GET_TABLE_MDC_COUNT_SQL.value
            keyword_sql = "AND  (o.name LIKE '%{keyword}%' OR t.DisplayName LIKE '%{keyword}%')"
        else:
            sql = MysoftNewERPTableSql.GET_TABLE_NOT_MDC_SQL.value
            count_sql = MysoftNewERPTableSql.GET_TABLE_NOT_MDC_COUNT_SQL.value
            keyword_sql = "AND  (o.name LIKE '%{keyword}%' OR t.table_chn LIKE '%{keyword}%')"
        offset = (query_model.page - 1) * query_model.page_size
        end = offset + query_model.page_size
        rp_keyword_sql = ""
        if query_model.keyword is not None:
            kw = query_model.keyword
            # SqlServer 下划线关键词转义后搜索
            if "_" in kw:
                keyword_list = kw.split("_")
                kw = "[_]".join(keyword_list)
            rp_keyword_sql = keyword_sql.format(keyword=kw)

        sql = sql.format(keyword_sql=rp_keyword_sql, begin=offset, end=end)
        count_sql = count_sql.format(keyword_sql=rp_keyword_sql)
        return MysoftNewDataSource._get_tables_rs(sql, count_sql, query_model)

    @staticmethod
    def _get_no_mysoft_tables(query_model: TableQueryModel):
        """
        异构系统（非明源）获取数据表。
        支持MySQL和Oracle
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        data_source = query_model.data_source
        db_type = data_source.db_type.lower()
        if db_type == MysoftNewERPDataBaseType.Mysql.value.lower():
            return MysoftNewDataSource._get_no_mysoft_mysql_tables(query_model)
        elif db_type == MysoftNewERPDataBaseType.Oracle.value.lower():
            # oracle 获取
            return MysoftNewDataSource._get_no_mysoft_oracle_tables(query_model)
        elif db_type == MysoftNewERPDataBaseType.DM.value.lower():
            return MysoftNewDataSource._get_no_mysoft_dm_tables(query_model)
        elif db_type == MysoftNewERPDataBaseType.SQL_Server.value.lower():
            return MysoftNewDataSource._get_mysoft_erp_tables(query_model)

    @staticmethod
    def _get_no_mysoft_mysql_tables(query_model: TableQueryModel):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        table_sql = MysoftNewERPTableSql.GET_TABLE_MYSQL_SQL.value
        count_sql = MysoftNewERPTableSql.GET_TABLE_MYSQL_COUNT_SQL.value
        keyword_sql = " AND (TABLE_NAME LIKE '%{keyword}%' or TABLE_COMMENT LIKE '%{keyword}%')"
        offset = (query_model.page - 1) * query_model.page_size
        rp_keyword_sql = ""
        if query_model.keyword:
            kw = query_model.keyword
            rp_keyword_sql = keyword_sql.format(keyword=kw)

        sql = table_sql.format(keyword_sql=rp_keyword_sql, offset=offset, count=query_model.page_size)
        count_sql = count_sql.format(keyword_sql=rp_keyword_sql)
        return MysoftNewDataSource._get_tables_rs(sql, count_sql, query_model)

    @staticmethod
    def _get_no_mysoft_oracle_tables(query_model: TableQueryModel):
        """
        Oracle获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        table_sql = MysoftNewERPTableSql.GET_TABLE_ORACLE_SQL.value
        count_sql = MysoftNewERPTableSql.GET_TABLE_ORACLE_COUNT_SQL.value
        keyword_sql = " and  table_name like '%{keyword}'"
        begin = (query_model.page - 1) * query_model.page_size
        end = begin + query_model.page_size + 1
        rp_keyword_sql = ""
        if query_model.keyword:
            kw = query_model.keyword
            rp_keyword_sql = keyword_sql.format(keyword=kw)

        sql = table_sql.format(keyword_sql=rp_keyword_sql, begin=begin, end=end)
        count_sql = count_sql.format(keyword_sql=rp_keyword_sql)
        query_model = MysoftNewDataSource._get_tables_rs(sql, count_sql, query_model)
        # 特别处理
        item_list = query_model.items
        new_list = []
        for item in item_list:
            new_item = {
                "name": item.get('NAME'),
                "comment": item.get('COMMENTS'),
            }
            new_list.append(new_item)
        query_model.items = new_list
        return query_model

    @staticmethod
    def _get_no_mysoft_dm_tables(query_model: TableQueryModel):
        """
        dm获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        table_sql = MysoftNewERPTableSql.GET_TABLE_DM_SQL.value
        count_sql = MysoftNewERPTableSql.GET_TABLE_DM_COUNT_SQL.value
        keyword_sql = " and  ut.table_name like '%{keyword}%'"
        begin = (query_model.page - 1) * query_model.page_size
        end = begin + query_model.page_size + 1
        rp_keyword_sql = ""
        if query_model.keyword:
            kw = query_model.keyword
            rp_keyword_sql = keyword_sql.format(keyword=kw)

        sql = table_sql.format(keyword_sql=rp_keyword_sql, begin=begin, end=end)
        count_sql = count_sql.format(keyword_sql=rp_keyword_sql)
        query_model = MysoftNewDataSource._get_tables_rs(sql, count_sql, query_model)
        # 特别处理
        item_list = query_model.items
        new_list = []
        for item in item_list:
            new_item = {
                "name": item.get('NAME') or item.get('name'),
                "comment": item.get('COMMENTS') or item.get('comments'),
            }
            new_list.append(new_item)
        query_model.items = new_list
        return query_model

    @staticmethod
    def get_table_columns(query_model):
        """
        获取某个表的所有字段列表
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        # 明源erp类别获取表字段
        if conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_ERP.value:
            return MysoftNewDataSource._get_table_erp_columns(query_model)
        # 异构系统获取表字段信息
        elif conn_str.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
            return MysoftNewDataSource._get_table_no_mysoft_columns(query_model)
        elif conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
            # 三云的返回空，暂不需要显示提示信息。以后可能优化。
            query_model.items = []
            query_model.total = 0
            return query_model

    @staticmethod
    def _get_table_no_mysoft_columns(query_model):
        """
        获取某个表的所有字段列表
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        data_source = query_model.data_source
        db_type = data_source.db_type.lower()
        if db_type == MysoftNewERPDataBaseType.Mysql.value.lower():
            return MysoftNewDataSource._get_table_no_mysoft_mysql_columns(query_model)
        elif db_type == MysoftNewERPDataBaseType.Oracle.value.lower():
            # Oracle column
            return MysoftNewDataSource._get_table_no_mysoft_oracle_columns(query_model)
        elif db_type == MysoftNewERPDataBaseType.DM.value.lower():
            # Oracle column
            return MysoftNewDataSource._get_table_no_mysoft_dm_columns(query_model)
        elif db_type == MysoftNewERPDataBaseType.SQL_Server.value.lower():
            return MysoftNewDataSource._get_table_erp_columns(query_model)

    @staticmethod
    def _get_table_no_mysoft_mysql_columns(query_model):
        """
        获取MySQL某个表的所有字段列表
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        sql = MysoftNewERPTableSql.GET_TABLE_MYSQL_COLUMN_SQL.value
        sql = sql.format(table_name=query_model.table_name)
        rs = get_data_by_sql(sql, query_model.data_source)
        column_list = []
        if 'Data' in rs:
            column_list = rs['Data']
        query_model.items = column_list
        query_model = handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.RDS.value)
        query_model.total = len(query_model.items)
        return query_model

    @staticmethod
    def _get_table_no_mysoft_oracle_columns(query_model):
        """
        获取Oracle某个表的所有字段列表
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        sql = MysoftNewERPTableSql.GET_TABLE_ORACLE_COLUMN_SQL.value
        sql = sql.format(table_name=query_model.table_name)
        rs = get_data_by_sql(sql, query_model.data_source)
        column_list = []
        if 'Data' in rs:
            column_list = rs['Data']
        query_model.items = column_list
        new_column_list = []
        for item in column_list:
            new_item = {
                "name": item.get('NAME'),
                "type": item.get('TYPE'),
                "comment": item.get('COMMENTS'),
            }
            new_column_list.append(new_item)
        query_model.items = new_column_list
        query_model = handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.ORACLE.value)
        query_model.total = len(query_model.items)
        return query_model

    @staticmethod
    def _get_table_no_mysoft_dm_columns(query_model):
        """
        获取Oracle某个表的所有字段列表
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        sql = MysoftNewERPTableSql.GET_TABLE_DM_COLUMN_SQL.value
        sql = sql.format(table_name=query_model.table_name)
        rs = get_data_by_sql(sql, query_model.data_source)
        column_list = []
        if 'Data' in rs:
            column_list = rs['Data']
        query_model.items = column_list
        new_column_list = []
        for item in column_list:
            new_item = {
                "name": item.get('NAME'),
                "type": item.get('TYPE'),
                "comment": item.get('COMMENTS'),
            }
            new_column_list.append(new_item)
        query_model.items = new_column_list
        query_model = handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.ORACLE.value)
        query_model.total = len(query_model.items)
        return query_model

    @staticmethod
    def _get_table_erp_columns(query_model):
        """
        获取某个表的所有字段列表
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        sql = MysoftNewDataSource.get_column_sql(query_model.data_source)
        sql = sql.format(table_name=query_model.table_name)
        rs = get_data_by_sql(sql, query_model.data_source)
        column_list = []
        if 'Data' in rs:
            column_list = rs['Data']
        query_model.items = column_list
        query_model = handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.MSSQL.value)
        query_model.total = len(query_model.items)
        return query_model

    @staticmethod
    def get_column_sql(data_source_model: DataSourceModel):
        # 数据服务中心
        if data_source_model.db_type.lower() == MysoftNewERPDataBaseType.Mysql.value.lower():
            sql = MysoftNewERPTableSql.GET_TABLE_MYSQL_COLUMN_SQL.value
        elif data_source_model.db_type.lower() == MysoftNewERPDataBaseType.DM.value.lower():
            sql = MysoftNewERPTableSql.GET_TABLE_DM_COLUMN_SQL.value
        else:
            if str(data_source_model.conn_str.AppCode) == "1401":
                sql = MysoftNewERPTableSql.GET_COLUMN_SQL_MDC.value
            else:
                is_exist = MysoftNewDataSource.get_dict_exist(data_source_model)
                if is_exist == 1:
                    sql = MysoftNewERPTableSql.GET_COLUMN_SQL_EXIST_DICT.value
                else:
                    sql = MysoftNewERPTableSql.GET_COLUMN_SQL_NOT_EXIST_DICT.value
        return sql

    @staticmethod
    def get_dict_exist(data_source_model: DataSourceModel):
        """
        非数据服务中心数据源
        检测data_dict 表是否存在
        :param data_source.models.DataSourceModel data_source_model:
        :return:
        """
        dict_sql = (
            "select case when name is not null then 1 else 0 end as isExist from sys.objects "
            "where name='data_dict' and type='U'"
        )
        is_exist = 0
        dict_list = get_data_by_sql(dict_sql, data_source_model)
        if dict_list['Data'] and dict_list['Data'][0]:
            rs = dict_list['Data'][0]
            if 'isExist' in rs:
                is_exist = rs.get('isExist')
        return is_exist

    @staticmethod
    def get_saas_orgcode_and_secret(conn_str_model: MysoftNewERPConnStrModel):
        if conn_str_model.DataFrom != MysoftNewERPDataFromType.MYSOFT_SAAS.value:
            raise UserError(message='非三云数据源类别，获取失败')
        app_level_code = conn_str_model.AppLevelCode
        if not app_level_code:
            raise UserError(message='参数AppLevelCode，不能为空')

        saas_map_list = MysoftNewDataSource.get_saas_map()
        if app_level_code not in saas_map_list and not saas_map_list.get(app_level_code):
            raise UserError(message='没有找到对应的移动产品')
        prod_code = saas_map_list.get(app_level_code)

        data = {"org_code": "", "secret": ""}
        code = g.code if hasattr(g, 'code') else ""
        # 调用接口云管家的接口，获取第三方租户
        corp_list = get_corpcode(code)
        org_code = ''
        for item in corp_list:
            if item and 'prod_code' in item and item['prod_code'] == prod_code:
                org_code = data['org_code'] = item['tenant_code']
                data['secret'] = item['access_secret']
                break
        if not org_code:
            raise UserError(message='没有找到对应移动产品的企业代码')
        if org_code and data['secret']:
            return data

        # 获取当前租户的接口管家秘钥
        erp_site = get_mysoft_erp()
        corp_secret = erp_site['erpapi_access_secret']
        if not corp_secret:
            raise UserError(message='没有找到对应移动产品的企业密钥')
        data['org_code'] = org_code
        data['secret'] = corp_secret
        return data

    @staticmethod
    def get_saas_map():
        """
        三云app_level_code与平台的映射关系
        :return dict:
        """
        saas_map_list = {
            '3000': 'gcxt',  # 云链        工程协同（云链）
            '3000.8002': 'gcxt',  # 移动验房
            '3000.8005': 'gcxt',  # 云物业
            '3000.9999': 'gcxt',  # 其他云链
            '4000': 'myfuwu_ykj',  # 云空间
            '4000.8007': 'myfuwu_ykj',  # 云租赁
            '4000.8009': 'myfuwu_ykj',  # 云物业(云空间)
            '4000.9999': 'myfuwu_ykj',  # 其他云空间
            '5000': 'fdccloud',  # 云助手
            '5000.20000': 'fdccloud',  # 云助手
            '5000.3022': 'ydsp',  # 移动审批
            '5000.3023': 'ydbb',  # 移动报表
            '5000.3042': 'ydjh',  # 移动计划
            '8000': 'myunke',  # 云客
            '8000.8006': 'myunke',  # 云客
            '8011': 'yzg',
            '4000.8011': 'yzg',
            '80111': 'yzg',
            '4000.80111': 'yzg',
            '80112': 'yzg',
            '4000.80112': 'yzg',
            '80113': 'yzg',
            '4000.80113': 'yzg',
            '80114': 'yzg',
            '4000.80114': 'yzg',
        }
        return saas_map_list


class DataHubDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model):
        """
        测试DataHub数据通道连接
        :param data_source.models.DataHubConnStrModel model:
        :return bool:
        """
        if CollectAPI(model.host, model.access_secret).ping(db_code=model.db_code):
            return True
        else:
            raise UserError(message='连接失败')

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        pass

    @staticmethod
    def get_params(model):
        """
        获取接口参数值
        :param data_source.models.APIConnStrModel model:
        :return:
        """
        result = CollectAPI(model.host, model.access_secret).get_params()
        if result.get('errmsg'):
            raise UserError(message=result.get('errmsg'))
        else:
            return result.get('data').get('data')

    @staticmethod
    def send_config(model, _data):
        """
        DataHub下发配置
        :param data_source.models.DataHubConnStrModel model:
        :return bool:
        """
        if CollectAPI(model.host, model.access_secret).send_config(_data):
            return True
        else:
            raise UserError(message='下发配置失败')

    @staticmethod
    def get_tables(query_model):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        result = CollectAPI(conn_str.host, conn_str.access_secret).get_tables(
            query_model.keyword, query_model.page, query_model.page_size, db_code=conn_str.db_code
        )
        if result.get('errmsg'):
            raise UserError(message=result.get('errmsg'))
        else:
            query_model.items = result.get('data').get('list')
            query_model.total = result.get('data').get('count')
        return query_model

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        result = CollectAPI(conn_str.host, conn_str.access_secret).get_table_columns(
            query_model.table_name, db_code=conn_str.db_code
        )
        if result.get('errmsg'):
            raise UserError(message=result.get('errmsg'))
        else:
            query_model.items = result.get('data').get('data') if result.get('data').get('data') else []
            query_model = handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.ORACLE.value)
            query_model.total = len(query_model.items)
        return query_model


class DataHubSQLServerDataSource(DataHubDataSource):
    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        result = CollectAPI(conn_str.host, conn_str.access_secret).get_table_columns(
            query_model.table_name, db_code=conn_str.db_code
        )
        if result.get('errmsg'):
            raise UserError(message=result.get('errmsg'))
        else:
            query_model.items = result.get('data').get('data') if result.get('data').get('data') else []
            query_model = handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.MSSQL.value)
            query_model.total = len(query_model.items)
        return query_model


class APIDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model: APIConnStrModel):
        """
        测试云客api连接
        :param data_source.models.APIConnStrModel model:
        :return bool:
        """
        if ExternalAPI(model.host, model.access_secret, g.cookie if hasattr(g, 'cookie') else {},
                       model.tenant_code).ping(model.params):
            return True
        else:
            raise UserError(message='连接失败')

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        # 构造query_structure
        import os
        from components.query_models import QueryStructure, Select, Object
        from dataset.services.dataset_api_service import DatasetAPIService
        from components import query_sql_encoder
        from components.query_models import ModelEncoder
        from dataset.common import sql_helper
        from dataset.query.result_data import DatasetQuerySqlException, DatasetQueryTimeOutException

        query_structure = QueryStructure()
        select = Select()
        select.prop_name = "*"
        query_structure.select = [select]
        table = Object()
        sql = sql.replace(';', '')
        # 去注释
        sql, _ = remove_comment(sql)
        table.name = " ({}) ".format(sql)
        table.alias = ' a '
        query_structure.object = [table]
        api = DatasetAPIService.get_api(model)

        params = DatasetAPIService.merge_params(model.conn_str.params, "")

        new_sql = query_sql_encoder.encode_query(query_structure)
        table_names = sql_helper.extract_tables(new_sql)
        complex_params = DatasetAPIService.get_complex_params(table_names, model)
        web_time_out = int(os.environ.get('TIMEOUT', 10)) - 1
        try:
            result_data = api.get_data_list(
                params,
                json.loads(json.dumps(query_structure, cls=ModelEncoder)),
                table_names=table_names,
                timeout=web_time_out,
                complex_params=complex_params,
            )
        except UserError as ue:
            if ue.message.startswith("请求接口超时:"):
                raise DatasetQueryTimeOutException(
                    msg="运行api数据集sql时长超过{}秒，{}".format(str(web_time_out), "请求接口超时"), sql=sql
                ) from ue
            raise DatasetQuerySqlException(msg="运行api数据集内部错误，" + ue.message, sql=sql) from ue

        if not result_data.get('result'):
            raise DatasetQuerySqlException(msg="运行api数据集外部错误，" + result_data.get('msg'), sql=sql)
        return result_data.get('data')

    @staticmethod
    def get_params(model: APIConnStrModel):
        """
        获取接口参数值
        :param data_source.models.APIConnStrModel model:
        :return:
        """
        result = ExternalAPI(
            model.host, model.access_secret, g.cookie if hasattr(g, 'cookie') else {}, model.tenant_code,
            model.third_party_id
        ).get_params(
            model.param_key
        )
        if not result.get('result'):
            raise UserError(message=result.get('msg'))
        return result.get('data')

    @staticmethod
    def get_tables(query_model):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        result = ExternalAPI(
            conn_str.host, conn_str.access_secret, g.cookie if hasattr(g, 'cookie') else {}, conn_str.tenant_code,
            conn_str.third_party_id
        ).get_tables(conn_str.params, query_model.keyword, query_model.page, query_model.page_size)
        if not result.get('result'):
            raise UserError(message=result.get('msg'))
        else:
            query_model.items = result.get('data').get('items')
            query_model.total = result.get('data').get('total')
        return query_model

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        conn_str = query_model.data_source.conn_str
        result = ExternalAPI(
            conn_str.host, conn_str.access_secret, g.cookie if hasattr(g, 'cookie') else {},
            conn_str.tenant_code, conn_str.third_party_id
        ).get_table_columns(conn_str.params, query_model.table_name)
        if not result.get('result'):
            raise UserError(message=result.get('msg'))
        else:
            result_data = result.get('data') or []
            query_model.items = handle_data_source_field_type(result_data, db_engine=DBEngine.RDS.value)
            query_model.total = len(query_model.items)
        return query_model


class HighDataDataSource(DataSourceClient):
    # 主题数据包文件oss存储路径
    FILE_PATH = 'dmp/dataset/subjects/'

    @staticmethod
    def test_connection(model: HighDataConnStrModel):
        """
        HighData数据源测试连接
        :param model:
        :return:
        """
        if not isinstance(model, HighDataConnStrModel):
            raise UserError(message='测试连接调用参数错误')

        if model.oss_url:
            if not model.access_key_id:
                raise UserError(message='配置了oss地址，AccessKeyId必填')
            if not model.access_key_secret:
                raise UserError(message='配置了oss地址，AccessKeySecret必填')

            # 使用输入的oss配置
            try:
                _url = urlparse(model.oss_url)
                domain_length = len(_url.netloc.split('.'))
                if domain_length == 4:
                    bucket, domain = _url.netloc.split('.', 1)
                elif domain_length == 3:
                    domain = _url.netloc
                    bucket = _url.path.split('/')[1]
                else:
                    raise UserError(message='oss地址格式错误')
                endpoint = _url.scheme + '://' + domain
            except Exception:
                raise UserError(
                    message='oss地址格式错误，必须带bucket，支持bucket放在子域名或者path路由格式，请修改oss地址')

            oss = OSSFileProxy(model.access_key_id, model.access_key_secret, bucket, endpoint)
        else:
            # 使用系统oss配置
            oss = OSSFileProxy()

        try:
            return oss.object_list(HighDataDataSource.FILE_PATH)
        except Exception:
            raise UserError(message='连接失败,请检查oss配置的AccessKeyId和AccessKeySecret是否正确')

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        pass

    @staticmethod
    def get_tables(query_model):
        highdata_data_source_repository.get_subjects(query_model)
        return query_model


class ADSDataSource(DataSourceClient):
    """
    ADS数据源
    """

    @staticmethod
    def test_connection(model: ADSDataConnStrModel):
        model.validate()
        if not str(model.port).isdigit():
            raise UserError(message='端口必须为整数')

        try:
            with ads_data_source_repository.get_ads_db(model) as db:
                db.query('SELECT DATABASE()')

            return True

        except Exception as e:
            error_msg = str(e)
            if error_msg.find('%(2005') >= 0:
                message = '地址错误'
            elif error_msg.find('%(2003') >= 0:
                message = '端口错误!'
            elif error_msg.find('%(1045') >= 0:
                if error_msg.find('AccessKeyNotFound') >= 0:
                    message = '用户不存在'
                elif error_msg.find('Unknown database') >= 0:
                    message = '数据库不存在'
                else:
                    message = '密码错误'
            else:
                message = '连接失败! 错误信息：' + str(e).replace('MYSQL', 'ADS')
            raise UserError(message=message)

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param model:
        :param sql:
        :return:
        """
        with ads_data_source_repository.get_ads_db(model.conn_str) as db:
            return db.query(sql)

    @staticmethod
    def create_table(model):
        """
        创建数据表
        :param data_source.models.CreateTableModel model:
        :return:
        """
        # 校验关键字
        validate_key_word(model.create_sql, exclude=['create', 'if'])
        table_name, table_exist = ADSDataSource.check_table_exist(model)
        if table_exist:
            raise UserError(message=f'表：{table_name}已存在')
        try:
            return ads_data_source_repository.create_table(model)
        except pymysql.Error as e:
            raise UserError(message='创建表错误：' + str(e))

    @staticmethod
    def check_table_exist(model):
        # 去注释，避免正则取出注释内容，导致不正确
        create_sql, _ = remove_comment(model.create_sql)
        table_names = re.findall(r'CREATE TABLE IF NOT exists `(.*)`|CREATE TABLE `(.*)`', create_sql)
        # 目前只支持单个语句，取第一个
        if not table_names:
            return '', False
        # table_names 结构 [('ods_datahub_sqlserver1_aa_big', '')]
        if table_names[0]:
            model.keyword = table_names[0][0] or table_names[0][1]
            return model.keyword, bool(ADSDataSource.get_tables(model).items)
        else:
            return '', False

    @staticmethod
    def _handle_records(records: list, field_mapping: dict, partition_columns: list = None):
        for r in records:
            for old, new in field_mapping.items():
                if old == new:
                    continue
                r[new] = r.pop(old)
            if partition_columns and r.get("name") in partition_columns:
                r["is_partition"] = True
            else:
                r["is_partition"] = False
        return records

    @staticmethod
    def get_tables(query_model: TableQueryModel):
        ads_data_source_repository.get_table_list(query_model)
        query_model.items = ADSDataSource._handle_records(
            query_model.items, {"TABLE_NAME": "name", "COMMENTS": "comment"}
        )
        return query_model

    @staticmethod
    def get_table_partition_columns(query_model: ColumnQueryModel):
        partition_columns = ads_data_source_repository.get_table_partition_column(query_model)
        return partition_columns

    @staticmethod
    def get_table_columns(query_model: ColumnQueryModel):
        """分区字段跟opds一样，在字段中返回，但ads的表的分区信息在information_schema.TABLES中，不在字段信息里，所以这里还需要请求get_table"""
        partition_columns = ADSDataSource.get_table_partition_columns(query_model)
        ads_data_source_repository.get_table_columns(query_model)
        query_model.items = ADSDataSource._handle_records(
            query_model.items,
            {"COLUMN_NAME": "name", "COLUMN_COMMENT": "comment", "COLUMN_TYPE": "type"},
            partition_columns=partition_columns,
        )
        return handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.ADS.value)


class PrestoDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model: PrestoConnStrModel):
        """
        测试presto数据库连接
        :param data_source.models.PrestoConnStrModel model:
        :return bool:
        """
        try:
            conn = trino.connect(
                host=model.host,
                port=int(model.port),
                username=model.user,
                password=model.password or None,
                catalog=model.catalog,
                schema=model.database,
            )
            cursor = conn.cursor()
            sql = "SELECT TABLE_NAME AS name, '' AS comment FROM information_schema.TABLES LIMIT 1"
            cursor.execute(sql)
            cursor.fetchall()
        # pylint:disable=E1101
        except Exception as e:
            raise UserError(message='数据库连接失败：' + str(e))
        return True

    @staticmethod
    def get_tables(query_model):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        try:
            return presto_data_source_repository.get_tables(query_model)
        except trino.DatabaseError as e:
            raise UserError(message='获取数据表错误：' + str(e))
        except Exception as be:
            raise UserError(message='获取数据表错误：' + str(be))

    @staticmethod
    def get_table_columns(query_model):
        """
        获取数据表字段
        :param data_source.models.ColumnQueryModel query_model:
        :return:
        """
        try:
            query_model = presto_data_source_repository.get_table_columns(query_model)
            return handle_data_source_field_type_by_query_model(query_model, db_engine=DBEngine.RDS.value)
        except trino.DatabaseError as e:
            raise UserError(message='获取数据表字段错误：' + str(e))
        except Exception as be:
            raise UserError(message='数据表字段处理错误：' + str(be))

    @staticmethod
    def create_table(model):
        """
        创建数据表
        :param data_source.models.CreateTableModel model:
        :return:
        """
        try:
            return presto_data_source_repository.create_table(model)
        except pymysql.Error as e:
            raise UserError(message='创建表错误：' + str(e))


class MysoftShuXinDataSource(DataSourceClient):
    @staticmethod
    def test_connection(model: MysoftShuXinConnStrModel):
        try:

            def replace_func(match):
                key = match.group(2) or ''
                value = config.get('ThirdDatasource.{}'.format(key))
                if value:
                    return value

            # 测试运行前删除access_token缓存
            # key = f"{model.project_code}:pulsar_api:{model.app_key}:token"
            pattern = r"(?P<value>\$\{(.*?)\})"

            app_id = re.sub(pattern, replace_func, model.app_id) or model.app_id
            app_key = re.sub(pattern, replace_func, model.app_key) or model.app_key

            key = f"{model.project_code}:pulsar_api{app_id}:{app_key}:token"
            RedisCache(key_prefix="indicator_model").delete(key)

            api = PulsarApi(
                _from='test_connect',
                host=model.api_host,
                app_key=model.app_id,
                app_secret=model.app_secret,
                pulsar_key=model.app_key,
                pulsar_code=model.project_code,
                set_token_cache=False
            )
            api.get_all_indicator_class()
        except Exception as e:
            raise UserError(message=f'数芯服务连接失败：{str(e)}') from e
        return True


class MysoftShuXin15DataSource(DataSourceClient):
    @staticmethod
    def test_connection(model: MysoftShuXinConnStrModel):
        try:

            def replace_func(match):
                key = match.group(2) or ''
                value = config.get('ThirdDatasource.{}'.format(key))
                if value:
                    return value

            # 测试运行前删除access_token缓存
            # key = f"{model.project_code}:pulsar_api:{model.app_key}:token"
            pattern = r"(?P<value>\$\{(.*?)\})"
            app_id = "app_id_default"
            app_key = "app_key_default"
            if not auth_util.is_env_enable_skyline_auth() or not model.app_id.startswith("${"):
                app_id = re.sub(pattern, replace_func, model.app_id) or model.app_id
                app_key = re.sub(pattern, replace_func, model.app_key) or model.app_key

            key = f"{model.project_code}:pulsar_api{app_id}:{app_key}:token"
            RedisCache(key_prefix="indicator_model").delete(key)

            api = Pulsar15Api(
                _from='test_connect',
                host=model.api_host,
                app_key=model.app_id,
                app_secret=model.app_secret,
                pulsar_key=model.app_key,
                pulsar_code=model.project_code,
                set_token_cache=False
            )
            api.get_tables()
        except Exception as e:
            raise UserError(message=f'服务连接失败：{str(e)}') from e
        return True

    @staticmethod
    def get_tables(query_model: TableQueryModel):
        """
        获取数据表
        :param data_source.models.TableQueryModel query_model:
        :return:
        """
        try:

            def replace_func(match):
                key = match.group(2) or ''
                value = config.get('ThirdDatasource.{}'.format(key))
                if value:
                    return value

            model = query_model.data_source.conn_str
            pattern = r"(?P<value>\$\{(.*?)\})"
            app_id = re.sub(pattern, replace_func, model.app_id) or model.app_id
            app_key = re.sub(pattern, replace_func, model.app_key) or model.app_key

            key = f"{model.project_code}:pulsar_api{app_id}:{app_key}:token"
            RedisCache(key_prefix="indicator_model").delete(key)

            api = Pulsar15Api(
                _from='datasource',
                host=model.api_host,
                app_key=model.app_id,
                app_secret=model.app_secret,
                pulsar_key=model.app_key,
                pulsar_code=model.project_code,
                datasource_name=query_model.data_source.name
            )

            api_data = api.get_tables()
            query_model.items = api_data.get('items', {})
            query_model.items = query_model.items if query_model.items else {}
            # 总记录数 list 不需要记录数
            query_model.total = 9999
            return query_model
        except Exception as e:
            raise UserError(message=f'服务连接失败：{str(e)}') from e
        return True

    @staticmethod
    def get_table_columns(query_model):
        model = query_model.data_source.conn_str
        api = Pulsar15Api(
            _from='datasource',
            host=model.api_host,
            app_key=model.app_id,
            app_secret=model.app_secret,
            pulsar_key=model.app_key,
            pulsar_code=model.project_code,
            datasource_name=query_model.data_source.name
        )
        column_datas = []
        api_data_structure = api.get_object_structs(table_name=query_model.table_name)
        columns = api_data_structure.get('field', [])
        columns = [] if not columns else columns
        sort = 0
        group_sort = {}
        for group_fields_order in api_data_structure.get('group_fields_order'):
            sort += 1
            group_sort[group_fields_order.get('group_name')] = sort
        num_relation = {}
        for column in columns:
            num_relation[column.get('name')] = []
        for column in columns:
            column['col_name'] = column.get('name')
            column['col_name'] = column.get('name')
            column['type'] = column.get('storage_field_type')
            column['data_type'] = column.get('storage_field_type')
            column['note'] = column.get('description')
            column['group_name'] = column.get('group_name', '')
            column['comment'] = column.get('name_cn')
            column['group_name_sort'] = group_sort.get(column.get('group_name', ''), 99)
            relation_fields = []
            if not column.get('analysis_dimension', {}):
                column_datas.append(column)
                continue
            for analysis_dimension in column.get('analysis_dimension', {}):  # 此处只有度量会包含哪些指标，数见会存储 指标数据相互影响的字段，所以需要双重计算
                for relation_field in columns:
                    name = relation_field.get('name')
                    if name == analysis_dimension.get('name', ''):
                        relation_fields.append(relation_field.get('id'))
                        if name not in num_relation:
                            num_relation[name] = [column['id']]
                        else:
                            num_relation[name].append(column['id'])
            column['relation_fields'] = list(set(relation_fields))
            column_datas.append(column)
        for insert_column in column_datas:
            if insert_column.get('dim_type') == 'indicator':
                continue
            insert_column['relation_fields'] = list(set(num_relation[insert_column.get('name')]))

        query_model.items = handle_data_source_field_type(column_datas, "mysql")  # 数芯的字段类型默认mysql兼容模式
        # MysoftShuXin15DataSource._trans_columns_data_type(query_model.items)
        query_model.total = len(query_model.items)

        # 变量处理
        variables = api_data_structure.get('variables', [])
        query_model.var_content = convert_var_shuxin15_to_dmp(variables)
        return query_model

    @staticmethod
    def _trans_columns_data_type(records: list):
        """
        修复数芯1.5的数据类型，将data_type转为dmp的数据类型
        """
        if not isinstance(records, list):
            return records
        for r in records:
            if not r.get('col_type'):
                continue
            r['data_type'] = r['col_type']
        return records

    @staticmethod
    def get_table_code(table_object, table_name):
        for obj in table_object:
            if obj['name'].lower() == table_name.lower():
                return obj['code']
            elif obj['children']:
                result = MysoftShuXin15DataSource.get_table_code(obj['children'], table_name)
                if result is not None:
                    return result
        return None

    @staticmethod
    def exec_sql(model: DataSourceModel, sql):
        """
        :param data_source.models model:
        :param sql:
        :return:
        """
        # 构造query_structure
        import os
        from components.query_models import QueryStructure, Select, Object
        from dataset.query.result_data import DatasetQuerySqlException, DatasetQueryTimeOutException
        query_structure = QueryStructure()
        select = Select()
        select.prop_name = "*"
        query_structure.select = [select]
        table = Object()
        sql = sql.replace(';', '')
        # 去注释
        sql, _ = remove_comment(sql)
        table.name = " ({}) ".format(sql)
        table.alias = ' a '
        query_structure.object = [table]
        api = Pulsar15Api(
            _from='datasource',
            host=model.conn_str.api_host,
            app_key=model.conn_str.app_id,
            app_secret=model.conn_str.app_secret,
            pulsar_key=model.conn_str.app_key,
            pulsar_code=model.conn_str.project_code,
            datasource_name=model.name
        )
        web_time_out = int(os.environ.get('TIMEOUT', 10)) - 1
        try:
            return api.get_data(query_structure).get('data', [])
        except UserError as ue:
            if ue.message.startswith("请求接口超时:"):
                raise DatasetQueryTimeOutException(
                    msg="运行api数据集sql时长超过{}秒，{}".format(str(web_time_out), "请求接口超时"), sql=sql
                ) from ue
            raise DatasetQuerySqlException(msg="运行api数据集内部错误，" + ue.message, sql=sql) from ue


def handle_data_source_field_type(records: list, db_engine: str):
    """
    处理get_table_columns字段记录，设置col_type(DMP字段类型:字符串、数值、日期等),用于视图模式的过滤条件的设置
    :param records:
    :return:
    """
    if not isinstance(records, list):
        return records

    for r in records:
        if not r.get('type'):
            continue
        r['col_type'] = db_engine_transform.get_dmp_data_type_by_db_engine(r['type'], db_engine)
    return records


def handle_data_source_field_type_by_query_model(query_model: ColumnQueryModel, db_engine: str):
    if not isinstance(query_model, ColumnQueryModel):
        return query_model
    if not query_model.items or not isinstance(query_model.items, list):
        return query_model

    query_model.items = handle_data_source_field_type(query_model.items, db_engine)
    return query_model


data_source_client_dict = {
    DataSourceType.ODPS.value: ODPSDataSource,
    DataSourceType.Mysql.value: MySQLDataSource,
    DataSourceType.SaaS.value: SaaSDataSource,
    DataSourceType.MysoftERP.value: MysoftDataSource,
    DataSourceType.API.value: APIDataSource,
    DataSourceType.DataHub.value: DataHubDataSource,
    DataSourceType.PostgreSQL.value: PostgreSQLDataSource,
    DataSourceType.HighData.value: HighDataDataSource,
    DataSourceType.ADS.value: ADSDataSource,
    DataSourceType.MysoftNewERP.value: MysoftNewDataSource,
    DataSourceType.Presto.value: PrestoDataSource,
    DataSourceType.MSSQL.value: MSSQLDataSource,
    DataSourceType.MysoftShuXin.value: MysoftShuXinDataSource,
    DataSourceType.MysoftShuXin15.value: MysoftShuXin15DataSource
}


def get_client(
        data_source_type: str
) -> Union[MysoftDataSource, APIDataSource, PostgreSQLDataSource, MysoftNewDataSource]:
    """
    获取数据
    :param str data_source_type:
    :param str erp_api_info_id:
    :return:
    """
    data_source = data_source_client_dict.get(data_source_type)
    if not data_source:
        raise UserError(message='数据源类型未实现')
    return data_source()


def query_data_of_sql_by_data_source(data_source_id, sql) -> List:
    """
    获取指定数据源中sql执行结果，当前支持sql数据源和api数据源
    :param data_source_id:
    :param sql:
    :return:
    """
    result = repository.get_data(
        "data_source",
        {"id": data_source_id},
        fields=["id", "name", "code", "description", "type", "conn_str", "is_buildin", "icon"],
    )
    result["conn_str"] = json.loads(result.get("conn_str"))
    data_source = DataSourceModel(**result)
    data_source.conn_str_to_model()
    try:
        data_source.conn_str.decrypt()
    except Exception:
        # 部分数据源不需要decrypt
        pass
    service = get_client(data_source.type)
    try:
        result = service.exec_sql(data_source, sql)
    except Exception as e:
        logger.error(f'关键字取数错误： {traceback.format_exc()}')
        raise UserError(message=str(e)) from e
    data = list()
    if isinstance(service, MysoftDataSource):
        result = result.get('data', {}).get('data', [])
    if result:
        key = list(result[0].keys())[0]
        for item in result:
            data.append(item.get(key))
    return data or [None]


relative_type_list = {
    'TODAY': '今天',
    'YESTERDAY': '昨天',
    'FAR': '距今天',
    'FAR_YESTERDAY': '距昨天',
    'FROM_WEEK': '本周',
    'FROM_MONTH': '本月',
    'FROM_QUARTER': '本季',
    'FROM_YEAR': '本年'
}

def convert_var_shuxin15_to_dmp(variables):
    dmp_vars = []
    for var in variables:

        var_type_shuxin15 = var.get('variable_type', '')
        if var_type_shuxin15 == 'number':
            var_type_dmp = DatasetVarVarType.Number.value
        elif var_type_shuxin15 == 'datetime':
            var_type_dmp = DatasetVarVarType.Datetime.value
        else:
            var_type_dmp = DatasetVarVarType.Text.value
        value_type_shuxin15 = var.get('scope_type', '')

        desc = var.get("desc", '')
        static_value_content = var.get('default_datetime_content', []).get('static_value_content', '')
        if value_type_shuxin15 == 'range':
            value_type_dmp = DatasetVarValueType.Section.value
            default_value_list = [static_value_content[0], static_value_content[1]]
            default_value = default_value_list
        elif value_type_shuxin15 == 'sequence':
            value_type_dmp = DatasetVarValueType.List.value
            default_value = var.get('default_string_content', []).get('value', '')
            if default_value == '':
                default_value = '_MYSOFT_BIGDATA_NULL_'
            default_value = [default_value]

        else:
            value_type_dmp = DatasetVarValueType.Value.value
            if var_type_dmp == DatasetVarVarType.Datetime.value:
                dynamic_type = var.get('default_datetime_content', []).get('dynamic_type', '')
                if dynamic_type == "dynamic":  # 动态值 数见不匹配
                    default_value = ''
                    dynamic_value_content = var.get('default_datetime_content', []).get('dynamic_value_content', [])
                    relative_type_key = dynamic_value_content.get('relative_type', '')
                    relative_type_key_value = relative_type_list.get(relative_type_key, relative_type_key)
                    relative_value = dynamic_value_content.get('relative_value', '')
                    desc = f'{desc}:动态类型：{relative_type_key_value}-{relative_value}'
                else:
                    default_value = static_value_content[0]
            else:
                default_value = var.get('default_string_content', []).get('value', '')
        if default_value == '':
            default_value = '_MYSOFT_BIGDATA_NULL_'
        default_value = json.dumps(default_value, ensure_ascii=False)

        dmp_vars.append({
            "id": seq_id(),
            "name": var.get('name', ''),
            "description": f'数芯引入:{desc}',
            "var_type": var_type_dmp,
            "value_type": value_type_dmp,
            "default_value": default_value,
            "accurate_type": 0,
            "keyword_list": [],
            "default_value_type": 2,
            "external_content": json.dumps(var)
        })
    return dmp_vars
