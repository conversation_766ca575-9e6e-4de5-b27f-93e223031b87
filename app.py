# -*- coding: utf-8 -*-
"""
    for description
"""
# pylint: disable=E0401

import json
import logging
import os
import time
from urllib.parse import urlparse
import hug
import regex as re

import patch_falcon  # noqa: E402

patch_falcon.patch()
# 需要保证在import api_route前

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)
os.environ.setdefault('PROMETHEUS_MULTIPROC_DIR', '/tmp')

import app_hook  # noqa: E402
from components.utils import jwt_patch  # noqa: E402

jwt_patch()
# noqa: E402
app_hook.hook_effect()
from components.oss import OSSFileProxy  # noqa: E402
from components.object_caching import ObjectCache  # noqa: E402
from components.storage_setting import set_storage_type, set_dmp_env_sign  # noqa: E402
from base.models import BaseModelEncoder  # noqa: E402
from base.enums import DatasetStorageType, TenantEnvConfig  # noqa: E402
from base.event import register_api  # noqa: E402
from base.dmp_constant import REDIS_TEMPORARY_LOG_STORE_KEY # noqa: E402
from dmplib.utils.errors import ServerError  # noqa: E402
from dmplib.hug import APIWrapper, g  # noqa: E402
from dmplib.constants import REDIS_SINGLE   # noqa: E402
from boot_verb import init_boot_process, api_route as license_api  # noqa: E402
from dmplib.utils.logs import init_logging  # noqa: E402
from dmplib.redis import conn as redis_conn, conn_custom_prefix  # noqa: E402
from dmplib import config  # noqa: E402
from dmplib.metrics.http_metrics import get_metrics_data  # noqa: E402
from base.errors import register_handles  # noqa: E402
from dashboard_tpl import api_route as dashboard_tpl_api  # noqa: E402
from dashboard_chart import share_api_route as share_dashboard_api  # noqa: E402
from feed import api_route as dashboard_feeds_api  # noqa: E402
from dashboard_chart import api_route as dashboard_chart_api  # noqa: E402
from dashboard_chart import released_api_route as released_dashboard_chart_api  # noqa: E402
from dashboard_chart import third_party_api as dashboard_chart_third_jump_api  # noqa: E402
from dashboard_scaffold import api_route as demo_dashboard_api  # noqa: E402
from dashboard_component import api_route as dashboard_component_api  # noqa: E402
from dashboard_snapshot import api_route as dashboard_snapshot_api  # noqa: E402
from open_api.api_route import OpenAPIWrapper  # noqa: E402
from dashboard_chart.report_view_api_route import ReportViewAPIWrapper  # noqa: E402
from user import api_route as user_api  # noqa: E402
from user_group import api_route as user_group_api  # noqa: E402a
from user_role_group import api_route as user_role_group_api  # noqa: E402a
from user import developer_api_route as developer_api_route  # noqa: E402a
from user import workbench_auth_api_route as workbench_auth_api  # noqa: E402
from data_source import api_route as data_source_api  # noqa: E402
from image import api_route as image_api  # noqa: E402
from upload import api_route as upload_api  # noqa: E402
from flow import api_route as flow_api  # noqa: E402
from indicator import api_route as indicator_api  # noqa: E402
from indicator_new import api_route as indicator_new_api  # noqa: E402
from label import api_route as label_api  # noqa: E402
from download import api_route as download_api  # noqa: E402
from dataset import api_route as dataset_api  # noqa: E402
from dataset import api_pulsar_route as dataset_pulsar_api  # noqa: E402
from healthy import api_dashboard as healthy_api  # noqa: E402
from message import api_route as message_api  # noqa: E402
from home_template import api_route as home_template_api  # noqa: E402
from app_menu import api_route as app_menu_api  # noqa: E402
from open_api import api_route as open_api  # noqa: E402
from open_api import omp_api_route as omp_api  # noqa: E402
from open_data import api_route as open_data  # noqa: E402
from open_data import external_api_route as external_route_api  # noqa: E402
from open_data import custom_report_api_route as custom_report  # noqa: E402
from user_log import api_route as user_log_api  # noqa: E402
from system import api_route as system_api  # noqa: E402
from rbac import api_route as rbac_api, data_api_route as rbac_api_route  # noqa: E402
from user import erp_sso_api_route as erp_sso_api  # noqa: E402
from exports import api_route as export_api  # noqa: E402
from async_task import api_route as async_task_api  # noqa: E402
from dashboard_guide import api_route as dashboard_guide_api  # noqa: E402
from dataset.cache.dataset_cache_service import DatasetCache, DatasetRowCache  # noqa: E402
from dashboard_chart.services.dashboard_cache_service import DashboardCache, DashboardReleaseCache  # noqa: E402
from dashboard_chart_embedded import api_route as dashboard_chart_embedded_api  # noqa: E402
from imports import api_route as imports_api  # noqa: E402
from dashboard_template import api_route as dashboard_template_api  # noqa: E402
from self_service import api_route as self_service_api
from keywords import api_route as keyword_api  # noqa: E402
from hd_upgrade import api_route as hd_upgrade_api  # noqa: E402
from hd_api import api_route as hd_api  # noqa: E402
from integrate import api_route as integrate_api  # noqa: E402
from monitor import api_route as monitor_api  # noqa: E402
from merge_metadata import api_route as merge_api  # noqa: E402
from ppt import api_route as ppt_api  # noqa: E402
from manual_filling import api_backend_route as filling_backend_api  # noqa: E402
from manual_filling import api_route as filling_api  # noqa: E402
from master_data import api_route as master_data_api  # noqa: E402
from akso import api_route as akso_api
from publish_center import api_route as publish_center_api# noqa: E402
from func_timing import api_route as func_timing_api  # noqa: E402
from sync_data import api_route as sync_data_api  # noqa: E402
from dashboard_chart import report_view_api_route  # noqa: E402
from mreport import api_route as mreport_api  # noqa: E402
from design_assets import api_route as design_assets_api  # noqa: E402
from cache_page import api_route as cache_page_api  # noqa: E402
from report_center import api_route as report_center_api  # noqa: E402
from open_hw import api_route as hw_produce_api  # noqa: E402
from open_hw.api_route import HwAPIWrapper  # noqa: E402
from dmplib.redis import get_redis_conn     # noqa: E402
from shuxin15_upgrade import api_route as shuxin15_upgrade_api  # noqa: E402
from publish_center.services.publish_center_service import register_publish_center
init_logging()
init_boot_process()

api = APIWrapper(__name__)

logger = logging.getLogger(__name__)
register_handles(api)

if not os.environ.get('CONFIG_AGENT_CLIENT_CODE'):
    os.environ.setdefault('CONFIG_AGENT_CLIENT_CODE', 'dmp_dev')

# 维护object cache模块集合
caches_collection = {
    'dataset': DatasetCache,
    'dataset_row': DatasetRowCache,
    'dashboard': DashboardCache,
    'dashboard_release_info': DashboardReleaseCache,
}


@api.admin_route.get('/metrics')
def handle_metrics(response):
    response.set_header('Content-Type', 'text/plain; charset=utf-8')

    data = get_metrics_data() or ''
    try:
        # 安全问题，禁止输出python版本信息
        data = "".join(data.decode().split("\n")[0:-4]).encode()
    except Exception as e:
        print(e)
    response.body = data


@api.admin_route.post('/cache/delete')
def handle_cache_clean(**kwargs):
    """
        /**
        @apiVersion 1.0.2
        @api    {post} /api/cache/delete 清除ObjectCache类型缓存或一般缓存
        @apiGroup    api
        @apiParam   formData  {string}    project_code 租户code
        @apiParam   formData  {string}    class 模块类名
        @apiParam   formData  {string}    object_id 对象实例id
        @apiParam   formData  {string}    key cache 内容的key
        @@apiParam  formData  {string}    key_prefix redis key前缀
        @apiResponse 200{
            "result": true,
            "msg": "ok"
        }
        **/
    """
    project_code = kwargs.get('project_code')
    cache_class = kwargs.get('class')
    object_id = kwargs.get('object_id')
    key = kwargs.get('key')
    key_prefix = kwargs.get('key_prefix')

    if not project_code:
        return False, '缺少project_code参数', None

    g.code = project_code

    # 清除ObjectCache类型缓存
    if cache_class:
        if not object_id:
            return False, '缺少object_id参数', None
        class_module = caches_collection.get(cache_class)
        if not class_module:
            return False, 'class:%s不存在!' % cache_class, None
        class_module(project_code, object_id, conn_custom_prefix(key_prefix) if key_prefix else redis_conn()).remove()
        return True, 'ok', None

    if not key:
        return False, '缺少key参数', None
    # 根据key清除缓存
    rconn = conn_custom_prefix(key_prefix) if key_prefix else redis_conn()
    ok = rconn.delete(key)
    return True, ok, None


@hug.post('/cache/special_key/del')
def del_cache_of_special_key(**kwargs):
    """
    删除指定的key
    """
    key = kwargs.get("key")
    if not key:
        return False, '缺少key参数', None

    # 根据key清除缓存
    rconn = redis_conn()
    ok = rconn.delete(key)
    return True, ok, None


@api.admin_route.post('/cache/query')
def handle_cache_query(**kwargs):
    """
        /**
        @apiVersion 1.0.2
        @api    {post} /api/cache/query 查询ObjectCache类型缓存
        @apiGroup    api
        @apiParam   formData  {string}    project_code 租户code
        @apiParam   formData  {string}    class 模块类名
        @apiParam   formData  {string}    object_id 对象实例id
        @@apiParam  formData  {string}    key_prefix redis key前缀
        @apiResponse 200{
            "result": true,
            "msg": "ok"
        }
        **/
    """
    project_code = kwargs.get('project_code')
    cache_class = kwargs.get('class')
    object_id = kwargs.get('object_id')
    if not project_code:
        return False, '缺少project_code参数', None
    g.code = project_code
    if not cache_class:
        return False, '缺少class参数', None
    if not object_id:
        return False, '缺少object_id参数', None
    class_module = caches_collection.get(cache_class)
    if not class_module:
        return False, 'class:%s不存在!' % cache_class, None
    rconn = redis_conn()
    cache = ObjectCache(cache_class, project_code, object_id, rconn)
    return True, "ok", cache.getall()


@api.admin_route.post('/cache/key/del')
def del_cache_of_special_key(**kwargs):
    """
    删除指定的key
    """
    g.code = ''
    from components.redis_utils import RCache
    from dataset.cache.extra_cache_redis import ExtraCacheRedis

    cache_redis = kwargs.get("cache_redis")
    key = kwargs.get("key")
    enable_ad = kwargs.get("enable_*")
    if not key:
        return False, '缺少key参数', None

    # 根据key清除缓存
    if not cache_redis:
        rconn = RCache()
    else:
        ExtraCacheRedis.del_by_scan = RCache.del_by_scan
        rconn = ExtraCacheRedis()

    if '*' in key and str(enable_ad) == '1':
        result = rconn.del_by_scan(key)
    else:
        result = {
            key: rconn.delete(key)
        }

    return True, f'msg from: <{rconn._connection}>', result


@api.admin_route.post('/cache/{db}/key_del')
def cache_db_key_del(**kwargs):
    """
    删除指定db的缓存
    """
    g.code = ''
    key = kwargs.get("key")
    db = int(kwargs.get("db"))
    enable_ad = kwargs.get("enable_*")
    if not key:
        return False, '缺少key参数', None

    r_conn = get_redis_conn(db=db, mode=REDIS_SINGLE)
    if '*' in key and str(enable_ad) == '1':
        result = {}
        cur, match_keys = r_conn.scan(match=key, count=10000)
        while match_keys or cur:
            for sub in match_keys:
                sub = sub.decode()
                result[sub] = r_conn.delete(sub)
            match_keys = []
            if cur:
                cur, match_keys = r_conn.scan(cursor=cur, match=key, count=10000)
    else:
        result = {key: r_conn.delete(key)}
    return True, 'ok', result


@api.admin_route.get('/cache/special/key')
def get_cache_of_special_key(**kwargs):
    """
    获取指定的key
    """
    g.code = ''
    from dataset.cache.extra_cache_redis import ExtraCacheRedis
    key = kwargs.get("key")
    cache_redis = kwargs.get("cache_redis")
    key_type = kwargs.get("key_type", 'get')
    if not key:
        return False, '缺少key参数', None

    if not cache_redis:
        # 默认的缓存redis
        rconn = redis_conn()
    else:
        # 额外的缓存redis
        rconn = ExtraCacheRedis()
    result = getattr(rconn, key_type, "get")(key)

    def type_deal(data):
        if isinstance(data, bytes):
            return data.decode()
        elif isinstance(data, list):
            results = []
            for one in data:
                results.append(type_deal(one))
            return results
        else:
            return data

    return True, f'msg from: <{rconn._connection}>', type_deal(data=result)


@hug.post('/cache/anything')
def get_cache_of_anything(request, response, **kwargs):
    """
    cache 操作
    """
    use_cache_redis = kwargs.get("use_cache_redis", 0)
    domain = kwargs.get("domain", '')
    action = kwargs.get("action", '')
    db = kwargs.get("db", config.get('Redis.db'))
    iargs = kwargs.get("iargs", '[]')  # noqa
    ikwargs = kwargs.get("ikwargs", '{}')  # noqa
    logger.error(f'request.forwarded_host={request.forwarded_host}')
    if (
            not domain
            or not action
            or domain != request.forwarded_host
    ):
        response.status = hug.falcon.HTTP_404
        response.body = hug.falcon.HTTP_404
        return

    class RawRedis:
        def __init__(self, use_cache_redis, iargs, ikwargs):
            self.errors = []
            self.use_cache_redis = use_cache_redis
            self.raw_redis_conn = self._get_redis_conn()
            self.real_redis_func = None
            self.iargs = self._load_args(iargs, name='iargs')
            self.ikwargs = self._load_args(ikwargs, name='ikwargs')

        def _get_redis_conn(self):
            import redis as pyredis
            try:
                if not self.use_cache_redis:
                    return get_redis_conn(db=db, mode=REDIS_SINGLE)
                else:
                    return pyredis.StrictRedis.from_url(
                        url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                            host=config.get('CacheRedis.host'),
                            port=config.get('CacheRedis.port'),
                            username=config.get('CacheRedis.username') or '',
                            db=int(config.get('CacheRedis.db', 2)),
                            password=config.get('CacheRedis.password') or ''
                        ),
                        socket_timeout=30,
                        decode_responses=True
                    )
            except Exception as e:
                self.errors.append({"连接redis失败": str(e)})

        def _load_args(self, value, name='iargs'):
            errors = self.errors
            if isinstance(value, (list, dict)):
                return value
            try:
                value = json.loads(value)
                if name == 'iargs':
                    value = tuple(value)
            except Exception as e:
                errors.append({f"反序列化{name}失败": str(e)})
                value = []
            return value

        def add_action(self, action):
            if not self.raw_redis_conn:
                return self
            self.real_redis_func = getattr(self.raw_redis_conn, action, None)
            if not self.real_redis_func:
                self.errors.append({"不存在的key名": action})
            return self

        def do(self):
            if self.errors:
                return {"data": [], "errors": self.errors}
            else:
                st = time.time()
                try:
                    value = self.real_redis_func(*self.iargs, **self.ikwargs)
                except Exception as e:
                    self.errors.append({"redis取数错误": str(e)})
                    value = []
                dr = int((time.time() - st) * 1000)
                # finally:
                #     self.raw_redis_conn.connection_pool.disconnect()
                if isinstance(value, set):
                    value = list(value)
                return {"data": value, "errors": self.errors, "timing": f'{dr:.2f}ms'}

    raw_redis_client = RawRedis(use_cache_redis=use_cache_redis, iargs=iargs, ikwargs=ikwargs)
    result = raw_redis_client.add_action(action).do()

    return True, f'msg from: <{raw_redis_client.raw_redis_conn}>', result


@hug.get('/cache/keys/match')
def get_cache_keys_match(**kwargs):
    """
    get keys
    :param kwargs:
    :return:
    """
    g.code = ''
    key = kwargs.get("key") or ''
    if not key:
        return False, '缺少key参数', None
    key = f"*{key.strip('*')}*"

    # 根据key获取缓存
    r_conn = redis_conn()
    cur, match_keys = r_conn.scan(match=key, count=1000)
    result = []
    while match_keys or cur:
        for sub in match_keys:
            sub = sub.decode()
            result.append(sub)
        match_keys = []
        if cur:
            cur, match_keys = r_conn.scan(cursor=cur, match=key, count=1000)
    return True, 'ok', result


@api.admin_route.get('/test_mdc_query')
def mdc_query_test(**kwargs):
    from components.mysoft import multi
    return True, 'ok', multi()


@api.admin_route.get('/tenant/update')
def update_tenant_storage_type(**kwargs):
    """
        /**
        @apiVersion 1.0.2
        @api    {get} /api/storage/update 更新租户拍照数据集存储方式缓存
        param dict kwargs:
        @apiResponse 200{
            "result": true,
            "msg": "ok"
        }
        **/
    """
    project_code = kwargs.get("project_code")
    storage = kwargs.get("storage_type")
    dmp_env_sign = kwargs.get("dmp_env_sign")
    if not project_code:
        return False, '缺少project_code参数', None
    if storage not in [
        '', DatasetStorageType.DatasetStorageOfCloud.value, DatasetStorageType.DatasetStorageOfLocal.value
    ]:
        return False, '不支持的storage_type类型', None
    if dmp_env_sign not in [
        "", TenantEnvConfig.HD.value, TenantEnvConfig.CLOUD.value, TenantEnvConfig.SHUJIAN.value,
        TenantEnvConfig.DMP.value
    ]:
        return False, '不支持的dmp_env_sign类型', None
    try:
        if storage:
            set_storage_type(project_code, storage)
        if dmp_env_sign:
            set_dmp_env_sign(project_code, dmp_env_sign)
    except Exception as e:
        logger.error("update storage type error: %r", e)
        return False, 'update storage type error', None
    return True, "ok", None


@api.admin_route.get('/idc/account')
def update_tenant_storage_type(**kwargs):
    """
        /**
        @apiVersion 1.0.2
        @api    {get} /idc/account 获取云服务器的账户id
        param dict kwargs:
        @apiResponse 200{
            "result": true,
            "msg": "ok"
        }
        **/
    """
    import requests
    cloud_map = dict(
        ali='http://100.100.100.200/latest/meta-data/owner-account-id',
        tencent='http://metadata.tencentyun.com/latest/meta-data/app-id',
        huawei='http://169.254.169.254/openstack/latest/meta_data.json',
    )

    def do_request(url):
        try:
            return requests.get(url, timeout=3).text
        except:
            return ''

    result = {name: do_request(url) for name, url in cloud_map.items()}
    return True, "ok", result


@hug.extend_api('/monitor', api.api)
def monitor_api_route():
    return [monitor_api]


@hug.extend_api('/ppt', api.api)
def ppt_api_route():
    return [ppt_api]


@hug.extend_api('/user', api.api)
def user_api_route():
    return [user_api]


@hug.extend_api('/user_group', api.api)
def user_group_api_route():
    return [user_group_api]


@hug.extend_api('/user_role_group', api.api)
def user_role_group_api_route():
    return [user_role_group_api]


@hug.extend_api('/developer', api.api)
def user_developer_api_route():
    return [developer_api_route]

@hug.extend_api('/data_source', api.api)
def data_source_api_route():
    return [data_source_api]


@hug.extend_api('/flow', api.api)
def flow_api_route():
    return [flow_api]


@hug.extend_api('/dashboard', api.api)
def feature_dashboard_modules():
    return [dashboard_chart_api]


@hug.extend_api('/dashboard_chart', api.api)
def dashboard_chart():
    return [dashboard_chart_api]


@hug.extend_api('/released_dashboard_chart', api.api)
def released_dashboard_chart():
    return [released_dashboard_chart_api]


@hug.extend_api('/dashboard_chart_third_jump', api.api)
def dashboard_chart_third_jump():
    return [dashboard_chart_third_jump_api]


@hug.extend_api('/dashboard_tpl', api.api)
def feature_dashboard_tpl_modules():
    return [dashboard_tpl_api]


@hug.extend_api('/share_dashboard', api.api)
def feature_share_dashboard_modules():
    return [share_dashboard_api]


@hug.extend_api('/released_dashboard', api.api)
def handle_released_dashboard():
    return [released_dashboard_chart_api]


@hug.extend_api('/dashboard_feeds', api.api)
def dashboard_feeds_modules():
    return [dashboard_feeds_api]


@hug.extend_api('/demo', api.api)
def demo_dashboard_modules():
    """
    报告编辑器组件模拟器示例接口模块
    :return:
    """
    return [demo_dashboard_api]


@hug.extend_api('/component', api.api)
def dashboard_component_modules():
    """
    组件模块
    :return:
    """
    return [dashboard_component_api]


@hug.extend_api('/dashboard_snapshot', api.api)
def dashboard_snapshot_modules():
    """
    报告拍照模块
    :return:
    """
    return [dashboard_snapshot_api]


@hug.extend_api('/design_assets', api.api)
def indicator_modules():
    return [design_assets_api]


@hug.extend_api('/indicator', api.api)
def indicator_modules():
    return [indicator_api]


@hug.extend_api('/indicator_new', api.api)
def indicator_new_modules():
    return [indicator_new_api]


@hug.extend_api('/label', api.api)
def label_modules():
    return [label_api]


@hug.extend_api('/download', api.api)
def handle_download():
    return [download_api]


@hug.extend_api('/upload', api.api)
def handle_upload():
    return [upload_api]


# 兼容 前端老接口
@hug.extend_api('/image', api.api)
def upload_image():
    return [image_api]


@hug.extend_api('/dataset', api.api)
def dataset_api_route():
    return [dataset_api]


@hug.extend_api('/pulsar_api', api.api)
def pulsar_api_route():
    return [dataset_pulsar_api]


@hug.extend_api('/healthy', api.api)
def healthy_api_route():
    return [healthy_api]


# 消息
@hug.extend_api('/message', api.api)
def message_api_route():
    return [message_api]


@hug.extend_api('/home_page', api.api)
def home_template_api_route():
    return [home_template_api]


@hug.extend_api('/app_menu', api.api)
def app_menu_api_route():
    return [app_menu_api]


@hug.extend_api('/open_data', api.api)
def open_data_route():
    return [open_data]


@hug.extend_api('/external', api.api)
def open_data_route():
    return [external_route_api]


@hug.extend_api('/custom_report', api.api)
def custom_report_route():
    return [custom_report]


@hug.extend_api('/user_log', api.api)
def user_log_api_route():
    return [user_log_api]


@hug.extend_api('/rbac', api.api)
def rbac_api_rout():
    return [rbac_api, rbac_api_route]


@hug.extend_api('/system', api.api)
def system_api_rout():
    return [system_api]


@hug.extend_api('/dashboard_component', api.api)
def component_api_route():
    return [dashboard_component_api]


@hug.extend_api('/async_task', api.api)
def async_task_api_route():
    return [async_task_api]


@hug.extend_api('/export', api.api)
def export_api_route():
    return [export_api]


@hug.extend_api('/imports', api.api)
def imports_api_route():
    return [imports_api]


@hug.extend_api('/sso', api.api)
def erp_sso_api_route():
    return [erp_sso_api]


@hug.extend_api('/sso-auth', api.api)
def erp_sso_auth_api_route():
    return [erp_sso_api]


@hug.extend_api('/dashboard_guide', api.api)
def handle_dashboard_guide():
    return [dashboard_guide_api]


@hug.extend_api('/dashboard_template', api.api)
def handle_dashboard_template():
    return [dashboard_template_api]


@hug.extend_api('/self_service', api.api)
def self_service():
    return [self_service_api]


@hug.extend_api('/keywords', api.api)
def keywords():
    return [keyword_api]


@hug.extend_api('/hd_upgrade', api.api)
def hd_upgrade():
    return [hd_upgrade_api]


@hug.extend_api('/hd_api', api.api)
def hd_get_data_api():
    return [hd_api]


@hug.extend_api('/integrate', api.api)
def integrate_route():
    return [integrate_api]


@hug.extend_api('/dashboard_chart_embedded', api.api)
def integrate_route():
    return [dashboard_chart_embedded_api]


@hug.extend_api('/license', api.api)
def self_service():
    return [license_api]


@hug.extend_api('/manual_filling', api.api)
def manual_filling_route():
    return [filling_backend_api, filling_api]


@hug.extend_api('/akso', api.api)
def manual_filling_route():
    return [akso_api]


@hug.extend_api('/publish_center', api.api)
def publish_center_route():
    return [publish_center_api]


@hug.extend_api('/mreport', api.api)
def manual_filling_route():
    return [mreport_api]


@hug.extend_api('/cache_page', api.api)
def cache_page():
    return [cache_page_api]


@hug.extend_api('/func_timing', api.api)
def cache_page():
    return [func_timing_api]


@hug.extend_api('/sync_data', api.api)
def cache_page():
    return [sync_data_api]


@api.admin_route.get("/static/{page}", output=hug.output_format.html)
def dashboard_render(page=None):
    import os
    if not page.endswith(".html"):
        page = f'{page}.html'
    file_name = os.path.join("./static", page)
    if not os.path.exists(file_name):
        return hug.redirect.to("/")
    with open(file_name, 'r', encoding="utf-8") as f:
        return f.read()


@hug.extend_api('/merge')
def merge_metadata():
    return [merge_api]


@hug.extend_api('/master_data', api.api)
def master_data():
    return [master_data_api]


@hug.extend_api('/report_center', api.api)
def report_center_modules():
    return [report_center_api]

@hug.extend_api('/shuxin15_upgrade', api.api)
def shuxin15_upgrade():
    return [shuxin15_upgrade_api]


@hug.extend_api('/omp', api.api)
def omp():
    return [omp_api]


@hug.extend_api('/workbench_auth', api.api)
def workbench_auth():
    return [workbench_auth_api]

hwProduceApi = HwAPIWrapper(__name__)
@hug.extend_api('', hwProduceApi.api)
def produce_api_route():
    return [hw_produce_api]

openapi = OpenAPIWrapper(__name__)


@hug.extend_api('', openapi.api)
def open_api_route():
    return [open_api]


report_view_api = ReportViewAPIWrapper(__name__)


@hug.extend_api('', report_view_api.api)
def report_view():
    return [report_view_api_route]



@hug.default_output_format(content_type='application/json', apply_globally=True)
def custom_output(data, request=None, response=None):  # pylint:disable=unused-argument
    """
    格式化固定格式输出, 如果返回的数据为2或3个元素的元组则这按输出{'result': true/false, 'msg': '', 'data': ''},
    其他返回数据或类型则按原json输出
    """
    if request.path.startswith("/api/akso"):
        res = {
            "errcode": data[0],
            "errmsg": data[1],
            "data": data[2]
        }
        return json.dumps(res).encode('utf8')
    if isinstance(data, tuple) and 1 < len(data) <= 3:
        result = data[0]
        if not isinstance(result, bool) and not isinstance(result, int):
            raise ServerError('无效的返回类型,元组第一个元素必须是bool或int类型')
        rv = {'result': bool(result), 'msg': data[1], 'data': data[2] if len(data) == 3 else None}
        assign_profiling(rv)
        res = json.dumps(rv, cls=BaseModelEncoder, ensure_ascii=False)
        # 临时方案， 处理oss私有化
        return auth_url(res)
    # hug自带参数校验返回格式转换 examples:{"errors": {"minute": "Required parameter 'minute' not supplied"}}
    elif isinstance(data, dict) and 'errors' in data:
        msg_data = []
        result = False
        for name, value in data['errors'].items():
            msg_data.append('%s: %s' % (name, value))
        msg = ', '.join(msg_data)
        rv = {'result': result, 'msg': msg, 'data': None}
        assign_profiling(rv)
        return json.dumps(rv, cls=BaseModelEncoder).encode('utf8')
    elif '__dict__' in dir(data):
        return json.dumps(data.__dict__).encode('utf8')
    return json.dumps(data, cls=BaseModelEncoder).encode('utf8')


def assign_profiling(rv):
    """附加profiling"""
    if hasattr(g, "profiling"):
        rv["profiling"] = {}
        for v in ['sqls', 'logs', 'redis', 'query']:
            rv["profiling"][v] = getattr(g, v, '')


@hug.response_middleware()
def assign_profiling_to_unjson_middleware_method(request, response, resource):
    """
    有些返回不是json的response，添加profiling，例如302重定向
    headers中添加一个查看临时日志的url
    """
    if not hasattr(g, "profiling"):
        return

    profiling = {}
    for v in ['sqls', 'logs', 'redis', 'query']:
        profiling[v] = getattr(g, v, '')
    if not profiling:
        return

    try:
        conn = redis_conn()
        uuid = str(time.time())
        key = f'{REDIS_TEMPORARY_LOG_STORE_KEY}:{uuid}'
        conn.set(key, json.dumps(profiling).encode(), 5 * 60)
        domain = config.get('Domain.dmp', '')
        response.set_header('Tmp-Log-Url', f'{domain}/api/monitor/tmp_log?uuid={uuid}')
    except Exception as e:
        logger.error(f'设置返回的profiling失败：{str(e)}')


def auth_url(data):
    is_private = int(config.get('OSS_Config.is_private', 0))
    if not is_private or not config.get('OSS_Config.service').upper() in ['OSS', 'MINIO']:
        return data.encode('utf8')

    access_expires = int(config.get('OSS_Config.access_expires', 31536000))
    oss = OSSFileProxy()
    endpoint = urlparse(oss.proxy.endpoint)
    bucket = oss.proxy.bucket if isinstance(oss.proxy.bucket, str) else oss.proxy.bucket.bucket_name
    def __replace(match):
        value = match.group("value")
        newVal = value
        if value.endswith("\\"):
            newVal = value[:-1]
        sigh_url = oss.get_sigh_url(newVal, access_expires, is_url=True)
        if not sigh_url:
            return value
        if value.endswith("\\"):
            return sigh_url + '&\\'
        else:
            return sigh_url + '&'

    if config.get('OSS_Config.service').upper() == "OSS":
        pattern = r"(?P<value>(?<=\")(?:https|http):\/\/(?:{bucket}|)\.{endpoint}[^\"].*?)(?=\")".format(
            bucket=bucket, endpoint=endpoint.netloc.replace(".", "\."))
    else:
        # minio最大过期时间七天
        access_expires = 604800 if access_expires > 604800 else access_expires
        pattern = r"(?P<value>(?<=\")(?:https|http):\/\/(?:{endpoint}|)\/{bucket}[^\"].*?)(?=\")".format(
            bucket=bucket, endpoint=endpoint.netloc.replace(".", "\."))


    data = re.sub(pattern, __replace, data)
    return data.encode('utf8')


license_api.patch_lic(api)
register_api()
# register_publish_center()

