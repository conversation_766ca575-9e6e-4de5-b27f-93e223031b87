#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/8
"""
from dashboard_chart.convertor.group.group import Group
from dashboard_chart.convertor.select.select import Select
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.models import ChartDataModel
from dashboard_chart.convertor.select.num_select import NumSelect


class AssistChart(CommonChart):
    """
    注意：组件单图类继承的是CommonChart类，因AssistChart和CommonChart类中get_chart_data方法
    内容经过重构后是一样的，只有get_select_fields，get_group_fields不一样，故不再单独实现get_chart_data
    """

    def get_select_fields(self, model: ChartDataModel = None):
        real_model = model or self.chart_data_model
        select_fields = []
        origin_selects = Select.get_select_fields_for_assist(real_model)
        select_fields.extend(origin_selects)
        if real_model.nums:
            num_select = NumSelect()
            select_fields.extend(num_select.get_select_fields(real_model))
        self.select_fields = select_fields
        return self.select_fields

    def get_group_fields(self, model: ChartDataModel = None):
        real_model = model or self.chart_data_model
        # 只有聚合才会有group
        if real_model.aggregation:
            self.group_fields = Group.get_group_fields_for_assist(real_model)
        else:
            self.group_fields = []
        return self.group_fields
