#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=E0203,W0613
"""
    <NAME_EMAIL> 2018/9/8
"""
import pandas as pd
from loguru import logger

from base.models import BaseModel
from base.enums import DataQueryMode
from dashboard_chart.data_query.charts.chart_query import ChartQuery
from dashboard_chart.data_query.widget.dataset_query import DatasetQuery
from dashboard_chart.data_query.widget.subtotal.subtotal_col_query import SubtotalColQuery
from dashboard_chart.data_query.widget.subtotal.subtotal_row_query import SubtotalRowQuery
from dashboard_chart.utils.common import add_api_dataset_params
from typing import Tuple, List, Dict
from dmplib.hug import g
from dmplib.utils.errors import UserError
from components.analysis_time import AnalysisTimeUtils

from dashboard_chart.models import SubtotalResult

query_values_map = {key: val.value for key, val in DataQueryMode.__members__.items()}


def set_query_mode(kwargs):
    # 设置取数模式
    mode = kwargs.get('mode')
    if mode in query_values_map.values():
        g.query_mode = mode
    else:
        g.query_mode = DataQueryMode.ORIGIN.value


def get_curr_mode():
    # 获取取数模式
    if hasattr(g, 'query_mode'):
        query_mode = getattr(g, 'query_mode')
        # if query_mode == DataQueryMode.DETAIL.value:
        #     return DataQueryMode.DETAIL.value
        # elif query_mode == DataQueryMode.SUMMARY.value:
        #     return DataQueryMode.SUMMARY.value
        if query_mode in query_values_map.values():
            return query_mode
        else:
            return DataQueryMode.ORIGIN.value
    else:
        return DataQueryMode.ORIGIN.value


class CommonChart(ChartQuery):
    def get_advance_num(self):
        """
        获取查询条数
        :return:
        """
        return int(bool(self.chart_data_model.enable_subtotal_col))

    def get_chart_data(self, dataset_version: str = None):
        query_mode = get_curr_mode()

        add_api_dataset_params(g, dataset_id=self.chart_data_model.dataset_id,
                               report_id=self.chart_data_model.dashboard_id,
                               dataset=self.chart_data_model.dataset)

        if query_mode == DataQueryMode.SUMMARY.value:
            return self.direct_get_col_summary_data()
        elif query_mode == DataQueryMode.DETAIL.value:
            # 只获取明细的会在后面总计中处理
            return self.origin_get_chart_data()
        else:
            # 走原来的逻辑
            return self.origin_get_chart_data()

    def origin_get_chart_data(self, dataset_version: str = None):
        AnalysisTimeUtils.recode_time_node('开始取主SQL数据')
        # 1. 验证字段是否正确
        self.validation_dataset_fields(self.get_need_validation_fields())

        advance_num = self.get_advance_num()

        # 2. 查询数据
        result = self.query_data(advance_next=advance_num, dataset_version=dataset_version)
        AnalysisTimeUtils.recode_time_node('结束取主SQL数据')

        # 3. 设置data数据
        data = self.get_data(result, advance_num)

        return data

    def direct_get_col_summary_data(self):
        # 直接获取列总计的数据
        # 取数接口取数拆分（！！！！！！！！！！！！！目前只支持没有对比维度，只配置列总计）：
        # 原有的取数接口包含
        # 明细 + 行、列小计数据 + 列总计数据， 现将部分数据分开获取
        # 1. 验证字段是否正确
        if self.chart_data_model.comparisons:
            raise UserError(message=f'当前取数模式[{get_curr_mode()}]不支持设置对比维度！')
        self.validation_dataset_fields(self.get_need_validation_fields())
        advance_num = self.get_advance_num()
        # result = {'data': [self._mock_detail_data()]}
        result = {'data': [], 'code': 200}
        data = self.get_data(result, advance_num)
        data['data'] = []
        return data

    # def _mock_detail_data(self):
    #     selects = self.get_select_fields()
    #     return {
    #         select.alias: 0 if select.logic_source == 'num' else 0
    #         for select in selects
    #     }

    def query_underlying_data(self):
        # 1. 验证字段是否正确
        self.validation_dataset_fields(self.get_need_validation_fields())

        advance_num = self.get_advance_num()

        # 2. 查询底层信息
        return self.get_query_underlying_data(advance_next=advance_num)

    def get_variable_chart_data(self):
        """
        根据前端传入参数灵活查询数据接口（前端传入dim、num等可以直接替换当前单图的对应字段数据后进行查询）
        :return:
        """
        # 1. 验证字段是否正确
        self.validation_dataset_fields(self.get_need_validation_fields())

        advance_num = self.get_advance_num()

        # 2. 查询数据
        result = self.query_data(advance_next=advance_num)

        # 3. 设置data数据
        data = self.get_variable_data(result, advance_num)

        return data

    def get_query_struct(self):
        """
        获取查询结构(完整结构)
        :return:
        """
        # 1. 验证字段是否正确
        self.validation_dataset_fields(self.get_need_validation_fields())

        # 2. 获取查询结构
        struct = self.query_struct()

        # 3. 设置sturct数据
        return self.get_struct(struct)

    def get_item_list(self):
        """
        获取
        :return:
        """
        dataset_query = DatasetQuery(chart_data_model=self.chart_data_model, user_id=self.get_user_id())
        result = dataset_query.get_item_list(self.get_limit_field())
        # 对结果进行加工
        data = result.get("data")
        if data:
            result["data"] = self.restructure_chart_data(pd.DataFrame(data), data)
        return result

    def get_subtotal_for_data(
            self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
            prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        获取小计
        :param data:
        :param next_item:
        :return:
        """
        original_where_fields = self.get_where_fields()
        subtotal_col_obj = SubtotalColQuery(
            chart_data_model=self.chart_data_model,
            origin_select_fields=self.get_select_fields(),
            origin_where_fields=original_where_fields,
            origin_group_fields=self.get_group_fields(),
            origin_order_fields=self.get_order_fields_for_multi(self.chart_data_model),
            query_vars=self.get_dataset_vars(self.chart_data_model),
            user_id=self.get_user_id(),
        )
        subtotal_row_obj = SubtotalRowQuery(
            self.chart_data_model,
            original_where_fields,
            self.get_dataset_vars(self.chart_data_model),
            self.get_user_id(),
        )
        AnalysisTimeUtils.recode_time_node('开始计算列总、小计')
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.subtotal_col_process.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            subtotal_col_result = subtotal_col_obj.get_data(data_frame, next_item_frame, prev_item_frame)
        AnalysisTimeUtils.recode_time_node('结束计算列总、小计')
        AnalysisTimeUtils.recode_time_node('开始计算行小计')
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.subtotal_row_process.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            subtotal_row_result, real_data, subtotal_col_result = subtotal_row_obj.get_data(data_frame, subtotal_col_result)
        AnalysisTimeUtils.recode_time_node('结束计算行小计')
        subtotal_result = SubtotalResult(**{'subtotal_col': subtotal_col_result, 'subtotal_row': subtotal_row_result})
        return self._data2dict(subtotal_result), real_data, original_data

    def _data2dict(self, data):
        """
        混合数据转换为字典
        :return:
        """
        if isinstance(data, BaseModel):
            data = data.get_dict()
            for key, item in data.items():
                data[key] = self._data2dict(item)
        elif isinstance(data, dict):
            for key, item in data.items():
                data[key] = self._data2dict(item)
        elif isinstance(data, (list,)):
            for index, item in enumerate(data):
                data[index] = self._data2dict(item)
        return data
