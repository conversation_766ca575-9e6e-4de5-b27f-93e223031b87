#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/8
"""
import collections
from typing import Tuple, List, Dict, Union

import pandas as pd
from base.dmp_constant import EXCEL_TABLE_CELL_MAX_SIZE
from dashboard_chart.convertor.group.group import Group
from dashboard_chart.convertor.limit.limit import Limit
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.models import ChartDataModel
from dmplib.utils.errors import UserError
from base.enums import DatasetConnectType


class ExcelTableChart(CommonChart):
    def __init__(self, model):
        super().__init__(model)
        if not model.aggregation:
            raise UserError(message="逻辑错误，透视表必须为聚合类型!")
        self.dim_type = 1
        self.compare_type = 2
        self.num_type = 3

    def apply_funcs(self, db_data: list):
        """
        该组件无其他辅助功能(如标准对比维度)
        :param db_data:
        :return:
        """
        return pd.DataFrame(db_data), db_data

    def get_group_fields(self, model: ChartDataModel = None):
        """
        获取Group对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        self.group_fields = Group.get_group_fields_for_excel_table(self.chart_data_model)
        return self.group_fields

    def get_limit_field(self, model: ChartDataModel = None):
        """
        获取Limit对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        self.limit_field = Limit.get_limit_field_for_excel_table()
        return self.limit_field

    def restructure_chart_data(self, data: pd.DataFrame, original_data: List[Dict]) -> Union[List, Dict]:
        """
        重新组织data数据满足各个单图类型数据结构
        :param pd.DataFrame data: 转换后的数据
        :param List original_data: 原始数据
        :return:
        """
        excel_data = []
        # 因get_data接口也需要调用，而get_data获取不需要这个透视表数据结构
        if len(original_data) and self.chart_data_model.comparisons:
            excel_data = self._get_excel_data(original_data)
        return excel_data

    def _get_excel_data(self, excel_data):
        """
        返回透视表特定树形列数据结构数据
        :param excel_data:
        :return:
        """
        # 1. 对dim,compare,num进行排序
        dims, sorted_dims = self.get_col_dict(self.chart_data_model.dims)
        compares, sorted_compares = self.get_col_dict(self.chart_data_model.comparisons)
        nums, sorted_nums = self.get_col_dict(self.chart_data_model.nums)
        sorted_compares = [sorted_compares.get(v) for v in sorted(sorted_compares.keys(), reverse=True)]
        sorted_dims = [sorted_dims.get(v) for v in sorted(sorted_dims.keys())]
        sorted_nums = [sorted_nums.get(v) for v in sorted(sorted_nums.keys())]

        result = collections.OrderedDict()
        result_data = []

        # 2. 根据每一行数据进行列数据的组装
        dim_value_dict = self._get_each_row_result_data(
            **{
                "excel_data": excel_data,
                "nums": nums,
                "sorted_compares": sorted_compares,
                "sorted_dims": sorted_dims,
                "sorted_nums": sorted_nums,
                "result": result,
                "result_data": result_data,
            }
        )

        dim_dict = collections.OrderedDict()
        for dim_col_name, dim_value_item in dim_value_dict.items():
            dim_dict[dim_col_name] = self.get_unique_list_data(dim_value_item)

        dim_inmost_item_list = []
        sorted_dims.reverse()
        for dim_col_name, dim_value_item in dim_dict.items():
            tmp_dim_key = []
            dim_col_value = self._get_alias_name_by_field(dims.get(dim_col_name))
            dim_item, innermost_dim_item = self._recursion_get_compares(
                excel_data[0], len(sorted_compares) - 1, dim_col_value, tmp_dim_key, is_dim=True, ltype=self.dim_type
            )
            result_data.append(dim_item)
            dim_inmost_item_list.append(innermost_dim_item)

        # 设置维度列的值并且返回x轴的行标记（key）
        x_value_list = self._get_dim_item_data(dim_value_dict, dim_inmost_item_list)
        # 兼容没有维度（行标签）情况
        if not x_value_list:
            x_value_list = [""]

        # 根据值标签返回每个值标签的汇总数据列表
        self._get_sum_data_by_num(
            **{
                "sorted_nums": sorted_nums,
                "row": excel_data[0],
                "sorted_compares": sorted_compares,
                "nums": nums,
                "x_value_list": x_value_list,
                "result": result,
                "result_data": result_data,
            }
        )
        # 超过限制条目数则直接跳过剩余逻辑，直接返回空数组
        if self.over_limit_flag:
            return []
        # data数据从dict转变为list结构，同时进行行汇总计算
        for num_tree_value, value_item in result.items():
            sum_x_value = 0
            tmp_num_data = []
            for x_key in x_value_list:
                value = value_item["data"].get(x_key)
                if value is not None:
                    sum_x_value += float(value)
                tmp_num_data.append(value)
            value_item["data"] = tmp_num_data
            # 返回行汇总数据（表格最后一行展示数据）
            value_item["data"].append(sum_x_value)

        # 返回数据前处理行标签下的字符串None
        self._recursive_handle_none_value(result_data)

        return result_data

    def _get_dim_item_data(self, dim_value_dict, dim_inmost_item_list):
        """
        设置维度列的值并且返回x轴的行标记（key）
        :param dim_value_dict:
        :param dim_inmost_item_list:
        :return:
        """
        dim_key_list = []
        dim_result = collections.OrderedDict()
        i = 0
        for dim_col_name, dim_value_list in dim_value_dict.items():
            dim_key_list.append(dim_value_list)
            for dim_value in dim_value_list:
                tmp_dim_item = {"name": dim_inmost_item_list[i]["name"], "type": self.dim_type, "child": [], "data": []}
                if i not in dim_result:
                    dim_inmost_item_list[i]["child"].append(tmp_dim_item)
                    dim_result[i] = tmp_dim_item
                dim_result[i]["data"].append(dim_value)
            i += 1
        if dim_key_list:
            dim_key_list = zip(*dim_key_list)
        return ["_".join(n) for n in dim_key_list]

    @staticmethod
    def _get_num_sum_num_value(num_tree, dim_tree_value, sum_num_value_dict):
        for num_tree_value, value_item in num_tree.items():
            data_item = value_item["data"]
            if dim_tree_value not in data_item.keys():
                data_item[dim_tree_value] = None
            # 返回列汇总数据（表格最后几列展示数据）
            tmp_row_item_value = float(0 if data_item[dim_tree_value] is None else data_item[dim_tree_value])
            if value_item.get("name") not in sum_num_value_dict:
                sum_num_value_dict[value_item.get("name")] = collections.OrderedDict()
            if dim_tree_value not in sum_num_value_dict[value_item.get("name")]:
                sum_num_value_dict[value_item.get("name")][dim_tree_value] = 0
            sum_num_value_dict[value_item.get("name")][dim_tree_value] += tmp_row_item_value
        return sum_num_value_dict

    def _append_none_item_and_sum_data_by_dim(self, x_value_list, result):
        """
        填充数据为空的项到data中并根据值标签返回每个值标签的汇总数据列表
        :param x_value_list:
        :param result:
        :return:
        """
        sum_num_value_dict = collections.OrderedDict()
        # 判断是否行列相乘大于50000则退出报错
        if x_value_list and result and len(x_value_list) * len(result) > EXCEL_TABLE_CELL_MAX_SIZE:
            self.over_limit_flag = True
            return sum_num_value_dict
        for dim_tree_value in x_value_list:
            self._get_num_sum_num_value(result, dim_tree_value, sum_num_value_dict)
        # 列汇总最后汇总每单个汇总的值
        for k, sum_num_item in sum_num_value_dict.items():
            sum_num_item["汇总"] = sum(sum_num_item.values())

        return sum_num_value_dict

    def _get_sum_data_by_num(self, **kwargs):
        """
        填充数据为空的项到data中并根据值标签返回每个值标签的汇总数据列表
        :param sorted_nums:
        :param row:
        :param sorted_compares:
        :param nums:
        :param x_value_list:
        :param result:
        :param result_data:
        :return:
        """
        sorted_nums = kwargs.get("sorted_nums")
        row = kwargs.get("row")
        sorted_compares = kwargs.get("sorted_compares")
        nums = kwargs.get("nums")
        x_value_list = kwargs.get("x_value_list")
        result = kwargs.get("result")
        result_data = kwargs.get("result_data")
        # 填充数据为空的项到data中并根据值标签返回每个值标签的汇总数据列表
        sum_num_value_dict = self._append_none_item_and_sum_data_by_dim(x_value_list, result)
        if self.over_limit_flag:
            return
        for num_col_name in sorted_nums:
            num_alias_name = self._get_alias_name_by_field(nums.get(num_col_name))
            sum_num_item, sum_num_innermost_item = self._recursion_get_compares(
                row, len(sorted_compares) - 1, num_alias_name, [], is_dim=True, ltype=self.num_type
            )
            sum_num_value_list = sum_num_value_dict.get(num_alias_name)
            col_name = (
                nums.get(num_col_name).get("alias")
                if nums.get(num_col_name).get("alias")
                else nums.get(num_col_name).get("col_name")
            )
            tmp_num_sub_item = {
                "col_name": col_name,
                "name": num_alias_name,
                "type": self.num_type,
                "child": [],
                "data": list(sum_num_value_list.values()) if sum_num_value_list else [],
            }
            # 列汇总（值汇总）最外层添加col_name
            sum_num_item["col_name"] = nums.get(num_col_name).get("col_name")
            sum_num_innermost_item["child"].append(tmp_num_sub_item)
            result_data.append(sum_num_item)

    def _get_each_row_result_data(self, **kwargs):
        excel_data = kwargs.get("excel_data")
        nums = kwargs.get("nums")
        sorted_compares = kwargs.get("sorted_compares")
        sorted_dims = kwargs.get("sorted_dims")
        sorted_nums = kwargs.get("sorted_nums")
        result = kwargs.get("result")
        result_data = kwargs.get("result_data")
        dim_value_dict = collections.OrderedDict()
        dim_value_unique_dict = collections.OrderedDict()
        dims_count = len(sorted_dims)
        for row in excel_data:
            key = []
            dim_key = self._set_dim_value_dict(row, dims_count, sorted_dims, dim_value_dict, dim_value_unique_dict)
            item, innermost_item = self._recursion_get_compares(
                row, len(sorted_compares) - 1, sorted_compares, key, is_dim=False, ltype=self.compare_type
            )

            key = "_".join(key)
            for num_col_name in sorted_nums:
                num_alias_name = self._get_alias_name_by_field(nums.get(num_col_name))
                num_item_data = collections.OrderedDict()
                num_item_data[dim_key] = row.get(num_col_name)
                col_name = (
                    nums.get(num_col_name).get("alias")
                    if nums.get(num_col_name).get("alias")
                    else nums.get(num_col_name).get("col_name")
                )
                num_item = {
                    "col_name": col_name,
                    "name": num_alias_name,
                    "type": self.compare_type,
                    "child": [],
                    "data": num_item_data,
                }
                tree_key = key + "_" + (num_alias_name or "None")
                if tree_key not in result:
                    innermost_item["child"].append(num_item)
                    result[tree_key] = num_item
                    # 将数据存入返回结果集
                    if len(innermost_item["child"]) == 1:
                        result_data.append(item)
                else:
                    result[tree_key]["data"][dim_key] = row.get(num_col_name)
        return dim_value_dict

    @staticmethod
    def _set_dim_value_dict(row, dims_count, sorted_dims, dim_value_dict, dim_value_unique_dict):
        """
        获取维度字段名称与字段值列表对应的dict数据
        :param dict row: 行数据
        :param int dims_count: 维度个数
        :param dict sorted_dims: 经过排序后的维度dict
        :param dict dim_value_dict: 返回维度字段名称与字段值列表对应的dict数据
        :param dict dim_value_dict: 返回维度字段名称组成的key与字段值列表对应的dict去重数据
        :return:
        """
        dim_key = []
        for i in range(dims_count):
            dim_value = str(row.get(sorted_dims[i])) if row.get(sorted_dims[i]) else "None"
            dim_key.append(dim_value)
        unique_dim_key = "_".join(dim_key)
        if unique_dim_key not in dim_value_unique_dict:
            dim_value_unique_dict[unique_dim_key] = unique_dim_key
            for i in range(dims_count):
                dim_value = str(row.get(sorted_dims[i])) if row.get(sorted_dims[i]) else "None"
                if sorted_dims[i] not in dim_value_dict:
                    dim_value_dict[sorted_dims[i]] = []
                dim_value_dict[sorted_dims[i]].append(dim_value)
        return "_".join(dim_key)

    def _recursion_get_compares(self, row, n, sorted_compares, key, is_dim=False, ltype=1):
        """
        递归返回树形表头列结构数据
        :param row:
        :param n:
        :param sorted_compares:
        :param key:
        :param is_dim:
        :param ltype:
        :return:
        """
        if is_dim:
            value = sorted_compares
        else:
            value = row.get(sorted_compares[n]) if row.get(sorted_compares[n]) else "None"

        if n == 0:
            key.append(str(value))
            item = {"name": value, "type": ltype, "child": []}
            innermost_item = item
            return item, innermost_item
        else:
            key.append(str(value))
            temp = {"name": value, "type": ltype, "child": []}
            item, innermost_item = self._recursion_get_compares(row, n - 1, sorted_compares, key, is_dim, ltype)
            temp["child"].append(item)
            return temp, innermost_item

    @staticmethod
    def get_col_dict(col_list):
        """
        将字段列表转换为key为字段名称value为字段数据的dict，并返回以字段排序值为key字段名为value的dict数据
        :param list col_list: 字段列表数据
        :return:
        """
        data = {}
        sorted_data = {}
        if col_list:
            for col in col_list:
                # 有类似day，year，sum，count等操作符的情况
                if (
                    col.get("dataset_type") == 'API'
                    or col.get('dataset_connect_type') == DatasetConnectType.Directly.value
                ):
                    col["col_name"] = col.get("alias") if col.get("alias") else col.get("col_name")
                if col.get("formula_mode"):
                    data[col.get("formula_mode") + "_" + col.get("col_name")] = col
                    sorted_data[col.get("rank")] = col.get("formula_mode") + "_" + col.get("col_name")
                else:
                    data[col.get("col_name")] = col
                    sorted_data[col.get("rank")] = col.get("col_name")
        return data, sorted_data

    @staticmethod
    def get_unique_list_data(value_list):
        """
        list数据去重并保留原有顺序
        :param list value_list: list数据
        :return:
        """
        data_list = list(set(value_list))
        data_list.sort(key=value_list.index)
        return data_list

    @staticmethod
    def _get_alias_name_by_field(field):
        """
        统一字段中文名称返回规则
        :param field:
        :return:
        """
        if field.get("alias_name"):
            alias_name = field.get("alias_name")
        elif field.get("alias"):
            alias_name = field.get("alias")
        else:
            alias_name = field.get("col_name")
        return alias_name

    def _recursive_handle_none_value(self, data: list):
        """
        递归处理行标签值为字符串None的情况
        :param data:
        :return:
        """
        if not data:
            return data
        for row in data:
            if row.get("type") != self.dim_type:
                continue
            if row.get("data"):
                row["data"] = [None if i == "None" else i for i in row.get("data")]
            if row.get("child"):
                self._recursive_handle_none_value(row.get("child"))
        return data

    def get_marklines_for_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        该组件无辅助线
        :return:
        """
        return []

    def get_subtotal_for_struct(self):
        """
        该组件无小计
        :return:
        """
        return {}

    def get_subtotal_for_data(
        self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
            prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        该组件无小计
        :param data:
        :param next_item:
        :return:
        """
        return {}, data_frame, original_data
