#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by yangzy02 on 2017/9/1 (jekins)
# pylint: skip-file
import copy
import json
import traceback
from abc import ABCMeta, abstractmethod
from typing import Union, Dict, List, Tuple
import pandas as pd

from base.enums import DataSourceType, DatasetConnectType, DatasetType
from dashboard_chart.data_query.widget.advanced_compute import ratio_compute, same_ring_ratio_compute
from dashboard_chart.data_query.widget.count_query import CountQuery
from dashboard_chart.data_query.widget.count_result_query import CountResultQuery
from dashboard_chart.data_query.widget.pivot_query import PivotQuery
from base.dmp_constant import CHART_QUERY_DEFAULT_LIMIT, CHART_LAST_N_SPECIAL_FLAG, EXTRA_NUMS_SUFFIX
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.group.group import Group
from dashboard_chart.convertor.limit.limit import Limit
from dashboard_chart.convertor.order.order import Order
from dashboard_chart.convertor.query_var.query_var import QueryVar
from dashboard_chart.convertor.select.select import Select
from dashboard_chart.convertor.where.where import Where
from dashboard_chart.data_query import utils as data_query_utils
from dashboard_chart.data_query.widget.markline_query import MarklineQuery
from dashboard_chart.models import ChartDataModel, ResultDataModel, ResultVariableDataModel, ResultOrderbyModel
from dashboard_chart.data_query.widget.indirect_query import IndirectQuery
from dashboard_chart.utils.common import add_api_dataset_params
from dataset.query.query_dataset_service import QueryDatasetService
from dmplib.hug import g
from dmplib.utils.errors import UserError
from hashlib import md5
from components.analysis_time import AnalysisTimeUtils


class ChartQuery(metaclass=ABCMeta):
    """
    单图查询基类
    """

    def __init__(self, model):
        """
        定义参数
        :param dashboard_chart.models.ChartDataModel model:
        """
        super().__init__()
        self._markline_instance = None
        self.chart_data_model = model
        self.select_fields = []
        self.where_fields = []
        self.group_fields = []
        self.order_fields = []
        self.limit_field = {}
        self.query_vars = []
        self.data_total = -1  # count查询的数据总量,初始化为-1便于优化多次count的逻辑
        self.over_limit_flag = False
        self.assign_indirect_values(model)

    @staticmethod
    def get_user_id():
        if hasattr(g, "userid"):
            return g.userid
        return None

    def assign_indirect_values(self, model: ChartDataModel = None):
        """
        为间接访问赋值
        :param model:
        :return:
        """
        real_model = model or self.chart_data_model
        indirect_query = IndirectQuery(real_model, self.get_user_id(), self.get_direct_dataset_vars(real_model))
        indirect_query.format_model()

    def get_select_fields(self, model: ChartDataModel = None):
        """
        获取select数据列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        self.select_fields = Select.get_select_fields(model or self.chart_data_model)
        return self.select_fields

    def get_where_fields(self, model: ChartDataModel = None):
        """
        获取where数据列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        where_instance = Where()
        self.where_fields = where_instance.get_where_fields(model or self.chart_data_model)
        return self.where_fields

    def get_limit_field(self, model: ChartDataModel = None):
        """
        获取limit数据
        在有对比维度存在的情况下 limit 获取受限的最大值
        个别定制老的查询 在相关类中重置该方法
        :param model:
        :return:
        """
        real_model = model or self.chart_data_model
        self.limit_field = Limit.get_limit_field(real_model)
        # 前N后N
        # 如果是后N的情况，需要重新设置limit中的offset值
        # 标记offset为-9999为后N类型，需要重新设置offset值
        if self.limit_field and self.limit_field.offset == CHART_LAST_N_SPECIAL_FLAG:
            # TODO count总量获取后续优化
            self.data_total = self.query_count()
            self.limit_field.offset = 0
            if self.data_total:
                self.limit_field.offset = (
                    self.data_total - self.limit_field.limit if self.data_total > self.limit_field.limit else 0
                )
        return self.limit_field

    def get_dataset_vars(self, model: ChartDataModel = None):
        self.query_vars = QueryVar.get_dataset_vars(model or self.chart_data_model)
        return self.query_vars

    def get_direct_dataset_vars(self, model: ChartDataModel = None):
        """
        获取直接变量
        即没有value_from的变量
        :param model:
        :return:
        """
        return QueryVar.get_direct_dataset_vars(model or self.chart_data_model)

    def get_order_fields_for_multi(self, model: ChartDataModel = None):
        self.order_fields = Order().get_order_fields_for_multi(model or self.chart_data_model)
        return self.order_fields

    def get_group_fields(self, model: ChartDataModel = None):
        real_model = model or self.chart_data_model
        # 只有有聚合的情况下才有group
        if real_model.aggregation:
            self.group_fields = Group.get_group_fields(real_model)
        else:
            self.group_fields = []
        return self.group_fields

    def get_having_fields(self):
        return []

    def validation_dataset_fields(self, fields):
        """
        字段数据校验基类方法
        :param list fields: 需要检验的字段列表
        :return:
        """
        dataset_field_dict = self.chart_data_model.dataset_field_dict
        dataset_field_id_list = dataset_field_dict.keys()
        for field in fields:
            if "mode" in list(field.keys()) and field.get("mode"):
                continue
            if field.get("dataset_field_id") and (field.get("dataset_field_id") not in dataset_field_id_list):
                raise UserError(message=f"组件引用的【{field.get('alias_name', '')}】字段丢失，请检查数据集或组件配置。")

    def get_need_validation_fields(self, fields_list=None):
        """
        获取需要进行字段校验的字段数据
        :param list fields_list: 需要检验的字段列表
        :return:
        """
        validation_fields = []
        if not fields_list:
            fields_list = [
                self.chart_data_model.dims,
                self.chart_data_model.nums,
                self.chart_data_model.zaxis,
                self.chart_data_model.desires,
                self.chart_data_model.comparisons,
            ]
        for fields in fields_list:
            if fields:
                validation_fields.extend(fields)

        return validation_fields

    @abstractmethod
    def get_chart_data(self, dataset_version: str = None):
        return []

    @abstractmethod
    def get_query_struct(self):
        return []

    @abstractmethod
    def get_variable_chart_data(self):
        return {}

    @abstractmethod
    def get_item_list(self):
        return []

    def restructure_chart_data(self, data: pd.DataFrame, original_data: List[Dict]) -> Union[List, Dict]:
        """
        重新组织data数据满足各个单图类型数据结构(各字类进行覆盖)
        :param list|dict data: 查询data库后返回的数据
        :return:
        """
        return original_data

    def compute_result(self, db_result_data: List[Dict]) -> List[Dict]:
        """
        计算结果数据(使用原型模式)
        1. 高级计算字段
        :param db_result_data:
        :return:
        """
        user_id = self.get_user_id()
        select_fields = self.get_select_fields()
        where_fields = self.get_where_fields()
        group_fields = self.get_group_fields()
        dataset_vars = self.get_dataset_vars()

        db_result_data = ratio_compute.RatioCompute(
            db_result_data,
            self.chart_data_model,
            user_id,
            copy.deepcopy(select_fields),
            copy.deepcopy(where_fields),
            copy.deepcopy(dataset_vars),
        ).compute()

        db_result_data = same_ring_ratio_compute.SameRingRatioCompute(
            db_result_data,
            self.chart_data_model,
            user_id,
            copy.deepcopy(select_fields),
            copy.deepcopy(where_fields),
            copy.deepcopy(group_fields),
            copy.deepcopy(dataset_vars),
        ).compute()

        return db_result_data

    def apply_funcs(self, db_data: list) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        此处为兼容新老程序 返回两种数据格式 具体类中决定使用那种数据格式
        追加功能应用 (如维度对比 基类中实现的为标准的)
        此处一律返回pandas dataFrame
        :return pd.DataFrame:
        """
        # 1. 维度对比功能
        if self.chart_data_model.comparisons:
            pivot_instance = PivotQuery(self.chart_data_model)
            # 此处由于是透视功能 转换为原始数据 数据不能匹配 因此返回未经透视的原始数据
            return pivot_instance.pivot(db_data, self.get_order_fields_for_multi(self.chart_data_model)), db_data
        else:
            # 需要对表头顺序进行排列  维度在前 度量在后
            df = pd.DataFrame(db_data)
            sorted_index_dict = {(val.alias or val.field): idx for idx, val in enumerate(self.get_select_fields())}

            def sort_columns(column):
                val = sorted_index_dict.get(column)
                return val if val is not None else 0

            return df.reindex(sorted(df.columns, key=sort_columns), axis=1), db_data

    def get_real_limit_data(
            self, data_frame: pd.DataFrame, original_data: List[Dict], advance_next: int = 0
    ) -> Tuple[pd.DataFrame, pd.DataFrame, List[Dict], List[Dict], pd.DataFrame, List[Dict]]:
        """
        此处为兼容新老程序 返回两种数据格式 具体类中决定使用那种数据格式
        获取返回结果
        此处主要是获取返回给调用端的真实数据
        此处和limit的区分在于limit仅仅影响查询结果，而有些情况返回结果不是查询后的返回结果而是经过一系列组装后的结果
        如透视表，查询需要查询所有数据，而返回只需要返回指定数据
        :param data_frame:
        :param original_data:
        :param advance_next:
        :return [data_frame, advance_next_frame, original_data, advance_next_data, advance_prev_frame, advance_prev_data]:
        """
        model = self.chart_data_model
        if model.comparisons:
            # 此处由于经过透视 原始数据已经发生变化
            if model.pagination.page:
                start_num = (int(model.pagination.page) - 1) * model.pagination.page_size
                end_num = int(model.pagination.page) * model.pagination.page_size
                advance_prev_frame = data_frame.iloc[start_num - advance_next: start_num]
                advance_prev_data = []
                return (
                    data_frame.iloc[start_num: start_num + model.pagination.page_size],
                    data_frame.iloc[end_num: end_num + advance_next],
                    original_data,
                    [],
                    advance_prev_frame,
                    advance_prev_data
                )
            else:
                # 不分页，取默认的分页值，意味着只有一页数据，不向前取数
                advance_prev_frame = pd.DataFrame([])
                advance_prev_data = []
                return (
                    data_frame.iloc[0:CHART_QUERY_DEFAULT_LIMIT],
                    data_frame.iloc[CHART_QUERY_DEFAULT_LIMIT: CHART_QUERY_DEFAULT_LIMIT + advance_next],
                    original_data,
                    [],
                    advance_prev_frame,
                    advance_prev_data
                )
        limit_field = self.get_limit_field()
        if not limit_field:
            return (data_frame, pd.DataFrame([]), original_data, [], pd.DataFrame([]), [])
        if limit_field.offset <= 0: # 第一页数据
            # 第一页不会向前多取一条
            advance_prev_frame = pd.DataFrame([])
            advance_prev_data = []
            return (
                data_frame.iloc[0: limit_field.limit],
                data_frame.iloc[limit_field.limit: limit_field.limit + advance_next],
                original_data[0: limit_field.limit],
                original_data[limit_field.limit: limit_field.limit + advance_next],
                advance_prev_frame,
                advance_prev_data
            )
        else:
            prev_start = 0
            prev_end = advance_next
            origin_start = advance_next
            origin_end = advance_next + limit_field.limit
            next_start = advance_next + limit_field.limit
            next_end = advance_next + limit_field.limit + advance_next

            advance_prev_frame = data_frame.iloc[prev_start: prev_end]
            advance_prev_data = original_data[prev_start: prev_end]
            return (
                data_frame.iloc[origin_start: origin_end],
                data_frame.iloc[next_start: next_end],
                original_data[origin_start: origin_end],
                original_data[next_start: next_end],
                advance_prev_frame,
                advance_prev_data
            )

    # def remove_extra_nums_from_model_nums(self):
    #     # 从度量中移除多余取数的度量，以免影响后续计算
    #     if not self.chart_data_model.extra_nums:
    #         return
    #
    #     origin_nums_num = len(self.chart_data_model.nums) - len(self.chart_data_model.extra_nums)
    #     self.chart_data_model.nums = self.chart_data_model.nums[:origin_nums_num]

    def pullout_extra_data(self, structured_data, subtotal):
        # 抽离出前端传的额外的取数字段，单独返回这个数据
        if not self.chart_data_model.extra_nums:
            return structured_data, []

        extra_data = []
        striped_structured_data = []
        extra_nums_alias = {e.get('alias') for e in self.chart_data_model.extra_nums if e.get('alias')}
        # 组件取数的行列模式返回data节点的数据不一样
        # 这里需要单独处理
        # !!!!! 行模式与列模式返回的extra_data数据将会是不一样的格式
        # 1. 列模式
        if self.chart_data_model.data_logic_type_code == 'column':
            has_deal = True
            for data in structured_data:
                curr_headers = data.get('header') or []
                curr_header_alias = {h.get('alias') for h in curr_headers if h.get('alias')}
                if curr_header_alias & extra_nums_alias:
                    # 有交集说明，这列数据是来自于额外的取数字段
                    self.erase_and_format_extra_num_alias(data)
                    extra_data.append(data)
                else:
                    striped_structured_data.append(data)
                # 处理额外字段已经在nums里面的场景
                self.append_already_in_nums_data_for_column(data, curr_headers, extra_data)

        # 2. 行（默认）模式
        elif self.chart_data_model.data_logic_type_code == 'default':
            has_deal = True
            # 这里调用的列模式的函数获取列的字段信息
            from dashboard_chart.data_query.charts.column_chart import ColumnChart
            col_chart = ColumnChart(model=copy.deepcopy(self.chart_data_model))
            dataset_alias_dict = col_chart.get_dataset_alias_dict()

            # structured_data: [{'sum_JE_7641406451': 767.0, 'sum_JE_7641471987': 1814.0, 'count_JE_7641471987': 10}]
            for data in structured_data:
                if not isinstance(data, dict):
                    continue
                line_data = []
                for col_name, value in data.copy().items():
                    col_header = dataset_alias_dict.get(col_name) or {}
                    if not col_header:
                        continue
                    if {col_header.alias} & extra_nums_alias:
                        # 有交集说明，这列数据是来自于额外的取数字段
                        # 行（默认）模式下的数据格式与列模式不一致，需要单独处理
                        default_data = self.format_default_data(col_header.get_dict(), value)
                        line_data.append(default_data)
                        # 移除data里面对应的额外数据
                        data.pop(col_name, None)
                    # 处理额外字段已经在nums里面的场景
                    self.append_already_in_nums_data_for_default(col_header, line_data, value)
                striped_structured_data.append(data)
                if line_data:
                    extra_data.append(line_data)
        else:
            return structured_data, []

        if not has_deal:
            return structured_data, []

        del structured_data
        # 处理小计数据， 删除额外字段信息
        self.drop_extra_data_from_subtotal(subtotal, extra_nums_alias)
        return striped_structured_data, extra_data

    def format_default_data(self, col_dict, value):
        new_header = copy.deepcopy(col_dict)
        new_header['col_value'] = value
        self.format_single_extra_header(header=new_header)
        return new_header

    def format_single_extra_header(self, header):
        nums_map = self.get_nums_map()
        field_id = header.get('field_id') or ''
        num = nums_map.get(field_id) or {}
        extra_suffix = num.get('extra_suffix') or ''
        header['formula_mode'] = self.get_extra_num_formula_mode(field_id)
        header['field_id'] = self.get_pure_dataset_field_id(field_id, extra_suffix)
        header['alias'] = self.get_pure_dataset_field_id(header['alias'], extra_suffix)

    def append_already_in_nums_data_for_default(self, header, line_data, value):
        # 处理行模式
        # 如果在extra_nums中传递了一个在nums中原本存在的字段以及同样的聚合方式，
        # 并不会真正的再次在取数SQL中添加一个字段，
        # 会把nums中的这个字段的SQL值，直接也放在extra_nums中
        if not header:
            return
        already_alias_name = header.alias_name
        already_in_nums_map = self.get_already_in_nums_map()
        if already_alias_name in already_in_nums_map.keys():
            extra_num_id = already_in_nums_map[already_alias_name].field_ref
            extra_num_dataset_dict = self.chart_data_model.dataset_field_dict.get(extra_num_id) or {}
            extra_suffix = extra_num_dataset_dict.get('extra_suffix', '')
            origin_num_id = self.get_pure_dataset_field_id(extra_num_id, extra_suffix)
            if header.field_id == origin_num_id:
                header.formula_mode = self.get_extra_num_formula_mode(origin_num_id)
                new_header = self.format_default_data(header.get_dict(), value=value)
                line_data.append(new_header)

    def append_already_in_nums_data_for_column(self, data, curr_headers, extra_data):
        # 处理列模式
        # 如果在extra_nums中传递了一个在nums中原本存在的字段以及同样的聚合方式，
        # 并不会真正的再次在取数SQL中添加一个字段，
        # 会把nums中的这个字段的SQL值，直接也放在extra_nums中
        if not curr_headers:
            return
        activate_header = curr_headers[-1]
        already_alias_name = activate_header.get('alias_name')
        already_in_nums_map = self.get_already_in_nums_map()
        if already_alias_name in already_in_nums_map.keys():
            extra_num_id = already_in_nums_map[already_alias_name].field_ref
            extra_num_dataset_dict = self.chart_data_model.dataset_field_dict.get(extra_num_id) or {}
            extra_suffix = extra_num_dataset_dict.get('extra_suffix', '')
            origin_num_id = self.get_pure_dataset_field_id(extra_num_id, extra_suffix)
            if activate_header.get('field_id') == origin_num_id:
                activate_header['formula_mode'] = self.get_extra_num_formula_mode(origin_num_id)
                extra_data.append(data)

    def get_already_in_nums_map(self):
        if not hasattr(self, '__already_in_nums_map__'):
            mock_extra_model = copy.deepcopy(self.chart_data_model)
            mock_extra_model.nums = mock_extra_model.extra_nums
            mock_extra_model.dims = []
            mock_extra_model.comparisons = []
            mock_selects = self.get_select_fields(mock_extra_model)
            nums_map = {}
            for select in mock_selects:
                field_id = select.field_ref
                if self.chart_data_model.dataset_field_dict.get(field_id, {}).get('extra_num_already_in_nums'):
                    nums_map[select.alias] = select
            setattr(self, '__already_in_nums_map__', nums_map)
            return nums_map
        else:
            return getattr(self, '__already_in_nums_map__', {})

    def drop_extra_data_from_subtotal(self, subtotal, extra_nums_alias):
        # 删除总小计里关于额外字段的信息
        if not self.chart_data_model.extra_nums or not subtotal:
            return

        col_summary = subtotal.get('subtotal_col', {}).get('summary', {}).get('cols') or []
        cols_rows_data = subtotal.get('subtotal_col', {}).get('rows') or []
        rows_data = subtotal.get('subtotal_row') or []
        if col_summary:
            cols1 = self.drop_extra_data_cols(col_summary, extra_nums_alias)
            subtotal['subtotal_col']['summary']['cols'] = cols1
        if cols_rows_data:
            for col_row in cols_rows_data:
                cols2 = col_row.get('cols') or []
                if cols2:
                    col_row['cols'] = self.drop_extra_data_cols(cols2, extra_nums_alias)
        if rows_data:
            cols3 = self.drop_extra_data_cols(rows_data, extra_nums_alias)
            subtotal['subtotal_row'] = cols3
        return

    @staticmethod
    def drop_extra_data_cols(datas, extra_nums_alias):
        result = []
        for data in datas:
            curr_headers = data.get('header') or []
            curr_header_alias = {h.get('alias') for h in curr_headers if h.get('alias')}
            if not (curr_header_alias & extra_nums_alias):
                result.append(data)
        return result

    def get_nums_map(self):
        if not hasattr(self, '__nums_map__'):
            nums_map = {num.get('num'): num for num in self.chart_data_model.nums}
            setattr(self, '__nums_map__', nums_map)
            return nums_map
        else:
            return getattr(self, '__nums_map__', {})

    @staticmethod
    def get_pure_dataset_field_id(mock_id, extra_suffix):
        return mock_id.replace(extra_suffix, '').strip()

    def get_extra_num_formula_mode(self, field_id):
        nums_map = self.get_nums_map()
        num = nums_map.get(field_id) or {}
        return num.get('formula_mode') or ''

    def erase_and_format_extra_num_alias(self, data):
        # 抹除extra_nums的alias标记, 并格式化
        headers = data.get('header') or []
        if not headers:
            return
        for header in headers:
            self.format_single_extra_header(header=header)

    def get_data(
            self,
            result: dict,
            advance_next: int = 0,
            model: ChartDataModel = None,
            where_fields: list = None,
            order_fields: list = None,
            over_limit_flag: bool = None,
    ) -> dict:
        """
        获取数据
        此处有三步
        本处为兼容新老程序，所有功能均返回新老两种数据格式 在具体字类中决定是用那种数据
        1. 计算结果数据(如高级计算字段等)
        2. 应用功能(如对比维度)
        3. 获取真实result和预支result
        4. 求小计
        5. 重组返回结构
        :param result:
        :param advance_next:
        :return:
        """
        AnalysisTimeUtils.recode_time_node('开始高级计算')
        real_model = model or self.chart_data_model

        # 1. 结果计算
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.super_total_calc_query.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            data = self.compute_result(result.get("data"))

        # 2. apply_funcs 追加应用
        # 3. 获取真实result和预支result
        data_frame, original_data = self.apply_funcs(data)
        (
            data_frame, advance_next_frame, original_data, advance_next_original,
            advance_prev_frame, advance_prev_original
        ) = self.get_real_limit_data(data_frame, original_data, advance_next)

        AnalysisTimeUtils.recode_time_node('结束高级计算')

        # 4. 求取小计数据(小计将会将原始数据中的行小计字段去除)
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.summary_subtotal_query.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            subtotal, data_frame, original_data = self.get_subtotal_for_data(
                data_frame, advance_next_frame, original_data, advance_next_original,
                advance_prev_frame, advance_prev_original
            )

        AnalysisTimeUtils.recode_time_node('开始组装最后的返回数据')
        # 5. 各单图相关类组装最终结构
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.return_data_deal.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            structured_data = self.restructure_chart_data(data_frame, original_data)
        AnalysisTimeUtils.recode_time_node('结束组装最后的返回数据')

        # 6. 抽离，分开返回额外的字段信息，删除额外的小计信息
        structured_data, extra_data = self.pullout_extra_data(structured_data, subtotal)

        return ResultDataModel(
            **{
                "data": structured_data,
                "extra_data": extra_data,
                "data_last_update_time": data_query_utils.get_data_last_update_time(
                    result.get("data_version") or result.get("meta_version")
                ),
                "marklines": self.get_marklines_for_data(data_frame, original_data),
                "conditions": self.get_conditions_for_data(where_fields),
                "msg": data_query_utils.get_data_msg(result),
                "msg_code": data_query_utils.get_msg_code(
                    result, self.over_limit_flag if over_limit_flag is None else over_limit_flag
                ),
                "error_code": result.get('error_code'),
                "dataset_query_type": result.get('dataset_query_type'),
                "execute_status": result.get("code"),
                "pagination": self.get_pagination_for_data(real_model),
                "data_limit": real_model.data_limit or CHART_QUERY_DEFAULT_LIMIT,
                "sql": result.get("sql"),
                "sql_execute_time": result.get("sql_execute_time"),
                "dataset_versions": {"version": result.get("meta_version"), "data_version": result.get("data_version")},
                "query_structure": result.get("query_structure", {}),
                "subtotal": subtotal,
                "orderby": self.get_orderby_for_data(order_fields),
            }
        ).get_dict()

    def modify_offset_and_limit(self, origin_limit_field, advance_next):
        # 此处不能更改原始的limit_field
        limit_field = copy.deepcopy(origin_limit_field)
        # 向前多取一条
        if limit_field.offset <= 0:
            # 向后多取出一条
            limit_field.limit += advance_next
        else:
            limit_field.offset -= advance_next
            limit_field.limit += 2 * advance_next
        return limit_field

    def get_query_underlying_data(self, advance_next: int = 0):
        limit_field = self.get_limit_field()
        # 有limit且有预支
        if self.get_limit_field() and advance_next:
            limit_field = self.modify_offset_and_limit(limit_field, advance_next)
        data = {
            "select": self.get_select_fields(),
            "where": self.get_where_fields(),
            "group": self.get_group_fields(),
            "order": self.get_order_fields_for_multi(),
            "limit": limit_field,
            "vars": self.get_dataset_vars(),
            "dataset_field_dict": self.chart_data_model.dataset_field_dict,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        query_agent = QueryAgent()
        return data, query_agent.convert(data)

    def query_data(self, advance_next: int = 0, dataset_version: str = None) -> Dict:
        """
        查询数据
        :advance_next: 预支下N条
        :return:
        """
        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.main_query_generate.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            _, json_struct = self.get_query_underlying_data(advance_next)
            if self.chart_data_model.filter_val_empty_mode == 1:
                json_struct = self._filter_empty_data(json_struct)
            query_params = {
                "user_id": self.get_user_id(),
                "dataset_id": self.chart_data_model.dataset_id,
                "chart_id": self.chart_data_model.id,
                "json_struct": json_struct,
                "is_order_master_id": self.get_master_id_order(),
                "dataset_version": dataset_version,
                "external_subject_ids": self.chart_data_model.external_subject_ids,
                "chart_data_model": self.chart_data_model,
            }
            # 修复调度时并发取数问题，调度类型并且非数芯数据源才会传递表名
            if self.chart_data_model.dataset.get('connect_type') != DatasetConnectType.Directly.value and self.chart_data_model.dataset.get('type') != DatasetType.Indicator.value:
                query_params['table_name'] = self.chart_data_model.dataset.get('table_name')

        with AnalysisTimeUtils.record_code(step=AnalysisTimeUtils.step_type.total_mian_query_process.value, sql=None, db_type=None, extra={}, need_type_inference=False):
            result = QueryAgent.query(**query_params)
        return result

    @staticmethod
    def _filter_empty_data(json_struct):
        json_data = json.loads(json_struct)
        json_data['where'] = [{
            "logical_relation": "",
            "left": {
                "obj_name": "", "prop_name": "", "alias": "", "specifier": "",
                "operator": "", "value": 1, "props": [], "func": "", "prop_raw": "",
                "prop_ref": ""
            },
            "right": {
                "obj_name": "", "prop_name": "", "alias": "", "specifier": "",
                "operator": "", "value": 1, "props": [], "func": "", "prop_raw": "",
                "prop_ref": ""
            },
            "conditions": [],
            "operator": "!="
        }]
        json_struct = json.dumps(json_data)
        return json_struct

    def get_struct(self, struct):
        """
        设置查询结构返回 结果
        :param struct:
        :return:
        """
        return {
            "dataset_name": struct.get("dataset_name"),
            "msg_code": struct.get("code"),
            "msg": struct.get("msg"),
            "meta_version": struct.get("meta_version"),
            "query_structure": struct.get("query_structure"),
            "subtotal": self.get_subtotal_for_struct(),
            "select_fields": self.get_select_fields(),
        }

    def query_struct(self):
        """
        查询结构
        :return:
        """
        data = {
            "select": self.get_select_fields(),
            "where": self.get_where_fields(),
            "group": self.get_group_fields(),
            "order": self.get_order_fields_for_multi(),
            "limit": self.get_limit_field(),
            "vars": self.get_dataset_vars(),
            "dataset_field_dict": self.chart_data_model.dataset_field_dict,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        query_agent = QueryAgent()
        json_struct = query_agent.convert(data)
        query_params = {
            "user_id": self.get_user_id(),
            "dataset_id": self.chart_data_model.dataset_id,
            "chart_id": self.chart_data_model.id,
            "json_struct": json_struct,
            "is_order_master_id": self.get_master_id_order(),
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        struct = QueryAgent.query_struct(**query_params)
        return struct

    def query_count(self, where_fields: list = None, group_fields: list = None) -> int:
        """
        查询数据总量
        :param where_fields:
        :param group_fields:
        :return:
        """
        from base.enums import DatasetType

        query_count_model = copy.deepcopy(self.chart_data_model)
        add_api_dataset_params(g, dataset_id=self.chart_data_model.dataset_id,
                               report_id=self.chart_data_model.dashboard_id, dataset=self.chart_data_model.dataset)

        # if query_count_model.dataset.get("type") == DatasetType.Indicator.value:
        #     from dataset.query.query_dataset_service import QueryDatasetService
        #     from dataset.query.indicator_model_query_data import IndicatorModelQuery
        #
        #     data_source_model = QueryDatasetService.get_data_source_model(query_count_model.dataset.get("content"))
        #     query_count_model.pagination = None
        #     query_data = IndicatorModelQuery(
        #         data_source_model,
        #         query_count_model.dataset,
        #         query_count_model,
        #         field_map=query_count_model.dataset_field_dict
        #     )
        #     total = query_data.get_query_data(mode="Count")
        #     return total
        # else:
        if query_count_model.comparisons:
            # 有对比维度的需要返回计算后的数据长度
            count_query = CountResultQuery(chart_data_model=query_count_model, user_id=self.get_user_id())
            result = self.query_data()
            data_frame, _ = (
                (pd.DataFrame([]), result.get('data'))
                if result.get('code') != 200
                else self.apply_funcs(result.get('data'))
            )
            return count_query.query_count(data_frame)
        else:
            # 无对比维度的直接查询数据库
            where_fields = where_fields or self.get_where_fields()
            group_fields = group_fields or self.get_group_fields()
            # count 属于聚合计算 考虑到非聚合类型单图也可能用到count 因此此处深度复制并更改聚合参数
            count_query = CountQuery(chart_data_model=query_count_model, user_id=self.get_user_id())
            return count_query.query_count(where_fields=where_fields, group_fields=group_fields)

    def get_pagination_for_data(self, model: ChartDataModel = None):
        """
        折线图需要返回给前端的total值
        :return:
        """
        real_model = model or self.chart_data_model
        pagination = real_model.pagination
        page = {
            "page": pagination.page if pagination and pagination.page else 1,
            "page_size": pagination.page_size if pagination and pagination.page_size else CHART_QUERY_DEFAULT_LIMIT,
        }
        return page

    @staticmethod
    def _append_conditions(where_fields):
        """
        生成conditions
        :param where_fields:
        :return:
        """
        conditions = []
        for where_item in where_fields:
            if isinstance(where_item.dn, list):
                conditions.extend(where_item.dn)
            else:
                conditions.append(where_item.dn)
            if where_item.complex:
                for complex_item in where_item.complex:
                    conditions.append(complex_item.dn)
        return conditions

    @staticmethod
    def _get_distinct_conditions(conditions):
        """
        去重conditions
        :param conditions:
        :return:
        """
        md5_dict = {}
        if not conditions:
            return conditions
        for i in conditions:
            md5_dict.update(
                {
                    md5(
                        i.get("dataset_field_id", "").encode(encoding="utf-8")
                        + i.get("operator", "").encode(encoding="utf-8")
                        + i.get("col_value", "").encode(encoding="utf-8")
                        + str(i.get("condition_type", 0)).encode(encoding="utf-8")
                    ).hexdigest(): i
                }
            )
        return list(md5_dict.values())

    def get_conditions_for_data(self, where_fields=None):
        """
        从where条件中收集所有条件数据返回给前端
        :param where_fields:
        :return:
        """
        conditions = []
        if not where_fields:
            where_fields = self.where_fields
        if where_fields:
            conditions = self._append_conditions(where_fields)
        try:
            conditions = [c for c in conditions if c]
            conditions = self._get_distinct_conditions(conditions)
        except:
            return conditions
        return conditions

    def get_orderby_for_data(self, order_fields=None):
        """
        从ordery中收集所有条件数据返回给前端
        :param order_fields:
        :return:
        """
        orderby = []
        if not order_fields:
            order_fields = self.order_fields
        for idx, order_field in enumerate(order_fields):
            field_sort = getattr(order_field, '__field_sort__', {})
            result_order_dict = {
                "dataset_field_id": order_field.field_ref,
                "weight": 100 - idx,
                "field_source": order_field.logic_source,
                "sort": order_field.field_sort,
                "col_name": order_field.field,
                "content": None,
            }
            if field_sort:
                result_order_dict['sort'] = field_sort.get('sort', order_field.field_sort)
                content = field_sort.get('content') or {}
                if result_order_dict['sort'] == 'FIELD':
                    result_order_dict['field_source'] = content.get('field_type', order_field.field_sort)
                result_order_dict['content'] = content
            orderby.append(
                ResultOrderbyModel(**result_order_dict).get_dict()
            )
        return orderby

    def get_marklines_for_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        获取辅助线数据
        :return:
        """
        markline = self._get_markline_query_instance()
        return markline.get_marklines_for_data(original_data)

    def _get_markline_query_instance(self):
        if self._markline_instance is None:
            self._markline_instance = MarklineQuery(chart_data_model=self.chart_data_model)
        return self._markline_instance

    def get_subtotal_for_struct(self):
        """
        获取小计查询结构
        默认基类中没有，若单图中支持小计，请在相关单图中实现
        :return:
        """
        return {}

    def get_subtotal_for_data(
            self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
            prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        获取小计
        common类中为老的小计
        基类为标准小计，后续统一为标准小计
        :param data_frame:
        :param next_item_frame:
        :param original_data:
        :param original_next_item:
        :return: (subtotal, data_frame, original_data)
        """
        return {}, data_frame, original_data

    def get_variable_data(
            self, result: dict, advance_next: int = 0, model: ChartDataModel = None, where_fields: list = None
    ):
        """
        设置data数据
        :param dict result:
        :return:
        """
        real_model = model or self.chart_data_model
        # 1. 结果计算
        # 2. apply_funcs 追加应用
        # 3. 获取真实result和预支result
        data = self.compute_result(result.get("data"))
        data_frame, original_data = self.apply_funcs(data)
        (
            data_frame, advance_next_frame, original_data,
            advance_next_original, advance_prev_frame, advance_prev_original
        ) = self.get_real_limit_data(
            data_frame, original_data, advance_next
        )

        # 3. 重新返回结构
        structured_variable_data = self.restructure_variable_chart_data(data_frame, original_data)

        return ResultVariableDataModel(
            **{
                "data": structured_variable_data,
                "marklines": self.get_marklines_for_data(data_frame, original_data),
                "conditions": self.get_conditions_for_data(where_fields),
                "msg": data_query_utils.get_data_msg(result),
                "msg_code": data_query_utils.get_msg_code(result),
                "execute_status": result.get("code"),
                "pagination": self.get_pagination_for_data(real_model),
                "sql": result.get("sql"),
                "sql_execute_time": result.get("sql_execute_time"),
                "subtotal": self.get_subtotal_for_data(
                    data_frame, advance_next_frame, original_data, advance_next,
                    advance_prev_frame, advance_prev_original
                ),
            }
        ).get_dict()

    def restructure_variable_chart_data(self, data: pd.DataFrame, original_data: List[Dict]) -> List[Dict]:
        """
        重新组织data数据满足各个单图类型数据结构
        :param list|dict data: 查询data库后返回的数据
        :return:
        """
        return original_data

    def get_master_id_order(self):
        """
        排序中是否加入master_id
        :return:
        """
        return False
