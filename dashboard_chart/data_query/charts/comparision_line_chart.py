#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/9 17:41
# <AUTHOR> caoxl
# @File     : comparision_line.py
# pylint:disable=W0613
from typing import Tuple, List, Dict

from base.dmp_constant import CHART_LAST_N_SPECIAL_FLAG
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.convertor.limit.limit import Limit
import itertools
from collections import defaultdict
from functools import reduce
import pandas as pd

from dashboard_chart.models import ChartDataModel


class ComparisionLineChart(CommonChart):
    def restructure_chart_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        重新组织data数据满足各个单图类型数据结构
        :param pd.DataFrame data: 转换后的数据
        :param original_data: 查询data后的原始数据
        :return:
        """
        return self._get_structure_data(original_data)

    def _get_structure_data(self, data):
        """
        返回的数据需要填充为空的记录
        注：当前只支持一个维度加一个对比字段的场景，多维度的复杂场景需要另外考虑方案
        :param data:
        :return:
        """
        # 获取当前使用的表头字段
        cur_header_col_name_list = [i.alias for i in self.select_fields]

        # 获取除了数值外的其他字段，支持维度和对比字段
        operate_col_name_list = []
        for i in self.chart_data_model.dims:
            self._get_real_operate_col_name(i, cur_header_col_name_list, operate_col_name_list)
        for i in (self.chart_data_model.comparisons or []):
            self._get_real_operate_col_name(i, cur_header_col_name_list, operate_col_name_list)

        # 没有维度，对比字段则直接退出
        if not operate_col_name_list:
            return data

        # 获取当前结果集中的数值字段名
        real_num_col_name_list = self._get_real_num_col_name(cur_header_col_name_list)

        # 获取结果集中除了数值外的字段值，同时需要去重
        existed_col_value_list = []
        for operate_col_name in operate_col_name_list:
            value_list = []
            for one in data:
                value = one.get(operate_col_name)
                if value not in value_list:
                    value_list.append(value)
            # existed_col_value_list.append(list({i.get(operate_col_name) for i in data}))
            existed_col_value_list.append(value_list)

        # 存在的排列组合超过10000则不处理
        all_col_value_group_cnt = reduce(lambda x, y: x * y, [len(i) for i in existed_col_value_list])
        if all_col_value_group_cnt > 10000:
            self.over_limit_flag = True
            return data

        # 获取字段值的所有排列组合
        all_col_value_group = list(itertools.product(*existed_col_value_list))

        # 已存在的排列组合
        existed_col_value_group = self._get_existed_col_value_group(data, operate_col_name_list)

        # 将数值为空的记录填充进返回的data数据中
        origin_order_data = []
        for single_item in all_col_value_group:
            if single_item not in existed_col_value_group:
                # 按顺序匹配字段，拼装单条数据
                tmp_single_data = {}
                for idx, item_value in enumerate(single_item):
                    match_key = operate_col_name_list[idx]
                    tmp_single_data[match_key] = item_value
                # 没有在结果集中的记录，数值统一为None
                for z in real_num_col_name_list:
                    tmp_single_data.update({z: None})
                # data.append(tmp_single_data)
                origin_order_data.append(tmp_single_data)
            else:
                origin_order_data.append(existed_col_value_group[single_item])

        # 按第一个维度的值进行分类，重新组装返回数据
        return self._get_regrouped_data(origin_order_data, operate_col_name_list)

    def _get_real_operate_col_name(self, item, cur_header_col_name_list, operate_col_name_list):
        """
        兼容api数据集这类的col_name匹配问题
        :param item:
        :param cur_header_col_name_list:
        :param operate_col_name_list:
        :return:
        """
        operate_col_name = None
        col_name = self._assign_formula_mode(item.get("col_name"), item.get("formula_mode"))
        alias = self._assign_formula_mode(item.get("alias"), item.get("formula_mode"))
        alias_name = self._assign_formula_mode(item.get("alias_name"), item.get("formula_mode"))
        if col_name and col_name in cur_header_col_name_list:
            operate_col_name = col_name
        elif not operate_col_name and alias and alias in cur_header_col_name_list:
            operate_col_name = alias
        elif not operate_col_name and alias_name and alias_name in cur_header_col_name_list:
            operate_col_name = alias_name
        if operate_col_name:
            operate_col_name_list.append(operate_col_name)

    def _get_real_num_col_name(self, cur_header_col_name_list):
        """
        匹配加上计算方式的数值字段的col_name
        :param cur_header_col_name_list:
        :return:
        """
        real_num_col_name_list = []
        num_col_name_list = []
        for i in self.chart_data_model.nums:
            num_col_name_list.append(self._assign_formula_mode(i.get("col_name"), i.get("formula_mode")))
            num_col_name_list.append(self._assign_formula_mode(i.get("alias"), i.get("formula_mode")))
            num_col_name_list.append(self._assign_formula_mode(i.get("alias_name"), i.get("formula_mode")))
        for x in cur_header_col_name_list:
            for y in num_col_name_list:
                if y == x:
                    real_num_col_name_list.append(x)
        return real_num_col_name_list

    @staticmethod
    def _get_existed_col_value_group(data, operate_col_name_list):
        """
        获取已存在的字段值组合
        :param data:
        :param operate_col_name_list:
        :return:
        """
        result = {}
        for item in data:
            result.update({tuple([item.get(col_name) for col_name in operate_col_name_list]): item})
        return result

    @staticmethod
    def _get_regrouped_data(data, operate_col_name_list):
        """
        组装返回的数据
        :param data:
        :param operate_col_name_list:
        :return:
        """
        tmp_zero_dict = defaultdict(list)
        for item in data:
            value_zero = item.get(operate_col_name_list[0])
            tmp_zero_dict[value_zero].append(item)
        del data
        new_data = []
        for i in tmp_zero_dict.values():
            new_data.extend(i)
        # 限制返回的结果集数量
        return new_data[:3000]

    def get_limit_field(self, model: ChartDataModel = None):
        """
        获取limit数据
        在有对比维度存在的情况下 limit 获取受限的最大值
        个别定制老的查询 在相关类中重置该方法
        :param model:
        :return:
        """
        real_model = model or self.chart_data_model
        self.limit_field = Limit.get_limit_field_for_default(real_model)
        # 前N后N
        # 如果是后N的情况，需要重新设置limit中的offset值
        # 标记offset为-9999为后N类型，需要重新设置offset值
        if self.limit_field and self.limit_field.offset == CHART_LAST_N_SPECIAL_FLAG:
            # TODO count总量获取后续优化
            display_item = real_model.display_item
            if self.chart_data_model.comparisons:
                real_model.display_item = "{}"
            self.data_total = self.query_count()
            if self.chart_data_model.comparisons:
                real_model.display_item = display_item
                self.limit_field = Limit.get_limit_field_for_default(real_model)
            self.limit_field.offset = 0
            if self.data_total:
                self.limit_field.offset = (
                    self.data_total - self.limit_field.limit if self.data_total > self.limit_field.limit else 0
                )
        return self.limit_field

    @staticmethod
    def _assign_formula_mode(field, fomula_mode):
        """
        处理formula_mode
        :param field:
        :param fomula_mode:
        :return:
        """
        return "{}_{}".format(fomula_mode, field) if fomula_mode else field

    def get_subtotal_for_struct(self):
        """
        该组件无小计
        :return:
        """
        return {}

    def get_subtotal_for_data(
        self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
            prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        该组件无小计
        :param data:
        :param next_item:
        :return:
        """
        return {}, data_frame, original_data

    def apply_funcs(self, db_data: list):
        """
        该组件无其他辅助功能(如透视)
        :param db_data:
        :return:
        """
        return pd.DataFrame(db_data), db_data
