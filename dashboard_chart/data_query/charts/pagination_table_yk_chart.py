#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    <NAME_EMAIL> 2018/9/8
"""
from dashboard_chart.convertor.limit.limit import Limit
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.models import ChartDataModel


class PaginationTableYkChart(CommonChart):

    # 重写父类，分页表默认追加master_id排序
    def get_master_id_order(self):
        return True

    def get_limit_field(self, model: ChartDataModel = None):
        """
        获取Limit对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        model = model or self.chart_data_model
        self.limit_field = Limit.get_limit_field_for_page(model)
        return self.limit_field

    def get_group_fields(self, model: ChartDataModel = None):
        """
        无页码的表格维度不需要聚合
        :param model:
        :return:
        """
        return False
