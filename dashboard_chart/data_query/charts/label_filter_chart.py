#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint:disable=E0102, W0613
"""
列表筛选
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.agent.query_agent import QueryAgent
from copy import deepcopy
from typing import List, Dict, Tuple
import pandas as pd

from dashboard_chart.models import ChartDataModel


class LabelFilterChart(CommonChart):
    def batch_query_in_normal_condition(self):
        """
        不带筛选的场景，分别拼装sql查询获取各个字段的所有值
        :return:
        """
        result_list = []
        for i in self.chart_data_model.dims:
            query_chart_data_model = deepcopy(self.chart_data_model)
            query_chart_data_model.dims = [i]
            result_list.append(self.query_specified_field_data(query_chart_data_model))
        return result_list

    def batch_query_in_filter_condition(self):
        """
        带筛选的场景
        注：查询字段时，只有除了字段自己的其他字段的筛选条件才生效
        :return:
        """
        result_list = []
        for i in self.chart_data_model.dims:
            operate_conditions = []
            for lf in self.chart_data_model.label_filter_conditions:
                if (
                    i.get("dataset_field_id")
                    and lf.get("dataset_field_id")
                    and i.get("dataset_field_id") != lf.get("dataset_field_id")
                ):
                    operate_conditions.append(lf)

            query_chart_data_model = deepcopy(self.chart_data_model)
            query_chart_data_model.dims = [i]
            query_chart_data_model.label_filter_conditions = operate_conditions
            result = self.query_specified_field_data(query_chart_data_model)
            result_list.append(result)
        return result_list

    def query_specified_field_data(self, model):
        """
        获取字段的值
        :param model:
        :return:
        """
        where_fields = self.get_where_fields(model)
        data = {
            "select": self.get_select_fields(model),
            "where": where_fields,
            "group": self.get_group_fields(model),
            "order": self.get_order_fields_for_multi(model),
            "limit": self.get_limit_field(model),
            "vars": self.get_dataset_vars(model),
            "dataset_field_dict": self.chart_data_model.dataset_field_dict,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        query_agent = QueryAgent()
        json_struct = query_agent.convert(data)
        query_params = {
            "user_id": self.get_user_id(),
            "dataset_id": self.chart_data_model.dataset_id,
            "chart_id": self.chart_data_model.id,
            "json_struct": json_struct,
            "is_order_master_id": self.get_master_id_order(),
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        result = QueryAgent.query(**query_params)
        # 给result另外赋值其他字段
        over_limit_flag = 1 if result and len(result.get("data", [])) > 500 else 0
        result = super().get_data(
            result=result, model=model, where_fields=where_fields, over_limit_flag=bool(over_limit_flag)
        )
        result['over_limit_flag'] = over_limit_flag
        return result

    def get_marklines_for_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        该组件无辅助线
        :return:
        """
        return []

    def get_subtotal_for_struct(self):
        """
        该组件无小计
        :return:
        """
        return {}

    def get_subtotal_for_data(
        self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
            prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        该组件无小计
        :param data_frame:
        :param next_item:
        :return:
        """
        return {}, data_frame, original_data

    def apply_funcs(self, db_data: list) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        追加功能应用 (如透视表)
        此处一律返回pandas dataFrame
        该定制组件无此功能
        :return pd.DataFrame:
        """
        return pd.DataFrame(db_data), db_data

    def query_data(self, advance_next=0, dataset_version: str = None) -> List[Dict]:
        """
        重写方法，用于标签列表筛选组件
        :param advance_next:
        :return:
        """
        # 1，有筛选条件的情况，将筛选条件应用在其他字段查询上
        if self.chart_data_model.label_filter_conditions:
            result = self.batch_query_in_filter_condition()
        # 2，没有筛选条件的情况，直接分别查询各个维度的所有字段值
        else:
            result = self.batch_query_in_normal_condition()
        return result

    def get_data(
        self,
        result: dict,
        advance_next: int = 0,
        model: ChartDataModel = None,
        where_fields: list = None,
        order_fields: list = None,
        over_limit_flag: bool = None,
    ):
        """
        重写方法，用于标签筛选组件
        :param result:
        :return:
        """
        return result
