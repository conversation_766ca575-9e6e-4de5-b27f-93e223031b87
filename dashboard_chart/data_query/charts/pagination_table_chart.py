#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/8
"""
from dashboard_chart.convertor.limit.limit import Limit
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.models import ChartDataModel
from dashboard_chart.convertor.field_types import OrderField


class PaginationTableChart(CommonChart):
    def get_limit_field(self, model: ChartDataModel = None):
        """
        获取Limit对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        model = model or self.chart_data_model
        self.limit_field = Limit.get_limit_field_for_page(model)
        return self.limit_field

    @staticmethod
    def _convert_group2order(group_field):
        """
        将GroupField 转换为 OrderField
        :param group_field:
        :return:
        """
        field_dict = group_field.get_dict()
        return OrderField(
            **{
                "col_name": field_dict.get("field"),
                "table_name": field_dict.get("table"),
                "value_type": field_dict.get("value_type"),
                "field_func": field_dict.get("field_func"),
                "type": field_dict.get("field_type"),
                "expressions": field_dict.get("expressions"),
                "props": field_dict.get("props"),
                "id": field_dict.get("field_ref"),
            }
        )

    def get_order_fields_for_multi(self, model: ChartDataModel = None):
        """
        获取多排序字段
        分页表格排序字段比较特殊
        对于ADS类型数据库 ORDER BY 所用的字段在 GROUP BY 里面必须存在
        因此此处解决方案为 在以后ORDER BY 后面添加GROUP BY 字段
        经查ORDER BY中字段重复不会产生影响(xxx)
        :param model:
        :return:
        """
        group_fields = self.get_group_fields(model)
        order_fields = super().get_order_fields_for_multi(model)
        if group_fields:
            order_fields_name = [order_field.field for order_field in order_fields]
            for group_field in group_fields:
                if group_field.field not in order_fields_name:
                    order_fields.append(self._convert_group2order(group_field))
        self.order_fields = order_fields
        return self.order_fields
