#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/2 18:27
# <AUTHOR> caoxl
# @File     : statistic_table_chart.py
# pylint:disable=W0613
import pandas as pd
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.data_query.widget.subtotal_query import SubtotalQuery
from typing import Tuple, Dict, List


class StatisticTableChart(CommonChart):
    # 重写父类，分页表默认追加master_id排序
    def get_master_id_order(self):
        return True

    def get_advance_num(self):
        """
        获取查询条数
        :return:
        """
        return int(bool(self.chart_data_model.enable_subtotal))

    def _get_subtotal_query_instance(self):
        if not hasattr(self, '_subtotal_instance'):
            self._subtotal_instance = SubtotalQuery(
                chart_data_model=self.chart_data_model,
                orgin_where_fields=self.get_where_fields(),
                query_vars=self.get_dataset_vars(),
                user_id=self.get_user_id(),
            )
        return self._subtotal_instance

    def get_subtotal_for_data(
        self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
        prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        获取小计
        common类中为老的小计
        基类为标准小计，后续统一为标准小计
        :param data:
        :param next_item:
        :return:
        """
        subtotal = self._get_subtotal_query_instance()
        subtotal_summary, subtotal_cate = subtotal.get_subtotal_for_data(
            data=original_data, next_item=original_next_item
        )
        return {"subtotal_cate": subtotal_cate, "subtotal_summary": subtotal_summary}, data_frame, original_data

    def get_subtotal_for_struct(self):
        """
        获取小计查询结构
        :return:
        """
        subtotal = self._get_subtotal_query_instance()
        subtotal_summary_struct, subtotal_cate_struct = subtotal.get_subtotal_for_struct()
        return {"subtotal_cate": subtotal_cate_struct, "subtotal_summary": subtotal_summary_struct}
