#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/16 15:36
# <AUTHOR> caoxl
# @File     : column_chart.py
# pylint: disable=W0613,R0201
import copy
import datetime

from loguru import logger

from dashboard_chart.data_query.charts.common_chart import CommonChart
import pandas as pd
import numpy as np
from typing import Union, Dict, List, Tuple
from base.enums import ColTypes, OrderType, MockField
from base.dmp_constant import CHART_COLUMN_LIMIT_NUM
from dashboard_chart.models import ColumnHeaderModel
from dmplib.utils.errors import UserError
from dashboard_chart.data_query.widget import utils as WidgetUtil
from dashboard_chart.convertor import utils as ConvertorUtils
from dashboard_chart.models import ChartDataModel
from dashboard_chart.data_query import utils as DataQueryUtil


class ColumnChart(CommonChart):
    """
    按列返回类型数据逻辑类型
    """

    # 重写父类，分页表默认追加master_id排序
    def get_master_id_order(self):
        if self.chart_data_model.aggregation:
            return False
        else:
            return True

    def _get_chart_alias_obj(self):
        if not hasattr(self, '_chart_alias_obj'):
            self._chart_alias_obj = DataQueryUtil.ChartDisplayAlias(self.chart_data_model)
        return self._chart_alias_obj

    def get_order_fields_for_multi(self, model: ChartDataModel = None):
        """
        获取排序字段
        此处若无排序 需要进行处理 维度字段->维度字段（补充）->数值字段（无对比维度时有效）->目标值字段的顺序排列优先级，同类字段中根据上下顺序排列优先级
        :param model:
        :return:
        """
        model = model or self.chart_data_model
        self.order_fields = super().get_order_fields_for_multi(model)
        return self.order_fields

    def _op_multi_column_header(
        self, col_header: Union[List, Tuple], col_names: List, dataset_alias_dict: Dict, formatted_header: Dict
    ) -> Dict:
        for idx, header in enumerate(col_header):
            col_value = None
            if col_names[idx] is not None:
                col_name = col_names[idx]
                col_value = header
            else:
                col_name = header
            field = dataset_alias_dict.get(col_name)
            if not field:
                raise UserError(message="展示名为 {alias} 的字段不存在!".format(alias=col_name))
            field.col_value = col_value if not pd.isnull(col_value) else None
            formatted_header["header"].append(field.get_dict())
        # 如果有对比维度且对比维度前置 则需要对表头顺序进行排序 对比维度放在前面其他放在后面
        if self.chart_data_model.pre_comparison and self.chart_data_model.comparisons:
            formatted_header["header"] = sorted(
                formatted_header["header"],
                key=lambda item: int(item.get("col_type") == ColTypes.Comparison.value),
                reverse=True,
            )
        return formatted_header

    def _op_simple_column_header(self, col_header: str, dataset_alias_dict: Dict, formatted_header: Dict) -> Dict:
        field = dataset_alias_dict.get(col_header)
        if not field:
            raise UserError(message="展示名为 {alias} 的字段不存在!".format(alias=col_header))
        formatted_header["header"] = [field.get_dict()]
        return formatted_header

    def _get_comparion_index_sort(self) -> List:
        """
        获取对比维度排序方法
        :return:
        """
        return [
            OrderType.Desc.value if comparison.get('sort') == OrderType.Desc.value else OrderType.Asc.value
            for comparison in self.chart_data_model.comparisons
        ]

    def _op_cross_headers(self, data_frame: pd.DataFrame) -> pd.DataFrame:
        if self.chart_data_model.pre_comparison and self.chart_data_model.comparisons:
            columns = self._sort_columns(data_frame.columns)
            return data_frame.reindex(columns, axis=1)
        return data_frame

    def _sort_columns(self, columns):
        comparion_index_sort = self._get_comparion_index_sort()
        index = len(comparion_index_sort) - 1
        column_values = columns.values.tolist()
        while index >= 0:
            sort_method = comparion_index_sort[index]
            reversed_sort = bool(sort_method == OrderType.Desc.value)
            none_values, num_values, str_values, other_values = [], [], [], []
            for item in column_values:
                val = item[index + 1]
                if isinstance(val, type(None)):
                    none_values.append(val)
                elif isinstance(val, (float, int)):
                    num_values.append(val)
                elif isinstance(val, str):
                    str_values.append(val)
                else:
                    other_values.append(val)
            sort_values = [
                *none_values,
                *sorted(num_values, reverse=reversed_sort),
                *sorted(str_values, reverse=reversed_sort),
                *other_values,
            ]
            sort_key_dict = {val: sort_num for sort_num, val in enumerate(sort_values)}
            column_values.sort(key=lambda column: sort_key_dict.get(column[index + 1], 0))
            index -= 1
        column_sort_dict = {column_value: sort_num for sort_num, column_value in enumerate(column_values)}
        return sorted(columns, key=lambda column: column_sort_dict.get(column, 0))

    def _get_cross_header_column_sort_dict(
        self, column_value_key, columns_dict_key, columns_dict_values, columns_sort_dict, current_index
    ):
        for i, _ in enumerate(columns_dict_values):
            if columns_dict_values[i] == column_value_key:
                continue
            columns_sort_dict[tuple([*columns_dict_values[i], *columns_dict_key])] = current_index

    def restructure_chart_data(self, data: pd.DataFrame, original_data: List[Dict]) -> Union[List, Dict]:
        """
        组织数据，此处按列返回
        :param:
        :return:
        """
        # 按列返回需要将DataFrame  index 和 columns 都返回
        result = []
        # 数据为空时直接返回
        if len(data) < 1:
            return result
        # 填充NaN为None
        data_frame = data.where(pd.notnull(data), None)
        dataset_alias_dict = self.get_dataset_alias_dict()
        # 1. 返回 index 列信息
        # pandas 默认会为数据增加一列为数字的索引 此时index names 只有一个元素 且 值为None
        # 此处有没有dim的情况(没有dim 透视表会模拟一行作为dim)
        index_names = list(filter(lambda item: item != MockField.Dim.value, data_frame.index.names))
        if index_names and (not (len(index_names) == 1 and index_names[0] is None)):
            self._op_index_cols(data_frame, index_names, dataset_alias_dict, result)

        # 2. 处理对比维度前置交叉表头问题
        # data_frame = self._op_cross_headers(data_frame)

        # 3. 返回 columns 列信息
        col_headers = data_frame.columns.values
        col_names = data_frame.columns.names
        for col_header in col_headers[: CHART_COLUMN_LIMIT_NUM]: # 对比维度的列可能非常多，限制到100列
            # 返回给前端的数据不能包含NaN
            rows = data_frame[col_header]
            rows = rows.where(rows.notnull(), None)
            # 如果是时间类型 按照字符串进行处理 否则pandas将会将其转换为数字
            if rows.dtype.name in [
                np.dtype('datetime64').name,
                datetime.timedelta.__name__,
                np.dtype('datetime64[ns]').name,
                datetime.timedelta.__name__,
            ]:
                rows.fillna('')
            # 老的备份
            if rows.dtype.name in [np.dtype('datetime64').name, datetime.timedelta.__name__]:
                rows.fillna('')
                row_values = rows.astype(np.dtype('datetime64')).tolist()
            elif rows.dtype.name in [np.dtype('datetime64[ns]').name, datetime.timedelta.__name__]:
                rows.fillna('')
                row_values = rows.astype(np.dtype('datetime64[ns]')).tolist()
            else:
                row_values = rows.values.tolist()
            row_values = self._convert_item_data_type(row_values)
            col = {"rows": row_values, "header": []}
            if isinstance(col_header, (list, tuple)):
                self._op_multi_column_header(col_header, col_names, dataset_alias_dict, col)
            else:
                self._op_simple_column_header(col_header, dataset_alias_dict, col)
            result.append(col)
        return result

    def _convert_item_data_type(self, items: list) -> list:
        return [DataQueryUtil.convert_to_py_data_type(item) for item in items]

    def _op_index_cols(
        self, data_frame: pd.DataFrame, index_names: List, dataset_alias_dict: Dict, result: List
    ) -> None:
        """
        处理索引列
        :param data_frame:
        :param index_names:
        :param result:
        :return:
        """
        index_values = data_frame.index.values.tolist()
        col_data_with_index = {index_name: [] for index_name in index_names}
        for value in index_values:
            if isinstance(value, (list, tuple)):
                for idx, val in enumerate(value):
                    val = WidgetUtil.convert_to_py_data_type(val)
                    col_data_with_index[index_names[idx]].append(val)
            else:
                val = WidgetUtil.convert_to_py_data_type(value)
                col_data_with_index[index_names[0]].append(val)
        for col_name, col_values in col_data_with_index.items():
            field = dataset_alias_dict.get(col_name)
            # TODO testlog
            if field is None:
                logger.error(f'field None: 1:{col_name}, 2:{col_values}, 3:{dataset_alias_dict}, 4:{col_data_with_index}')
            col = {"rows": col_values, "header": [field.get_dict()]}
            result.append(col)

    def get_dataset_alias_dict(self) -> Dict[str, ColumnHeaderModel]:
        select_fields = self.get_select_fields()
        dataset_alias_dict = {}
        chart_alias_obj = self._get_chart_alias_obj()
        for select_field in select_fields:
            dict_key = select_field.alias or select_field.field
            field_id = select_field.field_ref
            # 解决自助分析flag字段没有数据集字段id问题
            if field_id is None:
                continue
            display_name = select_field.field
            field_info = self.chart_data_model.dataset_field_dict.get(field_id)
            if field_info:
                display_name = chart_alias_obj.get_alias(field_id, select_field.logic_source) or field_info.get(
                    "alias_name"
                )
            dataset_alias_dict[dict_key] = ColumnHeaderModel(
                **{
                    "col_name": field_info.get("col_name"),
                    "alias_name": select_field.alias,
                    "alias": display_name,
                    "dataset_id": self.chart_data_model.dataset.get("id"),
                    "field_id": field_id,
                    "col_type": self.get_select_field_col_type(select_field.alias or select_field.field),
                }
            )
        return dataset_alias_dict

    def get_select_field_col_type(self, field_alias: str):
        col_type_dict = self._get_select_fields_col_type_dict()
        col_type = col_type_dict.get(field_alias)
        return col_type

    def _get_select_fields_col_type_dict(self) -> Dict:
        if not hasattr(self, '_select_fields_col_type_dict'):
            self._select_fields_col_type_dict = ConvertorUtils.get_alias_logic_source_dict(self.get_select_fields())
        return self._select_fields_col_type_dict
