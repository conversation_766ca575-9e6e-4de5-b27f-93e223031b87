#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/8
"""
from typing import List, Dict, Tuple

import pandas as pd
from dashboard_chart.data_query.charts.chart_query import ChartQuery


class NondatasetChart(ChartQuery):
    def get_chart_data(self, dataset_version: str = None):
        return {}

    def get_variable_chart_data(self):
        return {}

    def get_item_list(self):
        return []

    def apply_funcs(self, db_data: list):
        """
        该组件无其他辅助功能(如透视)
        :param db_data:
        :return:
        """
        return pd.DataFrame(db_data), db_data

    def get_query_struct(self):
        return {}

    def get_marklines_for_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        该组件无辅助线
        :return:
        """
        return []

    def get_subtotal_for_struct(self):
        """
        该组件无小计
        :return:
        """
        return {}

    def get_subtotal_for_data(
        self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
        prev_item_frame, original_prev_item
    ) -> <PERSON><PERSON>[Dict, pd.DataFrame, List[Dict]]:
        """
        该组件无小计
        :param data:
        :param next_item:
        :return:
        """
        return {}, data_frame, original_data
