#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=W0613
"""
    <NAME_EMAIL> 2018/9/8
"""
from decimal import Decimal
from typing import Tuple, List, Dict

import pandas as pd
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.group.group import Group
from dashboard_chart.convertor.limit.limit import Limit
from dashboard_chart.convertor.select.select import Select
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.models import ChartDataModel


class IntervalChart(CommonChart):
    def get_select_fields(self, model: ChartDataModel = None):
        self.select_fields = Select.get_select_fields_for_interval(self.chart_data_model)
        return self.select_fields

    def get_group_fields(self, model: ChartDataModel = None):
        self.group_fields = []
        return self.group_fields

    def get_order_fields_for_multi(self, model: ChartDataModel = None):
        self.order_fields = []
        return self.order_fields

    def get_limit_field(self, model: ChartDataModel = None):
        self.limit_field = Limit.get_limit_field_for_interval()
        return self.limit_field

    def restructure_chart_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        重新组织data数据满足各个单图类型数据结构
        :param pd.DataFrame data: 转换后的数据
        :param List original_data: 原始数据
        :return:
        """
        # 获取最小最大控件数据，将数据组装为最大值最小值格式
        filter_data = {}
        # 3. 目前只允许一个维度做区间筛选器
        dict_data = original_data
        for field in self.chart_data_model.dims:
            dataset_field = self.chart_data_model.dataset_field_dict.get(field.get("dim"))

            col_name = dataset_field.get("alias") if dataset_field.get("alias") else dataset_field.get("col_name")
            if dict_data:
                for r in dict_data:
                    temp_dict = {k: (lambda v: float(v) if isinstance(v, Decimal) else v)(v) for k, v in r.items()}
                    filter_data[col_name] = [temp_dict]
            else:
                filter_data[col_name] = dict_data
        if filter_data:
            return [filter_data]
        return []

    def get_variable_chart_data(self):
        """
        根据前端传入参数灵活查询数据接口（前端传入dim、num等可以直接替换当前单图的对应字段数据后进行查询）
        :return:
        """
        # 1. 验证字段是否正确
        self.validation_dataset_fields(self.get_need_validation_fields())

        # 2. 查询数据
        result = self.query_variable_chart_data()

        # 3. 设置data数据
        data = self.get_variable_data(result)

        return data

    def query_variable_chart_data(self):
        """
        查询数据
        :return:
        """
        data = {
            "select": Select.get_select_fields(self.chart_data_model),
            "where": self.get_where_fields(),
            "group": Group.get_group_fields(self.chart_data_model),
            "order": self.get_order_fields_for_multi(),
            "limit": Limit.get_limit_field(self.chart_data_model),
            "vars": self.get_dataset_vars(self.chart_data_model),
            "dataset_field_dict": self.chart_data_model.dataset_field_dict,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        query_agent = QueryAgent()
        json_struct = query_agent.convert(data)
        query_params = {
            "user_id": self.get_user_id(),
            "dataset_id": self.chart_data_model.dataset_id,
            "chart_id": self.chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        result = QueryAgent.query(**query_params)

        return result

    def apply_funcs(self, db_data: list) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        该类型组件无透视表等功能
        :return pd.DataFrame:
        """
        return pd.DataFrame(db_data), db_data

    def get_marklines_for_data(self, data: pd.DataFrame, original_data: List[Dict]):
        """
        该组件无辅助线
        :return:
        """
        return []

    def get_subtotal_for_struct(self):
        """
        该组件无小计
        :return:
        """
        return {}

    def get_subtotal_for_data(
        self, data_frame: pd.DataFrame, next_item_frame: pd.DataFrame, original_data, original_next_item,
            prev_item_frame, original_prev_item
    ) -> Tuple[Dict, pd.DataFrame, List[Dict]]:
        """
        该组件无小计
        :param data:
        :param next_item:
        :return:
        """
        return {}, data_frame, original_data
