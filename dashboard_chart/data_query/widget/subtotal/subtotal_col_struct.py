#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/11/5 18:09
# <AUTHOR> caoxl
# @File     : subtotal_col_struct.py
# pylint: disable=R0201
import json
import copy
from base.models import BaseModelEncoder
from typing import List, Dict
import pandas as pd
from base.enums import ColTypes
from dashboard_chart.models import ChartDataModel, SubtotalColRowModel, SubtotalColColOfRowModel, SubtotalHeaderModel


class SubtotalBaseStruct:
    """
    结构基础类
    结构分为 小计结构  和 总计结构
    """

    def __init__(
        self,
        subtotal_col_data: List[Dict],
        subtotal_row_data: List[Dict],
        header_data: dict,
        chart_data_model: ChartDataModel,
        subtotal_cate_fields: List = None,
        next_item: pd.DataFrame = None,
        prev_item: pd.DataFrame = None,
    ):
        self._subtotal_col_data = subtotal_col_data
        self._subtotal_row_data = subtotal_row_data
        self._chart_data_model = chart_data_model
        self._header_data = header_data
        self._subtotal_cate_fields = subtotal_cate_fields
        self._next_item = next_item
        self._prev_item = prev_item
        self._cols = []

    def sort_multi_header(self, multi_header: List) -> List:
        # 如果有对比维度且对比维度前置 则需要对表头顺序进行排序 对比维度放在前面其他放在后面
        if self._chart_data_model.pre_comparison and self._chart_data_model.comparisons:
            multi_header = sorted(
                multi_header, key=lambda item: int(item.col_type == ColTypes.Comparison.value), reverse=True
            )
        return multi_header

    def dumps_header_model(self, header):
        return json.dumps(header, cls=BaseModelEncoder).encode('utf8')

    def get_header_model(self, json_str):
        return SubtotalHeaderModel(**json.loads(json_str))

    def build_subtotal_row_struct(self, subtotal_row_data: List[dict], index_headers: List, num_headers: List):
        struct = {}
        for item in subtotal_row_data:
            num_dict = {}
            for num_header in num_headers:
                header_item = copy.deepcopy(num_header[0])
                num_dict[self.dumps_header_model(header_item)] = item.get(header_item.alias_name)
            index_header_tuple = []
            for index_header in index_headers:
                header_item = copy.deepcopy(index_header[0])
                header_item.col_value = item.get(header_item.alias_name)
                index_header_tuple.append(self.dumps_header_model(header_item))
            index_dict_key = tuple(index_header_tuple) if index_header_tuple else None
            if index_dict_key not in struct:
                struct[index_dict_key] = num_dict
            else:
                struct[index_dict_key].update(num_dict)
        return struct

    def build_subtotal_col_struct(
        self, subtotal_col_data: List[dict], index_headers: List, comparison_dim_headers: List, num_headers: List
    ):
        struct = {}
        for item in subtotal_col_data:
            index_list = []
            for index_header in index_headers:
                header_item = copy.deepcopy(index_header[0])
                header_item.col_value = item.get(header_item.alias_name)
                index_list.append(self.dumps_header_model(header_item))
            comparison_list = []
            for dim_header in comparison_dim_headers:
                header_item = copy.deepcopy(dim_header[0])
                header_item.col_value = item.get(header_item.alias_name)
                comparison_list.append(self.dumps_header_model(header_item))
            num_dict = {}
            for num_header in num_headers:
                header_item = copy.deepcopy(num_header[0])
                num_dict[self.dumps_header_model(header_item)] = item.get(header_item.alias_name)
            index_dict_key = tuple(index_list) if index_list else None
            index_dict = {tuple(comparison_list): num_dict} if comparison_list else num_dict
            if index_dict_key not in struct:
                struct[index_dict_key] = index_dict
            else:
                struct[index_dict_key].update(index_dict)
        return struct

    def get_header_dict_key(self, col_header: List[SubtotalHeaderModel]):
        return tuple([header_model.col_value or header_model.alias_name for header_model in col_header])

    def build_sutotal_row_cols(self, index_header_tuple: tuple, row_cols: list, subtotal_row_struct: dict):
        subtotal_row_nums = subtotal_row_struct.get(index_header_tuple, {})
        for subtotal_num_header, col_value in subtotal_row_nums.items():
            header_item = self.get_header_model(subtotal_num_header)
            row_cols.append(SubtotalColColOfRowModel(**{"header": [header_item], "col_value": col_value}))

    def build_data_num_cols(
        self,
        data_num_headers: list,
        index_header_tuple: tuple,
        num2subtotal_header_dict: dict,
        row_cols: list,
        subtotal_col_struct: dict,
    ):
        for num_header in data_num_headers:
            # 将数据header转换为列小计header
            subtotal_num_header = num2subtotal_header_dict.get(self.get_header_dict_key(num_header))
            if not subtotal_num_header:
                continue
            if self._chart_data_model.comparisons:
                # 透视表需要特殊处理
                comparison_num_header = None
                comparison_header_tuple = []
                # 列小计的对比维度部分和原始数据是一致的，只有num部分不一致，需要转换
                for item in subtotal_num_header:
                    if item.col_type == ColTypes.Num.value:
                        comparison_num_header = item
                    else:
                        comparison_header_tuple.append(self.dumps_header_model(item))
                comparison_header_tuple = tuple(comparison_header_tuple)
                comparison_row = subtotal_col_struct.get(index_header_tuple, {}).get(comparison_header_tuple, {})
                col_value = comparison_row.get(self.dumps_header_model(comparison_num_header))
                row_cols.append(
                    SubtotalColColOfRowModel(**{"header": self.sort_multi_header(num_header), "col_value": col_value})
                )
            else:
                # 普通数据直接获取
                subtotal_col_row = subtotal_col_struct.get(index_header_tuple, {})
                row_cols.append(
                    SubtotalColColOfRowModel(
                        **{
                            "header": num_header,
                            "col_value": subtotal_col_row.get(self.dumps_header_model(subtotal_num_header[0])),
                        }
                    )
                )

    def build(self):
        pass


class SubtotalStruct(SubtotalBaseStruct):
    """
    小计结构拼接类
    """

    def __init__(
        self,
        subtotal_col_data: List[Dict],
        subtotal_row_data: List[Dict],
        header_data: dict,
        chart_data_model: ChartDataModel,
        subtotal_cate_fields: List,
        next_item: pd.DataFrame,
        prev_item: pd.DataFrame,
    ):
        super().__init__(
            subtotal_col_data=subtotal_col_data,
            subtotal_row_data=subtotal_row_data,
            header_data=header_data,
            chart_data_model=chart_data_model,
            subtotal_cate_fields=subtotal_cate_fields,
            next_item=next_item,
            prev_item=prev_item,
        )

    def build(self):
        """
        获取最终结构
        :return:
        """
        index_headers = self._header_data.get("data_index_headers")
        subtotal_row_headers = self._header_data.get("subtotal_row_headers")
        data_num_headers = self._header_data.get("data_num_headers")
        num2subtotal_header_dict = self._header_data.get("data_num2subtotal_header_dict")
        num_headers = self._header_data.get("num_headers")
        comparison_dim_headers = self._header_data.get("comparsion_dim_headers")
        subtotal_row_struct = self.build_subtotal_row_struct(
            self._subtotal_row_data, index_headers, subtotal_row_headers
        )
        subtotal_col_struct = self.build_subtotal_col_struct(
            self._subtotal_col_data, index_headers, comparison_dim_headers, num_headers
        )
        for index_header_tuple, val in subtotal_col_struct.items():
            row_cols = []
            # 获取index header
            has_prev, has_next = 1, 1
            for index_header_item in index_header_tuple:
                header_item = self.get_header_model(index_header_item)
                col_value = header_item.col_value
                header_item.col_value = None
                if self._get_has_next(self._next_item, header_item.alias_name, col_value) == 0:
                    has_next = 0
                if self._get_has_prev(self._prev_item, header_item.alias_name, col_value) == 0:
                    has_prev = 0
                row_cols.append(SubtotalColColOfRowModel(**{"header": [header_item], "col_value": col_value}))
            # 获取num header
            self.build_data_num_cols(
                data_num_headers, index_header_tuple, num2subtotal_header_dict, row_cols, subtotal_col_struct
            )
            # 获取行小计 header
            self.build_sutotal_row_cols(index_header_tuple, row_cols, subtotal_row_struct)
            self._cols.append(
                SubtotalColRowModel(
                    **{
                        "has_next": has_next,
                        "has_prev": has_prev,
                        "cate_fields": self._subtotal_cate_fields,
                        "cols": row_cols
                    }
                )
            )
        return self._cols

    def _get_has_next(self, next_item: pd.DataFrame, key: str, value) -> int:
        """
        获取是否有下一条
        :param next_item:
        :param key:
        :param value:
        :return:
        """
        if next_item is None or not len(next_item):
            return 0
        if isinstance(next_item.index, pd.RangeIndex):
            return int(next_item[key].values[0] == value)
        elif isinstance(next_item.index, pd.MultiIndex):
            index_dict = {name: idx for idx, name in enumerate(next_item.index.names)}
            idx = index_dict.get(key)
            df_value = next_item.index[0][idx]
            return df_value == value
        elif isinstance(next_item.index, pd.Index):
            index_dict = {name: idx for idx, name in enumerate(next_item.index.names)}
            idx = index_dict.get(key)
            df_value = next_item.index[idx]
            return df_value == value
        return 0

    def _get_has_prev(self, prev_item: pd.DataFrame, key: str, value) -> int:
        """
        获取是否有上一条
        :param next_item:
        :param key:
        :param value:
        :return:
        """
        return self._get_has_next(prev_item, key, value)



class SubtotalSummaryStruct(SubtotalBaseStruct):
    """
    总计结构拼接
    """

    def __init__(
        self,
        subtotal_col_data: List[Dict],
        subtotal_row_data: List[Dict],
        header_data: dict,
        chart_data_model: ChartDataModel,
    ):
        super().__init__(
            subtotal_col_data=subtotal_col_data,
            subtotal_row_data=subtotal_row_data,
            header_data=header_data,
            chart_data_model=chart_data_model,
            subtotal_cate_fields=None,
            next_item=None,
        )

    def build(self):
        """
        获取最终结构
        :return:
        """
        index_headers = self._header_data.get("data_index_headers")
        subtotal_row_headers = self._header_data.get("subtotal_row_headers")
        data_num_headers = self._header_data.get("data_num_headers")
        num2subtotal_header_dict = self._header_data.get("data_num2subtotal_header_dict")
        num_headers = self._header_data.get("num_headers")
        comparison_dim_headers = self._header_data.get("comparsion_dim_headers")
        subtotal_row_struct = self.build_subtotal_row_struct(
            self._subtotal_row_data, index_headers, subtotal_row_headers
        )
        subtotal_col_struct = self.build_subtotal_col_struct(
            self._subtotal_col_data, index_headers, comparison_dim_headers, num_headers
        )
        for index_header_tuple, val in subtotal_col_struct.items():
            row_cols = []
            # 获取num header
            self.build_data_num_cols(
                data_num_headers, index_header_tuple, num2subtotal_header_dict, row_cols, subtotal_col_struct
            )
            # 获取行小计 header
            self.build_sutotal_row_cols(index_header_tuple, row_cols, subtotal_row_struct)
            self._cols = row_cols
        return self._cols
