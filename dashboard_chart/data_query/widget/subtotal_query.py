#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/5/16 14:38
# <AUTHOR> caoxl
# @File     : subtotal_query.py
# pylint:disable=R0401
from collections import defaultdict
import copy
from base.enums import SqlWhereOperator
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.group.dim_group import DimGroup
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.select.count_all_select import CountAllSelect
from dashboard_chart.convertor.select.num_select import NumSelect
from dashboard_chart.convertor.where.where import Where
from dashboard_chart.models import FieldExtDataModel


class SubtotalQuery:
    def __init__(self, chart_data_model, orgin_where_fields, query_vars, user_id):
        self._chart_data_model = chart_data_model
        self._user_id = user_id
        self._origin_where_fields = orgin_where_fields
        self._query_vars = query_vars
        self._query_angent = None

    def _get_subtotal_groups(self):
        subtotal_groups = []
        sorted_dims = copy.deepcopy(self._chart_data_model.dims)
        for idx, dim in enumerate(sorted_dims):
            if dim.get("is_subtotal_cate") == 1:
                subtotal_groups.append(sorted_dims[0 : idx + 1])
        return subtotal_groups

    @staticmethod
    def _get_dict_matrix(dict_data, keys):
        matrix = []
        for key in keys:
            matrix.append(dict_data.get(key))
        return matrix

    def _compare_dict(self, dict1, dect2, keys):
        if not dict1 or not dect2:
            return False
        dict1_matrix = self._get_dict_matrix(dict1, keys)
        dict2_matrix = self._get_dict_matrix(dect2, keys)
        is_eq = True
        for idx, _ in enumerate(keys):
            if dict1_matrix[idx] != dict2_matrix[idx]:
                is_eq = False
                break
        return is_eq

    @staticmethod
    def _get_data_col_name(field):
        return field.alias or field.field

    @staticmethod
    def _assign_field_extdata(field, ext_data_model: FieldExtDataModel):
        field.ext_data = ext_data_model.get_dict()

    def _assign_num_to_base_fields(self, base_select_fields: list):
        for num in self._chart_data_model.nums:
            if num.get("subtotal_formula_mode"):
                original_num_field = self._num2select(num)
                num_copy = copy.deepcopy(num)
                num_copy["formula_mode"] = num.get("subtotal_formula_mode")
                subtotal_num_field = self._num2select(num_copy)
                self._assign_field_extdata(
                    subtotal_num_field,
                    FieldExtDataModel(
                        **{
                            "detail_col_name": self._get_data_col_name(original_num_field),
                            "dataset_id": num_copy.get("dataset_id"),
                            "col_name": self._get_data_col_name(subtotal_num_field),
                            "dataset_field_id": num_copy.get("dataset_field_id"),
                        }
                    ),
                )
                base_select_fields.append(subtotal_num_field)
        return base_select_fields

    def _get_subtotal_base_select_fields(self):
        return self._assign_num_to_base_fields([])

    def _get_subtotal_query_vars(self):
        return self._query_vars

    @staticmethod
    def _get_select_fields(select_fields: list):
        fields = []
        for select_field in select_fields:
            fields.append(select_field.ext_data)
        return fields

    def _get_query_agent_convert_params(self, params: dict):
        params["vars"] = self._get_subtotal_query_vars()
        params["dataset_field_dict"] = self._chart_data_model.dataset_field_dict
        params["external_subject_ids"] = self._chart_data_model.external_subject_ids
        return params

    def get_subtotal_for_struct(self):
        """
        获取小计的取数结构
        :return:
        """
        subtotal_summary_struct = {}
        if not self._chart_data_model.enable_subtotal:
            # 未开启小计 直接返回空
            return subtotal_summary_struct, []
        # 获取group字段
        subtotal_cates_struct = []
        subtotal_cate_groups = self._get_subtotal_groups()
        query_agent = QueryAgent()
        base_select_fields = self._get_subtotal_base_select_fields()
        subtotal_summary_select_fields = copy.deepcopy(base_select_fields)
        for idx, group_dims in enumerate(subtotal_cate_groups):
            group_fields, subtotal_cate_fields = [], []
            select_fields = copy.deepcopy(base_select_fields)
            # 获取where字段 (需要将之前的where条件加上)
            where_fields = copy.deepcopy(self._origin_where_fields)
            for group_dim in group_dims:
                group_fields.append(self._dim2group(group_dim))
                select_field = self._dim2select(group_dim)
                col_name = self._get_data_col_name(select_field)
                self._assign_field_extdata(
                    select_field,
                    FieldExtDataModel(
                        **{
                            "dataset_id": group_dim.get("dataset_id"),
                            "dataset_field_id": group_dim.get("dataset_field_id"),
                            "col_name": col_name,
                            "detail_col_name": col_name,
                        }
                    ),
                )
                select_fields.append(select_field)
                subtotal_cate_fields.append(
                    {
                        "dataset_id": group_dim.get("dataset_id"),
                        "dataset_field_id": group_dim.get("dataset_field_id"),
                        "rank": group_dim.get("rank"),
                        "col_name": col_name,
                        "detail_col_name": col_name,
                    }
                )
                group_dim['data_key'] = select_field.alias
                group_dim["data_type"] = self._chart_data_model.dataset_field_dict.get(group_dim.get("dim")).get(
                    "data_type"
                )
            json_struct = query_agent.convert(
                self._get_query_agent_convert_params(
                    {"select": select_fields, "group": group_fields, "where": where_fields}
                )
            )
            query_params = {
                "user_id": self._user_id,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": json_struct,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
            }
            struct = QueryAgent.query_struct(**query_params)
            struct["select_fields"] = self._get_select_fields(select_fields)
            struct["subtotal_cate_fields"] = subtotal_cate_fields
            subtotal_cates_struct.append(struct)

        if self._chart_data_model.enable_summary:
            json_struct = query_agent.convert(
                self._get_query_agent_convert_params(
                    {"select": subtotal_summary_select_fields, "where": self._origin_where_fields}
                )
            )
            query_params = {
                "user_id": self._user_id,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": json_struct,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
            }
            subtotal_summary_struct = QueryAgent.query_struct(**query_params)
            subtotal_summary_struct["select_fields"] = self._get_select_fields(subtotal_summary_select_fields)

        return subtotal_summary_struct, subtotal_cates_struct

    def get_subtotal_for_data(self, data, next_item):
        """
        获取小计数据
       :param data:
       :param next_item:
       :return:
        """
        if not self._chart_data_model.enable_subtotal or not data:
            # 未开启小计 直接返回空
            return [], []
        # 获取group字段
        subtotal_cates, subtotal_summary = [], [{"data": []}]
        subtotal_cate_groups = self._get_subtotal_groups()
        query_agent = QueryAgent()
        base_select_fields = self._get_subtotal_base_select_fields()
        subtotal_summary_select_fields = copy.deepcopy(base_select_fields)
        for idx, group_dims in enumerate(subtotal_cate_groups):
            group_fields, subtotal_cate_fields = [], []
            select_fields = copy.deepcopy(base_select_fields)
            # 获取where字段 (需要将之前的where条件加上)
            where_fields = copy.deepcopy(self._origin_where_fields)
            where_fields_dict = dict()
            current_where_field_values = defaultdict(set)
            contain_keys = []
            for group_dim in group_dims:
                group_fields.append(self._dim2group(group_dim))
                select_field = self._dim2select(group_dim)
                select_fields.append(select_field)
                col_name = self._get_data_col_name(select_field)
                self._assign_field_extdata(
                    select_field,
                    FieldExtDataModel(
                        **{
                            "dataset_id": group_dim.get("dataset_id"),
                            "dataset_field_id": group_dim.get("dataset_field_id"),
                            "col_name": col_name,
                            "detail_col_name": col_name,
                        }
                    ),
                )
                subtotal_cate_fields.append(
                    {
                        "dataset_id": group_dim.get("dataset_id"),
                        "dataset_field_id": group_dim.get("dataset_field_id"),
                        "rank": group_dim.get("rank"),
                        "col_name": select_field.alias,
                        "detail_col_name": col_name,
                    }
                )
                group_dim['data_key'] = select_field.alias
                contain_keys.append(group_dim.get("data_key"))
                current_where_field_values = self._get_where_values(data, group_dim, current_where_field_values)
                group_dim["data_type"] = self._chart_data_model.dataset_field_dict.get(group_dim.get("dim")).get(
                    "data_type"
                )
                self._assign_where_fields_dict(where_fields_dict, group_dim, current_where_field_values)
            for _, where_field in where_fields_dict.items():
                where_fields.append(where_field)
            json_struct = query_agent.convert(
                self._get_query_agent_convert_params(
                    {"select": select_fields, "group": group_fields, "where": where_fields}
                )
            )
            query_params = {
                "user_id": self._user_id,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": json_struct,
            }
            result = QueryAgent.query(**query_params)
            self._assign_has_next(result, next_item, contain_keys)
            result["subtotal_cate_field"] = subtotal_cate_fields
            subtotal_cates.append(result)

        if self._chart_data_model.enable_summary:
            json_struct = query_agent.convert(
                self._get_query_agent_convert_params(
                    {"select": subtotal_summary_select_fields, "where": self._origin_where_fields}
                )
            )
            query_params = {
                "user_id": self._user_id,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": json_struct,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
            }
            subtotal_summary = QueryAgent.query(**query_params)

        return subtotal_summary, subtotal_cates

    def _assign_where_fields_dict(self, where_fields_dict, group_dim, current_where_field_values):
        where_value = (
            list(current_where_field_values.get(group_dim.get("data_key")))
            if current_where_field_values.get(group_dim.get("data_key"))
            else []
        )
        if where_value:
            where_fields_dict[group_dim.get("data_key")] = self._dim2where(
                group_dim, SqlWhereOperator.In.value, where_value
            )

    @staticmethod
    def _get_where_values(data, group_dim, current_where_field_values):
        for item in data:
            if item.get(group_dim.get("data_key")) is not None:
                current_where_field_values[group_dim.get("data_key")].add(item.get(group_dim.get("data_key")))
        return current_where_field_values

    def _assign_has_next(self, result, next_item, contain_keys):
        if result.get("code") == 200 and contain_keys:
            data = result.get("data")
            for each in data:
                each["has_next"] = int(self._compare_dict(each, self._get_first_item(next_item), contain_keys))
            result["data"] = data

    @staticmethod
    def _get_first_item(loopitems):
        return loopitems[0] if loopitems else []

    def _dim2group(self, dim):
        dim_group = DimGroup()
        _, group_field = dim_group.dim2group(dim, self._chart_data_model.dataset_field_dict, dim.get("formula_mode"))
        return group_field

    @staticmethod
    def _generate_count_all_field(alias="total"):
        select_count_all = CountAllSelect()
        return select_count_all.generate_field(alias)

    def _dim2select(self, dim):
        dim_select = DimSelect()
        _, select_field = dim_select.dim2select(dim, self._chart_data_model.dataset_field_dict)
        return select_field

    def _num2select(self, num):
        num_select = NumSelect()
        _, select_field = num_select.num2select(num, self._chart_data_model.dataset_field_dict)
        return select_field

    def _dim2where(self, field, operator, value):
        return Where.field2where(field, operator, value, self._chart_data_model)
