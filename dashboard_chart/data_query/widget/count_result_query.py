#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/24 10:51
# <AUTHOR> caoxl
# @File     : count_result_query.py
# pylint: disable=R0201
import pandas as pd


class CountResultQuery:
    def __init__(self, chart_data_model, user_id):
        self._chart_data_model = chart_data_model
        self._user_id = user_id

    def query_count(self, data_frame: pd.DataFrame) -> int:
        return len(data_frame)
