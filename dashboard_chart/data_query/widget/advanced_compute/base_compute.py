#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/6/17 15:48
# <AUTHOR> caoxl
# @File     : compute.py
# pylint:disable=R0201,E0402
from typing import List, Dict

from base.errors import InvalidCallError
from dashboard_chart.models import ChartDataModel


class BaseCompute:
    def __init__(
        self,
        result_data: List[Dict],
        chart_data_model: ChartDataModel,
        user_id: str,
        select_fields: List,
        where_fields: List,
        query_vars: List,
    ):
        self.result_data = result_data
        self.chart_data_model = chart_data_model
        self.select_fields = select_fields
        self.where_fields = where_fields
        self.query_vars = query_vars
        self.user_id = user_id

    def compute(self) -> List[Dict]:
        raise InvalidCallError(message="请在字类中实现计算方法！")
