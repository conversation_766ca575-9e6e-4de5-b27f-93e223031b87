#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/6/17  15:47
# <AUTHOR> caoxl
# @File     : proportion.py
# pylint:disable=R0201,E0402
import copy
import datetime
import json
import traceback
from typing import List, Dict
from dateutil.relativedelta import relativedelta

from loguru import logger
import pandas as pd

from base.errors import ChartQueryExceptionError
from dashboard_chart.agent.query_agent import QueryAgent
from base.enums import AdvancedComputeMothed, ExternalDatasetType
from dashboard_chart.convertor.select.num_select import NumSelect
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect
from .base_compute import BaseCompute
from dashboard_chart.formatter.same_ring_ratio_formatter import SRRatioFormatter
from base.errors import UserError
from dashboard_chart.convertor.select.select import Select
from dashboard_chart.convertor.group.group import Group
from dashboard_chart.data_query.utils import convert_to_py_data_type


class SameRingRatioCompute(BaseCompute):  # NOSONAR
    # NOSONAR
    def __init__(  # NOSONAR
            self,  # NOSONAR
            result_data: List[Dict],  # NOSONAR
            chart_data_model,  # NOSONAR
            user_id: str,  # NOSONAR
            select_fields: List,  # NOSONAR
            where_fields: List,  # NOSONAR
            group_fields: List,  # NOSONAR
            query_vars: List,  # NOSONAR
    ):  # NOSONAR
        super().__init__(result_data, chart_data_model, user_id, select_fields, where_fields, query_vars)  # NOSONAR
        self.group_fields = group_fields
        self.cached_date_map = {}  # 缓存的日期映射关系
        self.comparison_dims = {}  # 对比维度
        self.error_rule_columns = {}  # 记录同环比保存的异常规则列与是否异常的关系  {num_id -> boolean}

    def pre_check_conditions(self):
        if not self.chart_data_model.aggregation:
            dataset = self.chart_data_model.dataset
            # 如果是数芯指标数据集则self.chart_data_model.aggregatio=1的效果是一样的，都能进行同环比计算
            if not (dataset and ExternalDatasetType.PulsarIndicator.value == dataset.get("external_type")):
                return True
        if not self.chart_data_model.nums:
            return True
        return False

    def compute(self) -> List[Dict]:
        if self.pre_check_conditions():
            return self.result_data

        calc_ratio_nums = [
            num for num in self.chart_data_model.nums
            # 一个数值只能要么算同比，要么算环比
            if num.get("formula_mode") in {AdvancedComputeMothed.SameRatio.value, AdvancedComputeMothed.RingRatio.value}
        ]
        if not calc_ratio_nums:
            return self.result_data

        self._load_same_ring_ratio_config_to_dict(calc_ratio_nums)
        # 数值字段id与FieldObj的对应关系
        # noqa {'39ff3841-88f1-c097-95f3-9417bd366f0e': <dashboard_chart.convertor.field_types.FieldObj object at 0x7f06a3a32a68>}
        ratio_num_field_dict = self._get_advanced_compute_fields_dict(calc_ratio_nums)
        nums_field_dict = self._get_advanced_compute_fields_dict(self.chart_data_model.nums)
        field_id2num_select_key = {
            field_id: val.alias or val.field
            for field_id, val in nums_field_dict.items()
        }
        # 字段与数值字段id的对应关系
        # noqa {'same_ratio_NUM_3889433432': '39ff3841-88f1-c097-95f3-9417bd366f0e'}
        ratio_result_key2field_id = {val.alias or val.field: field_id for field_id, val in ratio_num_field_dict.items()}

        # 维度字段ID与已经同环比计算选择关联的维度对象的对应关系
        # noqa {'39ff3841-88f1-c097-95f3-9417bd366f0e': <dashboard_chart.convertor.field_types.FieldObj object at 0x7f8d3516e318>}
        dim_select_field_select = self._get_dim_fields_dict(self.chart_data_model.dims)
        self._load_same_ring_config_dim_to_dim_select(calc_ratio_nums, dim_select_field_select)
        # 维度id与维度字段名的对应关系
        # noqa {'39ff3838-f51e-ef9e-b3f1-7cc78f4fcfa8': 'TYPE_4089121738',  '39ff3838-f51f-f39c-5ce2-d6407942497e': 'day_DATA_4080470946'}
        field_id2dim_select_key = {
            field_id: val.alias or val.field
            for field_id, val in dim_select_field_select.items()
        }
        # 上面关系的反转
        # noqa {'TYPE_4089121738': '39ff3838-f51e-ef9e-b3f1-7cc78f4fcfa8', 'day_DATA_4080470946': '39ff3838-f51f-f39c-5ce2-d6407942497e'}
        dim_select_key2field_id = {v: k for k, v in field_id2dim_select_key.items()}
        if self.chart_data_model.aggregation and self.chart_data_model.comparisons:
            # 对比维度情况下，需要对比维度的字段信息更新进去， 拼到二次取数的where条件中
            comparison_fields = self._get_comparison_fields_dict_for_pivot()  # type: dict
            self.comparison_dims.update(comparison_fields)
            dim_select_key2field_id.update({
                col_obj.alias or col_obj.field: col_id
                for col_id, col_obj in comparison_fields.items()
            })

        # # 聚合维度字段名和计算规则的map
        # # {'day_DATA_4080470946': 'day_ring_ratio'}
        # dims_alias2calc_rule = {
        #     dim.alias or dim.field: num['same_ring_ratio_config']['calc_rule']
        #     for num in calc_ratio_nums
        #     for dim in dim_select_field_select.values()
        #     if dim.field_ref == num['same_ring_ratio_config'].get('related_dim_id')
        # }

        # 正常情况下的计算和对比维度下的计算基本是一致的，只是部分的维度条件需要处理
        # 这里同环比的计算不再像百分比一样分开计算，统一处理成相同的计算形式
        self._compute_result_data_for_common(
            calc_ratio_nums=calc_ratio_nums,
            ratio_result_key2field_id=ratio_result_key2field_id,
            dim_select_key2field_id=dim_select_key2field_id,
            field_id2dim_select_key=field_id2dim_select_key,
            field_id2num_select_key=field_id2num_select_key
        )

        return self.result_data

    def _load_same_ring_config_dim_to_dim_select(self, calc_ratio_nums, dim_select_field_select):
        for calc_ratio_num in calc_ratio_nums:
            config = calc_ratio_num['same_ring_ratio_config']
            type = config.get('type')
            related_dim_id = config.get('related_dim_id')
            by = config.get('by')
            if type == 2:
                if related_dim_id not in self.chart_data_model.dataset_field_dict:
                    raise UserError(message='请检查关联的时间维度是否存在于数据集中')
                related_dim = self.chart_data_model.dataset_field_dict[related_dim_id]
                # 把第二种情况的时间维度加入已经选择的维度中
                dim_select = DimSelect()
                related_dim['dim'] = related_dim['id']
                related_dim['formula_mode'] = by
                self.chart_data_model.dims.append(related_dim)
                _, dim_field = dim_select.dim2select(related_dim, self.chart_data_model.dataset_field_dict)
                dim_select_field_select[related_dim['dim']] = dim_field

    def is_comparison_dims(self, field_id):  # NOSONAR
        # 来源于对比维度下
        if self.chart_data_model.aggregation and self.chart_data_model.comparisons:
            if field_id in self.comparison_dims.keys():  # NOSONAR
                return True
        return False

    def _get_formula_mode(self, field_name):
        # 是对比维度中的维度
        if self.is_comparison_dims(field_name):
            return None

        # 普通的选中维度
        return self._get_related_chart_dim(field_name)['formula_mode']

    def _get_needed_where_condition(self, where_condition):
        fields_list = [
            {
                'data_type': '度量', 'operator': 'IN',
                'col_value': json.dumps(v, ensure_ascii=False),
                'field_id': k,
                'formula_mode': self._get_formula_mode(k),
                '_created_by': 'same_ring_ratio'
            }
            for condition in where_condition for k, v in condition.items()
        ]

        return self._extend_where_condition(self.where_fields, fields_list)

    def _extend_where_condition(self, where_fields, fields_list):
        where_fields = copy.deepcopy(where_fields)
        custom_where_fields = SRRatioFormatter(self.chart_data_model)
        custom_where_fields.set_condition(fields_list)
        where_fields.extend(custom_where_fields.conditions())
        return where_fields

    def _load_same_ring_ratio_config_to_dict(self, calc_ratio_nums):
        for num in calc_ratio_nums:
            same_ring_ratio_config = num.get('same_ring_ratio_config', None) or '{}'
            same_ring_ratio_config = json.loads(same_ring_ratio_config)

            num['same_ring_ratio_config'] = same_ring_ratio_config
            # https://www.tapd.cn/38229611/prong/stories/view/1138229611001096665
            # 这里现在的设计逻辑很奇怪，错误的数据又能让他保存进来，也就是元数据编辑不做校验，改在取数的时候做校验，所以这里有个元数据的校验
            # 逻辑是如果现在取数校验出现异常，就让返回的同环比计算列为none
            # 所以在这里校验，设置一个计算列与异常的dict
            status = self._unusual_scene_check(num, same_ring_ratio_config)
            if status == -1:
                # 异常记录
                same_ring_ratio_config_c = copy.deepcopy(same_ring_ratio_config)
                last_config = same_ring_ratio_config_c.get('last_config', None) or {}
                # last_config = json.loads(last_config)
                same_ring_ratio_config_c.pop('last_config', None)
                last_config.pop('last_config', None)
                logger.error('same_ring_ratio_config: %r, last_config: %r' % (same_ring_ratio_config_c, last_config))

    def _unusual_scene_check(self, num, same_ring_ratio_config):
        # 校验一些异常场景
        num_id = num['num']
        # last_config = same_ring_ratio_config.get('last_config', None) or {}
        # last_config = json.loads(last_config)
        # last_by = last_config.get('by')
        # last_calc_rule = last_config.get('calc_rule')
        # last_value_type = last_config.get('value_type')
        # last_type = last_config.get('type')
        by = same_ring_ratio_config.get('by')
        # calc_rule = same_ring_ratio_config.get('calc_rule')
        related_dim_id = same_ring_ratio_config.get('related_dim_id')
        # value_type = same_ring_ratio_config.get('value_type')
        _type = same_ring_ratio_config.get('type')
        select_dim_ids = [dim['dim'] for dim in self.chart_data_model.dims or []]

        # 基础异常校验
        try:
            self._base_calc_rule_check(num=num, same_ring_ratio_config=same_ring_ratio_config)
        except UserError as ue:
            self._record_error_num_rule(num_id)
            logger.error('同环比校验num_id: %r, 基础校验：%s' % (num_id, ue.message))
            return -1

        # 异常其他场景校验
        # 此时所有的dim和nums并未转换成对象，场景2的维度也还没有注入到维度中
        # 1）字段变更：
        if _type == 1:
            # 1.1）所选的时间日期字段被删除；
            if related_dim_id not in select_dim_ids:
                self._record_error_num_rule(num_id)
                logger.error('同环比校验num_id: %r, 字段变更：1.1）所选的时间日期字段被删除' % num_id)
                return -1
            # 1.2）所选的时间日期字段时间类型切换；
            if (self._get_related_chart_dim(related_dim_id) or {}).get('formula_mode') != by:
                self._record_error_num_rule(num_id)
                logger.error('同环比校验num_id: %r, 字段变更：1.2）所选的时间日期字段时间类型切换' % num_id)
                return -1
        elif _type == 2:  # NOSONAR
            # 1.3）已设置时间日期同环比后又新增了时间日期字段到组件维度中
            if bool(list(  # NOSONAR
                    filter(lambda x: x.get('origin_field_type') == 'datetime', self.chart_data_model.dims or [{}])
            )):  # NOSONAR
                self._record_error_num_rule(num_id)
                logger.error('同环比校验num_id: %r, 字段变更：1.3）已设置时间日期同环比后又新增了时间日期字段到组件维度中' % num_id)
                return -1

    @staticmethod
    def _base_calc_rule_check(num, same_ring_ratio_config):
        """
        字段高级计算， 基础的字段关系校验
        同环比规则检查和设置
        :return:
        """
        # 到这里same_ring_ratio_config必然不能为空
        if not same_ring_ratio_config:
            raise UserError(message='同环比配置为空')
        if not same_ring_ratio_config.get('related_dim_id'):
            raise UserError(message='同环比配置关联位的时间维度为空')
        formula_mode = num.get('formula_mode')
        if formula_mode not in ['same_ratio', 'ring_ratio']:
            raise UserError(message='数值formula_mode不正确: %s' % formula_mode)

        # 检查参数规则
        _type = same_ring_ratio_config.get("type")
        ratio_by = same_ring_ratio_config.get("by")
        calc_rule = same_ring_ratio_config.get("calc_rule")
        rule_dict = {
            'day': ['day_ring_ratio', 'last_month_same_ratio', 'last_year_same_ratio'],
            'month': ['month_ring_ratio', 'last_year_same_ratio'],
            'week': ['week_ring_ratio', 'last_year_same_ratio'],
            'quarter': ['quarter_ring_ratio', 'last_year_same_ratio'],
            'year': ['year_same_ratio'],
        }
        if ratio_by not in rule_dict:
            raise UserError(message="同环比计算时间周期不存在: %s" % ratio_by)
        if _type not in [1, 2, 3]:
            raise UserError(message="同环比计算类型不正确：%s" % _type)
        ratio_rule_list = rule_dict.get(ratio_by)
        if calc_rule not in ratio_rule_list:
            raise UserError(message="同环比计算规则不存在: %s" % calc_rule)
        value_type = same_ring_ratio_config.get("value_type")
        value_type_list = ['growth_rate', 'growth_value', 'contrast_value']
        if value_type not in value_type_list:
            raise UserError(message="同环比计算选项不存在: %s" % value_type)

    def _record_error_num_rule(self, num_id):
        self.error_rule_columns[num_id] = True

    def _current_num_rule_is_error(self, num_id):
        return self.error_rule_columns.get(num_id) is True

    def _get_related_chart_dim(self, dim_id):
        # 从已有的信息中获取维度信息
        matched_dims = [dim for dim in self.chart_data_model.dims if dim['dim'] == dim_id]
        if not matched_dims:
            raise UserError(message='同环比配置的关联的维度有误')
        return matched_dims[0]

    def _match_calc_rule_data_str(self, related_dim_formula_mode, current_by, calc_rule, value):  # NOSONAR
        if not value:
            return None
        try:
            if related_dim_formula_mode == 'day':
                # %Y-%m-%d
                if calc_rule == 'day_ring_ratio':
                    return (datetime.datetime.strptime(value, '%Y-%m-%d') - relativedelta(days=+1)).strftime('%Y-%m-%d')
                elif calc_rule == 'last_month_same_ratio':
                    return (datetime.datetime.strptime(value, '%Y-%m-%d') - relativedelta(months=+1)). \
                        strftime('%Y-%m-%d')
                elif calc_rule == 'last_year_same_ratio':
                    return (datetime.datetime.strptime(value, '%Y-%m-%d') - relativedelta(years=+1)). \
                        strftime('%Y-%m-%d')

            elif related_dim_formula_mode == 'month':
                # %Y-%m
                if calc_rule == 'month_ring_ratio':
                    return (datetime.datetime.strptime(value, '%Y-%m') - relativedelta(months=+1)).strftime('%Y-%m')
                elif calc_rule == 'last_year_same_ratio':
                    return (datetime.datetime.strptime(value, '%Y-%m') - relativedelta(years=+1)).strftime('%Y-%m')

            elif related_dim_formula_mode == 'week':
                # %Y-%W  目前只支持这种格式
                if calc_rule == 'week_ring_ratio':
                    return self._calc_week_timedelta(value, years=0, weeks=-1)
                elif calc_rule == 'last_year_same_ratio':
                    return self._calc_week_timedelta(value, years=-1, weeks=0)

            elif related_dim_formula_mode == 'quarter':
                # %Y-%Q  目前只支持这种格式
                if calc_rule == 'quarter_ring_ratio':
                    return self._calc_quarter_timedelta(value, years=0, quarters=-1)
                elif calc_rule == 'last_year_same_ratio':
                    return self._calc_quarter_timedelta(value, years=-1, quarters=0)

            elif related_dim_formula_mode == 'year':
                # %Y
                if calc_rule == 'year_same_ratio':  # NOSONAR
                    return (datetime.datetime.strptime(value, '%Y') - relativedelta(years=+1)).strftime('%Y')
        except:
            raise UserError(message='同环比时间维度推算错误：%s' % traceback.format_exc())

        raise UserError(message='错误的时间聚合方式[%s:%s:%s:%s]' % (related_dim_formula_mode, current_by, value, calc_rule))

    def _calc_week_timedelta(self, value, years=-1, weeks=-1):
        year, week = value.split('-')
        year, week = int(year), int(week)
        delta_year = year + years
        delta_week = week + weeks
        if delta_week < 1:
            # 去年的最后一周
            delta_week = (
                    datetime.datetime.strptime(f'{delta_year}-01-01', '%Y-%m-%d') - relativedelta(days=+1)
            ).strftime('%W')
            # 年减一
            delta_year -= 1
        return f'{delta_year}-{delta_week}'

    def _calc_quarter_timedelta(self, value, years=-1, quarters=-1):
        year, quarter = value.split('-')
        year, quarter = int(year), int(quarter)
        delta_year = year + years
        delta_quarter = quarter + quarters
        if delta_quarter < 1:
            # 去年的最后一季
            delta_quarter = 4
            # 年减一
            delta_year -= 1
        return f'{delta_year}-{delta_quarter}'

    @staticmethod
    def _get_current_time_str(by) -> str:
        if by == 'day':
            return datetime.datetime.now().strftime('%Y-%m-%d')
        elif by == 'month':
            return datetime.datetime.now().strftime('%Y-%m')
        elif by == 'year':
            return datetime.datetime.now().strftime('%Y')
        raise UserError(message='错误的同环比计算类型：%s' % by)

    def _get_needed_contrast_result(self, calc_rule: str, col_name: str, calc_type: int, by: str, formula_mode: str):
        # 获取需要的结果
        needed_contrast_result = copy.deepcopy(self.result_data)
        self.cached_date_map[col_name] = {}
        if calc_type == 1:
            # 基于字段筛选，有时间维度
            for row in needed_contrast_result:
                calc_date = self._match_calc_rule_data_str(formula_mode, by, calc_rule, row[col_name])
                self.cached_date_map[col_name][row[col_name]] = calc_date
                row[col_name] = calc_date
        elif calc_type == 2:
            # 基于字段筛选，没有时间维度
            for row in needed_contrast_result:
                now_str = self._get_current_time_str(by=by)
                calc_date = self._match_calc_rule_data_str(formula_mode, by, calc_rule, now_str)
                if col_name not in row:
                    row[col_name] = now_str
                self.cached_date_map[col_name][row[col_name]] = calc_date
                row[col_name] = calc_date
        else:
            raise UserError(message='暂时不支持的对比计算场景！')

        return needed_contrast_result

    def _diff_data(self, curr_result, needed_contrast_result, field_id2num_select_key) -> pd.DataFrame:
        # 对比出需要的查询数据
        num_fields = list(field_id2num_select_key.values())
        # curr_result_pd = pd.DataFrame(curr_result)
        # curr_result_pd = curr_result_pd.drop(num_fields, axis=1)  # 去掉所有的数值列
        #
        # needed_contrast_result_pd = pd.DataFrame(needed_contrast_result)
        # needed_contrast_result_pd = needed_contrast_result_pd.drop(num_fields, axis=1)  # 去掉所有的数值列
        #
        # # 对比差异值
        # set_diff_df = pd.concat([needed_contrast_result_pd, curr_result_pd, curr_result_pd]).drop_duplicates(keep=False)
        # set_diff_df = needed_contrast_result_pd
        #
        # # 差异值值为空
        # if set_diff_df.empty is True:
        #     return needed_contrast_result_pd
        #
        # del curr_result_pd
        # del needed_contrast_result_pd
        #
        # return set_diff_df

        # 上面是原有的对比逻辑 对比出需要的记录行
        # 下面是简化版，不对比，直接取要的记录行

        needed_contrast_result_pd = pd.DataFrame(needed_contrast_result)
        needed_contrast_result_pd = needed_contrast_result_pd.drop(num_fields, axis=1)  # 去掉所有的数值列
        return needed_contrast_result_pd

    # 跟已有的取数结果计算出二次取数需要的行数据，也就是去掉重复的结果
    def _calc_needed_rows(
            self,
            calc_ratio_num: Dict,
            dim_select_key2field_id: Dict,
            field_id2dim_select_key: Dict,
            field_id2num_select_key: Dict,
            related_dim_col_name: str,
    ) -> Dict:

        # 1. 先知道是那个时间维度字段
        same_ring_ratio_config = calc_ratio_num.get('same_ring_ratio_config')
        if not same_ring_ratio_config:
            return {}
        calc_rule = same_ring_ratio_config.get('calc_rule')
        by = same_ring_ratio_config.get('by')
        type = same_ring_ratio_config.get('type')
        related_dim_id = same_ring_ratio_config.get('related_dim_id')
        formula_mode = self._get_formula_mode(dim_select_key2field_id[related_dim_col_name])

        # 2. 根据这个维度字段计算出需要的数据
        needed_contrast_result = self._get_needed_contrast_result(
            calc_rule,
            field_id2dim_select_key[related_dim_id],
            calc_type=type,
            by=by,
            formula_mode=formula_mode
        )

        if not self.result_data:
            # 第一次的结果条件都查不出数据
            return {}

        # 3. 和已有的查询结果作差
        if type == 1:
            diff_data = self._diff_data(self.result_data, needed_contrast_result, field_id2num_select_key)

            # 拼接where条件的map, 键是字段id, 值已经是去重之后的值
            # noqa {'39ff3838-f51e-ef9e-b3f1-7cc78f4fcfa8': ['ta', 'tb'], '39ff3838-f51f-f39c-5ce2-d6407942497e': ['2021-09-19', '2021-09-20', '2021-09-22']}
            if diff_data.empty is True:  # 说明没有差异值
                return {}
            return {
                col_id: list(diff_data[col_name].unique())
                for col_name, col_id in dim_select_key2field_id.items()
                if col_name == related_dim_col_name  # 是否加上其他where条件的开关
            }
        else:
            # 直接返回当前日期和对比日期
            col_id = dim_select_key2field_id[related_dim_col_name]
            # ['2021-01-09', '2021-01-08']
            date_str_list = [[k, v] for k, v in copy.deepcopy(self.cached_date_map[related_dim_col_name]).items()][0]
            return {
                col_id: date_str_list
            }

    @staticmethod
    def _patch_where_fields(where_fields, wc_json):
        # 自己组装的dim转where可能没有值，没有值报错，补上条件值
        for wf in where_fields:
            # 只有来自构造的条件才会进行处理
            from_custom = False
            if isinstance(wf.dn, list):
                from_custom = len([d for d in wf.dn if d.get('_created_by') == 'same_ring_ratio']) > 0
            elif isinstance(wf.dn, dict):
                from_custom = wf.dn.get('_created_by') == 'same_ring_ratio'
            if wf.field_ref in wc_json and from_custom:
                wf.field_value.field = wc_json[wf.field_ref]

    def _compute_result_data_for_common(
            self,
            calc_ratio_nums: List,
            ratio_result_key2field_id: Dict,
            dim_select_key2field_id: Dict,
            field_id2dim_select_key: Dict,
            field_id2num_select_key: Dict,
    ):
        """
        :param ratio_nums:
        :param ratio_result_key2field_id:
        :return:
        """
        field_id2ratio_result_key = {field_id: alias for alias, field_id in ratio_result_key2field_id.items()}
        calc_scene_choices = {
            -1: self._calc_scene_none,  # 虚构的一种场景，-1不代表真实场景，直接把计算的对应列置为none，下面的都是真实场景
            1: self._calc_scene_type1,
            2: self._calc_scene_type2,
        }

        # 多个数值同时进行同环比计算，只能多次进行取数，这种场景使用很少，即使展示的结果也没有啥实际意义
        for calc_ratio_num in calc_ratio_nums:
            calc_field_col_name = field_id2ratio_result_key[calc_ratio_num['num']]
            # 关联维度的计算名字
            same_ring_ratio_config = calc_ratio_num['same_ring_ratio_config']
            related_dim_col_name = field_id2dim_select_key.get(same_ring_ratio_config['related_dim_id'])
            _type = same_ring_ratio_config.get('type')

            if self._current_num_rule_is_error(calc_ratio_num['num']):
                # 参数校验有异常，不需要真正的去查询，查询结果置为[]
                query_result = []
            else:
                # 构造where条件
                wc_json = self._calc_needed_rows(
                    calc_ratio_num, dim_select_key2field_id, field_id2dim_select_key,
                    field_id2num_select_key, related_dim_col_name
                )
                where_fields = self._get_needed_where_condition([wc_json]) if wc_json else self.where_fields
                self._patch_where_fields(where_fields, wc_json)
                # 没有选择时间维度场景需要重新生成select，group
                select_fields = Select.get_select_fields(self.chart_data_model)
                group_fields = Group.get_group_fields(self.chart_data_model) \
                    if self.chart_data_model.aggregation else []
                query_result = self._query_data(
                    select_fields=select_fields,  # 1. 原来查哪几个字段现在就查哪几个 2. 关联未选的维度二次查询要select，group by
                    groupby_fields=group_fields,
                    where_fields=where_fields,
                ) or []

            if not query_result:
                logger.debug('%s二次查询数据为空' % related_dim_col_name)
                _type = -1  # 虚构的一种场景， -1不代表真实场景

            kwargs = {
                'query_result': query_result,
                'related_dim_col_name': related_dim_col_name,
                'calc_field_col_name': calc_field_col_name,
                'same_ring_ratio_config': same_ring_ratio_config,
                'dim_select_key2field_id': dim_select_key2field_id,
            }

            if _type in calc_scene_choices:
                calc_scene_choices[_type](**kwargs)
            else:
                raise UserError(message='暂时不支持的二次取数计算场景')

        del self.cached_date_map

    def _calc_scene_none(
            self, query_result: List, related_dim_col_name: str,
            calc_field_col_name: str, same_ring_ratio_config: Dict, dim_select_key2field_id: Dict
    ):
        # 虚构的一种场景，-1不代表真实场景，直接把计算的对应列置为none
        # 能够正常返回其他列的数据
        for line in self.result_data:
            line[calc_field_col_name] = None

    def _calc_scene_type1(
            self, query_result: List, related_dim_col_name: str,
            calc_field_col_name: str, same_ring_ratio_config: Dict, dim_select_key2field_id: Dict
    ):
        pre_result_pd = pd.DataFrame(self.result_data)
        result_pd = pd.DataFrame(query_result)

        # 排除要计算的取剩下所有的，不取数值
        include_condition = lambda key: key in dim_select_key2field_id

        for line in self.result_data:
            pre_qs = ' and '.join([
                '%s == %r' % (k, v) for k, v in line.items() if include_condition(k)
            ])
            qs = ' and '.join([
                '%s == %r' % (k, self.cached_date_map[related_dim_col_name][v] if k == related_dim_col_name else v)
                for k, v in line.items() if include_condition(k)
            ])
            value = self._calc_value(
                pre_value=pre_result_pd.query(pre_qs)[[calc_field_col_name]],
                value=result_pd.query(qs)[[calc_field_col_name]],
                value_type=same_ring_ratio_config['value_type']
            )
            line[calc_field_col_name] = convert_to_py_data_type(value)

        del result_pd
        del pre_result_pd

    def _calc_scene_type2(
            self, query_result: List, related_dim_col_name: str,
            calc_field_col_name: str, same_ring_ratio_config: Dict, dim_select_key2field_id: Dict
    ):
        # 在同一个查询集合里面进行取值计算
        pre_result_pd = pd.DataFrame(query_result)
        # result_pd = pd.DataFrame(query_result)
        result_pd = pre_result_pd

        # 排除要计算的取剩下所有的，不取数值.
        include_condition = lambda key: key in dim_select_key2field_id
        curr, last = [(k, v) for k, v in copy.deepcopy(self.cached_date_map[related_dim_col_name]).items()][0]

        for line in self.result_data:
            pre_qs = ' and '.join(
                ['%s == %r' % (k, v) for k, v in line.items() if include_condition(k)] + \
                ['%s == %r' % (related_dim_col_name, curr)]
            )
            qs = ' and '.join(
                ['%s == %r' % (k, last if k == related_dim_col_name else v)
                 for k, v in line.items() if include_condition(k)] + \
                ['%s == %r' % (related_dim_col_name, last)]
            )
            value = self._calc_value(
                pre_value=pre_result_pd.query(pre_qs)[[calc_field_col_name]],
                value=result_pd.query(qs)[[calc_field_col_name]],
                value_type=same_ring_ratio_config['value_type']
            )
            line[calc_field_col_name] = convert_to_py_data_type(value)

        del result_pd
        del pre_result_pd

    @staticmethod
    def _calc_value(pre_value: pd.DataFrame, value: pd.DataFrame, value_type: str):
        # 计算最终的同环比值
        if value_type == 'growth_rate':
            # 增长率
            if any([pre_value.empty is True, value.empty is True]):
                return None
            pre_value = pre_value.iloc[0][0]
            value = value.iloc[0][0]
            if value == 0:  # 对比值为0
                return None
            return round((pre_value - value) / value, 4)
        elif value_type == 'growth_value':
            # 增长值
            if any([pre_value.empty is True, value.empty is True]):
                return None
            pre_value = pre_value.iloc[0][0]
            value = value.iloc[0][0]
            return pre_value - value
        elif value_type == 'contrast_value':
            # 对比值
            if value.empty is True:
                return None
            return value.iloc[0][0]
        raise UserError(message='错误的返回值类型：%s' % value_type)

    def _get_comparison_fields_dict_for_pivot(self) -> dict:
        """
        获取透视表基础select字段
        基础字段=对比维度字段
        :return:
        """
        base_select_fields_dict = {}
        comparison_select = ComparisonSelect()
        for comparison in self.chart_data_model.comparisons:
            _, comparison_field = comparison_select.comparison2select(
                comparison, self.chart_data_model.dataset_field_dict
            )
            base_select_fields_dict[comparison.get("dataset_field_id")] = comparison_field
        return base_select_fields_dict

    def _get_advanced_compute_fields_dict(self, advanced_num_fields: List) -> Dict:
        num_select = NumSelect()
        advanced_compute_fields_dict = {}
        for advanced_num_field in advanced_num_fields:
            _, field = num_select.num2select(advanced_num_field, self.chart_data_model.dataset_field_dict)
            advanced_compute_fields_dict[advanced_num_field.get("num")] = field
        return advanced_compute_fields_dict

    def _get_dim_fields_dict(self, dim_fields: List) -> Dict:
        dim_select = DimSelect()
        advanced_compute_fields_dict = {}
        for dim_field in dim_fields:
            _, field = dim_select.dim2select(dim_field, self.chart_data_model.dataset_field_dict)
            advanced_compute_fields_dict[dim_field.get("dim")] = field
        return advanced_compute_fields_dict

    def _query_data(self, select_fields: List, groupby_fields: List = None, where_fields: List = None) -> List[Dict]:
        query_agent = QueryAgent()
        query_params = {
            "select": select_fields,
            "where": where_fields,
            "group": groupby_fields,
            "vars": self.query_vars,
            "dataset_field_dict": self.chart_data_model.dataset_field_dict,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        json_struct = query_agent.convert(query_params)  # noqa
        query_data_params = {
            "user_id": self.user_id,
            "dataset_id": self.chart_data_model.dataset_id,
            "chart_id": self.chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
            "chart_data_model": self.chart_data_model
        }
        result = QueryAgent.query(**query_data_params)
        if result and result.get("code") != 200:
            # raise ChartQueryExceptionError(message=f"单图 {self.chart_data_model.id} 高级计算取数异常: {result.get('msg')}")
            logger.error(f"单图 {self.chart_data_model.id} 高级计算(同环比)取数异常: {result.get('msg')}")
            return []
        logger.debug('同环比计算二次查询结果: %r' % result)
        return result.get("data", [])
