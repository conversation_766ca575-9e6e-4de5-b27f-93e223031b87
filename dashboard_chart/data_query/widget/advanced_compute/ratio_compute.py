#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/6/17  15:47
# <AUTHOR> caoxl
# @File     : proportion.py
# pylint:disable=R0201,E0402
import copy
from typing import List, Dict
from dashboard_chart.convertor.group.comparison_group import ComparisonGroup
from base.errors import ChartQueryExceptionError
from dashboard_chart.agent.query_agent import QueryAgent
from base.enums import AdvancedComputeMothed, ChartNumFormulaMode
from dashboard_chart.convertor.select.num_select import NumSelect
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect
from .base_compute import BaseCompute


class RatioCompute(BaseCompute):
    def compute(self) -> List[Dict]:
        if not self.chart_data_model.aggregation:
            return self.result_data
        if not self.chart_data_model.nums:
            return self.result_data
        ratio_nums = []
        for num in self.chart_data_model.nums:
            if num.get("formula_mode") == AdvancedComputeMothed.Ratio.value:
                ratio_nums.append(num)
        if not ratio_nums:
            return self.result_data
        ratio_num_field_dict = self._get_advanced_compute_fields_dict(ratio_nums)
        ratio_result_key2field_id = {val.alias or val.field: field_id for field_id, val in ratio_num_field_dict.items()}
        if self.chart_data_model.aggregation and self.chart_data_model.comparisons:
            # 透视表情况下计算方法不一致
            self._compute_result_data_for_pivot(ratio_nums, ratio_result_key2field_id)
        else:
            # 无透视表情况下计算方法
            self._compute_result_data_for_normal(ratio_nums, ratio_result_key2field_id)
        return self.result_data

    def _compute_result_data_for_normal(self, ratio_nums: List, ratio_result_key2field_id: Dict):
        """
        计算无透视表情况下的结果
        :param ratio_nums:
        :param ratio_result_key2field_id:
        :return:
        """
        summay_fields_dict = self._get_summay_fields_dict_for_normal(ratio_nums)
        summary_result_to_num_dict = {
            field_id: field.alias or field.field for field_id, field in summay_fields_dict.items()
        }
        summary_result = self._query_data(select_fields=list(summay_fields_dict.values()))
        summary_result_dict = summary_result[0] if len(summary_result) > 0 else {}
        for line in self.result_data:
            for line_key, line_item in line.items():
                if line_key not in ratio_result_key2field_id:
                    continue
                if not line_item:
                    continue
                field_id = ratio_result_key2field_id[line_key]
                summary_result_key = summary_result_to_num_dict.get(field_id)
                summary_val = summary_result_dict.get(summary_result_key)
                line[line_key] = line_item / summary_val if summary_val else None

    def _compute_result_data_for_pivot(self, ratio_nums: List, ratio_result_key2field_id: Dict):
        """
        计算有透视表情况下的结果
        :param ratio_nums:
        :param ratio_result_key2field_id:
        :return:
        """
        base_select_fields = self._get_base_select_fields_dict_for_pivot()
        position_keys = self._get_position_keys_for_pivot(list(base_select_fields.values()))
        summary_advanced_compute_fields_dict = self._get_summary_advanced_compute_fields_dict_for_pivot(ratio_nums)
        summary_result_key2field_id = {
            field.alias or field.field: field_id for field_id, field in summary_advanced_compute_fields_dict.items()
        }
        select_fields_dict = self._get_summay_fields_dict_for_pivot(
            base_select_fields, summary_advanced_compute_fields_dict
        )
        group_fields_dict = self._get_group_fields_dict_for_pivot()
        summary_result = self._query_data(
            select_fields=list(select_fields_dict.values()), groupby_fields=list(group_fields_dict.values())
        )
        summary_result_dict = self._get_summary_result_dict_for_pivot(
            summary_result, summary_result_key2field_id, position_keys
        )
        for line in self.result_data:
            pivot_key = self._get_pivot_key(line, position_keys)
            summary_line_result = summary_result_dict.get(pivot_key, {})
            for line_key, line_item in line.items():
                if line_key not in ratio_result_key2field_id:
                    continue
                if not line_item:
                    continue
                field_id = ratio_result_key2field_id[line_key]
                summary_val = summary_line_result.get(field_id)
                line[line_key] = line_item / summary_val if summary_val else None

    def _get_summary_result_dict_for_pivot(
        self, summary_result: List, summary_result_key2field_id: Dict, position_keys: List
    ) -> Dict:
        summary_result_dict = {}
        for line in summary_result:
            pivot_key = self._get_pivot_key(line, position_keys)
            summary_result_dict[pivot_key] = {}
            for line_key, line_item in line.items():
                if line_key not in summary_result_key2field_id:
                    continue
                field_id = summary_result_key2field_id[line_key]
                summary_result_dict[pivot_key][field_id] = line_item
        return summary_result_dict

    def _get_pivot_key(self, data_item: Dict, position_keys: List) -> tuple:
        result = []
        for position_key in position_keys:
            result.append(data_item.get(position_key))
        return tuple(result)

    def _get_position_keys_for_pivot(self, base_select_fields: List) -> list:
        return [field.alias or field.field for field in base_select_fields]

    def _get_base_select_fields_dict_for_pivot(self) -> dict:
        """
        获取透视表基础select字段
        基础字段=对比维度字段
        :return:
        """
        base_select_fields_dict = {}
        comparison_select = ComparisonSelect()
        for comparison in self.chart_data_model.comparisons:
            _, comparison_field = comparison_select.comparison2select(
                comparison, self.chart_data_model.dataset_field_dict
            )
            base_select_fields_dict[comparison.get("dataset_field_id")] = comparison_field
        return base_select_fields_dict

    def _get_group_fields_dict_for_pivot(self) -> dict:
        """
        获取透视表group by 字段
        group by 字段=维度字段 + 对比维度字段
        :return:
        """
        group_fields_dict = {}
        comparison_group = ComparisonGroup()
        for comparison in self.chart_data_model.comparisons:
            _, group_field = comparison_group.comparison2group(
                comparison, self.chart_data_model.dataset_field_dict, comparison.get("formula_mode")
            )
            group_fields_dict[comparison.get("dataset_field_id")] = group_field
        return group_fields_dict

    def _get_summay_fields_dict_for_pivot(
        self, base_select_fields: Dict, summary_advanced_compute_fields: Dict
    ) -> Dict:
        """
        获取透视表汇总查询字段
        汇总查询字段=维度字段+对比维度字段+高级计算字段
        :param advanced_compute_nums:
        :return:
        """
        select_fields_dict = {}
        select_fields_dict.update(base_select_fields)
        select_fields_dict.update(summary_advanced_compute_fields)
        return select_fields_dict

    def _get_summary_advanced_compute_fields_dict_for_pivot(self, advanced_compute_nums: List[Dict]) -> Dict:
        nums = copy.deepcopy(advanced_compute_nums)
        num_select = NumSelect()
        num_fields_dict = {}
        for num in nums:
            num["formula_mode"] = ChartNumFormulaMode.Sum.value
            _, field = num_select.num2select(num, self.chart_data_model.dataset_field_dict)
            num_fields_dict[num.get("num")] = field
        return num_fields_dict

    def _get_advanced_compute_fields_dict(self, advanced_num_fields: List) -> Dict:
        num_select = NumSelect()
        advanced_compute_fields_dict = {}
        for advanced_num_field in advanced_num_fields:
            _, field = num_select.num2select(advanced_num_field, self.chart_data_model.dataset_field_dict)
            advanced_compute_fields_dict[advanced_num_field.get("num")] = field
        return advanced_compute_fields_dict

    def _get_summay_fields_dict_for_normal(self, advanced_compute_nums: List[Dict]) -> Dict:
        nums = copy.deepcopy(advanced_compute_nums)
        select_fields_dict = {}
        num_select = NumSelect()
        for num in nums:
            num["formula_mode"] = ChartNumFormulaMode.Sum.value
            _, select_field = num_select.num2select(num, self.chart_data_model.dataset_field_dict)
            select_fields_dict[num.get("num")] = select_field
        return select_fields_dict

    def _query_data(self, select_fields: List, groupby_fields: List = None) -> List[Dict]:
        query_agent = QueryAgent()
        query_params = {
            "select": select_fields,
            "where": self.where_fields,
            "vars": self.query_vars,
            "dataset_field_dict": self.chart_data_model.dataset_field_dict,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        if groupby_fields is not None:
            query_params["group"] = groupby_fields
        json_struct = query_agent.convert(query_params)
        query_data_params = {
            "user_id": self.user_id,
            "dataset_id": self.chart_data_model.dataset_id,
            "chart_id": self.chart_data_model.id,
            "json_struct": json_struct,
            "external_subject_ids": self.chart_data_model.external_subject_ids,
        }
        result = QueryAgent.query(**query_data_params)
        if result and result.get("code") != 200:
            raise ChartQueryExceptionError(message=f"单图 {self.chart_data_model.id} 高级计算取数异常: {result.get('msg')}")
        from loguru import logger
        logger.debug('百分比计算二次查询结果: %r' % result)
        return result.get("data", [])
