#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/9  15:03
# <AUTHOR> caoxl
# @File     : count_query.py
import json

from loguru import logger
from base.dmp_constant import EXTERNAL_CUBE_SUBJECT_COUNT_ALIAS
from base.errors import ChartQueryExceptionError
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.query_var.query_var import QueryVar
from dashboard_chart.convertor.select.select import Select
from base.enums import DatasetType
from data_source.services import data_source_service


class CountQuery:
    subquery_name = 'count_table'

    def __init__(self, chart_data_model, user_id):
        self._chart_data_model = chart_data_model
        self._user_id = user_id

    def _loads(self, content):
        try:
            return json.loads(content)
        except Exception as e:
            logger.error(f'CountQuery loads error: {str(e)}')

    def is_generate_subquery_mode(self):
        # 目前只处理数芯的api数据集，后续可能会拓展更多
        dataset = self._chart_data_model.dataset
        content = self._loads(dataset.get('content') or '{}')
        data_source_id = content.get('data_source_id') or ''
        if not data_source_id:
            return False
        data_source_model = data_source_service.get_data_source(data_source_id)
        if not data_source_model:
            return False
        if (
                data_source_model.type == DatasetType.Api.value
                and 'dmp_api/serve' in data_source_model.conn_str.host
        ):
            return True
        else:
            return False

    def generate_sub_query_query(self, where_fields, group_fields):
        # 生成子查询表SQL
        select_fields = [Select.get_asterisk_field()]
        origin_count_data = {
            "select": select_fields,
            "where": where_fields,
            "group": group_fields,
            "vars": QueryVar.get_dataset_vars(self._chart_data_model),
            "dataset_field_dict": self._chart_data_model.dataset_field_dict,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
        }
        query_agent = QueryAgent()
        count_json_struct = query_agent.convert(origin_count_data)
        query_params = {
            "user_id": self._user_id,
            "dataset_id": self._chart_data_model.dataset_id,
            "chart_id": self._chart_data_model.id,
            "query_structure_json": count_json_struct,
            "external_subject_ids": self._chart_data_model.external_subject_ids,
            "chart_data_model": self._chart_data_model
        }
        origin_sub_sql = QueryAgent.query_sql(**query_params)
        # 数芯api的处理逻辑是会把左边和右边的第一个括号去掉
        # 会把右边的冒号、换行也去掉
        # 以前name里面会传(select * from table;)这种
        return f'({origin_sub_sql})'

    def query_count(self, where_fields, group_fields):
        from base.enums import DatasetType

        if self._chart_data_model.external_subject_ids:
            select_fields = [
                *Select.get_flag_select_field(self._chart_data_model, alias=EXTERNAL_CUBE_SUBJECT_COUNT_ALIAS),
                *Select.get_select_fields(self._chart_data_model)
            ]
        elif self._chart_data_model.dataset.get("type") == "INDICATOR":
            select_fields = [
                *Select.get_select_fields(self._chart_data_model)
            ]
        else:
            select_fields = Select.get_count_all_select_fields(self._chart_data_model)

        is_subquery_mode = self.is_generate_subquery_mode()
        if is_subquery_mode:
            # 使用构造子查询的方式查询总数
            # 1. 生成子查询
            subquery_sql = self.generate_sub_query_query(where_fields, group_fields)
            # 2. 得到现在的struct
            count_data = {
                "select": select_fields,
                "vars": QueryVar.get_dataset_vars(self._chart_data_model),
                "dataset_field_dict": self._chart_data_model.dataset_field_dict,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
            }
            if self._chart_data_model.dataset.get("type") == "INDICATOR":
                self._chart_data_model.mode = "count"
            query_agent = QueryAgent()
            count_json_struct = query_agent.convert(count_data)
            # 3. 使用子查询进行查询
            query_params = {
                "user_id": self._user_id,
                "table_name": subquery_sql,
                "table_alias": self.subquery_name,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": count_json_struct,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
                "chart_data_model": self._chart_data_model
            }
            count = QueryAgent.query(**query_params)
            if count.get("code") != 200:
                raise ChartQueryExceptionError(message=f"单图取数异常: {count.get('msg')}")
            if not isinstance(count.get("data"), list) or not count.get("data"):
                return 0
            return int(count.get("data")[0].get("total"))

        else:
            # 这个分支的代码如果在子查询语法全部数据源适配后会被替代
            count_data = {
                "select": select_fields,
                "where": where_fields,
                "group": group_fields,
                "vars": QueryVar.get_dataset_vars(self._chart_data_model),
                "dataset_field_dict": self._chart_data_model.dataset_field_dict,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
            }
            if self._chart_data_model.dataset.get("type") == "INDICATOR":
                self._chart_data_model.mode = "count"
            query_agent = QueryAgent()
            count_json_struct = query_agent.convert(count_data)

            query_params = {
                "user_id": self._user_id,
                "dataset_id": self._chart_data_model.dataset_id,
                "chart_id": self._chart_data_model.id,
                "json_struct": count_json_struct,
                "external_subject_ids": self._chart_data_model.external_subject_ids,
                "chart_data_model": self._chart_data_model
            }
            count = QueryAgent.query(**query_params)
            if count.get("code") != 200:
                raise ChartQueryExceptionError(message=f"单图取数异常: {count.get('msg')}")
            # 有group by 的情况返回结果个数
            # 无 group by 的情况返回count(*)的结果
            # 多主体合并查询情况下会更改查询SQL，此时需要判断结构中是否有group by 若无group by 则只会返回一条记录，去其中total即可，否则按普通聚合取
            if self._chart_data_model.external_subject_ids:
                return int(count.get("data")[0].get("total"))
            if self._chart_data_model.aggregation and self._chart_data_model.dataset.get("type") != DatasetType.Indicator.value:
                return len(count.get("data"))
            if not isinstance(count.get("data"), list) or not count.get("data"):
                return 0
            return int(count.get("data")[0].get("total"))
