#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/9 10:49
# <AUTHOR> caoxl
# @File     : markline_query.py
import numpy
from dmplib.utils.errors import UserError
from base.enums import ChartMarklineMode, DatasetFieldType, ChartMarklineFormulaMode


class MarklineQuery:
    def __init__(self, chart_data_model):
        self._chart_data_model = chart_data_model

    def get_marklines_for_data(self, result_data):
        """
        获取辅助线数据
        :return:
        """
        if self._chart_data_model.marklines:
            return self.compute_marklines_value(result_data)
        return []

    def compute_marklines_value(self, result_data):
        """
        计算辅助线的值
        :return:
        """
        for markline in self._chart_data_model.marklines:
            if markline.get(
                "mode"
            ) == ChartMarklineMode.CalculatorValue.value and self._chart_data_model.dataset_field_dict.get(
                markline.get("markline")
            ):
                dataset_field = self._chart_data_model.dataset_field_dict.get(markline.get("markline"))
                col_name = dataset_field.get("alias") if dataset_field.get("alias") else dataset_field.get("col_name")
                formula_mode = dataset_field.get("formula_mode")
                is_calculate = dataset_field.get("type") in [
                    DatasetFieldType.Calculate.value,
                    DatasetFieldType.CalculateIndicator.value,
                ]
                markline_chart_data = self._classify_markline_value(
                    col_name, result_data, formula_mode, is_senior_field=bool(is_calculate)
                )
                markline["value"] = (
                    self._get_markline_value(
                        markline.get("formula_mode"), markline_chart_data, markline.get('percentile')
                    )
                    if markline_chart_data and len(markline_chart_data) > 0
                    else ""
                )
        return self._chart_data_model.marklines

    def _get_markline_value(self, markline_formula_mode, markline_chart_data, percentile):
        func_map = {
            ChartMarklineFormulaMode.Avg.value: self.avg,
            ChartMarklineFormulaMode.Min.value: self.min,
            ChartMarklineFormulaMode.Max.value: self.max,
            ChartMarklineFormulaMode.Percentile.value: self.percentile,
        }
        fn = func_map.get(markline_formula_mode)
        if not fn:
            raise UserError(message="不支持的辅助线计算方法：" + markline_formula_mode)
        return fn(markline_chart_data, percentile)

    @staticmethod
    def _classify_markline_value(col_name, chart_result_data, formula_mode, is_senior_field=False):
        markline_data = []
        for data in chart_result_data:
            if is_senior_field:
                if data.get(col_name):
                    markline_data.append(float(data.get(col_name)))
            else:
                field_name = formula_mode + "_" + col_name if formula_mode else col_name
                if data.get(field_name) is not None:
                    markline_data.append(float(data.get(field_name)))
        return markline_data

    @staticmethod
    def avg(markline_chart_data, percentile):
        return float(sum(markline_chart_data)) / len(markline_chart_data)

    @staticmethod
    def max(markline_chart_data, percentile):
        return max(markline_chart_data)

    @staticmethod
    def min(markline_chart_data, percentile):
        return min(markline_chart_data)

    @staticmethod
    def percentile(markline_chart_data, percentile):
        return float(numpy.percentile(markline_chart_data, percentile))
