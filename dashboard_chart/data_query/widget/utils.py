#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/10 14:51
# <AUTHOR> caoxl
# @File     : utils.py
# @File     : column_chart.py
# pylint: disable=C0327
from components import functions
from dashboard_chart.convertor.field_types import FieldObj
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.select.num_select import NumSelect
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect
from dashboard_chart.convertor.select.desire_select import DesireSelect
from dashboard_chart.data_query import utils as DataQueryUtil


def dim_to_field(
    dim_select_obj: DimSelect, field_id: str, dataset_field_dict: dict, formula_mode: str = ''
) -> FieldObj:
    dataset_field, field_obj = dim_select_obj.field2base(field_id, dataset_field_dict, formula_mode)
    field_obj.alias = dim_select_obj.get_dataset_field_alias(
        formula_mode, dataset_field.get("col_name"), dataset_field.get("alias")
    )
    if formula_mode:
        expression = field_obj.expressions if field_obj.expressions else None
        field_obj.expressions = None
        field_obj.get_field(["date_format"], [{"field": functions.get_time_formula_mode(formula_mode)}], expression)
    return field_obj


def num_to_field(
    num_select_obj: NumSelect, field_id: str, dataset_field_dict: dict, formula_mode: str = ''
) -> FieldObj:
    dataset_field, field_obj = num_select_obj.field2base(field_id, dataset_field_dict, formula_mode)
    field_obj.alias = num_select_obj.get_dataset_field_alias(
        formula_mode, dataset_field.get("col_name"), dataset_field.get("alias")
    )
    return field_obj


def desire_to_field(
    desire_select_obj: DesireSelect, field_id: str, dataset_field_dict: dict, formula_mode: str = ''
) -> FieldObj:
    dataset_field, field_obj = desire_select_obj.field2base(field_id, dataset_field_dict, formula_mode)
    field_obj.alias = "desire_" + desire_select_obj.get_dataset_field_alias(
        formula_mode, dataset_field.get("col_name"), dataset_field.get("alias")
    )
    return field_obj


def comparison_to_field(
    comparison_select_obj: ComparisonSelect, field_id: str, dataset_field_dict: dict, formula_mode: str = ''
) -> FieldObj:
    dataset_field, field_obj = comparison_select_obj.field2base(field_id, dataset_field_dict, formula_mode)
    field_obj.alias = comparison_select_obj.get_dataset_field_alias(
        formula_mode, dataset_field.get("col_name"), dataset_field.get("alias")
    )
    if formula_mode:
        expression = field_obj.expressions if field_obj.expressions else None
        field_obj.expressions = None
        field_obj.get_field(["date_format"], [{"field": functions.get_time_formula_mode(formula_mode)}], expression)
    return field_obj


def convert_to_py_data_type(col_value):
    """
    将值转换为数值
    由于pandas中值为对象 前端无法解析 因此需要转换为简单数据类型
    由于dmp中数值和日期/字符串等可能同时存在 因此无法转换时返回python中的简单类型
    :param col_value:
    :return:
    """
    return DataQueryUtil.convert_to_py_data_type(col_value)
