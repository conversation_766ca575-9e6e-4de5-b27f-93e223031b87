#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/4/17 17:35
# <AUTHOR> caoxl
# @File     : indirect_query.py
# pylint: disable=R0201
from dashboard_chart.models import ChartDataModel
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.where.where import Where
from datetime import datetime
from datetime import date

from dmplib.utils.errors import UserError


class IndirectQuery:
    """
    间接访问的查询 (即在一个单图中查询不相关数据集的数据)
    """

    def __init__(self, model: ChartDataModel, user_id, query_vars: list):
        self._chart_model = model
        self._vars = query_vars
        self._user_id = user_id

    def format_model(self):
        if not self._chart_model.indirect_query_map:
            return
        for chart_id, indirect_data in self._chart_model.indirect_query_map.items():
            data = {
                "select": self._get_select_fields(indirect_data["fields"], indirect_data["dataset_field_dict"]),
                "where": self._get_where_fields(
                    indirect_data["conditions"], indirect_data["dataset_field_dict"], indirect_data["dataset"]
                ),
                "vars": self._vars,
                "dataset_field_dict": indirect_data["dataset_field_dict"],
                "external_subject_ids": self._chart_model.external_subject_ids,
            }
            query_agent = QueryAgent()
            json_struct = query_agent.convert(data)
            query_params = {
                "user_id": self._user_id,
                "dataset_id": indirect_data.get("dataset_id"),
                "chart_id": self._chart_model.id,
                "json_struct": json_struct,
                "is_order_master_id": False,
                "external_subject_ids": self._chart_model.external_subject_ids,
            }
            result = QueryAgent.query(**query_params)
            if result["code"] != 200:
                raise UserError(message=f"单图 {chart_id} 间接关联条件无法获取到数据，错误信息 [ {result['msg']} ] ")
            self._chart_model.indirect_query_map[chart_id]["result"] = {}
            self._assign_result(chart_id, result)

    def _assign_result(self, chart_id, result):
        """
        为结果赋值
        :param chart_id:
        :param result:
        :return:
        """
        for row in result["data"]:
            for key, val in row.items():
                if key not in self._chart_model.indirect_query_map[chart_id]["result"]:
                    self._chart_model.indirect_query_map[chart_id]["result"][key] = {"data": [], "has_null": 0}
                if val is None:
                    self._chart_model.indirect_query_map[chart_id]["result"][key]["has_null"] = 1
                val = self._format_db_data(val)
                if val not in self._chart_model.indirect_query_map[chart_id]["result"][key]["data"]:
                    self._chart_model.indirect_query_map[chart_id]["result"][key]["data"].append(val)

    def _format_db_data(self, data_value):
        """
        格式化数据库数据
        数据库返回的数据日期类型 json.dumps 会转换为None
        此处转换为字符串
        :param data_value:
        :return:
        """
        if isinstance(data_value, datetime):
            return data_value.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(data_value, date):
            return data_value.strftime('%Y-%m-%d')
        return data_value

    def _get_select_fields(self, fields: list, dataset_field_dict: dict):
        data = []
        for field_info in fields:
            data.append(self._field2select(field_info, dataset_field_dict))
        return data

    def _get_where_fields(self, conditions: list, dataset_field_dict: dict, dataset: dict):
        where_fields = []
        # 由于生成where_field需要ChartDataModel, 因此此处模拟一个model, 实际该方法只使用了model.dataset_field_dict
        mock_chart_data_model = ChartDataModel(**{"dataset_field_dict": dataset_field_dict, "dataset": dataset})
        for condition in conditions:
            where_fields.append(
                self._field2where(condition, condition["operator"], condition["col_value"], mock_chart_data_model)
            )
        return where_fields

    def _field2select(self, field_info: dict, dataset_field_dict: dict):
        dim_select = DimSelect()
        field_info["dim"] = field_info["id"]
        _, select_field = dim_select.dim2select(field_info, dataset_field_dict)
        return select_field

    def _field2where(self, field_info, operator, value, chart_data_model: ChartDataModel):
        return Where.field2where(field_info, operator, value, chart_data_model)
