#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/9 14:43
# <AUTHOR> caoxl
# @File     : utils.py
import base64
import json
import hashlib
import functools
import pickle
from typing import Union

import pandas as pd
import numpy as np
from loguru import logger
from pandas.util import hash_pandas_object
from dmplib.redis import conn as conn_redis

from dmplib.hug import g
from base.models import BaseModelEncoder
from base.dmp_constant import CHART_QUERY_DEFAULT_LIMIT, __ANALYSIS_DOCTOR_KEYWORD__
from base.enums import DashboardDataMsgCode, ColTypes
from dashboard_chart.models import ChartDataModel


def get_msg_code(data, over_limit_flag=False):
    """
    根据数据内容获取信息code
    :param data:
    :param over_limit_flag:
    :return:
    """
    if data:
        if over_limit_flag:
            return DashboardDataMsgCode.OverDataLimit.value
        if data.get("code") == 200 and not data.get("data"):
            return DashboardDataMsgCode.NullDashboardData.value
        elif data.get("code") == 200:
            return DashboardDataMsgCode.Successful.value
        elif data.get("code") == 4003:
            return DashboardDataMsgCode.NoPermissionData.value
        elif data.get('code') == 510:
            return DashboardDataMsgCode.OtherException.value
        elif data.get("code") in [503, 504, 505, 506]:
            return DashboardDataMsgCode.QueryException.value
        else:
            return DashboardDataMsgCode.ErrorDatasetData.value
    return DashboardDataMsgCode.NullDashboardData.value


def get_data_msg(data):
    """
    数据超过默认1500条记录时做信息提示
    :param dict data: 查询结果数据
    :return:
    """
    if data.get("msg"):
        return data.get("msg")
    if data.get("data") and len(data.get("data")) == CHART_QUERY_DEFAULT_LIMIT:
        return "数据过长，为你挑选前{size}个数据显示".format(size=CHART_QUERY_DEFAULT_LIMIT)
    return ""


def get_data_last_update_time(data_version):
    """
    获取数据最后更新时间
    :param data_version:
    :return: str
    """
    if not data_version:
        return ""
    separator_index = data_version.find("_")
    if separator_index < 0:
        return ""
    return data_version[0:separator_index]


def get_order_fields_with_cate(order_fields: list):
    """
    获取排序字段的分类
    :param order_fields:
    :return:
    """
    cates = {ColTypes.Dim.value: {}, ColTypes.Comparison.value: {}}
    for order_field in order_fields:
        if order_field.logic_source == ColTypes.Dim.value:
            cates[ColTypes.Dim.value][order_field.field_ref] = order_field
        if order_field.logic_source == ColTypes.Comparison.value:
            cates[ColTypes.Comparison.value][order_field.field_ref] = order_field
    return cates


class ChartDisplayAlias:
    def __init__(self, model: ChartDataModel):
        self.chart_data_model = model

    def get_alias(self, field_id: str, logic_source: str) -> Union[str, bool]:
        """
        获取别名
        :param field_id:
        :param logic_source:
        :return:
        """
        dim2alias = self._get_dim2alias()
        comparison2alias = self._get_comparison2alias()
        num2alias = self._get_num2alias()
        desire2alias = self._get_desire2alias()
        if logic_source == ColTypes.Dim.value:
            return dim2alias.get(field_id)
        if logic_source == ColTypes.Comparison.value:
            return comparison2alias.get(field_id)
        if logic_source == ColTypes.Num.value:
            return num2alias.get(field_id)
        if logic_source == ColTypes.Desire.value:
            return desire2alias.get(field_id)
        return False

    def get_original_dim_dict(self):
        if not hasattr(self, '_original_dim_dict'):
            if not self.chart_data_model.original_dims:
                self._original_dim_dict = {}
            else:
                self._original_dim_dict = {v.get('dim'): v for v in self.chart_data_model.original_dims}
        return self._original_dim_dict

    def _get_dim2alias(self):
        if not hasattr(self, '_dim2alias'):
            if not self.chart_data_model.dims:
                self._dim2alias = {}
            else:
                self._dim2alias = {}
                _original_dim_dict = self.get_original_dim_dict()
                for v in self.chart_data_model.dims:
                    _current_dim = _original_dim_dict.get(v.get('dim'), {})
                    self._dim2alias[v.get('dim')] = _current_dim.get('alias') or v.get('alias')
        return self._dim2alias

    def get_original_num_dict(self):
        if not hasattr(self, '_original_num_dict'):
            if not self.chart_data_model.original_nums:
                self._original_num_dict = {}
            else:
                self._original_num_dict = {v.get('num'): v for v in self.chart_data_model.original_nums}
        return self._original_num_dict

    def _get_num2alias(self):
        if not hasattr(self, '_num2alias'):
            if not self.chart_data_model.nums:
                self._num2alias = {}
            else:
                self._num2alias = {}
                _original_num_dict = self.get_original_num_dict()
                for v in self.chart_data_model.nums:
                    _current_num = _original_num_dict.get(v.get('num'), {})
                    self._num2alias[v.get('num')] = _current_num.get('alias') or v.get('alias')
        return self._num2alias

    def get_original_comparison_dict(self):
        if not hasattr(self, '_original_comparison_dict'):
            if not self.chart_data_model.original_comparisons:
                self._original_comparison_dict = {}
            else:
                self._original_comparison_dict = {
                    v.get('dataset_field_id'): v for v in self.chart_data_model.original_comparisons
                }
        return self._original_comparison_dict

    def _get_comparison2alias(self):
        if not hasattr(self, '_comparison2alias'):
            if not self.chart_data_model.comparisons:
                self._comparison2alias = {}
            else:
                self._comparison2alias = {}
                _original_comparison_dict = self.get_original_comparison_dict()
                for v in self.chart_data_model.comparisons:
                    _current_comparison = _original_comparison_dict.get(v.get('dataset_field_id'), {})
                    self._comparison2alias[v.get('dataset_field_id')] = _current_comparison.get('alias') or v.get(
                        'alias'
                    )
        return self._comparison2alias

    def get_original_desire_dict(self):
        if not hasattr(self, '_original_desire_dict'):
            if not self.chart_data_model.original_desires:
                self._original_desire_dict = {}
            else:
                self._original_desire_dict = {
                    v.get('dataset_field_id'): v for v in self.chart_data_model.original_desires
                }
        return self._original_desire_dict

    def _get_desire2alias(self):
        if not hasattr(self, '_desire2alias'):
            if not self.chart_data_model.desires:
                self._desire2alias = {}
            else:
                self._desire2alias = {}
                _original_desire_dict = self.get_original_desire_dict()
                for v in self.chart_data_model.desires:
                    _current_desire = _original_desire_dict.get(v.get('dataset_field_id'), {})
                    self._desire2alias[v.get('dataset_field_id')] = _current_desire.get('alias') or v.get('alias')
        return self._desire2alias


def _convert_to_py_data_type(col_value):
    """
    将值转换为数值
    由于pandas中值为对象 前端无法解析 因此需要转换为简单数据类型
    由于dmp中数值和日期/字符串等可能同时存在 因此无法转换时返回python中的简单类型
    :param col_value:
    :return:
    """
    # 字符串不进行转换
    if isinstance(col_value, (str)):
        return col_value
    result = pd.to_numeric(col_value, errors='ignore')
    # NaN NaT 返回None
    if pd.isna(result):
        return None
    # int64 转换为int
    if isinstance(result, np.int64):
        return int(result)
    # float64 转换为float
    if isinstance(result, np.float64):
        return float(result)
    # datetime64 转字符串
    if isinstance(result, np.datetime64):
        return str(pd.to_datetime(result))
    # timestamp 转换为字符串
    if isinstance(result, pd.Timestamp):
        return str(pd.to_datetime(result))
    return result


@functools.lru_cache(maxsize=1500)
def _convert_to_py_data_type_with_lru(col_value):
    return _convert_to_py_data_type(col_value=col_value)


def convert_to_py_data_type(col_value):
    # 2023.1.9 该函数作为基础函数，被广泛在各处循环调用，这里将用换成优先从缓存直接读取
    # pandas的类型的可能无法支持直接调用lru，先尝试
    try:
        return _convert_to_py_data_type_with_lru(col_value=col_value)
    except Exception as e:
        logger.error(f'convert_to_py_data_type error: {str(e)}')
        return _convert_to_py_data_type(col_value=col_value)


def _calc_cache_args_key(args):
    if not args:
        return ""
    parts = []
    for arg in args:
        if isinstance(arg, pd.DataFrame):
            part = str(hash_pandas_object(arg).sum())
        elif isinstance(arg, dict):
            part = json.dumps(arg, sort_keys=True, ensure_ascii=False, cls=BaseModelEncoder)
        elif isinstance(arg, list):
            part = _calc_cache_args_key(arg)
        else:
            part = str(arg)
        parts.append(part)
    return hashlib.md5(';'.join(parts).encode("utf-8")).hexdigest()


# 取数函数支持cache
def chart_query_cache(timeout, is_class_method=True):
    def cache_wrapper(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # params = getattr(g, 'request_data', {}).get('params', {})
            # if __ANALYSIS_DOCTOR_KEYWORD__ not in params:
            #     return func(*args, **kwargs)
            # else:
            if kwargs:
                # 暂时不支持不定参数，后续改动支持
                raise NotImplementedError('暂时不支持不定参数！请检查代码！')

            # 只用作计算参数唯一性，便于lru_cache使用唯一id做缓存标识
            if is_class_method:
                cache_key = _calc_cache_args_key(args[1:])  # 去掉self, 在进行计算
            else:
                cache_key = _calc_cache_args_key(args)
            if not cache_key:
                return func(*args, **kwargs)

            conn = conn_redis()
            full_cache_key = f'chart_query_func_cache:{cache_key}'
            cache_data = conn.get(full_cache_key)
            if not cache_data:
                real_data = func(*args, **kwargs)
                cache_data = base64.b64encode(pickle.dumps(real_data))
                conn.set(full_cache_key, cache_data, time=timeout)
            else:
                real_data = pickle.loads(base64.b64decode(cache_data))
            return real_data

        return wrapper

    return cache_wrapper
