#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/8/3 17:30
# <AUTHOR> caoxl
# @File     : standalone_query.py
from .charts.column_chart import ColumnChart
from ..models import ChartDataModel

class StandAloneQuery:
    """
    独立查询服务，脱离单图
    此处统一使用按列返回取数
    """
    def __init__(self, chart_data_model: ChartDataModel):
        self.chart_data_model = chart_data_model

    def _create_query_cls(self):
        query_cls = ColumnChart(model=self.chart_data_model)
        return query_cls

    def query(self, dataset_version: str = None):
        query_cls = self._create_query_cls()
        return query_cls.get_chart_data(dataset_version)

    def query_underlying_data(self):
        query_cls = self._create_query_cls()
        return query_cls.query_underlying_data()
