#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/2 15:07
# <AUTHOR> caoxl
# @File     : base.py
from typing import Union


class BaseCls:
    @staticmethod
    def get_data_by_path(path: str, metadata: dict):
        """获取节点元数据"""
        ret = metadata
        for v in path.split('.'):
            if v in ret:
                ret = ret.get(v)
            else:
                return None
        return ret

    def get_chart_metadata(self, chart_id: str, dashboard_meta_data: dict) -> Union[dict, None]:
        charts = self.get_data_by_path("first_report.charts", dashboard_meta_data) or []
        for chart in charts:
            if chart.get("id") == chart_id:
                return chart
        return None
