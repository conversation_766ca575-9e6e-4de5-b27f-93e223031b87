#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/1 14:25
# <AUTHOR> caoxl
# @File     : transaction.py
import json
import time
from queue import Queue
import timeout_decorator
from base.enums import ChartCopyTransactionStatus, ChartCopyTaskStatus
from dashboard_chart.chart_reproducer.task import Task
from dashboard_chart.repositories import chart_copy_repository, chart_repository
from dashboard_chart.services import metadata_service
from dmplib.utils.errors import UserError
from message.services import message_service
from message.models import MessageModel


class Transaction:
    MAX_PROCESS_NUM = 4

    """
    事务类 该类职责为处理事务记录(事务ID，事务状态, 管理多进程，监控进程)
    """

    def __init__(self, userid, account, transcation_id):
        self.userid = userid
        self.account = account
        is_exist, self.transcation = chart_copy_repository.get_chart_copy_transaction(transcation_id)
        if not is_exist:
            raise UserError(message=f"事务 {transcation_id} 不存在！")
        msg, self.source_dashboard_metadata = metadata_service.get_screens_preview_metadata_v2(
            dashboard_id=self.transcation["source_dashboard_id"]
        )
        if not self.source_dashboard_metadata:
            error = f"获取源报告元数据发生错误 {msg}"
            chart_copy_repository.update_chart_copy_transaction(
                transcation_id, {"status": ChartCopyTransactionStatus.Succeed.value}
            )
            current_time = self.get_current_time()
            for task in self.transcation.get("tasks", []):
                chart_copy_repository.update_chart_copy_task(
                    task["id"],
                    {
                        "status": ChartCopyTaskStatus.Failed.value,
                        "end_time": current_time,
                        "messages": json.dumps([error]),
                    },
                )
            raise UserError(message=error)
        self._task_queue = Queue()

    def __call__(self, *args, **kwargs):
        self.run()

    def get_process_num(self, task_count: int):
        if task_count < self.MAX_PROCESS_NUM:
            return task_count
        return self.MAX_PROCESS_NUM

    def run(self):
        start_time = self.get_current_time()
        # 1. 更新事务状态
        chart_copy_repository.update_chart_copy_transaction(
            self.transcation["id"], {"status": ChartCopyTransactionStatus.Running.value, "start_time": start_time}
        )
        # 2. 塞入队列
        task_count = 0
        for task in self.transcation["tasks"]:
            if task["status"] == ChartCopyTaskStatus.Succeed.value:
                continue
            params = {
                "source_dashboard_id": self.transcation["source_dashboard_id"],
                "source_chart_id": self.transcation["source_chart_id"],
                "target_dashboard_id": task["target_dashboard_id"],
                "source_dashboard_metadata": self.source_dashboard_metadata,
                "userid": self.userid,
                "account": self.account,
                "task_id": task["id"],
            }
            task_count += 1
            self._task_queue.put(params)
        # 3. 多进程执行
        process_num = self.get_process_num(task_count)
        for _ in range(process_num):
            self.run_task(self._task_queue)
        end_time = self.get_current_time()
        # 3. 更新事务状态
        chart_copy_repository.update_chart_copy_transaction(
            self.transcation["id"], {"status": ChartCopyTransactionStatus.Succeed.value, "end_time": end_time}
        )
        # 4. 推送消息
        result, transaction = chart_copy_repository.get_chart_copy_transaction(self.transcation["id"])
        if result:
            successed_count, failed_count = 0, 0
            for task in transaction.get("tasks", []):
                if task.get("status") == ChartCopyTaskStatus.Succeed.value:
                    successed_count += 1
                else:
                    failed_count += 1
            chart = chart_repository.get_chart_info(self.transcation["source_chart_id"])
            title = f"报告({chart['dashboard_name']})单图({chart['name']})已复制完成,共{successed_count}条成功 {failed_count} 条失败"
            message_model = MessageModel(
                **{
                    "source_id": self.transcation["id"],
                    "source": "编辑器",
                    "type": "个人消息",
                    "title": title,
                    "url": f"copychart://{self.transcation['id']}",
                    "status": 1,
                    "level": "提示",
                    "user_id": self.userid,
                }
            )
            message_service.upset_message(message_model)

    @staticmethod
    def get_current_time():
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    @staticmethod
    def run_task(task_queue: Queue):
        task_ids = []
        while not task_queue.empty():
            task_data = task_queue.get()
            task_ids.append(task_data["task_id"])
            task = Task(task_data)
            messages = []
            # 更新任务状态
            chart_copy_repository.update_chart_copy_task(
                task_data["task_id"],
                {"status": ChartCopyTaskStatus.Running.value, "start_time": Transaction.get_current_time()},
            )
            try:
                success, messages = task.run()
                status = ChartCopyTaskStatus.Succeed.value if success else ChartCopyTaskStatus.Failed.value
            except timeout_decorator.TimeoutError:
                status = ChartCopyTaskStatus.Timeout.value
            except Exception as e:
                status = ChartCopyTaskStatus.Failed.value
                messages.append(str(e))
            end_time = Transaction.get_current_time()
            # 更新任务状态
            chart_copy_repository.update_chart_copy_task(
                task_data["task_id"], {"status": status, "end_time": end_time, "messages": json.dumps(messages, ensure_ascii=False)}
            )
        return task_ids
