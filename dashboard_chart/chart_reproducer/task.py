#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/1 14:37
# <AUTHOR> caoxl
# @File     : task.py
import timeout_decorator
from dashboard_chart.services import metadata_service, dashboard_service
from dmplib.utils.errors import UserError
from .nodes.dashboard_node import DashboardNode
from .models import CopyTaskDataModel, ReproducerChartNodeDataModel
from .base_cls import BaseCls
from dashboard_chart.repositories import chart_copy_repository
from copy import deepcopy


class Task(BaseCls):
    def __init__(self, task_data):
        """
        task_data 数据结构
        {
            "source_dashboard_id": "源报告ID",
            "source_chart_id": "源单图ID",
            "target_dashboard_id": "目标报告ID",
            "source_dashboard_metadata": "源报告元数据",
            "userid": "用户ID",
            "account": "账户名称",
            "task_id": "任务ID"
        }
        :param task_data:
        """
        task_id = task_data.get("task_id")
        if not task_id:
            raise UserError(message="未指定任务ID，无法运行任务")
        msg, target_dashboard_metadata = metadata_service.get_screens_preview_metadata_v2(
            dashboard_id=task_data.get("target_dashboard_id")
        )
        if not target_dashboard_metadata:
            raise UserError(message=f"目标报告 {task_data.get('target_dashboard_id')} 无法获取报告元数据，错误信息 {msg}")
        task_data["target_dashboard_metadata"] = target_dashboard_metadata
        node_data_model = CopyTaskDataModel(**task_data)
        node_data_model.validate()
        self.id = task_id
        self.node_data_model = node_data_model

    @timeout_decorator.timeout(60)
    def run(self):
        node_data_copy = deepcopy(self.node_data_model)
        # 检查原始报告和目标报告中的行高是否一致
        dashboard_service.check_dashboard_line_height(node_data_copy)
        # 报告节点
        dashboard_node = DashboardNode(ReproducerChartNodeDataModel(**node_data_copy.get_dict()))
        # 各个单图节点
        target_chart_node = dashboard_node.add_chart_nodes()
        # 对整个节点进行校验
        result, messages = dashboard_node.validate()
        if not result:
            return False, messages
        # 生成最终元数据
        complete_target_metadata = dashboard_node.build(node_data_copy.target_dashboard_metadata)
        if not complete_target_metadata:
            return False, [f"目标报告 {self.node_data_model.target_dashboard_id} 元数据生成失败，请重试！"]
        # 调用报告编辑器
        result, errrors = dashboard_service.update_metadata(complete_target_metadata)
        if result:
            dashboard_info = self.node_data_model.target_dashboard_metadata.get('dashboard') or {}
            dashboard_info['source_id'] = self.node_data_model.source_dashboard_id
            dashboard_service.update_dashboard_jump_info([dashboard_info])
            # dashboard_service.update_dashboard_jump_info([{
            #     'id': self.node_data_model.target_dashboard_id,
            #     'source_id': self.node_data_model.source_dashboard_id}]
            # )
        else:
            error_word = ",".join([error.get("msg") for error in errrors])
            messages = [f"报错目标报告 {self.node_data_model.target_dashboard_id} 元数据失败，错误信息: [ {error_word} ] "]
            return False, messages
        # 更改目标单图ID
        chart_copy_repository.update_chart_copy_task(self.id, {"target_chart_id": target_chart_node.get_id()})
        return True, []
