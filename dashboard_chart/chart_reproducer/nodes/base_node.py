#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/2 11:20
# <AUTHOR> caoxl
# @File     : node.py
import copy
from abc import ABCMeta, abstractmethod
from typing import List, Tuple
from ..models import ReproducerNodeDataModel
from ..base_cls import BaseCls


class BaseNode(BaseCls):
    __metaclass__ = ABCMeta

    def __init__(self, node_data_model: ReproducerNodeDataModel):
        self.source_chart_id = node_data_model.source_chart_id
        self.source_dashboard_id = node_data_model.source_dashboard_id
        self.target_dashboard_id = node_data_model.target_dashboard_id
        self.userid = node_data_model.userid
        self.account = node_data_model.account
        self.target_dashboard_name = self.get_data_by_path(
            "dashboard.dashboard.name", node_data_model.target_dashboard_metadata
        )
        self.source_dashboard_name = self.get_data_by_path(
            "dashboard.dashboard_name", node_data_model.source_dashboard_metadata
        )
        self._set_original_target_metadata(node_data_model.target_dashboard_metadata)
        self._set_source_metadata(node_data_model.source_dashboard_metadata)
        self.children = []

    def _set_original_target_metadata(self, target_metadata):
        # 此处做法是为了模拟只读属性，原始目标元数据不能更改
        self._ls324234dff34232213 = target_metadata

    def _set_source_metadata(self, source_metadata):
        # 此处做法是为了模拟只读属性，源元数据不能更改
        self._lsf3eeff232fgf565334 = source_metadata

    def get_source_metadata(self):
        # 此处统一返回副本，避免对原始元数据更改，造成难以定位的bug
        return copy.deepcopy(self._lsf3eeff232fgf565334)

    def get_original_target_metadata(self):
        # 此处统一返回副本，避免对原始元数据更改，造成难以定位的bug
        return copy.deepcopy(self._ls324234dff34232213)

    def add_node(self, node):
        self.children.append(node)

    def validate(self) -> Tuple[bool, List]:
        # 子节点校验
        for child in self.children:
            result, messages = child.validate()
            if not result:
                return result, messages
        return True, []

    @abstractmethod
    def build(self, metadata) -> dict:
        pass
