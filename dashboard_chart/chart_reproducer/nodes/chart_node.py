#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/2 11:30
# <AUTHOR> caoxl
# @File     : chart_node.py
# pylint:disable=R0201
import copy
from typing import List, Tuple

from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from .base_node import BaseNode
from .chart.leaf_base_node import LeafBaseNode
from ..models import ReproducerChartNodeDataModel, ChartLeafNodeDataModel


class ChartNode(BaseNode):
    def __init__(self, node_data_model: ReproducerChartNodeDataModel):
        super().__init__(node_data_model)
        self.parent_id = node_data_model.target_parent_chart_id
        self.container_id = node_data_model.container_id
        self.source_metadata = self.get_source_metadata()
        self.children_chart_ids = []
        self.source_children_chart_ids = []
        self.total_copy_chart_nodes = []
        self.copy_source_chart_list = []

    def get_id(self):
        attr = '_asd23324efwefwef33232'
        if not hasattr(self, attr):
            setattr(self, attr, seq_id())
        return getattr(self, attr)

    def set_parent_id(self, parent_id: str):
        self.parent_id = parent_id

    def set_copy_source_chart(self, copy_source_chart_list: list):
        self.copy_source_chart_list = copy_source_chart_list

    def add_node(self, node: LeafBaseNode):
        self.children.append(node)

    def validate(self) -> Tuple[bool, List]:
        source_chart_metadata = self.source_metadata
        if not source_chart_metadata:
            return False, [f"源报告 {self.source_dashboard_name} 单图 {self.source_chart_id} 不存在"]
        source_charts = self.get_data_by_path("first_report.charts", source_chart_metadata) or []
        re, messages = self._validate_chart_position("源", source_charts)
        if not re:
            return re, messages
        target_chart_metadata = self.get_old_target_metadata()
        tartget_charts = self.get_data_by_path("first_report.charts", target_chart_metadata) or []
        re, messages = self._validate_chart_position("目标", tartget_charts)
        if not re:
            return re, messages
        target_dashboard_layout = self.get_data_by_path("dashboard.layout", target_chart_metadata)
        re, messages = self._validate_dashboard_layout("目标", target_dashboard_layout)
        if not re:
            return re, messages
        return super().validate()

    def _validate_chart_position(self, from_type: str, charts: list) -> Tuple[bool, list]:
        re = True
        messages = []
        for chart in charts:
            position = chart.get("position", {})
            if not isinstance(position, (dict)):
                re = False
                messages.append(f"{from_type}报告单图 [{chart.get('name')}] 位置信息数据异常，不是字典类型数据")
        return re, messages

    def _validate_dashboard_layout(self, from_type: str, dashboard_layout: dict) -> Tuple[bool, list]:
        re = True
        messages = []
        if not isinstance(dashboard_layout, (dict)):
            re = False
            return re, [f"{from_type}报告数据异常，不是字典"]
        return re, messages

    def get_source_chart_metadata(self) -> dict:
        attr = "source_chart_metadata"
        if not hasattr(self, attr):
            source_chart_metadata = self.get_chart_metadata(self.source_chart_id, self.source_metadata)
            source_chart_metadata["id"] = self.get_id()
            source_chart_metadata['parent_chart_id'] = self.container_id
            source_chart_metadata['children_chart_ids'] = self.children_chart_ids
            # 解决组合筛选器前端配置跨报告复制id更新问题
            if self.children_chart_ids and self.source_children_chart_ids:
                config = source_chart_metadata['config']
                for source_id, target_id in zip(self.source_children_chart_ids, self.children_chart_ids):
                    config = config.replace(source_id, target_id)
                source_chart_metadata['config'] = config
            setattr(self, attr, source_chart_metadata)
        return getattr(self, attr)

    def get_old_target_metadata(self) -> dict:
        attr = "old_target_metadata"
        if not hasattr(self, attr):
            old_target_metadata = self.get_original_target_metadata()
            setattr(self, attr, old_target_metadata)
        return getattr(self, attr)

    def build(self, metadata: dict) -> dict:
        source_chart_metadata = self.get_source_chart_metadata()
        target_chart_metadata = copy.deepcopy(source_chart_metadata)
        # 1. 重新生成名称 (名称不能重复)
        target_chart_metadata["name"] = self._get_new_chart_name(source_chart_metadata.get("name"))
        # 2. 重新计算z-index 坐标等
        target_chart_metadata["position"] = self._compute_position(metadata)
        # 3. 筛选默认值ID替换
        target_chart_metadata = self._change_chart_default_value(target_chart_metadata) or []
        # 4. 生成单 图节点
        metadata["first_report"]["charts"].append(target_chart_metadata)
        for child in self.children:
            metadata = child.build(metadata)
        return metadata

    def _get_new_chart_name(self, old_chart_name: str) -> str:
        """
        获取单图新名称，避免重复
        :param old_chart_name:
        :return:
        """
        charts = self.get_data_by_path("first_report.charts", self.get_old_target_metadata()) or []
        names = [chart.get("name") for chart in charts]
        new_name = old_chart_name
        total = names.count(old_chart_name)
        if total > 0:
            new_name = f"{new_name}_{total}"
        return new_name

    def _compute_position(self, curr_metadata) -> dict:
        source_chart_metadata = self.get_source_chart_metadata()
        old_position = source_chart_metadata.get("position")
        new_position = copy.deepcopy(old_position)
        # 适配position
        new_position = self._adapt_position(new_position, "源报告来源单图")
        original_target_metadata = self.get_original_target_metadata()
        # 目标报告为自由布局需要更改z-index
        # new_position = self._compute_new_zindx(original_target_metadata, new_position)
        new_position = self._compute_new_zindex_v2(curr_metadata, new_position)
        # 目标报告为固定布局，需要调整大小及x,y轴
        new_position = self._get_new_coordinate_and_size(original_target_metadata, new_position)
        # 移除群组组件内部组件复制单组件的群组id
        new_position = self._remove_dmp_group_id(new_position)
        if "i" in new_position:
            new_position['i'] = self.get_id()
        return new_position

    def _remove_dmp_group_id(self, position: dict) -> dict:
        """
        复制单个群组内部的组件时，移除group_id
        """
        if len(self.total_copy_chart_nodes) == 1:
            position.pop('group_id', None)
        return position

    @staticmethod
    def _change_chart_default_value(target_chart_metadata):
        chart_default_list = target_chart_metadata.get('data', {}).get('chart_default_value', []) or []
        for default in chart_default_list:
            default['id'] = seq_id()
        return target_chart_metadata

    def _compute_new_zindex_v2(self, curr_metadata: dict, position: dict) -> dict:
        """
        取当前已知组件的最大的zindex的最大值+1
        :param curr_metadata: 目前报告当前的元数据信息
        :return:
        """
        target_layout = self.get_data_by_path("dashboard.layout", curr_metadata)
        new_layout_type = self.get_data_by_path("dashboard.new_layout_type", curr_metadata)
        mode = target_layout.get("mode") or new_layout_type
        if mode != 'free':
            return position
        charts = self.get_data_by_path("first_report.charts", curr_metadata)
        if charts:
            # 取已知组件的最大z值，每次加1
            max_zindex = 1501
            for chart in charts:
                current_position = chart.get("position", {})
                try:
                    zindex = int(current_position.get("z"))
                except Exception:
                    zindex = 1501
                max_zindex = max(max_zindex, zindex)
            position['z'] = max_zindex + 1
        else:
            position['z'] = 1501
        return position

    def _compute_new_zindx(self, original_target_metadata: dict, position: dict) -> dict:
        """
        :param original_target_metadata:
        :return:
        """
        target_layout = self.get_data_by_path("dashboard.layout", original_target_metadata)
        new_layout_type = self.get_data_by_path("dashboard.new_layout_type", original_target_metadata)
        mode = target_layout.get("mode") or new_layout_type
        if mode != 'free':
            return position
        charts = self.get_data_by_path("first_report.charts", original_target_metadata)
        max_zindex = 1
        count = 1
        for chart in charts:
            current_position = chart.get("position", {})
            try:
                zindex = int(current_position.get("z"))
            except Exception:
                zindex = max_zindex + count
            if zindex > max_zindex:
                max_zindex = zindex
            count += 1
        position['z'] = max_zindex
        return position

    def _get_new_coordinate_and_size(self, original_target_metadata: dict, position: dict) -> dict:
        charts = self.get_data_by_path("first_report.charts", original_target_metadata)
        target_layout = self._get_complete_layout(original_target_metadata)
        # 固定布局需要重新计算 x y 坐标 及大小
        self._get_new_coordinate_and_size_of_grid(charts, position, target_layout)
        # 自由布局需要限制不要超过目标报告的最大宽度和高度
        self._get_new_coordinate_and_size_of_free(original_target_metadata, position, target_layout)
        return position

    def _get_complete_layout(self, metadata: dict):
        """
        获取完整的layout
        :param metadata:
        :return:
        """
        layout = self.get_data_by_path("dashboard.layout", metadata)
        if "mode" not in layout:
            layout["mode"] = self.get_data_by_path("dashboard.new_layout_type", metadata)
        return layout

    def _get_new_coordinate_and_size_of_free(self, original_target_metadata: dict, position: dict, target_layout: dict):
        source_dashboard_metadata = self.source_metadata
        source_layout = self._get_complete_layout(source_dashboard_metadata)
        if target_layout.get("mode") == "free":
            # 清除容器组件父子记录
            self._clear_table_component_position(position)
            # 源头为固定布局 此处需要调整大小
            if source_layout.get("mode") == 'grid':
                position['col'] = 0
                position['row'] = 0
                position['size_x'] = 480
                position['size_y'] = 270
                return
            target_dashboard_layout = self._adapt_dashboard_layout(
                self.get_data_by_path("dashboard.layout", original_target_metadata), "目标报告"
            )
            max_height = float(
                target_dashboard_layout.get(
                    "height", 500 if target_dashboard_layout.get("platform") == 'mobile' else 1080
                )
            )
            max_width = float(
                target_dashboard_layout.get(
                    "width", 750 if target_dashboard_layout.get("platform") == 'mobile' else 1920
                )
            )
            if position['col'] > max_width:
                position['col'] = 0
            if position['row'] > max_height:
                position['row'] = 0

    def _clear_table_component_position(self, position: dict):
        # 容器组件需要删除key parent_id
        parent_id = position.get('parent_id')
        if 'key' in position and parent_id not in self.copy_source_chart_list:
            del position["key"]
        if parent_id and parent_id not in self.copy_source_chart_list:
            del position['parent_id']

    def _change_position_chart_id(self, position: dict):
        if 'i' in position:
            position['i'] = self.get_id()

    def _get_new_coordinate_and_size_of_grid(self, charts: List, position: dict, target_layout: dict):
        if target_layout.get("mode") == "grid":
            source_dashboard_metadata = self.source_metadata
            source_layout = self._get_complete_layout(source_dashboard_metadata)
            # 清除容器组件父子记录
            self._clear_table_component_position(position)
            # 更改position中chart_id
            self._change_position_chart_id(position)
            if position.get('parent_id') and position.get('parent_id') in self.copy_source_chart_list:
                return
            # 数据兼容处理
            self._adapt_charts_position(charts, "目标报告")
            charts = sorted(
                charts, key=lambda x: (x.get("position", {}).get("row", 0), x.get("position", {}).get("col", 0))
            )
            last_chart = charts[len(charts) - 1] if charts else {}
            last_chart_position = last_chart.get("position", {})
            # 固定到固定布局部更改大小
            if source_layout.get("mode") != "grid":
                position['size_x'] = 6 if position['size_x'] > 12 else position['size_x']
                position['size_y'] = 6 if position['size_y'] > 12 else position['size_y']
            # 过宽要换行(新移动换行)
            if (
                last_chart_position.get("col", 0) + last_chart_position.get("size_x", 0) + position['size_x']
            ) > 12 or target_layout.get("platform") == "mobile":
                position['col'] = 0
                position['row'] = last_chart_position.get("size_y", 0) + last_chart_position.get("row", 0)
            else:
                position['col'] = last_chart_position.get("col", 0) + last_chart_position.get("size_x", 0)
                position["row"] = last_chart_position.get("row", 0)

    def _adapt_dashboard_layout(self, dashboard_layout: dict, msg_prefix: str):
        try:
            dashboard_layout["height"] = float(dashboard_layout.get("height"))
        except Exception as e:
            raise UserError(message=f"{msg_prefix} height(高度)数据异常，不是数值类型数据, 错误信息 {str(e)}")
        try:
            dashboard_layout["width"] = float(dashboard_layout.get("width"))
        except Exception as e:
            raise UserError(message=f"{msg_prefix} width(宽度)数据异常，不是数值类型数据, 错误信息 {str(e)}")
        return dashboard_layout

    def _adapt_charts_position(self, charts: list, msg_prefix: str):
        for chart in charts:
            pos = chart.get("position", {})
            current_msg_prefix = f"{msg_prefix} 单图 {chart.get('name')} "
            chart["position"] = self._adapt_position(pos, current_msg_prefix)

    def _adapt_position(self, position: dict, msg_prefix: str):
        try:
            position["row"] = float(position.get("row", 0))
        except Exception as e:
            raise UserError(message=f"{msg_prefix} row (Y轴坐标) 数据有误，不是数值类型数据，错误信息 {str(e)}")
        try:
            position["col"] = float(position.get("col", 0))
        except Exception as e:
            raise UserError(message=f"{msg_prefix} col (X轴坐标) 数据有误，不是数值类型数据，错误信息 {str(e)}")
        try:
            position["size_x"] = float(position.get("size_x", 0))
        except Exception as e:
            raise UserError(message=f"{msg_prefix} size_x (宽度) 数据有误，不是数值类型数据，错误信息 {str(e)}")
        try:
            position["size_y"] = float(position.get("size_y", 0))
        except Exception as e:
            raise UserError(message=f"{msg_prefix} size_y (高度) 数据有误，不是数值类型数据，错误信息 {str(e)}")
        return position

    @staticmethod
    def scale(width: int, height: int, max_width: int, max_height: int) -> Tuple[int, int]:
        """
        等比缩放
        :param width:
        :param height:
        :param max_width:
        :param max_height:
        :return:
        """
        if width > max_width:
            height = int((max_width / width) * height)
            width = max_width
        if height > max_height:
            width = int((max_height / height) * width)
            height = max_height
        return width, height

    def add_chart_leaf_nodes(self):
        """
        添加叶子节点
        :param chart_node:
        :return:
        """
        leaf_nodes = [
            "chart_filter",
            "chart_param",
            "comparison",
            "desire",
            "field_sort",
            "markline",
            "dim",
            "num",
            "penetrate",
            "redirect",
            "zaxis",
        ]
        leaf_data_dict = {
            'source_dashboard_id': self.source_dashboard_id,
            'source_chart_id': self.source_chart_id,
            'target_dashboard_id': self.target_dashboard_id,
            'source_dashboard_metadata': self.source_metadata,
            'userid': self.userid,
            'account': self.account,
            'target_dashboard_metadata': self.get_original_target_metadata(),
            'target_chart_id': self.get_id(),
            'target_parent_chart_id': self.parent_id,
        }
        leaf_node_data_model = ChartLeafNodeDataModel(**leaf_data_dict)
        for node in leaf_nodes:
            node_class = self.get_chart_leaf_node_class(node)
            node_obj = node_class(leaf_node_data_model)
            self.add_node(node_obj)

    @staticmethod
    def get_chart_leaf_node_class(node_name: str):
        class_file_name = f"{node_name}_node"
        class_name = ''
        name_arr = node_name.split('_')
        for name in name_arr:
            class_name += name.capitalize()
        class_name += "Node"
        module_obj = __import__(
            'dashboard_chart.chart_reproducer.nodes.chart.' + class_file_name, fromlist=[class_name]
        )
        return getattr(module_obj, class_name)
