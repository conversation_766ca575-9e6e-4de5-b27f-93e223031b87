#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/3 14:30
# <AUTHOR> caoxl
# @File     : redirect_node.py
import copy

from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class RedirectNode(LeafBaseNode):
    """
    报告跳转节点
    """

    def build(self, metadata: dict) -> dict:
        source_redirects = self.get_data_by_path("first_report.chart_relations.redirects", self.get_source_metadata())
        if not source_redirects:
            return metadata
        target_redirects = []
        for redirect in source_redirects:
            if redirect.get("chart_id") == self.source_chart_id:
                target_redirect = copy.deepcopy(redirect)
                target_redirect["chart_id"] = self.target_chart_id
                for relation in target_redirect.get("chart_redirect", []):
                    relation["id"] = seq_id()
                for relation in target_redirect.get("complex_redirect", []):
                    relation["id"] = seq_id()
                target_redirects.append(target_redirect)
        metadata["first_report"]["chart_relations"]["redirects"].extend(target_redirects)
        return metadata
