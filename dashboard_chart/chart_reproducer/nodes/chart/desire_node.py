#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 11:03
# <AUTHOR> caoxl
# @File     : desire_node.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class DesireNode(LeafBaseNode):
    """
    维度节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        desires = self.get_data_by_path("data.indicator.desires", chart_metadata) or []
        for desire in desires:
            desire["dashboard_chart_id"] = self.target_chart_id
            desire["id"] = seq_id()
        return metadata
