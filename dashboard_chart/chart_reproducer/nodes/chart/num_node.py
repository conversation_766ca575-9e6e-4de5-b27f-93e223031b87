#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/3 14:30
# <AUTHOR> caoxl
# @File     : num_node.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class NumNode(LeafBaseNode):
    """
    度量节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        nums = self.get_data_by_path("data.indicator.nums", chart_metadata) or []
        for num in nums:
            num["dashboard_chart_id"] = self.target_chart_id
            num["id"] = seq_id()
        return metadata
