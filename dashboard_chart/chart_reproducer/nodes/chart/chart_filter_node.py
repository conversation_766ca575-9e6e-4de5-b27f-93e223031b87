#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 10:46
# <AUTHOR> caoxl
# @File     : chart_filter.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class ChartFilterNode(LeafBaseNode):
    """
    维度节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        filters = self.get_data_by_path("data.indicator.filters", chart_metadata) or []
        for item in filters:
            item["dashboard_chart_id"] = self.target_chart_id
            item["id"] = seq_id()
            operators = item.get("operators")
            if not isinstance(operators, (list, tuple)):
                continue
            for operator in operators:
                operator["id"] = seq_id()
            item["operators"] = operators
        return metadata
