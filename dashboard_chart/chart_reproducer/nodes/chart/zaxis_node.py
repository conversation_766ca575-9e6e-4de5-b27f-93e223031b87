#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/7 17:42
# <AUTHOR> caoxl
# @File     : zaxis_node.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class ZaxisNode(LeafBaseNode):
    """
    Z轴
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        params = self.get_data_by_path("data.indicator.zaxis", chart_metadata) or []
        for item in params:
            item["dashboard_chart_id"] = self.target_chart_id
            item["id"] = seq_id()
        return metadata
