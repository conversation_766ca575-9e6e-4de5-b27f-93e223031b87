#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/3 14:30
# <AUTHOR> caoxl
# @File     : penetrate_node.py
import copy

from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class PenetrateNode(LeafBaseNode):
    """
    穿透节点
    """

    def build(self, metadata: dict) -> dict:
        source_penetrates = self.get_data_by_path("first_report.chart_relations.penetrates", self.get_source_metadata())
        if not source_penetrates:
            return metadata
        target_penetrates = []
        for penetrate in source_penetrates:
            if penetrate.get("chart_id") == self.source_chart_id:
                target_penetrate = copy.deepcopy(penetrate)
                target_penetrate["chart_id"] = self.target_chart_id
                target_penetrate["parent_id"] = self.target_parent_chart_id
                for relation in target_penetrate.get("relation", []):
                    relation["id"] = seq_id()
                    relation["dashboard_chart_id"] = self.target_chart_id
                for relation in target_penetrate.get("penetrate_filter_relation", []):
                    relation["id"] = seq_id()
                    relation["dashboard_chart_id"] = self.target_chart_id
                for relation in target_penetrate.get("penetrate_var_filter_relation", []):
                    relation["id"] = seq_id()
                    relation["dashboard_chart_id"] = self.target_chart_id
                target_penetrates.append(target_penetrate)
        metadata["first_report"]["chart_relations"]["penetrates"].extend(target_penetrates)
        return metadata
