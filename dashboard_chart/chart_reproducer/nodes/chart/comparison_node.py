#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 11:00
# <AUTHOR> caoxl
# @File     : comparison_node.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class ComparisonNode(LeafBaseNode):
    """
    维度节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        comparsions = self.get_data_by_path("data.indicator.comparisons", chart_metadata) or []
        for comparsion in comparsions:
            comparsion["dashboard_chart_id"] = self.target_chart_id
            comparsion["id"] = seq_id()
        return metadata
