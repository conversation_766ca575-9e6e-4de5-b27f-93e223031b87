#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 11:07
# <AUTHOR> caoxl
# @File     : markline_node.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class MarklineNode(LeafBaseNode):
    """
    维度节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        marklines = self.get_data_by_path("data.indicator.marklines", chart_metadata) or []
        for markline in marklines:
            markline["dashboard_chart_id"] = self.target_chart_id
            markline["id"] = seq_id()
        return metadata
