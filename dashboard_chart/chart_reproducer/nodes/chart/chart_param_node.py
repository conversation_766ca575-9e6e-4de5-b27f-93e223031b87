#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 11:09
# <AUTHOR> caoxl
# @File     : chart_param.py
from dmplib.utils.strings import seq_id
from .leaf_base_node import LeafBaseNode


class ChartParamNode(LeafBaseNode):
    """
    维度节点
    """

    def build(self, metadata: dict) -> dict:
        chart_metadata = self.get_chart_metadata(self.target_chart_id, metadata)
        params = self.get_data_by_path("data.indicator.chart_params", chart_metadata) or []
        for item in params:
            item["dashboard_chart_id"] = self.target_chart_id
            item["param_id"] = seq_id()
        return metadata
