#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/2 11:38
# <AUTHOR> caoxl
# @File     : chart_base_node.py
from abc import ABCMeta, abstractmethod
from ..base_node import BaseNode
from ...models import ReproducerNodeDataModel, ChartLeafNodeDataModel


class LeafBaseNode(BaseNode):
    __metaclass__ = ABCMeta

    def __init__(self, node_data_model: ChartLeafNodeDataModel):
        super().__init__(ReproducerNodeDataModel(**node_data_model.get_dict()))
        self.target_chart_id = node_data_model.target_chart_id
        self.target_parent_chart_id = node_data_model.target_parent_chart_id

    @abstractmethod
    def build(self, metadata) -> dict:
        pass
