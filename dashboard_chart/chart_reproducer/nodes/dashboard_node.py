#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/1 21:17
# <AUTHOR> caoxl
# @File     : dashboard.py
import copy
import json

from dashboard_chart.chart_reproducer.nodes.chart_node import ChartNode
from ..models import ReproducerChartNodeDataModel
from .base_node import BaseNode
from .chart_node import ChartN<PERSON>
from typing import List, Tuple
from ...services import dashboard_service, dashboard_lock_service
from dmplib.utils.strings import seq_id


class DashboardNode(BaseNode):
    def __init__(self, node_data_model):
        super().__init__(node_data_model)
        self.replace_map = {}

    def validate(self) -> Tuple[bool, List]:
        # 1. 目标报告是否有编辑权限
        if not dashboard_service.dashboard_check_permission(self.userid, self.target_dashboard_id, "edit"):
            return False, [f"用户 {self.account} 对报告 {self.target_dashboard_name} 无编辑权限!"]
        # 2. 目标报告是否在编辑中
        if dashboard_lock_service.get_dashboard_lock_data(self.target_dashboard_id):
            return False, ["报告正在被其他用户编辑，请稍后再试!"]
        # 3. 目标报告平台是否匹配
        result, msg = self._check_dashboard_platform()
        if not result:
            return False, [msg]
        # 4 .父级子节点校验
        return super().validate()

    def build(self, metadata: dict):
        # 报告节点本身无需更改元数据
        for child in self.children:
            child.total_copy_chart_nodes = self.children
            metadata = child.build(metadata)
        # 处理组件之前的关联关系
        self._get_complete_chart_relations(metadata)
        # 将要复制的组件元数据执行替换id操作，维护元数据里面的原始组件关系
        if self.children:
            charts_count = len(self.children)
            copy_charts = metadata["first_report"]["charts"][-charts_count:]
            copy_charts = self.replace_other_ids(copy_charts)
            metadata["first_report"]["charts"] = [*metadata["first_report"]["charts"][:-charts_count], *copy_charts]
        return metadata

    def replace_other_ids(self, chart_metadata):
        # 解决剩余有关系的组件id，一同更新替换
        if not self.replace_map:
            return chart_metadata
        chart_metadata_str = json.dumps(chart_metadata)
        for old_id, new_id in self.replace_map.items():
            chart_metadata_str = chart_metadata_str.replace(old_id, new_id)
        return json.loads(chart_metadata_str)

    def _get_complete_layout(self, metadata):
        """
        获取完整的layout
        :param metadata:
        :return:
        """
        layout = self.get_data_by_path("dashboard.layout", metadata)
        if "mode" not in layout:
            layout["mode"] = self.get_data_by_path("dashboard.new_layout_type", metadata)
        return layout

    def _get_complete_chart_relations(self, metadata):
        source_metadata = self.get_source_metadata()
        chart_relations = source_metadata.get('first_report', {}).get('chart_relations', {}) or {}
        target_chart_relations = metadata.get('first_report', {}).get('chart_relations', {}) or {}
        # 变量关系替换
        target_chart_relations['var_relations'] = self._get_complete_chart_var_relations(chart_relations, target_chart_relations)
        chart_node_names = ['chart_filters', 'chart_linkages']
        for chart_node_name in chart_node_names:
            target_chart_relations[chart_node_name] = self._get_complete_chart_node(chart_relations, target_chart_relations, chart_node_name)

    def _get_complete_chart_node(self, chart_relations, target_chart_relations, chart_node_name):
        chart_node = chart_relations.get(chart_node_name, []) or []
        new_chart_node = target_chart_relations.get(chart_node_name) or []
        for chart in chart_node:
            if chart.get('chart_initiator_id') in list(self.replace_map.keys()):
                new_related = []
                chart['id'] = seq_id()
                chart['chart_initiator_id'] = self.replace_map.get(chart['chart_initiator_id'])
                related_list = chart.get('related_list') or []
                for related in related_list:
                    if related.get('chart_responder_id') in list(self.replace_map.keys()):
                        related['id'] = seq_id()
                        related['chart_responder_id'] = self.replace_map.get(related['chart_responder_id'])
                        new_related.append(related)
                chart['related_list'] = new_related
                new_chart_node.append(chart)
        return new_chart_node

    def _get_complete_chart_var_relations(self, chart_relations, target_chart_relations):
        var_relation = chart_relations.get('var_relations', []) or []
        new_var_relation = target_chart_relations.get('var_relations') or []
        for var in var_relation:
            if var.get('chart_initiator_id') in list(self.replace_map.keys()):
                var['id'] = seq_id()
                var['dashboard_id'] = self.target_dashboard_id
                var['chart_initiator_id'] = self.replace_map.get(var['chart_initiator_id'])
                if var.get('var_dim_obj', {}).get('dashboard_chart_id'):
                    var['var_dim_obj']['dashboard_chart_id'] = self.replace_map.get(var['var_dim_obj']['dashboard_chart_id'])
                new_var_relation.append(var)
        return new_var_relation

    def _check_dashboard_platform(self) -> Tuple[bool, str]:
        """
        检查报告平台是否匹配,  检查规则：
        i. 大屏：支持将大屏组件复制到大屏、大屏（移动）、仪表板
        ii. 大屏（移动）：支持将大屏（移动）组件复制到大屏（移动）、大屏、仪表板
        iii. 仪表板：支持复制仪表板组件到仪表板、大屏、大屏（移动）
        iv. 新移动：支持复制新移动组件至新移动
        :return:
        """
        source_metadata = self.get_source_metadata()
        original_target_metadata = self.get_original_target_metadata()
        source_layout = self._get_complete_layout(source_metadata)
        target_layout = self._get_complete_layout(original_target_metadata)
        # 新移动
        result, msg = self._check_new_mobile(source_layout, target_layout)
        if not result:
            return result, msg
        # 仪表板
        result, msg = self._check_grid(source_layout, target_layout)
        if not result:
            return result, msg
        # 大屏移动
        result, msg = self._check_old_mobile_free(source_layout, target_layout)
        if not result:
            return result, msg
        # 大屏
        result, msg = self._check_free(source_layout, target_layout)
        if not result:
            return result, msg
        return True, ''

    def _check_new_mobile(self, source_layout: dict, target_layout: dict) -> Tuple[bool, str]:
        if (
            source_layout.get("terninal_type") == "mobile_screen"
            and target_layout.get("terninal_type") != "mobile_screen"
        ):
            return False, f"源报告 {self.source_dashboard_name} 单图只能复制至移动端类型报告！"
        return True, ""

    def _check_old_mobile_free(self, source_layout: dict, target_layout: dict) -> Tuple[bool, str]:
        if source_layout.get("platform") == "mobile":
            if target_layout.get("mode") == "grid":
                return True, ''
            if target_layout.get("mode") == "free":
                return True, ''
            return False, f"源报告 {self.source_dashboard_name} 单图只能复制至仪表板/大屏/大屏(移动)类型报告！"
        return True, ''

    def _check_grid(self, source_layout: dict, target_layout: dict) -> Tuple[bool, str]:
        if source_layout.get("mode") == "grid":
            if target_layout.get("mode") == "grid":
                return True, ''
            if target_layout.get("mode") == "free":
                return True, ''
            return False, f"源报告 {self.source_dashboard_name} 单图只能复制至仪表板/大屏/大屏(移动)类型报告！"
        return True, ''

    def _check_free(self, source_layout: dict, target_layout: dict) -> Tuple[bool, str]:
        if source_layout.get("mode") == 'free':
            if target_layout.get("mode") == "grid":
                return True, ''
            if target_layout.get("mode") == "free":
                return True, ''
            return False, f"源报告 {self.source_dashboard_name} 单图只能复制至仪表板/大屏/大屏(移动)类型报告！"
        return True, ''

    def _get_dmp_group_chart_nodes(self, source_dashboard_metadata, chart_node_map: dict) -> dict:
        """
        处理dmp的群组功能的后端复制功能
        根据群组id复制整个相关联的群组所有组件
        """
        source_chart = self.source_charts_map.get(self.source_chart_id) or {}
        chart_component_code = source_chart.get('chart_component_code')
        if not source_chart or chart_component_code != 'dmp_group':
            return chart_node_map

        # 寻找以这个群组下面相关联的所有组件
        result = []
        self.__get_dmp_group_relation_chart(self.source_chart_id, result)
        if not result:
            return chart_node_map

        result.insert(0, source_chart)  # 入口的群组id也要复制一个
        for chart in result:
            chart_id = chart.get('id')
            chart_node = self._generate_chart_node(chart_id,'', '', 0)
            if chart_id not in chart_node_map:
                chart_node_map[chart_id] = chart_node
                new_id = chart_node.get_id()
                self.replace_map[chart_id] = new_id  # 添加替换处理映射
        return chart_node_map

    def __get_dmp_group_relation_chart(self, chart_group_id, result: list):
        """
        递归查找与群组id有关系的所有组件id
        """
        for chart in self.source_charts_map.values():
            chart_id = chart.get('id')
            position = chart.get('position') or {}
            group_id = position.get('group_id')
            if not group_id:
                continue
            if chart_group_id == group_id:
                result.append(chart)
                # 再往下找以这个组件为group_id的组件
                self.__get_dmp_group_relation_chart(chart_id, result)

    def _get_position_chart_nodes(self, chart_node_map: dict) -> dict:
        source_chart = self.source_charts_map.get(self.source_chart_id) or {}
        if not source_chart:
            return chart_node_map
        all_chart_ids = []
        all_chart_ids = self.__get_position_children_chart(source_chart, all_chart_ids)
        if not all_chart_ids:
            return chart_node_map
        all_chart_ids.insert(0, self.source_chart_id)
        for chart_id in all_chart_ids:
            chart_node = self._generate_chart_node(chart_id, '', '', 0)
            if chart_id not in chart_node_map:
                chart_node_map[chart_id] = chart_node
        return chart_node_map

    def __get_position_children_chart(self, source_chart, child_chart_ids: list):
        """
        递归查找与群组id有关系的所有组件id
        """
        child_chart_ids = child_chart_ids if child_chart_ids else []
        child_ids = source_chart.get('position', {}).get('children') or []
        for child in child_ids:
            child_chart_ids.append(child)
            child_chart = self.source_charts_map.get(child)
            if child_chart and child_chart.get('position', {}).get('children'):
                child_chart_ids += self.__get_position_children_chart(child_chart, child_chart_ids)
        return list(set(child_chart_ids))

    def _get_penetrates_chart_nodes(self, source_dashboard_metadata, chart_node_map: dict) -> dict:
        """
        获取报告的所有单图节点
        对于有穿透的需要获取整个穿透链条
        :return:
        """
        source_chart = self.source_charts_map.get(self.source_chart_id) or {}
        all_chart_ids = []
        all_chart_ids = self.__get_position_children_chart(source_chart, all_chart_ids)
        all_chart_ids.insert(0, self.source_chart_id)
        penetrates = self.get_data_by_path("first_report.chart_relations.penetrates", source_dashboard_metadata) or []
        relations = self._get_sorted_relations(
            self._get_penetrate_relations(all_chart_ids, copy.deepcopy(penetrates), []), ""
        )
        # 若有穿透则整个链条中的图都需要复制
        z_index_step = 0
        for relation in relations:
            parent_id = relation[0]
            chart_id = relation[1]
            chart_node = self._generate_chart_node(
                chart_id, chart_node_map[parent_id].get_id() if parent_id else '', '', z_index_step
            )
            # 若穿透关系包含在容器内, 不需要重复添加节点
            if chart_id not in chart_node_map:
                chart_node_map[chart_id] = chart_node
            z_index_step += 1
        return chart_node_map

    def _get_sorted_relations(self, relations: List, parent_id: str = None) -> List:
        sorted_relations = []
        for idx, relation in enumerate(relations):
            now_parent_id = relation[0]
            now_child_id = relation[1]
            if now_parent_id == parent_id:
                sorted_relations.append(relation)
                sorted_relations.extend(
                    self._get_sorted_relations([*relations[0:idx], *relations[idx + 1 :]], now_child_id)
                )
        return sorted_relations

    def _get_penetrate_relations(self, chart_ids: list, penetrates: List, relations: List):
        """
        获取完整的穿透关系
        :param chart_ids:
        :param penetrates:
        :param relations:
        :return:
        """
        for idx, penetrate in enumerate(penetrates):
            current_parent_id = penetrate.get("parent_id")
            current_chart_id = penetrate.get("chart_id")
            # 如果单图为当前单图则向父级寻源
            if current_chart_id in chart_ids:
                relations.append([current_parent_id, current_chart_id])
                if current_parent_id:
                    relations = self._get_penetrate_relations(
                        current_parent_id, [*penetrates[0:idx], *penetrates[idx + 1 :]], relations
                    )
            # 如果单图为当前单图父级则向下寻源
            if current_parent_id in chart_ids:
                relations.append([current_parent_id, current_chart_id])
                relations = self._get_penetrate_relations(
                    current_chart_id, [*penetrates[0:idx], *penetrates[idx + 1 :]], relations
                )
        return relations

    def _generate_chart_node(
        self, source_chart_id: str, parent_id: str, container_id: str, z_index_step: int = 0
    ) -> ChartNode:
        """
        生成chart_node节点
        :param source_chart_id:
        :return:
        """
        # 构建参数
        chart_data_dict = {
            'source_dashboard_id': self.source_dashboard_id,
            'source_chart_id': source_chart_id,
            'target_dashboard_id': self.target_dashboard_id,
            'source_dashboard_metadata': self.get_source_metadata(),
            'userid': self.userid,
            'account': self.account,
            'z_index_step': z_index_step,
            'target_dashboard_metadata': self.get_original_target_metadata(),
            'target_parent_chart_id': parent_id,
            'container_id': container_id,
        }
        chart_node = ChartNode(ReproducerChartNodeDataModel(**chart_data_dict))
        # 增加各个节点
        chart_node.add_chart_leaf_nodes()
        return chart_node

    def add_chart_nodes(self) -> ChartNode:
        # 各个单图节点
        target_chart_node, chart_nodes = self._generate_dashboard_chart_nodes()
        # 增加单图节点
        for chart_node in chart_nodes:
            self.add_node(chart_node)
        return target_chart_node

    def _generate_dashboard_chart_nodes(self):
        chart_node_map = {}
        source_dashboard_metadata = self.get_source_metadata()
        chart_node_map = self._generate_container_chat_nodes(source_dashboard_metadata, chart_node_map)
        chart_node_map = self._get_penetrates_chart_nodes(source_dashboard_metadata, chart_node_map)
        chart_node_map = self._get_dmp_group_chart_nodes(source_dashboard_metadata, chart_node_map)
        chart_node_map = self._get_position_chart_nodes(chart_node_map)

        if self.source_chart_id not in chart_node_map:
            chart_node_map[self.source_chart_id] = self._generate_chart_node(self.source_chart_id, '', '')

        all_chart_ids = list(chart_node_map.keys()) or []
        for chart_id, chart_node in chart_node_map.items():
            chart_node.set_copy_source_chart(all_chart_ids)
            self.replace_map[chart_id] = chart_node.get_id()  # 添加替换处理映射
        return chart_node_map[self.source_chart_id], [chart_node for _, chart_node in chart_node_map.items()]

    def _generate_container_chat_nodes(self, source_dashboard_metadata, chart_node_map: dict) -> dict:
        charts = self.get_data_by_path("first_report.charts", source_dashboard_metadata) or []
        parent_children_map = self._get_parent_children_map(charts)
        child_parent_map = self._get_children_parent_map(charts)
        parent_chart_id = child_parent_map.get(self.source_chart_id, '')
        # 需要复制的单图为容器组件的情况
        if self.source_chart_id in parent_children_map and parent_children_map[self.source_chart_id]:
            # 先生成容器节点，以便子组件获取新的容器id
            chart_node_map[self.source_chart_id] = self._generate_chart_node(self.source_chart_id, '', '')
            for chart_id in parent_children_map[self.source_chart_id]:
                chart_node = self._generate_chart_node(chart_id, '', chart_node_map[self.source_chart_id].get_id())
                chart_node_map[chart_id] = chart_node
                chart_node_map[self.source_chart_id].children_chart_ids.append(chart_node.get_id())
                chart_node_map[self.source_chart_id].source_children_chart_ids.append(chart_id)

        # 需要复制的单图为子组件的情况, 这里不用elif, 可能单图既是容器也是子组件
        if parent_chart_id and parent_chart_id in parent_children_map and parent_children_map[parent_chart_id]:
            chart_node_map[parent_chart_id] = self._generate_chart_node(parent_chart_id, '', '')
            for chart_id in parent_children_map[parent_chart_id]:
                chart_node = self._generate_chart_node(chart_id, '', chart_node_map[parent_chart_id].get_id())
                chart_node_map[chart_id] = chart_node
                chart_node_map[parent_chart_id].children_chart_ids.append(chart_node.get_id())
                chart_node_map[parent_chart_id].source_children_chart_ids.append(chart_id)

        return chart_node_map

    @staticmethod
    def _get_parent_children_map(charts):
        return {chart.get('id'): chart.get('children_chart_ids') for chart in charts}

    @staticmethod
    def _get_children_parent_map(charts):
        return {chart.get('id'): chart.get('parent_chart_id') for chart in charts}

    @property
    def source_charts_map(self):
        data = getattr(self, '__charts_map__', None)
        if data is None:
            source_dashboard_metadata = self.get_source_metadata()
            charts = self.get_data_by_path("first_report.charts", source_dashboard_metadata) or []
            return {chart.get('id'): chart for chart in charts}
        else:
            return data
