#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
released api route
"""

# ----------------  标准模块 ----------------
import json
import logging
import datetime
import os

# pylint: disable=E0401
import hug

from PIL import Image

# import app_celery
from base.enums import DashboardTypeAccessReleased
from components.dmark import DMark
from components.grayscale import set_grayscale_project
from components.utils import system_arch
from dashboard_chart.models import ChartDataModel

# ---------------- 业务模块 ----------------
from dashboard_chart.services.dashboard_service import get_dashboard_info, request_args_setting
from dashboard_chart.services.metadata_service import set_user_active_time
from dashboard_chart.services.third_party_service import ThirdPartyService
from dataset.services import dataset_var_service
from dmplib.hug import APIWrapper
from dmplib.hug import g
from dmplib import config
from dashboard_chart.services import (
    released_dashboard_service,
    metadata_service,
    authority_service,
    chart_service,
    dashboard_service,
    chart_model_adapter
)
from dashboard_chart.services.download import download_task_generator
from dashboard_chart.services.released_dashboard_service import get_download_flag
from dmplib.utils.errors import UserError
from dmplib.saas.project import set_correct_project_code
from components.versioned_query import (
    versioned_query_support,
    current_request_is_enable_versioned,
    cached as snapshot_cached
)

# pylint: disable=E0611
from hug.routing import URLRouter
import falcon
from hug import redirect
from dmplib.utils.jwt_login import LoginToken
from user.services.user_service import set_login_status
from dashboard_snapshot.services import dashboard_snapshot_service
from components.analysis_time import AnalysisTimeUtils
from dashboard_chart.utils.decorators import data_of_last_version

logger = logging.getLogger(__name__)


class ProxyAPIWrapper(APIWrapper):
    __slots__ = ["_route", "_released_route", "_data_route"]

    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._released_route = None
        self._route = hug.http(api=self.api)
        self._data_route = None
        self.api.http.base_url = "/dashboard_chart/released"

    @property
    def released_route(self) -> URLRouter:
        if not self._released_route:
            # pylint: disable=E1120
            self._released_route = hug.http(api=self.api, requires=authority_service.verify_released_handle(None))
        return self._released_route

    @property
    def data_route(self) -> URLRouter:
        if not self._data_route:
            # pylint: disable=E1120
            self._data_route = hug.http(api=self.api, requires=authority_service.verify_data_handle(None))
        return self._data_route


api = ProxyAPIWrapper(__name__)


def set_data_route_code(kwargs, request):
    """
    设置data_route的租户信息
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)


@api.data_route.get("/project/info")
def query_project(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/project 获取发布后单图数据结果集
    @apiParam formData {string}  code租户code
    @apiParam formData {string}  chart_params json格式化的报告查询条件
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    code = kwargs.get("code")
    if not code:
        raise UserError(message="缺少code参数")
    g.code = set_correct_project_code(code, request)
    project = released_dashboard_service.query_project_info()
    env = {
        'dmp_env_sign': project.get('dmp_env_sign'),
        'site_icon': project.get('site_icon'),
        'site_title': project.get('site_title'),
        'portal_logo': project.get('portal_logo'),
        'small_logo': project.get('small_logo'),
        'logo_uri': project.get('logo_uri')
    }
    return True, "", env


@api.data_route.post("/chart/data")
# pylint: disable=W0613
@data_of_last_version()
@versioned_query_support
@request_args_setting
def released_chart_data(request, response, *args, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/chart/data 获取发布后单图数据结果集
    @apiParam formData {string}  code租户code
    @apiParam formData {string}  chart_params json格式化的报告查询条件
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    AnalysisTimeUtils.recode_time_node('请求开始')
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    chart_params = kwargs.get("chart_params")
    results = released_dashboard_service.batch_get_released_chart_result(chart_params)
    AnalysisTimeUtils.recode_time_node('请求结束')
    return True, "", results


@api.data_route.post("/chart/get_data")
# pylint: disable=W0613
@versioned_query_support
def released_chart_get_data(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/chart/get_data 获取发布后动态获取单图数据结果集
    @apiParam formData {string}  code 租户code
    @apiParam formData {string}  dashboard_id 报告id
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    model = ChartDataModel(**kwargs)
    model.bootstrap_flag = True
    chart_model_adapter.adapt_get_data_model(model)
    result = released_dashboard_service.get_released_chart_result(model)
    return True, "", result


@api.data_route.post("/chart/get_total")
@data_of_last_version(mode='total')
@versioned_query_support
@request_args_setting
def released_chart_get_total(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/chart/get_total 获取总数
    @apiParam formData {string}  code 租户code
    @apiParam formData {string}  dashboard_id 报告id
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    model = ChartDataModel(**kwargs)
    model.bootstrap_flag = True
    return True, "", released_dashboard_service.get_released_chart_total(model)


@api.data_route.post("/chart/item_list")
def released_chart_get_item_list(request, **kwargs):
    """
    获取图表数据
    :param kwargs:
    :return:
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    model = ChartDataModel(**kwargs)
    result = released_dashboard_service.get_item_list(model)
    return True, result.get("msg", ""), result


# 旧接口
# @api.released_route.get("/v2/screens_metadata")
# def get_screens_metadata_v2(**kwargs):
#     """
#     /**
#     @apiVersion 1.0.0
#     @api {get} /api/released_dashboard_chart/v2/screens_metadata 获取多屏元数据
#     @apiParam query {string}  id 快照ID
#     @apiParam query {string}  version 元数据缓存版本号
#     @apiGroup  released_dashboard_chart
#     @apiResponse  200 {
#         "msg": "",
#         "result": true,
#         "data": {}
#     }
#     **/
#     """
#     snapshot_id = kwargs.get("id", "")
#     version = kwargs.get("version", "")
#
#     if not snapshot_id:
#         raise UserError(message="缺少报告ID")
#
#     data = {"version": version, "meta": None, "version_check_flag": MetaDataVersionCheckFlag.Valid.value}
#     cached_version = metadata_service.get_cache_version(snapshot_id=snapshot_id)
#     if version and version == cached_version:
#         return True, "数据没有变化", data
#
#     msg, meta, meta_cache_version = metadata_service.get_screens_release_metadata_v2(snapshot_id=snapshot_id)
#     cached_version = meta_cache_version if meta_cache_version else ""
#     data = {"version": cached_version, "meta": meta, "version_check_flag": MetaDataVersionCheckFlag.Invalid.value}
#
#     return True, msg, data


# 旧接口
# @api.admin_route.get("/v2/dashboard_metadata")
# def get_dashboard_metadata_v2(**kwargs):
#     """
#     /**
#     @apiVersion 1.0.0
#     @api {get} /api/released_dashboard_chart/v2/dashboard_metadata 获取单个报告元数据
#     @apiParam query {string}  id 报告id
#     @apiParam query {string}  snapshot_id 快照id
#     @apiParam query {string}  code 租户code
#     @apiParam query {string}  version 元数据缓存版本号
#     @apiGroup  released_dashboard_chart
#     @apiResponse  200 {
#         "msg": "",
#         "result": true,
#         "data": {}
#     }
#     **/
#     """
#     version = kwargs.get("version", "")
#     # 设置code
#     tenant_code = kwargs.get("code", "")
#     setattr(g, "code", tenant_code)
#
#     dashboard_id = kwargs.get("id", "")
#     snapshot_id = kwargs.get("snapshot_id", "")
#     if not dashboard_id:
#         raise UserError(message="缺少报告ID")
#     if not snapshot_id:
#         raise UserError(message="缺少快照ID")
#
#     data = {"version": version, "meta": None, "version_check_flag": MetaDataVersionCheckFlag.Valid.value}
#     cached_version = metadata_service.get_cache_version(snapshot_id=snapshot_id)
#     if version and version == cached_version:
#         return True, "数据没有变化", data
#     msg, result, meta_cache_version = metadata_service.get_dashboard_release_metadata_v2(
#         dashboard_id=dashboard_id, snapshot_id=snapshot_id
#     )
#     cached_version = meta_cache_version if meta_cache_version else ""
#     data = {"version": cached_version, "meta": result, "version_check_flag": MetaDataVersionCheckFlag.Invalid.value}
#     return True, msg, data

def is_public_share(snapshot_id):
    dashboard = get_dashboard_info(snapshot_id) or {}
    if dashboard.get('type_access_released') == DashboardTypeAccessReleased.NoLimited.value:
        return True
    return False


def is_public_share(snapshot_id):
    dashboard = get_dashboard_info(snapshot_id) or {}
    if dashboard.get('type_access_released') == DashboardTypeAccessReleased.NoLimited.value:
        return True
    return False


@api.released_route.get("/v2/screens_metadata")
@request_args_setting
@chart_service.return_dashboard_chart_config
@versioned_query_support
def get_screens_metadata_v2(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/released_dashboard_chart/v2/screens_metadata 获取多屏元数据
    @apiParam query {string}  id 快照ID
    @apiParam query {string}  version 元数据缓存版本号
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    code = request.get_param('code')
    if hasattr(g, "code"):
        code = g.code
    else:
        code = set_correct_project_code(code, request)
    set_grayscale_project(request, response, code)
    set_user_active_time(code)
    snapshot_id = kwargs.get("id", "")
    if not snapshot_id:
        raise UserError(message="缺少报告ID")

    try:
        token = request.cookies.get('token')  # 获取token，用于校验
        check_flag, errmsg, token_data = dashboard_service.check_token(token)
        if not check_flag:
            raise UserError(message=errmsg)
    except Exception as e:
        # 公共分享首次访问拿不到token
        if is_public_share(snapshot_id):
            token_data = {}
        else:
            raise UserError(code=500, message=str(e))

    flag = get_download_flag(token_data, snapshot_id, kwargs)
    download_flag = int(flag)

    is_arm = system_arch()

    if current_request_is_enable_versioned() and snapshot_cached.has_snapshot(g.snap_id):
        # 返回元数据的版本数据
        snapshot_meta = released_dashboard_service.get_snapshot_metadata_by_id(g.snap_id, snapshot_id)
        snapshot_meta = snapshot_meta.get('metadata', '')
        if snapshot_meta:
            return True, 'ok', {"meta": json.loads(snapshot_meta), "download": flag, "meta_version": g.snap_id, "is_arm": is_arm}

    # 对比ETag
    req_etag = request.headers.get("IF-NONE-MATCH")
    if config.get('OSS_Config.is_private') != 1 and metadata_service.compare_metadata_etag(req_etag, snapshot_id, download_flag=download_flag):
        # 没有更新数据，设置HTTP_CODE=304
        response.status = falcon.HTTP_304
        return True, "数据没有变化", None

    # 获取报告元数据
    msg, meta = metadata_service.get_screens_release_metadata_v2(snapshot_id=snapshot_id, token_data=token_data)

    # 设置header头信息的ETag
    new_etag = metadata_service.get_metadata_etag(snapshot_id, download_flag=download_flag)
    meta_304(response, new_etag)

    return True, msg, {"meta": meta, "download": flag, "meta_version": new_etag, "is_arm": is_arm}


@api.released_route.get("/v2/dashboard_metadata")
def get_dashboard_metadata_v2(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/released_dashboard_chart/v2/dashboard_metadata 获取单个报告元数据
    @apiParam query {string}  id 报告id
    @apiParam query {string}  snapshot_id 快照id
    @apiParam query {string}  code 租户code
    @apiParam query {string}  version 元数据缓存版本号
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    # 设置code
    tenant_code = kwargs.get("code") or kwargs.get("tenant_code") or ""
    if not tenant_code:
        raise UserError(message="缺少企业代码")
    setattr(g, "code", tenant_code)

    dashboard_id = kwargs.get("id", "")
    snapshot_id = kwargs.get("snapshot_id", "")
    if not dashboard_id:
        raise UserError(message="缺少报告ID")
    if not snapshot_id:
        raise UserError(message="缺少快照ID")

    # 对比ETag
    req_etag = request.headers.get("IF-NONE-MATCH")
    if metadata_service.compare_metadata_etag(req_etag, snapshot_id, download_flag=0):
        # 没有更新的数据，设置HTTP_CODE=304
        response.status = falcon.HTTP_304
        return True, "数据没有变化", None

    # 获取报告元数据
    msg, meta = metadata_service.get_dashboard_release_metadata_v2(dashboard_id=dashboard_id, snapshot_id=snapshot_id)

    # 设置header头信息的ETag
    new_etag = metadata_service.get_metadata_etag(snapshot_id, download_flag=0)
    meta_304(response, new_etag)
    return True, msg, {"meta": meta, "meta_version": new_etag}


@api.data_route.post('/download/export_chart_data')
@versioned_query_support
def export_chart_data(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/download/export_chart_data 发布页表格数据下载
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    token = request.cookies.get('token')
    kwargs = released_dashboard_service.assign_params_for_download(**kwargs)
    status, msg, result = download_task_generator.generate_download_task(token, kwargs)
    if status:
        download_task_generator.export_record_log(request, kwargs, True)
    return status, msg, result


# pylint: disable=I1101
@api.data_route.get('/mark_image', output=hug.output_format.image('png'))
def dashboard_chart_mark_image(request, response, **kwargs):
    """
    /*
      @apiVersion 1.0.7
      @api {post} /api/released_dashboard_chart/mark_image 报告单图中水印文字背景图片生成接口
      @apiGroup api
      @apiResponse 200
    */
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    options = released_dashboard_service.get_mark_image_options_by_params(kwargs.get("layout"), kwargs.get("platform"))
    ttf_file_path = os.path.join(os.environ.get('DMP_ROOT_PATH', '/home/<USER>/webapp'), "Arial-Unicode-Bold.ttf")
    options["font"] = ttf_file_path if ttf_file_path else "DejaVuSansMono-Bold.ttf"

    # 获取文本信息
    text = released_dashboard_service.get_mark_image_text(request, kwargs)
    img_path = DMark(text, options).png()
    img = Image.open(img_path)
    response.set_header('Cache-Control', 'max-age=15552000')
    return img


# pylint: disable=I1101
@api.data_route.get('/mark_dark_image', output=hug.output_format.image('png'))
def dashboard_chart_mark_dark_image(request, response, **kwargs):
    """
    /*
      @apiVersion 1.0.0
      @api {post} /api/released_dashboard_chart/mark_dark_image 报告单图中水印文字深色背景图片生成接口
      @apiGroup api
      @apiResponse 200
    */
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    options = released_dashboard_service.get_mark_image_options_by_params(kwargs.get("layout"), kwargs.get("platform"))
    ttf_file_path = os.path.join(os.environ.get('DMP_ROOT_PATH', '/home/<USER>/webapp'), "Arial-Unicode-Bold.ttf")
    options["font"] = ttf_file_path if ttf_file_path else "DejaVuSansMono-Bold.ttf"
    options["color"] = "#8995b0"
    options["alpha"] = 0.6

    # 获取文本信息
    text = released_dashboard_service.get_mark_image_text(request, kwargs)
    img_path = DMark(text, options).png()
    img = Image.open(img_path)
    response.set_header('Cache-Control', 'max-age=15552000')
    return img


@api.data_route.get('/external_token')
def get_external_token(request, **kwargs):
    """
    获取对外token 用于dmp跳转到外部页面进行认证
    :return:
    """
    set_data_route_code(kwargs, request)
    return True, '', {'token': dashboard_service.get_external_token()}


@api.released_route.get('/check')
@api.released_route.post('/check')
def check_password():
    """
    检查权限、密码接口
    :param request:
    :param kwargs:
    :return:
    """
    return True, '检查成功', {}


@api.data_route.get('/filter/info')
@versioned_query_support
def get_dashboard_filter_info(request, response, **kwargs):
    """房
    获取报告筛选信息
    :param request:
    :param kwargs:
    :return:
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    dashboard_filter_ids = kwargs.get('dashboard_filter_id')
    return True, '', dashboard_service.get_dashboard_filter_info(dashboard_filter_ids)


@api.data_route.get('/released_on')
def get_dashboard_release_info(request, **kwargs):
    """
    获取报告元数据相关信息
    :param request:
    :param kwargs:
    :return:
    """
    set_data_route_code(kwargs, request)
    dashboard_id = kwargs.get('id', '')
    if not dashboard_id:
        raise UserError(message="缺少报告ID")
    token = request.cookies.get('token')  # 获取token，用于校验
    check_flag, errmsg, token_data = dashboard_service.check_token(token)
    if not check_flag:
        raise UserError(message=errmsg)
    flag = get_download_flag(token_data, dashboard_id, kwargs)
    req_meta_version = kwargs.get("meta_version")
    result = released_dashboard_service.get_dashboard_release_info(
        dashboard_id, req_meta_version, download_flag=int(flag)
    )
    return True, '获取成功', result


@api.data_route.post('/dataset_var/batch_get_default_values')
def batch_get_dataset_var_default_values(request, **kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/released_dashboard_chart/dataset_var/batch_get_default_values 批量获取变量默认值
     @apiGroup  dataset
     @apiBodyParam {
     "dataset_vars": ["aaa", "bbb"]
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": {
            "id":"value"
         }
    }
     */
     """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    result = dataset_var_service.batch_get_dataset_var_default_values(
        kwargs.get("dataset_vars"), kwargs.get("dashboard_id")
    )
    result = dict((key, json.dumps(val)) for key, val in result.items())
    return True, '获取成功', result


def meta_304(response, etag: str):
    """
    发送etag的header,如果是ie11会缓存请求，必须增加额外几个header ie11下才会正常
    参考：https://stackoverflow.com/questions/24604921/ie-9-ignores-etag-value-change-and-returns-cached-response
    """
    response.set_header("ETAG", etag)
    response.set_header('Expires', '-1')
    response.set_header('Cache-Control', 'must-revalidate, private')
    now = datetime.datetime.now()
    # 东8区转gmt时间，要减8小时
    gmt = now - datetime.timedelta(seconds=3600 * 8)
    response.set_header('Last-Modified', gmt.strftime('%a, %d %b %Y %H:%M:%S GMT'))


@api.route.get("/view/{dashboard_id}")
def released_chart_view(request, response, dashboard_id, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/{dashboard_id}获取发布后单图数据结果
    @apiGroup  released_dashboard_chart

    https://dmp-test.mypaas.com.cn/dataview/share/39f6f42e-16ec-8130-513d-cddacbde08c2?code=test
    https://dmp-test.mypaas.com.cn/dataview/share/39f6f42d-76e7-66de-3026-e43d09f46ddb?code=test
    https://dmp-test.mypaas.com.cn/dataview-mobile/view/39f6f42c-7131-91b1-4848-c77c6e06f07d?code=test
    **/
    """
    token = kwargs.get('token', '')
    code = kwargs.get('code', '')
    data = LoginToken().verify_token(token)
    if not data:
        raise UserError(code=401, message="token无效")
    tenant_code = data.get("code", code)
    g.code = tenant_code
    account = data.get("account", '')
    userid = data.get("id", '')
    group_ids = data.get('group_ids', '')
    customize_roles = data.get('customize_roles', [])
    domain = request.host

    set_login_status(response, domain, tenant_code, userid, account, group_ids, **{'customize_roles': customize_roles})

    # pylint: disable=I1101
    return redirect.to(released_dashboard_service.get_dashboard_release_url(dashboard_id), falcon.HTTP_302)


@api.data_route.get('/get_erp_image_by_id', output=hug.output_format.image('png'))
def get_erp_image_by_id(request, **kwargs):
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    image_id = kwargs.get('image_id', '')
    dataset_id = kwargs.get('dataset_id', '')
    if not image_id:
        raise UserError(message='图片的guid不能为空')
    image = dashboard_service.get_erp_image_by_id(image_id, dataset_id=dataset_id)
    return image


@api.data_route.get('/try_get_erp_image_url')
def get_erp_image_by_id(request, **kwargs):
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    image_id = kwargs.get('image_id', '')
    dataset_id = kwargs.get('dataset_id', '')
    if not image_id:
        raise UserError(message='图片的guid不能为空')
    url = dashboard_service.try_get_erp_image_url(image_id, dataset_id=dataset_id)
    return True, '', url



@api.data_route.post("/chart_config/edit")
def chart_config_edit(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/chart_config/edit 编辑运行时组件，并将设计时与运行时数据同时刷新
    @apiGroup  released_dashboard_chart
    # 来源于故事
    https://www.tapd.cn/38229611/prong/stories/view/1138229611001330485?url_cache_key=from_url_iteration_list_7920e14ccdb579fc2d8232dd91bdacbe&action_entry_type=stories
    **/
    """
    from user_log.models import UserLogModel

    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)

    chart_id = kwargs.get('chart_id', '')
    config = kwargs.get('config', '')
    update_runtime = str(kwargs.get('update_runtime', '0'))

    extra_data = released_dashboard_service.edit_runtime_chart_config(chart_id, config, update_runtime)

    if extra_data:
        dashboard_name = extra_data.get('dashboard_name', '')
        chart_name = extra_data.get('chart_name', '')
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_chart',
                'extra': json.dumps(extra_data, ensure_ascii=False),
                'content': f'修改了 [ {dashboard_name} ] 中 [ {chart_name} ] 的内容',
            },
        )

    return True, 'ok', {}


@api.data_route.get('/snapshot/list')
def get_snapshot_list(request, **kwargs):
    """
    快照列表
    """
    set_data_route_code(kwargs, request)
    dashboard_id = kwargs.get("dashboard_id")
    return True, '', dashboard_snapshot_service.get_dashboard_snapshot_record_list(dashboard_id)


@api.data_route.get('/snapshot/all_list')
def get_snapshot_all_list(request, **kwargs):
    """
    新拍照列表，展示报告和简讯产生的快照数据列表
    报告快照+简讯快照
    """
    set_data_route_code(kwargs, request)
    dashboard_id = kwargs.get("dashboard_id")
    return True, '', dashboard_snapshot_service.get_dashboard_all_snapshot_list(dashboard_id)


@api.data_route.get("/get_erp_url")
def get_erp_system_url(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/released_dashboard_chart/get_erp_url 获取erp系统的站点url接口
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": [ {
                        "url": "http://10.5.10.162:730",
                        "app_code": "0000",
                        "app_name": "系统设置"
                },{
                        "url": "http://10.5.10.162:730",
                        "app_code": "1001",
                        "app_name": "项目主数据"
                }]
    }
    **/
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    result = dashboard_service.get_erp_system_url()
    return True, "", result


@api.data_route.get("/test_mysql")
@request_args_setting
def test_mysql(request, response, *args, **kwargs):
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)

    result = released_dashboard_service.test_mysql(kwargs)
    return True, "", result


@api.data_route.post("/chart/warning")
def released_chart_warning(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/released_dashboard_chart/chart/warning 前端组件渲染错误的日志记录和预警
    @apiParam {
      "code": "uitest",
      "platform": "new_mobile",
      "new_layout_type": 0,
      "dashboard_id": "1111111111111111111",
      "dashboard_name": "报告名称22222222",
      "chart_id": "3333333333333",
      "error_msg": "这是错误信息xxxxxxxxxxxxxxxxxxxx",
      "traceback": "异常的堆栈信息",
      "extra_info": {
          "aa": "这是扩展信息"
      },
      "is_key": 1
    }
    @apiGroup  released_dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    return True, "", released_dashboard_service.chart_warning(kwargs)


@api.data_route.get("/v2/third_jump/view")
def third_jump_view_v2(request, **kwargs):
    set_data_route_code(kwargs, request)
    third_party = ThirdPartyService(request, **kwargs)
    return third_party.view()


@api.data_route.get('/dataset_field/get')
def get_dataset_field(request, **kwargs):
    from dataset.services import dataset_field_service, advanced_field_service
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    # 高级字段保存时，在带有单引号的输入框值里面强行加入了双斜杠，返回给前端时需要去除双斜杠
    dataset_fields = dataset_field_service.get_dataset_field(
        kwargs.get('dataset_id'), kwargs.get('is_not_category'), kwargs.get("include_dataset_vars")
    )
    if not dataset_fields:
        dataset_fields = []
    if not kwargs.get('is_not_category'):
        for k, v in dataset_fields.items():
            dataset_fields[k] = advanced_field_service.del_diagonal(v)
    else:
        dataset_fields = advanced_field_service.del_diagonal(dataset_fields)
    return True, '获取成功', dataset_fields


@api.data_route.post('/save/params')
def save_params(**kwargs):
    from dashboard_chart.services.external_dashboard_service import save_params
    code = kwargs.get("code")
    if not code:
        raise UserError(message="缺少code参数")
    g.code = code
    del kwargs['code']
    if not kwargs:
        return False, '保存的参数不能为空', ''
    return True, 'success', save_params(kwargs)


@api.data_route.get('/get/params')
def get_params(**kwargs):
    from dashboard_chart.services.external_dashboard_service import get_params
    code = kwargs.get("code")
    if not code:
        raise UserError(message="缺少code参数")
    g.code = code
    key = kwargs.get('params_key')
    if not key:
        raise UserError(message='缺少参数')
    return True, 'success', get_params(key)


# 根据主题ID加载左侧的树型菜单
@api.data_route.post('/get_subject_detail')
def get_subject_detail(request, **kwargs):
    from self_service.services.external_subject_service import get_subject_fields
    code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""
    g.code = set_correct_project_code(code, request)
    dataset_id = kwargs.get('dataset_id')
    main_external_subject_id = kwargs.get('main_external_subject_id')
    detail = kwargs.get('get_child_report_fields') or False
    return True, '', get_subject_fields(dataset_id, main_external_subject_id, detail)

