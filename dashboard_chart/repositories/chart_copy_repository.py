#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/1 16:54
# <AUTHOR> caoxl
# @File     : chart_copy_repository.py
from typing import Dict, List

from base import repository
from base.enums import ChartCopyTaskStatus
from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id


def get_simple_chart_copy_transaction(transaction_id: str):
    return repository.get_data("dashboard_chart_copy_transaction", {"id": transaction_id})


def add_chart_copy_transaction(transaction_data: Dict, target_dashboard_ids: List):
    tid = seq_id()
    transaction_data["id"] = tid
    with get_db() as db:
        try:
            db.begin_transaction()
            db.insert("dashboard_chart_copy_transaction", transaction_data)
            for target_dashboard_id in target_dashboard_ids:
                task_data = {
                    "id": seq_id(),
                    "tid": tid,
                    "target_dashboard_id": target_dashboard_id,
                    "target_chart_id": "",
                    "status": Chart<PERSON>opyTaskStatus.Waiting.value,
                }
                db.insert("dashboard_chart_copy_task", task_data)
            db.commit()
            return True, tid
        except Exception as e:
            db.rollback()
            return False, f"插入事务数据失败，错误信息 {str(e)}"


def get_chart_copy_transaction(transaction_id: str):
    with get_db() as db:
        sql = (
            "SELECT `id`,`source_dashboard_id`,`source_chart_id`,`start_time`,`end_time`,`status` "
            "FROM dashboard_chart_copy_transaction WHERE `id`=%(transaction_id)s"
        )
        params = {"transaction_id": transaction_id}
        transaction = db.query(sql, params)
        if not transaction:
            return False, {}
        transaction = transaction[0]
        sql = (
            "SELECT `id`, `tid`, `target_dashboard_id`, `target_chart_id`, `start_time`, `end_time`, "
            "`status`, `messages` "
            "FROM `dashboard_chart_copy_task` WHERE `tid`=%(transaction_id)s"
        )
        tasks = db.query(sql, params)
        transaction["tasks"] = tasks or []
        return True, transaction


def update_chart_copy_transaction(transaction_id: str, data: Dict):
    return repository.update("dashboard_chart_copy_transaction", data, {"id": transaction_id})


def update_chart_copy_task(task_id: str, data: Dict):
    return repository.update("dashboard_chart_copy_task", data, {"id": task_id})
