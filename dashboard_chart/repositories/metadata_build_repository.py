#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
repository
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from base import repository
from dmplib.saas.project import get_db


def get_table_fields(table):
    """
    获取表字段
    :param table:
    :return:
    """
    sql = f"""SHOW COLUMNS FROM {table} """
    with get_db() as db:
        cur = db._execute(sql)
        return cur.fetchall()


def get_table_data_by_id(dashboard_id, table_name, fields, order_by):
    """
    根据报告id获取表数据
    :param dashboard_id:
    :param table_name:
    :param fields:
    :param order_by:
    :return:
    """
    return repository.get_data(
        table_name, conditions={"id": dashboard_id}, fields=fields, order_by=order_by, multi_row=True
    )


def get_table_data_by_dashboard_id(dashboard_id, table_name, fields, order_by):
    """
    根据报告id获取表数据
    :param dashboard_id:
    :param table_name:
    :param fields:
    :param order_by:
    :return:
    """
    return repository.get_data(
        table_name, conditions={"dashboard_id": dashboard_id}, fields=fields, order_by=order_by, multi_row=True
    )
