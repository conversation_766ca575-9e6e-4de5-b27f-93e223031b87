#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> 2018/9/11
"""
import json

from dmplib.saas.project import get_db
from datetime import datetime, timedelta
from base.enums import DataReportType, DashboardCreateType, DashboardNewLayoutType, DashboardJumpType, DashboardType
from base import repository
from dmplib.utils.errors import UserError
from dmplib.hug import g
from typing import Dict, Tuple, Union


def get_chart_code_and_type_code_by_chart_id(chart_id):
    sql = """
    SELECT dc.chart_code, c.data_logic_type_code
    FROM component as c
    LEFT JOIN dashboard_chart dc ON dc.chart_code=c.package
    WHERE dc.id=%(chart_id)s"""
    params = {'chart_id': chart_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dashboard_filter_dataset_fields(dashboard_id):
    """
    获取报告筛选数据集字段信息(从主数据集中获取)
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT filter.id,filter.dashboard_id,filter.operator,filter.col_value,filter.main_dataset_field_id as field_id,
    filter.main_dataset_field_id,filter.select_all_flag, filter.filter_relation
    FROM dashboard_filter filter WHERE filter.dashboard_id =%(dashboard_id)s
    """
    param = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, param)


def get_dashboard_filter_relation_dataset_fields(dashboard_id):
    """
    获取报告筛选数据集关联关系中的字段信息
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT filter.dashboard_id,filter.operator,filter.col_value,
    relation.related_dataset_field_id, relation.main_dataset_field_id,filter.select_all_flag,filter.id
    FROM dashboard_dataset_field_relation relation
    LEFT JOIN dashboard_filter filter
    ON
    relation.main_dataset_field_id=filter.main_dataset_field_id AND relation.dashboard_id=filter.dashboard_id
    WHERE
    filter.dashboard_id =%(dashboard_id)s
    """
    param = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, param)


def get_dashboard_filter_by_id(dashboard_filter_id):
    """
    通过报告筛选ID获取报告筛选信息
    :param dashboard_filter_id:
    :return:
    """
    sql = """
    SELECT dashboard_id FROM dashboard_filter WHERE id=%(dashboard_filter_id)s
    """
    params = {'dashboard_filter_id': dashboard_filter_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dash_list_by_group_id(**kwargs):
    """
    根据数据看板编码获取所有数据集
    :param status: 0: previewed, 1: released, 2: release_update
    :param end_time:
    :param start_time:
    :param str parent_id:
    :param int is_multiple_screen:
    :param create_type:
    :param new_layout_type:
    :return:
    """
    parent_id = kwargs.get("parent_id", "")
    is_multiple_screen = kwargs.get("is_multiple_screen", 0)
    status = kwargs.get("status", 0)
    start_time = kwargs.get("start_time")
    end_time = kwargs.get("end_time")
    create_type = kwargs.get("create_type")
    new_layout_type = kwargs.get("new_layout_type")
    terminal_type = kwargs.get("terminal_type", None)
    file_type = kwargs.get("file_type", "")
    application_type = kwargs.get('application_type', 0)
    include_child_file = kwargs.get('include_child_file', 0)  # 是否包含子报告， 默认不包含
    if not isinstance(application_type, list):
        application_type = [application_type]
    params = {'parent_id': parent_id, 'is_multiple_screen': is_multiple_screen, 'application_type': application_type}
    sql = """SELECT id,`name`,`type`,`platform`,`icon`,`cover`,`description`,`level_code`,`parent_id`,
          dashboard.`created_on`,dashboard.`modified_on`,`status`,dashboard.`rank`,
          dashboard.`is_multiple_screen`,dashboard.`created_by`,
          `create_type`,`new_layout_type`,
          `distribute_type`,`terminal_type`,`layout`,`application_type`,`main_external_subject_id`,`external_subject_ids`,`dataset_id`,`analysis_type`,
          `auto_snap`, `schedule_config` , `external_url`, `sort`
          FROM dashboard WHERE  dashboard.build_in = 0
          and dashboard.application_type in %(application_type)s
          and dashboard.is_multiple_screen=%(is_multiple_screen)s """

    if parent_id:
        # 只获取根节点下所有数据集对象列表
        if parent_id == 'root':
            parent_id = ''
            params['parent_id'] = parent_id
        level_code = repository.get_value("dashboard", {"id": parent_id}, ["level_code"])
        # 系统分发文件夹可能存在level_code错乱情况，仍按parent_id查询
        if level_code:
            params["level_code"] = level_code + "%"
            sql += ' and id != %(parent_id)s and dashboard.level_code like %(level_code)s '
        else:
            sql += ' and `parent_id`=%(parent_id)s '

    if start_time:
        sql += ' and modified_on >= %(start_time)s '
        params['start_time'] = start_time
    if end_time:
        sql += ' and modified_on <= %(end_time)s'
        if start_time == end_time:  # 2018-02-01 -> 2018-02-02
            end_time = datetime.strptime(end_time, '%Y-%m-%d')
            end_time = end_time + timedelta(days=1)
            end_time = end_time.strftime('%Y-%m-%d')

        params['end_time'] = end_time

    # 发布状态
    if status:
        if file_type == "folder":
            sql += ' and `status`=%(status)s '
            params['status'] = 1
        elif int(status) == 2:
            sql = sql.replace(
                'FROM dashboard',
                "FROM dashboard LEFT OUTER JOIN dashboard_extra ON dashboard.id=dashboard_extra.dashboard_id",
            )
            sql += ' and dashboard_extra.edit_on > dashboard_extra.released_on and dashboard.type=%(type)s '
        else:
            sql += ' and `status`=%(status)s  and `type`=%(type)s  '
            params['status'] = int(status)

        params['type'] = DataReportType.File.value

    # 文件类型
    if file_type:
        params['type'] = DataReportType.File.value
        if file_type == "folder":
            sql += ' and dashboard.`type`=%(type)s '
            params['type'] = DataReportType.Folder.value
        elif file_type == 'file':
            sql += ' and dashboard.`type`=%(type)s '
        # 文件类型为大屏报告
        elif file_type == "large_screen":
            sql += ' and dashboard.`platform`="pc" and dashboard.`new_layout_type`=0 and dashboard.`type`=%(type)s '
            # 文件类型为仪表板
        elif file_type == "dashboard":
            sql += ' and dashboard.`platform`="pc" and dashboard.`new_layout_type`=1 and dashboard.`type`=%(type)s '
            # 文件类型为新移动
        elif file_type == "new_mobile":
            sql += ' and dashboard.`platform`="mobile" and dashboard.`type`=%(type)s '
        elif file_type == "report_center":
            params['type'] = [DataReportType.File.value, DataReportType.CHILD_FILE.value]
            sql += ' and dashboard.`platform`="pc" and dashboard.`new_layout_type`=1 and dashboard.`type` in %(type)s '

    if int(include_child_file) == 0:
        params['child_file_type'] = DataReportType.CHILD_FILE.value
        sql += ' and dashboard.`type`!=%(child_file_type)s  '
    sql, params = _filter_by_application_type(application_type, sql, params)

    # 按需不显示新版报告
    try:
        create_type = int(create_type)
    except Exception:
        create_type = None
    if create_type in [e.value for e in DashboardCreateType.__members__.values()]:
        sql += ' and dashboard.`create_type`=%(create_type)s '
        params['create_type'] = create_type

    # 终端类型
    sql, params = _op_terminal_type(terminal_type, sql, params)

    # 支持过滤自由和固定布局类型的报告
    sql, params = _op_new_layout_type(new_layout_type, sql, params)

    sql += ' ORDER BY level_code'

    with get_db() as db:
        return db.query(sql, params)


def _filter_by_application_type(application_type, sql, params):
    if application_type == 1:  # 过滤掉用户基于模板创建的自助报告实例及对应文件夹
        user_dashboard = repository.get_data('user_dashboard', {})
        if user_dashboard:
            self_service = repository.get_data('dashboard', {'id': user_dashboard.get('id')})
            if self_service and self_service.get('level_code'):
                sql += ' and dashboard.level_code not like %(not_like_level_code)s '
                params["not_like_level_code"] = self_service.get('level_code')[0:5] + "%"
    return sql, params


def _op_terminal_type(
    terminal_type: str, sql: str, params: Dict[str, Union[int, str]]
) -> Tuple[str, Dict[str, Union[int, str]]]:
    """
    终端类型
    :param terminal_type:
    :param sql:
    :param params:
    :return:
    """
    if not sql:
        return sql, params
    if terminal_type is not None:
        sql += ' and (`terminal_type`=%(terminal_type)s OR type=%(dashboard_type)s) '
        params['terminal_type'] = terminal_type
        params['dashboard_type'] = DashboardType.Folder.value
    return sql, params


def _op_new_layout_type(
    new_layout_type: None, sql: str, params: Dict[str, Union[int, str]]
) -> Tuple[str, Dict[str, Union[int, str]]]:
    """
    支持过滤自由和固定布局类型的报告
    :param new_layout_type:
    :param sql:
    :param params:
    :return:
    """
    if not sql:
        return sql, params
    try:
        new_layout_type = int(new_layout_type)
    except Exception:
        new_layout_type = None
    if new_layout_type is not None and new_layout_type in [
        e.value for e in DashboardNewLayoutType.__members__.values()
    ]:
        sql += ' and (`new_layout_type`=%(new_layout_type)s OR type=%(dashboard_type)s) '
        params['new_layout_type'] = new_layout_type
        params['dashboard_type'] = DashboardType.Folder.value
    return sql, params


def get_dashboard(dashboard_id):
    """
    获取已授权应用看板
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT `id`,`theme`,`type`,`name`,`platform`,`user_group_id`,`default_show`,`description`,`share_secret_key`,
    `level_code`, `line_height`, `parent_id`,
    `layout`,`background`,`border`,`is_multiple_screen`, `status`, `cover`, `type_access_released`, `type_selector`,
    `biz_code`, `scale_mode`, `refresh_rate`, `grid_padding`, `create_type`, `new_layout_type`, `distribute_type`,
    `is_show_mark_img`,`terminal_type`,`application_type`,`main_external_subject_id`, `asset_id`,
    `external_subject_ids`,`is_deleted`,`dataset_id`,`smart_beauty_status`,`analysis_type`, `auto_play`, `is_key`
    FROM dashboard
    WHERE id=%(dashboard_id)s and build_in = 0
    """
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dashboard_route(level_code):
    """
    获取单个看板
    :param str level_code:
    :return:
    """
    sql = 'SELECT * FROM dashboard where `level_code`=%(level_code)s'
    params = {'level_code': level_code}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dashboard_filter_relation(dashboard_filter_id):
    """
    获取报告筛选关联表数据
    :param dashboard_filter_id:
    :return:
    """
    sql = """
    SELECT `id`,`operator`,`col_value`,`select_all_flag`
    FROM `dashboard_filter_relation` WHERE `dashboard_filter_id`=%(dashboard_filter_id)s
    """
    params = {'dashboard_filter_id': dashboard_filter_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_by_ids(ids):
    sql = """
        select * from dashboard
        where id in %(ids)s
    """
    return get_db().query(sql, {"ids": ids})


def get_jump_config_by_ids(ids):
    sql = """
        select * from dashboard_jump_config
        where id in %(ids)s
    """
    return get_db().query(sql, {"ids": ids})


def get_dashboard_filter_by_ids(ids):
    sql = """
        select * from dashboard_filter
        where id in %(ids)s
    """
    return get_db().query(sql, {"ids": ids})


def get_dashboard_var_filter_by_ids(ids):
    sql = """
            select * from dashboard_filter_vars_relation_field
            where id in %(ids)s
        """
    return get_db().query(sql, {"ids": ids})


def get_dashboard_chart_params_jump_by_dashboard_filter_ids(ids):
    sql = """
        select * from dashboard_chart_params_jump
        where dashboard_filter_id in %(ids)s
    """
    return get_db().query(sql, {"ids": ids})


def get_jump_relation_by_dashboard_filter_ids(ids):
    sql = """
        select * from dashboard_jump_relation
        where dashboard_filter_id in %(ids)s
    """
    return get_db().query(sql, {"ids": ids})


def search_dash_list(**kwargs):
    """
    根据数据看板名称搜索符合条件的所有数据集
    :return:
    """
    params = {}
    name = kwargs.get('name', '')
    status = kwargs.get('status', '')
    file_type = kwargs.get('file_type', '')
    application_type = kwargs.get('application_type', 0)
    if not isinstance(application_type, list):
        application_type = [application_type]
    if not name:
        raise UserError(message='搜索名称name不能为空')
    length = len(name)
    name_tmp = ''
    for i in range(0, length):
        name_tmp += '\\' + str(name[i])

    sql = """SELECT id,`name`,`type`,`cover`,`level_code`,`parent_id`,`created_on`,`status`,
          `created_by`,`modified_on`,`modified_by`,`platform`,`create_type`,`new_layout_type`,`distribute_type`,`terminal_type`,
          `application_type`,`main_external_subject_id`,`external_subject_ids`,`dataset_id`,`external_url`,`auto_snap`
          FROM dashboard WHERE `name` like %(name)s """

    if status:
        sql += ' and status = {} '.format(int(status))

    # 文件类型
    if file_type:
        params['type'] = DataReportType.File.value
        if file_type == "folder":
            sql += ' and dashboard.`type`=%(type)s '
            params['type'] = DataReportType.Folder.value
        elif file_type == 'file':
            sql += ' and dashboard.`type`=%(type)s '
        # 文件类型为大屏报告
        elif file_type == "large_screen":
            sql += ' and dashboard.`platform`="pc" and dashboard.`new_layout_type`=0 and dashboard.`type`=%(type)s '
            # 文件类型为仪表板
        elif file_type == "dashboard":
            sql += ' and dashboard.`platform`="pc" and dashboard.`new_layout_type`=1 and dashboard.`type`=%(type)s '
            # 文件类型为新移动
        elif file_type == "new_mobile":
            sql += ' and dashboard.`platform`="mobile" and dashboard.`type`=%(type)s '
    else:  # 不显示子报表
        params['type'] = DataReportType.CHILD_FILE.value
        sql += ' and dashboard.`type`!=%(type)s  '
    sql += (
        ' and build_in = 0 and is_multiple_screen = 0 and '
        'application_type in %(application_type)s ORDER BY type ASC ,modified_by DESC '
    )

    params['name'] = '%' + name_tmp + '%'
    params['application_type'] = application_type

    with get_db() as db:
        return db.query(sql, params)


def get_self_dashboard_list(is_search=True, is_admin=False, **kwargs):
    """
    自助报表的列表搜索
    :return:
    """
    params = dict()
    name = kwargs.get('name', '')
    status = kwargs.get('status', '')
    file_type = kwargs.get('file_type', '')
    parent_id = kwargs.get('parent_id', '')
    su_user = kwargs.get('su', '')
    with_sub = kwargs.get('with_sub', 1)
    application_type = kwargs.get('application_type', 1)

    if (not name) and is_search:
        raise UserError(message='搜索名称name不能为空!')
    name_tmp = ''
    for i in range(0,  len(name)):
        name_tmp += '\\' + str(name[i])

    sql = """SELECT id,`name`,`type`,`cover`,`level_code`,`parent_id`,`created_on`,`status`,
          `created_by`,`modified_on`,`modified_by`,`platform`,`create_type`,`new_layout_type`,`distribute_type`,`terminal_type`,
          `application_type`,`main_external_subject_id`,`external_subject_ids`,`dataset_id`,`external_url`, `sort`
          FROM dashboard WHERE 1=1"""

    if is_search:
        sql += ' and `name` like %(name)s'

    if status:
        sql += ' and status = {} '.format(int(status))

    # 如果是切换用户发过来的，只返回自己创建的报告，不管切换的用户是不是管理员
    if su_user:
        sql += ' and dashboard.`created_by` = %(created_by)s '
        params['created_by'] = su_user
    else:
        sql += ' and dashboard.`created_by` = %(created_by)s '
        params['created_by'] = g.account

    # 类型条件
    if file_type == "folder":
        sql += ' and dashboard.`type` = %(type)s '
        params['type'] = DataReportType.Folder.value
    elif file_type == "file":
        sql += ' and dashboard.`type` = %(type)s '
        params['type'] = DataReportType.File.value
    else:
        sql += ' and dashboard.`type` != %(type)s '
        params['type'] = DataReportType.CHILD_FILE.value

    # 层级条件，获取当前层级，或者所有层级下的报告
    if parent_id == 'root':
        parent_id = ''
    if str(with_sub) == '1':
        # 包含下面的所有的子文件夹
        if parent_id:
            # 非根目录
            # 所有层级下的报告（包含子层级）
            parent_level_code = repository.get_data_scalar(
                'dashboard', conditions={'id': parent_id}, col_name='level_code'
            )
            if parent_level_code:
                params['level_code'] = parent_level_code + '%'
                params['parent_id'] = parent_id
                sql += ' and id != %(parent_id)s  and dashboard.`level_code` like %(level_code)s '
        else:
            # 根目录
            pass
    else:
        # 当前层级
        params['parent_id'] = parent_id
        sql += ' and dashboard.`parent_id`=%(parent_id)s '

    # if (
    #     is_admin
    #     and getattr(g, 'request_data', {}).get('cookies', {}).get('account') == g.account
    # ):
    #     # 管理员查看自己文件夹时候，如果某个文件夹不是自己创建的，但是下面有自己的文件。这个时候展示别人的文件夹
    #     if file_type == 'folder':
    #         sql += """ and ( dashboard.created_by =  %(account)s or  (SELECT id from dashboard as idd WHERE idd.`type` = 'FILE' and idd.application_type =  %(application_type)s  and dashboard.id = idd.parent_id
    # AND idd.created_by = %(account)s limit 1) is not null )"""
    #     else:
    #         sql += " and dashboard.created_by =  %(account)s"
    #     params['account'] = g.account

    sql += (
        ' and build_in = 0 and is_multiple_screen = 0 and '
        'application_type = %(application_type)s ORDER BY level_code ASC '
    )

    params['name'] = '%' + name_tmp + '%'
    params['application_type'] = application_type

    with get_db() as db:
        return db.query(sql, params)


def get_self_report_created_users(**kwargs):
    """
    自助报表的列表搜索
    :return:
    """
    params = dict()
    name = kwargs.get('name', '')
    application_type = kwargs.get('application_type', 1)

    sql = """
SELECT DISTINCT
	dd.created_by AS account,
	u.name as name 
FROM
	dashboard AS dd
	LEFT JOIN user AS u ON u.account = dd.created_by 
WHERE
	( dd.created_by IS NOT NULL AND dd.created_by != '' ) 
	AND application_type = %(application_type)s
	AND dd.created_by != %(curr_user)s
	{where_fields}
GROUP BY
	dd.created_by
"""
    params['application_type'] = application_type
    params['curr_user'] = kwargs['curr_user']

    where_fields = []
    if name:
        name_tmp = ''
        for i in range(0, len(name)):
            name_tmp += '\\' + str(name[i])
        params['name'] = '%' + name_tmp + '%'
        where_fields.append('(u.`name` like %(name)s or dd.`created_by` like %(name)s)')

    if where_fields:
        where_fields_str = ' AND '.join(where_fields)
        where_fields_str = ' AND ' + where_fields_str
        sql = sql.replace('{where_fields}', where_fields_str)
    else:
        sql = sql.replace('{where_fields}', '')
    with get_db() as db:
        return db.query(sql, params) or []


def get_jump_dashboard_ids_by_target_dashboard_ids(target_dashboard_ids):
    """获取跳转到当前报告的所有报告"""
    ret = []
    # 必须从已经发布的数据中取数据
    # sql = """
    # SELECT dashboard_id, jump
    # FROM dashboard_released_snapshot_chart
    # WHERE jump REGEXP %(reg)s
    # """
    # result = get_db().query(sql, {"reg": '"target": "(%s)"' % '|'.join(target_dashboard_ids)})
    sql = """
    SELECT `dashboard_id`, `jump`
    FROM dashboard_released_snapshot_chart
    WHERE {PLACEHOLDER}
    """
    conditions = ["""(`jump` like '%%"target": "%s"%%')""" % dashboard_id for dashboard_id in target_dashboard_ids]
    condition_sql = ' or '.join(conditions)
    sql = sql.replace('{PLACEHOLDER}', condition_sql)
    result = get_db().query(sql)
    if not result:
        return ret
    for v in result:
        if not v['jump'] or v['dashboard_id'] in ret:
            continue
        jumps = json.loads(v['jump'])
        if not jumps or not isinstance(jumps, list):
            continue
        for jump in jumps:
            if jump.get('target') in target_dashboard_ids:
                ret.append(v['dashboard_id'])
                break
    return ret


def get_default_dashboard_name(parent_id, default_name='新建文件夹'):
    """
    获取默认报告名称
    :param parent_id:
    :param platform:
    :param new_layout_type:
    :return:
    """
    return repository.get_list(
        'dashboard', {"parent_id": parent_id, 'name REGEXP': rf'{default_name}\([0-9]+\)'}, 'name', order_by=[('name', 'desc')]
    )


def get_dashboard_name(parent_id, file_type, name, dashboard_id, application_type):
    """
    获取报告名称
    :param parent_id:
    :param file_type:
    :param name:
    :param dashboard_id:
    :param application_type:
    :return:
    """
    if not isinstance(application_type, list):
        application_type = [application_type]
    return repository.get_list(
        'dashboard',
        {
            'parent_id': parent_id,
            'type': file_type,
            'name': name,
            'id!=': dashboard_id,
            'application_type': application_type,
        },
        'name',
    )


def get_move_list_by_level_code(level_code, dashboard_type=None):
    """
    根据数据看板编码获取所有数据集
    :param str level_code:
    :param str dashboard_type:
    :return:
    """

    sql = (
        'SELECT id,`name`,`type`,`level_code`,`parent_id`,`created_on` FROM dashboard '
        'WHERE level_code like %(level_code)s'
    )
    params = {'level_code': level_code + '%'}
    if dashboard_type:
        sql += " and `type` =%(dashboard_type)s"
        params['dashboard_type'] = dashboard_type
    with get_db() as db:
        return db.query(sql, params)


def get_folder_by_level_code(level_code, dashboard_type='FOLDER'):
    """
    根据数据看板编码获取所有数据集
    :param str level_code:
    :param str dashboard_type:
    :return:
    """

    sql = (
        'SELECT id,`name`,`type`,`level_code`,`parent_id` FROM dashboard '
        'WHERE level_code like %(level_code)s and `type` =%(dashboard_type)s order by level_code'
    )
    params = {'level_code': level_code + '%', 'dashboard_type': dashboard_type}
    with get_db() as db:
        return db.query(sql, params)


def move_dashboard_tree(level_code, new_level_code):
    """
    :更新level_code树
    :date 2017/7/4
    :param :
    :return :
    """

    sql = (
        'UPDATE dashboard set `level_code`=replace(`level_code`,%(level_code)s,%(new_level_code)s )'
        'WHERE `level_code` like %(level_code_path)s'
    )

    params = {'level_code_path': level_code + '%', 'level_code': level_code, 'new_level_code': new_level_code}
    with get_db() as db:
        return db.exec_sql(sql, params)


def update_dashboard_to_child(level_code):
    """
    将报告类型变更为CHILD_FILE类型
    :param level_code:
    :return:
    """
    sql = "UPDATE dashboard set `type`= %(type)s WHERE `level_code` like %(level_code_path)s"
    params = {'type': DashboardType.CHILD_FILE.value, 'level_code_path': level_code + '%'}
    with get_db() as db:
        return db.exec_sql(sql, params)


def clear_dashboard_jump(dashboard_chart_id, dataset_field_id, dataset_field_type):
    """
    清空报告跳转
    :param dashboard_chart_id:
    :param dataset_field_id:
    :param dataset_field_type:
    :return:
    """
    with get_db() as db:
        sql = (
            "SELECT "
            "id "
            "FROM dashboard_jump_config "
            "WHERE "
            "`dashboard_chart_id`=%(dashboard_chart_id)s AND `source_id`=%(dataset_field_id)s AND "
            "`source_type`=%(source_type)s"
        )
        params = {
            'dashboard_chart_id': dashboard_chart_id,
            'source_id': dataset_field_id,
            'source_type': dataset_field_type,
        }
        jump_config = db.query_one(sql, params)
        if jump_config:
            db.delete('dashboard_jump_config', {'id': jump_config.get('id')}, False)
            db.delete('dashboard_jump_relation', {'jump_config_id': jump_config.get('id')}, False)
        return db.commit()


def get_related_dashboard_by_screen_id(screen_id):
    """
    获取关联的多屏id
    :param screen_id:
    :return:
    """
    sql = """select dashboard_id from screen_dashboard where screen_id=%(screen_id)s and screen_id!=dashboard_id"""
    params = {'screen_id': screen_id}
    with get_db() as db:
        return db.query(sql, params)


def get_jump_dashboard_info(target_dashboard_id):
    """
    获取起跳报告信息，新增需要返回单图名称
    :param target_dashboard_id:
    :return:
    """
    sql = """select distinct(djc.dashboard_chart_id) as dashboard_chart_id,
          djc.dashboard_id,d.name as dashboard_name, d.parent_id,dc.name as chart_name
          from dashboard_jump_config djc
          left join dashboard d on djc.dashboard_id=d.id
          left join dashboard_chart dc on djc.dashboard_chart_id=dc.id
          where djc.target=%(target_dashboard_id)s and djc.target_type in %(target_type)s"""
    params = {"target_dashboard_id": target_dashboard_id, "target_type": [DashboardJumpType.Dashboard.value, DashboardJumpType.SelfService.value, DashboardJumpType.Child_file.value]}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_filter_info(dashboard_filter_id):
    """
    获取报告筛选信息
    :param dashboard_filter_id:
    :return:
    """
    sql = """
                SELECT filter.id,filter.dashboard_id,filter.operator,filter.col_value,field.data_type,field.field_group,field.type,
        field.expression,field.format,filter.main_dataset_field_id as field_id,field.alias_name,field.col_name,
        dashboard.name as dashboard_name, dashboard.type as dashboard_type, filter.select_all_flag
        FROM dashboard_filter filter
        LEFT JOIN
        dataset_field field ON field.id=filter.main_dataset_field_id
        LEFT JOIN
        dashboard ON dashboard.id=filter.dashboard_id
        WHERE
        filter.id =%(dashboard_filter_id)s AND field.visible=1
        """
    param = {'dashboard_filter_id': dashboard_filter_id}
    with get_db() as db:
        return db.query_one(sql, param)


def get_dashboard_filters(dashboard_id):
    """
    获取报告筛选条件
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT dbf.`id`,dbf.`dashboard_id`,dbf.`main_dataset_field_id`,dbf.`operator`,dbf.`col_value`,dbf.`created_on`,
    dsf.`rank`,dsf.`dataset_id` as `main_dataset_id`,dsf.`alias_name`,dsf.`col_name`,dsf.`data_type`,dsf.`field_group`,
    dsf.`type`,dsf.`expression`,dsf.`format`,dbf.`select_all_flag`,dbf.`filter_relation`
    FROM dashboard_filter dbf
    LEFT JOIN dataset_field  dsf
    ON dbf.main_dataset_field_id=dsf.id
    WHERE dbf.dashboard_id=%(dashboard_id)s AND dsf.visible=1
    """
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_filter_relations(dashboard_id):
    """
    获取报告所有的筛选关联关系
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT `id`,`dashboard_id`,`main_dataset_field_id`,`related_dataset_field_id`
    FROM `dashboard_dataset_field_relation` WHERE `dashboard_id`=%(dashboard_id)s
    """
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_field_by_id(field_id):
    """
    获取数据集字段name
    :param field_id
    :return
    """ ""
    sql = "select alias_name, col_name from dataset_field where id=%(field_id)s"
    params = {'field_id': field_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dashboard_var_filter_relations(dashboard_id):
    """
    获取报告所有的筛选关联关系
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT `id`,`dashboard_id`,`var_name`,`related_dataset_field_id`, `related_dataset_id`
    FROM `dashboard_filter_vars_relation_field` WHERE `dashboard_id`=%(dashboard_id)s
    """
    params = {'dashboard_id': dashboard_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboards(dashboard_ids):
    """
    获取已授权的应用看板
    :param dashboard_ids:
    :return:
    """
    sql = """SELECT `id`,`theme`,`type`,`name`,`user_group_id`,`default_show`,`description`,`share_secret_key`,
        `level_code`, `layout`,`background`,`border`,`is_multiple_screen`, `status`, `cover`, `type_access_released`,
        `type_selector`,`platform`,`biz_code`, `scale_mode`, `grid_padding`, `is_show_mark_img`, `terminal_type`,
        `application_type`,`main_external_subject_id`,`external_subject_ids`,`dataset_id`
        FROM dashboard
        WHERE id in (%s) and build_in = 0
        """
    in_ids = ', '.join(map(lambda x: '%s', dashboard_ids))
    sql = sql % in_ids
    with get_db() as db:
        return db.query(sql, dashboard_ids)


def get_dashboard_selectors(dashboard_id):
    """
    获取报告的关联的单图ID
    :param dashboard_id:
    :return:
    """
    sql = """
    select chart_initiator_id, chart_responder_id, dashboard_id, dataset_id, `type`, is_same_dataset, `id`
    from dashboard_chart_selector
    where dashboard_id=%(dashboard_id)s
    """
    with get_db() as db:
        return db.query(sql, {"dashboard_id": dashboard_id})


def get_dashboard_selector_fields_by_selector_id(selector_id):
    """
    获取报告关联的字段数据
    :param str selector_id: dashboard_chart_selectorb表id
    :return:
    """
    sql = """
    select field_initiator_id, field_responder_id, dashboard_id, selector_id from dashboard_chart_selector_field
    where selector_id=%(selector_id)s
    """
    with get_db() as db:
        return db.query(sql, {"selector_id": selector_id})


def get_dashboard_usergroups(dashboard_id):
    """
    获取报告的列表
    :param dashboard_id:
    :return:
    """
    sql = """
    select `group_id` from user_group_dashboard where dashboard_id=%(dashboard_id)s
    """
    list_ = []
    with get_db() as db:
        for row in db.query(sql, {"dashboard_id": dashboard_id}):
            list_.append(row["group_id"])
    return list_


def get_all_dashboard():
    """
    获取所有报告(不包含目录)
    :return:
    """
    sql = (
        "SELECT id,theme,data_report_id,name,type,parent_id,level_code,icon,platform,is_multiple_screen,status,"
        "default_show,cover,description,layout_type,layout,scale_mode,background,`rank`,build_in,biz_code,border,"
        "type_access_released,type_selector,created_on,is_show_mark_img,terminal_type,"
        "application_type,main_external_subject_id,external_subject_ids,dataset_id "
        "FROM `dashboard` "
        "WHERE `type`='FILE' AND `build_in`=0 "
        "ORDER BY `created_on` DESC"
    )
    with get_db() as db:
        return db.query(sql)


def get_dashboard_has_biz_code():
    """
    获取看板数据
    """
    sql = 'select * from dashboard order by `rank`'
    with get_db() as db:
        return db.query(sql)


def get_dashboard_by_create_user(username, application_type=0):
    """
    通过创建者用户名获取报告信息（供组件标准化中使用）
    :param username: 创建者用户名称
    :return:
    """
    sql = """
    SELECT `id`,`theme`,`type`,`name`,`platform`,`user_group_id`,`default_show`,`description`,`share_secret_key`,`level_code`,
    `layout`,`background`,`border`,`is_multiple_screen`, `status`, `cover`, `type_access_released`, `type_selector`,
    `biz_code`, `scale_mode`, `is_show_mark_img`, `terminal_type`, `application_type`, `main_external_subject_id`, `external_subject_ids`, `dataset_id`
    FROM dashboard
    WHERE created_by=%(created_by)s and application_type=%(application_type)s and build_in = 0
    """
    params = {'created_by': username, 'application_type': application_type}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_folders(level_code):
    """
    根据level_code获取上级文件夹
    :param level_code:
    :return:
    """
    sql = 'SELECT * FROM dashboard WHERE `type` = %(dashboard_type)s AND `level_code` like %(level_code)s '
    with get_db() as db:
        return db.query(sql, {"dashboard_type": DashboardType.Folder.value, "level_code": level_code + "%"})


def get_dashboard_filters_by_dashboard_ids(dashboard_ids):
    """
    批量获取报告筛选条件
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT dbf.`id`,dbf.`dashboard_id`,dbf.`main_dataset_field_id`,dbf.`operator`,dbf.`col_value`,dbf.`created_on`,
    dsf.`rank`,dsf.`dataset_id` as `main_dataset_id`,dsf.`alias_name`,dsf.`col_name`,dsf.`data_type`,dsf.`field_group`,
    dsf.`type`,dsf.`expression`,dsf.`format`,dbf.`select_all_flag`
    FROM dashboard_filter dbf
    LEFT JOIN dataset_field  dsf
    ON dbf.main_dataset_field_id=dsf.id
    WHERE dbf.dashboard_id in %(dashboard_ids)s AND dsf.visible=1
    """
    params = {'dashboard_ids': dashboard_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_jump_relations(dashboard_filter_ids):
    """
    根据报告筛选dashboard_filter_ids列表，查出引用了这些报告筛选的所有跳转关系配置
    :param dashboard_filter_ids:
    :return:
    """
    sql = '''select dashboard_id, dashboard_chart_id, dataset_field_id, dashboard_filter_id, jump_config_id
    from dashboard_jump_relation where dashboard_filter_id in %(dashboard_filter_ids)s'''
    params = {'dashboard_filter_ids': dashboard_filter_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_jump_params_relations(dashboard_filter_ids):
    """
    根据报告筛选dashboard_filter_ids列表，查出引用了这些报告筛选的所有跳转关系配置
    :param dashboard_filter_ids:
    :return:
    """
    sql = '''select dashboard_id, dashboard_chart_id, source_id, dashboard_filter_id,
    param_dataset_field_id, `rank`
    from dashboard_chart_params_jump where dashboard_filter_id in %(dashboard_filter_ids)s'''
    params = {'dashboard_filter_ids': dashboard_filter_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_filters_by_ids(dashboard_filter_ids):
    """
    根据报告筛选dashboard_filter_ids列表，查出引用了这些报告筛选的所有跳转关系配置
    :param dashboard_filter_ids:
    :return:
    """
    sql = '''select id from dashboard_filter where id in %(dashboard_filter_ids)s'''
    params = {'dashboard_filter_ids': dashboard_filter_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_by_biz_code(biz_code):
    """

    :param biz_code:
    :return:
    """
    sql = 'select id, biz_code,name, icon, cover, terminal_type from dashboard where biz_code=%(biz_code)s'
    with get_db() as db:
        return db.query_one(sql, {'biz_code': biz_code})


def get_screen_by_id(screen_id):
    sql = 'select id, name from dashboard where id=%(screen_id)s and is_multiple_screen=1'
    params = {'screen_id': screen_id}
    with get_db() as db:
        return db.query(sql, params)


def get_sub_child_dashboard(dashboard, with_deleted=False):
    """
    根据当前报表dashboard_id获取所有子报表
    :param dashboard:
    :param with_deleted: bool
    :return:
    """
    sql = (
        " select  `id`, `theme`, `data_report_id`, `name`, `type`, `parent_id`, `level_code`,"
        " `icon`, `status`,`application_type`,`platform`,`new_layout_type`,`analysis_type`,`line_height`, `is_key` ,`terminal_type`"
        " from dashboard where level_code like %(level_code)s "
    )
    params = dict()
    params['level_code'] = str(dashboard.get('level_code')) + "%"
    if not with_deleted:
        sql += " and is_deleted = %(is_deleted)s"
        params['is_deleted'] = 0
    sql += " order by level_code"
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_by_level_code(level_code, with_deleted=False):
    root_dashboard_sql = (
        "select  `id`, `theme`, `data_report_id`, `name`, `type`, `parent_id`, `level_code`, `icon`, `asset_id`,"
        " `status`,`application_type`,`platform`,`is_deleted`,`new_layout_type`,`line_height`, `is_key`,`terminal_type` from"
        " dashboard where %(level_code)s like concat(level_code,%(level_code1)s) and type = %(types)s"
    )
    params = dict()
    params['level_code'] = level_code
    params['level_code1'] = '%'
    params['types'] = DashboardType.File.value
    with get_db() as db:
        root_dashboard = db.query_one(root_dashboard_sql, params)
        if not root_dashboard:
            raise UserError(message='根报告不存在，请刷新页面再试')
        return get_sub_child_dashboard(root_dashboard, with_deleted=with_deleted)


def get_all_dashboard_by_ids(dashboard_ids, with_delete=False):
    """
    批量获取根报告下所有子报告，包含根报告
    :param dashboard_ids:根报告id
    :param with_delete:是否删除
    :return:
    """
    if not dashboard_ids:
        return []
    sql = "select c.`id`, c.`theme`, c.`data_report_id`, c.`name`, c.`type`, c.`parent_id`, c.`level_code`, c.`icon`, c.`status`,c.`application_type`,c.`platform`, c.`is_deleted`,c.`new_layout_type` from dashboard d  join dashboard c on c.level_code like concat(d.level_code,%(level_code_suffix)s) where d.id in %(dashboard_ids)s "
    params = dict()
    params['dashboard_ids'] = dashboard_ids
    params['level_code_suffix'] = '%'
    if not with_delete:
        sql += " and d.is_deleted = 0 "
    sql += ' order by c.level_code '
    with get_db() as db:
        return db.query(sql, params)

def add_self_service_child_dashboard(child_dashboard, jump_config):
    with get_db() as db:
        db.insert('dashboard', child_dashboard, commit=False)
        db.insert('dashboard_jump_config', jump_config, commit=True)


def get_dashboard_chart_by_chart_ids(ids):
    sql = """
        select * from dashboard_chart
        where id in %(ids)s
    """
    return get_db().query(sql, {"ids": ids})


def get_dashboard_ref_dataset_ids(main_dashboard_id):
    main_dashboard = repository.get_one('dashboard', conditions={'id': main_dashboard_id}, fields=['level_code']) or {}
    level_code = main_dashboard.get('level_code', '')
    if not level_code:
        return []

    # 关联了数据集且数据集存在的
    chart_ref_sql = f"""
    select distinct(dc.source) from dashboard as dd 
    join dashboard_chart  as dc on dd.id = dc.dashboard_id 
    join dataset as dt on dt.id = dc.source 
    where dc.source != '' and dd.level_code like '{level_code}%%' and dt.`type` != 'INDICATOR'
    """
    chart_ref_dataset_ids = repository.get_data_by_sql(chart_ref_sql, params={}) or []
    chart_ref_dataset_ids = [i['source'] for i in chart_ref_dataset_ids]

    dashboard_ref_sql = f"""
    select distinct(df.main_dataset_field_id) from dashboard_filter as df 
    join dashboard as dd on dd.id = df.dashboard_id 
    join dataset as dt on dt.id = df.main_dataset_field_id 
    where dd.level_code like '{level_code}%%' and dt.`type` != 'INDICATOR'
    """
    dashboard_ref_dataset_ids = repository.get_data_by_sql(dashboard_ref_sql, params={}) or []
    dashboard_ref_dataset_ids = [i['main_dataset_field_id'] for i in dashboard_ref_dataset_ids]

    ref_dataset_ids = [*chart_ref_dataset_ids, *dashboard_ref_dataset_ids]
    return list(set(ref_dataset_ids))


def get_parent_dashboard_level_code(dashboard_id):
    sql = "SELECT level_code from dashboard WHERE id = %(dashboard_id)s  LIMIT 1"
    with get_db() as db:
        return db.query_scalar(sql, {'dashboard_id': dashboard_id})


def get_dashboard_scheduled_datasets(dashboard_id, yysc_dataset_parent_level_code):
    # 获取报告所用的应用市场的调度数据集
    if not dashboard_id or not yysc_dataset_parent_level_code:
        return {}
    sql = """
    SELECT distinct id, name from dataset WHERE id in (
        SELECT  source from dashboard_chart WHERE dashboard_id = %(dashboard_id)s
        ) and (connect_type is null or connect_type = '') and level_code like '{}%%'
    """.format(yysc_dataset_parent_level_code)
    with get_db() as db:
        return db.query(sql, {'dashboard_id': dashboard_id})


def get_chart_dataset(chart_id):
    sql = "SELECT source FROM dashboard_chart WHERE id = %(chart_id)s limit 1"
    with get_db() as db:
        return db.query_one(sql, {'chart_id': chart_id})


def get_last_scheduling_datasets(dataset_id):
    # 获取报告还未调度成功的数据集
    if not dataset_id:
        return []

    # 如果之前有调度成功的，返回空
    sql1 = "select * from instance WHERE flow_id = %(dataset_id)s and status = '已成功' limit 1"
    with get_db() as db:
        instance =  db.query(sql1, {'dataset_id': dataset_id})
        if instance:
            return []

    # 没有成功的调度的话，就检查最近的一条调度记录
    sql2 = "select * from instance  where flow_id = %(dataset_id)s  ORDER BY  created_on desc limit 1"
    with get_db() as db:
        return  db.query(sql2, {'dataset_id': dataset_id})


def get_parent_dataset_level_code(dataset_id):
    sql = "SELECT level_code FROM dataset WHERE id = %(dataset_id)s limit 1"
    with get_db() as db:
        return db.query_scalar(sql, {'dataset_id': dataset_id})


def is_son_dashboard_by_dashboard_id(dashboard_id, level_code):
    sql = "SELECT  id from dashboard WHERE id = '{}' and" \
          " level_code like '{}%%' LIMIT 1".format(dashboard_id, level_code)
    with get_db() as db:
        return db.query_scalar(sql, {})


def get_key_dashboard_list():
    """
    获取租户的重点大屏列表
    :return:
    """
    fields = ['id', 'name', 'type', 'platform', 'created_on', 'status', 'created_by']
    return repository.get_data(
        'dashboard',
        {
            'build_in': 0,
            'type': DataReportType.File.value,
            'platform': 'pc',
            'is_key': 1,
            'application_type': 0,
        },
        fields,
        order_by=[('created_on', 'desc')],
        multi_row=True
    )


def get_key_dashboard_num():
    """
    获取租户的重点大屏数量
    :return:
    """
    sql = """select count(*) as c from dashboard where build_in=0 and type = 'FILE' and platform = 'pc' 
        and is_key=1 and application_type = 0"""
    return repository.get_data_scalar_by_sql(sql, None)


def get_dashboard_and_chart_name(dashboard_id, chart_id, is_release=False):
    """
    按报告id和组件id
    查找设计时或运行时的报告和组件的名称
    :param dashboard_id:
    :param chart_id:
    :param is_release:
    :return:
    """
    if is_release:
        dashboard_table = 'dashboard_released_snapshot_dashboard'
        dashboard_chart_table = 'dashboard_released_snapshot_chart'
    else:
        dashboard_table = 'dashboard'
        dashboard_chart_table = 'dashboard_chart'

    query_sql = f"""SELECT d.id,d.name,dc.id as chart_id,dc.name as chart_name FROM {dashboard_table} as d
inner join {dashboard_chart_table} as dc
on d.id = dc.dashboard_id
where d.id = %(dashboard_id)s and dc.id = %(chart_id)s limit 1;
"""
    params = {
        'dashboard_id': dashboard_id,
        'chart_id': chart_id,
    }
    rs = repository.get_data_by_sql(query_sql, params) or []
    return rs[0] if rs else {}


def get_move_large_screen_task_data(task_id):
    """
    获取迁移大屏的任务状态信息
    :return:
    """
    fields = ['id', 'start_time', 'end_time', 'total_count', 'succ_count', 'failed_count', 'status']
    return repository.get_one(
        'move_dashboard_task', conditions={'id': task_id}, fields=fields
    ) or {}


def has_running_task():
    """
    获取是否还有正在进行中的任务
    :return:
    """
    from base.enums import MoveDashboardAsLargeScreenStatus
    sql = 'select id from move_dashboard_task where status in %(status)s'
    return repository.get_data_by_sql(sql, params={'status': [
        MoveDashboardAsLargeScreenStatus.Progressing.value,
        MoveDashboardAsLargeScreenStatus.Init.value
    ]}) or []


def get_move_large_screen_count():
    """
    获取能迁移大屏的数量
    :return:
    """
    sql = """select count(*) as c from dashboard 
    where type = 'FILE' and platform = 'pc' and build_in = 0
    and new_layout_type=0  
    and is_multiple_screen=0  
    and application_type = 0"""
    return repository.get_data_scalar_by_sql(sql, None)


def get_sys_tmp_folder():
    sql = '''SELECT a.`id`,a.`name`
            FROM dashboard as a
            INNER JOIN dashboard as b on a.parent_id=b.id 
            LEFT JOIN dashboard as c on b.parent_id=c.id
            LEFT JOIN dashboard as d on c.parent_id=d.id and d.id!=d.parent_id
            WHERE a.type = 'FOLDER' and a.distribute_type=1 and ( b.`name`='系统分发报告文件夹' or c.`name`='系统分发报告文件夹' or d.`name`='系统分发报告文件夹' )  and  a.id NOT IN (
                SELECT DISTINCT parent_id
                FROM dashboard
             )'''
    with get_db() as db:
        return db.query(sql)
