#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    @desc 组件模块 数据库操作模块
    @date 2018年2月2日
    <AUTHOR>
"""
from base.dmp_constant import component_table_map
from dmplib import config
from dmplib.db.mysql_wrapper import get_db as get_master_db


def get_component(package):
    """
    获取组件信息
    :param package:
    :return:
    """
    sql = (
        "SELECT c.name,c.icon,c.package,c.preview_image,c.description,"
        "c.status,c.is_build_in,c.data_logic_type_code  "
        "FROM {component} AS c "
        " WHERE c.package=%(package)s and c.status = 1"
    )
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component': component_table_map.get("component") if is_gray else 'component'})
    params = {"package": package}
    with get_master_db() as db:
        return db.query_one(sql, params)


def get_menu(menu_id):
    sql = "SELECT `id`,`level_code`,`parent_id`,`name`,`rank` FROM {component_menu} WHERE component_menu.ID=%(id)s"
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component_menu': component_table_map.get("component_menu") if is_gray else 'component_menu'})
    params = {"id": menu_id}
    with get_master_db() as db:
        return db.query_one(sql, params)


def get_data_logic_type(code):
    sql = "SELECT `code`,`name` FROM {component_data_logic_type} WHERE code=$(code)s"
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component_data_logic_type': component_table_map.get("component_data_logic_type") if is_gray else 'component_data_logic_type'})
    params = {"code": code}
    with get_master_db() as db:
        return db.query_one(sql, params)


def get_component_count():
    """
    获取组件表的组件个数
    :return:
    """
    sql = "SELECT count(*) as total from {component}"
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component': component_table_map.get("component") if is_gray else 'component'})
    with get_master_db() as db:
        return db.query_one(sql)


def get_component_menus_by_parentid(parent_id=None):
    sql = """
    SELECT id,name,`rank`,parent_id,level_code,icon
    FROM {component_menu}
    WHERE parent_id=%(parent_id)s
    ORDER BY `rank` ASC
    """
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component_menu': component_table_map.get("component_menu") if is_gray else 'component_menu'})
    param = {"parent_id": parent_id}
    with get_master_db() as db:
        return db.query(sql, param)


def get_all_menus():
    sql = """
    SELECT `id`,`icon`,`level_code`,`parent_id`,`name`,`rank`
    FROM `{component_menu}`
    ORDER BY `rank` DESC
    """
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component_menu': component_table_map.get("component_menu") if is_gray else 'component_menu'})
    with get_master_db() as db:
        return db.query(sql)


def get_components():
    sql = """
    SELECT c.menu_id,c.name,c.icon,c.package,c.chart_type,c.preview_image,c.description,c.version,
    c.data_logic_type_code,c.status,c.is_build_in,c.md5version,c.md5RunTimeversion,t.name as data_logic_type_name,
    c.created_on,m.level_code as menu_level_code,m.name as menu_name,m.icon as menu_icon,m.parent_id as menu_parent_id,
    c.data_source_origin,c.indicator_description,c.sortable,c.penetrable,c.linkage,c.can_linked,c.has_zaxis,
    c.has_desiredvalue,c.dims_report_redirect,c.nums_report_redirect,c.indicator_rules,c.contain_css,
    c.contain_mapgallery,c.layout,c.layout_preview_image,c.navbar_icon,c.runTerminal,c.base_chart_lib,c.extension
    FROM `{component}` c
    LEFT JOIN `{component_data_logic_type}` t ON t.code=c.data_logic_type_code
    LEFT JOIN `{component_menu}` m on m.id=c.menu_id
    WHERE `status`=1
    ORDER BY c.`created_on` DESC
    """

    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{
        'component': component_table_map.get("component") if is_gray else 'component',
        'component_menu': component_table_map.get("component_menu") if is_gray else 'component_menu',
        'component_data_logic_type': component_table_map.get("component_data_logic_type") if is_gray else 'component_data_logic_type'
    })
    with get_master_db() as db:
        return db.query(sql)
