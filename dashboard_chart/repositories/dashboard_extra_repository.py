#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
dashboard extra service
"""

# ---------------- 标准模块 ----------------
from datetime import datetime
# ---------------- 业务模块 ----------------
from dmplib.saas.project import get_db
from dmplib.hug import g
from base import repository

from typing import List


def batch_get_dashboard_extra_data(dashboard_id_list: List[str]):
    """
    批量获取数据
    :param dashboard_id_list:
    :return:
    """
    if not dashboard_id_list:
        return []
    sql = """select dashboard_id,edit_on,released_on
          from dashboard_extra where dashboard_id in %(dashboard_id_list)s"""
    with get_db() as db:
        return db.query(sql, {"dashboard_id_list": dashboard_id_list})


def get_dashboard_extra_data(dashboard_id):
    """
    获取单个报告的数据
    :param dashboard_id:
    :return:
    """
    if not dashboard_id:
        return []
    sql = """select dashboard_id,edit_on,released_on, copy_dash_id, root_dash_id, first_released
          from dashboard_extra where dashboard_id =%(dashboard_id)s"""
    with get_db() as db:
        return db.query_one(sql, {"dashboard_id": dashboard_id})


def update_edit_on(dashboard_id, edit_on=""):
    """
    登记edit_on
    :param dashboard_id:
    :param edit_on:
    :return:
    """
    edit_on = edit_on if edit_on else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    sql = """insert into dashboard_extra (dashboard_id, edit_on, modified_by, created_by) values
          (%(dashboard_id)s, %(edit_on)s, %(modified_by)s, %(created_by)s)
          on duplicate key update edit_on=%(edit_on)s, modified_by=%(modified_by)s"""
    with get_db() as db:
        return db.exec_sql(
            sql,
            {"dashboard_id": dashboard_id, "created_by": getattr(g, 'account'),
             "edit_on": edit_on, "modified_by": getattr(g, 'account')},
        )


def update_released_on(dashboard_id):
    """
    登记released_on
    :param dashboard_id:
    :return:
    """
    sql = """insert into dashboard_extra (dashboard_id, released_on, modified_by, created_by) values
          (%(dashboard_id)s, now(), %(modified_by)s, %(created_by)s)
          on duplicate key update released_on=now(), modified_by=%(modified_by)s"""
    with get_db() as db:
        return db.exec_sql(
            sql,
            {"dashboard_id": dashboard_id, "created_by": getattr(g, 'account'), "modified_by": getattr(g, 'account')},
        )


def reset_edit_on_or_released_on(key, dashboard_id):
    """
    重置字段值
    :param key:
    :param dashboard_id:
    :return:
    """
    sql = """update dashboard_extra set {key}=null, modified_on=now(), modified_by=%(modified_by)s
          where dashboard_id=%(dashboard_id)s """.format(
        key=key
    )
    with get_db() as db:
        return db.exec_sql(sql, {"dashboard_id": dashboard_id, "modified_by": getattr(g, 'account')})


def reset_edit_on_and_released_on(dashboard_id):
    """
    重置字段值
    :param dashboard_id:
    :return:
    """
    sql = """update dashboard_extra set edit_on=null,released_on=null, modified_on=now(), modified_by=%(modified_by)s
        where dashboard_id=%(dashboard_id)s """
    with get_db() as db:
        return db.exec_sql(sql, {"dashboard_id": dashboard_id, "modified_by": getattr(g, 'account')})


def replace_insert_released(dashboard_id, released_on):
    sql = """insert into dashboard_extra (dashboard_id, released_on, modified_by, modified_on, created_by, created_on) values
              (%(dashboard_id)s, %(released_on)s, %(modified_by)s, now(), %(created_by)s, now())
              on duplicate key update edit_on=null, released_on=%(released_on)s, modified_by=%(modified_by)s"""
    with get_db() as db:
        return db.exec_sql(
            sql,
            {"dashboard_id": dashboard_id, "released_on": released_on, "created_by": getattr(g, 'account'),
             "modified_by": getattr(g, 'account')},
        )


def replace_insert_released_edit(dashboard_id, released_on, edit_on):
    sql = """insert into dashboard_extra (dashboard_id, edit_on, released_on, modified_by, modified_on, created_by, created_on) values
              (%(dashboard_id)s, %(edit_on)s, %(released_on)s, %(modified_by)s, now(), %(created_by)s, now())
              on duplicate key update edit_on=%(edit_on)s, released_on=%(released_on)s, modified_by=%(modified_by)s"""
    with get_db() as db:
        return db.exec_sql(
            sql,
            {"dashboard_id": dashboard_id, "edit_on": edit_on, "released_on": released_on,
             "created_by": getattr(g, 'account'), "modified_by": getattr(g, 'account')},
        )


def update_dashboard_extra_data(
        dashboard_id, edit_on="", released_on="",
        copy_dash_id="", root_dash_id="", is_copy_sys=0, first_released=""
):
    """
    更新dashboard_extra数据，没有就新建一条
    """
    data = {'dashboard_id': dashboard_id}
    if edit_on:
        data['edit_on'] = edit_on
    if released_on:
        data['released_on'] = released_on
    if copy_dash_id:
        data['copy_dash_id'] = copy_dash_id
    if root_dash_id:
        data['root_dash_id'] = root_dash_id
    if is_copy_sys:
        data['is_copy_sys'] = is_copy_sys
    if first_released:
        data['first_released'] = first_released

    dashboard_extra = repository.get_one(
        'dashboard_extra',
        conditions={'dashboard_id': dashboard_id}, fields=['dashboard_id']
    )
    if not dashboard_extra:
        return repository.add_data('dashboard_extra', data)
    else:
        return repository.update('dashboard_extra', data, conditions={'dashboard_id': dashboard_id})
