#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2019/3/19
"""
from base.repository import Pagination
from dmplib.saas.project import get_db
from base import repository


def get_dashboard_page_chart_download_list(page, page_size, user_id=None):
    """

    :param page:
    :param page_size:
    :param user_id:
    :return:
    """
    pagination = Pagination(page, page_size)
    sql = """SELECT dt.dashboard_id,dt.status,dt.download_url,dt.created_on as download_time,
          dt.external_user_id as user_id,d.name as dashboard_name FROM dashboard_chart_download_task AS dt
          LEFT JOIN dashboard AS d ON d.id=dt.dashboard_id"""

    wheres = []
    params = {}
    if user_id:
        wheres.append('dt.`external_user_id`=%(user_id)s')
        params['user_id'] = user_id
    sql += (" WHERE " + " AND ".join(wheres)) if wheres else ""

    sql += ' ORDER BY dt.`created_on` DESC'

    with get_db() as db:
        total = repository.get_total(sql,  params, db)
        sql += ' LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
        data = db.query(sql, params)
        return {'data': data, 'total': total}
