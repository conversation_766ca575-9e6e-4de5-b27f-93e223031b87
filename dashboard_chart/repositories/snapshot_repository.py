#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : snapshot_repository.py
# @Author: guq  
# @Date  : 2021/8/23
# @Desc  :


import json

from dmplib.saas.project import get_db
from datetime import datetime, timedelta
from base.enums import DataReportType, DashboardCreateType, DashboardNewLayoutType, DashboardJumpType, DashboardType
from base import repository
from dmplib.utils.errors import UserError

from typing import Dict, <PERSON>ple, Union


def batch_get_chart_code_by_snapshot_id_v2(snapshot_id):
    return repository.get_list(
        "snapshot_dashboard_chart", {"snapshot_id": snapshot_id}, "distinct(chart_code) as chart_code"
    )


def batch_get_chart_code_by_dashboard_id(dashboard_id):
    return repository.get_list(
        "dashboard_chart", {"dashboard_id": dashboard_id}, "distinct(chart_code) as chart_code"
    )
