#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
dashboard extra service
"""

# ---------------- 标准模块 ----------------
import json

# ---------------- 业务模块 ----------------
from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id
from base.enums import DashboardType

TABLE_NAME = 'dashboard_released_design_data'


def add_dashboard_released_design_data(dashboard_id, dashboard_data):
    data = {
        'id': seq_id(),
        'dashboard_id': dashboard_id,
        'dashboard_data': json.dumps(dashboard_data)
    }
    with get_db() as db:
        # 一个报告只保留最近发布的一个版本
        db.delete(TABLE_NAME, {"dashboard_id": dashboard_id}, False)
        db.insert(TABLE_NAME, data, False)
        return db.commit()


def del_dashboard_released_design(dashboard_id):
    with get_db() as db:
        return db.delete(TABLE_NAME, {"dashboard_id": dashboard_id})


def get_dashboard_released_design(dashboard_id):
    sql = f"SELECT `id`,`dashboard_id` FROM {TABLE_NAME} WHERE dashboard_id=%(dashboard_id)s"
    with get_db() as db:
        return db.query_one(sql, params={"dashboard_id": dashboard_id})


def get_dashboard_level_code_by_id(dashboard_id):
    """
    获取指定报告id 的level_code
    :param dashboard_id:
    :return:
    """
    sql = f"SELECT `level_code` FROM dashboard WHERE id=%(dashboard_id)s"
    with get_db() as db:
        return db.query_scalar(sql, {"dashboard_id": dashboard_id})


def get_root_dashboard_by_level_code(level_code):
    """
    根据子报告的level_code 获取根报告
    :param level_code:
    :return:
    """
    root_dashboard_sql = (
        "select  `id`, `name`, `type`, `parent_id`, `level_code`, `status`, `is_key` from"
        " dashboard where %(level_code)s like concat(level_code,%(level_code1)s) and type = %(types)s"
    )
    params = dict()
    params['level_code'] = level_code
    params['level_code1'] = '%'
    params['types'] = DashboardType.File.value

    with get_db() as db:
        root_dashboard = db.query_one(root_dashboard_sql, params)
    return root_dashboard
