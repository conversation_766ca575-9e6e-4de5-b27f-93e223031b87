#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/12/26

"""
external dashboard repository
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from base import repository
import dmplib.db.errors
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from base.enums import DataReportType, DashboardType, ApplicationType
from dashboard_chart.models import DashboardOpenAPIQueryModel


def get_related_report_by_dataset_id(dataset_id):
    application_type = [ApplicationType.SimpleReport.value, ApplicationType.ActiveReport.value]
    sql = '''select t.id,t.name,d.name as folder,t.GroupGuid as folder_id,t.distribute_type,
    t.modified_on,t.application_type from (
    select dashboard.*,myrptdetail_design.GroupGuid from dashboard
    inner join myrptdetail_design on dashboard.id = myrptdetail_design.MyRptDetailId
    where (dashboard.type='FILE' or dashboard.type='CHILD_FILE') and dashboard.application_type in %(application_type)s
      and (myrptdetail_design.DsDefine like %(dataset_id)s
               or myrptdetail_design.RptVar like %(dataset_id)s)
    union
    select dashboard.*,myrptdetail.GroupGuid from dashboard
    inner join myrptdetail on dashboard.id = myrptdetail.MyRptDetailId
    where (dashboard.type='FILE' or dashboard.type='CHILD_FILE') and dashboard.application_type in %(application_type)s
      and (myrptdetail.DsDefine like %(dataset_id)s
               or myrptdetail.RptVar like %(dataset_id)s)) as t
    left join dashboard d on t.GroupGuid=d.id'''
    param = {
        'dataset_id': f'%{dataset_id}%',
        'application_type': application_type
    }
    with get_db() as db:
        try:
            return db.query(sql, param)
        except dmplib.db.errors.TableNotFoundError:
            return []


def get_related_dashboard_by_dataset_id(dataset_id):
    """
    获取数据集id关联的报告
    :param dataset_id:
    :return:
    """
    sql = '''select distinct(d.id) as dashboard_id,d.name as dashboard_name, d.type as dashboard_type,
          d.level_code as dashboard_level_code,d.parent_id,dc.id as dashboard_chart_id,dc.name as dashboard_chart_name,
          d.platform as dashboard_platform,d.new_layout_type as dashboard_new_layout_type,d.application_type,d.terminal_type
          from dashboard_chart as dc
          inner join dashboard as d on dc.dashboard_id=d.id
          where dc.source=%(dataset_id)s'''
    param = {'dataset_id': dataset_id}
    with get_db() as db:
        return db.query(sql, param)


def get_related_dashboard_by_field_ids(dataset_field_ids, query_table_name, field_key):
    """
    获取字段id关联的报告
    :param dataset_field_ids:
    :param query_table_name:
    :param field_key:
    :return:
    """
    sql = '''select dc.dashboard_id,d.name as dashboard_name,
          a.{field_key} as dataset_field_id,dc.name as dashboard_chart_name
          from {table_name} as a
          left join dashboard_chart as dc on a.dashboard_chart_id=dc.id
          left join dashboard as d on dc.dashboard_id=d.id
          where '''.format(
        field_key=field_key, table_name=query_table_name
    )

    if dataset_field_ids and len(dataset_field_ids) == 1:
        sql += ' a.{field_key} = %(dataset_field_ids)s'.format(field_key=field_key)
    elif dataset_field_ids and len(dataset_field_ids) > 1:
        sql += ' a.{field_key} in %(dataset_field_ids)s'.format(field_key=field_key)

    param = {'dataset_field_ids': dataset_field_ids}
    with get_db() as db:
        return db.query(sql, param)


def get_dataset_field_reference_by_field_ids(dataset_field_ids, query_table_name, field_key):
    """
    根据数据集字段dataset_field_ids查询单图引用记录
        维度：dashboard_chart_dim
        数值：dashboard_chart_num
        筛选：dashboard_chart_filter
        参数：dashboard_chart_params
        透视表：dashboard_chart_comparison
        目标值：dashboard_chart_desire
        辅助线：dashboard_chart_markline
        跳转：dashboard_jump_config
    :param dataset_field_ids:
    :param query_table_name:
    :param field_key:
    :param alias_name_key:
    :return:
    """
    sql = '''select dc.dashboard_id,d.name as dashboard_name,
          a.{field_key} as dataset_field_id, dc.id as dashboard_chart_id, dc.name as dashboard_chart_name
          from {table_name} as a
          left join dashboard_chart as dc on a.dashboard_chart_id=dc.id
          left join dashboard as d on dc.dashboard_id=d.id
          where '''.format(
        field_key=field_key, table_name=query_table_name
    )

    if dataset_field_ids and len(dataset_field_ids) == 1:
        sql += ' a.{field_key} = %(dataset_field_ids)s'.format(field_key=field_key)
    elif dataset_field_ids and len(dataset_field_ids) > 1:
        sql += ' a.{field_key} in %(dataset_field_ids)s'.format(field_key=field_key)

    param = {'dataset_field_ids': dataset_field_ids}
    with get_db() as db:
        return db.query(sql, param)


def get_penetrate_relation_by_field_ids(dataset_field_ids):
    """
    根据数据集字段dataset_field_ids查询穿透引用
    :param dataset_field_ids:
    :return:
    """
    sql = '''select id, dashboard_chart_id, dashboard_id, parent_chart_field_id, child_chart_field_id, `type`
    from dashboard_chart_penetrate_relation where parent_chart_field_id in %(dataset_field_ids)s
    or child_chart_field_id in %(dataset_field_ids)s
    '''
    params = {'dataset_field_ids': dataset_field_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_linkage_relations(dataset_id):
    sql = '''
    select
        cdl.id,
        cdl.chart_id as chart_initiator_id,
        cdl.dataset_field_id as field_initiator_id,
        cdl.dataset_id as initiator_dataset_id,
        cdl.dashboard_id,
        cdlf.chart_responder_id,
        cdlf.dataset_responder_id as dataset_id,
        cdlf.field_responder_id
    from
        dashboard_linkage_relation as cdlf
        left join dashboard_linkage as cdl on cdl.id = cdlf.link_id
    where
        cdlf.dataset_responder_id = %(dataset_id)s
    '''

    params = {"dataset_id": dataset_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_filter_chart_relations_by_field_ids(dataset_field_ids):
    """
    根据数据集字段dataset_field_ids查询组件筛选引用
    :param dataset_fields:
    :return:
    """
    sql = '''
        select dfcr.filter_id, dfcr.chart_responder_id, dfcr.field_responder_id, dfcr.dataset_responder_id,
            dfc.chart_id, dfc.dataset_field_id, dfc.dataset_id, dfc.dashboard_id
        from dashboard_filter_chart_relation as dfcr
            left join dashboard_filter_chart as dfc on dfc.id = dfcr.filter_id
        where dfcr.field_responder_id in %(dataset_field_ids)s
    '''
    params = {"dataset_field_ids": dataset_field_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_linkage_relations_by_field_ids(dataset_field_ids):
    """
    根据数据集字段dataset_field_ids查询联动引用
    :param dataset_fields:
    :return:
    """
    sql = '''
        select dlr.link_id, dlr.chart_responder_id, dlr.field_responder_id, dlr.dataset_responder_id,
            dl.chart_id, dl.dataset_field_id, dl.dataset_id, dl.dashboard_id
        from dashboard_linkage_relation as dlr
            left join dashboard_linkage as dl on dl.id = dlr.link_id
        where dlr.field_responder_id in %(dataset_field_ids)s
    '''
    params = {"dataset_field_ids": dataset_field_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_chart_by_chart_ids(chart_ids):
    sql = "select * from dashboard_chart where id in %(chart_ids)s"
    params = {"chart_ids": chart_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboards_by_dashboard_ids(dashboard_ids):
    sql = "select * from dashboard where id in %(dashboard_ids)s"
    params = {"dashboard_ids": dashboard_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_info_by_dashboard_ids(dashboard_ids):
    sql = "select id,biz_code,name from dashboard where id in %(dashboard_ids)s"
    params = {"dashboard_ids": dashboard_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_info_by_biz_codes(biz_codes):
    sql = "select id,biz_code from dashboard where biz_code in %(biz_codes)s"
    params = {"biz_codes": biz_codes}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_filters_by_field_ids(dataset_field_ids):
    """
    批量获取报告筛选条件
    :param dashboard_id:
    :return:
    """
    sql = """
    SELECT dbf.`id`,dbf.`dashboard_id`,dbf.`main_dataset_field_id`,dbf.`operator`,dbf.`col_value`,dbf.`created_on`,
    dsf.`rank`,dsf.`dataset_id` as `main_dataset_id`,dsf.`alias_name`,dsf.`col_name`,dsf.`data_type`,dsf.`field_group`,
    dsf.`type`,dsf.`expression`,dsf.`format`,dbf.`select_all_flag`
    FROM dashboard_filter as dbf
    LEFT JOIN dataset_field  as dsf
    ON dbf.main_dataset_field_id=dsf.id
    WHERE dbf.main_dataset_field_id in %(dataset_field_ids)s
    """
    params = {'dataset_field_ids': dataset_field_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_jump_relations_by_field_ids(dataset_field_ids):
    sql = '''
        select djr.dataset_field_id as field_initiator_id, djr.dashboard_filter_id,
            djc.dashboard_chart_id, djc.dashboard_id, djc.target, djc.target_type, djc.open_way,
            djc.status, djc.has_token, djc.source_type
        from dashboard_jump_relation as djr
        left join dashboard_jump_config as djc on djc.id = djr.jump_config_id
        where djr.dataset_field_id in %(dataset_field_ids)s
    '''
    params = {'dataset_field_ids': dataset_field_ids}
    with get_db() as db:
        return db.query(sql, params)


def dashboard_chart_params_jump_by_field_ids(dataset_field_ids):
    sql = '''
        select dashboard_id, dashboard_chart_id, param_dataset_field_id, source_id, dashboard_filter_id, `rank`
        from dashboard_chart_params_jump as dcpj
        where source_id in %(dataset_field_ids)s or param_dataset_field_id in %(dataset_field_ids)s
    '''
    params = {'dataset_field_ids': dataset_field_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_by_level_code(query_level_codes):
    """
    根据报告层级编码获取所有报告名称
    :param set query_level_codes:
    :return:
    """

    level_code_params = {}
    level_code_sql_list = []
    for query_level_code in query_level_codes:
        level_code_sql_list.append('level_code like %(' + str(query_level_code) + ')s')
        level_code_params[query_level_code] = str(query_level_code) + '%'
    level_code_sql = ' or '.join(level_code_sql_list)
    level_code_sql = '(' + level_code_sql + ')'
    sql = '''select name,level_code from dashboard where  type= %(dashboard_type)s and '''
    new_sql = sql + level_code_sql
    params = {'dashboard_type': DataReportType.Folder.value}
    new_params = dict(params, **level_code_params)

    with get_db() as db:
        return db.query(new_sql, new_params)


def get_dashboards_records_by_level_codes(level_code_list):
    """
    根据level_code列表获取对应的报告列表
    :param level_code_list:
    :return:
    """
    sql = '''SELECT id, `name`, `type`, modified_on, level_code, parent_id
    from dashboard where level_code in %(level_code_list)s'''
    params = {'level_code_list': level_code_list}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_records_by_level_codes(level_code_list):
    """
    根据level_code列表获取对应的报告列表
    :param level_code_list:
    :return:
    """
    sql = '''SELECT id, `name`, `type`, modified_on, level_code, parent_id
    from dataset where level_code in %(level_code_list)s'''
    params = {'level_code_list': level_code_list}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_fields_by_dashboard_id(query_table_name, dashboard_id, field_key):
    sql = ''' select df.dataset_id, df.col_name from {table_name} as a LEFT JOIN dataset_field as df
              on a.{field_key} = df.id WHERE a.dashboard_id = %(dashboard_id)s
          '''.format(
        table_name=query_table_name, field_key=field_key
    )
    with get_db() as db:
        return db.query(sql, {'dashboard_id': dashboard_id})


def get_dataset_fields_by_chart_dashboard_id(query_table_name, dashboard_id, field_key):
    sql = ''' select df.dataset_id, df.col_name from {table_name} as a
              LEFT JOIN dashboard_chart as dc on a.dashboard_chart_id = dc.id
              LEFT JOIN dataset_field as df
              on a.{field_key} = df.id WHERE dc.dashboard_id = %(dashboard_id)s
          '''.format(
        table_name=query_table_name, field_key=field_key
    )

    with get_db() as db:
        return db.query(sql, {'dashboard_id': dashboard_id})


def get_dataset_fields_by_chart_dashboard_id_where(query_table_name, dashboard_id, field_key):
    sql = ''' select df.dataset_id, df.col_name from {table_name} as a
              LEFT JOIN dashboard_chart as dc on a.dashboard_chart_id = dc.id
              LEFT JOIN dataset_field as df
              on a.{field_key} = df.id WHERE dc.dashboard_id = %(dashboard_id)s and a.sort in ('ASC', 'DESC')
          '''.format(
        table_name=query_table_name, field_key=field_key
    )

    with get_db() as db:
        return db.query(sql, {'dashboard_id': dashboard_id})


def get_related_dashboard_by_var_id(dataset_var_id):
    """
    获取提供的变量id相关联到的报告
    :param dataset_var_id:
    :return:
    """
    sql = """
    SELECT dashboard_id FROM `dashboard_dataset_vars_relation` WHERE `var_id`=%(dataset_var_id)s UNION
    SELECT dashboard_id FROM `dashboard_vars_jump_relation` WHERE `var_id`=%(dataset_var_id)s
    """
    params = {'dataset_var_id': dataset_var_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dashboard_list_by_query_model(query_model: DashboardOpenAPIQueryModel, d_type='dashboard'):
    """
    获取看板数据
    """
    if not isinstance(query_model, DashboardOpenAPIQueryModel):
        return []

    sql = 'select * from dashboard '
    params = {}
    wheres = []
    query_model.validate()
    if query_model.dashboard_ids:
        wheres.append('id in %(dashboard_ids)s')
        params['dashboard_ids'] = query_model.dashboard_ids
    if query_model.status:
        wheres.append('status = %(status)s')
        params['status'] = int(query_model.status)
    if query_model.type:
        wheres.append('type = %(type)s')
        params['type'] = query_model.type
    else:
        wheres.append('type != %(type)s')
        params['type'] = DashboardType.CHILD_FILE.value
    if query_model.platform:
        wheres.append('platform = %(platform)s')
        params['platform'] = query_model.platform
    if query_model.created_by:
        wheres.append('created_by = %(created_by)s')
        params['created_by'] = query_model.created_by
    if query_model.build_in is not None:
        wheres.append('build_in=%(build_in)s')
        params['build_in'] = query_model.build_in
    if query_model.is_multiple_screen is not None:
        # 多屏功能在大屏拆分后页面上化为酷炫大屏里面，但是实际上数据类型还是仪表版
        # 所以拆分后多屏的查询还是在仪表版里面查询
        # is_multiple_screen = 1 and d_type='dashboard'
        wheres.append('is_multiple_screen=%(is_multiple_screen)s')
        params['is_multiple_screen'] = query_model.is_multiple_screen
        # 指定酷炫大屏类型不能查询多屏信息
        if d_type == 'large_screen':
            return []
    if query_model.parent_id:
        # 只获取根节点下所有数据集对象列表
        if query_model.parent_id == 'root':
            query_model.parent_id = ''

        params["parent_id"] = query_model.parent_id
        if query_model.level_deep:
            wheres.append('`parent_id`=%(parent_id)s ')
        else:
            level_code = repository.get_value("dashboard", {"id": query_model.parent_id}, ["level_code"])
            # 系统分发文件夹可能存在level_code错乱情况，仍按parent_id查询
            if level_code and not level_code.startswith("9000-"):
                params["level_code"] = level_code + "%"
                wheres.append('id != %(parent_id)s and dashboard.level_code like %(level_code)s ')
            else:
                wheres.append('`parent_id`=%(parent_id)s ')

    if d_type == 'large_screen':
        wheres.append('application_type in %(application_type)s')
        params['application_type'] = [ApplicationType.LargeScreen.value]
    else:
        wheres.append('application_type not in %(not_in_application_type)s')
        params['not_in_application_type'] = [ApplicationType.SimpleReport.value, ApplicationType.ActiveReport.value, ApplicationType.LargeScreen.value]

    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' order by `rank`'
    with get_db() as db:
        return db.query(sql, params)


def get_user_create_dashboard_ids(user_account):
    sql = '''select id from dashboard where created_by = %(user_account)s and type !=%(type)s'''
    params = {'user_account': user_account,
              'type': DashboardType.CHILD_FILE.value}
    with get_db() as db:
        return db.query_columns(sql, params)


def get_released_dashboard_ids_by_dataset_ids(dataset_ids: list):
    sql = """
    select distinct d.id from dashboard as d left join dashboard_chart as dc on d.id = dc.dashboard_id
    where d.status = 1 and dc.source in %(dataset_ids)s
    """
    params = {'dataset_ids': dataset_ids}
    with get_db() as db:
        return db.query_columns(sql, params)


def get_dashboard_inspection_result(dashboard_ids: list):
    sql = """
    select hd.dashboard_id, d.name as dashboard_name, hd.status from healthy_dashboard as hd
    left join dashboard as d on d.id = hd.dashboard_id where hd.dashboard_id in %(dashboard_ids)s
    """
    params = {'dashboard_ids': dashboard_ids}
    with get_db() as db:
        return db.query(sql, params)


def batch_get_released_jump_config(dashboard_ids: list):
    sql = """
    select jump from dashboard_released_snapshot_chart
    where jump is not null and snapshot_id in %(dashboard_ids)s
    """
    params = {'dashboard_ids': dashboard_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_of_dashboard_id(dashboard_id):
    """
    根据dashboard_id获取当前报告及其子报告 所使用的数据集
    """
    cur_level_code = repository.get_data_scalar('dashboard', {"id": dashboard_id}, col_name="level_code")
    if not cur_level_code:
        raise UserError(message=f"报告不存在：{dashboard_id}")
    with get_db() as db:
        sql = "select id from dashboard where level_code like '{}%'".format(cur_level_code)
        ids = db.query_columns(sql) or []
    ids.append(dashboard_id)
    datasets = repository.get_list("dashboard_chart", {"dashboard_id": ids}, fields=["source"]) or []
    dataset_ids = [i.get('source') for i in datasets if i.get('source')]
    if dataset_ids:
        return repository.get_data("dataset", {"id": dataset_ids}, multi_row=True)
    else:
        return []
