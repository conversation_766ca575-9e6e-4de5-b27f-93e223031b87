#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
ps: 迁移自可视化报告弃用的dashboard目录模块
"""

# ---------------- 标准模块 ----------------
import hashlib
import logging
from abc import ABCMeta
import json

# ---------------- 业务模块 ----------------
from base.enums import (
    DashboardCreateType,
    DatasetType,
    DashboardDatasetFieldType,
    ChartPenetrateRelationType,
    ChartFilterInitiatorSouce,
)
from dashboard_chart.repositories import chart_repository, dashboard_repository, metadata_repository
from dashboard_chart.services import (
    dashboard_service,
    chart_service,
    metadata_service,
    proxy_dataset_service,
    chart_vars_service,
)
from dataset import external_query_service
from dataset.services import dataset_service
from dataset.repositories import dataset_repository
from flow.services import flow_service
from label.services import label_service
from base import repository

logger = logging.getLogger(__name__)


class BaseChartLogic:
    """
    单图logic基类
    """

    __metaclass__ = ABCMeta

    def __init__(self):
        """
        后期尽量只接收chart_id自行获取数据，而不是从前端传大部分数据
        """
        pass

    def save_chart(self, model):
        """
        新增单图
        :return:
        """
        pass

    def update_chart_layout(self, model):
        """
        编辑单图布局
        :return:
        """
        pass

    def update_chart(self, model):
        """
        编辑单图
        :return:
        """
        pass

    def delete_chart(self, chart_id):
        """
        删除单图
        :return:
        """
        pass

    def copy_chart(self, model):
        """
        复制单图
        :return:
        """
        pass

    def move_chart(self, model):
        """
        移动单图
        :return:
        """
        pass

    def save_dataset_field(self, model):
        """
        保存高级字段
        :return:
        """
        pass

    def delete_dataset_field(self, dataset_field_id):
        """
        删除高级字段
        :return:
        """
        pass

    def get_chart(self, chart_id):
        """
        获取单图定义
        :return:
        """
        pass

    def get_chart_data(self):
        """
        获取单图数据函数，子类需要重写
        :return:
        """
        pass

    def get_filter_options(self, field_id):
        """
        获取筛选器的value值
        :return:
        """
        pass

    def get_markline_data(self):
        """
        获取辅助线的计算值
        :return:
        """
        pass


class ChartDefinitionLogic(BaseChartLogic):
    """
    单图定义logic基类
    """

    def __init__(self):
        self.dataset_field_dict = dict()
        self.dataset_field_id_list = list()
        super().__init__()

    @staticmethod
    def get_chart_result_table_name(chart_id):
        """
        获取单图结果表名
        :param str chart_id:
        :return:
        """
        return 'chart_result_' + hashlib.md5(chart_id.encode('utf-8')).hexdigest()[8:-8]

    @staticmethod
    def _get_chart_params_jump_with_index(chart_params_jump):
        """
        获取默认值配置数据
        :param chart_params_jump:
        :return:
        """
        chart_params_jump_with_index = {}
        if not chart_params_jump:
            return chart_params_jump_with_index
        for jump in chart_params_jump:
            if jump.get('dashboard_chart_id') not in chart_params_jump_with_index:
                chart_params_jump_with_index[jump.get('dashboard_chart_id')] = {}
            if jump.get('source_id') not in chart_params_jump_with_index.get(jump.get('dashboard_chart_id')):
                chart_params_jump_with_index[jump.get('dashboard_chart_id')][jump.get('source_id')] = []
            chart_params_jump_with_index[jump.get('dashboard_chart_id')][jump.get('source_id')].append(jump)
        return chart_params_jump_with_index

    @staticmethod
    def _get_default_value(default_values, charts):
        """
        获取默认值配置数据
        :param default_values:
        :param charts:
        :return:
        """
        for dv in default_values:
            if dv["chart_id"] in charts:
                charts[dv["chart_id"]]["chart_default_value"].append(
                    {
                        "id": dv["id"],
                        "dataset_field_id": dv["dataset_field_id"],
                        "operator": dv["operator"],
                        "value": dv["value"],
                        "select_all": dv["select_all"],
                        "extend_data": dv["extend_data"],
                    }
                )

    @staticmethod
    def _get_chart_dims(rows, charts, chart_params_jump_with_index):
        """

        :param rows:
        :param charts:
        :param chart_params_jump_with_index:
        :return:
        """
        for row in rows:
            if "dims" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['dims'] = []
            # 增加跳转配置数据
            row['chart_params_jump'] = []
            if (row.get('dashboard_chart_id') in chart_params_jump_with_index) and (
                row.get('dim') in chart_params_jump_with_index.get(row.get('dashboard_chart_id'))
            ):
                row['chart_params_jump'] = chart_params_jump_with_index[row.get('dashboard_chart_id')].get(
                    row.get('dim')
                )
            charts[row['dashboard_chart_id']]['dims'].append(row)

    @staticmethod
    def _get_chart_comparisons(rows, charts):
        """
        获取单图对比配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "comparisons" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['comparisons'] = []
            charts[row['dashboard_chart_id']]['comparisons'].append(row)

    @staticmethod
    def _get_chart_layers(rows, charts):
        """
        获取单图仪表板配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "layers" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['layers'] = []
            charts[row['dashboard_chart_id']]['layers'].append(row)

    @staticmethod
    def _get_chart_nums_and_zaxis_in_show(rows, charts, chart_params_jump_with_index):
        """

        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "nums" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['nums'] = []
            if "zaxis" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['zaxis'] = []
            if row.get('axis_type'):
                charts[row['dashboard_chart_id']]['zaxis'].append(row)
            else:
                # 增加跳转配置数据
                row['chart_params_jump'] = []
                if (row.get('dashboard_chart_id') in chart_params_jump_with_index) and (
                    row.get('num') in chart_params_jump_with_index[row.get('dashboard_chart_id')]
                ):
                    row['chart_params_jump'] = chart_params_jump_with_index[row.get('dashboard_chart_id')].get(
                        row.get('num')
                    )
                charts[row['dashboard_chart_id']]['nums'].append(row)

    @staticmethod
    def _get_chart_nums_and_zaxis_not_in_show(rows, charts, chart_params_jump_with_index):
        """

        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            # 增加跳转配置数据
            row['chart_params_jump'] = []
            if "nums" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['nums'] = []
            if (row.get('dashboard_chart_id') in chart_params_jump_with_index) and (
                row.get('num') in chart_params_jump_with_index[row.get('dashboard_chart_id')]
            ):
                row['chart_params_jump'] = chart_params_jump_with_index[row.get('dashboard_chart_id')].get(
                    row.get('num')
                )
            charts[row['dashboard_chart_id']]['nums'].append(row)

    @staticmethod
    def _get_chart_desires(rows, charts):
        """
        获取单图目标值配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "desires" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['desires'] = []
            charts[row['dashboard_chart_id']]['desires'].append(row)

    @staticmethod
    def _get_chart_maps(rows, charts):
        """
        获取单图地图配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "map" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['map'] = []
            charts[row['dashboard_chart_id']]['map'].append(row)

    @staticmethod
    def _get_chart_colours(rows, charts):
        """
        获取单图颜色配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "colours" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['colours'] = []
            charts[row['dashboard_chart_id']]['colours'].append(row)

    @staticmethod
    def _get_chart_filters(rows, charts):
        """
        获取单图筛选配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            # 从新表取operator数据
            row['operators'] = chart_service.get_operators_by_filter_id(row['filter_id'])
            if "filters" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['filters'] = []
            charts[row['dashboard_chart_id']]['filters'].append(row)

    @staticmethod
    def _get_chart_marklines(rows, charts):
        """
        获取单图辅助线配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "marklines" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['marklines'] = []
            charts[row['dashboard_chart_id']]['marklines'].append(row)

    @staticmethod
    def _get_chart_field_sorts(rows, charts):
        """
        获取单图字段排序配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "field_sorts" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['field_sorts'] = []
            charts[row['dashboard_chart_id']]['field_sorts'].append(row)

    @staticmethod
    def _get_chart_params(rows, charts):
        """
        获取单图参数配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            if "chart_params" not in charts[row['dashboard_chart_id']]:
                charts[row['dashboard_chart_id']]['chart_params'] = []
            charts[row['dashboard_chart_id']]['chart_params'].append(row)

    @staticmethod
    def _get_chart_penetrate_filter_relation(rows, charts):
        """
        获取穿透关系配置数据
        :param rows:
        :param charts:
        :return:
        """
        rows = rows or []
        for row in rows:
            chart_id = row.get('dashboard_chart_id')
            chart = charts.get(chart_id)
            if not chart_id or not chart:
                continue
            if not chart.get('penetrate_relation'):
                chart['penetrate_relation'] = []
            if not chart.get('penetrate_filter_relation'):
                chart['penetrate_filter_relation'] = []
            if not chart.get('penetrate_var_filter_relation'):
                chart['penetrate_var_filter_relation'] = []
            item = {
                'parent_chart_field_id': row['parent_chart_field_id'],
                'child_chart_field_id': row['child_chart_field_id'],
                'type': row['type'],
            }
            if row.get('type') == ChartPenetrateRelationType.PenetrateType.value:
                chart['penetrate_relation'].append(item)
            elif row.get('type') == ChartPenetrateRelationType.PenetrateFilterType.value:
                chart['penetrate_filter_relation'].append(item)
            elif row.get('type') == ChartPenetrateRelationType.VarPenetrateFilterType.value:
                item['parent_chart_var_id'] = row.get('parent_chart_var_id', '')
                chart['penetrate_var_filter_relation'].append(item)

    @staticmethod
    def _get_chart_var_relations(rows, charts):
        """
        获取单图变量关系配置数据
        :param rows:
        :param charts:
        :return:
        """
        for row in rows:
            # # 2021/9/26帅帅要求恢复：https://www.tapd.cn/38229611/prong/stories/view/1138229611001091707， 运行时和设计时保持一致
            # row['field_initiator_id'] = row['field_initiator_id'] or None
            if "var_relations" not in charts[row['chart_initiator_id']]:
                charts[row['chart_initiator_id']]['var_relations'] = []
            charts[row['chart_initiator_id']]['var_relations'].append(row)

    @staticmethod
    def _get_chart_vars(chart_vars_dict, charts):
        """
        获取单图使用的变量
        :param chart_vars_dict:
        :param charts:
        :return:
        """
        for single_chart_id in list(charts.keys()):
            if "chart_vars" not in charts[single_chart_id]:
                charts[single_chart_id]["chart_vars"] = []
            charts[single_chart_id]["chart_vars"] = chart_vars_dict.get(single_chart_id, [])

    @staticmethod
    def _fill_new_filter(filter_rows):
        fixed_value_ids = []
        for fr in filter_rows:
            fr["related_list"] = []
            fr["fixed_value_data"] = None
            if fr.get("initiator_source") == ChartFilterInitiatorSouce.Fixedvalue.value:
                fixed_value_ids.append(fr.get("field_initiator_id"))
            relation = chart_repository.get_chart_new_filter_relation(fr["id"])
            if relation:
                for ra in relation:
                    ra["is_same_dataset"] = 1 if fr["dataset_id"] == ra["related_dataset_id"] else 0
                fr["related_list"] = relation
        # 为fixed_value赋值
        if fixed_value_ids:
            fixed_values_dict = {
                fixed_value["id"]: fixed_value
                for fixed_value in metadata_repository.get_filter_chart_fixed_values(fixed_value_ids)
            }
            for item in filter_rows:
                item["fixed_value_data"] = fixed_values_dict.get(item.get("field_initiator_id"))
        return filter_rows

    @staticmethod
    def _fill_new_linkage(linkage_rows):
        for lr in linkage_rows:
            lr["related_list"] = []
            relation = chart_repository.get_chart_new_linkage_relation(lr["id"])
            if relation:
                for ra in relation:
                    ra["is_same_dataset"] = 1 if lr["dataset_id"] == ra["related_dataset_id"] else 0

                lr["related_list"] = relation
        return linkage_rows

    def get_chart_new_filter_linkage_relation(self, chart_id):
        """
        获取新筛选和联动配置数据
        :param chart_id:
        :return:
        """
        frows = chart_repository.get_chart_new_filter(chart_id) or []
        lrows = chart_repository.get_chart_new_linkage(chart_id) or []
        frows = self._fill_new_filter(frows)
        lrows = self._fill_new_linkage(lrows)
        return frows, lrows

    def _get_top_parent_id(self, charts, chart_id):
        if chart_id not in charts:
            return
        if not charts[chart_id]['parent_id']:
            return chart_id
        return self._get_top_parent_id(charts, charts[chart_id]['parent_id'])

    def _op_no_parent_id_chart(self, charts, chart, result, result_tree_dict, create_type, need_extra_chart_flag):
        parent_id = self._get_top_parent_id(charts, chart['id'])
        if not parent_id:
            return result, result_tree_dict
        if parent_id not in result_tree_dict:
            result_tree_dict[parent_id] = charts[parent_id]
        result_tree_dict[parent_id]['penetrates'].append(charts[chart['id']])
        # 2018-09-07 穿透内的单图也需要存表
        if need_extra_chart_flag and chart['id'] not in result_tree_dict:
            result_tree_dict[chart['id']] = charts[chart['id']]
            # 新版报告不需要获取父级单图的position
            if create_type == DashboardCreateType.Standard.value:
                result_tree_dict[chart['id']]['position'] = charts[parent_id]['position']
            result_tree_dict[chart['id']]['refresh_rate'] = charts[parent_id]['refresh_rate']
            result.append(result_tree_dict[chart['id']])
        return result, result_tree_dict

    def _get_chart_result(self, chart_rows, charts, need_extra_chart_flag, create_type):
        """
        获取单图配置数据
        :param chart_rows:
        :param charts:
        :param need_extra_chart_flag:
        :param create_type:
        :return:
        """

        result = list()
        result_tree_dict = dict()

        for chart in chart_rows:
            if not charts[chart['id']]['parent_id']:
                result_tree_dict[chart['id']] = charts[chart['id']]
                result.append(result_tree_dict[chart['id']])
            else:
                result, result_tree_dict = self._op_no_parent_id_chart(
                    charts, chart, result, result_tree_dict, create_type, need_extra_chart_flag
                )
        return result

    def get_charts(self, dashboard_id, include_details=None, is_show_zaxis=False, need_extra_chart_flag=False):
        """
        根据报告ID所有单图详细数据
        :param dashboard_id:
        :param include_details:
        :param is_show_zaxis: 是否区分显示次轴数据
        :param need_extra_chart_flag:
        :return:
        """
        result = []
        charts = {}
        chart_ids = []
        penetrates = {}
        chart_sources = []

        chart_rows = chart_repository.get_chart_list(dashboard_id)
        if not include_details:
            if chart_rows:
                result = chart_rows
            return result

        for row in chart_rows:
            # 默认值default_value 已经不用(为保证数据升级重复执行 老的默认值仍让保留 但是不返回数据)
            row["default_value"] = ""
            # 获取度量或者维度的参数跳转配置
            chart_ids.append(row['id'])
            # 收集单图引用的数据集
            if row.get("source"):
                chart_sources.append(row.get("source"))
            # 老的筛选器组件已经废弃(为保证数据升级重复执行 老的筛选器配置并未删除 此处即使有数据也不入库)
            # 处理filter_config字段到新字段component_filter
            row['component_filter'] = []
            chart_new_filter, chart_new_linkage = self.get_chart_new_filter_linkage_relation(row.get("id"))
            logger.debug("releaseing...:new chart linkage data:%s", chart_new_linkage)
            row["chart_filters"] = chart_new_filter
            row["chart_linkages"] = chart_new_linkage

            charts[row['id']] = row
            charts[row['id']]['dims'] = []
            charts[row['id']]['comparisons'] = []
            charts[row['id']]['layers'] = []
            charts[row['id']]['nums'] = []
            charts[row['id']]['map'] = []
            charts[row['id']]['colours'] = []
            charts[row['id']]['filters'] = []
            charts[row['id']]['marklines'] = []
            charts[row['id']]["penetrates"] = []
            charts[row['id']]["jump"] = dashboard_service.get_chart_jump(row.get('id'))
            charts[row['id']]["chart_visible_triggers"] = chart_repository.get_chart_visible_triggers_by_chart_id(row.get('id'))
            charts[row.get('id')]['chart_params'] = []
            charts[row.get("id")]["chart_default_value"] = []
            penetrates[row['id']] = row['penetrate']

        # 统一处理数据集字段信息的获取
        section_data = self._batch_assign_field_data(chart_ids)

        # 所有图的参数跳转配置
        chart_params_jump = chart_repository.batch_get_chart_params_jump(chart_ids)
        chart_params_jump_with_index = self._get_chart_params_jump_with_index(chart_params_jump)
        del chart_params_jump

        default_values = chart_repository.get_dashboard_chart_values(dashboard_id) or []
        self._get_default_value(default_values, charts)

        self._get_chart_dims(section_data["dims"], charts, chart_params_jump_with_index)
        self._get_chart_comparisons(section_data["comparisons"], charts)
        self._get_chart_layers(section_data["layers"], charts)
        if is_show_zaxis:
            self._get_chart_nums_and_zaxis_in_show(section_data["nums"], charts, chart_params_jump_with_index)
        else:
            self._get_chart_nums_and_zaxis_not_in_show(section_data["nums"], charts, chart_params_jump_with_index)
        self._get_chart_desires(section_data["desires"], charts)
        self._get_chart_maps(section_data["maps"], charts)
        self._get_chart_colours(section_data["colours"], charts)
        self._get_chart_filters(section_data["filters"], charts)
        self._get_chart_marklines(section_data["marklines"], charts)
        self._get_chart_field_sorts(section_data["field_sorts"], charts)
        self._get_chart_params(section_data["chart_params"], charts)

        # 单图变量关系 var_relations
        var_relations = chart_repository.get_var_relation_by_chart_ids(chart_ids)
        self._get_chart_var_relations(var_relations, charts)

        # 附加穿透的关联关系
        penetrate_relations = chart_repository.get_penetrate_relation_by_chart_ids(chart_ids)
        self._get_chart_penetrate_filter_relation(penetrate_relations, charts)

        # 单图绑定的变量 chart_vars
        chart_vars_dict = chart_vars_service.get_chart_dataset_vars(dashboard_id, charts=charts)
        self._get_chart_vars(chart_vars_dict, charts)

        dashboard_data = dashboard_repository.get_dashboard(dashboard_id)
        create_type = dashboard_data.get('create_type', 0) if dashboard_data else 0

        # 转变成树形结构
        result = self._get_chart_result(chart_rows, charts, need_extra_chart_flag, create_type)
        return result

    def _batch_assign_field_data(self, chart_ids):
        """
        批量获取数据集字段信息
        :param chart_ids:
        :return:
        """
        dims = chart_repository.get_dataset_dims_by_chart_ids(chart_ids)
        comparisons = chart_repository.get_dataset_comparisons_by_chart_ids(chart_ids)
        layers = chart_repository.get_dataset_layers_by_chart_ids(chart_ids)
        nums = chart_repository.get_dataset_nums_by_chart_ids(chart_ids)
        desires = chart_repository.get_chart_desires_by_chart_ids(chart_ids)
        maps = chart_repository.get_map_by_chart_ids(chart_ids)
        colours = chart_repository.get_dataset_colour_by_chart_ids(chart_ids)
        filters = chart_repository.get_dataset_filter_by_chart_ids(chart_ids)
        marklines = chart_repository.get_dataset_markline_by_chart_ids(chart_ids)
        if marklines:
            for markline in marklines:
                if markline['config']:
                    markline['config'] = json.loads(markline['config'])
        chart_params = chart_repository.get_dataset_chart_params(chart_ids)

        # 收集数据集字段id
        self._collect_dataset_field_id_list(nums, field_key_name='num')
        self._collect_dataset_field_id_list(dims, field_key_name='dim')
        self._collect_dataset_field_id_list(comparisons)
        self._collect_dataset_field_id_list(filters)
        self._collect_dataset_field_id_list(desires)
        self._collect_dataset_field_id_list(marklines)
        self._collect_dataset_field_id_list(layers)
        self._collect_dataset_field_id_list(maps)
        self._collect_dataset_field_id_list(colours)
        self._collect_dataset_field_id_list(chart_params)

        # 批量获取数据集字段数据
        self._assign_dataset_field_data()

        result = {
            "nums": self._assign_chart_section_data(nums, field_key_name="num"),
            "dims": self._assign_chart_section_data(dims, field_key_name="dim"),
            "comparisons": self._assign_chart_section_data(comparisons),
            "filters": self._assign_chart_section_data(filters),
            "desires": self._assign_chart_section_data(desires),
            "marklines": self._assign_chart_section_data(marklines),
            "layers": self._assign_chart_section_data(layers),
            "maps": self._assign_chart_section_data(maps),
            "colours": self._assign_chart_section_data(colours),
            "field_sorts": chart_repository.get_dataset_field_sorts_by_chart_ids(chart_ids),
            "chart_params": self._assign_chart_section_data(chart_params),
        }

        return result

    def _assign_dataset_field_data(self):
        """
        调用数据集提供的服务获取数据集字段数据
        :return:
        """
        if self.dataset_field_id_list:
            query_dataset_field_list = external_query_service.get_multi_dataset_fields(self.dataset_field_id_list)
            if query_dataset_field_list:
                self.dataset_field_dict = {
                    item.get('id'): item
                    for item in query_dataset_field_list
                    if item.get('id') not in self.dataset_field_dict
                }

    def _collect_dataset_field_id_list(self, data, field_key_name='dataset_field_id'):
        """
        收集数据集字段id
        :param data: 需要收集数据集字段的数据
        :param field_key_name: 收集的来源字段名称
        :return:
        """
        if not data or not isinstance(data, list):
            return
        for item in data:
            if item.get(field_key_name) and item.get(field_key_name) not in self.dataset_field_id_list:
                self.dataset_field_id_list.append(item.get(field_key_name))

    def _assign_chart_section_data(self, section_data, field_key_name='dataset_field_id'):
        """
        拼装数据集字段数据
        :param section_data: 需要处理的单图组成数据
        :param field_key_name: 默认字段名是dataset_field_id
        :return:
        """
        _fields = [
            'id',
            'alias_name',
            'field_group',
            'dataset_id',
            'data_type',
            'col_name',
            'expression',
            'type',
            'visible',
        ]
        if not section_data:
            return section_data
        for single_item in section_data:
            dataset_field_id = single_item.get(field_key_name, '')
            single_dataset_field_data = proxy_dataset_service.switch_to_alias_name(
                self.dataset_field_dict.get(dataset_field_id)
            )
            if not single_dataset_field_data:
                continue
            for single_field in _fields:
                field_key = 'dataset_field_id' if single_field == 'id' else single_field
                single_item.update({field_key: single_dataset_field_data.get(single_field)})
        return section_data

    @staticmethod
    def _clear_dashboard_jump(sub_chart_list, sub_chart_id, record):
        dataset = dataset_repository.get_dataset(sub_chart_list['source'])
        if dataset:
            content = json.loads(dataset.get('content'))
            if (
                dataset.get('type') == DatasetType.Label.value
                and content
                and not label_service.get_label_sync_detail(content.get('label_id'))
            ):
                record += flow_service.delete_flow(sub_chart_list['id'])
            for num in chart_repository.get_nums_by_chart_id(sub_chart_list['id']):
                dashboard_repository.clear_dashboard_jump(
                    sub_chart_list['id'], num.get('num'), DashboardDatasetFieldType.Num.value
                )
            for dim in chart_repository.get_dims_by_chart_id(sub_chart_list['id']):
                dashboard_repository.clear_dashboard_jump(
                    sub_chart_list['id'], dim.get('dim'), DashboardDatasetFieldType.Dim.value
                )
        return record

    def delete_chart(self, chart_id):
        """
        删除单图
        :param chart_id:
        :return:
        """
        chart_list = []
        chart_data = chart_repository.get_chart_by_chart_id(chart_id)
        level_code = chart_data.get("level_code") if chart_data else ""
        operate_chart_id = chart_data.get("id") if chart_data else ""

        record = 0
        if level_code:
            chart_list = chart_repository.get_penetrate_by_chart_id(level_code)
        else:
            chart_list.append({'id': operate_chart_id})

        for sub_chart_id in chart_list:
            sub_chart_list = chart_repository.get_chart_by_chart_id(sub_chart_id["id"])
            record = self._clear_dashboard_jump(sub_chart_list, sub_chart_id, record)

            record += repository.delete_data('dashboard_chart_params', {'dashboard_chart_id': sub_chart_list['id']})
            record += repository.delete_data(
                'dashboard_chart_params_jump', {'dashboard_chart_id': sub_chart_list['id']}
            )
            record += repository.delete_data('dashboard_chart_dim', {'dashboard_chart_id': sub_chart_list['id']})
            record += repository.delete_data('dashboard_chart_num', {'dashboard_chart_id': sub_chart_list['id']})
            record += repository.delete_data('dashboard_chart_map', {'dashboard_chart_id': sub_chart_list['id']})
            record += repository.delete_data('dashboard_chart_colour', {'dashboard_chart_id': sub_chart_list['id']})
            record += repository.delete_data('dashboard_chart_filter', {'dashboard_chart_id': sub_chart_list['id']})
            record += repository.delete_data(
                'dashboard_chart_filter_relation', {'dashboard_chart_id': sub_chart_list['id']}
            )
            record += repository.delete_data('dashboard_chart_markline', {'dashboard_chart_id': sub_chart_list['id']})
            record += repository.delete_data('dashboard_chart', {'id': sub_chart_list['id']})
            record += repository.delete_data(
                'dashboard_chart_penetrate_relation', {'dashboard_chart_id': sub_chart_list['id']}
            )
            record += repository.delete_data('dashboard_chart_field_sort', {'dashboard_chart_id': sub_chart_list['id']})
            self.delete_chart_initiator_and_responder_selector(sub_chart_id['id'])
            # 删除筛选器配置数据
            self.delete_chart_initiator_and_responder_filter(sub_chart_id['id'])
            record += self.delete_chart_result_table(sub_chart_list['id'])

        return record

    @staticmethod
    def delete_chart_initiator_and_responder_selector(chart_id, selector_list=None):
        """
        删除主动与被动关联关系的记录
        :param str chart_id: 单图id
        :param list selector_list: 联动关联数据
        :return:
        """
        chart_repository.delete_selector_chart(chart_id)
        if selector_list is None:
            selector_list = chart_repository.get_chart_selector_list_by_chart_id(chart_id)
        if selector_list:
            chart_repository.delete_chart_selector_field([item.get('id') for item in selector_list])

    @staticmethod
    def delete_chart_initiator_and_responder_filter(chart_id):
        """
        删除主动与被动关联关系的记录
        :param str chart_id: 单图id
        :return:
        """
        chart_repository.delete_component_filter_chart(chart_id)
        chart_repository.delete_component_filter_field(chart_id)

    def delete_chart_result_table(self, chart_id):
        """
        删除结果表
        :param str chart_id:
        :return:
        """
        result_table_name = self.get_chart_result_table_name(chart_id)
        record = 0
        if dataset_service.dataset_table_exists(result_table_name):
            record += repository.delete_data_db_table(result_table_name)
        result_table_name += '_detail'
        if dataset_service.dataset_table_exists(result_table_name):
            record += repository.delete_data_db_table(result_table_name)
        return record
