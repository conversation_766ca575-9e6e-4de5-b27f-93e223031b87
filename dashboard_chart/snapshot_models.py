#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : snapshot_models.py
# @Author: guq  
# @Date  : 2021/8/26
# @Desc  :
import json

from dashboard_chart.services import metadata_service
# from dashboard_chart.repositories import snapshot_repository
from dmplib.utils.errors import UserError
from components.snapshot_service import (
    register_snapshot_service,
    SnapShotSingleTableBase,
)


# 表snapshot_dashboard_metadata
@register_snapshot_service
class DashboardMetadataSnapShot(SnapShotSingleTableBase):
    name = 'dashboard_metadata'
    target_table = 'snapshot_dashboard_metadata'
    exclude_fields = ['increment_id']
    filter_conditions = {'dashboard_id': 'self*dashboard_id'}

    # def get_needed_components(self, meta: dict, installed_chart_codes: list):
    #     # 获取当前终端类型的所有组件
    #     terminal_type = metadata_service.get_dashboard_runterminal(meta)
    #     component_metadata_model = metadata_service.DashboardInstalledComponentNodeModel(
    #         node_name='installed_component', runterminal=terminal_type
    #     )
    #     all_components = component_metadata_model.get_data()
    #
    #     # 默认的预览页面返回的全部的组件，实际拍照的组件可能不需要这么多，只拍使用到的组件
    #     installed_chart_codes += ['hd_mediator', 'hd_wrapper']
    #     return [
    #         component for component in all_components
    #         if component['package'] in installed_chart_codes
    #     ]

    def combine_data(self) -> list:
        data = []
        dashboard_ids = self.dashboard_id
        if isinstance(dashboard_ids, str):
            dashboard_ids = [dashboard_ids]
        for dashboard_id in dashboard_ids:
            msg, meta = metadata_service.get_screens_release_metadata_v2(snapshot_id=dashboard_id, token_data=None)
            if not meta:
                raise UserError(message='dashboard查询为空')
            # meta = data[0]
            # installed_chart_codes = snapshot_repository.batch_get_chart_code_by_dashboard_id(self.dashboard_id) or []
            # installed_chart_codes = [chart['chart_code'] for chart in installed_chart_codes]
            # meta['installed_component'] = self.get_needed_components(meta, installed_chart_codes)
            meta = json.dumps(meta, ensure_ascii=False)
            data.append({'dashboard_id': dashboard_id, 'metadata': meta})
        return data


# 查询暂时用不上
# 表 snapshot_dashboard
@register_snapshot_service
class DashboardSnapShot(SnapShotSingleTableBase):
    name = 'dashboard'
    target_table = 'snapshot_dashboard'
    exclude_fields = ['increment_id']
    filter_conditions = {'id': 'self*dashboard_id'}

#
# # 表 snapshot_dashboard_chart
# @register_snapshot_service
# class DashboardChartSnapShot(SnapShotSingleTableBase):
#     name = 'dashboard_chart'
#     target_table = 'snapshot_dashboard_chart'
#     exclude_fields = ['increment_id']
#     filter_conditions = {'dashboard_id': 'self*dashboard_id'}


# 表 dashboard_released_snapshot_dashboard
@register_snapshot_service
class DashboardReleasedSnapShot(SnapShotSingleTableBase):
    name = 'dashboard_released_snapshot_dashboard'
    target_table = 'snapshot_dashboard_released_snapshot_dashboard'
    exclude_fields = ['increment_id']
    filter_conditions = {'id': 'self*dashboard_id'}


# 表 dashboard_released_snapshot_chart
@register_snapshot_service
class DashboardReleasedChartSnapShot(SnapShotSingleTableBase):
    name = 'dashboard_released_snapshot_chart'
    target_table = 'snapshot_dashboard_released_snapshot_chart'
    exclude_fields = ['increment_id']
    filter_conditions = {'dashboard_id': 'self*dashboard_id'}


# # 表 dataset_tables_collection
# @register_snapshot_service
# class DatasetTablesCollectionSnapShot(SnapShotSingleTableBase):
#     name = 'dataset_tables_collection'
#     target_table = 'snapshot_dataset_tables_collection'
#     exclude_fields = ['increment_id']
#     filter_conditions = {'dataset_id': 'self*dataset_id'}


# 表 dashboard_chart_num
@register_snapshot_service
class DashboardReleasedChartNumSnapShot(SnapShotSingleTableBase):
    name = 'dashboard_chart_num'
    target_table = 'snapshot_dashboard_chart_num'
    exclude_fields = ['increment_id']
    filter_conditions = {'dashboard_id': 'self*dashboard_id'}


# 表 dashboard_chart_dim
@register_snapshot_service
class DashboardReleasedChartDimSnapShot(SnapShotSingleTableBase):
    name = 'dashboard_chart_dim'
    target_table = 'snapshot_dashboard_chart_dim'
    exclude_fields = ['increment_id']
    filter_conditions = {'dashboard_id': 'self*dashboard_id'}


# 表 dashboard_chart_dim
@register_snapshot_service
class DashboardFilterSnapShot(SnapShotSingleTableBase):
    name = 'dashboard_filter'
    target_table = 'snapshot_dashboard_filter'
    exclude_fields = ['increment_id']
    filter_conditions = {'dashboard_id': 'self*dashboard_id'}
