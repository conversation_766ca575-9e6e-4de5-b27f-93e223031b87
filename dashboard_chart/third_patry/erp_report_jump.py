import hug
from loguru import logger

from dashboard_chart.third_patry.jump import Jump
from ppt.services.ppt_service import build_ppt_redirect_url


# 打印服务的跳转
class ERPAdminSiteJump(Jump):
    def get_tree_data(self):
        return

    def check_role(self):
        return True

    def redirect(self, page_type = 'PRINT_TEMPLATE_PAGE'):
        # {报表制作后台地址}/sso/dmp?token={token}
        url = build_ppt_redirect_url("/sso/dmp", from_type='erp_report', params={}, backend=True)
        logger.info(f"erp后台地址URL：{url}")
        hug.redirect.to(url)
