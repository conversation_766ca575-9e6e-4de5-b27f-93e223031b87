from abc import ABCMeta, abstractmethod


# 第三方跳转抽象类，主要定义了获取数据和跳转的功能
class Jump(metaclass=ABCMeta):

    def __init__(self, request, **kwargs):
        self.request = request
        self.kwargs = kwargs

    @abstractmethod
    def get_tree_data(self):
        """
        获取树形结构的数据
        :params : {
            type:""//第三方类型
        }
        :return :[{
            "id": "",//数据id
            "name": "", //显示名
            "parentId": "", //显示名
            "use": 1, //1:能被选选中，2不能选中(如：分类，目录)
            "params": [{//需要被关联的参数
                "id":"",
                "name":""
            }]
        }]

        """
        pass

    @abstractmethod
    def redirect(self, request):
        """
        重定向
        :params:{
            "type":"",//第三方类型
            "target":"",//第三方数据ID
            "values":{key:value}//字段匹配的值
        }
        :return str:重定向
        """
        pass

    def check_role(self):
        raise NotImplementedError

    def view(self):
        raise NotImplementedError
