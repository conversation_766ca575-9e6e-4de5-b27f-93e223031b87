#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> 2018/9/8
"""
import time
import traceback

import curlify
import requests
import urllib.parse
import datetime

import app_celery
from base.enums import ChartCopyTransactionStatus, ApplicationType
from components.functions import str_reverse
from components.ingrate_platform import IngratePlatformApi
from components.utils import system_arch
from dashboard_chart.models import ChartDataModel, ReleaseModel, DashboardModel, ScreenModel, PdfExportModel, DashboardSortModel
from dashboard_chart.repositories import chart_copy_repository, dashboard_repository, chart_repository
from dashboard_chart.services import (
    chart_service,
    metadata_service,
    dashboard_lock_service,
    dashboard_service,
    released_dashboard_service,
    components_service,
    screen_dashboard_service,
    dashboard_login_service,
    dashboard_openapi_service,
    chart_model_adapter,
    pdf_service,
)
from dmplib.utils.strings import seq_id
from dashboard_chart.services.large_dashboard_service import (
    DashboardUtil, FolderMoveOldScreenUtil, get_move_task_data
)
from dashboard_chart import external_service
from dashboard_chart.services.download import download_service, chart_column_download_service, download_task_generator
import logging

from dashboard_chart.services.metadata_service import set_user_active_time
from dashboard_chart.utils import chart_utils
from dmplib.hug import APIWrapper
from dmplib.hug import g
from base.service import filter_permission_tree
from base.errors import UserError
from base.dmp_constant import ALL_REPORT_DATA_ACTIONS_DICT, CHART_COPY_TRANSACTION_TIME_OUT
from dmplib.saas.project import set_correct_project_code
from user.services.ingrate_service import get_page_url
from user_log.models import UserLogModel
from rbac.validator import PermissionValidator
from dataset.models import DatasetFieldModel
from dataset.services import advanced_field_service
from dmplib import config
import json
import jwt
import hug
from components.url import url_add_param
from components.utils import str_equal
from components.storage_setting import get_storage_type
from components.global_utils import kwargs_gzip_aes_decode
from app_celery import async_run_chart_copy
from user.services import user_service
from dashboard_chart.services.dashboard_service import request_args_setting, adapt_function_permission
from user.services import reporting_sso_service
from dashboard_chart.utils.decorators import data_of_last_version
from components.utils import debugger_and_logger

api = APIWrapper(__name__)
logger = logging.getLogger(__name__)
local_debugger = debugger_and_logger(__name__)

class DataApiWrapper(APIWrapper):
    __slots__ = ["_route", "_data_route"]

    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._route = hug.http(api=self.api)
        self._data_route = None

    @property
    def data_route(self):
        from dashboard_chart.services.authority_service import verify_data_handle
        if not self._data_route:
            # pylint: disable=E1120
            self._data_route = hug.http(api=self.api, requires=verify_data_handle(None))
        return self._data_route


dataApi = DataApiWrapper(__name__)


@api.admin_route.post("/chart/data")
@data_of_last_version(is_cache=False)
@request_args_setting
def get_dashboard_chart_data(request, response, *args, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/chart/data 获取单图结果集
    @apiParam formData {string}  dashboard_id 报告id
    @apiParam formData {string}  chart_params json格式化的报告查询条件
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    cookie = request.cookies
    g.cookie = cookie
    if hasattr(g, "code") and g.code:
        # 获取存储类型
        setattr(g, "storage", get_storage_type(g.code))
    result = chart_service.batch_get_chart_data(kwargs.get("chart_params"))

    return True, "", result


@api.admin_route.post("/chart/get_data")
def dashboard_chart_get_data(request, **kwargs):
    """
    /**
    @apiVersion 1.0.2
    @api {post} /api/dashboard_chart/chart/get_data 获取图表数据
    @apiGroup  dashboard
    @apiBodyParam {
        "dashboard_id": "39ec3bd4-a76a-7aa4-dfdf-089bb93e3519",
        "chart_params": [{
            "id": "8605ccca-39bd-11e9-9c25-55e9f21f2166",
            "report_id": "39ec3bd4-a76a-7aa4-dfdf-089bb93e3519",
            "dashboard_id": "39ec3bd4-a76a-7aa4-dfdf-089bb93e3519",
            "chart_code": "pagination_table_yk",
            "data_logic_type_code": "default",
            "conditions": [],
            "penetrate_conditions": [
                {
                    "col_name": "col1",
                    "col_value": "第一事业部",
                    "operator": "=",
                    "dim": {
                        "visible": 1,
                        "dashboard_chart_id": "bfd64f08-3644-11e9-8dff-a1dee455e1fc",
                        "alias_name": "事业部",
                        "field_group": "维度",
                        "expression": "",
                        "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
                        "dataset_field_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
                        "type": "普通",
                        "dim": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
                        "alias": "事业部",
                        "content": "",
                        "col_name": "col1",
                        "data_type": "字符串",
                        "id": "aba177cd-3645-11e9-8dff-a1dee455e1fc",
                        "rank": 0,
                        "formula_mode": "",
                        "sort": "",
                        "note": ""
                    }
                }
            ],
            "penetrate_filter_conditions": [],
            "filter_conditions": [],
            "chart_filter_conditions": [
                {
                    "chart_id": "34d5e063-398b-11e9-9e4f-2b56e5247c36",
                    "col_value": "[\"海亮公司\"]",
                    "col_name": "col1",
                    "dim": {
                        "visible": 1,
                        "dashboard_chart_id": "34d5e063-398b-11e9-9e4f-2b56e5247c36",
                        "alias_name": "事业部",
                        "field_group": "维度",
                        "expression": "",
                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "dataset_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                        "type": "普通",
                        "dim": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                        "alias": "事业部",
                        "content": "",
                        "col_name": "col1",
                        "data_type": "字符串",
                        "id": "183b2e31-39bb-11e9-88a6-6369aeba51ec",
                        "rank": 0,
                        "formula_mode": "",
                        "sort": "",
                        "note": ""
                    },
                    "operator": "in"
                }
            ],
            "chart_linkage_conditions": [
                {
                    "chart_id": "1b6b1c1f-35c3-11e9-b052-cd171e183a88",
                    "col_value": "海亮公司",
                    "col_name": "col1",
                    "dim": {
                        "visible": 1,
                        "dashboard_chart_id": "1b6b1c1f-35c3-11e9-b052-cd171e183a88",
                        "alias_name": "事业部",
                        "field_group": "维度",
                        "expression": "",
                        "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "dataset_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                        "type": "普通",
                        "dim": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                        "alias": "事业部",
                        "content": "",
                        "col_name": "col1",
                        "data_type": "字符串",
                        "id": "76c61a6e-35c3-11e9-b052-cd171e183a88",
                        "rank": 0,
                        "formula_mode": "",
                        "sort": "",
                        "note": ""
                    },
                    "operator": "="
                }
            ],
            "dashboard_conditions": [{
                "col_name": "col1",
                "operator": "=",
                "col_value": "第三事业部"
            }],
            "query_vars": [],
            "code": "test"
        }],
        "code": "test"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
                "8605ccca-39bd-11e9-9c25-55e9f21f2166":{
                    "execute_status": 200,
                    "sql": "sql语句",
                    "msg_code": 1001,
                    "data": [{
                        "col16": 6.0,
                        "col2": "\u6d59\u6c5f\u7701",
                        "col1": "\u7b2c\u4e09\u4e8b\u4e1a\u90e8",
                        "day_col9": "2014-02-09"
                    }],
                    "data_last_update_time": "20190327184155",
                    "sql_execute_time": 29,
                    "conditions": [{
                        "alias_name": "\u4e8b\u4e1a\u90e8",
                        "operator": "in",
                        "col_value": "[\"\u6d77\u4eae\u516c\u53f8\",\"\u7b2c\u4e00\u4e8b\u4e1a\u90e8\"]",
                        "format": "",
                        "dataset_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                        "col_name": "col1",
                        "condition_type": 1,
                        "formula_mode": "",
                        "data_type": "\u5b57\u7b26\u4e32"
                    }, {
                        "alias_name": "\u4e8b\u4e1a\u90e8",
                        "operator": "!=",
                        "col_value": "\u6d77\u4eae\u516c\u53f8",
                        "format": "",
                        "dataset_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
                        "col_name": "col1",
                        "condition_type": 5,
                        "formula_mode": "",
                        "data_type": "\u5b57\u7b26\u4e32"
                    }],
                    "msg": "",
                    "pagination": {
                        "page_size": 100,
                        "order": 0,
                        "total": 100,
                        "page": 1
                    },
                    "dataset_versions": {
                        "version": "20190327184155_39",
                        "data_version": "20190327184155"
                    },
                    "marklines": []
                }
          }
        }
    **/
    """
    cookie = request.cookies
    g.cookie = cookie
    model = ChartDataModel(**kwargs)
    model.bootstrap_flag = True
    chart_model_adapter.adapt_get_data_model(model)
    result = chart_service.get_chart_result(model=model, no_data=False)
    return True, result.get("msg", ""), result


@api.admin_route.post("/chart/item_list")
def dashboard_chart_get_item_list(request, **kwargs):
    """
    获取图表数据
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = ChartDataModel(**kwargs)
    dataset_field_ids = kwargs.get("dataset_field_ids", [])
    result = chart_service.get_item_list(model, dataset_field_ids=dataset_field_ids)
    return True, "ok", result


@api.admin_route.post("/chart/get_total")
@data_of_last_version(mode='total', is_cache=False)
@request_args_setting
def get_total(request, response, **kwargs):
    """
    获取图表数据
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = ChartDataModel(**kwargs)
    result = chart_service.get_total(model)
    return True, "", result


@api.admin_route.post('/dashboard_lock/checkin')
def checkin_dashboard(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/dashboard_lock/checkin 报告锁定检查
    @apiGroup  dashboard
    @apiBodyParam {
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "account{账号名}": "cc",
            "code{代码}": "1001",
            "msg{消息}": "",
            "user_name{用户名}": "cc"
          }
        }
    */
    """
    dashboard_id = kwargs.get('dashboard_id')
    if not dashboard_id:
        raise UserError(message="缺少报告ID")
    data = dashboard_lock_service.checkin_dashboard(dashboard_id=dashboard_id)
    return True, '', data


@api.admin_route.post('/dashboard_lock/unlock')
@api.admin_route.get('/dashboard_lock/unlock')
def unlock_dashboard(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/dashboard_lock/unlock 解锁报告
    @apiGroup  dashboard
    @apiBodyParam {
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "account{账号名}": "cc",
            "code{代码}": "1001",
            "msg{消息}": "",
            "user_name{用户名}": "cc"
          }
        }
    */
    """
    dashboard_id = kwargs.get('dashboard_id')
    if not dashboard_id:
        raise UserError(message="缺少报告ID")
    data = dashboard_lock_service.unlock_dashboard(dashboard_id=dashboard_id)
    return True, '', data


def __convert_to_list_param(param):
    if isinstance(param, str):
        param = param.strip(' ')
        if param == '':
            return []
        return param.split(',')
    if isinstance(param, list):
        return (','.join(param)).split(',')
    if isinstance(param, tuple):
        return (','.join(param)).split(',')
    raise UserError(message='非法的参数！')


@api.admin_route.get('/list')
def get_dashboard_list(**kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {get} /api/dashboard_chart/list 报告列表
    @apiGroup  dashboard
    @apiParam query {string}  include_platforms 包含的平台名称
    @apiParam query {string}  exclude_platforms 排除的平台名称
    @apiParam query {string}  reverse 排序方式0/1
    @apiParam query {string}  parent_id 父级报告ID
    @apiParam query {string}  is_multiple_screen 是否是多屏
    @apiParam query {string}  status 状态 0: 未发布 1: 已发布  2: 更新发布(最后一次发布后有改动)
    @apiParam query {string}  begin_time 开始时间
    @apiParam query {string}  end_time 结束时间
    @apiParam query {string}  order_by 排序字段
    @apiParam query {string}  create_type 创建类型
    @apiParam query {string}  new_layout_type 布局模式
    @apiParam query {string}  terminal_type 终端类型
    @apiParam query {string}  file_type (
                                folder: 只显示文件夹，
                                large_screen: 只显示大屏报告，
                                dashboard: 只显示仪表板，
                                new_mobile: 只显示移动报告)
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "route{路径}": [
                {
                    "id{报告ID}": "39eca73d-2386-4920-5758-95e1be8fd7e0",
                    "name{报告名称}": "苏晓测试"
                }
            ],
            "tree":[
                {
                    "actions":[
                        {
                            "action_code": "edit",
                            "action_name": "编辑"
                        }
                    ],
                    "cover": "",
                    "create_type{创建类型}": 0,
                    "created_by": "ycm",
                    "user_name": "袁成明",
                    "created_on": "2019-01-04 10:30:48",
                    "description": "",
                    "distribute_type": 0,
                    "edit_on": "2019-01-04 10:30:48",
                    "edit_status": 0,
                    "icon": "",
                    "id": "39eb28df-bac9-1c95-6088-77bc29789cb3",
                    "is_multiple_screen": 1,
                    "level_code": "1438-",
                    "modified_on": "2019-02-15 15:44:05",
                    "name": "20个多屏",
                    "new_layout_type{布局类型}": 0,
                    "parent_id": "",
                    "platform": "pc",
                    "released_on": "2019-03-15 09:53:31",
                    "status": 1,
                    "sub": [],
                    "type": "FILE"
                }
            ]
          }
        }
    */
    """
    results = dashboard_service.get_dashboard_list(**kwargs)
    logger.debug("get_dashboard_list params:%s", kwargs)

    # 邮件订阅选择报告时，需要有该报告的编辑权限
    if kwargs.get('permission') and isinstance(results, dict):
        action = ALL_REPORT_DATA_ACTIONS_DICT.get(kwargs.get('permission'))
        results['tree'] = filter_permission_tree(results.get('tree'), action)
    return True, '', results


@api.admin_route.get('/large_screen_list')
def get_large_screen_list(**kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {get} /api/dashboard_chart/large_screen_list 大屏报告列表
    @apiGroup  dashboard
    @apiParam query {string}  include_platforms 包含的平台名称
    @apiParam query {string}  exclude_platforms 排除的平台名称
    @apiParam query {string}  reverse 排序方式0/1
    @apiParam query {string}  parent_id 父级报告ID
    @apiParam query {string}  status 状态 0: 未发布 1: 已发布  2: 更新发布(最后一次发布后有改动)
    @apiParam query {string}  begin_time 开始时间
    @apiParam query {string}  end_time 结束时间
    @apiParam query {string}  order_by 排序字段
    @apiParam query {string}  create_type 创建类型
    @apiParam query {string}  new_layout_type 布局模式
    """
    results = dashboard_service.get_large_screen_list(**kwargs)

    # 邮件订阅选择报告时，需要有该报告的编辑权限
    if kwargs.get('permission') and isinstance(results, dict):
        action = ALL_REPORT_DATA_ACTIONS_DICT.get(kwargs.get('permission'))
        results['tree'] = filter_permission_tree(results.get('tree'), action)
    return True, '', results


@api.admin_route.get('/search')
def search_dashboard_list(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /api/dashboard_chart/search 搜索报告
    @apiGroup  dashboard
    @apiParam query {string}  name 报告名称
    {
        "name": "",
        "file_type": "",
        "status": "",
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": [
              {
                "cover{封面}": "",
                "create_type": 0,
                "created_by": "test",
                "created_on": "2019-03-06 10:05:36",
                "distribute_type": 0,
                "edit_on": "2019-01-04 10:30:48",
                "edit_status": 0,
                "id{报告ID}": "39ec62ec-9236-4420-5ec2-b2c25ed8d8f6",
                "level_code": "1509-",
                "name{报告名称}": "曹显龙-新版测试",
                "new_layout_type": 0,
                "parent_id": "",
                "platform{适应平台}": "pc",
                "released_on": "2019-01-04 10:30:48",
                "status": 0,
                "type": "FOLDER"
            }
          ]
        }
    */
    """
    application_type = kwargs.get('application_type')
    adapt_function_permission(dashboard_service.search_dashboard, application_type, 'view')
    return True, '', dashboard_service.search_dashboard(**kwargs)


@api.admin_route.get('/screen/list')
def get_screen_list(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/screen/list 多屏列表
    @apiGroup  dashboard
    @apiParam query {string}  parent_id 父级报告ID
    @apiParam query {string}  reverse 是否反向排序
    @apiParam query {string}  status 状态
    @apiParam query {string}  order_by 排序
    @apiParam query {string}  create_type 报告创建类型 0默认 1重构版本
    @apiParam query {string}  new_layout_type 布局类型
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": [{
                "actions":[
                    {
                        "action_code": "edit",
                        "action_name": "编辑"
                    }
                ],
                "cover": "",
                "create_type{创建类型}": 0,
                "created_by": "ycm",
                "created_on": "2019-01-04 10:30:48",
                "description": "",
                "distribute_type": 0,
                "edit_on": "2019-01-04 10:30:48",
                "edit_status": 0,
                "icon": "",
                "id": "39eb28df-bac9-1c95-6088-77bc29789cb3",
                "is_multiple_screen": 1,
                "level_code": "1438-",
                "modified_on": "2019-02-15 15:44:05",
                "name": "20个多屏",
                "new_layout_type{布局类型}": 0,
                "parent_id": "",
                "platform": "pc",
                "released_on": "2019-03-15 09:53:31",
                "status": 1,
                "sub": [],
                "type": "FILE"
          }]
        }
    */
    """
    return True, '', dashboard_service.get_screen_list(**kwargs)


@api.admin_route.get('/v2/screens_metadata')
@metadata_service.format_dashboard_jump_error_info
@chart_service.return_dashboard_chart_config
def handle_get_screens_metadata(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/dashboard_chart/v2/screens_metadata 获取多屏报告元数据
    @apiParam query {string}  id 报告ID（多屏ID）
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    code = set_correct_project_code(kwargs.get('code'))
    set_user_active_time(code)
    dashboard_id = kwargs.get('id', '')
    if not dashboard_id:
        raise UserError(message="缺少报告ID")

    # 加入/api/dashboard_chart/check_permission接口校验逻辑
    token = request.cookies.get('token')  # 获取token，用于校验
    check_flag, errmsg, token_data = dashboard_service.check_token(token)
    if not check_flag:
        raise UserError(message=errmsg)
    g.cookie = {'token': token}
    # 目前默认是下载权限
    # flag = dashboard_service.check_permission(dashboard_id, token_data, kwargs.get("action", "download"))
    # 上面的历史逻辑存在问题，运行时的报表不应该调用运行时的权限校验以及报表关联查询
    flag = True
    is_arm = system_arch()

    # 测试环境调试入口
    new_meta_switch = request.cookies.get("new_meta_switch")
    try:
        if new_meta_switch is not None and int(new_meta_switch) == 1:
            meta = metadata_service.get_screens_preview_metadata(dashboard_id)
            return True, "", {"meta": meta, "is_new_meta": True, "download": flag, "is_arm": is_arm}
    except Exception as e:
        raise UserError(message=str(e))

    msg, result = metadata_service.get_screens_preview_metadata_v2(dashboard_id=dashboard_id)
    data = {'meta': result, "download": flag, "is_arm": is_arm}
    return True, msg, data


@api.admin_route.get('/v2/dashboard_metadata')
def handle_dashboard_metadata_v2(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/dashboard_chart/v2/dashboard_metadata 获取单个报告元数据
    @apiParam query {string}  id 报告ID
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    dashboard_id = kwargs.get('id', '')
    if not dashboard_id:
        raise UserError(message="缺少报告ID")
    # 测试环境调试入口
    new_meta_switch = request.cookies.get("new_meta_switch")
    try:
        if new_meta_switch is not None and int(new_meta_switch) == 1:
            meta = metadata_service.get_dashboard_preview_metadata(dashboard_id)
            return True, "", {"meta": meta, "is_new_meta": True}
    except Exception as e:
        raise UserError(message=str(e))

    msg, result = metadata_service.get_dashboard_preview_metadata_v2(dashboard_id=dashboard_id)
    data = {'meta': result}
    return True, msg, data


@api.admin_route.post('/formula_expression/check')
def subtotal_col_formula_expression_check(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /v2/metadata/subtotal_col_formula_expression/check 单独校验小计的公式计算
    @apiParam query {string}  id 报告ID
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {}
    }
    **/
    """
    chart_service.check_subtotal_col_formula_expression(kwargs)
    return True, '校验成功', {}


@api.admin_route.post('/dashboard_edit')
@kwargs_gzip_aes_decode()
def handle_dashboard_edit(request, **kwargs):
    """
    报告元数据编辑接口
    :param request:
    :param kwargs:
    :return:
    """
    metadata = kwargs.get('metadata')
    if not metadata:
        raise UserError(message="请指定需要更改的元数据!")
    screen_dashboard_service.check_project_edit_permission(metadata)
    screens = metadata.get('screens')
    if not isinstance(screens, list) or len(screens) < 1:
        raise UserError(message="参数数据格式非法!")
    metadata['first_report'] = screens[0]
    metadata['screens'] = []
    result, errors = dashboard_service.update_metadata(metadata)
    data = {"errors": errors if errors else []}
    chart_service.record_metadata_edit_data(kwargs)
    return result, '', data


@api.route.get('/check_permission')
def dashboard_check_permission(request, **kwargs):
    """

    :param request:
    :param kwargs:
    :return:
    """
    token = request.cookies.get('token')
    check_flag, errmsg, data = dashboard_service.check_token(token)
    if not check_flag:
        return check_flag, errmsg, None
    g.cookie = {'token': token}
    code = data.get('tenant_code') if data.get('tenant_code') else kwargs.get('code')
    set_correct_project_code(code)

    # 目前默认是下载权限
    # flag = dashboard_service.check_permission(kwargs.get("dashboard_id"), data, kwargs.get("action", "download"))
    # 上面的历史逻辑存在问题，运行时的报表不应该调用运行时的权限校验以及报表关联查询
    flag = True
    return True, None, {"download": flag}


@api.route.post('/export/pdf')
def export_pdf(request, **kwargs):
    """
    导出pdf
    """
    token = request.cookies.get('token')
    model = PdfExportModel(**kwargs)

    return pdf_service.generate_pdf_task(token, model, kwargs)


@api.admin_route.post('/export/clear_screenshot')
def clear_screenshot(**kwargs):
    """
    清除报告的截图数据
    """
    dashboard_id = kwargs.get("dashboard_id", "")
    clear_type = kwargs.get("clear_type", "")
    return pdf_service.clear_screenshot(dashboard_id, clear_type)


@api.route.get('/export/message')
def export_message(request, **kwargs):
    """
    获取pdf导出进度
    """
    token = request.cookies.get('token')
    return pdf_service.get_export_message(token, kwargs)


@api.route.get('/download/message')
def download_message(request, **kwargs):
    """
    获取下载进度
    :param request:
    :param kwargs:
    :return:
    """
    token = request.cookies.get('token')
    return download_service.get_download_message(token, kwargs)


@api.route.post('/download/export_chart_data')
def export_chart_data(request, **kwargs):
    """
    导出数据
    :return:
    """
    token = request.cookies.get('token')
    status, msg, result = download_task_generator.generate_download_task(token, kwargs)
    if status:
        download_task_generator.export_record_log(request, kwargs)
    return status, msg, result


@api.route.post('/download/dashboard_chart_common')
def download_dashboard_chart_common(request, **kwargs):
    """
    报告单图数据下载
    :param request:
    :param kwargs:
    :return:
    """
    token = request.cookies.get('token')
    return chart_column_download_service.dashboard_chart_data_download(token, kwargs)


@api.route.get('/download/message')
def download_message(request, **kwargs):
    """
    获取下载进度
    :param request:
    :param kwargs:
    :return:
    """
    token = request.cookies.get('token')
    return download_service.get_download_message(token, kwargs)


@api.admin_route.get('/dashboard_dataset_data')
def handle_dashboard_dataset_data(**kwargs):
    """
    获取报告下的数据集信息
    :param kwargs:
    :return:
    """
    dashboard_id = kwargs.get('dashboard_id', '')
    if not dashboard_id:
        raise UserError(message="缺少报告ID")
    data = dashboard_service.get_dashboard_dataset_data(dashboard_id)
    return True, '', data


@api.admin_route.post('/release')
def release(request, **kwargs):
    """
    发布报告
    :param request:
    :param kwargs:
    :return:
    """
    model = ReleaseModel(**kwargs)
    application_type = kwargs.get('application_type')
    adapt_function_permission(dashboard_service.release, application_type, 'edit')
    re = dashboard_service.release(model)
    if re:
        dashboard = dashboard_service.get_dashboard(model.id)
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'release',
                'id': kwargs.get('id'),
                'content': '{dashboard_type} [ {dashboard_name} ] 发布成功'.format(
                    dashboard_name=dashboard.get('name'),
                    dashboard_type=dashboard_service.get_dashboard_type_word(dashboard.get('type'), application_type),
                ),
            },
        )
    return True, '', re


@api.admin_route.post('/copy')
def copy_dashboard(request, **kwargs):
    """
    复制报告
    :param kwargs:
    :return:
    """

    dashboard_service.license_control()

    application_type = kwargs.get('application_type')
    adapt_function_permission(dashboard_service.copy_dashboard, application_type, 'copy')
    source_dashboard = dashboard_repository.get_dashboard(kwargs.get('dashboard_id')) or {}
    re, errors = dashboard_service.copy_dashboard(
        kwargs.get('dashboard_id'), kwargs.get('target_id'), kwargs.get('dashboard_name'),
        kwargs.get('target_dataset_id'), kwargs.get('is_copy_dataset')
    )
    if not errors:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'copy_dashboard',
                'id': kwargs.get('dashboard_id'),
                'content': '复制{dashboard_type} [ {dashboard_name} ] 为 [ {target_name} ] 成功'.format(
                    dashboard_name=source_dashboard.get('name'),
                    target_name=re.get('name'),
                    dashboard_type=dashboard_service.get_dashboard_type_word(source_dashboard.get('type'),
                                                                             application_type),
                ),
            },
        )
        return True, '复制成功', re
    else:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'copy_dashboard',
                'id': kwargs.get('dashboard_id'),
                'content': '复制{dashboard_type} [ {dashboard_name} ] 失败'.format(
                    dashboard_name=kwargs.get('dashboard_name'),
                    dashboard_type=dashboard_service.get_dashboard_type_word(source_dashboard.get('type'),
                                                                             application_type),
                ),

            },
        )
        return False, '复制失败', errors[0]


@api.admin_route.post('/update/dataset')
def update_new_dataset_id(request, **kwargs):
    """
    刷新报告数据集id
    :param kwargs:
    :return:
    """
    source_dashboard = dashboard_repository.get_dashboard(kwargs.get('dashboard_id')) or {}
    if not source_dashboard:
        raise UserError(message='当前数据集不存在！')
    re, errors = dashboard_service.update_new_dataset_id(kwargs.get('dashboard_id'), kwargs.get('target_dataset_id'))
    if not errors:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_new_dataset_id',
                'id': kwargs.get('dashboard_id'),
                'content': '更新dataset-id [ {dashboard_name} ] 成功'.format(
                    dashboard_name=source_dashboard.get('name')
                ),
            },
        )
        return True, '更新数据集id成功', re
    else:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_new_dataset_id',
                'id': kwargs.get('dashboard_id'),
                'content': '更新dataset-id[ {dashboard_name} ] 失败'.format(
                    dashboard_name=source_dashboard.get('name'),
                ),

            },
        )
        return False, '更新数据集id失败', errors[0]


@api.admin_route.post('/update/dataset_id')
def update_new_dataset_id_by_folder(request, **kwargs):
    """
    刷新报告数据集id
    :param request:
    :param kwargs:
    :return:
    """

    re, errors = dashboard_service.update_new_dataset_id_by_folder(kwargs.get('folder_id'), kwargs.get('is_recursion'),
                                                                   kwargs.get('is_delete_dataset'))
    if not errors:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_new_dataset_id',
                'id': kwargs.get('dashboard_id'),
                'content': '更新dataset-id [ {folder_id} ] 成功'.format(
                    folder_id=kwargs.get('folder_id')
                ),
            },
        )
        return True, '更新数据集id成功', re
    else:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_new_dataset_id',
                'id': kwargs.get('dashboard_id'),
                'content': '更新dataset-id [ {folder_id} ] 失败'.format(
                    folder_id=kwargs.get('folder_id')
                ),

            },
        )
        return False, '更新数据集id失败', re


@api.admin_route.post('/repair/dataset_id')
def update_new_dataset_id_by_folder(request, **kwargs):
    """
    刷新报告数据集id
    :param request:
    :param kwargs:
    :return:
    """
    dataset_map = kwargs.get('dataset_maps')
    report_id_list = kwargs.get('report_centers')
    dashboard_ids = kwargs.get('dashboards')
    if not dataset_map and not report_id_list and not dashboard_ids:
        return False, '参数dataset_map、report_id_list、dashboard_ids错误'
    re, errors = dashboard_service.repair_new_dataset(dataset_map, report_id_list, dashboard_ids)
    if not errors:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_new_dataset_id',
                'id': kwargs.get('dashboard_id'),
                'content': '更新dataset-id [ {folder_id} ] 成功'.format(
                    folder_id=kwargs.get('folder_id')
                ),
            },
        )
        return True, '更新数据集id成功', re
    else:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_new_dataset_id',
                'id': kwargs.get('dashboard_id'),
                'content': '更新dataset-id [ {folder_id} ] 失败'.format(
                    folder_id=kwargs.get('folder_id')
                ),

            },
        )
        return False, '更新数据集id失败', re


@api.admin_route.post('/copy_as_child')
def copy_as_child_dashboard(request, **kwargs):
    """
    复制报告为子报表
    :param kwargs:
    :return:
    """
    result, errors, source_dashboard, target_parent_dashboard = dashboard_service.copy_as_child_dashboard(
        kwargs.get('source_dashboard_id'), kwargs.get('target_parent_dashboard_id')
    )
    application_type = source_dashboard.get('application_type')
    if not errors:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'copy_dashboard',
                'id': kwargs.get('source_dashboard_id'),
                'content': '复制{dashboard_type} [ {source_dashboard_name} ] 成为{dashboard_type} [ {target_parent_dashboard_name} ] 的子页面成功'.
                format(
                    source_dashboard_name=source_dashboard.get('name', ''),
                    target_parent_dashboard_name=target_parent_dashboard.get('name', ''),
                    dashboard_type=dashboard_service.get_dashboard_type_word(source_dashboard.get('type'),
                                                                             application_type),
                ),
            },
        )
        return True, '复制为子页面成功!', None
    else:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'copy_dashboard',
                'id': kwargs.get('source_dashboard_id'),
                'content': '复制{dashboard_type} [ {source_dashboard_name} ] 成为{dashboard_type} [ {target_parent_dashboard_name} ] 的子页面失败'.
                format(
                    source_dashboard_name=source_dashboard.get('name', ''),
                    target_parent_dashboard_name=target_parent_dashboard.get('name', ''),
                    dashboard_type=dashboard_service.get_dashboard_type_word(source_dashboard.get('type'),
                                                                             application_type),
                ),
            },
        )
        return False, '复制为子页面失败!', None


@api.admin_route.post('/copy_child_to')
def copy_child_to(request, **kwargs):
    """
    复制子报表为主报表或者子报表
    :param kwargs:
    :return:
    """

    result, errors, source_dashboard, target_parent_dashboard = dashboard_service.copy_child_to(
        kwargs.get('source_dashboard_id'), kwargs.get('target_parent_dashboard_id'), kwargs.get('copy_target_type')
    )
    application_type = source_dashboard.get('application_type')
    if not errors:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'copy_dashboard',
                'id': kwargs.get('source_dashboard_id'),
                'content': '复制{dashboard_type} [ {source_dashboard_name} ] 成为{dashboard_type} [ {target_parent_dashboard_name} ] 成功'.
                format(
                    source_dashboard_name=source_dashboard.get('name', ''),
                    target_parent_dashboard_name=target_parent_dashboard.get('name', ''),
                    dashboard_type=dashboard_service.get_dashboard_type_word(source_dashboard.get('type'),
                                                                             application_type),
                ),
            },
        )
        return True, '复制成功!', None
    else:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'copy_dashboard',
                'id': kwargs.get('source_dashboard_id'),
                'content': '复制{dashboard_type} [ {source_dashboard_name} ] 成为{dashboard_type} [ {target_parent_dashboard_name} ] 失败'.
                format(
                    source_dashboard_name=source_dashboard.get('name', ''),
                    target_parent_dashboard_name=target_parent_dashboard.get('name', ''),
                    dashboard_type=dashboard_service.get_dashboard_type_word(source_dashboard.get('type'),
                                                                             application_type),
                ),
            },
        )
        return False, '复制失败!', None


@dataApi.data_route.get('/gaode')
def get_gaode(request, response, **kwargs):
    """
    /*
        @apiVersion 1.0.0
        @api {get} /api/dashboard_chart/gaode 代理高德api接口
        @apiGroup  dashboard_chart
        @apiResponse  200 {
            "result": true,
            "msg": "ok",
            "data": {
            }
        }
    */
    """
    url = 'https://restapi.amap.com/v3/config/district?' + urllib.parse.urlencode(kwargs)
    logging.debug(request.get_param("level"))
    resp = requests.get(url)
    if resp.status_code == 200 and 'districts' in resp.text:
        expiry_time = datetime.datetime.utcnow() + datetime.timedelta(100)
        response.set_header("Expires", expiry_time.strftime("%a, %d %b %Y %H:%M:%S GMT"))
        response.body = resp.text


@api.admin_route.get('/get_dashboard_vars')
def get_dashboard_vars(**kwargs):
    """
    /**
        @apiVersion 1.0.0
        @api {get} /api/dashboard_chart/get_dashboard_vars 获取当前报告单图引用的数据集变量列表
        @apiGroup  dashboard_chart
        @apiParam query {string}  dashboard_id 报告ID
        @apiResponse  200 {
            "result": true,
            "msg": "ok",
            "data": {
                "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d{数据集ID}": {
                "dataset_data": {
                    "id{数据集ID}": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                    "name{数据集名称}": "\u5168\u5c40\u6570\u636e1"
                },
                "var_list": [{
                        "created_on{创建时间}": "2019-04-16 15:48:47",
                        "id{变量唯一ID}": "39ed374b-7f5a-6fba-f46f-b65ea90929c5",
                        "default_value{变量默认值}": "\"1\"",
                        "dataset_id{数据集ID}": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "var_type{变量类型}": 1,
                        "description{变量描述}": "1",
                        "name{变量名称}": "test",
                        "value_type{值类型}": 2
                    }]
                }
            }
        }
    **/
    """
    if not kwargs.get("dashboard_id"):
        raise UserError(message="缺少报告ID")
    return True, '', dashboard_service.batch_get_dashboard_vars(kwargs.get("dashboard_id"))


@api.admin_route.get("/components")
def get_components(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/dashboard_chart/components 报告编辑器组件列表
    @apiGroup dashboard_chart
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data":[{
                "package": "包名",
                "version": "版本号",
                "next_version": "下一个版本号",
                "operation": "操作add|upgrade|rollback",
                "status": "状态",
                "menu_id": "菜单ID",
                "name": "组件名称",
                "icon": "显示图标",
                "preview_image": "示例图",
                "navbar_icon": "icon图标",
                "layout_preview_image": "布局示例图",
                "layout":"支持的布局",
                "description": "描述",
                "data_logic_type_code": "组件数据逻辑类型代码",
                "is_build_in": "是否系统内置",
                "md5version": "文件的md5指纹",
                "chart_type": "图标类型",
                "data_source_origin": "数据来源",
                "indicator_description": "数据指标描述",
                "indicator_rules": "数据指标规则",
                "sortable": "是否可以拍下",
                "penetrable": "是否支持穿透",
                "linkage": "是否支持主动联动",
                "can_linked": "是否支持被联动",
                "has_zaxis": "是否有Z轴",
                "has_desiredvalue": "是否有目标值",
                "dims_report_redirect": "维度是否支持跳转",
                "nums_report_redirect": "数值是否支持跳转",
                "created_on": "创建时间"
            }]
    }
    **/
    """
    msg, data = components_service.get_editor_components(kwargs.get("runTerminal", ""))
    return True, msg, data


@api.admin_route.post('/async_release')
def async_release(request, **kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/async_release 报告异步发布
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id{报告ID}": "39ed12ee-b183-97a2-319f-a5ad88ae8215",
        "status{发布状态}": 1,
        "type_access_released{访问模式}": 4,
        "user_groups": [],
        "view_passwd{查看密码}": ""
    }
    @apiResponse  200 {
    "result": true,
    "msg": "ok",
    "data": true
    }
    */
    """
    from app_celery import async_release_message_push

    dashboard_service.license_control()

    model = ReleaseModel(**kwargs)
    application_type = kwargs.get('application_type')
    adapt_function_permission(released_dashboard_service.release_with_process, application_type, 'edit')
    re = released_dashboard_service.release_with_process(model)
    if re:
        dashboard = dashboard_service.get_dashboard_info(model.id)
        if config.get("ThirdParty.message_push_api"):
            async_release_message_push.delay(model.status, dashboard, g.code)
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'async_release',
                'id': kwargs.get('id'),
                'content': '{dashboard_type} [ {dashboard_name} ] 异步发布成功'.format(
                    dashboard_name=dashboard.get('name'),
                    dashboard_type=dashboard_service.get_dashboard_type_word(dashboard.get('type'),
                                                                             application_type),
                ),

            },
        )
        chart_service.record_released_data(model)
    return True, '', re


@api.admin_route.get("/installed_components")
def get_installed_components():
    """
    获取组件
    :return:
    """
    msg, result = components_service.get_installed_components()
    return True, msg, result


@api.admin_route.get('/external_token')
def get_external_token():
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/external_token 获取对外token 用于dmp跳转到外部页面进行认证
    @apiGroup  dashboard_chart
    @apiBodyParam
    @apiResponse  200 {
    "result": true,
    "msg": "ok",
    "data": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyaWQiOiIiLCJ0ZW5hbnRfY29kZSI6ImRldl90ZXN0IiwidXNlcm5hbWUiOiJ0ZXN0IiwidGltZSI6MTU1NjQ0MTI4OH0.HkB6JVUYuM0Txt5NvnFbZ4xYYBx-3Q6gNm_LPdlHWSA"
        }
    }
    **/
    """
    return True, '', {'token': dashboard_service.get_external_token()}


@api.admin_route.post('/add')
def add_dashboard(request, **kwargs):
    """
    /**
    @apiVersion 1.0.2
    @api {post} /api/dashboard_chart/add 新增报告
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "background{报告背景}": "{\"show\":true,\"color\":\"RGBA(255,255,255,1)\",\"image\":\"\",\"size\":\"stretch\"}",
        "create_type": 1,
        "layout{报告布局}": "{\"ratio\":\"16:9\",\"width\":1920,\"height\":1080,\"lattice\":10}",
        "name{报告名称}": "retrt",
        "new_layout_type": 0,
        "parent_id{父级报告ID}": "",
        "platform{适应平台}": "pc",
        "theme{主题}": "colorful_white",
        "type{类型}": "FILE/FOLDER"
        "application_type{报告类型}": 0/1,
        "main_external_subject_id{主主题id}": "",
        "external_subject_ids{所有主题id}: ["123"],
        "dataset_id{数据集id}": "",
    }
    @apiResponse  200 {
    "result": true,
    "msg": "ok",
    "data": {"id": "39ed12ee-b183-97a2-319f-a5ad88ae8215", "name": "retrt"}
    }
    **/
    """

    dashboard_service.license_control()

    application_type = kwargs.get('application_type')
    if str_equal(application_type, ApplicationType.SelfService.value):
        validator = PermissionValidator('self-service.edit')
    elif str_equal(application_type, ApplicationType.LargeScreen.value):
        validator = PermissionValidator('large_screen.edit')
    else:
        validator = PermissionValidator('data-report.edit')
    validator()

    kwargs["external_subject_ids"] = ','.join(kwargs.get("external_subject_ids", []))
    model = DashboardModel(**kwargs)
    dashboard_data = dashboard_service.add_dashboard(model, return_dict=True)
    dashboard_id = dashboard_data.get('id', '')
    if model.type == 'FILE':
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'add_dashboard',
                'id': dashboard_id,
                'content': '{dashboard_type} [ {name} ] 创建成功'.format(
                    name=model.name,
                    dashboard_type=dashboard_service.get_dashboard_type_word(model.type, application_type)
                ),
            },
        )
    return True, '添加成功', dashboard_data


@api.admin_route.get('/check')
@api.admin_route.post('/check')
def check_dashboard_permission(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/check 检测是否有权限查看该看板
    @apiGroup  dashboard_chart
    @apiBodyParam
    @apiResponse  200 {
    "result": true,
    "msg": "ok",
    "data": "39ed12ee-b183-97a2-319f-a5ad88ae8215"
    }
    **/
    """
    model = DashboardModel(**kwargs)
    action_code = kwargs.get('action_code')
    if action_code not in ['view', 'edit', 'copy', 'export']:
        raise UserError(message=u'action_code 只支持view, edit, copy, export')
    return True, None, dashboard_service.check_dashboard_permission(model, action_code)


@api.admin_route.get('/screen/check')
def check_dashboard_screen_permission(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/screen/check 检测是否有权限查看该多屏
    @apiGroup  dashboard_chart
    @apiBodyParam
    @apiResponse  200 {
    "result": true,
    "msg": "ok",
    "data": "39ed12ee-b183-97a2-319f-a5ad88ae8215"
    }
    **/
    """
    model = DashboardModel(**kwargs)
    action_code = kwargs.get('action_code')
    if action_code not in ['view', 'edit', 'copy', 'export']:
        raise UserError(message=u'action_code 只支持view, edit, copy, export')
    return True, None, dashboard_service.check_dashboard_permission(model, action_code)


@api.admin_route.post('/rename')
def update_dashboard_name(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/rename 重命名报告名称
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "cover": "",
        "create_type": 1,
        "created_by": "test",
        "created_on": "2019-04-09 14:21:05",
        "description": "",
        "distribute_type": "",
        "edit_on": "2019-04-09 14:21:05",
        "edit_status": 0,
        "icon": "",
        "id": "39ed12ee-b183-97a2-319f-a5ad88ae8215",
        "is_multiple_screen": 0,
        "level_code": "1572-",
        "modified_on": "2019-04-09 17:49:34",
        "name": "经济人报备分析-zwq",
        "new_layout_type": 1,
        "parent_id": "",
        "platform": "pc",
        "released_on": "2019-04-09 15:34:25",
        "status": 1,
        "sub": [],
        "type": "FILE"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    */
    """
    model = DashboardModel(**kwargs)
    pre_dashboard = dashboard_service.get_dashbaord_info(model.id)
    application_type = kwargs.get('application_type')
    adapt_function_permission(dashboard_service.update_dashboard_name, application_type, 'edit')
    dashboard_service.update_dashboard_name(model)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'application_type': application_type,
            'action': 'rename_dashboard',
            'id': kwargs.get('id'),
            'content': '{dashboard_type} [ {new_name} ] 名称修改成功，修改之前为:{pre_name}'.format(
                new_name=model.name,
                pre_name=pre_dashboard.get('name'),
                dashboard_type=dashboard_service.get_dashboard_type_word(model.type, application_type),
            ),
        },
    )

    return True, '修改成功', model.id


@api.admin_route.post('/move')
def move_dashboard(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/move 移动文件夹
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "dash_id{报告ID}": "39e69eca-d162-7f6f-20f4-bb644bb1ef47",
        "target_dash_id{目标目录}": "root"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    */
    """
    source_dashboard = dashboard_service.get_dashbaord_info(kwargs.get('dash_id'))
    application_type = kwargs.get('application_type')
    adapt_function_permission(dashboard_service.move_dashboard, application_type, 'edit')
    re = dashboard_service.move_dashboard(kwargs.get('dash_id'), kwargs.get('target_dash_id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'application_type': application_type,
            'action': 'move_dashboard',
            'id': kwargs.get('dash_id'),
            'content': '{dashboard_type} [ {dashbaord_name} ] 移动成功'.format(
                dashboard_type=dashboard_service.get_dashboard_type_word(source_dashboard.get('type'),
                                                                         application_type),
                dashbaord_name=source_dashboard.get('name'),
            ),
        },
    )
    return True, '移动成功', re


@api.admin_route.post('/update_folder_rank')
def update_dashboard_folder_rank(**kwargs):
    """
    修改应用排序
    :param kwargs:
    :return:
    """
    model = DashboardSortModel(**kwargs)
    re = dashboard_service.update_dashboard_folder_rank(model)
    return True, '修改成功', re


@api.admin_route.post('/set_default')
def set_default_show(**kwargs):
    """
    设置看板默认显示
    :param kwargs:
    :return:
    """
    if kwargs.get('application_type') in [1, '1']:
        dashboard_service.set_default_show.set_permissions('self_service-edit')
    return True, '设置成功', dashboard_service.set_default_show(kwargs.get('id'))


@api.admin_route.get('/get_default')
def get_default_show():
    return True, '', dashboard_service.get_default_show()


@api.admin_route.post('/delete')
def delete_dashboard(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/delete 删除报告
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id{报告id}": "39e80b95-2e31-f949-9ed0-be25eb0e8bf3"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    */
    """
    application_type = kwargs.get('application_type', 0)
    adapt_function_permission(dashboard_service.delete_dashboard_by_dashboard_id, application_type, 'edit')
    re = dashboard_service.delete_dashboard_by_dashboard_id(kwargs.get("id"), application_type)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'application_type': application_type,
            'action': 'delete_dashboard',
            'id': kwargs.get('id'),
            'content': '{dashboard_type} [ {dashboard_name} ] 删除成功'.format(
                dashboard_name=re.get('name'),
                dashboard_type=dashboard_service.get_dashboard_type_word(re.get('type'), application_type)
            ),
        },
    )
    return True, '删除成功', re


@api.admin_route.get('/check_delete')
def check_delete_dashboard(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/check_delete 检查报告是否被多屏引用
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id{报告id}": "39e80b95-2e31-f949-9ed0-be25eb0e8bf3"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
                "is_target_dashboard{是否为被跳转目标报告}": false,
                "is_released{是否已发布}": true,
                "jump_dashboard_list{跳转发起报告列表}": [{"dashboard_id": "39e80b95-2e31-f949-9ed0-be25eb0e8bf3", "dashboard_name": "test", "parent_id": "39e80b95-2e31-f949-9ed0-be25eb0e8bf3"}],
                "is_quoted_screen{是否被多屏引用}": false
            }
        }
    */
    """
    return True, '检查成功', dashboard_service.check_delete_dashboard(kwargs.get('id'))


@api.admin_route.get('/filter/info')
def get_dashboard_filter_info(request, **kwargs):
    """
    获取报告筛选信息
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    dashboard_filter_ids = kwargs.get('dashboard_filter_id')
    return True, '', dashboard_service.get_dashboard_filter_info(dashboard_filter_ids)


@api.admin_route.get('/filter/get_dashboard_filters')
def handle_get_dashboard_filters(request, **kwargs):
    """
    获取报告的所有筛选条件
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    dashboard_id = kwargs.get('dashboard_id')
    has_param = kwargs.get('has_param', "0")
    if not has_param.isdigit():
        raise UserError(message="has_param参数类型错误")
    return True, '', dashboard_service.get_dashboard_filters(dashboard_id, int(has_param))


@api.admin_route.get('/chart/list')
def get_dashboard_chart_list(**kwargs):
    """
    获取看板下所有单图
    :param kwargs:
    :return:
    """
    return True, '', chart_service.get_chart_list(kwargs.get('dashboard_id'), True, True)


@api.admin_route.post('/chart/add_dataset_field')
def add_dataset_field(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/chart/add_dataset_field 添加高级字段
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id{字段id}":"",
        "rank{排序}":"",
        "dataset_id{数据集id}":"39e8a106-c247-4c7b-3c4d-37b8c61fa170",
        "alias_name{高级字段别名}":"test1",
        "visible{是否显示}":1,
        "format":"",
        "field_group{字段分组：度量或维度}":"度量",
        "data_type{字段类型：字符串或数值或日期}":"字符串",
        "expression":"[]",
        "expression_advance{高级字段表达式}":"]91260754831_DIWG[",
        "mode{添加类型：add新增}":"add"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "添加成功",
          "data": "A_TEST_3891202923"
        }
    */
    """
    data = kwargs
    data['expression_advance'] = str_reverse(data.get('expression_advance'))
    dataset_field_model = DatasetFieldModel(**data)
    result = advanced_field_service.save_dataset_field(
        dataset_field_model, kwargs.get('external_subject_id'), kwargs.get('origin_table_name')
    )
    released_dashboard_service.check_release_status(kwargs.get('dashboard_id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'add_user_dataset_field',
            'id': kwargs.get('dataset_id'),
            'content': '高级字段 [ {alias_name} ] 添加成功'.format(alias_name=dataset_field_model.alias_name),
        },
    )
    return True, '添加成功', result


@api.admin_route.post('/chart/update_dataset_field')
def update_dataset_field(request, **kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/chart/update_dataset_field 修改高级字段
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id{字段id}":"39f1dd1b-1a87-01ae-1683-847d8b6e6829",
        "rank{排序}":3,
        "dataset_id{数据集id}":"39e8a106-c247-4c7b-3c4d-37b8c61fa170",
        "alias_name{高级字段别名}":"test1",
        "visible{是否显示}":1,
        "format":"",
        "field_group{字段分组：度量或维度}":"度量",
        "data_type{字段类型：字符串或数值或日期}":"字符串",
        "expression":"[]",
        "expression_advance{高级字段表达式}":"]2563454716_DIUGRESUNOITATS[",
        "mode{添加类型：edit修改}":"edit"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "保存成功",
          "data": "A_TEST_3891202923"
        }
    */
    """
    data = kwargs
    data['expression_advance'] = str_reverse(data.get('expression_advance'))
    dataset_field_model = DatasetFieldModel(**data)
    result = advanced_field_service.save_dataset_field(dataset_field_model, kwargs.get('external_subject_id'))
    released_dashboard_service.check_release_status(kwargs.get('dashboard_id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_user_dataset_field',
            'id': kwargs.get('dataset_id'),
            'content': '高级字段 [ {alias_name} ] 修改成功'.format(alias_name=dataset_field_model.alias_name),
        },
    )
    return True, '保存成功', result


@api.admin_route.post('/chart/delete_dataset_field')
def delete_dataset_field(request, **kwargs):
    """
    删除用户自定义字段
    :param kwargs:
    :return:
    """
    advanced_field_service.delete_dataset_field(
        kwargs.get('id'), kwargs.get('external_subject_id'), kwargs.get('origin_table_name')
    )
    released_dashboard_service.check_release_status(kwargs.get('dashboard_id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_user_dataset_field',
            'id': kwargs.get('dataset_id'),
            'content': '高级字段 [ {dataset_field_id} ] 删除成功'.format(dataset_field_id=kwargs.get('id')),
        },
    )
    return True, '删除成功', None


@api.admin_route.post('/screen/save')
def screen_save(request, **kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/screen/save 保存多屏
    @apiGroup  dashboard
    @apiBodyParam {
        "description{多屏描述}": "",
        "id": "",
        "name{多屏名称}": "gdfsgdfsg",
        "refresh_rate{刷新频率}": "{\"time\":0,\"unit\":\"minute\",\"isOpen\":false}",
        "screens{报告报告ID列表}": ["39ed27af-b5b7-301e-fb84-8f40511e4b83", "39ed2300-8c91-7074-1d11-1fe2bba90920"]
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    */
    """
    re = screen_dashboard_service.screen_save(ScreenModel(**kwargs))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'save_multi_dashboard',
            'name': kwargs.get('name'),
            'content': '多屏 [ {dashboard_name} ] 保存成功'.format(dashboard_name=kwargs.get('name')),
        },
    )
    return True, '', re


@api.admin_route.get('/screen/detail')
def screen_detail(**kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/screen/detail 多屏详情
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id": "39ed7519-b902-fb50-dad6-b936f706f25e"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "is_quoted_application": false,
            "edit_on": "2019-04-28 15:51:02",
            "dashboard": {
                "theme": "tech_blue",
                "user_group_id": "",
                "new_layout_type": 0,
                "background": {},
                "border": "",
                "user_groups": [],
                "distribute_type": 0,
                "type": "FILE",
                "refresh_rate": "{\"time\":0,\"unit\":\"minute\",\"isOpen\":false}",
                "name": "武汉多屏内存",
                "id": "39ed7519-b902-fb50-dad6-b936f706f25e",
                "type_selector": 1,
                "layout": {},
                "cover": "",
                "default_show": 0,
                "grid_padding": "",
                "create_type": 0,
                "is_show_mark_img": 0,
                "platform": "pc",
                "share_secret_key": "",
                "is_multiple_screen": 1,
                "type_access_released": 4,
                "selectors": {},
                "status": 1,
                "level_code": "1616-",
                "dashboard_filters": [],
                "scale_mode": 0,
                "description": "",
                "biz_code": "35a71937baee437886bba21b69967ff7"
            },
            "screens": [{
                "theme": "colorful_white",
                "user_group_id": "",
                "background": {
                    "show": true,
                    "image": "",
                    "size": "stretch",
                    "color": "RGBA(255,255,255,1)"
                },
                "border": "",
                "type": "FILE",
                "name": "所有wh单图自由布局_副本",
                "id": "39ed74d0-bb60-eaa2-6084-5413ca0639fa",
                "type_selector": 1,
                "layout": {
                    "platform": "pc",
                    "width": 1440,
                    "ratio": "16:9",
                    "lattice": 10,
                    "toolbar": "show",
                    "screenHeader": "show",
                    "height": 810,
                    "mode": "free"
                },
                "cover": "",
                "default_show": 0,
                "grid_padding": "{\"chart_padding\":[15,15,15,15],\"chart_background\":\"#FFFFFF\",\"container_padding\":[10,10],\"chart_margin\":[10,10]}",
                "is_show_mark_img": 0,
                "platform": "pc",
                "share_secret_key": "",
                "is_multiple_screen": 0,
                "type_access_released": 4,
                "selectors": {},
                "status": 1,
                "level_code": "0642-0020-0029-0018-",
                "scale_mode": 0,
                "description": "",
                "biz_code": "2775a48f518242c18c4a3b064ad0a473"
            }],
            "released_on": "2019-04-28 15:51:02",
            "edit_status": 0
            }
        }
    */
    """
    return True, '', screen_dashboard_service.screen_detail(kwargs.get('id'))


@api.admin_route.get('/screen/info')
def screen_info(request, **kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/screen/info 获取报告详情
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id": "39ed7519-b902-fb50-dad6-b936f706f25e"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "is_quoted_application": false,
            "edit_on": "2019-04-28 15:51:02",
            "dashboard": {
                "theme": "tech_blue",
                "user_group_id": "",
                "new_layout_type": 0,
                "background": {},
                "border": "",
                "user_groups": [],
                "distribute_type": 0,
                "type": "FILE",
                "refresh_rate": "{\"time\":0,\"unit\":\"minute\",\"isOpen\":false}",
                "name": "武汉多屏内存",
                "id": "39ed7519-b902-fb50-dad6-b936f706f25e",
                "type_selector": 1,
                "layout": {},
                "cover": "",
                "default_show": 0,
                "grid_padding": "",
                "create_type": 0,
                "is_show_mark_img": 0,
                "platform": "pc",
                "share_secret_key": "",
                "is_multiple_screen": 1,
                "type_access_released": 4,
                "selectors": {},
                "status": 1,
                "level_code": "1616-",
                "dashboard_filters": [],
                "scale_mode": 0,
                "description": "",
                "biz_code": "35a71937baee437886bba21b69967ff7"
            },
            "screens": [{
                "theme": "colorful_white",
                "user_group_id": "",
                "background": {
                    "show": true,
                    "image": "",
                    "size": "stretch",
                    "color": "RGBA(255,255,255,1)"
                },
                "border": "",
                "type": "FILE",
                "name": "所有wh单图自由布局_副本",
                "id": "39ed74d0-bb60-eaa2-6084-5413ca0639fa",
                "type_selector": 1,
                "layout": {
                    "platform": "pc",
                    "width": 1440,
                    "ratio": "16:9",
                    "lattice": 10,
                    "toolbar": "show",
                    "screenHeader": "show",
                    "height": 810,
                    "mode": "free"
                },
                "cover": "",
                "default_show": 0,
                "grid_padding": "{\"chart_padding\":[15,15,15,15],\"chart_background\":\"#FFFFFF\",\"container_padding\":[10,10],\"chart_margin\":[10,10]}",
                "is_show_mark_img": 0,
                "platform": "pc",
                "share_secret_key": "",
                "is_multiple_screen": 0,
                "type_access_released": 4,
                "selectors": {},
                "status": 1,
                "level_code": "0642-0020-0029-0018-",
                "scale_mode": 0,
                "description": "",
                "biz_code": "2775a48f518242c18c4a3b064ad0a473"
            }],
            "released_on": "2019-04-28 15:51:02",
            "edit_status": 0
            }
        }
    */
    """
    re = screen_dashboard_service.get_dashboard_info(kwargs.get('id'))
    if re.get('dashboard'):
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'preview_dashboard',
                'id': kwargs.get('id'),
                'content': '预览报告 [ {dashboard_name} ] '.format(dashboard_name=re.get('dashboard').get('name')),
            },
        )
    return True, '', re


@api.route.get('/login')
def login_dashboard(request, response, **kwargs):
    """
    第三方登录入口
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    login_service = dashboard_login_service.DashboardLoginService()
    data, redirect_url, user_info = login_service.login(kwargs.pop('token', "") , kwargs.pop('redirect_url', ""))
    # 设置登录cookie信息
    login_service.set_cookies(request, response, data)
    # 添加天眼需要的cookie信息
    user_service.write_ty_cookie(request.host, response, user_info.get('id'), user_info)
    # 透传其它参数
    redirect_url = url_add_param(redirect_url, kwargs)
    # 设置跨域
    reporting_sso_service.set_cookies_samesite_property(response=response)
    hug.redirect.to(redirect_url)


@api.route.get('/v2/login')
def login_dashboard(request, response, **kwargs):
    """
    第三方登录入口
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    login_service = dashboard_login_service.DashboardLoginService()
    data, redirect_url, user_info = login_service.login(kwargs.pop('token', ""), kwargs.pop('redirect_url', ""))
    # 设置登录cookie信息
    login_service.set_cookies(request, response, data)
    # 添加天眼需要的cookie信息
    user_service.write_ty_cookie(request.host, response, user_info.get('id'), user_info)
    # 透传其它参数
    redirect_url = url_add_param(redirect_url, kwargs)
    # 设置跨域
    reporting_sso_service.set_cookies_samesite_property(response=response)
    # 跳转前端中转页面
    reporting_sso_service.render_temporary_redirect_page(response, origin_redirect_url=redirect_url)


@api.admin_route.get('/dashboard/list')
def list_dashboard(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /api/dashboard_chart/dashboard/list 获取看板(报告)列表数据
    @apiGroup dashboard_chart
    @apiParam query {string="FILE","FOLDER"} [type] 报告类型,FILE:报告;FOLDER:文件夹
    @apiParam query {number} [status]  报告状态,0:未发布，1:已发布
    @apiParam query {string="pc","mobile"} [platform] 报告平台类型：pc或者mobile
    @apiParam query {string} [created_by] 创建者(用户账户),可筛选由该用户创建的报告
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "cover{封面}": "",
                "id{看板的标识}": "39ddc507-2cb6-150b-47f9-8ba1f5dc6c4c",
                "biz_code{看板的业务代码}": "sale",
                "icon{图标}": "",
                "type{是目录或报告FOLDER/FILE}": "FILE",
                "name{名字}": "京西（新）-6号楼"
            }
        ]
    }
    */
    """
    kwargs['user_account'] = g.account
    data = dashboard_openapi_service.get_dashboard_list_for_openapi(**kwargs)
    return True, 'ok', data


@api.admin_route.get('/third_party/get_redirect_url')
def get_redirect_url(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/dashboard_chart/third_party/get_redirect_url 获取第三方自定义跳转url
    @apiParam formData {string}  dashboard_id 报告id
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {
            "redirect_url": ""
        }
    }
    **/
    """
    redirect_url = dashboard_service.get_custom_redirect_url(kwargs.get("dashboard_id"))
    return True, "", {"redirect_url": redirect_url}


@api.admin_route.get('/compare_metadata')
def compare_metadata(**kwargs):
    """
    元数据对比调试接口
    :param kwargs:
    :return:
    """
    return True, "", metadata_service.compare_metadata(kwargs.get("dashboard_id", ""))


@api.route.get('/all_installed_components')
def get_all_installed_components(request, response, **kwargs):
    """
    无须鉴权，获取指定环境的已安装组件
    :param kwargs:
    :return:
    """
    if not request.headers.get('WEB-TO-TEST'):
        raise UserError(code=401, message='认证失败')
    g.code = kwargs.get("tenant_code", "test")
    msg, result = components_service.get_installed_components()
    return True, msg, result


@api.admin_route.post('/copy_chart')
def copy_chart(**kwargs):
    """
    跨报告拷贝单图
    :param kwargs:
    :return:
    """
    source_dashboard_id = kwargs.get("source_dashboard_id")
    source_chart_id = kwargs.get("source_chart_id")
    target_dashboard_ids = kwargs.get("target_dashboards")
    if not source_dashboard_id:
        raise UserError(message="请指定需要复制的源报告ID")
    if not source_chart_id:
        raise UserError(message="请指定需要复制的源单图ID")
    if not target_dashboard_ids:
        raise UserError(message="请指定目标报告")
    start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    transaction_data = {
        "source_dashboard_id": source_dashboard_id,
        "source_chart_id": source_chart_id,
        "start_time": start_time,
    }
    result, tid = chart_copy_repository.add_chart_copy_transaction(transaction_data, target_dashboard_ids)
    if not result:
        return False, tid, {}
    async_run_chart_copy.delay(g.code, g.userid, g.account, tid)
    return True, '', {"transcation_id": tid}


@api.admin_route.post('/retry_copy_chart')
def copy_chart(**kwargs):
    """
    重新运行拷贝单图
    :param kwargs:
    :return:
    """
    transcation_id = kwargs.get("transcation_id")
    if not transcation_id:
        raise UserError(message="请指定事务ID")
    if not transcation_id:
        raise UserError(message="请指定事务ID")
    transcation = chart_copy_repository.get_simple_chart_copy_transaction(transcation_id)
    if not transcation:
        raise UserError(message=f"事务 {transcation_id} 不存在！")
    if transcation["status"] == ChartCopyTransactionStatus.Running.value:
        raise UserError(message=f"事务 {transcation_id} 正在运行中，不可重复执行！")
    # 状态进行中需要进行判断，若超时则继续执行，否则退出
    if (
            transcation["status"] == ChartCopyTransactionStatus.Running.value
            and chart_utils.get_unix_timestamp(chart_utils.get_current_time())
            - chart_utils.get_unix_timestamp(str(transcation["start_time"]))
            < CHART_COPY_TRANSACTION_TIME_OUT
    ):
        raise UserError(message=f"事务 {transcation_id} 正在运行中，不可重复运行！")
    async_run_chart_copy.delay(g.code, g.userid, g.account, transcation_id)
    return True, '', {"transcation_id": transcation_id}


@api.admin_route.get('/copy_chart_transcation')
def get_copy_chart_transcation(**kwargs):
    """
    获取复制事务详情
    :param kwargs:
    :return:
    """
    transcation_id = kwargs.get("transcation_id")
    if not transcation_id:
        raise UserError(message="请指定事务ID")
    result, transcation = chart_copy_repository.get_chart_copy_transaction(transcation_id)
    if not result:
        raise UserError(message="事务不存在")
    dashboard_ids = [transcation["source_dashboard_id"]]
    chart_ids = [transcation["source_chart_id"]]
    for item in transcation.get("tasks", []):
        dashboard_ids.append(item["target_dashboard_id"])
    dashboards = dashboard_repository.get_dashboard_by_ids(dashboard_ids)
    dashboard_map = {dashboard["id"]: dashboard for dashboard in dashboards}
    charts = chart_repository.get_chart_by_chart_ids(chart_ids)
    chart_map = {chart["id"]: chart for chart in charts}
    transcation["source_dashboard_name"] = dashboard_map.get(transcation["source_dashboard_id"], {}).get("name", "")
    transcation["source_chart_name"] = chart_map.get(transcation["source_chart_id"], {}).get("name", "")
    new_layout_type_map = {0: 'free', 1: 'grid'}
    for item in transcation.get("tasks", []):
        target_dashboard = dashboard_map.get(item["target_dashboard_id"], {})
        item["layout"] = target_dashboard.get("layout")
        item["platform"] = target_dashboard.get("platform")
        item["terminal_type"] = target_dashboard.get("terminal_type")
        item["new_layout_type"] = target_dashboard.get("new_layout_type")
        try:
            item["layout"] = json.loads(item["layout"])
            if "mode" not in item:
                item["layout"]["mode"] = new_layout_type_map.get(item["new_layout_type"], 'free')
        except Exception as e:
            logging.exception(f"报告 {item['target_dashboard_id']} layout 解析失败 {str(e)}")
        item["target_dashboard_name"] = target_dashboard.get("name", "")
    return True, '', transcation


@api.admin_route.get('/dashboard_last_released')
def get_latest_modified_by(**kwargs):
    """
    /**
    获取上次发布报告的操作信息
    @apiVersion 1.0.0
    @api {get} /api/dashboard_chart/dashboard_last_released
    @apiParam formData kwargs
    {
        "dashboard_id": ""
    }
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "result": true,
        "msg": "",
        "data": {
            "dashboard_id": "",
            "edit_on": null,
            "released_on": "2019-03-14 15:40:09",
            "created_on": "2019-04-13 14:41:29",
            "created_by": "admin",
            "modified_on": "2019-04-13 17:14:03",
            "modified_by": "test"
        }
    }
    **/
    """
    dashboard_id = kwargs.get('dashboard_id', '')
    return True, "", dashboard_service.get_dashboard_operator_info(dashboard_id)


@api.route.get('/view')
def redirect_erp_dashboard(request, response):
    """
    /**
    【ERP】支持在云助手中扫描ERP功能页面上的二维码，打开数见报表页面
    @apiVersion 1.0.0
    @api {get} /api/dashboard/view
    @apiParam formData kwargs
    {
        "report_id": "2323"
    }
    @apiGroup  dashboard
    @apiResponse  200 {
        "result": true,
        "msg": "",
        "data": {
        }
    }
    **/
    """
    return True, "", external_service.redirect_to_dashboard(request, response)


# erp菜单集成打开报告
@api.route.get('/')
@reporting_sso_service.additional_header_token
@reporting_sso_service.erp_sso_authenticator(need_redirect=True)
def erp_sso_redirect_report(request, response, token, report_id, *args, **kwargs):
    from user.services.ingrate_service import get_redirect_url

    code_info = reporting_sso_service.get_yzs_config_by_erp_token(token)
    auto_down_task_id = dashboard_service.auto_download_report_from_rdc(report_id)
    # url = f"/dataview/share/{report_id}?code={code_info['code']}&__from=erpdashbordapi"
    url = get_page_url(report_id, kwargs.get('report_type', 'dashboard'), {})
    kwargs['code'] = code_info['code']
    kwargs['__from'] = 'erpdashbordapi'
    # 添加参数透传
    url = url_add_param(url, kwargs)
    if auto_down_task_id:
        down_load_waiting_url = f"/static/autoDownload/index.html?task_id={auto_down_task_id}&redirect_url={config.get('Domain.dmp')}{url}"
        hug.redirect.to(down_load_waiting_url)
    else:
        hug.redirect.to(url)


@api.route.get('/{code}')
def erp_sso_redirect_report(request, response, code, *args, **kwargs):
    params = request.params
    params["code"] = code
    hug.redirect.to(
        f"/api/dashboard?{urllib.parse.urlencode(params)}"
    )


@api.admin_route.get('/get_child_dashboard')
def get_child_dashboard(request, **kwargs):
    """
    :param request:
    :param kwargs: 当前dashboard_id
    :return:{
    "result":true,
    "msg":"",
    "data":[
        {
            "id":"39f19394-b209-1674-c121-e1cdba7985e8",
            "theme":"colorful_white",
            "data_report_id":null,
            "name":"维度为日期",
            "type":"FILE",
            "parent_id":"39f19394-3001-5888-a775-8a234a7c471e",
            "level_code":"0398-0001-0001-0001-",
            "icon":"",
            "status":1
        }
    ]
}
    """
    dashboard_id = kwargs.get('dashboard_id', '')
    is_all = True if int(kwargs.get('is_all', 1)) == 1 else False
    with_deleted = True if int(kwargs.get('with_deleted', 0)) == 1 else False
    return True, "", dashboard_service.get_child_dashboard(dashboard_id, is_all, with_deleted)


@api.admin_route.post('/import_hd_report', validate=PermissionValidator('data-report.edit'))
def import_hd_report(**kwargs):
    """
    @api {post} /api/dashboard_chart/import_hd_report 获取单图结果集
    hd_report_list 导入列表
    :param kwargs:
    :return:
    """
    hd_report_list = kwargs.get("hd_report_list")
    if not hd_report_list:
        raise UserError(message="引入的HD报告为空")
    dashboard_data = dashboard_service.import_hd_report(hd_report_list)
    return True, '添加成功', dashboard_data


@api.admin_route.get('/get_hd_report_frontend_url')
def get_hd_report_frontend_url(**kwargs):
    dashboard_id = kwargs.get('dashboard_id')
    if not dashboard_id:
        raise UserError(message="入参dashboard_id为空")
    return dashboard_service.get_hd_report_frontend_url(dashboard_id)


@api.admin_route.get('/redirect_hd_report')
def redirect_hd_report(**kwargs):
    """
    hd_report_list 调转到HD报告
    :param dashboard_id:报告ID
    :return:
    """
    dashboard_id = kwargs.get('dashboard_id')
    if not dashboard_id:
        raise UserError(message="入参dashboard_id为空")
    from rbac.services import data_permissions
    data_permissions.check_has_data_permission('dashboard', 'view', data_id=dashboard_id)

    return dashboard_service.redirect_external_report(**kwargs)


@api.admin_route.get('/redirect_external_report')
def redirect_external_report(request, response, **kwargs):
    """
    hd_report_list 调转到外部报告
    :param dashboard_id:报告ID
    :return:
    """
    dashboard_id = kwargs.get('dashboard_id')
    if not dashboard_id:
        raise UserError(message="入参dashboard_id为空")
    from rbac.services import data_permissions
    data_permissions.check_has_data_permission('dashboard', 'view', data_id=dashboard_id)

    return dashboard_service.redirect_external_report(**kwargs)


@api.admin_route.get('/get_external_dashboard_url')
def get_external_dashboard_url(**kwargs):
    """
    获取外部报表地址
    :param kwargs:
    :return:
    """
    dashboard_id = kwargs.get('dashboard_id')
    if not dashboard_id:
        raise UserError(message="入参dashboard_id为空")
    return True, 'ok', dashboard_service.get_dashboard_url_of_external(kwargs)


@api.admin_route.get('/fine_report_list')
def get_fine_report_list(**kwargs):
    """
    获取帆软报表列表
    :param kwargs:
    :return:
    """
    platform = kwargs.get("platform") or "pc"
    return True, "ok", IngratePlatformApi(g.code).get_fine_report_list(platform)


@api.admin_route.post('/update_hd_report_data', validate=PermissionValidator('data-report.edit'))
def update_hd_report_data(**kwargs):
    """
    hd_report_list 修改hd_report报告状态
    :param kwargs:
    :return:
    """
    dashboard_id = kwargs.get('dashboard_id')
    status = kwargs.get('status')
    if not dashboard_id:
        raise UserError(message="入参dashboard_id或status为空")
    return True, '修改成功', dashboard_service.update_external_report_status(dashboard_id, status,
                                                                             ApplicationType.HD_Dashboard.value)


@api.admin_route.get('/get_auto_download_report_status')
def get_auto_download_report_status(**kwargs):
    """
    报告自动下载，获取状态
    :kwargs
    :return:
    """
    task_id = kwargs.get('task_id')
    if not task_id:
        raise UserError(message="入参task_id为空")
    status_info = dashboard_service.get_yysc_report_install_status(task_id)
    return True, status_info.get('message'), status_info


@api.admin_route.get('/get_yysc_url')
def get_yysc_url(**kwargs):
    """
    跳转到应用市场
    :param kwargs:
    :return:
    """
    return True, '', dashboard_service.get_yysc_url()


@api.admin_route.get('/install_yysc_dashboard')
def install_yysc_dashboard(**kwargs):
    """
    跳转到应用市场
    :param kwargs:
    :return:
    """
    token = kwargs.get('token')
    if not token:
        raise UserError(message="入参token为空")
    return dashboard_service.install_yysc_dashboard(token)


# 暂时不做安装页面
# @api.admin_route.post('/install_yysc_dashboard', validate=PermissionValidator('data-report.edit'))
# def install_yysc_dashboard(**kwargs):
#     """
#     报告安装
#     :param kwargs:
#     :return:
#     """
#     dashboard_info = kwargs.get('dashboard_info')
#     if not dashboard_info:
#         raise UserError(message="入参dashboard_info为空")
#
#     target_dashboard_folder_id = kwargs.get('target_dashboard_folder_id', '')
#     target_dataset_folder_id = kwargs.get('target_dataset_folder_id', '')
#     return dashboard_service.install_yysc_dashboard(dashboard_info, target_dashboard_folder_id,
#                                                     target_dataset_folder_id)


# 暂时不做安装页面
# @api.admin_route.post('/get_yysc_dashboard_install_status')
# def get_yysc_dashboard_install_status(**kwargs):
#     """
#     报告安装，获取状态
#     :kwargs
#     :return:
#     """
#     task_id = kwargs.get('task_id')
#     if not task_id:
#         raise UserError(message="入参task_id为空")
#
#     dashboard_info = kwargs.get('dashboard_info')
#     if not dashboard_info:
#         raise UserError(message="入参dashboard_info为空")
#
#     status_info = dashboard_service.get_yysc_report_install_status(task_id)
#     status = status_info.get('status')
#     if status in [3, 4]:
#         dashboard_service.apply_install_progress(dashboard_info.get('id'), dashboard_info.get('version'), status)
#     return True, '', status_info
@api.route.get('/hd_report_redirect')
@reporting_sso_service.hd_sso_authenticator()
def hd_report_redirect(request, response, token, report_id, *args, **kwargs):
    """
    sso token 跳转报告
    :param kwargs:
    :return:
    """
    hug.redirect.to(released_dashboard_service.get_dashboard_release_url(report_id))


@api.admin_route.post('/edit_external_report', validate=PermissionValidator('data-report.edit'))
def edit_external_report(**kwargs):
    dashboard_id = kwargs.get('dashboard_id')
    if not dashboard_id:
        raise UserError(message="入参dashboard_id为空")
    name = kwargs.get('name')
    if not name:
        raise UserError(message="入参name为空")
    external_url = kwargs.get('external_url')
    if not external_url:
        raise UserError(message="入参external_url为空")

    return True, '修改成功', dashboard_service.edit_external_report(dashboard_id, name, external_url)


@api.admin_route.post('/update_external_report_status', validate=PermissionValidator('data-report.edit'))
def update_external_report_status(**kwargs):
    """
    修改报告状态
    :param kwargs:
    :return:
    """
    dashboard_id = kwargs.get('dashboard_id')
    status = kwargs.get('status')
    application_type = kwargs.get('application_type') or ApplicationType.External_Dashboard.value
    if not dashboard_id:
        raise UserError(message="入参dashboard_id或status为空")
    return True, '修改成功', dashboard_service.update_external_report_status(dashboard_id, status, application_type)


@api.admin_route.get('/fix_dashboard_level_code', validate=PermissionValidator('data-report.edit'))
def fix_dashboard_level_code(**kwargs):
    dashboard_id = kwargs.get("dashboard_id", '')
    if not dashboard_id:
        raise UserError(message="dashboard_id不能为空")
    return True, '修改成功', dashboard_service.fix_dashboard_level_code(dashboard_id)


@api.admin_route.post('/fix_dashboard_level_code_batch')
def fix_dashboard_level_code(**kwargs):
    kwargs["code"] = g.code
    app_celery.fix_level_code.apply_async(kwargs=kwargs)
    return True, '正在修复',


@api.admin_route.get('/jump_global_params/get')
def jump_global_params_get(**kwargs):
    """
    删除报告跳转全局参数
    :param kwargs:
    :return:
    """
    dashboard_id = kwargs.get("dashboard_id", '')
    if not dashboard_id:
        raise UserError(message="dashboard_id不能为空")
    return True, '获取成功', dashboard_service.get_dashboard_jump_global_params_by_id(dashboard_id)


@api.admin_route.post('/async_restore')
def dashboard_restore(request, **kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/restore 报告异步还原
    @apiGroup  dashboard_chart
    @apiBodyParam {
        "id{报告ID}": "39ed12ee-b183-97a2-319f-a5ad88ae8215",
    }
    @apiResponse  200 {
    "result": true,
    "msg": "ok",
    "data": true
    }
    */
    """
    model = ReleaseModel(**kwargs)
    application_type = kwargs.get('application_type')
    adapt_function_permission(released_dashboard_service.restore_with_process, application_type, 'edit')
    re = released_dashboard_service.restore_with_process(model)
    if re:
        dashboard = dashboard_service.get_dashboard_info(model.id)
        UserLogModel.log_setting(
            request=request,
            log_data={
                'application_type': application_type,
                'action': 'async_restore',
                'id': kwargs.get('id'),
                'content': '{dashboard_type} [ {dashboard_name} ] 异步还原成功'.format(
                    dashboard_name=dashboard.get('name'),
                    dashboard_type=dashboard_service.get_dashboard_type_word(dashboard.get('type'), application_type),
                ),
            },
        )
    return True, '', re


@api.admin_route.get("/get_erp_url")
def get_erp_system_url():
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/get_erp_url 获取erp系统的站点url接口
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": [{
                        "url": "http://10.5.10.162:730",
                        "app_code": "0000",
                        "app_name": "系统设置"
                },{
                        "url": "http://10.5.10.162:730",
                        "app_code": "1001",
                        "app_name": "项目主数据"
                }]
    }
    **/
    """
    result = dashboard_service.get_erp_system_url()
    return True, "", result


@api.admin_route.get("/export_dashboard_diff")
def dashboard_diff(response):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/dashboard_chart/export_dashboard_diff 报告比对结果导出接口
    @apiGroup  dashboard_chart
    @apiResponse  200
    **/
    """
    result = dashboard_service.export_dashboard_compare_result(response)
    return True, "", result


@api.admin_route.get('/key_list')
def get_key_dashboard_list():
    """
    获取租户的重点大屏列表
    :return:
    """
    results = dashboard_service.get_key_dashboard_list()
    return True, '', results


@api.admin_route.post('/set_key_dashboard')
def set_key_dashboard(**kwargs):
    """
    设置或删除报告为重点大屏
    :return:
    """
    dashboard_id = kwargs.get("dashboard_id")
    is_key = kwargs.get("is_key", 0)
    results = dashboard_service.set_key_dashboard(dashboard_id, is_key)
    return True, '', results


@api.admin_route.get('/key_dashboard_stat')
def exec_key_dashboard_stat():
    """
    当前环境所有租户的重点大屏数据统计
    :return:
    """
    from dashboard_chart.services.key_dashboard_service import key_dashboard_stat
    params = {'project_code': g.code, 'exec_type': 'manual'}
    key_dashboard_stat(params)
    return True, '任务处理完成', []


@api.admin_route.get('/list_key_dashboard_mem_usage')
def list_key_dashboard_mem_usage(**kwargs):
    """
        当前环境所有租户的重点内存占用
        :return:
        """
    from dashboard_chart.services.key_dashboard_service import key_dashboard_mem_usage
    codes = kwargs.get("codes", '')
    list = []
    if codes:
        list = codes.split(",")
    result = key_dashboard_mem_usage(list)
    return True, '', result


@api.admin_route.post('/save/params')
def save_params(**kwargs):
    from dashboard_chart.services.external_dashboard_service import save_params
    if not kwargs:
        return False, '保存的参数不能为空', ''
    return True, 'success', save_params(kwargs)


@api.admin_route.get('/get/params')
def get_params(**kwargs):
    from dashboard_chart.services.external_dashboard_service import get_params
    key = kwargs.get('params_key')
    if not key:
        raise UserError(message='缺少参数')
    return True, 'success', get_params(key)


def set_data_route_request_code(request):
    from dmplib.saas.errors import EmptyProjectCodeError

    try:
        token = request.cookies.get('token')
        data = jwt.decode(token, config.get('JWT.secret'), algorithms='HS256')
        code = data.get('code')
    except:
        raise UserError(message="token解析失败")

    if not code:
        raise UserError(message="缺少code参数")
    try:
        g.code = set_correct_project_code(code, request)
    except EmptyProjectCodeError:
        raise UserError(message='{}在数见系统中不存在'.format(code))


def get_mip_access_token():
    ingrate_api = IngratePlatformApi(code=g.code)
    try:
        access_token = ingrate_api.get_access_token()
    except:
        local_debugger({"集成平台token获取失败": traceback.format_exc(limit=1)})
        access_token = ''
    return ingrate_api, access_token


@dataApi.data_route.post('/skyline/call')
def ingrate_platform_call(request, **kwargs):
    """
    提供给前端SDK透明调用天际其他平台的接口
    :param request:
    :param kwargs:
    :return:
    """
    import curlify
    from base.enums import SkylineApps
    from components import auth_util
    from components.app_hosts import AppHosts

    set_data_route_request_code(request)

    appcode = kwargs.get("appcode") or ''
    inside = str(kwargs.get("inside")) or '0'
    path = kwargs.get("path") or ''
    method = (kwargs.get("method", '') or 'get').lower()
    params = kwargs.get("params") or {}
    req_headers = kwargs.get("headers") or {}

    key = auth_util.gen_auth_token()
    if isinstance(key, bytes):
        key = key.decode()
    headers = {
        auth_util.AUTHORIZATION_KEY: key
    }
    headers.update(req_headers)
    request_args = {}
    try:
        biz_code = dashboard_service.get_skyline_call_biz_code_from_path(path)
        local_debugger({"path": path, "解析的biz_code": biz_code})
        user_host = dashboard_service.get_skyline_call_host_from_user_setting(appcode, biz_code)
        if user_host:
            host = user_host
        else:
            is_inside = inside == '0'
            host = AppHosts.get(SkylineApps.custom_key(appcode), inside=is_inside).strip('/')
        request_args = {
            'url': f'{host}{path}',
            'headers': headers
        }
        if method == 'get':
            request_args['params'] = params
        else:
            if 'application/json' in str(headers):
                request_args['json'] = params
            else:
                request_args['data'] = params
        resp = getattr(requests, method.lower())(**request_args)
        try:
            curl_data = curlify.to_curl(resp.request, compressed=True)
            local_debugger({"调用请求详情": curl_data, "返回的结果": resp.content.decode('utf-8')})
        except:
            pass
        data = resp.json()
    except:
        traceback.print_exc()
        local_debugger({
            "请求参数": request_args,
            "发送请求失败": traceback.format_exc(limit=1),
        })
        data = {}
    return True, '', data


@dataApi.data_route.get('/get_skyline_auth')
def ingrate_platform_call(request, **kwargs):
    """
    提供给前端SDK透明调用集成平台的接口
    :param request:
    :param kwargs:
    :return:
    """
    from components.app_hosts import AppHosts
    from base.enums import SkylineApps
    from components import auth_util
    set_data_route_request_code(request)

    appcode = kwargs.get("appcode") or ''
    if not appcode:
        return False, '', 'appcode为空'
    try:
        enabled = auth_util.is_enable_skyline_auth()
        host = AppHosts.get(SkylineApps.custom_key(appcode), inside=False)
        if not enabled and appcode == 'ipaas':
            ingrate_api, access_token = get_mip_access_token()
            host = ingrate_api.host
        else:
            access_token = auth_util.gen_auth_token()
            if isinstance(access_token, bytes):
                access_token = access_token.decode()
        data = {'token': access_token, 'host': host}
    except Exception as e:
        local_debugger({"获取失败": traceback.format_exc(limit=1)})
        return False, '', f'获取失败：{str(e)}'
    return True, '', data


@api.admin_route.post('/move_dashboard_as_large_screen')
def move_dashboard_as_large_screen(request, **kwargs):
    """
    点击迁移按钮，将仪表板中的全量大屏迁移到酷炫大屏中
    """
    from dashboard_chart.services.large_dashboard_service import FolderMoveOldScreenUtil, check_has_running_task
    running_task = check_has_running_task()
    if running_task:
        # 如果有正在进行中的任务，返回正在进行的任务状态
        return True, 'ok', {'task_id': running_task.get('id')}
    task_id = seq_id()
    move_data = DashboardUtil.get_can_move_count()
    FolderMoveOldScreenUtil.init_task_status(task_id, tasks_data={}, total=move_data.get('move_count') or 0)
    app_celery.async_move_dashboard_as_large_screen.apply_async(
        kwargs={'code': g.code, 'account': g.account, 'task_id': task_id, 'userid': g.userid}
        # queue='upgrade'
    )
    return True, 'ok', {'task_id': task_id}


@api.admin_route.get('/get_move_task_status')
def get_move_task_status(request, **kwargs):
    """
    获取迁移状态
    """
    task_id = kwargs.get('task_id', '')
    return True, 'ok', get_move_task_data(task_id)


@api.admin_route.get('/can_move_as_large_screen')
def move_dashboard_as_large_screen(request, **kwargs):
    """
    获取当前能否迁移以及迁移数量
    """
    return True, 'ok', DashboardUtil.get_can_move_count()


@dataApi.data_route.post('/ingrate_platform/call')
def ingrate_platform_call(request, **kwargs):
    """
    提供给前端SDK透明调用集成平台的接口
    :param request:
    :param kwargs:
    :return:
    """
    from dmplib.saas.errors import EmptyProjectCodeError

    try:
        token = request.cookies.get('token')
        data = jwt.decode(token, config.get('JWT.secret'), algorithms='HS256')
        code = data.get('code')
    except:
        raise UserError(message="token解析失败")

    if not code:
        raise UserError(message="缺少code参数")
    try:
        g.code = set_correct_project_code(code, request)
    except EmptyProjectCodeError:
        raise UserError(message='{}在数见系统中不存在'.format(code))

    path = kwargs.get("path") or ''
    method = kwargs.get("method") or 'get'
    params = kwargs.get("params") or {}
    try:
        ingrate_api = IngratePlatformApi(code=g.code)
        if not ingrate_api.portal_check_ingrate_platform_is_exist():
            raise UserError(message='集成平台的配置有错误，请检查')
        data = ingrate_api.proxy_call(path, method, params)
    except UserError as e:
        if e.message == '账号名或登录密码错误':
            raise UserError(message=f'租户：{g.code}不存在')
        else:
            raise e
    return True, '', data


@api.admin_route.get('/set_dashboard_lock_status')
def set_dashboard_lock_status(**kwargs):
    from dashboard_chart.services.external_dashboard_service import set_dashboard_distribute_type
    dashboard_id = kwargs.get('id')
    try:
        lock_status = int(kwargs.get('status', 0))
    except ValueError:
        lock_status = 0
    if not dashboard_id:
        return False, '报告ID不能为空', ''
    return True, '', set_dashboard_distribute_type(dashboard_id, lock_status)


@api.route.post('/pkey/save')
def save_pkey(**kwargs):
    from dashboard_chart.services.external_dashboard_service import save_params
    from dashboard_chart.services.dashboard_login_service import DashboardLoginService
    token = kwargs.get('token')
    if not token:
        return False, 'token不能为空', {}
    # 校验token并解析
    login_service = DashboardLoginService()
    data = login_service.verify_sso_login_token(token)
    g.code = data.get('project_code')
    g.account = data.get('project_code') or ''
    del kwargs['token']
    return True, 'success', save_params(kwargs)
