#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2019/01/02

"""
decorators
ps: 用于各个service方法的装饰器
"""

# ---------------- 标准模块 ----------------
import json
import sys
import logging
import traceback
from functools import wraps, lru_cache
from hashlib import md5
from datetime import datetime, timedelta
from copy import deepcopy
import random

# ---------------- 业务模块 ----------------
from dmplib.utils.errors import UserError
from dmplib.hug import g
from dmplib.redis import RedisCache
from base.models import BaseModelEncoder
from components.fast_logger import FastLogger
from dataset.query.result_data import QueryDataError, QUERY_DATA_ERROR_MAP
from base.enums import DashboardDataMsgCode
from dashboard_chart.utils.common import LAST_VERSION_KEY, get_dashboard_biz_type, get_data_source_info, LAST_VERSION_KEYS
from components.utils import timed_lru_cache

from typing import Callable

logger = logging.getLogger(__name__)


def try_except_for_metadata(func: Callable) -> Callable:
    """
    异常捕获装饰器-报告元数据获取
    :param func:
    :return:
    """

    @wraps(func)
    def _handle(*args, **kwargs):
        try:
            res = func(*args, **kwargs)
            return res
        except Exception as e:
            exc_type, exc_instance, exc_traceback = sys.exc_info()
            formatted_traceback = ''.join(traceback.format_tb(exc_traceback))
            message = '报告元数据获取异常:\n{0}\n{1}:\n{2}'.format(formatted_traceback, exc_type.__name__, exc_instance)
            logger.exception(message)
            code = e.code if isinstance(e, UserError) else None
            raise UserError(message='报告元数据获取异常，异常信息：{}'.format(str(e)), code=code)

    return _handle


def auto_no_cache(func):
    """
    异常情况下为响应对象自动设置cache-control头内容为no-cache
    :param func:
    :return:
    """

    @wraps(func)
    def _handle(*args, **kwargs):
        try:
            res = func(*args, **kwargs)
            return res
        except Exception as e:
            response = args[1] if len(args) >= 2 else None
            if response:
                response.set_header("Cache-Control", "no-cache")
            raise e

    return _handle


def hash_k(k):
    k = sorted(k.items(), key=lambda d: d[0])
    return md5(str(k).encode("utf-8")).hexdigest()


# def timed_lru_cache(seconds: int = 120, maxsize: int = 128):
#     def wrapper_cache(func):
#         func = lru_cache(maxsize=maxsize)(func)
#         func.lifetime = timedelta(seconds=seconds)
#         func.expiration = datetime.utcnow() + func.lifetime
#
#         if not hasattr(func, 'lru_cache'):
#             setattr(func, 'lru_cache', func)
#
#         @wraps(func)
#         def wrapped_func(*args, **kwargs):
#             if datetime.utcnow() >= func.expiration:
#                 func.cache_clear()
#                 func.expiration = datetime.utcnow() + func.lifetime
#             return func(*args, **kwargs)
#
#         return wrapped_func
#
#     return wrapper_cache


def _get_expire_time():
    # 获取当前时间
    now = datetime.now()
    # 获取今天零点
    diff_date = datetime(year=now.year, month=now.month, day=now.day, hour=23, minute=59, second=59) - now

    return 172800 + diff_date.seconds + random.randint(1, 20)


def _is_open_protect():
    from dmplib.saas.project import get_project_info

    proj = get_project_info(getattr(g, 'code', '')) or {}

    return bool(proj.get('is_key_screen'))


def format_error_result(kwargs, e, mode='data'):
    results = dict()
    if mode != 'data':
        return 0
    for params in kwargs.get('chart_params') or {"": {}}:
        results[params.get('id')] = {
            "errmsg": str(e),
            "data": [],
            "marklines": [],
            "conditions": [],
            "msg": "单图取数接口异常，异常信息: " + str(e),
            "msg_code": DashboardDataMsgCode.QueryException.value,
            "error_code": QueryDataError.AnyError.value,
            "pagination": {},
            "sql": "",
            "sql_execute_time": "",
            "dataset_versions": {"data_version": "", "version": ""},
        }
    return results


def format_log_data(kwargs, e, data_id='', data=None, mode=''):
    error_lables1 = '后端取数异常'
    error_lables2 = ''
    error_code = ''
    msg = ''
    if data:
        error_lables2 = QUERY_DATA_ERROR_MAP.get(data.get('error_code')) \
                        or QUERY_DATA_ERROR_MAP.get(QueryDataError.AnyError.value)
        error_code = data.get('msg_code')
        msg = data.get('msg')
    if not msg and e:
        msg = str(e)
        if mode == 'total':
            error_code = DashboardDataMsgCode.GetTotalException.value
            error_lables2 = '获取总数异常'
    log_dashboard = getattr(g, 'log_dashboard', {})
    log_data_source = getattr(g, 'log_data_source', {})
    data_source_type, data_source_host = get_data_source_info(log_data_source)
    return {
        "org_code": getattr(g, 'code', ''),
        "module_type": FastLogger.ModuleType.DASHBOARD_MOBILE_SCREEN,
        "biz_type": get_dashboard_biz_type(log_dashboard),
        "biz_id": kwargs.get('dashboard_id'),
        "biz_name": log_dashboard.get("name", ""),
        "exec_from": getattr(g, 'sql_from', 'viewreport') or 'viewreport',
        "error_type": FastLogger.ErrorType.CHART_DATA_ERROR,
        "error_data_id": data_id,
        "error_lable1": error_lables1,
        "error_lable2":  error_lables2,
        "error_code": error_code,
        "error_msg": msg,
        "error_traceback": msg,
        "extra_info": {
            "dataset_id": getattr(g, 'dataset_id_of_query', ""),
            "data_source_id": log_data_source.get("id", ""),
            "data_source_host": data_source_host,
            "data_source_type": data_source_type,
            "mode": mode,
            "dashboard_type": log_dashboard.get("type", ""),  # 报表类型
        },
        "is_success": 0
    }


def format_normal_log_data(kwargs, chart_id='', mode='data'):
    """
    取数正常日志
    :param kwargs:
    :param chart_id:
    :param mode:
    :return:
    """
    log_data = format_log_data(kwargs, '', chart_id, None, mode)
    log_data["error_type"] = ''
    log_data["error_lable1"] = ''
    log_data["error_level"] = ''
    log_data["is_success"] = 1
    return log_data


def exclude_error_code_log(log_data, exclude_error_code):
    """
    对于取数msg_code状态码，业务上是正常的场景记录为正常日志
    包括以下场景：
    报告数据为空(没有符合条件的数据)
    :param log_data:
    :return:
    """
    if log_data.get("error_code") in exclude_error_code:
        log_data["error_type"] = ''
        log_data["error_lable1"] = ''
        log_data["error_lable2"] = ''
        log_data["error_lable3"] = ''
        log_data["error_level"] = ''
        log_data["is_success"] = 1
    return log_data


def set_cache_index(log_data, is_set_cache, is_used_cache):
    """
    记录重点大屏是否设置和使用缓存信息
    :param log_data:
    :param is_set_cache:
    :param is_used_cache:
    :return:
    """
    extra_info = log_data.get("extra_info")
    extra_info["is_set_cache"] = is_set_cache
    extra_info["is_used_cache"] = is_used_cache
    log_data["extra_info"] = extra_info
    return log_data


has_last_version = None


def data_of_last_version(mode: str = 'data', is_cache: bool = True):
    """
    上一版数, 只针对取数接口和获取总数接口
    :param mode: data（取数）、total（获取总数）
    :param is_cache: data（取数）、total（获取总数）
    :return:
    """
    def _format_data(ret: dict):
        if not ret:
            return {}
        if mode == 'data':
            # 去掉不需要的字段，减少缓存数据占用
            c_ret = deepcopy(ret)
            for _, v in c_ret.items():
                v.pop('analysis_data', '')
                v.pop('analyzer_node_time', '')
                v.pop('query_structure', '')
                v.pop('sql', '')
        else:
            c_ret = ret

        return json.dumps(c_ret, cls=BaseModelEncoder)

    def decorate(func):

        @wraps(func)
        def _handle(request, response, *args, **kwargs):

            expire_time = _get_expire_time()

            # 获取g.code
            g.code = kwargs.get("code") or request.cookies.get('tenant_code') or request.cookies.get('code') or ""

            cache = RedisCache()

            def _get_last_data(k):
                # 上一版数缓存key
                return cache.get(k)

            @timed_lru_cache(expire_time-60)
            def _has_last_version(lk):
                return 1 if _get_last_data(lk) else 0

            global has_last_version
            has_last_version = _has_last_version

            # 是否开启重点大屏
            is_key_screen = _is_open_protect()
            # 是否是重点报告
            is_key = kwargs.get("is_key")
            show_error_log = kwargs.get("show_error_log", 0)  # 1 返回真实数据
            dashboard_id = kwargs.get("dashboard_id", '')  # 1 返回真实数据
            # 重点大屏-是否设置缓存
            is_set_cache = 0
            # 重点大屏-是否使用缓存
            is_used_cache = 0
            # 去掉有变动的字段，这个字段在后端也没有使用
            if "chart_params" in kwargs:
                for item in kwargs['chart_params']:
                    item.pop('fetchTimestamp', '')
            kwargs.pop('is_real', '')
            kwargs.pop('t', '')
            kwargs.pop('__traceId__', '')

            # 租户关闭了重点大屏， 则不取缓存
            if not is_key_screen or not is_cache:
                is_key = False

            last_version_key = LAST_VERSION_KEY.format(
                user_id=getattr(g, "userid", ""),
                dashboard_id=dashboard_id,
                k=hash_k(kwargs)
            )
            last_version_key_24 = f'{last_version_key}_24'

            cache_keys = LAST_VERSION_KEYS.format(dashboard_id=dashboard_id)

            error_data = dict()

            def _check_error_data(item_data):
                if item_data.get('msg_code') != DashboardDataMsgCode.Successful.value or item_data.get('error_code'):
                    error_item = format_log_data(kwargs, None, chart_id, item_data, mode)
                    return error_item
                return None

            try:
                r, msg, res = func(request, response, *args, **kwargs)

                try:
                    # 判断是否取数异常,只处理单个取数场景，要求所有的环境都是单个取数
                    if mode == "data":
                        for chart_id, data in res.items() or {'': {}}.items():
                            if isinstance(data, dict):
                                error_data = _check_error_data(data)
                            elif isinstance(data, list):
                                for i in data:
                                    error_data = _check_error_data(i)
                            if error_data:
                                break
                        if error_data:
                            if not is_key or show_error_log in (1, '1'):
                                return r, msg, res
                            # 获取上一版数
                            res_data = _get_last_data(last_version_key)
                            if res_data:
                                res = json.loads(res_data)
                                is_used_cache = 1
                            return r, msg, res

                    if not is_key:
                        return r, msg, res

                    need_cache = True
                    # 添加二级缓存
                    if has_last_version(last_version_key):
                        # 判断是否有24小时缓存key
                        if cache.get(last_version_key_24):
                            need_cache = False
                    if show_error_log in (1, '1'):
                        need_cache = False
                    if need_cache:
                        ret = _format_data(res)
                        # 48小时缓存
                        cache.set(last_version_key, ret, expire_time)
                        # 24小时缓存
                        cache.set(last_version_key_24, 1, 86400)
                        # 记录缓存key, 供删除缓存时使用
                        cache.lpush(cache_keys, last_version_key, last_version_key_24)
                        cache.expire(cache_keys, 259200)

                        is_set_cache = 1
                except Exception as e:
                    logger.error(f"获取上一版数异常:{e}")
                return r, msg, res
            except Exception as ue:
                # get_total接口没有chart_id变量
                if 'chart_id' not in locals() and 'chart_id' not in globals():
                    chart_id = kwargs.get("id", "")
                error_data = format_log_data(kwargs, ue, chart_id, None, mode)
                res = format_error_result(kwargs, ue, mode=mode)
                if not is_key or show_error_log in (1, '1'):
                    return True, '', res
                res_data = _get_last_data(last_version_key)
                if res_data:
                    res = json.loads(res_data)
                    is_used_cache = 1
                return True, '', res
            finally:
                # 4001的错误编码不属于异常
                exclude_error_code = [DashboardDataMsgCode.NullDashboardData.value]
                # 报告取数访问日志记录，get_total接口没有chart_id变量
                if 'chart_id' not in locals() and 'chart_id' not in globals():
                    chart_id = kwargs.get("id", "")
                if error_data:
                    # 发送警告,4001状态的属于正常
                    if is_key and error_data.get("error_code") not in exclude_error_code:
                        from app_celery import dashboard_error_alarm
                        dashboard_error_alarm.apply_async(kwargs=error_data)
                else:
                    # 取数正常日志
                    error_data = format_normal_log_data(kwargs, chart_id, mode)

                # 是否重点大屏
                error_data["is_key"] = int(kwargs.get("is_key", 0))
                # 记录重点大屏是否设置缓存，是否使用缓存
                error_data = set_cache_index(error_data, is_set_cache, is_used_cache)
                # 日志特别处理
                error_data = exclude_error_code_log(error_data, exclude_error_code)
                # 记录日志
                FastLogger.BizErrorFastLogger(**error_data).record()

        return _handle

    return decorate
