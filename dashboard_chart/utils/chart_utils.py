#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/26 10:37
# <AUTHOR> caoxl
# @File     : dashboard_chart.py
import json
import time
from collections import defaultdict

from dashboard_chart.formatter.formatter import Formatter
from base.enums import FieldSortFieldSource, ColTypes
from dmplib.utils.errors import UserError
from dashboard_chart.services import proxy_dataset_service
from base.dmp_constant import KEYWORD_FLAG, FILTER_KEYWORD_PREFIX
from keywords.external_service import get_keyword_detail_by_vars, get_formatted_keyword_result_by_id


def convert_order_field_source2col_type(order_field_source: str):
    field_source_map = {
        FieldSortFieldSource.Dims.value: ColTypes.Dim.value,
        FieldSortFieldSource.Nums.value: ColTypes.Num.value,
        FieldSortFieldSource.Comparisons.value: ColTypes.Comparison.value,
        FieldSortFieldSource.Desires.value: ColTypes.Desire.value,
        FieldSortFieldSource.Zaxis.value: "zaxis",
    }
    col_type = field_source_map.get(order_field_source)
    if col_type is None:
        raise UserError(message="排序字段来源值 {order_field_source} 不存在!".format(order_field_source=col_type))
    return col_type


def convert_col_type2order_field_source(col_type: str):
    col_type_map = {
        ColTypes.Dim.value: FieldSortFieldSource.Dims.value,
        ColTypes.Num.value: FieldSortFieldSource.Nums.value,
        ColTypes.Comparison.value: FieldSortFieldSource.Comparisons.value,
        ColTypes.Desire.value: FieldSortFieldSource.Desires.value,
        "zaxis": FieldSortFieldSource.Zaxis.value,
    }
    order_field_source = col_type_map.get(col_type)
    if order_field_source is None:
        raise UserError(message="字段类型 {col_type} 不存在!".format(col_type=col_type))
    return order_field_source


def extract_indirect_query_map(filter_conditions: list, query_vars: list) -> dict:
    """
    提取间接访问的参数字典
    数据结构
    {
        "chart_id": {
            "fields": [
                "dataset_field_id": "xxxx",
                "col_name": "col1",
                "alias": "SYB_454545"
            ],
            "conditions": [
                {
                    "dataset_id": "数据集ID",
                    "dataset_field_id": "字段ID",
                    "col_name": "col1",
                    "operator": "=",
                    "col_value": "123",
                    "formula_mode": "",
                    "alias_name": "SYB_454545",
                }
            ],
            "dataset_field_dict": {},
            "result": { "col_name": {"data": [], "has_null": 1}},
            "dataset_id": "单图数据集ID",
            "dataset": {}
        }
    }
    算法说明 该结构综合query_vars 及 chart_filter_conditions 去重获取条件 合并获取field
    :param model:
    :return:
    """
    indirect_query_map = {}
    filter_conditions = filter_conditions or []
    query_vars = query_vars or []
    exist_conditions = []
    _get_indirect_query_map(indirect_query_map, exist_conditions, filter_conditions, "chart_id", "dataset_field_id")
    _get_indirect_query_map(
        indirect_query_map, exist_conditions, query_vars, "chart_initiator_id", "field_initiator_id"
    )
    return indirect_query_map


def _get_condition_key(chart_id: str, condition: dict) -> tuple:
    return tuple([chart_id, condition["operator"], condition["col_value"], condition["formula_mode"]])


def _replace_keyword_value(condition):
    # 处理传过来的条件，现在关键字的值不会支持传给前端，只是返回一个标记，
    # 前端将标记重新传回来，后端解析标记，将标记重新替换成真正的关键字的值
    if f'"{str(condition.get("value"))}"' == KEYWORD_FLAG:
        get_keyword_detail_by_vars([condition])
        default_value = condition.get('default_value')
        if condition.get('default_value_type') == 1 and default_value is not None:
            condition['value'] = default_value


def _get_indirect_query_map(
        indirect_query_map: dict,
        exist_conditions: list,
        chart_filter_conditions: list,
        chart_id_key: str,
        field_id_key: str,
):
    chart_condition_map = defaultdict(list)
    for condition in chart_filter_conditions:
        _replace_keyword_value(condition)
        chart_id = condition.get(chart_id_key)
        chart_condition_map[chart_id].append(condition)
        value_from = condition["value_from"] if "value_from" in condition and condition["value_from"] else None
        if not value_from:
            continue
        field_info = proxy_dataset_service.get_formatted_fields_by_field_id(condition.get(field_id_key))
        _extract_indirect_chart(chart_id, exist_conditions, field_info, indirect_query_map, value_from)
    # 将自身筛选条件加入到条件中
    # for chart_id, conditions in chart_condition_map.items():
    #     if chart_id not in indirect_query_map:
    #         continue
    #     _append_self_conditions(chart_id, conditions, indirect_query_map, exist_conditions)


def _append_self_conditions(chart_id: str, conditions: list, indirect_query_map: dict, exist_conditions: list):
    for condition in conditions:
        value_from = condition["value_from"] if "value_from" in condition and condition["value_from"] else None
        if condition["col_value"] is None and value_from:
            continue
        condition_key = _get_condition_key(chart_id, condition)
        if condition_key not in exist_conditions:
            continue
        exist_conditions.append(condition_key)
        indirect_query_map[chart_id]["conditions"].append(condition)


def _extract_indirect_chart(
        chart_id: str, exist_conditions: list, field_info: dict, indirect_query_map: dict, value_from: dict
):
    if chart_id not in indirect_query_map:
        indirect_query_map[chart_id] = {
            "fields": [],
            "conditions": [],
            "result": None,
            "dataset_field_dict": proxy_dataset_service.get_dataset_field_dict_with_field_id(field_info["dataset_id"]),
            "dataset_id": field_info["dataset_id"],
            "dataset": proxy_dataset_service.get_dataset(field_info["dataset_id"]),
        }
    for item in value_from["conditions"]:
        condition_key = _get_condition_key(chart_id, item)
        if condition_key in exist_conditions:
            continue
        exist_conditions.append(condition_key)
        try:
            col_value = json.loads(item["col_value"])
        except:
            col_value = item["col_value"]
        item["col_value"] = col_value
        indirect_query_map[chart_id]["conditions"].append(item)
    indirect_query_map[chart_id]["fields"].append(field_info)


def get_unix_timestamp(date_time):
    time_arr = time.strptime(date_time, "%Y-%m-%d %H:%M:%S")
    return int(time.mktime(time_arr))


def get_current_time():
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())


def batch_get_keyword_values(dashboard_filters, dashboard_chart_filters):
    # 提取条件过滤中的关键字的值
    dashboard_filter_operates = [op for df in (dashboard_filters or []) for op in (df.get('operators') or [])]
    dashboard_filters_col_values = _extra_keyword_ids_from_filters(dashboard_filter_operates)
    dashboard_chart_filters_col_values = _extra_keyword_ids_from_filters(dashboard_chart_filters)
    col_values = [*dashboard_filters_col_values, *dashboard_chart_filters_col_values]
    striped_keyword_ids = {value.strip() for value in col_values}

    return {
        keyword_id: get_formatted_keyword_result_by_id(keyword_id)
        for keyword_id in striped_keyword_ids
    }


def _extra_keyword_ids_from_filters(filters):
    # 目前条件格式的col_value只有单值字符串的形式。例如：@keyword_value:3a0807cd-15a5-e520-5bb3-cff23d9aaee1
    # 不考虑["ta","tb"]这种形式
    result = []
    for filter in filters:
        col_value = filter.get('col_value')
        operator = filter.get('operator')
        real_col_value = Formatter.get_keyword_id(operator, col_value)
        if FILTER_KEYWORD_PREFIX not in str(real_col_value):
            continue
        if isinstance(real_col_value, str):
            keyword_id = real_col_value.replace(FILTER_KEYWORD_PREFIX, '').strip()
            result.append(keyword_id)
    return result
