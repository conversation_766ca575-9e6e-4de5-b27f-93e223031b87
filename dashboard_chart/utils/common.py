import json
import logging

from base.enums import ApplicationType, DashboardStatus, DashboardTypeAccessReleased
from components.fast_logger import <PERSON><PERSON>ogger
from dmplib.hug import g


LAST_VERSION_KEY = 'last_version:{dashboard_id}:{user_id}:{k}'
LAST_VERSION_KEYS = 'last_version:{dashboard_id}'


logger = logging.getLogger(__name__)


def add_api_dataset_params(obj, sql_from=None, dataset_id=None, report_id=None, dashboard=None, dataset=None):
    if sql_from == "design_viewreport":
        _set_dashboard_designer_status(obj, sql_from)
    else:
        sql_from and setattr(obj, "sql_from", sql_from or "viewreport")
    dataset_id and setattr(obj, "dataset_id_of_query", dataset_id)
    report_id and setattr(obj, "dashboard_id_of_query", report_id)
    # 设置报告信息
    dashboard and setattr(obj, "log_dashboard", dashboard)
    # traceId
    request_data = getattr(obj, 'request_data', {})
    request_params = request_data.get('params', {})
    trace_id = request_params.get('__traceId__', '')
    setattr(obj, "trace_id_of_query", trace_id)
    if dataset:
        try:
            content = json.loads(dataset.get('content'))
        except Exception as e:
            msg = "数据集content内容序列化错误：" + str(e)
            logger.exception(msg)
            content = {}
        data_source_id = content.get("data_source_id")
        # 设置数据源
        _set_data_source(obj, data_source_id)


def set_data_index(layout):
    """
    设置是否开启 data_index
    """
    if not layout:
        return False
    if isinstance(layout, str):
        layout = json.loads(layout)
    if not isinstance(layout, dict):
        return False
    g.data_index = layout.get("dataIndex")


def _set_dashboard_designer_status(obj, sql_from):
    """
    区分设计器和预览的取数接口
    :param obj:
    :param sql_from:
    :return:
    """
    request_data = getattr(obj, 'request_data', {})
    refer = request_data.get('headers', {}).get('REFERER', '')
    if refer and 'design/' in refer:
        sql_from = "designer"
    sql_from and setattr(obj, "sql_from", sql_from or "design_viewreport")


def _set_data_source(obj, data_source_id):
    if data_source_id:
        from data_source.cache.data_source_meta_cache import get_data_source_cache
        data_source_data = get_data_source_cache(data_source_id)
        data_source_data and setattr(obj, "log_data_source", data_source_data)


def dashboard_fast_log_record(dashboard, errors: list):
    if not errors or not isinstance(errors, list):
        return
    biz_type = get_dashboard_biz_type(dashboard)
    log_data = {
        "module_type": FastLogger.ModuleType.DASHBOARD_MOBILE_SCREEN,
        "biz_type": biz_type,
        "biz_id": dashboard.get("id"),
        "biz_name": dashboard.get("name"),
        "exec_from": "design_viewreport",
        "error_type": f"{biz_type}编辑错误",
        "error_data_id": dashboard.get("id")
    }
    for error in errors:
        log_data["error_lable1"] = error.get("err_code")
        log_data["error_msg"] = error.get("msg")
        FastLogger.BizErrorFastLogger(**log_data).record()


def get_dashboard_biz_type(dashboard):
    biz_type = '未知报告类型'
    application_type = dashboard.get("application_type")
    if application_type == ApplicationType.SelfService.value:
        biz_type = FastLogger.BizType.SELF_SERVICE
    elif application_type == ApplicationType.Dashboard.value:
        biz_type = FastLogger.BizType.DASHBOARD
        if dashboard.get('platform') == 'pc':
            if dashboard.get('new_layout_type') == 1:
                biz_type = FastLogger.BizType.DASHBOARD
            elif dashboard.get('new_layout_type') == 0:
                biz_type = FastLogger.BizType.LARGE_SCREEN
        elif dashboard.get('platform') in ['new_mobile', 'mobile']:
            biz_type = FastLogger.BizType.MOBILE
    return biz_type


def get_data_source_info(data_source):
    # 获取数据源类型，数据源host
    data_source_type, data_source_host = '', ''
    if data_source:
        try:
            conn_str_dict = json.loads(data_source.get('conn_str'))
        except Exception as e:
            msg = "数据集content内容序列化错误：" + str(e)
            logger.error(msg)
            conn_str_dict = {}

        data_source_host = conn_str_dict.get("host", "")
        data_source_type = data_source.get("type")
    return data_source_type, data_source_host


def check_dashboard_release_access(obj):
    """
    检查报告的访问类型，如果是公开分享 或 授权码访问 则使用超级管理员进行api取数
    :param obj:
    :return:
    """
    dashboard = getattr(obj, 'log_dashboard', {})
    sql_from = getattr(obj, 'sql_from', 'viewreport')
    is_public_view = False
    # 报告发布且报告是公开分享或授权码
    if sql_from == 'viewreport' and dashboard.get("status") == DashboardStatus.Released.value \
            and dashboard.get("type_access_released") in [DashboardTypeAccessReleased.NoLimited.value,
                                                          DashboardTypeAccessReleased.Passwd.value]:
        is_public_view = True
    return is_public_view
