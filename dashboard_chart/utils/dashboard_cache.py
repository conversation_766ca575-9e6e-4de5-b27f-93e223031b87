#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/11/19

"""
dashboard cache
ps: 获取config配置文件节点值的方法
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from dmplib import config


def get_metadata_cache_key_prefix() -> str:
    """
    发布报告元数据缓存key前缀
    :return:
    """
    metadata_cache_key_prefix = config.get('Cache.released_dashboard_metadata_cache_key', 'dmp')
    return metadata_cache_key_prefix


def get_installed_components_cache_key() -> str:
    """
    已安装组件缓存前缀
    :return:
    """
    installed_components_cache_key = config.get('Cache.installed_components_cache_key', 'installed_components_v1')
    return installed_components_cache_key


def get_components_refresh_flag_cache_key() -> str:
    """
    组件刷新标志位缓存key
    :return:
    """
    components_refresh_flag_cache_key = config.get('Cache.components_refresh_flag_cache_key', 'components_refresh_flag')
    return components_refresh_flag_cache_key


def get_component_update_on_cache_key() -> str:
    """
    组件刷新时间缓存key
    :return:
    """
    component_update_on_cache_key = config.get('Cache.components_refresh_flag_cache_key', 'component_update_on')
    return component_update_on_cache_key
