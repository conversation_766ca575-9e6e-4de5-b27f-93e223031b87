#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/1/17 10:35
# <AUTHOR> caoxl
# @File     : external_service.py
import hug
from urllib.parse import urlencode
import logging

from base import repository
from dashboard_chart.models import ChartDataModel
from dmplib.utils.errors import UserError
from base.dmp_constant import YZS_REPORT_THIRD_APP_CODE, YZS_REPORT_APP_CODE
from dmplib import config
from .services import metadata_service, dashboard_service
from .data_query.standalone_query import StandAloneQuery
from dashboard_chart.services import dashboard_service
from user.services import user_service

logger = logging.getLogger(__name__)


def get_dashboard_metadata(dashboard_id: str, is_released: int):
    """
    获取报告元数据
    :param dashboard_id: 报告ID
    :param is_released: 是否发布
    :return:
    """
    if not dashboard_id:
        raise UserError(message="请指定报告ID")
    if not is_released:
        msg, result = metadata_service.get_screens_preview_metadata_v2(dashboard_id=dashboard_id)
    else:
        msg, result = metadata_service.get_screens_release_metadata_v2(snapshot_id=dashboard_id)
    return result


def get_dashboard_metadata_without_comment(dashboard_id: str, is_released: int):
    """
    元数据获取时不获取组件数据
    巡检目前用不到组件数据
    """
    if not dashboard_id:
        raise UserError(message="请指定报告ID")

    if not is_released:
        return metadata_service.get_screens_preview_metadata_v2_without_coment(dashboard_id)

    return metadata_service.get_screens_release_metadata_without_comment(dashboard_id)


def external_data_query(chart_data_model: ChartDataModel, dataset_version: str = None):
    """
    外部独立查询
    :param chart_data_model:
    :return:
    """
    query_cls = StandAloneQuery(chart_data_model=chart_data_model)
    return query_cls.query(dataset_version=dataset_version)


def external_get_query_underlying_data(chart_data_model: ChartDataModel):
    """
    外部独立获取查询底层结构
    :param chart_data_model:
    :return:
    """
    query_cls = StandAloneQuery(chart_data_model=chart_data_model)
    return query_cls.query_underlying_data()


def redirect_to_dashboard(request, response):
    """
    1、数见团队为ERP提供页面地址：/api/dashboard/view?report_id=123&roomid=101
    2、通过云助手扫描，打开报表页面
    3、根据云助手提供的corpid和code实现免登
    4、根据云助手提供的租户ID，获取对应数见的租户，跳转到真实的报表地址
    5、注意需要将所有的参数传递给报表页面，如示例中的 roomid
    :param request:
    :param response:
    :return:
    """
    # 参数校验
    report_id = request.params.get('report_id')
    if not all([request.headers.get("CODE"), request.headers.get("CORPID"), report_id]):
        raise UserError(code=404, message="报告不存在")
    del request.params["report_id"]
    kwargs = dict(
        app_secret=config.get('Yzs.report_app_key'),
        third_app_code=YZS_REPORT_THIRD_APP_CODE,
        app_code=YZS_REPORT_APP_CODE,
        func_name="yzs_report_login"
    )
    user_id, tenant_code, group_ids = user_service.yzs_login(request, response, kwargs.get('app_secret'), kwargs.get('third_app_code'))
    request.params["code"] = tenant_code
    redirect_url = f"/dataview/share/{report_id}?{urlencode(request.params)}"
    hug.redirect.to(redirect_url)


def get_root_dashboard_by_dashboard_id(dashboard_id):
    """
    通过当前报告ID获取跟报告ID
    :param chart_data_model:
    :return:
    """
    return dashboard_service.get_root_dashboard_by_dashboard_id(dashboard_id)


def filter_permission_dashboard(dashboard_ids):
    """
    过滤有权限的数据报告
    :param dashboard_ids:
    :return:
    """
    return dashboard_service.filter_permission_dashboard(dashboard_ids)


def get_all_dashboard_by_ids(dashboard_ids):
    """
    批量获取根报告下所有子报告，包含根报告
    :param dashboard_ids:根报告id
    :return:
    """
    return dashboard_service.get_all_dashboard_by_ids(dashboard_ids)


def get_url_for_external_report_redirect(dashboard_id):
    """
     获取外部报告跳转的中间链接，为外部报告报告跳转提供dmp的入口
    :param dashboard_id:报告ID
    :return:
    """
    return dashboard_service.get_url_for_external_report_redirect(dashboard_id)


def get_custom_redirect_url(dashboard_id):
    return dashboard_service.get_custom_redirect_url(dashboard_id)


def generate_base_url_by_terminal_type(dashboard_id, project_code, terminal_type):
    return dashboard_service.generate_base_url_by_terminal_type(dashboard_id, project_code, terminal_type)


def get_custom_normal_redirect_url(dashboard_id):
    return dashboard_service.get_custom_normal_redirect_url(dashboard_id)
