#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=R0201

"""
报告表数据仓库类
"""

# ---------------- 标准模块 ----------------
from collections import defaultdict
from copy import deepcopy

# ---------------- 业务模块 ----------------
from base.models import BaseModel
from dashboard_chart.services import proxy_dataset_service, metadata_service, chart_vars_service
from dataset import external_query_service
from dashboard_chart.repositories import metadata_build_repository
from base.enums import DashboardTypeStatus, DashboardJumpType


class MetadataStorage(BaseModel):
    """
    报告数据仓库对象
    """

    # 预览态报告表名
    tables_for_preview_dashboard = [
        "dashboard",
        "dashboard_chart",
        "screen_dashboard",
        "dashboard_chart_dim",
        "dashboard_chart_num",
        "dashboard_chart_filter",
        "dashboard_chart_filter_relation",
        "dashboard_chart_field_sort",
        "dashboard_chart_markline",
        "dashboard_chart_comparison",
        "dashboard_chart_desire",
        "dashboard_chart_params",
        "dashboard_chart_params_jump",
        "dashboard_chart_penetrate_relation",
        "dashboard_chart_selector",
        "dashboard_chart_selector_field",
        "dashboard_filter",
        "dashboard_filter_relation",
        "dashboard_jump_config",
        "dashboard_jump_relation",
        "dashboard_linkage",
        "dashboard_linkage_relation",
        "dashboard_filter_chart",
        "dashboard_filter_chart_relation",
        "dashboard_filter_chart_default_values",
        "dashboard_dataset_field_relation",
        "dashboard_dataset_vars_relation",
        "dashboard_vars_jump_relation",
    ]

    # 需要收集字段信息的报告表名
    tables_for_collect_dataset_field = [
        "dashboard_chart_dim",
        "dashboard_chart_num",
        "dashboard_chart_filter",
        "dashboard_chart_markline",
        "dashboard_chart_comparison",
        "dashboard_chart_desire",
        "dashboard_chart_params",
        "dashboard_dataset_field_relation",
        "dashboard_jump_relation",
        "dashboard_vars_jump_relation",
        "dashboard_chart_params_jump",
        "dashboard_filter",
    ]

    # 需要填充字段信息的报告表名
    tables_for_assign_dataset_field = [
        "dashboard_chart_dim",
        "dashboard_chart_num",
        "dashboard_chart_filter",
        "dashboard_chart_markline",
        "dashboard_chart_comparison",
        "dashboard_chart_desire",
        "dashboard_chart_params",
        "dashboard_filter",
    ]

    # 需要填充数据集信息的报告表名
    tables_for_assign_dataset = ["dashboard_chart"]

    # 单图section
    tables_for_chart_section = [
        "dashboard_chart_dim",
        "dashboard_chart_num",
        "dashboard_chart_filter",
        "dashboard_chart_field_sort",
        "dashboard_chart_markline",
        "dashboard_chart_comparison",
        "dashboard_chart_desire",
        "dashboard_chart_params",
    ]

    def __init__(self, dashboard_id):
        self.dashboard_id = dashboard_id
        self.table_data = {}
        self.dataset_field_id_set = set()
        self.dataset_field_dict = {}
        self.dataset_dict = {}
        self.target_dashboard_name_dict = {}
        self.section_key_dict = {}
        self.chart_vars_dict = {}
        self.__initialize_data()
        super().__init__()

    def __initialize_data(self):
        """
        初始化表数据
        :return:
        """

        # 批量获取预览态报告的表基础数据
        self._query_preview_table_data()

        # 收集引用到的数据集字段
        self._batch_collect_dataset_field()

        # 获取数据集字段信息
        self._batch_query_dataset_field()

        # 填充数据集字段信息
        self._batch_assign_dataset_field_for_section()

        # 获取数据集的信息
        self._batch_collect_and_query_dataset()

        # 批量获取跳转关系中目标报告的名称
        self._batch_get_target_dashboard_name()

        # 字段还需要另外作兼容处理的情况
        self._fillup_dashboard_chart_table_data()

        # 表基础数据预处理
        self._batch_pre_deal_with_section_data()

        # 转换单图的表基础数据为以单图id为key的dict数据
        self._batch_convert_section_to_dict()

        # 调用外部service方法，获取结构化的变量数据
        self._get_chart_vars_dict()

    @staticmethod
    def get_query_order_by(table_name):
        """
        获取排序字段
        :param table_name:
        :return:
        """
        order_by = []
        if table_name in [
            "dashboard_chart_dim",
            "dashboard_chart_num",
            "dashboard_chart_markline",
            "dashboard_chart_comparison",
            "dashboard_chart_desire",
            "dashboard_chart_params",
        ]:
            order_by.append(("rank", "ASC"))
        return order_by

    def _get_table_fields(self):
        """
        获取查询的表字段，同时统一过滤掉不需要的表字段
        :return:
        """
        table_fields_dict = {}
        for table in self.tables_for_preview_dashboard:
            table_fields = metadata_build_repository.get_table_fields(table)
            not_valid_fields = ["created_on", "modified_on", "created_by", "modified_by"]
            # 与旧元数据保持一致，屏蔽dashboard_id字段
            if table in self.tables_for_chart_section:
                not_valid_fields.append("dashboard_id")
            table_fields_dict[table] = [
                item.get("Field") for item in table_fields if item.get("Field") not in not_valid_fields
            ]
        return table_fields_dict

    def _query_preview_table_data(self):
        """
        查询预览态报告的表数据
        :return:
        """
        table_fields_dict = self._get_table_fields()
        for table_name, fields in table_fields_dict.items():

            # 查询表时需要的字段排序
            query_order_by = self.get_query_order_by(table_name)

            # dashboard表主键是id
            if table_name in ["dashboard"]:
                table_data = metadata_build_repository.get_table_data_by_id(
                    self.dashboard_id, table_name, fields, query_order_by
                )
            # 其他报告数据表主键是dashboard_id
            else:
                table_data = metadata_build_repository.get_table_data_by_dashboard_id(
                    self.dashboard_id, table_name, fields, query_order_by
                )
            self.table_data[table_name] = table_data

    def _collect_dataset_field_id(self, data, field_key_name_list):
        """
        收集数据集字段id
        :param data: 需要收集数据集字段的数据
        :param field_key_name_list: 收集的来源字段名称
        :return:
        """
        if not data or not isinstance(data, list) or not field_key_name_list:
            return self.dataset_field_id_set
        for item in data:
            for field in field_key_name_list:
                if item.get(field) and item.get(field) not in self.dataset_field_id_set:
                    self.dataset_field_id_set.add(item.get(field))
        return self.dataset_field_id_set

    def _batch_collect_dataset_field(self):
        """
        批量收集关联的数据集字段
        :return:
        """
        for single_table_name in self.tables_for_collect_dataset_field:
            single_table_data = self.table_data.get(single_table_name)
            field_key_name_list = ["dataset_field_id"]
            if single_table_name in ["dashboard_chart_num"]:
                field_key_name_list = ["num"]
            elif single_table_name in ["dashboard_chart_dim"]:
                field_key_name_list = ["dim"]
            elif single_table_name in ["dashboard_filter"]:
                field_key_name_list = ["main_dataset_field_id"]
            elif single_table_name in ["dashboard_dataset_field_relation"]:
                field_key_name_list = ["main_dataset_field_id", "related_dataset_field_id"]
            elif single_table_name in ["dashboard_chart_params_jump"]:
                field_key_name_list = ["param_dataset_field_id", "chart_dataset_field_id"]

            if single_table_data:
                self._collect_dataset_field_id(single_table_data, field_key_name_list)

    def _batch_query_dataset_field(self):
        """
        调用数据集提供的服务获取数据集字段信息
        :return:
        """
        if self.dataset_field_id_set:
            query_dataset_field_id_list = [i for i in self.dataset_field_id_set if i]
            query_dataset_field_list = external_query_service.get_multi_dataset_fields(query_dataset_field_id_list)
            if query_dataset_field_list:
                self.dataset_field_dict = {
                    item.get('id'): item
                    for item in query_dataset_field_list
                    if item.get('id') not in self.dataset_field_dict
                }

    def _assign_dataset_field_for_single_section(self, section_data, field_key_name_list):
        """
        查询到的字段信息添加到原数据项中
        :param section_data: 需要处理的单图组成数据
        :param field_key_name_list: 默认字段名是dataset_field_id
        :return:
        """
        _fields = [
            'id',
            'alias_name',
            'field_group',
            'dataset_id',
            'data_type',
            'col_name',
            'expression',
            'type',
            'visible',
        ]
        if not section_data:
            return section_data
        for single_section in section_data:
            for field_key_name in field_key_name_list:
                dataset_field_id = single_section.get(field_key_name, '')
                single_dataset_field_data = proxy_dataset_service.switch_to_alias_name(
                    self.dataset_field_dict.get(dataset_field_id, {})
                )
                # 字段id可能不存在的情况
                if not dataset_field_id or not single_dataset_field_data:
                    continue
                for single_field in _fields:
                    field_key = 'dataset_field_id' if single_field == 'id' else single_field
                    single_section.update({field_key: single_dataset_field_data.get(single_field)})
        return section_data

    def _batch_assign_dataset_field_for_section(self):
        """
        批量赋值
        :return:
        """
        for single_table_name in self.tables_for_assign_dataset_field:
            single_table_data = self.table_data.get(single_table_name)
            field_key_name_list = ["dataset_field_id"]
            if single_table_name in ["dashboard_chart_num"]:
                field_key_name_list = ["num"]
            elif single_table_name in ["dashboard_chart_dim"]:
                field_key_name_list = ["dim"]
            elif single_table_name in ["dashboard_filter"]:
                field_key_name_list = ["main_dataset_field_id", "related_dataset_field_id"]
            if single_table_data:
                self.table_data[single_table_name] = self._assign_dataset_field_for_single_section(
                    single_table_data, field_key_name_list
                )

    def _batch_collect_and_query_dataset(self):
        """
        批量调用数据集提供的服务方法，获取数据集信息
        :return:
        """
        for single_table_name in self.tables_for_assign_dataset:
            single_table_data = self.table_data.get(single_table_name)
            dataset_ids = [record.get("source") for record in single_table_data]
            for single_dataset_id in dataset_ids:
                if single_dataset_id:
                    self.dataset_dict.update({single_dataset_id: external_query_service.get_dataset(single_dataset_id)})

    def _batch_get_target_dashboard_name(self):
        """
        批量获取跳转关系中目标报告
        :return:
        """
        dashboard_jump_configs = self.table_data.get("dashboard_jump_config")
        dashboard_id_list = [
            record.get("target", "")
            for record in dashboard_jump_configs
            if record.get("target_type") in [DashboardJumpType.Dashboard.value, DashboardJumpType.SelfService.value, DashboardJumpType.Child_file.value] and record.get("target")
        ]
        self.target_dashboard_name_dict = (
            metadata_service.batch_get_dashboard_name(dashboard_id_list) if dashboard_id_list else {}
        )

    def _batch_pre_deal_with_section_data(self):
        """
        对表字段值的预处理
        :return:
        """
        tables_for_pre_deal_with = [
            "dashboard_chart_dim",
            "dashboard_chart_num",
            "dashboard_chart_field_sort",
            "dashboard_chart_desire",
        ]
        for table_name in tables_for_pre_deal_with:
            table_data = self.table_data.get(table_name)

            new_table_data = table_data
            if table_name in ["dashboard_chart_dim"]:
                new_table_data = metadata_service.pre_deal_with_section_data(
                    table_data, loads_keys=['content'], pop_keys=['chart_params_jump', 'dashboard_jump_config']
                )
            elif table_name in ["dashboard_chart_num"]:
                new_table_data = metadata_service.pre_deal_with_section_data(
                    table_data, loads_keys=['display_format'], pop_keys=['chart_params_jump', 'dashboard_jump_config']
                )
            elif table_name in ["dashboard_chart_desire"]:
                new_table_data = metadata_service.pre_deal_with_section_data(table_data, loads_keys=['display_format'])
            elif table_name in ["dashboard_chart_field_sort"]:
                new_table_data = metadata_service.pre_deal_with_section_data(table_data, loads_keys=['content'])
            self.table_data[table_name] = new_table_data

    def _batch_convert_section_to_dict(self):
        """
        批量转换为以单图id为key的数组数据
        :return:
        """
        for table_name in self.tables_for_chart_section:
            table_data = self.table_data.get(table_name)
            single_section_dict = defaultdict(list)
            if not table_data:
                self.section_key_dict.update({table_name: {}})
                continue
            for record in table_data:
                single_section_dict[record.get("dashboard_chart_id")].append(record)
            self.section_key_dict.update({table_name: single_section_dict})

    def _get_chart_vars_dict(self):
        """
        获取单图变量数据
        :return:
        """
        chart_list = []
        dashboard_charts = self.table_data.get("dashboard_chart")
        for single_chart in dashboard_charts:
            chart_id = single_chart.get("id")
            tmp_single_chart = deepcopy(single_chart)
            tmp_single_chart["dims"] = self.section_key_dict.get("dashboard_chart_dim", {}).get(chart_id)
            tmp_single_chart["nums"] = self.section_key_dict.get("dashboard_chart_num", {}).get(chart_id)
            tmp_single_chart["comparisons"] = self.section_key_dict.get("dashboard_chart_comparison", {}).get(chart_id)
            tmp_single_chart["filters"] = self.section_key_dict.get("dashboard_chart_filter", {}).get(chart_id)
            tmp_single_chart["zaxis"] = self.section_key_dict.get("dashboard_chart_num", {}).get(chart_id)
            tmp_single_chart["params"] = self.section_key_dict.get("dashboard_chart_params", {}).get(chart_id)
            tmp_single_chart["desires"] = self.section_key_dict.get("dashboard_chart_desire", {}).get(chart_id)
            tmp_single_chart["marklines"] = self.section_key_dict.get("dashboard_chart_markline", {}).get(chart_id)
            chart_list.append(tmp_single_chart)

        # 调用chart_vars_service提供方法获取变量绑定数据
        self.chart_vars_dict = chart_vars_service.get_chart_dataset_vars(self.dashboard_id, chart_list)

    def _fillup_dashboard_chart_table_data(self):
        """
        对dashboard_chart表数据的字段兼容处理
        :return:
        """
        # 填充dashboard_chart表数据中的logic_type
        self.table_data["dashboard_chart"] = metadata_service.batch_fill_data_logic_type_code(
            self.table_data.get("dashboard_chart", [])
        )

    def get_specific_table_data(self, table_name):
        """
        获取指定表数据
        :param table_name:
        :return:
        """
        if not self.table_data:
            return []
        return self.table_data.get(table_name, [])

    def get_dashboard_info(self):
        """
        获取报告信息表数据
        :return:
        """
        dashboard_list = self.get_specific_table_data("dashboard")
        if not dashboard_list:
            return {}
        return dashboard_list[0]

    @staticmethod
    def filter_screens_table_data(screen_dashboard_list):
        """
        对多屏数组进行筛选，排序
        :param screen_dashboard_list:
        :return:
        """
        if not screen_dashboard_list:
            return screen_dashboard_list
        # 目前只支持适用于预览态的元数据
        screen_dashboard_list = list(
            filter(lambda i: i.get("type") == DashboardTypeStatus.Draft.value, screen_dashboard_list)
        )
        screen_dashboard_list = sorted(screen_dashboard_list, key=lambda i: i.get("rank"))
        return screen_dashboard_list
