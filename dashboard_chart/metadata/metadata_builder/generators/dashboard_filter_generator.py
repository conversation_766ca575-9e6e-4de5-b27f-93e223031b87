#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
from collections import defaultdict

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardFilterGenerator(MetadataBaseGenerator):
    """
    报告级筛选
    """

    def generator_name(self):
        return "dashboard_filter"

    @staticmethod
    def get_real_operator(dashboard_filters, dashboard_filter_relations):
        """
        获取到真实的operator数据
        :param dashboard_filters:
        :param dashboard_filter_relations:
        :return:
        """
        for single_filter in dashboard_filters:
            single_filter["operators"] = []
            for single_relation in dashboard_filter_relations:
                if single_filter.get("id") == single_relation.get("dashboard_filter_id"):
                    single_operator = {
                        "id": single_relation.get("id"),
                        "operator": single_relation.get("operator"),
                        "col_value": single_relation.get("col_value"),
                        "select_all_flag": single_relation.get("select_all_flag"),
                    }
                    single_filter["operators"].append(single_operator)

    def _get_formatted_dashboard_filters(self, dashboard_filters, dashboard_dataset_field_relations):
        """
        格式化报告级筛选数据
        :param dashboard_filters:
        :param dashboard_dataset_field_relations:
        :return:
        """
        # 获取related_dataset_field_id对应数据集id
        dashboard_fitler_relation_dict = defaultdict(list)
        for field_relation in dashboard_dataset_field_relations:
            related_dataset_field_id = field_relation.get('related_dataset_field_id')
            main_dataset_field_id = field_relation.get('main_dataset_field_id')
            # 调用数据集服务获取数据集数据
            related_dataset_data = self.storage.dataset_field_dict.get(related_dataset_field_id, {})
            field_relation['related_dataset_id'] = related_dataset_data.get('dataset_id', '')
            # 同一个主数据集字段id下的关联关系
            dashboard_fitler_relation_dict[main_dataset_field_id].append(field_relation)

        # 格式化的报告级筛选数据
        formatted_filter_data = list()
        for single_dashboard_filter in dashboard_filters:
            main_dataset_field_id = single_dashboard_filter.get('main_dataset_field_id', '')
            query_field_data = self.storage.dataset_field_dict.get(main_dataset_field_id, {})
            # 如果字段已不存在，筛选条件则无法生效，直接过滤掉
            if not query_field_data:
                continue
            filter_data = {
                'id': single_dashboard_filter.get('id'),
                'dashboard_id': single_dashboard_filter.get('dashboard_id'),
                'main_dataset_field_id': single_dashboard_filter.get('main_dataset_field_id'),
                'operator': single_dashboard_filter.get('operator'),
                'col_value': single_dashboard_filter.get('col_value'),
                'select_all_flag': single_dashboard_filter.get('select_all_flag'),
                'operators': single_dashboard_filter.get('operators'),
                'main_dataset_id': query_field_data.get('dataset_id'),
                'alias_name': query_field_data.get('alias_name'),
                'col_name': query_field_data.get('col_name'),
                'data_type': query_field_data.get('data_type'),
                'field_group': query_field_data.get('field_group'),
                'type': query_field_data.get('type'),
                'expression': query_field_data.get('expression'),
                'format': query_field_data.get('format'),
                "filter_relations": dashboard_fitler_relation_dict.get(main_dataset_field_id, []),
            }
            formatted_filter_data.append(filter_data)
        return formatted_filter_data

    def build(self):
        """

        :return:
        """
        # 获取报告级筛选数据
        dashboard_filters = self.storage.get_specific_table_data("dashboard_filter")
        dashboard_filter_relations = self.storage.get_specific_table_data("dashboard_filter_relation")
        self.get_real_operator(dashboard_filters, dashboard_filter_relations)

        # 获取字段关联数据
        dashboard_dataset_field_relations = self.storage.get_specific_table_data("dashboard_dataset_field_relation")

        # 格式化报告级筛选数据
        result = self._get_formatted_dashboard_filters(dashboard_filters, dashboard_dataset_field_relations)

        return result
