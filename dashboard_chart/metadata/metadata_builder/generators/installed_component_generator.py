#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator
from dashboard_chart.services import components_service


class InstalledComponentGenerator(MetadataBaseGenerator):
    """
    已安装组件
    """

    def generator_name(self):
        return "installed_component"

    def build(self):
        """

        :return:
        """
        _, components = components_service.get_installed_components()
        return components
