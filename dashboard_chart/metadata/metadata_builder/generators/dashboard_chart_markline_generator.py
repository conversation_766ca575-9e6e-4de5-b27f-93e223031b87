#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartMarklineGenerator(MetadataBaseGenerator):
    """
    单图辅助线
    """

    def generator_name(self):
        return "dashboard_chart_markline"

    def build(self):
        """

        :return:
        """
        marklines_data = []
        data_dict = self.storage.section_key_dict.get("dashboard_chart_markline", {})
        chart_markline_data = data_dict.get(self.op_chart_id, [])
        for item in chart_markline_data:
            marklines_data.append(
                {
                    "id": item.get("id"),
                    "dashboard_chart_id": item.get("dashboard_chart_id"),
                    "formula_mode": item.get("formula_mode"),
                    "name": item.get("name"),
                    "mode": item.get("mode"),
                    "value": item.get("value"),
                    "axis_type": item.get("axis_type"),
                    "num": item.get("num"),
                }
            )
        return marklines_data
