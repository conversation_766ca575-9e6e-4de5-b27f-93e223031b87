#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
from collections import defaultdict

# ---------------- 业务模块 ----------------
from base.enums import DashboardJumpType
from .generator import MetadataBaseGenerator


class RedirectGenerator(MetadataBaseGenerator):
    """
    跳转
    """

    def generator_name(self):
        return "redirect"

    def _get_jump_relations(self, dashboard_chart_id, jump_config_id):
        """

        :param dashboard_chart_id:
        :param jump_config_id:
        :return:
        """
        jump_relations = self.storage.get_specific_table_data("dashboard_jump_relation")
        results = list()
        if jump_relations:
            for item in jump_relations:
                if item.get("dashboard_chart_id") != dashboard_chart_id or item.get("jump_config_id") != jump_config_id:
                    continue
                field_initiator_id = item.get('dataset_field_id', '')
                match_field_info = self.storage.dataset_field_dict.get(field_initiator_id, {})
                results.append(
                    {
                        "relation_type": 0,
                        "field_initiator_id": field_initiator_id,
                        "dashboard_filter_id": item.get('dashboard_filter_id', ""),
                        "initiator_alias": match_field_info.get("alias_name", ""),
                    }
                )
        return results

    def _get_var_jump_relations(self, jump_config_id):
        """
        组装获取普通跳转关系
        :param jump_config_id:
        :return:
        """
        var_jump_relations = self.storage.get_specific_table_data("dashboard_vars_jump_relation")
        results = list()
        for item in var_jump_relations:

            if item.get("jump_config_id") != jump_config_id:
                continue
            field_initiator_id = item.get('dataset_field_id', '')
            match_field_info = self.storage.dataset_field_dict.get(field_initiator_id, {})
            results.append(
                {
                    "relation_type": 2,
                    "dataset_id": item.get('dataset_id', ""),
                    "var_id": item.get("var_id", ""),
                    "field_initiator_id": field_initiator_id,
                    "dashboard_filter_id": item.get("dashboard_filter_id", ""),
                    "initiator_alias": match_field_info.get("alias_name", ""),
                }
            )
        return results

    def _get_param_jump_relations(self, dashboard_chart_id, dataset_field_id):
        """
        组装获取参数跳转关系
        :param dashboard_chart_id:
        :param dataset_field_id:
        :return:
        """
        param_jump_relations = self.storage.get_specific_table_data("dashboard_chart_params_jump")
        results = list()
        if param_jump_relations:
            for item in param_jump_relations:

                if (
                    item.get("dashboard_chart_id") != dashboard_chart_id
                    or item.get("chart_dataset_field_id") != dataset_field_id
                ):
                    continue
                field_initiator_id = item.get('param_dataset_field_id', '')
                match_field_info = self.storage.dataset_field_dict.get(field_initiator_id, {})
                results.append(
                    {
                        "relation_type": 1,
                        "field_initiator_id": field_initiator_id,
                        "dashboard_filter_id": item.get('dashboard_filter_id', ""),
                        "initiator_alias": match_field_info.get("alias_name", ""),
                    }
                )
        return results

    def build(self):
        """

        :return:
        """
        dashboard_jump_configs = self.storage.get_specific_table_data("dashboard_jump_config")

        chart_redirect_dict = defaultdict(list)
        for single_jump_config in dashboard_jump_configs:
            relations = list()
            dashboard_chart_id = single_jump_config.get('dashboard_chart_id', '')
            dataset_field_id = single_jump_config.get('dataset_field_id', '')
            dataset_field_type = single_jump_config.get('dataset_field_type', 0)
            jump_config_id = single_jump_config.get('id', '')
            target = single_jump_config.get("target")
            target_type = single_jump_config.get("target_type")

            single_field_data_dict = {
                "has_token": single_jump_config.get('has_token'),
                "target": target,
                "target_type": target_type,
                "open_way": single_jump_config.get('open_way'),
                "dataset_field_id": dataset_field_id,
                "type": dataset_field_type,
                "id": single_jump_config.get('id'),
                "status": single_jump_config.get("status"),
            }

            # 被跳转的是报告则需要报告名称
            dashboard_name = (
                self.storage.target_dashboard_name_dict.get(target, "")
                if target_type in [DashboardJumpType.Dashboard.value, DashboardJumpType.Child_file.value, DashboardJumpType.SelfService.value] and target
                else ""
            )
            single_field_data_dict['dashboard_name'] = dashboard_name

            # 获取字段跳转关系
            jump_relations = self._get_jump_relations(dashboard_chart_id, jump_config_id)
            relations = [*relations, *jump_relations]

            # 获取变量跳转关系
            var_jump_relations = self._get_var_jump_relations(jump_config_id)
            relations = [*relations, *var_jump_relations]

            # 获取参数跳转关系
            param_jump_relations = self._get_param_jump_relations(dashboard_chart_id, dataset_field_id)
            relations = [*relations, *param_jump_relations]

            single_field_data_dict['relations'] = relations
            chart_redirect_dict[dashboard_chart_id].append(single_field_data_dict)

        # 转换结构
        redirects = list()
        for chart_id, chart_redirect_list in chart_redirect_dict.items():
            redirects.append({'chart_id': chart_id, 'chart_redirect': chart_redirect_list})

        return redirects
