#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartZaxisGenerator(MetadataBaseGenerator):
    """
    单图z轴
    """

    def generator_name(self):
        return "dashboard_chart_zaxis"

    def build(self):
        """

        :return:
        """
        zaxis_data = []
        data_dict = self.storage.section_key_dict.get("dashboard_chart_num", {})
        chart_num_data = data_dict.get(self.op_chart_id, [])
        for item in chart_num_data:
            if item.get('axis_type'):
                zaxis_data.append(item)
        return zaxis_data
