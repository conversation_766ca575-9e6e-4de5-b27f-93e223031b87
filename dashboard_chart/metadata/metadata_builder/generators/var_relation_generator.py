#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class VarRelationGenerator(MetadataBaseGenerator):
    """
    数据集字段关联的变量
    """

    def generator_name(self):
        return "var_relation"

    def build(self):
        """

        :return:
        """
        build_data = []
        var_relations = self.storage.get_specific_table_data("dashboard_dataset_vars_relation")
        for item in var_relations:
            build_data.append(
                {
                    "id": item.get("id"),
                    "chart_initiator_id": item.get("chart_initiator_id"),
                    "field_initiator_id": item.get("field_initiator_id"),
                    "dashboard_id": item.get("dashboard_id"),
                    "var_id": item.get("var_id"),
                    "dataset_id": item.get("var_dataset_id"),
                }
            )
        return build_data
