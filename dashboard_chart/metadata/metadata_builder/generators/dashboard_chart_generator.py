#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from dmplib.utils.errors import UserError
from .generator import MetadataBaseGenerator


class DashboardChartGenerator(MetadataBaseGenerator):
    """
    单图
    """

    def generator_name(self):
        return "dashboard_chart"

    def build(self):
        """

        :return:
        """
        dashboard_charts = self.storage.get_specific_table_data("dashboard_chart")
        dashboard_charts_dict = self.convert_to_dict(dashboard_charts, "id")
        op_chart_infos = dashboard_charts_dict.get(self.op_chart_id)
        op_chart_info = op_chart_infos[0] if op_chart_infos else {}
        if not op_chart_info:
            raise UserError(message=f"获取单图ID【{self.op_chart_id}】配置数据异常")
        extra_dataset_info = self.storage.dataset_dict.get(op_chart_info.get("source"), {})

        data_type_meta = {"logic_type": op_chart_info.get("data_logic_type_code", "")}
        datasource_meta = {"id": op_chart_info.get("source", ""), "type": extra_dataset_info.get("type", "")}
        funcsetup_meta = {
            "display_item": op_chart_info.get("display_item"),
            "refresh_rate": op_chart_info.get("refresh_rate"),
        }

        data_meta = {
            "data_type": data_type_meta,
            "datasource": datasource_meta,
            "default_value": "",
            "enable_subtotal": op_chart_info.get("enable_subtotal"),
            "enable_summary": op_chart_info.get("enable_summary"),
            "filter_relation": op_chart_info.get("filter_relation"),
            "chart_default_value": [],  # 在dashboard_filter_chart_default_value_generator中处理
            "indicator": {},  # 在各个单独的generator中处理
        }

        chart_meta = {
            "id": op_chart_info.get("id"),
            "name": op_chart_info.get("name"),
            "chart_component_code": op_chart_info.get("chart_code"),
            "chart_type": op_chart_info.get("chart_type"),
            "column_order": op_chart_info.get("column_order"),
            "config": op_chart_info.get("config") if op_chart_info.get("config") else "",
            "level_code": op_chart_info.get("level_code"),
            "page_size": op_chart_info.get("page_size"),
            "percentage": op_chart_info.get("percentage"),
            "sort_method": op_chart_info.get("sort_method"),
            "data_modified_on": op_chart_info.get("data_modified_on"),
            "funcSetup": funcsetup_meta,
            "position": self._decode_for_json(op_chart_info.get("position")),
            "data": data_meta,
            "parent_child_id": op_chart_info.get('parent_chart_id'),
            "child_rank": op_chart_info.get('child_rank'),
        }
        return chart_meta
