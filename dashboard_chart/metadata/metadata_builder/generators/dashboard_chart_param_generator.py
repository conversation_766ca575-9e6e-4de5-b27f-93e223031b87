#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartParamGenerator(MetadataBaseGenerator):
    """
    参数字段
    """

    def generator_name(self):
        return "dashboard_chart_param"

    def build(self):
        """

        :return:
        """
        cur_dashboard_id = self.storage.get_dashboard_info().get("id", "")
        data_dict = self.storage.section_key_dict.get("dashboard_chart_params", {})
        chart_param_data = data_dict.get(self.op_chart_id, [])
        for item in chart_param_data:
            item["dashboard_id"] = cur_dashboard_id
            # 与旧元数据保持一致，param_id换为id
            if "param_id" in item:
                item["id"] = item["param_id"]
                item.pop("param_id")
        return chart_param_data
