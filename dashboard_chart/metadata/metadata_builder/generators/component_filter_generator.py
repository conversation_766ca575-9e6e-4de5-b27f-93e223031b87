#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class ComponentFilterGenerator(MetadataBaseGenerator):
    """
    组件筛选
    """

    def generator_name(self):
        return "component_filter"

    def build(self):
        """

        :return:
        """
        config_relations = self.storage.get_specific_table_data("dashboard_filter_chart")
        valid_config_realtions = self.get_valid_config_relations(config_relations)
        field_relations = self.storage.get_specific_table_data("dashboard_filter_chart_relation")
        filter_relations = self.get_filter_relations(valid_config_realtions, field_relations, "chart_filter")
        return self.convert_filter_relations_to_tree(filter_relations)
