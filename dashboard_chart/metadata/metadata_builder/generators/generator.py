#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
构造器基类
"""

# ---------------- 标准模块 ----------------
import json
from datetime import datetime
from collections import defaultdict
from copy import deepcopy

# ---------------- 业务模块 ----------------
from base.models import BaseModel
from ..storage import MetadataStorage


class MetadataBaseGenerator(BaseModel):
    def __init__(self, storage: MetadataStorage, op_chart_id=""):
        self.op_chart_id = op_chart_id
        self.storage = storage
        super().__init__()

    def build(self):
        """
        执行构造
        :return:
        """

    def generator_name(self):
        """
        构造器名称
        :return:
        """

    @staticmethod
    def _encode_for_json(data):
        """
        json.dumps()
        :param data:
        :return:
        """
        try:
            data = json.dumps(data)
            return data
        except Exception:
            return data

    @staticmethod
    def _decode_for_json(data):
        """
        json.loads()
        :param data:
        :return:
        """
        try:
            data = json.loads(data)
            return data
        except Exception:
            return data

    @staticmethod
    def _time_to_str(time, time_format="'%Y-%m-%d %H:%M:%S'"):
        """
        转换时间戳
        :param time:
        :param format:
        :return:
        """
        try:
            if isinstance(time, datetime):
                return time.strftime(time_format)
            return time
        except Exception:
            return time

    @staticmethod
    def convert_to_dict(data, primary_key):
        """
        转换为以primary_key为键的字典数据
        :param data:
        :param primary_key:
        :return:
        """
        result = defaultdict(list)
        if not data or not isinstance(data, list):
            return result
        for item in data:
            result[item.get(primary_key, "")].append(item)
        return result

    @staticmethod
    def get_filter_relations(config_relations, field_relations, relation_source):
        """
        获取filter_relations
        :return:
        """
        filter_relations = []
        for cf in config_relations:
            single_filter = {
                "config_id": cf.get("id"),
                "chart_initiator_id": cf.get("chart_id"),
                "chart_initiator_field_id": cf.get("dataset_field_id"),
                "chart_initiator_dataset_id": cf.get("dataset_id"),
            }
            for fr in field_relations:
                filter_id = ""
                if relation_source == "chart_filter":
                    filter_id = fr.get("filter_id", "")
                elif relation_source == "chart_linkage":
                    filter_id = fr.get("link_id", "")
                if cf.get("id") == filter_id:
                    op_single_filter = deepcopy(single_filter)
                    op_single_filter.update(
                        {
                            "relation_id": fr.get("id"),
                            "chart_responder_id": fr.get("chart_responder_id"),
                            "chart_responder_field_id": fr.get("field_responder_id"),
                            "chart_responder_dataset_id": fr.get("dataset_responder_id"),
                        }
                    )
                    filter_relations.append(op_single_filter)
        return filter_relations

    @staticmethod
    def convert_filter_relations_to_tree(filter_relations):
        """
        将单图的关系型数据转换为层级数据
        :param filter_relations:
        :return:
        """
        chart_relation_dict = dict()
        for item in filter_relations:
            config_id = item.get("config_id")
            relation_info = {
                "id": item.get("relation_id"),
                "chart_responder_id": item.get("chart_responder_id"),
                "related_dataset_id": item.get("chart_responder_dataset_id"),
                "field_responder_id": item.get("chart_responder_field_id"),
                "is_same_dataset": 1
                if item.get("chart_initiator_dataset_id") == item.get("chart_responder_dataset_id")
                else 0,
            }
            if config_id not in chart_relation_dict:
                single_relation = {
                    "id": config_id,
                    "chart_initiator_id": item.get("chart_initiator_id"),
                    "dataset_id": item.get("chart_initiator_dataset_id"),
                    "field_initiator_id": item.get("chart_initiator_field_id"),
                    "related_list": [],
                }
                chart_relation_dict[config_id] = single_relation
            chart_relation_dict[config_id]["related_list"].append(relation_info)

        return list(chart_relation_dict.values())

    def get_valid_config_relations(self, config_relations):
        """
        获取筛选后的config_relations数据
        :param config_relations:
        :return:
        """
        dashboard_charts = self.storage.get_specific_table_data("dashboard_chart")
        valid_chart_id_list = [item.get("id") for item in dashboard_charts]
        return filter(lambda i: i.get("chart_id") in valid_chart_id_list, config_relations)
