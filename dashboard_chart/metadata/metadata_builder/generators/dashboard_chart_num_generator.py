#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartNumGenerator(MetadataBaseGenerator):
    """
    数值字段
    """

    def generator_name(self):
        return "dashboard_chart_num"

    def build(self):
        """

        :return:
        """
        nums_data = []
        data_dict = self.storage.section_key_dict.get("dashboard_chart_num", {})
        chart_num_data = data_dict.get(self.op_chart_id, [])
        for item in chart_num_data:
            if not item.get('axis_type'):
                nums_data.append(item)
        # 保持和旧的num查询逻辑一致，按rank排序
        return sorted(nums_data, key=lambda i: i.get("rank"))
