#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardGenerator(MetadataBaseGenerator):
    """
    报告信息
    """

    def generator_name(self):
        return "dashboard"

    def build(self):
        """

        :return:
        """
        dashboard = self.storage.get_dashboard_info()

        layout = self._decode_for_json(dashboard.get("layout"))
        layout = {} if not layout else layout
        layout["platform"] = dashboard.get("platform")

        styles_meta = {
            "theme": dashboard.get("theme"),
            "attrs": {},
            "background": self._decode_for_json(dashboard.get("background")),
            "grid_padding": self._decode_for_json(dashboard.get("grid_padding")),
        }

        publish_meta = {
            "status": dashboard.get("status"),
            "type_access_released": dashboard.get("type_access_released"),
            "share_secret_key": dashboard.get("share_secret_key"),
            "released_on": "",  # 预览态报告数据此字段值是没有内容的
        }

        result = {
            "id": dashboard.get("id"),
            "name": dashboard.get("name"),
            "description": dashboard.get("description"),
            "level_code": dashboard.get("level_code"),
            "biz_code": dashboard.get("biz_code"),
            "cover": dashboard.get("cover"),
            "create_type": dashboard.get("create_type", 0),
            "new_layout_type": "grid" if dashboard.get("new_layout_type", 0) == 1 else "free",
            "parent_id": dashboard.get("parent_id"),
            "is_show_mark_img": dashboard.get("is_show_mark_img"),
            "layout": layout,
            "styles": styles_meta,
            "scale_mode": dashboard.get("scale_mode"),
            "rank": dashboard.get("rank"),
            "publish": publish_meta,
        }
        return result
