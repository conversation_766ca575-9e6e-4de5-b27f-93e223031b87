#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartDesireGenerator(MetadataBaseGenerator):
    """
    单图目标值字段
    """

    def generator_name(self):
        return "dashboard_chart_desire"

    def build(self):
        """

        :return:
        """
        data_dict = self.storage.section_key_dict.get("dashboard_chart_desire", {})
        return data_dict.get(self.op_chart_id, [])
