#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartVarGenerator(MetadataBaseGenerator):
    """
    单图绑定的变量
    """

    def generator_name(self):
        return "dashboard_chart_var"

    def build(self):
        """

        :return:
        """
        return self.storage.chart_vars_dict.get(self.op_chart_id, [])
