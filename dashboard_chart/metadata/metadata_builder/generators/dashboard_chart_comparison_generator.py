#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartComparisonGenerator(MetadataBaseGenerator):
    """
    对比字段
    """

    def generator_name(self):
        return "dashboard_chart_comparison"

    def build(self):
        """

        :return:
        """
        data_dict = self.storage.section_key_dict.get("dashboard_chart_comparison", {})
        return data_dict.get(self.op_chart_id, [])
