#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class ScreensGenerator(MetadataBaseGenerator):
    """
    多屏
    """

    def generator_name(self):
        return "screens"

    def build(self):
        """

        :return:
        """
        result = []
        screen_dashboard_list = self.storage.get_specific_table_data("screen_dashboard")
        dashboard = self.storage.get_dashboard_info()
        if dashboard and dashboard.get("is_multiple_screen") == 1:
            # 对多屏数组进行筛选，排序
            screen_dashboard = self.storage.filter_screens_table_data(screen_dashboard_list)
            for single_screen_dashboard in screen_dashboard:
                screen_id = single_screen_dashboard.get('screen_id', '')
                if not screen_id:
                    continue
                # 兼容前端调用接口使用，dashboard_id和screen_id意义互换
                # 兼容已发布元数据接口，带上snapshot_id
                single_dashboard_dict = dict()
                single_dashboard_dict['dashboard_id'] = single_screen_dashboard.get('screen_id')
                single_dashboard_dict['screen_id'] = single_screen_dashboard.get('dashboard_id')
                single_dashboard_dict['snapshot_id'] = ''
                result.append(single_dashboard_dict)
        elif dashboard and dashboard.get("is_multiple_screen") == 0:
            single_dashboard_dict = dict()
            single_dashboard_dict['dashboard_id'] = dashboard.get('id')
            single_dashboard_dict['screen_id'] = dashboard.get('id')
            single_dashboard_dict['snapshot_id'] = ''
            result.append(single_dashboard_dict)

        return result
