#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class LinkageGenerator(MetadataBaseGenerator):
    """
    联动
    """

    def generator_name(self):
        return "linkage"

    def build(self):
        """

        :return:
        """
        config_relations = self.storage.get_specific_table_data("dashboard_linkage")
        valid_config_realtions = self.get_valid_config_relations(config_relations)
        field_relations = self.storage.get_specific_table_data("dashboard_linkage_relation")
        filter_relations = self.get_filter_relations(valid_config_realtions, field_relations, "chart_linkage")
        return self.convert_filter_relations_to_tree(filter_relations)
