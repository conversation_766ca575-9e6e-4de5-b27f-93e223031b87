#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartDimGenerator(MetadataBaseGenerator):
    """
    维度字段
    """

    def generator_name(self):
        return "dashboard_chart_dim"

    def build(self):
        """

        :return:
        """
        data_dict = self.storage.section_key_dict.get("dashboard_chart_dim", {})
        # 保持和旧的dim查询逻辑一致，按rank，dim_type排序
        dims_data = sorted(data_dict.get(self.op_chart_id, []), key=lambda i: (i.get("rank"), i.get("dim_type")))
        return dims_data
