#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartFilterGenerator(MetadataBaseGenerator):
    """
    单图筛选
    """

    def generator_name(self):
        return "dashboard_chart_filter"

    @staticmethod
    def get_real_operator(dashboard_chart_filters, dashboard_chart_filter_relations):
        """
        获取到真实的operator数据
        :param dashboard_chart_filters:
        :param dashboard_chart_filter_relations:
        :return:
        """
        for single_chart_filter in dashboard_chart_filters:
            single_chart_filter["operators"] = []
            single_chart_filter["filter_id"] = single_chart_filter.get("id", "")
            for single_relation in dashboard_chart_filter_relations:
                if single_chart_filter.get("id") == single_relation.get("dashboard_chart_filter_id"):
                    single_operator = {
                        "id": single_relation.get("id"),
                        "operator": single_relation.get("operator"),
                        "col_value": single_relation.get("col_value"),
                    }
                    single_chart_filter["filter_id"] = single_chart_filter.get("id")
                    single_chart_filter["operators"].append(single_operator)

    def build(self):
        """

        :return:
        """
        dashboard_chart_filters = self.storage.get_specific_table_data("dashboard_chart_filter")
        dashboard_chart_filters = list(
            filter(lambda i: i.get("dashboard_chart_id") == self.op_chart_id, dashboard_chart_filters)
        )
        dashboard_chart_filter_relations = self.storage.get_specific_table_data("dashboard_chart_filter_relation")
        self.get_real_operator(dashboard_chart_filters, dashboard_chart_filter_relations)
        return dashboard_chart_filters
