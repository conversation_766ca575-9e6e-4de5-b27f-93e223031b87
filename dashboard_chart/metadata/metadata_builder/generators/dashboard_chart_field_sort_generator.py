#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class DashboardChartFieldSortGenerator(MetadataBaseGenerator):
    """
    字段排序
    """

    def generator_name(self):
        return "dashboard_chart_field_sort"

    def build(self):
        """

        :return:
        """
        field_sort_data = []
        data_dict = self.storage.section_key_dict.get("dashboard_chart_field_sort", {})
        orig_data = data_dict.get(self.op_chart_id, [])
        # 保持和旧元数据一致
        for item in orig_data:
            field_sort_data.append(
                {
                    "id": item.get("id"),
                    "dataset_field_id": item.get("dataset_field_id"),
                    "field_source": item.get("field_source"),
                    "sort": item.get("sort"),
                    "content": item.get("content"),
                    "weight": item.get("weight"),
                }
            )
        return field_sort_data
