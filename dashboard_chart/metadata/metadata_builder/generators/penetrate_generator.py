#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
from collections import defaultdict

# ---------------- 业务模块 ----------------
from .generator import MetadataBaseGenerator


class PenetrateGenerator(MetadataBaseGenerator):
    """
    穿透
    """

    def generator_name(self):
        return "penetrate"

    def get_penetrate_chart_list(self):
        """

        :return:
        """
        penetrate_list = []
        dashboard_charts = self.storage.get_specific_table_data("dashboard_chart")
        parent_ids = [item.get("parent_id") for item in dashboard_charts if item.get("parent_id")]
        for chart in dashboard_charts:
            if chart.get("id") in parent_ids or chart.get("parent_id"):
                penetrate_list.append({"chart_id": chart.get("id"), "parent_id": chart.get("parent_id")})
        return penetrate_list

    def build(self):
        """

        :return:
        """
        dashboard_chart_penetrate_relations = self.storage.get_specific_table_data("dashboard_chart_penetrate_relation")
        penetrate_chart_list = self.get_penetrate_chart_list()

        chart_id_penetrate_relation = defaultdict(list)
        chart_id_penetrate_filter_relation = defaultdict(list)
        for relation in dashboard_chart_penetrate_relations:
            item = {
                'id': relation.get("id"),
                'parent_chart_field_id': relation.get("parent_chart_field_id"),
                'child_chart_field_id': relation.get("child_chart_field_id"),
                "type": relation.get("type"),
            }
            # type为1是筛选联动配置数据
            if relation.get('type') == 1:
                chart_id_penetrate_filter_relation[relation['dashboard_chart_id']].append(item)
            else:
                chart_id_penetrate_relation[relation['dashboard_chart_id']].append(item)
        for single_penetrate in penetrate_chart_list:
            single_penetrate['relation'] = chart_id_penetrate_relation[single_penetrate['chart_id']]
            single_penetrate['penetrate_filter_relation'] = chart_id_penetrate_filter_relation[
                single_penetrate['chart_id']
            ]
        return penetrate_chart_list
