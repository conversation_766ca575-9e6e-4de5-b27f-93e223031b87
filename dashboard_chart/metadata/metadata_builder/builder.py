#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
元数据构造类
"""

# ---------------- 标准模块 ----------------
from copy import deepcopy

# ---------------- 业务模块 ----------------
from base.models import BaseModel
from dmplib.utils.errors import UserError
from .storage import MetadataStorage
from .generators.generator import MetadataBaseGenerator
from .generators.dashboard_generator import DashboardGenerator
from .generators.screens_generator import ScreensGenerator
from .generators.installed_component_generator import InstalledComponentGenerator
from .generators.dashboard_filter_generator import DashboardFilterGenerator
from .generators.penetrate_generator import PenetrateGenerator
from .generators.linkage_generator import LinkageGenerator
from .generators.redirect_generator import RedirectGenerator
from .generators.component_filter_generator import ComponentFilterGenerator
from .generators.dashboard_chart_generator import DashboardChartGenerator
from .generators.dashboard_filter_chart_default_value_generator import DashboardFilterChartDefaultValueGenerator
from .generators.var_relation_generator import VarRelationGenerator
from dashboard_chart.services import metadata_service


class MetadataBuilder(BaseModel):
    """
    元数据构造器
    """

    def __init__(self, dashboard_id):
        self.query_dashboard_id = dashboard_id
        self.main_dashboard_storage = None
        self.first_report_storage = None
        self.__initialize_storage()
        super().__init__()

    def __initialize_storage(self):
        """
        初始化storage对象
        :return:
        """
        orig_storage = MetadataStorage(dashboard_id=self.query_dashboard_id)
        dashboard = orig_storage.get_dashboard_info()
        if not dashboard:
            raise UserError(message="获取报告数据失败")
        # 对多屏数组进行筛选，排序
        screen_dashboard = orig_storage.filter_screens_table_data(
            orig_storage.get_specific_table_data("screen_dashboard")
        )

        # 多屏报告的情况
        if dashboard.get("is_multiple_screen") == 1 and len(screen_dashboard):
            screen_info = screen_dashboard[0]
            first_dashboard_id = screen_info.get("screen_id")
            self.first_report_storage = MetadataStorage(dashboard_id=first_dashboard_id)
        # 单屏报告的情况
        elif dashboard.get("is_multiple_screen") == 0:
            # 复用storage对象，减少一次实例化动作
            self.first_report_storage = deepcopy(orig_storage)
        else:
            raise UserError(message="获取报告数据异常")

        # 无论是否多屏，主信息的storage都是同一个
        self.main_dashboard_storage = orig_storage

    def _create_generator(self, generator_name, op_chart_id):
        """
        创建generator实例
        :param generator_name:
        :param op_chart_id:
        :return:
        """
        class_file_name = "dashboard_chart_" + generator_name + "_generator"
        class_name = ""
        name_arr = class_file_name.split("_")
        for name in name_arr:
            class_name += name.capitalize()
        module_obj = __import__(
            "dashboard_chart.metadata.metadata_builder.generators." + class_file_name, fromlist=[class_name]
        )
        generator_class = getattr(module_obj, class_name)
        return generator_class(self.first_report_storage, op_chart_id)

    def _build_dashboard(self):
        """
        构造dashboard
        :return:
        """
        return DashboardGenerator(storage=self.main_dashboard_storage).build()

    def _build_screens(self):
        """
        构造screens
        :return:
        """
        return ScreensGenerator(storage=self.main_dashboard_storage).build()

    def _build_chart_indicator(self, op_chart_id):
        """
        构造chart内的indicator
        :param op_chart_id:
        :return:
        """
        indicator_meta = {}
        # 注册的单图组成部分数据
        regist_indicator_generators = [
            "dims",
            "nums",
            "filters",
            "zaxis",
            "comparisons",
            "filters",
            "desires",
            "marklines",
            "field_sorts",
            "chart_params",
            "chart_vars",
        ]
        for indicator_name in regist_indicator_generators:
            generator_name = indicator_name[:-1]
            if indicator_name in ["zaxis"]:
                generator_name = indicator_name
            elif indicator_name in ["chart_params", "chart_vars"]:
                generator_name = (indicator_name.split("_")[-1])[:-1]
            generator_instance = self._create_generator(generator_name, op_chart_id)
            if isinstance(generator_instance, MetadataBaseGenerator):
                indicator_meta.update({indicator_name: generator_instance.build()})
        return indicator_meta

    def _build_installed_component(self):
        """
        构造installed_component
        :return:
        """
        return InstalledComponentGenerator(storage=self.first_report_storage).build()

    def _build_charts(self):
        """
        构造charts
        :return:
        """
        charts_meta = []
        dashboard_charts = self.first_report_storage.get_specific_table_data("dashboard_chart")
        for chart in dashboard_charts:
            op_chart_id = chart.get("id")
            if not op_chart_id:
                continue
            single_meta_chart = DashboardChartGenerator(self.first_report_storage, op_chart_id).build()
            # 填充chart_default_value
            single_meta_chart["data"]["chart_default_value"] = DashboardFilterChartDefaultValueGenerator(
                self.first_report_storage, op_chart_id
            ).build()
            # 填充indicator
            single_meta_chart["data"]["indicator"] = self._build_chart_indicator(op_chart_id)
            single_meta_chart['children_chart_ids'] = self._build_chart_children(op_chart_id)
            charts_meta.append(single_meta_chart)
        return charts_meta

    def _build_first_report(self):
        """
        构造first_report
        :return:
        """
        first_report = DashboardGenerator(storage=self.first_report_storage).build()
        first_report["dashboard_filters"] = DashboardFilterGenerator(self.first_report_storage).build()
        first_report["chart_relations"] = {
            "chart_filters": ComponentFilterGenerator(self.first_report_storage).build(),
            "chart_linkages": LinkageGenerator(self.first_report_storage).build(),
            "penetrates": PenetrateGenerator(self.first_report_storage).build(),
            "redirects": RedirectGenerator(self.first_report_storage).build(),
            "var_relations": VarRelationGenerator(self.first_report_storage).build(),
            "filters": [],  # 已弃用
            "linkages": [],  # 已弃用
        }
        first_report["charts"] = self._build_charts()
        return first_report

    def build_for_screen(self):
        """
        构造多屏元数据
        :return:
        """
        # 定义元数据的基础骨架
        metadata = {
            "dashboard": self._build_dashboard(),
            "first_report": self._build_first_report(),
            "screens": self._build_screens(),
            "installed_component": self._build_installed_component(),
        }
        return metadata

    def build_for_dashboard(self):
        """
        构造单个报告元数据
        :return:
        """
        return self._build_first_report()

    # @staticmethod
    def _build_chart_children(self, chart_id):
        return metadata_service.get_chart_children(chart_id, self.query_dashboard_id)
