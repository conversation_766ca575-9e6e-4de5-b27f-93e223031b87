#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""
chart schema
"""
from base.dmp_constant import SCHEMA_VERSION

# chart节点
chart_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "null"],
        "title": "The Items Schema",
        "required": ["id", "position", "funcSetup", "name", "data", "config", "chart_type", "chart_component_code"],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "position": {
                "$id": "#/items/properties/position",
                "type": ["object", "string", "null"],
                "title": "The Position Schema",
            },
            "chart_code": {
                "$id": "#/items/properties/chart_code",
                "type": ["string", "null"],
                "title": "The Chart_code Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
            "funcSetup": {
                "$id": "#/items/properties/funcSetup",
                "type": ["object", "string", "null"],
                "title": "The Funcsetup Schema",
                "required": ["refresh_rate", "display_item"],
                "properties": {
                    "refresh_rate": {
                        "$id": "#/items/properties/funcSetup/properties/refresh_rate",
                        "type": ["string", "null"],
                        "title": "The Refresh_rate Schema",
                        "default": "",
                        "examples": ["{\"isOpen\":false,\"time\":0,\"unit\":\"second\"}"],
                        "pattern": "^(.*)$",
                    },
                    "display_item": {
                        "$id": "#/items/properties/funcSetup/properties/display_item",
                        "type": ["string", "null"],
                        "title": "The Display_item Schema",
                        "default": "",
                        "examples": ["{\"top_head\":100,\"top_tail\":\"\"}"],
                        "pattern": "^(.*)$",
                    },
                },
            },
            "name": {
                "$id": "#/items/properties/name",
                "type": "string",
                "title": "The Name Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "data": {
                "$id": "#/items/properties/data",
                "type": ["object", "string", "null"],
                "title": "The Data Schema",
                "required": ["indicator", "default_value", "datasource"],
            },
            "config": {
                "$id": "#/items/properties/config",
                "type": ["string", "null"],
                "title": "The Config Schema",
                "default": "",
            },
            "chart_type": {
                "$id": "#/items/properties/chart_type",
                "type": ["string", "null"],
                "title": "The Chart_type Schema",
                "pattern": "^(.*)$",
            },
            "chart_component_code": {
                "$id": "#/items/properties/chart_component_code",
                "type": ["string", "null"],
                "title": "The Chart_component_code Schema",
                "pattern": "^(.+)$",
            },
            "sort_method": {
                "$id": "#/items/properties/sort_method",
                "type": ["string", "null"],
                "title": "The Sort_method Schema",
                "pattern": "^(.*)$",
            },
            "percentage": {
                "$id": "#/items/properties/percentage",
                "type": ["integer", "null"],
                "title": "The Percentage Schema",
                "pattern": "^(.*)$",
            },
            "column_order": {
                "$id": "#/items/properties/column_order",
                "type": ["string", "null"],
                "title": "The Column_order Schema",
                "pattern": "^(.*)$",
            },
        },
    },
}

single_chart_data_schema = {
    "definitions": {},
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "http://example.com/root.json",
    "type": ["object", "string", "null"],
    "title": "The Root Schema",
    "required": ["default_value", "datasource", "indicator"],
    "properties": {
        "default_value": {
            "$id": "#/properties/default_value",
            "type": ["string", "null"],
            "title": "The Default_value Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "datasource": {
            "$id": "#/properties/datasource",
            "type": ["object", "string", "null"],
            "title": "The Datasource Schema",
            "required": ["id"],
            "properties": {
                "type": {
                    "$id": "#/properties/datasource/properties/type",
                    "type": ["string", "null"],
                    "title": "The Type Schema",
                    "default": "",
                    "examples": ["EXCEL"],
                    "pattern": "^(.*)$",
                },
                "id": {
                    "$id": "#/properties/datasource/properties/id",
                    "type": ["string", "null"],
                    "title": "The Id Schema",
                    "default": "",
                    "examples": ["39e42e2f-0c03-b4cd-a97c-d409d2aee03c"],
                    "pattern": "^(.*)$",
                },
            },
        },
        "indicator": {
            "$id": "#/properties/indicator",
            "type": ["object", "string", "null"],
            "title": "The Indicator Schema",
            "required": ["nums", "filters", "comparisons", "zaxis", "dims", "marklines", "chart_params", "desires"],
        },
        "data_type": {
            "$id": "#/properties/data_type",
            "type": ["object", "string", "null"],
            "title": "The Data_type Schema",
            "required": ["logic_type"],
            "properties": {
                "logic_type": {
                    "$id": "#/properties/data_type/properties/logic_type",
                    "type": "string",
                    "title": "The Logic_type Schema",
                    "pattern": "^(.+)$",
                }
            },
        },
    },
}
