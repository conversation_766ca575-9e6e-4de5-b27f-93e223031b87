#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""

"""
from base.dmp_constant import SCHEMA_VERSION


# screens节点
screens_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["dashboard_id", "snapshot_id", "screen_id"],
        "properties": {
            "dashboard_id": {
                "$id": "#/items/properties/dashboard_id",
                "type": "string",
                "title": "The Dashboard_id Schema",
                "default": "",
                "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
                "pattern": "^(.*)$",
            },
            "snapshot_id": {
                "$id": "#/items/properties/snapshot_id",
                "type": "string",
                "title": "The Snapshot_id Schema",
                "default": "",
                "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
                "pattern": "^(.*)$",
            },
            "screen_id": {
                "$id": "#/items/properties/screen_id",
                "type": "string",
                "title": "The Screen_id Schema",
                "default": "",
                "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
                "pattern": "^(.*)$",
            },
        },
    },
}
