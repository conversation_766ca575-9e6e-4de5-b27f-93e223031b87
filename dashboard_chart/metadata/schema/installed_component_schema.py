#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""

"""
from base.dmp_constant import SCHEMA_VERSION


# installed_component节点
installed_component_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": [
            "indicator_rules",
            "data_logic_type_name",
            "name",
            "linkage",
            "next_version",
            "status",
            "nums_report_redirect",
            "preview_image",
            "menu_parent_id",
            "menu_level_code",
            "operation",
            "indicator_description",
            "description",
            "menu_id",
            "endpoint",
            "is_build_in",
            "penetrable",
            "icon",
            "md5version",
            "md5RunTimeversion",
            "can_linked",
            "dims_report_redirect",
            "has_desiredvalue",
            "menu_icon",
            "data_source_origin",
            "package",
            "has_zaxis",
            "chart_type",
            "data_logic_type_code",
            "version",
            "created_on",
            "menu_name",
            "sortable",
        ],
        "properties": {
            "indicator_rules": {
                "$id": "#/items/properties/indicator_rules",
                "type": ["string", "null"],
                "title": "The Indicator_rules Schema",
                "default": "",
                "examples": ["[{\"dim\": {\"min\": 1}, \"value\": {\"max\": 0, \"min\": 0}}]"],
                "pattern": "^(.*)$",
            },
            "data_logic_type_name": {
                "$id": "#/items/properties/data_logic_type_name",
                "type": ["string", "null"],
                "title": "The Data_logic_type_name Schema",
                "default": "",
                "examples": ["辅助组件"],
                "pattern": "^(.*)$",
            },
            "name": {
                "$id": "#/items/properties/name",
                "type": ["string", "null"],
                "title": "The Name Schema",
                "default": "",
                "examples": ["按钮筛选"],
                "pattern": "^(.*)$",
            },
            "linkage": {"$id": "#/items/properties/linkage", "type": "integer", "title": "The Linkage Schema"},
            "next_version": {
                "$id": "#/items/properties/next_version",
                "type": ["string", "null"],
                "title": "The Next_version Schema",
                "default": "",
                "examples": ["0.2.7"],
                "pattern": "^(.*)$",
            },
            "status": {"$id": "#/items/properties/status", "type": "integer", "title": "The Status Schema"},
            "nums_report_redirect": {
                "$id": "#/items/properties/nums_report_redirect",
                "type": "integer",
                "title": "The Nums_report_redirect Schema",
            },
            "preview_image": {
                "$id": "#/items/properties/preview_image",
                "type": ["string", "null"],
                "title": "The Preview_image Schema",
                "default": "",
                "examples": ["platform/preview/button_filter_sample.svg"],
                "pattern": "^(.*)$",
            },
            "menu_parent_id": {
                "$id": "#/items/properties/menu_parent_id",
                "type": ["string", "null"],
                "title": "The Menu_parent_id Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            "menu_level_code": {
                "$id": "#/items/properties/menu_level_code",
                "type": ["string", "null"],
                "title": "The Menu_level_code Schema",
                "default": "",
                "examples": ["0004-"],
                "pattern": "^(.*)$",
            },
            "indicator_description": {
                "$id": "#/items/properties/indicator_description",
                "type": ["string", "null"],
                "title": "The Indicator_description Schema",
                "default": "",
                "examples": ["多个维度，0个数值"],
                "pattern": "^(.*)$",
            },
            "description": {
                "$id": "#/items/properties/description",
                "type": ["string", "null"],
                "title": "The Description Schema",
                "default": "",
                "examples": ["按钮筛选"],
                "pattern": "^(.*)$",
            },
            "menu_id": {
                "$id": "#/items/properties/menu_id",
                "type": ["string", "null"],
                "title": "The Menu_id Schema",
                "default": "",
                "examples": ["39e438fe-bae3-6f9b-dd8c-2adc0ca73991"],
                "pattern": "^(.*)$",
            },
            "endpoint": {
                "$id": "#/items/properties/endpoint",
                "type": ["string", "null"],
                "title": "The Endpoint Schema",
                "default": "",
                "examples": ["https://dmp-open.mypaas.com.cn/dmp-test/component/package/"],
                "pattern": "^(.*)$",
            },
            "is_build_in": {
                "$id": "#/items/properties/is_build_in",
                "type": ["string", "null"],
                "title": "The Is_build_in Schema",
            },
            "penetrable": {"$id": "#/items/properties/penetrable", "type": "integer", "title": "The Penetrable Schema"},
            "icon": {
                "$id": "#/items/properties/icon",
                "type": ["string", "null"],
                "title": "The Icon Schema",
                "default": "",
                "examples": ["platform/icon/button_filter.svg"],
                "pattern": "^(.*)$",
            },
            "md5version": {
                "$id": "#/items/properties/md5version",
                "type": ["string", "null"],
                "title": "The Md5version Schema",
                "default": "",
                "examples": ["7f6f9d0d"],
                "pattern": "^(.*)$",
            },
            "can_linked": {"$id": "#/items/properties/can_linked", "type": "integer", "title": "The Can_linked Schema"},
            "dims_report_redirect": {
                "$id": "#/items/properties/dims_report_redirect",
                "type": "integer",
                "title": "The Dims_report_redirect Schema",
            },
            "has_desiredvalue": {
                "$id": "#/items/properties/has_desiredvalue",
                "type": "integer",
                "title": "The Has_desiredvalue Schema",
            },
            "menu_icon": {
                "$id": "#/items/properties/menu_icon",
                "type": ["string", "null"],
                "title": "The Menu_icon Schema",
                "default": "",
                "examples": ["filter.svg"],
                "pattern": "^(.*)$",
            },
            "data_source_origin": {
                "$id": "#/items/properties/data_source_origin",
                "type": ["string", "null"],
                "title": "The Data_source_origin Schema",
                "default": "",
                "examples": ["dataSet"],
                "pattern": "^(.*)$",
            },
            "package": {
                "$id": "#/items/properties/package",
                "type": ["string", "null"],
                "title": "The Package Schema",
                "default": "",
                "examples": ["button_filter"],
                "pattern": "^(.*)$",
            },
            "has_zaxis": {"$id": "#/items/properties/has_zaxis", "type": "integer", "title": "The Has_zaxis Schema"},
            "chart_type": {
                "$id": "#/items/properties/chart_type",
                "type": ["string", "null"],
                "title": "The Chart_type Schema",
                "default": "",
                "examples": ["filter"],
                "pattern": "^(.*)$",
            },
            "data_logic_type_code": {
                "$id": "#/items/properties/data_logic_type_code",
                "type": ["string", "null"],
                "title": "The Data_logic_type_code Schema",
                "default": "",
                "examples": ["assist"],
                "pattern": "^(.*)$",
            },
            "version": {
                "$id": "#/items/properties/version",
                "type": ["string", "null"],
                "title": "The Version Schema",
                "default": "",
                "examples": ["0.2.7"],
                "pattern": "^(.*)$",
            },
            "created_on": {
                "$id": "#/items/properties/created_on",
                "type": ["string", "null"],
                "title": "The Created_on Schema",
                "default": "",
                "examples": ["2018-10-17T16:03:07"],
                "pattern": "^(.*)$",
            },
            "menu_name": {
                "$id": "#/items/properties/menu_name",
                "type": ["string", "null"],
                "title": "The Menu_name Schema",
                "default": "",
                "examples": ["筛选器"],
                "pattern": "^(.*)$",
            },
            "sortable": {"$id": "#/items/properties/sortable", "type": "integer", "title": "The Sortable Schema"},
        },
    },
}
