#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""

"""
from base.dmp_constant import SCHEMA_VERSION

# chart_redirects
chart_redirects_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "required": ["chart_redirect", "chart_id"],
    "properties": {
        "chart_redirect": {
            "$id": "#/properties/chart_redirect",
            "type": "array",
            "title": "The Chart_redirect Schema",
            "items": {
                "$id": "#/properties/chart_redirect/items",
                "type": ["object", "string", "null"],
                "title": "The Items Schema",
                "required": [
                    "dashboard_name",
                    "has_token",
                    "type",
                    "target",
                    "open_way",
                    "target_type",
                    "dataset_field_id",
                    "relations",
                ],
                "properties": {
                    "dashboard_name": {
                        "$id": "#/properties/chart_redirect/items/properties/dashboard_name",
                        "type": ["string", "null"],
                        "title": "The Dashboard_name Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "has_token": {
                        "$id": "#/properties/chart_redirect/items/properties/has_token",
                        "type": "integer",
                        "title": "The Has_token Schema",
                    },
                    "type": {
                        "$id": "#/properties/chart_redirect/items/properties/type",
                        "type": "integer",
                        "title": "The Type Schema",
                    },
                    "target": {
                        "$id": "#/properties/chart_redirect/items/properties/target",
                        "type": ["string", "null"],
                        "title": "The Target Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "open_way": {
                        "$id": "#/properties/chart_redirect/items/properties/open_way",
                        "type": "integer",
                        "title": "The Open_way Schema",
                    },
                    "target_type": {
                        "$id": "#/properties/chart_redirect/items/properties/target_type",
                        "type": ["string", "null"],
                        "title": "The Target_type Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "dataset_field_id": {
                        "$id": "#/properties/chart_redirect/items/properties/dataset_field_id",
                        "type": ["string", "null"],
                        "title": "The Dataset_field_id Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "relations": {
                        "$id": "#/properties/chart_redirect/items/properties/relations",
                        "type": "array",
                        "title": "The Relations Schema",
                        "items": {
                            "$id": "#/properties/chart_redirect/items/properties/relations/items",
                            "type": ["object", "string", "null"],
                            "title": "The Items Schema",
                            "required": [
                                "relation_type",
                                "dashboard_filter_id",
                                "field_initiator_id",
                                "initiator_alias",
                            ],
                            "properties": {
                                "relation_type": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/relation_type",
                                    "type": "integer",
                                    "title": "The Relation_type Schema",
                                },
                                "dashboard_filter_id": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/dashboard_filter_id",
                                    "type": ["string", "null"],
                                    "title": "The Dashboard_filter_id Schema",
                                    "default": "",
                                    "pattern": "^(.*)$",
                                },
                                "field_initiator_id": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/field_initiator_id",
                                    "type": ["string", "null"],
                                    "title": "The Field_initiator_id Schema",
                                    "default": "",
                                    "pattern": "^(.*)$",
                                },
                                "initiator_alias": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/initiator_alias",
                                    "type": ["string", "null"],
                                    "title": "The Initiator_alias Schema",
                                    "default": "",
                                    "pattern": "^(.*)$",
                                },
                            },
                        },
                    },
                },
            },
        },
        "chart_id": {
            "$id": "#/properties/chart_id",
            "type": "string",
            "title": "The Chart_id Schema",
            "default": "",
            "pattern": "^(.+)$",
        },
    },
}

# chart_penetrates
chart_penetrates_schema = {
    "definitions": {},
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["parent_id", "chart_id"],
        "properties": {
            "parent_id": {
                "$id": "#/items/properties/parent_id",
                "type": ["string", "null"],
                "title": "The Parent_id Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
            "chart_id": {
                "$id": "#/items/properties/chart_id",
                "type": "string",
                "title": "The Chart_id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
        },
    },
}

# chart_linkages
chart_linkages_schema = {
    "definitions": {},
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["related_list", "chart_initiator_id", "dataset_id"],
        "properties": {
            "related_list": {
                "$id": "#/items/properties/related_list",
                "type": "array",
                "title": "The Related_list Schema",
                "items": {
                    "$id": "#/items/properties/related_list/items",
                    "type": ["object", "string", "null"],
                    "title": "The Items Schema",
                    "required": ["related_dataset_id", "chart_responder_id", "relations"],
                    "properties": {
                        "related_dataset_id": {
                            "$id": "#/items/properties/related_list/items/properties/related_dataset_id",
                            "type": ["string", "null"],
                            "title": "The Related_dataset_id Schema",
                            "default": "",
                        },
                        "chart_responder_id": {
                            "$id": "#/items/properties/related_list/items/properties/chart_responder_id",
                            "type": ["string", "null"],
                            "title": "The Chart_responder_id Schema",
                            "default": "",
                            "pattern": "^(.*)$",
                        },
                        "relations": {
                            "$id": "#/items/properties/related_list/items/properties/relations",
                            "type": "array",
                            "title": "The Relations Schema",
                            "items": {
                                "$id": "#/items/properties/related_list/items/properties/relations/items",
                                "type": ["object", "string", "null"],
                                "title": "The Items Schema",
                                "required": ["is_same_dataset", "field_initiator_id", "field_responder_id"],
                                "properties": {
                                    "is_same_dataset": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/is_same_dataset",
                                        "type": "integer",
                                        "title": "The Is_same_dataset Schema",
                                    },
                                    "field_initiator_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_initiator_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_initiator_id Schema",
                                        "default": "",
                                    },
                                    "field_responder_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_responder_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_responder_id Schema",
                                        "default": "",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            "chart_initiator_id": {
                "$id": "#/items/properties/chart_initiator_id",
                "type": "string",
                "title": "The Chart_initiator_id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "dataset_id": {
                "$id": "#/items/properties/dataset_id",
                "type": ["string", "null"],
                "title": "The Dataset_id Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
        },
    },
}

# chart_filters
chart_filters_schema = {
    "definitions": {},
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["related_list", "chart_initiator_id", "dataset_id"],
        "properties": {
            "related_list": {
                "$id": "#/items/properties/related_list",
                "type": "array",
                "title": "The Related_list Schema",
                "items": {
                    "$id": "#/items/properties/related_list/items",
                    "type": ["object", "string", "null"],
                    "title": "The Items Schema",
                    "required": ["related_dataset_id", "chart_responder_id", "relations"],
                    "properties": {
                        "related_dataset_id": {
                            "$id": "#/items/properties/related_list/items/properties/related_dataset_id",
                            "type": ["string", "null"],
                            "title": "The Related_dataset_id Schema",
                            "default": "",
                        },
                        "chart_responder_id": {
                            "$id": "#/items/properties/related_list/items/properties/chart_responder_id",
                            "type": "string",
                            "title": "The Chart_responder_id Schema",
                            "default": "",
                            "pattern": "^(.+)$",
                        },
                        "relations": {
                            "$id": "#/items/properties/related_list/items/properties/relations",
                            "type": "array",
                            "title": "The Relations Schema",
                            "items": {
                                "$id": "#/items/properties/related_list/items/properties/relations/items",
                                "type": ["object", "string", "null"],
                                "title": "The Items Schema",
                                "required": ["is_same_dataset", "field_initiator_id", "field_responder_id"],
                                "properties": {
                                    "is_same_dataset": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/is_same_dataset",
                                        "type": "integer",
                                        "title": "The Is_same_dataset Schema",
                                    },
                                    "field_initiator_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_initiator_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_initiator_id Schema",
                                        "default": "",
                                    },
                                    "field_responder_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_responder_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_responder_id Schema",
                                        "default": "",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            "chart_initiator_id": {
                "$id": "#/items/properties/chart_initiator_id",
                "type": ["string", "null"],
                "title": "The Chart_initiator_id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "dataset_id": {
                "$id": "#/items/properties/dataset_id",
                "type": ["string", "null"],
                "title": "The Dataset_id Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
        },
    },
}
