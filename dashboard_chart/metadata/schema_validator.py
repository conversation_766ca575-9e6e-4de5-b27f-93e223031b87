#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""
元数据json校验器

ps: 此模块的json校验实现是引用jsonchema开源库，开源库官方站点为http://json-schema.org/，
    生成schema可以参考使用https://jsonschema.net/提供的工具
"""

# ---------------- 标准模块 ----------------
import sys
import traceback
import logging
import jsonschema
from dmplib.utils.errors import UserError
from jsonschema.exceptions import ValidationError
from dashboard_chart.metadata.schema import (
    main_schema,
    dashboard_schema,
    first_report_schema,
    screens_schema,
    installed_component_schema,
    chart_schema,
    chart_relation_schema,
)


from typing import Callable

logger = logging.getLogger(__name__)


def try_except(func: Callable) -> Callable:
    """
    异常捕获装饰器
    :param func:
    :return:
    """

    def _handle(*args, **kwargs):
        try:
            res = func(*args, **kwargs)
            return res
        except ValidationError as e:
            exc_type, exc_instance, exc_traceback = sys.exc_info()
            formatted_traceback = ''.join(traceback.format_tb(exc_traceback))
            message = '报告元数据JSON校验异常:\n{0}\n{1}:\n{2}'.format(formatted_traceback, exc_type.__name__, exc_instance)
            logger.exception(msg=message)
            return False, '报告数据合法性校验不通过，错误信息:{}'.format(str(exc_instance))
        except Exception as e:
            logger.exception(msg=str(e))
            raise UserError(message=str(e))

    return _handle


class SchemaBaseValidator:
    """
    校验基类
    """

    def __init__(self):
        pass

    @staticmethod
    def _validate_data(validate_data, schema):
        """
        执行校验
        :param validate_data:
        :param schema:
        :return:
        """
        if not schema:
            return True
        return jsonschema.validate(validate_data, schema)


class MetadataValidator(SchemaBaseValidator):
    def __init__(self, **kwargs):
        self.data = kwargs.get('data', None)
        super().__init__()

    @staticmethod
    def _check_validate_switch(schema):
        """
        校验开关控制
        True表示开启校验，False则反之
        :param schema:
        :return:
        """
        validate_switch = {
            'main': True,
            'dashboard': True,
            'first_report': True,
            'chart_relations': True,
            'charts': True,
            'charts_data': True,
            'screens': True,
            'installed_component': False,
        }
        return validate_switch.get(schema, False)

    @try_except
    def validate_screens_metadata(self):
        """
        多屏元数据校验
        step1: 将元数据按一级主要节点先拆分出来
        step2: 开关控制是否校验节点json数据，按需执行校验，校验不通过会抛出exception
        ps: 如果元数据后续有字段更新或增加，需要修改对应节点的schema配置文件，否则此方法校验不会对更新后的字段做校验
        :return:
        """

        # 元数据不能为空
        if not self.data:
            return False, '报告元数据不能为空'
        if not isinstance(self.data, dict):
            return False, '报告元数据类型错误'

        # 主节点
        if self._check_validate_switch(schema='main'):
            self._validate_data(self.data, main_schema.main_schema)

        # dashboard
        if self._check_validate_switch(schema='dashboard'):
            dashboard = self.data.get('dashboard')
            self._validate_data(dashboard, dashboard_schema.dashboard_schema)

        # first_report
        first_report = dict()
        if self._check_validate_switch(schema='first_report'):
            first_report = self.data.get('first_report')
            self._validate_data(first_report, first_report_schema.first_report_schema)

        # chart_relations
        if self._check_validate_switch(schema='chart_relations'):
            chart_relations = first_report.get('chart_relations')
            chart_redirects = chart_relations.get('redirects')
            chart_penetrates = chart_relations.get('penetrates')
            chart_filters = chart_relations.get('filters')
            chart_linkages = chart_relations.get('linkages')
            self._validate_data(chart_redirects, chart_relation_schema.chart_redirects_schema)
            self._validate_data(chart_penetrates, chart_relation_schema.chart_penetrates_schema)
            self._validate_data(chart_filters, chart_relation_schema.chart_filters_schema)
            self._validate_data(chart_linkages, chart_relation_schema.chart_linkages_schema)

        # charts
        charts = list()
        if self._check_validate_switch(schema='charts'):
            charts = first_report.get('charts')
            self._validate_data(charts, chart_schema.chart_schema)

        # chart_data
        if self._check_validate_switch(schema='charts_data') and charts and isinstance(charts, list):
            for single_chart in charts:
                chart_data = single_chart.get('data')
                self._validate_data(chart_data, chart_schema.single_chart_data_schema)

        # screens
        if self._check_validate_switch(schema='screens'):
            screens = self.data.get('screens')
            self._validate_data(screens, screens_schema.screens_schema)

        # installed_component
        if self._check_validate_switch(schema='installed_component'):
            installed_component = self.data.get('installed_component')
            self._validate_data(installed_component, installed_component_schema.installed_component_schema)

        return True, ''


class DashboardMetadataValidator(SchemaBaseValidator):
    def __init__(self, **kwargs):
        self.data = kwargs.get('data', None)
        super().__init__()

    @staticmethod
    def _check_validate_switch(schema):
        """
        校验开关控制
        True表示开启校验，False则反之
        :param schema:
        :return:
        """
        validate_switch = {'first_report': True, 'chart_relations': True, 'charts': True, 'charts_data': True}
        return validate_switch.get(schema, False)

    @try_except
    def validate_metadata(self):
        """
        单个报告元数据校验
        :return:
        """
        # 元数据不能为空
        if not self.data:
            return False, '报告元数据不能为空'
        if not isinstance(self.data, dict):
            return False, '报告元数据类型错误'

        # first_report
        if self._check_validate_switch(schema='first_report'):
            self._validate_data(self.data, first_report_schema.first_report_schema)

        # chart_relations
        if self._check_validate_switch(schema='chart_relations'):
            chart_relations = self.data.get('chart_relations')
            chart_redirects = chart_relations.get('redirects')
            chart_penetrates = chart_relations.get('penetrates')
            chart_filters = chart_relations.get('filters')
            chart_linkages = chart_relations.get('linkages')
            self._validate_data(chart_redirects, chart_relation_schema.chart_redirects_schema)
            self._validate_data(chart_penetrates, chart_relation_schema.chart_penetrates_schema)
            self._validate_data(chart_filters, chart_relation_schema.chart_filters_schema)
            self._validate_data(chart_linkages, chart_relation_schema.chart_linkages_schema)

        # charts
        charts = list()
        if self._check_validate_switch(schema='charts'):
            charts = self.data.get('charts')
            self._validate_data(charts, chart_schema.chart_schema)

        # chart_data
        if self._check_validate_switch(schema='charts_data') and charts and isinstance(charts, list):
            for single_chart in charts:
                chart_data = single_chart.get('data')
                self._validate_data(chart_data, chart_schema.single_chart_data_schema)

        return True, ''


class PreviewMetadataValidator(SchemaBaseValidator):
    def __init__(self, **kwargs):
        self.data = kwargs.get('data', None)
        super().__init__()

    @staticmethod
    def _check_validate_switch(schema):
        """
        校验开关控制
        True表示开启校验，False则反之
        :param schema:
        :return:
        """
        validate_switch = {
            'main': True,
            'dashboard': True,
            'first_report': True,
            'chart_relations': True,
            'charts': True,
            'charts_data': True,
        }
        return validate_switch.get(schema, False)

    @try_except
    def validate_metadata(self):
        """
        预览元数据的校验
        step1: 将元数据按一级主要节点先拆分出来
        step2: 开关控制是否校验节点json数据，按需执行校验，校验不通过会抛出exception
        ps: 如果元数据后续有字段更新或增加，需要修改对应节点的schema配置文件，否则此方法校验不会对更新后的字段做校验
        :return:
        """

        # 元数据不能为空
        if not self.data:
            return False, '报告元数据不能为空'
        if not isinstance(self.data, dict):
            return False, '报告元数据类型错误'

        # 主节点
        if self._check_validate_switch(schema='main'):
            self._validate_data(self.data, main_schema.main_schema)

        # dashboard
        if self._check_validate_switch(schema='dashboard'):
            dashboard = self.data.get('dashboard')
            self._validate_data(dashboard, dashboard_schema.dashboard_schema)

        # first_report
        first_report = dict()
        if self._check_validate_switch(schema='first_report'):
            first_report = self.data.get('first_report')
            self._validate_data(first_report, first_report_schema.first_report_schema)

        # chart_relations
        if self._check_validate_switch(schema='chart_relations'):
            chart_relations = first_report.get('chart_relations')
            chart_redirects = chart_relations.get('redirects')
            chart_penetrates = chart_relations.get('penetrates')
            chart_filters = chart_relations.get('filters')
            chart_linkages = chart_relations.get('linkages')
            self._validate_data(chart_redirects, chart_relation_schema.chart_redirects_schema)
            self._validate_data(chart_penetrates, chart_relation_schema.chart_penetrates_schema)
            self._validate_data(chart_filters, chart_relation_schema.chart_filters_schema)
            self._validate_data(chart_linkages, chart_relation_schema.chart_linkages_schema)

        # charts
        charts = list()
        if self._check_validate_switch(schema='charts'):
            charts = first_report.get('charts')
            self._validate_data(charts, chart_schema.chart_schema)

        # chart_data
        if self._check_validate_switch(schema='charts_data') and charts and isinstance(charts, list):
            for single_chart in charts:
                chart_data = single_chart.get('data')
                self._validate_data(chart_data, chart_schema.single_chart_data_schema)

        return True, ''
