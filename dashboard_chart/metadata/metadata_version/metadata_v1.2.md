
## 概览

	元数据字段属性注释

	- 当前版本：v1.2
	- 启用时间：2019年01月07日
	- 更新说明：

## 节点

	- dashboard：当前报告ID基础数据
	- screens：当前报告（单屏/多屏）包括的屏幕ID信息
	- first_report：第一个屏幕ID对应的详细报告数据
	- installed_component：已安装组件数据

## dashboard

```
      "dashboard":{
        "id": "39e84337-f21d-335a-2f9b-6af55b8c7b83",  # 报告id
        "name": "DMP大屏报告",  # 报告名称
        "description": "",  # 报告描述
        "level_code": "", # 层级
        "cover": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39e84342-2753-adee-8d21-8319ee42d20a.jpg",  # 报告封面图
        "create_type": 0,  # 报告创建类型 0默认 1重构版本
		"new_layout_type": 'grid',  # 报告布局类型 free标准布局 grid固定布局
		"parent_id": "39e84337-f21d-335a-2f9b-6af55b8c7b83", # 父级id
		"is_show_mark_img": 1, # 是否显示水印图片

		# 报告布局
		"layout": {
	        "platform": "pc",  # 所属平台
	        "ratio": "16:9",
	        "width": 750,
	        "height": 421,
	        "lattice": 10
        },

		# 报告风格
        "styles": {
	        "theme": "tech_blue",  # 报告主题
	        "attrs":{}, # 扩展字段，暂不使用
			# 报告背景
	        "background": {
		          "show": true,
		          "color": "rgba(15,47,46,1)",
		          "image": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39e84351-1147-dfa4-6371-b2d01a02ab57.jpg",
		          "size": "stretch"
	        	}
	        "grid_padding": { // 固定布局信息
              "containerPadding": [
                10,
                10
              ],
              "chartMargin": [
                10,
                10
              ],
              "chartPadding": [
                15,
                15,
                15,
                15
              ]
            }
        	},

        "scale_mode": 0,  # 页面缩放方式
		"rank":0,

		# 报告发布信息
        "publish": {
	        "status": 1,  # 是否已发布 状态，0为关闭，1为发布
	        "type_access_released": 1,  # 查看已发布报告方式
	        "share_secret_key": "123456"  # 看板分享密钥
			"released_on": "2018-11-19 10:14:38" # 发布时间
        	}
    },
```

## screens

```
	"screens":[
        {
            "dashboard_id":"39e5ba95-1d47-d2f6-91d1-aa7bb3979cfc", # 报告ID（单屏/多屏）
            "screen_id":"39e5ba95-1d47-d2f6-91d1-aa7bb3979cfc", # 实际屏幕ID
            "snapshot_id":"39e5ba95-1d47-d2f6-91d1-aa7bb3979cfc"  # 快照ID
        }
    ]
```

## first_report

```
	"first_report": {
		"id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",  # 报告id
		"snapshot_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732", # 快照id
		# 报告发布信息
		"publish": {
			"type_access_released": 4,  # 查看已发布报告方式
			"released_on": "2018-09-10T10:59:37",  #发布时间
			"share_secret_key": "",  # 看板分享密钥
			"status": 1,  # 是否已发布 状态，0为关闭，1为发布
		},
		"data_type": 0,
		"biz_code": "f7f21ac8305e4f6b8d3dee59186eddef", 业务编码
		"name": "cghhj",  # 报告名称
		"cover": "",  # 封面
		"scale_mode": 0,  # 页面缩放方式
		"is_show_mark_img": 1, # 是否显示水印图片
		# 报告风格
		"styles": {
			"attrs": {},
			"background": {
				"size": "stretch",
				"color": "RGBA(15,24,47,1)",
				"show": true,
				"image": ""
			},
			"theme": "tech_blue"
		},
		# 报告布局
		"layout": {
			"ratio": "16:9",
			"platform": "pc",
			"lattice": 10,
			"width": 960,
			"height": 540
		},
		# 报告级筛选数组
		"dashboard_filters": [
		{
		    "id": "39e84e96-5009-1ec2-cf24-8a467010f37f",  # 筛选配置id
		    "main_dataset_id": "39e45738-b81f-8a06-d712-135939f00c7d",  # 数据集id
		    "main_dataset_field_id": "39e45738-b81e-5427-190f-4f9bd669a330",  # 数据集字段id
		    "type": "普通",  # 字段类型（'普通','普通高级','计算高级'）
			"data_type": "字符串",  # 数据类型('字符串','日期','数值','地址','枚举')
			"operator": "in",  # 操作符
			"dashboard_id": "39e8495e-d8aa-90bd-7f27-82e0c9af961a",
			"col_name": "col2",  # 字段名称
      		"format": "",  # 字段格式
			"field_group": "",
			"select_all_flag": 0,  # 是否全选 0否 1是
      		"col_value": "[\"上海区域\",\"北京区域\",\"成都区域\",\"武汉区域\",\"沈阳区域\",\"深圳区域\"]",  # 字段值
      		"operators": [{
      		    "id": "39e8495e-d8aa-90bd-7f27-82e0c9af961a",  # 筛选关系id
                "col_value": "[\"上海区域\",\"北京区域\",\"成都区域\",\"武汉区域\",\"沈阳区域\",\"深圳区域\"]",  # 字段值
                "select_all_flag": 0,  # 是否全选 0否 1是
                "operator": "in"  # 操作符
			}],
			"filter_relations": [{
                "id": "39ea61c9-cfc1-9993-1543-6bce1f2cd8bf",  # 关联id
                "dashboard_id": "39ea5096-2c04-1739-9ec9-e189704d63e3",  # 报告id
                "main_dataset_field_id": "39e9f3e7-cc78-3139-e5fe-205baba60b38",  # 主数据集字段id
                "related_dataset_field_id": "39e9fe2a-d1f9-5a94-5e92-3a165606214a",  # 被关联字段id
                "related_dataset_id": "39e8495e-d8aa-90bd-7f27-82e0c9af961a",  # 被关联数据集id
            }]
    	}],
    	# 报告变量取值来源数组
    	"var_value_sources": [
            {
                "id": "39e84e96-5009-1ec2-cf24-8a467010f37f",  # 筛选配置id
                "value_source_name": "取值来源名称",  # 取值来源名称
                "value_source": "userdefined",  # 值来源
                "value_identifier": "userid",  # 值标识符
                "relations": ["39ea618f-5b14-7850-7f2c-5d724971b529"], # 绑定变量id
            }
    	]
		# 报告内的单图关系
		"chart_relations": {
			# 联动关系
			"linkages": [
				{
		          "chart_initiator_id":"39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起单图id
		          "dataset_id": "10087",  # 发起单图关联数据集id
					# 发起单图对应的联动关系
          			"related_list":[
			            {
			            id: "39ea6155-1df9-6251-e27e-fefae780769d", # 联动id
			           	"chart_responder_id": "39e84375-e214-c",  # 响应单图id
			           	"related_dataset_id": "789",  # 响应单图关联数据集id
			             # 字段关系
						"relations": [{
			                "field_initiator_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起字段id
			                "field_responder_id": "39e45738-b81e-5427-190f-4f9bd669a330",  # 响应字段id
			                "is_same_dataset": 1  # 是否同一数据集
			              },
							{
							id: "39ea6155-1dfd-4f20-bd7f-7f199bddce9a", # 数据库主键
			                "field_initiator_id": "",  # 发起字段id
			                "field_responder_id": "",  # 响应字段id
			                "is_same_dataset": 0  # 是否同一数据集
			              }
						]
			          }
          			]
        		}
			],
			# 穿透关系
			"penetrates": [
				{
          		"parent_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 父级单图id
          		"chart_id": "39e843b5-16e6-1b4d-7778-51d42ecae1a3",  # 单图id
          		"relation": {
          		    "id": "39ea6b65-ebc8-7354-c24b-4f29ae9f3c65", # 主键
          		    "child_chart_field_id": "39e9fe2a-d1f9-5a94-5e92-3a165606214a", # 穿透到的单图的数据集字段id
                    "parent_chart_field_id": "39e9f3e7-cc78-3139-e5fe-205baba60b38", # 穿透发起单图的数据集字段id
          		},
          		"penetrate_filter_relation": [                    # 穿透筛选联动等字段关联关系
          		    {
                        "id": "39ea6b65-ebc8-7354-c24b-4f29ae9f3c65", # 主键
                        "child_chart_field_id": "39e9fe2a-d1f9-5a94-5e92-3a165606214a", # 穿透到的单图的数据集字段id
                        "parent_chart_field_id": "39e9f3e7-cc78-3139-e5fe-205baba60b38", # 穿透发起单图的数据集字段id
                    }
          		],
          		"penetrate_var_filter_relation": [                    # 变量穿透筛选关系
          		    {
                        "id": "39ea6b65-ebc8-7354-c24b-4f29ae9f3c65", # 主键
                        "child_chart_field_id": "39e9fe2a-d1f9-5a94-5e92-3a165606214a", # 关联的子级单图字段id
                        "parent_chart_var_id": "39e9f3e7-cc78-3139-e5fe-205baba60b38", # 父级单图变量id
                        "type": 2,
                    }
          		],
        		}
			],
			# 跳转关系
			"redirects": [
				{
          		"chart_id":"39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起单图
          		# 发起单图的跳转关系
				"chart_redirect":[
		            {
		              "id": "39ea61ca-a0b4-0be3-902c-d60480b15cb4", # 主键id
		              "type": 0,  # 字段类型(0 -> dim, 1 -> num)
		              "has_token": 0,  # 是否加token（0->否，1->是）
		              "target":"",  # 跳转目标(URL或者目标ID)
		              "target_type":"dashboard/url",  # 打开方式
		              "dataset_field_id" : "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 关联字段id
		              "dashboard_name": "被跳转报告",  # 目标报告名称
		              "status": 1, #是否启用 1启用，0未启用
		              # 字段关系
					  "relations": [{
		                	"relation_type": 0,  # 关系类型 0普通跳转 1参数跳转
		                	"initiator_alias": "名称",  # 发起字段别名
		                	"field_initiator_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起字段id
		                	"dashboard_filter_id": "39e790b3-4cc8-f0e2-0514-3dae23159f8a",  # 筛选配置id
		        	      	}]
		        	 	}
          			],
				# 单图标题跳转关系
				"complex_redirect":[
		            {
		              "id": "39ea61ca-a0b4-0be3-902c-d60480b15cb4", # 主键id
		              "type": 0,  # 字段类型(0 -> dim, 1 -> num 2 -> title)
		              "has_token": 0,  # 是否加token（0->否，1->是）
		              "target":"",  # 跳转目标(URL或者目标ID)
		              "target_type":"dashboard/url",  # 打开方式
		              "dashboard_name": "被跳转报告",  # 目标报告名称
		              "status": 1, #是否启用 1启用，0未启用
		              # 字段关系
					  "relations": [{
		                	"relation_type": 0,  # 关系类型 0普通跳转 1参数跳转 2变量跳转
		                	"initiator_alias": "名称",  # 发起字段别名
		                	"field_initiator_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起字段id
		                	"dashboard_filter_id": "39e790b3-4cc8-f0e2-0514-3dae23159f8a",  # 筛选配置id
		        	      	}]
		        	 	}
          			]
        		}
			],
			# 单图筛选关系
			"filters": [
				{
				"id": "39ea618f-5b14-7850-7f2c-5d724971b529", #主键
          		"chart_initiator_id":"39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起单图id
          		"dataset_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起单图关联数据集
          		# 筛选关系
				"related_list":[
			            {
			              "id": "39ea618f-5b19-83aa-26e8-b9961c56a598", # 主键
			              "chart_responder_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起单图id
			              "related_dataset_id": "39e84375-e214-c51c-361b-e1b7dbc04832",  # 关联数据集id
							# 字段关系
			              "relations": [{
			                	"field_initiator_id": "39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起字段id
			                	"field_responder_id": "39e84375-e214-c51c-361b-e1b7dbc04832",  # 响应字段id
			                	"is_same_dataset": 1  # 是否同一数据集
			              	}]
			            }
          			]
        		}
			],
            # 新编辑器筛选关系
			"chart_filters": [
                {
                    "chart_initiator_id": "1256b108-5604-11e9-be1d-9b823dde16f4",
                    "dataset_id": "39eae188-b193-4ad5-88d8-40652e35f8ed",
                    "id": "3f5e8ddf-5604-11e9-8944-3ffc89a9a8d9",
                    "field_initiator_id": "39eae189-3824-c973-112f-1ae93b2643e3",
                    "filter_type": 0,  # 筛选类型 0->直接筛选 1->间接筛选
                    "related_list": [{
                        "is_same_dataset": 1,
                        "id": "3e9b3dd6-5604-11e9-8944-3ffc89a9a8d9",
                        "field_responder_id": "39eae189-3824-c973-112f-1ae93b2643e3",
                        "chart_responder_id": "39ecef3c-19ba-dbe3-5882-9bd6825e9178",
                        "related_dataset_id": "39eae188-b193-4ad5-88d8-40652e35f8ed"
                    }],
                }
			],
			# 新编辑器联动关系
			 "chart_linkages": [
                 {
                    "chart_initiator_id": "39ecef3c-19ba-df65-8bdf-e0b5d257ec98",
                    "dataset_id": "39ea2227-5128-43b6-0987-7a1938d1648b",
                    "id": "bf0aba48-5604-11e9-ba32-791414009862",
                    "field_initiator_id": "39ea2227-d167-b70c-a62b-b8291510ff33",
                    "related_list": [{
                        "is_same_dataset": 0,
                        "id": "bc7a2454-5604-11e9-ba32-791414009862",
                        "field_responder_id": "39eae189-3824-c973-112f-1ae93b2643e3",
                        "chart_responder_id": "39ecef3c-19ba-dbe3-5882-9bd6825e9178",
                        "related_dataset_id": "39eae188-b193-4ad5-88d8-40652e35f8ed"
                    }],
                 }
             ],
			# 变量关系
			"var_relations": [
				{
                    "id": "39ea618f-5b14-7850-7f2c-5d724971b529", #配置主键ID
                    "chart_initiator_id":"39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起单图id
                    "field_initiator_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起方单图字段ID
                    "dashboard_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 报告ID
                    "var_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 变量id,
                    "dataset_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 变量数据集id
                    "initiator_type": 0,  # 0->直接关联 1->间接关联
        		},
			]
		},
		# 单图数组
		"charts": [
				{
				# 单图位置
				"position": {
					"z": 1501,
					"size_y": 270,
					"row": 90,
					"col": 20,
					"size_x": 360
				},
			"name": "分页表格-1",  # 单图名称
			"chart_component_code": "pagination_table",  # 单图组件code
			"id": "39e7d8f8-77ea-95c2-7d8f-4b94008d7b2f",  # 单图id
			"chart_type": "chart",  # 单图类型
			"export_type": "['data', 'style']",  # 单图支持的导出方式
			"refresh_rate": "{"on":false,"value":1,"unit":"H"}"  # 刷新频率
			"data_modified_on": "", # v1.1 数据更新时间
			"sort_method": "",  # v1.1 单图数据排序
			"percentage": 0,  # v1.1 百分比
			"column_order": "",  # v1.1 表头列排序json数据
			"level_code": "", # 层级
			"page_size": 10, #分页
			"enable_summary": 1,  # 是否开启小计（老）, 0: 否, 1: 是
			"enable_subtotal": 1,  # 是否开启列汇总（老）, 0: 不开启, 1: 开启
			"enable_subtotal_col": 1,  # 是否开启列小计, 0: 不开启, 1: 开启
			"enable_subtotal_col_summary": 1,  # 是否开启列汇总, 0: 不开启, 1: 开启
			"enable_subtotal_row": 1,  # 是否开启行小计, 0: 不开启, 1: 开启
			"enable_subtotal_row_summary": 1,  # 是否开启行汇总, 0: 不开启, 1: 开启
			"subtotal_row_summary_formula_mode": '',  # 行汇总计算方法
			# 单图配置数据
			"data": {
				# 单图关联数据集
				"datasource": {
					"type": "EXCEL",  # 数据集类型
					"id": "39e87db5-c5fe-7aaa-dafc-47f2709dc11a"  # 数据集id
				},
				# 筛选器间关系：满足所有条件-0， 满足任意条件-1. 默认0
				"filter_relation": 0,
				# 单图字段配置
				"indicator": {
					# 度量
					"nums": [{
						"alias_name": "本年净签约金额",  # 字段别名
						"expression": null,  # 表达式
						"col_name": "col3",  # 字段名
						"data_type": "数值",  # 数据类型('字符串','日期','数值','地址','枚举')
						"axis_type": 0,  # 轴类型 0：默认数值 1：次轴数值
						"formula_mode": "sum",  # 数值操作('','count','sum','avg','max','min','distinct')
						"alias": "本年净签约金额",  # 数值别名
						"chart_code": "",  # 图表类型
						"dataset_id": "39e87db5-c5fe-7aaa-dafc-47f2709dc11a",  # 数据集id
						"num": "39e87db5-c668-6edb-501e-7bcfed584b5b",  # 当sql数据源是直接为列名，标签数据源为指标id
						"field_group": "度量",  # 字段分组('维度','度量')
						"sort": null,  # 排序
						# 数值显示格式
						"display_format": {
							"thousand_point_separator": 1,
							"display_mode": "num",
							"fixed_decimal_places": 0,
							"unit": "无",
							"column_unit_name": ""
						},
						"visible": 1,  # 字段可见性
						"rank": 0,
						"type": "普通",# 类型('普通','普通高级','计算高级')
						"dashboard_chart_id": "39e7d8f8-77ea-95c2-7d8f-4b94008d7b2f"  # 单图id
					}],
					# 维度
					"dims": [{
						"alias_name": "区域",
						"col_name": "col1",
						"formula_mode": "",
						"dim": "39e87db5-c665-8c25-822a-8ea253ec5985",
						"data_type": "字符串",
						"field_group": "维度",
						"dataset_id": "39e87db5-c5fe-7aaa-dafc-47f2709dc11a",
						"visible": 1,
						"dashboard_chart_id": "39e7d8f8-77ea-95c2-7d8f-4b94008d7b2f",
						"sort": null,
						"content": null,
						"rank": 0,
						"alias": "区域"
						}
					],
					# zaxis
					"zaxis": [],
					# 筛选
					"filters": [
					    {
					        "alias_name": "成绩", # 别名
                            "col_name": "CG_7950876137", # 列名
                            "col_value": "10", # 过滤器的值
                            "dashboard_chart_id": "39ea6154-82c3-80df-106f-4dab63ab29d4", # 单图id
                            "dataset_field_id": "39e9f3e7-cc7a-d2c4-61c7-00c757a816b2", #数据集字段id
                            "dataset_id": "39e9f3e7-517b-8780-cd32-68c92089af16", # 数据集id
                            "id": "39ea8931-4fce-bb39-bfff-b522a0ad8aa9", # 主键
                            "operator": ">" #比较操作符
                            "operators": [{
                                "id": "39e8495e-d8aa-90bd-7f27-82e0c9af961a",  # 筛选关系id
                                "col_value": "10", # 过滤器的值
                                "operator": ">"  # 操作符
                            }],
					    }
					],
					# 参数
					"chart_params": [],
					# 对比
					"comparisons": [],
					# 辅助线
					"marklines": [
						{
							"id": "39e9894c-22d4-d088-1c53-3d7102785c7f",
							"mode": "计算值",
							"value": "",
							"num": "39e535e5-8b53-cd84-accb-81a82f68ee67",
							"name": "辅助线(1)",
							"formula_mode": "avg",
							"dataset_field_id": "39e535e5-8b53-cd84-accb-81a82f68ee67",
							"dashboard_chart_id": "39e97e67-302c-dba7-6ad8-85ff350b5941",
							"axis_type": 1
						}
					],
					# 目标值
					"desires": [],
					# 单图引用的变量
					"chart_vars": [
                        {
                            "var_id": "39e9894c-22d4-d088-1c53-3d7102785c7f",  # 变量ID
                            "field_id": "39e9894c-22d4-d088-1c53-3d7102785c7f",  # 数据集字段ID
                            "dataset_id": "39e9894c-22d4-d088-1c53-3d7102785c7f",  # 所属数据集ID
                            "name": "test",  # 变量名称
                            "var_type": 1,  # 变量类型 1:文本 2:日期 3:数值
                            "value_type": 2,  # 值类型 1: 列表 2: 任意值 3: 区间
                            "description": "desc",  # 变量描述
                        },
				    ],
				    # 字段排序
					"field_sorts": [{
						"id": "39e87db5-c665-8c25-822a-8ea253ec5985",  # 配置ID
						"dataset_field_id": "39e87db5-c665-8c25-822a-8ea253ec5985",  # 字段ID
						"field_source": "dim",  # 字段排序来源 dim,num,desire,comparison
						"sort": "DESC",  # 排序类型 DESC,ASC,CUSTOM
						"content": "",  # 自定义排序内容
						"weight": 1,  # 排序权重
						}
					],
				},
				# 数据类型
				"data_type": {
					"logic_type": "default"  # data_logic_type_code
				},
				"default_value": null  # 默认值（前端使用）
			},
			# 其他配置
			"funcSetup": {
				"refresh_rate": "{\"isOpen\":true,\"time\":11,\"unit\":\"second\"}",  # 刷新频率
				"display_item": "{\"top_head\":\"\",\"top_tail\":\"\"}"
			},
			# 配置数据（前端使用）
			"config": ""
		}]
	}
```

## installed_component

```
	"installed_component":[
        {
            "dims_report_redirect":1,
            "preview_image":"platform/preview/sample.jpg",
            "status":1,
            "penetrable":1,
            "linkage":0,
            "data_logic_type_name":"默认组件",
            "data_source_origin":"dataSet",
            "description":"内置表格组件",
            "can_linked":1,
            "icon":"platform/icon/table.svg",
            "md5version":"0a961d5b",
            "created_on":"2018-11-21 18:04:11",
            "menu_name":"辅助图形",
            "has_desiredvalue":0,
            "package":"table_warn",
            "indicator_rules":"[{"value": {"min": 1}, "dim": {"min": 0}}, {"value": {"min": 0}, "dim": {"min": 1}}]",
            "name":"表格预警",
            "is_build_in":0,
            "indicator_description":"0个或多个维度，0个或多个数值",
            "nums_report_redirect":1,
            "has_zaxis":0,
            "contain_css":1,
            "operation":null,
            "next_version":"0.4.8",
            "chart_type":"chart",
            "sortable":1,
            "menu_parent_id":"",
            "version":"0.4.8",
            "menu_level_code":"0005-",
            "data_logic_type_code":"default",
            "menu_icon":"assist.svg",
            "endpoint":"https://dmp-open.mypaas.com.cn/dmp-test/component/package/",
            "contain_mapgallery":0,
            "menu_id":"39e438fe-bae3-6f9b-dd8c-2adc0ca73992"
        }
    ]
```

