
## 概览

	元数据字段属性注释

	- 当前版本：v1.0
	- 启用时间：2018年09月30日
	- 更新说明：


## 节点	

	- dashboard：当前报告ID基础数据
	- screens：当前报告（单屏/多屏）包括的屏幕ID信息
	- first_report：第一个屏幕ID对应的详细报告数据
	- installed_component：已安装组件数据

## dashboard

      "dashboard":{
        "id": "39e84337-f21d-335a-2f9b-6af55b8c7b83",  # 报告id
        "name": "DMP大屏报告",  # 报告名称
        "description": "",  # 报告描述
        "cover": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39e84342-2753-adee-8d21-8319ee42d20a.jpg",  # 报告封面图
        
		# 报告布局
		"layout": {
	        "platform": "pc",  # 所属平台
	        "ratio": "16:9",
	        "width": 750,
	        "height": 421,
	        "lattice": 10
        },

		# 报告风格
        "styles": {
	        "theme": "tech_blue",  # 报告主题
	        "attrs":{}, # 扩展字段，暂不使用
			# 报告背景
	        "background": {
		          "show": true,
		          "color": "rgba(15,47,46,1)",
		          "image": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39e84351-1147-dfa4-6371-b2d01a02ab57.jpg",
		          "size": "stretch"
	        	}
        	},

        "scale_mode": 0,  # 页面缩放方式

		# 报告发布信息
        "publish": {
	        "status": 1,  # 是否已发布 状态，0为关闭，1为发布
	        "type_access_released": 1,  # 查看已发布报告方式
	        "share_secret_key": "123456"  # 看板分享密钥
			"released_on": "2018-11-19 10:14:38" # 发布时间
        	}
    },

## screens

	"screens":[
        {
            "dashboard_id":"39e5ba95-1d47-d2f6-91d1-aa7bb3979cfc", # 报告ID（单屏/多屏）
            "screen_id":"39e5ba95-1d47-d2f6-91d1-aa7bb3979cfc", # 实际屏幕ID
            "snapshot_id":"39e5ba95-1d47-d2f6-91d1-aa7bb3979cfc"  # 快照ID
        }
    ]


## first_report

	"first_report": {
		"id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",  # 报告id
		"snapshot_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732", # 快照id
		# 报告发布信息
		"publish": {
			"type_access_released": 4,  # 查看已发布报告方式
			"released_on": "2018-09-10T10:59:37",  #发布时间
			"share_secret_key": "",  # 看板分享密钥
			"status": 1,  # 是否已发布 状态，0为关闭，1为发布
		},
		"data_type": 0,
		"biz_code": "f7f21ac8305e4f6b8d3dee59186eddef", 业务编码
		"name": "cghhj",  # 报告名称
		"cover": "",  # 封面
		"scale_mode": 0,  # 页面缩放方式
		# 报告风格
		"styles": {
			"attrs": {},
			"background": {
				"size": "stretch",
				"color": "RGBA(15,24,47,1)",
				"show": true,
				"image": ""
			},
			"theme": "tech_blue"
		},
		# 报告布局
		"layout": {
			"ratio": "16:9",
			"platform": "pc",
			"lattice": 10,
			"width": 960,
			"height": 540
		},
		# 报告级筛选数组
		"dashboard_filters": [
		{
		    "id": "39e84e96-5009-1ec2-cf24-8a467010f37f",  # 筛选配置id
		    "main_dataset_id": "39e45738-b81f-8a06-d712-135939f00c7d",  # 数据集id
		    "main_dataset_field_id": "39e45738-b81e-5427-190f-4f9bd669a330",  # 数据集字段id
		    "type": "普通",  # 字段类型（'普通','普通高级','计算高级'）
			"data_type": "字符串",  # 数据类型('字符串','日期','数值','地址','枚举')
			"operator": "in",  # 操作符
			"dashboard_id": "39e8495e-d8aa-90bd-7f27-82e0c9af961a",
			"col_name": "col2",  # 字段名称
      		"format": "",  # 字段格式
			"field_group": "",
			"select_all_flag": 0,  # 是否全选 0否 1是
      		"col_value": "[\"上海区域\",\"北京区域\",\"成都区域\",\"武汉区域\",\"沈阳区域\",\"深圳区域\"]",  # 字段值
    	}],
		# 报告内的单图关系
		"chart_relations": {
			# 联动关系
			"linkages": [
				{
		          "chart_initiator_id":"39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起单图id
		          "dataset_id": "10087",  # 发起单图关联数据集id
					# 发起单图对应的联动关系
          			"related_list":[
			            {
			           	"chart_responder_id": "39e84375-e214-c",  # 响应单图id
			           	"related_dataset_id": "789",  # 响应单图关联数据集id
			             # 字段关系 	
						"relations": [{ 
			                "field_initiator_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起字段id
			                "field_responder_id": "39e45738-b81e-5427-190f-4f9bd669a330",  # 响应字段id
			                "is_same_dataset": 1  # 是否同一数据集
			              },
							{ 
			                "field_initiator_id": "",  # 发起字段id
			                "field_responder_id": "",  # 响应字段id
			                "is_same_dataset": 0  # 是否同一数据集
			              }
						]
			          }
          			]
        		}
			],
			# 穿透关系
			"penetrates": [
				{
          		"parent_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 父级单图id
          		"chart_id": "39e843b5-16e6-1b4d-7778-51d42ecae1a3",  # 单图id
        		}
			],
			# 跳转关系
			"redirects": [
				{
          		"chart_id":"39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起单图
          		# 发起单图的跳转关系
				"chart_redirect":[
		            {
		              "type": 0,  # 字段类型(0 -> dim, 1 -> num)
		              "has_token": 0,  # 是否加token（0->否，1->是）
		              "target":"",  # 跳转目标(URL或者目标ID)
		              "target_type":"dashboard/url",  # 打开方式
		              "dataset_field_id" : "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 关联字段id
		              "dashboard_name": "被跳转报告",  # 目标报告名称
		              # 字段关系
					  "relations": [{
		                	"relation_type": 0,  # 关系类型 0普通跳转 1参数跳转
		                	"initiator_alias": "名称",  # 发起字段别名
		                	"field_initiator_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起字段id 
		                	"dashboard_filter_id": "39e790b3-4cc8-f0e2-0514-3dae23159f8a",  # 筛选配置id
		        	      	}]
		        	 	}
          			]
        		}
			],
			# 单图筛选关系
			"filters": [
				{
          		"chart_initiator_id":"39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起单图id
          		"dataset_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起单图关联数据集
          		# 筛选关系
				"related_list":[
			            {
			              "chart_responder_id": "39e45738-b661-c2b9-bbdd-8ee366e2f399",  # 发起单图id
			              "related_dataset_id": "39e84375-e214-c51c-361b-e1b7dbc04832",  # 关联数据集id
							# 字段关系
			              "relations": [{ 
			                	"field_initiator_id": "39e84375-e214-c51c-361b-e1b7dbc04832",  # 发起字段id
			                	"field_responder_id": "39e84375-e214-c51c-361b-e1b7dbc04832",  # 响应字段id 
			                	"is_same_dataset": 1  # 是否同一数据集
			              	}]
			            }
          			]
        		}
			]
		},
		# 单图数组
		"charts": [
				{
				# 单图位置
				"position": {
					"z": 1501,
					"size_y": 270,
					"row": 90,
					"col": 20,
					"size_x": 360
				},
			"name": "分页表格-1",  # 单图名称
			"chart_component_code": "pagination_table",  # 单图组件code
			"id": "39e7d8f8-77ea-95c2-7d8f-4b94008d7b2f",  # 单图id
			"chart_type": "chart",  # 单图类型
			"refresh_rate": "{"on":false,"value":1,"unit":"H"}"  # 刷新频率
			# 单图配置数据
			"data": {
				# 单图关联数据集
				"datasource": {
					"type": "EXCEL",  # 数据集类型
					"id": "39e87db5-c5fe-7aaa-dafc-47f2709dc11a"  # 数据集id
				},
				# 单图字段配置
				"indicator": {
					# 度量
					"nums": [{
						"alias_name": "本年净签约金额",  # 字段别名
						"expression": null,  # 表达式
						"col_name": "col3",  # 字段名
						"data_type": "数值",  # 数据类型('字符串','日期','数值','地址','枚举')
						"axis_type": 0,  # 轴类型 0：默认数值 1：次轴数值
						"formula_mode": "sum",  # 数值操作('','count','sum','avg','max','min','distinct')
						"alias": "本年净签约金额",  # 数值别名
						"chart_code": "",  # 图表类型
						"dataset_id": "39e87db5-c5fe-7aaa-dafc-47f2709dc11a",  # 数据集id
						"num": "39e87db5-c668-6edb-501e-7bcfed584b5b",  # 当sql数据源是直接为列名，标签数据源为指标id
						"field_group": "度量",  # 字段分组('维度','度量')
						"sort": null,  # 排序
						# 数值显示格式
						"display_format": {
							"thousand_point_separator": 1,
							"display_mode": "num",
							"fixed_decimal_places": 0,
							"unit": "无",
							"column_unit_name": ""
						},
						"visible": 1,  # 字段可见性
						"rank": 0,
						"type": "普通",# 类型('普通','普通高级','计算高级')
						"dashboard_chart_id": "39e7d8f8-77ea-95c2-7d8f-4b94008d7b2f"  # 单图id
					}],
					# 维度
					"dims": [{
						"alias_name": "区域",
						"col_name": "col1",
						"formula_mode": "",
						"dim": "39e87db5-c665-8c25-822a-8ea253ec5985",
						"data_type": "字符串",
						"field_group": "维度",
						"dataset_id": "39e87db5-c5fe-7aaa-dafc-47f2709dc11a",
						"visible": 1,
						"dashboard_chart_id": "39e7d8f8-77ea-95c2-7d8f-4b94008d7b2f",
						"sort": null,
						"content": null,
						"rank": 0,
						"alias": "区域"
						}
					],
					# zaxis
					"zaxis": [],
					# 筛选
					"filters": [],
					# 参数
					"chart_params": [],
					# 对比
					"comparisons": [],
					# 辅助线
					"marklines": [],
					# 目标值
					"desires": []
				},
				# 数据类型
				"data_type": {
					"logic_type": "default"  # data_logic_type_code
				},
				"default_value": null  # 默认值（前端使用）
			},
			# 其他配置
			"funcSetup": {
				"refresh_rate": "{\"isOpen\":true,\"time\":11,\"unit\":\"second\"}",  # 刷新频率
				"display_item": "{\"top_head\":\"\",\"top_tail\":\"\"}"
			},
			# 配置数据（前端使用）
			"config": ""
		}]
	}

## installed_component

	"installed_component":[
        {
            "dims_report_redirect":1,
            "preview_image":"platform/preview/sample.jpg",
            "status":1,
            "penetrable":1,
            "linkage":0,
            "data_logic_type_name":"默认组件",
            "data_source_origin":"dataSet",
            "description":"内置表格组件",
            "can_linked":1,
            "icon":"platform/icon/table.svg",
            "md5version":"0a961d5b",
            "created_on":"2018-11-21 18:04:11",
            "menu_name":"辅助图形",
            "has_desiredvalue":0,
            "package":"table_warn",
            "indicator_rules":"[{"value": {"min": 1}, "dim": {"min": 0}}, {"value": {"min": 0}, "dim": {"min": 1}}]",
            "name":"表格预警",
            "is_build_in":0,
            "indicator_description":"0个或多个维度，0个或多个数值",
            "nums_report_redirect":1,
            "has_zaxis":0,
            "contain_css":1,
            "operation":null,
            "next_version":"0.4.8",
            "chart_type":"chart",
            "sortable":1,
            "menu_parent_id":"",
            "version":"0.4.8",
            "menu_level_code":"0005-",
            "data_logic_type_code":"default",
            "menu_icon":"assist.svg",
            "endpoint":"https://dmp-open.mypaas.com.cn/dmp-test/component/package/",
            "contain_mapgallery":0,
            "menu_id":"39e438fe-bae3-6f9b-dd8c-2adc0ca73992"
        }
    ]


## jsonschema校验器

	用于校验元数据内数据有效性

## dashboard

	{
    "definitions": {},
    "$schema": "",
    "$id": "http://example.com/root.json",
    "type": ["object", "string", "null"],
    "title": "dashboard_schema",
    "required": [
        "cover",
        "layout",
        "publish",
        "name",
        "id",
        "level_code",
        "type",
        "refresh_rate",
        "biz_code",
        "data_type",
        "is_multiple_screen",
        "snapshot_id",
        "scale_mode",
        "styles",
    ],
    "properties": {
        "cover": {
            "$id": "#/properties/cover",
            "type": ["string", "null"],
            "title": "The Cover Schema",
            "default": "",
        },
        "layout": {
            "$id": "#/properties/layout",
            "type": ["object", "string", "null"],
            "title": "The Layout Schema",
        },
        "publish": {
            "$id": "#/properties/publish",
            "type": ["object", "string", "null"],
            "title": "The Publish Schema",
            "required": ["share_secret_key", "status", "type_access_released", "released_on"],
            "properties": {
                "share_secret_key": {
                    "$id": "#/properties/publish/properties/share_secret_key",
                    "type": ["string", "null"],
                    "title": "The Share_secret_key Schema",
                    "default": "",
                    "pattern": "^(.*)$",
                },
                "status": {
                    "$id": "#/properties/publish/properties/status",
                    "type": "integer",
                    "title": "The Status Schema",
                },
                "type_access_released": {
                    "$id": "#/properties/publish/properties/type_access_released",
                    "type": "integer",
                    "title": "The Type_access_released Schema",
                },
                "released_on": {
                    "$id": "#/properties/publish/properties/released_on",
                    "type": ["string", "null"],
                    "title": "The Released_on Schema",
                    "default": "",
                    "examples": ["2018-10-16T10:06:25"],
                    "pattern": "^(.*)$",
                },
            },
        },
        "name": {
            "$id": "#/properties/name",
            "type": ["string", "null"],
            "title": "The Name Schema",
            "pattern": "^(.+)$",
        },
        "id": {
            "$id": "#/properties/id",
            "type": "string",
            "title": "The Id Schema",
            "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
            "pattern": "^(.+)$",
        },
        "level_code": {
            "$id": "#/properties/level_code",
            "type": ["string", "null"],
            "title": "The Level_code Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "type": {
            "$id": "#/properties/type",
            "type": ["string", "null"],
            "title": "The Type Schema",
            "default": "",
            "examples": ["FILE"],
            "pattern": "^(.*)$",
        },
        "refresh_rate": {
            "$id": "#/properties/refresh_rate",
            "type": ["string", "null"],
            "title": "The Refresh_rate Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "biz_code": {
            "$id": "#/properties/biz_code",
            "type": ["string", "null"],
            "title": "The Biz_code Schema",
            "default": "",
            "examples": ["a6dcdd0f75514cf38dcaf5e13d827a89"],
            "pattern": "^(.*)$",
        },
        "data_type": {"$id": "#/properties/data_type", "type": "integer", "title": "The Data_type Schema"},
        "is_multiple_screen": {
            "$id": "#/properties/is_multiple_screen",
            "type": "integer",
            "title": "The Is_multiple_screen Schema",
            "default": 0,
        },
        "snapshot_id": {
            "$id": "#/properties/snapshot_id",
            "type": ["string", "null"],
            "title": "The Snapshot_id Schema",
            "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
            "pattern": "^(.*)$",
        },
        "scale_mode": {
            "$id": "#/properties/scale_mode",
            "type": "integer",
            "title": "The Scale_mode Schema",
            "default": 0,
        },
        "styles": {
            "$id": "#/properties/styles",
            "type": ["object", "string", "null"],
            "title": "The Styles Schema",
            "required": ["attrs", "theme"],
            "properties": {
                "background": {
                    "$id": "#/properties/styles/properties/background",
                    "type": ["object", "string", "null"],
                    "title": "The Background Schema",
                    "required": ["image", "size", "color"],
                    "properties": {
                        "image": {
                            "$id": "#/properties/styles/properties/background/properties/image",
                            "type": ["string", "null"],
                            "title": "The Image Schema",
                            "default": "",
                            "pattern": "^(.*)$",
                        },
                        "size": {
                            "$id": "#/properties/styles/properties/background/properties/size",
                            "type": ["string", "null"],
                            "title": "The Size Schema",
                            "default": "",
                            "examples": ["stretch"],
                            "pattern": "^(.*)$",
                        },
                        "color": {
                            "$id": "#/properties/styles/properties/background/properties/color",
                            "type": ["string", "null"],
                            "title": "The Color Schema",
                            "default": "",
                            "examples": ["RGBA(15,24,47,1)"],
                            "pattern": "^(.*)$",
                        },
                    },
                },
                "attrs": {
                    "$id": "#/properties/styles/properties/attrs",
                    "type": ["object", "string", "null"],
                    "title": "The Attrs Schema",
                },
                "theme": {
                    "$id": "#/properties/styles/properties/theme",
                    "type": ["string", "null"],
                    "title": "The Theme Schema",
                    "default": "",
                    "examples": ["tech_blue"],
                    "pattern": "^(.*)$",
                },
            },
        },
    },
	}


## screens

	{
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["dashboard_id", "snapshot_id", "screen_id"],
        "properties": {
            "dashboard_id": {
                "$id": "#/items/properties/dashboard_id",
                "type": "string",
                "title": "The Dashboard_id Schema",
                "default": "",
                "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
                "pattern": "^(.*)$",
            },
            "snapshot_id": {
                "$id": "#/items/properties/snapshot_id",
                "type": "string",
                "title": "The Snapshot_id Schema",
                "default": "",
                "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
                "pattern": "^(.*)$",
            },
            "screen_id": {
                "$id": "#/items/properties/screen_id",
                "type": "string",
                "title": "The Screen_id Schema",
                "default": "",
                "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
                "pattern": "^(.*)$",
            },
        },
    },
	}

## first_report

	{
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": ["object", "string", "null"],
    "title": "The Root Schema",
    "required": [
        "cover",
        "layout",
        "publish",
        "chart_relations",
        "name",
        "dashboard_filters",
        "id",
        "level_code",
        "type",
        "refresh_rate",
        "charts",
        "biz_code",
        "data_type",
        "is_multiple_screen",
        "snapshot_id",
        "scale_mode",
        "styles",
    ],
    "properties": {
        "cover": {
            "$id": "#/properties/cover",
            "type": ["string", "null"],
            "title": "The Cover Schema",
            "default": "",
        },
        "layout": {
            "$id": "#/properties/layout",
            "type": ["object", "string", "null"],
            "title": "The Layout Schema",
        },
        "publish": {
            "$id": "#/properties/publish",
            "type": ["object", "string", "null"],
            "title": "The Publish Schema",
            "required": ["share_secret_key", "status", "type_access_released", "released_on"],
            "properties": {
                "share_secret_key": {
                    "$id": "#/properties/publish/properties/share_secret_key",
                    "type": ["string", "null"],
                    "title": "The Share_secret_key Schema",
                    "default": "",
                    "pattern": "^(.*)$",
                },
                "status": {
                    "$id": "#/properties/publish/properties/status",
                    "type": "integer",
                    "title": "The Status Schema",
                },
                "type_access_released": {
                    "$id": "#/properties/publish/properties/type_access_released",
                    "type": "integer",
                    "title": "The Type_access_released Schema",
                },
                "released_on": {
                    "$id": "#/properties/publish/properties/released_on",
                    "type": ["string", "null"],
                    "title": "The Released_on Schema",
                    "default": "",
                    "examples": ["2018-10-16T10:06:25"],
                    "pattern": "^(.*)$",
                },
            },
        },
        "name": {
            "$id": "#/properties/name",
            "type": "string",
            "title": "The Name Schema",
            "default": "",
            "pattern": "^(.+)$",
        },
        "dashboard_filters": {
            "$id": "#/properties/dashboard_filters",
            "type": "array",
            "title": "The Dashboard_filters Schema",
        },
        "id": {
            "$id": "#/properties/id",
            "type": "string",
            "title": "The Id Schema",
            "default": "",
            "pattern": "^(.+)$",
        },
        "level_code": {
            "$id": "#/properties/level_code",
            "type": ["string", "null"],
            "title": "The Level_code Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "type": {
            "$id": "#/properties/type",
            "type": ["string", "null"],
            "title": "The Type Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "refresh_rate": {
            "$id": "#/properties/refresh_rate",
            "type": ["string", "null"],
            "title": "The Refresh_rate Schema",
            "default": "",
        },
        "charts": {
            "$id": "#/properties/charts",
            "type": "array",
            "title": "The Charts Schema",
        },
        "biz_code": {
            "$id": "#/properties/biz_code",
            "type": ["string", "null"],
            "title": "The Biz_code Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "data_type": {
            "$id": "#/properties/data_type",
            "type": "integer",
            "title": "The Data_type Schema",
        },
        "is_multiple_screen": {
            "$id": "#/properties/is_multiple_screen",
            "type": "integer",
            "title": "The Is_multiple_screen Schema",
        },
        "snapshot_id": {
            "$id": "#/properties/snapshot_id",
            "type": ["string", "null"],
            "title": "The Snapshot_id Schema",
            "default": "",
            "examples": ["39e98ccb-ffb2-081d-f626-0b32c67a9faa"],
            "pattern": "^(.*)$",
        },
        "scale_mode": {
            "$id": "#/properties/scale_mode",
            "type": "integer",
            "title": "The Scale_mode Schema",
        },
        "styles": {
            "$id": "#/properties/styles",
            "type": ["object", "string", "null"],
            "title": "The Styles Schema",
            "required": ["background", "attrs", "theme"],
            "properties": {
                "background": {
                    "$id": "#/properties/styles/properties/background",
                    "type": ["object", "string", "null"],
                    "title": "The Background Schema",
                    "required": ["image", "size", "color"],
                    "properties": {
                        "image": {
                            "$id": "#/properties/styles/properties/background/properties/image",
                            "type": ["string", "null"],
                            "title": "The Image Schema",
                            "default": "",
                            "pattern": "^(.*)$",
                        },
                        "size": {
                            "$id": "#/properties/styles/properties/background/properties/size",
                            "type": ["string", "null"],
                            "title": "The Size Schema",
                            "default": "",
                            "examples": ["stretch"],
                            "pattern": "^(.*)$",
                        },
                        "color": {
                            "$id": "#/properties/styles/properties/background/properties/color",
                            "type": ["string", "null"],
                            "title": "The Color Schema",
                            "default": "",
                            "examples": ["RGBA(15,24,47,1)"],
                            "pattern": "^(.*)$",
                        },
                    },
                },
                "attrs": {
                    "$id": "#/properties/styles/properties/attrs",
                    "type": ["object", "string", "null"],
                    "title": "The Attrs Schema",
                },
                "theme": {
                    "$id": "#/properties/styles/properties/theme",
                    "type": ["string", "null"],
                    "title": "The Theme Schema",
                    "default": "",
                    "examples": ["tech_blue"],
                    "pattern": "^(.*)$",
                },
            },
        },
        "chart_relations": {
            "$id": "#/properties/chart_relations",
            "type": ["object", "string", "null"],
            "title": "The Chart_relations Schema",
            "required": ["linkages", "filters", "penetrates", "redirects"],
            "properties": {
                "linkages": {
                    "$id": "#/properties/chart_relations/properties/linkages",
                    "type": "array",
                    "title": "The Linkages Schema",
                },
                "filters": {
                    "$id": "#/properties/chart_relations/properties/filters",
                    "type": "array",
                    "title": "The Filters Schema",
                },
                "penetrates": {
                    "$id": "#/properties/chart_relations/properties/penetrates",
                    "type": "array",
                    "title": "The Penetrates Schema",
                },
                "redirects": {
                    "$id": "#/properties/chart_relations/properties/redirects",
                    "type": "array",
                    "title": "The Redirects Schema",
                },
            },
        },
    },
	}


## chart_schema

	{
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "null"],
        "title": "The Items Schema",
        "required": [
            "id",
            "position",
            "chart_code",
            "funcSetup",
            "name",
            "data",
            "config",
            "chart_type",
            "chart_component_code",
        ],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "position": {
                "$id": "#/items/properties/position",
                "type": ["object", "string", "null"],
                "title": "The Position Schema",
            },
            "chart_code": {
                "$id": "#/items/properties/chart_code",
                "type": ["string", "null"],
                "title": "The Chart_code Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
            "funcSetup": {
                "$id": "#/items/properties/funcSetup",
                "type": ["object", "string", "null"],
                "title": "The Funcsetup Schema",
                "required": ["refresh_rate", "display_item"],
                "properties": {
                    "refresh_rate": {
                        "$id": "#/items/properties/funcSetup/properties/refresh_rate",
                        "type": ["string", "null"],
                        "title": "The Refresh_rate Schema",
                        "default": "",
                        "examples": ["{\"isOpen\":false,\"time\":0,\"unit\":\"second\"}"],
                        "pattern": "^(.*)$",
                    },
                    "display_item": {
                        "$id": "#/items/properties/funcSetup/properties/display_item",
                        "type": ["string", "null"],
                        "title": "The Display_item Schema",
                        "default": "",
                        "examples": ["{\"top_head\":100,\"top_tail\":\"\"}"],
                        "pattern": "^(.*)$",
                    },
                },
            },
            "name": {
                "$id": "#/items/properties/name",
                "type": "string",
                "title": "The Name Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "data": {
                "$id": "#/items/properties/data",
                "type": ["object", "string", "null"],
                "title": "The Data Schema",
                "required": ["indicator", "data_type", "default_value", "datasource"],
            },
            "config": {
                "$id": "#/items/properties/config",
                "type": ["string", "null"],
                "title": "The Config Schema",
                "default": "",
            },
            "chart_type": {
                "$id": "#/items/properties/chart_type",
                "type": ["string", "null"],
                "title": "The Chart_type Schema",
                "pattern": "^(.*)$",
            },
            "chart_component_code": {
                "$id": "#/items/properties/chart_component_code",
                "type": ["string", "null"],
                "title": "The Chart_component_code Schema",
                "pattern": "^(.+)$",
            },
        },
    },
	}

##	single_chart_data_schema

	{
	"definitions": {},
	"$schema": "http://json-schema.org/draft-07/schema#",
	"$id": "http://example.com/root.json",
	"type": ["object", "string", "null"],
	"title": "The Root Schema",
	"required": ["default_value", "datasource", "indicator", "data_type"],
	"properties": {
		"default_value": {
			"$id": "#/properties/default_value",
			"type": ["string", "null"],
			"title": "The Default_value Schema",
			"default": "",
			"pattern": "^(.*)$",
		},
		"datasource": {
			"$id": "#/properties/datasource",
			"type": ["object", "string", "null"],
			"title": "The Datasource Schema",
			"required": ["type", "id"],
			"properties": {
				"type": {
					"$id": "#/properties/datasource/properties/type",
					"type": ["string", "null"],
					"title": "The Type Schema",
					"default": "",
					"examples": ["EXCEL"],
					"pattern": "^(.*)$",
				},
				"id": {
					"$id": "#/properties/datasource/properties/id",
					"type": ["string", "null"],
					"title": "The Id Schema",
					"default": "",
					"examples": ["39e42e2f-0c03-b4cd-a97c-d409d2aee03c"],
					"pattern": "^(.*)$",
				},
			},
		},
		"indicator": {
			"$id": "#/properties/indicator",
			"type": ["object", "string", "null"],
			"title": "The Indicator Schema",
			"required": ["nums", "filters", "comparisons", "zaxis", "dims", "marklines", "chart_params", "desires"],
		},
		"data_type": {
			"$id": "#/properties/data_type",
			"type": ["object", "string", "null"],
			"title": "The Data_type Schema",
			"required": ["logic_type"],
			"properties": {
				"logic_type": {
					"$id": "#/properties/data_type/properties/logic_type",
					"type": "string",
					"title": "The Logic_type Schema",
					"pattern": "^(.+)$",
				}
			},
		},
	},
	}

## chart_redirects

	{
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "required": ["chart_redirect", "chart_id"],
    "properties": {
        "chart_redirect": {
            "$id": "#/properties/chart_redirect",
            "type": "array",
            "title": "The Chart_redirect Schema",
            "items": {
                "$id": "#/properties/chart_redirect/items",
                "type": ["object", "string", "null"],
                "title": "The Items Schema",
                "required": [
                    "dashboard_name",
                    "has_token",
                    "type",
                    "target",
                    "open_way",
                    "target_type",
                    "dataset_field_id",
                    "relations",
                ],
                "properties": {
                    "dashboard_name": {
                        "$id": "#/properties/chart_redirect/items/properties/dashboard_name",
                        "type": ["string", "null"],
                        "title": "The Dashboard_name Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "has_token": {
                        "$id": "#/properties/chart_redirect/items/properties/has_token",
                        "type": "integer",
                        "title": "The Has_token Schema",
                    },
                    "type": {
                        "$id": "#/properties/chart_redirect/items/properties/type",
                        "type": "integer",
                        "title": "The Type Schema",
                    },
                    "target": {
                        "$id": "#/properties/chart_redirect/items/properties/target",
                        "type": ["string", "null"],
                        "title": "The Target Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "open_way": {
                        "$id": "#/properties/chart_redirect/items/properties/open_way",
                        "type": "integer",
                        "title": "The Open_way Schema",
                    },
                    "target_type": {
                        "$id": "#/properties/chart_redirect/items/properties/target_type",
                        "type": ["string", "null"],
                        "title": "The Target_type Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "dataset_field_id": {
                        "$id": "#/properties/chart_redirect/items/properties/dataset_field_id",
                        "type": ["string", "null"],
                        "title": "The Dataset_field_id Schema",
                        "default": "",
                        "pattern": "^(.*)$",
                    },
                    "relations": {
                        "$id": "#/properties/chart_redirect/items/properties/relations",
                        "type": "array",
                        "title": "The Relations Schema",
                        "items": {
                            "$id": "#/properties/chart_redirect/items/properties/relations/items",
                            "type": ["object", "string", "null"],
                            "title": "The Items Schema",
                            "required": [
                                "relation_type",
                                "dashboard_filter_id",
                                "field_initiator_id",
                                "initiator_alias",
                            ],
                            "properties": {
                                "relation_type": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/relation_type",
                                    "type": "integer",
                                    "title": "The Relation_type Schema",
                                },
                                "dashboard_filter_id": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/dashboard_filter_id",
                                    "type": ["string", "null"],
                                    "title": "The Dashboard_filter_id Schema",
                                    "default": "",
                                    "pattern": "^(.*)$",
                                },
                                "field_initiator_id": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/field_initiator_id",
                                    "type": ["string", "null"],
                                    "title": "The Field_initiator_id Schema",
                                    "default": "",
                                    "pattern": "^(.*)$",
                                },
                                "initiator_alias": {
                                    "$id": "#/properties/chart_redirect/items/properties/relations/items/properties/initiator_alias",
                                    "type": ["string", "null"],
                                    "title": "The Initiator_alias Schema",
                                    "default": "",
                                    "pattern": "^(.*)$",
                                },
                            },
                        },
                    },
                },
            },
        },
        "chart_id": {
            "$id": "#/properties/chart_id",
            "type": "string",
            "title": "The Chart_id Schema",
            "default": "",
            "pattern": "^(.+)$",
        },
    },
	}

## chart_penetrates

	{
    "definitions": {},
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["parent_id", "chart_id"],
        "properties": {
            "parent_id": {
                "$id": "#/items/properties/parent_id",
                "type": ["string", "null"],
                "title": "The Parent_id Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
            "chart_id": {
                "$id": "#/items/properties/chart_id",
                "type": "string",
                "title": "The Chart_id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
        },
    },
	}

## chart_linkages

	{
    "definitions": {},
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["related_list", "chart_initiator_id", "dataset_id"],
        "properties": {
            "related_list": {
                "$id": "#/items/properties/related_list",
                "type": "array",
                "title": "The Related_list Schema",
                "items": {
                    "$id": "#/items/properties/related_list/items",
                    "type": ["object", "string", "null"],
                    "title": "The Items Schema",
                    "required": ["related_dataset_id", "chart_responder_id", "relations"],
                    "properties": {
                        "related_dataset_id": {
                            "$id": "#/items/properties/related_list/items/properties/related_dataset_id",
                            "type": ["string", "null"],
                            "title": "The Related_dataset_id Schema",
                            "default": "",
                        },
                        "chart_responder_id": {
                            "$id": "#/items/properties/related_list/items/properties/chart_responder_id",
                            "type": ["string", "null"],
                            "title": "The Chart_responder_id Schema",
                            "default": "",
                            "pattern": "^(.*)$",
                        },
                        "relations": {
                            "$id": "#/items/properties/related_list/items/properties/relations",
                            "type": "array",
                            "title": "The Relations Schema",
                            "items": {
                                "$id": "#/items/properties/related_list/items/properties/relations/items",
                                "type": ["object", "string", "null"],
                                "title": "The Items Schema",
                                "required": ["is_same_dataset", "field_initiator_id", "field_responder_id"],
                                "properties": {
                                    "is_same_dataset": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/is_same_dataset",
                                        "type": "integer",
                                        "title": "The Is_same_dataset Schema",
                                    },
                                    "field_initiator_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_initiator_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_initiator_id Schema",
                                        "default": "",
                                    },
                                    "field_responder_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_responder_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_responder_id Schema",
                                        "default": "",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            "chart_initiator_id": {
                "$id": "#/items/properties/chart_initiator_id",
                "type": "string",
                "title": "The Chart_initiator_id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "dataset_id": {
                "$id": "#/items/properties/dataset_id",
                "type": ["string", "null"],
                "title": "The Dataset_id Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
        },
    },
	}

## chart_filters

	{
    "definitions": {},
    "$schema": "http://json-schema.org/draft-07/schema#",
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": ["related_list", "chart_initiator_id", "dataset_id"],
        "properties": {
            "related_list": {
                "$id": "#/items/properties/related_list",
                "type": "array",
                "title": "The Related_list Schema",
                "items": {
                    "$id": "#/items/properties/related_list/items",
                    "type": ["object", "string", "null"],
                    "title": "The Items Schema",
                    "required": ["related_dataset_id", "chart_responder_id", "relations"],
                    "properties": {
                        "related_dataset_id": {
                            "$id": "#/items/properties/related_list/items/properties/related_dataset_id",
                            "type": ["string", "null"],
                            "title": "The Related_dataset_id Schema",
                            "default": "",
                        },
                        "chart_responder_id": {
                            "$id": "#/items/properties/related_list/items/properties/chart_responder_id",
                            "type": "string",
                            "title": "The Chart_responder_id Schema",
                            "default": "",
                            "pattern": "^(.+)$",
                        },
                        "relations": {
                            "$id": "#/items/properties/related_list/items/properties/relations",
                            "type": "array",
                            "title": "The Relations Schema",
                            "items": {
                                "$id": "#/items/properties/related_list/items/properties/relations/items",
                                "type": ["object", "string", "null"],
                                "title": "The Items Schema",
                                "required": ["is_same_dataset", "field_initiator_id", "field_responder_id"],
                                "properties": {
                                    "is_same_dataset": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/is_same_dataset",
                                        "type": "integer",
                                        "title": "The Is_same_dataset Schema",
                                    },
                                    "field_initiator_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_initiator_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_initiator_id Schema",
                                        "default": "",
                                    },
                                    "field_responder_id": {
                                        "$id": "#/items/properties/related_list/items/properties/relations/items/properties/field_responder_id",
                                        "type": ["string", "null"],
                                        "title": "The Field_responder_id Schema",
                                        "default": "",
                                    },
                                },
                            },
                        },
                    },
                },
            },
            "chart_initiator_id": {
                "$id": "#/items/properties/chart_initiator_id",
                "type": ["string", "null"],
                "title": "The Chart_initiator_id Schema",
                "default": "",
                "pattern": "^(.+)$",
            },
            "dataset_id": {
                "$id": "#/items/properties/dataset_id",
                "type": ["string", "null"],
                "title": "The Dataset_id Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
        },
    },
	}


## installed_component

	{
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": ["object", "string", "null"],
        "title": "The Items Schema",
        "required": [
            "indicator_rules",
            "data_logic_type_name",
            "name",
            "linkage",
            "next_version",
            "status",
            "nums_report_redirect",
            "preview_image",
            "menu_parent_id",
            "menu_level_code",
            "operation",
            "indicator_description",
            "description",
            "menu_id",
            "endpoint",
            "is_build_in",
            "penetrable",
            "icon",
            "md5version",
            "can_linked",
            "dims_report_redirect",
            "has_desiredvalue",
            "menu_icon",
            "data_source_origin",
            "package",
            "has_zaxis",
            "chart_type",
            "data_logic_type_code",
            "version",
            "created_on",
            "menu_name",
            "sortable",
        ],
        "properties": {
            "indicator_rules": {
                "$id": "#/items/properties/indicator_rules",
                "type": ["string", "null"],
                "title": "The Indicator_rules Schema",
                "default": "",
                "examples": ["[{\"dim\": {\"min\": 1}, \"value\": {\"max\": 0, \"min\": 0}}]"],
                "pattern": "^(.*)$",
            },
            "data_logic_type_name": {
                "$id": "#/items/properties/data_logic_type_name",
                "type": ["string", "null"],
                "title": "The Data_logic_type_name Schema",
                "default": "",
                "examples": ["辅助组件"],
                "pattern": "^(.*)$",
            },
            "name": {
                "$id": "#/items/properties/name",
                "type": ["string", "null"],
                "title": "The Name Schema",
                "default": "",
                "examples": ["按钮筛选"],
                "pattern": "^(.*)$",
            },
            "linkage": {"$id": "#/items/properties/linkage", "type": "integer", "title": "The Linkage Schema"},
            "next_version": {
                "$id": "#/items/properties/next_version",
                "type": ["string", "null"],
                "title": "The Next_version Schema",
                "default": "",
                "examples": ["0.2.7"],
                "pattern": "^(.*)$",
            },
            "status": {"$id": "#/items/properties/status", "type": "integer", "title": "The Status Schema"},
            "nums_report_redirect": {
                "$id": "#/items/properties/nums_report_redirect",
                "type": "integer",
                "title": "The Nums_report_redirect Schema",
            },
            "preview_image": {
                "$id": "#/items/properties/preview_image",
                "type": ["string", "null"],
                "title": "The Preview_image Schema",
                "default": "",
                "examples": ["platform/preview/button_filter_sample.svg"],
                "pattern": "^(.*)$",
            },
            "menu_parent_id": {
                "$id": "#/items/properties/menu_parent_id",
                "type": ["string", "null"],
                "title": "The Menu_parent_id Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            "menu_level_code": {
                "$id": "#/items/properties/menu_level_code",
                "type": ["string", "null"],
                "title": "The Menu_level_code Schema",
                "default": "",
                "examples": ["0004-"],
                "pattern": "^(.*)$",
            },
            "indicator_description": {
                "$id": "#/items/properties/indicator_description",
                "type": ["string", "null"],
                "title": "The Indicator_description Schema",
                "default": "",
                "examples": ["多个维度，0个数值"],
                "pattern": "^(.*)$",
            },
            "description": {
                "$id": "#/items/properties/description",
                "type": ["string", "null"],
                "title": "The Description Schema",
                "default": "",
                "examples": ["按钮筛选"],
                "pattern": "^(.*)$",
            },
            "menu_id": {
                "$id": "#/items/properties/menu_id",
                "type": ["string", "null"],
                "title": "The Menu_id Schema",
                "default": "",
                "examples": ["39e438fe-bae3-6f9b-dd8c-2adc0ca73991"],
                "pattern": "^(.*)$",
            },
            "endpoint": {
                "$id": "#/items/properties/endpoint",
                "type": ["string", "null"],
                "title": "The Endpoint Schema",
                "default": "",
                "examples": ["https://dmp-open.mypaas.com.cn/dmp-test/component/package/"],
                "pattern": "^(.*)$",
            },
            "is_build_in": {
                "$id": "#/items/properties/is_build_in",
                "type": ["string", "null"],
                "title": "The Is_build_in Schema",
            },
            "penetrable": {"$id": "#/items/properties/penetrable", "type": "integer", "title": "The Penetrable Schema"},
            "icon": {
                "$id": "#/items/properties/icon",
                "type": ["string", "null"],
                "title": "The Icon Schema",
                "default": "",
                "examples": ["platform/icon/button_filter.svg"],
                "pattern": "^(.*)$",
            },
            "md5version": {
                "$id": "#/items/properties/md5version",
                "type": ["string", "null"],
                "title": "The Md5version Schema",
                "default": "",
                "examples": ["7f6f9d0d"],
                "pattern": "^(.*)$",
            },
            "can_linked": {"$id": "#/items/properties/can_linked", "type": "integer", "title": "The Can_linked Schema"},
            "dims_report_redirect": {
                "$id": "#/items/properties/dims_report_redirect",
                "type": "integer",
                "title": "The Dims_report_redirect Schema",
            },
            "has_desiredvalue": {
                "$id": "#/items/properties/has_desiredvalue",
                "type": "integer",
                "title": "The Has_desiredvalue Schema",
            },
            "menu_icon": {
                "$id": "#/items/properties/menu_icon",
                "type": ["string", "null"],
                "title": "The Menu_icon Schema",
                "default": "",
                "examples": ["filter.svg"],
                "pattern": "^(.*)$",
            },
            "data_source_origin": {
                "$id": "#/items/properties/data_source_origin",
                "type": ["string", "null"],
                "title": "The Data_source_origin Schema",
                "default": "",
                "examples": ["dataSet"],
                "pattern": "^(.*)$",
            },
            "package": {
                "$id": "#/items/properties/package",
                "type": ["string", "null"],
                "title": "The Package Schema",
                "default": "",
                "examples": ["button_filter"],
                "pattern": "^(.*)$",
            },
            "has_zaxis": {"$id": "#/items/properties/has_zaxis", "type": "integer", "title": "The Has_zaxis Schema"},
            "chart_type": {
                "$id": "#/items/properties/chart_type",
                "type": ["string", "null"],
                "title": "The Chart_type Schema",
                "default": "",
                "examples": ["filter"],
                "pattern": "^(.*)$",
            },
            "data_logic_type_code": {
                "$id": "#/items/properties/data_logic_type_code",
                "type": ["string", "null"],
                "title": "The Data_logic_type_code Schema",
                "default": "",
                "examples": ["assist"],
                "pattern": "^(.*)$",
            },
            "version": {
                "$id": "#/items/properties/version",
                "type": ["string", "null"],
                "title": "The Version Schema",
                "default": "",
                "examples": ["0.2.7"],
                "pattern": "^(.*)$",
            },
            "created_on": {
                "$id": "#/items/properties/created_on",
                "type": ["string", "null"],
                "title": "The Created_on Schema",
                "default": "",
                "examples": ["2018-10-17T16:03:07"],
                "pattern": "^(.*)$",
            },
            "menu_name": {
                "$id": "#/items/properties/menu_name",
                "type": ["string", "null"],
                "title": "The Menu_name Schema",
                "default": "",
                "examples": ["筛选器"],
                "pattern": "^(.*)$",
            },
            "sortable": {"$id": "#/items/properties/sortable", "type": "integer", "title": "The Sortable Schema"},
        },
    },
	}
