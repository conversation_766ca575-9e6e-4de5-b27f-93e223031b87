import copy
from collections import defaultdict
import json


from dashboard_chart.dashboard_editor.metadata_storage import MetadataStorage


class ReleaseParser:
    """
    把元数据翻译成发布表所需要的数据格式
    """

    def __init__(self, meta):
        self.meta = MetadataStorage(meta)

    @classmethod
    def parse_dashboard(cls, meta, extra):
        """
        解析dashboard
        :param meta:
        :param extra: dict 包括的key type_access_released snapshot_id share_secret_key
        :return:
        """
        meta = MetadataStorage(meta)
        dashboard = meta.get_data_by_path('first_report')
        same_keys = (
            'id',
            'name',
            'level_code',
            'scale_mode',
            'cover',
            'biz_code',
            'refresh_rate',
            'new_layout_type',
            'create_type',
        )
        data = {v: dashboard.get(v) for v in same_keys}
        data.update(extra)
        grid_padding = meta.get_data_by_path('style.grid_padding', dashboard)
        if grid_padding:
            grid_padding = json.dumps(grid_padding)
        layout = copy.copy(dashboard.get('layout'))
        platform = layout.pop('platform')
        data.update(
            {
                "background": json.dumps(dashboard.get('styles').get('background')),
                "dashboard_filters": json.dumps(dashboard.get('dashboard_filters')),
                "data_type": 1,
                "grid_padding": grid_padding,
                "layout": json.dumps(layout),
                "platform": platform,
                "selectors": json.dumps(cls.parse_link(meta)),
                "status": 0,
                "theme": meta.get_data_by_path('styles.theme', dashboard),
                # 下面是默认值
                "type_access_released": 4,
                "type": 'FILE',
                "user_group_id": '',
                "refresh_rate": '',
            }
        )
        return 'dashboard_released_snapshot_dashboard', data

    @classmethod
    def parse_link_relations(cls, relations, data):
        for relation in relations:
            if relation.get('is_same_dataset'):
                data['is_same_dataset'] = 1
                return data
            data['fields'].append(
                {'initiator_id': relation.get('field_initiator_id'), 'responder_id': relation.get('field_responder_id')}
            )
        return data

    @classmethod
    def parse_link(cls, meta):
        """处理联动"""
        ret = defaultdict(list)
        links = meta.get_data_by_path('first_report.chart_relations.linkages')
        if links and isinstance(links, list):
            for link in links:
                chart_initiator_id = link.get('chart_initiator_id')
                related_list = link.get('related_list')
                for relate in related_list:
                    data = {
                        "chart_id": relate.get('chart_responder_id'),
                        "dataset_id": link.get('dataset_id'),
                        "is_same_dataset": 0,
                        "fields": [],
                    }
                    ret[chart_initiator_id].append(cls.parse_link_relations(relate.get('relations'), data))
        return ret

    @classmethod
    def parse_chart(cls, meta, extra):
        """
        解析chart
        :param meta:
        :param extra: dict 包括的key snapshot_id
        :return:
        """
        meta = MetadataStorage(meta)
        ret = []
        # penetrate 单独处理
        same_keys = ('id', 'chart_type', 'config', 'layout_extend', 'layout', 'level_code', 'name', 'sort_method')
        dashboard_id = meta.get_data_by_path('first_report.id')
        charts = meta.metadata_dashboard_charts()
        normal_redirect, param_redirect = cls.parse_redirect(meta)
        component_filter = cls.parse_component_filter(meta)
        chart_id2penetrate_relation, chart_id2parent_id = cls.parse_penetrate(meta)
        if charts:
            for chart in charts:
                chart_id = chart['id']
                data = {v: chart.get(v) for v in same_keys}
                data.update(extra)
                data.update(
                    {
                        'chart_code': chart.get('chart_component_code'),
                        'chart_params': json.dumps(cls.parse_chart_param(meta, chart)),
                        'chart_params_jump': json.dumps(param_redirect[chart_id]),
                        'comparisons': json.dumps(meta.get_data_by_path('data.indicator.comparisons', chart)),
                        'component_filter': json.dumps(component_filter[chart_id]),
                        'desires': json.dumps(cls.parse_desire(meta, chart)),
                        'dims': json.dumps(cls.parse_dim(meta, chart)),
                        'dashboard_id': dashboard_id,
                        'data_logic_type_code': meta.get_data_by_path("data.data_type.logic_type", chart),
                        'display_item': meta.get_data_by_path("funcSetup.display_item", chart),
                        'filter_config': '',  # TODO 有用？
                        'filters': json.dumps(meta.get_data_by_path('data.indicator.filters', chart)),
                        'jump': '',  # TODO 处理
                        'marklines': json.dumps(meta.get_data_by_path('data.indicator.marklines', chart)),
                        'nums': json.dumps(cls.parse_num(meta, chart)),
                        'parent_id': chart_id2parent_id[chart_id],
                        'penetrate_relation': json.dumps(chart_id2penetrate_relation[chart_id]),
                        'penetrates': '',  # TODO
                        'position': json.dumps(chart.get('position')),
                        'refresh_rate': meta.get_data_by_path("funcSetup.refresh_rate", chart),
                        'source': meta.get_data_by_path("data.datasource.id", chart),
                        'source_type': meta.get_data_by_path('data.datasource.type', chart),
                    }
                )
                ret.append(data)
        return 'dashboard_released_snapshot_chart', ret

    @staticmethod
    def parse_chart_param(meta, chart):
        ret = []
        use_keys = ('alias', 'dashboard_chart_id', 'dashboard_id', 'dataset_field_id', 'rank')
        params = meta.get_data_by_path('data.indicator.chart_params', chart)
        if params:
            for param in params:
                data = {k: param[k] for k in use_keys}
                data['param_id'] = param['id']
                ret.append(data)
        return ret

    @classmethod
    def parse_redirect(cls, meta):
        # 普通跳转
        normal_redirect = defaultdict(list)
        # 参数跳转
        param_redirect = defaultdict(list)
        redirects = meta.get_data_by_path('first_report.chart_relations.redirects')
        if redirects and isinstance(redirects, list):
            dashboard_id = meta.get_data_by_path('first_report.id')
            for redirect in redirects:
                chart_id = redirect.get('chart_id')
                chart_redirects = redirect.get('chart_redirect')
                for chart_redirect in chart_redirects:
                    relations = chart_redirect.get('relations')
                    if chart_redirect.get('type') == 1 and relations:
                        # 参数跳转
                        param_redirect[chart_id].append(
                            {
                                'chart_dataset_field_id': chart_redirect.get('dataset_field_id'),
                                'dashboard_chart_id': chart_id,
                                'dashboard_filter_id': relations[0].get('dashboard_filter_id'),
                                'dashboard_id': dashboard_id,
                                'param_dataset_field_id': relations[0].get('field_initiator_id'),
                            }
                        )
        return normal_redirect, param_redirect

    @classmethod
    def parse_component_filter_relations(cls, relate, data, chart_initiator_id):
        relations = relate.get("relations")
        for relation in relations:
            if relation.get('is_same_dataset'):
                data['is_same_dataset'] = 1
                continue
            data['relation'].append(
                {
                    'chart_id': chart_initiator_id,
                    'field_initiator_id': relation.get('field_initiator_id'),
                    'field_responder_id': relation.get('field_responder_id'),
                    'filter_id': relate.get('id'),
                    'id': relation.get('id'),
                }
            )
        return data

    @classmethod
    def parse_component_filter(cls, meta):
        ret = defaultdict(list)
        filters = meta.get_data_by_path('first_report.chart_relations.filters')
        if filters and isinstance(filters, list):
            for item in filters:
                related_list = item.get('related_list')
                chart_initiator_id = item.get('chart_initiator_id')
                for relate in related_list:
                    data = {
                        'chart_initiator_id': chart_initiator_id,
                        'chart_responder_id': relate.get('chart_responder_id'),
                        'dataset_id': item.get('dataset_id'),
                        'id': relate.get('id'),
                        'is_same_dataset': 0,
                        'relation': [],
                    }
                    ret[chart_initiator_id].append(
                        cls.parse_component_filter_relations(relate, data, chart_initiator_id)
                    )
        return ret

    @classmethod
    def parse_desire(cls, meta, chart):
        desires = meta.get_data_by_path('data.indicator.desires', chart)
        return cls.json_display_format(desires)

    @staticmethod
    def parse_dim(meta, chart):
        # TODO 解析 chart_params_jump dashboard_jump_config
        dim_list = meta.get_data_by_path("data.indicator.dims", chart)
        return dim_list

    @staticmethod
    def parse_num(meta, chart):
        # TODO 解析 chart_params_jump dashboard_jump_config
        nums = meta.get_data_by_path("data.indicator.nums", chart)
        return nums

    @staticmethod
    def parse_penetrate(meta):
        chart_id2penetrate_relation = defaultdict(list)
        chart_id2parent_id = defaultdict(lambda: '')
        penetrates = meta.get_data_by_path('first_report.chart_relations.penetrates')
        if penetrates and isinstance(penetrates, list):
            for penetrate in penetrates:
                chart_id = penetrate.get('chart_id')
                parent_id = penetrate.get('parent_id')
                if parent_id:
                    chart_id2parent_id[chart_id] = parent_id
                relations = penetrate.get('relation')
                chart_id2penetrate_relation[chart_id] = relations
        return chart_id2penetrate_relation, chart_id2parent_id

    @classmethod
    def parse_z(cls, meta, chart):
        zs = meta.get_data_by_path("data.indicator.zaxis", chart)
        return cls.json_display_format(zs)

    @staticmethod
    def json_display_format(items):
        ret = []
        if ret:
            for item in items:
                item = copy.copy(item)
                item['display_format'] = json.dumps(item['display_format'])
                ret.append(item)
        return ret
