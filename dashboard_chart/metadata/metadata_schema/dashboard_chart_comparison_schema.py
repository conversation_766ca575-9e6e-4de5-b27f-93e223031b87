#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"comparisons": [
          {
            "id": "39ef0b9c-0887-fd5c-37eb-22b84ba91797",
            "alias": "项目",
            "content": null,
            "dashboard_chart_id": "39ef0b9c-087c-4cda-72d4-3ddadcc23f9a",
            "formula_mode": "",
            "rank": 0,
            "sort": null,
            "dataset_field_id": "39e7702f-6d4e-8ce3-6671-0ba8133b7cd6",
            "alias_name": "项目",
            "field_group": "维度",
            "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
            "data_type": "字符串",
            "col_name": "col5",
            "expression": null,
            "type": "普通",
            "visible": 1
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_comparison_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["id", "alias", "dashboard_chart_id", "formula_mode", "dataset_field_id"],
        "properties": {
            # 配置id
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["8c1fb43c-81c1-11e9-abbd-6d6b5792d135"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 别名
            "alias": {
                "$id": "#/items/properties/alias",
                "type": ["string", "null"],
                "title": "The Alias Schema",
                "default": "",
                "examples": ["col3"],
                "pattern": "^(.*)$",
            },
            # 自定义排序
            "content": {
                "$id": "#/items/properties/content",
                "type": ["string", "null", "object"],
                "title": "The Content Schema",
            },
            # 单图id
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["f5411d66-81c0-11e9-a360-3da6f0020d37"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 格式
            "formula_mode": {
                "$id": "#/items/properties/formula_mode",
                "type": "string",
                "title": "The Formula_mode Schema",
                "pattern": "^(.*)$",
            },
            # 字段id
            "dataset_field_id": {
                "$id": "#/items/properties/dataset_field_id",
                "type": "string",
                "title": "The Dataset_field_id Schema",
                "default": "",
                "examples": ["39ea6fe0-5991-d7ba-04da-538e83358171"],
                "minLength": 36,
                "maxLength": 36,
            },
        },
    },
}
