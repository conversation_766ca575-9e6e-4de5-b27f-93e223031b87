#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"dims": [
          {
            "id": "39ef0b9c-0887-095e-aa43-48ab4c70597c",
            "dashboard_chart_id": "39ef0b9c-0887-ecf5-94d9-a7a107f1ff38",
            "alias": "事业部",
            "content": "",
            "formula_mode": "",
            "rank": 0,
            "sort": "",
            "dim": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
            "note": null,
            "is_subtotal_cate": 0,
            "dim_type": 0,
            "dataset_field_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
            "alias_name": "事业部",
            "field_group": "维度",
            "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
            "data_type": "字符串",
            "col_name": "col1",
            "expression": null,
            "type": "普通",
            "visible": 1
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_dim_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["id", "dashboard_chart_id", "alias", "formula_mode", "dim"],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["39ef0702-3b22-d4f2-a797-8e0320303916"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["39ef0702-3b14-7811-d802-f008101921f9"],
                "minLength": 36,
                "maxLength": 36,
            },
            "alias": {
                "$id": "#/items/properties/alias",
                "type": ["string", "null"],
                "title": "The Alias Schema",
                "default": "",
                "examples": ["事业部"],
                "pattern": "^(.*)$",
            },
            "content": {
                "$id": "#/items/properties/content",
                "type": ["string", "null", "object"],
                "title": "The Content Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            "formula_mode": {
                "$id": "#/items/properties/formula_mode",
                "type": ["string", "null"],
                "title": "The Formula_mode Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            # 维度id
            "dim": {
                "$id": "#/items/properties/dim",
                "type": "string",
                "title": "The Dim Schema",
                "default": "",
                "examples": ["39e7702f-6d48-1e92-97ec-ef8cc6fbee81"],
                "minLength": 36,
                "maxLength": 36,
            },
            "note": {"$id": "#/items/properties/note", "type": ["string", "null"], "title": "The Note Schema"},
            # 是否是小计分类汇总字段
            "is_subtotal_cate": {
                "$id": "#/items/properties/is_subtotal_cate",
                "type": "integer",
                "title": "The Is_subtotal_cate Schema",
            },
            # 维度类型
            "dim_type": {"$id": "#/items/properties/dim_type", "type": "integer", "title": "The Dim_type Schema"},
        },
    },
}
