#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"field_sorts": [
          {
            "id": "39ef0b9c-0889-61c8-e413-1318704e832e",
            "dataset_field_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
            "field_source": "dims",
            "sort": "ASC",
            "content": "",
            "weight": 2
          },
          {
            "id": "39ef0b9c-0889-634d-aaa9-483a0ca34da8",
            "dataset_field_id": "39e63c4b-0df1-7393-5f0d-01707030d86d",
            "field_source": "dims",
            "sort": "ASC",
            "content": "",
            "weight": 1
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_field_sort_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["id", "dataset_field_id", "field_source", "sort", "content", "weight"],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["09cab503-a225-11e9-a3b8-0d6d8253deb5"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dataset_field_id": {
                "$id": "#/items/properties/dataset_field_id",
                "type": "string",
                "title": "The Dataset_field_id Schema",
                "default": "",
                "examples": ["39e7702f-6d4d-3364-bd2d-855adaf150b9"],
                "minLength": 36,
                "maxLength": 36,
            },
            "field_source": {
                "$id": "#/items/properties/field_source",
                "type": ["string", "null"],
                "title": "The Field_source Schema",
                "default": "",
                "examples": ["dims"],
                "pattern": "^(.*)$",
            },
            "sort": {
                "$id": "#/items/properties/sort",
                "type": "string",
                "title": "The Sort Schema",
                "default": "",
                "examples": ["CUSTOM"],
                "pattern": "^(.*)$",
            },
            "content": {
                "$id": "#/items/properties/content",
                "type": ["object", "null", "string"],
                "title": "The Content Schema",
            },
            "weight": {"$id": "#/items/properties/weight", "type": "integer", "title": "The Weight Schema"},
        },
    },
}
