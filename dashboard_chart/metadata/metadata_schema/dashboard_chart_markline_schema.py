#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"marklines": [
          {
            "id": "39ef0b9c-0888-2208-5226-5e1032d8241b",
            "dashboard_chart_id": "39ef0b9c-0887-ecf5-94d9-a7a107f1ff38",
            "formula_mode": "avg",
            "mode": "计算值",
            "name": "辅助线(1)",
            "value": "",
            "axis_type": 2,
            "num": "39e63c4b-0df2-95b1-903e-126fd547a1c3"
          },
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_markline_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["id", "dashboard_chart_id", "formula_mode", "mode", "name", "value", "axis_type", "num"],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["5c72b8b4-a226-11e9-a3b8-0d6d8253deb5"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["5c657313-5d9a-11e9-890b-a30666fa5797"],
                "minLength": 36,
                "maxLength": 36,
            },
            "formula_mode": {
                "$id": "#/items/properties/formula_mode",
                "type": "string",
                "title": "The Formula_mode Schema",
                "default": "",
                "examples": ["avg"],
                "pattern": "^(.*)$",
            },
            "mode": {
                "$id": "#/items/properties/mode",
                "type": "string",
                "title": "The Mode Schema",
                "default": "",
                "examples": ["计算值"],
                "pattern": "^(.*)$",
            },
            "name": {
                "$id": "#/items/properties/name",
                "type": ["string", "null"],
                "title": "The Name Schema",
                "default": "",
                "examples": ["辅助线(1)"],
                "pattern": "^(.*)$",
            },
            "value": {
                "$id": "#/items/properties/value",
                "type": "string",
                "title": "The Value Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            "axis_type": {"$id": "#/items/properties/axis_type", "type": "integer", "title": "The Axis_type Schema"},
            "num": {
                "$id": "#/items/properties/num",
                "type": ["string", "null"],
                "title": "The Num Schema",
                "default": "",
                "examples": ["39e63c4b-0df2-95b1-903e-126fd547a1c3"],
            },
        },
    },
}
