#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"chart_params": [
          {
            "alias": "事业部(1)",
            "dashboard_chart_id": "39ef0b9c-0887-ecf5-94d9-a7a107f1ff38",
            "dashboard_id": "39ef0b9c-087c-47b1-9927-c5fe2b7d2a52",
            "dataset_field_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
            "id": "39ef0b9c-0888-34a5-467e-36566ecf81c5",
            "rank": 0,
            "alias_name": "事业部",
            "field_group": "维度",
            "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
            "data_type": "字符串",
            "col_name": "col1",
            "expression": null,
            "type": "普通",
            "visible": 1
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_param_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["alias", "dashboard_chart_id", "dataset_field_id", "id", "rank"],
        "properties": {
            "alias": {
                "$id": "#/items/properties/alias",
                "type": "string",
                "title": "The Alias Schema",
                "default": "",
                "examples": ["事业部(1)"],
                "pattern": "^(.*)$",
            },
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["39ef0b9c-0887-ecf5-94d9-a7a107f1ff38"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dataset_field_id": {
                "$id": "#/items/properties/dataset_field_id",
                "type": "string",
                "title": "The Dataset_field_id Schema",
                "default": "",
                "examples": ["39e63c4b-0df1-6c98-dd79-106f0597fecc"],
                "minLength": 36,
                "maxLength": 36,
            },
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["39ef0b9c-0888-34a5-467e-36566ecf81c5"],
                "minLength": 36,
                "maxLength": 36,
            },
            "rank": {"$id": "#/items/properties/rank", "type": "integer", "title": "The Rank Schema"},
        },
    },
}
