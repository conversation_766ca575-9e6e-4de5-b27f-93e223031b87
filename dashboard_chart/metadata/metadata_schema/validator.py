#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""
元数据json校验器

ps: 此模块的json校验实现是引用jsonchema开源库，开源库官方站点为http://json-schema.org/，
    生成schema可以参考使用https://jsonschema.net/提供的工具
"""

# ---------------- 标准模块 ----------------
import logging
import jsonschema
from dmplib.utils.errors import UserError
from jsonschema.exceptions import ValidationError
from dashboard_chart.metadata.metadata_schema import (
    first_report_schema,
    dashboard_filters_schema,
    component_filter_schema,
    linkage_schema,
    redirect_schema,
    penetrate_schema,
    single_chart_schema,
    var_relation_schema,
)


from typing import Callable, Any, Dict, Tuple

logger = logging.getLogger(__name__)


def catch_validate_except(func: Callable) -> Callable:
    """
    异常捕获装饰器
    :param func:
    :return:
    """

    def _handle(*args, **kwargs):
        try:
            res = func(*args, **kwargs)
            return res
        except ValidationError as e:
            # 暂时不上报sentry
            # exc_type, exc_instance, exc_traceback = sys.exc_info()
            # formatted_traceback = "".join(traceback.format_tb(exc_traceback))
            # logger.exception(msg=f"报告数据合法性校验异常:\n{formatted_traceback}")
            return False, f"报告数据合法性校验异常，错误信息:{str(e.message)}"
        except Exception as e:
            logger.exception(msg=str(e))
            raise UserError(message=str(e))

    return _handle


class Validator:
    """
    校验基类
    """

    def __init__(self):
        pass

    @staticmethod
    def _validate_data(validate_data: Any, schema: Dict[Any, Any]) -> None:
        """
        执行校验
        :param validate_data:
        :param schema:
        :return:
        """
        return jsonschema.validate(validate_data, schema)


class EditorMetadataValidator(Validator):

    # 单图需要校验的schema
    chart_schema_list = [
        "dims",
        "nums",
        "zaxis",
        "comparisons",
        "filters",
        "desires",
        "marklines",
        "field_sorts",
        "chart_params",
    ]

    def __init__(self, **kwargs):
        self.data = kwargs.get("data", None)
        super().__init__()

    def _batch_import_schema_rules(self) -> Dict[str, Dict[Any, Any]]:
        """
        预先批量引入校验用的schema文件
        :return:
        """
        schema_rules_dict = {}
        for schema_name in self.chart_schema_list:
            real_schema_name = schema_name[:-1]
            if schema_name in ["zaxis"]:
                real_schema_name = schema_name
            elif schema_name in ["chart_params", "chart_vars"]:
                real_schema_name = (schema_name.split("_")[-1])[:-1]

            schema_filename = schema = "dashboard_chart_" + real_schema_name + "_schema"
            module_obj = __import__("dashboard_chart.metadata.metadata_schema." + schema_filename, fromlist=[schema])
            schema_rules_dict[schema_name] = getattr(module_obj, schema)
        return schema_rules_dict

    @catch_validate_except
    def validate_metadata(self) -> Tuple[bool, str]:
        """
        元数据字段校验
        step1: 将元数据按一级主要节点先拆分出来
        step2: 执行校验，校验不通过会抛出exception
        ps: 如果元数据后续有字段更新或增加，需要修改对应节点的schema配置文件，否则此方法校验不会对更新后的字段做校验
        :return:
        """
        if not self.data:
            return False, "报告元数据不能为空"
        if not isinstance(self.data, dict):
            return False, "报告元数据类型错误"

        # first_report
        first_report = self.data.get("first_report")
        self._validate_data(first_report, first_report_schema.first_report_schema)
        # dashboard_filters
        dashboard_filters = first_report.get("dashboard_filters")
        self._validate_data(dashboard_filters, dashboard_filters_schema.dashboard_filters_schema)
        chart_relations = first_report.get("chart_relations")
        # redirects
        redirects = chart_relations.get("redirects")
        self._validate_data(redirects, redirect_schema.redirect_schema)
        # penetrates
        penetrates = chart_relations.get("penetrates")
        self._validate_data(penetrates, penetrate_schema.penetrate_schema)
        # chart_filters
        filters = chart_relations.get("chart_filters")
        self._validate_data(filters, component_filter_schema.component_filter_schema)
        # chart_linkages
        linkages = chart_relations.get("chart_linkages")
        self._validate_data(linkages, linkage_schema.linkage_schema)
        # var_relations
        var_relations = chart_relations.get("var_relations")
        self._validate_data(var_relations, var_relation_schema.var_relation_schema)
        # charts
        chart_schema_rules_dict = self._batch_import_schema_rules()
        charts = first_report.get("charts")
        for chart in charts:
            self._validate_data(chart, single_chart_schema.single_chart_schema)
            data = chart.get("data", {})
            indicator = data.get("indicator", {})
            for schema_name in self.chart_schema_list:
                self._validate_data(indicator.get(schema_name), chart_schema_rules_dict.get(schema_name))

        return True, ""
