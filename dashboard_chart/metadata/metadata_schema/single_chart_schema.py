#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
{
    "id": "39ef0b9c-087c-4cda-72d4-3ddadcc23f9a",
    "name": "折线图-1",
    "chart_type": "chart",
    "position": {
      "i": "39ef0b9c-087c-4cda-72d4-3ddadcc23f9a",
      "col": 890,
      "row": 770,
      "size_x": 480,
      "size_y": 270,
      "z": 1505
    },
    "config": "",
    "data_modified_on": null,
    "sort_method": null,
    "percentage": null,
    "column_order": null,
    "level_code": "",
    "page_size": 0,
    "chart_component_code": "comparison_line",
    "funcSetup": {
      "display_item": "",
      "refresh_rate": ""
    },
    "data": {
      "enable_subtotal": 0,
      "enable_summary": 0,
      "indicator": {
        "dims": [],
        "nums": [],
        "comparisons": [],
        "filters": [],
        "zaxis": [],
        "chart_params": [],
        "desires": [],
        "marklines": [],
        "chart_vars": [],
        "field_sorts": []
      },
      "default_value": "",
      "chart_default_value": [],
      "datasource": {
        "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
        "type": "EXCEL"
      },
      "data_type": {
        "logic_type": "default"
      },
      "filter_relation": 0
    }
  }
"""
from base.dmp_constant import SCHEMA_VERSION

single_chart_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "object",
    "title": "The Root Schema",
    "required": ["id", "name", "chart_type", "chart_component_code", "position", "config", "data", "funcSetup"],
    "properties": {
        # 单图id
        "id": {
            "$id": "#/properties/id",
            "type": "string",
            "title": "The Id Schema",
            "default": "",
            "examples": ["39ef0702-3b14-7313-b06a-0c8c5a9f7831"],
            "minLength": 36,
            "maxLength": 36,
        },
        # 单图名称
        "name": {
            "$id": "#/properties/name",
            "type": "string",
            "title": "The Name Schema",
            "default": "",
            "examples": ["折线图-1"],
            "pattern": "^(.+)$",
        },
        # level_code
        "level_code": {
            "$id": "#/properties/level_code",
            "type": ["string", "null"],
            "title": "The Level_code Schema",
            "pattern": "^(.*)$",
        },
        # 单图类型
        "chart_type": {
            "$id": "#/properties/chart_type",
            "type": "string",
            "title": "The Chart_type Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        # 单图code
        "chart_component_code": {
            "$id": "#/properties/chart_component_code",
            "type": "string",
            "title": "The Chart_component_code Schema",
            "default": "",
            "examples": ["comparison_line"],
            "pattern": "^(.+)$",
        },
        # 分页值
        "page_size": {"$id": "#/properties/page_size", "type": "integer", "title": "The Page_size Schema"},
        # 单图位置信息
        "position": {
            "$id": "#/properties/position",
            "type": ["object", "string", "null"],
            "title": "The Position Schema",
        },
        # 前端用的样式config
        "config": {
            "$id": "#/properties/config",
            "type": ["string", "null"],
            "title": "The Config Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        "data": {
            "$id": "#/properties/data",
            "type": "object",
            "title": "The Data Schema",
            "required": ["filter_relation", "data_type", "datasource", "indicator"],
            "properties": {
                # 是否开启小计（老）
                "enable_subtotal": {
                    "$id": "#/properties/data/properties/enable_subtotal",
                    "type": "integer",
                    "title": "The Enable_subtotal Schema",
                },
                # 是否开启列汇总（老）
                "enable_summary": {
                    "$id": "#/properties/data/properties/enable_summary",
                    "type": "integer",
                    "title": "The Enable_summary Schema",
                },
                # 是否开启列小计
                "enable_subtotal_col": {
                    "$id": "#/properties/data/properties/enable_subtotal_col",
                    "type": "integer",
                    "title": "The Enable_subtotal_col Schema",
                },
                # 是否开启列汇总
                "enable_subtotal_col_summary": {
                    "$id": "#/properties/data/properties/enable_subtotal_col_summary",
                    "type": "integer",
                    "title": "The Enable_subtotal_col_summary Schema",
                },
                # 是否开启行小计
                "enable_subtotal_row": {
                    "$id": "#/properties/data/properties/enable_subtotal_row",
                    "type": "integer",
                    "title": "The Enable_subtotal_row Schema",
                },
                # 是否开启行汇总
                "enable_subtotal_row_summary": {
                    "$id": "#/properties/data/properties/enable_subtotal_row_summary",
                    "type": "integer",
                    "title": "The Enable_subtotal_row_summary Schema",
                },
                # 行总计计算算方法
                "subtotal_row_summary_formula_mode": {
                    "$id": "#/properties/data/properties/enable_subtotal_row_summary",
                    "type": "string",
                    "default": "",
                    "title": "The Enable_subtotal_row_summary Schema",
                },
                "chart_default_value": {
                    "$id": "#/properties/data/properties/chart_default_value",
                    "type": "array",
                    "title": "The Chart_default_value Schema",
                },
                # 逻辑与或条件
                "filter_relation": {
                    "$id": "#/properties/data/properties/filter_relation",
                    "type": "integer",
                    "title": "The Filter_relation Schema",
                },
                "data_type": {
                    "$id": "#/properties/data/properties/data_type",
                    "type": "object",
                    "title": "The Data_type Schema",
                    "required": ["logic_type"],
                    "properties": {
                        # 单图逻辑类型
                        "logic_type": {
                            "$id": "#/properties/data/properties/data_type/properties/logic_type",
                            "type": ["string", "null"],
                            "title": "The Logic_type Schema",
                            "default": "",
                            "examples": ["default"],
                            "pattern": "^(.*)$",
                        }
                    },
                },
                "datasource": {
                    "$id": "#/properties/data/properties/datasource",
                    "type": "object",
                    "title": "The Datasource Schema",
                    "properties": {
                        # 关联数据集id
                        "id": {
                            "$id": "#/properties/data/properties/datasource/properties/id",
                            "type": ["string", "null"],
                            "title": "The Id Schema",
                            "default": "",
                            "examples": ["39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"],
                            "pattern": "^(.*)$",
                        },
                        # 关联数据集类型
                        "type": {
                            "$id": "#/properties/data/properties/datasource/properties/type",
                            "type": ["string", "null"],
                            "title": "The Type Schema",
                            "default": "",
                            "examples": ["EXCEL"],
                            "pattern": "^(.*)$",
                        },
                    },
                },
                "indicator": {
                    "$id": "#/properties/data/properties/indicator",
                    "type": "object",
                    "title": "The Indicator Schema",
                    "required": [
                        "dims",
                        "nums",
                        "comparisons",
                        "filters",
                        "zaxis",
                        "chart_params",
                        "desires",
                        "marklines",
                        "field_sorts",
                    ],
                    "properties": {
                        "dims": {
                            "$id": "#/properties/data/properties/indicator/properties/dims",
                            "type": "array",
                            "title": "The Dims Schema",
                        },
                        "nums": {
                            "$id": "#/properties/data/properties/indicator/properties/nums",
                            "type": "array",
                            "title": "The Nums Schema",
                        },
                        "comparisons": {
                            "$id": "#/properties/data/properties/indicator/properties/comparisons",
                            "type": "array",
                            "title": "The Comparisons Schema",
                        },
                        "filters": {
                            "$id": "#/properties/data/properties/indicator/properties/filters",
                            "type": "array",
                            "title": "The Filters Schema",
                        },
                        "zaxis": {
                            "$id": "#/properties/data/properties/indicator/properties/zaxis",
                            "type": "array",
                            "title": "The Zaxis Schema",
                        },
                        "chart_params": {
                            "$id": "#/properties/data/properties/indicator/properties/chart_params",
                            "type": "array",
                            "title": "The Chart_params Schema",
                        },
                        "desires": {
                            "$id": "#/properties/data/properties/indicator/properties/desires",
                            "type": "array",
                            "title": "The Desires Schema",
                        },
                        "marklines": {
                            "$id": "#/properties/data/properties/indicator/properties/marklines",
                            "type": "array",
                            "title": "The Marklines Schema",
                        },
                        "chart_vars": {
                            "$id": "#/properties/data/properties/indicator/properties/chart_vars",
                            "type": "array",
                            "title": "The Chart_vars Schema",
                        },
                        "field_sorts": {
                            "$id": "#/properties/data/properties/indicator/properties/field_sorts",
                            "type": "array",
                            "title": "The Field_sorts Schema",
                        },
                    },
                },
            },
        },
        "funcSetup": {
            "$id": "#/properties/funcSetup",
            "type": "object",
            "title": "The Funcsetup Schema",
            "required": ["display_item", "refresh_rate"],
            "properties": {
                # 显示条目数
                "display_item": {
                    "$id": "#/properties/funcSetup/properties/display_item",
                    "type": ["string", "null"],
                    "title": "The Display_item Schema",
                    "default": "",
                    "pattern": "^(.*)$",
                },
                # 刷新频率
                "refresh_rate": {
                    "$id": "#/properties/funcSetup/properties/refresh_rate",
                    "type": ["string", "null"],
                    "title": "The Refresh_rate Schema",
                    "default": "",
                    "pattern": "^(.*)$",
                },
            },
        },
    },
}
