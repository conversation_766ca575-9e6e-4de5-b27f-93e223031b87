#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"nums": [
          {
            "id": "39ef0b9c-0888-2605-1313-ef84486d8902",
            "dashboard_chart_id": "39ef0b9c-087c-4cda-72d4-3ddadcc23f9a",
            "num": "39e84452-098d-cef6-e6bb-6a36aa199f8e",
            "alias": "已售货值(/万元)",
            "formula_mode": "sum",
            "rank": 0,
            "sort": "",
            "note": null,
            "calc_null": 0,
            "display_format": {
              "column_unit_name": "",
              "display_mode": "num",
              "thousand_point_separator": 1,
              "fixed_decimal_places": 0,
              "unit": "无",
              "hidden_unit": 0
            },
            "axis_type": 0,
            "chart_code": "",
            "subtotal_formula_mode": "",
            "dataset_field_id": "39e84452-098d-cef6-e6bb-6a36aa199f8e",
            "alias_name": "已售货值(/万元)",
            "field_group": "度量",
            "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
            "data_type": "数值",
            "col_name": "col21",
            "expression": null,
            "type": "普通",
            "visible": 1
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_num_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["id", "dashboard_chart_id", "num", "alias", "formula_mode", "display_format", "chart_code"],
        "properties": {
            # 配置id
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["39ef0702-3b23-f8c3-f3fe-b45248173433"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 单图id
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["39ef0702-3b14-7811-d802-f008101921f9"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 度量字段id
            "num": {
                "$id": "#/items/properties/num",
                "type": "string",
                "title": "The Num Schema",
                "default": "",
                "examples": ["39e84452-0985-1310-8048-b28895354af8"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 别名
            "alias": {
                "$id": "#/items/properties/alias",
                "type": ["string", "null"],
                "title": "The Alias Schema",
                "default": "",
                "examples": ["销售均价(/万元)"],
                "pattern": "^(.*)$",
            },
            # 格式
            "formula_mode": {
                "$id": "#/items/properties/formula_mode",
                "type": "string",
                "title": "The Formula_mode Schema",
                "default": "",
                "examples": ["sum"],
                "pattern": "^(.*)$",
            },
            "note": {"$id": "#/items/properties/note", "type": ["string", "null"], "title": "The Note Schema"},
            # 是否包含ifnull
            "calc_null": {"$id": "#/items/properties/calc_null", "type": "integer", "title": "The Calc_null Schema"},
            "display_format": {
                "$id": "#/items/properties/display_format",
                "type": ["object", "string", "null"],
                "title": "The Display_format Schema",
            },
            # 轴类型
            "axis_type": {"$id": "#/items/properties/axis_type", "type": "integer", "title": "The Axis_type Schema"},
            # 单图code
            "chart_code": {
                "$id": "#/items/properties/chart_code",
                "type": "string",
                "title": "The Chart_code Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            # 小计计算公式(老)
            "subtotal_formula_mode": {
                "$id": "#/items/properties/subtotal_formula_mode",
                "type": "string",
                "title": "The Subtotal_formula_mode Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            # 列小计计算公式
            "subtotal_col_formula_mode": {
                "$id": "#/items/properties/subtotal_col_formula_mode",
                "type": "string",
                "title": "The Subtotal_col_formula_mode Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            # 行小计计算公式
            "subtotal_row_formula_mode": {
                "$id": "#/items/properties/subtotal_row_formula_mode",
                "type": "string",
                "title": "The Subtotal_row_formula_mode Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
        },
    },
}
