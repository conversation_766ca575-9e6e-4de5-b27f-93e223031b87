#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"first_report": {
        "id": "39ef0b9c-087c-47b1-9927-c5fe2b7d2a52",
        "name": "元数据全场景_副本_副本",
        "description": "",
        "layout": {
          "mode": "free",
          "platform": "pc",
          "ratio": "16:9",
          "width": 1920,
          "height": 1080,
          "lattice": 10,
          "toolbar": "show",
          "screenHeader": "show"
        },
        "cover": "",
        "scale_mode": 0,
        "rank": null,
        "biz_code": "a76ab65ef214455abe16e0da6e2913b4",
        "level_code": "1467-0146-",
        "create_type": 1,
        "new_layout_type": "free",
        "parent_id": "39e7a3c4-ab73-9ffe-4f47-649f83cf3ae4",
        "is_show_mark_img": 0,
        "styles": {
          "theme": "colorful_white",
          "background": {
            "show": true,
            "color": "RGBA(255,255,255,1)",
            "image": "",
            "size": "stretch"
          },
          "grid_padding": {
            "container_padding": [
              10,
              10
            ],
            "chart_margin": [
              10,
              10
            ],
            "chart_padding": [
              15,
              15,
              15,
              15
            ],
            "chart_background": "#FFFFFF"
          },
          "attrs": {}
        },
        "publish": {
          "share_secret_key": "",
          "type_access_released": 0,
          "status": 0,
          "released_on": ""
        },
        "dashboard_filters": [],
        "chart_relations": {
          "filters": [],
          "linkages": [],
          "redirects": [],
          "penetrates": [],
          "chart_filters": [],
          "chart_linkages": [],
          "var_relations": []
        },
        "charts": []
      }
"""
from base.dmp_constant import SCHEMA_VERSION


first_report_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "object",
    "title": "The Root Schema",
    "required": [
        "biz_code",
        "cover",
        "create_type",
        "description",
        "id",
        "is_show_mark_img",
        "layout",
        "level_code",
        "name",
        "new_layout_type",
        "scale_mode",
        "styles",
    ],
    "properties": {
        # biz_code
        "biz_code": {
            "$id": "#/properties/biz_code",
            "type": ["string", "null"],
            "title": "The Biz_code Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        # 封面
        "cover": {"$id": "#/properties/cover", "type": ["string", "null"], "title": "The Cover Schema", "default": ""},
        # create_type
        "create_type": {"$id": "#/properties/create_type", "type": "integer", "title": "The Create_type Schema"},
        # 描述
        "description": {
            "$id": "#/properties/dashboard/properties/description",
            "type": "string",
            "title": "The Description Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        # 报告id
        "id": {
            "$id": "#/properties/id",
            "type": "string",
            "title": "The Id Schema",
            "default": "",
            "minLength": 36,
            "maxLength": 36,
        },
        # 是否显示水印
        "is_show_mark_img": {
            "$id": "#/properties/dashboard/properties/is_show_mark_img",
            "type": "integer",
            "title": "The Is_show_mark_img Schema",
        },
        # layout
        "layout": {"$id": "#/properties/layout", "type": ["object", "null"], "title": "The Layout Schema"},
        # level_code
        "level_code": {
            "$id": "#/properties/level_code",
            "type": "string",
            "title": "The Level_code Schema",
            "default": "",
            "minLength": 1,
        },
        # 报告名称
        "name": {
            "$id": "#/properties/name",
            "type": "string",
            "title": "The Name Schema",
            "default": "",
            "pattern": "^(.+)$",
        },
        # new_layout_type
        "new_layout_type": {
            "$id": "#/properties/dashboard/properties/new_layout_type",
            "type": "string",
            "title": "The New_layout_type Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        # 父级id
        "parent_id": {
            "$id": "#/properties/dashboard/properties/parent_id",
            "type": ["string", "null"],
            "title": "The Parent_id Schema",
            "default": "",
            "pattern": "^(.*)$",
        },
        # scale_mode
        "scale_mode": {"$id": "#/properties/scale_mode", "type": "integer", "title": "The Scale_mode Schema"},
        # styles
        "styles": {
            "$id": "#/properties/styles",
            "type": "object",
            "title": "The Styles Schema",
            "required": ["background", "attrs", "theme", "grid_padding"],
            "properties": {
                "background": {
                    "$id": "#/properties/styles/properties/background",
                    "type": ["object", "null"],
                    "title": "The Background Schema",
                },
                "theme": {
                    "$id": "#/properties/styles/properties/theme",
                    "type": ["string", "null"],
                    "title": "The Theme Schema",
                    "default": "",
                    "examples": ["tech_blue"],
                    "pattern": "^(.*)$",
                },
            },
        },
    },
}
