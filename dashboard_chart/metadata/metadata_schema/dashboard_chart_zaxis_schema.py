#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"zaxis": [
          {
            "id": "39ef0b9c-0888-3fe8-9bca-7e39c41696fb",
            "dashboard_chart_id": "39ef0b9c-0887-ecf5-94d9-a7a107f1ff38",
            "num": "39e63c4b-0df2-95b1-903e-126fd547a1c3",
            "alias": "未售货值(/万元)",
            "formula_mode": "sum",
            "rank": 0,
            "sort": "",
            "note": null,
            "calc_null": 0,
            "display_format": {
              "column_unit_name": "",
              "thousand_point_separator": 1,
              "fixed_decimal_places": 0,
              "unit": "无",
              "display_mode": "num"
            },
            "axis_type": 1,
            "chart_code": "line",
            "subtotal_formula_mode": "",
            "dataset_field_id": "39e63c4b-0df2-95b1-903e-126fd547a1c3",
            "alias_name": "未售货值(/万元)",
            "field_group": "度量",
            "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
            "data_type": "数值",
            "col_name": "col19",
            "expression": null,
            "type": "普通",
            "visible": 1
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_zaxis_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": [
            "id",
            "dashboard_chart_id",
            "num",
            "alias",
            "formula_mode",
            "display_format",
            "chart_code",
            "subtotal_formula_mode",
        ],
        "properties": {
            # 配置id
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["39ef0702-3b23-f8c3-f3fe-b45248173433"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 单图id
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["39ef0702-3b14-7811-d802-f008101921f9"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 度量字段id
            "num": {
                "$id": "#/items/properties/num",
                "type": "string",
                "title": "The Num Schema",
                "default": "",
                "examples": ["39e84452-0985-1310-8048-b28895354af8"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 别名
            "alias": {
                "$id": "#/items/properties/alias",
                "type": ["string", "null"],
                "title": "The Alias Schema",
                "default": "",
                "examples": ["销售均价(/万元)"],
                "pattern": "^(.*)$",
            },
            # 格式
            "formula_mode": {
                "$id": "#/items/properties/formula_mode",
                "type": "string",
                "title": "The Formula_mode Schema",
                "default": "",
                "examples": ["sum"],
                "pattern": "^(.*)$",
            },
            "note": {"$id": "#/items/properties/note", "type": ["string", "null"], "title": "The Note Schema"},
            # 是否包含ifnull
            "calc_null": {"$id": "#/items/properties/calc_null", "type": "integer", "title": "The Calc_null Schema"},
            "display_format": {
                "$id": "#/items/properties/display_format",
                "type": ["object", "string", "null"],
                "title": "The Display_format Schema",
            },
            # 轴类型
            "axis_type": {"$id": "#/items/properties/axis_type", "type": "integer", "title": "The Axis_type Schema"},
            # 单图code
            "chart_code": {
                "$id": "#/items/properties/chart_code",
                "type": "string",
                "title": "The Chart_code Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            # 小计计算公式
            "subtotal_formula_mode": {
                "$id": "#/items/properties/subtotal_formula_mode",
                "type": "string",
                "title": "The Subtotal_formula_mode Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
        },
    },
}
