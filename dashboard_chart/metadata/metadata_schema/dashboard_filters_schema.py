#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"dashboard_filters": [
          {
            "id": "9a95cec1-3657-9df2-3954-30758228890a",
            "dashboard_id": "39ef0b9c-087c-47b1-9927-c5fe2b7d2a52",
            "operator": null,
            "col_value": null,
            "main_dataset_field_id": "39e63c4b-0df2-7953-1e43-30f05a940911",
            "select_all_flag": "0",
            "operators": [
              {
                "id": "39ef0b9c-0888-4695-1c55-61ef6d914a51",
                "operator": "!=",
                "col_value": "光谷",
                "select_all_flag": 0
              }
            ],
            "main_dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
            "alias_name": "项目",
            "col_name": "col5",
            "data_type": "字符串",
            "field_group": "维度",
            "type": "普通",
            "expression": null,
            "format": null,
            "filter_relations": [
              {
                "id": "39ef0b9c-0888-49b7-2af5-5ba3e02a46ad",
                "dashboard_id": "39ef0b9c-087c-47b1-9927-c5fe2b7d2a52",
                "main_dataset_field_id": "39e63c4b-0df2-7953-1e43-30f05a940911",
                "related_dataset_field_id": "39e7702f-6d4e-8ce3-6671-0ba8133b7cd6",
                "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"
              }
            ]
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_filters_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": [
            "id",
            "main_dataset_id",
            "main_dataset_field_id",
            "type",
            "data_type",
            "operators",
            "filter_relations",
            "dashboard_id",
        ],
        "properties": {
            # 筛选配置id
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["ec678c3f-9bd1-11e9-ac09-5dd605efbc78"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 主数据集id
            "main_dataset_id": {
                "$id": "#/items/properties/main_dataset_id",
                "type": "string",
                "title": "The Main_dataset_id Schema",
                "default": "",
                "examples": ["39ea2227-5128-43b6-0987-7a1938d1648b"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 主数据集字段id
            "main_dataset_field_id": {
                "$id": "#/items/properties/main_dataset_field_id",
                "type": "string",
                "title": "The Main_dataset_field_id Schema",
                "default": "",
                "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 操作符数组
            "operators": {
                "$id": "#/items/properties/operators",
                "type": "array",
                "title": "The Operators Schema",
                "items": {
                    "$id": "#/items/properties/operators/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": ["id", "operator", "col_value", "select_all_flag"],
                    "properties": {
                        # 配置id
                        "id": {
                            "$id": "#/items/properties/operators/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "default": "",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 操作符
                        "operator": {
                            "$id": "#/items/properties/operators/items/properties/operator",
                            "type": "string",
                            "title": "The Operator Schema",
                            "default": "",
                            "pattern": "^(.*)$",
                        },
                        # 字段值
                        "col_value": {
                            "$id": "#/items/properties/operators/items/properties/col_value",
                            "type": ["string", "null"],
                            "title": "The Col_value Schema",
                            "default": "",
                            "pattern": "^(.*)$",
                        },
                        # 是否全选标志位 0默认 1已全选
                        "select_all_flag": {
                            "$id": "#/items/properties/operators/items/properties/select_all_flag",
                            "type": "integer",
                            "title": "The Select_all_flag Schema",
                        },
                    },
                },
            },
            # 关联参数
            "var_filter_relations": {
                "$id": "#/items/properties/var_filter_relations",
                "type": "array",
                "title": "The Var_Filter_relations Schema",
                "items": {
                    "$id": "#/items/properties/var_filter_relations/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": ["id", "var_name", "related_dataset_id", "related_dataset_field_id"],
                    "properties": {
                        # 配置id
                        "id": {
                            "$id": "#/items/properties/var_filter_relations/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 0,
                            "maxLength": 36,
                        },
                        # 参数名字
                        "var_name": {
                            "$id": "#/items/properties/filter_relations/items/properties/var_name",
                            "type": "string",
                            "title": "The Dashboard_id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 1,
                            "maxLength": 128,
                        },
                        # 关联数据集id
                        "related_dataset_id": {
                            "$id": "#/items/properties/filter_relations/items/properties/related_dataset_id",
                            "type": "string",
                            "title": "The Main_dataset_field_id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 被关联数据集字段id
                        "related_dataset_field_id": {
                            "$id": "#/items/properties/filter_relations/items/properties/related_dataset_field_id",
                            "type": "string",
                            "title": "The Related_dataset_field_id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                    },
                },
            },
            # 字段关联数组
            "filter_relations": {
                "$id": "#/items/properties/filter_relations",
                "type": "array",
                "title": "The Filter_relations Schema",
                "items": {
                    "$id": "#/items/properties/filter_relations/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": ["id", "dashboard_id", "main_dataset_field_id", "related_dataset_field_id"],
                    "properties": {
                        # 配置id
                        "id": {
                            "$id": "#/items/properties/filter_relations/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 报告id
                        "dashboard_id": {
                            "$id": "#/items/properties/filter_relations/items/properties/dashboard_id",
                            "type": "string",
                            "title": "The Dashboard_id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 主数据集字段id
                        "main_dataset_field_id": {
                            "$id": "#/items/properties/filter_relations/items/properties/main_dataset_field_id",
                            "type": "string",
                            "title": "The Main_dataset_field_id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 被关联数据集字段id
                        "related_dataset_field_id": {
                            "$id": "#/items/properties/filter_relations/items/properties/related_dataset_field_id",
                            "type": "string",
                            "title": "The Related_dataset_field_id Schema",
                            "examples": ["39ea2227-d167-b70c-a62b-b8291510ff33"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                    },
                },
            },
            # 报告id
            "dashboard_id": {
                "$id": "#/items/properties/dashboard_id",
                "type": "string",
                "title": "The Dashboard_id Schema",
                "default": "",
                "examples": ["39eebe9c-4988-a253-715a-789e4eb4107f"],
                "minLength": 36,
                "maxLength": 36,
            },
        },
    },
}
