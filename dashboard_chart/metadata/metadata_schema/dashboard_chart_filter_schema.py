#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"filters": [
          {
            "id": "39ef0b9c-0888-1ae0-599f-b0317a6f9d16",
            "col_name": "col18",
            "col_value": null,
            "operator": null,
            "dashboard_chart_id": "39ef0b9c-0887-f0fc-a7c2-227995f8cc45",
            "dataset_field_id": "39e63c4b-0df2-9403-a723-0d13cd1ddec4",
            "filter_id": "39ef0b9c-0888-1ae0-599f-b0317a6f9d16",
            "operators": [
              {
                "id": "39ef0b9c-0888-197f-05d8-e8f0b47a6696",
                "operator": "is not null",
                "col_value": ""
              }
            ],
            "alias_name": "未售套数(/套)",
            "field_group": "度量",
            "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
            "data_type": "数值",
            "expression": null,
            "type": "普通",
            "visible": 1
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_filter_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": [
            "id",
            "col_name",
            "col_value",
            "operator",
            "dashboard_chart_id",
            "dataset_field_id",
            "filter_id",
            "operators",
        ],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["ca056a16-5d9a-11e9-890b-a30666fa5797"],
                "minLength": 36,
                "maxLength": 36,
            },
            "col_name": {
                "$id": "#/items/properties/col_name",
                "type": ["string", "null"],
                "title": "The Col_name Schema",
                "default": "",
                "examples": ["col1"],
                "pattern": "^(.*)$",
            },
            "col_value": {
                "$id": "#/items/properties/col_value",
                "type": ["string", "null"],
                "title": "The Col_value Schema",
            },
            "operator": {
                "$id": "#/items/properties/operator",
                "type": ["string", "null"],
                "title": "The Operator Schema",
            },
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["58ee26a0-5d9a-11e9-890b-a30666fa5797"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dataset_field_id": {
                "$id": "#/items/properties/dataset_field_id",
                "type": "string",
                "title": "The Dataset_field_id Schema",
                "default": "",
                "examples": ["39e7702f-6d48-1e92-97ec-ef8cc6fbee81"],
                "minLength": 36,
                "maxLength": 36,
            },
            "filter_id": {
                "$id": "#/items/properties/filter_id",
                "type": "string",
                "title": "The Filter_id Schema",
                "default": "",
                "examples": ["ca056a16-5d9a-11e9-890b-a30666fa5797"],
                "minLength": 36,
                "maxLength": 36,
            },
            "operators": {
                "$id": "#/items/properties/operators",
                "type": "array",
                "title": "The Operators Schema",
                "items": {
                    "$id": "#/items/properties/operators/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": ["id", "operator", "col_value"],
                    "properties": {
                        "id": {
                            "$id": "#/items/properties/operators/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "default": "",
                            "examples": ["cac9cb82-5d9a-11e9-890b-a30666fa5797"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        "operator": {
                            "$id": "#/items/properties/operators/items/properties/operator",
                            "type": "string",
                            "title": "The Operator Schema",
                            "default": "",
                            "examples": ["in"],
                            "pattern": "^(.*)$",
                        },
                        "col_value": {
                            "$id": "#/items/properties/operators/items/properties/col_value",
                            "type": "string",
                            "title": "The Col_value Schema",
                            "default": "",
                            "examples": ["[\"海亮公司\",\"第一事业部\",\"第三事业部\",\"第二事业部\",\"第四事业部\"]"],
                            "pattern": "^(.*)$",
                        },
                    },
                },
            },
        },
    },
}
