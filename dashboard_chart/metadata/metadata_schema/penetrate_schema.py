#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"penetrates": [
      {
        "chart_id": "39ef0b9c-0887-f3d3-fa32-990910cf1719",
        "parent_id": "39ef0b9c-0887-f267-9578-2de8de7d6ccc",
        "relation": [
          {
            "id": "39ef0b9c-0888-3b97-29d6-2dee7c31820d",
            "parent_chart_field_id": "39e7702f-6d4a-7b0a-dd26-8254f8fcc297",
            "child_chart_field_id": "39e63c4b-0df1-7393-5f0d-01707030d86d",
            "type": 0
          }
        ],
        "penetrate_filter_relation": [
          {
            "id": "39ef0b9c-0888-3e56-b993-ad71e80f3065",
            "parent_chart_field_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
            "child_chart_field_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
            "type": 1
          }
        ]
      }
    ]
"""
from base.dmp_constant import SCHEMA_VERSION


penetrate_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["parent_id", "chart_id"],
        "properties": {
            # 父级单图id
            "parent_id": {
                "$id": "#/items/properties/parent_id",
                "type": "string",
                "title": "The Parent_id Schema",
                "default": "",
                "examples": ["39ef0702-3b22-c1c1-64db-47ca54d0dc39"],
                "maxLength": 36,
            },
            # 单图id
            "chart_id": {
                "$id": "#/items/properties/chart_id",
                "type": "string",
                "title": "The Chart_id Schema",
                "default": "",
                "examples": ["39ef0702-3b22-c34d-6452-cbdadbeb3042"],
                "minLength": 36,
                "maxLength": 36,
            },
            "relation": {
                "$id": "#/items/properties/relation",
                "type": "array",
                "title": "The Relation Schema",
                "items": {
                    "$id": "#/items/properties/relation/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": ["id", "parent_chart_field_id", "child_chart_field_id", "type"],
                    "properties": {
                        # 配置id
                        "id": {
                            "$id": "#/items/properties/relation/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "default": "",
                            "examples": ["39ef0702-3b23-0b7e-7773-2cea4259e09b"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 父级单图字段id
                        "parent_chart_field_id": {
                            "$id": "#/items/properties/relation/items/properties/parent_chart_field_id",
                            "type": "string",
                            "title": "The Parent_chart_field_id Schema",
                            "default": "",
                            "examples": ["39e7702f-6d4a-7b0a-dd26-8254f8fcc297"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 子级单图字段id
                        "child_chart_field_id": {
                            "$id": "#/items/properties/relation/items/properties/child_chart_field_id",
                            "type": "string",
                            "title": "The Child_chart_field_id Schema",
                            "default": "",
                            "examples": ["39e63c4b-0df1-7393-5f0d-01707030d86d"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 字段关联类型 0：穿透关联 1：筛选关联
                        "type": {
                            "$id": "#/items/properties/relation/items/properties/type",
                            "type": "integer",
                            "title": "The Type Schema",
                        },
                    },
                },
            },
            "penetrate_filter_relation": {
                "$id": "#/items/properties/penetrate_filter_relation",
                "type": "array",
                "title": "The Penetrate_filter_relation Schema",
                "items": {
                    "$id": "#/items/properties/penetrate_filter_relation/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": ["id", "parent_chart_field_id", "child_chart_field_id", "type"],
                    "properties": {
                        # 配置id
                        "id": {
                            "$id": "#/items/properties/penetrate_filter_relation/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "default": "",
                            "examples": ["39ef0702-3b23-0e80-8086-cd90fa6d3bbd"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 父级单图字段id
                        "parent_chart_field_id": {
                            "$id": """#/items/properties/penetrate_filter_relation/
                            items/properties/parent_chart_field_id""",
                            "type": "string",
                            "title": "The Parent_chart_field_id Schema",
                            "default": "",
                            "examples": ["39e7702f-6d48-1e92-97ec-ef8cc6fbee81"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 子级单图字段id
                        "child_chart_field_id": {
                            "$id": "#/items/properties/penetrate_filter_relation/items/properties/child_chart_field_id",
                            "type": "string",
                            "title": "The Child_chart_field_id Schema",
                            "default": "",
                            "examples": ["39e63c4b-0df1-6c98-dd79-106f0597fecc"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 字段关联类型 0：穿透关联 1：筛选关联
                        "type": {
                            "$id": "#/items/properties/penetrate_filter_relation/items/properties/type",
                            "type": "integer",
                            "title": "The Type Schema",
                        },
                    },
                },
            },
        },
    },
}
