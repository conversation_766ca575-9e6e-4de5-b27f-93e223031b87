#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"chart_linkages": [
      {
        "id": "39ef0b9c-0889-5453-b59f-0589d19deee1",
        "chart_initiator_id": "39ef0b9c-0887-f0fc-a7c2-227995f8cc45",
        "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
        "field_initiator_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
        "related_list": [
          {
            "id": "39ef0b9c-0889-576d-3b7a-d9031f3293a4",
            "chart_responder_id": "39ef0b9c-087c-51c6-ebdc-77d189efa57c",
            "related_dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
            "field_responder_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
            "is_same_dataset": 0
          },
          {
            "id": "39ef0b9c-0889-59ef-a703-8a662657dc81",
            "chart_responder_id": "39ef0b9c-0887-ecf5-94d9-a7a107f1ff38",
            "related_dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
            "field_responder_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
            "is_same_dataset": 1
          }
        ]
      }
    ]
"""
from base.dmp_constant import SCHEMA_VERSION


linkage_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["id", "chart_initiator_id", "field_initiator_id", "dataset_id", "related_list"],
        "properties": {
            # 配置id
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "examples": ["39ef0702-3b23-23fa-1de6-aca9afb52078"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 发起方单图id
            "chart_initiator_id": {
                "$id": "#/items/properties/chart_initiator_id",
                "type": "string",
                "title": "The Chart_initiator_id Schema",
                "examples": ["39ef0702-3b22-bece-be86-610f43dba5f0"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 发起方字段id
            "field_initiator_id": {
                "$id": "#/items/properties/field_initiator_id",
                "type": "string",
                "title": "The Field_initiator_id Schema",
                "examples": ["39e63c4b-0df1-6c98-dd79-106f0597fecc"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 数据集id
            "dataset_id": {
                "$id": "#/items/properties/dataset_id",
                "type": "string",
                "title": "The Dataset_id Schema",
                "examples": ["39e63c4b-0d6a-7a67-b782-2e8acec10c67"],
                "minLength": 36,
                "maxLength": 36,
            },
            "related_list": {
                "$id": "#/items/properties/related_list",
                "type": "array",
                "title": "The Related_list Schema",
                "items": {
                    "$id": "#/items/properties/related_list/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": [
                        "chart_responder_id",
                        "related_dataset_id",
                        "field_responder_id",
                        "is_same_dataset",
                        "id",
                    ],
                    "properties": {
                        # 响应方单图id
                        "chart_responder_id": {
                            "$id": "#/items/properties/related_list/items/properties/chart_responder_id",
                            "type": "string",
                            "title": "The Chart_responder_id Schema",
                            "default": "",
                            "examples": ["39ef0702-3b14-7811-d802-f008101921f9"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 响应方数据集id
                        "related_dataset_id": {
                            "$id": "#/items/properties/related_list/items/properties/related_dataset_id",
                            "type": "string",
                            "title": "The Related_dataset_id Schema",
                            "default": "",
                            "examples": ["39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 响应方字段id
                        "field_responder_id": {
                            "$id": "#/items/properties/related_list/items/properties/field_responder_id",
                            "type": "string",
                            "title": "The Field_responder_id Schema",
                            "default": "",
                            "examples": ["39e7702f-6d48-1e92-97ec-ef8cc6fbee81"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 是否同一数据集
                        "is_same_dataset": {
                            "$id": "#/items/properties/related_list/items/properties/is_same_dataset",
                            "type": "integer",
                            "title": "The Is_same_dataset Schema",
                        },
                        # 配置id
                        "id": {
                            "$id": "#/items/properties/related_list/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "default": "",
                            "examples": ["39ef0702-3b23-2704-c224-6b568df4ea18"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                    },
                },
            },
        },
    },
}
