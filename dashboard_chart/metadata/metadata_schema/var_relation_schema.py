#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"var_relations": [
            {
              "id": "39ef0702-3b24-2bad-6d8f-9004b702d055",
              "chart_initiator_id": "39ef0702-3b22-c97c-540a-6f07f79ed956",
              "field_initiator_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
              "dashboard_id": "39ef0702-3b14-6e68-2646-7ef3c3bff6be",
              "var_id": "39ed3c88-c491-4c22-9ed8-c52cb5a3ab71",
              "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"
            }
          ]
"""
from base.dmp_constant import SCHEMA_VERSION


var_relation_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["id", "chart_initiator_id", "field_initiator_id", "dashboard_id", "var_id", "dataset_id"],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["39ef0702-3b24-2bad-6d8f-9004b702d055"],
                "minLength": 36,
                "maxLength": 36,
            },
            "chart_initiator_id": {
                "$id": "#/items/properties/chart_initiator_id",
                "type": "string",
                "title": "The Chart_initiator_id Schema",
                "default": "",
                "examples": ["39ef0702-3b22-c97c-540a-6f07f79ed956"],
                "minLength": 36,
                "maxLength": 36,
            },
            "field_initiator_id": {
                "$id": "#/items/properties/field_initiator_id",
                "type": "string",
                "title": "The Field_initiator_id Schema",
                "default": "",
                "examples": ["39e63c4b-0df1-6c98-dd79-106f0597fecc"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dashboard_id": {
                "$id": "#/items/properties/dashboard_id",
                "type": "string",
                "title": "The Dashboard_id Schema",
                "default": "",
                "examples": ["39ef0702-3b14-6e68-2646-7ef3c3bff6be"],
                "minLength": 36,
                "maxLength": 36,
            },
            "var_id": {
                "$id": "#/items/properties/var_id",
                "type": "string",
                "title": "The Var_id Schema",
                "default": "",
                "examples": ["39ed3c88-c491-4c22-9ed8-c52cb5a3ab71"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dataset_id": {
                "$id": "#/items/properties/dataset_id",
                "type": "string",
                "title": "The Dataset_id Schema",
                "default": "",
                "examples": ["39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"],
                "minLength": 36,
                "maxLength": 36,
            },
        },
    },
}
