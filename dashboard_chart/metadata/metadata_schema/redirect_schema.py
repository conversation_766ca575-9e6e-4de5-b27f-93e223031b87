#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"redirects": [
    {
      "chart_id": "39ef0b9c-087c-51c6-ebdc-77d189efa57c",
      "chart_redirect": [
        {
          "has_token": 0,
          "target": "39ec3a8c-445d-c41e-478a-c38c4a8665e0",
          "target_type": "dashboard",
          "open_way": 1,
          "dataset_field_id": "39ed413e-92c2-602a-b70c-a7d3bc898c41",
          "type": 1,
          "id": "39ef0b9c-0888-1fae-ae9a-d043c7820009",
          "status": 1,
          "dashboard_name": "目标报告1",
          "relations": [
            {
              "relation_type": 0,
              "field_initiator_id": "39e7702f-6d48-1e92-97ec-ef8cc6fbee81",
              "dashboard_filter_id": "ae70b095-39b7-11e9-888e-6369aeba51ec",
              "initiator_alias": "事业部"
            },
            {
              "relation_type": 2,
              "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
              "var_id": "39ed3c88-c491-4c22-9ed8-c52cb5a3ab71",
              "field_initiator_id": "39ed413e-92c2-602a-b70c-a7d3bc898c41",
              "dashboard_filter_id": "a3243bd9-39b7-11e9-888b-6369aeba51ec",
              "initiator_alias": "变量-文本序列"
            }
          ]
        }
      ]
    }
  ]
"""
from base.dmp_constant import SCHEMA_VERSION


redirect_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": ["chart_id", "chart_redirect"],
        "properties": {
            # 单图id
            "chart_id": {
                "$id": "#/items/properties/chart_id",
                "type": "string",
                "title": "The Chart_id Schema",
                "default": "",
                "examples": ["39ef0702-3b14-7811-d802-f008101921f9"],
                "minLength": 36,
                "maxLength": 36,
            },
            # 跳转配置信息
            "chart_redirect": {
                "$id": "#/items/properties/chart_redirect",
                "type": "array",
                "title": "The Chart_redirect Schema",
                "items": {
                    "$id": "#/items/properties/chart_redirect/items",
                    "type": "object",
                    "title": "The Items Schema",
                    "required": [
                        "type",
                        "has_token",
                        "target",
                        "target_type",
                        "dataset_field_id",
                        "open_way",
                        "dashboard_name",
                        "id",
                        "relations",
                    ],
                    "properties": {
                        #
                        "type": {
                            "$id": "#/items/properties/chart_redirect/items/properties/type",
                            "type": "integer",
                            "title": "The Type Schema",
                        },
                        # 是否加token
                        "has_token": {
                            "$id": "#/items/properties/chart_redirect/items/properties/has_token",
                            "type": "integer",
                            "title": "The Has_token Schema",
                        },
                        # 跳转目标
                        "target": {
                            "$id": "#/items/properties/chart_redirect/items/properties/target",
                            "type": "string",
                            "title": "The Target Schema",
                            "default": "",
                            "examples": ["39ec3a8c-445d-c41e-478a-c38c4a8665e0"],
                            "pattern": "^(.*)$",
                        },
                        # 目标类型
                        "target_type": {
                            "$id": "#/items/properties/chart_redirect/items/properties/target_type",
                            "type": "string",
                            "title": "The Target_type Schema",
                            "default": "",
                            "examples": ["dashboard"],
                            "pattern": "^(.*)$",
                        },
                        # 跳转字段id
                        "dataset_field_id": {
                            "$id": "#/items/properties/chart_redirect/items/properties/dataset_field_id",
                            "type": "string",
                            "title": "The Dataset_field_id Schema",
                            "default": "",
                            "examples": ["39ed413e-92c2-602a-b70c-a7d3bc898c41"],
                            "minLength": 36,
                            "maxLength": 36,
                        },
                        # 打开方式
                        "open_way": {
                            "$id": "#/items/properties/chart_redirect/items/properties/open_way",
                            "type": "integer",
                            "title": "The Open_way Schema",
                        },
                        # 目标报告名称
                        "dashboard_name": {
                            "$id": "#/items/properties/chart_redirect/items/properties/dashboard_name",
                            "type": "string",
                            "title": "The Dashboard_name Schema",
                            "default": "",
                            "examples": ["目标报告1"],
                            "pattern": "^(.*)$",
                        },
                        # 配置id
                        "id": {
                            "$id": "#/items/properties/chart_redirect/items/properties/id",
                            "type": "string",
                            "title": "The Id Schema",
                            "default": "",
                            "examples": ["39ef0702-3b23-ee36-0c16-0f1eed74b53a"],
                            "pattern": "^(.*)$",
                        },
                        "relations": {
                            "$id": "#/items/properties/chart_redirect/items/properties/relations",
                            "type": "array",
                            "title": "The Relations Schema",
                            "items": {
                                "$id": "#/items/properties/chart_redirect/items/properties/relations/items",
                                "type": "object",
                                "title": "The Items Schema",
                                "required": [
                                    "relation_type",
                                    "initiator_alias",
                                    "field_initiator_id",
                                    "dashboard_filter_id",
                                ],
                                "properties": {
                                    # 关系类型
                                    "relation_type": {
                                        "$id": """#/items/properties/chart_redirect/items/properties/relations/
                                        items/properties/relation_type""",
                                        "type": "integer",
                                        "title": "The Relation_type Schema",
                                    },
                                    # 发起方字段别名
                                    "initiator_alias": {
                                        "$id": """#/items/properties/chart_redirect/items/properties/relations/
                                        items/properties/initiator_alias""",
                                        "type": ["string", "null"],
                                        "title": "The Initiator_alias Schema",
                                        "default": "",
                                        "examples": ["事业部"],
                                        "pattern": "^(.*)$",
                                    },
                                    # 发起方字段id
                                    "field_initiator_id": {
                                        "$id": """#/items/properties/chart_redirect/items/properties/relations/
                                        items/properties/field_initiator_id""",
                                        "type": "string",
                                        "title": "The Field_initiator_id Schema",
                                        "default": "",
                                        "examples": ["39e7702f-6d48-1e92-97ec-ef8cc6fbee81"],
                                        "minLength": 36,
                                        "maxLength": 36,
                                    },
                                    # 目标报告级筛选配置id
                                    "dashboard_filter_id": {
                                        "$id": """#/items/properties/chart_redirect/items/properties/relations/
                                        items/properties/dashboard_filter_id""",
                                        "type": "string",
                                        "title": "The Dashboard_filter_id Schema",
                                        "default": "",
                                        "examples": ["ae70b095-39b7-11e9-888e-6369aeba51ec"],
                                        "minLength": 36,
                                        "maxLength": 36,
                                    },
                                    # 变量id
                                    "var_id": {
                                        "$id": "#/items/properties/chart_redirect/items/properties/relations/"
                                        "items/properties/var_id",
                                        "type": "string",
                                        "title": "The Var_id Schema",
                                        "default": "",
                                        "examples": ["ae70b095-39b7-11e9-888e-6369aeba51ec"],
                                        "minLength": 36,
                                        "maxLength": 36,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        },
    },
}
