#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"desires": [
        {
            "id": "39ef0b9c-0887-02f9-552e-374d5c9a5608",
            "alias": "已售货值(/万元)",
            "dashboard_chart_id": "39ef0b9c-0887-ecf5-94d9-a7a107f1ff38",
            "dataset_field_id": "39e63c4b-0df2-991f-5b00-29cbc9743e08",
            "mode": 0,
            "rank": 0,
            "sort": null,
            "formula_mode": "sum",
            "value": null,
            "display_format": {
              "column_unit_name": "",
              "thousand_point_separator": 1,
              "fixed_decimal_places": 0,
              "unit": "无",
              "display_mode": "num"
            },
            "alias_name": "已售货值(/万元)",
            "field_group": "度量",
            "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
            "data_type": "数值",
            "col_name": "col21",
            "expression": null,
            "type": "普通",
            "visible": 1
          }
    ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_desire_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": [
            "id",
            "alias",
            "dashboard_chart_id",
            "dataset_field_id",
            "mode",
            "formula_mode",
            "value",
            "display_format",
        ],
        "properties": {
            "id": {
                "$id": "#/items/properties/id",
                "type": "string",
                "title": "The Id Schema",
                "default": "",
                "examples": ["9e56711b-5d9b-11e9-890b-a30666fa5797"],
                "minLength": 36,
                "maxLength": 36,
            },
            "alias": {
                "$id": "#/items/properties/alias",
                "type": ["string", "null"],
                "title": "The Alias Schema",
                "default": "",
                "examples": ["已售货值(/万元)"],
                "pattern": "^(.*)$",
            },
            "dashboard_chart_id": {
                "$id": "#/items/properties/dashboard_chart_id",
                "type": "string",
                "title": "The Dashboard_chart_id Schema",
                "default": "",
                "examples": ["5c657313-5d9a-11e9-890b-a30666fa5797"],
                "minLength": 36,
                "maxLength": 36,
            },
            "dataset_field_id": {
                "$id": "#/items/properties/dataset_field_id",
                "type": ["string", "null"],
                "title": "The Dataset_field_id Schema",
                "default": "",
                "examples": ["39e63c4b-0df2-991f-5b00-29cbc9743e08"],
            },
            "formula_mode": {
                "$id": "#/items/properties/formula_mode",
                "type": "string",
                "title": "The Formula_mode Schema",
                "default": "",
                "examples": ["sum"],
                "pattern": "^(.*)$",
            },
            "value": {"$id": "#/items/properties/value", "type": ["string", "null"], "title": "The Value Schema"},
            "display_format": {
                "$id": "#/items/properties/display_format",
                "type": "object",
                "title": "The Display_format Schema",
            },
        },
    },
}
