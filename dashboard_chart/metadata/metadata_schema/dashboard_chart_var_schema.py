#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"chart_vars": [
          {
            "name": "文本-序列",
            "description": "下拉，列表，按钮",
            "var_type": 1,
            "value_type": 1,
            "default_value": "[\"\"]",
            "value_source": "userdefined",
            "value_identifier": "",
            "dataset_id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
            "field_id": "39ed413e-92c2-602a-b70c-a7d3bc898c41",
            "var_id": "39ed3c88-c491-4c22-9ed8-c52cb5a3ab71",
            "var_source": 0
          }
        ]
"""
from base.dmp_constant import SCHEMA_VERSION


dashboard_chart_var_schema = {
    "definitions": {},
    "$schema": SCHEMA_VERSION,
    "$id": "http://example.com/root.json",
    "type": "array",
    "title": "The Root Schema",
    "items": {
        "$id": "#/items",
        "type": "object",
        "title": "The Items Schema",
        "required": [
            "name",
            "description",
            "var_type",
            "value_type",
            "default_value",
            "value_source",
            "value_identifier",
            "dataset_id",
            "field_id",
            "var_id",
        ],
        "properties": {
            "name": {
                "$id": "#/items/properties/name",
                "type": "string",
                "title": "The Name Schema",
                "default": "",
                "examples": ["文本-序列"],
                "pattern": "^(.*)$",
            },
            "description": {
                "$id": "#/items/properties/description",
                "type": "string",
                "title": "The Description Schema",
                "default": "",
                "examples": ["下拉，列表，按钮"],
                "pattern": "^(.*)$",
            },
            "var_type": {"$id": "#/items/properties/var_type", "type": "integer", "title": "The Var_type Schema"},
            "value_type": {"$id": "#/items/properties/value_type", "type": "integer", "title": "The Value_type Schema"},
            "default_value": {
                "$id": "#/items/properties/default_value",
                "type": "string",
                "title": "The Default_value Schema",
                "default": "",
                "pattern": "^(.*)$",
            },
            "value_source": {
                "$id": "#/items/properties/value_source",
                "type": "string",
                "title": "The Value_source Schema",
                "default": "",
                "examples": ["userdefined"],
                "pattern": "^(.*)$",
            },
            "value_identifier": {
                "$id": "#/items/properties/value_identifier",
                "type": "string",
                "title": "The Value_identifier Schema",
                "default": "",
                "examples": [""],
                "pattern": "^(.*)$",
            },
            "dataset_id": {
                "$id": "#/items/properties/dataset_id",
                "type": "string",
                "title": "The Dataset_id Schema",
                "default": "",
                "examples": ["39e7702f-6ce1-d0b3-4007-cf7a51c7f22d"],
                "minLength": 36,
                "maxLength": 36,
            },
            "field_id": {
                "$id": "#/items/properties/field_id",
                "type": "string",
                "title": "The Field_id Schema",
                "default": "",
                "examples": ["39ed413e-92c2-602a-b70c-a7d3bc898c41"],
                "minLength": 36,
                "maxLength": 36,
            },
            "var_id": {
                "$id": "#/items/properties/var_id",
                "type": "string",
                "title": "The Var_id Schema",
                "default": "",
                "examples": ["39ed3c88-c491-4c22-9ed8-c52cb5a3ab71"],
                "minLength": 36,
                "maxLength": 36,
            },
        },
    },
}
