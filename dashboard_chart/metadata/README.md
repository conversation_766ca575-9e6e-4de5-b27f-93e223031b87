### 可视化报告-元数据关联功能修改点梳理

**以下梳理的修改点仅供参考，具体如何修改还需视情况而定**



#### 元数据新增，更新或删除字段，一般需评估以下模块，文件或方法是否需要修改：



- 报告元数据的示例md文件，此md文件维护目的是为了方便查阅元数据结构：

```
dashboard_chart/metadata/metadata_version/metadata_v1.2.md
```

- 报告单图model类

```
dashboard_chart/models.py/ChartDataModel
```

-   预览态元数据的组装

```
dashboard_chart/metadata/query_preview_metadata_model.py
dashboard_chart/metadata/dashboard_preview_metadata_models.py
dashboard_chart/services/metadata_service.py
```

- 发布态元数据的组装

```
dashboard_chart/metadata/query_release_metadata_model.py
dashboard_chart/metadata/dashboard_release_metadata_models.py
dashboard_chart/services/released_dashboard_service.py
```

- 元数据编辑（前端上报元数据接口），修改点需对应新增或更新的字段所在的editor，以及对应的editor的model类
- 报告复制（基于元数据编辑功能的复制，一般不需修改）

```
dashboard_chart/dashboard_editor/editor/editor.py
dashboard_chart/dashboard_editor/editor/models.py
```

- 报告发布，一般新增,更新或删除字段的修改点集中在获取报告表数据的repository方法中

```
dashboard_chart/repositories/chart_repository.py/get_chart_list()
```

- 报告巡检中的报告配置子模块（字段巡检并非必须，视需求而定）

```
healthy/services/dashboard_healthy_check
```

- 基于JSON格式的元数据校验（字段校验并非必须，视需求而定）

```
dashboard_chart/metadata/metadata_schema
```



#### 元数据新增功能区块（新增数据表）或修改功能区块，修改点与新增更新字段的保持一致，另外还需评估以下修改点：



- 报告分发-导出模块

```
exports/services/export_dashboard_service.py/get_one_issue_data()
```

- DMP-ADMIN平台的报告分发-导入模块

```
dmp-admin/issue_dashboard/services/dashboard_service.py
```

- DMP-ADMIN平台的报告安装模块

```
dmp-admin/issue_dashboard/services/template_install_handler.py
dmp-admin/issue_dashboard/dashboard_template_models.py
```
