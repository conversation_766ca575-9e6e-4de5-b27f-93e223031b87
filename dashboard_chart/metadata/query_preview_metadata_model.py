#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/22
# pylint: skip-file

"""
预览元数据查询类
"""

# ---------------- 标准模块 ----------------
import logging
import json
from copy import deepcopy
from collections import defaultdict

# ---------------- 业务模块 ----------------
from base.enums import DashboardTypeStatus
from dashboard_chart.services import metadata_service, chart_service, proxy_dataset_service, chart_vars_service
from dashboard_chart.repositories import metadata_repository
from dashboard_chart.metadata.common_metadata_model import MetadataQueryBaseModel
from base import repository
from dashboard_chart.metadata.dashboard_preview_metadata_models import (
    DefaultNodeModel,
    DashboardDataNodeModel,
    DashboardFilterNodeModel,
    DashboardGlobalParamsNodeModel,
    DashboardChartFilterNodeModel,
    DashboardChartLinkageNodeMode<PERSON>,
    DashboardChartRedirectNodeModel,
    DashboardChartPenetratesNodeModel,
    DashboardSingleChartNodeModel,
    DashboardChartIndicatorNodeModel,
    DashboardChartDimsNodeModel,
    DashboardChartNumsNodeModel,
    DashboardChartComparisonNodeModel,
    DashboardChartFiltersNodeModel,
    DashboardChartZaxisNodeModel,
    DashboardChartParamsNodeModel,
    DashboardChartDesiresNodeModel,
    DashboardChartMarklinesNodeModel,
    NewChartFilterNodeModel,
    NewChartLinkageNodeModel,
    DashboardSingleChartDefaultValue,
    VarRelationNodeModel,
    ChartVisibilityTriggersNodeModel,
    DashboardChartVarsNodeModel,
    DashboardChartFieldSortsNodeModel,
    DashboardValueSourceNodeModel,
    DashboardVarFilterNodeModel,
)
from dataset import external_query_service
from base.errors import UserError
from dmplib.utils.strings import is_number
from keywords.external_service import get_keyword_detail_special_flag_by_vars

logger = logging.getLogger(__name__)


class DashboardMetadataQueryModel(MetadataQueryBaseModel):
    """
    报告元数据查询类
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.dims_dict = None
        self.nums_dict = None
        self.comparisons_dict = None
        self.filters_dict = None
        self.params_dict = None
        self.desires_dict = None
        self.zaxis_dict = None
        self.marklines_dict = None
        self.field_sorts_dict = None
        self.dataset_field_id_list = list()
        self.dataset_field_dict = dict()
        self.chart_vars_dict = dict()
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_id', 'string', {'max': 36, 'required': True}))
        return rules

    @staticmethod
    def change_to_dict_in_key(data, key='dashboard_chart_id'):
        """
        转换为以单图id为key的字典类型数据
        eg.
            [{"dashboard_chart_id": "A", "param": "value"}, {"dashboard_chart_id": "A", "param": "value"}] ->
            {"A": [{"dashboard_chart_id": "A", "param": "value"}, {"dashboard_chart_id": "A", "param": "value"}]}

        :param data: 需要处理的单图数据,数据类型限制为list,且不能为空
        :param key: 作为字典key的字段名称,默认字段名是dashboard_chart_id
        :return:
        """
        result_dict = defaultdict(list)
        if not data or not isinstance(data, list):
            return result_dict
        for item in data:
            if item.get(key, ''):
                result_dict[item.get(key, '')].append(item)
        return result_dict

    @staticmethod
    def _assign_chart_data_list(chart_data_list):
        """
        补充chart_data数据
        :param chart_data_list: 单图数据数组
        :return:
        """
        if not len(chart_data_list):
            return chart_data_list
        chart_data_list = metadata_service.batch_fill_data_logic_type_code(chart_data_list)
        return chart_data_list

    def _assign_chart_vars(self, operate_dashboard_id, chart_data_list, chart_relations):
        """
        获取单图绑定使用的变量
        :param operate_dashboard_id:
        :param chart_data_list:
        :param chart_relations:
        :return:
        """
        tmp_chart_data_list = list()
        for single_chart in chart_data_list:
            tmp_single_chart = deepcopy(single_chart)
            chart_id = tmp_single_chart.get("id")
            tmp_single_chart["dims"] = self.dims_dict.get(chart_id, None)
            tmp_single_chart["nums"] = self.nums_dict.get(chart_id, None)
            tmp_single_chart["comparisons"] = self.comparisons_dict.get(chart_id, None)
            tmp_single_chart["filters"] = self.filters_dict.get(chart_id, None)
            tmp_single_chart["zaxis"] = self.zaxis_dict.get(chart_id, None)
            tmp_single_chart["params"] = self.params_dict.get(chart_id, None)
            tmp_single_chart["desires"] = self.desires_dict.get(chart_id, None)
            tmp_single_chart["marklines"] = self.marklines_dict.get(chart_id, None)
            tmp_chart_data_list.append(tmp_single_chart)
        chart_vars_dict = chart_vars_service.get_chart_dataset_vars(
            operate_dashboard_id, tmp_chart_data_list, chart_relations=chart_relations
        )
        get_keyword_detail_special_flag_by_vars([val for vals in chart_vars_dict.values() for val in vals])
        self.chart_vars_dict = chart_vars_dict


    def _collect_dataset_field_id_list(self, data, field_key_name='dataset_field_id'):
        """
        收集数据集字段id
        :param data: 需要收集数据集字段的数据
        :param field_key_name: 收集的来源字段名称
        :return:
        """
        if not data or not isinstance(data, list):
            return self.dataset_field_id_list
        for item in data:
            if item.get(field_key_name) and item.get(field_key_name) not in self.dataset_field_id_list:
                self.dataset_field_id_list.append(item.get(field_key_name))

    def _assign_dataset_field_data(self):
        """
        调用数据集提供的服务获取数据集字段数据
        :return:
        """
        if self.dataset_field_id_list:
            query_dataset_field_list = external_query_service.get_multi_dataset_fields(self.dataset_field_id_list)
            if query_dataset_field_list:
                self.dataset_field_dict = {
                    item.get('id'): item
                    for item in query_dataset_field_list
                    if item.get('id') not in self.dataset_field_dict
                }

    def _assign_chart_section_data(self, section_data, field_key_name='dataset_field_id'):
        """
        拼装数据集字段数据
        :param section_data: 需要处理的单图组成数据
        :param field_key_name: 默认字段名是dataset_field_id
        :return:
        """
        _fields = [
            'id',
            'alias_name',
            'field_group',
            'dataset_id',
            'data_type',
            'col_name',
            'expression',
            'type',
            'visible',
        ]
        if not section_data:
            return section_data
        for single_item in section_data:
            dataset_field_id = single_item.get(field_key_name, '')
            field_data = self.dataset_field_dict.get(dataset_field_id)
            single_dataset_field_data = proxy_dataset_service.switch_to_alias_name(
                field_data
            )
            if not single_dataset_field_data:
                continue
            try:
                single_item['relation_fields'] = field_data.get('relation_fields') or single_item.get('relation_fields')
            except Exception as e:
                logger.error(f"get relation_fields error: {e}")
            for single_field in _fields:
                field_key = 'dataset_field_id' if single_field == 'id' else single_field
                single_item.update({field_key: single_dataset_field_data.get(single_field)})
        return section_data

    def batch_get_chart_indicator_data(self, dashboard_id):
        """
        预先批量获取单图相关数据
        :param dashboard_id: 报告id
        :return:
        """
        nums_query_data, zaxis_query_data = metadata_service.batch_get_chart_nums_and_zaxis(dashboard_id)
        dims_query_data = metadata_repository.batch_get_dims(dashboard_id)
        comparisons_query_data = metadata_repository.batch_get_comparisons(dashboard_id)
        # 组装operators
        filters_query_data = chart_service.batch_get_operators(metadata_repository.batch_get_filters(dashboard_id))
        params_query_data = metadata_repository.batch_get_params(dashboard_id)
        desires_query_data = metadata_repository.batch_get_desires(dashboard_id)
        marklines_query_data = metadata_repository.batch_get_marklines(dashboard_id)
        if marklines_query_data:
            # config的json处理
            for markline in marklines_query_data:
                if markline["config"]:
                    markline["config"] = json.loads(markline['config'])
        field_sorts_query_data = metadata_repository.batch_get_field_sorts(dashboard_id)

        # 收集数据集字段id
        self._collect_dataset_field_id_list(nums_query_data, field_key_name='num')
        self._collect_dataset_field_id_list(zaxis_query_data, field_key_name='num')
        self._collect_dataset_field_id_list(dims_query_data, field_key_name='dim')
        self._collect_dataset_field_id_list(comparisons_query_data)
        self._collect_dataset_field_id_list(filters_query_data)
        self._collect_dataset_field_id_list(params_query_data)
        self._collect_dataset_field_id_list(desires_query_data)
        self._collect_dataset_field_id_list(marklines_query_data)

        # 批量获取数据集字段数据
        self._assign_dataset_field_data()

        # 数据集字段数据按需组装进query_data
        self.dims_dict = self.change_to_dict_in_key(
            self._assign_chart_section_data(dims_query_data, field_key_name='dim')
        )
        self.nums_dict = self.change_to_dict_in_key(
            self._assign_chart_section_data(nums_query_data, field_key_name='num')
        )
        self.zaxis_dict = self.change_to_dict_in_key(
            self._assign_chart_section_data(zaxis_query_data, field_key_name='num')
        )
        self.comparisons_dict = self.change_to_dict_in_key(self._assign_chart_section_data(comparisons_query_data))
        self.filters_dict = self.change_to_dict_in_key(self._assign_chart_section_data(filters_query_data))
        self.params_dict = self.change_to_dict_in_key(self._assign_chart_section_data(params_query_data))
        self.desires_dict = self.change_to_dict_in_key(self._assign_chart_section_data(desires_query_data))
        self.marklines_dict = self.change_to_dict_in_key(self._assign_chart_section_data(marklines_query_data))
        # field_sorts不需要另外获取数据集字段信息
        self.field_sorts_dict = self.change_to_dict_in_key(field_sorts_query_data)

    def combine_chart_relations(self, dashboard_id: str):
        """
        获取单图关系节点集合
        :param dashboard_id: 报告id
        :return:
        """
        # chart_relations主节点
        chart_relations = DefaultNodeModel(node_name='chart_relations', node_data=dict())
        # 筛选关系
        chart_filters = DashboardChartFilterNodeModel(dashboard_id=dashboard_id, node_name='filters')
        # 联动关系
        chart_linkage = DashboardChartLinkageNodeModel(dashboard_id=dashboard_id, node_name='linkages')
        # 新联动和筛选关系
        n_chart_filters = NewChartFilterNodeModel(dashboard_id=dashboard_id, node_name='chart_filters')
        n_chart_linkage = NewChartLinkageNodeModel(dashboard_id=dashboard_id, node_name='chart_linkages')

        # 跳转关系
        chart_redirect = DashboardChartRedirectNodeModel(dashboard_id=dashboard_id, node_name='redirects')
        # 穿透关系
        chart_penetrates = DashboardChartPenetratesNodeModel(dashboard_id=dashboard_id, node_name='penetrates')
        # 单图变量关系
        var_relations = VarRelationNodeModel(dashboard_id=dashboard_id, node_name='var_relations')
        # 组件触发事件
        chart_visible_triggers = ChartVisibilityTriggersNodeModel(dashboard_id=dashboard_id, node_name='chart_visible_triggers')

        chart_relations.batch_add_sub_node(
            [
                chart_filters,
                chart_linkage,
                chart_redirect,
                chart_penetrates,
                n_chart_filters,
                n_chart_linkage,
                var_relations,
                chart_visible_triggers
            ]
        )

        return chart_relations

    def combine_single_chart_indicators(self, chart_id: str):
        """
        获取单图indicator节点集合
        :param chart_id: 单图id
        :return:
        """
        chart_dims_data = self.dims_dict.get(chart_id, None)
        chart_nums_data = self.nums_dict.get(chart_id, None)
        chart_comparisons_data = self.comparisons_dict.get(chart_id, None)
        chart_filters_data = self.filters_dict.get(chart_id, None)
        chart_zaxis_data = self.zaxis_dict.get(chart_id, None)
        chart_params_data = self.params_dict.get(chart_id, None)
        chart_desires_data = self.desires_dict.get(chart_id, None)
        chart_marklines_data = self.marklines_dict.get(chart_id, None)
        chart_field_sorts_data = self.field_sorts_dict.get(chart_id, None)

        # indicator
        chart_indicator_node = DashboardChartIndicatorNodeModel(chart_id=chart_id, node_name='indicator')
        # dims
        chart_dims = DashboardChartDimsNodeModel(chart_id=chart_id, node_name='dims', orig_data=chart_dims_data)
        # nums
        chart_nums = DashboardChartNumsNodeModel(chart_id=chart_id, node_name='nums', orig_data=chart_nums_data)
        # comparisons
        chart_comparisons = DashboardChartComparisonNodeModel(
            chart_id=chart_id, node_name='comparisons', orig_data=chart_comparisons_data
        )
        # filters
        chart_filters = DashboardChartFiltersNodeModel(
            chart_id=chart_id, node_name='filters', orig_data=chart_filters_data
        )
        # zaxis
        chart_zaxis = DashboardChartZaxisNodeModel(chart_id=chart_id, node_name='zaxis', orig_data=chart_zaxis_data)
        # chart_params
        chart_params = DashboardChartParamsNodeModel(
            chart_id=chart_id, node_name='chart_params', orig_data=chart_params_data
        )
        # desires
        chart_desires = DashboardChartDesiresNodeModel(
            chart_id=chart_id, node_name='desires', orig_data=chart_desires_data
        )
        # marklines
        chart_marklines = DashboardChartMarklinesNodeModel(
            chart_id=chart_id, node_name='marklines', orig_data=chart_marklines_data
        )
        # field_sort
        chart_field_sorts = DashboardChartFieldSortsNodeModel(
            chart_id=chart_id, node_name='field_sorts', orig_data=chart_field_sorts_data
        )

        # 预处理
        chart_dims.node_data = metadata_service.pre_deal_with_section_data(
            chart_dims.node_data,
            loads_keys=['content', 'display_format'],
            pop_keys=['chart_params_jump', 'dashboard_jump_config'],
        )
        chart_nums.node_data = metadata_service.pre_deal_with_section_data(
            chart_nums.node_data, loads_keys=['display_format', 'subtotal_col_formula_expression'],
            pop_keys=['chart_params_jump', 'dashboard_jump_config']
        )
        chart_comparisons.node_data = metadata_service.pre_deal_with_section_data(chart_comparisons.node_data)
        chart_filters.node_data = metadata_service.pre_deal_with_section_data(chart_filters.node_data)
        chart_zaxis.node_data = metadata_service.pre_deal_with_section_data(
            chart_zaxis.node_data,
            loads_keys=['display_format'],
            pop_keys=['chart_params_jump', 'dashboard_jump_config'],
        )
        chart_params.node_data = metadata_service.pre_deal_with_section_data(chart_params.node_data)
        chart_desires.node_data = metadata_service.pre_deal_with_section_data(
            chart_desires.node_data, loads_keys=['display_format']
        )
        chart_marklines.node_data = metadata_service.pre_deal_with_section_data(chart_marklines.node_data)
        chart_field_sorts.node_data = metadata_service.pre_deal_with_section_data(
            chart_field_sorts.node_data, loads_keys=['content'], pop_keys=["dashboard_chart_id"]
        )

        # 获取单图引用到的数据集字段绑定的变量
        chart_vars_data = self.chart_vars_dict.get(chart_id, [])
        chart_vars = DashboardChartVarsNodeModel(chart_id=chart_id, node_name='chart_vars', orig_data=chart_vars_data)
        # 组装indicator
        chart_indicator_node.batch_add_sub_node(
            [
                chart_dims,
                chart_nums,
                chart_comparisons,
                chart_filters,
                chart_zaxis,
                chart_params,
                chart_desires,
                chart_marklines,
                chart_vars,
                chart_field_sorts,
            ]
        )
        return chart_indicator_node

    def combine_single_chart(self, chart_id: str, single_chart_data: dict):
        """
        获取单个单图节点
        :param chart_id: 单图id
        :param single_chart_data: 单一个单图的数据
        :return:
        """
        # single_chart
        single_chart = DashboardSingleChartNodeModel(
            chart_id=chart_id, node_name='single_chart', orig_data=single_chart_data
        )
        chart_node_data = single_chart.get_data()
        single_chart.batch_move_item_to_dict('funcSetup', ['display_item', 'refresh_rate'])
        default_value = chart_node_data.get('default_value', '')
        datasoure_id = chart_node_data.get('source', '')
        source_type = chart_node_data.get('type', '')
        logic_type = chart_node_data.get('data_logic_type_code', '')
        filter_relation = chart_node_data.get('filter_relation', '')
        aggregation = chart_node_data.get("aggregation", 1)
        pre_comparison = chart_node_data.get("pre_comparison", 1)
        single_chart.batch_remove_item(
            [
                'default_value',
                'source',
                'type',
                'data_logic_type_code',
                'filter_relation',
                'enable_summary',
                'enable_subtotal',
                'enable_subtotal_col',
                'enable_subtotal_col_summary',
                'enable_subtotal_row',
                'enable_subtotal_row_summary',
                'subtotal_row_summary_formula_mode',
                'aggregation',
                'pre_comparison',
                'reset_field_sort',
            ]
        )

        # indicator
        single_chart_indicator = self.combine_single_chart_indicators(chart_id)

        # chart_data
        single_chart_node = DefaultNodeModel(chart_id=chart_id, node_name='data', node_data=dict())
        subtotal_node = DefaultNodeModel(node_name='enable_subtotal', node_data=chart_node_data.get("enable_subtotal"))
        summary_node = DefaultNodeModel(node_name='enable_summary', node_data=chart_node_data.get("enable_summary"))
        enable_subtotal_col_node = DefaultNodeModel(
            node_name='enable_subtotal_col', node_data=chart_node_data.get("enable_subtotal_col")
        )
        enable_subtotal_col_summary_node = DefaultNodeModel(
            node_name='enable_subtotal_col_summary', node_data=chart_node_data.get("enable_subtotal_col_summary")
        )
        enable_subtotal_row_node = DefaultNodeModel(
            node_name='enable_subtotal_row', node_data=chart_node_data.get("enable_subtotal_row")
        )
        enable_subtotal_row_summary_node = DefaultNodeModel(
            node_name='enable_subtotal_row_summary', node_data=chart_node_data.get("enable_subtotal_row_summary")
        )
        subtotal_row_summary_formula_mode_node = DefaultNodeModel(
            node_name='subtotal_row_summary_formula_mode',
            node_data=chart_node_data.get("subtotal_row_summary_formula_mode"),
        )
        reset_field_sort = DefaultNodeModel(
            node_name='reset_field_sort', node_data=chart_node_data.get("reset_field_sort")
        )
        default_value_node = DefaultNodeModel(node_name='default_value', node_data=default_value)
        chart_default_value_node = DashboardSingleChartDefaultValue(chart_id=chart_id, node_name="chart_default_value")
        datasoure_node = DefaultNodeModel(node_name='datasource', node_data={'id': datasoure_id, 'type': source_type, 'external_type': chart_node_data.get('external_type')})
        data_type_node = DefaultNodeModel(node_name='data_type', node_data={'logic_type': logic_type})
        filter_relation_node = DefaultNodeModel(node_name='filter_relation', node_data=filter_relation)
        aggregation_node = DefaultNodeModel(node_name='aggregation', node_data=aggregation)
        pre_comparison_node = DefaultNodeModel(node_name='pre_comparison', node_data=pre_comparison)
        external_subject_node = DefaultNodeModel(
            node_name='external_subject_id', node_data=chart_node_data.get("external_subject_id")
        )
        is_highdata_node = DefaultNodeModel(node_name='is_highdata', node_data=chart_node_data.get("is_highdata"))
        close_detail_mode_node = DefaultNodeModel(node_name='close_detail_mode', node_data=chart_node_data.get("close_detail_mode"))
        single_chart_node.batch_add_sub_node(
            [
                subtotal_node,
                summary_node,
                enable_subtotal_col_node,
                enable_subtotal_col_summary_node,
                enable_subtotal_row_node,
                enable_subtotal_row_summary_node,
                subtotal_row_summary_formula_mode_node,
                reset_field_sort,
                single_chart_indicator,
                default_value_node,
                chart_default_value_node,
                datasoure_node,
                data_type_node,
                filter_relation_node,
                aggregation_node,
                pre_comparison_node,
                external_subject_node,
                is_highdata_node,
                close_detail_mode_node
            ]
        )
        single_chart.add_sub_node(single_chart_node)
        return single_chart

    def combine_dashboard_metadata(
        self, operate_dashboard_id: str, main_dashboard_node=None, screen_dashboard_data=None
    ):
        """
        获取报告元数据
        [标记] 设计时返回获取元数据入口
        :param operate_dashboard_id: 实际需要组装为节点的报告id
        :param main_dashboard_node: 已组装完成的报告节点
        :param screen_dashboard_data: 单个报告数据
        :return:
        """
        # dashboard主节点
        dashboard = (
            main_dashboard_node
            if main_dashboard_node
            else DashboardDataNodeModel(
                dashboard_id=operate_dashboard_id, node_name='dashboard', orig_data=screen_dashboard_data
            )
        )
        # global params
        global_params = DashboardGlobalParamsNodeModel(dashboard_id=operate_dashboard_id, node_name='global_params')
        # dashbaord_filter
        dashboard_filter = DashboardFilterNodeModel(dashboard_id=operate_dashboard_id, node_name='dashboard_filters')
        # dashboard_var_filter
        dashboard_var_filter = DashboardVarFilterNodeModel(dashboard_id=operate_dashboard_id, node_name='dashboard_var_filters')
        # chart_relations
        chart_relations = self.combine_chart_relations(operate_dashboard_id)

        # 预先批量获取单图相关的数据
        self.batch_get_chart_indicator_data(operate_dashboard_id)

        # charts
        charts = DefaultNodeModel(dashboard_id=operate_dashboard_id, node_name='charts', node_data=list())
        chart_data_list = metadata_service.get_chart_by_dashboard_id(operate_dashboard_id)
        chart_data_list = self._assign_chart_data_list(chart_data_list)

        # 预先收集单图的变量数据
        self._assign_chart_vars(operate_dashboard_id, chart_data_list, chart_relations.get_data())

        chart_id_list = [chart.get('id') for chart in chart_data_list]
        parent_children_map = metadata_service.get_preview_parent_children_map(chart_id_list, operate_dashboard_id)

        for single_chart_data in chart_data_list:
            chart_id = single_chart_data.get('id', '')
            if not chart_id:
                continue
            # 数据升级后，老的default_value已不存在 (为支持重复升级 因此表中default_value并未去除)
            single_chart_data["default_value"] = ""
            self.set_fixed_manual_value(single_chart_data)
            single_chart_data["children_chart_ids"] = parent_children_map.get(chart_id, [])
            single_chart = self.combine_single_chart(chart_id, single_chart_data)
            charts.batch_add_item_in_list(single_chart.get_data())

        # 变量的取值来源数组
        var_value_sources = DashboardValueSourceNodeModel(
            dashboard_id=operate_dashboard_id, node_name='var_value_sources'
        )
        dashboard.batch_add_sub_node([
            global_params,
            dashboard_filter, dashboard_var_filter,
            chart_relations, charts, var_value_sources,
        ])
        return dashboard.get_data()

    @staticmethod
    def set_fixed_manual_value(single_chart_data):
        try:
            fixed_manual_value = json.loads(single_chart_data.get('fixed_manual_value'))
        except Exception:
            fixed_manual_value = []
            single_chart_data['fixed_manual_value'] = fixed_manual_value
        if isinstance(fixed_manual_value, list):
            try:
                display_item = json.loads(single_chart_data.get('display_item'))
            except Exception:
                display_item = {}
            top_head = display_item.get('top_head')
            top_tail = display_item.get('top_tail')
            if top_head and is_number(top_head):
                fixed_manual_value = fixed_manual_value[: int(top_head)]
            elif top_tail and is_number(top_tail):
                fixed_manual_value = fixed_manual_value[-int(top_tail) :]
        single_chart_data['fixed_manual_value'] = fixed_manual_value

    def _op_screen_dashboards(self, screen_dashboard_list, screens, first_report):
        have_first_report_flag = False
        for single_screen_dashboard in screen_dashboard_list:
            screen_id = single_screen_dashboard.get('screen_id', '')
            if not screen_id:
                continue
            # 兼容前端调用接口使用，dashboard_id和screen_id意义互换
            # 兼容已发布元数据接口，带上snapshot_id
            single_dashboard_dict = dict()
            single_dashboard_dict['dashboard_id'] = single_screen_dashboard.get('screen_id')
            single_dashboard_dict['screen_id'] = single_screen_dashboard.get('dashboard_id')
            single_dashboard_dict['snapshot_id'] = ''
            screens.batch_add_item_in_list(single_dashboard_dict)

            # 只返回第一个报告的详细信息，其余的只返回id
            if not have_first_report_flag:
                first_report.node_data = self.combine_dashboard_metadata(screen_id)
                have_first_report_flag = True
        return screens, first_report

    def combine_screen_metadata(self):
        """
        预览元数据主方法-用于获取多屏元数据
        ps：只返回第一个报告的详细元数据，其他的只返回id信息
        :return:
        """
        # 获取报告是否为多屏
        dashboard_data = repository.get_data(
            'dashboard', {'id': self.dashboard_id}, ['is_multiple_screen', 'create_type']
        )
        if not dashboard_data:
            raise UserError(code=404, message='查询不到报告id对应数据')

        # 主数据节点
        main_data = DefaultNodeModel(node_name='main_node', node_data=dict())
        # 报告id对应的基本数据
        dashboard = DashboardDataNodeModel(node_name='dashboard', dashboard_id=self.dashboard_id)
        # 多屏下的报告列表节点
        screens = DefaultNodeModel(node_name='screens', node_data=list())

        # 第一个报告的数据节点
        # create_type = dashboard_data["create_type"]
        first_report = DefaultNodeModel(node_name='first_report', node_data=dict())

        if dashboard_data and dashboard_data.get('is_multiple_screen'):
            screen_dashboard_list = repository.get_data(
                'screen_dashboard',
                {'dashboard_id': self.dashboard_id, 'type': DashboardTypeStatus.Draft.value},
                ['screen_id', 'rank', 'dashboard_id'],
                True,
                [('rank', 'asc')],
            )
            if screen_dashboard_list:
                screens, first_report = self._op_screen_dashboards(screen_dashboard_list, screens, first_report)
        else:
            # 兼容前端调用接口使用，dashboard_id和screen_id意义互换
            # 兼容已发布元数据接口，带上snapshot_id
            single_dashboard_dict = dict()
            single_dashboard_dict['dashboard_id'] = self.dashboard_id
            single_dashboard_dict['screen_id'] = self.dashboard_id
            single_dashboard_dict['snapshot_id'] = ''
            screens.batch_add_item_in_list(single_dashboard_dict)

            single_screen_data = self.combine_dashboard_metadata(
                self.dashboard_id, main_dashboard_node=deepcopy(dashboard)
            )
            first_report.node_data = single_screen_data

        # 组合元数据节点
        main_data.batch_add_sub_node([dashboard, screens, first_report])
        return main_data
