#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/22
# pylint: disable=R1710,E1137

"""
已发布元数据节点类
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from base.enums import DashboardType
from base.errors import DataNotExistError
from dashboard_chart.repositories import metadata_repository
from dashboard_chart.services import metadata_service
from dashboard_chart.metadata.common_metadata_model import MetadataNodeBaseModel


logger = logging.getLogger(__name__)


class ReleasedDashboardDataNodeModel(MetadataNodeBaseModel):
    """
    dashboard数据节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.snapshot_id = None
        self.global_params = None
        self.dashboard_filters = None
        self.dashboard_var_filters = None
        self.selectors = None
        self.penetrates = None
        self.var_value_sources = None
        self.orig_data = None
        self.default_node_data = dict()
        self.rank = 0
        self.external_subject_id = None
        self.main_external_subject_id = None
        self.application_type = 0
        self.external_subject_ids = []
        self.dataset_id = ""
        self.analysis_type = None
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def _modify_data_structure(self):
        """
        调整数据
        :return:
        """
        # 转换为正常数据格式
        self.batch_loads_item_in_json(
            ['layout', 'background', 'dashboard_filters', 'grid_padding', 'var_value_sources']
        )
        self.batch_add_item_in_dict({'attrs': {}})
        modified_on = '' if not self.node_data.get('modified_on') else str(self.node_data.get('modified_on'))
        self.node_data['modified_on'] = self.time_to_str(modified_on)
        self.update_key_name(old_key='modified_on', new_key='released_on')
        new_layout_type = self.node_data.get('new_layout_type')
        self.node_data['new_layout_type'] = 'grid' if new_layout_type == 1 else 'free'
        self.node_data['create_type'] = self.node_data.get('create_type', 0)
        self.batch_move_item_to_dict('styles', ['theme', 'background', 'attrs', 'grid_padding'])
        self.batch_move_item_to_dict(
            'publish', ['status', 'url', 'share_secret_key', 'type_access_released', 'released_on']
        )
        self.move_key(operate_key='platform', aim_key='layout')
        self.dashboard_filters = self.node_data.get('dashboard_filters', [])
        global_params = metadata_service.pre_deal_with_section_data(self.node_data.get('global_params', []))
        self.node_data['global_params'] = self.global_params = global_params
        self.dashboard_var_filters = self.node_data.get('dashboard_var_filters', [])
        self.selectors = self.node_data.get('selectors', [])
        self.penetrates = self.node_data.get('penetrates', [])
        self.var_value_sources = self.node_data.get('var_value_sources', [])
        self.rank = self.node_data.get('rank', 0)
        self.main_external_subject_id = self.node_data.get('main_external_subject_id', None)
        # convert external_subject_ids str -> list
        external_subject_ids = self.node_data.get('external_subject_ids', None)
        self.external_subject_ids = external_subject_ids.split(',') if external_subject_ids else external_subject_ids
        # 处理历史数据
        if self.main_external_subject_id and not self.external_subject_ids:
            self.external_subject_ids = [self.main_external_subject_id]
        self.node_data['external_subject_ids'] = self.external_subject_ids

        self.application_type = self.node_data.get('application_type', 0)
        self.dataset_id = self.node_data.get("dataset_id", "")
        self.analysis_type = self.node_data.get("analysis_type", "")

        self.node_data['parent_name'] = ''
        parent_id = self.node_data.get('parent_id')
        dashboard_type = self.node_data.get('type')
        if parent_id and self.application_type == 1 and dashboard_type == DashboardType.CHILD_FILE.value:
            parent_dashboard = metadata_repository.get_snapshot_name_by_id(parent_id)
            parent_name = parent_dashboard.get('name') if parent_dashboard else ''
            self.node_data['parent_name'] = parent_name
        if self.application_type == 1 and self.main_external_subject_id:
            external_subject = metadata_repository.get_external_subject_name_by_id(self.main_external_subject_id)
            external_subject_name = external_subject.get('name') if external_subject else ''
            self.node_data['main_external_subject_name'] = external_subject_name
        self.batch_remove_item(operate_keys=['dashboard_filters', 'selectors', 'penetrates', 'var_value_sources'])

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.orig_data:
            self.node_data = self.orig_data
        else:
            self.node_data = metadata_service.get_released_dashbaoard_data(self.snapshot_id, self.dashboard_id)
        if not self.node_data:
            raise DataNotExistError(message='已发布报告节点数据为空')
        self._modify_data_structure()
        return self.node_data


class ReleasedDashboardFilterNodeModel(MetadataNodeBaseModel):
    """
    报告筛选数据节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardGlobalParamsNodeModel(MetadataNodeBaseModel):
    """
    报告全局参数据节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardVarFilterNodeModel(MetadataNodeBaseModel):
    """
    报告参数筛选数据节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartFilterNodeModel(MetadataNodeBaseModel):
    """
    报告单图筛选节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartLinkageNodeModel(MetadataNodeBaseModel):
    """
    报告单图联动节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartRedirectNodeModel(MetadataNodeBaseModel):
    """
    报告单图跳转节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartPenetratesNodeModel(MetadataNodeBaseModel):
    """
    报告单图穿透节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardSingleChartNodeModel(MetadataNodeBaseModel):
    """
    报告单个单图节点
    """

    def __init__(self, **kwargs):
        self.orig_data = None
        self.default_node_data = dict()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if not self.orig_data:
            return self.orig_data
        self.orig_data['config'] = '' if not self.orig_data.get('config') else self.orig_data.get('config')
        self.node_data = self.orig_data
        self.batch_loads_item_in_json(['position', 'export_type'])
        self.update_key_name('chart_code', 'chart_component_code')
        self.node_data['export_type'] = self.node_data.get('export_type') or []
        self.node_data['parent_chart_id'] = self.node_data.get('parent_chart_id') or ''
        self.node_data['children_chart_ids'] = self.node_data.get('children_chart_ids') or []
        self.node_data['fixed_data_mode'] = self.node_data.get('fixed_data_mode') or ''
        self.node_data['fixed_manual_value'] = self.node_data.get('fixed_manual_value') or []

        return self.node_data


class ReleasedDashboardChartIndicatorNodeModel(MetadataNodeBaseModel):
    """
    报告单图关联条件节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = dict()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        return self.node_data


class ReleasedDashboardChartDimsNodeModel(MetadataNodeBaseModel):
    """
    报告单图dims节点
    """

    def __init__(self, **kwargs):
        self.node_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartNumsNodeModel(MetadataNodeBaseModel):
    """
    报告单图nums节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartComparisonNodeModel(MetadataNodeBaseModel):
    """
    报告单图comparison节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartFiltersNodeModel(MetadataNodeBaseModel):
    """
    报告单图filter节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartZaxisNodeModel(MetadataNodeBaseModel):
    """
    报告单图zaxis节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartParamsNodeModel(MetadataNodeBaseModel):
    """
    报告单图chart_params节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartDesiresNodeModel(MetadataNodeBaseModel):
    """
    报告单图desires节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartMarklinesNodeModel(MetadataNodeBaseModel):
    """
    报告单图marklines节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartVarsNodeModel(MetadataNodeBaseModel):
    """
    单图引用变量节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data


class ReleasedDashboardChartFieldSortsNodeModel(MetadataNodeBaseModel):
    """
    单图字段排序节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取发布后数据
        :return:
        """
        if self.node_data:
            return self.node_data
