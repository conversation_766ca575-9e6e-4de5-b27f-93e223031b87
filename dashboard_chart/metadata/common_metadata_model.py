#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/22
# pylint: skip-file

"""
元数据节点基类
"""

# ---------------- 标准模块 ----------------
import json
import traceback
from abc import ABCMeta, abstractmethod
from datetime import datetime

# ---------------- 业务模块 ----------------
from base.models import QueryBaseModel
from base.errors import InvalidParamsError, InvalidDataError, InvalidCallError


class MetadataNodeBaseModel:
    """
    节点基类

    :param self.node_name str: 节点名称
    :param self.node_data: 节点数据
    :param self.has_sub_node_flag bool: 当前节点有无子节点
    :param self.default_node_data: 当节点数据为None时的默认值
    :param self.set_default_data_flag bool: 当节点数据为空时是否设置default_node_data默认值
    """

    __metaclass__ = ABCMeta

    def __init__(self, **kwargs):
        self.node_name = None
        self.node_data = None
        self.has_sub_node_flag = True
        self.default_node_data = None
        self.set_default_data_flag = True
        self.__set_attributes(**kwargs)
        self.__init_node(**kwargs)

    def __str__(self):
        return self.node_name

    @abstractmethod
    def init_data(self):
        """
        获取预览数据（子类必须实现重写）
        :return:
        """
        raise NotImplementedError("Please Implement this method")

    @staticmethod
    def is_builtin_string(param):
        """
        判断是否内置字符串类型
        :param param:
        :return:
        """
        return isinstance(param, str)

    @staticmethod
    def is_builtin_list(param):
        """
        判断是否内置列表类型
        :param param:
        :return:
        """
        return isinstance(param, list)

    @staticmethod
    def is_builtin_dict(param):
        """
        判断是否内置字典类型
        :param param:
        :return:
        """
        return isinstance(param, dict)

    @staticmethod
    def time_to_str(time):
        """
        转换时间戳
        :param time:
        :return:
        """
        try:
            if isinstance(time, datetime):
                return time.strftime('%Y-%m-%d %H:%M:%S')
            return time
        except Exception:
            return time

    def __init_node(self, **kwargs):
        """
        初始化数据
        :param kwargs:
        :return:
        """
        if not self.set_default_data_flag:
            return
        # 先尝试调用init_data方法获取node_data
        self.node_data = self.init_data() if not self.node_data else self.node_data
        # 如果node_data仍旧为空则尝试设置默认值default_node_data
        self.node_data = self.default_node_data if not self.node_data and self.set_default_data_flag else self.node_data

    def __set_attributes(self, **kwargs):
        """
        将dict数据初始化到对象属性
        :param kwargs:
        :return:
        """
        if not kwargs:
            return
        dirs = [a for a in dir(self) if a[0:1] != '_' and not callable(getattr(self, a))]
        dirs = set(dirs).intersection(kwargs.keys())
        if not dirs:
            return
        for k in dirs:

            attribute = getattr(self, k)
            val = kwargs.get(k)
            if isinstance(attribute, MetadataNodeBaseModel):
                if not val:
                    continue
                if self.is_builtin_dict(val):
                    attribute.__set_attributes(**val)
                elif self.is_builtin_string(val):
                    attribute.__set_attributes(**json.loads(val))
                else:
                    setattr(self, k, val)
            else:
                setattr(self, k, val)

    def get_dict(self, attributes=None):
        """
        获取属性字典
        注：当申明__slots__之后 self.__dict__将为空，必须使用dir
        :param list attributes:
        :return dict:
        """
        attr_dict = {}
        dirs = dir(self)
        if attributes:
            dirs = list(set(dirs).intersection(set(attributes)))
        for attribute in dirs:
            if attribute[0:1] == '_':
                continue
            value = getattr(self, attribute)
            if callable(value):
                continue
            attr_dict[attribute] = value
        return attr_dict

    def add_sub_node(self, operate_node):
        """
        添加子节点
        :param operate_node: 目标节点
        :return:
        """
        if not isinstance(operate_node, MetadataNodeBaseModel):
            raise InvalidParamsError(message='添加对象必须为节点数据基类的子类')
        if not self.has_sub_node_flag:
            raise InvalidCallError(message='当前节点不支持添加子节点')
        if self.is_builtin_dict(self.node_data):
            self.node_data.update({operate_node.node_name: operate_node.node_data})
        elif self.is_builtin_list(self.node_data):
            self.node_data.append(self.node_data)
        return self

    def batch_add_sub_node(self, node_list: list):
        """
        批量添加子节点
        :param node_list: 节点列表
        :return:
        """
        for node in node_list:
            self.add_sub_node(node)
        return self

    def batch_add_item_in_dict(self, data: dict):
        """
        node_data为dict，批量update新数据
        :param data:
        :return:
        """
        if self.is_builtin_dict(self.node_data):
            if not self.is_builtin_dict(data):
                raise InvalidParamsError(message='data必须为字典类型')
            for key, value in data.items():
                self.node_data.update({key: value})
        return self

    def batch_add_item_in_list(self, data: (dict or list)):
        """
        node_data为list，批量append新数据
        :param data:
        :return:
        """
        if self.is_builtin_list(self.node_data):
            if self.is_builtin_list(data):
                self.node_data.extend(data)
            elif self.is_builtin_dict(data):
                self.node_data.append(data)
        return self

    def remove_sub_node(self, sub_node):
        """
        移除子节点
        :param sub_node: 待移除子节点
        :return:
        """
        if not isinstance(sub_node, MetadataNodeBaseModel):
            raise InvalidParamsError(message='添加对象必须为节点数据基类的子类')
        if self.is_builtin_dict(self.node_data):
            if sub_node.node_name not in self.node_data:
                return self
            try:
                self.node_data.pop(sub_node.node_name)
            except Exception:
                raise InvalidCallError(message='{}'.format(traceback.print_exc()))
        return self

    def get_data(self):
        """
        获取节点数据
        :return:
        """
        return self.node_data

    def get_json_data(self):
        """
        获取json数据
        :return:
        """
        if self.node_data and self.is_builtin_string(self.node_data):
            try:
                return json.loads(self.node_data)
            except Exception:
                raise InvalidCallError(message='{}'.format(traceback.print_exc()))
        return self.node_data

    def get_dict_data(self):
        """
        获取dict节点数据
        :return:
        """
        return {self.node_name: self.node_data}

    def get_keys(self):
        """
        获取节点所有key
        :return:
        """
        if self.is_builtin_dict(self.node_data):
            return list(self.node_data.keys())
        return None

    def get_node_name(self):
        """
        获取节点名称
        :return:
        """
        return self.node_name

    def batch_move_item_to_dict(self, aim_key: str, pop_keys: list):
        """
        批量移动字段到dict
        :param aim_key: 目标dict
        :param pop_keys: 操作key列表
        :return:
        """
        changed_node_data = dict()
        new_key_dict = dict()
        if not aim_key or not pop_keys or not self.node_data:
            return self
        if not self.is_builtin_dict(self.node_data):
            raise InvalidDataError(message='{}节点数据为空，无法执行操作'.format(self.node_name))
        for key, value in self.node_data.items():
            if key in pop_keys:
                new_key_dict.update({key: value})
                continue
            changed_node_data.update({key: value})
        changed_node_data.update({aim_key: new_key_dict})
        self.node_data = changed_node_data
        return self

    def batch_remove_item(self, operate_keys: list):
        """
        批量删除字段
        :param operate_keys: 操作删除的字段key列表
        :return:
        """
        if not self.is_builtin_dict(self.node_data):
            raise InvalidDataError(message='{}节点数据类型非法，无法执行操作'.format(self.node_name))
        for operate_key in operate_keys:
            if operate_key not in self.node_data:
                continue
            if not self.is_builtin_string(operate_key):
                raise InvalidParamsError(message='操作字段必须为str类型')
            self.node_data.pop(operate_key)
        return self

    def move_key(self, operate_key: str, aim_key: str, delete_orig_flag=True):
        """
        移动字段
        :param operate_key: 操作移动的字段key
        :param aim_key: 目标字段key
        :param delete_orig_flag: 是否删除移动前的字段数据
        :return:
        """
        if not self.is_builtin_dict(self.node_data):
            return self
        if operate_key not in self.get_keys() or aim_key not in self.get_keys():
            return self
        operate_value = self.node_data[operate_key]
        aim_value = self.node_data[aim_key]
        json_dumps_flag = False
        aim_value = dict() if not aim_value else aim_value
        if not self.is_builtin_dict(aim_value):
            try:
                aim_value = json.loads(aim_value)
                json_dumps_flag = True
            except Exception:
                raise InvalidCallError(message='{}'.format(traceback.print_exc()))
        aim_value.update({operate_key: operate_value})
        if json_dumps_flag:
            aim_value = json.dumps(aim_value)
        self.node_data.update({aim_key: aim_value})
        if delete_orig_flag:
            self.batch_remove_item([operate_key])
        return self

    def batch_loads_item_in_json(self, keys_list):
        """
        批量json序列化字段数据(字典)
        :param keys_list: 字段key列表
        :return:
        """
        for key in keys_list:
            if not self.node_data or key not in self.node_data:
                continue
            if not self.node_data[key]:
                continue
            try:
                self.node_data[key] = json.loads(self.node_data[key])
            except Exception:
                pass

    def batch_loads_list_value_item_in_json(self, keys_list):
        """
        批量json序列化字段数据（list中的每个key）
        :param keys_list: 字段key列表
        :return:
        """
        if isinstance(self.node_data, dict):
            return self.batch_loads_item_in_json(keys_list)
        elif isinstance(self.node_data, list):
            for data in self.node_data:
                for key in keys_list:
                    if not data or key not in data:
                        continue
                    if not data[key]:
                        continue
                    try:
                        data[key] = json.loads(data[key])
                    except Exception:
                        pass

    def update_key_name(self, old_key='', new_key=''):
        """
        更新字段名称
        :param old_key: 原有key
        :param new_key: 目标key
        :return:
        """
        if old_key not in self.node_data.keys() or new_key in self.node_data.keys():
            return self
        if not self.is_builtin_string(old_key) or not self.is_builtin_string(new_key):
            return self
        self.node_data[new_key] = self.node_data[old_key]
        self.batch_remove_item([old_key])
        return self


class MetadataQueryBaseModel(QueryBaseModel):
    """
    查询基类(用于元数据节点组合拼装)
    """

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    @staticmethod
    def time_to_str(time):
        """
        转换时间戳
        :param time:
        :return:
        """
        try:
            if isinstance(time, datetime):
                return time.strftime('%Y-%m-%d %H:%M:%S')
            return time
        except Exception:
            return time
