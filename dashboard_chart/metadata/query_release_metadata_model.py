#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/22

"""
已发布元数据查询类
"""

# ---------------- 标准模块 ----------------
import logging
import json

# ---------------- 业务模块 ----------------
from base.enums import ChartNumSubtotalFormulaMode, ChartFilterInitiatorSouce
from dashboard_chart.services import metadata_service, proxy_dataset_service
from dashboard_chart.metadata.common_metadata_model import MetadataQueryBaseModel
from dmplib.hug import g
from dmplib.utils.strings import is_number
from dashboard_chart.services.dashboard_cache_service import DashboardCache
from dmplib.redis import conn as conn_redis
from dashboard_chart.metadata.dashboard_preview_metadata_models import DefaultNodeModel
from dashboard_chart.metadata.dashboard_release_metadata_models import (
    ReleasedDashboardDataNodeModel,
    ReleasedDashboardFilterNodeModel,
    ReleasedDashboardGlobalParamsNodeModel,
    ReleasedDashboardChartFilterNodeModel,
    ReleasedDashboardChartLinkageNodeModel,
    ReleasedDashboardChartRedirectNodeModel,
    ReleasedDashboardChartPenetratesNodeModel,
    ReleasedDashboardSingleChartNodeModel,
    ReleasedDashboardChartIndicatorNodeModel,
    ReleasedDashboardChartDimsNodeModel,
    ReleasedDashboardChartNumsNodeModel,
    ReleasedDashboardChartComparisonNodeModel,
    ReleasedDashboardChartFiltersNodeModel,
    ReleasedDashboardChartZaxisNodeModel,
    ReleasedDashboardChartParamsNodeModel,
    ReleasedDashboardChartDesiresNodeModel,
    ReleasedDashboardChartMarklinesNodeModel,
    ReleasedDashboardChartVarsNodeModel,
    ReleasedDashboardChartFieldSortsNodeModel,
    ReleasedDashboardVarFilterNodeModel
)
from open_api.services.screen_auth_service import get_third_all_auth_cache
from keywords.external_service import get_keyword_detail_special_flag_by_vars


logger = logging.getLogger(__name__)


class ReleasedDashboardMetadataQueryModel(MetadataQueryBaseModel):
    """
    多屏元数据查询类
    """

    def __init__(self, **kwargs):
        self.snapshot_id = None
        self.penetrates_data = []
        self.conn = None
        self.use_meta_cache = True
        super().__init__(**kwargs)

    @staticmethod
    def get_chart_data_fields():
        _fields = [
            'id',
            'name',
            'chart_type',
            'position',
            'config',
            'default_value',
            'source',
            'refresh_rate',
            'refresh_rate',
            'source_type',
            'data_logic_type_code',
            ('chart_code', 'chart_component_code'),
            'display_item',
            'chart_code',
            'data_modified_on',
            'sort_method',
            'percentage',
            'column_order',
            'chart_default_value',
            'filter_relation',
            "enable_subtotal",
            "enable_summary",
            "enable_subtotal_col",
            "enable_subtotal_col_summary",
            "enable_subtotal_row",
            "enable_subtotal_row_summary",
            "subtotal_row_summary_formula_mode",
            "reset_field_sort",
            "aggregation",
            "pre_comparison",
            "page_size",
            "export_type",
            "parent_chart_id",
            "children_chart_ids",
            "fixed_data_mode",
            "fixed_manual_value",
            "is_highdata",
            "close_detail_mode",
            "layout_extend",
            "chart_visible"
        ]
        return _fields

    @staticmethod
    def combine_released_chart_indicators(single_released_chart_data: dict):
        """
        组合indicators数据
        :param single_released_chart_data: 单个已发布报告的数据
        :return:
        """
        chart_id = single_released_chart_data.get('id', '')
        chart_dims = single_released_chart_data.get('dims', [])
        chart_nums = single_released_chart_data.get('nums', [])
        chart_comparisons = single_released_chart_data.get('comparisons', [])
        chart_filters = single_released_chart_data.get('filters', [])
        chart_zaxis = single_released_chart_data.get('zaxis', [])
        chart_params = single_released_chart_data.get('chart_params', [])
        chart_desires = single_released_chart_data.get('desires', [])
        chart_marklines = single_released_chart_data.get('marklines', [])
        chart_vars = single_released_chart_data.get("chart_vars", [])
        chart_field_sorts = single_released_chart_data.get("field_sorts", [])

        # 预处理数据
        chart_dims = metadata_service.pre_deal_with_section_data(
            chart_dims, loads_keys=['content'], pop_keys=['chart_params_jump', 'dashboard_jump_config']
        )
        # 老数据增加 is_subtotal_cate
        for chart_dim in chart_dims:
            if "is_subtotal_cate" not in chart_dim:
                chart_dim["is_subtotal_cate"] = 0
            if "parent_id" not in chart_dim:
                chart_dim["parent_id"] = ""

        chart_nums = metadata_service.pre_deal_with_section_data(
            chart_nums, loads_keys=['display_format', 'subtotal_col_formula_expression'],
            pop_keys=['chart_params_jump', 'dashboard_jump_config']
        )
        # 老的数据增加 subtotal_formula_mode
        for chart_num in chart_nums:
            if "subtotal_formula_mode" not in chart_num:
                chart_num["subtotal_formula_mode"] = ChartNumSubtotalFormulaMode.No.value
            if "subtotal_col_formula_mode" not in chart_num:
                chart_num["subtotal_col_formula_mode"] = ChartNumSubtotalFormulaMode.No.value
            if "subtotal_row_formula_mode" not in chart_num:
                chart_num["subtotal_row_formula_mode"] = ChartNumSubtotalFormulaMode.No.value

        chart_comparisons = metadata_service.pre_deal_with_section_data(chart_comparisons)
        chart_filters = metadata_service.pre_deal_with_section_data(chart_filters)
        chart_filters = metadata_service.tmp_deal_with_chart_filters(chart_filters)
        chart_zaxis = metadata_service.pre_deal_with_section_data(
            chart_zaxis, loads_keys=['display_format'], pop_keys=['chart_params_jump', 'dashboard_jump_config']
        )
        chart_params = metadata_service.pre_deal_with_section_data(chart_params)
        chart_desires = metadata_service.pre_deal_with_section_data(chart_desires, loads_keys=['display_format'])
        chart_marklines = metadata_service.pre_deal_with_section_data(chart_marklines)
        chart_vars = metadata_service.pre_deal_with_section_data(chart_vars)
        # get_keyword_detail_by_vars(chart_vars)
        # get_keyword_detail_special_flag_by_vars(chart_vars)
        chart_field_sorts = metadata_service.pre_deal_with_section_data(chart_field_sorts, loads_keys=['content'])

        # indicator
        chart_indicator_node = ReleasedDashboardChartIndicatorNodeModel(chart_id=chart_id, node_name='indicator')
        # dims
        chart_dims = ReleasedDashboardChartDimsNodeModel(chart_id=chart_id, node_data=chart_dims, node_name='dims')
        # nums
        chart_nums = ReleasedDashboardChartNumsNodeModel(chart_id=chart_id, node_data=chart_nums, node_name='nums')
        # comparisons
        chart_comparisons = ReleasedDashboardChartComparisonNodeModel(
            chart_id=chart_id, node_data=chart_comparisons, node_name='comparisons'
        )
        # filters
        chart_filters = ReleasedDashboardChartFiltersNodeModel(
            chart_id=chart_id, node_data=chart_filters, node_name='filters'
        )
        # zaxis
        chart_zaxis = ReleasedDashboardChartZaxisNodeModel(chart_id=chart_id, node_data=chart_zaxis, node_name='zaxis')
        # chart_params
        chart_params = ReleasedDashboardChartParamsNodeModel(
            chart_id=chart_id, node_data=chart_params, node_name='chart_params'
        )
        # desires
        chart_desires = ReleasedDashboardChartDesiresNodeModel(
            chart_id=chart_id, node_data=chart_desires, node_name='desires'
        )
        # marklines
        chart_marklines = ReleasedDashboardChartMarklinesNodeModel(
            chart_id=chart_id, node_data=chart_marklines, node_name='marklines'
        )
        # chart_vars
        chart_vars = ReleasedDashboardChartVarsNodeModel(
            chart_id=chart_id, node_data=chart_vars, node_name="chart_vars"
        )
        chart_field_sorts = ReleasedDashboardChartFieldSortsNodeModel(
            chart_id=chart_id, node_data=chart_field_sorts, node_name="field_sorts"
        )
        # 组装indicator
        chart_indicator_node.batch_add_sub_node(
            [
                chart_dims,
                chart_nums,
                chart_comparisons,
                chart_filters,
                chart_zaxis,
                chart_params,
                chart_desires,
                chart_marklines,
                chart_vars,
                chart_field_sorts,
            ]
        )

        # json格式
        chart_indicator_node.batch_loads_item_in_json(
            ['dims', 'nums', 'comparisons', 'filters', 'zaxis', 'chart_params', 'desires', 'marklines', 'field_sorts']
        )

        return chart_indicator_node

    @staticmethod
    def _assign_released_chart_data(released_chart_data_list):
        """
        补充数据
        :param released_chart_data_list: 已发布报告数组
        :return:
        """
        reassign_chart_data_list = []
        regroup_chart_data = {}
        if not released_chart_data_list:
            return reassign_chart_data_list, regroup_chart_data
        # 补充缺失的data_logic_type_code字段数据
        reassign_chart_data_list = metadata_service.batch_fill_data_logic_type_code(released_chart_data_list)
        # 获取重组后的单图数据
        regroup_chart_data = metadata_service.regroup_chart_data(released_chart_data_list)

        return reassign_chart_data_list, regroup_chart_data

    @staticmethod
    def get_dashboard_chart_cache_key(dashboard_id, chart_id):
        """
        获取key
        :param dashboard_id: 报告id
        :param chart_id: 单图id
        :return:
        """
        return "{}_{}_meta".format(dashboard_id, chart_id)

    @staticmethod
    def get_chart_ids_cache_key(dashboard_id):
        """
        获取key
        :param dashboard_id: 报告id
        :return:
        """
        return "{}_chart_id_list".format(dashboard_id)

    def deal_with_penetrates_data(self, single_released_chart_data: dict):
        """
        获取penetrates数据
        :param single_released_chart_data: 单个已发布报告的数据
        :return:
        """
        main_chart_id = single_released_chart_data.get('id', '')
        main_penetrate = single_released_chart_data.get('penetrate', 0)
        main_parent_id = single_released_chart_data.get('parent_id', '')
        penetrates_config_list = single_released_chart_data.get('penetrates', [])
        if not isinstance(penetrates_config_list, list):
            penetrates_config_list = json.loads(penetrates_config_list)
        if not main_penetrate or not len(penetrates_config_list):
            return
        self.penetrates_data.append({'parent_id': main_parent_id, 'chart_id': main_chart_id, 'relation': []})
        for item in penetrates_config_list:
            self.penetrates_data.append(
                {
                    'parent_id': item['parent_id'],
                    'chart_id': item['id'],
                    'relation': item.get('penetrate_relation') or [],
                    'penetrate_filter_relation': item.get('penetrate_filter_relation') or [],
                    'penetrate_var_filter_relation': item.get('penetrate_var_filter_relation') or [],
                }
            )

    @staticmethod
    def _get_dataset(dataset_id):
        from base import repository
        return repository.get_data('dataset', {'id': dataset_id}, ['external_type', 'type']) or {}

    def combine_released_single_chart(self, chart_id: str, single_released_chart_data: dict):
        """
        组合单个单图节点
        :param chart_id: 单图id
        :param single_released_chart_data: 单个已发布报告的数据
        :return:
        """
        # 获取chart主数据
        _fields = self.get_chart_data_fields()
        chart_main_data = metadata_service.get_field_data_by_fields(single_released_chart_data, _fields)
        metadata_service._loads_key_for_section_data(['layout_extend'], chart_main_data)
        # 组合单个单图
        single_chart = ReleasedDashboardSingleChartNodeModel(
            chart_id=chart_id, node_name='single_chart', orig_data=chart_main_data
        )
        chart_node_data = single_chart.get_data()
        single_chart.batch_move_item_to_dict('funcSetup', ['display_item', 'refresh_rate'])
        default_value = chart_node_data.get('default_value', '')
        datasoure_id = chart_node_data.get('source', '')
        source_type = chart_node_data.get('source_type', '')
        logic_type = chart_node_data.get('data_logic_type_code', '')
        filter_relation = chart_node_data.get('filter_relation', '')
        aggregation = chart_node_data.get("aggregation", 1)
        pre_comparison = chart_node_data.get("pre_comparison", 1)
        reset_field_sort = chart_node_data.get("reset_field_sort", 1)
        chart_default_data = chart_node_data.get("chart_default_value", [])
        external_subject_id = chart_node_data.get("external_subject_id")
        is_highdata = chart_node_data.get("is_highdata")
        chart_default_data = json.loads(chart_default_data) if chart_default_data else []
        dataset = self._get_dataset(datasoure_id)
        external_type = dataset.get('external_type')
        if external_type:
            source_type = dataset.get('type')
        single_chart.batch_remove_item(
            [
                'default_value',
                'source',
                'source_type',
                'data_logic_type_code',
                'filter_relation',
                'aggregation',
                'pre_comparison',
                'reset_field_sort',
                'chart_default_value',
            ]
        )

        # 组合indicator
        single_chart_indicator = self.combine_released_chart_indicators(single_released_chart_data)

        # 组合chart_data
        single_chart_data = DefaultNodeModel(chart_id=chart_id, node_name='data', node_data=dict())
        subtotal_node = DefaultNodeModel(
            node_name='enable_subtotal', node_data=chart_main_data.get('enable_subtotal', 0)
        )
        summary_node = DefaultNodeModel(node_name='enable_summary', node_data=chart_main_data.get('enable_summary', 0))
        enable_subtotal_col_node = DefaultNodeModel(
            node_name='enable_subtotal_col', node_data=chart_main_data.get('enable_subtotal_col', 0)
        )
        enable_subtotal_col_summary_node = DefaultNodeModel(
            node_name='enable_subtotal_col_summary', node_data=chart_main_data.get('enable_subtotal_col_summary', 0)
        )
        enable_subtotal_row_node = DefaultNodeModel(
            node_name='enable_subtotal_row', node_data=chart_main_data.get('enable_subtotal_row', 0)
        )
        enable_subtotal_row_summary_node = DefaultNodeModel(
            node_name='enable_subtotal_row_summary', node_data=chart_main_data.get('enable_subtotal_row_summary', 0)
        )
        subtotal_row_summary_formula_mode_node = DefaultNodeModel(
            node_name='subtotal_row_summary_formula_mode',
            node_data=chart_main_data.get('subtotal_row_summary_formula_mode', ''),
        )
        default_value_node = DefaultNodeModel(node_name='default_value', node_data=default_value)
        chart_default_value_node = DefaultNodeModel(node_name="chart_default_value", node_data=chart_default_data)

        datasoure_node = DefaultNodeModel(node_name='datasource', node_data={
                'id': datasoure_id, 'type': source_type, 'external_type': external_type})
        data_type_node = DefaultNodeModel(node_name='data_type', node_data={'logic_type': logic_type})
        filter_relation_node = DefaultNodeModel(node_name='filter_relation', node_data=filter_relation)
        aggregation_node = DefaultNodeModel(node_name='aggregation', node_data=aggregation)
        pre_comparison_node = DefaultNodeModel(node_name='pre_comparison', node_data=pre_comparison)
        reset_field_sort_node = DefaultNodeModel(node_name='reset_field_sort', node_data=reset_field_sort)
        is_highdata_node = DefaultNodeModel(node_name='is_highdata', node_data=is_highdata)
        external_subject_node = DefaultNodeModel(node_name='external_subject_id', node_data=external_subject_id)
        single_chart_data.batch_add_sub_node(
            [
                subtotal_node,
                summary_node,
                enable_subtotal_col_node,
                enable_subtotal_col_summary_node,
                enable_subtotal_row_node,
                enable_subtotal_row_summary_node,
                subtotal_row_summary_formula_mode_node,
                single_chart_indicator,
                default_value_node,
                chart_default_value_node,
                datasoure_node,
                data_type_node,
                filter_relation_node,
                aggregation_node,
                pre_comparison_node,
                reset_field_sort_node,
                external_subject_node,
                is_highdata_node,
            ]
        )
        single_chart.add_sub_node(single_chart_data)
        return single_chart

    def _set_dashboard_chart_cache(self, cache_instance, operate_dashboard_id, chart_data_list):
        """
        设置chart缓存
        :param cache_instance: 缓存实例对象
        :param operate_dashboard_id: 实际需要组装为节点的报告id
        :param chart_data_list: 已发布报告中的单图数组
        :return:
        """
        chart_data_cache_dict = dict()
        chart_ids_cache_list = list()
        chart_source_cache_dict = dict()
        if not chart_data_list:
            return
        for single_chart in chart_data_list:
            single_chart_id = single_chart.get('id')
            source = single_chart.get('source')
            dashboard_chart_cache_key = self.get_dashboard_chart_cache_key(operate_dashboard_id, single_chart_id)
            chart_data_cache_dict.update({dashboard_chart_cache_key: single_chart})
            chart_ids_cache_list.append(single_chart_id)
            if source and source not in list(chart_source_cache_dict.keys()):
                chart_source_cache_dict.update({source: ''})
        # 批量设置cache数据
        if self.use_meta_cache:
            chart_ids_cache_key = self.get_chart_ids_cache_key(operate_dashboard_id)
            chart_data_cache_dict.update(
                {
                    chart_ids_cache_key: chart_ids_cache_list,
                    cache_instance.prop_chart_source_dict: chart_source_cache_dict,
                }
            )
            cache_instance.set_mprop(chart_data_cache_dict, expire_flag=True)

    def _get_screens_cache(self, released_dashboard_data_list, cache_instance):
        for_set_dict = dict()
        screens_cache_list = list()
        for single_dashboard in released_dashboard_data_list:
            modified_on = single_dashboard.get('modified_on')
            if modified_on:
                single_dashboard['modified_on'] = self.time_to_str(modified_on)

            if single_dashboard.get('data_type', 0) == 0:
                for_set_dict.update({DashboardCache.prop_info: single_dashboard})
            else:
                # 获取单个报告的单图数据
                operate_dashboard_id = single_dashboard.get('id')
                released_chart_data_list = metadata_service.get_released_chart_data(
                    self.snapshot_id, operate_dashboard_id
                )
                self._set_dashboard_chart_cache(cache_instance, operate_dashboard_id, released_chart_data_list)
                screens_cache_list.append(single_dashboard)
        return screens_cache_list, for_set_dict

    def _get_released_dashboard_data_by_snapshot_id(self):
        """
        1，获取缓存快照报告数据
        2，设置缓存数据
        :return:
        """
        # 报告发布流程过来的，直接在入参连接实例conn下查询表数据
        if self.conn:
            released_dashboard_data_list = metadata_service.batch_get_released_dashbaoard_data_with_conn(
                self.snapshot_id, conn=self.conn
            )
            return released_dashboard_data_list

        # 校验报告分发时间戳
        check_deliver_flag = metadata_service.check_deliver_timestamp(self.snapshot_id)

        screens_detail_cache_data = None
        cache_instance = DashboardCache(g.code, self.snapshot_id, conn_redis())
        # 先尝试获取快照id下的cache
        if check_deliver_flag and self.use_meta_cache:
            info_cache_data = cache_instance.get_prop(cache_instance.prop_info)
            screens_detail_cache_data = cache_instance.get_prop(cache_instance.prop_screens_detail)
            if info_cache_data and isinstance(screens_detail_cache_data, list):
                screens_detail_cache_data.append(info_cache_data)
                return screens_detail_cache_data

        # 没有cache则查表
        if not screens_detail_cache_data:
            released_dashboard_data_list = metadata_service.batch_get_released_dashbaoard_data(self.snapshot_id)
            screens_cache_list, for_set_dict = self._get_screens_cache(released_dashboard_data_list, cache_instance)
            # 设置screens
            for_set_dict.update({DashboardCache.prop_screens_detail: screens_cache_list})

            # 设置chart_code_list
            chart_code_list = metadata_service.get_chart_code_by_snapshot_id(self.snapshot_id)
            for_set_dict.update({DashboardCache.prop_chart_code_list: chart_code_list})

            # 批量设置key缓存
            if self.use_meta_cache:
                cache_instance.set_mprop(for_set_dict, expire_flag=True)
            return released_dashboard_data_list
        return screens_detail_cache_data

    def _get_released_chart_data_by_dashboard_id(self, operate_dashboard_id):
        """
        获取缓存单图数据
        :param operate_dashboard_id: 报告id
        :return:
        """
        # 报告发布流程过来的，直接在入参连接实例conn下查询表数据
        if self.conn:
            released_chart_data_list = metadata_service.get_released_chart_data_with_conn(
                self.snapshot_id, operate_dashboard_id, conn=self.conn
            )
            return released_chart_data_list

        # 先尝试获取报告id下的cache
        cache_instance = None
        released_chart_data_list = list()
        if self.use_meta_cache:
            chart_ids_cache_key = self.get_chart_ids_cache_key(operate_dashboard_id)
            cache_instance = DashboardCache(g.code, self.snapshot_id, conn_redis())
            chart_ids_cache_data = cache_instance.get_prop(chart_ids_cache_key)
            if chart_ids_cache_data:
                released_chart_data_list = cache_instance.batch_get_charts(operate_dashboard_id, chart_ids_cache_data)

        # 没有cache数据则需要查表
        if not released_chart_data_list:
            released_chart_data_list = metadata_service.get_released_chart_data(self.snapshot_id, operate_dashboard_id)
            self._set_dashboard_chart_cache(cache_instance, operate_dashboard_id, released_chart_data_list)
        return released_chart_data_list

    def _extract_dict_element_from_nested(self, datas, result: list=[]):
        # 从嵌套数据结构中获取字典元素
        if isinstance(datas, list):
            for data in datas:
                self._extract_dict_element_from_nested(data, result)
        elif isinstance(datas, dict):
            result.append(datas)

    def _append_released_chart_redirect_target_global_params(self, regrouped_chart_data):
        # 提前获取组件跳转的全局参数信息
        RedirectRelationKeys = metadata_service.RedirectRelationKeys
        # 1. 提取所有的除开参数跳转以外的所有跳转信息
        redirects_relations = []
        self._extract_dict_element_from_nested(regrouped_chart_data.get('redirects') or [], result=redirects_relations)
        except_params_jump_relations = [
            RedirectRelationKeys.get_except_params_jump_relation(relation) for relation in redirects_relations
        ]
        all_single_relation = []
        self._extract_dict_element_from_nested(except_params_jump_relations, result=all_single_relation)
        # 2. 参数跳转是单独的, 单独处理
        params_jump_relations = regrouped_chart_data.get(RedirectRelationKeys.chart_params_key) or []
        self._extract_dict_element_from_nested(params_jump_relations, result=all_single_relation)

        # 3. 提取所有的跳转信息里面的全局参数id
        all_global_params_ids = {
            relation.get('global_params_id') for relation in all_single_relation if relation.get('global_params_id')
        }
        global_params = metadata_service.batch_get_global_params(list(all_global_params_ids))
        global_params = self._format_grouped_global_params(global_params)

        # 4. 给每个跳转关系添加上这个目标报告的全局参数信息
        for relation in all_single_relation:
            chart_global_params_id = relation.get('global_params_id')
            relation['target_global_params'] = global_params.get(chart_global_params_id) or dict()

        return regrouped_chart_data

    def _format_grouped_global_params(self, global_params: list):
        result = {}
        for global_param in global_params:
            result[global_param.get('id')] = global_param
        return result

    def _get_single_released_dashboard_data(self, operate_dashboard_id):
        """
        获取缓存单个报告info数据
        :param operate_dashboard_id: 报告id
        :return:
        """
        single_released_dashboard_data = dict()
        # 先尝试获取报告id下的cache
        if self.use_meta_cache:
            cache_instance = DashboardCache(g.code, self.snapshot_id, conn_redis())
            screens_cache_data = cache_instance.get_prop(cache_instance.prop_screens_detail)
            if screens_cache_data:
                for single_screen in screens_cache_data:
                    if single_screen.get('id') == operate_dashboard_id:
                        single_released_dashboard_data = single_screen

        # 没有cache数据则需要查表
        if not single_released_dashboard_data:
            single_released_dashboard_data = metadata_service.get_released_dashbaoard_data(
                self.snapshot_id, operate_dashboard_id
            )
        return single_released_dashboard_data

    def combine_chart_relation_metadata(self, regrouped_chart_data, orig_linkages_data):
        """
        组装单图关系数据
        :param regrouped_chart_data: 重构后的单图数据
        :param orig_linkages_data: 原始的单图联动数据
        :return:
        """
        chart_relations = DefaultNodeModel(node_name='chart_relations', node_data=dict())
        snapshot_chart_dict = regrouped_chart_data.get('snapshot_chart_dict')
        orig_component_filters_data = regrouped_chart_data.get('component_filters')
        orig_redirects_data = regrouped_chart_data.get('redirects')
        orig_chart_params_jump = regrouped_chart_data.get('chart_params_jump')
        chart_visible_triggers = regrouped_chart_data.get('chart_visible_triggers') or []
        # orig_chart_params_jump_dict = metadata_service.turn_chart_params_jump_list_to_dict(orig_chart_params_jump)
        orig_chart_params_jump_dict = orig_chart_params_jump

        # component_filters
        component_filters_data = metadata_service.get_release_structure_filter_data(
            orig_component_filters_data, self.snapshot_id, snapshot_chart_dict
        )
        filters_node = ReleasedDashboardChartFilterNodeModel(node_name='filters', node_data=component_filters_data)

        # linkages
        linkages_data = metadata_service.get_release_structure_linkages_data(orig_linkages_data, snapshot_chart_dict)
        linkages_node = ReleasedDashboardChartLinkageNodeModel(node_name='linkages', node_data=linkages_data)

        # 新版联动和筛选
        logger.debug(
            "chart_linkages:%s, chart_filters:%s",
            regrouped_chart_data.get("chart_linkage"),
            regrouped_chart_data.get("chart_filter"),
        )
        new_linkage_node = DefaultNodeModel(
            node_name="chart_linkages", node_data=regrouped_chart_data.get("chart_linkage")
        )

        # new filters
        new_filter_data = metadata_service.fillup_fields_in_list(
            regrouped_chart_data.get("chart_filter"),
            {
                "filter_type": 0,
                "available": 1,
                "initiator_source": ChartFilterInitiatorSouce.Datasetfield.value,
                "fixed_value_data": None,
            },
        )
        for new_filter in new_filter_data:
            if new_filter.get('indicator_dim_obj', ''):
                new_filter['indicator_dim_obj'] = json.loads(new_filter['indicator_dim_obj'])
        new_filter_node = DefaultNodeModel(node_name="chart_filters", node_data=new_filter_data)

        # redirects
        redirects_data = metadata_service.get_release_structure_redirects_data(
            orig_redirects_data, orig_chart_params_jump_dict
        )
        redirects_node = ReleasedDashboardChartRedirectNodeModel(node_name='redirects', node_data=redirects_data)

        # 变量关系
        var_relations_data = metadata_service.fillup_fields_in_list(
            regrouped_chart_data.get("var_relations"), {"initiator_type": 0}
        )
        for var_relation in var_relations_data:
            if var_relation.get('var_dim_obj', ''):
                var_relation['var_dim_obj'] = json.loads(var_relation['var_dim_obj'])
        var_relations_node = DefaultNodeModel(node_name="var_relations", node_data=var_relations_data)
        chart_visible_triggers = DefaultNodeModel(node_name="chart_visible_triggers", node_data=chart_visible_triggers)
        chart_visible_triggers.batch_loads_list_value_item_in_json(['conditions', 'actions'])

        chart_relations.batch_add_sub_node(
            [linkages_node, filters_node, redirects_node, new_linkage_node,
             new_filter_node, var_relations_node, chart_visible_triggers]
        )
        return chart_relations

    def combine_dashboard_metadata(
        self, operate_dashboard_id: str, operate_snapshot_id=None, single_released_dashboard=None
    ):
        """
        组合报告元数据
        [标记] 运行时获取返回元数据入口
        :param operate_dashboard_id: 报告id
        :param operate_snapshot_id: 快照id
        :param single_released_dashboard: 单个已发布报告的数据
        :return:
        """
        self.snapshot_id = operate_snapshot_id if operate_snapshot_id else self.snapshot_id

        # 如果没有单个报告数据，则提前获取
        if not single_released_dashboard:
            single_released_dashboard = self._get_single_released_dashboard_data(operate_dashboard_id)

        # dashboard主节点
        dashboard = ReleasedDashboardDataNodeModel(
            snapshot_id=self.snapshot_id,
            dashboard_id=operate_dashboard_id,
            node_name='dashboard',
            orig_data=single_released_dashboard,
        )
        # 这里获取数据，避免查询多次
        # 赋值被关联数据集字段对应的数据集id
        dashboard_filters = proxy_dataset_service.fillup_dashboard_filter_related_dataset(dashboard.dashboard_filters)
        orig_linkages_data = dashboard.selectors

        # global_params
        global_params = ReleasedDashboardGlobalParamsNodeModel(
            dashboard_id=operate_dashboard_id, node_name='global_params', node_data=dashboard.global_params
        )
        # dashbaord_filters
        dashboard_filter = ReleasedDashboardFilterNodeModel(
            dashboard_id=operate_dashboard_id, node_name='dashboard_filters', node_data=dashboard_filters
        )

        # dashboard_var_filters
        dashboard_var_filters = json.loads(dashboard.dashboard_var_filters) if dashboard.dashboard_var_filters else dashboard.dashboard_var_filters
        dashboard_var_filter = ReleasedDashboardVarFilterNodeModel(
            dashboard_id=operate_dashboard_id, node_name='dashboard_var_filters', node_data=dashboard_var_filters
        )

        # var_value_sources
        var_value_sources = DefaultNodeModel(
            dashboard_id=operate_dashboard_id, node_name='var_value_sources', node_data=dashboard.var_value_sources
        )

        # 已发布的chart配置数据
        released_chart_data_list = self._get_released_chart_data_by_dashboard_id(operate_dashboard_id)
        # 组装数据
        released_chart_data_list, regrouped_chart_data = self._assign_released_chart_data(released_chart_data_list)
        # 添加组件跳转的报告全局参数信息
        self._append_released_chart_redirect_target_global_params(regrouped_chart_data)

        # charts
        charts = DefaultNodeModel(dashboard_id=operate_dashboard_id, node_name='charts', node_data=list())

        release_chart_id_list = [
            single_released_chart_data.get('id', '') for single_released_chart_data in released_chart_data_list
        ]
        parent_children_map = metadata_service.get_release_parent_children_map(release_chart_id_list, self.snapshot_id)
        self.filling_var_keyword_value(released_chart_data_list)

        # test_flag = str(getattr(g, 'request_data', {}).get('params', {}).get('use_test', '0'))
        # if test_flag == '1':
        #     sss_key = f'test_chart_data_123456:{operate_dashboard_id}'
        #     sss_timeout = 600
        #     sss_conn = conn_redis()
        #     data = sss_conn.get(sss_key)
        #     if isinstance(data, bytes):
        #         data = data.decode()
        #     if not data:
        #         new_data = self.get_test_chart_data_cache(released_chart_data_list, parent_children_map, charts)
        #         sss_conn.set(sss_key, json.dumps(new_data, ensure_ascii=False), sss_timeout)
        #     else:
        #         new_data = json.loads(data)
        #     charts.node_data = new_data
        # else:
        for single_released_chart_data in released_chart_data_list:
            chart_id = single_released_chart_data.get('id', '')
            if not chart_id:
                continue
            single_released_chart_data['children_chart_ids'] = parent_children_map.get(chart_id, [])
            self.set_fixed_manual_value(single_released_chart_data)
            single_chart = self.combine_released_single_chart(chart_id, single_released_chart_data)
            charts.batch_add_item_in_list(single_chart.get_data())
            # 处理penetrates
            self.deal_with_penetrates_data(single_released_chart_data)

        # 穿透数据另外组装
        penetrates = ReleasedDashboardChartPenetratesNodeModel(node_name='penetrates', node_data=self.penetrates_data)

        # 组装chart_relations数据
        chart_relations = self.combine_chart_relation_metadata(regrouped_chart_data, orig_linkages_data)

        chart_relations.batch_add_sub_node([penetrates])
        dashboard.batch_add_sub_node([global_params, dashboard_filter, dashboard_var_filter, var_value_sources, chart_relations, charts])
        return dashboard.get_data()

    # def get_test_chart_data_cache(self, released_chart_data_list, parent_children_map, charts):
    #     for single_released_chart_data in released_chart_data_list:
    #         chart_id = single_released_chart_data.get('id', '')
    #         if not chart_id:
    #             continue
    #         single_released_chart_data['children_chart_ids'] = parent_children_map.get(chart_id, [])
    #         self.set_fixed_manual_value(single_released_chart_data)
    #         single_chart = self.combine_released_single_chart(chart_id, single_released_chart_data)
    #         charts.batch_add_item_in_list(single_chart.get_data())
    #         # 处理penetrates
    #         self.deal_with_penetrates_data(single_released_chart_data)
    #     return charts.node_data

    def filling_var_keyword_value(self, released_chart_data_list):
        # 填充变量引用的关键字的真实值
        all_chart_vars = []
        for chart in released_chart_data_list:
            chart_vars = metadata_service.pre_deal_with_section_data(chart.get('chart_vars')) or []
            chart['chart_vars'] = chart_vars
            all_chart_vars.extend(chart_vars)
        get_keyword_detail_special_flag_by_vars(all_chart_vars)

    @staticmethod
    def set_fixed_manual_value(single_released_chart_data):
        try:
            fixed_manual_value = json.loads(single_released_chart_data.get('fixed_manual_value'))
        except Exception:
            fixed_manual_value = []
            single_released_chart_data['fixed_manual_value'] = fixed_manual_value
        if isinstance(fixed_manual_value, list):
            try:
                display_item = json.loads(single_released_chart_data.get('display_item'))
            except Exception:
                display_item = {}
            top_head = display_item.get('top_head')
            top_tail = display_item.get('top_tail')
            if top_head and is_number(top_head):
                fixed_manual_value = fixed_manual_value[: int(top_head)]
            elif top_tail and is_number(top_tail):
                fixed_manual_value = fixed_manual_value[-int(top_tail) :]
        single_released_chart_data['fixed_manual_value'] = fixed_manual_value

    def combine_screen_metadata(self, token_data=None):
        """
        发布元数据主方法-用于获取多屏元数据
        ps：只返回第一个报告的详细元数据，其他的只返回id信息
        :return:
        """
        main_released_data = DefaultNodeModel(node_name='main_released_data', node_data=dict())
        screens = DefaultNodeModel(node_name='screens', node_data=list())
        # 第一个报告
        have_first_report_flag = False
        first_report = DefaultNodeModel(node_name='first_report', node_data=dict())

        released_dashboard_data_list = self._get_released_dashboard_data_by_snapshot_id()
        if token_data and token_data.get("screen_id"):
            auths = get_third_all_auth_cache(token_data.get("session_id", "")) if token_data.get("session_id") else {}
        else:
            auths = {}
        # 添加多屏下的各个报告
        for single_released_dashboard in released_dashboard_data_list:
            snapshot_id = single_released_dashboard.get('snapshot_id', '')
            dashboard_id = single_released_dashboard.get('id', '')
            data_type = single_released_dashboard.get('data_type', 0)

            if data_type == 0:
                dashboard = ReleasedDashboardDataNodeModel(
                    node_name='dashboard',
                    snapshot_id=snapshot_id,
                    dashboard_id=dashboard_id,
                    orig_data=single_released_dashboard,
                )
                main_released_data.add_sub_node(dashboard)
            else:
                # 第三方多屏单点登录需要剔除没有权限的报告
                if token_data and token_data.get("screen_id") and "view" not in auths.get(dashboard_id, ""):
                    continue
                # 为了兼容前端调用接口使用，dashboard_id和screen_id意义互换
                single_dashboard_dict = dict()
                single_dashboard_dict['dashboard_id'] = dashboard_id
                single_dashboard_dict['snapshot_id'] = snapshot_id
                single_dashboard_dict['screen_id'] = snapshot_id
                screens.batch_add_item_in_list(single_dashboard_dict)
                # 只获取第一个报告的数据
                if have_first_report_flag:
                    continue
                single_screen_data = self.combine_dashboard_metadata(
                    operate_dashboard_id=dashboard_id, single_released_dashboard=single_released_dashboard
                )
                first_report.node_data = single_screen_data
                have_first_report_flag = True

        # 组合元数据节点
        main_released_data.batch_add_sub_node([screens, first_report])
        return main_released_data
