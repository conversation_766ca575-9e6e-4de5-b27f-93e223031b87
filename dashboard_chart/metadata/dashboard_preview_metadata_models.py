#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/22

"""
元数据节点类
"""

# ---------------- 标准模块 ----------------
import json
import logging
from collections import defaultdict

# ---------------- 业务模块 ----------------
from base.errors import DataNotExistError
from dashboard_chart.services import components_service, metadata_service, dashboard_service
from dashboard_chart.services import proxy_dataset_service as dataset_service
from dashboard_chart.repositories import metadata_repository, chart_repository
from dashboard_chart.metadata.common_metadata_model import MetadataNodeBaseModel
from base.enums import ChartPenetrateRelationType, DashboardType

logger = logging.getLogger(__name__)


class DefaultNodeModel(MetadataNodeBaseModel):
    """
    默认节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.set_default_data_flag = False
        kwargs['set_default_data_flag'] = self.set_default_data_flag
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """


class DashboardDataNodeModel(MetadataNodeBaseModel):
    """
    dashboard数据节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.dashboard_filters = None
        self.selectors = None
        self.penetrates = None
        self.orig_data = None
        self.default_node_data = dict()
        self.rank = 0
        self.main_external_subject_id = None
        self.application_type = 0
        self.external_subject_ids = []
        self.dataset_id = ""
        self.analysis_type = None
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def _modify_data_structure(self):
        """
        调整数据
        :return:
        """
        # 转换为正常数据格式
        self.batch_loads_item_in_json(['layout', 'background', 'dashboard_filters', 'grid_padding'])
        # 处理历史数据问题
        self._check_node_data()
        self.batch_add_item_in_dict({'attrs': {}})
        modified_on = '' if not self.node_data.get('modified_on') else str(self.node_data.get('modified_on'))
        self.node_data['modified_on'] = self.time_to_str(modified_on)
        self.update_key_name(old_key='modified_on', new_key='released_on')
        new_layout_type = self.node_data.get('new_layout_type')
        self.node_data['new_layout_type'] = 'grid' if new_layout_type == 1 else 'free'
        self.node_data['create_type'] = self.node_data.get('create_type', 0)
        self.batch_move_item_to_dict('styles', ['theme', 'background', 'attrs', 'grid_padding'])
        self.batch_move_item_to_dict(
            'publish', ['status', 'url', 'share_secret_key', 'type_access_released', 'released_on']
        )
        self.move_key(operate_key='platform', aim_key='layout')
        self.dashboard_filters = self.node_data.get('dashboard_filters', [])
        self.selectors = self.node_data.get('selectors', [])
        self.penetrates = self.node_data.get('penetrates', [])
        self.rank = self.node_data.get('rank')
        self.main_external_subject_id = self.node_data.get('main_external_subject_id', None)
        # convert external_subject_ids str -> list
        external_subject_ids = self.node_data.get('external_subject_ids', None)
        self.external_subject_ids = external_subject_ids.split(',') if external_subject_ids else external_subject_ids
        # 处理历史数据
        if self.main_external_subject_id and not self.external_subject_ids:
            self.external_subject_ids = [self.main_external_subject_id]
        self.node_data['external_subject_ids'] = self.external_subject_ids

        self.application_type = self.node_data.get('application_type', 0)
        self.dataset_id = self.node_data.get('dataset_id', "")

        # 编辑页元数据需要返回父报告的报告名称
        self.node_data['parent_name'] = ''
        parent_id = self.node_data.get('parent_id')
        dashboard_type = self.node_data.get('type')
        if parent_id and self.application_type == 1 and dashboard_type == DashboardType.CHILD_FILE.value:
            parent_dashboard = metadata_repository.get_dashboard_name_by_id(parent_id)
            parent_name = parent_dashboard.get('name') if parent_dashboard else ''
            self.node_data['parent_name'] = parent_name
        if self.application_type == 1 and self.main_external_subject_id:
            external_subject = metadata_repository.get_external_subject_name_by_id(self.main_external_subject_id)
            external_subject_name = external_subject.get('name') if external_subject else ''
            self.node_data['main_external_subject_name'] = external_subject_name
        self.batch_remove_item(operate_keys=['dashboard_filters', 'selectors', 'penetrates'])

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            self.node_data = self.orig_data
        else:
            if not self.dashboard_id:
                raise DataNotExistError(message='报告ID为空')
            self.node_data = metadata_repository.get_dashboard_data_by_id(self.dashboard_id)
        if not self.node_data:
            raise DataNotExistError(message='报告节点数据为空')
        self._modify_data_structure()
        return self.node_data

    def _check_node_data(self):
        # 给background和layout中增加默认节点,处理历史数据问题
        if self.node_data['background'] and 'user_image' not in self.node_data['background']:
            self.node_data['background']['user_image'] = ''
        if self.node_data['layout'] and 'slider_top' not in self.node_data['layout']:
            self.node_data['layout']['slider_top'] = 0


class DashboardVarFilterNodeModel(MetadataNodeBaseModel):
    """
    报告筛选数据节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        dashboard_filter_relations = metadata_service.get_dashboard_var_filter_relations_by_id(self.dashboard_id)
        self.node_data = dataset_service.get_dashboard_var_filters(dashboard_filter_relations)
        return self.node_data


class DashboardFilterNodeModel(MetadataNodeBaseModel):
    """
    报告筛选数据节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        dashboard_filters = metadata_service.get_dashboard_filters_by_id(self.dashboard_id)
        dashboard_filter_relations = metadata_service.get_dashboard_filter_relations_by_id(self.dashboard_id)
        self.node_data = dataset_service.get_dashboard_filters(dashboard_filters, dashboard_filter_relations)
        return self.node_data


class DashboardGlobalParamsNodeModel(MetadataNodeBaseModel):
    """
    报告全局参数节点
    """

    def  __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        self.node_data = dashboard_service.get_dashboard_global_params(self.dashboard_id)
        return self.node_data


class DashboardValueSourceNodeModel(MetadataNodeBaseModel):
    """
    取值来源数据节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        self.node_data = metadata_service.get_dashboard_value_sources_by_id(self.dashboard_id)
        return self.node_data


class DashboardChartFilterNodeModel(MetadataNodeBaseModel):
    """
    报告单图筛选节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if not self.dashboard_id:
            raise DataNotExistError(message='报告ID为空')
        # 数据升级后老的组件筛选已不存在(为保证数据升级能够重复执行，因此老的数据并未删除)
        # query_data = metadata_repository.get_filters_by_dashboard_id(self.dashboard_id)
        # chart_relation_tree_data = metadata_service.get_chart_relation_tree_data(query_data)
        chart_relation_tree_data = dict()
        self.node_data = metadata_service.get_structure_component_filter_data(chart_relation_tree_data)
        return self.node_data


class DashboardChartLinkageNodeModel(MetadataNodeBaseModel):
    """
    报告单图联动节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        # 老的联动已经去掉(为保证数据升级重复执行，此处老数据并未删除，因此此处即使有数据也不返回)
        # query_data = metadata_repository.get_linkages_by_dashboard_id(self.dashboard_id)
        # chart_relation_tree_data = metadata_service.get_chart_relation_tree_data(query_data)
        chart_relation_tree_data = dict()
        self.node_data = metadata_service.get_structure_linkages_data(chart_relation_tree_data)
        return self.node_data


class NewChartLinkageNodeModel(MetadataNodeBaseModel):
    """
    单图联动关系节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs["default_node_data"] = self.default_node_data
        super().__init__(**kwargs)

    def init_data(self):
        query_data = metadata_repository.get_new_linkage_by_dashboard_id(self.dashboard_id)
        self.node_data = list(metadata_service.convert_relation_to_dict(query_data).values())
        logger.debug("new chart linkage node info:%s, %s", self.node_data, type(self.node_data))
        return self.node_data


class NewChartFilterNodeModel(MetadataNodeBaseModel):
    """
    单图筛选关系节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs["default_node_data"] = self.default_node_data
        super().__init__(**kwargs)

    def init_data(self):
        query_data = metadata_repository.get_new_filter_by_dashboard_id(self.dashboard_id)
        self.node_data = list(metadata_service.convert_relation_to_dict(query_data, 'chart_filter').values())
        logger.debug("new chart Filter node info:%s,%s", self.node_data, query_data)
        return self.node_data


class ChartVisibilityTriggersNodeModel(MetadataNodeBaseModel):
    """
    组件触发事件
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs["default_node_data"] = self.default_node_data
        super().__init__(**kwargs)

    def init_data(self):
        query_data = metadata_repository.get_chart_visible_triggers_by_dashboard_id(self.dashboard_id)
        for data in query_data:
            self.node_data = data
            self.batch_loads_item_in_json(['conditions', 'actions'])
        self.node_data = query_data
        return self.node_data


class VarRelationNodeModel(MetadataNodeBaseModel):
    """
    变量关系节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs["default_node_data"] = self.default_node_data
        super().__init__(**kwargs)

    def init_data(self):
        query_data = metadata_repository.get_var_relation_by_dashboard_id(self.dashboard_id)
        # 我已不知道为啥要这么搞， 前端让加的
        # for data in query_data:
            # if not data['field_initiator_id']:
            #     data['field_initiator_id'] = None
        for data in query_data:
            if data['var_dim_obj']:
                data['var_dim_obj'] = json.loads(data['var_dim_obj'])
        self.node_data = query_data
        logger.debug("var relation node info:%s,%s", self.dashboard_id, query_data)
        return self.node_data


class DashboardChartRedirectNodeModel(MetadataNodeBaseModel):
    """
    报告单图跳转节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        self.node_data = metadata_service.get_structure_redirect_data(self.dashboard_id)
        return self.node_data


class DashboardChartPenetratesNodeModel(MetadataNodeBaseModel):
    """
    报告单图穿透节点
    """

    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        self.node_data = ''
        orig_penetrates_data = metadata_repository.get_penetrates_by_dashboard_id(self.dashboard_id)
        if orig_penetrates_data and len(orig_penetrates_data) > 1:
            chart_ids = [chart['chart_id'] for chart in orig_penetrates_data]
            relations = chart_repository.get_penetrate_relation_by_chart_ids(chart_ids)
            chart_id_penetrate_relation = defaultdict(list)
            chart_id_penetrate_filter_relation = defaultdict(list)
            chart_id_penetrate_var_filter_relation = defaultdict(list)
            if relations:
                for relation in relations:
                    item = {
                        'id': relation['id'],
                        'parent_chart_field_id': relation['parent_chart_field_id'],
                        'child_chart_field_id': relation['child_chart_field_id'],
                        "type": relation['type'],
                    }
                    # type为1是筛选联动配置数据
                    if relation.get('type') == ChartPenetrateRelationType.PenetrateFilterType.value:
                        chart_id_penetrate_filter_relation[relation['dashboard_chart_id']].append(item)
                    elif relation.get('type') == ChartPenetrateRelationType.VarPenetrateFilterType.value:
                        item['parent_chart_var_id'] = relation.get('parent_chart_var_id')
                        chart_id_penetrate_var_filter_relation[relation['dashboard_chart_id']].append(item)
                    else:
                        chart_id_penetrate_relation[relation['dashboard_chart_id']].append(item)
            for penetrates_data in orig_penetrates_data:
                penetrates_data['relation'] = chart_id_penetrate_relation[penetrates_data['chart_id']]
                penetrates_data['penetrate_filter_relation'] = chart_id_penetrate_filter_relation[
                    penetrates_data['chart_id']
                ]
                penetrates_data['penetrate_var_filter_relation'] = chart_id_penetrate_var_filter_relation[
                    penetrates_data['chart_id']
                ]
            self.node_data = orig_penetrates_data
        return self.node_data


class DashboardSingleChartNodeModel(MetadataNodeBaseModel):
    """
    报告单个单图节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = dict()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            chart_data = self.orig_data
        else:
            chart_data = metadata_service.get_single_chart_by_id(self.chart_id)
        if not chart_data:
            return self.node_data
        dataset_id = chart_data.get('source', '')
        dataset_type = dataset_service.get_dataset_data_by_id(['type', 'external_type'], dataset_id)
        chart_data.update({'type': dataset_type.get('type', '') if dataset_type else ''})
        chart_data.update({'external_type': dataset_type.get('external_type', '') if dataset_type else ''})
        chart_data['config'] = '' if not chart_data.get('config') else chart_data.get('config')

        self.node_data = chart_data
        self.batch_loads_item_in_json(['position', 'export_type', 'layout_extend'])
        self.update_key_name('chart_code', 'chart_component_code')
        self.node_data['export_type'] = self.node_data.get('export_type') or []
        self.node_data['parent_chart_id'] = self.node_data.get('parent_chart_id') or ''
        self.node_data['children_chart_ids'] = self.node_data.get('children_chart_ids') or []
        return self.node_data


class DashboardSingleChartDefaultValue(MetadataNodeBaseModel):
    """
    重构后的筛选单图默认值
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.default_node_data = []
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        default_value = metadata_service.get_single_chart_default_value(self.chart_id)
        if default_value:
            self.node_data = default_value

        return self.node_data


class DashboardChartIndicatorNodeModel(MetadataNodeBaseModel):
    """
    报告单图关联条件节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = dict()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        return self.node_data or None


class DashboardChartDimsNodeModel(MetadataNodeBaseModel):
    """
    报告单图dims节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartNumsNodeModel(MetadataNodeBaseModel):
    """
    报告单图nums节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartComparisonNodeModel(MetadataNodeBaseModel):
    """
    报告单图comparison节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartFiltersNodeModel(MetadataNodeBaseModel):
    """
    报告单图filter节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartZaxisNodeModel(MetadataNodeBaseModel):
    """
    报告单图zaxis节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartParamsNodeModel(MetadataNodeBaseModel):
    """
    报告单图chart_params节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartDesiresNodeModel(MetadataNodeBaseModel):
    """
    报告单图desires节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartMarklinesNodeModel(MetadataNodeBaseModel):
    """
    报告单图marklines节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardChartFieldSortsNodeModel(MetadataNodeBaseModel):
    """
    报告单图field_sorts节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data


class DashboardInstalledComponentNodeModel(MetadataNodeBaseModel):
    """
    已安装组件节点
    """

    def __init__(self, **kwargs):
        self.default_node_data = list()
        self.runterminal = ""
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def get_installed_components(self):
        """
        获取已安装组件数据
        :return:
        """
        _, components = components_service.get_installed_components()
        components = components_service.get_terminal_components(self.runterminal, components)
        return components

    def init_data(self):
        """
        获取数据
        :return:
        """
        self.node_data = self.get_installed_components()
        return self.node_data


class DashboardChartVarsNodeModel(MetadataNodeBaseModel):
    """
    单图引用变量节点
    """

    def __init__(self, **kwargs):
        self.chart_id = None
        self.orig_data = None
        self.default_node_data = list()
        kwargs.update({'default_node_data': self.default_node_data})
        super().__init__(**kwargs)

    def init_data(self):
        """
        获取数据
        :return:
        """
        if self.orig_data:
            return self.orig_data
        return self.node_data
