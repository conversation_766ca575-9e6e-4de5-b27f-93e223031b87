#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
from dmplib.hug import g
from tests.base import BaseTest
import os
from dashboard_chart.services.download import download_service


os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)

import unittest
import logging

logger = logging.getLogger(__name__)


class TestDownloadTaskService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='fangzhiadmin_test', account='fangzhiadmin_test')

    # def test_generate_download_task(self):
    #     g.code = 'fangzhiadmin_test'
    #     g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
    #     g.cookie = {'token': 'eyJhbGciOiJIUzI1NiIsInR5cC'}
    #     kwargs = {
    #         "chart_params": [
    #             {
    #                 "id": "c9196e30-5f20-11e9-969d-370008c12c79",
    #                 "conditions": [],
    #                 "filter_conditions": [],
    #                 "dashboard_conditions": [],
    #                 "dashboard_id": "39ed21b1-6604-04ec-a61e-11f9665dbe61",
    #                 "chart_code": "pagination_table",
    #                 "data_logic_type_code": "",
    #                 "penetrate_conditions": [],
    #                 "chart_filter_conditions": [],
    #                 "chart_linkage_conditions": [],
    #             }
    #         ],
    #         "dashboard_id": "39ed21b1-6604-04ec-a61e-11f9665dbe61",
    #         "code": "test",
    #     }
    #     kwargs = {
    #         "chart_params": [
    #             {
    #                 "id": "4beba4e3-61b5-11e9-8468-c1ff997e6e5e",
    #                 "conditions": [],
    #                 "filter_conditions": [],
    #                 "dashboard_conditions": [],
    #                 "dashboard_id": "39ed41c4-d69b-cb66-dbc0-c911939b20e8",
    #                 "chart_code": "pagination_table",
    #                 "data_logic_type_code": "",
    #                 "penetrate_conditions": [],
    #                 "chart_filter_conditions": [],
    #                 "chart_linkage_conditions": [],
    #             }
    #         ],
    #         "dashboard_id": "39ed41c4-d69b-cb66-dbc0-c911939b20e8",
    #         "code": "fangzhiadmin_test",
    #     }
    #     kwargs = {
    #         "chart_params": [
    #             {
    #                 "id": "f3d2965a-838e-11e9-b153-c55b369f6c57",
    #                 "conditions": [],
    #                 "filter_conditions": [],
    #                 "dashboard_conditions": [],
    #                 "dashboard_id": "39ee1f94-9eea-0fbf-7f73-9e4d615142d0",
    #                 "chart_code": "statistic_table",
    #                 "data_logic_type_code": "nonaggregation",
    #                 "penetrate_conditions": [],
    #                 "chart_filter_conditions": [],
    #                 "chart_linkage_conditions": [],
    #                 "query_vars": [],
    #                 "row_number": 0,
    #                 "order": None,
    #                 "subtotal_display": {
    #                     "subtotal_position": "tail",
    #                     "summary_position": "tail",
    #                     "subtotal_alias": "my计",
    #                     "summary_alias": "my总",
    #                 },
    #             }
    #         ],
    #         "dashboard_id": "39ee1f94-9eea-0fbf-7f73-9e4d615142d0",
    #         "code": "fangzhiadmin_test",
    #     }
    #     token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************.6SwWeYIcIsJseDpWqgxwaD_yl7ZMT61P0iy3XdFuhTM'
    #     token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************.VmMY4IaEy32qffrfQ2bJY_CtcvrT86YXnehB59FFsMU'
    #     flag, msg, result = download_service.generate_download_task(token, kwargs)
    #     assert isinstance(result, dict)


if __name__ == '__main__':
    unittest.main()
