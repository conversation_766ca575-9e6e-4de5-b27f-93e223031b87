"""
test dashboard cache_service
"""

from dmplib.tests.base_test import BaseTest
from dmplib import redis
from dashboard_chart.services import dashboard_cache_service


class TestDashboardCache(BaseTest):

    dashboard_cache = None
    conn = None
    prefix = 'unitest'

    def setUp(self):
        if self.conn is None:
            self.conn = redis.RedisCache(self.prefix, 10)
        self.dashboard_cache = dashboard_cache_service.DashboardCache("unitest", "1", self.conn)
        super().setUp()

    def tearDown(self):
        self.dashboard_cache.remove()

    def test_set_prop(self):
        result = self.dashboard_cache.set_prop('name', 'foo')
        self.assertTrue(isinstance(result, str) and len(result) > 0)
        val = self.dashboard_cache.get_prop('name')
        self.assertEqual(val, 'foo')
        self.dashboard_cache.remove()

    def test_set_mprop(self):
        result = self.dashboard_cache.set_mprop({'name': 'foo', 'age': 11})
        self.assertTrue(result)
        data = self.dashboard_cache.getall()
        self.assertTrue(data.get('name') == 'foo' and data.get('age') == 11)

    def test_get_prop(self):
        self.dashboard_cache.remove()
        result = self.dashboard_cache.get_prop('name')
        self.assertEqual(result, None)
        self.dashboard_cache.set_prop('name', 'bar')
        result = self.dashboard_cache.get_prop('name')
        self.assertEqual(result, 'bar')

    def test_get_version(self):
        self.dashboard_cache.remove()
        version = self.dashboard_cache.get_version()
        self.assertEqual(version, None)
        version = self.dashboard_cache.set_prop('age', 11)
        new_version = self.dashboard_cache.get_version()
        self.assertEqual(version, new_version)

    def test_getall(self):
        props = self.dashboard_cache.getall()
        self.assertEqual(props, None)
        self.dashboard_cache.set_prop('name', 'foo')
        self.dashboard_cache.set_prop('age', 11)
        result = self.dashboard_cache.getall()
        self.assertEqual(result.get('name'), 'foo')
        self.assertEqual(result.get('age'), 11)

    def test_refresh_version(self):
        self.dashboard_cache.set_prop('name', 'foo')
        version = self.dashboard_cache.get_version()
        new_version = self.dashboard_cache.refresh_version()
        data = self.dashboard_cache.get_version()
        self.assertTrue(len(new_version) > 0)
        self.assertEqual(new_version, data)
        self.assertNotEqual(new_version, version)
