#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import os
import json

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)
from tests.base import BaseTest
from dashboard_chart.services import chart_service
from dashboard_chart.services.download import (
    chart_column_download_result_transform,
    chart_column_download_async,
    download_task_generator,
)
from dashboard_chart.models import ChartDownloadContext, ChartDataModel, ChartPaginationModel

data_source_id = '39ec81f4-ff2e-cdc7-b238-6ea12ac13e12'


class TestDatasetSubject(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='fangzhiadmin_test', account='fangzhiadmin_test')

    def setUp(self):
        super(TestDatasetSubject, self).setUp()
        self.g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'

    # def test_common_download(self):
    #     with open('data.json') as fd:
    #         request_data = json.loads(fd.read())
    #     token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************.RRmEOZPRB7ckPmtyX0RA6FIcKEB8NW5d8RtXEyZgcwI'
    #     result = download_task_generator.generate_download_task(token, request_data)
    #     print(result)

    # def test_async_download_service(self):
    #     with open('download_context.json') as fd:
    #         download_context = json.loads(fd.read())
    #     context = ChartDownloadContext(**download_context)
    #     context.chart_params = json.loads(context.chart_params)
    #     result = chart_column_download_async.AsyncChartDownloadService(context).run()
    #     print(result)

    # def test_subtotal_transform(self):
    #     with open('data.json') as fd:
    #         data = json.loads(fd.read())
    #
    #     chart_params_list = data.get('chart_params')
    #     chart_params = chart_params_list[0] if len(chart_params_list) else {}
    #     chart_model = ChartDataModel(**chart_params)
    #     pagination = ChartPaginationModel(page=1, page_size=50)
    #     chart_model.pagination = pagination
    #     chart_result = chart_service.get_chart_result(chart_model)
    #     with open('chart_data.json', 'w') as fd:
    #         fd.write(json.dumps(chart_result, indent=4, ensure_ascii=True))
    #     result = chart_column_download_result_transform.ChartResultTransform(chart_model, chart_result, 1).process()
    #     print(result)

    # def test_get_total(self):
    #     data = {
    #         "id": "1fc2a7c9-d081-11e9-b928-23e0ec748086",
    #         "report_id": "39f012de-d1ad-b5c7-aec2-270261c63c32",
    #         "screen_id": "39f012de-d1ad-b5c7-aec2-270261c63c32",
    #         "chart_code": "dmp_handsontable",
    #         "data_logic_type_code": "column",
    #         "conditions": [],
    #         "penetrate_conditions": [],
    #         "penetrate_filter_conditions": [],
    #         "filter_conditions": [],
    #         "dashboard_conditions": [],
    #         "chart_filter_conditions": [],
    #         "chart_linkage_conditions": [],
    #         "isShareView": False,
    #         "query_vars": [],
    #         "tenantCode": "",
    #         "order": "",
    #         "pagination": {"page": 1, "total": 0, "page_size": 150},
    #         "dashboard_id": "39f012de-d1ad-b5c7-aec2-270261c63c32",
    #     }
    #     model = ChartDataModel(**data)
    #     result = chart_service.get_total(model)
    #     print(result)
