#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"""

import unittest
import logging
import json
from tests.base import BaseTest
from base import repository
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services import chart_service
from dashboard_chart.convertor.where.where import Where


logger = logging.getLogger(__name__)


class TestChartService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev_test', account='test')

    # def test_chart_filter_formatter(self):
    #
    #     kwargs = {
    #         "id": "39e93a93-2bf4-0520-e0fe-36e31a7ee045",
    #         "report_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "dashboard_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "chart_code": "table",
    #         "data_logic_type_code": "default",
    #         "conditions": [],
    #         "penetrate_conditions": [
    #             {
    #                 "col_name": "col6",
    #                 "col_value": "大都会",
    #                 "operator": "=",
    #                 "dim": {
    #                     "dataset_id": "39e53ff1-3b77-a051-94b8-9d33a077fae6",
    #                     "sort": None,
    #                     "dashboard_chart_id": "39e93a92-f2d2-32d2-b827-9416604ce391",
    #                     "rank": 1,
    #                     "dim": "39e53ff1-3c2d-11d7-a111-f5ba894077b7",
    #                     "content": None,
    #                     "field_group": "维度",
    #                     "formula_mode": "",
    #                     "col_name": "col6",
    #                     "alias": "备案名",
    #                     "dataset_field_id": "39e53ff1-3c2d-11d7-a111-f5ba894077b7",
    #                     "data_type": "字符串",
    #                 },
    #             }
    #         ],
    #         "filter_conditions": [],
    #         "dashboard_conditions": [],
    #     }
    #     self.test_formatter(kwargs)

    def test_chart_linkage_formatter(self):

        pass

    # def test_chart_penetrate_formatter(self):
    #
    #     kwargs = {
    #         "id": "39e93a93-2bf4-0520-e0fe-36e31a7ee045",
    #         "report_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "dashboard_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "chart_code": "table",
    #         "data_logic_type_code": "default",
    #         "conditions": [],
    #         "penetrate_conditions": [
    #             {
    #                 "col_name": "col6",
    #                 "col_value": "大都会",
    #                 "operator": "=",
    #                 "dim": {
    #                     "dataset_id": "39e53ff1-3b77-a051-94b8-9d33a077fae6",
    #                     "sort": None,
    #                     "dashboard_chart_id": "39e93a92-f2d2-32d2-b827-9416604ce391",
    #                     "rank": 1,
    #                     "dim": "39e53ff1-3c2d-11d7-a111-f5ba894077b7",
    #                     "content": None,
    #                     "field_group": "维度",
    #                     "formula_mode": "",
    #                     "col_name": "col6",
    #                     "alias": "备案名",
    #                     "dataset_field_id": "39e53ff1-3c2d-11d7-a111-f5ba894077b7",
    #                     "data_type": "字符串",
    #                 },
    #             }
    #         ],
    #         "filter_conditions": [],
    #         "dashboard_conditions": [],
    #     }
    #     self.test_formatter(kwargs)

    # def test_component_filter_formatter(self):
    #     kwargs = {
    #         "id": "39e93564-56fb-3cb0-0d2c-7247063d7374",
    #         "report_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "dashboard_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "chart_code": "table",
    #         "data_logic_type_code": "default",
    #         "conditions": [],
    #         "penetrate_conditions": [],
    #         "filter_conditions": [
    #             {
    #                 "col_name": "col11",
    #                 "field_name": "col11",
    #                 "field_id": "39e53ff1-3c2e-1e41-acf1-77245e80c895",
    #                 "col_value": "[\"2016-11-11\",\"2016-11-12\",\"2016-11-17\",\"2016-11-18\",\"2016-11-19\"]",
    #                 "operator": "in",
    #                 "dim": {
    #                     "dataset_id": "39e53ff1-3b77-a051-94b8-9d33a077fae6",
    #                     "sort": None,
    #                     "dashboard_chart_id": "39e93594-b15e-89f5-b82e-19a1e272d6e5",
    #                     "rank": 0,
    #                     "dim": "39e53ff1-3c2e-1e41-acf1-77245e80c895",
    #                     "content": None,
    #                     "field_group": "维度",
    #                     "formula_mode": "day",
    #                     "col_name": "col11",
    #                     "alias": "竣工日期",
    #                     "dataset_field_id": "39e53ff1-3c2e-1e41-acf1-77245e80c895",
    #                     "data_type": "日期",
    #                 },
    #                 "select_type": 0,
    #             }
    #         ],
    #         "dashboard_conditions": [],
    #     }
    #     self.test_formatter(kwargs=kwargs)

    # def test_dashboard_filter_formatter(self):
    #
    #     kwargs = {
    #         "id": "39e93a93-2bf4-0520-e0fe-36e31a7ee045",
    #         "report_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "dashboard_id": "39e7d8f8-4da7-5337-9ac5-c4bea24ab732",
    #         "chart_code": "table",
    #         "data_logic_type_code": "default",
    #         "conditions": [],
    #         "penetrate_conditions": [
    #             {
    #                 "col_name": "col6",
    #                 "col_value": "大都会",
    #                 "operator": "=",
    #                 "dim": {
    #                     "dataset_id": "39e53ff1-3b77-a051-94b8-9d33a077fae6",
    #                     "sort": None,
    #                     "dashboard_chart_id": "39e93a92-f2d2-32d2-b827-9416604ce391",
    #                     "rank": 1,
    #                     "dim": "39e53ff1-3c2d-11d7-a111-f5ba894077b7",
    #                     "content": None,
    #                     "field_group": "维度",
    #                     "formula_mode": "",
    #                     "col_name": "col6",
    #                     "alias": "备案名",
    #                     "dataset_field_id": "39e53ff1-3c2d-11d7-a111-f5ba894077b7",
    #                     "data_type": "字符串",
    #                 },
    #             }
    #         ],
    #         "filter_conditions": [],
    #         "dashboard_conditions": [],
    #     }
    #     self.test_formatter(kwargs)

    def test_dashboard_jump_formatter(self):

        pass

    # def test_formatter(self, kwargs):
    #
    #     chart_data_model = ChartDataModel(**kwargs)
    #     chart = repository.get_data(
    #         "dashboard_chart", {"id": chart_data_model.id}, ["id", "source", "chart_code", "display_item", "page_size"]
    #     )
    #     if not chart:
    #         raise Exception
    #     chart_service._assign_dashboard_chart_model(chart_data_model, chart)
    #     result = Where().get_where_fields(chart_data_model)
    #     print('result:::::::::::' + json.dumps(result))
    #     if result:
    #         for item in result:
    #             print('item type:::::' + str(type(item)))
    #             print('formatter item::::::' + json.dumps(item))


if __name__ == '__main__':
    unittest.main()
