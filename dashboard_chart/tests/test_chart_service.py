#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
# @Time     : 2021/08/21 09:31
# <AUTHOR> wangf10
# @File     : test_chart_service.py

from dashboard_chart.models import ChartDataModel
from dashboard_chart.services import chart_service
from dmplib.hug import g
from tests.base_test import BaseTest
from unittest.mock import patch


class TestChartService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='local', account='local')

    def test_get_chart_result(self):  # NOSONAR
        data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979", "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
        model = ChartDataModel(**data)
        chart_service.get_chart_result(model)
        model.dims = model.nums = []
        chart_service.get_chart_result(model, reassign_model=False)
        model.dataset_id = None
        chart_service.get_chart_result(model, reassign_model=False)
        data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979", "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
        model = ChartDataModel(**data)
        model.dataset_id = '39fe88fb-6f51-3e04-408d-d4c0ada79ccb'
        model.dims = model.nums = ['39ed3842-10bd-288f-81ea-aba37b69c23b']
        chart_service.get_chart_result(model, reassign_model=False)
        chart_service.get_chart_result(model, False, False)

    def test_get_chart_query_struct(self):  # NOSONAR
        data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979", "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
        model = ChartDataModel(**data)
        result = chart_service.get_chart_query_struct(model)
        print(result)
        with patch.object(chart_service, 'assign_dashboard_chart_model', return_value=model) as _:
            chart_service.get_chart_query_struct(model)
        with patch.object(chart_service, 'assign_dashboard_chart_model', return_value=model) as _:
            model.dataset_id = None
            chart_service.get_chart_query_struct(model)
        with patch.object(chart_service, 'assign_dashboard_chart_model', return_value=model) as _:
            model.dataset_id = 111
            model.dims = model.nums = None
            chart_service.get_chart_query_struct(model)

    def test_get_item_query_vars(self):  # NOSONAR
        from dataset import external_query_service
        with patch.object(external_query_service, 'batch_get_dataset_include_vars',
                          return_value=[{'field_id': '111', 'value': None, 'default_value': 111}]) as _:
            data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979",
                    "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
            model = ChartDataModel(**data)
            result = chart_service._get_item_query_vars(model, ['111'])
            self.assertEqual(result, [{'field_id': '111', 'value': 111, 'default_value': 111}])

    def test_get_chart_item(self):  # NOSONAR
        data = {"dataset_field_id": "39ed3842-10bd-288f-81ea-aba37b69c23b"}
        model = ChartDataModel(**data)
        chart_service.get_item_list(model)
        data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979", "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
        model = ChartDataModel(**data)
        with patch.object(chart_service, '_get_chart_section_data', return_value=[{'dataset_id': '39fe88fb-6f51-3e04-408d-d4c0ada79ccb'}]) as _:
            chart_service.get_item_list(model)

    def test_merge_get_field_items(self):  # NOSONAR
        dataset_list = ['39fe88fc-6369-3b3e-835b-d9bc20b53405', '39fe88fc-6369-3b36-b4e1-5899656a1f53']
        chart_service.merge_get_field_items(dataset_list, {})
        chart_service.merge_get_field_items([None], {})

    def test_get_chart_data(self):
        g.userid = '39e9dc29-4940-0740-79b6-53b064475ef0'
        g.cookie = {'token': 'eyJhbGciOiJIUzI1NiIsInR5cC'}
        # g.customize_roles = ['39e47d2d-3f20-422b-94c9-e286d7190967']
        kwargs = {
            "dashboard_id": "39fa3af0-9db7-2525-e129-f854f9af2334",
            "chart_params": [
                {
                    "id": "73fa6f94-5c7a-11eb-b618-514ab26d2b8c",
                    "report_id": "39fa3af0-9db7-2525-e129-f854f9af2334",
                    "dashboard_id": "39fa3af0-9db7-2525-e129-f854f9af2334",
                    "chart_code": "analysis_table",
                    "data_logic_type_code": "column",
                    "conditions": [],
                    "external_subject_ids": ["322c3698-3f6e-11eb-97a3-a257289e13c2"],
                    "penetrate_conditions": [],
                    "penetrate_filter_conditions": [],
                    "filter_conditions": [],
                    "chart_filter_conditions": [],
                    "chart_linkage_conditions": [],
                    "drill_conditions": [],
                    "dims": [],
                    "dashboard_conditions": [],
                    "query_vars": [],
                    "pagination": {"page": 1, "total": 0, "page_size": 150},
                    "column_display": [
                        {
                            "dataset_id": "00000000-0000-0000-0000-000000000001",
                            "dataset_field_id": "b0bb08ce-e423-5333a-0edac-cc6fd",
                            "data_type": "字符串",
                            "col_name": "NAME_8274317119",
                            "alias_name": "项目名称",
                            "order": 0,
                            "rank": 0,
                            "col_type": "dim",
                            "is_show": 1,
                            "group": None,
                        },
                        {
                            "dataset_id": "00000000-0000-0000-0000-000000000001",
                            "dataset_field_id": "a60cbdce-5637-8d695-d74c3-1a6de",
                            "data_type": "字符串",
                            "col_name": "BATCHNAME_9523695928",
                            "alias_name": "批次名称",
                            "order": 1,
                            "rank": 1,
                            "col_type": "dim",
                            "is_show": 1,
                            "group": None,
                        },
                    ],
                }
            ],
        }
        chart_params = kwargs.get('chart_params')
        results = chart_service.batch_get_chart_data(chart_params)
        print(results)

    def test_chart_query_struct(self):
        g.userid = '39e9dc29-4940-0740-79b6-53b064475ef0'
        g.cookie = {'token': 'eyJhbGciOiJIUzI1NiIsInR5cC'}
        kwargs = {
            "dashboard_id": "39ee13bb-1d79-0625-5e0f-ddcd831554c4",
            "chart_params": [
                {
                    "id": "f3cf9dff-81be-11e9-993b-9512c73950b5",
                    "report_id": "39ee13bb-1d79-0625-5e0f-ddcd831554c4",
                    "dashboard_id": "39ee13bb-1d79-0625-5e0f-ddcd831554c4",
                    "chart_code": "statistic_table",
                    "data_logic_type_code": "nonaggregation",
                    "conditions": [],
                    "penetrate_conditions": [],
                    "penetrate_filter_conditions": [],
                    "filter_conditions": [],
                    "chart_filter_conditions": [],
                    "chart_linkage_conditions": [],
                    "dashboard_conditions": [],
                    "query_vars": [],
                }
            ],
        }
        param = kwargs.get("chart_params")[0]
        struct = chart_service.get_chart_query_struct(ChartDataModel(**param))
        print('aaa')
        print(struct)

    def test_get_items(self):
        g.userid = '39e9dc29-4940-0740-79b6-53b064475ef0'
        g.cookie = {'token': 'eyJhbGciOiJIUzI1NiIsInR5cC'}
        model = ChartDataModel(**{"dataset_field_id": "37e92ad5-507f-49b8b-2dd8e-c4040"})
        model.external_subject_ids = ["39f86569-983c-a751-504e-882f202b3746"]
        result = chart_service.get_item_list(model)
        print(result)

    def test_get_total(self):  # NOSONAR
        data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979", "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
        model = ChartDataModel(**data)
        with patch.object(chart_service, 'assign_dashboard_chart_model', return_value=model) as _:
            try:
                chart_service.get_total(model)
            except Exception as e:
                print(str(e))
        with patch.object(chart_service, 'assign_dashboard_chart_model', return_value=model) as _:
            model.dataset_id = '39fe88fb-6f51-3e04-408d-d4c0ada79ccb'
            # chart_service.get_total(model)
            try:
                chart_service.get_total(model)
            except Exception as e:
                print(str(e))

    def test_assign_dataset_field_dict(self):  # NOSONAR
        data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979", "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
        model = ChartDataModel(**data)
        chart_service._assign_dataset_field_dict(model)
        model.dataset_field_dict = {'aaa'}
        model.dashboard_filters = [{'main_dataset_field_id': 'bbb'}]
        result = chart_service._assign_dataset_field_dict(model)
        self.assertEqual(result, {'aaa', 'bbb'})

    def test_get_penetrate_relation(self):  # NOSONAR
        from dashboard_chart.repositories import chart_repository
        relation = [
            {'parent_chart_field_id': 'a', 'child_chart_field_id': 'b', 'type': 0},
            {'parent_chart_field_id': 'a', 'child_chart_field_id': 'b', 'type': 1},
            {'parent_chart_field_id': 'a', 'child_chart_field_id': 'b', 'type': 2},
        ]
        with patch.object(chart_repository, 'get_penetrate_relation_by_chart_id', return_value=relation) as _:
            chart_service._get_penetrate_relation('a')

    def test_get_marklines_for_result(self):  # NOSONAR
        from dashboard_chart.repositories import chart_repository
        mark_line = [{'num': 123, 'dashboard_chart_id': 'abc'}]
        with patch.object(chart_repository, 'get_dataset_markline_by_chart_id', return_value=mark_line) as _:
            with patch.object(chart_repository, 'get_num_by_field_id', return_value=1) as _:
                chart_service._get_marklines_for_result('a')

    def test_get_union_field_data(self):  # NOSONAR
        from dashboard_chart.services import proxy_dataset_service
        data = {"dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979", "id": "30f90818-04a3-11ec-b2d6-ef795812897c"}
        model = ChartDataModel(**data)
        model.dataset_id = '39fe88fb-6f51-3e04-408d-d4c0ada79ccb'
        with patch.object(proxy_dataset_service, 'get_formatted_fields_by_field_id', return_value={'id': 'abc'}) as _:
            result = chart_service.get_union_field_data(model.id, model.dataset_field_id, model)
            self.assertEqual(
                result, {'id': 'abc', 'dim': 'abc', 'dashboard_chart_id': '30f90818-04a3-11ec-b2d6-ef795812897c'}
            )

    def test_get_chart_selector_data(self):  # NOSONAR
        from dashboard_chart.services import proxy_dataset_service
        from dashboard_chart.repositories import chart_repository
        result = chart_service.get_chart_selector_data('a', 'b', 'c')
        self.assertEqual(result, [])
        with patch.object(chart_repository, 'get_chart_selector', return_value=[{'field_responder_id': 'a', 'is_same_dataset': 'a'}]) as _:
            with patch.object(proxy_dataset_service, 'get_formatted_fields_by_field_id', return_value={'b': 'c'}) as _:
                result = chart_service.get_chart_selector_data('a', 'b', 'c')
                self.assertEqual(result, [{'field_responder_id': 'a', 'b': 'c', 'is_same_dataset': 'a'}])

    def test_get_selector_data_dict(self):  # NOSONAR
        result = chart_service.get_selector_data_dict({}, 'abc')
        self.assertEqual(result, {})
        with patch.object(chart_service, 'get_chart_selector_data', return_value='abc') as _:
            result = chart_service.get_selector_data_dict(
                [{'dim': {'dashboard_chart_id': 'aaa'}}, {'dim': {'dashboard_chart_id': ''}}], 'a'
            )
            self.assertEqual(result, {'aaa': 'abc'})

    def test_get_display_item(self):  # NOSONAR
        model = ChartDataModel()
        model.display_item = {'top_head': '20px'}
        result = chart_service._get_display_item({}, model)
        self.assertEqual(result, {'top_head': '20px'})

    def test_get_new_linkage_filter_data_dict(self):  # NOSONAR
        from dashboard_chart.services import proxy_dataset_service
        conditions = [{'chart_id': '111'}]

        def a(c, b):  # NOSONAR
            return [{'field_responder_id': c, 'chart_responder_id': b}]
        func = a
        with patch.object(proxy_dataset_service, 'get_formatted_fields_by_field_id', return_value={'b': 'c'}) as _:
            result = chart_service.get_new_linkage_filter_data_dict(conditions, '222', func)
            self.assertEqual(result, {'111': [{'field_responder_id': '111', 'chart_responder_id': '222', 'b': 'c'}]})
        chart_service.get_new_linkage_filter_data_dict([{}], '', func)
        chart_service.get_new_linkage_filter_data_dict([{'chart_id': None}], '', func)

    def test_get_new_linkage_data_dict(self):  # NOSONAR
        with patch.object(chart_service, 'get_new_linkage_filter_data_dict', return_value={'b': 'c'}) as _:
            result = chart_service.get_new_linkage_data_dict('', '')
            self.assertEqual(result, {'b': 'c'})

    def test_get_new_filter_data_dict(self):  # NOSONAR
        with patch.object(chart_service, 'get_new_linkage_filter_data_dict', return_value={'b': 'c'}) as _:
            result = chart_service.get_new_filter_data_dict('', '')
            self.assertEqual(result, {'b': 'c'})

    def test_get_penetrate_relation_dict_by_chart_id(self):  # NOSONAR
        from dashboard_chart.repositories import chart_repository
        with patch.object(chart_repository, 'get_penetrate_relation_by_chart_id',
                          return_value=[{'parent_chart_field_id': 'a'}]) as _:
            result = chart_service.get_penetrate_relation_dict_by_chart_id('a')
            self.assertEqual(result, {'a': 'a'})

    def test_get_operators_by_filter_id(self):  # NOSONAR
        from dashboard_chart.repositories import chart_repository
        chart_service.get_operators_by_filter_id('')
        with patch.object(chart_repository, 'get_dashboard_chart_filter_relation',
                          return_value=[]) as _:
            result = chart_service.get_operators_by_filter_id('a')
            self.assertEqual(result, [])

    def test_batch_get_operators(self):  # NOSONAR
        chart_service.batch_get_operators([])
        with patch.object(chart_service, 'get_operators_by_filter_id', return_value='=') as _:
            result = chart_service.batch_get_operators([{'filter_id': 'a'}])
            self.assertEqual(result, [{'filter_id': 'a', 'operators': '='}])

    def test_split_operators(self):  # NOSONAR
        result = chart_service.split_operators([{}])
        self.assertEqual(result, [{}])
        data_list = [{'operators': [{'operator': '=', 'col_value': 1, 'select_all_flag': 0}]}]
        result = chart_service.split_operators(data_list, 'dashboard_filters')
        self.assertEqual(result, [{'operator': '=', 'col_value': 1, 'select_all_flag': 0}])

    def test_get_chart_list(self):  # NOSONAR
        dashboard_id = ''
        result = chart_service.get_chart_list(dashboard_id)
        self.assertEqual(result, [])
        dashboard_id = '39fe88f6-d3a4-7598-a4e9-dbdaa454e979'
        result = chart_service.get_chart_list(dashboard_id)
        # self.assertEqual(result, [{'id': '2c995770-04a3-11ec-b2d6-ef795812897c'}, {'id': '30f90818-04a3-11ec-b2d6-ef795812897c'}])
        self.assertEqual(result, [])
        dashboard_id = '39fe88f6-d3a4-7598-a4e9-dbdaa454e979'
        result = chart_service.get_chart_list(dashboard_id, True)
        print(result)

    def test_delete_chart(self):  # NOSONAR
        from dashboard_chart.logics.chart_definition_logic import ChartDefinitionLogic
        with patch.object(ChartDefinitionLogic, 'delete_chart', return_value=True) as _:
            result = chart_service.delete_chart('a')
            self.assertEqual(result, True)

    def test_upgrade_dashboard_chart_filter(self):  # NOSONAR
        result = chart_service.upgrade_dashboard_chart_filter('')
        self.assertEqual(result, True)
        from dmplib.db.mysql_wrapper import SimpleMysql
        with patch.object(SimpleMysql, 'query', return_value=[{'id': 'a', 'filter_config': []}]) as _:
            chart_service.upgrade_dashboard_chart_filter('')

    def test_update_chart_component_filter(self):  # NOSONAR
        from dashboard_chart.services import proxy_dataset_service
        with patch.object(proxy_dataset_service, 'get_dataset_field', return_value=[{}]) as _:
            chart_service.update_chart_component_filter('{"a": "abc"}', 'a')

    def test_op_component_filter_relation(self):  # NOSONAR
        from base import repository
        from dashboard_chart.models import DashboardComponentFilterFieldModel
        chart_id = 'a'
        filter_id = ''
        filter_dict = {'a': 'b', 'active': False}
        responder_dataset_id = 'a'
        chart_service._op_component_filter_relation(chart_id, filter_id, filter_dict, '', '', responder_dataset_id)
        with patch.object(chart_service, 'get_field_initiator_id', return_value=[[], []]) as _:
            filter_dict = {'a': {'id': 'b'}}
            chart_service._op_component_filter_relation(chart_id, filter_id, filter_dict, '', '', responder_dataset_id)
        with patch.object(chart_service, 'get_field_initiator_id', return_value=[1, 2]) as _:
            filter_dict = {'a': {'id': 'b'}}
            with patch.object(DashboardComponentFilterFieldModel, 'rules', return_value=[]) as _:
                with patch.object(repository, 'add_model', return_value=True) as _:
                    chart_service._op_component_filter_relation(chart_id, filter_id, filter_dict, '', '', responder_dataset_id)

    def test_get_field_initiator_id(self):  # NOSONAR
        chart_service.get_field_initiator_id('', '')
        chart_service.get_field_initiator_id('a', {'a': ''})
        chart_service.get_field_initiator_id('a', {'a': ['a']})

    def test_get_filter_selector_dict(self):  # NOSONAR
        from dashboard_chart.services import proxy_dataset_service
        chart_service._get_filter_selector_dict([], '')
        chart_service._get_filter_selector_dict([{}], '')
        chart_service._get_filter_selector_dict([{'chart_id': ''}], '')
        with patch.object(chart_service, '_get_filter_data', return_value=[{'field_responder_id': 'b'}]) as _:
            with patch.object(proxy_dataset_service, 'get_formatted_fields_by_field_id',
                              return_value={'a': 'b'}) as _:
                result = chart_service._get_filter_selector_dict([{'chart_id': 'aa'}], '')
                self.assertEqual(result, {'aa': [{'a': 'b', 'field_responder_id': 'b'}]})

    def test_get_linkage_selector_dict(self):  # NOSONAR
        from dashboard_chart.services import proxy_dataset_service
        chart_service._get_linkage_selector_dict([], '')
        chart_service._get_linkage_selector_dict([{}], '')
        chart_service._get_linkage_selector_dict([{'chart_id': ''}], '')
        with patch.object(chart_service, '_get_linkage_data', return_value=[{'field_responder_id': 'b'}]) as _:
            with patch.object(proxy_dataset_service, 'get_formatted_fields_by_field_id', return_value={'a': 'b'}) as _:
                result = chart_service._get_linkage_selector_dict([{'chart_id': 'aa'}], '')
                self.assertEqual(result, {'aa': [{'a': 'b', 'field_responder_id': 'b'}]})

    def test_get_filter_data(self):
        chart_service._get_filter_data('a', 'b')

    def test_get_linkage_data(self):
        chart_service._get_linkage_data('a', 'b')
