#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test metadata service
"""

import json
import logging
import unittest

from dmplib.utils.errors import UserError
from tests.base import BaseTest
from dashboard_chart.services import metadata_service, screen_dashboard_service, dashboard_service

logger = logging.getLogger(__name__)


class TestDashBoardService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='admin')

    def test_screens_preview_metadata_v2(self):
        dashboard_id = '39f938e1-4d4b-eb34-790c-67b9b129c3ec'
        result = metadata_service.get_screens_preview_metadata_v2(dashboard_id=dashboard_id)
        print(result)

    # def test_release_metadata_v2(self):
    #     snapshot_id = '39f97831-ab36-b7d6-d274-278c9083640b'
    #     msg, meta = metadata_service.get_screens_release_metadata_v2(snapshot_id=snapshot_id)
    #     print([msg, meta])

    # def test_get_chart_childrn(self):
    #     children = metadata_service.get_chart_children(
    #         "********-f700-11ea-a2b6-01a10f8ffbef", "39f73735-65eb-ca9b-33d2-ab948a6a9e5a"
    #     )
    #     print(children)

    # def test_dashboard_edit(self):
    #     kwargs = {
    #         "metadata": {
    #             "dashboard": {
    #                 "id": "39f97831-ab36-b7d6-d274-278c9083640b",
    #                 "name": "test_export",
    #                 "description": "",
    #                 "level_code": "3666-0023-0008-",
    #                 "parent_id": "39f939e1-c5ee-9eff-93ef-4c0853536a69",
    #                 "biz_code": "3a94231849f84b78abffb77e8558c576",
    #                 "cover": "",
    #                 "scale_mode": 0,
    #                 "layout": {
    #                     "mode": "free",
    #                     "platform": "pc",
    #                     "ratio": "16:9",
    #                     "width": 1920,
    #                     "height": 1080,
    #                     "lattice": 10,
    #                     "toolbar": "show",
    #                     "screenHeader": "show",
    #                     "slider_top": 0,
    #                     "layout_mode": "none",
    #                     "card_radius": 0,
    #                 },
    #                 "is_show_mark_img": 0,
    #                 "new_layout_type": "free",
    #                 "styles": {
    #                     "theme": "colorful_white",
    #                     "attrs": {},
    #                     "background": {
    #                         "show": True,
    #                         "color": "RGBA(255,255,255,1)",
    #                         "image": "",
    #                         "size": "stretch",
    #                         "user_image": "",
    #                     },
    #                     "grid_padding": {
    #                         "container_padding": [10, 10],
    #                         "chart_margin": [10, 10],
    #                         "chart_padding": [15, 15, 15, 15],
    #                         "chart_background": "#FFFFFF",
    #                     },
    #                 },
    #                 "publish": {"status": 1, "type_access_released": 4, "share_secret_key": "", "released_on": ""},
    #                 "terminal_type": "",
    #             },
    #             "screens": [
    #                 {
    #                     "id": "39f97831-ab36-b7d6-d274-278c9083640b",
    #                     "name": "test_export",
    #                     "description": "",
    #                     "level_code": "3666-0023-0008-",
    #                     "biz_code": "3a94231849f84b78abffb77e8558c576",
    #                     "cover": "",
    #                     "scale_mode": 0,
    #                     "layout": {
    #                         "mode": "free",
    #                         "platform": "pc",
    #                         "ratio": "16:9",
    #                         "width": 1920,
    #                         "height": 1080,
    #                         "lattice": 10,
    #                         "toolbar": "show",
    #                         "screenHeader": "show",
    #                         "slider_top": 0,
    #                         "layout_mode": "none",
    #                         "card_radius": 0,
    #                     },
    #                     "is_show_mark_img": 0,
    #                     "new_layout_type": "free",
    #                     "create_type": 1,
    #                     "styles": {
    #                         "theme": "colorful_white",
    #                         "attrs": {},
    #                         "background": {
    #                             "show": True,
    #                             "color": "RGBA(255,255,255,1)",
    #                             "image": "",
    #                             "size": "stretch",
    #                             "user_image": "",
    #                         },
    #                         "grid_padding": {
    #                             "container_padding": [10, 10],
    #                             "chart_margin": [10, 10],
    #                             "chart_padding": [15, 15, 15, 15],
    #                             "chart_background": "#FFFFFF",
    #                         },
    #                     },
    #                     "dashboard_filters": [],
    #                     "publish": {"status": 1, "type_access_released": 4, "share_secret_key": "", "released_on": ""},
    #                     "chart_relations": {
    #                         "filters": [],
    #                         "linkages": [],
    #                         "redirects": [],
    #                         "penetrates": [],
    #                         "chart_filters": [
    #                             {
    #                                 "id": "c3f24ff6-3f46-11eb-8b76-6dc8d68f5d48",
    #                                 "chart_initiator_id": "063dbc46-3f43-11eb-b918-335e857691db",
    #                                 "field_initiator_id": "c3f24ff6-3f46-11eb-8b77-6dc8d68f5d48",
    #                                 "dataset_id": "",
    #                                 "filter_type": 0,
    #                                 "initiator_source": "fixed_value",
    #                                 "fixed_value_data": {
    #                                     "id": "c3f24ff6-3f46-11eb-8b77-6dc8d68f5d48",
    #                                     "chart_id": "063dbc46-3f43-11eb-b918-335e857691db",
    #                                     "name": "手工录入值",
    #                                     "value_type": "string",
    #                                     "identifier": "",
    #                                     "extra_data": "",
    #                                 },
    #                                 "available": 1,
    #                                 "related_list": [
    #                                     {
    #                                         "chart_responder_id": "9c5595c4-3f45-11eb-8b76-6dc8d68f5d48",
    #                                         "related_dataset_id": "39f63671-0ee1-ed61-d985-dc0398386f90",
    #                                         "field_responder_id": "39f63671-e3f5-39ca-e73d-9e65917eff68",
    #                                         "is_same_dataset": 0,
    #                                         "id": "c4eb2eec-3f46-11eb-8b77-6dc8d68f5d48",
    #                                     }
    #                                 ],
    #                             }
    #                         ],
    #                         "chart_linkages": [],
    #                         "var_relations": [],
    #                     },
    #                     "charts": [
    #                         {
    #                             "id": "063dbc46-3f43-11eb-b918-335e857691db",
    #                             "name": "下拉筛选-1",
    #                             "level_code": "",
    #                             "chart_type": "filter",
    #                             "chart_component_code": "select_filter",
    #                             "page_size": 0,
    #                             "position": {
    #                                 "i": "063dbc46-3f43-11eb-b918-335e857691db",
    #                                 "col": 500,
    #                                 "row": 200,
    #                                 "size_x": 360,
    #                                 "size_y": 45,
    #                                 "z": 1502,
    #                             },
    #                             "config": "",
    #                             "data": {
    #                                 "aggregation": 1,
    #                                 "default_value": "",
    #                                 "enable_subtotal": 0,
    #                                 "enable_summary": 0,
    #                                 "chart_default_value": [
    #                                     {
    #                                         "id": "4e671f8b-3f65-11eb-be46-19893686775b",
    #                                         "dataset_field_id": "",
    #                                         "operator": "in",
    #                                         "value": '["第二事业部","第三事业部"]',
    #                                         "select_all": 0,
    #                                         "extend_data": "{}",
    #                                     }
    #                                 ],
    #                                 "filter_relation": 0,
    #                                 "external_subject_id": "",
    #                                 "enable_subtotal_row_summary": 0,
    #                                 "enable_subtotal_col": 0,
    #                                 "enable_subtotal_row": 0,
    #                                 "enable_subtotal_col_summary": 0,
    #                                 "reset_field_sort": 1,
    #                                 "data_type": {"logic_type": "default"},
    #                                 "datasource": {"id": "", "type": ""},
    #                                 "indicator": {
    #                                     "dims": [],
    #                                     "nums": [],
    #                                     "comparisons": [],
    #                                     "filters": [],
    #                                     "zaxis": [],
    #                                     "chart_params": [],
    #                                     "desires": [],
    #                                     "marklines": [],
    #                                     "chart_vars": [],
    #                                     "field_sorts": [],
    #                                 },
    #                             },
    #                             "funcSetup": {"display_item": "", "refresh_rate": ""},
    #                             "export_type": ["data"],
    #                             "children_chart_ids": [],
    #                             "parent_chart_id": "",
    #                             "fixed_data_mode": "manual_input",
    #                             "fixed_manual_value": ["海亮公司", "第一事业部", "第二事业部", "第三事业部", "第四事业部", "第五事业部", "第一事业部"],
    #                         },
    #                         {
    #                             "id": "9c5595c4-3f45-11eb-8b76-6dc8d68f5d48",
    #                             "name": "通用表格-1",
    #                             "level_code": "",
    #                             "chart_type": "chart",
    #                             "chart_component_code": "dmp_handsontable",
    #                             "page_size": 150,
    #                             "position": {
    #                                 "i": "9c5595c4-3f45-11eb-8b76-6dc8d68f5d48",
    #                                 "col": 930,
    #                                 "row": 280,
    #                                 "size_x": 480,
    #                                 "size_y": 270,
    #                                 "z": 1503,
    #                             },
    #                             "config": "",
    #                             "data": {
    #                                 "aggregation": 1,
    #                                 "default_value": "",
    #                                 "enable_subtotal": 0,
    #                                 "enable_summary": 0,
    #                                 "chart_default_value": [],
    #                                 "filter_relation": 0,
    #                                 "external_subject_id": "",
    #                                 "enable_subtotal_row_summary": 0,
    #                                 "enable_subtotal_col": 0,
    #                                 "enable_subtotal_row": 0,
    #                                 "enable_subtotal_col_summary": 0,
    #                                 "reset_field_sort": 1,
    #                                 "data_type": {"logic_type": "column"},
    #                                 "datasource": {"id": "39f63671-0ee1-ed61-d985-dc0398386f90", "type": "EXCEL"},
    #                                 "indicator": {
    #                                     "dims": [
    #                                         {
    #                                             "id": "ba531609-3f46-11eb-8b76-6dc8d68f5d48",
    #                                             "dashboard_chart_id": "9c5595c4-3f45-11eb-8b76-6dc8d68f5d48",
    #                                             "alias": "事业部",
    #                                             "content": "",
    #                                             "formula_mode": "",
    #                                             "rank": 0,
    #                                             "sort": "",
    #                                             "dim": "39f63671-e3f5-39ca-e73d-9e65917eff68",
    #                                             "note": "",
    #                                             "is_subtotal_cate": 0,
    #                                             "dim_type": 0,
    #                                             "display_format": {
    #                                                 "column_unit_name": "",
    #                                                 "display_mode": "dim",
    #                                                 "display_way": "text",
    #                                                 "show_zero_way": "default",
    #                                                 "show_null_way": "default",
    #                                                 "show_empty_way": "default",
    #                                                 "image_height": 72,
    #                                                 "image_width": 72,
    #                                                 "thousand_point_separator": 1,
    #                                                 "fixed_decimal_places": 0,
    #                                                 "unit": "无",
    #                                                 "hidden_unit": 0,
    #                                                 "justify_decimal": 0,
    #                                                 "justify_decimal_places": 0,
    #                                             },
    #                                             "dataset_field_id": "39f63671-e3f5-39ca-e73d-9e65917eff68",
    #                                             "alias_name": "事业部",
    #                                             "field_group": "维度",
    #                                             "dataset_id": "39f63671-0ee1-ed61-d985-dc0398386f90",
    #                                             "data_type": "字符串",
    #                                             "col_name": "SYB_8123229660",
    #                                             "expression": "",
    #                                             "type": "普通",
    #                                             "visible": 1,
    #                                         }
    #                                     ],
    #                                     "nums": [
    #                                         {
    #                                             "id": "bd354e65-3f46-11eb-8b76-6dc8d68f5d48",
    #                                             "dashboard_chart_id": "9c5595c4-3f45-11eb-8b76-6dc8d68f5d48",
    #                                             "num": "39f63671-e3f5-4954-fb6d-847f45951a27",
    #                                             "alias": "合作项目数",
    #                                             "formula_mode": "sum",
    #                                             "rank": 0,
    #                                             "sort": "",
    #                                             "note": "",
    #                                             "calc_null": 0,
    #                                             "display_format": {
    #                                                 "column_unit_name": "",
    #                                                 "display_mode": "num",
    #                                                 "display_way": "text",
    #                                                 "show_zero_way": "default",
    #                                                 "show_null_way": "default",
    #                                                 "show_empty_way": "default",
    #                                                 "image_height": 72,
    #                                                 "image_width": 72,
    #                                                 "thousand_point_separator": 1,
    #                                                 "fixed_decimal_places": 0,
    #                                                 "unit": "无",
    #                                                 "hidden_unit": 0,
    #                                                 "justify_decimal": 0,
    #                                                 "justify_decimal_places": 0,
    #                                             },
    #                                             "axis_type": 0,
    #                                             "chart_code": "",
    #                                             "subtotal_formula_mode": "",
    #                                             "subtotal_col_formula_mode": "sum",
    #                                             "subtotal_row_formula_mode": "",
    #                                             "dataset_field_id": "39f63671-e3f5-4954-fb6d-847f45951a27",
    #                                             "alias_name": "合作项目数",
    #                                             "field_group": "度量",
    #                                             "dataset_id": "39f63671-0ee1-ed61-d985-dc0398386f90",
    #                                             "data_type": "数值",
    #                                             "col_name": "HZXMS_12284181507",
    #                                             "expression": "",
    #                                             "type": "普通",
    #                                             "visible": 1,
    #                                         }
    #                                     ],
    #                                     "comparisons": [],
    #                                     "filters": [],
    #                                     "zaxis": [],
    #                                     "chart_params": [],
    #                                     "desires": [],
    #                                     "marklines": [],
    #                                     "chart_vars": [],
    #                                     "field_sorts": [],
    #                                 },
    #                             },
    #                             "funcSetup": {"display_item": "", "refresh_rate": ""},
    #                             "export_type": ["data"],
    #                             "children_chart_ids": [],
    #                             "parent_chart_id": "",
    #                             "fixed_data_mode": "",
    #                             "fixed_manual_value": [],
    #                         },
    #                     ],
    #                     "terminal_type": "",
    #                     "var_value_sources": [],
    #                 }
    #             ],
    #             "download": True,
    #         }
    #     }
    #     metadata = kwargs.get('metadata')
    #     if not metadata:
    #         raise UserError(message="请指定需要更改的元数据!")
    #     screen_dashboard_service.check_project_edit_permission(metadata)
    #     screens = metadata.get('screens')
    #     if not isinstance(screens, list) or len(screens) < 1:
    #         raise UserError(message="参数数据格式非法!")
    #     metadata['first_report'] = screens[0]
    #     metadata['screens'] = []
    #     result, errors = dashboard_service.update_metadata(metadata)
    #     print(result, errors)

    # def test_set_user_active_time(self):
    #     code = 'test'
    #     userid = "39f8bdf4-5fe7-e564-4dd5-6f80474f164f"
    #     account = "zhangwb"
    #     res = metadata_service.set_user_last_active_time(code, userid, account)
    #     print(res)


if __name__ == '__main__':
    unittest.main()
