#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from tests.base_test import BaseTest
from dashboard_chart.api_route import get_dashboard_list, search_dashboard_list


# class AddDistributeFolderLimitTest(BaseTest):
    # def test_get_dashboard_list(self):
    #     """
    #     报告列表
    #     :return:
    #     """
    #     kwargs = {"parent_id": "", "order_by": "", "reverse": 0, "t": 1550545717613}
    #     code, _, data = get_dashboard_list(**kwargs)
    #     self.assertIsNotNone(data, "数据不能为空")
    #     tree_data = data.get("tree")
    #     self.assertIsNotNone(tree_data, "列表数据不能为空")
    #     distribute_folder_flag = False
    #     for single_item in tree_data:
    #         if single_item.get("id") == "39ec15f8-532e-bcf5-a438-bc949a615836" and single_item.get("distribute_type"):
    #             distribute_folder_flag = True
    #     self.assertTrue(distribute_folder_flag, "不存在被分发的报告或文件夹")

    # def test_search_dashboard_list(self):
    #     """
    #     搜索报告
    #     :return:
    #     """
    #     kwargs = {"name": "系统", "t": 1550545717613}
    #     code, _, data = search_dashboard_list(**kwargs)
    #     data = data if data else None
    #     self.assertIsNotNone(data, "数据不能为空")
    #     distribute_folder_flag = False
    #     for single_item in data:
    #         if single_item.get("id") == "39ec15f8-532e-bcf5-a438-bc949a615836" and single_item.get("distribute_type"):
    #             distribute_folder_flag = True
    #     self.assertTrue(distribute_folder_flag, "不存在被分发的报告或文件夹")
