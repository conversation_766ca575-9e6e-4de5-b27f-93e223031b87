#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
test external dashboard service
"""
import json
import logging
import unittest
import urllib.parse

import hug
from falcon.testing import StartResponseMock

from tests.base import BaseTest
import jwt
import time
import base64
from http.cookies import SimpleCookie
from dashboard_chart import api_route

logger = logging.getLogger(__name__)


def get_cookie(response):
    cookie = SimpleCookie()
    for (k, v) in response.headers:
        if k != 'set-cookie':
            continue
        cookie.load(v)
    return cookie


def make_token(payload):
    jwt_secret = '0UZR4h#@'
    jwt_alg = 'HS256'
    jwt_token = jwt.encode(payload, jwt_secret, jwt_alg)
    exp = int(time.time()) + 60 * 60 * 24
    payload.update({'exp': exp})
    token = base64.b64encode(jwt_token).decode('utf-8')
    return token


def decode_token(token):
    jwt_secret = 'YC2UFKz7AW'
    return jwt.decode(token, jwt_secret, algorithms="HS256")


class TestDashboardLoginApi(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')

    def check_cookies(self, rsp, account, user_name):
        cookie = get_cookie(rsp)
        for item in ['account', 'user_name']:
            self.assertEqual(item in cookie.keys(), True)
        self.assertEqual(cookie['account'].value, urllib.parse.quote(account))
        self.assertEqual(
            cookie['user_name'].value == urllib.parse.quote(json.dumps(user_name)) or cookie['user_name'].value == '',
            True,
        )

    # def test_main(self):
    #     self.normal_test()
    #     self.account_test()
    #     self.external_params_test()

    def get_token_data(self, rsp):
        cookie = get_cookie(rsp)
        self.assertEqual('token' in cookie.keys(), True)
        token = cookie['token'].value
        data = decode_token(token)
        return data

    def check_token_user_name(self, payload, expect_user_name):
        token = make_token(payload)
        rsp: StartResponseMock = hug.test.call('GET', api_route, '/api/login', token=token, redirect_url='test')
        self.assertEqual(rsp.status, hug.HTTP_302)
        token_data = self.get_token_data(rsp)
        external_params = token_data.get('external_params')
        self.assertEqual(external_params.get('user_name'), expect_user_name)

    def external_params_test(self):
        # 测试传了user_name时, external_params中有user_name的场景
        payload = {
            'project_code': 'test',
            'user_name': 'user_name',
            'biz_code': 'ffacbad52b5443598be13739413845a1',
        }
        self.check_token_user_name(payload, 'user_name')
        payload = {
            'project_code': 'test',
            'portal_id': '39ee568a-6712-eb4d-ecaf-ce8ab8cbe602',
            'user_name': 'user_namexx',
        }
        self.check_token_user_name(payload, 'user_namexx')

    def normal_test(self):
        # 测试一般场景
        self.login_with_biz_code()
        self.login_with_account()
        self.login_with_portal_id()

    def login_with_account(self):
        payload = {
            'user_account': 'zhud04',
            'project_code': 'test',
        }
        token = make_token(payload)
        response: StartResponseMock = hug.test.call('GET', api_route, '/api/login', token=token, redirect_url='test')
        self.assertEqual(response.status, hug.HTTP_302)
        self.assertEqual(response.headers_dict.get('location'), 'test')
        self.check_cookies(response, 'zhud04', 'zd')

    def login_with_portal_id(self):
        payload = {
            'project_code': 'test',
            'portal_id': '39ee568a-6712-eb4d-ecaf-ce8ab8cbe602',
        }
        token = make_token(payload)
        response: StartResponseMock = hug.test.call('GET', api_route, '/api/login', token=token)
        self.assertEqual(response.status, hug.HTTP_302)
        self.assertEqual(
            response.headers_dict.get('location'), '/app/index/39ee568a-6712-eb4d-ecaf-ce8ab8cbe602/mobile'
        )
        self.check_cookies(response, 'external_user', '')

    def login_with_biz_code(self):
        account = 'sdflasdfsfs23902'
        user_name = 'ykj_jhsy 超级用户'
        payload = {
            'project_code': 'test',
            'user_account': account,
            'user_auth': 'view,download',
            'tenant_code': 'test',
            'user_name': user_name,
            'biz_code': 'ffacbad52b5443598be13739413845a1',
            'redirect': 'http://localhost/intelligent-report',
        }
        token = make_token(payload)

        response: StartResponseMock = hug.test.call('GET', api_route, '/api/login', token=token, redirect_url='test')
        self.assertEqual(
            response.headers_dict.get('location'), '/dataview/share/39f1229e-3d38-39e1-38e0-50339f9031c2?code=test'
        )
        self.assertEqual(response.status, hug.HTTP_302)
        self.check_cookies(response, account, user_name)

    def account_test(self):
        # 特殊场景
        # 1. account无值, 无user_name
        # 2. account无值, 有user_name
        # 3. account有值, 用户不存在, 无user_name
        # 4. account有值, 用户不存在, 有user_name
        # 5. account有值, 用户存在, 无user_name
        # 6. account有值, 用户存在, 有user_name
        self.special_account('', '', 'external_user', '', 'external_user')
        self.special_account('', 'user_name', 'external_user', 'user_name', 'external_user')
        self.special_account('123xxx', '', '123xxx', '', 'external_user')
        self.special_account('123xxx', 'user_name', '123xxx', 'user_name', 'external_user')
        self.special_account('zhud04', '', 'zhud04', 'zd', 'zhud04')
        self.special_account('zhud04', 'user_name', 'zhud04', 'user_name', 'zhud04')

    def special_account(self, account, user_name, expect_account, expect_user_name, expect_token_account):
        logging.info(f"params: {[account, user_name, expect_account, expect_user_name, expect_token_account]}")
        payload = {
            'user_account': account,
            'user_name': user_name,
            'project_code': 'test',
            'user_auth': 'view,download',
            'tenant_code': 'test',
            'biz_code': 'ffacbad52b5443598be13739413845a1',
        }
        token = make_token(payload)
        response: StartResponseMock = hug.test.call('GET', api_route, '/api/login', token=token, redirect_url='test')
        self.assertEqual(response.status, hug.HTTP_302)
        self.check_cookies(response, expect_account, expect_user_name)
        token_data = self.get_token_data(response)
        self.assertEqual(token_data.get('account'), expect_token_account)


if __name__ == '__main__':
    unittest.main()
