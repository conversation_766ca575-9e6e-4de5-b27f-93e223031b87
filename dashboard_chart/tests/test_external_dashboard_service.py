#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
test external dashboard service
"""

import json
import logging
import unittest

from dmplib.hug import g
from tests.base import BaseTest
from dashboard_chart.services import external_dashboard_service, dashboard_service

logger = logging.getLogger(__name__)


class TestDashBoardService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')

    # def test_dataset_id(self):
    #     dataset_id = '39e7702f-6ce1-d0b3-4007-cf7a51c7f22d'
    #     result = external_dashboard_service.get_related_dashboard_by_dataset_id(dataset_id)
    #     result = 'test_dataset_id::::::' + json.dumps(result)
    #     print(result)

    # def test_get_related_dashboard_chart_by_field_ids(self):
    #     dataset_field_ids = ['39eaf082-e375-2cfd-fb42-1a955a8c1d98', '39e51292-5a0c-d295-b271-2d3a4b7173d6']
    #     result = external_dashboard_service.batch_get_related_dashboard_by_field_id(dataset_field_ids)
    #     result = 'test_get_related_dashboard_chart_by_field_ids::::::' + json.dumps(result)
    #     print(result)

    # def test_get_dataset_fields_by_dashboard(self):
    #     dashboard_id = '39eb2f0e-b874-4014-da10-d480fe4c416f'
    #     result = external_dashboard_service.get_dataset_fields_by_dashboard(dashboard_id)
    #     assert isinstance(result, list)
    #
    # def test_get_dashboard_list(self):
    #     g.userid = "39ef353d-fcca-6979-18d8-d8c168b9aa10"
    #     g.account = "ee"
    #     kwargs = {"parent_id": "39e9e5fa-9805-5310-3926-bdad5c4abdc9"}
    #     result = dashboard_service.get_dashboard_list(**kwargs)
    #     assert isinstance(result, list)


if __name__ == '__main__':
    unittest.main()
