#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"""

import unittest
import logging
import json
from tests.base import BaseTest
from dashboard_chart.services import dashboard_lock_service


logger = logging.getLogger(__name__)


class TestChartService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='dev_test', account='test')

    def test_checkin(self):

        res = dashboard_lock_service.checkin_dashboard(dashboard_id='39e2fb5f-482f-7016-8d0e-8f545b1100c5')
        print('result:::::::::::' + json.dumps(res))

    def test_unlock(self):

        res = dashboard_lock_service.unlock_dashboard(dashboard_id='39e2fb5f-482f-7016-8d0e-8f545b1100c5')
        print('result:::::::::::' + json.dumps(res))


if __name__ == '__main__':
    unittest.main()
