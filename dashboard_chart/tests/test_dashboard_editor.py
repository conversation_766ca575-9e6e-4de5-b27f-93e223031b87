#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/22 17:31
# <AUTHOR> caoxl
# @File     : test_dashboard_editor.py
from dashboard_chart.services import dashboard_service
from dmplib.tests.base_test import BaseTest
from dmplib.utils.errors import UserError


class TestChartService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='zk', account='zk')

    # pylint:disable=C0301
    # pylint:disable=R0201
    # def test_update_metadata(self):
    #     kwargs = {
    #         "metadata": {
    #             "dashboard": {
    #                 "id": "39f73c12-1d82-5bed-6abe-0054eec4a64c",
    #                 "name": "报告",
    #                 "description": "",
    #                 "level_code": "0128-0001-",
    #                 "parent_id": "39f73c11-e41e-690d-4be6-75e4b5692528",
    #                 "biz_code": "f9e7c1838eee4c739929fa7372e3a1f1",
    #                 "cover": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39f78902-882f-764a-17de-882076fc31f6.jpg",
    #                 "scale_mode": 0,
    #                 "layout": {
    #                     "mode": "grid",
    #                     "platform": "pc",
    #                     "ratio": "16:9",
    #                     "width": 1920,
    #                     "height": 1080,
    #                     "lattice": 10,
    #                     "toolbar": "show",
    #                     "screenHeader": "show",
    #                 },
    #                 "is_show_mark_img": 1,
    #                 "new_layout_type": "grid",
    #                 "styles": {
    #                     "theme": "colorful_white",
    #                     "attrs": {},
    #                     "background": {"show": True, "color": "#EBEDF2", "image": "", "size": "stretch"},
    #                     "grid_padding": {
    #                         "container_padding": [10, 10],
    #                         "chart_margin": [5, 5],
    #                         "chart_padding": [15, 15, 15, 15],
    #                         "chart_background": "#FFFFFF",
    #                     },
    #                 },
    #                 "publish": {"status": 1, "type_access_released": 4, "share_secret_key": "", "released_on": ""},
    #                 "terminal_type": "",
    #             },
    #             "screens": [
    #                 {
    #                     "id": "39f73c12-1d82-5bed-6abe-0054eec4a64c",
    #                     "name": "报告",
    #                     "description": "",
    #                     "level_code": "0128-0001-",
    #                     "biz_code": "f9e7c1838eee4c739929fa7372e3a1f1",
    #                     "cover": "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/39f78902-882f-764a-17de-882076fc31f6.jpg",
    #                     "scale_mode": 0,
    #                     "layout": {
    #                         "mode": "grid",
    #                         "platform": "pc",
    #                         "ratio": "16:9",
    #                         "width": 1920,
    #                         "height": 1080,
    #                         "lattice": 10,
    #                         "toolbar": "show",
    #                         "screenHeader": "show",
    #                     },
    #                     "is_show_mark_img": 1,
    #                     "new_layout_type": "grid",
    #                     "create_type": 1,
    #                     "styles": {
    #                         "theme": "colorful_white",
    #                         "attrs": {},
    #                         "background": {"show": True, "color": "#EBEDF2", "image": "", "size": "stretch"},
    #                         "grid_padding": {
    #                             "container_padding": [10, 10],
    #                             "chart_margin": [5, 5],
    #                             "chart_padding": [15, 15, 15, 15],
    #                             "chart_background": "#FFFFFF",
    #                         },
    #                     },
    #                     "dashboard_filters": [],
    #                     "publish": {"status": 1, "type_access_released": 4, "share_secret_key": "", "released_on": ""},
    #                     "chart_relations": {
    #                         "filters": [],
    #                         "linkages": [],
    #                         "redirects": [],
    #                         "penetrates": [],
    #                         "chart_filters": [],
    #                         "chart_linkages": [],
    #                         "var_relations": [],
    #                     },
    #                     "charts": [
    #                         {
    #                             "id": "b29334bc-f8cc-11ea-bed3-db996a9ba9e9",
    #                             "name": "组合筛选器-1",
    #                             "parent_chart_id": "",
    #                             "children_chart_ids": [
    #                                 "b89a05a5-f8cc-11ea-bed3-db996a9ba9e9",
    #                                 "b89a05af-f8cc-11ea-bed3-db996a9ba9e9",
    #                                 "b89a05b7-f8cc-11ea-bed3-db996a9ba9e9",
    #                             ],
    #                             "level_code": "",
    #                             "chart_type": "container",
    #                             "chart_component_code": "combine_filter",
    #                             "page_size": 0,
    #                             "position": {
    #                                 "i": "b29334bc-f8cc-11ea-bed3-db996a9ba9e9",
    #                                 "col": 0,
    #                                 "row": 0,
    #                                 "size_x": 12,
    #                                 "size_y": 2,
    #                             },
    #                             "config": "[{\"title\":\"全局样式\",\"field\":\"global\",\"spread\":false,\"items\":[{\"field\":\"filterWidthConfig\",\"label\":\"\",\"data\":{}},{\"field\":\"filterSpace\",\"label\":\"筛选器间距\",\"data\":24}]},{\"title\":\"标签\",\"field\":\"labelConfig\",\"spread\":false,\"show\":false,\"items\":[{\"field\":\"position\",\"label\":\"显示位置\",\"data\":\"left\"},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"fontColor\",\"label\":\"文本颜色\",\"data\":\"RGBA(46,46,46,1)\",\"theme_controled\":True},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"}}]},{\"title\":\"查询按钮\",\"field\":\"searchButton\",\"spread\":false,\"items\":[{\"field\":\"buttonPosition\",\"label\":\"显示位置\",\"data\":\"horizontal\"}]},{\"title\":\"标题\",\"field\":\"containerTitle\",\"spread\":false,\"scope\":\"container.title\",\"items\":[{\"field\":\"text\",\"label\":\"标题\",\"data\":\"组合筛选器-1\",\"scope\":\"container.title.text\"},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":16,\"scope\":\"container.title.fontSize\"},{\"field\":\"color\",\"label\":\"文本颜色\",\"data\":\"#000\",\"scope\":\"container.title.color\"},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"},\"scope\":\"container.title.fontStyle\"},{\"field\":\"distance\",\"label\":\"上下间距\",\"data\":10,\"scope\":\"container.title.distance\"},{\"field\":\"textAlign\",\"label\":\"显示位置\",\"data\":\"left\",\"scope\":\"container.title.textAlign\"},{\"field\":\"hideTitle\",\"label\":\"隐藏标题\",\"scope\":\"container.title.hideTitle\",\"show\":{\"field\":\"show\",\"data\":True},\"items\":[]}]},{\"title\":\"图表跳转\",\"field\":\"chartRedirect\",\"spread\":false,\"scope\":\"container.redirect\",\"items\":[{\"field\":\"redirectText\",\"label\":\"跳转文本\",\"data\":\"查看详情\",\"scope\":\"container.redirect.redirectText\"},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14,\"scope\":\"container.redirect.fontSize\"},{\"field\":\"color\",\"label\":\"文本颜色\",\"data\":\"#C6C8D1\",\"scope\":\"container.redirect.color\"},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"underline\"},\"scope\":\"container.redirect.fontStyle\"},{\"field\":\"textPosition\",\"label\":\"显示位置\",\"data\":\"follow-title\",\"scope\":\"container.redirect.textPosition\"}]},{\"title\":\"图表备注\",\"field\":\"chartRemarkConfig\",\"spread\":false,\"scope\":\"container.chartRemark\",\"items\":[{\"field\":\"dataUpdate\",\"label\":\"数据更新时间\",\"scope\":\"container.chartRemark.dataUpdate\",\"show\":{\"field\":\"checked\",\"data\":false},\"items\":[{\"field\":\"dataTimeType\",\"label\":\"时间展示方式\",\"scope\":\"container.chartRemark.dataUpdate.timeType\"}]},{\"field\":\"remarkDescription\",\"label\":\"备注说明\",\"scope\":\"container.chartRemark.remarkDescription\",\"items\":[{\"field\":\"title\",\"label\":\"标题文本\",\"data\":\"\"},{\"field\":\"textAreaLabel\",\"label\":\"内容文本\",\"data\":\"\"},{\"field\":\"position\",\"label\":\"显示位置\",\"data\":\"follow-title\"}]}]},{\"title\":\"背景\",\"field\":\"containerBackground\",\"spread\":false,\"show\":True,\"scope\":\"container.background\",\"items\":[{\"field\":\"backgroundColor\",\"label\":\"背景颜色\",\"data\":\"#FFFFFF\",\"scope\":\"container.background.backgroundColor\"}]},{\"title\":\"边框\",\"field\":\"containerBorder\",\"spread\":false,\"show\":false,\"scope\":\"container.border\",\"items\":[{\"field\":\"border\",\"label\":\"\",\"data\":{\"borderColor\":\"\",\"borderStyle\":\"solid\",\"borderWidth\":0},\"scope\":\"container.border.borderStyle\"}]}]",
    #                             "data": {
    #                                 "aggregation": 1,
    #                                 "default_value": "",
    #                                 "enable_subtotal": 0,
    #                                 "enable_summary": 0,
    #                                 "chart_default_value": [],
    #                                 "filter_relation": 0,
    #                                 "enable_subtotal_row_summary": 0,
    #                                 "enable_subtotal_col": 0,
    #                                 "enable_subtotal_row": 0,
    #                                 "enable_subtotal_col_summary": 0,
    #                                 "reset_field_sort": 1,
    #                                 "data_type": {"logic_type": "nondataset"},
    #                                 "datasource": {"id": "", "type": ""},
    #                                 "indicator": {
    #                                     "dims": [],
    #                                     "nums": [],
    #                                     "comparisons": [],
    #                                     "filters": [],
    #                                     "zaxis": [],
    #                                     "chart_params": [],
    #                                     "desires": [],
    #                                     "marklines": [],
    #                                     "chart_vars": [],
    #                                     "field_sorts": [],
    #                                 },
    #                             },
    #                             "funcSetup": {"display_item": "", "refresh_rate": ""},
    #                             "export_type": ["data"],
    #                         },
    #                         {
    #                             "id": "b89a05a5-f8cc-11ea-bed3-db996a9ba9e9",
    #                             "name": "搜索筛选-1",
    #                             "parent_chart_id": "b29334bc-f8cc-11ea-bed3-db996a9ba9e9",
    #                             "children_chart_ids": [],
    #                             "level_code": "",
    #                             "chart_type": "filter",
    #                             "chart_component_code": "search_filter",
    #                             "page_size": 0,
    #                             "position": {
    #                                 "i": "b89a05a5-f8cc-11ea-bed3-db996a9ba9e9",
    #                                 "col": 0,
    #                                 "row": 2,
    #                                 "size_x": 6,
    #                                 "size_y": 2,
    #                             },
    #                             "config": "",
    #                             "data": {
    #                                 "aggregation": 1,
    #                                 "default_value": "",
    #                                 "enable_subtotal": 0,
    #                                 "enable_summary": 0,
    #                                 "chart_default_value": [],
    #                                 "filter_relation": 0,
    #                                 "enable_subtotal_row_summary": 0,
    #                                 "enable_subtotal_col": 0,
    #                                 "enable_subtotal_row": 0,
    #                                 "enable_subtotal_col_summary": 0,
    #                                 "reset_field_sort": 1,
    #                                 "data_type": {"logic_type": "assist"},
    #                                 "datasource": {"id": "", "type": ""},
    #                                 "indicator": {
    #                                     "dims": [],
    #                                     "nums": [],
    #                                     "comparisons": [],
    #                                     "filters": [],
    #                                     "zaxis": [],
    #                                     "chart_params": [],
    #                                     "desires": [],
    #                                     "marklines": [],
    #                                     "chart_vars": [],
    #                                     "field_sorts": [],
    #                                 },
    #                             },
    #                             "funcSetup": {"display_item": "", "refresh_rate": ""},
    #                             "export_type": ["data"],
    #                         },
    #                         {
    #                             "id": "b89a05af-f8cc-11ea-bed3-db996a9ba9e9",
    #                             "name": "时间筛选-1",
    #                             "parent_chart_id": "b29334bc-f8cc-11ea-bed3-db996a9ba9e9",
    #                             "children_chart_ids": [],
    #                             "level_code": "",
    #                             "chart_type": "filter",
    #                             "chart_component_code": "date_interval_filter",
    #                             "page_size": 0,
    #                             "position": {
    #                                 "i": "b89a05af-f8cc-11ea-bed3-db996a9ba9e9",
    #                                 "col": 6,
    #                                 "row": 2,
    #                                 "size_x": 6,
    #                                 "size_y": 6,
    #                             },
    #                             "config": "",
    #                             "data": {
    #                                 "aggregation": 1,
    #                                 "default_value": "",
    #                                 "enable_subtotal": 0,
    #                                 "enable_summary": 0,
    #                                 "chart_default_value": [],
    #                                 "filter_relation": 0,
    #                                 "enable_subtotal_row_summary": 0,
    #                                 "enable_subtotal_col": 0,
    #                                 "enable_subtotal_row": 0,
    #                                 "enable_subtotal_col_summary": 0,
    #                                 "reset_field_sort": 1,
    #                                 "data_type": {"logic_type": "nondataset"},
    #                                 "datasource": {"id": "", "type": ""},
    #                                 "indicator": {
    #                                     "dims": [],
    #                                     "nums": [],
    #                                     "comparisons": [],
    #                                     "filters": [],
    #                                     "zaxis": [],
    #                                     "chart_params": [],
    #                                     "desires": [],
    #                                     "marklines": [],
    #                                     "chart_vars": [],
    #                                     "field_sorts": [],
    #                                 },
    #                             },
    #                             "funcSetup": {"display_item": "", "refresh_rate": ""},
    #                             "export_type": ["data"],
    #                         },
    #                         {
    #                             "id": "b89a05b7-f8cc-11ea-bed3-db996a9ba9e9",
    #                             "name": "下拉筛选-1",
    #                             "parent_chart_id": "b29334bc-f8cc-11ea-bed3-db996a9ba9e9",
    #                             "children_chart_ids": [],
    #                             "level_code": "",
    #                             "chart_type": "filter",
    #                             "chart_component_code": "select_filter",
    #                             "page_size": 0,
    #                             "position": {
    #                                 "i": "b89a05b7-f8cc-11ea-bed3-db996a9ba9e9",
    #                                 "col": 0,
    #                                 "row": 4,
    #                                 "size_x": 6,
    #                                 "size_y": 2,
    #                             },
    #                             "config": "",
    #                             "data": {
    #                                 "aggregation": 1,
    #                                 "default_value": "",
    #                                 "enable_subtotal": 0,
    #                                 "enable_summary": 0,
    #                                 "chart_default_value": [],
    #                                 "filter_relation": 0,
    #                                 "enable_subtotal_row_summary": 0,
    #                                 "enable_subtotal_col": 0,
    #                                 "enable_subtotal_row": 0,
    #                                 "enable_subtotal_col_summary": 0,
    #                                 "reset_field_sort": 1,
    #                                 "data_type": {"logic_type": "default"},
    #                                 "datasource": {"id": "", "type": ""},
    #                                 "indicator": {
    #                                     "dims": [],
    #                                     "nums": [],
    #                                     "comparisons": [],
    #                                     "filters": [],
    #                                     "zaxis": [],
    #                                     "chart_params": [],
    #                                     "desires": [],
    #                                     "marklines": [],
    #                                     "chart_vars": [],
    #                                     "field_sorts": [],
    #                                 },
    #                             },
    #                             "funcSetup": {"display_item": "", "refresh_rate": ""},
    #                             "export_type": ["data"],
    #                         },
    #                     ],
    #                     "terminal_type": "",
    #                     "var_value_sources": [],
    #                 }
    #             ],
    #             "download": True,
    #         }
    #     }
    #     metadata = kwargs.get('metadata')
    #     if not metadata:
    #         raise UserError(message="请指定需要更改的元数据!")
    #     screens = metadata.get('screens')
    #     if not isinstance(screens, list) or len(screens) < 1:
    #         raise UserError(message="参数数据格式非法!")
    #     metadata['first_report'] = screens[0]
    #     metadata['screens'] = []
    #     result, errors = dashboard_service.update_metadata(metadata)
    #     data = {"errors": errors if errors else []}
    #     print([result, '', data])
