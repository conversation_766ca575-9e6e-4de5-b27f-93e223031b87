# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-
# # @Time     : 2021/07/21 09:31
# # <AUTHOR> wangfei
# # @File     : test_dashboard_editor.py
# import json
# import os
# from copy import deepcopy
#
# os.environ['prometheus_multiproc_dir'] = '/tmp'
# from tests.base_test import BaseTest, patch_property
# from dashboard_chart.services.chart_service import get_chart_result
# from dashboard_chart.models import ChartDataModel
# from dashboard_chart.data_query.charts import chart_factory
# from dashboard_chart.services.chart_service import assign_dashboard_chart_model
#
# from dmplib.hug import g
# from dmplib.utils.errors import UserError
# from unittest.mock import patch
# import pandas as pd
#
#
# class TestQueryData(BaseTest):
#     """
#     解析json数据类的测试
#     """
#
#     def __init__(self, method_name="runTest"):
#         super().__init__(method_name, code='test', account='test')
#
#     def setUp(self):
#         super().setUp()
#         self.dashboard_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
#         token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************.s8ijllLyrobkMUInVaxJdYeO0s4M7b5J4D03S7zc_Z4'
#         g.cookie = {'token': token}
#         g.code = 'local'
#         g.userid = '39fdf2bb-6219-3306-4013-e60a8cfdfd0d'
#
#     # def get_user_id(self, code):
#
#     # 测试没有数据集
#     def test_no_dataset(self):
#         model = ChartDataModel(**{'dashboard_id': self.dashboard_id})
#         result = get_chart_result(model, no_data=True, reassign_model=False)
#         self.assertEqual(result['msg'], '没有配置数据集')
#
#     # 没有配置维度和度量
#     def test_no_dims_nums(self):
#         model = ChartDataModel(**{'dashboard_id': self.dashboard_id, 'dataset_id': self.dashboard_id})
#         result = get_chart_result(model, no_data=True, reassign_model=False)
#         self.assertEqual(result['msg'], '没有配置维度和度量')
#
#     # 创建create_chart类
#     @patch('dashboard_chart.repositories.dashboard_repository.get_chart_code_and_type_code_by_chart_id')
#     def test_create_chart(self, mock_chart):
#         model = ChartDataModel(**{
#             'dashboard_id': self.dashboard_id,
#             'dataset_id': 'fake-dataset-id',
#             'chart_code': 'excel_table',
#             'aggregation': True,
#         })
#         # 根据chart_code进行实例化
#         model = chart_factory.ChartFactory.create_chart(model.id, model)
#         self.assertIsInstance(model, chart_factory.ExcelTableChart)
#
#         model = ChartDataModel(**{
#             'dashboard_id': self.dashboard_id,
#             'dataset_id': 'fake-dataset-id',
#             'chart_code': '',
#             'data_logic_type_code': 'default',
#             'aggregation': True,
#         })
#         # 根据data_logic_type_code实例化
#         model = chart_factory.ChartFactory.create_chart(model.id, model)
#         self.assertIsInstance(model, chart_factory.CommonChart)
#
#         # 没有chart_id
#         c_model = ChartDataModel(**{
#             'dashboard_id': self.dashboard_id,
#             'dataset_id': 'fake-dataset-id',
#             'chart_code': '',
#             'data_logic_type_code': '',
#             'aggregation': True,
#         })
#         try:
#             model = chart_factory.ChartFactory.create_chart(c_model.id, c_model)
#         except UserError as e:
#             self.assertIn('实例化单图类时单图id为空', e.message)
#
#         # 无法找到单图信息
#         mock_chart.return_value = {}
#         try:
#             model = chart_factory.ChartFactory.create_chart(self.fake_data('id'), c_model)
#         except UserError as e:
#             self.assertIn('无法找到单图信息', e.message)
#
#         # dashboard_chart/data_query/charts/chart_factory.py:70
#         mock_chart.return_value = {'chart_code': 'excel_table'}
#         model = chart_factory.ChartFactory.create_chart(self.fake_data('id'), c_model)
#         self.assertIsInstance(model, chart_factory.ExcelTableChart)
#
#         mock_chart.return_value = {'data_logic_type_code': 'default'}
#         model = chart_factory.ChartFactory.create_chart(self.fake_data('id'), c_model)
#         self.assertIsInstance(model, chart_factory.CommonChart)
#
#         mock_chart.return_value = {'data_logic_type_code': self.fake_data('code')}
#         try:
#             model = chart_factory.ChartFactory.create_chart(self.fake_data('id'), c_model)
#         except UserError as e:
#             self.assertIn('实例化单图类失败', e.message)
#
#     # dashboard_chart/data_query/charts/assist_chart.py
#     def test_data_query_charts_assist_chart_file(self):
#         from dashboard_chart.data_query.charts.assist_chart import AssistChart
#         c_model = ChartDataModel(**{
#             'dashboard_id': self.dashboard_id,
#             'dataset_id': 'fake-dataset-id',
#             'chart_code': '',
#             'data_logic_type_code': '',
#             'aggregation': True,
#             'dims': [],
#         })
#         result = AssistChart(c_model).get_select_fields(c_model)
#         self.assertEqual(result, [])
#
#         result = AssistChart(c_model).get_group_fields(c_model)
#         self.assertEqual(result, [])
#
#         c_model.aggregation = False
#         result = AssistChart(c_model).get_group_fields(c_model)
#         self.assertEqual(result, [])
#
#     # dashboard_chart/data_query/charts/interval_chart.py
#     def test_data_query_charts_interval_chart_file(self):
#         from dashboard_chart.data_query.charts.interval_chart import IntervalChart
#
#         c_model = self._create_model('query_data')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = IntervalChart(c_model)
#
#         result = chart.query_variable_chart_data()
#         self.assertEqual(result['code'], 200)
#
#         data = chart.get_data(result, 0)
#         self.assertIn('data', data)
#
#         r1 = chart.get_group_fields(c_model)
#         self.assertEqual(r1, [])
#
#         r2 = chart.get_variable_chart_data()
#         self.assertIn('data', r2)
#         # self.assertEqual(r1, [])
#
#         r3 = chart.get_subtotal_for_struct()
#         self.assertEqual({}, r3)
#
#         r4 = chart.restructure_chart_data(pd.DataFrame(), [])
#         self.assertIsInstance(r4, list)
#
#         chart.chart_data_model.dims = []
#         r5 = chart.restructure_chart_data(pd.DataFrame(), [])
#         self.assertEqual(r5, [])
#
#     # dashboard_chart/data_query/charts/nondataset_chart.py
#     def test_data_query_charts_nondataset_chart_file(self):
#         from dashboard_chart.data_query.charts.nondataset_chart import NondatasetChart
#
#         c_model = ChartDataModel(**{
#             'dashboard_id': self.dashboard_id,
#             'dataset_id': 'fake-dataset-id',
#             'chart_code': '',
#             'data_logic_type_code': '',
#             'aggregation': True,
#             'dims': [],
#         })
#         chart = NondatasetChart(c_model)
#
#         r1 = chart.get_chart_data()
#         self.assertEqual(r1, {})
#
#         r2 = chart.get_variable_chart_data()
#         self.assertEqual(r2, {})
#
#         r3 = chart.get_item_list()
#         self.assertEqual(r3, [])
#
#         r4, r5 = chart.apply_funcs([])
#         self.assertEqual(r5, [])
#
#         r6 = chart.get_query_struct()
#         self.assertEqual(r6, {})
#
#         r7 = chart.get_marklines_for_data(pd.DataFrame(), [])
#         self.assertEqual(r7, [])
#
#         r8 = chart.get_subtotal_for_struct()
#         self.assertEqual(r8, {})
#
#         r9, _, _ = chart.get_subtotal_for_data(pd.DataFrame(), pd.DataFrame(), [], [])
#         self.assertEqual(r9, {})
#
#     # dashboard_chart/data_query/charts/label_filter_chart.py
#     def test_data_query_charts_label_filter_chart_file(self):
#         from dashboard_chart.data_query.charts.label_filter_chart import LabelFilterChart
#
#         mock_data = """{"dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_params":[{"id":"95d78fac-f408-11eb-a6f8-379ee47942d0","report_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_code":"label_filter","data_logic_type_code":"assist","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"label_filter_conditions":[{"dataset_field_id":"39fcd381-2659-b699-8de1-2d50a028e774","col_value":"[\\"B\\"]","operator":"in","col_name":"XM_6256763489","data_type":"字符串","currentField":"39fcd381-2659-b699-8de1-2d50a028e774","dim":{"id":"97a54ac9-f408-11eb-a6f8-379ee47942d0","dataset_id":"39fcd380-f77a-358c-3e20-9d42cf85ca1f","alias_name":"项目","col_name":"XM_6256763489","origin_col_name":"项目","origin_field_type":"varchar(765)","origin_table_id":null,"origin_table_comment":null,"origin_table_name":null,"origin_table_alias_name":null,"data_type":"字符串","group_type":"","rank":0,"visible":1,"field_group":"维度","format":"","type":"普通","inspection_rules":"","note":null,"include_vars":[],"text":"项目","parent_id":"维度","alias":"项目","hidden":false,"formula_mode":"","display_format":{"column_unit_name":"","display_mode":"dim","display_way":"text","show_zero_way":"default","show_null_way":"default","show_empty_way":"default","image_height":72,"image_width":72,"thousand_point_separator":1,"fixed_decimal_places":0,"unit":"无","hidden_unit":0,"justify_decimal":0,"justify_decimal_places":0,"smart_num_format":0,"separate_config_decimal_places":0,"num_valid_places":4},"chart_code":"","dim":"39fcd381-2659-b699-8de1-2d50a028e774","dashboard_chart_id":"95d78fac-f408-11eb-a6f8-379ee47942d0","is_subtotal_cate":0,"dim_type":0},"formula_mode":""}],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = LabelFilterChart(c_model)
#
#         r1 = chart.query_data()
#         self.assertIsInstance(r1, list)
#
#         r2 = chart.get_data(r1, 0, c_model)
#         self.assertIsInstance(r2, list)
#
#         r8 = chart.get_subtotal_for_struct()
#         self.assertEqual(r8, {})
#
#         chart1 = LabelFilterChart(c_model)
#         chart1.chart_data_model.label_filter_conditions = []
#         r3 = chart1.query_data()
#         self.assertIsInstance(r3, list)
#
#         # chart2 = LabelFilterChart(c_model)
#         # chart2.chart_data_model.label_filter_conditions = [{'dataset_field_id': '39fcd381-2659-b699-8de1-2d50a028e777', 'col_value': '["B"]', 'operator': 'in', 'col_name': 'XM_6256763489', 'data_type': '字符串', 'currentField': '39fcd381-2659-b699-8de1-2d50a028e774', 'dim': {'id': '97a54ac9-f408-11eb-a6f8-379ee47942d0', 'dataset_id': '39fcd380-f77a-358c-3e20-9d42cf85ca1f', 'alias_name': '项目', 'col_name': 'XM_6256763489', 'origin_col_name': '项目', 'origin_field_type': 'varchar(765)', 'origin_table_id': None, 'origin_table_comment': None, 'origin_table_name': None, 'origin_table_alias_name': None, 'data_type': '字符串', 'group_type': '', 'rank': 0, 'visible': 1, 'field_group': '维度', 'format': '', 'type': '普通', 'inspection_rules': '', 'note': None, 'include_vars': [], 'text': '项目', 'parent_id': '维度', 'alias': '项目', 'hidden': False, 'formula_mode': '', 'display_format': {'column_unit_name': '', 'display_mode': 'dim', 'display_way': 'text', 'show_zero_way': 'default', 'show_null_way': 'default', 'show_empty_way': 'default', 'image_height': 72, 'image_width': 72, 'thousand_point_separator': 1, 'fixed_decimal_places': 0, 'unit': '无', 'hidden_unit': 0, 'justify_decimal': 0, 'justify_decimal_places': 0, 'smart_num_format': 0, 'separate_config_decimal_places': 0, 'num_valid_places': 4}, 'chart_code': '', 'dim': '39fcd381-2659-b699-8de1-2d50a028e774', 'dashboard_chart_id': '95d78fac-f408-11eb-a6f8-379ee47942d0', 'is_subtotal_cate': 0, 'dim_type': 0}, 'formula_mode': ''}]
#         # r4 = chart2.query_data()
#         # self.assertIsInstance(r4, list)
#
#         d1 = [{'dataset_field_id': self.fake_data('id')}]
#         with  patch_property(chart.chart_data_model, 'label_filter_conditions', d1):
#             try:
#                 chart.batch_query_in_filter_condition()
#             except UserError as e:
#                 self.assertIn('数据异常', e.message)
#
#     # dashboard_chart/data_query/charts/excel_table_chart.py
#     def test_data_query_charts_excel_table_chart_file(self):
#         from dashboard_chart.data_query.charts.excel_table_chart import ExcelTableChart
#
#         mock_data = """{"id":"3907a4fb-ef70-11eb-b999-1125aade6399","report_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-037f-7cf9-eadc-bc8126258602","dataset_field_id":"39fcd383-2d1b-0603-5b52-a6f89d8117af","data_type":"字符串","col_name":"XMID_7206642225","alias_name":"项目id","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-037f-7cf9-eadc-bc8126258602","dataset_field_id":"39fcd383-2d1b-07c3-2a4a-093b7aeb120b","data_type":"字符串","col_name":"FQID_14567916458","alias_name":"分期id","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-037f-7cf9-eadc-bc8126258602","dataset_field_id":"39fcd3a3-ebcc-3a77-ff9e-36547ca57380","data_type":"字符串","col_name":"QT_7582755332","alias_name":"其他","order":2,"rank":2,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-037f-7cf9-eadc-bc8126258602","dataset_field_id":"39fcd3a3-ebcc-3b62-529e-5de862c0e1e1","data_type":"字符串","col_name":"CS_8039023338","alias_name":"参数","order":3,"rank":3,"col_type":"dim","is_show":1,"group":null}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data))
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = ExcelTableChart(c_model)
#
#         r1 = chart.get_chart_data()
#         self.assertIsInstance(r1, dict)
#
#         r2 = chart.get_subtotal_for_struct()
#         self.assertEqual(r2, {})
#
#         r3 = chart._recursive_handle_none_value([])
#         self.assertEqual(r3, [])
#
#         r4 = chart._get_alias_name_by_field({
#             'alias': self.fake_data('alias'),
#         })
#         self.assertEqual(r4, self.fake_data('alias'))
#
#         r5 = chart._get_alias_name_by_field({
#             'col_name': self.fake_data('col_name'),
#         })
#         self.assertEqual(r5, self.fake_data('col_name'))
#
#         try:
#             c_model.aggregation = False
#             chart1 = ExcelTableChart(c_model)
#         except UserError as e:
#             self.assertIn('逻辑错误，透视表必须为聚合类', e.message)
#
#     # dashboard_chart/data_query/charts/comparision_line_chart.py
#     def test_data_query_charts_comparision_line_chart_file(self):
#         from dashboard_chart.data_query.charts.comparision_line_chart import ComparisionLineChart, ChartDataModel
#         from dashboard_chart.convertor.limit.limit import Limit, LimitField
#         import functools
#
#         mock_data = """{"dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_params":[{"id":"0ddfeed6-f4cb-11eb-ba21-1795d3520ff7","report_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         chart = ComparisionLineChart(c_model)
#
#         r1 = chart.get_chart_data()
#         self.assertIsInstance(r1, dict)
#
#         r2 = chart.get_subtotal_for_struct()
#         self.assertEqual(r2, {})
#
#         mock_value = LimitField()
#         mock_value.offset = -9999
#         mock_value.limit = 100
#         with patch.object(Limit, 'get_limit_field_for_default', return_value=mock_value) as mm:
#             chart1 = ComparisionLineChart(c_model)
#             r3 = chart1.get_limit_field()
#             self.assertEqual(bool(r3), True)
#
#         # dashboard_chart/data_query/charts/comparision_line_chart.py:45
#         chart2 = ComparisionLineChart(c_model)
#         chart2.chart_data_model.comparisons = [{}]
#         r4 = chart1._get_structure_data([])
#         self.assertEqual(r4, [])
#
#         with patch.object(functools, 'reduce', return_value=20000) as mm:
#             chart3 = ComparisionLineChart(deepcopy(_c_model))
#             chart3.chart_data_model.comparisons = []
#             chart3.over_limit_flag = True
#             r5 = chart3._get_structure_data([])
#             self.assertEqual(r5, [])
#
#     # dashboard_chart/data_query/charts/common_chart.py
#     def test_data_query_charts_common_chart_file(self):
#         from dashboard_chart.data_query.charts.common_chart import CommonChart
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         chart = CommonChart(c_model)
#
#         r1 = chart.get_chart_data()
#         self.assertIsInstance(r1, dict)
#
#         r2 = chart.get_query_struct()
#         self.assertIsInstance(r2, dict)
#
#         r3 = chart.get_variable_chart_data()
#         self.assertIsInstance(r3, dict)
#
#         r4 = chart.query_underlying_data()
#         self.assertIsInstance(r4, tuple)
#
#         r5 = chart.get_item_list()
#         self.assertIsInstance(r5, dict)
#
#         r6 = chart._data2dict({'test-1': {'test-2': 'test-3'}})
#         self.assertIsInstance(r6, dict)
#
#         r7 = chart._data2dict([['t1', 't2']])
#         self.assertIsInstance(r7, list)
#
#     # dashboard_chart/data_query/charts/column_chart.py
#     def test_data_query_column_chart_file(self):
#         from dashboard_chart.data_query.charts.column_chart import ColumnChart
#         from dashboard_chart.data_query.charts.common_chart import DatasetQuery
#         from dashboard_chart.convertor.field_types import LimitField
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = ColumnChart(c_model)
#
#         r1 = chart.get_chart_data()
#         self.assertIsInstance(r1, dict)
#
#         r2 = chart._op_simple_column_header(
#             self.fake_data('col'),
#             {self.fake_data('col'): LimitField()},
#             {}
#         )
#         self.assertIn('header', r2)
#
#         try:
#             chart._op_simple_column_header(
#                 self.fake_data('col'),
#                 {self.fake_data('col-1'): LimitField()},
#                 {}
#             )
#         except UserError as e:
#             self.assertIn('的字段不存在', e.message)
#
#         # dashboard_chart/data_query/charts/column_chart.py:157
#         class CusColumnChart(ColumnChart):
#             def get_pd_data(self):
#                 dataset_query = DatasetQuery(chart_data_model=self.chart_data_model, user_id=self.get_user_id())
#                 result = dataset_query.get_item_list(self.get_limit_field())
#                 data = result.get("data")
#                 return pd.DataFrame(data)
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart1 = CusColumnChart(c_model)
#
#         r3 = chart1._op_cross_headers(chart1.get_pd_data())
#         self.assertIsInstance(r3, pd.DataFrame)
#
#         # dashboard_chart/data_query/charts/column_chart.py:127
#         r4 = chart._get_cross_header_column_sort_dict(
#             [1, 2],
#             [8, 9],
#             [[1, 2], [3, 2], [4, 2], [5, 2]],
#             {},
#             1
#         )
#         self.assertEqual(r4, None)
#
#         r5 = chart.restructure_chart_data(pd.DataFrame([]), [])
#         self.assertEqual(r5, [])
#
#         chart.chart_data_model.aggregation = False
#         self.assertEqual(chart.get_master_id_order(), True)
#
#         chart.chart_data_model.pre_comparison = False
#         chart.chart_data_model.comparisons = False
#         self.assertIsInstance(chart._op_cross_headers(pd.DataFrame([])), pd.DataFrame)
#
#     # dashboard_chart/data_query/charts/chart_query.py
#     def test_data_query_chart_query_file(self):
#         # from dashboard_chart.data_query.charts.chart_query import ChartQuery
#         from dashboard_chart.data_query.charts.nondataset_chart import NondatasetChart
#         from dashboard_chart.convertor.limit.limit import Limit, LimitField
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = NondatasetChart(c_model)
#
#         g.userid = self.fake_data('id')
#         r1 = chart.get_user_id()
#         self.assertEqual(r1, self.fake_data('id'))
#         delattr(g, 'userid')
#
#         # dashboard_chart/data_query/charts/chart_query.py:103
#         # TODO  覆盖不了，死循环
#         # with patch.object(Limit, 'get_limit_field') as mm:
#         #     mock_value = LimitField()
#         #     mock_value.offset = -9999
#         #     mock_value.limit = 100
#         #     mm.return_value = mock_value
#         #
#         #     chart1 = NondatasetChart(c_model)
#         #     r3 = chart1.get_limit_field()
#         #     self.assertEqual(bool(r3), True)
#
#         r4 = chart.get_having_fields()
#         self.assertEqual(r4, [])
#
#         chart.get_chart_data()
#
#     # dashboard_chart/data_query/charts/statistic_table_chart.py
#     def test_data_query_statistic_table_chart_file(self):
#         from dashboard_chart.data_query.charts.statistic_table_chart import StatisticTableChart
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = StatisticTableChart(c_model)
#
#         r1 = chart.query_data()
#         self.assertEqual(200, r1['code'])
#
#         r2 = chart.get_chart_data()
#         self.assertIsInstance(r2, dict)
#
#         r3 = chart.get_subtotal_for_struct()
#         self.assertIn('subtotal_summary', r3)
#
#     # dashboard_chart/data_query/charts/test_select_filter_chart.py
#     def test_data_query_select_filter_chart_file(self):
#         from dashboard_chart.data_query.charts.select_filter_chart import SelectFilterChart
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = SelectFilterChart(c_model)
#
#         r1 = chart.query_data()
#         self.assertEqual(200, r1['code'])
#
#         r2 = chart.get_chart_data()
#         self.assertIsInstance(r2, dict)
#
#         r3 = chart.get_subtotal_for_struct()
#         self.assertEqual({}, r3)
#
#         r4 = chart.get_variable_chart_data()
#         self.assertIn('data', r4)
#
#         # penetrate_conditions = [
#         #     {
#         #         "col_name": "col1",
#         #         "col_value": "第一事业部",
#         #         "operator": "=",
#         #         "dim": {
#         #             "visible": 1,
#         #             "dashboard_chart_id": "bfd64f08-3644-11e9-8dff-a1dee455e1fc",
#         #             "alias_name": "事业部",
#         #             "field_group": "维度",
#         #             "expression": "",
#         #             "dataset_id": "39e63c4b-0d6a-7a67-b782-2e8acec10c67",
#         #             "dataset_field_id": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
#         #             "type": "普通",
#         #             "dim": "39e63c4b-0df1-6c98-dd79-106f0597fecc",
#         #             "alias": "事业部",
#         #             "content": "",
#         #             "col_name": "col1",
#         #             "data_type": "字符串",
#         #             "id": "aba177cd-3645-11e9-8dff-a1dee455e1fc",
#         #             "rank": 0,
#         #             "formula_mode": "",
#         #             "sort": "",
#         #             "note": ""
#         #         }
#         #     }
#         # ]
#         chart.chart_data_model.dims += chart.chart_data_model.dims
#         chart.chart_data_model.penetrate_conditions = [{}]
#         r5 = chart.get_chart_data()
#         self.assertIsInstance(r5, dict)
#
#         with patch.dict(chart.dataset_field, {}, clear=True):
#             r6 = chart.get_chart_data()
#             self.assertEqual(r6, {})
#
#         with patch.dict(chart.dataset_field, {}, clear=True):
#             r7 = chart.get_variable_chart_data()
#             self.assertEqual(r7, {})
#
#         chart.chart_data_model.dims += chart.chart_data_model.dims
#         r8 = chart.get_variable_chart_data()
#         self.assertIsInstance(r8, dict)
#
#     # dashboard_chart/data_query/charts/pagination_table_chart.py
#     def test_data_query_pagination_table_chart_file(self):
#         from dashboard_chart.data_query.charts.pagination_table_chart import PaginationTableChart
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = PaginationTableChart(c_model)
#
#         r1 = chart.query_data()
#         self.assertEqual(200, r1['code'])
#
#         group_fields = chart.get_group_fields(c_model)
#         with patch.object(PaginationTableChart, 'get_group_fields', ) as mm:
#             group_fields[0].field = self.fake_data('id')
#             mm.return_value = group_fields
#             chart1 = PaginationTableChart(c_model)
#             r3 = chart1.get_order_fields_for_multi(c_model)
#             self.assertEqual(bool(r3), True)
#
#     # dashboard_chart/data_query/charts/pagination_table_yk_chart.py
#     def test_data_query_pagination_table_yk_chart_file(self):
#         from dashboard_chart.data_query.charts.pagination_table_yk_chart import PaginationTableYkChart
#
#         c_model = self._create_model('query_data_sample_1')
#         assign_dashboard_chart_model(c_model, no_data=True)
#         chart = PaginationTableYkChart(c_model)
#
#         r1 = chart.query_data()
#         self.assertEqual(200, r1['code'])
#
#     # dashboard_chart/data_query/widget/subtotal/subtotal_col_query.py
#     def test_data_query_subtotal_col_query_file(self):
#         from dashboard_chart.data_query.charts.common_chart import CommonChart
#         from dashboard_chart.data_query.widget.subtotal.subtotal_col_query import SubtotalColQuery, handle_g
#
#         mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"7f2472a1-fa43-11eb-8cdf-b1878405dec1","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd391-7254-9d8e-054f-4b4418a541cc","data_type":"字符串","col_name":"LX_8957975949","alias_name":"类型","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":3,"rank":3,"col_type":"num","is_show":1,"group":null}]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         chart = CommonChart(c_model)
#
#         r1 = chart.get_chart_data()
#         self.assertIsInstance(r1, dict)
#
#         @handle_g
#         def f1():
#             return self.fake_data('x1')
#
#         self.assertEqual(f1(), self.fake_data('x1'))
#
#         scq = SubtotalColQuery(
#             chart.chart_data_model,
#             chart.get_select_fields(),
#             chart.get_where_fields(),
#             chart.get_order_fields_for_multi(chart.chart_data_model),
#             chart.get_dataset_vars(chart.chart_data_model),
#             chart.get_user_id(),
#         )
#         # with patch.object(PaginationTableChart, 'get_group_fields', ) as mm:
#         #     group_fields[0].field = self.fake_data('id')
#         #     mm.return_value = group_fields
#         r2 = scq.get_subtotal_groups()
#         self.assertIsInstance(r2, list)
#
#         old_column_display_map = scq._get_column_display_map()
#         with patch.object(SubtotalColQuery, '_get_column_display_map') as mm:
#             old_column_display_map[list(old_column_display_map.keys())[0]]['is_show'] = 0
#             mm.return_value = old_column_display_map
#             r3 = scq._get_uncompute_subtotal_fields_v3()
#             self.assertGreaterEqual(len(r3), 1)
#
#         with patch.object(SubtotalColQuery, '_compare_subtotal_dim_and_nums_display_weight') as mm:
#             mm.return_value = 1
#             r3 = scq._get_uncompute_subtotal_fields_v3()
#             self.assertGreaterEqual(len(r3), 1)
#
#         column_display_map = {('39fcd383-d543-d4d0-9ec4-99ee8072bd08', 'dim'): {'order': 10, 'is_show': 1},
#                               ('39fcd391-7254-9d8e-054f-4b4418a541cc', 'dim'): {'order': 1, 'is_show': 1},
#                               ('39fcd383-d543-da7c-5d10-18709d914703', 'num'): {'order': 2, 'is_show': 1},
#                               ('39fcd383-d543-db36-60a2-3303732bfe1f', 'num'): {'order': 3, 'is_show': 1}}
#         r4 = scq._compare_subtotal_dim_and_nums_display_weight(column_display_map,
#                                                                '39fcd383-d543-d4d0-9ec4-99ee8072bd08')
#         self.assertEqual(r4, 1)
#
#         with patch_property(scq._chart_data_model, 'column_display', []) as f:
#             r5 = scq._get_column_display_map()
#             self.assertIsInstance(r5, dict)
#
#         r6 = scq._get_num_field_dict()
#         self.assertIsInstance(r6, dict)
#
#         r7 = scq._get_dim_headers()
#         self.assertIsInstance(r7, list)
#
#         r8 = scq._is_dim_header(())
#         self.assertIsInstance(r8, bool)
#
#         # r9 = scq._get_data_index_headers(pd.DataFrame([[1,2,3],[4,5,6],[7,8,9]]))
#         # self.assertIsInstance(r9, bool)
#         r10 = scq.get_subtotal_base_select_fields_dict()
#         self.assertIsInstance(r10, dict)
#
#         r11 = scq.get_subtotal_base_group_dims()
#         self.assertIsInstance(r11, list)
#
#         r12 = scq.get_subtotal_base_group_fields()
#         self.assertIsInstance(r12, list)
#
#         # from dmplib import  config
#         # print(config.get('Function.subtotal_query_method', 'serial') )
#         #
#         # with patch_property(config, 'get', lambda x, y: '666') as f:
#         #     print(config.get('Function.subtotal_query_method', 'serial'))
#         #     self.assertIsInstance(r5, dict)
#         #
#         # print(config.get('Function.subtotal_query_method', 'serial'))
#         #
#         # r13 = scq._get_header_from_multi([1,2,3],[4,5,6], {1: Field()})
#         # self.assertIsInstance(r13, list)
#
#     # dashboard_chart/data_query/widget/count_query.py
#     def test_data_query_count_query_file(self):
#         from dashboard_chart.data_query.widget.count_query import CountQuery
#         from dashboard_chart.agent.query_agent import QueryAgent
#
#         mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"7f2472a1-fa43-11eb-8cdf-b1878405dec1","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd391-7254-9d8e-054f-4b4418a541cc","data_type":"字符串","col_name":"LX_8957975949","alias_name":"类型","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":3,"rank":3,"col_type":"num","is_show":1,"group":null}]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         chart = CountQuery(c_model, g.userid)
#
#         with patch.object(QueryAgent, 'query') as f:
#             f.return_value = {'code': 300}
#             try:
#                 r1 = chart.query_count([], [])
#             except UserError as e:
#                 self.assertIn('单图取数异常', e.message)
#
#         with patch.object(QueryAgent, 'query') as f, \
#                 patch_property(chart._chart_data_model, 'external_subject_ids', [1, 2]):
#             f.return_value = {'code': 200, 'query_structure': {'group_by': 0}, 'data': [{'total': 666}]}
#             r2 = chart.query_count([], [])
#             self.assertEqual(r2, 666)
#
#         with patch.object(QueryAgent, 'query') as f, \
#                 patch_property(chart._chart_data_model, 'aggregation', []):
#             f.return_value = {'code': 200, 'query_structure': {'group_by': 0}, 'data': []}
#             r3 = chart.query_count([], [])
#             self.assertEqual(r3, 0)
#
#         with patch.object(QueryAgent, 'query') as f, \
#                 patch_property(chart._chart_data_model, 'aggregation', []):
#             f.return_value = {'code': 200, 'query_structure': {'group_by': 0}, 'data': [{'total': 777}]}
#             r2 = chart.query_count([], [])
#             self.assertEqual(r2, 777)
#
#     # dashboard_chart/data_query/widget/count_result_query.py
#     def test_data_query_count_result_query_file(self):
#         from dashboard_chart.data_query.widget.count_result_query import CountResultQuery
#         mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"7f2472a1-fa43-11eb-8cdf-b1878405dec1","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd391-7254-9d8e-054f-4b4418a541cc","data_type":"字符串","col_name":"LX_8957975949","alias_name":"类型","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":3,"rank":3,"col_type":"num","is_show":1,"group":null}]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         chart = CountResultQuery(c_model, g.userid)
#
#         r1 = chart.query_count(data_frame=pd.DataFrame())
#         self.assertEqual(r1, 0)
#
#     # # dashboard_chart/data_query/widget/dataset_query.py
#     # def test_data_query_dataset_query_file(self):
#     #     from dashboard_chart.data_query.widget.dataset_query import DatasetQuery
#     #     mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"7f2472a1-fa43-11eb-8cdf-b1878405dec1","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd391-7254-9d8e-054f-4b4418a541cc","data_type":"字符串","col_name":"LX_8957975949","alias_name":"类型","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":3,"rank":3,"col_type":"num","is_show":1,"group":null}]}]}"""
#     #     c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#     #     assign_dashboard_chart_model(c_model, no_data=True)
#     #     _c_model = deepcopy(c_model)
#     #     chart = DatasetQuery(c_model, g.userid)
#     #
#     #     r1 = chart.get_item_list([])
#     #     self.assertEqual(r1, 0)
#
#     # dashboard_chart/data_query/widget/indirect_query.py
#     def test_data_query_indirect_query_file(self):
#         from dashboard_chart.data_query.widget.indirect_query import IndirectQuery
#         from dashboard_chart.data_query.charts.common_chart import CommonChart
#
#         mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"8d07bd10-fa7e-11eb-bd19-a995f388098a","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[{"var_type":2,"value_type":3,"var_id":"39fdf427-7225-ec20-0c87-595ea9f02564","default_value_type":2,"value":["2021-01-01 00:00:00","2021-12-31 23:59:59"],"value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fd9139-d80b-c11f-6f54-64a927c15528","default_value_type":2,"value":"","value_source":"userdefined","value_identifier":""}],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd382-657d-fe39-f1b1-8b7104cdf7ed","dataset_field_id":"39fd913a-07f2-7d71-fefa-973ed8bb0f99","data_type":"字符串","col_name":"A_CS_7995442000","alias_name":"参数","order":0,"rank":0,"col_type":"num","is_show":1,"group":null}]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         query_model = CommonChart(c_model)
#         chart = IndirectQuery(c_model, g.userid, [])
#
#         print(query_model.assign_indirect_values(c_model))
#         print(c_model.indirect_query_map)
#
#     # dashboard_chart/data_query/widget/markline_query.py
#     def test_data_query_markline_query_file(self):
#         from dashboard_chart.data_query.widget.markline_query import MarklineQuery
#         from dashboard_chart.data_query.charts.common_chart import CommonChart
#
#         mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"98ca8d57-fb0d-11eb-80d1-c556e2276197","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         chart = CommonChart(c_model)
#         mlq = MarklineQuery(c_model)
#
#         r1 = chart.get_chart_data()
#         self.assertIn('data', r1)
#
#         d1 = [0.0, 442.0, 205.0, 104.0]
#
#         test_result_map = {
#             'avg': float,
#             'min': float,
#             'max': float,
#             'percentile': float,
#         }
#         for k, v in test_result_map.items():
#             r2 = mlq._get_markline_value(k, d1, 40)
#             self.assertIsInstance(r2, v)
#
#         r3 = mlq._classify_markline_value('A', [{'A': 0.6}], 'avg', True)
#         self.assertEqual(r3, [0.6])
#
#         try:
#             mlq._get_markline_value(self.fake_data('mode'), d1, 40)
#         except UserError as e:
#             self.assertIn('不支持的辅助线计算方法', e.message)
#
#     # dashboard_chart/data_query/widget/advanced_compute/base_compute.py
#     def test_data_query_base_compute_file(self):
#         from dashboard_chart.data_query.widget.advanced_compute.base_compute import BaseCompute
#
#         try:
#             BaseCompute([], 1, '', [], [], []).compute()
#         except UserError as e:
#             self.assertIn('请在字类中实现计算方法', e.message)
#
#     # dashboard_chart/data_query/widget/utils.py
#     def test_data_query_utils_file(self):
#         from dashboard_chart.data_query.widget.utils import (
#             FieldObj, DimSelect, dim_to_field, desire_to_field,
#             DesireSelect, comparison_to_field, ComparisonSelect
#         )
#
#         r1 = dim_to_field(
#             DimSelect(), self.fake_data('A'),
#             {self.fake_data('A'): {'alias_name': 'xx', 'col_name': 'vvv'}},
#             'avg'
#         )
#         self.assertIsInstance(r1, FieldObj)
#
#         r2 = desire_to_field(
#             DesireSelect(), self.fake_data('A'),
#             {self.fake_data('A'): {'alias_name': 'xx', 'col_name': 'vvv'}},
#             'avg'
#         )
#         self.assertIsInstance(r2, FieldObj)
#
#         r3 = comparison_to_field(
#             ComparisonSelect(), self.fake_data('A'),
#             {self.fake_data('A'): {'alias_name': 'xx', 'col_name': 'vvv'}},
#             'avg'
#         )
#         self.assertIsInstance(r3, FieldObj)
#
#     # dashboard_chart/data_query/utils.py
#     def test_data_query_utils___file(self):
#         from dashboard_chart.data_query.utils import (
#             get_msg_code, DashboardDataMsgCode, get_data_msg, CHART_QUERY_DEFAULT_LIMIT,
#             get_data_last_update_time, get_order_fields_with_cate, ChartDisplayAlias, ColTypes
#         )
#         from dashboard_chart.convertor.field_types import FieldObj, WhereField, GroupField, OrderField, LimitField, \
#             FieldObj
#
#         r1 = get_msg_code({'code': 200}, True)
#         self.assertEqual(r1, DashboardDataMsgCode.OverDataLimit.value)
#
#         r2 = get_msg_code({'code': 200}, False)
#         self.assertEqual(r2, DashboardDataMsgCode.NullDashboardData.value)
#
#         r3 = get_msg_code({'code': 200, 'data': [1, 2]}, False)
#         self.assertEqual(r3, DashboardDataMsgCode.Successful.value)
#
#         r4 = get_msg_code({'code': 4003, 'data': [1, 2]}, False)
#         self.assertEqual(r4, DashboardDataMsgCode.NoPermissionData.value)
#
#         r5 = get_msg_code({'code': 666, 'data': [1, 2]}, False)
#         self.assertEqual(r5, DashboardDataMsgCode.ErrorDatasetData.value)
#
#         r6 = get_msg_code({}, False)
#         self.assertEqual(r6, DashboardDataMsgCode.NullDashboardData.value)
#
#         r7 = get_data_msg({'msg': 'succ'})
#         self.assertEqual(r7, 'succ')
#
#         r8 = get_data_msg({'data': list(range(CHART_QUERY_DEFAULT_LIMIT))})
#         self.assertIn('数据过长', r8)
#
#         r9 = get_data_last_update_time(0)
#         self.assertEqual(r9, '')
#
#         r10 = get_data_last_update_time('xx')
#         self.assertEqual(r10, '')
#
#         O1 = OrderField(**{'field': '', 'table': '', 'operator': '=', 'field_sort': 'asc', 'logic_source': 'dim', })
#         O1.logic_source = 'dim'
#         O2 = OrderField(**{'field': 'field2', 'table': 'table_test', 'logic_source': 'comparison'})
#         O2.logic_source = 'comparison'
#         r11 = get_order_fields_with_cate([O1, O2])
#         self.assertIn('dim', r11)
#
#         mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"98ca8d57-fb0d-11eb-80d1-c556e2276197","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         cda = ChartDisplayAlias(c_model)
#
#         # TODO
#         r12 = cda.get_alias(self.fake_data('id'), ColTypes.Desire.value)
#         print(r12)
#
#     # dashboard_chart/data_query/standalone_query.py
#     def test_data_query_standalone_query_file(self):
#         from dashboard_chart.data_query.standalone_query import StandAloneQuery
#
#         mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"98ca8d57-fb0d-11eb-80d1-c556e2276197","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         cda = StandAloneQuery(c_model)
#
#         r1 = cda.query()
#         self.assertIn('data', r1)
#         r2 = cda.query_underlying_data()
#         self.assertIn('select', str(r2))
#
#     # dashboard_chart/data_query/widget/advanced_compute/ratio_compute.py
#     def test_data_query_ratio_compute_file(self):
#         from dashboard_chart.data_query.widget.advanced_compute.ratio_compute import RatioCompute
#         from dashboard_chart.data_query.charts.common_chart import CommonChart
#
#         mock_data = """{"dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_params":[{"id":"3250ebc3-fb33-11eb-91f5-99dfd66669d9","report_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":3,"value_type":2,"var_id":"39fcf883-10d4-e07e-ac64-956eeb10cbc5","default_value_type":2,"value":0,"value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""}],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd892-ff8f-5ebe-ece3-7d299f9b8e08","data_type":"字符串","col_name":"A_GJZD_16315574852","alias_name":"高级字段1","order":1,"rank":1,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcdc62-8c64-61f8-6ae5-db7ebbb68ee9","data_type":"字符串","col_name":"A_WWZ_16806768220","alias_name":"维-文-字","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fce855-e71a-f3c7-8470-6a637beafe1a","data_type":"数值","col_name":"A_SBJH_16939795241","alias_name":"数-变-聚合","order":3,"rank":3,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcf885-e95a-6ef0-3fcf-d6e314610061","data_type":"数值","col_name":"A_SZNUM_20949221552","alias_name":"数值-num","order":4,"rank":4,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd392-ff1b-2944-80c2-346a1ea22d95","data_type":"数值","col_name":"A_GJZD_12385249811","alias_name":"高级字段","order":5,"rank":5,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcfd59-c24e-edd9-5214-adfabc50f9c3","data_type":"字符串","col_name":"A_SBLWB_17004806908","alias_name":"数-变量（文本）","order":6,"rank":6,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fd923c-15e5-62c7-c79d-543edbac090c","data_type":"数值","col_name":"A_CS_5030704850","alias_name":"测试","order":7,"rank":7,"col_type":"num","is_show":1,"group":null}]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#         _c_model = deepcopy(c_model)
#         # cda = RatioCompute(c_model)
#         chart = CommonChart(c_model)
#
#         r1 = chart.get_variable_chart_data()
#         print(r1)
#
#     # dashboard_chart/data_query/widget/subtotal/subtotal_col_struct.py
#     def test_data_query_subtotal_col_struct_file(self):
#         from dashboard_chart.data_query.widget.subtotal.subtotal_col_struct import SubtotalBaseStruct
#         from dashboard_chart.convertor.field_types import FieldObj
#
#         mock_data = """{"dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_params":[{"id":"3250ebc3-fb33-11eb-91f5-99dfd66669d9","report_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":3,"value_type":2,"var_id":"39fcf883-10d4-e07e-ac64-956eeb10cbc5","default_value_type":2,"value":0,"value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""}],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd892-ff8f-5ebe-ece3-7d299f9b8e08","data_type":"字符串","col_name":"A_GJZD_16315574852","alias_name":"高级字段1","order":1,"rank":1,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcdc62-8c64-61f8-6ae5-db7ebbb68ee9","data_type":"字符串","col_name":"A_WWZ_16806768220","alias_name":"维-文-字","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fce855-e71a-f3c7-8470-6a637beafe1a","data_type":"数值","col_name":"A_SBJH_16939795241","alias_name":"数-变-聚合","order":3,"rank":3,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcf885-e95a-6ef0-3fcf-d6e314610061","data_type":"数值","col_name":"A_SZNUM_20949221552","alias_name":"数值-num","order":4,"rank":4,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd392-ff1b-2944-80c2-346a1ea22d95","data_type":"数值","col_name":"A_GJZD_12385249811","alias_name":"高级字段","order":5,"rank":5,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcfd59-c24e-edd9-5214-adfabc50f9c3","data_type":"字符串","col_name":"A_SBLWB_17004806908","alias_name":"数-变量（文本）","order":6,"rank":6,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fd923c-15e5-62c7-c79d-543edbac090c","data_type":"数值","col_name":"A_CS_5030704850","alias_name":"测试","order":7,"rank":7,"col_type":"num","is_show":1,"group":null}]}]}"""
#         c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
#         assign_dashboard_chart_model(c_model, no_data=True)
#
#         # dashboard_chart/data_query/widget/subtotal/subtotal_col_struct.py:39
#         with patch_property(c_model, 'comparisons', [{}]):
#             sbs = SubtotalBaseStruct([], [], {}, c_model, [], pd.DataFrame())
#             r1 = sbs.sort_multi_header([])
#             self.assertEqual(r1, [])
#
#         # with patch_property(c_model, 'comparisons', [{}]):
#         #     sbs = SubtotalBaseStruct([], [], {}, c_model, [], pd.DataFrame())
#         #     r1 = sbs.build_subtotal_col_struct([{}], [], [[FieldObj(**{'alias_name': 'A'}),]], [])
#         #     self.assertEqual(r1, [])
#
#
#     @classmethod
#     def _create_model(cls, file_name='meta'):
#         json_obj = cls._get_json(file_name=file_name)
#         meta_sub = ChartDataModel(**json_obj)
#         return meta_sub
#
#     @staticmethod
#     def _get_json(file_name='meta'):
#         file_path = '{}/metadata/{}.json'.format(os.path.dirname(__file__), file_name)
#         with open(file_path, encoding='utf8') as f:
#             return json.load(f)
#
#     @staticmethod
#     def fake_data(i):
#         return 'fake-data-%s' % i
#
#
