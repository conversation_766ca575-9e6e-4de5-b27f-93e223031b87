#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
"""

import unittest
import logging
import json
from tests.base import BaseTest

logger = logging.getLogger(__name__)


class TestDashboardAutoLoad(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='yudc')

    def test_auto_download(self):
        from dashboard_chart.services import dashboard_service
        dashboard_service.auto_download_report_from_rdc('39fb135b-0af0-b63a-fb56-a86a9948d880')


    # def test_install(self):
    #     try:
    #         from dashboard_chart.services import dashboard_service
    #         json_data = '''{"id": 522, "name": "\u6570\u89c1-\u5168\u56fd\u9879\u76ee\u5206\u5e03\u56fe", "version": "0.0.0.1",
    #                 "app_code": "0202", "dependent_product_key": "jhxt",
    #                 "dependent_product_name": "\u8ba1\u5212\u7cfb\u7edf",
    #                 "dependent_version_start": "3.5.0.10", "dependent_version_end": "\u6700\u65b0\u7248\u672c",
    #                 "download_url": "https://rdc.mingyuanyun.com/rdc-exchange-service/api/v1/file/ver/1653/erp/download",
    #                 "plugin_id": 522, "version_id": 1653}'''
    #        # '1569e1a6-e444-11eb-9287-00155d0a440c', '********-e444-11eb-9287-00155d0a440c'
    #         dashboard_service.install_yysc_dashboard(json.loads(json_data), '', '39fdcfea-474e-9760-fcab-a76716d2b77b')
    #     except Exception as e:
    #         raise e


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestDashboardAutoLoad("test_dashboard_auto_load"))
    runner = unittest.TextTestRunner()
    runner.run(s)
