#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : base.py
# @Author: guq  
# @Date  : 2021/8/17
# @Desc  :


# !/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from tests.base_test import BaseTest
from dmplib.hug import g
from dmplib import config
from dashboard_chart.models import ChartDataModel


class TestDataQueryBaseModel(BaseTest):

    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')

    def setUp(self):
        super().setUp()
        self.dashboard_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
        self.snapshot_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
        self.op_chart_id = '39fd437d-8b21-7c74-288c-01464efc954d'
        token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************.s8ijllLyrobkMUInVaxJdYeO0s4M7b5J4D03S7zc_Z4'
        g.cookie = {'token': token}
        g.code = 'local'

    @classmethod
    def _create_model(cls, file_name='meta'):
        json_obj = cls._get_json(file_name=file_name)
        meta_sub = ChartDataModel(**json_obj)
        return meta_sub

    @staticmethod
    def _get_json(file_name='meta'):
        file_path = '{}/dashboard_chart/tests/metadata/{}.json'.format(config.root_path, file_name)
        with open(file_path, encoding='utf8') as f:
            return json.load(f)

    @staticmethod
    def fake_data(i):
        return 'fake-data-%s' % i

    @staticmethod
    def fd(i):
        return TestDataQueryBaseModel.fake_data(i)
