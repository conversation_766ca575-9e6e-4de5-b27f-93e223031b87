#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
from dashboard_chart.data_query.utils import (
    get_msg_code, DashboardDataMsgCode, get_data_msg, CHART_QUERY_DEFAULT_LIMIT,
    get_data_last_update_time, get_order_fields_with_cate, ChartDisplayAlias, ColTypes
)
from dashboard_chart.convertor.field_types import FieldObj, Where<PERSON>ield, GroupField, OrderField, LimitField, \
    FieldObj


class TestSubtotalQueryModel(TestDataQueryBaseModel):

    # dashboard_chart/data_query/utils.py
    def test_data_query_utils_get_msg_code(self):
        r1 = get_msg_code({'code': 200}, True)
        self.assertEqual(r1, DashboardDataMsgCode.OverDataLimit.value)

        r2 = get_msg_code({'code': 200}, False)
        self.assertEqual(r2, DashboardDataMsgCode.NullDashboardData.value)

        r3 = get_msg_code({'code': 200, 'data': [1, 2]}, False)
        self.assertEqual(r3, DashboardDataMsgCode.Successful.value)

        r4 = get_msg_code({'code': 4003, 'data': [1, 2]}, False)
        self.assertEqual(r4, DashboardDataMsgCode.NoPermissionData.value)

        r5 = get_msg_code({'code': 666, 'data': [1, 2]}, False)
        self.assertEqual(r5, DashboardDataMsgCode.ErrorDatasetData.value)

        r6 = get_msg_code({}, False)
        self.assertEqual(r6, DashboardDataMsgCode.NullDashboardData.value)

        r7 = get_data_msg({'msg': 'succ'})
        self.assertEqual(r7, 'succ')

        r8 = get_data_msg({'data': list(range(CHART_QUERY_DEFAULT_LIMIT))})
        self.assertIn('数据过长', r8)

    def test_data_query_utils_get_data_last_update_time(self):
        r9 = get_data_last_update_time(0)
        self.assertEqual(r9, '')

        r10 = get_data_last_update_time('xx')
        self.assertEqual(r10, '')

    def test_data_query_utils_get_order_fields_with_cate(self):
        O1 = OrderField(**{'field': '', 'table': '', 'operator': '=', 'field_sort': 'asc', 'logic_source': 'dim', })
        O1.logic_source = 'dim'
        O2 = OrderField(**{'field': 'field2', 'table': 'table_test', 'logic_source': 'comparison'})
        O2.logic_source = 'comparison'
        r11 = get_order_fields_with_cate([O1, O2])
        self.assertIn('dim', r11)

    def test_data_query_utils_get_alias(self):
        mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"98ca8d57-fb0d-11eb-80d1-c556e2276197","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
        assign_dashboard_chart_model(c_model, no_data=True)
        # _c_model = deepcopy(c_model)
        cda = ChartDisplayAlias(c_model)

        # TODO
        r12 = cda.get_alias(self.fake_data('id'), ColTypes.Desire.value)
        print(r12)
