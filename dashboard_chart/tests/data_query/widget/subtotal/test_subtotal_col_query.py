#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from tests.base_test import patch_property
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
from dashboard_chart.data_query.charts.common_chart import CommonChart
from dashboard_chart.data_query.widget.subtotal.subtotal_col_query import SubtotalColQuery, handle_g
import pandas as pd


class TestSubtotalColQueryModel(TestDataQueryBaseModel):


    def get_models(self):
        mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"7f2472a1-fa43-11eb-8cdf-b1878405dec1","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd391-7254-9d8e-054f-4b4418a541cc","data_type":"字符串","col_name":"LX_8957975949","alias_name":"类型","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":3,"rank":3,"col_type":"num","is_show":1,"group":null}]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
        assign_dashboard_chart_model(c_model, no_data=True)
        _c_model = deepcopy(c_model)
        chart = CommonChart(c_model)

        # scq = SubtotalColQuery(
        #     chart.chart_data_model,
        #     chart.get_select_fields(),
        #     chart.get_where_fields(),
        #     chart.get_order_fields_for_multi(chart.chart_data_model),
        #     chart.get_dataset_vars(chart.chart_data_model),
        #     chart.get_user_id(),
        # )
        scq = SubtotalColQuery(
            **{
                "chart_data_model": chart.chart_data_model,
                "user_id": chart.get_user_id(),
                "origin_where_fields": chart.get_where_fields(),
                "origin_select_fields": chart.get_select_fields(),
                "origin_group_fields": [],
                "origin_order_fields": chart.get_order_fields_for_multi(chart.chart_data_model),
                "query_vars": [],
            }
        )

        return chart, c_model, _c_model, scq

    # dashboard_chart/data_query/widget/subtotal/subtotal_col_query.py
    def test_data_query_subtotal_col_get_chart_data(self):
        chart, c_model, _c_model, scq = self.get_models()

        r1 = chart.get_chart_data()
        self.assertIsInstance(r1, dict)

    # dashboard_chart/data_query/widget/subtotal/subtotal_col_query.py
    def test_data_query_subtotal_col_handle_g(self):
        chart, c_model, _c_model, scq = self.get_models()

        @handle_g
        def fake():
            return self.fake_data('x1')

        self.assertEqual(fake(), self.fake_data('x1'))

    def test_data_query_subtotal_col_get_subtotal_groups(self):
        chart, c_model, _c_model, scq = self.get_models()

        # with patch.object(PaginationTableChart, 'get_group_fields', ) as mm:
        #     group_fields[0].field = self.fake_data('id')
        #     mm.return_value = group_fields
        r2 = scq.get_subtotal_groups()
        self.assertIsInstance(r2, list)

    def test_data_query_subtotal_col__get_uncompute_subtotal_fields_v3(self):
        chart, c_model, _c_model, scq = self.get_models()

        old_column_display_map = scq._get_column_display_map()
        with patch.object(SubtotalColQuery, '_get_column_display_map') as mm:
            old_column_display_map[list(old_column_display_map.keys())[0]]['is_show'] = 0
            mm.return_value = old_column_display_map
            r3 = scq._get_uncompute_subtotal_fields_v3()
            self.assertGreaterEqual(len(r3), 1)

        with patch.object(SubtotalColQuery, '_compare_subtotal_dim_and_nums_display_weight') as mm:
            mm.return_value = 1
            r3 = scq._get_uncompute_subtotal_fields_v3()
            self.assertGreaterEqual(len(r3), 1)

    def test_data_query_subtotal_col__compare_subtotal_dim_and_nums_display_weight(self):
        chart, c_model, _c_model, scq = self.get_models()

        column_display_map = {('39fcd383-d543-d4d0-9ec4-99ee8072bd08', 'dim'): {'order': 10, 'is_show': 1},
                              ('39fcd391-7254-9d8e-054f-4b4418a541cc', 'dim'): {'order': 1, 'is_show': 1},
                              ('39fcd383-d543-da7c-5d10-18709d914703', 'num'): {'order': 2, 'is_show': 1},
                              ('39fcd383-d543-db36-60a2-3303732bfe1f', 'num'): {'order': 3, 'is_show': 1}}
        r4 = scq._compare_subtotal_dim_and_nums_display_weight(column_display_map,
                                                               '39fcd383-d543-d4d0-9ec4-99ee8072bd08')
        self.assertEqual(r4, 1)

    def test_data_query_subtotal_col__get_column_display_map(self):
        chart, c_model, _c_model, scq = self.get_models()

        with patch_property(scq._chart_data_model, 'column_display', []):
            r5 = scq._get_column_display_map()
            self.assertIsInstance(r5, dict)

    def test_data_query_subtotal_col__get_num_field_dict(self):
        chart, c_model, _c_model, scq = self.get_models()

        r6 = scq._get_num_field_dict()
        self.assertIsInstance(r6, dict)

    def test_data_query_subtotal_col__get_dim_headers(self):
        chart, c_model, _c_model, scq = self.get_models()

        r7 = scq._get_dim_headers()
        self.assertIsInstance(r7, list)

    def test_data_query_subtotal_col__is_dim_header(self):
        chart, c_model, _c_model, scq = self.get_models()

        r8 = scq._is_dim_header(())
        self.assertIsInstance(r8, bool)
    #
    # def test_data_query_subtotal_col__get_data_index_headers(self):
    #     chart, c_model, _c_model, scq = self.get_models()
    #
    #     r9 = scq._get_data_index_headers(pd.DataFrame([[1, 2, 3], [4, 5, 6], [7, 8, 9]]))
    #     self.assertIsInstance(r9, bool)

    def test_data_query_subtotal_col_get_subtotal_base_select_fields_dict(self):
        chart, c_model, _c_model, scq = self.get_models()

        r10 = scq.get_subtotal_base_select_fields_dict()
        self.assertIsInstance(r10, dict)

    def test_data_query_subtotal_col_get_subtotal_base_group_dims(self):
        chart, c_model, _c_model, scq = self.get_models()

        r11 = scq.get_subtotal_base_group_dims()
        self.assertIsInstance(r11, list)

    def test_data_query_subtotal_colget_subtotal_base_group_fields(self):
        chart, c_model, _c_model, scq = self.get_models()

        r12 = scq.get_subtotal_base_group_fields()
        self.assertIsInstance(r12, list)

        # from dmplib import  config
        # print(config.get('Function.subtotal_query_method', 'serial') )
        #
        # with patch_property(config, 'get', lambda x, y: '666') as f:
        #     print(config.get('Function.subtotal_query_method', 'serial'))
        #     self.assertIsInstance(r5, dict)
        #
        # print(config.get('Function.subtotal_query_method', 'serial'))
        #
        # r13 = scq._get_header_from_multi([1,2,3],[4,5,6], {1: Field()})
        # self.assertIsInstance(r13, list)

