#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
import pandas as pd
from dashboard_chart.data_query.widget.indirect_query import IndirectQuery
from dashboard_chart.data_query.charts.common_chart import CommonChart


class TestIndirectQueryModel(TestDataQueryBaseModel):

    def get_models(self):
        mock_data = '{"dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_params":[{"id":"6d416986-0169-11ec-b36c-156171151cb2","report_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","dashboard_id":"39fdfe0e-a0e1-e1f0-9a5c-1ed2ef9e4037","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[{"chart_id":"ec69e1d5-017b-11ec-b6a5-e19d8d4e6ef8","col_name":null,"col_value":null,"operator":"in","dataset_field_id":"39fcd383-2d1b-0603-5b52-a6f89d8117af","value_from":{"conditions":[{"dataset_field_id":"39fcd383-2d1b-0339-7269-74d18c589d9a","col_name":"XM_6343270961","operator":"in","col_value":"[\\"0\\"]","formula_mode":null,"alias_name":"项目","condition_type":2,"data_type":"字符串"}]}}],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":3,"value_type":2,"var_id":"39fcf883-10d4-e07e-ac64-956eeb10cbc5","default_value_type":2,"value":0,"value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fe748a-c881-8e5f-a37d-046afada2e38","default_value_type":1,"value":"local","value_source":"userdefined","value_identifier":""}],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":1,"rank":1,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd392-ff1b-2944-80c2-346a1ea22d95","data_type":"数值","col_name":"A_GJZD_12385249811","alias_name":"高级字段","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fce855-e71a-f3c7-8470-6a637beafe1a","data_type":"数值","col_name":"A_SBJH_16939795241","alias_name":"数-变-聚合","order":3,"rank":3,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fd923c-15e5-62c7-c79d-543edbac090c","data_type":"数值","col_name":"A_CS_5030704850","alias_name":"测试","order":4,"rank":4,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcf885-e95a-6ef0-3fcf-d6e314610061","data_type":"数值","col_name":"A_SZNUM_20949221552","alias_name":"数值-num","order":5,"rank":5,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fe748b-887c-42d1-0612-21ce1c7ccb68","data_type":"数值","col_name":"A_GJZ_9814863912","alias_name":"关键字","order":6,"rank":6,"col_type":"num","is_show":1,"group":null}]}]}'

        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
        assign_dashboard_chart_model(c_model, no_data=True)
        # _c_model = deepcopy(c_model)
        query_model = CommonChart(c_model)
        chart = IndirectQuery(c_model, g.userid, [])

        return chart, query_model, c_model

    # dashboard_chart/data_query/widget/indirect_query.py
    def test_data_query_indirect_query_file(self):
        chart, query_model, c_model = self.get_models()

        r1 = query_model.assign_indirect_values(c_model)
        self.assertEqual(r1, None)

    def test__format_db_data(self):
        chart, query_model, c_model = self.get_models()

        from datetime import datetime
        from datetime import date

        r2 = chart._format_db_data(datetime.now())
        self.assertIsInstance(r2, str)

        r3 = chart._format_db_data(date.today())
        self.assertIsInstance(r3, str)
