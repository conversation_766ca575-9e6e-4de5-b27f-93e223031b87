#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
import pandas as pd
from dashboard_chart.data_query.widget.count_query import CountQuery
from dashboard_chart.data_query.widget.markline_query import MarklineQuery
from dashboard_chart.data_query.charts.common_chart import CommonChart


class TestMarklineQueryModel(TestDataQueryBaseModel):


    def get_models(self):

        mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"98ca8d57-fb0d-11eb-80d1-c556e2276197","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
        assign_dashboard_chart_model(c_model, no_data=True)
        _c_model = deepcopy(c_model)
        chart = CommonChart(c_model)
        mlq = MarklineQuery(c_model)
        return chart, c_model, _c_model, mlq

    # dashboard_chart/data_query/widget/markline_query.py
    def test_data_query_markline_query_get_chart_data(self):
        chart, c_model, _c_model, mlq = self.get_models()

        r1 = chart.get_chart_data()
        self.assertIn('data', r1)

    # dashboard_chart/data_query/widget/markline_query.py
    def test_data_query_markline_query_get_markline_value(self):
        chart, c_model, _c_model, mlq = self.get_models()

        d1 = [0.0, 442.0, 205.0, 104.0]
        test_result_map = {
            'avg': float,
            'min': float,
            'max': float,
            'percentile': float,
        }
        for k, v in test_result_map.items():
            r2 = mlq._get_markline_value(k, d1, 40)
            self.assertIsInstance(r2, v)

        try:
            mlq._get_markline_value(self.fake_data('mode'), d1, 40)
        except UserError as e:
            self.assertIn('不支持的辅助线计算方法', e.message)

    def test_data_query_markline_query__classify_markline_value(self):
        chart, c_model, _c_model, mlq = self.get_models()
        r3 = mlq._classify_markline_value('A', [{'A': 0.6}], 'avg', True)
        self.assertEqual(r3, [0.6])

