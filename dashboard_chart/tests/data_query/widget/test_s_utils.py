#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wang<PERSON>i
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
import pandas as pd
from dashboard_chart.data_query.widget.count_query import CountQuery
from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.data_query.widget.utils import (
    FieldObj, DimSelect, dim_to_field, desire_to_field,
    DesireSelect, comparison_to_field, ComparisonSelect
)


class TestUtilsModel(TestDataQueryBaseModel):


    # dashboard_chart/data_query/widget/utils.py
    def test_data_query_utils_dim_to_field(self):
        r1 = dim_to_field(
            DimSelect(), self.fake_data('A'),
            {self.fake_data('A'): {'alias_name': 'xx', 'col_name': 'vvv'}},
            'avg'
        )
        self.assertIsInstance(r1, FieldObj)

    def test_data_query_utils_desire_to_field(self):
        r2 = desire_to_field(
            DesireSelect(), self.fake_data('A'),
            {self.fake_data('A'): {'alias_name': 'xx', 'col_name': 'vvv'}},
            'avg'
        )
        self.assertIsInstance(r2, FieldObj)

    def test_data_query_utils_comparison_to_field(self):
        r3 = comparison_to_field(
            ComparisonSelect(), self.fake_data('A'),
            {self.fake_data('A'): {'alias_name': 'xx', 'col_name': 'vvv'}},
            'avg'
        )
        self.assertIsInstance(r3, FieldObj)

