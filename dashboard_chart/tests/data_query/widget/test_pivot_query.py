#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
import pandas as pd
from dashboard_chart.data_query.widget.pivot_query import PivotQuery, CustomerFieldGroup, PivotSort, IndexGroupSort,\
ColumnGroupSort, NormalFieldGroup
from dashboard_chart.convertor.field_types import FieldObj
from dashboard_chart.models import FieldSortModel

from dashboard_chart.agent.query_agent import QueryAgent
from dashboard_chart.data_query.charts.common_chart import CommonChart


class TestPivotQueryModel(TestDataQueryBaseModel):

    def get_models(self):
        mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"98ca8d57-fb0d-11eb-80d1-c556e2276197","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"comparison_line","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
        assign_dashboard_chart_model(c_model, no_data=True)
        _c_model = deepcopy(c_model)
        chart = CommonChart(c_model)
        q_obj = PivotQuery(c_model)
        return chart, c_model, _c_model, q_obj

    # def test_compute_pivot_data_v1(self):
    #     chart, c_model, _c_model, q_obj = self.get_models()
    #
    #     mock_data  = {'data': [{}, {}], }
    #
    #     r1 = q_obj.compute_pivot_data_v1(mock_data)
    #     print(r1)

    def test_CustomerFieldGroup(self):
        cfg = CustomerFieldGroup()

        r2 = cfg.get_levels_items()
        self.assertEqual(r2, [])

        r3 = cfg.append(FieldSortModel(), self.fake_data('l'))
        self.assertEqual(r3, None)

    def test_IndexGroupSort(self):
        d = [(FieldSortModel(**{'sort': 'asc'}), 0), (FieldSortModel(**{'sort': 'desc'}), 0)]
        d1 = pd.DataFrame({
            'c1': [1,2,3],
            'c2': [3,4,5],
            'c3': [3,4,5],
            'c4': [3,4,5],
        })
        d1.index.name = 'c1'

        ps = IndexGroupSort(d1, d)

        cfg = CustomerFieldGroup()
        cfg.append(FieldSortModel(), self.fake_data('l'))
        cfg.append(FieldSortModel(), self.fake_data('k'))
        r4 = ps.get_customer_level_sort_dict(cfg)
        self.assertEqual(r4, None)
        #
        # r5 = ps.sort_has_customer_data()
        # self.assertIsInstance(r5, pd.DataFrame)

    def test_ColumnGroupSort(self):
        d = [(FieldSortModel(**{'sort': 'asc'}), 1), (FieldSortModel(**{'sort': 'desc'}), 2)]

        ps = ColumnGroupSort(pd.DataFrame(), d)
        r6 = ps.sort_has_customer_data()
        self.assertIsInstance(r6, pd.DataFrame)

        # r7 = ps.get_normal_level_sort_dict(NormalFieldGroup(), ('last', 0))
