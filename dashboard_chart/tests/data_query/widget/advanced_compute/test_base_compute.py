#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wang<PERSON>i
# @File     : test_dashboard_editor.py
import importlib
import json
import os

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model


class TestBasecomputeModel(TestDataQueryBaseModel):


    # dashboard_chart/data_query/widget/advanced_compute/base_compute.py
    def test_data_query_base_compute_file(self):
        from dashboard_chart.data_query.widget.advanced_compute.base_compute import BaseCompute

        try:
            BaseCompute([], 1, '', [], [], []).compute()
        except UserError as e:
            self.assertIn('请在字类中实现计算方法', e.message)

