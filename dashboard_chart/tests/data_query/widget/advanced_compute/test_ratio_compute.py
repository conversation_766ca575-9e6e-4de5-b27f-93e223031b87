#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import  pytest
import json
import os
from copy import deepcopy
from _pytest.config import Config
os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from tests.base_test import patch_property
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.data_query.widget.advanced_compute.ratio_compute import RatioCompute
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
from dashboard_chart.data_query.charts.common_chart import CommonChart


class TestRatioComputeModel(TestDataQueryBaseModel):

    def get_models(self):
        from dashboard_chart.data_query.charts.common_chart import CommonChart

        mock_data = """{"dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_params":[{"id":"3250ebc3-fb33-11eb-91f5-99dfd66669d9","report_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":3,"value_type":2,"var_id":"39fcf883-10d4-e07e-ac64-956eeb10cbc5","default_value_type":2,"value":0,"value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""}],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd892-ff8f-5ebe-ece3-7d299f9b8e08","data_type":"字符串","col_name":"A_GJZD_16315574852","alias_name":"高级字段1","order":1,"rank":1,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcdc62-8c64-61f8-6ae5-db7ebbb68ee9","data_type":"字符串","col_name":"A_WWZ_16806768220","alias_name":"维-文-字","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fce855-e71a-f3c7-8470-6a637beafe1a","data_type":"数值","col_name":"A_SBJH_16939795241","alias_name":"数-变-聚合","order":3,"rank":3,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcf885-e95a-6ef0-3fcf-d6e314610061","data_type":"数值","col_name":"A_SZNUM_20949221552","alias_name":"数值-num","order":4,"rank":4,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd392-ff1b-2944-80c2-346a1ea22d95","data_type":"数值","col_name":"A_GJZD_12385249811","alias_name":"高级字段","order":5,"rank":5,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcfd59-c24e-edd9-5214-adfabc50f9c3","data_type":"字符串","col_name":"A_SBLWB_17004806908","alias_name":"数-变量（文本）","order":6,"rank":6,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fd923c-15e5-62c7-c79d-543edbac090c","data_type":"数值","col_name":"A_CS_5030704850","alias_name":"测试","order":7,"rank":7,"col_type":"num","is_show":1,"group":null}]},{"id":"03175fa1-00ca-11ec-addc-bb831d0d5c5b","report_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_code":"pie","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][1])
        assign_dashboard_chart_model(c_model, no_data=True)
        # _c_model = deepcopy(c_model)

        # rc = RatioCompute([], c_model, '', [], [], [])
        return c_model

    # dashboard_chart/data_query/widget/advanced_compute/ratio_compute.py
    def test_data_query_ratio_compute_normal(self):
        c_model = self.get_models()
        chart = CommonChart(c_model)

        r1 = chart.get_variable_chart_data()
        self.assertIn('data', r1)

    # dashboard_chart/data_query/widget/advanced_compute/ratio_compute.py
    def test_data_query_ratio_compute_pivot(self):
        mock_data = """{"dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_params":[{"id":"3250ebc3-fb33-11eb-91f5-99dfd66669d9","report_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":3,"value_type":2,"var_id":"39fcf883-10d4-e07e-ac64-956eeb10cbc5","default_value_type":2,"value":0,"value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""},{"var_type":1,"value_type":2,"var_id":"39fcd392-ce76-cec6-1a70-f95c0b035a83","default_value_type":2,"value":"测试","value_source":"userdefined","value_identifier":""}],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd892-ff8f-5ebe-ece3-7d299f9b8e08","data_type":"字符串","col_name":"A_GJZD_16315574852","alias_name":"高级字段1","order":1,"rank":1,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcdc62-8c64-61f8-6ae5-db7ebbb68ee9","data_type":"字符串","col_name":"A_WWZ_16806768220","alias_name":"维-文-字","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fce855-e71a-f3c7-8470-6a637beafe1a","data_type":"数值","col_name":"A_SBJH_16939795241","alias_name":"数-变-聚合","order":3,"rank":3,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcf885-e95a-6ef0-3fcf-d6e314610061","data_type":"数值","col_name":"A_SZNUM_20949221552","alias_name":"数值-num","order":4,"rank":4,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd392-ff1b-2944-80c2-346a1ea22d95","data_type":"数值","col_name":"A_GJZD_12385249811","alias_name":"高级字段","order":5,"rank":5,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcfd59-c24e-edd9-5214-adfabc50f9c3","data_type":"字符串","col_name":"A_SBLWB_17004806908","alias_name":"数-变量（文本）","order":6,"rank":6,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fd923c-15e5-62c7-c79d-543edbac090c","data_type":"数值","col_name":"A_CS_5030704850","alias_name":"测试","order":7,"rank":7,"col_type":"num","is_show":1,"group":null}]},{"id":"03175fa1-00ca-11ec-addc-bb831d0d5c5b","report_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_code":"pie","data_logic_type_code":"default","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[]},{"id":"ad8e7bc5-00cb-11ec-9570-29d981b95a4d","report_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","dashboard_id":"39fe4b21-92cc-b8f6-f9c7-b4f9e926d2be","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":1,"rank":1,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null}]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][2])
        assign_dashboard_chart_model(c_model, no_data=True)
        # _c_model = deepcopy(c_model)

        chart = CommonChart(c_model)

        r1 = chart.get_variable_chart_data()
        self.assertIn('data', r1)

    def test_compute(self):
        c_model = self.get_models()
        rc = RatioCompute([], c_model, '', [], [], [])

        with patch_property(c_model, 'aggregation', False):
            r1 = rc.compute()
            self.assertEqual(r1, [])

        with patch_property(c_model, 'nums', []):
            r2 = rc.compute()
            self.assertEqual(r2, [])
        #
        # with patch_property(c_model, 'nums', [{'formula_mode': 'ratio'}]):
        #     r2 = rc.compute()
        #     self.assertEqual(r2, [])

        r3 = rc.compute()
        self.assertEqual(r3, [])

