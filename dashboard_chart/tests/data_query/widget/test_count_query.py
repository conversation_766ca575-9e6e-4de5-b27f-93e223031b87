#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os
from copy import deepcopy

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from tests.base_test import patch_property
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
import pandas as pd
from dashboard_chart.data_query.widget.count_query import CountQuery
from dashboard_chart.agent.query_agent import QueryAgent


class TestBasecomputeModel(TestDataQueryBaseModel):


    def get_models(self):

        mock_data = """{"dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_params":[{"id":"7f2472a1-fa43-11eb-8cdf-b1878405dec1","report_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","dashboard_id":"39fe44fe-c22b-8594-b24e-ab2e3dfc3ed5","chart_code":"dmp_handsontable","data_logic_type_code":"column","conditions":[],"external_subject_ids":[],"penetrate_conditions":[],"penetrate_filter_conditions":[],"filter_conditions":[],"chart_filter_conditions":[],"chart_linkage_conditions":[],"drill_conditions":[],"dims":[],"nums":[],"dashboard_conditions":[],"query_vars":[],"pagination":{"page":1,"total":0,"page_size":150},"column_display":[{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-d4d0-9ec4-99ee8072bd08","data_type":"字符串","col_name":"XM_6128902656","alias_name":"项目","order":0,"rank":0,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd391-7254-9d8e-054f-4b4418a541cc","data_type":"字符串","col_name":"LX_8957975949","alias_name":"类型","order":1,"rank":1,"col_type":"dim","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-da7c-5d10-18709d914703","data_type":"数值","col_name":"JE_7345642328","alias_name":"金额1","order":2,"rank":2,"col_type":"num","is_show":1,"group":null},{"dataset_id":"39fcd383-a951-386a-6c45-61d41f41c9be","dataset_field_id":"39fcd383-d543-db36-60a2-3303732bfe1f","data_type":"数值","col_name":"JE_7345707864","alias_name":"金额2","order":3,"rank":3,"col_type":"num","is_show":1,"group":null}]}]}"""
        c_model = ChartDataModel(**json.loads(mock_data)['chart_params'][0])
        assign_dashboard_chart_model(c_model, no_data=True)
        _c_model = deepcopy(c_model)
        chart = CountQuery(c_model, g.userid)
        return chart, c_model, _c_model

    # dashboard_chart/data_query/widget/count_query.py
    def test_data_query_count_query_query_count(self):
        chart, c_model, _c_model = self.get_models()

        with patch.object(QueryAgent, 'query') as f:
            f.return_value = {'code': 300}
            try:
                chart.query_count([], [])
            except UserError as e:
                self.assertIn('单图取数异常', e.message)

        with patch.object(QueryAgent, 'query') as f, \
                patch_property(chart._chart_data_model, 'external_subject_ids', [1, 2]):
            f.return_value = {'code': 200, 'query_structure': {'group_by': 0}, 'data': [{'total': 666}]}
            r2 = chart.query_count([], [])
            self.assertEqual(r2, 666)

        with patch.object(QueryAgent, 'query') as f, \
                patch_property(chart._chart_data_model, 'aggregation', []):
            f.return_value = {'code': 200, 'query_structure': {'group_by': 0}, 'data': []}
            r3 = chart.query_count([], [])
            self.assertEqual(r3, 0)

        with patch.object(QueryAgent, 'query') as f, \
                patch_property(chart._chart_data_model, 'aggregation', []):
            f.return_value = {'code': 200, 'query_structure': {'group_by': 0}, 'data': [{'total': 777}]}
            r2 = chart.query_count([], [])
            self.assertEqual(r2, 777)

