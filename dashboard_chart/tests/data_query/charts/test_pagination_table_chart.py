#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wang<PERSON>i
# @File     : test_dashboard_editor.py
import importlib
import json
import os

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model


class TestPGTableModel(TestDataQueryBaseModel):


    # dashboard_chart/data_query/charts/pagination_table_chart.py
    def test_data_query_pagination_table_query_data(self):
        from dashboard_chart.data_query.charts.pagination_table_chart import PaginationTableChart

        c_model = self._create_model('query_data_sample_1')
        assign_dashboard_chart_model(c_model, no_data=True)
        chart = PaginationTableChart(c_model)

        r1 = chart.query_data()
        self.assertEqual(200, r1['code'])

    # dashboard_chart/data_query/charts/pagination_table_chart.py
    def test_data_query_pagination_table_get_order_fields_for_multi(self):
        from dashboard_chart.data_query.charts.pagination_table_chart import PaginationTableChart

        c_model = self._create_model('query_data_sample_1')
        assign_dashboard_chart_model(c_model, no_data=True)
        chart = PaginationTableChart(c_model)

        group_fields = chart.get_group_fields(c_model)
        with patch.object(PaginationTableChart, 'get_group_fields', ) as mm:
            group_fields[0].field = self.fake_data('id')
            mm.return_value = group_fields
            chart1 = PaginationTableChart(c_model)
            r3 = chart1.get_order_fields_for_multi(c_model)
            self.assertEqual(bool(r3), True)

