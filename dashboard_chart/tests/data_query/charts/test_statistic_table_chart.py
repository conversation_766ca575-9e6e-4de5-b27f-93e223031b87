#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import importlib
import json
import os

os.environ['prometheus_multiproc_dir'] = '/tmp'

from unittest.mock import patch

from dashboard_chart.services import dashboard_service, metadata_service
from dashboard_chart.metadata import dashboard_preview_metadata_models
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.tests.data_query.base import TestDataQueryBaseModel
from dashboard_chart.services.chart_service import assign_dashboard_chart_model
from dashboard_chart.data_query.charts.statistic_table_chart import StatisticTableChart


class TestStaticTableChartModel(TestDataQueryBaseModel):


    def get_models(self):
        c_model = self._create_model('query_data_sample_1')
        assign_dashboard_chart_model(c_model, no_data=True)
        chart = StatisticTableChart(c_model)
        return chart, c_model

    # dashboard_chart/data_query/charts/statistic_table_chart.py
    def test_data_query_statistic_table_query_data(self):
        chart, c_model = self.get_models()

        r1 = chart.query_data()
        self.assertEqual(200, r1['code'])

    # dashboard_chart/data_query/charts/statistic_table_chart.py
    def test_data_query_statistic_table_get_chart_data(self):
        chart, c_model = self.get_models()

        r2 = chart.get_chart_data()
        self.assertIsInstance(r2, dict)

    # dashboard_chart/data_query/charts/statistic_table_chart.py
    def test_data_query_statistic_table_get_subtotal_for_struct(self):
        chart, c_model = self.get_models()

        r3 = chart.get_subtotal_for_struct()
        self.assertIn('subtotal_summary', r3)

