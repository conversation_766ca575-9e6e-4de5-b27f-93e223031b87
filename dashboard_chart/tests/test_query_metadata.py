# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-
# # @Time     : 2021/07/21 09:31
# # <AUTHOR> wangfei
# # @File     : test_dashboard_editor.py
# import importlib
# import json
# import os
#
# from unittest.mock import patch
#
# os.environ['prometheus_multiproc_dir'] = '/tmp'
# import inspect
# import re
# import time
# from collections import defaultdict
#
# from dashboard_chart.models import ReleaseModel
# from dashboard_chart.services import dashboard_service, metadata_service
# from dashboard_chart.metadata import dashboard_preview_metadata_models
# from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
# from tests.base_test import BaseTest
# from dashboard_chart.metadata.metadata_builder import storage, builder, generators
# from dmplib.hug import g
# from dmplib.utils.errors import UserError
#
#
# class TestQueryMetadata(BaseTest):
#     """
#     解析json数据类的测试
#     """
#
#     def __init__(self, method_name="runTest"):
#         super().__init__(method_name, code='test', account='test')
#
#     def setUp(self):
#         super().setUp()
#         self.dashboard_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
#         self.snapshot_id = '39fd437d-8b21-7793-7c70-cd9e2713c94e'
#         self.op_chart_id = '39fd437d-8b21-7c74-288c-01464efc954d'
#         token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************.s8ijllLyrobkMUInVaxJdYeO0s4M7b5J4D03S7zc_Z4'
#         g.cookie = {'token': token}
#         g.code = 'local'
#
#     # token校验
#     def test_check_token(self):
#         token1 = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************.3XLsY4JaMGPdXAo67Lkn6-RKbHgce2pzCJ6XpYYtgx8"
#         token2 = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjIyYjExZGI0LWU5MDctNGYxZi04ODM1LWI5ZGFhYjZlMWYyMyIsIl9mbGFnIjoxNjI3MDI2NDUxLCJhY2NvdW50IjoidWl0ZXN0IiwiY29kZSI6InVpdGVzdCIsImdyb3VwX2lkcyI6WyIzOWY5MTEzNS1lY2QzLTk3MWUtMmMxZi0zYmIxYzgxM2QzNTMiXSwiY3VzdG9taXplX3JvbGVzIjpbXSwiZXh0ZXJuYWxfdXNlclUb9pZCI6bnVsbH0.3XLsY4JaMGPdXAo67Lkn6-RKbHgce2pzCJ6XpYYtgx8"
#         token3 = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjIyYjExZGI0LWU5MD*(ctNGYxZi04ODM1LWI5ZGFhYjZlMWYyMyIsIl9mbGFnIjoxNjI3MDI2NDUxLCJhY2NvdW50IjoidWl0ZXN0IiwiY29kZSI6InVpdGVzdCIsImdyb3VwX2lkcyI6WyIzOWY5MTEzNS1lY2QzLTk3MWUtMmMxZi0zYmIxYzgxM2QzNTMiXSwiY3VzdG9taXplX3JvbGVzIjpbXSwiZXh0ZXJuYWxfdXNlclUb9pZCI6bnVsbH0==.3XLsY4JaMGPdXAo67Lkn6-RKbHgce2pzCJ6XpYYtgx668"
#         token_map = {
#             token1: True,
#             token2: False,
#             token3: False,
#         }
#         for k, v in token_map.items():
#             result = dashboard_service.check_token(k)
#             self.assertEqual(result[0], v)
#
#     # check_permission校验
#     def test_check_permission(self):
#         dashboard_id = self.dashboard_id
#         kwargs = {}
#
#         token_data = {}  # 默认情况
#         flag = dashboard_service.check_permission(dashboard_id, token_data, kwargs.get("action", "download"))
#         self.assertEqual(flag, True)
#
#         token_data = {'dashboard_id': dashboard_id}  # 默认情况
#         flag = dashboard_service.check_permission(dashboard_id, token_data, kwargs.get("action", "download"))
#         self.assertEqual(flag, False)
#
#         token_data = {'dashboard_id': dashboard_id, 'external_params': {'user_auth': 'download'}}  # dashboard_id一致
#         flag = dashboard_service.check_permission(dashboard_id, token_data, kwargs.get("action", "download"))
#         self.assertEqual(flag, True)
#
#         token_data = {'screen_id': dashboard_id, 'external_params': {'session_id': ''}}  # session_id不存在
#         flag = dashboard_service.check_permission(dashboard_id, token_data, kwargs.get("action", "download"))
#         self.assertEqual(flag, True)
#
#         token_data = {'screen_id': dashboard_id, 'external_params': {'session_id': dashboard_id}}  # session_id不存在
#         flag = dashboard_service.check_permission(dashboard_id, token_data, kwargs.get("action", "download"))
#         self.assertEqual(flag, False)
#
#     # 获取v1
#     def test_get_screens_preview_metadata_v1(self):
#         dashboard_id = self.dashboard_id
#         meta = metadata_service.get_screens_preview_metadata(dashboard_id)
#         self.assertEqual(dashboard_id, meta.get('dashboard', {}).get('id', ''))
#
#     # 获取v2
#     def test_get_screens_preview_metadata_v2(self):
#         # # 非多屏
#         # dashboard_id = self.dashboard_id
#         # msg, result = metadata_service.get_screens_preview_metadata_v2(dashboard_id)
#         # self.assertEqual(dashboard_id, result.get('dashboard', {}).get('id', ''))
#
#         # # 多屏
#         # dashboard_id2 = '39fdfe13-b4c4-d2d4-5799-e71668527cd1'
#         # msg, result = metadata_service.get_screens_preview_metadata_v2(dashboard_id2)
#         # self.assertEqual(dashboard_id2, result.get('dashboard', {}).get('id', ''))
#
#         # id不存在
#         try:
#             dashboard_id2 = '39fd30bc-bdb0-7d5f-d204-24d60000003f'
#             msg, result = metadata_service.get_screens_preview_metadata_v2(dashboard_id2)
#         except UserError as e:
#             self.assertIn('查询不到报告id对应数据', e.message)
#
#     # dashboard_preview_metadata_models 文件
#     def test_metadata_models(self):
#         dicts = dashboard_preview_metadata_models.__dict__
#         models = {
#             k: v for k, v in dicts.items()
#             if inspect.isclass(v) and issubclass(v, dashboard_preview_metadata_models.MetadataNodeBaseModel) \
#                and v not in [
#                    dashboard_preview_metadata_models.MetadataNodeBaseModel
#                ]
#         }
#
#         for k, v in models.items():
#             cls = v(dashboard_id=self.dashboard_id)
#             data = cls.init_data()
#             if data:
#                 self.assertIsInstance(data, (dict, list))
#                 self.assertEqual(len(data) > 0, True)
#             else:
#                 self.assertIn(data, [None, [], '', {}])
#
#     # dashboard_chart/metadata/metadata_builder/storage.py
#     def test_metadata_builder_storage_file(self):
#         data = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         self.assertIn(self.dashboard_id, str(data))
#
#     # dashboard_chart/metadata/metadata_builder/builder.py
#     @patch('dashboard_chart.metadata.metadata_builder.storage.MetadataStorage.get_specific_table_data')
#     @patch('dashboard_chart.metadata.metadata_builder.storage.MetadataStorage.get_dashboard_info')
#     def test_metadata_builder_builder_file(self, mock_dashboard, mock_table):
#         # 正常情况
#         mock_data = {'id': '39fd437d-8b21-7793-7c70-cd9e2713c94e', 'theme': 'colorful_white', 'data_report_id': None,
#                      'name': '参数-跳转-后端测试副本', 'type': 'FILE', 'parent_id': '39fd4df1-0832-5336-45a9-aa1e88b14fdf',
#                      'level_code': '0057-0004-', 'icon': '', 'platform': 'pc', 'terminal_type': '',
#                      'is_multiple_screen': 0, 'status': 0, 'user_group_id': '', 'default_show': 0, 'cover': '',
#                      'description': '', 'layout_type': '标准布局', 'share_secret_key': '',
#                      'layout': '{"mode":"grid","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}',
#                      'scale_mode': 0,
#                      'background': '{"show":true,"color":"#EBEDF2","image":"","size":"stretch","user_image":""}',
#                      'rank': None, 'build_in': 0, 'biz_code': '929e8ef9fdb746a297ee7a1a666f9fca', 'border': None,
#                      'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'create_type': 1,
#                      'new_layout_type': 1,
#                      'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}',
#                      'distribute_type': 0, 'is_show_mark_img': 1, 'application_type': 0, 'main_external_subject_id': '',
#                      'external_subject_ids': '', 'is_highdata': 0, 'is_deleted': 0, 'erp_app_code': None,
#                      'dataset_id': '', 'analysis_type': ''}
#         mock_dashboard.return_value = mock_data.copy()
#         data = builder.MetadataBuilder(**{'dashboard_id': self.dashboard_id})
#         data.build_for_dashboard()
#         self.assertIn(self.dashboard_id, str(data))
#
#         # 异常1 dashboard_chart/metadata/metadata_builder/builder.py:50
#         try:
#             mock_dashboard.return_value = {}
#             data = builder.MetadataBuilder(**{'dashboard_id': 'fake-id'})
#         except UserError as e:
#             self.assertIn('获取报告数据失败', e.message)
#
#         # 异常2 dashboard_chart/metadata/metadata_builder/builder.py:66
#         try:
#             mock_dashboard.return_value = mock_data.copy()
#             mock_dashboard.return_value['is_multiple_screen'] = 1
#             data = builder.MetadataBuilder(**{'dashboard_id': 'fake-id'})
#         except UserError as e:
#             self.assertIn('获取报告数据异常', e.message)
#
#         # # 分支 dashboard_chart/metadata/metadata_builder/builder.py:58
#         # try:
#         #     mock_dashboard.return_value = mock_data.copy()
#         #     mock_dashboard.return_value['is_multiple_screen'] = 1
#         #     mock_table.return_value = [{'screen_id': self.dashboard_id}]
#         #     data = builder.MetadataBuilder(**{'dashboard_id': 'fake-id'})
#         # except UserError as e:
#         #     self.assertIn('获取报告数据异常', e.message)
#
#     # dashboard_chart/metadata/metadata_builder/generators/generator.py
#     def test_metadata_builder_generators_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = generator.MetadataBaseGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), None)
#
#         result = gr.build()
#         self.assertEqual(result, None)
#
#         # 一些静态方法
#         self.assertEqual(gr._encode_for_json({}), '{}')
#         try:
#             gr._encode_for_json({'0'})
#         except Exception as e:
#             self.assertEqual(bool(e), True)
#
#         self.assertEqual(gr._decode_for_json('{}'), {})
#         try:
#             gr._decode_for_json({'0'})
#         except Exception as e:
#             self.assertEqual(bool(e), True)
#
#         # 时间转换
#         import datetime
#         self.assertEqual(len(gr._time_to_str(datetime.datetime.now())), 21)
#         try:
#             gr._time_to_str({'0'})
#         except Exception as e:
#             self.assertEqual(bool(e), True)
#
#         self.assertEqual(gr.convert_to_dict({}, {}), defaultdict(list))
#
#     # dashboard_chart/metadata/metadata_builder/generators/component_filter_generator.py
#     def test_metadata_builder_generators_component_filter_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import component_filter_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = component_filter_generator.ComponentFilterGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'component_filter')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'field_initiator_id', 'related_list', 'chart_initiator_id', 'id', 'dataset_id'} - \
#                 set(result[0].keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_comparison_generator.py
#     def test_metadata_builder_generators_dashboard_chart_comparison_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_comparison_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = dashboard_chart_comparison_generator.DashboardChartComparisonGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_comparison')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertIn('id', result[0])
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_desire_generator.py
#     def test_metadata_builder_generators_dashboard_chart_desire_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_desire_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = dashboard_chart_desire_generator.DashboardChartDesireGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_desire')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertIn('id', result[0])
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_dim_generator.py
#     def test_metadata_builder_generators_dashboard_chart_dim_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_dim_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = dashboard_chart_dim_generator.DashboardChartDimGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_dim')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_field_sort_generator.py
#     def test_metadata_builder_generators_dashboard_chart_field_sort_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_field_sort_generator
#         storage_obj = self.get_fake_storage()
#         gr = dashboard_chart_field_sort_generator.DashboardChartFieldSortGenerator(**{
#             'storage': storage_obj, 'op_chart_id': self.op_chart_id
#         })
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_field_sort')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'id', 'dataset_field_id', 'field_source', 'sort', 'content', 'weight'} - \
#                 set(result[0].keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_filter_generator.py
#     def test_metadata_builder_generators_dashboard_chart_filter_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_filter_generator
#         storage_obj = self.get_fake_storage()
#
#         gr = dashboard_chart_filter_generator.DashboardChartFilterGenerator(
#             **{'storage': storage_obj, 'op_chart_id': self.op_chart_id})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_filter')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_generator.py
#     def test_metadata_builder_generators_dashboard_chart_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#
#         # 异常情况(没有id)
#         try:
#             gr = dashboard_chart_generator.DashboardChartGenerator(**{'storage': storage_obj})
#             gr.build()
#         except UserError as e:
#             self.assertIn('配置数据异常', e.message)
#
#         # 正常情况
#         gr = dashboard_chart_generator.DashboardChartGenerator(**{
#             'storage': storage_obj,
#             'op_chart_id': self.op_chart_id
#         })
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart')
#
#         result = gr.build()
#
#         self.assertIsInstance(result, dict)
#         if result:
#             self.assertEqual(
#                 {'id', 'name', 'chart_component_code', 'chart_type', 'column_order', 'config', 'level_code',
#                  'page_size', 'percentage', 'sort_method', 'data_modified_on', 'funcSetup', 'position', 'data',
#                  'parent_child_id', 'child_rank'} - \
#                 set(result.keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_markline_generator.py
#     def test_metadata_builder_generators_dashboard_chart_markline_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_markline_generator
#         storage_obj = self.get_fake_storage()
#         gr = dashboard_chart_markline_generator.DashboardChartMarklineGenerator(**{
#             'storage': storage_obj, 'op_chart_id': self.op_chart_id
#         })
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_markline')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'id', 'dashboard_chart_id', 'formula_mode', 'name', 'mode', 'value', 'axis_type', 'num'} - \
#                 set(result[0].keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_num_generator.py
#     def test_metadata_builder_generators_dashboard_chart_num_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_num_generator
#         storage_obj = self.get_fake_storage()
#
#         gr = dashboard_chart_num_generator.DashboardChartNumGenerator(**{
#             'storage': storage_obj, 'op_chart_id': self.op_chart_id
#         })
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_num')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_param_generator.py
#     def test_metadata_builder_generators_dashboard_chart_param_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_param_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = dashboard_chart_param_generator.DashboardChartParamGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_param')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_var_generator.py
#     def test_metadata_builder_generators_dashboard_chart_var_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_var_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = dashboard_chart_var_generator.DashboardChartVarGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_var')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_chart_zaxis_generator.py
#     def test_metadata_builder_generators_dashboard_chart_zaxis_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_chart_zaxis_generator
#         storage_obj = self.get_fake_storage()
#         gr = dashboard_chart_zaxis_generator.DashboardChartZaxisGenerator(**{'storage': storage_obj,
#                                                                              'op_chart_id': self.op_chart_id})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_chart_zaxis')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     def get_fake_storage(self):
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#
#         storage_obj.section_key_dict = {
#             'dashboard_chart_num': {
#                 self.op_chart_id: [
#                     {
#                         "id": self.fake_data("id"),
#                         "dashboard_chart_id": self.fake_data("dashboard_chart_id"),
#                         "formula_mode": self.fake_data("formula_mode"),
#                         "name": self.fake_data("name"),
#                         "mode": self.fake_data("mode"),
#                         "value": self.fake_data("value"),
#                         "axis_type": self.fake_data("axis_type"),
#                         "num": self.fake_data("num"),
#                     }
#                 ]},
#             'dashboard_chart_markline': {
#                 self.op_chart_id: [
#                     {
#                         "id": self.fake_data("id"),
#                         "dashboard_chart_id": self.fake_data("dashboard_chart_id"),
#                         "formula_mode": self.fake_data("formula_mode"),
#                         "name": self.fake_data("name"),
#                         "mode": self.fake_data("mode"),
#                         "value": self.fake_data("value"),
#                         "axis_type": self.fake_data("axis_type"),
#                         "num": self.fake_data("num"),
#                     }
#                 ]}
#             ,
#             'dashboard_chart_field_sort': {
#                 self.op_chart_id: [
#                     {
#                         "id": self.fake_data(1),
#                         "dataset_field_id": self.fake_data(1),
#                         "field_source": self.fake_data(1),
#                         "sort": self.fake_data(1),
#                         "content": self.fake_data(1),
#                         "weight": self.fake_data(1),
#                     }
#                 ]}
#         }
#
#         storage_obj.table_data['dashboard_chart_filter'] = [{
#             'id': self.fake_data(1),
#             'dashboard_id': self.fake_data(2),
#             'dashboard_chart_id': self.op_chart_id,
#             'col_name': self.fake_data(4),
#             'operator': self.fake_data(5),
#             'col_value': self.fake_data(6),
#             'dataset_field_id': self.fake_data(7),
#         }]
#         storage_obj.table_data['dashboard_chart_filter_relation'] = [{
#             'id': self.fake_data(1),
#             'dashboard_id': self.fake_data(2),
#             'dashboard_chart_id': self.fake_data(3),
#             'dashboard_chart_filter_id': self.fake_data(1),
#             'operator': self.fake_data(5),
#             'col_value': self.fake_data(6),
#         }]
#
#         storage_obj.table_data['dashboard_filter'] = [{
#             'id': self.fake_data(1),
#             'dashboard_id': self.fake_data(2),
#             'main_dataset_field_id': self.op_chart_id,
#             'operator': self.fake_data(4),
#             'col_value': self.fake_data(5),
#             'select_all_flag': self.fake_data(6),
#         }]
#
#         storage_obj.table_data['dashboard_filter_relation'] = [{
#             'id': self.fake_data(1),
#             'dashboard_id': self.fake_data(2),
#             'dashboard_filter_id': self.fake_data(1),
#             'operator': self.fake_data(4),
#             'col_value': self.fake_data(5),
#             'select_all_flag': self.fake_data(6),
#         }]
#
#         storage_obj.table_data['dashboard_dataset_field_relation'] = [{
#             'id': self.fake_data(1),
#             'dashboard_id': self.fake_data(2),
#             'main_dataset_field_id': self.fake_data(1),
#             'related_dataset_field_id': self.fake_data(4),
#         }]
#
#         storage_obj.table_data['dashboard_chart_penetrate_relation'] = [
#             {
#                 'id': self.fake_data(1),
#                 'dashboard_chart_id': self.fake_data(2),
#                 'dashboard_id': self.fake_data(3),
#                 'parent_chart_field_id': self.fake_data(4),
#                 'parent_chart_var_id': self.fake_data(5),
#                 'child_chart_field_id': self.fake_data(6),
#                 'type': 1,
#             },
#             {
#                 'id': self.fake_data(1),
#                 'dashboard_chart_id': self.fake_data(2),
#                 'dashboard_id': self.fake_data(3),
#                 'parent_chart_field_id': self.fake_data(4),
#                 'parent_chart_var_id': self.fake_data(5),
#                 'child_chart_field_id': self.fake_data(6),
#                 'type': 0,
#             },
#         ]
#
#         storage_obj.table_data['screen_dashboard'] = [
#             {
#                 'id': self.fake_data(1),
#                 'dashboard_id': self.fake_data(2),
#                 'screen_id': self.fake_data(3),
#                 'rank': self.fake_data(4),
#                 'type': self.fake_data(5),
#             }
#         ]
#
#         return storage_obj
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_filter_chart_default_value_generator.py
#     def test_metadata_builder_generators_dashboard_filter_chart_default_value_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_filter_chart_default_value_generator
#
#         storage_obj = self.get_fake_storage()
#         gr = dashboard_filter_chart_default_value_generator.DashboardFilterChartDefaultValueGenerator(**{
#             'storage': storage_obj, 'op_chart_id': self.op_chart_id
#         })
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_filter_chart_default_value')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'id', 'dataset_field_id', 'operator', 'value', 'select_all', 'extend_data'} - \
#                 set(result[0].keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_ilter_generatofr.py
#     def test_metadata_builder_generators_dashboard_filter_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_filter_generator
#         storage_obj = self.get_fake_storage()
#
#         gr = dashboard_filter_generator.DashboardFilterGenerator(**{
#             'storage': storage_obj, 'op_chart_id': self.op_chart_id
#         })
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard_filter')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'id', 'dashboard_id', 'main_dataset_field_id', 'operator', 'col_value', 'select_all_flag', 'operators',
#                  'main_dataset_id', 'alias_name', 'col_name', 'data_type', 'field_group', 'type', 'expression',
#                  'format', 'filter_relations'} - \
#                 set(result[0].keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/dashboard_generator.py
#     def test_metadata_builder_generators_dashboard_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import dashboard_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = dashboard_generator.DashboardGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'dashboard')
#
#         result = gr.build()
#         self.assertIsInstance(result, dict)
#         if result:
#             self.assertEqual(
#                 {'id', 'name', 'description', 'level_code', 'biz_code', 'cover', 'create_type', 'new_layout_type',
#                  'parent_id', 'is_show_mark_img', 'layout', 'styles', 'scale_mode', 'rank', 'publish'} - \
#                 set(result.keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/installed_component_generator.py
#     def test_metadata_builder_generators_installed_component_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import installed_component_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = installed_component_generator.InstalledComponentGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'installed_component')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/linkage_generator.py
#     def test_metadata_builder_generators_linkage_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import linkage_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = linkage_generator.LinkageGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'linkage')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/penetrate_generator.py
#     def test_metadata_builder_generators_penetrate_generator_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import penetrate_generator
#         storage_obj = self.get_fake_storage()
#
#         for index, i in enumerate(storage_obj.table_data['dashboard_chart']):
#             i['parent_id'] = self.fake_data(index)
#
#         gr = penetrate_generator.PenetrateGenerator(**{'storage': storage_obj, 'op_chart_id': self.op_chart_id})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'penetrate')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/redirect_generator.py
#     @patch('dashboard_chart.metadata.metadata_builder.storage.MetadataStorage.get_dashboard_info')
#     def test_metadata_builder_generators_redirect_generator_file(self, mock_dashboard):
#         from dashboard_chart.metadata.metadata_builder.generators import redirect_generator
#         mock_data = {'id': '39fd437d-8b21-7793-7c70-cd9e2713c94e', 'theme': 'colorful_white', 'data_report_id': None,
#                      'name': '参数-跳转-后端测试副本', 'type': 'FILE', 'parent_id': '39fd4df1-0832-5336-45a9-aa1e88b14fdf',
#                      'level_code': '0057-0004-', 'icon': '', 'platform': 'pc', 'terminal_type': '',
#                      'is_multiple_screen': 0, 'status': 0, 'user_group_id': '', 'default_show': 0, 'cover': '',
#                      'description': '', 'layout_type': '标准布局', 'share_secret_key': '',
#                      'layout': '{"mode":"grid","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}',
#                      'scale_mode': 0,
#                      'background': '{"show":true,"color":"#EBEDF2","image":"","size":"stretch","user_image":""}',
#                      'rank': None, 'build_in': 0, 'biz_code': '929e8ef9fdb746a297ee7a1a666f9fca', 'border': None,
#                      'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'create_type': 1,
#                      'new_layout_type': 1,
#                      'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}',
#                      'distribute_type': 0, 'is_show_mark_img': 1, 'application_type': 0, 'main_external_subject_id': '',
#                      'external_subject_ids': '', 'is_highdata': 0, 'is_deleted': 0, 'erp_app_code': None,
#                      'dataset_id': '', 'analysis_type': ''}
#         storage_obj = self.get_fake_storage()
#         gr = redirect_generator.RedirectGenerator(**{'storage': storage_obj, 'op_chart_id': self.op_chart_id})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'redirect')
#
#         mock_dashboard.return_value = mock_data.copy()
#         mock_dashboard.return_value['is_multiple_screen'] = 1
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'chart_id', 'chart_redirect'} - set(result[0].keys()),
#                 set()
#             )
#
#         mock_dashboard.return_value = mock_data.copy()
#         mock_dashboard.return_value['is_multiple_screen'] = 0
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'chart_id', 'chart_redirect'} - set(result[0].keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_builder/generators/screens_generator.py
#     @patch('dashboard_chart.metadata.metadata_builder.storage.MetadataStorage.filter_screens_table_data')
#     @patch('dashboard_chart.metadata.metadata_builder.storage.MetadataStorage.get_dashboard_info')
#     def test_metadata_builder_generators_screens_generator_file(self, mock_dashboard, mock_table):
#         from dashboard_chart.metadata.metadata_builder.generators import screens_generator
#         storage_obj = self.get_fake_storage()
#         mock_data = {'id': '39fd437d-8b21-7793-7c70-cd9e2713c94e', 'theme': 'colorful_white', 'data_report_id': None,
#                      'name': '参数-跳转-后端测试副本', 'type': 'FILE', 'parent_id': '39fd4df1-0832-5336-45a9-aa1e88b14fdf',
#                      'level_code': '0057-0004-', 'icon': '', 'platform': 'pc', 'terminal_type': '',
#                      'is_multiple_screen': 0, 'status': 0, 'user_group_id': '', 'default_show': 0, 'cover': '',
#                      'description': '', 'layout_type': '标准布局', 'share_secret_key': '',
#                      'layout': '{"mode":"grid","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}',
#                      'scale_mode': 0,
#                      'background': '{"show":true,"color":"#EBEDF2","image":"","size":"stretch","user_image":""}',
#                      'rank': None, 'build_in': 0, 'biz_code': '929e8ef9fdb746a297ee7a1a666f9fca', 'border': None,
#                      'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'create_type': 1,
#                      'new_layout_type': 1,
#                      'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}',
#                      'distribute_type': 0, 'is_show_mark_img': 1, 'application_type': 0, 'main_external_subject_id': '',
#                      'external_subject_ids': '', 'is_highdata': 0, 'is_deleted': 0, 'erp_app_code': None,
#                      'dataset_id': '', 'analysis_type': ''}
#
#         gr = screens_generator.ScreensGenerator(**{'storage': storage_obj, 'op_chart_id': self.op_chart_id})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'screens')
#
#         mock_dashboard.return_value = mock_data.copy()
#         mock_dashboard.return_value['is_multiple_screen'] = 1
#         mock_table.return_value = [{'screen_id': ''}, {'screen_id': self.fake_data(1)}]
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#         mock_dashboard.return_value = mock_data.copy()
#         mock_dashboard.return_value['is_multiple_screen'] = 0
#         result = gr.build()
#         self.assertIsInstance(result, list)
#
#     # dashboard_chart/metadata/metadata_builder/generators/var_relation_generator.py
#     def test_metadata_builder_generators_var_relation_file(self):
#         from dashboard_chart.metadata.metadata_builder.generators import var_relation_generator
#         storage_obj = storage.MetadataStorage(**{'dashboard_id': self.dashboard_id})
#         gr = var_relation_generator.VarRelationGenerator(**{'storage': storage_obj})
#         self.assertIn(self.dashboard_id, str(gr))
#         self.assertEqual(gr.generator_name(), 'var_relation')
#
#         result = gr.build()
#         self.assertIsInstance(result, list)
#         if result:
#             self.assertEqual(
#                 {'id', 'chart_initiator_id', 'field_initiator_id', 'dashboard_id', 'var_id', 'dataset_id'} - \
#                 set(result[0].keys()),
#                 set()
#             )
#
#     # dashboard_chart/metadata/metadata_schema
#     # 由于整个文件夹是定义变量，将整个文件夹一起覆盖
#     def test_metadata_schema(self):
#         from dashboard_chart.metadata import metadata_schema
#
#         schema_path = os.path.split(metadata_schema.__file__)[0]
#         schemas = [i for i in os.listdir(schema_path) if i.endswith('_schema.py')]
#
#         schema_modules = [
#             importlib.import_module("dashboard_chart.metadata.metadata_schema." + i.split('.py')[0])
#             for i in schemas
#         ]
#         for i in schema_modules:
#             for k, v in i.__dict__.items():
#                 if k.endswith('_schema'):
#                     self.assertIn('$schema', v)
#
#         # dashboard_chart/metadata/metadata_schema/validator.py
#         # 正常情况
#         from dashboard_chart.metadata.metadata_schema import validator
#         metadata = self._get_json('update_meta').get('metadata')
#         screens = metadata.get('screens')
#         metadata['first_report'] = screens[0]
#         metadata['screens'] = []
#         result = validator.EditorMetadataValidator(data=metadata).validate_metadata()
#         self.assertEqual(result[0], False)
#
#         # 异常情况1 dashboard_chart/metadata/metadata_schema/validator.py:124
#         data = None
#         result = validator.EditorMetadataValidator(data=data).validate_metadata()
#         self.assertEqual(result[0], False)
#
#         # 异常情况2  dashboard_chart/metadata/metadata_schema/validator.py:126
#         data = [1, 2]
#         result = validator.EditorMetadataValidator(data=data).validate_metadata()
#         self.assertEqual(result[0], False)
#
#         # 异常情况3 dashboard_chart/metadata/metadata_schema/validator.py:50
#         result = validator.EditorMetadataValidator(data=self._get_json('update_meta')).validate_metadata()
#         self.assertEqual(result[0], False)
#
#     # dashboard_chart/metadata/query_release_metadata_model.py
#     # TODO 75%
#     def test_metadata_query_release_metadata_model_file(self):
#         from dashboard_chart.metadata import query_release_metadata_model
#         from dmplib.redis import conn as conn_redis
#
#         cache_instance = query_release_metadata_model.DashboardCache(g.code, self.snapshot_id, conn_redis())
#         model = query_release_metadata_model.ReleasedDashboardMetadataQueryModel(snapshot_id=self.snapshot_id)
#         main_metadata_data = model.combine_screen_metadata().get_data()
#         self.assertIn(self.snapshot_id, str(main_metadata_data))
#
#         # dashboard_chart/metadata/query_release_metadata_model.py:507
#         single_released_dashboard_data = model._get_single_released_dashboard_data(self.dashboard_id)
#         self.assertIsInstance(single_released_dashboard_data, dict)
#
#         # dashboard_chart/metadata/query_release_metadata_model.py:478
#         data = model._get_released_chart_data_by_dashboard_id(self.dashboard_id)
#         self.assertIsInstance(data, list)
#
#         # dashboard_chart/metadata/query_release_metadata_model.py:415
#         data = model._get_screens_cache([
#             {
#                 'modified_on': self.fake_data(1),
#                 'data_type': 1,
#                 'id': self.fake_data('id'),
#             },
#             {
#                 'modified_on': self.fake_data(1),
#                 'data_type': 0,
#             }
#         ], cache_instance)
#         self.assertIsInstance(data[0], list)
#
#         # dashboard_chart/metadata/query_release_metadata_model.py:383
#         data = model._set_dashboard_chart_cache(cache_instance, self.fake_data('mock-dashboard-id'), [
#             {
#                 'id': self.fake_data('id-1'),
#                 'source': self.fake_data('source-1'), }
#         ])
#         self.assertEqual(data, None)
#
#     # dashboard_chart/metadata/schema_validator.py
#     @patch('dashboard_chart.metadata.schema_validator.SchemaBaseValidator._validate_data')
#     def test_metadata_schema_validator(self, mock_validate):
#         from dashboard_chart.metadata import schema_validator
#
#         # 正常情况
#         metadata = self._get_json('update_meta').get('metadata')
#         screens = metadata.get('screens')
#         metadata['first_report'] = screens[0]
#         metadata['screens'] = []
#         mock_validate.return_value = True
#
#         result = schema_validator.MetadataValidator(data=metadata).validate_screens_metadata()
#         self.assertEqual(result[0], True)
#         result = schema_validator.DashboardMetadataValidator(data=screens[0]).validate_metadata()
#         self.assertEqual(result[0], True)
#         result = schema_validator.PreviewMetadataValidator(data=metadata).validate_metadata()
#         self.assertEqual(result[0], True)
#
#         # 异常情况1 dashboard_chart/metadata/schema_validator.py:117
#         data = {}
#         result = schema_validator.MetadataValidator(data=data).validate_screens_metadata()
#         self.assertEqual(result[0], False)
#         result = schema_validator.DashboardMetadataValidator(data=data).validate_metadata()
#         self.assertEqual(result[0], False)
#         result = schema_validator.PreviewMetadataValidator(data=data).validate_metadata()
#         self.assertEqual(result[0], False)
#
#         # 异常情况2 dashboard_chart/metadata/schema_validator.py:119
#         data = [1, 2]
#         result = schema_validator.MetadataValidator(data=data).validate_screens_metadata()
#         self.assertEqual(result[0], False)
#         result = schema_validator.DashboardMetadataValidator(data=data).validate_metadata()
#         self.assertEqual(result[0], False)
#         result = schema_validator.PreviewMetadataValidator(data=data).validate_metadata()
#         self.assertEqual(result[0], False)
#
#         # 异常情况3 dashboard_chart/metadata/schema_validator.py:46
#         try:
#             result = schema_validator.MetadataValidator(data=self._get_json('update_meta')).validate_screens_metadata()
#             self.assertEqual(result[0], False)
#         except Exception as e:
#             self.assertIsInstance(e, UserError)
#
#     # dashboard_chart/metadata/common_metadata_model.py
#     def test_metadata_common_metadata_model_file(self):
#         from dashboard_chart.metadata import common_metadata_model
#         from dashboard_chart.metadata.query_preview_metadata_model import DefaultNodeModel
#         import datetime
#
#         model = DefaultNodeModel(**{
#             'id': self.fake_data('id')
#         })
#         time_str = model.time_to_str(datetime.datetime.now())
#         self.assertEqual(len(time_str), 19)
#
#         # dashboard_chart/metadata/common_metadata_model.py:387
#         try:
#             time_str = model.time_to_str(self.fake_data(1))
#         except:
#             self.assertEqual(time_str, self.fake_data(1))
#
#         model = DefaultNodeModel(**{
#             'set_default_data_flag': DefaultNodeModel(**{}),
#             'test_data': DefaultNodeModel(**{}),
#             'node_name': self.fake_data('node_name'),
#             'node_data': {'a': 'c'},
#             'default_node_data': DefaultNodeModel(**{}),
#             'has_sub_node_flag': self.fake_data('has_sub_node_flag'),
#         })
#         result = model.get_data()
#         self.assertEqual(result['a'], 'c')
#
#         # dashboard_chart/metadata/common_metadata_model.py:232
#         result = DefaultNodeModel(node_data='{}').get_json_data()
#         self.assertIsInstance(result, dict)
#         try:
#             result = DefaultNodeModel(node_data='xxx').get_json_data()
#         except Exception as e:
#             self.assertIsInstance(e, common_metadata_model.InvalidCallError)
#
#         result = model.get_dict_data()
#         self.assertIsInstance(result, dict)
#
#         # dashboard_chart/metadata/common_metadata_model.py:251
#         result = DefaultNodeModel(node_data='{}').get_keys()
#         self.assertEqual(result, None)
#         # result = DefaultNodeModel(node_data='[1,2]').get_keys()
#         # self.assertEqual(result, [])
#
#         result = model.get_node_name()
#         self.assertEqual(result, self.fake_data('node_name'))
#         result = model.get_dict()
#         self.assertIsInstance(result, dict)
#
#         node1 = DefaultNodeModel(**{'node_name': self.fake_data('node1')})
#         node1.node_data = {'b': 'v'}
#         try:
#             model.batch_add_sub_node([
#                 DefaultNodeModel(**{}),
#                 node1,
#                 int,
#             ])
#         except common_metadata_model.InvalidParamsError as e:
#             self.assertIn('添加对象必须为节点数据基类的子类', e.message)
#
#         model.batch_add_item_in_dict({'f': 'e'})
#         model.remove_sub_node(node1)
#         try:
#             model.remove_sub_node(int)
#         except Exception as e:
#             self.assertIsInstance(e, common_metadata_model.InvalidParamsError)
#
#         print(model.node_data)
#
#         #
#         # # dashboard_chart/metadata/common_metadata_model.py:108
#         # DefaultNodeModel(**{})
#
#
#     # dashboard_chart/metadata/release_parser.py
#     def test_metadata_release_parser_file(self):
#         from dashboard_chart.metadata import release_parser
#
#         metadata = self._get_json('update_meta').get('metadata')
#         screens = metadata.get('screens')
#         metadata['first_report'] = screens[0]
#         metadata['screens'] = []
#         parser = release_parser.ReleaseParser(meta=metadata)
#
#         # dashboard_chart/metadata/release_parser.py:66
#         relations = [
#             {
#                 'is_same_dataset': 1, 'field_initiator_id': self.fake_data(1), 'field_responder_id': self.fake_data(2)
#             }
#         ]
#         data = {'fields': []}
#         parser.parse_link_relations(relations, data)
#         self.assertEqual(data['is_same_dataset'], 1)
#         relations = [
#             {
#                 'is_same_dataset': 0, 'field_initiator_id': self.fake_data(1), 'field_responder_id': self.fake_data(2)
#             }
#         ]
#         data = {'fields': []}
#         parser.parse_link_relations(relations, data)
#         self.assertEqual(data['fields'][0]['initiator_id'], self.fake_data(1))
#
#         # dashboard_chart/metadata/release_parser.py:77
#         result = parser.parse_link(parser.meta)
#         self.assertIsInstance(result, defaultdict)
#
#         # dashboard_chart/metadata/release_parser.py:96
#         result = parser.parse_chart(metadata, {})
#         self.assertIsInstance(result[1], list)
#
#         # dashboard_chart/metadata/release_parser.py:18
#         result = parser.parse_dashboard(metadata, {})
#         self.assertIsInstance(result[1], dict)
#
#         # dashboard_chart/metadata/release_parser.py:18
#         result = parser.json_display_format([{"display_format": "display_format"}])
#         self.assertIsInstance(result, list)
#
#         # dashboard_chart/metadata/release_parser.py:18
#         result = parser.parse_z(parser.meta, [])
#         self.assertIsInstance(result, list)
#
#     @classmethod
#     def _create_editor(cls, editor):
#         json_obj = cls._get_json()
#         meta_sub = MetadataSubject(json_obj)
#         return meta_sub.create_editor(editor)
#
#     @staticmethod
#     def _get_json(file_name='meta'):
#         file_path = '{}/metadata/{}.json'.format(os.path.dirname(__file__), file_name)
#         with open(file_path, encoding='utf8') as f:
#             return json.load(f)
#
#     @staticmethod
#     def fake_data(i):
#         return 'fake-data-%s' % i
