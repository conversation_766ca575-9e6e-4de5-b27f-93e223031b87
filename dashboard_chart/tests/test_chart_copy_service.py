#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 14:13
# <AUTHOR> caoxl
# @File     : test_chart_copy.py
# pylint: skip-file
from dmplib.hug import g
from tests.base import BaseTest
import os
from dashboard_chart.chart_reproducer.transaction import Transaction

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)


import unittest
import logging

logger = logging.getLogger(__name__)


class TestChartCopyService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='zk', account='zk')

    # def test_get_copy_chart(self):
    #     g.code = "zk"
    #     g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
    #     # g.cookie = {'token': 'eyJhbGciOiJIUzI1NiIsInR5cC'}
    #     g.account = 'zk'
    #     tid = '39f7d5bc-6be7-02b4-7f83-83e947a4b4be'
    #     transaction = Transaction(g.userid, g.account, tid)
    #     transaction()


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestChartCopyService("test_get_copy_chart"))
    runner = unittest.TextTestRunner()
    runner.run(s)
