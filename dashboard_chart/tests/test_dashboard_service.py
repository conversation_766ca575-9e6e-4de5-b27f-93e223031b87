#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
# @Time     : 2021/09/02 09:31
# <AUTHOR> wangfei
"""
test dashboard_service
"""
import app_celery
from tests.base_test import BaseTest
import logging
from dashboard_chart.services import (
    dashboard_service,
    metadata_service,
    released_dashboard_service,
    chart_service,
    components_service,
    dashboard_lock_service, external_dashboard_service
)
from dashboard_chart.models import DashboardModel, ReleaseModel

logger = logging.getLogger(__name__)


class TestDashboardService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='uitest')

    # 查询报告列表
    def test_dashboard_list(self):
        kwargs = {
            "parent_id": "",
            "order_by": "mtime",
            "reverse": 1,
            "file_type": "",
            "include_child_file": 0,
            "include_hd_report": 1
        }
        res = dashboard_service.get_dashboard_list(**kwargs)
        print(res)

        kwargs = {
            "parent_id": "39fe88f6-5faa-edc1-c002-5ed4530022b2",
            "order_by": "mtime",
            "reverse": 1,
            "file_type": "",
            "include_child_file": 0,
            "include_hd_report": 1
        }
        res = dashboard_service.get_dashboard_list(**kwargs)
        print(res)

    # 报表搜索场景
    def test_search_dashboard(self):
        kwargs = {'name': "王斐"}
        res = dashboard_service.search_dashboard(**kwargs)
        print(res)
        kwargs = {'name': "unit", 'include_hd_report': 1}
        res = dashboard_service.search_dashboard(**kwargs)
        print(res)

    # 获取报告元数据场景
    def test_screens_metadata(self):
        kwargs = {'id': '39fe88f6-d3a4-7598-a4e9-dbdaa454e979'}
        msg, result = metadata_service.get_screens_preview_metadata_v2(kwargs.get('id', ''))
        print(result)

    # 报告的添加删除场景
    def test_add_delete_dashboard(self):
        kwargs = {
            "name": "cccdd", "platform": "pc", "theme": "colorful_white", "is_show_mark_img": 1,
            "type": "FILE", "level_code": "0005-0002-", "parent_id": "39fe88f6-5faa-edc1-c002-5ed4530022b2",
            "layout": "{\"ratio\": \"16:9\",\"width\":1920,\"height\":1080,\"lattice\":10, \"mode\": \"grid\"}",
            "background": "{\"show\": true,\"color\":\"#EBEDF2\",\"image\":\"\",\"size\":\"stretch\"}",
            "create_type": 1, "new_layout_type": 1
        }
        model = DashboardModel(**kwargs)
        dashboard_data = dashboard_service.add_dashboard(model, return_dict=True)
        dashboard_id = dashboard_data.get('id', '')
        print(dashboard_id)
        result = dashboard_service.delete_dashboard_by_dashboard_id(dashboard_id)
        print(result)

    # 报告复制场景
    def test_copy_dashboard(self):
        kwargs = {
            "name": "bbbb", "platform": "pc", "theme": "colorful_white", "is_show_mark_img": 1,
            "type": "FILE", "level_code": "0005-0002-", "parent_id": "39fe88f6-5faa-edc1-c002-5ed4530022b2",
            "layout": "{\"ratio\": \"16:9\",\"width\":1920,\"height\":1080,\"lattice\":10, \"mode\": \"grid\"}",
            "background": "{\"show\": true,\"color\":\"#EBEDF2\",\"image\":\"\",\"size\":\"stretch\"}",
            "create_type": 1, "new_layout_type": 1
        }
        model = DashboardModel(**kwargs)
        dashboard_data = dashboard_service.add_dashboard(model, return_dict=True)
        dashboard_id = dashboard_data.get('id', '')
        kwargs = {
            "dashboard_id": dashboard_id,
            "target_id": "39fe88f6-5faa-edc1-c002-5ed4530022b2",
            "dashboard_name": "bbbb_副本",
        }
        res = dashboard_service.copy_dashboard(
            kwargs.get('dashboard_id'), kwargs.get('target_id'), kwargs.get('dashboard_name')
        )
        try:
            dashboard_service.delete_dashboard_by_dashboard_id(dashboard_id)
            dashboard_service.delete_dashboard_by_dashboard_id(res[0].get('id'))
        except Exception as e:
            print(str(e))

    # 报告发布场景
    def test_release_dashboard(self):
        kwargs = {
            'id': '39ffb988-aaf9-dc1d-5250-26e7eb96ac66',
            'status': 1,
            'type_access_released': 4,
            'user_groups': [],
            'view_passwd': ''
        }
        model = ReleaseModel(**kwargs)
        res = released_dashboard_service.release_with_process(model)
        print(res)

    # 报告重命名
    def test_dashboard_rename(self):
        kwargs = {
            "id": "3a00d996-6c75-9c60-b1a3-e3eebde8ff1d",
            "name": "update_test", "type": "FILE", "platform": "pc", "icon": "", "cover": "", "description": "",
            "level_code": "0005-0002-", "parent_id": "39fe88f6-5faa-edc1-c002-5ed4530022b2",
            "created_on": "2021-08-25 15:25:37", "modified_on": "2021-09-02 09:26:10", "status": 0,
            "is_multiple_screen": 0, "created_by": "local", "create_type": 1, "new_layout_type": 1,
            "distribute_type": None, "terminal_type": "", "layout":
                {"mode": "grid", "platform": "pc", "ratio": "16:9", "width": 1920, "height": 1080, "lattice": 10,
                 "toolbar": "show", "screenHeader": "show", "slider_top": 0, "layout_mode": "none", "card_radius": 0},
            "application_type": 0, "main_external_subject_id": "", "external_subject_ids": "",
            "edit_on": "2021-09-02 09:47:39", "released_on": None, "edit_status": 0, "user_name": "local", "sub": [],
            "actions": [{"action_code": "copy", "action_name": "复制"}, {"action_code": "edit", "action_name": "编辑"}]
        }
        model = DashboardModel(**kwargs)
        result = dashboard_service.update_dashboard_name(model)
        print(result)

    # 组件获取数据场景
    def test_dashboard_chart_data(self):
        kwargs = {
            "dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979",
            "chart_params": [
                {
                    "id": "2c995770-04a3-11ec-b2d6-ef795812897c",
                    "report_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979",
                    "dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979",
                    "chart_code": "dmp_handsontable",
                    "data_logic_type_code": "column",
                    "conditions": [],
                    "external_subject_ids":[],
                    "penetrate_conditions":[],
                    "penetrate_filter_conditions":[],
                    "filter_conditions":[],
                    "chart_filter_conditions":[],
                    "chart_linkage_conditions":[],
                    "drill_conditions":[],
                    "dims":[],
                    "nums":[],
                    "dashboard_conditions":[],
                    "query_vars":[],
                    "pagination":{
                        "page": 1,
                        "total": 0,
                        "page_size": 150
                    },
                    "column_display": [
                        {
                            "dataset_id": "39fe88fb-6f51-3e04-408d-d4c0ada79ccb",
                            "dataset_field_id": "39fe88fc-6369-3b3e-835b-d9bc20b53405",
                            "data_type": "字符串",
                            "col_name": "NAME_3931114469",
                            "alias_name": "name",
                            "order": 0,
                            "rank": 0,
                            "col_type": "dim",
                            "is_show": 1,
                            "group": None
                        },
                        {
                            "dataset_id": "39fe88fb-6f51-3e04-408d-d4c0ada79ccb",
                            "dataset_field_id": "39fe88fc-6369-3b36-b4e1-5899656a1f53",
                            "data_type": "数值",
                            "col_name": "XSE_12313228402",
                            "alias_name": "销售额",
                            "order": 1,
                            "rank": 1,
                            "col_type": "num",
                            "is_show": 1,
                            "group": None
                        },
                        {
                            "dataset_id": "39fe88fb-6f51-3e04-408d-d4c0ada79ccb",
                            "dataset_field_id": "39fe88fc-6369-3b33-a302-498bf725d118",
                            "data_type": "数值",
                            "col_name": "MJ_6348251558",
                            "alias_name": "面积",
                            "order": 2,
                            "rank": 2,
                            "col_type": "num",
                            "is_show": 1,
                            "group": None
                        }
                    ]
                },
                {
                    "id": "30f90818-04a3-11ec-b2d6-ef795812897c",
                    "report_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979",
                    "dashboard_id": "39fe88f6-d3a4-7598-a4e9-dbdaa454e979",
                    "chart_code": "cluster_column",
                    "data_logic_type_code": "default",
                    "conditions": [],
                    "external_subject_ids": [],
                    "penetrate_conditions":[],
                    "penetrate_filter_conditions":[],
                    "filter_conditions":[],
                    "chart_filter_conditions":[],
                    "chart_linkage_conditions":[],
                    "drill_conditions":[],
                    "dims":[],
                    "nums":[],
                    "dashboard_conditions":[],
                    "query_vars":[]
                }
            ]
        }
        result = chart_service.batch_get_chart_data(kwargs.get("chart_params"))
        print(result)

    def test_move_dashboard(self):
        kwargs = {
            "dash_id": "3a027eab-ef65-0fa7-be79-598eb42c433f", "target_dash_id": "3a027eac-06ab-b099-fd1d-7e065e99a1e2"
        }
        re = dashboard_service.move_dashboard(kwargs.get('dash_id'), kwargs.get('target_dash_id'))
        print(re)

    def test_get_all_installed_components(self):
        res = components_service.get_installed_components()
        print(res)

    def test_get_child_dashboard(self):
        kwargs = {
            "dashboard_id": "39ffb988-aaf9-dc1d-5250-26e7eb96ac66",
            "is_all": 1,
        }
        dashboard_id = kwargs.get('dashboard_id', '')
        is_all = True if int(kwargs.get('is_all', 1)) == 1 else False
        with_deleted = True if int(kwargs.get('with_deleted', 0)) == 1 else False
        res = dashboard_service.get_child_dashboard(dashboard_id, is_all, with_deleted)
        print(res)

    def test_get_erp_site_url(self):
        res = dashboard_service.get_erp_site_url()
        print(res)

    def test_dashboard_error_alarm(self):
        dict = {
            "org_code":"uitest",
            "biz_type":"大小屏",
            "biz_id":"3a09ebe0-f8c1-1946-7194-d77a64d989d5",
            "biz_name":"测试大屏名称1",
            "error_type":"组件取数报错",
            "error_data_id":"5dde2f5d-c175-11ed-a812-b53e117c03e1",
            "error_msg":"错误信息错误信息错误信息错误信息错误信息错误信息",
        }
        external_dashboard_service.dashboard_error_alarm(**dict)

    def test_dashboard_error_alarm_celery(self):
        dict = {
            "org_code": "uitest",
            "biz_type": "大小屏",
            "biz_id": "3a09ebe0-f8c1-1946-7194-d77a64d989d5",
            "biz_name": "测试大屏名称1",
            "error_type": "组件取数报错",
            "error_data_id": "5dde2f5d-c175-11ed-a812-b53e117c03e1",
            "error_msg": "错误信息错误信息错误信息错误信息错误信息错误信息",
        }
        app_celery.dashboard_error_alarm.apply_async(**dict)