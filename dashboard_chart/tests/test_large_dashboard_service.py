#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test dashboard extra
"""
import json
import logging
from tests.base import BaseTest
from dashboard_chart.services.large_dashboard_service import MoveOldDashboardAsLargeScreen,MoveOldDashboardAsLargeScreenWithFolder

logger = logging.getLogger(__name__)


# class TestDashboardExtraService(BaseTest):
#     def __init__(self, method_name="runTest"):
#         super().__init__(method_name, code='testlevel222', account='testlevel222')
#
#     def test_move_dashboard(self):
#         from dmplib.hug import g
#         g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
#         dashboard_id = '3a0cea1e-2113-381f-506e-7a088e31e2a8'
#         mover = MoveOldDashboardAsLargeScreen(dashboard_id, 'root')
#         mover.move()
#         print('op_records::::\n', json.dumps(mover.op_records, ensure_ascii=False, indent=2))


class TestDashboardExtraService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='tz10_01', account='tz10_01')

    def test_move_dashboard(self):
        from dmplib.hug import g
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
        dashboard_id = '3a0cea1e-2113-381f-506e-7a088e31e2a8'
        mover = MoveOldDashboardAsLargeScreen(dashboard_id, 'root')
        mover.move()
        print('op_records::::\n', json.dumps(mover.op_records, ensure_ascii=False, indent=2))

    def test_move_dashboard2(self):
        from dmplib.hug import g
        g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
        dashboard_id = '3a0cea1e-2113-381f-506e-7a088e31e2a8'
        mover = MoveOldDashboardAsLargeScreenWithFolder(dashboard_id, 'root')
        mover.move()
        print('op_records::::\n', json.dumps(mover.op_records, ensure_ascii=False, indent=2))
