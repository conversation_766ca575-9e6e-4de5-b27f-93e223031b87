#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test key_dashboard_service
"""
import json
import app_celery
from tests.base_test import BaseTest
import logging
from dashboard_chart.services.key_dashboard_service import key_dashboard_stat
from dashboard_chart.services.key_dashboard_service import key_dashboard_mem_usage

logger = logging.getLogger(__name__)


class TestKeyDashboardService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='uitest')

    def test_key_dashboard_stat(self):
        params = {
            "project_code": "uitest"
        }
        key_dashboard_stat(params)

    def test_key_dashboard_stat_celery(self):
        params = {
            "project_code": "uitest"
        }
        app_celery.key_dashboard_stat.apply_async(**params)

    def test_key_dashboard_mem_usage(self):
        res = key_dashboard_mem_usage([])
        print(json.dumps(res, ensure_ascii=False))