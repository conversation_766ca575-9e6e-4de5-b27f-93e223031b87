#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/07/21 09:31
# <AUTHOR> wangfei
# @File     : test_dashboard_editor.py
import json
import os
import random
import re
import time
from collections import defaultdict

from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id
from dashboard_chart.dashboard_editor.editor_utils import EditorUtils
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.dashboard_editor.metadata_storage import MetadataStorage
from dataset import external_query_service
from dashboard_chart.services.dashboard_service import update_metadata, generate_insert_table
from tests.base_test import BaseTest
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardChartParamsJumpModel,
    DashboardChartPenetrateRelationModel,
    DashboardChartSelectorFieldModel,
    DashboardChartSelectorModel,
    DashboardComponentFilterFieldModel,
    DashboardComponentFilterModel,
    DashboardJumpConfigModel,
    DashboardChartDesireModel,
    DashboardJumpRelationModel,
    DashboardChartParamsModel,
    DashboardChartFilterModel,
    DashboardChartDimModel,
    DashboardVarJumpRelationModel,
    DashboardFixedVarJumpRelationModel,
    DashboardChartFilterRelation,
    ChartFitlerModel,
    ChartFitlerFixedValueModel,
    ChartFilterRelation,
    ChartLinkageModel,
    ChartLinkageRelation,
    DashboardChartMarklineModel,
    DashboardChartComparisonModel,
    DashboardChartFieldSortModel,
    DashboardModel,
    DashboardFilterModel,
    DashboardFilterRelationModel,
    DashboardDatasetFieldRelationModel,
    DashboardDatasetVarFieldRelationModel,
    DashboardValueSourceModel,
    DashboardVarsValueSourceRelationModel,
    VarRelationsModel,
    DashboardChartModel
)
from dashboard_chart.metadata.release_parser import ReleaseParser
from dashboard_chart.dashboard_editor.editor.editor_checker import (
    EditorChecker,
    NotFoundErrorEditorChecker,
    NotDelErrorEditorChecker,
    DbErrorEditorChecker
)
from unittest.mock import patch


class TestEditorParser(BaseTest):
    """
    解析json数据类的测试
    """

    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='uitest')

    def test_dashboard(self):
        editor = self._create_editor('dashboard')
        model, error = editor.edit()
        yes_model = [
            DashboardModel(
                **{
                    "id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "dataset_id": '',
                    "theme": 'tech_blue',
                    "type": 'FILE',
                    "platform": 'pc',
                    "is_multiple_screen": 0,
                    "cover": '',
                    "description": '',
                    "layout_type": '标准布局',
                    "layout": '{"lattice":10,"ratio":"16:9","height":1080,"toolbar":"show","width":1920,"platform":"pc"}',
                    "scale_mode": 0,
                    "background": '{"image":"","size":"stretch","color":"RGBA(15,24,47,1)","show":true}',
                    "rank": 0,
                    "biz_code": model[0].biz_code,
                    "refresh_rate": None,
                    "grid_padding": 'null',
                    "create_type": 1,
                    "is_show_mark_img": 1,
                    "new_layout_type": 0,
                    "smart_beauty_status": 0,
                    "main_external_subject_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "application_type": 0,
                    "external_subject_ids": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "line_height":40
                }
            )
        ]
        self.assertEqual(model, yes_model)

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '报告元数据保存')

        editor._get_layout_type({"layout": {"mode": "free"}})
        editor._get_value({"layout": {"mode": "free"}}, 'a')
        editor.generate_code(None)

    def test_dashboard_chart(self):
        editor = self._create_editor('dashboard_chart')
        editor.edit()

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图元数据保存')

        # 验证不合法的场景
        meta = {"first_report": {"charts": []}}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_chart')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        meta_sub.attach_editor('dashboard_chart_penetrate_relation')

        editor.get_desired_value({"data": {"indicator": []}})
        editor.get_desired_value({"data": {"indicator": {"desires": [{"mode": 0}]}}})

    def test_dashboard_chart_penetrate_relation(self):
        editor = self._create_editor('dashboard_chart_penetrate_relation')
        models, errors = editor.edit()
        yes_models = [
            DashboardChartPenetrateRelationModel(
                child_chart_field_id='39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                dashboard_chart_id='39eacc20-58eb-4dfc-6b8f-dc2a76a84e1f',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                id='39eacc24-c045-c96a-2d4f-3fd031eb96bf',
                parent_chart_field_id='39e9f3e7-cc78-3139-e5fe-205baba60b38',
                type=0,
            ),
            DashboardChartPenetrateRelationModel(
                id='39eacc24-c045-c96a-2d4f-3fd031eb96bf',
                dashboard_chart_id='39eacc20-58eb-4dfc-6b8f-dc2a76a84e1f',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                parent_chart_field_id='39e9f3e7-cc78-3139-e5fe-205baba60b38',
                child_chart_field_id='39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                type=1,
            ),
            DashboardChartPenetrateRelationModel(
                id='39eacc24-c045-c96a-2d4f-3fd031eb96bf',
                dashboard_chart_id='39eacc20-58eb-4dfc-6b8f-dc2a76a84e1f',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                parent_chart_field_id='39e9f3e7-cc78-3139-e5fe-205baba60b38',
                child_chart_field_id='39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                parent_chart_var_id='',
                type=2,
            )
        ]
        self.assertEqual(models, yes_models, '解析穿透关联关系失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '保存穿透关联关系')

        # 验证不合法的场景
        meta_sub = MetadataSubject('[]')
        editor = meta_sub.create_editor('dashboard_chart_penetrate_relation')
        models, errors = editor.edit()
        self.assertEqual(models, [])

    def test_dashboard_chart_selector(self):
        editor = self._create_editor('dashboard_chart_selector')
        models, errors = editor.edit()
        yes_models = [
            DashboardChartSelectorModel(
                **{
                    "chart_initiator_id": '39eacc3a-cfd0-4046-570d-fa9d93ffb99e',
                    "chart_responder_id": '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "id": '39eacc3b-7b9f-6e0e-c9ad-140cdf93eeba',
                    "is_same_dataset": 1,
                    "type": 0,
                }
            ),
            DashboardChartSelectorFieldModel(
                **{
                    "chart_id": '39eacc3a-cfd0-4046-570d-fa9d93ffb99e',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "field_initiator_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                    "field_responder_id": '39e9f3e7-cc79-910f-2f5d-2c2028397583',
                    "id": '39eacc3b-7ba3-6154-f1e3-ff7fcc1a0010',
                    "selector_id": None,
                }
            ),
        ]
        self.assertEqual(models, yes_models, '解析联动关系失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '保存联动')

        # 验证不合法的场景
        meta_sub = MetadataSubject('[]')
        editor = meta_sub.create_editor('dashboard_chart_selector')
        models, errors = editor.edit()
        self.assertEqual(models, [])

    def test_dashboard_component_filter(self):
        editor = self._create_editor('dashboard_component_filter')
        models, errors = editor.edit()
        yes_models = [
            DashboardComponentFilterModel(
                **{
                    "chart_initiator_id": '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    "chart_responder_id": '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    "dataset_id": '39e9fe2a-976f-8eff-15dc-0d7ebba321d6',
                    "id": '39eacc33-e996-32e9-8a73-ac850f879bc9',
                    "is_same_dataset": 0,
                }
            ),
            DashboardComponentFilterFieldModel(
                **{
                    "chart_id": '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    "field_initiator_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                    "field_responder_id": '39e9f3e7-cc78-3139-e5fe-205baba60b38',
                    "filter_id": None,
                    "id": '39eacc33-e99b-3932-e4d9-4dfa1b341473',
                }
            ),
            DashboardComponentFilterModel(
                **{
                    "chart_initiator_id": '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    "chart_responder_id": '39eacc26-666e-d3e0-e055-530ae5768cc9',
                    "dataset_id": '39e9fe2a-976f-8eff-15dc-0d7ebba321d6',
                    "id": '39eacc33-e9a0-ae26-7869-6972924a7be0',
                    "is_same_dataset": 0,
                }
            ),
            DashboardComponentFilterFieldModel(
                **{
                    "chart_id": '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    "field_initiator_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                    "field_responder_id": '39e9f3e7-cc78-3139-e5fe-205baba60b38',
                    "filter_id": None,
                    "id": '39eacc33-e9a4-9f1e-56ee-4f1dd0bb42d2',
                }
            ),
            DashboardComponentFilterModel(
                **{
                    "chart_initiator_id": '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    "chart_responder_id": '39eacc2e-a77f-9957-f048-d7b60e5cbb98',
                    "dataset_id": '39e9fe2a-976f-8eff-15dc-0d7ebba321d6',
                    "id": '39eacc33-e9aa-fe04-96ae-fb714e4e1d1f',
                    "is_same_dataset": 1,
                }
            ),
        ]
        self.assertEqual(models, yes_models, '解析筛选组件失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '保存组件筛选器')

        # 验证不合法的场景
        meta_sub = MetadataSubject('[]')
        editor = meta_sub.create_editor('dashboard_component_filter')
        models, errors = editor.edit()
        self.assertEqual(models, [])

    def test_dashboard_chart_jump(self):
        editor = self._create_editor('dashboard_chart_jump')
        models, errors = editor.edit()
        yes_models = [
            DashboardJumpConfigModel(
                **{
                    "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "has_token": 0,
                    "id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
                    "open_way": 1,
                    "source_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                    "source_type": 0,
                    "status": 1,
                    "target": '39eaae9b-fe19-349c-881a-14d032a36d32',
                    "target_type": 'dashboard',
                    "with_params": 0
                }
            ),
            DashboardJumpRelationModel(
                **{
                    "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                    "dashboard_filter_id": '2a20561c-b761-ef5c-6ed7-916bc766b3b2',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "dataset_field_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                    "filter_type": 0,
                    "jump_config_id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
                    "relation_field_type": 1,
                    "target_filter_field_id": '',
                    "target_filter_id": ''
                }
            ),
            DashboardJumpConfigModel(
                **{
                    "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "has_token": 0,
                    "id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
                    "open_way": 1,
                    "source_id": '39eacc35-0969-161d-a173-eaff172e559d',
                    "source_type": 0,
                    "status": 1,
                    "target": '39eaae9b-fe19-349c-881a-14d032a36d32',
                    "target_type": 'dashboard',
                    "with_params": 0
                }
            ),
            DashboardVarJumpRelationModel(
                **{
                    "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                    "dashboard_filter_id": '2a20561c-b761-ef5c-6ed7-916bc766b3b2',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "dataset_field_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                    "dataset_id": '39ea6201-53d6-ab7f-7ed4-46f9b6b633f8',
                    "filter_type": 0,
                    "jump_config_id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
                    "var_id": 'de75a13e-96b5-11eb-bb89-33b40e2f3731',
                    "relation_field_type": 1,
                    "target_filter_field_id": '',
                    "target_filter_id": ''
                }
            ),
            DashboardFixedVarJumpRelationModel(
                **{
                    "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                    "dashboard_filter_id": '2a20561c-b761-ef5c-6ed7-916bc766b3b2',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "dataset_field_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                    "filter_type": 0,
                    "jump_config_id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
                    "var_name": '维度',
                    "var_value": '123',
                    "relation_field_type": 1,
                    "target_filter_field_id": '',
                    "target_filter_id": ''
                }
            ),
            DashboardJumpConfigModel(
                **{
                    "dashboard_chart_id": '39eacc38-1ccd-1531-19ff-5ab09e289469',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "has_token": 0,
                    "id": '39eacc3a-1f1d-7a87-afcc-7a09dc51adcf',
                    "open_way": 1,
                    "source_id": '39ea74ac-df91-e1f5-aa05-42049334adfb',
                    "source_type": 0,
                    "status": 1,
                    "target": '39eaae9b-fe19-349c-881a-14d032a36d32',
                    "target_type": 'dashboard',
                    "with_params": 0
                }
            ),
            DashboardChartParamsJumpModel(
                **{
                    "chart_dataset_field_id": "39e9fe2a-d1f9-5a94-5e92-3a165606214a",
                    "dashboard_chart_id": '39eacc38-1ccd-1531-19ff-5ab09e289469',
                    "dashboard_filter_id": '2a20561c-b761-ef5c-6ed7-916bc766b3b2',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "param_dataset_field_id": '39ea74ac-df96-0247-1d9d-501671dc7a9a',
                    "source_id": '39ea74ac-df91-e1f5-aa05-42049334adfb',
                    "rank": 0,
                    "filter_type": 0,
                    "relation_field_type": 1,
                    "target_filter_field_id": '',
                    "target_filter_id": ''
                }
            ),
        ]
        self.assertEqual(models, yes_models, '解析跳转失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '保存跳转')

    def test_dashboard_chart_desire(self):
        editor = self._create_editor('dashboard_chart_desire')
        models, errors = editor.edit()
        yes_models = [
            DashboardChartDesireModel(
                **{
                    "alias": '成绩(1)',
                    "axis_type": None,
                    "dashboard_chart_id": '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "dataset_field_id": '39e9f3e7-cc7a-d2c4-61c7-00c757a816b2',
                    "display_format": '{"unit": "\\u65e0", "thousand_point_separator": 1, "display_mode": "num", "column_unit_name": "", "fixed_decimal_places": 0}',
                    "formula_mode": "max",
                    "id": '39eacc24-bf91-e3d8-5f73-d4ce771a640e',
                    "mode": 1,
                    "rank": 0,
                    "sort": None,
                    "value": "1",
                }
            ),
        ]
        self.assertEqual(models, yes_models, '解析目标值失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '保存目标值')

    def test_dashboard_chart_param(self):
        editor = self._create_editor('dashboard_chart_param')
        models, errors = editor.edit()
        yes_models = [
            DashboardChartParamsModel(
                **{
                    "alias": '城市',
                    "dashboard_chart_id": '39eacc38-1ccd-1531-19ff-5ab09e289469',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "dataset_field_id": '39ea74ac-df96-0247-1d9d-501671dc7a9a',
                    "param_id": '39eacc3a-1f01-a370-8bf8-0072db8eedba',
                    "rank": 4,
                }
            )
        ]
        self.assertEqual(models, yes_models, '解析单图参数失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '保存单图参数')

    def test_dashboard_chart_filter(self):
        editor = self._create_editor('dashboard_chart_filter')
        models, errors = editor.edit()
        yes_models = [
            DashboardChartFilterRelation(
                **{
                    "col_value": '10',
                    "dashboard_chart_filter_id": '39eacc24-bfae-13a9-febf-36ea7be776e3',
                    "dashboard_chart_id": '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "id": '39eacc24-bfb3-1c90-eda6-d60bb4e8ce00',
                    "operator": '>',
                }
            ),
            DashboardChartFilterModel(
                **{
                    "col_name": "CG_7950876137",
                    "col_value": None,
                    "dashboard_chart_id": '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    "dataset_field_id": '39e9f3e7-cc7a-d2c4-61c7-00c757a816b2',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "id": '39eacc24-bfae-13a9-febf-36ea7be776e3',
                    "operator": None,
                }
            )
        ]
        self.assertEqual(models, yes_models, '解析单图过滤器失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '保存单图过滤器')

    def test_chart_filter_editor(self):
        editor = self._create_editor('chart_filter_editor')
        models, errors = editor.edit()
        yes_models = [
            ChartFitlerFixedValueModel(
                id='39eacc35-0969-161d-a173-eaff172e559d',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                chart_id='39eacc1c-aff1-4085-d478-ba24167b1bf0',
                name='测试',
                value_type=0,
                identifier='',
                extra_data='',
            ),
            ChartFitlerModel(
                id='39eacc3a-cfd0-4046-570d-fa9d93ffb99e',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                dataset_id="39eaae9b-fe19-349c-881a-14d032a36d32",
                dataset_field_id="39e9fe2a-d1f9-5a94-5e92-3a165606214a",
                chart_id="39eacc1c-aff1-4085-d478-ba24167b1bf0",
                filter_type=0,
                available=1,
                initiator_source="fixed_value",
            ),
            ChartFilterRelation(
                id='39eacc3b-7b9f-6e0e-c9ad-140cdf93eeba',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                filter_id='39eacc3a-cfd0-4046-570d-fa9d93ffb99e',
                chart_responder_id='39e9f3e7-cc79-910f-2f5d-2c2028397583',
                dataset_responder_id='39e9f3e7-517b-8780-cd32-68c92089af16',
                field_responder_id='39e9f3e7-cc79-910f-2f5d-2c2028397583',
            )
        ]
        self.assertEqual(models, yes_models, '解析单图筛选失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图间筛选')
        print(editor)

        # 验证不合法的场景
        meta_sub = MetadataSubject('[]')
        editor = meta_sub.create_editor('chart_filter_editor')
        models, errors = editor.edit()
        self.assertEqual(models, [])

    def test_chart_linkage_editor(self):
        editor = self._create_editor('chart_linkage_editor')
        models, errors = editor.edit()
        yes_models = [
            ChartLinkageModel(
                id='39eacc3b-7ba3-6154-f1e3-ff7fcc1a0010',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                dataset_id='39e9f3e7-517b-8780-cd32-68c92089af16',
                dataset_field_id='39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                chart_id='39eacc3a-cfd0-4046-570d-fa9d93ffb99e',
            ),
            ChartLinkageRelation(
                id='39eacc3b-7b9f-6e0e-c9ad-140cdf93eeba',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                chart_responder_id='39eacc1c-aff1-4085-d478-ba24167b1bf0',
                dataset_responder_id='39e9f3e7-517b-8780-cd32-68c92089af16',
                field_responder_id='39e9f3e7-cc79-910f-2f5d-2c2028397583',
                link_id='39eacc3b-7ba3-6154-f1e3-ff7fcc1a0010'
            )
        ]
        self.assertEqual(models, yes_models, '解析单图联动失败')

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图间联动')

        # 验证不合法的场景
        meta_sub = MetadataSubject('[]')
        editor = meta_sub.create_editor('chart_linkage_editor')
        models, errors = editor.edit()
        self.assertEqual(models, [])
        print(editor)

    def test_dashboard_chart_mark_line(self):
        editor = self._create_editor('dashboard_chart_markline')
        models, errors = editor.edit()
        yes_models = [
            DashboardChartMarklineModel(
                **{
                    'id': '39eacc33-057f-7d36-8a28-f19021052e13',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'dashboard_chart_id': '39eacc2e-a77f-9957-f048-d7b60e5cbb98',
                    'name': '辅助线(1)',
                    'mode': '计算值',
                    'num': '39e9fe2a-d1fb-b06d-6929-8b1f0afe7669',
                    'formula_mode': 'avg',
                    'value': '',
                    'axis_type': 1,
                }
            )
        ]
        self.assertEqual(models, yes_models, '解析单图联动失败')

        # 验证不合法的场景
        meta = {"first_report": {"charts": []}}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_chart_markline')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '图表辅助线')

    def test_dashboard_chart_num(self):
        editor = self._create_editor('dashboard_chart_num')
        models, errors = editor.edit()

        # 验证不合法的场景
        meta = {"first_report": {"charts": []}}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_chart_num')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图度量元数据保存')

    def test_dashboard_chart_dim(self):
        editor = self._create_editor('dashboard_chart_dim')
        models, errors = editor.edit()
        yes_model = DashboardChartDimModel(
            **{
                "id": '39eacc24-bf72-59df-ccc6-1ee333c4831c',
                "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                "dashboard_chart_id": '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                "dim": '39e9f3e7-cc78-3139-e5fe-205baba60b38',
                "dim_type": 0,
                "alias": '学生姓名',
                "content": '',
                "is_subtotal_cate": 0,
                "formula_mode": '',
                "rank": 0,
                "display_format": '{"unit": "\\u65e0", "thousand_point_separator": 1, "display_mode": "num", "column_unit_name": "", "fixed_decimal_places": 0}',
                "parent_id": ''
            }
        )
        self.assertEqual(models[0], yes_model)
        # 验证不合法的场景
        meta = {"first_report": {"charts": []}}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_chart_dim')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图维度元数据保存')

    def test_dashboard_chart_comparison(self):
        editor = self._create_editor('dashboard_chart_comparison')
        models, errors = editor.edit()
        yes_model = [
            DashboardChartComparisonModel(
                **{
                    "id": '39eacc2a-261c-f4e2-4cdc-dfbe40c9bf83',
                    "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    "dashboard_chart_id": '39eacc26-666e-d3e0-e055-530ae5768cc9',
                    "dataset_field_id": '39e9f3e7-cc79-910f-2f5d-2c2028397583',
                    "alias": '学科',
                    "formula_mode": '',
                    "rank": 0,
                }
            )
        ]
        self.assertEqual(models, yes_model)
        # 验证不合法的场景
        meta = {"first_report": {"charts": []}}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_chart_comparison')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图对比元数据保存')

        editor = self._create_editor('dashboard_chart_comparison')
        editor.metadata_storage.metadata_comparisons = self.mock_metadata_comparisons
        models, errors = editor.edit()

    @staticmethod
    def mock_metadata_comparisons():
        return {'111': [{'dashboard_chart_id': '111111'}]}

    def test_dashboard_chart_field_sort(self):
        editor = self._create_editor('dashboard_chart_field_sort')
        models, errors = editor.edit()
        yes_model = [
            DashboardChartFieldSortModel(
                **{
                    "id": '39e9f3e7-517b-8780-cd32-68c92089af16',
                    "dashboard_id": "39eacc1c-3972-72ca-0b22-6be660daff5a",
                    "dashboard_chart_id": '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    "dataset_field_id": '39e9f3e7-cc7a-d2c4-61c7-00c757a816b2',
                    "field_source": '',
                    "sort": 'asc',
                    "content": '',
                    "weight": 0,
                }
            )
        ]
        self.assertEqual(models, yes_model)
        # 验证不合法的场景
        meta = {"first_report": {"charts": []}}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_chart_field_sort')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图字段排序元数据保存')

    def test_dashboard_chart_zaxis(self):
        editor = self._create_editor('dashboard_chart_zaxis')
        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图次轴元数据保存')

    def test_dashboard_filter(self):
        editor = self._create_editor('dashboard_filter')
        models, errors = editor.edit()
        yes_model = [
            DashboardFilterModel(
                **{
                    'id': '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'main_dataset_field_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                }
            ),
            DashboardFilterRelationModel(
                **{
                    'id': '39eacc24-bf72-59df-ccc6-1ee333c4831c',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'dashboard_filter_id': '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    'operator': '=',
                    'col_value': '',
                    'select_all_flag': 1,
                }
            ),
            DashboardDatasetFieldRelationModel(
                **{
                    'id': '39eacc24-bf72-59df-ccc6-1ee333c4831c',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'main_dataset_field_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'related_dataset_field_id': '39e9fe2a-976f-8eff-15dc-0d7ebba321d6',
                }
            )
        ]
        self.assertEqual(models, yes_model)

        # 验证不合法的场景
        meta = {"dashboard": []}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_filter')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '报告级筛选')

    def test_dashboard_var_filter(self):
        editor = self._create_editor('dashboard_var_filter')
        models, errors = editor.edit()
        yes_model = [
            DashboardDatasetVarFieldRelationModel(
                **{
                    'id': '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'var_name': '测试',
                    'data_type': '字符串',
                    'related_dataset_field_id': '39e9f3e7-517b-8780-cd32-68c92089af16',
                    'related_dataset_id': '39e9fe2a-976f-8eff-15dc-0d7ebba321d6',
                }
            ),
            DashboardDatasetVarFieldRelationModel(
                **{
                    'id': '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'var_name': '测试1',
                    'data_type': '字符串',
                    'related_dataset_field_id': '',
                    'related_dataset_id': '',
                }
            )
        ]
        self.assertEqual(models, yes_model)

        # 验证不合法的场景
        meta = {"dashboard": []}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_var_filter')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '报告级变量筛选')

    def test_dashboard_var_value_source_editor(self):
        editor = self._create_editor('dashboard_var_value_source_editor')
        models, errors = editor.edit()
        yes_model = [
            DashboardValueSourceModel(
                **{
                    'id': '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'value_source_name': '测试',
                    'value_source': '',
                    'value_identifier': ''
                }
            ),
            DashboardVarsValueSourceRelationModel(
                **{
                    'var_id': '39e9f3e7-517b-8780-cd32-68c92089af16',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'value_source_id': '39eacc2a-d999-f4f0-55a4-7178ed755451'
                }
            )
        ]
        self.assertEqual(models, yes_model)

        # 验证不合法的场景
        meta = {"dashboard": []}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('dashboard_var_value_source_editor')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '变量取值来源绑定关系')

    def test_var_relations_editor(self):
        editor = self._create_editor('var_relations_editor')
        models, errors = editor.edit()
        yes_model = [
            VarRelationsModel(
                **{
                    'id': '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                    'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    'chart_initiator_id': '39e9fe2a-976f-8eff-15dc-0d7ebba321d6',
                    'field_initiator_id': '',
                    'var_id': '39eacc2a-d999-f4f0-55a4-7178ed755451',
                    'var_dataset_id': '39e9f3e7-517b-8780-cd32-68c92089af16',
                    'initiator_type': 0
                }
            )
        ]
        self.assertEqual(models, yes_model)

        # 验证不合法的场景
        meta = {"dashboard": []}
        meta_sub = MetadataSubject(meta)
        editor = meta_sub.create_editor('var_relations_editor')
        models, errors = editor.edit()
        self.assertEqual(models, [])

        action_name = editor.get_action_name()
        self.assertEqual(action_name, '单图变量关系')

    def test_editor_check(self):
        editor_check = EditorChecker()
        result = editor_check._get_field_info(None)
        self.assertEqual(result, {})
        with patch.object(external_query_service, 'get_multi_dataset_fields', return_value=[1]) as _:
            result = editor_check._get_field_info('abc')
            self.assertEqual(result, 1)

        not_check = NotFoundErrorEditorChecker()
        not_check.check({}, {}, [])
        not_check.check({'aaa': ''}, {}, [123])

        not_del_check = NotDelErrorEditorChecker()
        model = [
            DashboardFilterModel(
                **{
                    "id": 123,
                    "dashboard_id": "456",
                }
            )
        ]
        not_del_check.check({}, model)
        not_del_check.check({"dashboard_filter_ids": [123]}, model)
        not_del_check.check({"dashboard_filter_ids": None}, model)

        db_check = DbErrorEditorChecker()
        db_check.check(None)
        db_check.check([123])

        model = DashboardFilterRelationModel(
            **{
                'id': '39eacc24-bf72-59df-ccc6-1ee333c4831c',
                'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                'dashboard_filter_id': '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                'operator': '=',
                'col_value': '',
                'select_all_flag': 1,
            }
        )
        result = model.get_used_resource()
        self.assertEqual(result, None)

        model = DashboardJumpRelationModel(
            **{
                "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                "dashboard_filter_id": '2a20561c-b761-ef5c-6ed7-916bc766b3b2',
                "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                "dataset_field_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                "filter_type": 1,
                "jump_config_id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
            }
        )
        result = model.get_use_resource()
        self.assertEqual(result,
                         {"dashboard_var_filter_ids": {'2a20561c-b761-ef5c-6ed7-916bc766b3b2'},
                          "dataset_field_ids": {'39e9fe2a-d1f9-5a94-5e92-3a165606214a'}}
                         )

    def test_models(self):
        model = DashboardFilterModel(
            **{
                "id": 123,
                "dashboard_id": "456",
            }
        )
        model1 = DashboardChartParamsJumpModel(
            **{
                'dashboard_id': 1,
                'dashboard_chart_id': 1,
                'param_dataset_field_id': 1,
                'source_id': 1,
                'dashboard_filter_id': 1,
                'rank': 1,
            }
        )
        result = model.get_used_resource()
        self.assertEqual(result, None)
        result = model.get_where()
        self.assertEqual(result, {'id': 123})
        result = model1.get_where()
        self.assertEqual(model1.get_dict(), result)

        filter_model = DashboardChartFilterRelation()
        result = filter_model.get_used_resource()
        self.assertEqual(result, None)
        editor_check = EditorChecker()
        result = filter_model.__eq__(editor_check)
        self.assertEqual(result, False)

        model = DashboardVarJumpRelationModel(
            **{
                "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                "dashboard_filter_id": '2a20561c-b761-ef5c-6ed7-916bc766b3b2',
                "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                "dataset_field_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                "dataset_id": '39ea6201-53d6-ab7f-7ed4-46f9b6b633f8',
                "filter_type": 1,
                "jump_config_id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
                "var_id": 'de75a13e-96b5-11eb-bb89-33b40e2f3731'
            }
        )
        result = model.get_use_resource()
        self.assertEqual(result,
                         {
                             "dashboard_var_filter_ids": {'2a20561c-b761-ef5c-6ed7-916bc766b3b2'},
                             "dataset_field_ids": {'39e9fe2a-d1f9-5a94-5e92-3a165606214a'}}
                         )

        model = DashboardFixedVarJumpRelationModel(
            **{
                "dashboard_chart_id": '39eacc35-0969-161d-a173-eaff172e559d',
                "dashboard_filter_id": '2a20561c-b761-ef5c-6ed7-916bc766b3b2',
                "dashboard_id": '39eacc1c-3972-72ca-0b22-6be660daff5a',
                "dataset_field_id": '39e9fe2a-d1f9-5a94-5e92-3a165606214a',
                "filter_type": 1,
                "jump_config_id": '39eacc37-0b8a-1c74-128f-a120e3be57b5',
                "var_name": '维度',
                "var_value": '123'
            }
        )
        result = model.get_use_resource()
        self.assertEqual(result, {
                "dashboard_var_filter_ids": {'2a20561c-b761-ef5c-6ed7-916bc766b3b2'},
                "dataset_field_ids": {'39e9fe2a-d1f9-5a94-5e92-3a165606214a'}
            }
        )
        try:
            model.var_name = None
            model.rules()
        except Exception as e:
            print(str(e))
        try:
            model.var_name = '维度'
            model.var_value = None
            model.rules()
        except Exception as e:
            print(str(e))

        model = DashboardChartModel()
        result = model.get_use_resource()
        self.assertEqual(result, None)

        model = DashboardChartDesireModel()
        result = model.get_use_resource()
        self.assertEqual(result, None)

        model = ChartFitlerModel(
                id='39eacc3a-cfd0-4046-570d-fa9d93ffb99e',
                dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                dataset_id="39eaae9b-fe19-349c-881a-14d032a36d32",
                dataset_field_id="39e9fe2a-d1f9-5a94-5e92-3a165606214a",
                chart_id="39eacc1c-aff1-4085-d478-ba24167b1bf0",
                filter_type=0,
                available=1,
                initiator_source="dataset_field",
            )
        result = model.get_use_resource()
        self.assertEqual(result, {
            'dataset_field_ids': {"39e9fe2a-d1f9-5a94-5e92-3a165606214a"},
            'dataset_ids': {"39eaae9b-fe19-349c-881a-14d032a36d32"}
        })

    def test_editor_utils(self):
        editor_utils = EditorUtils()
        result = editor_utils.batch_exec_sql(['select 1'])
        self.assertEqual(result, 1)

        result, sql = editor_utils.create_insert_sql('abc', ['a', 'b'], [{'a': '1', 'b': '2'}])
        self.assertEqual(sql, 'INSERT INTO `abc` (`a`,`b`) VALUES (1,2)')
        editor_utils.create_insert_sql('abc', ['a', 'b'], [{'c': '1', 'b': '2'}])
        editor_utils.create_insert_sql('abc', [], [])

        editor_utils.convert_to_db_value([1, 2, 3])
        editor_utils.convert_to_db_value(123)

    def test_editor_storage(self):
        meta = MetadataStorage({})
        result = meta.get_metadata()
        self.assertEqual(result, {})

        with patch.object(meta, 'metadata_dashboard_charts', return_value=[]) as _:
            result = meta.metadata_chart_marklines()
            self.assertEqual(result, [])

        meta = MetadataStorage({})
        with patch.object(meta, 'metadata_dashboard_charts', return_value=[{'abc': []}, {'abc': []}]) as _:
            result = meta.metadata_chart_marklines()
            self.assertEqual(result, [])

        meta = MetadataStorage({})
        data = [{'data': {}}, {'data': {}}]
        with patch.object(meta, 'metadata_dashboard_charts', return_value=data) as _:
            result = meta.metadata_chart_marklines()
            self.assertEqual(result, [])

        meta = MetadataStorage({})
        data = [{'data': {'indicator': {'marklines': ''}}}, {'data': {'indicator': {'marklines': ''}}}]
        with patch.object(meta, 'metadata_dashboard_charts', return_value=data) as _:
            result = meta.metadata_chart_marklines()
            self.assertEqual(result, [])

        meta = MetadataStorage({})
        data = [{'data': {'indicator': {'marklines': [123]}}}]
        with patch.object(meta, 'metadata_dashboard_charts', return_value=data) as _:
            result = meta.metadata_chart_marklines()
            self.assertEqual(result, [123])

        meta = MetadataStorage({})
        result = meta.get_chart_data_by_id()
        self.assertEqual(result, {})
        result = meta.get_chart_data_by_id(chart_id=123)
        self.assertEqual(result, {})

        meta = MetadataStorage({'first_report': {'charts': [{'id': 123}]}})
        result = meta.get_chart_data_by_id(chart_id=123)
        self.assertEqual(result, {'id': 123})
        meta.get_chart_data_by_id(chart_id=456)

        result = meta.metadata_chart_section_data()
        self.assertEqual(result, {})

    def test_editor_metadata_comparator(self):
        from dashboard_chart.dashboard_editor.metadata_comparator import MetadataComparator
        from dashboard_chart.repositories import editor_repository
        from dashboard_chart.services import metadata_service

        def mock_dashboard(dashboard_id):
            return dashboard_id

        def mock_dashboard_pre_metadata_v2(dashboard_id):
            return 500, {}
        editor_repository.get_dashboard_pre_metadata = mock_dashboard
        MetadataComparator.get_pre_metadata(123)
        metadata_service.get_screens_preview_metadata_v2 = mock_dashboard_pre_metadata_v2
        try:
            MetadataComparator.get_pre_metadata(None)
        except Exception as e:
            print(str(e))
        model = [
                ChartFitlerModel(
                    id='39eacc3a-cfd0-4046-570d-fa9d93ffb99e',
                    dashboard_id='39eacc1c-3972-72ca-0b22-6be660daff5a',
                    dataset_id="39eaae9b-fe19-349c-881a-14d032a36d32",
                    dataset_field_id="39e9fe2a-d1f9-5a94-5e92-3a165606214a",
                    chart_id="39eacc1c-aff1-4085-d478-ba24167b1bf0",
                    filter_type=0,
                    available=1,
                    initiator_source="dataset_field",
                ),
                DashboardFilterModel(
                    **{
                        'id': '39eacc1c-aff1-4085-d478-ba24167b1bf0',
                        'dashboard_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                        'main_dataset_field_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
                    }
                ),
        ]
        with patch.object(MetadataComparator, 'get_pre_metadata', return_value=self._get_json('meta')) as _:
            meta = MetadataComparator(self._get_json('meta'))
            try:
                meta.validate_multi_used_resource(model)
                meta.do_edit()
            except Exception as e:
                print(str(e))

        with patch.object(NotFoundErrorEditorChecker, 'check', return_value=None) as _:
            with patch.object(editor_repository, 'batch_operate_editor_modes', return_value=[False, {}]) as _:
                with patch.object(DbErrorEditorChecker, 'check', return_value=[1]) as _:
                    try:
                        meta.current_errors = []
                        meta.do_edit()
                    except Exception as e:
                        print(str(e))

        with patch.object(MetadataComparator, 'validate_multi_used_resource', return_value=1) as _:
            with patch.object(MetadataComparator, 'get_pre_metadata', return_value=self._get_json('meta')) as _:
                with patch.object(NotDelErrorEditorChecker, 'check', return_value=1) as _:
                    try:
                        meta1 = MetadataComparator(self._get_json('meta'))
                        meta1.current_errors = []
                        meta1.do_edit()
                    except Exception as e:
                        print(str(e))

        with patch.object(MetadataComparator, 'validate_multi_used_resource', return_value=None) as _:
            with patch.object(MetadataComparator, 'get_pre_metadata', return_value=self._get_json('meta')) as _:
                with patch.object(NotDelErrorEditorChecker, 'check', return_value=None) as _:
                    with patch.object(MetadataComparator, 'validate_use_resource', return_value=None) as _:
                        with patch.object(MetadataSubject, 'do_edit', return_value=[{}, None]) as _:
                            try:
                                meta1 = MetadataComparator(self._get_json('meta'))
                                meta1.current_errors = []
                                result = meta1.do_edit()
                                self.assertEqual(result, True)
                            except Exception as e:
                                print(str(e))
        try:
            from dashboard_chart.repositories import dashboard_repository
            with patch.object(
                    dashboard_repository, 'get_dashboard_chart_params_jump_by_dashboard_filter_ids',
                    return_value=[{"dashboard_filter_id": '123'}]
            ) as _:
                MetadataComparator.validate_used_resource(model)
        except Exception as e:
            print(str(e))

    def test_save_metadata(self):
        from dashboard_chart.dashboard_editor.metadata_comparator import MetadataComparator
        # 这个单测使用了测试环境的数据，只能在测试环境运行才能通过
        meta = self._get_json('meta')
        modify_times = defaultdict(lambda: 0)
        if 0:
            for chart in meta['first_report']['charts']:
                if modify_times['chart'] < 3:
                    # 名称都改一下
                    old_name = chart['name']
                    chart['name'] = re.sub(r'\d+', str(random.randint(2, 999)), chart['name'])
                    if old_name != chart['name']:
                        modify_times['chart'] += 1
        start = time.time()
        with patch.object(MetadataComparator, 'get_pre_metadata', return_value=meta) as _:
            update_metadata(meta)
        print('保存报告元数据耗时： ', time.time() - start, '秒')

    def test_copy_dashboard(self):
        meta = self._get_json('meta')
        pre_models, _ = MetadataSubject(meta).do_edit()
        generate_insert_table(meta, "39eacc1c-3972-72ca-0b22-6be660daff5a", "", "test_dashboard_copy")

    def test_release(self):
        # dashboard 解析
        # 测试的报告，这个数据不要随便改
        dashboard_id = '39eacc1c-3972-72ca-0b22-6be660daff5a'
        meta = self._get_json()
        dashboard_extra = {'snapshot_id': dashboard_id}
        table, dashboard_parsed = ReleaseParser.parse_dashboard(meta, dashboard_extra)
        self.assertEqual(table, 'dashboard_released_snapshot_dashboard', '解析表名错误')
        ok_dashboard = {
            'id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
            'snapshot_id': '39eacc1c-3972-72ca-0b22-6be660daff5a',
            'data_type': 1,
            'name': '编辑器v3测试元数据不能删除',
            'type': 'FILE',
            'level_code': '1313-0080-',
            'platform': 'pc',
            'status': 0,
            'user_group_id': '',
            'cover': '',
            'selectors': '{"39eacc3a-cfd0-4046-570d-fa9d93ffb99e": [{"chart_id": "39eacc1c-aff1-4085-d478-ba24167b1bf0"'
            ', "is_same_dataset": 1, "dataset_id": "39e9f3e7-517b-8780-cd32-68c92089af16", '
                         '"fields": [{"initiator_id": "39e9fe2a-d1f9-5a94-5e92-3a165606214a",'
                         ' "responder_id": "39e9f3e7-cc79-910f-2f5d-2c2028397583"}]}]}',
            'dashboard_filters': '[{"id": "39eacc1c-aff1-4085-d478-ba24167b1bf0", '
                      '"dashboard_id": "39eacc1c-3972-72ca-0b22-6be660daff5a", '
                      '"main_dataset_field_id": '
                      '"39eacc1c-3972-72ca-0b22-6be660daff5a", "operators": '
                      '[{"id": "39eacc24-bf72-59df-ccc6-1ee333c4831c", '
                      '"select_all_flag": 1, "operator": "=", "col_value": '
                      '1}], "filter_relations": [{"id": '
                      '"39eacc24-bf72-59df-ccc6-1ee333c4831c", '
                      '"main_dataset_field_id": '
                      '"39eacc1c-3972-72ca-0b22-6be660daff5a", '
                      '"related_dataset_field_id": '
                      '"39e9fe2a-976f-8eff-15dc-0d7ebba321d6"}]}]',
            'scale_mode': 0,
            'background': '{"image": ""' ', "size": "stretch", "color": "RGBA(15,24,47,1)", "show": true}',
            'biz_code': '52d5847a4f844b4da04ba7efc8f81fab',
            'theme': 'tech_blue',
            'type_access_released': 4,
            'refresh_rate': '',
            'layout': '{"width": 1920, "ratio": "16:9", "lattice": 10, "height": 1080, "toolbar": "show"}',
            'create_type': 0,
            'new_layout_type': 'free',
            "grid_padding": None,
        }
        for json_key in ('selectors', 'layout'):
            dashboard_parsed[json_key] = json.loads(dashboard_parsed[json_key])
            ok_dashboard[json_key] = json.loads(ok_dashboard[json_key])
        ok_dashboard.update(dashboard_extra)
        self.assertEqual(ok_dashboard, dashboard_parsed, '解析dashboard出错')

    def test_seq_id(self):
        old = []
        for i in range(9999):
            new_id = seq_id()
            self.assertFalse(new_id in old, '重复' + str(i))
            old.append(new_id)

    def test_update_with_none(self):
        db = get_db()
        db.update('dashboard_chart_dim', {'sort': None}, {"id": '796fde9f-0337-11e9-a94f-53f9ce9af3db'}, with_none=True)

    @classmethod
    def _create_editor(cls, editor):
        json_obj = cls._get_json()
        meta_sub = MetadataSubject(json_obj)
        return meta_sub.create_editor(editor)

    @staticmethod
    def _get_json(file_name='meta'):
        file_path = '{}/metadata/{}.json'.format(os.path.dirname(__file__), file_name)
        with open(file_path, encoding='utf8') as f:
            return json.load(f)
