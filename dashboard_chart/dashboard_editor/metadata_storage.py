#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/21 15:30
# <AUTHOR> caoxl
# @File     : metadata_storage.py
# pylint: disable=R0205

import functools


class MetadataStorage(object):
    """
    元数据取数类
    所有取数方法请以'metadata_'开头 以区分其他方法
    """

    def __init__(self, metadata):
        """
        初始化
        :param metadata: 元数据
        """
        self._metadata = metadata

    def get_metadata(self):
        return self._metadata

    @functools.lru_cache()
    def metadata_dashboard(self):
        """
        获取报告ID
        :return:
        """
        return self._metadata.get('dashboard')

    @functools.lru_cache()
    def metadata_dashboard_charts(self):
        """
        获取单图数据
        :return:
        """
        return self._metadata.get("first_report").get('charts')

    @functools.lru_cache()
    def metadata_dashboard_filters(self):
        """
        获取报告筛选配置
        :return:
        """
        if self._metadata.get('first_report'):
            return self._metadata.get('first_report').get('dashboard_filters')
        return []

    @functools.lru_cache()
    def metadata_global_params(self):
        """
        获取报告筛选配置
        :return:
        """
        if self._metadata.get('first_report'):
            return self._metadata.get('first_report').get('global_params') or []
        return []

    @functools.lru_cache()
    def metadata_dashboard_var_filters(self):
        """
        获取报告筛选配置
        :return:
        """
        if self._metadata.get('first_report'):
            return self._metadata.get('first_report').get('dashboard_var_filters') or []
        return []

    @functools.lru_cache()
    def metadata_dashboard_var_value_sources(self):
        """
        获取报告筛选配置
        :return:
        """
        if self._metadata.get('first_report'):
            return self._metadata.get('first_report').get('var_value_sources')
        return []

    @functools.lru_cache()
    def metadata_screens(self):
        """
        获取多屏数据
        :return:
        """
        return self._metadata.get('screens')

    @functools.lru_cache()
    def metadata_chart_marklines(self):
        """
        单图辅助线
        :return:
        """
        marklines = []
        charts = self.metadata_dashboard_charts()
        if len(charts) < 1:
            return marklines
        for chart in charts:
            if not chart.get('data') or not isinstance(chart.get('data'), dict):
                continue
            if not chart.get('data').get('indicator') or not isinstance(chart.get('data').get('indicator'), dict):
                continue
            _marklines = chart.get('data').get('indicator').get('marklines')
            if not isinstance(_marklines, list) or len(_marklines) < 1:
                continue
            marklines.extend(_marklines)
        return marklines

    @functools.lru_cache()
    def metadata_comparisons(self):
        """
        获取表格对比
        :return:
        """
        return self.metadata_chart_section_data("comparisons")

    @functools.lru_cache()
    def metadata_marklines(self):
        """
        获取表格对比
        :return:
        """
        return self.metadata_chart_section_data("marklines")

    def metadata_chart_section_data(self, section=''):
        """
        获取报告下的单图某同一片段配置
        :param section:
        :return:
        """
        chart_section_data = dict()
        charts = self.get_data_by_path('first_report.charts')
        if charts:
            for chart in charts:
                chart_id = chart.get('id')
                single_chart_indicator = self.get_data_by_path('data.indicator', chart)
                if not single_chart_indicator:
                    continue
                chart_section_data[chart_id] = single_chart_indicator.get(section)
        return chart_section_data

    def get_data_by_path(self, path, metadata=None):
        """获取节点元数据"""
        if metadata:
            ret = metadata
        else:
            ret = self._metadata
        for v in path.split('.'):
            if v in ret:
                ret = ret.get(v)
            else:
                return None
        return ret

    def get_chart_data_by_id(self, field_name_list=None, chart_id=""):
        """
        获取单图的指定字段数据
        :param field_name_list:
        :param chart_id:
        :return:
        """
        if not chart_id:
            return {}
        charts = self.get_data_by_path('first_report.charts')
        if not charts:
            return {}
        for chart in charts:
            if chart_id == chart.get("id"):
                return (
                    {field_name: chart.get(field_name) for field_name in field_name_list} if field_name_list else chart
                )
        return {}

    def get_chart_info_dict(self):
        """

        :return:
        """
        charts = self.get_data_by_path('first_report.charts')
        return {item.get("id"): item for item in charts} if charts and len(charts) > 0 else {}
