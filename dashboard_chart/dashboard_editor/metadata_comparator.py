#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/26 11:32
# <AUTHOR> caoxl
# @File     : metadata_comparator.py
from collections import defaultdict
import json
from datetime import datetime

from dmplib.utils.errors import UserError
from dashboard_chart.services import metadata_service, dashboard_extra_service
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from dashboard_chart.repositories import editor_repository
from dashboard_chart.repositories import dashboard_repository
from dataset.repositories import dataset_repository, dataset_var_repository
from dataset.repositories import dataset_field_repository
import logging
from dashboard_chart.dashboard_editor.editor.editor_checker import (
    NotFoundError<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    NotDelError<PERSON>ditor<PERSON><PERSON><PERSON>,
    <PERSON>b<PERSON>rror<PERSON>ditor<PERSON><PERSON><PERSON>,
    EditorCommonErrorChecker
)
from base import repository

logger = logging.getLogger(__name__)


class MetadataComparator:
    def __init__(self, metadata):
        self.metadata = metadata
        self._current_metadata_subject = MetadataSubject(metadata)
        dashboard = self._current_metadata_subject.metadata_storage().metadata_dashboard()
        self.dashboard_id = dashboard.get('id')
        pre_metadata = self.get_pre_metadata(self.dashboard_id)
        self._pre_metadata_subject = MetadataSubject(pre_metadata, edit_source="edit_pre_meta")
        self.current_errors = []

    def do_edit(self):
        current_models, current_edit_errors = self._current_metadata_subject.do_edit()
        pre_models, _ = self._pre_metadata_subject.do_edit()
        errors = dict()
        # 对比两次model 获得相关语句
        if not isinstance(current_models, set):
            current_models = set(current_models)
        if not isinstance(pre_models, set):
            pre_models = set(pre_models)

        self.common_edit_error_check(current_edit_errors)

        logger.debug("current_models:%s, pre_models:%s", current_models, pre_models)

        del_models = pre_models - current_models

        insert_models = current_models - pre_models
        not_found_errors = self.validate_use_resource(current_models)
        if not_found_errors:
            errors['not_found_errors'] = not_found_errors

        logger.debug("insert_models:%s, del_models:%s", insert_models, del_models)

        update_models = self.merge_update(insert_models, del_models)

        logger.debug("update models:%s", update_models)

        not_del_errors = self.validate_multi_used_resource(del_models)
        if not_del_errors:
            errors['not_del_errors'] = not_del_errors

        # 引用数据检查
        new_not_found_errors = NotFoundErrorEditorChecker().check(not_found_errors, self.metadata, current_models)
        if new_not_found_errors:
            self.current_errors.extend(new_not_found_errors)
        # 删除检查
        new_not_del_errors = NotDelErrorEditorChecker().check(not_del_errors, del_models)
        if new_not_del_errors:
            self.current_errors.extend(new_not_del_errors)
        # 如果有校验错误，直接返回错误
        if self.current_errors:
            return False, self.current_errors

        insert_table2dict = self.merge_insert(insert_models)
        if del_models or insert_table2dict or update_models:
            result, errors['db_errors'] = editor_repository.batch_operate_editor_modes(
                del_models,
                [insert_table2dict],
                update_models,
                {"metadata": json.dumps(self.metadata), "dashboard_id": self.dashboard_id, "version": 'V1.0.0'},
            )
            # 登记当前报告的编辑时间
            dashboard_extra_service.update_dashboard_edit_on(self.dashboard_id)
            # 如果修改的是子报告，那么主报告也需要更新时间
            if self.metadata.get('dashboard', {}).get('parent_id'):
                level_code = self.metadata.get('dashboard', {}).get('level_code') or ''
                if level_code:
                    dashboard_extra_service.update_root_dashboard_status(level_code)
        else:
            result = True
        if result is False:
            new_db_errors = DbErrorEditorChecker().check(errors.get("db_errors", []))
            if new_db_errors:
                self.current_errors.extend(new_db_errors)
            return False, self.current_errors
        return True, []

    def common_edit_error_check(self, errors):
        # dashboard_chart/dashboard_editor/metadata_subject.py:94
        # errors.append(editor.get_action_name() + ' 发生异常，错误信息: ' + str(e))
        # 返回的是文本， 需要先提提取出原始的错误信息
        raw_errors = [e.split('错误信息: ')[-1] for e in errors]
        new_errors = EditorCommonErrorChecker().check(raw_errors)
        self.current_errors.extend(new_errors)

    @staticmethod
    def merge_update(insert_models, del_models):
        """通过表的主键id找到是同一条记录，把delete和insert语句转化为update"""
        update_models = set()
        exclude_del_models = set()
        exclude_insert_models = set()
        for del_model in del_models:
            del_pk = del_model.get_pk()
            if not del_pk:
                continue
            for insert_model in insert_models:
                insert_pk = insert_model.get_pk()
                if not insert_pk:
                    continue
                if del_model.get_table_name() == insert_model.get_table_name() and getattr(
                        del_model, del_pk
                ) == getattr(insert_model, insert_pk):
                    update_models.add(insert_model)
                    exclude_del_models.add(del_model)
                    exclude_insert_models.add(insert_model)
        del_models -= exclude_del_models
        insert_models -= exclude_insert_models
        return update_models

    @staticmethod
    def merge_insert(insert_models):
        """合并同一个表的insert为批量insert"""
        ret = defaultdict(list)
        for model in insert_models:
            table = model.get_table_name()
            ret[table].append(model.get_dict())
        return ret

    @staticmethod
    def is_highdata_model(operate_model, chart_flag_map):
        if (
            chart_flag_map.get(operate_model.get_dict().get('id'))
            or chart_flag_map.get(operate_model.get_dict().get('chart_id'))
            or chart_flag_map.get(operate_model.get_dict().get('dashboard_chart_id'))
        ):
            return True
        return False

    @staticmethod
    def get_chart_flag_map(edit_models):
        chart_flag_map = {}
        for model in edit_models:
            if hasattr(model, "is_highdata") and hasattr(model, "id"):
                chart_flag_map[model.id] = model.is_highdata
        return chart_flag_map

    @staticmethod
    def validate_use_resource(models):
        """验证引用的外部资源是否存在"""
        errors = dict()
        fetch_fn = {
            "dashboard_ids": {"fn": dashboard_repository.get_dashboard_by_ids, "pk": 'id'},
            "dataset_ids": {"fn": dataset_repository.get_dataset_by_ids, "pk": 'id'},
            "dataset_field_ids": {'fn': dataset_field_repository.get_dataset_field_by_ids, "pk": 'id'},
            "dashboard_filter_ids": {'fn': dashboard_repository.get_dashboard_filter_by_ids, "pk": 'id'},
            "dashboard_var_filter_ids": {'fn': dashboard_repository.get_dashboard_var_filter_by_ids, "pk": 'id'},
            "jump_config_ids": {'fn': dashboard_repository.get_jump_config_by_ids, 'pk': 'id'},
            "dataset_var_ids": {'fn': dataset_var_repository.get_dataset_var_by_ids, 'pk': 'id'},
            "chart_ids": {"fn": dashboard_repository.get_dashboard_chart_by_chart_ids, "pk": 'id'},
        }
        # 查询出个引用的外部资源id
        use_resource = defaultdict(set)
        # TODO: 暂时屏蔽highdata组件相关校验，后续同步数据再开放
        chart_flag_map = MetadataComparator.get_chart_flag_map(models)
        for model in models:
            model.validate()
            resource = model.get_use_resource()
            if not resource:
                continue
            for key, ids in resource.items():
                ids = set(filter(bool, ids))
                if not ids:
                    continue
                # TODO: 暂时屏蔽highdata组件相关校验，后续同步数据再开放
                if MetadataComparator.is_highdata_model(model, chart_flag_map):
                    key = "highdata"
                use_resource[key] |= ids

        for resource_type, ids in use_resource.items():
            # TODO: 暂时屏蔽highdata组件相关校验，后续同步数据再开放；元数据保存不进行数据集引用，数据集字段引用的检查校验
            if resource_type in ["highdata", "dataset_ids", "dataset_field_ids"]:
                continue
            info = fetch_fn.get(resource_type)
            if not info:
                raise UserError(message="未定义资源" + resource_type)
            items = info['fn'](list(ids))
            if not items:
                errors[resource_type] = list(ids)
                continue
            exist_ids = (item.get(info['pk']) for item in items)
            exist_ids = set(exist_ids)
            not_found_ids = ids - exist_ids
            if not_found_ids:
                errors[resource_type] = list(not_found_ids)
        return errors

    @staticmethod
    def validate_used_resource(models):
        """验证被删除的资源，是否被外部引用"""
        errors = dict()
        fetch_fn = {
            "dashboard_filter_ids": {
                "fn": dashboard_repository.get_dashboard_chart_params_jump_by_dashboard_filter_ids,
                "pk": 'dashboard_filter_id',
            }
        }
        used_resource = defaultdict(set)
        for model in models:
            model.validate()
            resource = model.get_used_resource()
            if not resource:
                continue
            for key, ids in resource.items():
                used_resource[key] |= ids
        for resource_type, ids in used_resource.items():
            info = fetch_fn[resource_type]
            items = info['fn'](list(ids))
            if not items:
                break
            exist_ids = (item.get(info['pk']) for item in items)
            exist_ids = set(exist_ids)
            if exist_ids:
                errors[resource_type] = list(exist_ids)
                break
        return errors

    def validate_multi_used_resource(self, models):
        """验证被删除的资源，是否被外部引用"""
        errors = dict()
        fetch_fn = {
            "dashboard_filter_ids": [
                {
                    "fn": dashboard_repository.get_dashboard_chart_params_jump_by_dashboard_filter_ids,
                    "pk": 'dashboard_filter_id',
                },
                {"fn": dashboard_repository.get_jump_relation_by_dashboard_filter_ids, "pk": 'dashboard_filter_id'},
            ]
        }
        used_resource = defaultdict(set)
        for model in models:
            model.validate()
            resource = model.get_used_resource()
            if not resource:
                continue
            for key, ids in resource.items():
                used_resource[key] |= ids
        for resource_type, ids in used_resource.items():
            info = fetch_fn[resource_type]
            for operator in info:
                if not operator:
                    break
                items = operator['fn'](list(ids))
                exist_ids = (item.get(operator['pk']) for item in items)
                exist_ids = set(exist_ids)
                if exist_ids:
                    errors[resource_type] = errors.get(resource_type, set()) | exist_ids
        return self._item_to_list(errors)

    @staticmethod
    def _item_to_list(errors):
        """
        保持和原先方法的返回数据类型一样，转换为list类型
        :param errors:
        :return:
        """
        return {key: list(item) if item and isinstance(item, set) else item for key, item in errors.items()}

    @staticmethod
    def get_pre_metadata(dashboard_id):
        """
        获取指定报告的上一条元数据
        :param dashboard_id:
        :return:
        """
        meta = editor_repository.get_dashboard_pre_metadata(dashboard_id)
        if not meta:
            msg, meta = metadata_service.get_screens_preview_metadata_v2(dashboard_id)
            if msg:
                raise UserError(500, '获取元数据失败:' + msg)
        return meta
