#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from base.enums import ChartPenetrateRelationType
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartPenetrateRelationModel


class DashboardChartPenetrateRelation(Editor):
    def edit(self):
        errors = []
        editor_models = []
        penetrates = self.metadata_storage.get_data_by_path('first_report.chart_relations.penetrates')
        if not penetrates or not isinstance(penetrates, list):
            return editor_models, errors
        dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
        for penetrate in penetrates:
            # 穿透数据
            relations = penetrate.get('relation')
            if relations:
                for relation in relations:
                    editor_models.append(
                        DashboardChartPenetrateRelationModel(
                            id=relation.get('id'),
                            dashboard_chart_id=penetrate.get('chart_id'),
                            dashboard_id=dashboard_id,
                            parent_chart_field_id=relation.get('parent_chart_field_id'),
                            child_chart_field_id=relation.get('child_chart_field_id'),
                            type=ChartPenetrateRelationType.PenetrateType.value,
                        )
                    )
            # 穿透筛选联动等字段关联关系
            filter_relations = penetrate.get('penetrate_filter_relation')
            if filter_relations:
                for relation in filter_relations:
                    editor_models.append(
                        DashboardChartPenetrateRelationModel(
                            id=relation.get('id'),
                            dashboard_chart_id=penetrate.get('chart_id'),
                            dashboard_id=dashboard_id,
                            parent_chart_field_id=relation.get('parent_chart_field_id'),
                            child_chart_field_id=relation.get('child_chart_field_id'),
                            type=ChartPenetrateRelationType.PenetrateFilterType.value,
                        )
                    )
            penetrate_var_filter_relations = penetrate.get("penetrate_var_filter_relation", [])
            self._op_penetrate_var_filter_relations(
                penetrate_var_filter_relations, editor_models, penetrate, dashboard_id
            )

        return editor_models, errors

    @staticmethod
    def _op_penetrate_var_filter_relations(penetrate_var_filter_relations, editor_models, penetrate, dashboard_id):
        """
        处理变量穿透关联关系
        :param penetrate_var_filter_relations:
        :param editor_models:
        :param penetrate:
        :param dashboard_id:
        :return:
        """
        if penetrate_var_filter_relations:
            for relation in penetrate_var_filter_relations:
                editor_models.append(
                    DashboardChartPenetrateRelationModel(
                        id=relation.get('id', ''),
                        dashboard_chart_id=penetrate.get('chart_id', ''),
                        dashboard_id=dashboard_id,
                        parent_chart_field_id=relation.get('parent_chart_field_id', ''),
                        child_chart_field_id=relation.get('child_chart_field_id'),
                        parent_chart_var_id=relation.get('parent_chart_var_id', ''),
                        type=ChartPenetrateRelationType.VarPenetrateFilterType.value,
                    )
                )

    def get_action_name(self):
        return '保存穿透关联关系'
