#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""
单图字段排序关系
"""

# ---------------- 标准模块 ----------------
import logging
import json
from dmplib.utils.errors import UserError

# ---------------- 业务模块 ----------------
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartFieldSortModel

logger = logging.getLogger(__name__)


class DashboardChartFieldSort(Editor):
    def edit(self):
        errors = []
        editor_models = []
        dashboard = self.metadata_storage.metadata_dashboard()
        dashboard_charts = self.metadata_storage.metadata_dashboard_charts()
        if len(dashboard_charts) < 1:
            return editor_models, errors
        for chart in dashboard_charts:
            field_sort_list = self.metadata_storage.get_data_by_path("data.indicator.field_sorts", chart)
            if not field_sort_list:
                continue
            for single_field_sort in field_sort_list:
                if not single_field_sort:
                    continue
                aggregation = self.metadata_storage.get_data_by_path("data.aggregation", chart)
                if aggregation != 0:
                    self.verify_content(single_field_sort.get("sort"), single_field_sort.get("content"))
                content = json.dumps(single_field_sort.get("content")) if single_field_sort.get("content") else ''
                args = {
                    "id": single_field_sort.get("id"),
                    "dashboard_id": dashboard.get('id'),
                    "dashboard_chart_id": chart.get("id"),
                    "dataset_field_id": single_field_sort.get("dataset_field_id", ""),
                    "field_source": single_field_sort.get("field_source"),
                    "sort": single_field_sort.get("sort"),
                    "content": content,
                    "weight": single_field_sort.get("weight", 0),
                }
                chart_model = DashboardChartFieldSortModel(**args)
                editor_models.append(chart_model)
        return editor_models, errors

    def verify_content(self, sort, content):
        if sort != 'FIELD':
            return
        content = content or {}
        if not content:
            return
        field_type = content.get('field_type')
        formula_mode = content.get('formula_mode')
        if field_type == 'nums' and formula_mode not in (
                'sum', 'count', 'avg', 'max', 'min', 'distinct'
        ):
            raise UserError(message='自定义排序字段度量的集合方式只能是求和/计数/平均值/最大值/最小值/去重计数')

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "单图字段排序元数据保存"
