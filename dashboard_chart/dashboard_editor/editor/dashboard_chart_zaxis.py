#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by yangzy02 on 2018/11/23
from dashboard_chart.dashboard_editor.editor.dashboard_chart_num import DashboardChartNum


class DashboardChartZaxis(DashboardChartNum):
    def __init__(self, metadata_storage):
        """
        初始化
        :param metadata_storage: dashboard_chart.dashboard_editor.metadata_storage.MetadataStorage
        """
        super().__init__(metadata_storage)
        self.axis_type = 1
        self.node_name = 'zaxis'

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "单图次轴元数据保存"
