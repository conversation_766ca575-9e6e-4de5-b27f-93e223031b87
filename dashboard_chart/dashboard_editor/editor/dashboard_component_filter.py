#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardComponentFilterModel
from dashboard_chart.dashboard_editor.editor.models import DashboardComponentFilterFieldModel


class DashboardComponentFilter(Editor):
    def edit(self):
        errors = []
        editor_models = []
        filters = self.metadata_storage.get_data_by_path('first_report.chart_relations.filters')
        if not filters or not isinstance(filters, list):
            return editor_models, errors
        for single_filter in filters:
            related_list = single_filter.get('related_list')
            for relate in related_list:
                relations = relate.get('relations')
                model = DashboardComponentFilterModel(
                    id=relate.get('id'),
                    chart_initiator_id=single_filter.get('chart_initiator_id'),
                    chart_responder_id=relate.get('chart_responder_id'),
                    dataset_id=single_filter.get('dataset_id'),
                    is_same_dataset=0,
                )
                editor_models.append(model)
                for relation in relations:
                    if relation.get('is_same_dataset'):
                        model.is_same_dataset = 1
                        continue
                    editor_models.append(
                        DashboardComponentFilterFieldModel(
                            id=relation.get('id'),
                            filter_id=single_filter.get('id'),
                            chart_id=single_filter.get('chart_initiator_id'),
                            field_initiator_id=relation.get('field_initiator_id'),
                            field_responder_id=relation.get('field_responder_id'),
                        )
                    )
        return editor_models, errors

    def get_action_name(self):
        return '保存组件筛选器'
