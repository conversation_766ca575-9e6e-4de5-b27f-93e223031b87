#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by yangzy02 on 2018/11/23
import json
from collections import defaultdict

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartModel, ChartDefaultValueModel

from dashboard_chart.dashboard_editor.metadata_storage import MetadataStorage
from typing import Any, Dict, List, Tuple, Union


class DashboardChart(Editor):
    def __init__(self, metadata_storage: MetadataStorage) -> None:
        """
        初始化
        :param metadata_storage: dashboard_chart.dashboard_editor.metadata_storage.MetadataStorage
        """
        super().__init__(metadata_storage)
        self.has_penetrate = 1
        self.default_desired_value = 0

    def edit(self) -> Union[Tuple[List[Any], List[Any]], Tuple[List[DashboardChartModel], List[Any]]]:
        errors = []
        editor_models = []
        charts = self.metadata_storage.metadata_dashboard_charts()
        if len(charts) < 1:
            return editor_models, errors
        dashboard = self.metadata_storage.metadata_dashboard()
        dashboard_id = dashboard.get('id')
        penetrate_info = self.get_penetrate(charts)
        rank_map = {
            chart['id']: {value: index for index, value in enumerate(chart.get('children_chart_ids', []))}
            for chart in charts
        }
        for chart in charts:
            chart_data = chart.get("data", {})
            parent_chart_id = chart.get("parent_chart_id", '')
            fixed_manual_value = chart.get("fixed_manual_value", [])
            # 去重
            fixed_manual_value = list(set(fixed_manual_value)) if isinstance(fixed_manual_value, list) else []
            chart_id = chart.get('id')
            position = (
                chart.get("position") if isinstance(chart.get("position"), str) else json.dumps(chart.get("position"))
            )
            penetrate = penetrate_info[chart.get('id')]
            desired_value = self.get_desired_value(chart)
            layout_extend = chart.get("layout_extend")
            args = {
                "id": chart.get("id"),
                "dashboard_id": dashboard_id,
                "name": chart.get("name"),
                "chart_code": chart.get("chart_component_code"),
                "filter_config": chart.get("filter_config"),
                "page_size": chart.get("page_size", 0),
                "data_logic_type_code": self.metadata_storage.get_data_by_path("data.data_type.logic_type", chart),
                "chart_type": chart.get("chart_type"),
                "export_type": json.dumps(chart.get("export_type", [])),
                "source": self.metadata_storage.get_data_by_path("data.datasource.id", chart),
                "position": position,
                "penetrate": penetrate.get("has_penetrate") or 0,
                "parent_id": penetrate.get("parent_id", '') or '',
                "level_code": penetrate.get("level_code", ''),
                "refresh_rate": self.metadata_storage.get_data_by_path("funcSetup.refresh_rate", chart),
                "display_item": self.metadata_storage.get_data_by_path("funcSetup.display_item", chart),
                "desired_value": desired_value,
                "layout": chart.get("layout"),
                "config": chart.get("config"),
                "default_value": self.metadata_storage.get_data_by_path("data.default_value", chart),
                #
                "data_modified_on": chart.get("data_modified_on"),
                "sort_method": chart.get("sort_method"),
                "percentage": chart.get("percentage"),
                "column_order": chart.get("column_order"),
                ###
                "layout_extend": layout_extend if not isinstance(layout_extend, dict) else json.dumps(layout_extend,
                                                                                                      ensure_ascii=False),
                "content": chart.get("content"),
                "style_type": chart.get("style_type"),
                "layers": chart.get("layers"),
                "filter_relation": self.metadata_storage.get_data_by_path("data.filter_relation", chart),
                "enable_subtotal": chart_data.get("enable_subtotal", 0),
                "enable_summary": chart_data.get("enable_summary", 0),
                "enable_subtotal_col": chart_data.get("enable_subtotal_col", 0),
                "enable_subtotal_col_summary": chart_data.get("enable_subtotal_col_summary", 0),
                "enable_subtotal_row": chart_data.get("enable_subtotal_row", 0),
                "enable_subtotal_row_summary": chart_data.get("enable_subtotal_row_summary", 0),
                "subtotal_row_summary_formula_mode": chart_data.get("subtotal_row_summary_formula_mode", ''),
                "aggregation": chart_data.get("aggregation", 1),
                "pre_comparison": chart_data.get("pre_comparison", 1),
                "reset_field_sort": chart_data.get("reset_field_sort", 1),
                "external_subject_id": chart_data.get("external_subject_id"),
                "parent_chart_id": parent_chart_id,
                "child_rank": (
                    rank_map[parent_chart_id][chart_id]
                    if (parent_chart_id in rank_map) and (chart_id in rank_map[parent_chart_id])
                    else 0
                ),
                "fixed_data_mode": chart.get("fixed_data_mode"),
                "fixed_manual_value": json.dumps(fixed_manual_value),
                "is_highdata": chart.get("is_highdata"),
                "chart_visible": chart.get("chart_visible", 1),
                "asset_id": chart.get("asset_id") or 0,
                "close_detail_mode": chart.get("close_detail_mode")
            }

            chart_default_values = self.metadata_storage.get_data_by_path("data.chart_default_value", chart)
            if chart_default_values:
                for chart_default_v in chart_default_values:
                    default_value_args = {
                        "id": chart_default_v.get("id"),
                        "dashboard_id": dashboard_id,
                        "dataset_id": self.metadata_storage.get_data_by_path("data.datasource.id", chart),
                        "chart_id": chart.get("id"),
                        "dataset_field_id": chart_default_v.get("dataset_field_id"),
                        "operator": chart_default_v.get("operator"),
                        "value": chart_default_v.get("value"),
                        "select_all": chart_default_v.get("select_all"),
                        "extend_data": chart_default_v.get("extend_data"),
                    }
                    default_value_model = ChartDefaultValueModel(**default_value_args)
                    editor_models.append(default_value_model)

            chart_model = DashboardChartModel(**args)
            editor_models.append(chart_model)

        return editor_models, errors

    def get_penetrate(self, charts: List) -> defaultdict:
        """获取穿透数据"""
        penetrates = self.metadata_storage.get_data_by_path("first_report.chart_relations.penetrates")
        penetrate_info = defaultdict(dict)
        for penetrate in penetrates:
            # 找出穿透根
            chart_id = penetrate.get('chart_id')
            parent_id = penetrate.get('parent_id')
            penetrate_info[chart_id]['parent_id'] = parent_id
            if parent_id:
                penetrate_info[parent_id]['has_penetrate'] = 1
        for chart in charts:
            penetrate_info[chart.get('id')]['level_code'] = chart.get('level_code', '')

        def get_level_code(chart_id):
            my_parent_id = penetrate_info[chart_id]['parent_id']
            if penetrate_info[chart_id]['level_code']:
                return penetrate_info[chart_id]['level_code']
            if my_parent_id:
                parent_level_code = get_level_code(my_parent_id)
                penetrate_info[chart_id]['level_code'] = parent_level_code + '000001-'
            elif penetrate_info[chart_id].get('has_penetrate'):
                penetrate_info[chart_id]['level_code'] = self.generate_level_code('')
            return penetrate_info[chart_id]['level_code']

        for penetrate in penetrates:
            chart_id = penetrate.get('chart_id')
            penetrate_info[chart_id]['level_code'] = get_level_code(chart_id)
        return penetrate_info

    def get_desired_value(self, chart: Dict) -> int:
        """
        从元数据获取单图固定目标值数据
        :param dict chart: 单图数据
        :return:
        """
        chart_data = chart.get("data")
        indicator = chart_data.get("indicator")
        if not indicator:
            return self.default_desired_value
        desires = indicator.get("desires")
        if not desires:
            return self.default_desired_value
        for desire in desires:
            if desire.get("mode"):
                return desire.get("value")
        return self.default_desired_value

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "单图元数据保存"
