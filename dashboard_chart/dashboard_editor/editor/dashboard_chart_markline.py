#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/12/3 11:00
# <AUTHOR> caoxl
# @File     : dashboard_chart_markline.py
import json
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartMarklineModel


class DashboardChartMarkline(Editor):
    """
    单图辅助线
    """

    def get_action_name(self):
        return '图表辅助线'

    def edit(self):
        marklines = self.metadata_storage.metadata_marklines()
        errors = []
        editor_models = []
        dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
        if len(marklines) < 1:
            return errors, editor_models
        for chart_id, markline_list in marklines.items():
            for markline in markline_list:
                editor_models.append(
                    DashboardChartMarklineModel(
                        **{
                            'id': markline.get('id'),
                            'dashboard_id': dashboard_id,
                            'dashboard_chart_id': markline.get('dashboard_chart_id'),
                            'name': markline.get('name'),
                            'mode': markline.get('mode'),
                            'num': markline.get('num'),
                            'formula_mode': markline.get('formula_mode'),
                            'value': markline.get('value'),
                            'rank': markline.get('rank'),
                            'axis_type': markline.get('axis_type'),
                            'color': markline.get('color'),
                            'config': json.dumps(markline.get('config')) if markline.get('config') else None,
                            'percentile': markline.get('percentile'),
                        }
                    )
                )
        return editor_models, errors
