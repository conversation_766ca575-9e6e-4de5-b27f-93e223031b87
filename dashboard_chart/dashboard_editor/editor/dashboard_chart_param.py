#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartParamsModel


class DashboardChartParam(Editor):
    def edit(self):
        errors = []
        editor_models = []
        charts = self.metadata_storage.metadata_dashboard_charts()
        if charts and isinstance(charts, list):
            dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
            for chart in charts:
                chart_params = self.metadata_storage.get_data_by_path('data.indicator.chart_params', chart)
                if chart_params and isinstance(chart_params, list):
                    for chart_param in chart_params:
                        model = DashboardChartParamsModel(
                            dashboard_id=dashboard_id,
                            dashboard_chart_id=chart.get('id'),
                            dataset_field_id=chart_param.get('dataset_field_id'),
                            alias=chart_param.get('alias'),
                            rank=chart_param.get('rank', 0),
                            param_id=chart_param.get('id'),
                        )
                        editor_models.append(model)
        return editor_models, errors

    def get_action_name(self):
        return '保存单图参数'
