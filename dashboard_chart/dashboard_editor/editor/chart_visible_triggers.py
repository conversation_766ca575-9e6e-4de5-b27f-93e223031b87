#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardChartTriggersModel
)


class ChartVisibleTriggers(Editor):
    def edit(self):
        errors = []
        editor_models = []
        triggers = self.metadata_storage.get_data_by_path('first_report.chart_relations.chart_visible_triggers')
        if triggers and isinstance(triggers, list):
            dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
            self._deal_with_chart_triggers(triggers, editor_models, dashboard_id)
        return editor_models, errors

    def _format_json_str(self, value):
        if isinstance(value, (list, dict)):
            return json.dumps(value, ensure_ascii=False)
        if not value:
            return ''
        return str(value)

    def _deal_with_chart_triggers(self, triggers, editor_models, dashboard_id):
        """
        处理组件事件数据
        :param chart_redirects:
        :param chart_id:
        :param editor_models:
        :param dashboard_id:
        :return:
        """
        for trigger in triggers:
            editor_models.append(
                DashboardChartTriggersModel(
                    id=trigger.get('id'),
                    dashboard_chart_id=trigger.get('chart_id'),
                    dashboard_id=dashboard_id,
                    type=trigger.get('type', 0),
                    actions=self._format_json_str(trigger.get('actions')),
                    conditions=self._format_json_str(trigger.get('conditions')),
                )
            )

    def get_action_name(self):
        return '保存组件事件'
