#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""
单图筛选
"""

# ---------------- 标准模块 ----------------
import json
import logging


# ---------------- 业务模块 ----------------
from base.enums import ChartFilterInitiatorSouce
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    ChartFitlerModel,
    ChartFilterRelation,
    ChartFitlerFixedValueModel,
)

from typing import Any, List, Tuple

logger = logging.getLogger(__name__)


class ChartFilterEditor(Editor):
    """
    单图筛选
    """

    def __repr__(self):
        return '单图筛选'

    def get_action_name(self):
        return "单图间筛选"

    def edit(self) -> <PERSON><PERSON>[List[Any], List[Any]]:
        errors = []
        editor_models = []
        chart_filters = self.metadata_storage.get_data_by_path("first_report.chart_relations.chart_filters")
        dashboard_id = self.metadata_storage.get_data_by_path("first_report.id")

        if (not chart_filters) or (not isinstance(chart_filters, list)):
            return editor_models, errors

        for chart_filter in chart_filters:
            if chart_filter["related_list"].__len__() > 0:
                chart_filter_mode_id = chart_filter.get("id")
                # 筛选组件支持自定义值字段
                indicator_dim_obj = chart_filter.get("indicator_dim_obj", '')
                if indicator_dim_obj:
                    indicator_dim_obj = json.dumps(indicator_dim_obj, ensure_ascii=False)
                chart_filter_mode = ChartFitlerModel(
                    id=chart_filter_mode_id,
                    dashboard_id=dashboard_id,
                    dataset_id=chart_filter.get("dataset_id"),
                    dataset_field_id=chart_filter.get("field_initiator_id"),
                    chart_id=chart_filter.get("chart_initiator_id"),
                    filter_type=chart_filter.get("filter_type", 0),
                    available=chart_filter.get("available", 1),
                    initiator_source=chart_filter.get("initiator_source", "dataset_field"),
                    indicator_dim_obj=indicator_dim_obj,
                )
                # 固定值
                if (
                    chart_filter_mode.initiator_source == ChartFilterInitiatorSouce.Fixedvalue.value
                    and chart_filter.get("fixed_value_data")
                ):
                    fixed_value_data = chart_filter.get("fixed_value_data")
                    fixed_value_model = ChartFitlerFixedValueModel(
                        id=fixed_value_data.get("id"),
                        dashboard_id=dashboard_id,
                        chart_id=chart_filter.get("chart_initiator_id"),
                        name=fixed_value_data.get("name"),
                        value_type=fixed_value_data.get("value_type"),
                        identifier=fixed_value_data.get("identifier"),
                        extra_data=fixed_value_data.get("extra_data"),
                    )
                    editor_models.append(fixed_value_model)
                editor_models.append(chart_filter_mode)

                for related in chart_filter["related_list"]:
                    chart_filter_related = ChartFilterRelation(
                        id=related.get("id"),
                        dashboard_id=dashboard_id,
                        filter_id=chart_filter_mode_id,
                        chart_responder_id=related.get("chart_responder_id"),
                        dataset_responder_id=related.get("related_dataset_id"),
                        field_responder_id=related.get("field_responder_id"),
                    )
                    editor_models.append(chart_filter_related)

        return editor_models, errors
