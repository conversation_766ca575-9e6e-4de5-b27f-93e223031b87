#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>
# pylint: disable=W0221


"""
单图编辑错误校验类
"""

# ---------------- 标准模块 ----------------
from copy import deepcopy
from abc import abstractmethod

# ---------------- 业务模块 ----------------
from dataset import external_query_service
from dashboard_chart.dashboard_editor.metadata_storage import MetadataStorage
from base.enums import DashboardJumpType
from base import repository
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardJumpConfigModel,
    DashboardJumpRelationModel,
    DashboardChartModel,
    DashboardChartDimModel,
    DashboardChartNumModel,
    DashboardChartComparisonModel,
    DashboardChartDesireModel,
    DashboardChartMarklineModel,
    DashboardChartFilterModel,
    DashboardFilterModel,
    DashboardChartParamsModel,
    DashboardChartParamsJumpModel,
    DashboardVarJumpRelationModel,
    DashboardFixedVarJumpRelationModel,
)

error_info_dict = {
    "5000": {"err_code": 5000, "msg": "未知异常"},
    "5001": {"err_code": 5001, "msg": "未找到【{}】引用的数据集,请重新选择数据集"},
    "5002": {"err_code": 5002, "msg": "【{}】引用的“{}”字段未找到,请重新选择数据集"},
    "50020": {"err_code": 5002, "msg": "【{}】引用数芯指标数据集的“{}”字段已下线"},
    "5003": {"err_code": 5003, "msg": "报告级筛选引用的“{}”字段未找到,请重新选择字段"},
    "5004": {"err_code": 5004, "msg": "【{}】跳转的目标报告未找到,请重新配置【{}】的跳转设置"},
    "5005": {"err_code": 5005, "msg": "【{}】跳转的目标报告关联字段未找到,请重新配置【{}】的跳转设置"},
    "5006": {"err_code": 5006, "msg": "“{}”字段已经被设置了报告跳转关联,无法删除"},
    "5007": {"err_code": 5007, "msg": "抱歉，解析上一版本元数据失败，请重试!"},
    "5008": {"err_code": 5008, "msg": "报告级筛选数据有误，报告ID非当前报告ID！"},
    "5009": {"err_code": 5009, "msg": "报告级别筛选关联关系配置有误，主数据集字段和关联关系主数据集字段不一致!"},
    "5010": {"err_code": 5010, "msg": "缺少变量关系配置唯一ID，请核对！"},
    "5011": {"err_code": 5011, "msg": "缺少数据集字段变量ID！"},
    "5012": {"err_code": 5012, "msg": "缺少变量所属数据集ID！"},
    "5013": {"err_code": 5013, "msg": "数据操作异常，{}"},
    "5014": {"err_code": 5014, "msg": "{}"},
    "5015": {"err_code": 5015, "msg": "目标报告引用的筛选器未找到，请重新设置"},
    "5016": {"err_code": 5016, "msg": "目标报告引用的筛选组件字段未找到，请重新设置"},
    "5017": {"err_code": 5017, "msg": "目标报告引用的变量字段未找到，请重新设置"},
}


class EditorChecker:
    """
    错误校验基类
    """

    def __init__(self):
        self.errors = []

    @abstractmethod
    def check(self, *args, **kwargs):
        """
        （子类重写）
        :return:
        """
        return self.errors

    def append_single_error_msg(self, err_code, *msgargs):
        """
        追加错误信息
        :param err_code:
        :param msgargs:
        :return:
        """
        error_info = error_info_dict.get(str(err_code))
        if error_info:
            result = deepcopy(error_info)
            result["msg"] = result["msg"].format(*msgargs)
            self.errors.append(result)

    @staticmethod
    def _get_field_info(dataset_field_id):
        """
        获取字段信息
        :param dataset_field_id:
        :return:
        """
        if not dataset_field_id:
            return {}
        dataset_field_query_data = external_query_service.get_multi_dataset_fields([dataset_field_id])
        return dataset_field_query_data[0] if dataset_field_query_data else {}


class NotFoundErrorEditorChecker(EditorChecker):
    """
    not found error
    """

    @staticmethod
    def _get_field_name_for_chart_filter(metadata_storage, operate_data, dataset_field_id):
        """

        :param metadata_storage:
        :param operate_data:
        :param dataset_field_id:
        :return:
        """
        chart_filters_dict = metadata_storage.metadata_chart_section_data("filters")
        chart_filter_infos = chart_filters_dict.get(operate_data.get("dashboard_chart_id"), [])
        chart_filter_info = {}
        for i in chart_filter_infos:
            chart_filter_info = i if i.get("dataset_field_id") == dataset_field_id else {}
        field_name = (
            chart_filter_info.get("alias_name")
            or chart_filter_info.get("alias")
            or chart_filter_info.get("col_name")
            or ""
        )
        return field_name

    def _check_redirect_target_for_not_found(self, edit_models: list, not_found_ids: list, chart_info_dict: dict):
        """
        校验跳转目标
        :param edit_models:
        :param not_found_ids:
        :param chart_info_dict:
        :return:
        """
        dashboard_jump_config_models = [i for i in edit_models if isinstance(i, DashboardJumpConfigModel)]
        for operate_model in dashboard_jump_config_models:

            chart_info = chart_info_dict.get(operate_model.dashboard_chart_id, {})
            if operate_model.target_type == DashboardJumpType.Dashboard.value and operate_model.target in not_found_ids:
                self.append_single_error_msg(5004, chart_info.get("name", ""), chart_info.get("name", ""))

    def _check_dashboard_filter_id_for_not_found(self, edit_models: list, not_found_ids: list, chart_info_dict: dict):
        """
        校验跳转目标报告级筛选id
        :param edit_models:
        :param not_found_ids:
        :param chart_info_dict:
        :return:
        """
        dashboard_jump_relation_models = [
            i for i in edit_models if isinstance(i, (DashboardJumpRelationModel,
                                                     DashboardChartParamsJumpModel,
                                                     DashboardVarJumpRelationModel,
                                                     DashboardFixedVarJumpRelationModel))
        ]
        for operate_model in dashboard_jump_relation_models:
            operate_data = operate_model.get_dict()
            dashboard_chart_id = operate_data.get("dashboard_chart_id")
            dashboard_filter_id = operate_data.get("dashboard_filter_id")
            chart_info = chart_info_dict.get(dashboard_chart_id, {})
            if dashboard_filter_id and dashboard_filter_id in not_found_ids:
                self.append_single_error_msg(5005, chart_info.get("name", ""), chart_info.get("name", ""))

    def _check_chart_indicators_for_not_found(
        self, edit_models: list, not_found_ids: list, chart_info_dict: dict, metadata_storage
    ):
        """
        校验单图incicators数据
        :param edit_models:
        :param not_found_ids:
        :param chart_info_dict:
        :return:
        """
        section_models = (
            DashboardChartDimModel,
            DashboardChartNumModel,
            DashboardChartComparisonModel,
            DashboardChartDesireModel,
            DashboardChartMarklineModel,
            DashboardChartFilterModel,
            DashboardChartParamsModel,
        )
        operate_models = [i for i in edit_models if isinstance(i, section_models)]
        for operate_model in operate_models:
            operate_data = operate_model.get_dict()
            dataset_field_id = (
                operate_data.get("dataset_field_id") or operate_data.get("dim") or operate_data.get("num")
            )
            if dataset_field_id and dataset_field_id in not_found_ids:
                chart_info = chart_info_dict.get(operate_data.get("dashboard_chart_id"), {})
                field_name = operate_data.get("alias") or operate_data.get("name") or ""
                # chart_filter另外匹配字段名称
                if not field_name and isinstance(operate_model, DashboardChartFilterModel):
                    field_name = self._get_field_name_for_chart_filter(metadata_storage, operate_data, dataset_field_id)
                error_code = 5002
                if self._check_dataset_is_indicator_model(chart_info.get('id')):
                    error_code = 50020
                self.append_single_error_msg(error_code, chart_info.get("name", ""), field_name)

    def _check_dataset_is_indicator_model(self, chart_id):
        """
        组件绑定的是否是指标数据集
        :param chart_id:
        :return:
        """
        source = repository.get_data_scalar("dashboard_chart", {"id": chart_id}, 'source')
        if repository.get_data_scalar('dataset', {'id': source}, 'external_type') == 'pulsar_indicator':
            return True
        return False

    def _check_dashboard_filters_for_not_found(
        self, edit_models: list, not_found_ids: list, dashboard_filters_dict: dict
    ):
        """
        校验报告级筛选
        :param edit_models:
        :param not_found_ids:
        :param dashboard_filters_dict:
        :return:
        """
        dashboard_filter_models = [i for i in edit_models if isinstance(i, DashboardFilterModel)]
        for operate_model in dashboard_filter_models:
            if operate_model.main_dataset_field_id and operate_model.main_dataset_field_id in not_found_ids:
                field_data = dashboard_filters_dict.get(operate_model.main_dataset_field_id, {})
                self.append_single_error_msg(5003, field_data.get("alias_name") or field_data.get("col_name") or "")

    def _check_chart_source_for_not_found(self, edit_models: list, not_found_ids: list):
        """
        校验单图source
        :param edit_models:
        :param not_found_ids:
        :return:
        """
        dashboard_chart_models = [i for i in edit_models if isinstance(i, DashboardChartModel)]
        for single_model in dashboard_chart_models:
            if single_model.source and single_model.source in not_found_ids:
                self.append_single_error_msg(5001, single_model.name)

    def _check_chart_relations_target_for_not_found(
        self, resource_type, edit_models: list, not_found_ids: list
    ):
        """
        组件的跳转作用于目标报告的数据检查
        :param edit_models:
        :param not_found_ids:
        :return:
        """
        section_models = (
            DashboardJumpRelationModel,
            DashboardChartParamsJumpModel,
            DashboardVarJumpRelationModel,
            DashboardFixedVarJumpRelationModel,
        )
        check_filter_field_map = {
            "chart_ids": "target_filter_id",
            "dataset_field_ids": "target_filter_field_id",
            "dataset_var_ids": "target_filter_field_id",
        }
        check_filter_code_map = {
            "chart_ids": 5015,
            "dataset_field_ids": 5016,
            "dataset_var_ids": 5017,
        }
        operate_models = [i for i in edit_models if isinstance(i, section_models)]
        for operate_model in operate_models:
            operate_data = operate_model.get_dict()
            filter_field = check_filter_field_map.get(resource_type) \
                if check_filter_field_map.get(resource_type) else ''
            if not filter_field:
                continue
            biz_id = operate_data.get(filter_field)
            if biz_id and biz_id in not_found_ids:
                err_code = check_filter_code_map.get(resource_type)
                self.append_single_error_msg(err_code, "")

    def check(self, not_found_errors: dict, metadata: dict, edit_models):
        """
        not_found_errors
        :param not_found_errors:
        :param metadata:
        :param edit_models:
        :return:
        """
        if not not_found_errors or not edit_models:
            return self.errors
        metadata_storage = MetadataStorage(metadata=metadata)
        chart_info_dict = metadata_storage.get_chart_info_dict()
        dashboard_filters_dict = {
            item.get("main_dataset_field_id", ""): item for item in metadata_storage.metadata_dashboard_filters()
        }
        for resource_type, not_found_ids in not_found_errors.items():
            if not resource_type or not not_found_ids:
                continue
            # 2022-10-28 https://www.tapd.cn/38229611/prong/stories/view/1138229611001319170?action_entry_type=stories
            # 取消元数据编的跳转报告的存在性校验
            # # 校验报告id
            # if resource_type == "dashboard_ids":
            #     self._check_redirect_target_for_not_found(edit_models, not_found_ids, chart_info_dict)
            # 校验报告级筛选id
            elif resource_type == "dashboard_filter_ids":
                self._check_dashboard_filter_id_for_not_found(edit_models, not_found_ids, chart_info_dict)
            # 数据集字段id
            elif resource_type == "dataset_field_ids":
                self._check_chart_indicators_for_not_found(
                    edit_models, not_found_ids, chart_info_dict, metadata_storage
                )
                self._check_dashboard_filters_for_not_found(edit_models, not_found_ids, dashboard_filters_dict)
                # 目标报告页的资源检查。跳转的目标报告页筛选器的字段是否存在的检查
                # 同样是数据集字段合法性检查，当前报告与目标报告的校验输出结果只是提示信息不一样，校验逻辑是一样的
#                 self._check_chart_relations_target_for_not_found(
#                     resource_type, edit_models, not_found_ids
#                 )
            # 数据集id
            elif resource_type == "dataset_ids":
                self._check_chart_source_for_not_found(edit_models, not_found_ids)
            #elif resource_type in ["chart_ids", "dataset_var_ids"]:
            # 2022-5-21 刘成帅，取消目标报告的检查设置
            # 目标报告页的变量，筛选组件资源检查
            #     self._check_chart_relations_target_for_not_found(
            #      resource_type, edit_models, not_found_ids
            #                 )
        return self.errors


class NotDelErrorEditorChecker(EditorChecker):
    """
    not del error
    """

    def _check_dashboard_filter_for_not_del(self, del_models: list, not_del_ids: list):
        """
        校验dashboard_filters
        :param del_models:
        :param not_del_ids:
        :return:
        """
        dashboard_filter_model = [i for i in del_models if isinstance(i, DashboardFilterModel)]
        for single_model in dashboard_filter_model:
            if single_model.id and single_model.id in not_del_ids:
                field_data = self._get_field_info(single_model.main_dataset_field_id)
                self.append_single_error_msg(5006, field_data.get("alias_name") or "")

    def check(self, not_del_errors: dict, del_models):
        """
        not_del_errors
        :param not_del_errors:
        :param del_models:
        :return:
        """
        if not not_del_errors or not del_models:
            return self.errors
        for resource_type, not_del_ids in not_del_errors.items():
            if not resource_type or not not_del_ids:
                continue
            # 被删除的报告级筛选id被其他报告关联
            if resource_type == "dashboard_filter_ids":
                self._check_dashboard_filter_for_not_del(del_models, not_del_ids)
        return self.errors


class DbErrorEditorChecker(EditorChecker):
    """
    db error
    """

    def restructure_db_errors(self, db_errors):
        """
        构造db_errors
        :param db_errors:
        :return:
        """
        if not db_errors:
            return self.errors
        for i in db_errors:
            if i:
                self.append_single_error_msg(5013, i)
        return self.errors

    def check(self, db_errors):
        return self.restructure_db_errors(db_errors)


class EditorCommonErrorChecker(EditorChecker):
    """
    db error
    """

    def restructure_errors(self, errors):
        """
        构造db_errors
        :param db_errors:
        :return:
        """
        if not errors:
            return self.errors
        for i in errors:
            if i:
                self.append_single_error_msg(5014, i)
        return self.errors

    def check(self, errors):
        return self.restructure_errors(errors)

