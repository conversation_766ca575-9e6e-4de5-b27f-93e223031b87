#!/usr/bin/env python3
# -*- coding: utf-8 -*-


from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    GlobalParams2DatasetFieldRelation,
    GlobalParams2DatasetVarsRelation,
    GlobalParams2FilterChartRelation,
    DashboardGlobalParams as DashboardGlobalParamsModel,
)


class DashboardGlobalParams(Editor):
    """
    全局参数
    """

    def edit(self):
        errors = []
        editor_models = []
        global_params = self.metadata_storage.metadata_global_params()
        if len(global_params) < 1:
            return editor_models, errors

        for global_param in global_params:
            dataset_field_relations = global_param.get('dataset_field_relations') or []
            dataset_vars_relations = global_param.get('dataset_vars_relations') or []
            filter_chart_relations = global_param.get('filter_chart_relations') or []

            self._op_global_params(editor_models, global_param)
            self._op_dataset_field_relations(editor_models, global_param, dataset_field_relations)
            self._op_dataset_vars_relations(editor_models, global_param, dataset_vars_relations)
            self._op_dataset_filter_chart_relations(editor_models, global_param, filter_chart_relations)

        return editor_models, errors

    def _op_global_params(self, editor_models: list, global_param):
        editor_models.append(
            DashboardGlobalParamsModel(
                id=global_param.get('id', ''),
                dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                name=global_param.get('name', ''),
                alias_name=global_param.get('alias_name', ''),
            )
        )

    def _op_dataset_field_relations(self, editor_models: list, global_param, dataset_field_relations):
        for dataset_field_relation in dataset_field_relations:
            editor_models.append(
                GlobalParams2DatasetFieldRelation(
                    global_params_id=global_param.get('id', ''),
                    dashboard_id=dataset_field_relation.get('dashboard_id', ''),
                    chart_id=dataset_field_relation.get('chart_id', ''),
                    dataset_id=dataset_field_relation.get('dataset_id', ''),
                    dataset_field_id=dataset_field_relation.get('dataset_field_id', ''),
                )
            )

    def _op_dataset_vars_relations(self, editor_models: list, global_param, vars_relations):
        for vars_relation in vars_relations:
            editor_models.append(
                GlobalParams2DatasetVarsRelation(
                    global_params_id=global_param.get('id', ''),
                    dashboard_id=vars_relation.get('dashboard_id', ''),
                    dataset_id=vars_relation.get('dataset_id', ''),
                    var_id=vars_relation.get('var_id', ''),
                )
            )

    def _op_dataset_filter_chart_relations(self, editor_models: list, global_param, filter_chart_relations):
        for filter_chart_relation in filter_chart_relations:
            editor_models.append(
                GlobalParams2FilterChartRelation(
                    global_params_id=global_param.get('id', ''),
                    dashboard_id=filter_chart_relation.get('dashboard_id', ''),
                    filter_chart_id=filter_chart_relation.get('filter_chart_id', ''),
                    dataset_field_id=filter_chart_relation.get('dataset_field_id', ''),
                    date_filter_chart_flag=filter_chart_relation.get('date_filter_chart_flag', ''),
                )
            )

    def get_action_name(self):
        return '报表全局参数'
