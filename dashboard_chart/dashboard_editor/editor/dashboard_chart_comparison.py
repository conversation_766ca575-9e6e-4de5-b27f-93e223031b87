#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by yangzy02 on 2018/11/23
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartComparisonModel


from typing import Any, List, Tuple


class DashboardChartComparison(Editor):
    def edit(self) -> Tuple[List[Any], List[Any]]:
        errors = []
        editor_models = []
        dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
        comparisons = self.metadata_storage.metadata_comparisons()
        if len(comparisons) < 1:
            return editor_models, errors
        for chart_id, comparison_list in comparisons.items():
            for comparison in comparison_list:
                if comparison.get("dashboard_chart_id") != chart_id:
                    continue
                args = {
                    "id": comparison.get("id"),
                    "dashboard_id": dashboard_id,
                    "dashboard_chart_id": chart_id,
                    "dataset_field_id": comparison.get("dataset_field_id"),
                    "alias": comparison.get("alias"),
                    "content": comparison.get("content"),
                    "formula_mode": comparison.get("formula_mode"),
                    "rank": comparison.get("rank"),
                    "sort": comparison.get("sort"),
                }
                comparison_model = DashboardChartComparisonModel(**args)
                editor_models.append(comparison_model)
        return editor_models, errors

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "单图对比元数据保存"
