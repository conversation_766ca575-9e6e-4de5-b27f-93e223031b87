#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by yangzy02 on 2018/11/23
# pylint: disable=E0401

"""
注：不需要在此编辑方法内处理的报告相关配置的字段如下：
    1,报告名称（元数据路径: meta.dashboard.name）
    2，报告发布状态相关的字段（元数据路径: meta.dashboard.publish.*）
    3,报告的父级信息（元数据路径: meta.dashboard.parent_id，meta.dashboard.level_code）
"""

import ujson

from base.enums import DashboardType, DashboardLayoutType
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardModel
from base import repository


from typing import Any, Dict, List, Tuple, Union


class Dashboard(Editor):

    LAYOUT_TYPE_FIXED = "fixed"
    LAYOUT_TYPE_FREE = "free"

    def edit(self) -> <PERSON><PERSON>[List[DashboardModel], List[Any]]:
        errors = []
        editor_models = {}
        dashboard = self.metadata_storage.metadata_dashboard()
        screens = self.metadata_storage.metadata_screens()
        biz_code = dashboard.get("biz_code")
        if not biz_code:
            biz_code = self.generate_code()
            dashboard["biz_code"] = biz_code
        level_code = dashboard.get("level_code")
        if not level_code:
            level_code = self.generate_level_code(dashboard.get("parent_id", ""))
            dashboard["level_code"] = level_code
        main_external_subject_id = dashboard.get("main_external_subject_id", None)
        external_subject_ids = dashboard.get("external_subject_ids", []) or []
        if main_external_subject_id and not external_subject_ids:
            external_subject_ids = [main_external_subject_id]
        exist_dashboard = repository.get_data('dashboard', {'id': dashboard.get("id")})
        args = {
            "id": dashboard.get("id"),
            "theme": self.metadata_storage.get_data_by_path("styles.theme", dashboard),
            "type": exist_dashboard.get('type') if exist_dashboard else DashboardType.File.value,
            "platform": self.metadata_storage.get_data_by_path("layout.platform", dashboard),
            "is_multiple_screen": 1 if len(screens) > 1 else 0,
            "cover": dashboard.get("cover"),
            "description": dashboard.get("description"),
            "layout_type": self._get_layout_type(dashboard),
            "layout": self._get_value(dashboard, "layout"),
            "scale_mode": dashboard.get("scale_mode"),
            "background": self._get_value(dashboard.get("styles"), "background"),
            "rank": dashboard.get("rank"),
            "biz_code": biz_code,
            "refresh_rate": dashboard.get("refresh_rate"),
            "grid_padding": self._get_value(dashboard.get("styles"), "grid_padding") or "{}",
            "create_type": 1,
            "is_show_mark_img": dashboard.get("is_show_mark_img", 1),
            "new_layout_type": dashboard.get("new_layout_type"),
            "main_external_subject_id": main_external_subject_id,
            "application_type": dashboard.get("application_type", 0),
            "external_subject_ids": ','.join(external_subject_ids),
            "line_height": dashboard.get("line_height", 40),
            "smart_beauty_status": dashboard.get("smart_beauty_status", 0),
            "dataset_id": dashboard.get("dataset_id", ""),
            "analysis_type": dashboard.get("analysis_type"),
            "external_url": dashboard.get("external_url")
        }
        model = DashboardModel(**args)
        editor_models.update({dashboard.get("id"): model})

        return list(editor_models.values()), errors

    def _get_layout_type(
        self,
        dashboard: Dict[
            str,
            Union[
                str,
                Dict[str, Union[int, str]],
                int,
                Dict[str, Union[str, Dict[str, Union[bool, str]], None]],
                Dict[str, Union[Dict[str, Union[bool, str]], str, Dict[str, Union[List[int], str]]]],
            ],
        ],
    ) -> str:
        """
        获取穿透数据
        :param dict dashboard: 报告数据
        :return:
        """
        mode = self.metadata_storage.get_data_by_path("layout.mode", dashboard)
        if mode == self.LAYOUT_TYPE_FREE:
            return DashboardLayoutType.Auto.value
        return DashboardLayoutType.Stand.value

    @staticmethod
    def _get_value(item: Dict[str, Any], key: str) -> str:
        """
        获取layout数据
        :param dict item: 数据
        :param str key: 要取的key值
        :return:
        """
        if key not in list(item.keys()):
            return None
        return item.get(key) if isinstance(item.get(key), str) else ujson.dumps(item.get(key))

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "报告元数据保存"
