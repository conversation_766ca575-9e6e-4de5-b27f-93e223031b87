#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by yangzy02 on 2018/11/23
import re
import json
import traceback

from loguru import logger
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartNumModel
from dataset.services.advanced_field_service import (
    validate_expression as ad_validate_expression,
    handle_expression_advance_vars
)
from dataset.models import DatasetFieldModel
from dataset.common import advance_field_helper
from dmplib.utils.errors import UserError


class DashboardChartNum(Editor):
    def __init__(self, metadata_storage):
        """
        初始化
        :param metadata_storage: dashboard_chart.dashboard_editor.metadata_storage.MetadataStorage
        """
        super().__init__(metadata_storage)
        self.axis_type = 0
        self.node_name = 'nums'

    def edit(self):
        errors = []
        editor_models = []
        dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
        dashboard_charts = self.metadata_storage.metadata_dashboard_charts()
        if len(dashboard_charts) < 1:
            return editor_models, errors
        for chart in dashboard_charts:
            num_list = self.metadata_storage.get_data_by_path("data.indicator." + self.node_name, chart)
            for num in num_list:
                subtotal_col_formula_mode = num.get("subtotal_col_formula_mode", "")
                subtotal_col_formula_expression = num.get("subtotal_col_formula_expression")

                expression_col = self.validate_expression(
                    subtotal_col_formula_mode, subtotal_col_formula_expression, num
                )

                args = {
                    "id": num.get("id"),
                    "dashboard_id": dashboard_id,
                    "dashboard_chart_id": chart.get("id"),
                    "num": num.get("num") if num.get("num") else num.get("dataset_field_id"),
                    "alias": num.get("alias"),
                    "formula_mode": num.get("formula_mode"),
                    "display_format": self.get_display_format(num),
                    "axis_type": self.axis_type,
                    "chart_code": num.get("chart_code"),
                    "rank": num.get("rank", 0),
                    "sort": num.get("sort", ''),
                    "sub_type_code": num.get("sub_type_code", ''),
                    "note": num.get("note"),
                    "calc_null": num.get("calc_null", 0),
                    "subtotal_formula_mode": num.get("subtotal_formula_mode", ""),
                    "subtotal_row_formula_mode": num.get("subtotal_row_formula_mode", ""),
                    "subtotal_col_formula_mode": subtotal_col_formula_mode,
                    "same_ring_ratio_config": num.get("same_ring_ratio_config", ""),
                    "hidden": num.get("hidden", 0),
                    "subtotal_col_formula_expression": expression_col if expression_col else '',
                    "relation_fields": num.get("relation_fields")
                }
                chart_model = DashboardChartNumModel(**args)
                editor_models.append(chart_model)
        return editor_models, errors

    def validate_expression(self, subtotal_col_formula_mode, subtotal_col_formula_expression, num):
        dataset_id = num.get("dataset_id")
        if subtotal_col_formula_mode == 'expression':
            replaced_raw_exp = None
            if isinstance(subtotal_col_formula_expression, dict):
                # 报表复制会走到这里， subtotal_col_formula_expression会被预处理成dict
                _raw_exp = subtotal_col_formula_expression.get('raw', '')
            else:
                # 新添加保存这个字段的时候
                _raw_exp = subtotal_col_formula_expression
                replaced_raw_exp = self._deal_divisor_zero(subtotal_col_formula_expression)

            if not re.findall(r'(sum|max|min|avg|count|distinct)', _raw_exp, re.I):
                raise UserError(message='列小计计算表达必须包含聚合函数！')

            # 先替换高级字段中的数据集字段，再处理变量，解决变量中包含[]高级字段中引用字段的占位符导致的bug问题
            replaced_field_exp = advance_field_helper.expression_real_name(
                replaced_raw_exp if replaced_raw_exp else _raw_exp,
                dataset_id
            )
            # 判断是否带有变量, 存在变量则需要替换变量值
            model = DatasetFieldModel(**dict(id=num.get("dataset_field_id"), dataset_id=dataset_id))
            _, expression_advance_var = handle_expression_advance_vars(model, replaced_field_exp)

            # try:
            #     is_valid, msg = ad_validate_expression(expression_advance_var, dataset_id)
            #     if not is_valid:
            #         raise UserError(message=msg)
            # except Exception:
            #     logger.error('列小计计算表达式错误，%s' % traceback.format_exc())
            #     raise UserError(message="列小计计算表达式错误， 请检查列小计的表达式！")

            return json.dumps(
                {'raw': _raw_exp, 'exp': expression_advance_var}, ensure_ascii=False
            )
        return ''

    def _deal_divisor_zero(self, expression):
        # 处理除数是0的情况，将除数的表达式进行替换
        divisor_functions= re.findall(r'/.*?(\w+\(.*?\))', expression)
        for func in divisor_functions:
            expression = expression.replace(func, f'(CASE {func} WHEN 0 THEN null ELSE {func} END)')
        return expression

    @staticmethod
    def get_display_format(num):
        result = num.get("display_format")
        if isinstance(result, dict):
            result = json.dumps(result)
        return result

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "单图度量元数据保存"
