#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/20 17:22
# <AUTHOR> caoxl
# @File     : editor.py
import uuid
from abc import ABCMeta, abstractmethod

from level_sequence.models import DashboardChartLevelSequenceModel
from level_sequence.services import level_sequence_service


class Editor(object, metaclass=ABCMeta):
    def __init__(self, metadata_storage):
        """
        初始化
        :param metadata_storage: dashboard_chart.dashboard_editor.metadata_storage.MetadataStorage
        """
        self.metadata_storage = metadata_storage

    @abstractmethod
    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        pass

    @abstractmethod
    def edit(self):
        """
        执行编辑动作, 子类中必须实现
        :return EditorModels : list
        :return errors: list
        """
        pass

    @staticmethod
    def generate_level_code(parent_id=None):
        """
        根据父级组生成层级编码
        :param parent_id:
        :return:
        """
        return level_sequence_service.generate_level_code(DashboardChartLevelSequenceModel(level_id=parent_id))

    @staticmethod
    def generate_code(type=1):
        """
        生成业务代码
        :param type: 1 为获取业务代码code
        :return:
        """
        if type:
            return uuid.uuid4().__str__().replace('-', '')
        return uuid.uuid4().__str__()
