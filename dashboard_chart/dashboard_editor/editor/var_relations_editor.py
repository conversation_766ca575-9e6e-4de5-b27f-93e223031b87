#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""
单图变量关系
"""

# ---------------- 标准模块 ----------------
import logging
import json


# ---------------- 业务模块 ----------------
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import VarRelationsModel


logger = logging.getLogger(__name__)


class VarRelationsEditor(Editor):
    """
    单图变量关系
    """

    def get_action_name(self):
        return "单图变量关系"

    def edit(self):
        """

        :return:
        """
        errors = list()
        editor_models = list()
        dashboard = self.metadata_storage.metadata_dashboard()
        if not dashboard or len(dashboard) < 1:
            return editor_models, errors
        var_relations = self.metadata_storage.get_data_by_path('first_report.chart_relations.var_relations')
        if var_relations and isinstance(var_relations, list):
            for single_relation in var_relations:
                relation_model = VarRelationsModel()
                relation_model.id = single_relation.get("id")
                relation_model.chart_initiator_id = single_relation.get("chart_initiator_id")
                # 2021/9/26帅帅要求恢复：https://www.tapd.cn/38229611/prong/stories/view/1138229611001091707
                relation_model.field_initiator_id = single_relation.get("field_initiator_id")
                relation_model.dashboard_id = single_relation.get("dashboard_id")
                relation_model.var_id = single_relation.get("var_id")
                relation_model.var_dataset_id = single_relation.get("dataset_id")
                relation_model.initiator_type = single_relation.get("initiator_type", 0)
                relation_model.initiator_type = single_relation.get("initiator_type", 0)
                # 筛选组件支持自定义值字段
                var_dim_obj = single_relation.get("var_dim_obj", '')
                if var_dim_obj:
                    relation_model.var_dim_obj = json.dumps(var_dim_obj, ensure_ascii=False)
                editor_models.append(relation_model)
        return editor_models, errors
