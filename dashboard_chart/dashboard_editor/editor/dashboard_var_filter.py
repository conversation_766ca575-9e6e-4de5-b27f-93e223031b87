#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/20 17:23
# <AUTHOR> caoxl
# @File     : dashboard_filter_editor.py

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardFilterModel,
    DashboardDatasetFieldRelationModel,
    DashboardFilterRelationModel,
    DashboardDatasetVarFieldRelationModel
)


class DashboardVarFilter(Editor):
    """
    报告级筛选
    """

    def get_action_name(self):
        return '报告级变量筛选'

    def edit(self):
        errors = []
        editor_models = []
        dashboard_var_filters = self.metadata_storage.metadata_dashboard_var_filters()
        if len(dashboard_var_filters) < 1:
            return editor_models, errors
        dashboard = self.metadata_storage.metadata_dashboard()
        dashboard_id = dashboard.get('id')
        for dashboard_var_filter in dashboard_var_filters:
            model = DashboardDatasetVarFieldRelationModel(
                **{
                    'id': dashboard_var_filter.get('id'),
                    'dashboard_id': dashboard_id,
                    'var_name': dashboard_var_filter.get('var_name'),
                    'data_type': dashboard_var_filter.get('data_type') or '字符串',
                    'related_dataset_field_id': "",
                    'related_dataset_id': '',
                }
            )
            if dashboard_var_filter.get("filter_relations"):
                for item in dashboard_var_filter.get("filter_relations"):
                    if item.get('related_dataset_field_id'):
                        model.id = item.get("id")
                        model.related_dataset_field_id = item.get('related_dataset_field_id')
                        model.related_dataset_id = item.get('related_dataset_id')
                        editor_models.append(model)
            else:
                editor_models.append(model)
        return editor_models, errors