#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import DashboardChartSelectorModel
from dashboard_chart.dashboard_editor.editor.models import DashboardChartSelectorFieldModel


class DashboardChartSelector(Editor):
    def edit(self):
        errors = []
        editor_models = []
        links = self.metadata_storage.get_data_by_path('first_report.chart_relations.linkages')
        if not links or not isinstance(links, list):
            return editor_models, errors
        for link in links:
            related_list = link.get('related_list')
            for relate in related_list:
                relations = relate.get('relations')
                model = DashboardChartSelectorModel(
                    id=relate.get('id'),
                    chart_initiator_id=link.get('chart_initiator_id'),
                    type=0,
                    dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                    chart_responder_id=relate.get('chart_responder_id'),
                    is_same_dataset=0,
                )
                editor_models.append(model)
                for relation in relations:
                    if relation.get('is_same_dataset'):
                        # 这里使用了，子节点的数据来判断，这个结构可能需要调整
                        model.is_same_dataset = 1
                        continue
                    editor_models.append(
                        DashboardChartSelectorFieldModel(
                            id=relation.get('id'),
                            selector_id=link.get('id'),
                            chart_id=link.get('chart_initiator_id'),
                            field_initiator_id=relation.get('field_initiator_id'),
                            field_responder_id=relation.get('field_responder_id'),
                            dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                        )
                    )
        return editor_models, errors

    def get_action_name(self):
        return '保存联动'
