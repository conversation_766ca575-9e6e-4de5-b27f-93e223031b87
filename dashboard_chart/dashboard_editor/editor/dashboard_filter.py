#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/20 17:23
# <AUTHOR> caoxl
# @File     : dashboard_filter_editor.py

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardFilterModel,
    DashboardDatasetFieldRelationModel,
    DashboardFilterRelationModel,
)


class DashboardFilter(Editor):
    """
    报告级筛选
    """
    def edit(self):
        errors = []
        editor_models = []
        dashboard_filters = self.metadata_storage.metadata_dashboard_filters()
        if len(dashboard_filters) < 1:
            return editor_models, errors
        dashboard = self.metadata_storage.metadata_dashboard()
        dashboard_id = dashboard.get('id')
        for dashboard_filter in dashboard_filters:
            editor_models.append(
                DashboardFilterModel(
                    **{
                        'id': dashboard_filter.get('id'),
                        'dashboard_id': dashboard_filter.get('dashboard_id'),
                        'main_dataset_field_id': dashboard_filter.get('main_dataset_field_id'),
                        'filter_relation': dashboard_filter.get('filter_relation'),
                    }
                )
            )
            _filter_relations = dashboard_filter.get('filter_relations')
            if isinstance(dashboard_filter.get('operators'), list) and len(dashboard_filter.get('operators')) > 0:
                self._deal_with_operators(dashboard_filter, editor_models)
            if _filter_relations and len(_filter_relations) > 0:
                self._deal_with_filter_relation(dashboard_filter, dashboard_id, editor_models)
        return editor_models, errors

    @staticmethod
    def _deal_with_operators(dashboard_filter, editor_models):
        """

        :param dashboard_filter:
        :param editor_models:
        :return:
        """
        _operators = dashboard_filter.get('operators')
        for _operator in _operators:
            col_value = _operator.get('col_value')
            if _operator.get('select_all_flag') == 1:
                col_value = ''
            editor_models.append(
                DashboardFilterRelationModel(
                    **{
                        'id': _operator.get('id'),
                        'dashboard_id': dashboard_filter.get('dashboard_id'),
                        'dashboard_filter_id': dashboard_filter.get('id'),
                        'operator': _operator.get('operator'),
                        'col_value': col_value,
                        'select_all_flag': _operator.get('select_all_flag'),
                    }
                )
            )

    @staticmethod
    def _deal_with_filter_relation(dashboard_filter, dashboard_id, editor_models):
        """

        :param dashboard_filter:
        :param dashboard_id:
        :param editor_models:
        :return:
        """
        _filter_relations = dashboard_filter.get('filter_relations')
        for _filter_relation in _filter_relations:
            editor_models.append(
                DashboardDatasetFieldRelationModel(
                    **{
                        'id': _filter_relation.get('id'),
                        'dashboard_id': dashboard_id,
                        'main_dataset_field_id': _filter_relation.get('main_dataset_field_id'),
                        'related_dataset_field_id': _filter_relation.get('related_dataset_field_id'),
                    }
                )
            )

    def get_action_name(self):

        return '报告级筛选'