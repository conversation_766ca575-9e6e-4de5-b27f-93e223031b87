#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardJumpConfigModel,
    DashboardJumpRelationModel,
    DashboardChartParamsJumpModel,
    DashboardVarJumpRelationModel,
    DashboardFixedVarJumpRelationModel,
    DashboardFilterChartRelationModel,
    DashboardGlobalParamsRedirectRelationModel,
)


class DashboardChartJump(Editor):
    def edit(self):
        errors = []
        editor_models = []
        redirects = self.metadata_storage.get_data_by_path('first_report.chart_relations.redirects')
        if redirects and isinstance(redirects, list):
            dashboard_id = self.metadata_storage.get_data_by_path('first_report.id')
            for redirect in redirects:
                chart_id = redirect.get('chart_id')
                chart_redirects = redirect.get('chart_redirect')
                complex_redirects = redirect.get('complex_redirect')
                if chart_redirects:
                    self._deal_with_chart_redirects(chart_redirects, chart_id, editor_models, dashboard_id)
                if complex_redirects:
                    self._deal_with_complex_redirects(complex_redirects, chart_id, editor_models, dashboard_id)
        return editor_models, errors

    def _format_json_str(self, value):
        if isinstance(value, (list, dict)):
            return json.dumps(value, ensure_ascii=False)
        if not value:
            return ''
        return str(value)

    def _deal_with_chart_redirects(self, chart_redirects, chart_id, editor_models, dashboard_id):
        """
        单图普通跳转
        :param chart_redirects:
        :param chart_id:
        :param editor_models:
        :param dashboard_id:
        :return:
        """
        for chart_redirect in chart_redirects:
            relations = chart_redirect.get('relations') or []
            editor_models.append(
                DashboardJumpConfigModel(
                    id=chart_redirect.get('id'),
                    dashboard_chart_id=chart_id,
                    dashboard_id=dashboard_id,
                    source_id=chart_redirect.get('dataset_field_id'),
                    target=chart_redirect.get('target'),
                    target_type=chart_redirect.get('target_type'),
                    open_way=chart_redirect.get('open_way'),
                    status=chart_redirect.get('status', 1),
                    has_token=chart_redirect.get('has_token'),
                    with_params=chart_redirect.get('with_params', 0),
                    sort=chart_redirect.get('sort', 0),
                    is_default=chart_redirect.get('is_default', 1),
                    source_type=chart_redirect.get('type'),
                    title=chart_redirect.get('title'),
                    condition_jump=self._format_json_str(chart_redirect.get('condition_jump')),
                    unbound_related_dims=chart_redirect.get('unbound_related_dims'),
                    redirect_window_config=chart_redirect.get('redirect_window_config'),
                )
            )
            # 组件跳转关系数据存储
            for relation in relations:
                self._op_normal_jump_relation(chart_id, editor_models, relation, chart_redirect)
                self._op_params_jump_relation(chart_id, editor_models, relation, chart_redirect)
                self._op_vars_jump_relation(chart_id, editor_models, relation, chart_redirect)
                self._op_fixed_vars_jump_relation(chart_id, editor_models, relation, chart_redirect)
                self._op_filter_chart_relation(chart_id, editor_models, relation, chart_redirect)
                self._op_global_params_relation(chart_id, editor_models, relation, chart_redirect)

    def _deal_with_complex_redirects(self, complex_redirects, chart_id, editor_models, dashboard_id):
        """
        单图标题跳转
        :param complex_redirects:
        :param chart_id:
        :param editor_models:
        :param dashboard_id:
        :return:
        """
        for complex_redirect in complex_redirects:
            relations = complex_redirect.get('relations') or []
            editor_models.append(
                DashboardJumpConfigModel(
                    id=complex_redirect.get('id'),
                    dashboard_chart_id=chart_id,
                    dashboard_id=dashboard_id,
                    source_id=chart_id,
                    target=complex_redirect.get('target'),
                    target_type=complex_redirect.get('target_type'),
                    open_way=complex_redirect.get('open_way'),
                    status=complex_redirect.get('status', 1),
                    has_token=complex_redirect.get('has_token'),
                    sort=complex_redirect.get('sort', 0),
                    is_default=complex_redirect.get('is_default', 1),
                    with_params=complex_redirect.get('with_params', 0),
                    source_type=complex_redirect.get('type'),
                    unbound_related_dims=complex_redirect.get('unbound_related_dims'),
                    condition_jump=self._format_json_str(complex_redirect.get('condition_jump')),
                    redirect_window_config=complex_redirect.get('redirect_window_config'),
                )
            )
            for relation in relations:
                self._op_normal_jump_relation(chart_id, editor_models, relation, complex_redirect)
                self._op_params_jump_relation(chart_id, editor_models, relation, complex_redirect)
                self._op_vars_jump_relation(chart_id, editor_models, relation, complex_redirect)
                self._op_fixed_vars_jump_relation(chart_id, editor_models, relation, complex_redirect)
                self._op_filter_chart_relation(chart_id, editor_models, relation, complex_redirect)
                self._op_global_params_relation(chart_id, editor_models, relation, complex_redirect)

    def _op_params_jump_relation(self, chart_id, editor_models, relation, redirect):
        # [标记] 参数跳转获取保存
        if relation.get('relation_type') == 1:
            editor_models.append(
                DashboardChartParamsJumpModel(
                    dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                    dashboard_chart_id=chart_id,
                    param_dataset_field_id=relation.get('field_initiator_id'),
                    source_id=redirect.get('dataset_field_id') or chart_id,
                    dashboard_filter_id=relation.get('dashboard_filter_id'),
                    rank=relation.get('rank', 0),
                    relation_field_type=relation.get('relation_field_type', 1),
                    target_filter_id=relation.get('target_filter_id', ''),
                    target_filter_field_id=relation.get('target_filter_field_id', ''),
                    global_params_id=relation.get('global_params_id', ''),
                )
            )
        return editor_models

    def _op_normal_jump_relation(self, chart_id, editor_models, relation, redirect):
        # [标记] 维度跳转
        if relation.get('relation_type') == 0:
            editor_models.append(
                DashboardJumpRelationModel(
                    jump_config_id=redirect.get('id'),
                    dashboard_chart_id=chart_id,
                    dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                    dashboard_filter_id=relation.get('dashboard_filter_id'),
                    dataset_field_id=relation.get('field_initiator_id'),
                    filter_type=relation.get('filter_type', 0),
                    relation_field_type=relation.get('relation_field_type', 1),
                    target_filter_id=relation.get('target_filter_id', ''),
                    target_filter_field_id=relation.get('target_filter_field_id', ''),
                    global_params_id=relation.get('global_params_id', ''),
                )
            )
        return editor_models

    def _op_vars_jump_relation(self, chart_id, editor_models, relation, redirect):
        # [标记] 变量跳转
        if relation.get('relation_type') == 2:
            editor_models.append(
                DashboardVarJumpRelationModel(
                    jump_config_id=redirect.get('id'),
                    dashboard_chart_id=chart_id,
                    dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                    dashboard_filter_id=relation.get('dashboard_filter_id'),
                    dataset_field_id=relation.get('field_initiator_id'),
                    dataset_id=relation.get('dataset_id'),
                    var_id=relation.get('var_id'),
                    filter_type=relation.get('filter_type', 0),
                    relation_field_type=relation.get('relation_field_type', 1),
                    target_filter_id=relation.get('target_filter_id', ''),
                    target_filter_field_id=relation.get('target_filter_field_id', ''),
                    global_params_id=relation.get('global_params_id', ''),
                )
            )
        return editor_models

    def _op_fixed_vars_jump_relation(self, chart_id, editor_models, relation, redirect):
        # [标记] 固定值跳转
        if relation.get('relation_type') == 3:
            editor_models.append(
                DashboardFixedVarJumpRelationModel(
                    jump_config_id=redirect.get('id'),
                    dashboard_chart_id=chart_id,
                    dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                    dashboard_filter_id=relation.get('dashboard_filter_id'),
                    dataset_field_id=relation.get('field_initiator_id'),
                    # dataset_id=relation.get('dataset_id'),
                    var_name=relation.get('initiator_alias'),
                    var_value=relation.get('initiator_value'),
                    filter_type=relation.get('filter_type', 0),
                    relation_field_type=relation.get('relation_field_type', 1),
                    target_filter_id=relation.get('target_filter_id', ''),
                    target_filter_field_id=relation.get('target_filter_field_id', ''),
                    global_params_id=relation.get('global_params_id', ''),
                )
            )
        return editor_models

    def _op_filter_chart_relation(self, chart_id, editor_models, relation, redirect):
        # [标记] 筛选器跳转
        if relation.get('relation_type') == 4:
            editor_models.append(
                DashboardFilterChartRelationModel(
                    jump_config_id=redirect.get('id'),
                    dashboard_chart_id=chart_id,
                    dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                    dataset_field_id=relation.get('field_initiator_id', ''),
                    filter_chart_id=relation.get('filter_chart_id', ''),
                    date_filter_chart_flag=relation.get('date_filter_chart_flag', ''),
                    global_params_id=relation.get('global_params_id', ''),
                )
            )
        return editor_models

    def _op_global_params_relation(self, chart_id, editor_models, relation, redirect):
        # [标记] 筛选器跳转
        if relation.get('relation_type') == 5:
            editor_models.append(
                DashboardGlobalParamsRedirectRelationModel(
                    jump_config_id=redirect.get('id'),
                    dashboard_chart_id=chart_id,
                    dashboard_id=self.metadata_storage.get_data_by_path('first_report.id'),
                    initiator_global_params_id=relation.get('initiator_global_params_id', ''),
                    global_params_id=relation.get('global_params_id', ''),
                )
            )
        return editor_models

    def get_action_name(self):
        return '保存跳转'
