"""
单图筛选
"""

# ---------------- 标准模块 ----------------
import logging


# ---------------- 业务模块 ----------------
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import ChartLinkageModel, ChartLinkageRelation


from typing import Any, List, Tuple

logger = logging.getLogger(__name__)


class ChartLinkageEditor(Editor):
    """
    单图筛选
    """

    def __repr__(self):
        return '单图联动'

    def get_action_name(self):
        return "单图间联动"

    def edit(self) -> Tuple[List[Any], List[Any]]:
        errors = []
        editor_models = []
        chart_linkages = self.metadata_storage.get_data_by_path("first_report.chart_relations.chart_linkages")
        dashboard_id = self.metadata_storage.get_data_by_path("first_report.id")
        logger.debug("edit chartlinkage:%s", chart_linkages)

        if (not chart_linkages) or (not isinstance(chart_linkages, list)):
            return editor_models, errors

        for chart_linkage in chart_linkages:
            if chart_linkage["related_list"].__len__() > 0:
                chart_linkage_mode_id = chart_linkage.get("id")
                chart_linkage_mode = ChartLinkageModel(
                    id=chart_linkage_mode_id,
                    dashboard_id=dashboard_id,
                    dataset_id=chart_linkage.get("dataset_id"),
                    dataset_field_id=chart_linkage.get("field_initiator_id"),
                    chart_id=chart_linkage.get("chart_initiator_id"),
                )
                editor_models.append(chart_linkage_mode)

                for related in chart_linkage["related_list"]:
                    chart_linkage_related = ChartLinkageRelation(
                        id=related.get("id"),
                        dashboard_id=dashboard_id,
                        link_id=chart_linkage_mode_id,
                        chart_responder_id=related.get("chart_responder_id"),
                        dataset_responder_id=related.get("related_dataset_id"),
                        field_responder_id=related.get("field_responder_id"),
                    )
                    editor_models.append(chart_linkage_related)
        logger.debug("chart linkage models, errors:%s, %s", editor_models, errors)

        return editor_models, errors
