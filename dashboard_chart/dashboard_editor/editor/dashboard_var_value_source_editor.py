#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL>

"""
变量取值来源绑定关系
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardValueSourceModel,
    DashboardVarsValueSourceRelationModel,
)


logger = logging.getLogger(__name__)


class DashboardVarValueSourceEditor(Editor):
    def edit(self):
        errors = []
        editor_models = []
        dashboard = self.metadata_storage.metadata_dashboard()
        var_value_sources = self.metadata_storage.metadata_dashboard_var_value_sources()
        if not var_value_sources:
            return editor_models, errors
        for single_value_source in var_value_sources:
            value_source_id = single_value_source.get("id")
            editor_models.append(
                DashboardValueSourceModel(
                    **{
                        'id': value_source_id,
                        'dashboard_id': dashboard.get('id'),
                        'value_source_name': single_value_source.get('value_source_name'),
                        'value_source': single_value_source.get('value_source'),
                        'value_identifier': single_value_source.get('value_identifier'),
                    }
                )
            )
            relations = single_value_source.get('relations', [])
            self._op_relations(relations, dashboard, value_source_id, editor_models)

        return editor_models, errors

    @staticmethod
    def _op_relations(relations, dashboard, value_source_id, editor_models):
        """
        处理绑定关系
        :param relations:
        :param editor_models:
        :return:
        """
        for single_relation_var_id in relations:
            editor_models.append(
                DashboardVarsValueSourceRelationModel(
                    **{
                        'dashboard_id': dashboard.get('id'),
                        'var_id': single_relation_var_id,
                        'value_source_id': value_source_id,
                    }
                )
            )

    def get_action_name(self):
        """
        获取操作名称
        :return str:
        """
        return "变量取值来源绑定关系"
