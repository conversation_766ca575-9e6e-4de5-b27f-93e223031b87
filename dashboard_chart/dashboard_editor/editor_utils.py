#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/23 15:49
# <AUTHOR> caoxl
# @File     : editor_utils.py
# pylint: disable=R0205

import json

from base import repository
from base.errors import InvalidParamsError


class EditorUtils(object):
    @staticmethod
    def batch_exec_sql(sqls: list):
        """
        批量执行sql
        :return:
        """
        sql = ''
        for each in sqls:
            sql += str(each) + ';'
        return repository.exec_sql(sql)

    @staticmethod
    def create_insert_sql(table, fields: list, data: list):
        """
        创建插入语句
        :param table: str
        :param fields: list
        :param data: list
        :return: str
        """
        fields_str = '(' + ','.join(map(lambda x: '`' + x + '`', fields)) + ')'
        sql = 'INSERT INTO `{table_name}` {fields_str} VALUES '.format(table_name=table, fields_str=fields_str)
        values = []
        for each in data:
            _value = []
            _keys = each.keys()
            for field in fields:
                if field not in _keys:
                    return False, '插入数据缺少字段: {field_name}'.format(field_name=field)
                _value.append(each.get(field))
            values.append('(' + ','.join(_value) + ')')
        if len(values) < 1:
            return False, '抱歉，解析插入数据异常，请重试！'
        sql += ','.join(values)
        return True, sql

    @staticmethod
    def convert_to_db_value(value):
        """
        将python类型转换为数据库类型
        1. str int float None bool 对应为 string int float/double null boolean
        2. set list touple dict 对应为string （json）
        :param value:
        :return:
        """
        if isinstance(value, (set, list, tuple, dict)):
            try:
                return json.dumps(value)
            except Exception as e:
                raise InvalidParamsError(message='值无法转换为json字符串,错误信息:{error_msg}'.format(error_msg=str(e)))
        # elif isinstance(value, (str, int, float, type(None), bool)):
        #     return value
        # else:
        #     raise InvalidParamsError(message='数据库不支持该类型的值!')
        return value
