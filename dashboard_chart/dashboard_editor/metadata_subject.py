#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/11/20 17:22
# <AUTHOR> caoxl
# @File     : metadata_subject.py
# pylint: disable=R0205,W0703
import logging

from dashboard_chart.dashboard_editor.metadata_storage import MetadataStorage


from dashboard_chart.dashboard_editor.editor.editor import Editor
from dashboard_chart.dashboard_editor.editor.models import (
    DashboardChartDimModel,
    DashboardChartModel,
    DashboardChartNumModel,
    DashboardModel,
)
from typing import Any, List, Tuple, Union


class MetadataSubject(object):

    # 解析器
    _observer_editors = [
        'dashboard',
        'dashboard_chart',
        'dashboard_chart_comparison',
        'dashboard_chart_desire',
        'dashboard_chart_dim',
        'dashboard_chart_filter',
        'dashboard_chart_jump',
        'dashboard_chart_markline',
        'dashboard_chart_num',
        'dashboard_chart_param',
        'dashboard_chart_penetrate_relation',
        'dashboard_chart_selector',
        'dashboard_chart_zaxis',
        'dashboard_component_filter',
        'dashboard_filter',
        'dashboard_global_params',
        'dashboard_var_filter',
        'chart_filter_editor',
        'chart_linkage_editor',
        'var_relations_editor',
        'dashboard_chart_field_sort',
        'dashboard_var_value_source_editor',
        'chart_visible_triggers',
    ]

    # 需要忽略校验错误的业务调用来源
    _ignored_error_edit_source = ["copy", "edit_pre_meta"]

    def __init__(self, metadata, edit_source="default"):
        """
        初始化
        :param metadata: 元数据
        """
        self._editors = dict()
        self._metadata_storage = MetadataStorage(metadata=metadata)
        self.edit_source = edit_source  # 业务来源

    def metadata_storage(self) -> MetadataStorage:
        """
        获取当前对象代理的storage对象
        :return:
        """
        return self._metadata_storage

    def do_edit(
        self
    ) -> Union[
        Tuple[List[Union[DashboardModel, DashboardChartModel]], List[Any]],
        Tuple[List[Union[DashboardModel, DashboardChartModel, DashboardChartDimModel]], List[Any]],
        Tuple[List[DashboardModel], List[Any]],
        Tuple[
            List[Union[DashboardModel, DashboardChartModel, DashboardChartDimModel, DashboardChartNumModel]], List[Any]
        ],
    ]:
        """
        触发编辑
        :return:
        """
        errors = []
        editor_models = []
        for editor_name in self._observer_editors:
            editor = self.create_editor(editor_name)
            try:
                _editor_models, _errors = editor.edit()
                if len(_errors) > 0 and self.edit_source not in self._ignored_error_edit_source:
                    errors.extend(_errors)
                else:
                    editor_models.extend(_editor_models)
            except Exception as e:
                logging.exception(e)
                errors.append(editor.get_action_name() + ' 发生异常，错误信息: ' + str(e))
        return editor_models, errors

    def attach_editor(self, editor_name: str):
        """
        注册editor
        :param editor_name:
        :return:
        """
        if editor_name not in self._editors:
            self._editors[editor_name] = self.create_editor(editor_name=editor_name)
        return self._editors

    def create_editor(self, editor_name: str) -> Editor:
        """
        根据editor名称创建editor
        :param editor_name:
        :return:
        """
        class_file_name = editor_name
        class_name = ''
        name_arr = editor_name.split('_')
        for name in name_arr:
            class_name += name.capitalize()
        module_obj = __import__('dashboard_chart.dashboard_editor.editor.' + class_file_name, fromlist=[class_name])
        editor_class = getattr(module_obj, class_name)
        return editor_class(metadata_storage=self._metadata_storage)
