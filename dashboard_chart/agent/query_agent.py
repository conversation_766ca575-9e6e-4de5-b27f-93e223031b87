#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=R0201

"""
    <NAME_EMAIL> 2018/9/21
"""
import json

from typing import List

from base.enums import ColTypes, FieldValueType
from base.query_builder.query import Query
from dashboard_chart.agent.agents import FieldAgent, ConditionAgent, OrderAgent, LimitAgent, OffsetAgent, QueryVarAgent
from dashboard_chart.convertor.field_types import FieldObj
from dataset.external_query_service import get_dataset_data, get_dataset_query_struct, get_dataset_query_sql
from dmplib.context.globals import g
from traceback import FrameSummary
from self_service import external_service as external_subject_service

# 定义查询结构体profiling的trace深度
from dmplib.utils.errors import UserError

QUERY_PROFILE_TRACE_DEEP = 7


class QueryAgent:
    @staticmethod
    def _convert_select(data):
        if data.get("select"):
            select_agent = FieldAgent()
            return select_agent.convert(data.get("select"))
        return []

    @staticmethod
    def _convert_where(data):
        if data.get("where") and data.get("dataset_field_dict"):
            where_agent = ConditionAgent()
            return where_agent.convert(data.get("where"), data.get("dataset_field_dict"))
        return []

    @staticmethod
    def _convert_having(data):
        if data.get("having"):
            where_agent = ConditionAgent()
            return where_agent.convert(data.get("having"), data.get("dataset_field_dict"))
        return []

    @staticmethod
    def _convert_group(data):
        if data.get("group"):
            group_agent = FieldAgent()
            return group_agent.convert(data.get("group"))
        return []

    @staticmethod
    def _convert_order(data):
        if data.get("order"):
            order_agent = OrderAgent()
            return order_agent.convert(data.get("order"))
        return []

    @staticmethod
    def _convert_limit(data):
        if data.get("limit"):
            limit_agent = LimitAgent()
            offset_agent = OffsetAgent()
            limit = limit_agent.convert(data.get("limit"))
            offset = offset_agent.convert(data.get("limit"))
            return limit, offset
        return None, None

    @staticmethod
    def _convert_distinct(data):
        if data.get("distinct"):
            return True
        return False

    @staticmethod
    def _convert_vars(data):
        if data.get("vars"):
            query_var_agent = QueryVarAgent()
            return query_var_agent.convert(data.get("vars"))
        return []

    def _add_func2field(self, field: FieldObj, obj_name: str, field_name: str, func_name: str, args: List) -> FieldObj:
        """
        递归为field对象增加函数
        :param field:
        :param func_name:
        :param args:
        :return:
        """
        if not field.field_func:
            if field.table != obj_name or field.field != field_name:
                return field
            for arg in args:
                field_obj = FieldObj()
                field_obj.table = arg[0]
                field_obj.field = arg[1]
                field_obj.field_ref = arg[2]
                field_obj.logic_source = ColTypes.Dim.value
                field_obj.value_type = FieldValueType.Column.value
                field.props.append(field_obj)
            field.field_func = func_name
        else:
            for idx, prop in enumerate(field.props):
                field.props[idx] = self._add_func2field(prop, obj_name, field_name, func_name, args)
        return field

    def convert(self, data):
        # 此处需要对多外部主体查询的字段进行处理（转换为case when 结构）
        # self._pre_format_convert_data(data)

        select = self._convert_select(data)
        # select 不能为空
        if not select:
            raise UserError(message="请指定需要取数的字段!")
        where = self._convert_where(data)
        having = self._convert_having(data)
        group = self._convert_group(data)
        limit, offset = self._convert_limit(data)
        order = self._convert_order(data)
        distinct = self._convert_distinct(data)
        var_list = self._convert_vars(data)

        # 更改sonar之前的代码
        # if data.get("select"):
        #     select_agent = FieldAgent()
        #     select = select_agent.convert(data.get("select"))
        # if data.get("where"):
        #     where_agent = ConditionAgent()
        #     where = where_agent.convert(data.get("where"))
        # if data.get("having"):
        #     where_agent = ConditionAgent()
        #     having = where_agent.convert(data.get("having"))
        # if data.get("group"):
        #     group_agent = FieldAgent()
        #     group = group_agent.convert(data.get("group"))
        # if data.get("order"):
        #     order_agent = OrderAgent()
        #     order = order_agent.convert(data.get("order"))
        # if data.get("limit"):
        #     limit_agent = LimitAgent()
        #     offset_agent = OffsetAgent()
        #     limit = limit_agent.convert(data.get("limit"))
        #     offset = offset_agent.convert(data.get("offset"))
        # if data.get("distinct"):
        #     distinct = True
        # if data.get("vars"):
        #     query_var_agent = QueryVarAgent()
        #     var_list = query_var_agent.convert(data.get("vars"))

        query_model = Query()
        query = query_model.select(select)
        if where:
            query.where(where)
        if having:
            query.having(having)
        if group:
            query.group(group)
        if order:
            query.order(order)
        if limit:
            query.limit(limit)
        if offset:
            query.offset(offset)
        if distinct:
            query.distinct()
        if var_list:
            query.vars(var_list)
        struct_obj = query.build(output_format="json")
        json_struct = json.dumps(struct_obj)
        if hasattr(g, "profiling"):
            self.add_profiling(struct_obj)
        return json_struct

    @staticmethod
    def add_profiling(json_struct):
        """
        将查询json结构体添加到profiling日志
        :param str json_struct: 查询json结构体
        :return:
        """
        import traceback

        extracted_list = traceback.extract_stack(limit=QUERY_PROFILE_TRACE_DEEP)

        g.query.append(
            {
                "struct": json_struct,
                "stacks": [
                    "{fname} {lineno} {name}".format(fname=frame.filename, lineno=frame.lineno, name=frame.name)
                    if isinstance(frame, FrameSummary)
                    else "{fname} {lineno} {name}".format(fname=frame[0], lineno=frame[1], name=frame[2])
                    for frame in extracted_list
                    if (isinstance(frame, (list, tuple)) and not frame[0].find('site-packages') > 0)
                    or (isinstance(frame, FrameSummary) and not frame.filename.find('site-packages') > 0)
                ],
            }
        )

    @staticmethod
    def query(**params):
        """
        :param user_id:
        :param dataset_id:
        :param chart_id:
        :param query_structure_json:
        :param is_order_master_id:
        :param table_name: 表名，可选
        :return:
        """
        params["query_structure_json"] = params["json_struct"]
        del params["json_struct"]
        return get_dataset_data(**params)

    @staticmethod
    def query_struct(**params):
        """
        :param user_id:
        :param dataset_id:
        :param chart_id:
        :param query_structure_json:
        :param is_order_master_id:
        :return:
        """
        return get_dataset_query_struct(**params)

    @staticmethod
    def query_sql(**params):
        """
        :param user_id:
        :param dataset_id:
        :param chart_id:
        :param query_structure_json:
        :param is_order_master_id:
        :return:
        """
        return get_dataset_query_sql(**params)
