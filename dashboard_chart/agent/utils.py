#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/9/27 15:24
# <AUTHOR> caoxl
# @File     : utils.py
# pylint: disable=W0603

from dashboard_chart.agent.date_value_parser import Date<PERSON><PERSON>er<PERSON>enerator, get_date_util
from dashboard_chart.convertor.field_types import WhereField
from base.enums import SqlWhereOperator, FieldValueType, SqlWhereDateType, DatasetFieldDataType


def _convert_day_operator(where_field: WhereField, value) -> WhereField:
    date_util = get_date_util()
    where_field.operator = SqlWhereOperator.Between.value
    day_start = date_util.get_from_today(value)
    day_end = date_util.get_from_today(0)
    if value < 0:
        # 往后推算
        where_field.field_value.field = [day_end, day_start]
    else:
        # 往前推算
        where_field.field_value.field = [day_start, day_end]
    where_field.field_value.value_type = FieldValueType.List.value
    return where_field


def _convert_yesterday_operator(where_field: WhereField, value) -> WhereField:
    date_util = get_date_util()
    where_field.operator = SqlWhereOperator.Between.value
    where_field.field_value.value_type = FieldValueType.List.value
    day_start = date_util.get_from_today(value)
    if value < 0:
        # 往后推算(不含今天)
        day_end = date_util.get_from_today(-1)
        where_field.field_value.field = [day_end, day_start]
    else:
        day_end = date_util.get_yesterday()
        # 往前推算
        where_field.field_value.field = [day_start, day_end]
    return where_field


def _convert_week_operator(where_field: WhereField, value) -> WhereField:
    date_util = get_date_util()
    value = 0 - value
    week_start = date_util.get_week_start(value)
    week_end = date_util.get_week_end(value)
    where_field.operator = SqlWhereOperator.Between.value
    where_field.field_value.field = [week_start, week_end]
    where_field.field_value.value_type = FieldValueType.List.value
    return where_field


def _convert_month_operator(where_field: WhereField, value) -> WhereField:
    date_util = get_date_util()
    value = 0 - value
    month_start = date_util.get_month_start(value)
    month_end = date_util.get_month_end(value)
    where_field.operator = SqlWhereOperator.Between.value
    where_field.field_value.field = [month_start, month_end]
    where_field.field_value.value_type = FieldValueType.List.value
    return where_field


def _convert_quarter_operator(where_field: WhereField, value) -> WhereField:
    date_util = get_date_util()
    value = 0 - value
    quarter_start = date_util.get_quarter_start(value)
    quarter_end = date_util.get_quarter_end(value)

    where_field.operator = SqlWhereOperator.Between.value
    where_field.field_value.field = [quarter_start, quarter_end]
    where_field.field_value.value_type = FieldValueType.List.value
    return where_field


def _convert_year_operator(where_field: WhereField, value) -> WhereField:
    date_util = get_date_util()
    value = 0 - value
    year_start = date_util.get_year_start(value)
    year_end = date_util.get_year_end(value)
    where_field.operator = SqlWhereOperator.Between.value
    where_field.field_value.field = [year_start, year_end]
    where_field.field_value.value_type = FieldValueType.List.value
    return where_field


def _op_field_word(value, value_type, where_field: WhereField, field_info: dict) -> WhereField:
    # 判断值是否是日期类型的值(非日期类型不进行解析)
    if (
            isinstance(field_info, dict)
            and (field_info.get("data_type") == DatasetFieldDataType.Datetime.value)
            and value_type
            and (value_type in [FieldValueType.List.value, FieldValueType.String.value])
    ):
        if value_type == FieldValueType.String.value:
            where_field.field_value.field = get_datetime_from_word(value)
        else:
            _field = []
            for _each in where_field.field_value.field:
                _field.append(get_datetime_from_word(_each))
            where_field.field_value.field = _field
    return where_field


def convert_operator_and_value(where_field: WhereField, dataset_field_dict: dict):
    """
    转换操作符和值
    :param where_field:
    :return:
    """
    if where_field.operator is None:
        return where_field
    _operator = None
    if where_field.operator:
        _operator = where_field.operator.upper()
    _value = where_field.field_value.field
    _value_type = where_field.field_value.value_type
    # 如果是日期类操作符
    if _operator and (_value is not None) and is_date_operator(_operator):
        _value = int(_value)
        # 距今日
        if SqlWhereOperator.FromDay.value == _operator:
            where_field = _convert_day_operator(where_field, _value)
        # 距昨日
        elif SqlWhereOperator.FromYesterday.value == _operator:
            where_field = _convert_yesterday_operator(where_field, _value)
        # 距周
        elif SqlWhereOperator.FromWeek.value == _operator:
            where_field = _convert_week_operator(where_field, _value)
        # 距月份
        elif SqlWhereOperator.FromMonth.value == _operator:
            where_field = _convert_month_operator(where_field, _value)
        # 距季度
        elif SqlWhereOperator.FromQuarter.value == _operator:
            where_field = _convert_quarter_operator(where_field, _value)
        # 距年
        elif SqlWhereOperator.FromYear.value == _operator:
            where_field = _convert_year_operator(where_field, _value)
    where_field = _op_field_word(_value, _value_type, where_field, dataset_field_dict.get(where_field.field_ref))
    # 如果是嵌套 递归更改
    if where_field.complex:
        for sub_where_field in where_field.complex:
            convert_operator_and_value(sub_where_field, dataset_field_dict)
    return where_field


def get_datetime_from_word(word):
    """
    根据关键字返回具体时间
    :param word:
    :return:
    """
    date_util = get_date_util()
    if SqlWhereDateType.Today.value == word:
        return date_util.get_today()
    elif SqlWhereDateType.Yesterday.value == word:
        return date_util.get_yesterday()
    elif SqlWhereDateType.Lastweek.value == word:
        start, end = date_util.get_last_week()
        return end
    elif SqlWhereDateType.Lastmonth.value == word:
        start, end = date_util.get_last_month()
        return end
    elif SqlWhereDateType.Lastquarter.value == word:
        start, end = date_util.get_last_quarter()
        return end
    elif SqlWhereDateType.Lastyear.value == word:
        start, end = date_util.get_last_year()
        return end
    elif isinstance(word, str):
        return DateParserGenerator().parse(word)
    else:
        return word


def is_date_operator(operator):
    """
    是否是日期类操作符
    :param operator:
    :return:
    """
    operator = operator.upper()
    is_date_operators = [
        SqlWhereOperator.FromDay.value,
        SqlWhereOperator.FromMonth.value,
        SqlWhereOperator.FromQuarter.value,
        SqlWhereOperator.FromWeek.value,
        SqlWhereOperator.FromYear.value,
        SqlWhereOperator.FromYesterday.value,
    ]
    if operator in is_date_operators:
        return True
    return False
