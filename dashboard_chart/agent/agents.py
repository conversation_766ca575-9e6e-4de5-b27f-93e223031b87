#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/9/25 19:11
# <AUTHOR> caoxl
# @File     : condition_agent.py
# pylint: skip-file
import copy

from base.enums import FieldValueType
from base.query_builder.expressions import as_, case, eq, desc, is_
from base.query_builder.query_types import Column, Text, Number, Null, DateTime
from base.query_builder.vars import DatasetVar as QueryDatasetVar
from dashboard_chart.agent import utils as agent_utils
from dashboard_chart.agent.expression_map import ExpressionMap
from dashboard_chart.agent.func_map import FuncMap
from dashboard_chart.convertor.field_types import FieldObj, OrderCustomField
from dashboard_chart.convertor.query_vars import DatasetVar
from dmplib.utils.errors import InvalidArgumentError


class Agent(object):
    @staticmethod
    def _get_query_obj(field):
        if field.value_type == FieldValueType.Column.value:
            return Column(name=field.field, table=field.table)
        if field.value_type == FieldValueType.String.value:
            return Text(field.field)
        if field.value_type == FieldValueType.Number.value:
            return Number(field.field)
        if field.value_type == FieldValueType.Null.value:
            return Null()
        if field.value_type == FieldValueType.Datetime.value:
            return DateTime(field.field)
        return None

    def _get_normal_field(self, field):
        """
        普通字段
        :param field:
        :return:
        """
        if field.value_type in (
            FieldValueType.Column.value,
            FieldValueType.String.value,
            FieldValueType.Number.value,
            FieldValueType.Null.value,
            FieldValueType.Datetime.value,
        ):
            if isinstance(field.field, (Column, Text, Number, Null, DateTime)):
                _each = field.field
            else:
                _each = self._get_query_obj(field)
        elif field.value_type == FieldValueType.List.value:
            _each = field.field
        else:
            raise InvalidArgumentError(message="字段格式不正确！")
        return _each

    def format_field(self, field):
        # 函数
        if hasattr(field, "field_func") and field.field_func:
            params = []
            for prop in field.props:
                params.append(self.format_field(prop))
            func_class = FuncMap.get_func_class(field.field_func)
            if not func_class:
                raise InvalidArgumentError(message="函数{func_name}不存在！".format(func_name=field.field_func))
            _each = func_class(*params)
        # 一般字段
        else:
            _each = self._get_normal_field(field)

            # 修复sonar之前的代码
            # 字段
            # if field.value_type == FieldValueType.Column.value:
            #     if isinstance(field.field, Column):
            #         _each = field.field
            #     else:
            #         _each = Column(name=field.field, table=field.table)
            # elif field.value_type == FieldValueType.String.value:
            #     if isinstance(field.field, Text):
            #         _each = field.field
            #     else:
            #         _each = Text(field.field)
            # elif field.value_type == FieldValueType.Number.value:
            #     if isinstance(field.field, Number):
            #         _each = field.field
            #     else:
            #         _each = Number(field.field)
            # elif field.value_type == FieldValueType.Null.value:
            #     if isinstance(field.field, Null):
            #         _each = field.field
            #     else:
            #         _each = Null()
            # elif field.value_type == FieldValueType.Datetime.value:
            #     if isinstance(field.field, DateTime):
            #         _each = field.field
            #     else:
            #         _each = DateTime(field.field)
            # elif field.value_type == FieldValueType.List.value:
            #     _each = field.field
            # else:
            #     raise InvalidArgumentError(message="字段格式不正确！")
        if field.alias:
            _each = as_(_each, field.alias)
        return _each


class FieldAgent(Agent):
    """
    字段转换
    """

    def convert(self, data):
        fields = list()
        for _field in data:
            fields.append(self.format_field(_field))
        return fields


class OrderAgent(Agent):
    def _op_custom_order(self, field):
        _whens = list()
        for idx, elem in enumerate(field.items):
            if elem is None:
                _whens.append((is_(self.format_field(copy.deepcopy(field)), elem), Number(1500 - idx)))
            else:
                _whens.append((eq(self.format_field(copy.deepcopy(field)), elem), Number(1500 - idx)))
        field = desc(case(whens=_whens, else_=0))
        return field

    def _op_normal_order(self, field):
        expression_class = ExpressionMap.get_expression_class(field.field_sort.lower())
        if not expression_class:
            raise InvalidArgumentError(message="非法的表达式")
        field = self.format_field(field)
        field = expression_class(field)
        return field

    def convert(self, data):
        fields = list()
        for _field in data:
            if _field.field_sort:
                # 用户自定义排序转换为CASE表达式
                if _field.field_sort == "CUSTOM" and isinstance(_field, OrderCustomField) and _field.items:
                    _field = self._op_custom_order(_field)
                # DESC ASC
                else:
                    _field = self._op_normal_order(_field)
            else:
                _field = self.format_field(_field)
            fields.append(_field)
        return fields


class LimitAgent(Agent):
    @staticmethod
    def convert(data):
        return data.limit


class OffsetAgent(Agent):
    @staticmethod
    def convert(data):
        return data.offset


class ConditionAgent(Agent):
    """
    条件表达式转换
    """

    def get_logic_class(self, logic):
        return ExpressionMap.get_expression_class(logic)

    def _op_complex_condition(self, condition, param, dataset_field_dict):
        _sub_logic_class = self.get_logic_class(condition.complex_logic)
        if param:
            param = _sub_logic_class(*[param, self.convert(condition.complex, dataset_field_dict)])
        else:
            param = _sub_logic_class(*[self.convert(condition.complex, dataset_field_dict)])
        return param

    def _op_normal_condition(self, condition, param):
        _left = self.format_field(condition)
        param.append(_left)
        param.append(condition.operator)
        _right = condition.field_value
        if not isinstance(_right, FieldObj):
            raise InvalidArgumentError(message="条件右侧数据类型不正确")
        _right = self.format_field(_right)
        param.append(_right)
        return param

    def convert(self, data, dataset_field_dict):
        pre_logic = data[0].logic
        logic_params = []
        pre_logic_class = self.get_logic_class(pre_logic)
        for _condition in data:
            # 对条件进行转换，替换far操作符号和相关值
            _each = []
            if _condition.operator:
                _condition = agent_utils.convert_operator_and_value(_condition, dataset_field_dict)
                _each = self._op_normal_condition(_condition, _each)
            if _condition.complex:
                _each = self._op_complex_condition(_condition, _each, dataset_field_dict)
            if _condition.logic != pre_logic:
                logic_params = [pre_logic_class(*logic_params)]
            if _each:
                logic_params.append(_each)
            pre_logic = _condition.logic
            pre_logic_class = self.get_logic_class(pre_logic)
        return pre_logic_class(*logic_params)


class QueryVarAgent(Agent):
    @staticmethod
    def convert(data):
        result = []
        for item in data:
            if not isinstance(item, DatasetVar):
                raise InvalidArgumentError(message="变量数据类型不正确！")
            result.append(
                QueryDatasetVar(
                    **{
                        "var_id": item.var_id,
                        "var_type": item.var_type,
                        "value_type": item.value_type,
                        "value_source": item.value_source,
                        "value_identifier": item.value_identifier,
                        "value": item.value,
                        "default_value": item.default_value,
                        "default_value_type": item.default_value_type,
                        "external_content": item.external_content,
                    }
                )
            )
        return result
