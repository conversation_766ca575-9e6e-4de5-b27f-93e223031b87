#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=C0327
"""
    <NAME_EMAIL> 2018/9/14
"""
import json
from abc import ABCMeta, abstractmethod

from base.dmp_constant import CHART_QUERY_DEFAULT_LIMIT
from dashboard_chart.convertor.field_base import FieldBase


class LimitBase(FieldBase):
    __metaclass__ = ABCMeta

    @abstractmethod
    def get_limit_field(self, model):
        """
        获取limit数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """

    @staticmethod
    def get_display_item(display_item):
        """
        获取前n和后n值
        :return:
        """
        head, tail = -1, -1
        if display_item:
            if isinstance(display_item, str):
                display_item = json.loads(display_item)
            top_head = display_item.get("top_head")
            if top_head and int(top_head) > 0:
                head = CHART_QUERY_DEFAULT_LIMIT if int(top_head) > CHART_QUERY_DEFAULT_LIMIT else int(top_head)
            top_tail = display_item.get("top_tail")
            if top_tail and int(top_tail) > 0:
                tail = int(top_tail)
        return head, tail
