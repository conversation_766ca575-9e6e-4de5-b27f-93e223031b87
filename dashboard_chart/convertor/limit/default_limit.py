#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
from base.dmp_constant import (
    CHART_QUERY_DEFAULT_LIMIT,
    DATASET_MAX_SIZE,
    CHART_LAST_N_SPECIAL_FLAG,
    CHART_QUERY_UPPER_LIMIT,
)
from dashboard_chart.convertor.field_types import LimitField
from dashboard_chart.convertor.limit.limit_base import LimitBase
from dmplib.utils.errors import UserError
from dmplib.utils.strings import is_number


class DefaultLimit(LimitBase):
    def get_limit_field(self, model):
        """
        获取默认的limit数据模块
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        limit_field = LimitField()
        limit_field.limit = CHART_QUERY_DEFAULT_LIMIT
        if model.data_limit:
            if not is_number(model.data_limit):
                raise UserError(code=400, message='data_limit必须是数值类型')
            model.data_limit = int(float(model.data_limit))
            if model.data_limit <= CHART_QUERY_UPPER_LIMIT:
                limit_field.limit = model.data_limit
            else:
                limit_field.limit = CHART_QUERY_UPPER_LIMIT
        try:
            head, tail = self.get_display_item(model.display_item)
        except Exception as e:
            raise UserError(message="数据显示条目数属性配置错误：" + str(e))
        # 前N
        if head > -1:
            limit_field.limit = head
        # 后N
        if tail > -1:
            if tail >= DATASET_MAX_SIZE:
                raise UserError(message="设置的后N值太大！")
            # 标记offset为-9999为后N类型，需要在chart_query中重新设置offset值
            limit_field.offset = CHART_LAST_N_SPECIAL_FLAG
            limit_field.limit = tail
        return limit_field
