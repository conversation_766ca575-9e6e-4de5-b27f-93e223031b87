#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=R0201
"""
    <NAME_EMAIL> 2018/9/14
"""
from base.dmp_constant import CHART_DEFAULT_PAGE_SIZE
from dashboard_chart.convertor.field_types import LimitField
from dashboard_chart.convertor.limit.limit_base import LimitBase


class PageLimit(LimitBase):
    def get_limit_field(self, model):
        """
        分页表格获取limit数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        limit_field = LimitField()
        page_index = int(model.pagination.page) if model.pagination.page else 1
        page_size = model.pagination.page_size if model.pagination.page_size else CHART_DEFAULT_PAGE_SIZE
        limit_field.limit = page_size
        limit_field.offset = (page_index - 1) * page_size
        return limit_field

    def get_common_limit_field(self, model):
        """
        通用表格获取limit数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        limit_field = LimitField()
        page_index = int(model.pagination.page) if model.pagination.page else 1
        page_size = model.pagination.page_size if model.pagination.page_size else 20
        limit_field.limit = page_size
        limit_field.offset = (page_index - 1) * page_size
        return limit_field
