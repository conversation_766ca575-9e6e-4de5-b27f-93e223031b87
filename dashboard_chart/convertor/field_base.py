#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/20
"""
from abc import ABCMeta

from base.enums import AdvancedComputeMothed
from base.enums import DatasetFieldDataType
from dmplib.utils.errors import UserError


class FieldBase:
    __metaclass__ = ABCMeta

    def __init__(self):
        super(FieldBase, self).__init__()
        self.label_dataset_key = "big_lab_dataset"

    @staticmethod
    def get_dataset_field_alias(formula_mode, col_name, alias_name, is_big_label_dataset=False):
        """
        根据formula_mode和col_name获取字段的别名，此别名跟前端约定的规则进行的拼接
        :param formula_mode: 字段对应的formula_mode值
        :param col_name: 字段名称
        :param alias_name: 字段别名
        :param is_big_label_dataset: 是否大数据不同步标签数据集
        :return: str 跟前端约定好的规则进行拼接的字段别名
        """
        if is_big_label_dataset:
            return col_name
        else:
            if formula_mode:
                return (formula_mode + "_" + alias_name) if alias_name else (formula_mode + "_" + col_name)
            else:
                return alias_name if alias_name else col_name

    def get_dataset_field(self, field_id, dataset_field_dict, formula_mode, is_big_label_dataset=False):
        """
        根据formula_mode和col_name更改字段的别名，并返回修改后的字段dict
        :param field_id: 字段id
        :param dataset_field_dict: 数据集所有字段dict
        :param formula_mode: 字段对应的formula_mode值
        :param is_big_label_dataset: 是否大数据不同步标签数据集
        :return dict  跟前端约定好的规则进行拼接的字段别名
        """
        dataset_field = dataset_field_dict.get(field_id)
        if not dataset_field:
            raise UserError(message="数据集字段不存在， 字段id:{id}".format(id=field_id))
        dataset_field = self.change_dataset_field_col_name(dataset_field, formula_mode, is_big_label_dataset)

        return dataset_field

    @staticmethod
    def change_dataset_field_col_name(dataset_field, formula_mode, is_big_label_dataset=False):
        """
        根据是否为大数据不同步标签数据集和是否有formula_mode来改变col_name
        :param dict dataset_field: 数据集字段数据
        :param str formula_mode: 计算方法， count、sum等
        :param bool is_big_label_dataset: 是否为大数据不同步标签数据集， 默认否
        :return:
        """
        if is_big_label_dataset and formula_mode:
            dataset_field["col_name"] = "{formula_mode}_{col_name}".format(
                formula_mode=formula_mode, col_name=dataset_field.get("col_name")
            )
        return dataset_field

    @staticmethod
    def add_ifnull_to_funcs(field_data, other_funcs, other_args):
        calc_null = field_data.get("calc_null") or 0
        if calc_null:
            other_funcs.append("ifnull")
            if field_data.get('data_type') == DatasetFieldDataType.Description.value:
                other_args.append({"field": '0'})
            else:
                other_args.append({"field": 0})

            return other_funcs, other_args

        return other_funcs, other_args

    def get_func_of_advanced_compute_method(self, formula_mode: str) -> str:
        return AdvancedComputeMothed.get_mothed_original_formula_mode(formula_mode)
