#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/3/29 14:38
# <AUTHOR> caoxl
# @File     : vars.py
from dmplib.utils.errors import InvalidArgumentError
from base.enums import DatasetVarValueSource


class DatasetVar:
    """
    数据集变量
    """

    __slots__ = [
        "var_id", "var_type", "value_type", "value", "value_source",
        "value_identifier", "default_value", "default_value_type", "external_content",
    ]

    def __init__(self, **kwargs):
        if not kwargs.get("var_id"):
            raise InvalidArgumentError(message="请指定变量ID！")
        if not kwargs.get("var_type"):
            raise InvalidArgumentError(message="请指定变量类型！")
        if not kwargs.get("value_type"):
            raise InvalidArgumentError(message="请指定变量值类型！")
        if not kwargs.get("value_source"):
            raise InvalidArgumentError(message="请指定变量值来源!")
        if kwargs.get("value_source") not in [e.value for e in DatasetVarValueSource.__members__.values()]:
            raise InvalidArgumentError(message="非法的变量值来源!")
        if kwargs.get("value_source") != DatasetVarValueSource.Userdefined.value and not kwargs.get("value_identifier"):
            raise InvalidArgumentError(message="请指定变量值标识符")
        self.var_id = kwargs.get("var_id")  # 变量ID
        self.var_type = kwargs.get("var_type")  # 变量类型
        self.value_type = kwargs.get("value_type")  # 变量值类型
        self.value = kwargs.get("value")  # 变量值
        self.value_source = kwargs.get("value_source")  # 值来源
        self.value_identifier = kwargs.get("value_identifier")  # 值标识符
        self.default_value = kwargs.get("default_value")
        self.default_value_type = kwargs.get("default_value_type", 2)   # 绑定的默认值类型
        self.external_content = kwargs.get("external_content")   # 绑定的默认值类型