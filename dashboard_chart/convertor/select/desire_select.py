#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=R0201

"""
    <NAME_EMAIL> 2018/9/13
"""
from base.enums import ChartNumFormulaMode
from dashboard_chart.convertor.select.select_base import SelectBase


class DesireSelect(SelectBase):
    def get_desire_select_for_agg_obj(self):
        from dashboard_chart.convertor.select.agg.desire_select import DesireSelectForAgg

        return DesireSelectForAgg()

    def get_desire_select_for_noagg_obj(self):
        from dashboard_chart.convertor.select.noagg.desire_select import DesireSelectForNoAgg

        return DesireSelectForNoAgg()

    def get_select_fields(self, model):
        """
        获取目标值字段select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_obj = (
            self.get_desire_select_for_agg_obj() if model.aggregation else self.get_desire_select_for_noagg_obj()
        )
        return select_obj.get_select_fields(model)

    def generate_select_field(self, field, model):
        select_field = None
        dataset_field = None
        is_label_dataset = 0
        if field.get("mode") == 0:
            dataset_field, select_field = self.field2base(
                field.get("dataset_field_id"),
                model.dataset_field_dict,
                field.get("formula_mode"),
                model.dataset.get(self.label_dataset_key),
            )
            is_label_dataset = model.dataset.get(self.label_dataset_key)
            select_field.alias = "desire_" + self.get_dataset_field_alias(
                field.get("formula_mode"),
                dataset_field.get("col_name"),
                dataset_field.get("alias"),
                model.dataset.get(self.label_dataset_key),
            )

            # 高级计算字段不支持聚合函数
            expression = select_field.expressions or None
            select_field.expressions = None
            if not field["formula_mode"]:
                select_field.get_field(["ifnull"], [{"field": 0}], expression)
            elif field["formula_mode"] == ChartNumFormulaMode.Distinct.value:
                select_field.get_field(["count", "distinct", "ifnull"], [{"field": 0}], expression)
                # 辅助线需要获取此值
                dataset_field['formula_mode'] = field.get('formula_mode')
            else:
                # 辅助线需要获取此值
                dataset_field['formula_mode'] = field.get('formula_mode')
                select_field.get_field([field["formula_mode"], "ifnull"], [{"field": 0}], expression)
        return select_field, dataset_field, is_label_dataset
