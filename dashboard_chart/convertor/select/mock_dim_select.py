#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/13
"""
from base.enums import FieldValueType, ColTypes, MockField
from dashboard_chart.convertor.field_types import FieldObj
from dashboard_chart.convertor.select.select_base import SelectBase


class MockDimSelect(SelectBase):
    # pylint: disable=W0221
    def get_select_fields(self, model):
        """
        度量获取select数据
        :param dashboard_chart.models.ChartDataModel model:
        :param str alias: 别名
        :return:
        """
        return [self.generate_field()]

    @staticmethod
    def generate_field():
        select_field = FieldObj()
        select_field.value_type = FieldValueType.String.value
        select_field.field = MockField.Dim.value
        select_field.alias = MockField.Dim.value
        select_field.expressions = None
        select_field.logic_source = ColTypes.Dim.value
        return select_field
