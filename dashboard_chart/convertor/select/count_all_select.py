#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/13
"""
from base.enums import ChartNumFormulaMode, FieldValueType, ColTypes
from dashboard_chart.convertor.field_types import FieldObj
from dashboard_chart.convertor.select.select_base import SelectBase


class CountAllSelect(SelectBase):
    # pylint: disable=W0221
    def get_select_fields(self, model, alias="total"):
        """
        度量获取select数据
        :param dashboard_chart.models.ChartDataModel model:
        :param str alias: 别名
        :return:
        """
        return [self.generate_field(alias)]

    def get_count_count_fields(self, total_field_alias="total", alias="count_total"):
        """
        获取sum(total)字段
        :param total_field_alias: total字段别名，默认total
        :param alias: count(total) 字段别名，count_total
        :return:
        """
        return [self.generate_count_count_field(total_field_alias, alias)]

    @staticmethod
    def generate_field(alias):
        select_field = FieldObj()
        select_field.value_type = FieldValueType.String.value
        select_field.field = "*"
        select_field.alias = alias
        expression = select_field.expressions if select_field.expressions else None
        select_field.expressions = None
        select_field.get_field(funcs=[ChartNumFormulaMode.Count.value], expressions=expression)
        select_field.logic_source = ColTypes.Num.value
        return select_field

    @staticmethod
    def generate_count_count_field(total_field_alias, alias):
        sum_count_field = FieldObj()
        sum_count_field.field = total_field_alias
        sum_count_field.alias = alias
        expression = sum_count_field.expressions if sum_count_field.expressions else None
        sum_count_field.expressions = None
        sum_count_field.get_field(funcs=[ChartNumFormulaMode.Count.value], expressions=expression)
        sum_count_field.logic_source = ColTypes.Num.value
        return sum_count_field

    @staticmethod
    def generate_asterisk_field():
        # 生成select *
        select_field = FieldObj()
        select_field.value_type = FieldValueType.String.value
        select_field.field = '*'
        select_field.expressions = None
        select_field.logic_source = ColTypes.Dim.value
        return select_field
