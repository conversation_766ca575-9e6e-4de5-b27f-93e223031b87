#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=R0201

"""
    <NAME_EMAIL> 2018/9/13
"""
from base.enums import DatasetFieldType, ChartNumFormulaMode, ColTypes
from components import functions
from dashboard_chart.convertor.field_types import FieldObj
from dashboard_chart.convertor.select.select_base import SelectBase
from dmplib.utils.errors import UserError


class DimSelect(SelectBase):
    def __init__(self):
        super(DimSelect, self).__init__()
        self.select_fields = []

    def get_dim_select_for_agg_obj(self):
        from dashboard_chart.convertor.select.agg.dim_select import DimSelectForAgg

        return DimSelectForAgg()

    def get_dim_select_for_noagg_obj(self):
        from dashboard_chart.convertor.select.noagg.dim_select import DimSelectForNoAgg

        return DimSelectForNoAgg()

    def get_select_fields(self, model):
        """
        获取维度字段中的select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_obj = self.get_dim_select_for_agg_obj() if model.aggregation else self.get_dim_select_for_noagg_obj()
        return select_obj.get_select_fields(model)

    def dim2select(self, field, dataset_field_dict=None, label_dataset_key=None):
        dataset_field, select_field = self.field2base(
            field.get("dim"), dataset_field_dict, field.get("formula_mode"), label_dataset_key
        )
        select_field.alias = self.get_dataset_field_alias(
            field.get("formula_mode"), dataset_field.get("col_name"), dataset_field.get("alias"), label_dataset_key
        )

        if field.get("type") in [
            DatasetFieldType.Calculate.value,
            DatasetFieldType.Customer.value,
            DatasetFieldType.Indicator.value,
            DatasetFieldType.CalculateIndicator.value,
        ]:
            select_field.field = dataset_field.get("col_name")
        if field.get("formula_mode"):
            # 辅助线需要获取此值
            dataset_field['formula_mode'] = field.get('formula_mode')
            expression = select_field.expressions if select_field.expressions else None
            select_field.expressions = None
            select_field.get_field(
                ["date_format"], [{"field": functions.get_time_formula_mode(field.get("formula_mode"))}], expression
            )
        return dataset_field, select_field

    def get_select_fields_for_assist(self, model):
        """
        筛选器单图获取维度字段中的select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_obj = self.get_dim_select_for_agg_obj() if model.aggregation else self.get_dim_select_for_noagg_obj()
        return select_obj.get_select_fields(model)

    def generate_select_field_for_assist(self, field, model):
        dataset_field, select_field = self.field2base(
            field.get("dim"), model.dataset_field_dict, field.get("formula_mode")
        )
        expression = dataset_field.get("expressions") or None
        select_field.expressions = expression
        select_field.logic_source = ColTypes.Dim.value
        select_field.alias = self.get_dataset_field_alias(
            field.get("formula_mode"),
            dataset_field.get("col_name"),
            dataset_field.get("alias"),
            model.dataset.get(self.label_dataset_key),
        )
        # 不同步标签控件字段名称需要添加formula_mode
        if field.get("formula_mode") and model.dataset.get(self.label_dataset_key):
            col_name = field.get("formula_mode") + "_" + dataset_field.get("col_name")
            select_field.field = col_name
        elif field.get("formula_mode"):
            select_field.field = dataset_field.get("col_name")
            select_field.expressions = None
            select_field.get_field(
                ["date_format"], [{"field": functions.get_time_formula_mode(field.get("formula_mode"))}], expression
            )
        else:
            select_field.field = dataset_field.get("col_name")
        return select_field

    def get_select_fields_for_interval(self, model):
        """
        区间筛选器单图获取维度字段中的select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        # 对聚合进行判断
        if not model.aggregation:
            raise UserError(message="逻辑错误，区间数据必须使用聚合！")
        for field in model.dims:
            dataset_field, min_select_field = self.field2base(
                field.get("dim"), model.dataset_field_dict, field.get("formula_mode")
            )
            min_select_field.expressions = None
            max_select_field = FieldObj(**dataset_field)
            max_select_field.expressions = None
            min_select_field.logic_source = ColTypes.Dim.value
            max_select_field.logic_source = ColTypes.Dim.value
            expressions = dataset_field.get("expressions") or None
            # 不同步标签需要添加formula_mode作为字段前缀
            if field.get("formula_mode") and model.dataset.get(self.label_dataset_key):
                col_name = field.get("formula_mode") + "_" + dataset_field.get("col_name")
                min_select_field.field = col_name
                min_select_field.alias = "{min_func}_{field}".format(
                    min_func=ChartNumFormulaMode.Min.value, field=col_name
                )
                min_select_field.get_field([ChartNumFormulaMode.Min.value], expressions=expressions)

                max_select_field.field = col_name
                max_select_field.alias = "{max_func}_{field}".format(
                    max_func=ChartNumFormulaMode.Max.value, field=col_name
                )
                max_select_field.get_field([ChartNumFormulaMode.Max.value], expressions=expressions)
            else:
                col_name = dataset_field.get("alias") if dataset_field.get("alias") else dataset_field.get("col_name")
                min_select_field.alias = "{min_func}_{field}".format(
                    min_func=ChartNumFormulaMode.Min.value, field=col_name
                )
                min_select_field.get_field([ChartNumFormulaMode.Min.value], expressions=expressions)

                max_select_field.alias = "{max_func}_{field}".format(
                    max_func=ChartNumFormulaMode.Max.value, field=col_name
                )
                max_select_field.get_field([ChartNumFormulaMode.Max.value], expressions=expressions)

            self.select_fields.append(min_select_field)
            self.select_fields.append(max_select_field)

        return self.select_fields

    def get_select_fields_for_data_field(self, model):
        """
        获取维度字段中的select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        return self.get_select_fields(model)

    def generate_select_field_for_data_field(self, field, model):
        dataset_field, select_field = self.field2base(
            field.get("dim"), model.dataset_field_dict, field.get("formula_mode")
        )
        select_field.alias = self.get_dataset_field_alias(
            field.get("formula_mode"), dataset_field.get("col_name"), dataset_field.get("alias")
        )
        return select_field
