#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/13
"""
from base.dmp_constant import EXTERNAL_CUBE_SUBJECT_DETAIL_ALIAS
from dashboard_chart.convertor.field_base import FieldBase
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect
from dashboard_chart.convertor.select.count_all_select import CountAllSelect
from dashboard_chart.convertor.select.desire_select import DesireSelect
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor.select.flag_select import FlagSelect
from dashboard_chart.convertor.select.num_select import NumSelect
from dashboard_chart.convertor.select.subtotal_row_select import SubtotalRowSelect
from dashboard_chart.convertor import utils as ConvertorUtils


class Select:
    @staticmethod
    def get_select_fields(model):
        """
        获取select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        # 当为不同步标签数据集的情况下，不再拼接字段对象，直接返回表里面的所有字段
        if model.dataset.get(FieldBase().label_dataset_key):
            return "*"
        select_fields = []
        # 明细模式flag
        if not model.aggregation and model.external_subject_ids:
            select_fields.extend(Select.get_flag_select_field(model, alias=EXTERNAL_CUBE_SUBJECT_DETAIL_ALIAS))
        if model.dims:
            dim_select = DimSelect()
            select_fields.extend(dim_select.get_select_fields(model))
        if model.nums:
            num_select = NumSelect()
            select_fields.extend(num_select.get_select_fields(model))
        if model.comparisons:
            comparison_select = ComparisonSelect()
            select_fields.extend(comparison_select.get_select_fields(model))
        if model.desires:
            desire_select = DesireSelect()
            select_fields.extend(desire_select.get_select_fields(model))
        # 支持行小计的需要将行小计字段加入到查询字段 (透视表由于需要查询全部的 考虑到性能 在获取实际结果后在进行拼接)
        if model.enable_subtotal_row and not model.comparisons:
            subtotal_row_select = SubtotalRowSelect()
            select_fields.extend(subtotal_row_select.get_select_fields(model))
        # 查找是否有相同的别名 若有相同别名 需要加上后缀进行区分 若 col  col_1 col_2 依次类推
        select_fields = ConvertorUtils.rename_reduplicative_alias(select_fields)
        return select_fields

    @staticmethod
    def get_select_fields_for_assist(model):
        """
        筛选组件类单图获取select字段
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select = DimSelect()
        return select.get_select_fields_for_assist(model)

    @staticmethod
    def get_select_fields_for_interval(model):
        """
        筛选组件类单图获取select字段
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select = DimSelect()
        return select.get_select_fields_for_interval(model)

    @staticmethod
    def get_count_all_select_fields(model, alias="total"):
        """
        拼接 count(*) as total 作为select来查询数据总量
        :param dashboard_chart.models.ChartDataModel model:
        :param str alias:
        :return:
        """
        select = CountAllSelect()
        return select.get_select_fields(model, alias)

    @staticmethod
    def get_asterisk_field():
        """
        生成一个 select * 字段
        :param dashboard_chart.models.ChartDataModel model:
        :param str alias:
        :return:
        """
        select = CountAllSelect()
        return select.generate_asterisk_field()

    @staticmethod
    def get_flag_select_field(model, alias):
        select = FlagSelect()
        return select.get_select_fields(model, alias)

    @staticmethod
    def get_count_count_select_fields(total_alias="total", alias="count_total"):
        """
        拼接 sum(total) as sum_total 作为select查询重量结果
        :param total_alias:
        :param alias:
        :return:
        """
        select = CountAllSelect()
        return select.get_count_count_fields(total_alias, alias)

    @staticmethod
    def get_select_fields_for_dataset_field(model):
        """
        拼接 count(*) as total 作为select来查询数据总量
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_fields = []
        if model.dims:
            dim_select = DimSelect()
            select_fields.extend(dim_select.get_select_fields_for_data_field(model))
        if model.chart_code == "treegrid" and model.nums:
            num_select = NumSelect()
            select_fields.extend(num_select.get_select_fields_for_data_field(model))
        return select_fields

    @staticmethod
    def get_select_fields_for_label_filter(model):
        """
        筛选组件类单图获取select字段
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select = DimSelect()
        return select.get_select_fields(model)
