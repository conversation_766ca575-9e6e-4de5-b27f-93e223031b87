#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
# pylint:disable=R0201,E0402

from abc import ABCMeta, abstractmethod

from base.enums import MapChartGeog
from dashboard_chart.convertor.field_base import FieldBase
from dashboard_chart.convertor.field_types import FieldObj


class SelectBase(FieldBase):
    __metaclass__ = ABCMeta

    @abstractmethod
    def get_select_fields(self, model):
        """
        获取select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        raise NotImplementedError("Please Implement this method")

    def get_address_num_select_fields(self, field, dataset_field, is_label_dataset=False):
        """
        获取地址度量字段的7个细分明细字段（经度，维度，country、province、city、district、street）
        :param field: 传入的num或者dim字段数据
        :param dataset_field: dataset_field表中根据字段id查询到的字段数据
        :param is_label_dataset: 是否不同步标签数据集
        :return:
        """
        select_fields = []
        for suffix in [e.value for e in MapChartGeog.__members__.values()]:
            select_field = FieldObj(**dataset_field)
            col_name = dataset_field.get("col_name") + "_" + suffix
            select_field.alias = self.get_dataset_field_alias(
                self.get_func_of_advanced_compute_method(field.get("formula_mode")),
                col_name,
                dataset_field.get("alias"),
                is_label_dataset,
            )
            select_field.field = col_name
            expression = select_field.expressions if select_field.expressions else None
            select_field.expressions = None
            select_field.get_field(funcs=[field["formula_mode"]], expressions=expression)
            select_fields.append(select_field)
        return select_fields

    @staticmethod
    def get_address_dim_select_fields(dataset_field):
        """
        获取地址维度字段的7个细分明细字段（经度，维度，country、province、city、district、street）
        :param dataset_field: dataset_field表中根据字段id查询到的字段数据
        :return:
        """
        select_fields = []
        for suffix in [e.value for e in MapChartGeog.__members__.values()]:
            select_field = FieldObj(**dataset_field)
            alias_name = dataset_field.get("alias") if dataset_field.get("alias") else dataset_field.get("col_name")
            select_field.alias = alias_name + "_" + suffix
            select_field.field = dataset_field.get("col_name") + "_" + suffix
            select_fields.append(select_field)
        return select_fields

    def field2base(self, field_id, dataset_field_dict, formula_mode, label_dataset_key=None):
        dataset_field = self.get_dataset_field(field_id, dataset_field_dict, formula_mode, label_dataset_key)
        return dataset_field, FieldObj(**dataset_field)
