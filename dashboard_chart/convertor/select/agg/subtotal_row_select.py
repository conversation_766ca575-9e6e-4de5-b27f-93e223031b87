#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/6 18:55
# <AUTHOR> caoxl(cxl)
# <AUTHOR> caoxl(cxl)
# @File     : subtotal_row_select.py
import copy
from typing import List
from dashboard_chart.models import ChartDataModel
from ..subtotal_row_select import SubtotalRowSelect
from base.enums import ChartNumSubtotalFormulaMode, SubtotalRowAliasPrefix, DatasetFieldType, ColTypes


class SubtotalRowSelectForAgg(SubtotalRowSelect):
    def get_select_fields(self, model: ChartDataModel) -> List[object]:
        return [*self.get_subtotal_select_fields(model), *self.get_summary_select_fields(model)]

    def get_summary_select_fields(self, model: ChartDataModel) -> List[object]:
        """
        获取小计汇总字段
        :param model:
        :return:
        """
        fields = []
        if not model.enable_subtotal_row_summary or not model.subtotal_row_summary_formula_mode:
            return fields
        # 小计行汇总 则必须将单图所有num获取到
        for num in model.nums:
            # 过滤掉隐藏字段
            if num.get("formula_mode") == ChartNumSubtotalFormulaMode.GroupConcat.value:
                continue
            summary_num = copy.deepcopy(num)
            # 如果字段是高级聚合字段 则不需要加计算方法
            num_info = model.dataset_field_dict.get(num.get("num"))
            formula_mode = model.subtotal_row_summary_formula_mode
            if num_info and num_info.get("type") in [
                DatasetFieldType.Calculate.value,
                DatasetFieldType.CalculateIndicator.value,
            ]:
                formula_mode = ''
            summary_num['formula_mode'] = formula_mode
            field = self.num2select(summary_num, model, SubtotalRowAliasPrefix.Summary.value)
            field.logic_source = ColTypes.Num.value
            fields.append(field)
        return fields

    def get_subtotal_select_fields(self, model: ChartDataModel) -> List[object]:
        """
        获取小计字段
        :param model:
        :return:
        """
        fields = []
        if not model.enable_subtotal_row:
            return fields
        for num in model.nums:
            subtotal_row_formula_mode = num.get("subtotal_row_formula_mode")
            if subtotal_row_formula_mode == ChartNumSubtotalFormulaMode.No.value:
                continue
            if num.get("formula_mode") == ChartNumSubtotalFormulaMode.GroupConcat.value:
                continue
            subtotal_num = copy.deepcopy(num)
            if subtotal_row_formula_mode in [ChartNumSubtotalFormulaMode.Auto.value]:
                subtotal_num['formula_mode'] = ''
            else:
                subtotal_num['formula_mode'] = subtotal_row_formula_mode
            field = self.num2select(subtotal_num, model, SubtotalRowAliasPrefix.Row.value)
            field.logic_source = ColTypes.Num.value
            fields.append(field)
        return fields
