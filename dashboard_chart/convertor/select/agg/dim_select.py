#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 17:28
# <AUTHOR> caoxl
# @File     : dim_select.py
from dashboard_chart.convertor.select.dim_select import DimSelect
from dashboard_chart.convertor import utils as ConvertorUtils
from base.enums import DatasetFieldDataType, ColTypes


class DimSelectForAgg(DimSelect):
    def get_select_fields(self, model):
        """
        获取维度字段中的select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        for field in model.dims:
            dataset_field, select_field = self.dim2select(
                field, model.dataset_field_dict, model.dataset.get(self.label_dataset_key)
            )
            select_field.logic_source = ColTypes.Dim.value
            self.select_fields.append(select_field)
            # 地址字段返回7个细分字段
            if field.get("data_type") == DatasetFieldDataType.Address.value:
                address_fields = self.get_address_dim_select_fields(dataset_field)
                self.select_fields.extend(ConvertorUtils.assign_logic_source(address_fields, ColTypes.Dim.value))
        return self.select_fields

    def get_select_fields_for_assist(self, model):
        for field in model.dims:
            select_field = self.generate_select_field_for_assist(field, model)
            select_field.logic_source = ColTypes.Dim.value
            self.select_fields.append(select_field)
        return self.select_fields

    def get_select_fields_for_data_field(self, model):
        """
        获取维度字段中的select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        for field in model.dims:
            select_field = self.generate_select_field_for_data_field(field, model)
            select_field.logic_source = ColTypes.Dim.value
            self.select_fields.append(select_field)
        return self.select_fields
