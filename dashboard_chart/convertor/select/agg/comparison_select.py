#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 17:07
# <AUTHOR> caoxl
# @File     : comparison_select.py
from base.enums import ColTypes
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect


class ComparisonSelectForAgg(ComparisonSelect):
    def get_select_fields(self, model):
        """
        获取对比字段select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_fields = []
        for field in model.comparisons:
            select_field = self.generate_select_field(field, model)
            select_field.logic_source = ColTypes.Comparison.value
            select_fields.append(select_field)
        return select_fields
