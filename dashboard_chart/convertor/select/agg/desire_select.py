#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 17:17
# <AUTHOR> caoxl
# @File     : desire_select.py
from dashboard_chart.convertor.select.desire_select import DesireSelect
from base.enums import DatasetFieldDataType, ColTypes
from dashboard_chart.convertor import utils as ConverterUtils


class DesireSelectForAgg(DesireSelect):
    def get_select_fields(self, model):
        """
        获取目标值字段select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_fields = []
        for field in model.desires:
            select_field, dataset_field, is_label_dataset = self.generate_select_field(field, model)
            # select_field为None的情况下不需要 append
            if not select_field:
                continue
            select_field.logic_source = ColTypes.Desire.value
            select_fields.append(select_field)
            # 地址字段返回7个细分字段
            if field.get("data_type") == DatasetFieldDataType.Address.value:
                address_fields = self.get_address_num_select_fields(field, dataset_field, is_label_dataset)
                select_fields.extend(ConverterUtils.assign_logic_source(address_fields, ColTypes.Desire.value))
        return select_fields
