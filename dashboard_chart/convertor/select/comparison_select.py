#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint:disable=R0201
"""
    <NAME_EMAIL> 2018/9/13
"""
from components import functions
from typing import Dict
from dashboard_chart.convertor.select.select_base import SelectBase
from dashboard_chart.convertor.field_types import FieldObj


class ComparisonSelect(SelectBase):
    def get_comparison_select_for_agg_obj(self):
        from dashboard_chart.convertor.select.agg.comparison_select import ComparisonSelectForAgg

        return ComparisonSelectForAgg()

    def get_comparison_select_for_noagg_obj(self):
        from dashboard_chart.convertor.select.noagg.comparison_select import ComparisonSelectForNoAgg

        return ComparisonSelectForNoAgg()

    def get_select_fields(self, model) -> list:
        """
        获取对比字段select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_obj = (
            self.get_comparison_select_for_agg_obj()
            if model.aggregation
            else self.get_comparison_select_for_noagg_obj()
        )
        return select_obj.get_select_fields(model)

    def generate_select_field(self, field, model):
        dataset_field, select_field = self.field2base(
            field.get("dataset_field_id"),
            model.dataset_field_dict,
            field.get("formula_mode"),
            model.dataset.get(self.label_dataset_key),
        )
        if field.get("formula_mode"):
            # 辅助线需要获取此值
            dataset_field['formula_mode'] = field.get('formula_mode')
            expression = select_field.expressions if select_field.expressions else None
            select_field.expressions = None
            select_field.get_field(
                ["date_format"], [{"field": functions.get_time_formula_mode(field.get("formula_mode"))}], expression
            )
        select_field.alias = self.get_dataset_field_alias(
            field.get("formula_mode"),
            dataset_field.get("col_name"),
            dataset_field.get("alias"),
            model.dataset.get(self.label_dataset_key),
        )
        return select_field

    def comparison2select(
        self, field: Dict, dataset_field_dict: Dict = None, label_dataset_key: bool = None
    ) -> [Dict, FieldObj]:
        dataset_field, select_field = self.field2base(
            field.get("dataset_field_id"), dataset_field_dict, field.get("formula_mode"), label_dataset_key
        )
        if field.get("formula_mode"):
            # 辅助线需要获取此值
            dataset_field['formula_mode'] = field.get('formula_mode')
            expression = select_field.expressions if select_field.expressions else None
            select_field.expressions = None
            select_field.get_field(
                ["date_format"], [{"field": functions.get_time_formula_mode(field.get("formula_mode"))}], expression
            )
        select_field.alias = self.get_dataset_field_alias(
            field.get("formula_mode"), dataset_field.get("col_name"), dataset_field.get("alias"), label_dataset_key
        )
        return dataset_field, select_field
