#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 17:14
# <AUTHOR> caoxl
# @File     : comparison_select.py
from base.enums import ColTypes
from dashboard_chart.convertor.select.comparison_select import ComparisonSelect
from dashboard_chart.convertor import utils as ConvertorUtils
from dmplib.utils.errors import UserError


class ComparisonSelectForNoAgg(ComparisonSelect):
    def get_select_fields(self, model):
        """
        获取对比字段select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_fields = []
        for field in model.comparisons:
            # 对聚合进行判断
            if not model.aggregation and ConvertorUtils.is_aggregation(field, 'formula_mode'):
                raise UserError(message="非法参数，非聚合状态下对比维度不能使用聚合函数！")
            select_field = self.generate_select_field(field, model)
            select_field.logic_source = ColTypes.Comparison.value
            select_fields.append(select_field)
        return select_fields
