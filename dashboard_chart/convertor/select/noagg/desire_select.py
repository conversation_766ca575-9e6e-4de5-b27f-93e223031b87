#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 17:24
# <AUTHOR> caoxl
# @File     : desire_select.py
# pylint:disable=R0401

from dashboard_chart.convertor.select.desire_select import DesireSelect
from base.enums import DatasetFieldDataType, ColTypes
from dashboard_chart.convertor import utils as ConvertorUtils


class DesireSelectForNoAgg(DesireSelect):
    def get_select_fields(self, model):
        """
        获取目标值字段select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_fields = []
        for field in model.desires:
            # 对聚合进行判断
            if not model.aggregation and ConvertorUtils.is_aggregation(field, 'formula_mode'):
                field["formula_mode"] = ""
            select_field, dataset_field, is_label_dataset = self.generate_select_field(field, model)
            if not select_field:
                continue
            select_field.logic_source = ColTypes.Desire.value
            select_fields.append(select_field)
            # 地址字段返回7个细分字段
            if field.get("data_type") == DatasetFieldDataType.Address.value:
                address_fields = self.get_address_num_select_fields(field, dataset_field, is_label_dataset)
                select_fields.extend(ConvertorUtils.assign_logic_source(address_fields, ColTypes.Desire.value))
        return select_fields
