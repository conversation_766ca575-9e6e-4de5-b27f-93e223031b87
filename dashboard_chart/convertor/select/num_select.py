#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> 2018/9/13
"""
from base.enums import ChartNumFormulaMode, FieldValueType
from dashboard_chart.convertor.select.select_base import SelectBase


class NumSelect(SelectBase):
    def get_num_select_for_agg_obj(self):
        from dashboard_chart.convertor.select.agg.num_select import NumSelectForAgg

        return NumSelectForAgg()

    def get_num_select_for_noagg_obj(self):
        from dashboard_chart.convertor.select.noagg.num_select import NumSelectForNoAgg

        return NumSelectForNoAgg()

    def get_select_fields(self, model):
        """
        度量获取select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select_obj = self.get_num_select_for_agg_obj() if model.aggregation else self.get_num_select_for_noagg_obj()
        return select_obj.get_select_fields(model)

    def num2select(self, field, dataset_field_dict=None, label_dataset_key=None):
        dataset_field, select_field = self.field2base(
            field.get("num"), dataset_field_dict, field.get("formula_mode"), label_dataset_key
        )

        is_label_dataset = label_dataset_key
        select_field.value_type = FieldValueType.Column.value
        select_field.alias = self.get_dataset_field_alias(
            field.get("formula_mode"), dataset_field.get("col_name"), dataset_field.get("alias"), is_label_dataset
        )

        # 高级计算字段不支持聚合函数
        expression = select_field.expressions or None
        # 这类属于高级字段中的计算高级字段情况
        if not field.get("formula_mode") and select_field.expressions:
            select_field.alias = dataset_field["col_name"]
        elif field.get("formula_mode") == ChartNumFormulaMode.Distinct.value:  # distinct 特殊情况
            select_field.expressions = None
            funcs, args = self.add_ifnull_to_funcs(field, ["count", "distinct"], [])
            select_field.get_field(funcs, args, expression)
            # 辅助线需要获取此值
            dataset_field['formula_mode'] = field.get('formula_mode')
        else:
            select_field.expressions = None
            select_field.field = dataset_field.get("col_name")
            # 需要对高级计算方法进行转换
            funcs, args = self._get_funcs_and_args(field)
            select_field.get_field(funcs=funcs, args=args, expressions=expression)
            # 辅助线需要获取此值
            dataset_field["formula_mode"] = field.get("formula_mode")
        return dataset_field, select_field

    def _get_funcs_and_args(self, field):
        # 高级计算 数据库查询的函数为原始函数，但是字段别名需要变成真实的别名
        real_formula_mode = self.get_func_of_advanced_compute_method(field.get("formula_mode"))
        funcs, args = self.add_ifnull_to_funcs(field, [real_formula_mode], [])
        return funcs, args

    def get_select_fields_for_data_field(self, model):
        """
        获取数值字段中的select数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        return self.get_select_fields(model)
