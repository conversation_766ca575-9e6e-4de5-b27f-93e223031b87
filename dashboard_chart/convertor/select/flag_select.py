#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/13
"""
from base.enums import ChartNumFormulaMode, FieldValueType, ColTypes
from dashboard_chart.convertor.field_types import FieldObj
from dashboard_chart.convertor.select.select_base import SelectBase


class FlagSelect(SelectBase):
    """用作自助分析标识"""
    # pylint: disable=W0221
    def get_select_fields(self, model, alias=''):
        """
        :param dashboard_chart.models.ChartDataModel model:
        :param str alias: 别名
        :return:
        """
        return [self.generate_field(alias)]

    @staticmethod
    def generate_field(alias):
        select_field = FieldObj()
        select_field.value_type = FieldValueType.String.value
        select_field.field = "*"
        select_field.alias = alias
        expression = select_field.expressions if select_field.expressions else None
        select_field.expressions = None
        select_field.get_field(funcs=[ChartNumFormulaMode.Count.value], expressions=expression)
        select_field.logic_source = ColTypes.Num.value
        return select_field
