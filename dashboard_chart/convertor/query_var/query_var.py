#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/3/29 14:26
# <AUTHOR> caoxl
# @File     : dataset_var.py
from dashboard_chart.convertor.query_vars import DatasetVar
from dmplib.utils.errors import UserError


class QueryVar:
    """
    数据集变量
    """

    @staticmethod
    def get_dataset_vars(model):
        var_list = []
        if not model.query_vars:
            return []
        for var in model.query_vars:
            QueryVar.get_indirect_condition_value(model, var)
            var_list.append(DatasetVar(**var))
        return var_list

    @staticmethod
    def get_direct_dataset_vars(model):
        var_list = []
        if not model.query_vars:
            return []
        for var in model.query_vars:
            if var["value"] is None and var.get("value_from"):
                continue
            var_list.append(DatasetVar(**var))
        return var_list

    @staticmethod
    def get_indirect_condition_value(model, query_var):
        """
        获取间接查询条件的值
        :param model:
        :param query_var:
        :return:
        """
        if query_var["value"] is None and query_var.get("value_from"):
            chart_id = query_var.get("chart_initiator_id")
            dataset_field_id = query_var.get("field_initiator_id")
            indirect_data = model.indirect_query_map.get(chart_id)
            if not indirect_data:
                raise UserError(message="无法获取到间接查询单图 {chart_id} 条件值".format(chart_id=chart_id))
            # 转换获取变量对应的字段信息
            initiator_field_info = indirect_data["dataset_field_dict"].get(dataset_field_id)
            if not initiator_field_info:
                raise UserError(message="无法获取到间接查询关联字段 {dataset_field_id} 信息".format(dataset_field_id=dataset_field_id))
            result = indirect_data.get("result")
            data_key = initiator_field_info.get("alias") or initiator_field_info.get("col_name")
            if isinstance(result, dict):
                col_data = result.get(data_key) or {}
                query_var["value"] = col_data.get("data", [])
            else:
                query_var["value"] = []
            if not query_var["value"]:
                raise UserError(message="变量 {var_id} 值为空 ".format(var_id=query_var["var_id"]))
