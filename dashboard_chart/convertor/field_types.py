#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/9/25 15:57
# <AUTHOR> caoxl
# @File     : field_types.py
import copy

from base.enums import SqlWhereOperator
from dmplib.utils.errors import InvalidArgumentError
from base.enums import FieldValueType


class BaseFieldObj:
    def get_dict(self, attributes=None):
        """
        获取属性字典
        注：当申明__slots__之后 self.__dict__将为空， 必须使用dir
        :param list attributes:
        :return dict:
        """
        attr_dict = {}
        dirs = dir(self)
        if attributes:
            dirs = list(set(dirs).intersection(set(attributes)))
        for attribute in dirs:
            if attribute[0:1] == '_':
                continue
            value = getattr(self, attribute)
            if callable(value):
                continue
            attr_dict[attribute] = value
        return attr_dict


class FieldObj(BaseFieldObj):
    """
    字段父类
    field：字段名
    table：表名
    value_type：column, string, number, null, datetime  field值类型
    field_func：字段函数
    alias：别名
    is_senior_field：是否高级字段
    expression：高级字段表达式
    props: 函数的参数
    ext_data: 扩展数据(用于业务扩展数据)
    logic_source: 业务来源 （维度 度量 目标值 对比维度 等）
    """

    __slots__ = [
        "field",
        "table",
        "value_type",
        "field_func",
        "field_type",
        "alias",
        "expressions",
        "props",
        "field_ref",
        "ext_data",
        "logic_source",
    ]

    def __init__(self, **kwargs):
        self.field = kwargs.get("col_name")  # 默认字段名称
        self.table = kwargs.get("table_name")  # 默认表名称
        self.value_type = FieldValueType.Column.value  # 默认字段值的类型为字段
        self.field_func = kwargs.get("field_func")
        self.alias = None
        self.field_type = kwargs.get("type")  # 默认字段类型
        self.expressions = kwargs.get("expressions")
        self.props = kwargs.get("props") or []
        # 数据集字段id引用：用于高级字段、分组字段
        self.field_ref = kwargs.get("id")  # 默认字段id
        self.ext_data = None  # 业务扩展数据使用 若无不需要给值
        self.logic_source = None  # 业务来源 如 Dim Num Desire Comparion 等 原则上对该属性进行标识 用于辨别业务来源出处 用于辨别相同字段情况下业务出处

    def get_field(self, funcs, args=None, expressions=None):
        """
        返回field_obj对象数据结构
        :param list funcs: 函数列表，如: [ifnull, date_format, max]
        :param list args: 额外参数 如: [{'field': "%Y-%m-%d"}, {'field': 0}]
        :return: FieldObj
        """
        data = self.recursion_create_field(funcs, args, expressions=expressions)
        if data and isinstance(data, tuple):
            for i in list(data):
                self.props.append(i)
        else:
            self.props.append(data)

        return self

    @staticmethod
    def _op_special_func(func, args, field_model):
        """
        处理特殊函数
        :param func:
        :param args:
        :param field_model:
        :return:
        """
        new_field_model = None
        if func in ("date_format", "ifnull"):
            extra = args.pop(0)
            new_field_model = copy.deepcopy(field_model)
            new_field_model.alias = ""
            new_field_model.field = extra.get("field")
            new_field_model.expressions = None
            new_field_model.field_func = extra.get("field_func") if extra.get("field_func") else ""
            new_field_model.value_type = extra.get("value_type") if extra.get("value_type") else "string"
        return new_field_model

    @staticmethod
    def _op_field_model(result, field_model, expressions):
        if result:
            if isinstance(result, tuple):
                for i in list(result):
                    field_model.props.append(i)
            else:
                field_model.props.append(result)
        else:
            if expressions and field_model.field:
                field_model.expressions = expressions
        return field_model

    def recursion_create_field(self, funcs, args=None, field_model=None, expressions=None):
        """
        通过递归方式返回field_obj对象数据结构
        :param list funcs: 函数列表，如: [ifnull, date_format, max]
        :param list args: 额外参数 如: [{'field': "%Y-%m-%d"}, {'field': 0}]
        :param FieldObj field_model: FieldObj对象
        :return:
        """
        field_model = field_model or self
        new_field_model = copy.deepcopy(field_model)
        new_field_model.alias = ""
        new_field_model.expressions = None
        if funcs and isinstance(funcs, list):
            func = funcs.pop(0)
            new_field_model1 = self._op_special_func(func, args, field_model)
            field_model.field_func = func
            item = self.recursion_create_field(funcs, args, new_field_model, expressions)
            new_field_model = self._op_field_model(item, new_field_model, expressions)
            if new_field_model1:
                return new_field_model, new_field_model1

            return new_field_model


class GroupField(FieldObj):
    pass


class OrderField(FieldObj):
    __slots__ = ["field_sort"]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.field_sort = kwargs.get("field_sort")

        if not self.field_sort:
            self.field_sort = ""

        if self.field_sort.upper() not in ["ASC", "DESC", ""]:
            raise InvalidArgumentError(message="参数指定排序方式不支持")


class OrderCustomField(FieldObj):
    __slots__ = ["items", "col", "field_sort"]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.field_sort = kwargs.get("field_sort") if kwargs.get("field_sort") else []

        if not self.field_sort:
            self.field_sort = ""

        if self.field_sort.upper() not in ["ASC", "DESC", "", "CUSTOM"]:
            raise InvalidArgumentError(message="参数指定排序方式不支持")
        self.items = kwargs.get("items")


class LimitField(BaseFieldObj):
    __slots__ = ["offset", "limit"]

    def __init__(self, **kwargs):
        self.offset = kwargs.get("offset")
        self.limit = kwargs.get("limit")

        if not self.offset:
            self.offset = 0


class WhereField(FieldObj):
    __slots__ = ["field_value", "operator", "complex", "complex_logic", "logic", "dn"]

    def __init__(self, **kwargs):
        """
        :param col_type:字段类型
        :param col_name:字段名称
        :param operator:操作符 <,>,=,!=,<=,>=,between,in,not in,far,like,far_yesterday,from_week,from_month,from_year,
                from_quarter
        :param field_value:字段值(字段对象)
        :param complex:逻辑嵌套
        :param complex_logic:逻辑嵌套关系 AND|OR
        """
        super().__init__(**kwargs)
        self.dn = kwargs.get("dn")
        self.operator = kwargs.get("operator")

        self.complex = kwargs.get("complex") if kwargs.get("complex") else []
        if not kwargs.get("complex_logic"):
            self.complex_logic = "AND"
        else:
            if kwargs.get("complex_logic").upper() not in ["AND", "OR", "XOR"]:
                raise InvalidArgumentError(message="参数指定逻辑操作符不支持")
            self.complex_logic = kwargs.get("complex_logic")

        self.logic = kwargs.get("logic")

        if kwargs.get("field_value") and not isinstance(kwargs.get("field_value"), FieldObj):
            raise InvalidArgumentError(message="条件值数据格式不正确")
        self.field_value = kwargs.get("field_value")

        if not kwargs.get("logic"):
            self.logic = " AND "
        else:
            if kwargs.get("logic").upper() not in ["AND", "OR", "XOR"]:
                raise InvalidArgumentError(message="参数指定逻辑操作符不支持")
            self.logic = kwargs.get("logic")

        if kwargs.get("complex"):
            self.complex = kwargs.get("complex")
        else:
            self.complex = None

        if kwargs.get("operator") and kwargs.get("operator").upper() not in [
            e.value for e in SqlWhereOperator.__members__.values()
        ]:
            raise InvalidArgumentError(message="参数指定操作方法不支持")
