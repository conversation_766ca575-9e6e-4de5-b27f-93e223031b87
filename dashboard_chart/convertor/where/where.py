#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/21
"""
# pylint: disable=R0201
from dashboard_chart.formatter.common_datetime_conditions import CommonDatetimeFormatter
from dashboard_chart.formatter.drill_formatter import DrillFormatter
from dashboard_chart.formatter.formatter import Formatter
from dashboard_chart.formatter.chart_filter_formatter import ChartFilterFormatter
from dashboard_chart.formatter.chart_linkage_formatter import ChartLinkageFormatter
from dashboard_chart.formatter.chart_penetrate_filter_formatter import ChartPenetrateFilterFormatter
from dashboard_chart.formatter.chart_penetrate_formatter import ChartPenetrateFormatter
from dashboard_chart.formatter.component_filter_formatter import Component<PERSON>ilterFormatter
from dashboard_chart.formatter.dashboard_filter_formatter import DashboardFilterFormatter
from dashboard_chart.formatter.dashboard_jump_formatter import DashboardJumpFormatter
from dashboard_chart.formatter.dashboard_var_jump_formatter import DashboardVarJumpFormatter
from dashboard_chart.formatter.new_chart_linkage_formatter import NewChart<PERSON>inkage<PERSON>ormatter
from dashboard_chart.formatter.new_chart_filter_formatter import NewChartFilterFormatter
from dashboard_chart.formatter.label_filter_condition_formatter import LabelFilterFormatter
from dashboard_chart.formatter.self_service_jump_formatter import SelfServiceJumpFormatter
from dashboard_chart.models import ChartDataModel


class Where:
    def _op_filters(self, model, where_fields):
        if hasattr(model, "filters") and model.filters:
            where_fields.extend(ChartFilterFormatter(model).conditions())
        return where_fields

    def _op_conditions(self, model, where_fields):
        if hasattr(model, "conditions") and model.conditions:
            where_fields.extend(ChartLinkageFormatter(model).conditions())
        return where_fields

    def _op_penetrate_conditions(self, model, where_fields):
        if hasattr(model, "penetrate_conditions") and model.penetrate_conditions:
            where_fields.extend(ChartPenetrateFormatter(model).conditions())
        return where_fields

    def _op_penetrate_filter_conditions(self, model, where_fields):
        if hasattr(model, "penetrate_filter_conditions") and model.penetrate_filter_conditions:
            where_fields.extend(ChartPenetrateFilterFormatter(model).conditions())
        return where_fields

    def _op_filter_conditions(self, model, where_fields):
        if hasattr(model, "filter_conditions") and model.filter_conditions:
            where_fields.extend(ComponentFilterFormatter(model).conditions())
        return where_fields

    def _op_dashboard_filters(self, model, where_fields):
        if hasattr(model, "dashboard_filters") and model.dashboard_filters:
            where_fields.extend(DashboardFilterFormatter(model).conditions())
        return where_fields

    def _op_dashboard_conditions(self, model, where_fields):
        if hasattr(model, "dashboard_conditions") and model.dashboard_conditions:
            where_fields.extend(DashboardJumpFormatter(model).conditions())
        return where_fields

    def _op_dashboard_var_filters(self, model, where_fields):
        if hasattr(model, "dashboard_conditions") and model.dashboard_conditions:
            where_fields.extend(DashboardVarJumpFormatter(model).conditions())
        return where_fields

    def _op_chart_linkage_conditions(self, model, where_fields):
        if hasattr(model, "chart_linkage_conditions") and model.chart_linkage_conditions:
            where_fields.extend(NewChartLinkageFormatter(model).conditions())
        return where_fields

    def _op_chart_filter_conditions(self, model, where_fields):
        if hasattr(model, "chart_filter_conditions") and model.chart_filter_conditions:
            where_fields.extend(NewChartFilterFormatter(model).conditions())
        return where_fields

    def _op_drill_conditions(self, model: ChartDataModel, where_fields):
        if hasattr(model, "drill_conditions") and model.drill_conditions:
            where_fields.extend(DrillFormatter(model).conditions())
        return where_fields

    def _op_label_filter_conditions(self, model, where_fields):
        if hasattr(model, "label_filter_conditions") and model.label_filter_conditions:
            where_fields.extend(LabelFilterFormatter(model).conditions())
        return where_fields

    def _op_self_service_conditions(self, model, where_fields):
        if hasattr(model, "self_service_conditions") and model.self_service_conditions:
            where_fields.extend(SelfServiceJumpFormatter(model).conditions())
        return where_fields

    def _op_common_datetime_conditions(self, model, where_fields):
        if hasattr(model, "common_datetime_conditions") and model.common_datetime_conditions:
            where_fields.extend(CommonDatetimeFormatter(model).conditions())
        return where_fields

    def get_where_fields(self, model: ChartDataModel):
        """
        获取where数据
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        where_fields = list()
        where_fields = self._op_filters(model, where_fields)
        where_fields = self._op_conditions(model, where_fields)
        where_fields = self._op_penetrate_conditions(model, where_fields)
        where_fields = self._op_penetrate_filter_conditions(model, where_fields)
        where_fields = self._op_filter_conditions(model, where_fields)
        where_fields = self._op_dashboard_filters(model, where_fields)
        where_fields = self._op_dashboard_conditions(model, where_fields)
        where_fields = self._op_dashboard_var_filters(model, where_fields)
        where_fields = self._op_chart_linkage_conditions(model, where_fields)
        where_fields = self._op_chart_filter_conditions(model, where_fields)
        where_fields = self._op_label_filter_conditions(model, where_fields)
        where_fields = self._op_drill_conditions(model, where_fields)
        where_fields = self._op_self_service_conditions(model, where_fields)
        where_fields = self._op_common_datetime_conditions(model, where_fields)
        return where_fields

    @staticmethod
    def field2where(field, operator, value, chart_data_model):
        formatter = Formatter(chart_data_model)
        formula_mode = field.get('formula_mode')
        formula_result_dict = formatter.deal_with_formula_params(formula_mode, field.get("data_type"), operator, value)
        dim_data = dict(
            alias_name=field.get('alias_name'),
            col_name=field.get('col_name'),
            col_value=formula_result_dict.get("col_value"),
            operator=formula_result_dict.get('operator'),
            dataset_field_id=field.get('dataset_field_id'),
            data_type=field.get("data_type"),
            format=field.get('format'),
            formula_mode=formula_mode,
        )
        return formatter.generate_where_field(
            **{
                'field_value': formula_result_dict.get("col_value"),
                'formula_result_dict': formula_result_dict,
                'cur_dataset_field_data': chart_data_model.dataset_field_dict.get(field.get('dataset_field_id')),
                'dn': dim_data,
            }
        )
