#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
from base.enums import DatasetFieldType
from components import functions
from dashboard_chart.convertor.group.group_base import GroupBase
from dmplib.utils.errors import UserError


class DimGroup(GroupBase):
    def __init__(self):
        super(DimGroup, self).__init__()
        self.group_fields = []

    def get_group_fields(self, model):
        """
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        for dim in model.dims:
            dataset_field, group_field = self.dim2group(
                dim, model.dataset_field_dict, dim.get("formula_mode"), model.dataset.get(self.label_dataset_key)
            )

            # 高级计算字段不支持聚合函数
            if dataset_field.get("type") == DatasetFieldType.Calculate.value:
                raise UserError(code=509, message="维度中高级计算字段不支持聚合函数。")
            if dataset_field.get("type") == DatasetFieldType.CalculateIndicator.value:
                raise UserError(code=509, message="计算指标字段不支持聚合函数。")

            self.group_fields.append(group_field)
        return self.group_fields

    def get_group_fields_for_assist(self, model):
        """
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        for field in model.dims:
            dataset_field, group_field = self.field2base(
                field.get("dim"), model.dataset_field_dict, field.get("formula_mode")
            )
            # 不同步标签控件字段名称需要添加formula_mode
            if field.get("formula_mode"):
                if model.dataset.get(self.label_dataset_key):
                    col_name = field.get("formula_mode") + "_" + dataset_field.get("col_name")
                    group_field.field = col_name
                else:
                    expression = group_field.expressions or None
                    group_field.expressions = None
                    group_field.get_field(
                        ['date_format'],
                        [{'field': functions.get_time_formula_mode(field.get("formula_mode"))}],
                        expression,
                    )
            self.group_fields.append(group_field)
        return self.group_fields

    def dim2group(self, dim, dataset_field_dict=None, formula_mode=None, label_dataset_key=None):
        dataset_field, group_field = self.field2base(
            dim.get("dim"), dataset_field_dict, formula_mode, label_dataset_key
        )
        if dim.get("formula_mode"):
            expression = group_field.expressions or None
            group_field.expressions = None
            group_field.get_field(
                ['date_format'], [{'field': functions.get_time_formula_mode(dim.get("formula_mode"))}], expression
            )
        return dataset_field, group_field

    def get_group_fields_for_data_field(self, model):
        """
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        for field in model.dims:
            _, group_field = self.field2base(field.get("dim"), model.dataset_field_dict, field.get("formula_mode"))
            self.group_fields.append(group_field)
        return self.group_fields
