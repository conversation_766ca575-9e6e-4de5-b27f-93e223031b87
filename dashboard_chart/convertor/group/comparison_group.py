#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
from base.enums import DatasetFieldType
from components import functions
from dashboard_chart.convertor.group.group_base import GroupBase
from dashboard_chart.convertor.field_types import Group<PERSON>ield
from dmplib.utils.errors import UserError
from typing import Dict


class ComparisonGroup(GroupBase):
    def get_group_fields(self, model):
        """
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        group_fields = []
        for comparison in model.comparisons:
            dataset_field, group_field = self.field2base(
                comparison.get("dataset_field_id"),
                model.dataset_field_dict,
                comparison.get("formula_mode"),
                model.dataset.get(self.label_dataset_key),
            )

            # 高级计算字段不支持聚合函数
            if dataset_field.get("type") == DatasetFieldType.Calculate.value:
                raise UserError(code=509, message="维度中高级计算字段不支持聚合函数。")
            if dataset_field.get("type") == DatasetFieldType.CalculateIndicator.value:
                raise UserError(code=509, message="高级计算指标字段不支持聚合函数。")

            if comparison.get("formula_mode"):
                expression = group_field.expressions if group_field.expressions else None
                group_field.expressions = None
                group_field.get_field(
                    ['date_format'],
                    [{'field': functions.get_time_formula_mode(comparison.get("formula_mode"))}],
                    expression,
                )
            group_fields.append(group_field)
        return group_fields

    def comparison2group(
        self,
        comparison: Dict,
        dataset_field_dict: Dict = None,
        formula_mode: str = None,
        label_dataset_key: bool = None,
    ) -> [Dict, GroupField]:
        dataset_field, group_field = self.field2base(
            comparison.get("dataset_field_id"), dataset_field_dict, formula_mode, label_dataset_key
        )
        if comparison.get("formula_mode"):
            expression = group_field.expressions if group_field.expressions else None
            group_field.expressions = None
            group_field.get_field(
                ['date_format'],
                [{'field': functions.get_time_formula_mode(comparison.get("formula_mode"))}],
                expression,
            )
        return dataset_field, group_field
