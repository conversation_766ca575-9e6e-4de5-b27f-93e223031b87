#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
from dashboard_chart.convertor.field_base import FieldBase
from dashboard_chart.convertor.group.comparison_group import ComparisonGroup
from dashboard_chart.convertor.group.dim_group import DimGroup


class Group:
    @staticmethod
    def get_group_fields(model):
        """
        获取GroupField对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        group_fields = []
        # 当为不同步标签数据集的情况下，不需要group部分
        if model.dataset.get(FieldBase().label_dataset_key):
            return group_fields
        if model.dims:
            dim_group = DimGroup()
            group_fields.extend(dim_group.get_group_fields(model))
        if model.comparisons:
            comparison_group = ComparisonGroup()
            group_fields.extend(comparison_group.get_group_fields(model))
        return group_fields

    @staticmethod
    def get_group_fields_for_assist(model):
        """
        单图筛选器获取GroupField对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        group_fields = []
        if model.dims:
            dim_group = DimGroup()
            group_fields.extend(dim_group.get_group_fields_for_assist(model))
        return group_fields

    @staticmethod
    def get_group_fields_for_excel_table(model):
        """
        透视表获取GroupField对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        group_fields = []
        # 当为不同步标签数据集的情况下，不需要group部分
        if model.dataset.get(FieldBase().label_dataset_key):
            return group_fields
        if model.dims:
            dim_group = DimGroup()
            group_fields.extend(dim_group.get_group_fields(model))
        if model.comparisons:
            comparison_group = ComparisonGroup()
            group_fields.extend(comparison_group.get_group_fields(model))
        return group_fields

    @staticmethod
    def get_group_fields_for_dataset_field(model):
        """
        拼接 count(*) as total 作为select来查询数据总量
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        select = DimGroup()
        return select.get_group_fields_for_data_field(model)

    @staticmethod
    def get_group_fields_for_line(model):
        """
        获取GroupField对象列表
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        # 当为不同步标签数据集的情况下，不需要group部分
        if model.dataset.get(FieldBase().label_dataset_key):
            return False
        group_fields = []
        if model.dims:
            dim_group = DimGroup()
            group_fields.extend(dim_group.get_group_fields(model))
        if model.comparisons:
            comparison_group = ComparisonGroup()
            group_fields.extend(comparison_group.get_group_fields(model))
        return group_fields
