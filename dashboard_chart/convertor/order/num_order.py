#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=R1710, W0221

"""
    created by  <EMAIL> 2018/9/14
"""
from base.enums import ChartNumFormulaMode
from dashboard_chart.convertor.field_types import OrderField
from dashboard_chart.convertor.order.order_base import OrderBase
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel


class NumOrder(OrderBase):
    def get_order_fields_for_multi(
        self, section_dict, field_sort_model: ChartFieldSortModel, chart_data_model: ChartDataModel
    ):
        """
        度量多字段排序
        :param section_dict:
        :param field_sort_model:
        :param chart_data_model:
        :return:
        """
        from ..order.agg.num_order import NumOrderForAgg
        from ..order.noagg.num_order import NumOrderForNoAgg

        select_obj = NumOrderForAgg() if chart_data_model.aggregation else NumOrderForNoAgg()
        return select_obj.get_order_fields_for_multi(section_dict, field_sort_model, chart_data_model)

    def generate_order_field(self, chart_data_model, num):
        dataset_field = chart_data_model.dataset_field_dict.get(num.get("num"))
        order_field = OrderField(**dataset_field)
        expression = order_field.expressions if order_field.expressions else None
        if num.get("formula_mode") == ChartNumFormulaMode.Distinct.value:
            order_field.expressions = None
            funcs, args = self.add_ifnull_to_funcs(
                num, [ChartNumFormulaMode.Count.value, ChartNumFormulaMode.Distinct.value], []
            )
            order_field.get_field(funcs=funcs, args=args, expressions=expression)
        # 这类属于高级字段中的计算高级字段情况
        elif not num.get("formula_mode") and expression:
            order_field.field = dataset_field.get("col_name")
        else:
            order_field.expressions = None
            # 需要对高级计算方法进行转换
            funcs, args = self._get_funcs_and_args(num)
            order_field.get_field(funcs=funcs, args=args, expressions=expression)
        order_field.field_sort = num.get("sort")
        return order_field

    def _get_funcs_and_args(self, field):
        # 高级计算 数据库查询的函数为原始函数，但是字段别名需要变成真实的别名
        real_formula_mode = self.get_func_of_advanced_compute_method(field.get("formula_mode"))
        funcs, args = self.add_ifnull_to_funcs(field, [real_formula_mode], [])
        return funcs, args
