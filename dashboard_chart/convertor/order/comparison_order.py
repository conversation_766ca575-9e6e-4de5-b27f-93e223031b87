#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/14
"""
from base.enums import ColTypes
from dashboard_chart.convertor.order.order_base import OrderBase
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel


class ComparisonOrder(OrderBase):
    def get_order_fields_for_multi(self, section_dict, field_sort_model: ChartFieldSortModel, model: ChartDataModel):
        """
        对比字段排序数据
        :param section_dict:
        :param field_sort_model:
        :param model:
        :return:
        """
        orders = self.get_advanced_order_fields(section_dict, field_sort_model, model)
        for order in orders:
            order.logic_source = ColTypes.Comparison.value
        return orders
