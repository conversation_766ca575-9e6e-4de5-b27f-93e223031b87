#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=R1710,W0221,R0201
"""
    <NAME_EMAIL> 2018/9/14
"""
import copy
import json

from typing import List

from base.enums import FieldSortFieldSource, ColTypes, OrderType
from dashboard_chart.convertor.order.comparison_order import ComparisonOrder
from dashboard_chart.convertor.order.desire_order import DesireOrder
from dashboard_chart.convertor.order.dim_order import DimOrder
from dashboard_chart.convertor.order.num_order import NumOrder
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel
from dashboard_chart.utils import chart_utils
from dashboard_chart.convertor.group.group import Group


class Order:
    @staticmethod
    def get_section_dict(data, match_key="dataset_field_id"):
        """
        组装dict
        :param data:
        :param match_key:
        :return:
        """
        return {i.get(match_key): i for i in data} if data else {}

    def get_order_fields_for_multi_v2(self, model):
        """
        处理步骤：
        1. 去除无用排序(排序方式为DEFAULT的排序及透视表num字段排序)
        3. 按照weight进行排序
        4. 增加自动序列（有勾选分类汇总字段和聚合模式下有透视）
        5. 生成order_field列表
        :param model:
        :return:
        """
        order_fields = []
        orders = copy.deepcopy(model.new_order)
        # 1. 去除无用排序
        orders = self._remove_useless_orders(model, orders)
        # 3. 按照weight进行排序
        orders = sorted(
            orders, key=lambda i: i.get("weight") if i and isinstance(i.get("weight"), int) else 0, reverse=True
        )
        # 4. 增加自动序列
        orders.extend(self._get_auto_sort_orders(model, orders))
        # 5. 生成order_fields 字段列表
        # 为了避免重复遍历，创建为字段ID为键的字典数据
        section_dict = {
            FieldSortFieldSource.Dims.value: self.get_section_dict(model.dims, match_key="dim"),
            FieldSortFieldSource.Nums.value: self.get_section_dict(model.nums, match_key="num"),
            FieldSortFieldSource.Comparisons.value: self.get_section_dict(model.comparisons),
            FieldSortFieldSource.Desires.value: self.get_section_dict(model.desires),
        }
        # 生成order_field
        for single_field_sort in orders:
            operate_order_fields = []
            field_sort_model = ChartFieldSortModel(**single_field_sort)
            field_sort_model, section_dict = self._adapt_field_sort_model(model, field_sort_model, section_dict)

            if field_sort_model.field_source == FieldSortFieldSource.Dims.value:
                operate_order_fields = DimOrder().get_order_fields_for_multi(
                    section_dict[field_sort_model.field_source], field_sort_model, model
                )
            elif field_sort_model.field_source in [FieldSortFieldSource.Nums.value, FieldSortFieldSource.Zaxis.value]:
                match_field_source = "nums"
                operate_order_fields = NumOrder().get_order_fields_for_multi(
                    section_dict[match_field_source], field_sort_model, model
                )
            elif field_sort_model.field_source == FieldSortFieldSource.Comparisons.value:
                operate_order_fields = ComparisonOrder().get_order_fields_for_multi(
                    section_dict[field_sort_model.field_source], field_sort_model, model
                )
            elif field_sort_model.field_source == FieldSortFieldSource.Desires.value:
                operate_order_fields = DesireOrder().get_order_fields_for_multi(
                    section_dict[field_sort_model.field_source], field_sort_model, model
                )
            if operate_order_fields:
                self._add_field_sort_info(operate_order_fields, single_field_sort)
                order_fields.extend(operate_order_fields)
        return order_fields

    def _add_field_sort_info(self, operate_order_fields, single_field_sort):
        if isinstance(operate_order_fields, list):
            for operate_order_field in operate_order_fields:
                if single_field_sort.get('sort') == 'FIELD':
                    single_field_sort['content'] = self._load_content(single_field_sort.get('content'))
                setattr(operate_order_field, '__field_sort__', copy.deepcopy(single_field_sort))

    def _load_content(self, content):
        return (
            content if isinstance(content, dict) else json.loads(content or '{}')
        )

    def _adapt_field_sort_model(self, model, field_sort_model, section_dict):
        # 替换自定义字段排序的关键信息
        if field_sort_model.sort != 'FIELD':
            return field_sort_model, section_dict

        field_sort_model, section_dict = copy.deepcopy(field_sort_model), copy.deepcopy(section_dict)
        content = self._load_content(field_sort_model.content)
        sort_field_id = content.get('sort_field_id')
        sort = content.get('sort')
        field_type = content.get('field_type')
        formula_mode = content.get('formula_mode')
        field_sort_model.sort = sort
        field_sort_model.field_source = field_type # dims/nums
        field_sort_model.dataset_field_id = sort_field_id

        if sort_field_id not in section_dict:
            # 选择了不在面板上的维度或者度量
            mock_data = self.mock_dim_or_num_data(model, field_type, dataset_field_id=sort_field_id)
            section_dict[field_type][sort_field_id] = mock_data

        section_dict[field_type][sort_field_id]['_field_sort_'] = 1

        # 替换num的聚合方式
        if field_sort_model.field_source == FieldSortFieldSource.Nums.value:
            num = section_dict[field_sort_model.field_source].get(sort_field_id, {})
            num['formula_mode'] = formula_mode
            if not model.aggregation:  # 明细模式下，直接丢掉客户设置的聚合
                num['formula_mode'] = ''

        elif field_sort_model.field_source == FieldSortFieldSource.Dims.value:
            # !!!!! GROUP BY [CgPlanGUID],[ContractName] ORDER BY MAX([ContractCode]) ASC
            # 对于SQLserver的语法校验，如果ORDER BY中出现没有聚合的维度，在GROUP BY后SQL语法将是有争议的，会报错
            # 这里将所有维度默认取max后进行order by
            # 将所有的字段排序维度全部用max进行聚合
            group_fields = self.get_group_fields(model)
            if group_fields:  # 只有存在group by才走 ，不然报错
                self.trans_order_dim_num(section_dict[field_type][sort_field_id])
                section_dict['nums'][sort_field_id] = section_dict[field_type][sort_field_id]
                field_sort_model.field_source = FieldSortFieldSource.Nums.value

        return field_sort_model, section_dict

    def get_group_fields(self, model: ChartDataModel = None):
        real_model = model
        # 只有有聚合的情况下才有group
        if real_model.aggregation:
            group_fields = Group.get_group_fields(real_model)
        else:
            group_fields = []
        return group_fields

    def mock_dim_or_num_data(self, model, field_type, dataset_field_id):
        from dashboard_chart.services.chart_service import _get_chart_section_data

        model = copy.deepcopy(model)
        filed = model.dataset_field_dict[dataset_field_id]
        filed['dataset_field_id'] = dataset_field_id
        setattr(model, field_type, [filed])

        mock_data = _get_chart_section_data(model, section_type=field_type)
        mock_data = mock_data[0] if mock_data else {}
        mock_data['dataset_field_id'] = mock_data.pop('id', None)
        # dim/num 字段补充， 因为构造出来的数据不全
        ty = field_type[:-1]  # dim/num
        mock_data[ty] = mock_data['dataset_field_id']
        return mock_data

    def trans_order_dim_num(self, dim_data):
        dim_data['data_type'] = '数值'
        dim_data['field_group'] = '度量'
        dim_data['formula_mode'] = 'max'
        dim_data['num'] = dim_data.pop('dim', '')

    def _remove_useless_orders(self, model: ChartDataModel, orders: list):
        result = []
        for order in orders:
            if order["sort"] == "DEFAULT":
                continue
            if model.aggregation and model.comparisons and order["field_source"] == FieldSortFieldSource.Nums.value:
                continue
            result.append(order)
        return result

    def _get_auto_sort_orders(self, model: ChartDataModel, hand_orders: list):
        auto_sort_orders, orders = [], []
        enable_auto_sort = model.layout_extend.get('enable_auto_sort') if model.layout_extend else 1  # 默认开启自动排序
        has_opt_cate_col = any([d.get('is_subtotal_cate') for d in model.dims])  # 是否配置了任意一个分类小计
        if model.enable_subtotal_col:
            if enable_auto_sort == 0 and not has_opt_cate_col:
                # 开启汇总，关闭了自动排序，并且没有配置分类小计
                # 去掉自动排序
                pass
            else:
                orders.extend(self._get_auto_orders_for_subtotal(model, hand_orders))

        if model.aggregation and model.comparisons:
            orders.extend(self._get_auto_orders_for_pivot(model, hand_orders))
        # 去重
        orders_set = []
        for order in orders:
            key = tuple([order["dataset_field_id"], order["field_source"]])
            if key not in orders_set:
                orders_set.append(key)
                auto_sort_orders.append(order)
        return auto_sort_orders

    def _get_auto_orders_for_subtotal(self, model: ChartDataModel, hand_orders: list):
        auto_orders = []
        hand_dim_ids = []
        max_weight = 0
        for order in hand_orders:
            if order["field_source"] == FieldSortFieldSource.Dims.value:
                hand_dim_ids.append(order["dataset_field_id"])
            weight = int(order.get("weight", 0))
            if weight > max_weight:
                max_weight = weight
        for dim in model.dims:
            if dim.get("dim") not in hand_dim_ids:
                max_weight += 1
                auto_orders.append(
                    {
                        "alias": dim.get("alias", ""),
                        "content": "",
                        "dataset_field_id": dim["dim"],
                        "sort": OrderType.Asc.value,
                        "weight": max_weight,
                        "field_source": FieldSortFieldSource.Dims.value,
                    }
                )
        return auto_orders

    def _get_auto_orders_for_pivot(self, model: ChartDataModel, hand_orders):
        hand_data, auto_orders = [], []
        max_weight = 0
        for order in hand_orders:
            hand_data.append(
                tuple(
                    [chart_utils.convert_order_field_source2col_type(order["field_source"]), order["dataset_field_id"]]
                )
            )
            weight = int(order.get("weight", 0))
            if weight > max_weight:
                max_weight = weight
        auto_dim_orders, max_weight = self._generate_oders_from_list(
            model.dims, ColTypes.Dim.value, hand_data, max_weight
        )
        auto_comparison_orders, _ = self._generate_oders_from_list(
            model.comparisons, ColTypes.Comparison.value, hand_data, max_weight
        )
        return auto_orders + auto_dim_orders + auto_comparison_orders

    def _generate_oders_from_list(self, list_data: List[dict], col_type: str, hand_data: list, max_weight: int):
        auto_orders = []
        for item in list_data:
            if col_type == ColTypes.Dim.value:
                dataset_field_id = item["dim"]
            elif col_type == ColTypes.Num.value:
                dataset_field_id = item["num"]
            else:
                dataset_field_id = item["dataset_field_id"]
            if tuple([col_type, dataset_field_id]) in hand_data:
                continue
            max_weight += 1
            auto_orders.append(
                {
                    "alias": item.get("alias", ""),
                    "content": "",
                    "dataset_field_id": dataset_field_id,
                    "sort": OrderType.Asc.value,
                    "weight": max_weight,
                    "field_source": chart_utils.convert_col_type2order_field_source(col_type),
                }
            )
        return auto_orders, max_weight

    def get_order_fields_for_multi(self, model: ChartDataModel):
        """
        新版排序
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        return self.get_order_fields_for_multi_v2(model)
