#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> 2018/9/14
"""
import json
from abc import ABCMeta, abstractmethod

from components.utils import mysql_escape_string
from base.enums import DatasetFieldType
from components import functions
from dashboard_chart.convertor.field_base import FieldBase
from dashboard_chart.convertor.field_types import Order<PERSON>ust<PERSON><PERSON>ield, OrderField
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel


class OrderBase(FieldBase):
    __metaclass__ = ABCMeta

    @abstractmethod
    def get_order_fields_for_multi(self, section_dict, field_sort_model: ChartFieldSortModel, model: ChartDataModel):
        """
        获取order数据
        :param section_dict:
        :param dashboard_chart.models.ChartFieldSortModel field_sort_model:
        :param dashboard_chart.models.ChartDataModel model:
        :return:
        """
        pass

    @staticmethod
    def _string_to_escape_for_list(data):
        """
        mysql字符转义
        :param data:
        :return:subtotal/subtotal_row_query.py
        """
        if not data:
            return []
        return [
            # MySQLdb.escape_string(str(i).encode(encoding='utf-8').decode(encoding='utf-8')).decode(encoding='utf-8')
            mysql_escape_string(i)
            if i is not None
            else None
            for i in data
        ]

    @staticmethod
    def _op_custom_field(dataset_field, item, content):
        order_field = OrderCustomField(**dataset_field)
        order_field.field_sort = item.get("sort").upper()
        order_field.items = OrderBase._string_to_escape_for_list(content.get("sort"))
        if item.get("formula_mode"):
            order_field.get_field(
                ["date_format"], [{"field": functions.get_time_formula_mode(item.get("formula_mode"))}]
            )
        else:
            order_field.field = dataset_field.get("col_name")
        return order_field

    @staticmethod
    def _op_order_field(dataset_field, item):
        order_field = OrderField(**dataset_field)
        order_field.field_sort = item.get("sort").upper()
        if item.get("type") in [
            DatasetFieldType.Calculate.value,
            DatasetFieldType.Customer.value,
            DatasetFieldType.Indicator.value,
            DatasetFieldType.CalculateIndicator.value,
        ]:
            order_field.field = dataset_field.get("col_name")
        if item.get("formula_mode"):
            # 辅助线需要获取此值
            dataset_field['formula_mode'] = item.get('formula_mode')
            expression = order_field.expressions or None
            order_field.expressions = None
            order_field.get_field(
                ["date_format"], [{"field": functions.get_time_formula_mode(item.get("formula_mode"))}], expression
            )
        return order_field

    def _op_order_fields(self, model, loop_item, content, order_fields):
        if "sort" in loop_item.keys() and loop_item.get("sort"):
            dataset_field = model.dataset_field_dict.get(loop_item.get("dataset_field_id"))
            if not dataset_field:
                return order_fields
            if content and "sort" in content and type(content["sort"]) == list and loop_item.get("sort") == "CUSTOM":
                order_fields.append(self._op_custom_field(dataset_field, loop_item, content))
            else:
                order_fields.append(self._op_order_field(dataset_field, loop_item))
            return order_fields
        return order_fields

    def get_order_fields_from_dataset_field(self, fields, model):
        from ..order.agg.order_base import OrderBaseForAgg
        from ..order.noagg.order_base import OrderBaseForNoAgg

        select_obj = OrderBaseForAgg() if model.aggregation else OrderBaseForNoAgg()
        return select_obj.get_order_fields_from_dataset_field(fields, model)

    def generate_order_field_from_dataset_field(self, item, model, order_fields):
        content = {}
        if item.get("content"):
            content = item["content"] if isinstance(item["content"], dict) else json.loads(item["content"])
        order_fields = self._op_order_fields(model, item, content, order_fields)
        return order_fields

    @staticmethod
    def _assign_field_sort_data(data, field_sort_model: ChartFieldSortModel):
        """
        替换排序功能用的字段
        :param data:
        :param field_sort_model:
        :return:
        """
        if not data or not isinstance(data, dict):
            return data
        data["sort"] = field_sort_model.sort
        data["content"] = field_sort_model.content

    def get_advanced_order_fields(self, section_dict, field_sort_model: ChartFieldSortModel, model: ChartDataModel):
        """

        :param section_dict:
        :param field_sort_model:
        :param model:
        :return:
        """
        from ..order.agg.order_base import OrderBaseForAgg
        from ..order.noagg.order_base import OrderBaseForNoAgg

        select_obj = OrderBaseForAgg() if model.aggregation else OrderBaseForNoAgg()
        return select_obj.get_advanced_order_fields(section_dict, field_sort_model, model)

    def generate_advanced_order_field(self, model, operate_field_data):
        order_field = None
        content = {}
        if operate_field_data.get("content"):
            content = (
                operate_field_data["content"]
                if isinstance(operate_field_data["content"], dict)
                else json.loads(operate_field_data["content"])
            )
        if "sort" in operate_field_data.keys() and operate_field_data.get("sort"):
            dataset_field = model.dataset_field_dict.get(operate_field_data.get("dataset_field_id"))
            if not dataset_field:
                return order_field
            if (
                content
                and "sort" in content
                and type(content["sort"]) == list
                and operate_field_data.get("sort") == "CUSTOM"
            ):
                return self._op_custom_field(dataset_field, operate_field_data, content)
            else:
                return self._op_order_field(dataset_field, operate_field_data)
        return order_field
