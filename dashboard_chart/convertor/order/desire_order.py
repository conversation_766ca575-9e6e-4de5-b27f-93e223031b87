#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> 2018/9/14
"""
from base.enums import ChartNumFormulaMode
from dashboard_chart.convertor.field_types import OrderField
from dashboard_chart.convertor.order.order_base import OrderBase
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel
from dmplib.utils.errors import UserError


class DesireOrder(OrderBase):
    def get_order_fields_for_multi(self, section_dict, field_sort_model: ChartFieldSortModel, model: ChartDataModel):
        """
        目标值字段排序数据
        :param section_dict:
        :param field_sort_model:
        :param model:
        :return:
        """
        from ...convertor.order.agg.desire_order import DesireOrderForAgg
        from ...convertor.order.noagg.desire_order import DesireOrderForNoAgg

        select_obj = DesireOrderForAgg() if model.aggregation else DesireOrderForNoAgg()
        return select_obj.get_order_fields_for_multi(section_dict, field_sort_model, model)

    def generate_order_field(self, desire, model):
        dataset_field = model.dataset_field_dict.get(desire.get("dataset_field_id"))
        if not dataset_field:
            raise UserError(message="无法获取到目标值字段信息，请联系管理员！")
        order_field = OrderField(**dataset_field)
        expression = order_field.expressions if order_field.expressions else None
        order_field.expressions = None
        if desire.get("formula_mode") == ChartNumFormulaMode.Distinct.value:
            order_field.get_field(
                funcs=[ChartNumFormulaMode.Count.value, ChartNumFormulaMode.Distinct.value], expressions=expression
            )
        else:
            order_field.get_field(funcs=[desire.get("formula_mode")], expressions=expression)
        order_field.field_sort = desire.get("sort")
        return order_field
