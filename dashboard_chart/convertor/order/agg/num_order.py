#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 18:01
# <AUTHOR> caoxl
# @File     : num_order.py
from base.enums import ColTypes
from dashboard_chart.convertor.order.num_order import NumOrder
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel


class NumOrderForAgg(NumOrder):
    def get_order_fields_for_multi(
        self, section_dict, field_sort_model: ChartFieldSortModel, chart_data_model: ChartDataModel
    ):
        """
        度量多字段排序
        :param section_dict:
        :param field_sort_model:
        :param chart_data_model:
        :return:
        """
        order_fields = []
        num = section_dict.get(field_sort_model.dataset_field_id)
        self._assign_field_sort_data(num, field_sort_model)
        if not num:
            return order_fields
        # sort 为默认值时为空字符串， 则不排序
        if "sort" in num.keys() and num.get("sort"):
            order_field = self.generate_order_field(chart_data_model, num)
            order_field.logic_source = ColTypes.Num.value
            order_fields.append(order_field)
        return order_fields
