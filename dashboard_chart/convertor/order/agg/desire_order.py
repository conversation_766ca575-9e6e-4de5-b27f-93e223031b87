#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 17:51
# <AUTHOR> caoxl
# @File     : desire_order.py
from base.enums import ColTypes
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel
from dashboard_chart.convertor.order.desire_order import DesireOrder


class DesireOrderForAgg(DesireOrder):
    def get_order_fields_for_multi(self, section_dict, field_sort_model: ChartFieldSortModel, model: ChartDataModel):
        """
        目标值字段排序数据
        :param section_dict:
        :param field_sort_model:
        :param model:
        :return:
        """
        order_fields = []
        desire = section_dict.get(field_sort_model.dataset_field_id)
        self._assign_field_sort_data(desire, field_sort_model)
        if not desire or not desire.get("dataset_field_id"):
            return order_fields
        if "sort" in desire.keys() and desire.get("sort"):
            # 对聚合进行判断
            order_field = self.generate_order_field(desire, model)
            order_field.logic_source = ColTypes.Desire.value
            order_fields.append(order_field)
        return order_fields
