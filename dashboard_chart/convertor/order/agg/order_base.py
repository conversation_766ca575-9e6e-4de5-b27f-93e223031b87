#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/8/5 18:03
# <AUTHOR> caoxl
# @File     : order.py
from dashboard_chart.models import ChartDataModel, ChartFieldSortModel
from dashboard_chart.convertor.order.order_base import OrderBase
from collections import Iterable


class OrderBaseForAgg(OrderBase):
    def get_advanced_order_fields(self, section_dict, field_sort_model: ChartFieldSortModel, model: ChartDataModel):
        """

        :param section_dict:
        :param field_sort_model:
        :param model:
        :return:
        """
        order_fields = []
        operate_field_data = section_dict.get(field_sort_model.dataset_field_id)
        self._assign_field_sort_data(operate_field_data, field_sort_model)
        if not operate_field_data:
            return order_fields
        order_field = self.generate_advanced_order_field(model, operate_field_data)
        if not order_field:
            return order_fields
        order_fields.append(order_field)
        return order_fields

    def get_order_fields_from_dataset_field(self, fields, model):
        order_fields = []
        if not isinstance(fields, Iterable):
            return order_fields
        for item in fields:
            order_fields = self.generate_order_field_from_dataset_field(item, model, order_fields)
        return order_fields
