#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2018/9/29 20:06
# <AUTHOR> caoxl
# @File     : utils.py
from base.enums import ChartNumFormulaMode


def is_aggregation(field: dict, aggregation_key: str) -> bool:
    """
    校验是否是聚合
    :param field:
    :param aggregation_key:
    :return:
    """
    if isinstance(field, dict) and field.get(aggregation_key) in [
        e.value for e in ChartNumFormulaMode.__members__.values()
    ]:
        return True
    return False


def rename_reduplicative_alias(fields: list) -> list:
    """
    修改相同别名的field 在后面加上后缀
    如 col col_1 col_2 以此类推
    :param fields:
    :return:
    """
    alias_count_dict = {}
    for field in fields:
        if not field.alias:
            continue
        if field.alias not in alias_count_dict:
            alias_count_dict[field.alias] = 0
        else:
            alias_count_dict[field.alias] += 1
        if alias_count_dict.get(field.alias, 0) > 0:
            field.alias = '_'.join([field.alias, str(alias_count_dict.get(field.alias, 0))])
    return fields


def assign_logic_source(fields: list, logic_source: str) -> list:
    """
    为字段赋值 logic_source
    :param fields:
    :param logic_source:
    :return:
    """
    for field in fields:
        field.logic_source = logic_source
    return fields


def get_alias_logic_source_dict(fields: list):
    """
    获取字段别名对应的logic_source
    :param fields:
    :return:
    """
    logic_source_dict = {}
    for field in fields:
        logic_source_dict[field.alias or field.field] = field.logic_source
    return logic_source_dict
