from dmplib.hug import APIWrapper

from dashboard_chart.services.third_party_service import ThirdPartyService

api = APIWrapper(__name__)


@api.admin_route.post('/tree_data')
def tree_data(request, response, **kwargs):
    third_party = ThirdPartyService(request, **kwargs)
    return True, '', third_party.get_tree_data()


@api.admin_route.get('/view')
def view(request, response, **kwargs):
    third_party = ThirdPartyService(request, **kwargs)
    return third_party.view()
