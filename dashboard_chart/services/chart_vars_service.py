#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E1101,R1718,W0640,R1710

"""
chart vars services
"""

# ---------------- 标准模块 ----------------
from collections import defaultdict
from copy import deepcopy

# ---------------- 业务模块 ----------------
from dashboard_chart.services import metadata_service
from dataset import external_query_service
from dataset.repositories import dataset_var_repository
from keywords.external_service import get_keyword_detail_by_vars


def get_chart_dataset_vars(dashboard_id, chart_data_list=None, charts=None, chart_relations=None):
    """
    获取单图关联数据集对应绑定的变量
    支持调用的来源模块：1,dashboard_chart目录下的预览元数据组合类 2，dashboard目录下的报告发布
    :param dashboard_id:
    :param chart_data_list:
    :param charts:
    :param chart_relations:
    :return:
    """
    chart_penetrate_field_dict = {}
    if not chart_data_list and not charts:
        return {}
    if not chart_data_list and charts:
        chart_data_list = [i for i in charts.values()] if charts.values() else []
        chart_penetrate_field_dict = _generate_chart_penetrate_field_dict(charts)

    # 暂时不开放联动的应用场景
    # n_chart_linkages = generate_chart_relation_dict(metadata_service.get_new_linkages(dashboard_id))
    penetrates = generate_chart_relation_dict(
        metadata_service.get_penetrates(dashboard_id), parent_key="parent_id", son_key="chart_id"
    )

    # 获取联动，组件筛选，穿透关系下的单图父子级关系
    new_filters = metadata_service.get_new_filters(dashboard_id)
    n_chart_filters = generate_chart_relation_dict(new_filters)
    chart_relation_list = [n_chart_filters, penetrates]

    # 获取数据集字段变量关系
    dataset_field_vars_dict = defaultdict(list)
    dataset_id_list = list(set([i.get("source") for i in chart_data_list if i.get("source")]))
    if dataset_id_list:
        query_data = external_query_service.batch_get_dataset_include_vars(dataset_id_list)
        if query_data:
            for item in query_data:
                dataset_field_vars_dict[item.get("field_id")].append(item)

    # 获取单图关联数据集对应绑定的变量
    result_vars_dict = batch_get_chart_vars(
        chart_data_list, dataset_field_vars_dict, chart_relation_list, chart_relations, chart_penetrate_field_dict
    )

    # 需要获取sql直接引用的变量
    _assgin_external_vars(chart_data_list, dataset_id_list, result_vars_dict)

    # 获取间接关联 高级字段使用的变量
    chart_indirect_dataset_field_ids = {
        new_filter["chart_responder_id"]: new_filter["chart_initiator_filed_id"]
        for new_filter in new_filters
        if new_filter["filter_type"] == 1
    }
    _assgin_indirect_vars(chart_indirect_dataset_field_ids, dataset_field_vars_dict, result_vars_dict)

    return result_vars_dict


def _assgin_indirect_vars(indirect_dataset_field_ids: dict, dataset_field_vars_dict: dict, result_vars_dict: dict):
    """
    将间接关联变量加入其中
    :param indirect_dataset_field_ids:
    :param dataset_field_vars_dict:
    :param result_vars_dict:
    :return:
    """
    for chart_id, dataset_field_id in indirect_dataset_field_ids.items():
        dataset_field_var = dataset_field_vars_dict.get(dataset_field_id)
        if not dataset_field_var:
            continue
        if chart_id not in result_vars_dict:
            result_vars_dict[chart_id] = []
        result_vars_dict[chart_id].extend(dataset_field_var)


def _get_dataset_with_vars_dict(external_dataset_vars_dict, all_var_info_dict):
    """
    获取数据集sql引用的变量
    :param external_dataset_vars_dict:
    :param all_var_info_dict:
    :return:
    """
    dataset_with_vars_dict = defaultdict(list)
    for dataset_id, var_id_list in external_dataset_vars_dict.items():
        for var_id in var_id_list:
            single_var_info = all_var_info_dict.get(var_id)
            if single_var_info:
                single_var_info["var_id"] = single_var_info.get("id")
                single_var_info["field_id"] = ""
                single_var_info["var_source"] = 0  # 0: 自身绑定 1: 父级单图绑定
                single_var_info["is_from_sql"] = 1  # 0 默认，1 被数据集sql引用
                dataset_with_vars_dict[dataset_id].append(single_var_info)
    return dataset_with_vars_dict


def _op_update_external_vars(var_list, orig_var_id_list, match_external_vars_list):
    """
    var_list中需要更新is_from_sql字段的变量
    :param var_list:
    :param orig_var_id_list:
    :param match_external_vars_list:
    :return:
    """
    update_external_vars_list = list(filter(lambda i: i.get("var_id") in orig_var_id_list, match_external_vars_list))
    update_external_vars_id_list = (
        [i.get("var_id") for i in update_external_vars_list] if update_external_vars_list else []
    )
    if update_external_vars_id_list:
        for v in var_list:
            if v.get('var_id') in update_external_vars_id_list:
                v['is_from_sql'] = 1


def _op_insert_external_vars(var_list, orig_var_id_list, match_external_vars_list):
    """
    需要新增到var_list的变量
    :param var_list:
    :param orig_var_id_list:
    :param match_external_vars_list:
    :return:
    """
    # 需要去重
    insert_external_vars_list = list(
        filter(lambda i: i.get("var_id") not in orig_var_id_list, match_external_vars_list)
    )
    if insert_external_vars_list:
        var_list.extend(insert_external_vars_list)


def _assgin_external_vars(chart_data_list, dataset_id_list, result_vars_dict):
    """
    获取sql中直接引用的变量
    :param chart_data_list:
    :param dataset_id_list:
    :param result_vars_dict:
    :return:
    """
    if not result_vars_dict:
        return

    # 先加上标志位字段，区分变量是否为sql引用的变量
    for _, var_list in result_vars_dict.items():
        for v in var_list:
            v["is_from_sql"] = 0  # 0 默认，1 被数据集sql引用

    # 需要收集sql中直接引用的变量
    chart_dataset_dict = {i.get("id"): i.get("source", "") for i in chart_data_list}
    external_dataset_vars_dict = external_query_service.get_dataset_sql_used_var_ids_by_dataset_ids(dataset_id_list)
    all_var_id_list = []
    for var_id_list in external_dataset_vars_dict.values():
        all_var_id_list.extend(var_id_list)
    if not all_var_id_list:
        return

    all_var_info_list = dataset_var_repository.get_dataset_var_by_ids(list(set(all_var_id_list)))
    # 获取变量和关键字的关系并赋值默认值
    get_keyword_detail_by_vars(all_var_info_list)
    all_var_info_dict = {item.get("id"): item for item in all_var_info_list}

    dataset_with_vars_dict = _get_dataset_with_vars_dict(external_dataset_vars_dict, all_var_info_dict)

    for chart_id, var_list in result_vars_dict.items():
        match_dataset_id = chart_dataset_dict.get(chart_id, "")
        if not match_dataset_id:
            continue
        match_external_vars_list = dataset_with_vars_dict.get(match_dataset_id, [])
        orig_var_id_list = [i.get("var_id") for i in var_list] if var_list else []

        # 同时被报告和数据集sql引用的变量需要更新is_from_sql为1
        _op_update_external_vars(var_list, orig_var_id_list, match_external_vars_list)

        # 只被数据集sql引用的变量需要新增到vars_list数组中
        _op_insert_external_vars(var_list, orig_var_id_list, match_external_vars_list)


def add_bind_type(field_var_dict, bind_type):
    """
    新增字段区分是否为单图自身绑定的变量
    0: 自身绑定 1: 父级单图绑定
    :param field_var_dict:
    :param bind_type:
    :return:
    """
    if not field_var_dict:
        return field_var_dict
    for k, v in field_var_dict.items():
        if not v:
            continue
        for i in v:
            i["var_source"] = bind_type
    return field_var_dict


def _recursive_append_chart_vars(operate_chart_id, relation_dict, operate_vars_dict):
    """
    递归添加当前操作单图的父级单图的绑定变量
    :param operate_chart_id:
    :param relation_dict:
    :param operate_vars_dict:
    :return:
    """
    parent_chart_id_list = relation_dict.get(operate_chart_id, [])
    for parent_chart_id in set(parent_chart_id_list):
        # 父级单图本身也有父级单图的情况
        if relation_dict.get(parent_chart_id):
            _recursive_append_chart_vars(parent_chart_id, relation_dict, operate_vars_dict)
        # 将父级单图自身绑定的变量也追加到当前的子级单图中
        parent_field_var_dict = operate_vars_dict.get(parent_chart_id)
        if parent_field_var_dict:
            cur_parent_field_var_dict = deepcopy(parent_field_var_dict)
            operate_field_var_dict = operate_vars_dict.get(operate_chart_id)
            operate_field_var_id_list = operate_field_var_dict.keys() if operate_field_var_dict else []
            # 需要过滤子级单图已存在的变量
            cur_parent_field_var_dict = {
                k: v for k, v in cur_parent_field_var_dict.items() if k not in operate_field_var_id_list
            }
            operate_vars_dict[operate_chart_id].update(add_bind_type(cur_parent_field_var_dict, bind_type=1))


def batch_get_chart_vars(
    chart_data_list, dataset_field_vars_dict, chart_relation_list, chart_relations, chart_penetrate_field_dict
):
    """
    批量获取各单图绑定的变量数组
    :param chart_data_list:
    :param dataset_field_vars_dict:
    :param chart_relation_list:
    :param chart_relations:
    :param chart_penetrate_field_dict:
    :return:
    """
    operate_vars_dict = defaultdict(dict)
    penetrates = chart_relations.get("penetrates", []) if chart_relations else []

    for single_chart_data in chart_data_list:
        chart_id = single_chart_data.get("id")
        field_var_dict = defaultdict(list)

        # 收集单图各个section使用到的高级字段
        get_field_var_dict(field_var_dict, single_chart_data, dataset_field_vars_dict)
        # 收集穿透关系中使用到的高级字段
        if penetrates:
            get_field_var_dict_for_penetrates(field_var_dict, chart_id, penetrates, dataset_field_vars_dict)
        # 报告发布流程的场景，收集穿透关系中使用到的高级字段
        elif chart_penetrate_field_dict:
            get_field_var_dict_for_release_penetrates(
                field_var_dict, chart_id, chart_penetrate_field_dict, dataset_field_vars_dict
            )

        if chart_id not in operate_vars_dict.keys():
            # 新增字段区分是否为单图自身绑定的变量
            field_var_dict = add_bind_type(field_var_dict, bind_type=0)
            operate_vars_dict[chart_id] = field_var_dict

    # 每个单图需要extend父级关系单图的绑定变量
    for single_chart in chart_data_list:
        chart_id = single_chart.get("id")
        for relation_dict in chart_relation_list:
            # 遇到死循环的情况先跳过
            try:
                _recursive_append_chart_vars(chart_id, relation_dict, operate_vars_dict)
            except RecursionError:
                pass

    return _change_data_structure(operate_vars_dict)


def _change_data_structure(operate_vars_dict):
    """
    转换结构
    :param operate_vars_dict:
    :return:
    """
    result_vars_dict = defaultdict(list)
    if not operate_vars_dict:
        return result_vars_dict
    # 转换结构
    for k, v in operate_vars_dict.items():
        result_vars_dict[k] = []
        for i in v.values():
            if i:
                result_vars_dict[k].extend(i)
    return result_vars_dict


def get_field_var_dict(field_vars_dict, chart_data, dataset_field_vars_dict):
    """
    补充获取单图引用到的数据集字段绑定的变量
    :param field_vars_dict:
    :param chart_data:
    :param dataset_field_vars_dict:
    :return:
    """
    if not chart_data or not dataset_field_vars_dict:
        return field_vars_dict
    for section in [
        chart_data.get("dims"),
        chart_data.get("nums"),
        chart_data.get("comparisons"),
        chart_data.get("filters"),
        chart_data.get("zaxis"),
        chart_data.get("chart_params"),
        chart_data.get("desires"),
        chart_data.get("marklines"),
    ]:
        if not section:
            continue
        tmp_field_vars_dict = defaultdict(list)
        for single_item in section:
            match_field_id = single_item.get("dataset_field_id") or single_item.get("dim") or single_item.get("num")
            if match_field_id and dataset_field_vars_dict.get(match_field_id):
                tmp_field_vars_dict[match_field_id] = dataset_field_vars_dict.get(match_field_id)
        field_vars_dict.update(tmp_field_vars_dict)


def _collect_field(data_list, collected_field_id_set):
    """

    :param data_list:
    :param collected_field_id_set:
    :return:
    """
    if data_list:
        for i in data_list:
            collected_field_id_set.add(i.get("child_chart_field_id", ""))


def get_field_var_dict_for_penetrates(field_var_dict, chart_id, penetrates, dataset_field_vars_dict):
    """

    :param field_var_dict:
    :param chart_id:
    :param penetrates:
    :param dataset_field_vars_dict:
    :return:
    """
    for single_penetrate in penetrates:
        collected_field_id_set = set()
        if not single_penetrate or single_penetrate.get("chart_id") != chart_id:
            continue
        _collect_field(single_penetrate.get("relation", []), collected_field_id_set)
        _collect_field(single_penetrate.get("penetrate_filter_relation", []), collected_field_id_set)
        _collect_field(single_penetrate.get("penetrate_var_filter_relation", []), collected_field_id_set)
        if collected_field_id_set:
            for match_field_id in collected_field_id_set:
                if (
                    match_field_id
                    and dataset_field_vars_dict.get(match_field_id)
                    and match_field_id not in field_var_dict.keys()
                ):
                    field_var_dict.update({match_field_id: dataset_field_vars_dict.get(match_field_id)})


def get_field_var_dict_for_release_penetrates(
    field_var_dict, chart_id, chart_penetrate_field_dict, dataset_field_vars_dict
):
    """

    :param field_var_dict:
    :param chart_id:
    :param chart_penetrate_field_dict:
    :param dataset_field_vars_dict:
    :return:
    """
    collected_field_id_set = chart_penetrate_field_dict.get(chart_id, set())
    if collected_field_id_set:
        for match_field_id in collected_field_id_set:
            if (
                match_field_id
                and dataset_field_vars_dict.get(match_field_id)
                and match_field_id not in field_var_dict.keys()
            ):
                field_var_dict.update({match_field_id: dataset_field_vars_dict.get(match_field_id)})


def generate_chart_relation_dict(data, parent_key="chart_initiator_id", son_key="chart_responder_id"):
    """
    根据指定字段名拼装父子级关系数据
    :param data:
    :param parent_key:
    :param son_key:
    :return:
    """
    result = defaultdict(list)
    if not data:
        return result
    for item in data:
        parent_chart_id = item.get(parent_key)
        son_chart_id = item.get(son_key)
        if son_chart_id and parent_chart_id:
            result[son_chart_id].append(parent_chart_id)
    return result


def _generate_chart_penetrate_field_dict(charts):
    """

    :param charts:
    :return:
    """
    chart_penetrate_field_dict = {}
    if not charts:
        return chart_penetrate_field_dict
    for chart_id, single_chart in charts.items():
        collected_field_id_set = set()
        _collect_field(single_chart.get("penetrate_relation", []), collected_field_id_set)
        _collect_field(single_chart.get("penetrate_filter_relation", []), collected_field_id_set)
        _collect_field(single_chart.get("penetrate_var_filter_relation", []), collected_field_id_set)
        chart_penetrate_field_dict[chart_id] = collected_field_id_set
    return chart_penetrate_field_dict
