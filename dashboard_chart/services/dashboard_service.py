#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
dashboard service
"""

# ---------------- 标准模块 ----------------
from copy import deepcopy
import hashlib
import json
import logging
from collections import defaultdict
import uuid
import re
import random
import datetime as dt
import builtins
from urllib.parse import urlparse, urljoin, urlencode, urlunparse
import base64
from PIL import Image
from io import BytesIO
import hug
import traceback
from loguru import logger

# ---------------- 业务模块 ----------------
import time

from components.url import url_add_param
from components.ingrate_platform import IngratePlatformApi
from dashboard_chart.metadata.release_parser import ReleaseParser
from dmplib.saas.project import get_db, get_mysoft_erp, get_project_info
from dmplib.utils.strings import seq_id
from components.utils import str_equal
from base.dmp_constant import (
    DASHBOARD_EDITOR_CACHE_PREFIX,
    DASHBOARD_LAST_UPDATE_TIME_CACHE_KEY,
    TABLES_FOR_DELETE_DASHBOARD,
    EXTERNAL_SNOW_SUBJECT_DATASET_ID, EXTERNAL_MULTI_DIM_SUBJECT_DATASET_ID, SUPPORT_ERP_IMAGE_URL)
from base.errors import UserError
from dashboard_chart.repositories import dashboard_repository, editor_repository
from dataset import external_query_service
from dashboard_chart.services import (
    chart_service,
    released_dashboard_service,
    dashboard_extra_service,
    metadata_service,
    authority_service,
    external_dashboard_service
)
from dashboard_chart.dashboard_editor.metadata_comparator import MetadataComparator
from dashboard_chart.dashboard_editor.metadata_subject import MetadataSubject
from level_sequence.services import level_sequence_service
from dashboard_chart.dashboard_editor.editor.models import DashboardModel, DashboardChartModel, DashboardFilterModel, \
    DashboardGlobalParams, VarRelationsModel
from base import repository, service
from base.service import sort_tree_data, sort_report_tree_data
from rbac.services.data_permissions import data_permission_filter, data_permission_edit_filter
from dashboard_chart.repositories import dashboard_metadata_history_repository, chart_repository
from base.enums import (
    DashboardType,
    DashboardTypeSelector,
    DashboardTypeStatus,
    DashboardTypeAccessReleased,
    DashboardTerminalType,
    ApplicationType,
    DashboardJumpType,
    DashboardAnalysisType,
    ProjectValueAddedFunc,
    DistributeType, CopyDashboardType
)
# from user.repositories import user_repository
from user_group.repositories.user_group_repository import check_group_exists
# from dashboard_chart.metadata.metadata_schema.validator import EditorMetadataValidator
from components.log_setting import Debug
import jwt
from jwt import DecodeError, ExpiredSignatureError
from dmplib import config
from dmplib.redis import conn as conn_redis
import binascii
import os
import zipfile
from base.enums import DashboardJumpConfigStatus, DataReportType, DashboardStatus, DatasetType, DatasetVarValueSource
from level_sequence.models import DashboardLevelSequenceModel
from dmplib.hug import g
from components.mysoft import CloudAPI
from components.data_center_api import get_new_erp_datasource_model, get_erp_site_url

from dashboard_chart.utils.common import dashboard_fast_log_record
from dashboard_chart.models import DashboardModel as ChartDashboardModel
from dashboard_chart.models import ReleaseModel, DashboardGlobalParamsModel
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from flow.services import flow_service
from label.services import label_service
from dataset.services import dataset_service
from rbac import external_service as rbac_service
import requests
from urllib import request
from upload import external_service as external_service_upload
import io
from user.services.user_service import get_project_license
from dmplib.umbrella.core.tools import dispatch_old_env
from dmplib.umbrella.api import LicenseApi
from dataset.services.dataset_service import copy_datasets
from dashboard_chart.services.dashboard_released_design_service import del_dashboard_released_design_record
from dmplib.constants import ADMINISTRATORS_GROUP_ID
from base.dmp_constant import ALL_REDIRECT_TABLES, OLD_REDIRECT_TABLES

logger = logging.getLogger(__name__)
debugger = Debug(__name__)


# pylint: disable-msg=R0912


def _get_dashboard_filter_field(main_field_id_list, related_field_id_dict):
    query_field_external_data = dict()
    query_main_field_external_data = dict()
    if list(related_field_id_dict.keys()):
        try:
            query_field_external_data = external_query_service.get_multi_dataset_fields(
                list(related_field_id_dict.keys())
            )
        except Exception:
            raise UserError(message='报告级筛选关联数据集不存在，请核对')
    if len(main_field_id_list):
        try:
            query_main_field_external_data = external_query_service.get_multi_dataset_fields(main_field_id_list)
        except Exception:
            raise UserError(message='报告级筛选关联数据集不存在，请核对')
    return query_main_field_external_data, query_field_external_data


def get_dashboard_filters_v2(
        current_dataset_id: str, dashboard_filters: List[Any], dashboard_filter_relations: List[Any]
) -> List[Any]:
    """
    报告级筛选
    :param current_dataset_id:
    :param dashboard_filters:
    :param dashboard_filter_relations:
    :return:
    """
    # 如果数据集不是当前单图的数据集的筛选需要过滤掉
    table_field_id_dict = dict()

    if dashboard_filters:
        for single_dashboard_filter in dashboard_filters:
            filter_field_id = single_dashboard_filter.get('main_dataset_field_id')
            if filter_field_id and filter_field_id not in list(table_field_id_dict.keys()):
                table_field_id_dict.update({filter_field_id: single_dashboard_filter})

    # 批量获取字段的信息
    valid_dashboard_filters = get_valid_dashboard_filters(table_field_id_dict, current_dataset_id)
    if valid_dashboard_filters:
        # 从新的子表中获取报告筛选关系
        valid_dashboard_filters = chart_service.split_operators(
            valid_dashboard_filters, filter_source='dashboard_filters'
        )
        return valid_dashboard_filters

    # 如果当前单图对应数据集下的报告级筛选为空，则尝试从dashboard_dataset_field_relation表取
    query_main_field_external_data_dict = dict()
    related_field_id_dict, main_field_id_list = _get_field_id_info(dashboard_filter_relations, table_field_id_dict)

    query_main_field_external_data, query_field_external_data = _get_dashboard_filter_field(
        main_field_id_list, related_field_id_dict
    )

    if query_main_field_external_data:
        for query_single_main_field in query_main_field_external_data:
            main_field_id = query_single_main_field.get('id')
            if main_field_id and main_field_id not in list(query_main_field_external_data_dict.keys()):
                query_main_field_external_data_dict.update({main_field_id: query_single_main_field})

    valid_dashboard_filters = _append_valid_dashboard_filters(
        query_field_external_data, current_dataset_id, related_field_id_dict, query_main_field_external_data_dict
    )

    # 从新的子表中获取报告筛选关系
    valid_dashboard_filters = chart_service.split_operators(valid_dashboard_filters, filter_source='dashboard_filters')
    return valid_dashboard_filters


def get_valid_dashboard_filters(table_field_id_dict: Dict[Any, Any], current_dataset_id: str) -> List[Any]:
    """

    :param table_field_id_dict:
    :param current_dataset_id:
    :return:
    """
    valid_dashboard_filters = list()
    if not list(table_field_id_dict.keys()):
        return valid_dashboard_filters
    try:
        query_field_external_data = external_query_service.get_multi_dataset_fields(list(table_field_id_dict.keys()))
    except Exception:
        raise UserError(message='报告级筛选关联数据集不存在，请核对')
    if not query_field_external_data:
        return valid_dashboard_filters
    for query_single_field in query_field_external_data:
        field_id = query_single_field.get('id')
        dataset_id = query_single_field.get('dataset_id')
        # 字段所属数据集是当前单图数据集才生效
        if (dataset_id != current_dataset_id) and (
                dataset_id not in [EXTERNAL_SNOW_SUBJECT_DATASET_ID, EXTERNAL_MULTI_DIM_SUBJECT_DATASET_ID]):
            continue
        single_filter_data = table_field_id_dict.get(field_id)
        if not single_filter_data:
            continue
        single_filter_data.update(
            {
                'data_type': query_single_field.get('data_type'),
                'field_group': query_single_field.get('field_group'),
                'type': query_single_field.get('type'),
                'expression': query_single_field.get('expression'),
                'format': query_single_field.get('format'),
                'alias_name': query_single_field.get('alias_name'),
                'col_name': query_single_field.get('col_name'),
                'field_id': field_id,
                'origin_col_name': query_single_field.get('origin_col_name'),
                'alias': query_single_field.get('alias'),
            }
        )
        valid_dashboard_filters.append(single_filter_data)
    return valid_dashboard_filters


def _get_field_info_in_list_condition(
        single_related_filter, table_field_id_dict, related_field_id_dict, main_field_id_list
):
    """

    :param single_related_filter:
    :param table_field_id_dict:
    :param related_field_id_dict:
    :param main_field_id_list:
    :return:
    """
    for item in single_related_filter:
        main_dataset_field_id = item.get('main_dataset_field_id')
        choose_field_id_data = table_field_id_dict.get(main_dataset_field_id)
        if not choose_field_id_data:
            continue
        item.update(
            {
                'select_all_flag': choose_field_id_data.get('select_all_flag'),
                'operator': choose_field_id_data.get('operator'),
                'col_value': choose_field_id_data.get('col_value'),
            }
        )
        related_dataset_field_id = item.get('related_dataset_field_id')
        if related_dataset_field_id and related_dataset_field_id not in list(related_field_id_dict.keys()):
            related_field_id_dict.update({related_dataset_field_id: item})
        main_dataset_field_id = item.get('main_dataset_field_id')
        if main_dataset_field_id and main_dataset_field_id not in main_field_id_list:
            main_field_id_list.append(main_dataset_field_id)


def _get_field_info_in_dict_condition(single_related_filter, related_field_id_dict, main_field_id_list):
    """

    :param single_related_filter:
    :param related_field_id_dict:
    :param main_field_id_list:
    :return:
    """
    related_dataset_field_id = single_related_filter.get('related_dataset_field_id')
    if related_dataset_field_id and related_dataset_field_id not in list(related_field_id_dict.keys()):
        related_field_id_dict.update({related_dataset_field_id: single_related_filter})
    main_dataset_field_id = single_related_filter.get('main_dataset_field_id')
    if main_dataset_field_id and main_dataset_field_id not in main_field_id_list:
        main_field_id_list.append(main_dataset_field_id)


def _get_field_id_info(
        dashboard_filter_relations: List[Any], table_field_id_dict: Dict[Any, Any]
) -> Tuple[Dict[Any, Any], List[Any]]:
    """

    :param dashboard_filter_relations:
    :param table_field_id_dict:
    :return:
    """
    related_field_id_dict = dict()
    main_field_id_list = list()
    if not dashboard_filter_relations:
        return related_field_id_dict, main_field_id_list
    for single_related_filter in dashboard_filter_relations:
        if isinstance(single_related_filter, list):
            _get_field_info_in_list_condition(
                single_related_filter, table_field_id_dict, related_field_id_dict, main_field_id_list
            )
        elif isinstance(single_related_filter, dict):
            _get_field_info_in_dict_condition(single_related_filter, related_field_id_dict, main_field_id_list)
    return related_field_id_dict, main_field_id_list


def _append_valid_dashboard_filters(
        query_field_external_data: Dict[Any, Any],
        current_dataset_id: str,
        related_field_id_dict: Dict[Any, Any],
        query_main_field_external_data_dict: Dict[Any, Any],
) -> List[Any]:
    """

    :param query_field_external_data:
    :param current_dataset_id:
    :param related_field_id_dict:
    :param query_main_field_external_data_dict:
    :return:
    """
    valid_dashboard_filters = list()
    if not query_field_external_data:
        return valid_dashboard_filters
    for query_single_field in query_field_external_data:
        dataset_id = query_single_field.get('dataset_id')
        field_id = query_single_field.get('id')
        # 字段所属数据集是当前单图数据集才生效
        if dataset_id == current_dataset_id:
            single_filter_data = related_field_id_dict.get(field_id)
            single_filter_data.update(
                {
                    'data_type': query_single_field.get('data_type'),
                    'field_group': query_single_field.get('field_group'),
                    'type': query_single_field.get('type'),
                    'expression': query_single_field.get('expression'),
                    'format': query_single_field.get('format'),
                    'alias_name': query_single_field.get('alias_name'),
                    'col_name': query_single_field.get('col_name'),
                    'field_id': field_id,
                }
            )
            # 将关联数据集字段的col_name赋值给主数据集的字段
            single_main_field_data = query_main_field_external_data_dict.get(
                single_filter_data.get('main_dataset_field_id')
            )
            # 直连，api数据集等存在col_name匹配的问题，需要获取其他字段值来尝试匹配
            if single_main_field_data:
                single_filter_data['col_name'] = single_main_field_data.get('col_name')
                single_filter_data['alias_name'] = single_main_field_data.get('alias_name')
                single_filter_data['origin_col_name'] = single_main_field_data.get('origin_col_name')
                single_filter_data['alias'] = single_main_field_data.get('alias')

            valid_dashboard_filters.append(single_filter_data)
    return valid_dashboard_filters


def get_operators_by_filter_id(dashboard_filter_id):
    """
    从新的子表获取筛选关系
    :param dashboard_filter_id:
    :return:
    """
    if not dashboard_filter_id:
        return list()
    # 获取新表数据
    operators = dashboard_repository.get_dashboard_filter_relation(dashboard_filter_id)
    return list() if not operators else operators


def batch_get_operators(dashboard_filters: List[Any]) -> List[Any]:
    """
    获取operators数据
    :param dashboard_filters:
    :return:
    """
    if not dashboard_filters:
        return dashboard_filters
    for dashboard_filter in dashboard_filters:
        # 从新的子表中获取筛选条件
        dashboard_filter['operators'] = get_operators_by_filter_id(dashboard_filter['id'])
    return dashboard_filters


def get_dashboard_filter(dashboard_filter_id):
    """
    获取报告筛选详情
    :param dashboard_filter_id:
    :return:
    """
    return dashboard_repository.get_dashboard_filter_by_id(dashboard_filter_id)


def _check_platforms_params(
        dashboard: Dict[str, Union[str, None, int, datetime]], include_platforms: List[Any],
        exclude_platforms: List[Any]
) -> bool:
    """
    校验platforms参数
    :param dashboard:
    :param include_platforms:
    :param exclude_platforms:
    :return:
    """
    check_flag = True
    if dashboard.get('type') == DashboardType.File.value:
        if include_platforms and (dashboard.get('platform') not in include_platforms):
            check_flag = False
        if exclude_platforms and (dashboard.get('platform') in exclude_platforms):
            check_flag = False
    return check_flag


def get_dash_result(
        dashboards: List[Dict[str, Union[str, None, int, datetime]]],
        include_platforms: List[Any],
        exclude_platforms: List[Any],
) -> List[Dict[str, Any]]:
    """
    获取报告列表数据
    :param dashboards:
    :param include_platforms:
    :param exclude_platforms:
    :return:
    """
    result = []
    tmp_dict = {}
    for dashboard in dashboards:
        if not isinstance(dashboard, dict):
            continue
        dashboard['sub'] = []
        # code格式: 0001-、0001-0001-
        level_code = dashboard.get('level_code')
        if level_code:
            level_code_arr = str(level_code).rstrip('-').split('-')
            level_code_arr.pop()
            parent_code = "{}-".format('-'.join(level_code_arr)) if level_code_arr else ''
        else:
            parent_code = ''
        if parent_code not in tmp_dict:
            tmp_dict[level_code] = dashboard

            #  如果是文件 需要进行包含和排除判断
            if not _check_platforms_params(dashboard, include_platforms, exclude_platforms):
                continue
            result.append(dashboard)
        else:
            tmp_dict[level_code] = dashboard
            #  如果是文件 需要进行包含和排除判断
            if not _check_platforms_params(dashboard, include_platforms, exclude_platforms):
                continue
            tmp_dict.get(parent_code)['sub'].append(dashboard)
    return result


def __convert_dashboards_to_tree(
        dashboards: List[Dict[str, Union[str, None, int, datetime]]],
        parent_id: str,
        include_platforms: Optional[List[Any]] = None,
        exclude_platforms: Optional[List[Any]] = None,
        order_by: Optional[str] = None,
        reverse: bool = True,
) -> Dict[str, List[Dict[str, Any]]]:
    result = get_dash_result(dashboards, include_platforms, exclude_platforms)
    dash_ret_data = {'route': []}
    # route格式[{'name':'一级文件夹','id':'11'},{'name':'二级文件夹','id':'22'}]
    if parent_id:
        dash_one = dashboard_repository.get_dashboard(parent_id)
        if dash_one:
            level_code = dash_one['level_code'] or ''
            level_code_arr = str(level_code).rstrip('-').split('-')
            route_code_list = []
            for code in level_code_arr:
                if not route_code_list:
                    route_code_list.append('{}-'.format(code))
                else:
                    last_code = '{}{}-'.format(route_code_list[-1], code)
                    route_code_list.append(last_code)
            dash_ret_data['route'] = repository.get_list('dashboard', {'level_code': route_code_list},
                                                         ['id', 'name']) or {}
    # dash_ret_data['tree'] = _sort_dashboard(result)
    # 上面的排序有问题，使用新的排序
    dash_ret_data['tree'] = sort_tree_data(result, order_by=order_by, reverse=reverse)

    return dash_ret_data


def __convert_to_list_param(param: str) -> List[Any]:
    if isinstance(param, str):
        param = param.strip(' ')
        if param == '':
            return []
        return param.split(',')
    if isinstance(param, list):
        return (','.join(param)).split(',')
    if isinstance(param, tuple):
        return (','.join(param)).split(',')
    raise UserError(message='非法的参数！')


@data_permission_filter('dashboard-view')
def get_dashboard_list(**kwargs) -> Dict[str, List[Dict[str, Any]]]:
    """
    :param exclude_platforms:
    :param include_platforms:
    :param status:
    :param end_time:
    :param start_time:
    :获取数据看板文件夹树形结构
    :date 2017/7/4
    :param str parent_id :
    :param is_multiple_screen :
    :param order_by :
    :param reverse :
    :param create_type :
    :param new_layout_type :
    :return list:
    """
    parent_id = kwargs.get('parent_id', '')
    is_multiple_screen = kwargs.get('is_multiple_screen', 0)
    include_platforms = __convert_to_list_param(kwargs.get('include_platforms', ''))
    exclude_platforms = __convert_to_list_param(kwargs.get('exclude_platforms', ''))
    status = kwargs.get('status', 0)
    file_type = kwargs.get('file_type', '')
    start_time = kwargs.get('begin_time', None)
    end_time = kwargs.get('end_time', None)
    order_by = kwargs.get("order_by", "mtime")  # 排序依据
    reverse = bool(int(kwargs.get("reverse", 1)))  # 升 降
    create_type = kwargs.get('create_type')  # 是否过滤显示新版报告
    new_layout_type = kwargs.get('new_layout_type')  # 布局类型
    terminal_type = kwargs.get('terminal_type')  # 终端类型
    include_child_file = kwargs.get('include_child_file', 0)  # 是否包含子报告，默认不包含
    include_hd_report = kwargs.get('include_hd_report', 0)  # 是否包含HD报告，默认不包含
    include_external_report = kwargs.get('include_external_report', 0)
    application_type = [ApplicationType.Dashboard.value]
    if int(include_hd_report) == 1:
        application_type.append(ApplicationType.HD_Dashboard.value)
    if int(include_external_report) == 1:
        application_type.append(ApplicationType.External_Dashboard.value)
        application_type.append(ApplicationType.FineReport.value)

    if terminal_type and 'mobile' in __convert_to_list_param(terminal_type):
        terminal_type = ''

    list_data = dashboard_repository.get_dash_list_by_group_id(
        **{
            "parent_id": parent_id,
            "is_multiple_screen": is_multiple_screen,
            "status": status,
            "file_type": file_type,
            "start_time": start_time,
            "end_time": end_time,
            "create_type": create_type,
            "new_layout_type": new_layout_type,
            "terminal_type": terminal_type,
            "include_child_file": include_child_file,
            "application_type": application_type,
        }
    )
    # 标准化layout
    reformat_dashboard_layout(list_data)

    # 补充报告额外信息
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data)
    return __convert_dashboards_to_tree(list_data, parent_id, include_platforms, exclude_platforms, order_by, reverse)


def reformat_dashboard_layout(list_data: List[Dict[str, Any]]):
    """
    标准化layout
    """
    new_layout_type_map = {0: 'free', 1: 'grid'}
    for item in list_data:
        try:
            item["layout"] = json.loads(item["layout"])
            if "mode" not in item["layout"]:
                item["layout"]["mode"] = new_layout_type_map.get(item["new_layout_type"], 'free')
        except Exception:
            pass


@data_permission_filter('large_screen-view')
def get_large_screen_list(**kwargs) -> Dict[str, List[Dict[str, Any]]]:
    """
    获取酷炫大屏的报告列表
    """
    parent_id = kwargs.get('parent_id', '')
    is_multiple_screen = kwargs.get('is_multiple_screen', 0)
    include_platforms = __convert_to_list_param(kwargs.get('include_platforms', ''))
    exclude_platforms = __convert_to_list_param(kwargs.get('exclude_platforms', ''))
    status = kwargs.get('status', 0)
    file_type = kwargs.get('file_type', '')
    start_time = kwargs.get('begin_time', None)
    end_time = kwargs.get('end_time', None)
    order_by = kwargs.get("order_by", "mtime")  # 排序依据
    reverse = bool(int(kwargs.get("reverse", 1)))  # 升 降
    create_type = kwargs.get('create_type')  # 是否过滤显示新版报告
    new_layout_type = kwargs.get('new_layout_type')  # 布局类型
    include_child_file = kwargs.get('include_child_file', 0)  # 是否包含子报告，默认不包含
    application_type = [ApplicationType.LargeScreen.value]

    list_data = dashboard_repository.get_dash_list_by_group_id(
        **{
            "parent_id": parent_id,
            "is_multiple_screen": is_multiple_screen,
            "status": status,
            "file_type": file_type,
            "start_time": start_time,
            "end_time": end_time,
            "create_type": create_type,
            "new_layout_type": new_layout_type,
            "include_child_file": include_child_file,
            "application_type": application_type,
        }
    )
    # 标准化layout
    reformat_dashboard_layout(list_data)

    # 补充报告额外信息
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data)
    return __convert_dashboards_to_tree(list_data, parent_id, include_platforms, exclude_platforms, order_by, reverse)


def get_dashboard_list_not_permission(**kwargs):
    parent_id = kwargs.get('parent_id', '')
    is_multiple_screen = kwargs.get('is_multiple_screen', 0)
    data_type = kwargs.get('data_type', '')
    application_type = kwargs.get('application_type', 0)
    status = kwargs.get('status', 0)
    order_by = kwargs.get("order_by", "mtime")  # 排序依据
    reverse = bool(int(kwargs.get("reverse", 1)))  # 升降
    list_data = dashboard_repository.get_dash_list_by_group_id(
        **{
            "parent_id": parent_id,
            "is_multiple_screen": is_multiple_screen,
            "status": status,
            "application_type": application_type,
        }
    )

    if data_type == 'report_center':
        order_by = "name"
        reverse = False
        return __convert_report_center_to_tree(list_data, parent_id, order_by=order_by, reverse=reverse)
    else:
        return __convert_dashboards_to_tree(list_data, parent_id, order_by=order_by, reverse=reverse)


@data_permission_filter('multiple_screen-view')
def get_screen_list(**kwargs):
    """
    :获取数据看板文件夹树形结构
    :date 2017/7/4
    :param str parent_id :
    :param int is_multiple_screen :
    :return list:
    """
    parent_id = kwargs.get('parent_id', '')
    status = kwargs.get('status', 0)
    order_by = kwargs.get("order_by", "mtime")
    reverse = bool(int(kwargs.get("reverse", 1)))
    create_type = kwargs.get('create_type')
    new_layout_type = kwargs.get('new_layout_type')  # 布局类型
    include_platforms = __convert_to_list_param(kwargs.get('include_platforms', ''))
    exclude_platforms = __convert_to_list_param(kwargs.get('exclude_platforms', ''))
    list_data = dashboard_repository.get_dash_list_by_group_id(
        **{
            "parent_id": parent_id,
            "is_multiple_screen": 1,
            "status": status,
            "create_type": create_type,
            "new_layout_type": new_layout_type,
        }
    )
    # 补充报告额外信息
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data)
    return __convert_dashboards_to_tree(list_data, parent_id, include_platforms, exclude_platforms, order_by, reverse)


def su_self_report_user(**kwargs):
    # 切换自助报表获取用户身份
    from user.repositories.user_repository import get_user_info

    su_user = kwargs.get('su', '')
    if su_user == g.account:
        return
    if su_user:
        # 只有管理员才能切换用户视角
        check_current_user_is_admin()
        user = get_user_info(account=su_user) or {}
        g.account = su_user
        g.userid = user.get('id') or ''


@data_permission_filter('self_service-view')
def get_self_report_list(**kwargs):
    """
    :获取数据看板文件夹树形结构
    :date 2017/7/4
    :param str parent_id :
    :return list:
    """
    su_self_report_user(**kwargs)
    parent_id = kwargs.get('parent_id', '')
    name = kwargs.get('name', '')
    order_by = kwargs.get("order_by", "mtime")
    reverse = bool(int(kwargs.get("reverse", 1)))
    include_platforms = __convert_to_list_param(kwargs.get('include_platforms', ''))
    exclude_platforms = __convert_to_list_param(kwargs.get('exclude_platforms', ''))
    application_type = ApplicationType.SelfService.value
    is_search = bool(name)
    is_admin = bool(_is_current_user_is_admin())
    list_data = dashboard_repository.get_self_dashboard_list(is_search=is_search, is_admin=is_admin, **kwargs)
    # 补充报告额外信息
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data, application_type)
    return __convert_dashboards_to_tree(list_data, parent_id, include_platforms, exclude_platforms, order_by, reverse)


def get_all_self_report_list(**kwargs):
    """
    :获取数据看板文件夹树形结构
    :date 2017/7/4
    :param str parent_id :
    :return list:
    """
    parent_id = kwargs.get('parent_id', '')
    status = kwargs.get('status', 0)
    order_by = kwargs.get("order_by", "mtime")
    reverse = bool(int(kwargs.get("reverse", 1)))
    include_platforms = __convert_to_list_param(kwargs.get('include_platforms', ''))
    exclude_platforms = __convert_to_list_param(kwargs.get('exclude_platforms', ''))
    application_type = 1
    list_data = dashboard_repository.get_dash_list_by_group_id(
        **{"parent_id": parent_id, "status": status, "application_type": application_type}
    )
    # 补充报告额外信息
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data, application_type)
    return __convert_dashboards_to_tree(list_data, parent_id, include_platforms, exclude_platforms, order_by, reverse)


def _is_current_user_is_admin():
    from user.repositories import user_repository
    is_admin = user_repository.is_admin_by_user_id(g.userid)
    return bool(is_admin)


def check_current_user_is_admin():
    if not _is_current_user_is_admin():
        raise UserError(message='当前没有管理员权限！')


def get_self_report_create_grouped_users(**kwargs):
    # 管理员获取自助报表创建者分组
    check_current_user_is_admin()
    kwargs['curr_user'] = g.account
    kwargs['application_type'] = ApplicationType.SelfService.value
    data = dashboard_repository.get_self_report_created_users(**kwargs)
    for user in data:
        if not user.get('name'):
            user['name'] = user.get('account') or ''
    return data


# @data_permission_filter('self_service-view')
def sort_self_report_list(**kwargs):
    """
    自助报表排序
    :param kwargs:
    :return:
    """
    current = kwargs.get("current")
    location = kwargs.get("location")
    if not current or not location:
        raise UserError(message='参数错误')
    current_sort = int(current.get("sort"))
    location_sort = int(location.get("sort"))

    with get_db() as db:
        db.begin_transaction()

        # 第一步：确定是往上拖还是往下拖, 从小往大排
        direction = "up" if current_sort > location_sort else "down"

        # 第二步：更新排序
        parent_id = db.query_scalar(
            'select parent_id from dashboard where id=%(_id)s',
            params={'_id': current.get('id')}
        )
        if direction == "up":
            sort_calculate = '`sort`+ 1'
            sort_where = f"sort >= {location_sort} and sort < {current_sort}"
        else:
            sort_calculate = '`sort` - 1'
            sort_where = f"sort > {current_sort} and sort <= {location_sort}"
        db.exec_sql(
            f"update dashboard set `sort` = {sort_calculate} where parent_id=%(parent_id)s and {sort_where}",
            params={'parent_id': parent_id},
            commit=False
        )

        # 第三步：更新current的排序
        db.update('dashboard', {"sort": location_sort}, {'id': current.get("id")}, commit=False)

        db.commit()

    return get_self_report_list(order_by='sort', parent_id=kwargs.get('parent_id', ''))


@data_permission_filter('report_center-view')
def get_report_center_list(**kwargs):
    """
    获取报表中心的列表数据
    :param kwargs:
    :return:
    """
    parent_id = kwargs.get('parent_id', '')
    file_type = kwargs.get('file_type', '')
    status = kwargs.get('status', 0)
    order_by = kwargs.get("order_by", "mtime")
    reverse = bool(int(kwargs.get("reverse", 1)))
    start_time = kwargs.get('begin_time', None)
    end_time = kwargs.get('end_time', None)
    include_child_file = kwargs.get('include_child_file', 1)
    application_type = [ApplicationType.SimpleReport.value, ApplicationType.ActiveReport.value]
    list_data = dashboard_repository.get_dash_list_by_group_id(
        **{"parent_id": parent_id, "status": status, "file_type": file_type, "application_type": application_type,
           "include_child_file": include_child_file, "start_time": start_time,
           "end_time": end_time}
    )
    # 补充报告额外信息
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data, application_type)
    # 如果为空，则查询的所有数据，文件和文件夹都按照名称排序，只有单独查询文件列表的时候才按照修改时间排序
    if not file_type or file_type == "folder":
        order_by = "name"
        reverse = False
    return __convert_report_center_to_tree(list_data, parent_id, None, None, order_by, reverse)


def __convert_report_center_to_tree(
        dashboards: List[Dict[str, Union[str, None, int, datetime]]],
        parent_id: str,
        include_platforms: Optional[List[Any]] = None,
        exclude_platforms: Optional[List[Any]] = None,
        order_by: Optional[str] = None,
        reverse: bool = True,
) -> Dict[str, List[Dict[str, Any]]]:
    result = get_dash_result(dashboards, include_platforms, exclude_platforms)
    dash_ret_data = {'route': []}
    # route格式[{'name':'一级文件夹','id':'11'},{'name':'二级文件夹','id':'22'}]
    if parent_id:
        dash_one = dashboard_repository.get_dashboard(parent_id)
        if dash_one:
            level_code = dash_one['level_code'] or ''
            level_code_arr = str(level_code).rstrip('-').split('-')
            route_code_list = []
            for code in level_code_arr:
                if not route_code_list:
                    route_code_list.append('{}-'.format(code))
                else:
                    last_code = '{}{}-'.format(route_code_list[-1], code)
                    route_code_list.append(last_code)
            dash_ret_data['route'] = repository.get_list('dashboard', {'level_code': route_code_list},
                                                         ['id', 'name']) or {}
    # dash_ret_data['tree'] = _sort_dashboard(result)
    # 上面的排序有问题，使用新的排序
    dash_ret_data['tree'] = sort_report_tree_data(result, order_by=order_by, reverse=reverse)

    return dash_ret_data


@data_permission_filter('report_center-view')
def get_report_permissions_from_report_center(report_id):
    order_by = 'mtime'
    reverse = True
    sql = 'select * from dashboard where id = %(report_id)s limit 1'
    list_data = repository.get_data_by_sql(sql, params={'report_id': report_id})
    application_type = [ApplicationType.SelfService.value, ApplicationType.ActiveReport.value]
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data, application_type)
    return __convert_dashboards_to_tree(list_data, '', None, None, order_by, reverse)


@data_permission_filter('dashboard-view')
def search_dashboard(**kwargs):
    """
    :数据看板搜索接口
    :date 2017/7/24
    :param str name :
    :param create_type :
    :return list:
    """
    application_type = kwargs.get('application_type', 0)
    include_hd_report = kwargs.get('include_hd_report', 0)  # 是否包含HD报告，默认不包含
    include_external_report = kwargs.get('include_external_report', 0)
    if application_type == ApplicationType.Dashboard.value:
        application_type = [ApplicationType.Dashboard.value]
        if int(include_hd_report) == 1:
            application_type.append(ApplicationType.HD_Dashboard.value)
        if int(include_external_report) == 1:
            application_type.append(ApplicationType.External_Dashboard.value)
    kwargs['application_type'] = application_type
    list_data = dashboard_repository.search_dash_list(**kwargs)
    # 补充报告额外信息
    list_data = dashboard_extra_service.batch_get_dashboard_extra_data(list_data, application_type=application_type)
    return list_data


def _get_dashboard_editor_lock_key(dashboard_id: str) -> str:
    """
    获取报告编辑器锁key
    :param dashboard_id:
    :return:
    """
    return ''.join((DASHBOARD_EDITOR_CACHE_PREFIX, dashboard_id))


def get_dashboard_editor_lock(dashboard_id: str) -> bool:
    """
    获取报告编辑器锁
    :param dashboard_id:
    :return: Boolean
    """
    lock_key = _get_dashboard_editor_lock_key(dashboard_id)
    count = 0
    while True:
        # 每个锁最大30S自动超时
        if conn_redis().set_nx_ex(lock_key, 1, ex=30, nx=True):
            return True
        count += 1
        time.sleep(0.6)
        # 最大重复获取20次则认为失败
        if count >= 20:
            return False


def release_dashboard_editor_lock(dashboard_id: str) -> None:
    """
    释放报告编辑器锁
    :param dashboard_id:
    :return:
    """
    lock_key = _get_dashboard_editor_lock_key(dashboard_id)
    conn_redis().delete(lock_key)


def get_dashboard_last_update_time(dashboard_id: str) -> Optional[int]:
    """
    获取指定报告的最后更新时间
    :param dashboard_id:
    :return:
    """
    return conn_redis().hget(DASHBOARD_LAST_UPDATE_TIME_CACHE_KEY, dashboard_id)


def update_dashboard_last_update_time(dashboard_id: str, update_time: int) -> None:
    """
    更新报告编辑器指定报告最后编辑时间
    :param dashboard_id:
    :param update_time:
    :return:
    """
    conn_redis().hset(DASHBOARD_LAST_UPDATE_TIME_CACHE_KEY, dashboard_id, update_time)


def update_metadata(metadata):
    """
    更新元数据
    :param metadata:
    :return:
    """
    # 当前更新时间(精确到毫秒数)
    update_time = round(time.time() * 1000)
    dashboard = metadata.get("dashboard")
    if not dashboard or not dashboard.get("id"):
        raise UserError(message="元数据格式异常，请重试！")
    # 获取编辑锁
    if not get_dashboard_editor_lock(dashboard.get("id")):
        raise UserError(message="报告编辑器正在锁定状态，请稍后再试！")
    last_update_time = get_dashboard_last_update_time(dashboard.get("id"))
    # 如果有最后更新时间 且当前时间小于最后更新时间 释放锁 不更新元数据
    if last_update_time and update_time <= last_update_time:
        release_dashboard_editor_lock(dashboard.get("id"))
        return True, []
    comparator = MetadataComparator(metadata)
    # 接入校验器，验证元数据有效性
    # _, _ = EditorMetadataValidator(data=metadata).validate_metadata()
    # 调试阶段schema校验异常先不抛出错误，只需上报sentry以备排查问题
    # if not validate_code:
    #     # 释放锁
    #     release_dashboard_editor_lock(dashboard.get("id"))
    #     raise UserError(message=validate_msg)
    try:
        result, errors = comparator.do_edit()
    except UserError as e:
        release_dashboard_editor_lock(dashboard.get("id"))
        # 日志记录
        errors = [{"err_code": 5000, "msg": str(e.message)}]
        dashboard_fast_log_record(dashboard, errors)
        return False, errors
    if errors and isinstance(errors, dict):
        errors = [{"err_code": 5000, "msg": "更新失败"}]
    if result:
        # 更新最后更新时间
        update_dashboard_last_update_time(dashboard.get("id"), update_time)
    # 释放锁
    release_dashboard_editor_lock(dashboard.get("id"))
    # 日志记录
    dashboard_fast_log_record(dashboard, errors)
    return result, errors


def _debug_insert_table(table2dict):
    for table, data_list in table2dict.items():
        for i, item in enumerate(data_list):
            sort = item.get('sort')
            if sort and not isinstance(sort, int):
                print('error')
            if 'parent_id' in item and item['parent_id'] is None:
                print('parent_id is None', i)


def update_dataset_info(replace_map: dict):
    # data['dataset_map'].update({dataset_id: helper.new_dataset_id})
    # data['field_id_map'].update(helper.fields_map)
    # data['col_name_map'].update(helper.col_name_map)
    # data['var_map'].update(helper.vars_map)

    # 判断是否选择了复制数据集
    dataset_map = getattr(g, '_dataset_map', {})
    if not dataset_map:
        return replace_map

    # 更新之前的替换map，包含数据集ID以及字段信息
    dataset_ids_map = dataset_map.get('dataset_map', {})
    field_ids_map = dataset_map.get('field_id_map', {})
    var_ids_map = dataset_map.get('var_map', {})
    col_name_map = dataset_map.get('col_name_map', {})

    replace_map.update(dataset_ids_map)
    replace_map.update(field_ids_map)
    replace_map.update(var_ids_map)
    replace_map.update(col_name_map)
    return replace_map


def generate_insert_table(pre_meta, dashboard_id, parent_id, dashboard_name):
    import time
    meta_sub = MetadataSubject(metadata=pre_meta, edit_source="copy")
    models, errors = meta_sub.do_edit()

    new_dashboard_id = seq_id()
    replace_map = {dashboard_id: new_dashboard_id}
    global_params_redirect_map = {}
    get_replace_map(models, replace_map, new_dashboard_id, global_params_redirect_map)
    update_dataset_info(replace_map)
    meta_json = json.dumps(pre_meta)
    pre_dashboard_info = meta_sub.metadata_storage().metadata_dashboard()
    terminal_type = pre_dashboard_info.get("terminal_type", "") if pre_dashboard_info else ""
    st = time.time()
    for old_str, new_str in replace_map.items():
        meta_json = meta_json.replace(old_str, new_str)
    print('replace_time:', time.time() - st)
    meta_sub = MetadataSubject(metadata=json.loads(meta_json), edit_source="copy")
    models, errors = meta_sub.do_edit()
    for model in models:
        if isinstance(model, DashboardModel):
            model.parent_id = parent_id or ''
            model.level_code = level_sequence_service.generate_dashboard_level_code(model.parent_id)
            model.name = dashboard_name
            model.status = 0
            model.type_selector = DashboardTypeSelector.Custom.value
            model.biz_code = str(uuid.uuid4()).replace('-', '')
            # 分发类型的报告被复制后 必须改为0
            model.distribute_type = 0
            # 复制报告发布状态不复制
            model.type_access_released = DashboardTypeAccessReleased.UserRole.value
            model.status = 0
            model.share_secret_key = ''
            model.terminal_type = terminal_type
            break
    table2dict = MetadataComparator.merge_insert(models)
    _debug_insert_table(table2dict)

    return new_dashboard_id, table2dict, global_params_redirect_map, replace_map


def generate_update_table(pre_meta):
    import time
    replace_map = {}
    update_dataset_info(replace_map)
    meta_json = json.dumps(pre_meta)
    st = time.time()
    for old_str, new_str in replace_map.items():
        meta_json = meta_json.replace(old_str, new_str)
    print('replace_time:', time.time() - st)
    meta_sub = MetadataSubject(metadata=json.loads(meta_json), edit_source="copy")
    models, errors = meta_sub.do_edit()
    table2dict = MetadataComparator.merge_insert(models)
    _debug_insert_table(table2dict)

    return table2dict, replace_map


# def get_dashboard_chart_ref_dataset_ids(table2dict):
#     # 获取组件引用的数据集
#     dashboard_chart_records = table2dict.get('dashboard_chart', [])
#     chart_ref_dataset_ids = list(
#         set(record.get('source') for record in dashboard_chart_records if record.get('source'))
#     )
#     # 获取报表引用的数据集
#     dashboard_filter_records = table2dict.get('dashboard_filter', [])
#     dashboard_ref_dataset_ids = list(
#         set(record.get('main_dataset_field_id') for record in dashboard_filter_records if record.get('main_dataset_field_id'))
#     )
#     ref_dataset_ids = [*chart_ref_dataset_ids, *dashboard_ref_dataset_ids]
#     return list(set(ref_dataset_ids))


def get_replace_map(models, replace_map, new_dashboard_id, global_params_redirect_map):
    """
    处理replace_map
    :param models:
    :param replace_map:
    :param new_dashboard_id:
    :return:
    """
    for model in models:
        pk = model.get_pk()
        if not pk:
            continue
        if isinstance(model, DashboardModel):
            continue
        new_id = seq_id()
        if isinstance(model, DashboardFilterModel):
            # dashboard_filter 主键有特定的规则生成
            new_id = _generate_dashboard_filter_id(new_dashboard_id, model.main_dataset_field_id)
        if isinstance(model, VarRelationsModel):
            # 这个场景的id设计设计上有点问题，有业务含义，有_1,_2后缀，只能后端处理了
            new_id = _generate_var_relation_new_id(new_id, model)
        if isinstance(model, DashboardChartModel) and not model.parent_id and model.level_code:
            # 要生成新的level_code
            new_level_code = level_sequence_service.generate_dashboard_chart_level_code()
            replace_map['"level_code": "%s' % model.level_code] = '"level_code": "%s' % new_level_code
        # 这里只是 "id" => "new_id" 做的替换，如果不是字符串引用将来改成id替换
        old_str = getattr(model, pk)
        if replace_map.get(old_str):
            print('duplicate')
        replace_map[old_str] = new_id
        if isinstance(model, DashboardGlobalParams):
            global_params_redirect_map[old_str] = new_id


def _generate_var_relation_new_id(new_id, model: VarRelationsModel):
    if len(model.id) == 36:
        return new_id
    elif '_' in model.id:
        idx = model.id.index('_')
        return f'{new_id}{model.id[idx:]}'
    else:
        return new_id


@data_permission_edit_filter("dashboard-copy")
def copy_dashboard(dashboard_id, parent_id, dashboard_name, target_dataset_id, is_copy_dataset=0, copy_target_type=''):
    """
    通过报告id复制报告
    :param dashboard_id:
    :param parent_id:
    :param dashboard_name:
    :return:
    """
    dashboard = repository.get_data('dashboard', {'id': dashboard_id})
    if copy_target_type == CopyDashboardType.CHILD_TO_FILE.value:
        dashboard["type"] = DashboardType.File.value
    if not dashboard:
        raise UserError(message="数据看板不存在")
    count = 0
    while True:
        repeat_flag = validate_repeat_name(
            dashboard_name, parent_id, dashboard.get('type'), application_type=dashboard.get('application_type')
        )
        if repeat_flag and count >= 10:
            raise UserError(message='存在重名的报告：' + dashboard_name)
        if not repeat_flag:
            break
        count += 1
        dashboard_name += '_副本'
    all_dashboards = get_sub_child_dashboard(dashboard)
    all_dashboards[0]['name'] = dashboard_name
    copy_info = []
    index = 0
    for dashboard in all_dashboards:
        if index > 0:
            dashboard['name'] = dashboard['name'] + '_副本'
        index += 1

    # 复制数据集
    if str(is_copy_dataset) == '1':
        ref_dataset_ids = dashboard_repository.get_dashboard_ref_dataset_ids(dashboard_id)
        if ref_dataset_ids:
            dataset_map = copy_datasets(dataset_ids=ref_dataset_ids, folder_id=target_dataset_id)
            setattr(g, '_dataset_map', dataset_map)

    _copy_child_dashboard(all_dashboards[0], parent_id, all_dashboards, copy_info)

    with get_db() as db:
        errors = []
        try:
            db.begin_transaction()
            editor_repository.batch_operate_editor_modes_no_commit(
                db, [], [table2dict.get('table2dict') for table2dict in copy_info], []
            )
            update_other_info(copy_info, parent_id, copy_target_type)
            update_dashboard_copy_ref(copy_info)
            db.commit()
        except Exception as e:
            logger.error('复制报告失败：%s' % traceback.format_exc())
            db.rollback()
            errors.append('插入表dashboard异常: %s' % str(e))
        return (copy_info[0], errors) if not errors else ({}, errors)  # 返回复制后的根节点ID


@data_permission_edit_filter("dashboard-copy")
def update_new_dataset_id(dashboard_id, target_dataset_id):
    """
    通过报告id复制报告
    :param dashboard_id: 看板或文件夹名称
    :param target_dataset_id: 数据集复制到
    :return:
    """
    dashboard = repository.get_data('dashboard', {'id': dashboard_id})
    if not dashboard:
        raise UserError(message="数据看板不存在")
    all_file_dashboards = []
    if dashboard.get('type') == DashboardType.Folder.value:
        # 获取所有看板
        all_file_dashboard_list = [file_dashboard for file_dashboard in
                                   get_child_dashboard(dashboard.get("id"), is_all=False) if
                                   file_dashboard.get('type') == DashboardType.File.value]
        for file_dashboard in all_file_dashboard_list:
            all_file_dashboards.extend(get_child_dashboard(file_dashboard.get("id"), is_all=False))
    else:
        # 获取所有看板
        all_file_dashboards = get_child_dashboard(dashboard.get("id"), is_all=True)

    # 获取所有与看板子看板
    all_dashboards = []
    for child_dashboard in all_file_dashboards:
        all_dashboards.append(get_sub_child_dashboard(child_dashboard))

    dashboard_dataset_maps = []
    for update_dashboard_model in all_dashboards:
        copy_info = []
        # 复制数据集
        ref_dataset_ids = dashboard_repository.get_dashboard_ref_dataset_ids(update_dashboard_model[0].get("id"))
        if ref_dataset_ids:
            dataset_map = copy_datasets(dataset_ids=ref_dataset_ids, folder_id=target_dataset_id)
            setattr(g, '_dataset_map', dataset_map)

        _update_child_dashboard(update_dashboard_model[0], update_dashboard_model, copy_info)

        with get_db() as db:
            errors = []
            try:
                db.begin_transaction()
                editor_repository.batch_operate_update_editor_models(
                    db, [table2dict.get('table2dict') for table2dict in copy_info]
                )
                # 更新其他字段
                for dashboard in copy_info:
                    update_dashboard_dict = {
                        'id': dashboard.get('id'),
                        'parent_id': dashboard.get('parent_id'),
                        'level_code': dashboard.get('level_code'),
                        'type': dashboard['type'],
                        'name': dashboard.get('name'),
                        'terminal_type': dashboard.get('terminal_type'),
                    }
                    repository.update('dashboard', update_dashboard_dict, {'id': dashboard.get('id')})
                db.commit()
            except Exception as e:
                logger.error('更新数据集id失败：%s' % traceback.format_exc())
                db.rollback()
                errors.append('插入表dashboard异常: %s' % str(e))
                break

        dataset_map = getattr(g, '_dataset_map', {})
        dataset_maps = dataset_map.get('dataset_map', {})
        try:
            for dataset_id in ref_dataset_ids:
                old_dataset = dataset_service.get_dataset(dataset_id)
                from dataset.services import dataset_define_service
                # 删除数据集
                dataset_define_service.delete_dataset(dataset_id)
                # 修改新数据集的名称
                dataset_define_service.rename_dataset(
                    dataset_maps[dataset_id],
                    old_dataset['name'],
                    old_dataset['type'],
                    old_dataset['parent_id']
                )
                # 修改新数据集的文件夹名称
                dataset_define_service.move_dataset(dataset_maps[dataset_id],
                                                    old_dataset['parent_id'])
        except Exception as e:
            logger.error('更新数据集id失败：%s' % traceback.format_exc())
            errors.append('更新数据集信息异常: %s' % str(e))
        # 返回修改的数据集id对应关系
        dashboard_dataset_maps.append(
            {"dashboard": update_dashboard_model[0].get("id"), "dataset_maps": dataset_maps})

    return (dashboard_dataset_maps, errors) if not errors else ({}, errors)  # 返回复制后的根节点ID


@data_permission_edit_filter("dashboard-copy")
def update_new_dataset_id_by_folder(folder_id, is_recursion=True, is_delete_dataset=True):
    """
    通过报告id复制报告
    :param folder_id: 数据集文件夹id
    :param is_recursion: 是否递归获取数据集
    :param is_delete_dataset: 是否删除数据库重命名
    :return:
    """
    if is_recursion and (is_recursion == 'false' or is_recursion == '0' or is_recursion == 0):
        is_recursion = False
    else:
        is_recursion = True

    if is_delete_dataset and (is_delete_dataset == 'false' or is_delete_dataset == '0' or is_delete_dataset == 0):
        is_delete_dataset = False
    else:
        is_delete_dataset = True

    datasets = dataset_service.recursion_get_dataset_tree_for_dataset_ids(parent_id=folder_id,
                                                                          is_recursion=is_recursion)
    if not datasets or len(datasets) < 1:
        raise UserError(message="数据集不存在")
    copy_info = []
    ref_dataset_ids = [dataset.get('id') for dataset in datasets]
    # 根据数据集查询报告信息
    dashboards = []
    related_reports = []
    related_report_designs = []
    report_id_list = []
    for ref_dataset_id in ref_dataset_ids:
        dashboards.extend(external_dashboard_service.get_related_dashboard_by_dataset_id(ref_dataset_id))
        related_reports.extend(external_dashboard_service.get_related_report_by_dataset_id(ref_dataset_id))

    all_file_dashboards = []
    if dashboards and len(dashboards) > 0:
        dashboards = repository.get_list('dashboard',
                                         {'id': [dashboard.get('dashboard_id') for dashboard in dashboards]})

    if related_reports and len(related_reports) > 0:
        report_id_list = [item['id'] for item in related_reports]
        related_reports = repository.get_list(
            'myrptdetail',
            {'MyRptDetailId': report_id_list})
        related_report_designs = repository.get_list(
            'myrptdetail_design',
            {'MyRptDetailId': report_id_list})

    for dashboard in dashboards:
        all_file_dashboards.append(get_sub_child_dashboard(dashboard))

    if ref_dataset_ids:
        dataset_map = copy_datasets(dataset_ids=ref_dataset_ids, folder_id=folder_id)
        setattr(g, '_dataset_map', dataset_map)

    dashboard_dataset_map = {}
    errors = []
    for update_dashboard_model in all_file_dashboards:
        try:
            _update_child_dashboard(update_dashboard_model[0], update_dashboard_model, copy_info)
        except Exception as e:
            logger.error('复制前准备看板报错：%s' % traceback.format_exc())
            errors.append('复制前准备看板报错: %s' % str(e))
            break
        with get_db() as db:
            try:
                db.begin_transaction()
                for dashboard in copy_info:
                    # 删除无效的跳转
                    jump_configs = repository.get_list('dashboard_jump_config', {'dashboard_id': dashboard.get('id')})
                    for jump_config in jump_configs:
                        if jump_config.get('target_type') in ['dashboard', 'self_service']:
                            delete_error_jump_relations(jump_config.get('id'))

                editor_repository.batch_operate_update_editor_models(
                    db, [table2dict.get('table2dict') for table2dict in copy_info]
                )

                for dashboard in copy_info:
                    # 更新其他字段
                    update_dashboard_dict = {
                        'id': dashboard.get('id'),
                        'parent_id': dashboard.get('parent_id'),
                        'level_code': dashboard.get('level_code'),
                        'type': dashboard['type'],
                        'name': dashboard.get('name'),
                        'terminal_type': dashboard.get('terminal_type'),
                    }
                    repository.update('dashboard', update_dashboard_dict, {'id': dashboard.get('id')})
                db.commit()
            except Exception as e:
                logger.error('看板更新数据集id失败：%s' % traceback.format_exc())
                db.rollback()
                errors.append('看板更新数据集id失败: %s' % str(e))
                break

    # 复杂报表替换
    if (related_reports and len(related_reports) > 0) or (related_report_designs and len(related_report_designs) > 0):
        with get_db() as db:
            db.begin_transaction()
            batch_update_reports(related_reports, db, dataset_map, 'myrptdetail')
            batch_update_reports(related_report_designs, db, dataset_map, 'myrptdetail_design')
            db.commit()

    dataset_map = getattr(g, '_dataset_map', {})
    dataset_maps = dataset_map.get('dataset_map', {})
    if is_delete_dataset and len(errors) < 1:
        for dataset_id in ref_dataset_ids:
            try:
                old_dataset = dataset_service.get_dataset(dataset_id)
                from dataset.services import dataset_define_service
                # 删除数据集
                dataset_define_service.delete_dataset(dataset_id)
                # 修改新数据集的名称
                dataset_define_service.rename_dataset(
                    dataset_maps[dataset_id],
                    old_dataset['name'],
                    old_dataset['type'],
                    old_dataset['parent_id']
                )
                # 修改新数据集的文件夹名称
                dataset_define_service.move_dataset(dataset_maps[dataset_id],
                                                    old_dataset['parent_id'])
            except Exception as e:
                logger.error('更新数据集id失败：%s' % traceback.format_exc())
                errors.append(f'更新数据集信息{dataset_id}异常: %s' % str(e))
    # 返回修改的数据集id对应关系
    dashboard_dataset_map['dashboards'] = [dashboard[0].get("id") for dashboard in all_file_dashboards]
    dashboard_dataset_map['report_centers'] = report_id_list
    dashboard_dataset_map['dataset_maps'] = dataset_map
    dashboard_dataset_map['error_infos'] = errors

    return dashboard_dataset_map, errors  # 返回复制后的根节点ID


def repair_new_dataset(dataset_map, report_id_list, dashboard_ids):
    """
    通过报告id复制报告
    :param dataset_map: 需要替换的内容
    :param report_id_list: 修改复杂报表ids
    :param dashboard_ids: 修改看板ids
    :return:
    """
    setattr(g, '_dataset_map', dataset_map)
    dashboards = []
    all_file_dashboards = []
    if dashboard_ids and len(dashboard_ids) > 0:
        dashboards = repository.get_list('dashboard', {'id': dashboard_ids})
    for dashboard in dashboards:
        all_file_dashboards.append(get_sub_child_dashboard(dashboard))

    related_reports = []
    related_report_designs = []
    if report_id_list and len(report_id_list) > 0:
        related_reports = repository.get_list(
            'myrptdetail',
            {'MyRptDetailId': report_id_list})
        related_report_designs = repository.get_list(
            'myrptdetail_design',
            {'MyRptDetailId': report_id_list})

    copy_info = []
    errors = []
    dashboard_dataset_map = {}
    for update_dashboard_model in all_file_dashboards:
        try:
            _update_child_dashboard(update_dashboard_model[0], update_dashboard_model, copy_info)
        except Exception as e:
            logger.error('复制前准备看板报错：%s' % traceback.format_exc())
            errors.append('复制前准备看板报错: %s' % str(e))
            break
        with get_db() as db:
            try:
                db.begin_transaction()
                editor_repository.batch_operate_update_editor_models(
                    db, [table2dict.get('table2dict') for table2dict in copy_info]
                )
                # 更新其他字段
                for dashboard in copy_info:
                    update_dashboard_dict = {
                        'id': dashboard.get('id'),
                        'parent_id': dashboard.get('parent_id'),
                        'level_code': dashboard.get('level_code'),
                        'type': dashboard['type'],
                        'name': dashboard.get('name'),
                    }
                    repository.update('dashboard', update_dashboard_dict, {'id': dashboard.get('id')})
                db.commit()
            except Exception as e:
                logger.error('看板更新数据集id失败：%s' % traceback.format_exc())
                db.rollback()
                errors.append('看板更新数据集id失败: %s' % str(e))
                break
    # 复杂报表替换
    if (related_reports and len(related_reports) > 0) or (related_report_designs and len(related_report_designs) > 0):
        with get_db() as db:
            db.begin_transaction()
            batch_update_reports(related_reports, db, dataset_map, 'myrptdetail')
            batch_update_reports(related_report_designs, db, dataset_map, 'myrptdetail_design')
            db.commit()

    dashboard_dataset_map['dashboards'] = [dashboard[0].get("id") for dashboard in all_file_dashboards]
    dashboard_dataset_map['report_centers'] = report_id_list
    dashboard_dataset_map['dataset_maps'] = dataset_map
    dashboard_dataset_map['error_infos'] = errors

    return dashboard_dataset_map, errors  # 返回复制后的根节点ID


def batch_update_reports(related_reports, db, dataset_map, table):
    for report in related_reports:
        rpt_xml = report['RptXML']
        ds_define = report['DsDefine']
        rpt_var = report['RptVar']
        report_id = report['MyRptDetailId']
        dataset_id_map = dataset_map.get('dataset_map', {})
        dataset_field_map = dataset_map.get('field_id_map', {})
        for old_id in dataset_id_map:
            new_id = dataset_id_map[old_id]
            rpt_xml = rpt_xml.replace(old_id, new_id)
            rpt_var = rpt_var.replace(old_id, new_id)
            ds_define = ds_define.replace(old_id, new_id)

        for old_id in dataset_field_map:
            new_id = dataset_field_map[old_id]
            rpt_xml = rpt_xml.replace(old_id, new_id)

        param = {
            'report_id': report_id,
            'rpt_xml': rpt_xml,
            'ds_define': ds_define,
            'rpt_var': rpt_var
        }
        db.exec_sql(
            f'''
            UPDATE {table} SET RptXML=%(rpt_xml)s,DsDefine=%(ds_define)s,RptVar=%(rpt_var)s 
            WHERE MyRptDetailId=%(report_id)s
            ''', param)


def update_dashboard_copy_ref(copy_info):
    """
    处理报告复制的时候，来源关系处理
    """
    if not copy_info:
        return
    for copy_data in copy_info:
        dashboard_id = copy_data.get('source_id') or ''
        new_dashboard_id = copy_data.get('id') or ''
        dashboard_extra = dashboard_extra_service.get_dashboard_extra_data(dashboard_id)
        root_dash_id = dashboard_extra.get('root_dash_id')
        if not root_dash_id:
            # 这个报告没有dashboard_extra记录，新建一条记录
            dashboard_extra_service.update_dashboard_extra_data(dashboard_id, root_dash_id=dashboard_id)
            # 更新复制出来的报告信息
            dashboard_extra_service.update_dashboard_extra_data(new_dashboard_id, root_dash_id=dashboard_id,
                                                                copy_dash_id=dashboard_id)
        else:
            # 更新复制出来的报告信息
            dashboard_extra_service.update_dashboard_extra_data(new_dashboard_id, root_dash_id=root_dash_id,
                                                                copy_dash_id=dashboard_id)


def update_other_info(list_dashboard, parent_id='', copy_target_type=''):
    update_dashboard_other_field(list_dashboard)  # 维护level_code，type
    update_dashboard_jump_info(list_dashboard, parent_id, copy_target_type)  # 维护跳转目标ID
    update_child_dashboard_global_params_redirect(list_dashboard)


def update_child_dashboard_global_params_redirect(list_dashboard):
    # 替换子报表复制的全局参数跳转关系
    redirect_tables = ALL_REDIRECT_TABLES
    dashboard_ids = {i.get('id') for i in list_dashboard if i.get('id')}
    for dashboard_info in list_dashboard:
        global_params_redirect_map = dashboard_info.get('global_params_redirect_map', {})
        if not global_params_redirect_map:
            continue
        for old_id, new_id in global_params_redirect_map.items():
            for table in redirect_tables:
                sql = f"""select * from `{table}` where `global_params_id`=%(global_params_id)s and `dashboard_id` in %(dashboard_ids)s"""
                datas = repository.get_data_by_sql(sql, params={'dashboard_ids': list(dashboard_ids),
                                                                'global_params_id': old_id})
                if not datas:
                    continue
                for data in datas:
                    conditions = data.copy()
                    conditions.pop('global_params_id', None)
                    repository.update_data(table, condition=conditions, data={'global_params_id': new_id}, commit=False)


def update_dashboard_other_field(list_dashboard):
    # 子报表复制，跳转配置信息target需修改为复制后的报表
    for dashboard in list_dashboard:
        parent_dashboard = repository.get_data('dashboard', {'id': dashboard.get('parent_id')})
        dashboard['type'] = DashboardType.CHILD_FILE.value
        if not parent_dashboard or parent_dashboard.get('type') in [DashboardType.Folder.value]:
            dashboard['type'] = DashboardType.File.value
        dashboard['level_code'] = level_sequence_service.generate_dashboard_level_code(dashboard.get('parent_id'))
        if dashboard.get('type') == DashboardType.CHILD_FILE.value:
            repeat_flag = validate_repeat_name(
                dashboard.get('name'),
                dashboard.get('parent_id'),
                dashboard.get('type'),
                True,
                dashboard.get('id'),
                dashboard.get('application_type'),
            )
            if repeat_flag:
                new_name = get_child_file_name(dashboard.get('name'), dashboard.get('parent_id'))
                if new_name == dashboard['name']:
                    new_name = new_name + '(1)'
                dashboard['name'] = new_name

        update_dashboard_dict = {
            'id': dashboard.get('id'),
            'parent_id': dashboard.get('parent_id'),
            'level_code': dashboard.get('level_code'),
            'type': dashboard['type'],
            'name': dashboard.get('name'),
        }
        repository.update('dashboard', update_dashboard_dict, {'id': dashboard.get('id')})


def _get_all_replace_map(list_dashboard):
    result = {}
    for dashboard_data in list_dashboard:
        one_map = dashboard_data.get('replace_map') or {}
        result.update(one_map)
    return result


def get_outer_parent_dashboard(list_dashboard):
    # 找到最外层的父级报告，接入包含子报告的话
    for dashboard_data in list_dashboard:
        if dashboard_data.get('type') == 'FILE':
            return dashboard_data
    # logger.error(f'没有找到父报告！！！')
    return {}


def get_dashboard_file(parent_id, for_num):
    if for_num < 10:
        dashboard_data = repository.get_data('dashboard', {"id": parent_id}, ['level_code', 'type', 'parent_id'],
                                             multi_row=False) or {}
        if not dashboard_data:
            return {}
        if dashboard_data.get("type") == DashboardType.File.value:
            return dashboard_data
        else:
            return get_dashboard_file(dashboard_data.get("parent_id"), for_num + 1)


def update_dashboard_jump_info(list_dashboard, parent_id='', copy_target_type=''):
    source_target_map = {dashboard.get('source_id'): dashboard.get('id') for dashboard in list_dashboard}
    # 将所有处理过的替换id合到一起
    all_deal_replace_map = _get_all_replace_map(list_dashboard)
    outer_parent_dashboard = get_outer_parent_dashboard(list_dashboard)
    # copy_target_type='CopyDashboardType.CHILD_TO_CHILD'子报报表复制到子报表的时候，需要根据parent_id往上递归找到根目录
    if copy_target_type == CopyDashboardType.CHILD_TO_CHILD.value and not outer_parent_dashboard:
        outer_parent_dashboard = get_dashboard_file(parent_id, 0)
    for dashboard in list_dashboard:
        dashboard_id = dashboard.get('id')
        _update_copy_jump_config(dashboard_id, source_target_map, outer_parent_dashboard)
        _update_old_direct_relations(dashboard_id, source_target_map, all_deal_replace_map)
        # _update_copy_jump_relation(dashboard_id, source_target_map)
        # _update_copy_var_jump_relation(dashboard_id, source_target_map)
        # _update_copy_params_jump(dashboard_id, source_target_map)


def _update_copy_jump_config(dashboard_id, source_target_map, outer_parent_dashboard):
    jump_configs = repository.get_list('dashboard_jump_config', {'dashboard_id': dashboard_id})
    # dashboard = repository.get_data('dashboard', {'id': dashboard_id}) or {}
    for jump_config in jump_configs:
        if jump_config.get('target_type') in ['dashboard', 'self_service']:  # 只处理dashboard类型的跳转，url类型不处理
            target_dashboard = repository.get_data('dashboard', {'id': jump_config.get('target')}) or {}
            if target_dashboard and target_dashboard.get('type') == DashboardType.File.value:  # 跳转到其它报表的父报表
                continue
            # 只处理跳转到子报表的
            target = source_target_map.get(jump_config['target'])
            if target:
                jump_config['target'] = target
                target_dashboard = repository.get_data('dashboard', {'id': target}) or {}
                repository.update('dashboard_jump_config', jump_config, {'id': jump_config.get('id')})
            ##### 解决复制组件丢失跳转配置的问题
            # else:
            #     repository.delete_data('dashboard_jump_config', {'id': jump_config.get('id')})
            # dashboard_level_code = dashboard.get('level_code', '')
            outer_parent_dashboard_level_code = outer_parent_dashboard.get('level_code', '')
            target_dashboard_level_code = target_dashboard.get('level_code', '')
            if not all([outer_parent_dashboard_level_code, target_dashboard_level_code]):
                continue
            # 如果是跳到其他报告的子报表的，删除这个错误跳转
            if not target_dashboard_level_code.startswith(outer_parent_dashboard_level_code):
                repository.delete_data('dashboard_jump_config', {'id': jump_config.get('id')})
                delete_error_jump_relations(jump_config.get('id'))
            ##### 解决复制组件丢失跳转配置的问题


def delete_error_jump_relations(jump_config_id):
    # 删除跳转关系
    if not jump_config_id:
        return
    redirect_tables = ALL_REDIRECT_TABLES
    for table in redirect_tables:
        repository.delete_data(table, {'jump_config_id': jump_config_id})


def _deal_redirect_to_child_dashboard_relation(relation: dict, all_deal_replace_map: dict):
    # 处理跳转到子报告的关系替换
    target_filter_id = relation.get('target_filter_id', '')
    target_filter_field_id = relation.get('target_filter_field_id', '')
    if target_filter_id and target_filter_id in all_deal_replace_map:
        relation['target_filter_id'] = all_deal_replace_map[target_filter_id]
    if target_filter_field_id and target_filter_field_id in all_deal_replace_map:
        relation['target_filter_field_id'] = all_deal_replace_map[target_filter_field_id]
    return relation


# def _judge_is_redirect_child_dashboard(relation, parent_dashboard_id):
#     # 判断是否是跳转到子报告
#     sql = """
#     select parent_id from dashboard where id = (
#         select target from dashboard_jump_config where id = %(jump_config_id)s limit 1
#     )
#     """
#     parent_id = repository.get_data_scalar_by_sql(sql, {'jump_config_id': relation.get('jump_config_id', '')})
#     if parent_id and parent_id == parent_dashboard_id:
#         return True
#     else:
#         return False


def _get_parent_dashboard_ids_by_jump_config_ids(relations):
    # 返回jump_config_id与跳转目标报告的map
    jump_config_ids = list(set(
        [relation.get('jump_config_id', '') for relation in relations if relation.get('jump_config_id', '')]
    ))
    if not jump_config_ids:
        return {}
    sql = """
    select dd.parent_id, djc.id as jump_config_id from dashboard as dd 
    join dashboard_jump_config as djc 
    on djc.target = dd.id and djc.target_type = 'dashboard'
    where djc.id in %(jump_config_ids)s
    """
    jump_parent_dashboard_ids = repository.get_data_by_sql(sql, {'jump_config_ids': jump_config_ids}) or {}
    return {
        data.get('jump_config_id', ''): data.get('parent_id', '')
        for data in jump_parent_dashboard_ids
        if data.get('jump_config_id', '') and data.get('parent_id', '')
    }


def _update_old_direct_relations(dashboard_id, source_target_map, all_deal_replace_map):
    old_tables = OLD_REDIRECT_TABLES
    for old_table_name in old_tables:
        _update_single_direct_relations(dashboard_id, old_table_name, source_target_map, all_deal_replace_map)


def _update_single_direct_relations(dashboard_id, table_name, source_target_map, all_deal_replace_map):
    # 处理子报表复制的各个跳转表的跳转关系
    dashboard_jump_relation = repository.get_list(table_name, {'dashboard_id': dashboard_id})
    jump_parent_dashboard_map = _get_parent_dashboard_ids_by_jump_config_ids(dashboard_jump_relation)
    for jump_relation in dashboard_jump_relation:
        jump_config_id = jump_relation.get('jump_config_id')
        is_child = bool(jump_parent_dashboard_map.get(jump_config_id) == dashboard_id)
        if is_child:
            old_jump_relation = deepcopy(jump_relation)
            dashboard_filter_id = jump_relation.get('dashboard_filter_id')
            update_data = {}
            real_filter_id = _get_real_filter_id(dashboard_filter_id, source_target_map)
            if real_filter_id:
                update_data["dashboard_filter_id"] = real_filter_id
            new_jump_relation = _deal_redirect_to_child_dashboard_relation(jump_relation, all_deal_replace_map)
            update_data["target_filter_id"] = new_jump_relation.get('target_filter_id')
            update_data["target_filter_field_id"] = new_jump_relation.get('target_filter_field_id')
            repository.update(
                table_name, conditions=old_jump_relation, data=update_data
            )


# def _update_copy_jump_relation(dashboard_id, source_target_map):
#     dashboard_jump_relation = repository.get_list('dashboard_jump_relation', {'dashboard_id': dashboard_id})
#     for jump_relation in dashboard_jump_relation:
#         dashboard_filter_id = jump_relation.get('dashboard_filter_id')
#         if dashboard_filter_id:
#             real_filter_id = _get_real_filter_id(dashboard_filter_id, source_target_map)
#             # 如果跳转的是非子报表，不需要处理
#             if not real_filter_id:
#                 continue
#             repository.update(
#                 'dashboard_jump_relation',
#                 {"dashboard_filter_id": real_filter_id},
#                 {
#                     'jump_config_id': jump_relation.get('jump_config_id'),
#                     'dashboard_chart_id': jump_relation.get('dashboard_chart_id'),
#                     'dashboard_id': jump_relation.get('dashboard_id'),
#                     'dataset_field_id': jump_relation.get('dataset_field_id'),
#                 },
#             )


# def _update_copy_var_jump_relation(dashboard_id, source_target_map):
#     var_jump_relations = repository.get_list('dashboard_vars_jump_relation', {'dashboard_id': dashboard_id})
#     for vars_jump_relation in var_jump_relations:
#         dashboard_filter_id = vars_jump_relation.get('dashboard_filter_id')
#         if dashboard_filter_id:
#             real_filter_id = _get_real_filter_id(dashboard_filter_id, source_target_map)
#             # 如果跳转的是非子报表，不需要处理
#             if not real_filter_id:
#                 continue
#             repository.update(
#                 'dashboard_vars_jump_relation',
#                 {"dashboard_filter_id": real_filter_id},
#                 {
#                     'jump_config_id': vars_jump_relation.get('jump_config_id'),
#                     'dashboard_chart_id': vars_jump_relation.get('dashboard_chart_id'),
#                     'dashboard_id': vars_jump_relation.get('dashboard_id'),
#                     'dataset_field_id': vars_jump_relation.get('dataset_field_id'),
#                 },
#             )
#
#
# def _update_copy_params_jump(dashboard_id, source_target_map):
#     params_jumps = repository.get_list('dashboard_chart_params_jump', {'dashboard_id': dashboard_id})
#     for params_jump in params_jumps:
#         dashboard_filter_id = params_jump.get('dashboard_filter_id')
#         if dashboard_filter_id:
#             real_filter_id = _get_real_filter_id(dashboard_filter_id, source_target_map)
#             # 如果跳转的是非子报表，不需要处理
#             if not real_filter_id:
#                 continue
#             repository.update(
#                 'dashboard_chart_params_jump',
#                 {"dashboard_filter_id": real_filter_id},
#                 {
#                     'jump_config_id': params_jump.get('jump_config_id'),
#                     'dashboard_chart_id': params_jump.get('dashboard_chart_id'),
#                     'dashboard_id': params_jump.get('dashboard_id'),
#                     'param_dataset_field_id': params_jump.get('param_dataset_field_id'),
#                     'source_id': params_jump.get('source_id'),
#                 },
#             )


def _get_real_filter_id(dashboard_filter_id, source_target_map):
    if not dashboard_filter_id:
        return None
    dashboard_filter = repository.get_data('dashboard_filter', {'id': dashboard_filter_id})
    if not dashboard_filter:
        return None
    target_dashboard_id = source_target_map.get(dashboard_filter.get('dashboard_id'))
    if not target_dashboard_id:
        return None
    main_dataset_field_id = dashboard_filter.get('main_dataset_field_id')
    real_filter_id = _generate_dashboard_filter_id(target_dashboard_id, main_dataset_field_id)
    return real_filter_id


def _copy_child_dashboard(current_dashboard, target_id, all_dashboards, copy_info: []):
    _, pre_meta = metadata_service.get_screens_preview_metadata_v2(current_dashboard.get('id'))
    new_dashboard_id, table2dict, global_params_redirect_map, replace_map = generate_insert_table(
        pre_meta, current_dashboard.get('id'), target_id, current_dashboard.get('name')
    )
    copy_info.append(
        {
            'id': new_dashboard_id,
            'parent_id': target_id,
            'source_id': current_dashboard.get('id'),
            'name': current_dashboard.get('name'),
            'type': current_dashboard.get('type'),
            'platform': current_dashboard.get('platform'),
            'new_layout_type': current_dashboard.get('new_layout_type'),
            'application_type': current_dashboard.get('application_type'),
            'analysis_type': current_dashboard.get('analysis_type'),
            'line_height': current_dashboard.get('line_height'),
            'table2dict': table2dict,
            'global_params_redirect_map': global_params_redirect_map,
            'replace_map': replace_map,
        }
    )
    for child_dashboard in all_dashboards:
        if child_dashboard.get('parent_id') == current_dashboard.get('id'):  # 子报表复制
            _copy_child_dashboard(child_dashboard, new_dashboard_id, all_dashboards, copy_info)


def _update_child_dashboard(current_dashboard, all_dashboards, copy_info: []):
    _, pre_meta = metadata_service.get_screens_preview_metadata_v2(current_dashboard.get('id'))
    table2dict, replace_map = generate_update_table(pre_meta)
    copy_info.append(
        {
            'id': current_dashboard.get('id'),
            'parent_id': current_dashboard.get('parent_id'),
            'source_id': current_dashboard.get('source_id'),
            'name': current_dashboard.get('name'),
            'type': current_dashboard.get('type'),
            'terminal_type': current_dashboard.get('terminal_type'),
            'level_code': current_dashboard.get('level_code'),
            'platform': current_dashboard.get('platform'),
            'new_layout_type': current_dashboard.get('new_layout_type'),
            'application_type': current_dashboard.get('application_type'),
            'analysis_type': current_dashboard.get('analysis_type'),
            'line_height': current_dashboard.get('line_height'),
            'table2dict': table2dict,
            'replace_map': replace_map,
        }
    )
    for child_dashboard in all_dashboards:
        if child_dashboard.get('parent_id') == current_dashboard.get('id'):  # 子报表复制
            _update_child_dashboard(child_dashboard, all_dashboards, copy_info)


def copy_dashboard_with_no_permission(dashboard_id, parent_id, dashboard_name):
    """
    通过报告id复制报告，不校验是否有权限
    :param dashboard_id:
    :param parent_id:
    :param dashboard_name:
    :return:
    """
    _, pre_meta = metadata_service.get_screens_preview_metadata_v2(dashboard_id)
    new_dashboard_id, table2dict, global_params_redirect_map, replace_map = generate_insert_table(pre_meta,
                                                                                                  dashboard_id,
                                                                                                  parent_id,
                                                                                                  dashboard_name)
    re, errors = editor_repository.batch_operate_editor_modes([], [table2dict], [])
    return (new_dashboard_id, errors) if re else (re, errors)


def _generate_dashboard_filter_id(dashboard_id, main_dataset_field_id):
    """
    生成报告筛选ID
    :param dashboard_id:
    :param main_dataset_field_id:
    :return:
    """
    _value = str(dashboard_id + main_dataset_field_id)
    md5_obj = hashlib.md5()
    md5_obj.update(_value.encode("utf-8"))
    md5_value = md5_obj.hexdigest()
    return '-'.join([md5_value[:8], md5_value[8:12], md5_value[12:16], md5_value[16:20], md5_value[20:]])


def get_dashboard_dataset_data(
        dashboard_id: str,
) -> Dict[str, Dict[str, Union[str, None, bool, List[Dict[str, Union[str, int, None]]]]]]:
    """
    获取报告下的数据集信息
    :param dashboard_id:
    :return:
    """
    try:
        result_dataset_dict = dict()
        query_source_data = repository.get_data(
            'dashboard_chart', {'dashboard_id': dashboard_id}, ['id', 'source'], multi_row=True
        )
        if not query_source_data:
            return result_dataset_dict
        not_existed_datasets = set()
        for single_data in query_source_data:
            single_dataset_id = single_data.get('source', '')
            if not single_dataset_id:
                continue
            if single_dataset_id and single_dataset_id not in result_dataset_dict.keys():
                result_dataset_dict[single_dataset_id] = dict()
            if result_dataset_dict.get(single_dataset_id):
                continue
            # 数据集的数据
            query_extra_dataset_data = external_query_service.get_dataset(single_dataset_id)
            if query_extra_dataset_data is None:
                not_existed_datasets.add(single_dataset_id)
                continue
            # 数据集的字段数据
            query_extra_dataset_field_data = external_query_service.get_dataset_fields(single_dataset_id)
            query_extra_dataset_data['fields'] = query_extra_dataset_field_data

            result_dataset_dict[single_dataset_id] = query_extra_dataset_data
        if not_existed_datasets:
            raise Exception(f'请检查数据集【{",".join(list(not_existed_datasets))}】是否存在！')
        return result_dataset_dict
    except Exception as e:
        message = '获取报告下的数据集信息异常,异常信息：{}'.format(e)
        logging.error(message)
        raise UserError(message=message)


def _get_single_dashbaord_insert_tables(screen_dashboard_ids, model, to_insert_table2list, top_dashboard):
    meta_list = dashboard_metadata_history_repository.get_history_by_ids(screen_dashboard_ids)
    for meta in meta_list:
        meta = json.loads(meta['metadata'])
        table, item = ReleaseParser.parse_dashboard(meta, {"snapshot_id": model.id})
        if not to_insert_table2list[table]:
            # 单屏
            top_dashboard['dashboard_filters'] = item['dashboard_filters']
            top_dashboard['selectors'] = item['selectors']
            to_insert_table2list[table].append(top_dashboard)
        to_insert_table2list[table].append(item)
        table, items = ReleaseParser.parse_chart(meta, {"snapshot_id": model.id})
        to_insert_table2list[table].extend(items)
    return to_insert_table2list


def _op_user_group_access_permission(model, to_insert_table2list, db):
    if int(model.type_access_released) == DashboardTypeAccessReleased.UserGroups.value:
        db.delete("user_group_dashboard", {"dashboard_id": model.id})
        # 校验group是否存在
        if not check_group_exists(model.user_groups):
            raise UserError(message="提交的用户组不存在")
        for group in model.user_groups:
            to_insert_table2list['user_group_dashboard'].append({"group_id": group, "dashboard_id": model.id})
    return to_insert_table2list


@data_permission_edit_filter("dashboard-edit")
def release(model):
    """发布报告"""
    model.validate()
    dashboard = dashboard_repository.get_dashboard(model.id)
    if not dashboard:
        raise UserError(message="报告不存在")
    dashboard["status"] = model.status
    dashboard["share_secret_key"] = model.view_passwd
    dashboard["type_access_released"] = model.type_access_released

    db = get_db()
    # 要插入的数据 表名到数据列表的映射
    to_insert_table2list = defaultdict(list)
    try:
        # 开启事务
        db.begin_transaction()
        # 清除旧数据
        db.delete("dashboard_released_snapshot_dashboard", {"snapshot_id": model.id})
        db.delete("dashboard_released_snapshot_chart", {"snapshot_id": model.id})
        db.delete("screen_dashboard", {"dashboard_id": model.id, "type": DashboardTypeStatus.Release.value})
        # 处理用户组查看权限
        to_insert_table2list = _op_user_group_access_permission(model, to_insert_table2list, db)

        if model.status == 1:
            # 最上面层级的dashboard
            top_dashboard = get_top_screen_dashboard(dashboard)
            top_dashboard.update(
                {
                    "status": model.status,
                    "share_secret_key": model.view_passwd,
                    "type_access_released": model.type_access_released,
                }
            )
            if dashboard["is_multiple_screen"] == 1:
                screens = repository.get_data(
                    "screen_dashboard",
                    {"dashboard_id": model.id, "type": DashboardTypeStatus.Draft.value},
                    ["dashboard_id", "screen_id", "rank"],
                    True,
                    [("rank", "asc")],
                )
                to_insert_table2list['dashboard_released_snapshot_dashboard'].append(top_dashboard)
            else:
                screens = [{"dashboard_id": dashboard["id"], "screen_id": dashboard["id"], "rank": 1}]
            screen_dashboard_ids = []
            for screen in screens:
                to_insert_table2list['screen_dashboard'].append(
                    {
                        "screen_id": screen["screen_id"],
                        "type": DashboardTypeStatus.Release.value,
                        "dashboard_id": model.id,
                        "rank": screen["rank"],
                        "id": seq_id(),
                    }
                )
                screen_dashboard_ids.append(screen["screen_id"])
            # 单报告的数据列表
            to_insert_table2list = _get_single_dashbaord_insert_tables(
                screen_dashboard_ids, model, to_insert_table2list, top_dashboard
            )
        db.update(
            "dashboard",
            {
                "status": model.status,
                "share_secret_key": model.view_passwd,
                "type_access_released": model.type_access_released,
            },
            {"id": model.id},
        )
        for table, data_list in to_insert_table2list.items():
            db.insert_multi_data(table, data_list, list(data_list[0].keys()))
        released_dashboard_service.delete_metadata_dashboard_cache(dashboard)
        # 取消发布的情况，需要清理dashboard_extra的登记时间
        if int(model.status) == DashboardTypeStatus.Draft.value:
            dashboard_extra_service.reset_edit_and_released_on(model.id)
        # 报告发布的情况，需要登记当前报告的发布时间
        elif int(model.status) == DashboardTypeStatus.Release.value:
            dashboard_extra_service.reset_edit_or_released_on(model.id, "edit_on")
            dashboard_extra_service.update_dashboard_released_on(model.id)

        db.commit()
    except UserError as e:
        db.rollback()
        message = "报告发布失败，异常信息:{}".format(str(e))
        raise UserError(message=message)
    except Exception as e:
        db.rollback()
        logger.error(e)
        raise e
    return True


def get_top_screen_dashboard(dashboard):
    """得到第一屏的数据"""
    keys = [
        "id",
        "name",
        "type",
        "level_code",
        "platform",
        "is_multiple_screen",
        "status",
        "user_group_id",
        "cover",
        "share_secret_key",
        "layout",
        "scale_mode",
        "background",
        "biz_code",
        "type_access_released",
        "theme",
        "refresh_rate",
        "grid_padding",
        "line_height",
        "parent_id",
    ]
    data = {k: dashboard[k] for k in keys}
    data.update({"snapshot_id": dashboard['id'], "data_type": 0})
    return data


def get_dashboard(dashboard_id):
    return dashboard_repository.get_dashboard(dashboard_id)


def get_dashboard_advanced(
        dashboard_id: str, is_export: None = None
) -> Dict[str, Union[str, int, Dict[str, Union[str, int]], Dict[str, Union[bool, str]], None]]:
    """
    获取看板
    :param str dashboard_id:
    :param is_export: 是否是导出模板 True为是，False为否
    :return:
    """
    if not dashboard_id:
        raise UserError(message='缺少看板id')
    res = dashboard_repository.get_dashboard(dashboard_id)

    if res:
        res['layout'] = json.loads(res['layout']) if res['layout'] else {}
        res['background'] = json.loads(res['background']) if res['background'] else {}

        if not is_export:
            # 获取报告筛选条件
            res['dashboard_filters'] = get_dashboard_filters(dashboard_id)
            # 获取全局参数
            res['global_params'] = get_dashboard_global_params(dashboard_id)
            # 获取报告筛选变量
            res['dashboard_var_filters'] = get_dashboard_var_filters(dashboard_id)
            # 老的联动已经废弃(为保证数据升级 老的数据并未删除 此处即使有数据也不入库)
            # 返回关联关系
            # res["selectors"] = get_dashboard_chart_selectors(dashboard_id)
            res["selectors"] = dict()
            # 新增变量取值来源绑定关系
            res['var_value_sources'] = get_dashboard_value_sources_by_id(dashboard_id)
    return res


def check_permission(dashboard_id: str, data: Dict[str, Union[str, int, List[str]]], action: str) -> bool:
    debugger.log({"jump_path check_permission": {'dashboard_id': dashboard_id, 'data': data, 'action': action}})
    if data.get('dashboard_id'):
        user_auth = data.get('external_params', {}).get("user_auth")
        if user_auth and user_auth.find(action) != -1:
            return has_jump_path_v2(data.get('dashboard_id'), dashboard_id)
        return False
    elif data.get('screen_id'):
        return authority_service.verify_third_dashboard_download_auth(dashboard_id, data.get('external_params'))
    return True


def has_jump_path_v2(from_dashboard_id, to_dashboard_id):
    """
    LoginFrom.SuperPortal.value, LoginFrom.SuppApp.value
    集成登录的时候mip场景会将报告相关联有权限的数据存到redis
    现在的运行时报告页面打开其他报表会校验与集成源头报告的关系（或者说有没有当前这个报告的权限）
    由于调用历史的这个跳转关系的方法出现sql性能问题，这里将调用按集成场景进行区分
    1. 如果来自mip的redis集成场景，这里将先从redis中获取集成的时候设置的key
    2. 保留原来的3云集成的sql查询逻辑，不动
    """
    if from_dashboard_id == to_dashboard_id:
        return True

    from user.services.ingrate_service import get_cache_report_auth

    auth_value = get_cache_report_auth(to_dashboard_id)
    if auth_value is not None:
        if 'download' in str(auth_value):
            return True
        else:
            return False
    else:
        return has_jump_path(from_dashboard_id, to_dashboard_id)


def has_jump_path(from_dashboard_id, to_dashboard_id):
    """
    是否有from 到 to的跳转
    检查url中的报告是不是由集成登录的报告跳转过来的
    from_dashboard_id: cookies中的报告id
    to_dashboard_id: url中实际的id
    """
    to_check_ids = [to_dashboard_id]
    checked_ids = set()
    while to_check_ids:
        from_ids = dashboard_repository.get_jump_dashboard_ids_by_target_dashboard_ids(to_check_ids)
        if not from_ids:
            return False
        if from_dashboard_id in from_ids:
            return True
        checked_ids |= set(to_check_ids)
        to_check_ids = list(set(from_ids) - checked_ids)
    return False


def check_token(token: str) -> Tuple[bool, str, Dict[str, Union[str, int, List[str]]]]:
    """
    单独校验token
    :param token:
    :return:
    """
    if not token:
        raise UserError(code=403, message="未授权")
    verified = True
    errmsg = ""
    data = {}
    try:
        data = jwt.decode(token, config.get("JWT.secret"), algorithms="HS256")
    except DecodeError:
        verified, errmsg = False, "无效的签名"
    except ExpiredSignatureError:
        verified, errmsg = False, "签名过期"
    except binascii.Error:
        verified, errmsg = False, "非法签名"
    except Exception as e:
        verified, errmsg = False, str(e)

    if not verified or not data:
        return False, errmsg, {}
    return True, errmsg, data


class ZipFile(object):
    def __init__(self, filename, mode='r', basedir=''):
        self.filename = filename
        self.mode = mode
        if self.mode in ('w', 'a'):
            self.zip_file = zipfile.ZipFile(
                os.path.join(basedir, filename), self.mode, compression=zipfile.ZIP_DEFLATED
            )
        else:
            self.zip_file = zipfile.ZipFile(os.path.join(basedir, filename), self.mode)
        self.basedir = basedir
        if not self.basedir:
            self.basedir = os.path.dirname(filename)

    def addfile(self, path, arcname=None):
        path = path.replace('//', '/')
        if not arcname:
            if path.startswith(self.basedir):
                arcname = path[len(self.basedir):]
            else:
                arcname = ''
        self.zip_file.write(path, arcname)

    def addfiles(self, paths):
        for path in paths:
            if isinstance(path, tuple):
                self.addfile(*path)
            else:
                self.addfile(path)

    def close(self):
        self.zip_file.close()


def batch_get_dashboard_vars(dashboard_id):
    """
    批量获取当前报告单图引用的数据集变量列表
    :param dashboard_id:
    :return:
    """
    result_dict = defaultdict(dict)
    if not dashboard_id:
        return result_dict

    # 当前报告引用到的所有数据集ID
    chart_list = chart_repository.get_chart_list_by_dashboard_id(dashboard_id)
    chart_source_list = [i.get("source") for i in chart_list if i.get("source")] if chart_list else list()
    if not chart_source_list:
        return result_dict

    dataset_dict = {
        i.get("id"): i for i in [external_query_service.get_dataset(k) for k in list(set(chart_source_list))]
    }
    var_list = external_query_service.batch_get_dataset_vars(list(set(chart_source_list)))

    # 组装返回给前端的数据格式
    for single_var in var_list:
        var_dataset_id = single_var.get("dataset_id")
        if var_dataset_id and var_dataset_id not in result_dict.keys():
            result_dict[var_dataset_id] = {"dataset_data": {}, "var_list": []}
        dataset_data = dataset_dict.get(var_dataset_id)
        needed_data = {key: dataset_data.get(key) for key in ["id", "name"]} if dataset_data else dict()
        result_dict[var_dataset_id]["dataset_data"] = needed_data
        result_dict[var_dataset_id]["var_list"].append(single_var)
    return result_dict


def get_dashboard_info(dashboard_id: str, fields: list = None) -> Dict[str, Union[str, int, datetime, None]]:
    """
    获取报告详情
    :param dashboard_id:
    :param fields:
    :return:
    """
    return repository.get_data('dashboard', {'id': dashboard_id}, fields=fields)


def get_chart_jump(dashbaord_chart_id: str) -> List[Any]:
    """
    获取单图跳转配置
    [标记] 发布时保存跳转
    :param dashbaord_chart_id:
    :return:
    """
    jump_configs = repository.get_data(
        'dashboard_jump_config',
        {'dashboard_chart_id': dashbaord_chart_id, 'status': DashboardJumpConfigStatus.Valid.value},
        multi_row=True,
    )

    var_jump_relations = repository.get_data(
        'dashboard_vars_jump_relation', {'dashboard_chart_id': dashbaord_chart_id}, multi_row=True
    )

    jump_relations = repository.get_data(
        'dashboard_jump_relation', {'dashboard_chart_id': dashbaord_chart_id}, multi_row=True
    )

    fixed_var_jump_relations = repository.get_data(
        'dashboard_fixed_var_jump_relation', {'dashboard_chart_id': dashbaord_chart_id}, multi_row=True
    )

    filter_chart_relations = repository.get_data(
        'dashboard_filter_chart_jump_relation', {'dashboard_chart_id': dashbaord_chart_id}, multi_row=True
    )

    dashboard_filter_relations = repository.get_data(
        'dashboard_global_params_jump_relation', {'dashboard_chart_id': dashbaord_chart_id}, multi_row=True
    )

    jump_relations_with_configid = get_jump_relation(jump_relations)
    var_jump_relations_with_configid = get_jump_relation(var_jump_relations)
    fixed_var_jump_relations_with_configid = get_jump_relation(fixed_var_jump_relations)
    filter_chart_relations_with_configid = get_jump_relation(filter_chart_relations)
    global_params_relations_with_configid = get_jump_relation(dashboard_filter_relations)

    if jump_configs:
        for jump_config in jump_configs:
            jump_config_id = jump_config.get('id')
            jump_config['jump_relation'] = deal_relation_value(jump_relations_with_configid, jump_config_id)
            jump_config['var_jump_relation'] = deal_relation_value(var_jump_relations_with_configid, jump_config_id)
            jump_config['fixed_var_jump_relation'] = deal_relation_value(fixed_var_jump_relations_with_configid,
                                                                         jump_config_id)
            jump_config['filter_chart_jump_relation'] = deal_relation_value(filter_chart_relations_with_configid,
                                                                            jump_config_id)
            jump_config['global_params_jump_relation'] = deal_relation_value(global_params_relations_with_configid,
                                                                             jump_config_id)

    return jump_configs


def deal_relation_value(relations_with_configid, jump_config_id):
    return relations_with_configid.get(jump_config_id) or []


def get_jump_relation(relations: list):
    result = defaultdict(list)
    for relation in (relations or []):
        result[relation.get('jump_config_id')].append(relation)
    return result


def get_external_secret_key():
    """
    获取对外密钥
    :return:
    """
    result = repository.get_data(
        table_name='project', conditions={'code': g.code}, fields=['external_secret_key'], from_config_db=True
    )
    if result:
        return result.get('external_secret_key')
    return False


def get_external_token():
    """
    获取对外token
    :return:
    """
    external_secret_key = get_external_secret_key()
    if not external_secret_key:
        raise UserError(message='对外密钥为空,无法生成token,请先在admin平台配置密钥！')

    external_params = getattr(g, 'external_params', {}) or {}
    external_data = {
        "userid": g.userid if hasattr(g, 'userid') else '',
        "tenant_code": g.code,
        "username": g.account if hasattr(g, 'account') else '',
        'time': int(time.time()),
        'external_user_id': external_params.get('external_user_id', ''),
    }
    # 如果id不存在，就尝试取第三方集成的用户id
    if not external_data.get('userid'):
        external_data['userid'] = external_params.get('user_id') or ''

    token = jwt.encode(external_data, external_secret_key)
    return token


def generate_jump_config(source_dashboard_id, source_chart_id, source_field_id, target_dashboard_id):
    return {
        'id': seq_id(),
        'dashboard_id': source_dashboard_id,
        'dashboard_chart_id': source_chart_id,
        'source_id': source_field_id,
        'source_type': 3,
        'target': target_dashboard_id,
        'target_type': DashboardJumpType.SelfService.value,
        "open_way": 1,
        'has_token': 1,
        'status': 1,
        'with_params': 1,
    }


def add_self_service_child_dashboard(parent_dashboard_id, parent_dashboard_chart_id, dataset_field_id, name, dashboard):
    parent_dashboard = dashboard_repository.get_dashboard(parent_dashboard_id)
    if not parent_dashboard:
        raise UserError(message='报告不存在')
    if not repository.data_is_exists('dashboard_chart',
                                     {'dashboard_id': parent_dashboard_id, 'id': parent_dashboard_chart_id}):
        raise UserError(message='单图不存在')
    if not repository.data_is_exists('dataset_field', {'id': dataset_field_id}):
        raise UserError(message='字段不存在')

    child_dashboard_analysis_type = DashboardAnalysisType.SummaryDetail.value \
        if parent_dashboard.get('analysis_type') == DashboardAnalysisType.Summary.value \
        else DashboardAnalysisType.MultiDimDetail.value

    model = ChartDashboardModel(**dashboard)
    model.id = seq_id()
    model.name = name
    model.application_type = 1
    model.type = DashboardType.CHILD_FILE.value
    model.parent_id = parent_dashboard_id
    model.main_external_subject_id = parent_dashboard.get('main_external_subject_id')
    model.external_subject_ids = parent_dashboard.get('external_subject_ids')
    model.analysis_type = child_dashboard_analysis_type
    model.validate()

    # 自动生成跳转数据，添加子报告，添加跳转配置
    child_dashboard_data = model.get_dict(ChartDashboardModel.fields)
    child_dashboard_data['level_code'] = generate_level_code(model.parent_id)
    child_dashboard_data['biz_code'] = uuid.uuid4().__str__().replace('-', '')
    jump_config_data = generate_jump_config(parent_dashboard_id, parent_dashboard_chart_id, dataset_field_id, model.id)
    dashboard_repository.add_self_service_child_dashboard(child_dashboard_data, jump_config_data)

    return model.id


def rename_self_service_child_dashboard_name(id, name):
    # 自助报告子报告允许重名
    data = get_dashboard(id)
    if not data:
        raise UserError(message='报告不存在')
    if data.get('type') != DashboardType.CHILD_FILE.value or data.get('application_type') != 1:
        raise UserError(message='报告类型错误')
    dashboard_extra_service.update_dashboard_edit_on(id)
    repository.update_data('dashboard', {'name': name}, {'id': id})


def del_self_service_child_dashboard(child_dashboard_id):
    child_dashboard = dashboard_repository.get_dashboard(child_dashboard_id)
    if not child_dashboard:
        if repository.data_is_exists('dashboard_jump_config', {'target': child_dashboard_id}):
            # 如果报告不存在, 但关系存在, 有可能是请求顺序错乱导致的元数据不同步, 在这里做二次删除
            repository.delete_data(
                'dashboard_jump_config',
                {'target': child_dashboard_id, 'target_type': DashboardJumpType.SelfService.value},
            )
            return
        raise UserError(message=f'报告不存在')

    if (child_dashboard.get('application_type') != 1) or (
            child_dashboard.get('type') != DashboardType.CHILD_FILE.value
    ):
        raise UserError(message=f'报告不存在')

    parent_dashboard_id = child_dashboard.get('parent_id')
    parent_dashboard = dashboard_repository.get_dashboard(parent_dashboard_id)
    if not parent_dashboard:
        raise UserError(message="数据看板不存在")

    # 先标记为已删除, 并删除跳转关系
    repository.update('dashboard', {'is_deleted': 1}, {'id': child_dashboard_id})
    with get_db() as db:
        db.update('dashboard', {'is_deleted': 1}, {'id': child_dashboard_id}, commit=False)
        db.delete('dashboard_jump_config', {'dashboard_id': parent_dashboard_id, 'target': child_dashboard_id})

    # 如果报告未发布, 实际删除报告
    if child_dashboard.get("status") == DashboardStatus.Drafted.value:
        delete_child_dashboard(child_dashboard_id)


def add_dashboard(model: ChartDashboardModel, return_dict: bool = False) -> Any:
    """
    :添加看板文件夹
    :date 2017/7/4
    :param model:
    :param return_dict:
    :return :
    """
    model.id = seq_id()
    model.validate()

    # 默认命名
    if (model.name == '新建文件夹' and model.type == DashboardType.Folder.value) or \
            (model.name == '未命名报表' and model.application_type == 1 and model.type == DashboardType.File.value):
        model.name = get_default_name(model.name, model.parent_id)
    if model.name == '新建子页面' and model.type == DashboardType.CHILD_FILE.value:
        model.name = get_child_file_name(model.name, model.parent_id)

    # 校验文件或文件夹名称重复
    repeat_flag = validate_repeat_name(model.name, model.parent_id, model.type, application_type=model.application_type)
    if repeat_flag and ((model.name == '新建文件夹' and model.type == DashboardType.Folder.value) or
                        (
                                model.name == '未命名报表' and model.type == DashboardType.File.value and model.application_type == 1)):
        model.name = f'{model.name}(1)'
    if repeat_flag and model.name == '新建子页面' and model.type == DashboardType.CHILD_FILE.value:
        model.name = '新建子页面(1)'
    if repeat_flag and model.type == DashboardType.File.value:
        raise UserError(message='存在重名文件或文件夹')

    data = model.get_dict(ChartDashboardModel.fields)

    if model.parent_id is None:
        model.parent_id = ''
        data['parent_id'] = ''

    # 创建目录需要获取对应层级下的最大RANK值
    if model.type == DashboardType.Folder.value:
        max_rank = repository.get_one('dashboard', {'type': 'FOLDER', 'parent_id': model.parent_id}, ['MAX(`rank`) as `rank`']) or {}
        max_rank = max_rank.get('rank') or 0
        data['rank'] = int(max_rank) + 1

    # 判断第几级别，最多创建10级文件夹
    if model.parent_id:

        parent_dashboard = dashboard_repository.get_dashboard(model.parent_id)
        if (
                parent_dashboard
                and len(parent_dashboard['level_code'].split('-')) > 10
                and model.type == DashboardType.Folder.value
        ):
            raise UserError(message='最多创建10级文件夹')

    data['level_code'] = generate_level_code(model.parent_id)
    data['biz_code'] = uuid.uuid4().__str__().replace('-', '')

    repository.add_data('dashboard', data)
    chart_repository.update_dashboard_modified_time(model.id)
    dashboard_extra_service.update_dashboard_extra_data(model.id, root_dash_id=model.id, copy_dash_id=None)

    # 新增子报表同事更新主报告的修改时间
    if model.type == DashboardType.CHILD_FILE.value:
        dashboard_extra_service.update_root_dashboard_status(data.get('level_code'))

    if return_dict:
        return {'id': model.id, 'name': model.name, 'level_code': data['level_code']}
    return model.id


def get_default_name(default_name, parent_id):
    """
    默认名称
    :param default_name:
    :param parent_id:
    :return:
    """
    default_name_seq_id_list = list()
    default_name_list = dashboard_repository.get_default_dashboard_name(parent_id, default_name)
    for item in default_name_list:
        res = re.search(r'(\d+)', item['name'])
        if res:
            try:
                default_name_seq_id_list.append(int(res.groups()[0]))
            except Exception as e:
                logger.exception(e)
    lastest_seq_id = max(default_name_seq_id_list) + 1 if len(default_name_seq_id_list) else 0
    default_name = f'{default_name}({str(lastest_seq_id)})' if lastest_seq_id else default_name
    return default_name


def get_child_file_name(default_name, parent_id):
    """
    默认子报表名称
    :param default_name:
    :param parent_id:
    :return:
    """
    default_name_seq_id_list = list()
    parent_dashboard = repository.get_data('dashboard', {'id': parent_id}, ['name', 'level_code'])
    all_dashboard = dashboard_repository.get_dashboard_by_level_code(parent_dashboard.get('level_code'))
    original_name, _ = __get_original_name(default_name)
    for dashboard in all_dashboard:
        match_name, index = __get_original_name(dashboard['name'])
        if original_name.lower() == match_name.lower() and index > 0:
            default_name_seq_id_list.append(index)
    lastest_seq_id = max(default_name_seq_id_list) + 1 if len(default_name_seq_id_list) else 0
    if lastest_seq_id:
        default_name = '{}({})'.format(original_name, str(lastest_seq_id))
    return default_name


def __get_original_name(name):
    original_name = name
    index = 0
    res = re.search(r'(\d+)', name)
    if res:
        try:
            match_index = int(res.groups()[0])
            match_name = name[0: str(name).find(f'({str(match_index)})')]
            if f'{match_name}({match_index})'.lower() == name.lower():
                original_name = match_name
                index = match_index
        except Exception as e:
            logger.exception(e)
    return original_name, index


def validate_repeat_name(
        name: str, parent_id: str, file_type: str, ignore_flag: bool = False, dashboard_id: str = '', application_type=0
) -> bool:
    """
    校验名称重复
    :param name:
    :param parent_id:
    :param file_type:
    :param application_type:
    :param ignore_flag:
    :param dashboard_id:
    :return:
    """
    parent_id = '' if not parent_id else parent_id
    # # 自助报表单独处理
    if application_type == ApplicationType.SelfService.value:
        return validate_self_report_repeat_name(name, parent_id, file_type, ignore_flag, dashboard_id, application_type)

    if file_type == DashboardType.CHILD_FILE.value:  # 子报表在当前根报表下全局唯一
        parent_dashboard = repository.get_data('dashboard', {'id': parent_id})
        if not parent_dashboard:
            raise UserError(message='当前父级不存在请刷新页面再试')

        # 获取当前父级报表关联的父级和自己
        list_dashboard = dashboard_repository.get_dashboard_by_level_code(parent_dashboard.get('level_code'))
        for dashboard in list_dashboard:
            if dashboard.get('name') == name and dashboard.get('application_type') == application_type:
                if ignore_flag:
                    if dashboard.get('id') != dashboard_id:
                        return True
                else:
                    return True
        return False
    else:
        application_type = _get_assign_application_type(application_type)
        if ignore_flag:
            dashboard_data = dashboard_repository.get_dashboard_name(
                parent_id, file_type, name, dashboard_id, application_type
            )
            if dashboard_data:
                return True
            return False
        condition = {'name': name, 'parent_id': parent_id, 'type': file_type, 'application_type': application_type}
        dashboard_data = repository.get_data('dashboard', condition, ['name'], multi_row=False)
        if dashboard_data:
            return True
        return False


def validate_self_report_repeat_name(
        name: str, parent_id: str, file_type: str, ignore_flag: bool = False, dashboard_id: str = '', application_type=0
) -> bool:
    # 1. 自助报表目前没有子报表
    # 2. 自助报表重名校验需要允许不同用户在同一层级下创建同一名称文件夹

    # 重命名逻辑
    if ignore_flag:
        condition = {
            'name': name, 'parent_id': parent_id, 'type': file_type,
            'application_type': application_type, 'id !=': dashboard_id,
            'created_by': getattr(g, 'account', '')
        }
    # 新建逻辑
    else:
        condition = {
            'name': name, 'parent_id': parent_id,
            'type': file_type, 'application_type': application_type,
            'created_by': getattr(g, 'account', '')
        }
    dashboard_data = repository.get_data('dashboard', condition, ['name'], multi_row=False)
    return bool(dashboard_data)


def _get_assign_application_type(application_type):
    """
    重置报告的类型 application_type 值
    :param application_type:
    :return:
    """
    # 外部报告与仪表板名称不能重复
    if application_type in [ApplicationType.Dashboard.value, ApplicationType.External_Dashboard.value,
                            ApplicationType.HD_Dashboard.value]:
        application_type = [ApplicationType.Dashboard.value, ApplicationType.External_Dashboard.value,
                            ApplicationType.HD_Dashboard.value]
    elif application_type and application_type in [ApplicationType.SimpleReport.value,
                                                   ApplicationType.ActiveReport.value]:
        # 报表中心-自助报表+统计报表的报表名称不能重复
        application_type = [ApplicationType.SimpleReport.value, ApplicationType.ActiveReport.value]
    return application_type


def generate_level_code(parent_id: Optional[str] = None, build_in="") -> str:
    """
    根据父级组生成层级编码
    :param parent_id:
    :param build_in:
    :return:
    """
    return level_sequence_service.generate_level_code(
        DashboardLevelSequenceModel(level_id=parent_id, attach_identify=build_in)
    )


def get_dashboard_type_word(dashboard_type: str, application_type=0) -> str:
    """
    获取报告类型单词
    :param dashboard_type:
    :return:
    """
    if dashboard_type == DataReportType.File.value:
        if application_type == ApplicationType.LargeScreen.value:
            type_name = '大屏'
        else:
            type_name = '报告'
    elif dashboard_type == DataReportType.CHILD_FILE.value:
        type_name = '子报告'
    else:
        type_name = '文件夹'
    return type_name


def check_dashboard_permission(model: ChartDashboardModel, action_code: str) -> str:
    dashboard = repository.get_data('dashboard', {'id': model.id})
    if not dashboard:
        raise UserError(message='当前报告不存在，请重试!')
    model = ChartDashboardModel(**dashboard)
    if dashboard.get('type') == DashboardType.CHILD_FILE.value:
        model = ChartDashboardModel(**get_root_dashboard_by_dashboard_id(model.id))
    # 判断报告的分发锁定，如果被锁定则无法编辑
    if model.distribute_type == DistributeType.Distribute.value:
        raise UserError(501, '仪表板已被锁定，不允许编辑，建议复制后再调整')
    # 检测是多屏还是报告
    is_multiple_screen = repository.data_is_exists('dashboard', {'id': model.id, 'is_multiple_screen': '1'})
    if not is_multiple_screen:
        # TODO 这地方可以优化查询次数
        # is_self_service = repository.data_is_exists('dashboard', {'id': model.id, 'application_type': 1})
        if dashboard and dashboard.get("application_type") == 1:
            self_service_dict = {
                'view': check_can_view_self_service,
                'edit': check_can_edit_self_service,
                'copy': check_can_copy_self_service,
                'export': check_can_export_self_service,
            }
            return self_service_dict.get(action_code)(model)
        if dashboard and dashboard.get("application_type") in [5, 6]:
            self_service_dict = {
                'view': check_can_view_report_center,
                'edit': check_can_edit_report_center,
                'copy': check_can_copy_report_center
            }
            return self_service_dict.get(action_code)(model)
        if dashboard and dashboard.get("application_type") == ApplicationType.LargeScreen.value:
            large_screen_dict = {
                'view': check_can_view_large_screen,
                'edit': check_can_edit_large_screen,
                'copy': check_can_copy_large_screen
            }
            return large_screen_dict.get(action_code)(model)
        dashboard_dict = {
            'view': check_can_view_dashboard,
            'edit': check_can_edit_dashboard,
            'copy': check_can_copy_dashboard,
            'export': check_can_export_dashboard,
        }
        if model.application_type and model.application_type in [5, 6]:
            model.dataset_id = '';
        return dashboard_dict.get(action_code)(model)
    else:
        if action_code in ['export', 'copy']:
            raise UserError(message=u'多屏只有view, edit权限')
        multiple_screen_dict = {'view': check_can_view_multiple_screen, 'edit': check_can_edit_multiple_screen}
        return multiple_screen_dict.get(action_code)(model)


@data_permission_edit_filter('multiple_screen-edit')
def check_can_edit_multiple_screen(model: ChartDashboardModel):
    """
    检测是否可以编辑该多屏
    :param str model:
    :return:
    """
    return model.id


@data_permission_edit_filter('multiple_screen-view')
def check_can_view_multiple_screen(model: ChartDashboardModel):
    """
    检测是否可以查看该多屏
    :param str model:
    :return:
    """
    return model.id


@data_permission_edit_filter('self_service-view')
def check_can_view_self_service(model: ChartDashboardModel):
    """
    检查是否可以查看该自助报表
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('self_service-copy')
def check_can_copy_self_service(model: ChartDashboardModel):
    """
    检查是否可以复制该自助报表
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('report_center-edit')
def check_can_edit_report_center(model: ChartDashboardModel):
    """
    检查是否可以编辑该报表中心报表
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('report_center-view')
def check_can_view_report_center(model: ChartDashboardModel):
    """
    检查是否可以查看该报表中心报表
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('report_center-copy')
def check_can_copy_report_center(model: ChartDashboardModel):
    """
    检查是否可以复制该报表中心报表
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('large_screen-edit')
def check_can_edit_large_screen(model: ChartDashboardModel):
    """
    检查是否可以编辑酷炫大屏
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('large_screen-view')
def check_can_view_large_screen(model: ChartDashboardModel):
    """
    检查是否可以查看酷炫大屏
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('large_screen-copy')
def check_can_copy_large_screen(model: ChartDashboardModel):
    """
    检查是否可以复制酷炫大屏
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('self_service-edit')
def check_can_edit_self_service(model: ChartDashboardModel):
    """
    检查是否可以编辑该自助报表
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('self_service-export')
def check_can_export_self_service(model: ChartDashboardModel):
    """
    检查是否可以导出该自助报表
    :param model:
    :return:
    """
    return model.id


@data_permission_edit_filter('dashboard-edit')
def check_can_edit_dashboard(model: ChartDashboardModel) -> str:
    """
    检测是否可以编辑该报告
    :param str model:
    :return:
    """
    return model.id


@data_permission_edit_filter('dashboard-view')
def check_can_view_dashboard(model: ChartDashboardModel):
    """
    检测是否可以查看该报告
    :param str model:
    :return:
    """
    return model.id


@data_permission_edit_filter('dashboard-copy')
def check_can_copy_dashboard(model: ChartDashboardModel):
    """
    检测是否可以复制该报告
    :param str model:
    :return:
    """
    return model.id


@data_permission_edit_filter('dashboard-export')
def check_can_export_dashboard(model: ChartDashboardModel):
    """
    检测是否可以下载该报告
    :param str model:
    :return:
    """
    return model.id


def get_dashbaord_info(dashbaord_id):
    """
    获取报告详情
    :param dashbaord_id:
    :return:
    """
    return repository.get_data('dashboard', {'id': dashbaord_id})


@data_permission_edit_filter('dashboard-edit')
def update_dashboard_name(model):
    """
    重命名看板
    :param dashboard.models.DashboardModel model:
    :return:
    """
    model.validate()
    if not get_dashboard(model.id):
        raise UserError(message='数据看板不存在')
    fields = ['name']

    # 校验文件或文件夹名称重复
    repeat_flag = validate_repeat_name(
        model.name,
        model.parent_id,
        model.type,
        ignore_flag=True,
        dashboard_id=model.id,
        application_type=model.application_type,
    )
    if repeat_flag:
        raise UserError(message='存在重名的文件或文件夹')

    # 登记当前报告的编辑时间
    dashboard_extra_service.update_dashboard_edit_on(model.id)

    # 如果为子报告则需要更新主报告的状态
    if model.parent_id:
        level_code = model.level_code or ''
        if level_code:
            dashboard_extra_service.update_root_dashboard_status(level_code)

    return repository.update_model('dashboard', model, {'id': model.id}, fields)


def _validate_source_dashboard(source_dashboard, target_dashboard):
    """
    原报告类型判断
    :param source_dashboard:
    :param target_dashboard:
    :return:
    """
    source_dashboard_type = source_dashboard.get("type")
    if source_dashboard_type == DashboardType.CHILD_FILE.value:
        if target_dashboard:
            # 子报告无法移动到文件夹中
            if target_dashboard.get("type") == DashboardType.Folder.value:
                raise UserError(message='子报告只能移到到报告中')
        else:
            # 子报告不能移动到根目录
            raise UserError(message='子报告移动目标报告不存在')


def _validate_target_dashboard(source_dashboard, source_dash_id, target_dashboard, target_dash_id, is_open_api=False):
    if not target_dashboard:
        raise UserError(message='目标报告或文件夹不存在')

    # 不用必须是文件夹，报告可以移动到文件夹，可以移动到其他报告，可以移动到其他报告子报告
    if source_dashboard.get("type") == DashboardType.Folder.value \
            and target_dashboard['type'] != DashboardType.Folder.value:
        raise UserError(message='目标必须是文件夹')

    if target_dashboard['level_code'].find(source_dashboard['level_code']) == 0:
        raise UserError(message='不能移动到自身报告或自身文件夹下面')

    # 校验文件重名
    repeat = validate_repeat_name(
        source_dashboard['name'],
        target_dash_id,
        source_dashboard['type'],
        True,
        source_dash_id,
        application_type=source_dashboard.get('application_type'),
    )
    if repeat and is_open_api is False:
        raise UserError(message='目标报告或文件夹存在重名文件')

    if (
            source_dashboard['type'] == DashboardType.Folder.value
            and source_dashboard['level_code'].find(target_dashboard['level_code']) == -1
    ):
        source_sub_list = dashboard_repository.get_move_list_by_level_code(
            source_dashboard['level_code'], DashboardType.Folder.value
        )
        sub_depth = _dashboard_depth_count(source_sub_list)
        source_depth = sub_depth - source_dashboard['level_code'].count('-') + 1
        target_depth = target_dashboard['level_code'].count('-')
        if source_depth + target_depth > 10:
            raise UserError(message='文件夹最多只能有10层')


@data_permission_edit_filter('dashboard-edit')
def move_dashboard(dash_id, target_dash_id, is_open_api: bool = False, use_pymysql=True):
    """
    :移动数据文件夹到目标文件夹下面
    :date 2017/7/4
    :param str dash_id:
    :param str target_dash_id:
    :param str is_open_api:
    :return :
    """
    if dash_id == target_dash_id:
        UserError(message='移动的数据集不能和目标文件夹一样')

    # 判断目标数据集是否可以移动
    target_dashboard = repository.get_data(
        'dashboard',
        {"id": target_dash_id, "build_in": 0},
        ['id', 'level_code', 'type', 'application_type', 'platform', 'new_layout_type'],
    )
    source_dashboard = repository.get_data(
        'dashboard',
        {"id": dash_id, "build_in": 0},
        ['id', 'level_code', 'type', 'name', 'application_type', 'platform', 'new_layout_type'],
    )

    # 校验原报告移动信息
    _validate_source_dashboard(source_dashboard, target_dashboard)
    # 更新数据集以及子集的level_code
    if target_dash_id == 'root':
        target_dash_id = ''
    else:
        _validate_target_dashboard(source_dashboard, dash_id, target_dashboard, target_dash_id, is_open_api=is_open_api)

    # 获取target_id下的最大排序
    max_sort = repository.get_one('dashboard', {'parent_id': target_dash_id}, fields=['sort'],
                                  order_by=[('sort', 'desc')]) or {}
    sort = int(max_sort.get('sort', 0) or 0) + 1
    update_sql = "update dashboard set parent_id = '{}', sort = {} where id = '{}';".format(target_dash_id, sort,
                                                                                            dash_id)
    # 报告移动到其他报告中变为子报告，需变更报告类型
    _update_dashboard_to_child(source_dashboard, target_dashboard)

    new_level_code = generate_level_code(target_dash_id)
    return move_dashboard_tree(source_dashboard['level_code'], new_level_code, update_sql, use_pymysql=use_pymysql)


def move_dashboard_tree(source_level_code, new_level_code, update_sql, use_pymysql=True):
    # 获取对应层级下的所有子级的level_code
    old_data = dashboard_repository.get_move_list_by_level_code(source_level_code)
    if not old_data:
        return
    update_level_sql = [update_sql]
    for data in old_data:
        level_code = re.subn(r'^{}'.format(source_level_code), new_level_code, data.get('level_code'), flags=re.I)[0]
        sql = "update dashboard set level_code = '{}' where id = '{}';".format(level_code, data.get('id'))
        update_level_sql.append(sql)
    if update_level_sql:
        exec_sql = ''.join(update_level_sql)
        if use_pymysql is True:
            repository.execute_multi_sql(sql=exec_sql)
        else:
            for u_sql in update_level_sql:
                repository.exec_sql(u_sql)


def update_dashboard_folder_rank(model):
    """
    更新应用顺序
    :param dashboard_chart.models.DashboardSortModel model:
    :return:
    """
    model.validate()
    source_dashboard = repository.get_one('dashboard', {'id': model.source_id}, ['id', 'parent_id', 'type', 'rank'])
    if not source_dashboard:
        raise UserError(message='移动的对象不存在，请刷新后重试')
    with get_db() as db:
        try:
            db.begin_transaction()
            if source_dashboard.get('parent_id') != model.parent_id:
                parent_id = model.parent_id if model.parent_id else 'root'
                move_dashboard(model.source_id, parent_id)
            if model.target_id:
                # 获取目标当前的排序
                target_info = repository.get_one('dashboard', {'id': model.target_id}, ['id', 'parent_id', 'type', 'rank'])
                target_rank = target_info.get('rank') or 0
                target_rank = target_rank + 1
            else:
                target_rank = 0
            sql = "update dashboard set `rank` = `rank` + 1 where parent_id = '{}' and `rank` >= {} and `type`='FOLDER'".format(model.parent_id, target_rank)
            db.exec_sql(sql)
            repository.update_data('dashboard', {'rank': target_rank}, {'id': model.source_id})
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=str(e))


def _update_dashboard_to_child(source_dashboard, target_dashboard):
    """
    将报告类型修改为子报告
    报告可以移动到其他报告或子报告中，需要将原报告类型修改为子报告 todo 仪表板报告变更为子报告的影响
    :param source_dashboard:
    :param target_dashboard:
    :return:
    """
    if target_dashboard:
        source_dashboard_type = source_dashboard.get("type")
        target_dashboard_type = target_dashboard.get("type")
        if source_dashboard_type == DashboardType.File.value \
                and target_dashboard_type in [DashboardType.File.value, DashboardType.CHILD_FILE.value]:
            dashboard_repository.update_dashboard_to_child(source_dashboard.get("level_code"))


def _dashboard_depth_count(list_data):
    """
    :取数据看板所有同级别和子分类文件夹，判断最大层数
    :date 2017/7/7
    :param list list_data 单维度数据集:
    :return list:
    """
    if list_data is None:
        return 0

    max_dash_id = 1
    for v in list_data:
        max_new = v['level_code'].count('-')
        if max_new > max_dash_id:
            max_dash_id = max_new

    return max_dash_id


@data_permission_edit_filter('dashboard-edit')
def set_default_show(dashboard_id):
    """
    设置看板默认首页显示
    :param str dashboard_id:
    :return:
    """
    dashboard = get_dashboard(dashboard_id)
    if not dashboard:
        raise UserError(message='数据看板不存在')
    record = repository.update_data(
        'dashboard', {'default_show': 0}, {'user_group_id': dashboard.get('user_group_id'), 'default_show': 1}
    )
    record += repository.update_data('dashboard', {'default_show': 1}, {'id': dashboard_id})
    return record


def get_default_show():
    """
    获取默认显示看板
    :return:
    """
    condition = {'default_show': 1}
    fields = ['id', 'name', 'user_group_id', 'default_show']
    result = repository.get_data('dashboard', condition, fields)

    res = dashboard_repository.get_dashboard(result['id'])
    if res:
        if res['layout']:
            result['layout'] = json.loads(res['layout'])
        if res['background']:
            result['background'] = json.loads(res['background'])
    return result


def delete_screens(dashboard_id, screen_list):
    for screen in screen_list:
        repository.delete_data(
            'dashboard_released_snapshot_chart',
            {'dashboard_id': dashboard_id, 'snapshot_id': screen.get('dashboard_id')},
        )
        repository.delete_data(
            'dashboard_released_snapshot_dashboard', {'id': dashboard_id, 'snapshot_id': screen.get('dashboard_id')}
        )
        repository.delete_data(
            'screen_dashboard', {'dashboard_id': screen.get('dashboard_id'), 'screen_id': dashboard_id}
        )


@data_permission_edit_filter('dashboard-edit')
def delete_dashboard(dashboard_id):
    """
    删除看板
    :param str dashboard_id:
    :return:
    """
    dashboard_data = get_dashboard(dashboard_id)
    if not dashboard_data:
        raise UserError(message='数据看板不存在')

    if dashboard_data["status"] == DashboardStatus.Released.value:
        raise UserError(message="报告已发布，请先取消报告发布再执行删除")

    if dashboard_data.get("type") == DashboardType.Folder.value:
        list_data = dashboard_repository.get_dash_list_by_group_id(**{"parent_id": dashboard_id})
        if list_data:
            raise UserError(message="此文件夹下还有未删除的报告，请先删除报告后再删除此文件夹")
    else:
        # 如果被多屏引用，则删除多屏引用关系
        screen_list = repository.get_data(
            'screen_dashboard', {'screen_id': dashboard_id}, ['id', 'dashboard_id'], multi_row=True
        )
        if screen_list:
            delete_screens(dashboard_id, screen_list)
        # 如果被门户引用，则删除门户引用记录
        function_info = repository.get_data('function', {'url': dashboard_id}, ['id'], multi_row=False)
        if function_info:
            repository.delete_data('function', {'url': dashboard_id})

    # 如果是多屏，删除多屏的关联关系
    if dashboard_data['is_multiple_screen'] == 1:
        repository.delete_data("screen_dashboard", {"dashboard_id": dashboard_id})

    # 删除单图关联关系
    repository.delete_data("dashboard_chart_selector", {"dashboard_id": dashboard_id})
    repository.delete_data("dashboard_chart_selector_field", {"dashboard_id": dashboard_id})

    # 删除报告的 参数配置 及 参数筛选
    repository.delete_data("dashboard_chart_params", {'dashboard_id': dashboard_id})
    repository.delete_data("dashboard_chart_params_jump", {'dashboard_id': dashboard_id})

    # 删除报告级筛选配置
    repository.delete_data('dashboard_filter', {'dashboard_id': dashboard_id})
    repository.delete_data('dashboard_filter_relation', {'dashboard_id': dashboard_id})
    # 删除dashboard_metadata_history
    repository.delete_data("dashboard_metadata_history", {"dashboard_id": dashboard_id})
    # 删除报告跳转
    repository.delete_data("dashboard_jump_config", {'dashboard_id': dashboard_id})
    repository.delete_data("dashboard_chart_params_jump", {'dashboard_id': dashboard_id})
    repository.delete_data("dashboard_jump_relation", {'dashboard_id': dashboard_id})
    repository.delete_data("dashboard_vars_jump_relation", {'dashboard_id': dashboard_id})

    # 删除单图
    chart_list = repository.get_data(
        'dashboard_chart', {'dashboard_id': dashboard_id, 'parent_id': ''}, ['id', 'chart_code'], True
    )
    if chart_list:
        for chart in chart_list:
            chart_service.delete_chart(chart["id"])

    # 删除新筛选和联动关系
    repository.delete_data("dashboard_linkage", {"dashboard_id": dashboard_id})
    repository.delete_data("dashboard_linkage_relation", {"dashboard_id": dashboard_id})
    repository.delete_data("dashboard_filter_chart", {"dashboard_id": dashboard_id})
    repository.delete_data("dashboard_filter_chart_relation", {"dashboard_id": dashboard_id})
    repository.delete_data("dashboard_filter_chart_default_values", {"dashboard_id": dashboard_id})

    # 删除变量绑定
    repository.delete_data("dashboard_dataset_vars_relation", {"dashboard_id": dashboard_id})

    repository.delete_data("dashboard", {"id": dashboard_id})

    # 删除dashboard_extra表数据
    repository.delete_data("dashboard_extra", {"dashboard_id": dashboard_id})

    # 删除报告巡检相关表记录
    repository.delete_data("healthy_dashboard", {"dashboard_id": dashboard_id})
    repository.delete_data("healthy_dashboard_instance", {"dashboard_id": dashboard_id})
    repository.delete_data("healthy_dashboard_result_node", {"dashboard_id": dashboard_id})

    return dashboard_data


@data_permission_edit_filter('dashboard-edit')
def delete_dashboard_by_dashboard_id(dashboard_id: str, application_type=0) -> Dict[str, Any]:
    """
    删除报告看板
    :param str dashboard_id:
    :param str application_type:
    :return:
    """
    return execute_delete_dashboard(dashboard_id, application_type)


def execute_delete_dashboard(dashboard_id: str, application_type=0) -> Dict[str, Any]:
    """
    删除报告看板
    :param str dashboard_id:
    :param str application_type: 报告类型，默认为0仪表板
    :return:
    """
    dashboard_data = get_dashboard(dashboard_id)
    if not dashboard_data:
        raise UserError(message="数据看板不存在")

    # 如果删除子报告，测需要更新主报告的修改时间
    if dashboard_data.get('type') == DashboardType.CHILD_FILE.value:
        dashboard_extra_service.update_root_dashboard_status(dashboard_data.get('level_code'))

    # 如果是删除子报告, 并且子报告已经发布, 将报告和报告下的所有子报告标记为已删除
    if (
            dashboard_data.get('type') == DashboardType.CHILD_FILE.value
            and dashboard_data.get("status") == DashboardStatus.Released.value
    ):
        del_dashboard_ids = get_sub_child_dashboard_ids(dashboard_data)
        repository.update('dashboard', {'is_deleted': 1}, {'id': del_dashboard_ids})
        return dashboard_data

    if dashboard_data.get("status") == DashboardStatus.Released.value:
        raise UserError(message="报告已发布，请先取消报告发布再执行删除")

    # 删除文件夹的情况
    if dashboard_data.get("type") == DashboardType.Folder.value:
        list_data = dashboard_repository.get_dash_list_by_group_id(**{"parent_id": dashboard_id,
                                                                      "application_type": application_type})
        if list_data:
            raise UserError(message="此文件夹下还有未删除的报告，请先删除报告后再删除此文件夹")
        # 文件夹只需删除dashboard表
        repository.delete_data("dashboard", {"id": dashboard_id})
        return dashboard_data

    # 删除报告的情况
    # 如果被多屏引用，则先删除多屏引用关系
    screen_list = repository.get_data(
        "screen_dashboard", {"screen_id": dashboard_id}, ["id", "dashboard_id"], multi_row=True
    )
    if screen_list:
        delete_screens(dashboard_id, screen_list)

    # 如果被门户引用，则删除门户引用记录
    function_info = repository.get_data("function", {"url": dashboard_id}, ["id"], multi_row=False)
    if function_info:
        repository.delete_data("function", {"url": dashboard_id})

    del_dashboard_ids = get_sub_child_dashboard_ids(dashboard_data, with_deleted=True)
    # 删除表之前先获取报告下的所有单图id，包括穿透子单图
    del_chart_list = repository.get_list("dashboard_chart", {"dashboard_id": del_dashboard_ids}, ["id", "source"])
    for del_dashboard_id in del_dashboard_ids:
        for table_name in TABLES_FOR_DELETE_DASHBOARD:
            del_conditions = {"dashboard_id": del_dashboard_id}
            if table_name in ["dashboard"]:
                del_conditions = {"id": del_dashboard_id}
            repository.delete_data(table_name, del_conditions)

    # 需要按单图id处理的表
    delete_charts_related_table(del_chart_list)

    # 删除报告的拍照记录
    delete_snapshot_record(del_dashboard_ids)

    # 删除报告最近一次发布的设计时数据
    del_dashboard_released_design_record(dashboard_id)
    return dashboard_data


def delete_snapshot_record(dashboard_ids):
    """
    删除所有关联的快照记录
    :param dashboard_ids:
    :return:
    """
    from dashboard_chart.services.released_dashboard_service import get_all_dashboard_and_dataset_of_need_snapshot
    from components.snapshot_service import DropSnapShotTables

    if isinstance(dashboard_ids, str):
        dashboard_ids = list(dashboard_ids)
    # 获取所有关联的 dashboard_ids和dataset_ids
    dashboard_ids, dataset_ids = get_all_dashboard_and_dataset_of_need_snapshot(
        dashboard_ids, [], dashboard_ids
    )
    # 获取dashboard_ids下所有的snap_id
    snapshot = repository.get_data("snapshot_dashboard_released_snapshot_dashboard", {"id": dashboard_ids}, ["snap_id"])
    snap_ids = [item.get("snap_id") for item in snapshot or []]
    # 执行删除
    snap_ids and DropSnapShotTables(dashboard_ids, dataset_ids, snap_ids).do()


def delete_child_dashboard(dashboard_id):
    del_chart_list = repository.get_list("dashboard_chart", {"dashboard_id": dashboard_id}, ["id", "source"])
    for table_name in TABLES_FOR_DELETE_DASHBOARD:
        del_conditions = {"dashboard_id": dashboard_id}
        if table_name in ["dashboard"]:
            del_conditions = {"id": dashboard_id}
        repository.delete_data(table_name, del_conditions)

    # 需要按单图id处理的表
    delete_charts_related_table(del_chart_list)


def delete_charts_related_table(del_chart_list: List[Dict[str, Any]]) -> None:
    """
    删除单图关联的其他表
    :param del_chart_list:
    :return:
    """
    for single_chart in del_chart_list:
        if not single_chart:
            continue

        # 旧组件筛选
        chart_repository.delete_component_filter_chart(single_chart.get("id"))
        chart_repository.delete_component_filter_field(single_chart.get("id"))

        # 旧联动
        selector_list = chart_repository.get_chart_selector_list_by_chart_id(single_chart.get("id"))
        chart_repository.delete_selector_chart(single_chart.get("id"))
        if selector_list:
            chart_repository.delete_chart_selector_field_new([item.get('id') for item in selector_list if item])

        # 单图关联数据集flow流程
        delete_related_flow(single_chart)

        # 结果表
        delete_related_result_table(single_chart)


def delete_related_flow(single_chart: Dict[str, Any]) -> bool:
    """
    删除关联的flow
    :param single_chart:
    :return:
    """
    chart_source = single_chart.get("source")
    if not chart_source:
        return False
    dataset = external_query_service.get_dataset(chart_source)
    if dataset:
        content = json.loads(dataset.get('content'))
        if (
                dataset.get('type') == DatasetType.Label.value
                and content
                and not label_service.get_label_sync_detail(content.get('label_id'))
        ):
            flow_service.delete_flow(chart_source)
    return True


def delete_related_result_table(single_chart: Dict[str, Any]) -> bool:
    """
    删除结 果表
    :param single_chart:
    :return:
    """
    chart_id = single_chart.get("id")
    if not chart_id:
        return False
    result_table_name = "chart_result_" + hashlib.md5(chart_id.encode('utf-8')).hexdigest()[8:-8]
    if dataset_service.dataset_table_exists(result_table_name):
        repository.delete_data_db_table(result_table_name)
    result_table_name += '_detail'
    if dataset_service.dataset_table_exists(result_table_name):
        repository.delete_data_db_table(result_table_name)
    return True


def check_delete_dashboard(dashboard_id):
    """
    删除看板时检查报告数据是否被多屏引用， 被门户引用等功能
    :param str dashboard_id:
    :return:
    """
    if not dashboard_id:
        raise UserError(message='报告id为空')

    data = {'is_quoted_screen': False, 'is_released': False}
    # 报告已经发布的情况
    query_record = repository.get_data("dashboard", {"id": dashboard_id}, ["status", "type"])
    if (
            query_record
            and query_record.get("status") == DashboardStatus.Released.value
            and query_record.get('type') != DashboardType.CHILD_FILE.value
    ):
        data['is_released'] = True

    # 1. 被多屏引用情况
    screen_info = dashboard_repository.get_related_dashboard_by_screen_id(dashboard_id)
    if screen_info and len(screen_info) >= 1:
        data['is_quoted_screen'] = True
    if not data["is_quoted_screen"]:
        # 2. 被门户引用情况
        function_info = repository.get_data('function', {'url': dashboard_id}, ['id'], multi_row=False)
        if function_info:
            data['is_quoted_screen'] = True

    # 3. 当前报告有被其他报告跳转的情况
    data["is_target_dashboard"], data["jump_dashboard_list"] = check_target_dashboard_info(dashboard_id)

    return data


def check_target_dashboard_info(dashboard_id):
    """
    检查是否有被其他报告跳转的情况
    :param dashboard_id:
    :return:
    """
    is_target_dashboard, jump_dashboard_list = False, list()
    if not dashboard_id:
        return is_target_dashboard, jump_dashboard_list
    jump_dashboard_list = dashboard_repository.get_jump_dashboard_info(dashboard_id)
    is_target_dashboard = True if jump_dashboard_list and len(jump_dashboard_list) else is_target_dashboard
    return is_target_dashboard, jump_dashboard_list


def get_dashboard_filter_info(dashboard_filter_ids):
    """
    获取报告筛选条件信息
    :param str dashboard_filter_ids: filter_id列表字符串
    :return:
    """
    if dashboard_filter_ids is None:
        raise UserError(message='dashboard_filter_id为空')
    dashboard_filter_id_list = {*dashboard_filter_ids.split(',')}
    data = []
    for dashboard_filter_id in dashboard_filter_id_list:
        data.append(dashboard_repository.get_dashboard_filter_info(dashboard_filter_id))
    return data


def get_dashboard_var_filters(dashboard_id: str) -> List[Any]:
    """
    获取报告筛选
    :param dashboard_id:
    :return:
    """
    var_filter_relations = dashboard_repository.get_dashboard_var_filter_relations(dashboard_id)
    res = []
    # 获取参数筛选逻辑
    for var_filter_relation in var_filter_relations:
        var_filter = dict(
            id=var_filter_relation.get("id"),
            dashboard_id=var_filter_relation.get("dashboard_id"),
            data_type=var_filter_relation.get("data_type"),
            main_dataset_field_id="",
            operator=None,
            filter_relations=[],
        )
        var_filter['operators'] = get_operators_by_filter_id(var_filter_relation.get("id"))
        var_name = var_filter_relation.get("var_name")
        related_dataset_field_id = var_filter_relation.get("related_dataset_field_id", "")
        if related_dataset_field_id:
            field = dashboard_repository.get_dataset_field_by_id(related_dataset_field_id)
            filter_relation = dict(
                id=var_filter_relation.get("id"),
                related_dataset_id=var_filter_relation.get("related_dataset_id", ""),
                related_dataset_field_id=var_filter_relation.get("related_dataset_field_id", ""),
                alias_name=field.get("alias_name"),
                col_name=field.get("col_name"),
            )
        else:
            filter_relation = {}
        if not res:
            var_filter['var_name'] = var_name
            var_filter['filter_relations'] = [filter_relation]
            res.append(var_filter)
        else:
            res_copy = deepcopy(res)
            for item in res_copy:
                if var_name not in item.keys():
                    var_filter['var_name'] = var_name
                    var_filter['filter_relations'] = [filter_relation]
                    res.append(var_filter)
                else:
                    var_filter['filter_relations'].append(filter_relation)
    return res


def get_dashboard_filters(dashboard_id: str, has_param: int = 0) -> List[Any]:
    """
    获取报告筛选
    :param dashboard_id:
    :param has_param:
    :return:
    """
    filters = dashboard_repository.get_dashboard_filters(dashboard_id)
    filter_relations = dashboard_repository.get_dashboard_filter_relations(dashboard_id)
    for single_filter in filters:
        # 从新的子表中获取筛选条件
        single_filter['operators'] = get_operators_by_filter_id(single_filter['id'])
        if 'filter_relations' not in single_filter:
            single_filter['filter_relations'] = []
        for filter_relation in filter_relations:
            if filter_relation.get('main_dataset_field_id') == single_filter.get('main_dataset_field_id'):
                related_dataset_field_id = filter_relation.get('related_dataset_field_id')
                # 调用数据集服务获取数据集数据
                related_dataset_data = external_query_service.get_multi_dataset_fields([related_dataset_field_id])
                if related_dataset_data:
                    filter_relation['related_dataset_id'] = related_dataset_data[0].get('dataset_id')
                single_filter['filter_relations'].append(filter_relation)
    # 获取参数筛选
    if has_param:
        var_filters = dashboard_repository.get_dashboard_var_filter_relations(dashboard_id)
        for _filter in var_filters:
            var_filter = dict(
                alias_name=_filter.get("var_name"),
                col_name=_filter.get("col_name"),
                dashboard_id=_filter.get("dashboard_id"),
                data_type="字符串",  # TODO
                field_group="维度",  # TODO
                id=_filter.get("id"),
                type="参数",
            )
            filters.append(var_filter)
    return filters


def get_dashboard_global_params(dashboard_id):
    # 获取报告的全局参数信息
    global_params = get_dashboard_global_params_by_id(dashboard_id)
    data = []
    for global_param in global_params:
        global_params_id = global_param.get('id')
        global_param['dataset_field_relations'] = get_dataset_field_relations_by_global_params_id(global_params_id)
        global_param['dataset_vars_relations'] = get_dataset_vars_relations_by_global_params_id(global_params_id)
        global_param['filter_chart_relations'] = get_filter_chart_relations_by_global_params_id(global_params_id)
        data.append(global_param)
    return data


def get_dashboard_global_params_by_id(dashboard_id: str) -> List[Any]:
    """
    根据报告ID获取报告全局参数
    :param dashboard_id:
    :return:
    """
    fields = ['id', 'dashboard_id', 'name', 'alias_name']
    global_params = repository.get_data(
        'dashboard_jump_global_params',
        {'dashboard_id': dashboard_id}, fields, multi_row=True, order_by=[('created_on', 'asc'), ('name', 'asc')]
    )
    return global_params


def get_dataset_field_relations_by_global_params_id(global_params_id: str) -> List[Any]:
    """
    根据全局参数ID获取数据集字段关联关系
    :param global_params_id:
    :return:
    """
    fields = ['global_params_id', 'dashboard_id', 'dataset_id', 'dataset_field_id', 'chart_id']
    dataset_field_relation = repository.get_data('dashboard_global_params_2_dataset_field_relation',
                                                 {'global_params_id': global_params_id}, fields, multi_row=True)
    return dataset_field_relation


def get_dataset_vars_relations_by_global_params_id(global_params_id: str) -> List[Any]:
    """
    根据全局参数ID获取数据集变量关联关系
    :param global_params_id:
    :return:
    """
    fields = ['global_params_id', 'dashboard_id', 'dataset_id', 'var_id']
    vars_relation = repository.get_data('dashboard_global_params_2_dataset_vars_relation',
                                        {'global_params_id': global_params_id}, fields, multi_row=True)
    return vars_relation


def get_filter_chart_relations_by_global_params_id(global_params_id: str) -> List[Any]:
    """
    根据全局参数ID获取数据集筛选器关联关系
    :param global_params_id:
    :return:
    """
    fields = ['global_params_id', 'dashboard_id', 'filter_chart_id', 'dataset_field_id', 'date_filter_chart_flag']
    filter_chart_relation = repository.get_data('dashboard_global_params_2_filter_chart_relation',
                                                {'global_params_id': global_params_id}, fields, multi_row=True)
    return filter_chart_relation


@data_permission_edit_filter('dashboard-edit')
def update_dashboard(model):
    """
    更新看板
    :param dashboard.models.DashboardModel model:
    :return:
    """
    model.validate()
    if not get_dashboard(model.id):
        raise UserError(message='数据看板不存在')
    fields = ['name', 'description', 'layout', 'refresh_rate', 'auto_play']
    return repository.update_model('dashboard', model, {'id': model.id}, fields)


def get_dashboards(dashboard_ids):
    """
    获取看板
    :param list dashboard_ids: 报告id列表
    :return:
    """
    if not dashboard_ids:
        raise UserError(message='缺少看板id')
    data = dashboard_repository.get_dashboards(dashboard_ids)
    result = {}
    for res in data:
        if res:
            res['layout'] = json.loads(res['layout']) if res['layout'] else {}
            res['background'] = json.loads(res['background']) if res['background'] else {}
            # 返回关联关系
            res["selectors"] = get_dashboard_chart_selectors(res['id'])

        result[res['id']] = res
    return result


def get_dashboard_chart_selectors(dashboard_id):
    """
    通过报告id获取报告联动关联数据
    :param str dashboard_id: 报告id
    :return:
    """
    selector_data = {}
    selector_list = dashboard_repository.get_dashboard_selectors(dashboard_id)
    for selector in selector_list:
        item = {
            'chart_id': selector.get('chart_responder_id'),
            'dataset_id': selector.get('dataset_id'),
            'is_same_dataset': selector.get('is_same_dataset'),
            'fields': [],
        }
        if selector.get('is_same_dataset') == 0:  # 不同数据集关联
            field_list = dashboard_repository.get_dashboard_selector_fields_by_selector_id(selector.get('id'))
            if len(field_list) > 0:
                field_data = []
                for field in field_list:
                    field_item = {
                        'initiator_id': field.get('field_initiator_id'),
                        'responder_id': field.get('field_responder_id'),
                    }
                    field_data.append(field_item)
                item['fields'] = field_data

        if selector.get('chart_initiator_id') not in selector_data:
            selector_data[selector.get('chart_initiator_id')] = []
        selector_data[selector.get('chart_initiator_id')].append(item)
    return selector_data


def init_dev_user_dashboards(**kwargs):
    """
    初始化开发者报告
    :param kwargs:
    :return:
    """
    _init_pc_dashboard_to_dev_user(**kwargs)
    _init_mobile_dashboard_to_dev_user(**kwargs)


def _init_pc_dashboard_to_dev_user(**kwargs):
    pc_dashboard_args = {
        "name": "开发者报告" + str(int(time.time())) + str(random.randint(1, 1000)).rjust(4, '0'),
        "platform": "pc",
        "theme": "tech_blue",
        "type": "FILE",
        "parent_id": "",
        "layout": "{\"ratio\":\"16:9\",\"width\":1920,\"height\":1080,\"lattice\":10}",
        "background": "{\"show\":true,\"color\":\"RGBA(15,24,47,1)\",\"image\":\"\",\"size\":\"stretch\"}",
        "create_type": 1,
        "new_layout_type": 0,
    }
    pc_model = ChartDashboardModel(**pc_dashboard_args)
    return add_dashboard(pc_model)


def _init_mobile_dashboard_to_dev_user(**kwargs):
    mobile_dashboard_args = {
        "name": "开发者报告(移动端)" + str(int(time.time())) + str(random.randint(1, 1000)).rjust(4, '0'),
        "platform": "mobile",
        "theme": "colorful_white",
        "is_show_mark_img": 1,
        "type": "FILE",
        "parent_id": "",
        "layout": "{ \"mode\": \"grid\" }",
        "background": "{\"show\": true,\"color\":\"#F7F8FA\",\"image\":\"\",\"size\":\"stretch\"}",
        "create_type": 1,
        "new_layout_type": 1,
        "terminal_type": "mobile_screen",
        "created_by": kwargs.get('account'),
        "modified_by": kwargs.get('account'),
    }
    mobile_model = ChartDashboardModel(**mobile_dashboard_args)
    return add_dashboard(mobile_model)


def _check_dev_user_mobile_dashboard(dashboards):
    """
    检测开发者是否有移动报告
    :param dashboards:
    :return:
    """
    has_mobile_dashboard = False
    for dashboard in dashboards:
        if dashboard["platform"] == 'mobile':
            has_mobile_dashboard = True
            break
    return has_mobile_dashboard


def set_dashboard_to_cookie_by_create_user(username, response, request):
    """
    通过创建者用户名获取报告信息（供组件标准化中使用）存入cookie中
    :param username: 创建者用户名称
    :param response: 响应对象
    :param request: 请求对象
    :return:
    """
    if not username:
        raise UserError(message='创建者用户名为空')
    dashboards = dashboard_repository.get_dashboard_by_create_user(username)
    # 检查若开发者没有移动报告则添加
    dashboard_cookies = _get_dev_user_cookies(dashboards, username)
    if dashboard_cookies:
        for dashboard_cookie in dashboard_cookies:
            name = 'mobile_dashboard_id' if dashboard_cookie['platform'] == 'mobile' else 'pc_dashboard_id'
            response.set_cookie(
                name=name,
                value=dashboard_cookie["id"],
                max_age=None,
                domain=request.host,
                path='/',
                secure=False,
                http_only=False,
            )
    return dashboards


def _get_dev_user_cookies(dashboards, username):
    dashboard_cookies = []
    has_mobile_dashboard = False
    has_pc_dashboard = False
    for dashboard in dashboards:
        dashboard_cookies.append({"id": dashboard.get('id'), "platform": dashboard.get("platform")})
        if dashboard.get("platform") == 'mobile':
            has_mobile_dashboard = True
        if dashboard.get("platform") == 'pc':
            has_pc_dashboard = True
    if not has_mobile_dashboard:
        dashboard_id = _init_mobile_dashboard_to_dev_user(**{"account": username})
        if dashboard_id:
            dashboard_cookies.append({"id": dashboard_id, "platform": "mobile"})
    if not has_pc_dashboard:
        dashboard_id = _init_pc_dashboard_to_dev_user(**{"account": username})
        if dashboard_id:
            dashboard_cookies.append({"id": dashboard_id, "platform": "pc"})
    return dashboard_cookies


def get_dashboards_by_ids(dashboard_ids):
    if not dashboard_ids:
        return []

    return repository.get_data_by_sql(
        "select  id, name, modified_on, created_on from dashboard where id in %(ids)s order by created_on desc",
        {'ids': dashboard_ids},
    )


def get_application_type(dashboard_id):
    data = repository.get_column("dashboard", {"id": dashboard_id}, "application_type")
    return data[0] if data else None


def get_custom_redirect_url(dashboard_id, need_normal_share_url_flag=True):
    """
    获取第三方自定义跳转url
    :param dashboard_id:
    :param need_normal_share_url_flag:
    :return:
    """
    if not dashboard_id:
        raise UserError(message="报告ID不能为空")

    dashboard_info = repository.get_one(
        "dashboard", conditions={"id": dashboard_id}, fields=["biz_code", "terminal_type", "is_multiple_screen"]
    )

    terminal_type = dashboard_info.get("terminal_type") if dashboard_info else ""
    suffix_url = generate_base_url_by_terminal_type(dashboard_id, g.code, terminal_type)

    base_redirect_url = (
        config.get("ThirdParty.screen_base_redirect_url", "")
        if dashboard_info.get("is_multiple_screen")
        else config.get("ThirdParty.base_redirect_url", "")
    )
    application_type = get_application_type(dashboard_id)
    if application_type in ["1", 1]:
        base_redirect_url = config.get("SelfService.self_redirect_url", "")
    normal_share_url = urljoin(base=config.get("Domain.dmp"), url=suffix_url)
    if not base_redirect_url:
        return normal_share_url if need_normal_share_url_flag else ""

    # 获取biz_code
    biz_code = dashboard_info.get("biz_code") if dashboard_info else ""
    if not biz_code:
        return normal_share_url if need_normal_share_url_flag else ""

    # 拼接后的url请求参数
    parsed_url = urlparse(base_redirect_url)

    extend_url_params = (
        urlencode({"biz_code": biz_code, "code": g.code})
        if not dashboard_info.get("is_multiple_screen")
        else urlencode({"screen_id": dashboard_id, "code": g.code})
    )
    new_query_params = parsed_url.query + "&" + extend_url_params if parsed_url.query else extend_url_params

    # 重新组装url
    new_redirect_url = urlunparse(
        [
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query_params,
            parsed_url.fragment,
        ]
    )
    return new_redirect_url


def get_custom_normal_redirect_url(dashboard_id):
    if not dashboard_id:
        raise UserError(message="报告ID不能为空")

    dashboard_info = repository.get_one(
        "dashboard", conditions={"id": dashboard_id}, fields=["biz_code", "terminal_type", "is_multiple_screen"]
    )

    terminal_type = dashboard_info.get("terminal_type") if dashboard_info else ""
    suffix_url = generate_base_url_by_terminal_type(dashboard_id, g.code, terminal_type)
    normal_share_url = urljoin(base=config.get("Domain.dmp"), url=suffix_url)
    return normal_share_url


def get_dashboard_value_sources_by_id(dashboard_id: str) -> List[Any]:
    """
    根据报告ID获取取值来源配置数据
    :param dashboard_id:
    :return:
    """
    fields = ['id', 'value_source_name', 'value_source', 'value_identifier']
    value_source_query_data = repository.get_data(
        'dashboard_value_source', {'dashboard_id': dashboard_id}, fields, multi_row=True
    )
    if not value_source_query_data:
        return []
    value_source_query_data = list(
        filter(lambda i: i.get("value_source") != DatasetVarValueSource.Userdefined.value, value_source_query_data)
    )
    temp_value_source_dict = defaultdict(list)
    relation_query_data = repository.get_data(
        "dashboard_vars_value_source_relation", {"dashboard_id": dashboard_id}, multi_row=True
    )
    for single_relation in relation_query_data:
        temp_value_source_dict[single_relation.get("value_source_id")].append(single_relation.get("var_id", ""))
    for single_value_source in value_source_query_data:
        single_value_source["relations"] = (
            temp_value_source_dict.get(single_value_source.get("id"))
            if temp_value_source_dict.get(single_value_source.get("id"))
            else []
        )
    return value_source_query_data


def generate_base_url_by_terminal_type(dashboard_id, project_code, terminal_type):
    """
    生成报告访问链接
    注：pc报告和移动报告的访问链接有区别
    :param dashboard_id:
    :param project_code:
    :param terminal_type:
    """
    access_url = '/dataview/share/%s?code=%s'
    if terminal_type == DashboardTerminalType.Mobile.value:
        access_url = '/dataview-mobile/view/%s?code=%s'
    return access_url % (dashboard_id, project_code)


def dashboard_check_permission(user_id, dashboard_id, action_code):
    """
    检查报告是否有相关权限
    :param user_id:
    :param dashboard_id:
    :return:
    """
    try:
        root_dashboard = get_root_dashboard_by_dashboard_id(dashboard_id)
        flag = rbac_service.check_has_data_permission(
            "dashboard", action_code, data_id=root_dashboard.get('id'), user_id=user_id, check_creator=False
        )
    # check_has_data_permission 函数内，会对没有权限的数据抛UserError错误，这里不做处理
    except UserError:
        flag = False
    return flag


def get_dashboard_operator_info(dashboard_id):
    return repository.get_one('dashboard_extra', {'dashboard_id': dashboard_id}, order_by="released_on desc")

def get_erp_api_instance(dataset_id=''):
    erp_api_info_id = None
    if dataset_id:
        dataset_content = repository.get_data_scalar('dataset', {'id': dataset_id}, "content")
        if dataset_content:
            dataset_content = json.loads(dataset_content)
            bind_source_id = dataset_content.get("bind_source_id")
            #  支持op和SaaS两套图片获取,有些数据源是source_id ,此处由于后续必须取数据源对应的接口管家id，所以如果没有接口管家id 则取project表默认的 接口管家地址，
            #  此处会有个隐藏问题，如果产品出厂的1.5 理论上默认是SaaS接口地址，但是project表存储的是op的地址，就会寻址错误，此处需要把data_source 表 erp_api_info_id 字段手动更新正确
            if not bind_source_id:
                bind_source_id = dataset_content.get("data_source_id", '')
            if bind_source_id:
                # builtins.code = project_code
                conn_str = repository.get_data_scalar('data_source', {'id': bind_source_id}, 'conn_str')
                if conn_str:
                    erp_api_info_id = json.loads(conn_str).get('erp_api_info_id')
    erp_site = get_mysoft_erp(erp_api_info_id=erp_api_info_id)
    if erp_site['erpapi_host'] is None:
        raise UserError(message='未配置接口管家地址!')
    erp_site['erpapi_access_id'] = '' if erp_site['erpapi_access_id'] is None else erp_site['erpapi_access_id']
    erp_site['erpapi_access_secret'] = (
        '' if erp_site['erpapi_access_secret'] is None else erp_site['erpapi_access_secret']
    )
    api = CloudAPI(
        erp_site['erpapi_host'],
        erp_site['erpapi_access_id'],
        erp_site['erpapi_access_secret'],
        CloudAPI.DATA_CENTER_PATH,
    )
    return api


def try_get_erp_image_url(image_id, dataset_id=''):
    api = get_erp_api_instance(dataset_id)
    if is_support_image_url(api):
        params = {'DocumentGuid': image_id, 'action_name': 'DocImage/GetImageUrl'}
        url = api.datacenter_request(params)
        return url

def get_erp_image_by_id(image_id, dataset_id=''):
    api = get_erp_api_instance(dataset_id)
    params = {'DocumentGuid': image_id, 'action_name': 'DocImage/GetBase64Image'}
    data = api.datacenter_request(params)
    data = base64.b64decode(data)
    image = Image.open(BytesIO(data))
    return image


def is_support_image_url(api):
    cache = conn_redis()
    is_support = False
    if cache.exists(SUPPORT_ERP_IMAGE_URL):
        is_support = cache.get(SUPPORT_ERP_IMAGE_URL)
    else:
        params = {'action_name': 'DocImage/GetDocSupportList'}
        data = api.datacenter_request(params)
        if data:
            for feature in data:
                if feature == 'ViewImage':
                    is_support = True
                    break
        cache.set(SUPPORT_ERP_IMAGE_URL, is_support, 24 * 60 * 60)
    return is_support


def get_child_dashboard(dashboard_id, is_all=True, with_deleted=False):
    fields = [
        'id',
        'theme',
        'data_report_id',
        'name',
        'type',
        'parent_id',
        'level_code',
        'icon',
        'status',
        'application_type',
        'platform',
        'is_deleted',
        'line_height',
        'smart_beauty_status',
        'asset_id',
        'is_key'
    ]
    dashboard = repository.get_data('dashboard', {'id': dashboard_id}, fields)
    if not dashboard:
        raise UserError(message='当前报告不存在，请刷新页面再试!')
    if is_all:
        return dashboard_repository.get_dashboard_by_level_code(dashboard.get('level_code'), with_deleted)
    else:
        conditions = {'parent_id': dashboard_id}
        if not with_deleted:
            conditions['is_deleted'] = 0
        child_dashboard = repository.get_list('dashboard', conditions, fields)
        child_dashboard.append(dashboard)
        return child_dashboard


def get_sub_child_dashboard_ids(dashboard_data, with_deleted=False):
    all_sub_dashboard = get_sub_child_dashboard(dashboard_data, with_deleted)
    return [child.get('id') for child in all_sub_dashboard]


def get_sub_child_dashboard(dashboard_data, with_deleted=False):
    sub_dashboard_data = [dashboard_data]
    if dashboard_data.get("type") in [DashboardType.File.value, DashboardType.CHILD_FILE.value]:
        sub_dashboard_data = dashboard_repository.get_sub_child_dashboard(dashboard_data, with_deleted)
    return sub_dashboard_data


def get_root_dashboard_by_dashboard_id(dashboard_id):
    dashboard = repository.get_data('dashboard', {'id': dashboard_id})
    if not dashboard:
        raise UserError(message='当前报告不存在，请刷新页面再试!')
    if dashboard.get('type') == DashboardType.File.value:
        return dashboard
    all_dashboard = get_child_dashboard(dashboard_id)
    for dashboard in all_dashboard:
        if dashboard.get('type') == DashboardType.File.value:
            return dashboard


def open_self_service(dashboard_templete_id):
    """
    erp集成打开自助报告
    """
    if not dashboard_templete_id:
        raise UserError(message='自助报表ID未配置!')
    user_id = g.userid
    user_dashboard_dict = {'user_id': user_id, 'dashboard_templete_id': dashboard_templete_id}
    user_dashboard = repository.get_data('user_dashboard', user_dashboard_dict)
    dashboard_id = ''
    if user_dashboard and repository.data_is_exists(
            'dashboard', {'id': user_dashboard.get('id')}
    ):  # 用户报告是否存在，存在则直接打开，不存在则创建
        dashboard_id = user_dashboard.get("id")
        parent_id = user_dashboard.get('parent_id')
    if not dashboard_id:
        dashboard_templete = repository.get_data('dashboard', {'id': dashboard_templete_id})
        if not dashboard_templete:
            raise UserError(message='系统未导入对应自助报表模板，请联系管理员进行导入后再试')
        # 创建父级文件夹
        parent_dashboard_dict = {
            'name': '用户自助报告文件夹实例，根目录创建，页面不显示',
            'parent_id': '',
            'application_type': 1,
            'type': DashboardType.Folder.value,
        }
        # 创建父级文件夹，将用户创建的自助报表实例存放到一个固定的文件夹，防止将报告放到根目录造成文件名重复
        parent_dashboard = repository.get_data('dashboard', parent_dashboard_dict)
        if not parent_dashboard:
            parent_dashboard = add_dashboard(ChartDashboardModel(**parent_dashboard_dict), return_dict=True)
        parent_id = parent_dashboard.get('id')
        # 复制报告
        dashboard_id, errors = copy_dashboard_with_no_permission(
            dashboard_templete_id, parent_id, dashboard_templete.get('name')
        )
        if dashboard_id:
            repository.delete_data('user_dashboard', user_dashboard_dict)
            user_dashboard_dict['id'] = dashboard_id
            repository.add_data('user_dashboard', user_dashboard_dict)
        else:
            raise UserError(message='创建自助报告失败，请联系管理员！')

    url = f'{config.get("Domain.dmp")}/intelligent-report/design/{parent_id}/{dashboard_id}?integration=1'
    hug.redirect.to(url)


def get_report_type_by_dashboard_id(dashboard_id):
    dashboard = repository.get_data('dashboard', {'id': dashboard_id})
    return get_report_type_by_dashboard(dashboard)


def get_report_type_by_dashboard(dashboard):
    if not dashboard:
        raise UserError(message='当前报告不存在，请刷新页面再试!')
    if dashboard.get('platform') == 'pc':
        if dashboard.get('new_layout_type') == 1:
            return 'dashboard'
        elif dashboard.get('new_layout_type') == 0:
            return 'large_screen'
    elif dashboard.get('platform') == 'mobile':
        return 'new_mobile'


def filter_permission_dashboard(dashboard_ids):
    """
    过滤有权限的数据报告
    :param dashboard_ids:
    :return:
    """
    all_dashboard = repository.get_list('dashboard', {'id': dashboard_ids})
    auth_dashboard = []
    auth_dashboard += get_permission_dashboard(all_dashboard)
    return auth_dashboard


@data_permission_filter('dashboard-view')
def get_permission_dashboard(dashboards):
    return dashboards


def get_all_dashboard_by_ids(dashboard_ids: List[Any]):
    """
    批量获取根报告下所有子报告，包含根报告
    :param dashboard_ids:根报告id
    :return:
    """
    if not dashboard_ids:
        return []
    # 过滤，只保留根报告
    root_dashboards = repository.get_list(
        'dashboard', {'id': dashboard_ids, 'type': DashboardType.File.value, 'is_deleted': 0}
    )
    root_dashboard_ids = [dashboard.get('id') for dashboard in root_dashboards]
    return dashboard_repository.get_all_dashboard_by_ids(root_dashboard_ids)


def validate_report_has_reached_limit():
    product_conf = builtins.product_conf  # noqa type: dict
    valid_num = LicenseApi.valid_report_nums(product_conf)
    total = repository.get_data_scalar_by_sql(sql="select count(1) from dashboard where `type` = 'FILE'", params=())
    if total >= valid_num:
        return True
    else:
        return False


def license_control():
    error_msg = '添加失败，当前报告数量达到了产品数量限制'
    # 只有saas模式下会判断租户license
    if has_tenant_license():
        if validate_tenant_report_expire():
            raise UserError(message="当前租户已过期")
        if validate_tenant_report_num():
            raise UserError(message=error_msg)
    # 其他情况会走系统级的license报表数量限制
    else:
        license_validate = getattr(builtins, 'license_validate', {})
        if license_validate.get('is_invalid'):
            raise UserError(message="当前租户已过期")
        if not dispatch_old_env():
            verify_result = validate_report_has_reached_limit()
            if verify_result:
                raise UserError(message=error_msg)


def has_tenant_license():
    # 现有的license有两种， 一是来自于系统级的license，客户自身私有化部署
    # 二是saas模式下，租户级的license
    project = get_project_license(g.code)
    if project['use_license'] == 1 and project['report_num'] is not None:
        return True
    return False


def validate_tenant_report_num():
    project = get_project_license(g.code)

    if (project['use_license'] == 1) and project['report_num'] is not None:
        total = repository.get_data_scalar_by_sql(sql="select count(1) from dashboard where `type` = 'FILE'", params=())
        if total >= (project['report_num'] + 1):
            return True
    return False


def validate_tenant_report_expire():
    project = get_project_license(g.code)
    if project and project['use_license'] == 1:
        try:
            now = datetime.now()
            start = datetime.strptime(project.get('product_start'), "%Y-%m-%d %H:%M:%S")
            end = datetime.strptime(project.get('product_end'), "%Y-%m-%d %H:%M:%S")
            if not (start <= now <= end):
                return True
        except Exception as e:
            logger.error(f"验证失败: {str(e)}")
    return False


def import_hd_report(hd_report_list: list):
    """
    hd_report_list 导入列表
    :param kwargs:
    :return:
    """
    report_ids = []
    add_list = []
    for hd_report in hd_report_list:
        dashboard = dict()
        dashboard['id'] = hd_report.get('id')
        dashboard['name'] = hd_report.get('name')
        dashboard['parent_id'] = hd_report.get('parent_id') if hd_report.get('parent_id') else ''
        dashboard['type'] = DashboardType.File.value
        dashboard['level_code'] = level_sequence_service.generate_dashboard_level_code(dashboard.get('parent_id'))
        dashboard['platform'] = hd_report.get('platform')
        dashboard['new_layout_type'] = 0 if dashboard.get('platform') == 'pc' else 1
        dashboard['status'] = hd_report.get('status')
        dashboard['terminal_type'] = hd_report.get('terminal_type')
        dashboard['application_type'] = ApplicationType.HD_Dashboard.value
        dashboard['is_highdata'] = 1
        report_ids.append(dashboard.get('id'))
        add_list.append(dashboard)

    if report_ids and repository.get_list('dashboard', {'id': report_ids}):
        raise UserError(message='选中的报告已引入')
    fields = [
        'id',
        'name',
        'parent_id',
        'type',
        'level_code',
        'new_layout_type',
        'platform',
        'status',
        'terminal_type',
        'application_type',
        'is_highdata',
    ]
    return repository.add_list_data('dashboard', add_list, fields)


def get_hd_report_frontend_url(dashboard_id):
    """
    hd_report_list 获取HD跳转URL
    :param dashboard_id:报告ID
    :return:
    """
    dashboard = repository.get_data('dashboard', {"id": dashboard_id})
    if not dashboard:
        raise UserError(message='报告不存在，获取报告类型错误')
    if dashboard.get('application_type') == ApplicationType.HD_Dashboard.value:
        report_type = "dashboard" if dashboard.get('platform') == "pc" else "mreport"
        from integrate import external_service

        url = external_service.go_hd_frontend_report_url(dashboard_id, report_type)
        return url
    return ""


def get_dashboard_url_of_external(kwargs):
    dashboard_id = kwargs.get('dashboard_id')
    dashboard = repository.get_data('dashboard', {"id": dashboard_id})
    if not dashboard:
        raise UserError(message='报告不存在，获取报告类型错误')
    if dashboard.get('application_type') not in [ApplicationType.External_Dashboard.value,
                                                 ApplicationType.HD_Dashboard.value, ApplicationType.FineReport.value]:
        raise UserError(message='非外部报告类型')

    if dashboard.get('application_type') == ApplicationType.External_Dashboard.value:  # 外部报告
        from integrate.services import integrate_service

        external_url = dashboard.get('external_url')
        if not external_url:
            raise UserError(message='外部报告url不存在')

        # 云链自研报表需要替换占位符
        # https://www.tapd.cn/38229611/prong/stories/view/1138229611001489293?action_entry_type=stories
        # 处理占位符
        external_url = get_report_url_of_yl(external_url)
        external_url = url_add_param(external_url,
                                     {"user_code": integrate_service.get_aes_user_code()})
    elif dashboard.get('application_type') == ApplicationType.FineReport.value:  # 帆软报表
        # 帆软报表这里存的是报表id, 需要通过报表id实时获取报表链接
        report_id = dashboard.get('external_url')
        if not report_id:
            raise UserError(message='帆软报告id不存在')
        result = IngratePlatformApi(code=g.code).get_fine_report_view_url(report_id) or {}
        external_url = result.get(report_id)
    else:  # HD报告
        external_url = get_hd_report_frontend_url(dashboard_id)
        kwargs.pop('dashboard_id')
        if kwargs.get('token'):
            kwargs.pop('token')
        external_url = url_add_param(external_url, kwargs)

    return external_url


def redirect_external_report(**kwargs):
    """
    hd_report_list 调转到HD报告
    :param kwargs:报告ID
    :return:
    """
    external_url = get_dashboard_url_of_external(kwargs)
    hug.redirect.to(external_url)


def get_report_url_of_yl(url: str):
    """
    处理占位符
    :param url:
    :return:
    """
    try:
        if "${超级工作台前台地址}" not in url and "${根域名}" not in url:
            return url
        # 获取超级APP host
        from components.common_service import get_superportal_host
        superportal_host = get_superportal_host()
        client_domain = config.get("Domain.client_domain")
        # 将work/tj-work替换为：{企业代码}+'.portal'
        superportal_host = superportal_host.replace("work/tj-work", f"{g.code}.portal")
        if superportal_host:
            url = url.replace('${超级工作台前台地址}', superportal_host)
        if client_domain:
            url = url.replace('${根域名}', client_domain)
        return url
    except Exception as e:
        msg = f"处理云链自研报表占位符异常: {e}"
        logger.exception(msg=msg)
        return url


def get_url_for_external_report_redirect(dashboard_id):
    """
    hd_report_list 获取hd报告跳转的中间链接，为hd报告挑战提供dmp的入口
    :param dashboard_id:报告ID
    :return:
    """
    dashboard = repository.get_data('dashboard', {"id": dashboard_id})
    if dashboard and dashboard.get('application_type') in [ApplicationType.HD_Dashboard.value,
                                                           ApplicationType.External_Dashboard.value,
                                                           ApplicationType.FineReport.value]:
        from rbac.services import data_permissions
        data_permissions.check_has_data_permission('dashboard', 'view', data_id=dashboard_id)
        return f"{config.get('Domain.dmp')}/api/dashboard_chart/redirect_external_report?dashboard_id={dashboard_id}"
    return None


def update_external_report_status(dashboard_id, status, application_type):
    """
    hd_report_list 修改hd_report报告状态
    :param dashboard_id:报告ID
    :param status:状态
    :return:
    """
    repository.update(
        'dashboard', {"status": status}, {"id": dashboard_id, "application_type": application_type}
    )


def auto_download_report_from_rdc(dashboard_id):
    """
    应用市场自动下载报告
    :param dashboard_id:报告ID
    :return:
    """
    if not dashboard_id:
        return None

    if not repository.data_is_exists('dashboard', {'id': dashboard_id}):
        # 查找数据服务中心的数据库版本
        is_dashboard_edition, sql_edition_dashboard_id = _get_sql_edition_dashboard_id(dashboard_id)

        # 查找MySQL或SqlServer版本的报告
        if is_dashboard_edition:
            dashboard_info = None
            try:
                dashboard_info = _get_yysc_dashboard_info(sql_edition_dashboard_id)
            except Exception as e:
                logger.error(f"获取应用市场数据库版本的报告失败，"
                             f"sql_edition_dashboard_id: {sql_edition_dashboard_id} errs:{str(e)}")
            # 默认报告下载
            if not dashboard_info or not dashboard_info.get('downloadUrl'):
                dashboard_info = _request_get_yysc_dashboard_info(dashboard_id)
        else:
            dashboard_info = _request_get_yysc_dashboard_info(dashboard_id)

        download_url = dashboard_info.get('downloadUrl')
        file_name = dashboard_info.get('name')

        upload_url = _upload_yysc_dashboard(download_url, file_name)
        return _import_yysc_dashboard(upload_url, _get_yysc_dashboard_folder(), _get_yysc_dataset_folder(), False)
    return None


def _get_sql_edition_dashboard_id(dashboard_id):
    """
    获取数据库版本的报告id
    """
    is_dashboard_edition = False
    data_source_model = get_new_erp_datasource_model()
    if not data_source_model:
        logger.error("数据服务中心数据源不存在，不需要匹配数据库版本报告")
        return is_dashboard_edition, dashboard_id

    db_type = data_source_model.db_type if data_source_model.db_type else ''
    sql_edition_dashboard_id = dashboard_id
    if db_type:
        sql_edition_dashboard_id = dashboard_id + '_' + db_type
        sql_edition_dashboard_id = sql_edition_dashboard_id.lower()
    # 获取原始的报告id
    if sql_edition_dashboard_id.endswith('_sqlserver') or sql_edition_dashboard_id.endswith('_mysql'):
        is_dashboard_edition = True
        dashboard_id = sql_edition_dashboard_id
    return is_dashboard_edition, dashboard_id


def _request_get_yysc_dashboard_info(dashboard_id):
    dashboard_info = _get_yysc_dashboard_info(dashboard_id)
    if not dashboard_info or not dashboard_info.get('downloadUrl'):
        raise UserError(message="未获取到对应报告下载地址")
    return dashboard_info


def _get_yysc_dashboard_info(dashboard_id):
    """
    获取应用市场报告信息
    :return:
    """
    url = f"api/v1/plugin-infos/biz/{dashboard_id}"
    return _get_yysc_dashboard_info_by_url(url)


def _get_yysc_dashboard_info_by_url(url):
    """
    获取应用市场报告信息
    :return:
    """
    jwt_token = _generate_yysc_jwt_token()
    api_url = config.get('Yysc.api_url', "https://rdc.mingyuanyun.com/rdc-exchange-service")
    url = f"{api_url}/{url}"
    response_data = requests.post(url=url, json={}, headers={"ErpJwt": jwt_token}, timeout=100).json()
    if response_data.get('code') != 200:
        raise UserError(message=response_data.get('msg'))
    return response_data.get('data')


def _upload_yysc_dashboard(download_url, file_name):
    """
    下载应用市场报告，并上传到oss
    :return:
    """
    jwt_token = _generate_yysc_jwt_token()
    req = request.Request(download_url, headers={"ErpJwt": jwt_token})
    res = request.urlopen(req)
    byte_io = io.BytesIO(res.read())
    upload_location = f'upload-file/{int(round(time.time() * 1000000))}/{file_name}.zip'
    upload_params = {'file': byte_io, 'upload_location': upload_location, 'file_name': f'{file_name}.zip'}
    data = external_service_upload.upload_file(**upload_params)
    return data.get('url')


def _import_yysc_dashboard(url, target_dashboard_id, target_dataset_id, include_folder):
    # 报告导入
    from imports import external_service

    file_data = external_service.get_preview_data(url, False)
    dashboards = [d for d in file_data.get('dashboards') if d.get('type') != DashboardType.Folder.value]
    datasets = [d for d in file_data.get('datasets') if d.get('type') != DatasetType.Folder.value]
    ppt_datas = extract_and_process_ppt_report(file_data)
    # PPT的报告放在他们根目录文件夹
    if file_data.get(ProjectValueAddedFunc.PPT.value):
        target_dashboard_id = ''
    params = {
        "include_dashboard_folder": include_folder,
        "include_dataset_folder": include_folder,
        "oss_url": url,
        "pick_data": {
            "dashboards": dashboards,
            "datasets": datasets,
            "applications": file_data.get('applications'),
            ProjectValueAddedFunc.PPT.value: ppt_datas
        },
        "target_dashboard_folder_id": target_dashboard_id,
        "target_dataset_folder_id": target_dataset_id,
    }

    return external_service.dashboard_import_data(**params)


def extract_sub_report(datas: list, result: list):
    for data in datas:
        sub = data.pop('sub', None) or []
        if data.get('type') != "FOLDER":
            extra_data = {
                "sub": [],
                "check_status": True,
                "text": data.get('name') or '',
                "children": [],
                "level": 1,
                "childFileList": None
            }
            data.update(extra_data)
            result.append(data)
        extract_sub_report(sub, result)


def extract_and_process_ppt_report(file_data):
    ppt_datas = file_data.get(ProjectValueAddedFunc.PPT.value) or []
    result = []
    extract_sub_report(ppt_datas, result)
    return result


def _get_yysc_dashboard_folder():
    # 创建父级文件夹
    parent_id = '1569e1a6-e444-11eb-9287-00155d0a440c'
    parent_dashboard_dict = {
        'id': parent_id,
        'name': '应用市场下载报告',
        'parent_id': '',
        'application_type': 0,
        'type': DashboardType.Folder.value,
    }
    # 创建父级文件夹，将用户创建的自助报表实例存放到一个固定的文件夹，防止将报告放到根目录造成文件名重复
    parent_dashboard = repository.get_data('dashboard', {'id': parent_id})
    if not parent_dashboard:
        folder = add_dashboard(ChartDashboardModel(**parent_dashboard_dict), return_dict=True)
        repository.update('dashboard', {'id': parent_id}, {'id': folder.get('id')})
    return parent_id


def _get_yysc_dataset_folder():
    # 创建父级数据集文件夹
    parent_id = '23292623-e444-11eb-9287-00155d0a440c'
    parent_dataset_dict = {'id': parent_id, 'name': '应用市场下载数据集', 'parent_id': '',
                           'type': DatasetType.Folder.value}
    parent_dataset = repository.get_data('dataset', {"id": parent_id})
    if not parent_dataset:
        from dataset import external_query_service
        from dataset.models import DatasetModel

        external_query_service.add_dataset(DatasetModel(**parent_dataset_dict))
    return parent_id


def _generate_yysc_jwt_token():
    """
    生成请求应用市场jwt_token
    :return:
    """
    aud = config.get('Yysc.aud', 'https://appexchange.mingyuanyun.com')
    exp = int(config.get('Yysc.exp', 3600))
    secret = config.get('Yysc.secret', '37EF41DE-CE7E-11EB-B44F-0242AC110008')

    project_yzs_config = repository.get_data('project_yzs_config', {'code': g.code}, from_config_db=True)
    payload = {
        'iss': config.get('Yysc.iss', 'dataplatform'),
        'iat': int(time.time()),
        'exp': int((dt.datetime.now() + dt.timedelta(seconds=exp)).timestamp()),
        'aud': aud,
        'u': g.code,
        'tn': g.code,
        'c': project_yzs_config.get('customer_id') if project_yzs_config else '',
        'jti': seq_id(),
    }
    headers = {'alg': "HS256", 'typ': 'JWT'}
    return jwt.encode(payload, secret, algorithm="HS256", headers=headers)


def get_yysc_report_install_status(task_id):
    dashboard_imports = repository.get_data('dashboard_imports', {'id': task_id}, ['status', 'message'])
    task_status = dashboard_imports.get('status')
    task_status_dict = dict()
    task_status_dict["已创建"] = 1
    task_status_dict["运行中"] = 2
    task_status_dict["成功"] = 3
    task_status_dict["失败"] = 4
    return {
        "status": task_status_dict[task_status],
        'status_name': task_status,
        "message": dashboard_imports.get('message'),
    }


def get_yysc_url():
    """
    跳转到应用市场
    :param :
    :return:
    """
    aud = config.get('Yysc.aud', 'https://appexchange.mingyuanyun.com')
    jwt_token = _generate_yysc_jwt_token()
    callback_url = f"{config.get('Domain.dmp')}/api/dashboard_chart/install_yysc_dashboard"
    return f'{aud}/?token={jwt_token}&callback={callback_url}&moduleId=sjmb'


def get_yysc_install_info(token):
    """
    应用市场安装回调
    :param token:
    :return:
    """
    secret = config.get('Yysc.secret', '37EF41DE-CE7E-11EB-B44F-0242AC110008')
    iss = (config.get('Yysc.iss', 'dataplatform'),)
    payload = jwt.decode(token, secret, algorithm=['HS256'], verify=True, audience=iss)
    plugin_id = payload.get('pluginId')
    version_id = payload.get('versionId')
    url = f"api/v1/plugin-infos/{plugin_id}/ver/{version_id}"
    dashboard_info = _get_yysc_dashboard_info_by_url(url)
    if not dashboard_info or not dashboard_info.get('downloadUrl'):
        raise UserError(message="未获取到对应报告下载地址")
    return {
        'id': dashboard_info.get('id'),
        'name': dashboard_info.get('name'),
        'version': dashboard_info.get('version'),
        'app_code': dashboard_info.get('appCode'),
        'dependent_product_key': dashboard_info.get('dependentProductKey'),
        'dependent_product_name': dashboard_info.get('dependentProductName'),
        'dependent_version_start': dashboard_info.get('dependentVersionStart'),
        'dependent_version_end': dashboard_info.get('dependentVersionEnd'),
        'download_url': dashboard_info.get('downloadUrl'),
        'plugin_id': plugin_id,
        'version_id': version_id,
    }


def install_yysc_dashboard(token):
    """
    应用市场报告安装
    :param token:
    :return:
    """
    dashboard_info = get_yysc_install_info(token)
    target_dashboard_id = _get_yysc_dashboard_folder()
    target_dataset_id = _get_yysc_dataset_folder()

    download_url = dashboard_info.get('download_url')
    file_name = dashboard_info.get('name')
    upload_url = _upload_yysc_dashboard(download_url, file_name)
    apply_install_progress(dashboard_info.get('id'), dashboard_info.get('version'), 2)
    task_id = _import_yysc_dashboard(upload_url, target_dashboard_id, target_dataset_id, False)
    count = 10
    result = False
    while count > 0:
        count = count - 1
        time.sleep(5)
        # 5秒获取一次
        status_info = get_yysc_report_install_status(task_id)
        status = status_info.get('status')
        logger.info(f'install yysc dashboard status: {status}')
        if status in [3, 4]:
            apply_install_progress(dashboard_info.get('id'), dashboard_info.get('version'), status)
            result = True
            break
    if not result:  # 安装失败
        apply_install_progress(dashboard_info.get('id'), dashboard_info.get('version'), 4)
    # hug.redirect.to(get_yysc_url())
    hug.redirect.to('/home')


def apply_install_progress(plugin_info_id, version, status):
    api_host = config.get('Yysc.api_url', "https://rdc.mingyuanyun.com/rdc-exchange-service")
    url = f"{api_host}/api/v1/plugin-version/apply"
    project_yzs_config = repository.get_data('project_yzs_config', {'code': g.code}, from_config_db=True)
    customer_id = project_yzs_config.get('customer_id') if project_yzs_config else ''
    created_time = int(dt.datetime.now().timestamp())
    status_name = '正在安装中'
    if status == 3:
        status_name = '安装完成'
    if status == 4:
        status_name = '安装失败'
    creator_name = repository.get_data('user', {'account': g.account}, ['name']).get('name')
    customer_name = repository.get_data('project', {'code': g.code}, ['name'], from_config_db=True).get('name')
    install_info = {
        'pluginInfoId': plugin_info_id,
        'customerId': customer_id,
        'customerName': customer_name,
        'tenantCode': g.code,
        'creationTime': created_time,
        'creatorName': creator_name,
        'version': version,
        'environment': config.get('App.runtime'),
        'installState': status,
        'installMessage': status_name,
    }
    jwt_token = _generate_yysc_jwt_token()
    response = requests.post(url, json=install_info, headers={"ErpJwt": jwt_token}, timeout=100).json()
    if response.get('code') != 200:
        raise UserError(message=response.get('msg'))


# def query_data_when_dashboard_from_yysc(dashboard_id, chart_id):
#     # 当报告来之用用市场时候，如果取数时候，数据集还没调度完成，提示用户为调度完成，等待调度完成
#     yysc_dashboard_folder_parent_id = '1569e1a6-e444-11eb-9287-00155d0a440c'
#     yysc_dataset_folder_id = '23292623-e444-11eb-9287-00155d0a440c'
#     yysc_dashboard_parent_level_code = dashboard_repository.get_parent_dashboard_level_code(yysc_dashboard_folder_parent_id)
#     if not yysc_dashboard_parent_level_code:
#         # 租户还没有应用市场这个文件夹
#         return []
#     yysc_dataset_parent_level_code = dashboard_repository.get_parent_dataset_level_code(yysc_dataset_folder_id)
#     if not yysc_dataset_parent_level_code:
#         # 数据集还没有应用市场这个文件夹
#         return []
#
#     # 是否是来自应用市场的报告
#     is_son_dashboard = dashboard_repository.is_son_dashboard_by_dashboard_id(dashboard_id, yysc_dashboard_parent_level_code)
#     if not is_son_dashboard:
#         return []
#
#     datasets = dashboard_repository.get_chart_dataset(chart_id) or {}
#     dataset_id = datasets.get('source',  '')
#     # 检查所有的依赖数据集时候调度完成
#     s_datasets = dashboard_repository.get_last_scheduling_datasets([dataset_id])
#     if not s_datasets:
#         return []
#     dataset_ids = [d.get('flow_id') for d in s_datasets if d.get('flow_id') if d.get('status') in ('已创建', '运行中')]
#     return dataset_ids


def check_dashboard_line_height(node_data_model):
    try:
        source_line_height = int(node_data_model.source_dashboard_metadata.get('dashboard').get('line_height', 40))
        target_line_height = int(node_data_model.target_dashboard_metadata.get('dashboard').get('line_height', 40))
        if abs(source_line_height) == 5 and abs(target_line_height) == 5:
            return
        if source_line_height != target_line_height:
            charts = node_data_model.source_dashboard_metadata.get('first_report').get('charts', [])
            for chart in charts:
                if chart.get('is_highdata', 0) == 1:
                    continue
                position = chart.get('position', {})
                change_dashboard_line_height(position, source_line_height, target_line_height)
    except Exception as e:
        msg = "行高变更错误：" + str(e)
        logger.error(msg)


def change_dashboard_line_height(position: dict, source_line_height, target_line_height):
    source_line_height = abs(source_line_height)
    target_line_height = abs(target_line_height)
    search_key = ['col', 'row', 'size_x', 'size_y', 'header_position']
    head_key = ['x', 'y', 'w', 'h']
    ratio = (
        (source_line_height + 10) / target_line_height
        if source_line_height == 40
        else source_line_height / (target_line_height + 10)
    )
    for s_key in search_key:
        if s_key in position.keys():
            if s_key != 'header_position':
                position[s_key] = round(position[s_key] * ratio, 2)
            else:
                for h_key in head_key:
                    if h_key in position[s_key].keys():
                        position[s_key][h_key] = round(position[s_key][h_key] * ratio, 2)


def delete_dashboards(dashboard_ids):
    """
    报告删除，应用于报告回收场景
    :param dashboard_ids:
    :return:
    """
    try:
        for dashboard_id in dashboard_ids:
            dashboard = repository.get_data('dashboard', {"id": dashboard_id})
            # 报告不存在，或者非报告，直接跳过
            if not dashboard or dashboard.get('type') != DashboardType.File.value:
                continue

            # 报告是发布状态，先取消发布
            if dashboard.get('status') == DashboardStatus.Released.value:
                model = ReleaseModel(
                    **{
                        "id": dashboard_id,
                        "status": DashboardStatus.Drafted.value,
                        "type_access_released": dashboard.get('type_access_released'),
                    }
                )
                released_dashboard_service.release_dashboard(model)
            # 删除报告
            execute_delete_dashboard(dashboard_id)
    except:
        pass
    return "删除成功"


@data_permission_edit_filter('dashboard-edit')
def edit_external_report(id, name, external_url):
    """
    修改外部报告信息
    :param token:
    :return:
    """
    dashboard = repository.get_data("dashboard", {"id": id})
    if not dashboard:
        raise UserError(message="报告不存在，请刷新页面再试")
    if dashboard.get('application_type') != ApplicationType.External_Dashboard.value:
        raise UserError(message="非外部报告，不允许编辑")
    if external_url != dashboard.get('external_url'):
        if dashboard.get('status') == DashboardStatus.Released.value:
            raise UserError(message="已发布报告，不允许修改url")
        repository.update('dashboard', {"external_url": external_url}, {"id": id})
    if name != dashboard.get('name'):
        model = ChartDashboardModel(**dashboard)
        model.name = name
        update_dashboard_name(model)


def fix_dashboard_level_code(dashboard_id):
    dashboard = repository.get_data("dashboard", {"id": dashboard_id})
    if not dashboard:
        raise UserError(message="报告不存在")

    # 如果以当前报告为根，或当前报告通过父级id找不到父级，重新生成当前报告level_code,parent_id置为空
    parent_id = dashboard.get('parent_id') if dashboard.get('parent_id') else ''
    level_code = dashboard.get('level_code')
    if level_code != '9000-':
        parent_dashboard = repository.get_data("dashboard", {"id": parent_id})
        if not parent_dashboard:
            parent_id = ''
            level_code = level_sequence_service.generate_dashboard_level_code(parent_id)
            repository.update('dashboard', {'level_code': level_code, 'parent_id': parent_id}, {"id": dashboard_id})

    all_dashboard = []
    # 通过父级id递归获取报告，如果存在循环依赖，报异常
    all_dashboard = recursive_get_dashboard([dashboard_id], all_dashboard)
    all_dashboard.append({'id': dashboard_id, 'parent_id': parent_id, 'level_code': level_code})

    # 计算所有报告level_sequence
    dashboards_dict, level_sequences = get_level_sequence(all_dashboard)

    # 递归计算所有报告level_code
    recursive_set_dashboard_level_code(dashboards_dict, dashboard_id, level_code)
    with get_db() as db:
        try:
            db.begin_transaction()
            for dashboard in all_dashboard:
                db.update('dashboard', dashboard, {"id": dashboard.get('id')}, commit=False)
            for level_sequence in level_sequences:
                db.delete('level_sequence', {'table_name': 'dashboard', 'level_id': level_sequence.get('level_id')},
                          commit=False)
                db.insert('level_sequence', level_sequence, commit=False)
            db.commit()
        except:
            db.rollback()
    return dashboards_dict


def recursive_get_dashboard(parent_ids: list, result: list):
    """
    通过父级id递归获取报告，如果存在循环依赖，报异常
    :param parent_ids:父级id
    :param result:查询结果
    :return:
    """
    child_dashboards = repository.get_list('dashboard', {"parent_id": parent_ids}, ['id', 'parent_id'],
                                           order_by='level_code asc')
    if child_dashboards:
        child_dashboard_ids = []
        exist_parent_ids = [item.get('id') for item in result]
        for child in child_dashboards:
            if child.get('id') in exist_parent_ids:
                raise UserError(message=f"报告id={child.get('id')}，父级id，存在循环依赖，请修复完数据后，再调用此接口")
            child_dashboard_ids.append(child.get('id'))
            result.append(child)
        recursive_get_dashboard(child_dashboard_ids, result)
    return result


def get_level_sequence(all_dashboard: list):
    dashboards_dict = {}
    level_sequence_list = []
    for item in all_dashboard:
        parent_id = item.get('id')
        childs = [item for item in all_dashboard if item.get('parent_id') == parent_id]
        if childs:
            level_sequence_list.append({'table_name': 'dashboard', 'level_id': parent_id, 'max_sequence': len(childs)})
            dashboards_dict[parent_id] = childs
    return dashboards_dict, level_sequence_list


def recursive_set_dashboard_level_code(dashboards_dict: dict, parent_id, parent_level_code):
    """
    通过父级递归重置子集level_code
    :param dashboards_dict:
    :param parent_id:
    :param parent_level_code:
    :return:
    """
    child_dashboards = dashboards_dict.get(parent_id)
    if child_dashboards:
        for i, child in enumerate(child_dashboards):
            level_code = parent_level_code + str(i + 1).zfill(4) + "-"
            child['level_code'] = level_code
            if child.get('id'):
                recursive_set_dashboard_level_code(dashboards_dict, child.get('id'), level_code)


def get_dashboard_jump_global_params_by_id(dashboard_id):
    table_name = DashboardGlobalParamsModel.__tablename__
    return repository.get_data(
        table_name, conditions={'dashboard_id': dashboard_id}, multi_row=True, fields=['id', 'name', 'alias_name'],
        order_by=[('created_on', 'asc'), ('name', 'asc')]
    )


def get_dashboard_and_chart_name(dashboard_id, chart_id, is_release=False):
    return dashboard_repository.get_dashboard_and_chart_name(dashboard_id, chart_id, is_release=False)


class ArgsSet:
    def __init__(self, request: hug.Request, response: hug.Response, args, kwargs):
        self.request = request
        self.response = response
        self.args = args
        self.kwargs = kwargs
        self.unreleased_funcs = [
            'set_query_mode',
            'set_sql_debug',
            'set_request_args',
        ]
        self.released_funcs = [
            'set_query_mode',
            'set_request_args',
        ]
        self.default_funcs = [
            'set_request_args',
        ]

    def set_query_mode(self):
        # 设置取数模式
        from dashboard_chart.data_query.charts.common_chart import set_query_mode
        set_query_mode(kwargs=self.kwargs)

    def set_sql_debug(self):
        # 是否开启性能医生的debug模式
        from components.analysis_time import set_sql_debug
        set_sql_debug(self.request)

    def set_request_args(self):
        # 是否开启性能医生的debug模式
        _request_data = {
            'headers': self.request.headers,
            'params': self.request.params,
            'path': self.request.path,
            'cookies': self.request.cookies,
        }
        setattr(g, 'request_data', _request_data)

    def set(self):
        funcs = []
        if self.request.path.startswith('/api/dashboard_chart/chart/data'):
            funcs = self.unreleased_funcs
        elif self.request.path.startswith('/api/released_dashboard_chart'):
            funcs = self.released_funcs
        else:
            funcs = self.default_funcs

        for func_name in funcs:
            function = getattr(self, func_name, None)
            if not function:
                continue
            function()


def request_args_setting(func):
    def wrapper(request, response, *args, **kwargs):
        ArgsSet(request, response, args, kwargs).set()
        return func(request, response, *args, **kwargs)

    return wrapper


def get_erp_system_url():
    """
    获取erp系统的站点url接口
    """
    return get_erp_site_url()


def copy_as_child_dashboard(source_dashboard_id, target_parent_dashboard_id):
    if not all([source_dashboard_id, target_parent_dashboard_id]):
        raise UserError(message='缺少原始或者目标报告id!')

    source_dashboard = repository.get_one(
        'dashboard', fields='*',
        conditions={'id': source_dashboard_id, 'type': 'FILE'}
    ) or {}
    if not source_dashboard:
        raise UserError(message='原始报告不存在！')
    target_parent_dashboard = repository.get_one(
        'dashboard', fields='*',
        conditions={'id': target_parent_dashboard_id, 'type': ['FILE', 'CHILD_FILE']}
    ) or {}
    if not target_parent_dashboard:
        raise UserError(message='目标报告不存在！')

    application_type = source_dashboard.get('application_type')
    adapt_function_permission(copy_dashboard, application_type, 'copy')

    result, errors = copy_dashboard(
        source_dashboard_id, parent_id=target_parent_dashboard_id,
        dashboard_name=source_dashboard.get('name'), target_dataset_id='', is_copy_dataset=0
    )
    return result, errors, source_dashboard, target_parent_dashboard


def copy_child_to(source_dashboard_id, target_parent_dashboard_id, copy_target_type):
    if copy_target_type == CopyDashboardType.CHILD_TO_FILE.value:
        if not all([source_dashboard_id]):
            raise UserError(message='缺少原始报告id!')
    else:
        if not all([source_dashboard_id, target_parent_dashboard_id]):
            raise UserError(message='缺少原始或者目标报告id!')

    source_dashboard = repository.get_one(
        'dashboard', fields='*',
        conditions={'id': source_dashboard_id, 'type': ['CHILD_FILE']}
    ) or {}
    if not source_dashboard:
        raise UserError(message='原始报告不存在！')
    filter_type = ['FILE', 'CHILD_FILE']
    if copy_target_type == CopyDashboardType.CHILD_TO_FILE.value:
        filter_type = ["FOLDER"]
    target_parent_dashboard = repository.get_one(
        'dashboard', fields='*',
        conditions={'id': target_parent_dashboard_id, 'type': filter_type}
    ) or {}
    if not target_parent_dashboard:
        raise UserError(message='目标报告不存在！')

    application_type = source_dashboard.get('application_type')
    adapt_function_permission(copy_dashboard, application_type, 'copy')

    result, errors = copy_dashboard(
        source_dashboard_id, parent_id=target_parent_dashboard_id,
        dashboard_name=source_dashboard.get('name'), target_dataset_id='', is_copy_dataset=0,
        copy_target_type=copy_target_type
    )
    return result, errors, source_dashboard, target_parent_dashboard


def export_dashboard_compare_result(response):
    """
    基准租户的分发报告与其他租户分发报告的差异对比结果导出
    :param response:
    :return:
    """
    from components.excel import Excel
    cache_key = 'dmp-admin:dashboard_compare_result'
    compare_result_byte = conn_redis()._connection.get(cache_key)
    if compare_result_byte:
        data = []
        title = {'target_code': '租户code', 'id': '报告id', 'name': '报告名称', 'chart_info': '组件变化',
                 'dataset_info': '数据集变化', 'msg': '备注信息'}
        compare_result_json = compare_result_byte.decode()
        compare_result = json.loads(compare_result_json)
        for item in compare_result:
            chart = item.get("chart", {})
            chart_info = ''
            if chart.get("status"):
                chart_info = "\n".join(chart.get("data"))

            dataset = item.get("dataset", {})
            dataset_info = ''
            if dataset.get("status"):
                dataset_info = "\n".join(dataset.get("data"))

            new_item = {
                "target_code": item.get("target_code"),
                "id": item.get("dashboard_id"),
                "name": item.get("dashboard_name"),
                "chart_info": chart_info,
                "dataset_info": dataset_info,
                "msg": item.get("msg"),
            }
            data.append(new_item)
        env_code = os.environ.get("CONFIG_AGENT_CLIENT_CODE", "")
        Excel.export_csv(f'{env_code}_指定报告的对比结果记录.csv', data, title, response=response)
    else:
        raise UserError(message=f'暂无报告比对数据，请先执行统计操作')


def get_key_dashboard_list():
    """
    获取租户的重点大屏列表
    :return:
    """
    dashboard_list = []
    is_key_project = get_current_project_is_key()
    if is_key_project:
        dashboard_list = dashboard_repository.get_key_dashboard_list()
    return dashboard_list


def get_current_project_is_key():
    """
    获取当前租户是否开启了重点大屏管理功能
    :return:
    """
    project_info = get_project_info(g.code)
    return bool(int(project_info.get("is_key_screen", 0)))


def check_dashboard_is_key(dashboard):
    """
    检查报告是否为重点大屏报告
    :param dashboard: 报告信息
    :return:
    """
    dashboard_is_key = dashboard.get("is_key", 0)
    return get_current_project_is_key() and bool(int(dashboard_is_key))


def set_key_dashboard(dashboard_id, is_key):
    """
    设置或删除报告为重点大屏
    :param dashboard_id:
    :param is_key: 标记是否为添加重点大屏，0：不是，1：是
    :return:
    """
    # 标记重点大屏，对报告进行验证
    if is_key:
        dashboard = get_dashboard(dashboard_id)
        if not dashboard:
            raise UserError(message="报告不存在")
        if dashboard.get("status") != DashboardStatus.Released.value:
            raise UserError(message="报告未发布，请先发布报告再添加")
        # 判断重点大屏数量
        _check_key_dashboard_num()

    # 编辑dashboard表
    update_status = repository.update(
        "dashboard", {"is_key": is_key}, conditions={"id": dashboard_id}
    )
    if update_status:
        # 删除报告运行时元数据缓存，重新加载元数据
        metadata_service.delete_release_metadata(dashboard_id)
        metadata_service.delete_release_metadata_cache(dashboard_id)

    # 标记为非重点大屏，删除报告的上一版本数缓存
    if is_key == 0:
        metadata_service.delete_last_version_cache(dashboard_id)

    return update_status


def _check_key_dashboard_num():
    """
    如果是增加报告为重点大屏，则判断最大数量
    :return:
    """
    allow_num = 5
    num = dashboard_repository.get_key_dashboard_num()
    if num >= allow_num:
        raise UserError(message=f"重点大屏最多允许添加{allow_num}张")


def adapt_function_permission(function, application_type, permission='view'):
    """
    自助报表和酷炫大屏使用仪表版的相同函数，这些函数定义权限是dashboard-view等
    这里将函数的权限转换成自助报表和酷炫大屏的权限进行使用
    """

    # 自助报表
    if str_equal(application_type, ApplicationType.SelfService.value):
        function.set_permissions(f'self_service-{permission}')
    # 酷炫大屏
    elif str_equal(application_type, ApplicationType.LargeScreen.value):
        function.set_permissions(f'large_screen-{permission}')
    # 默认的函数定义权限，一般是仪表权限
    else:
        function.set_permissions(f'dashboard-{permission}')


def get_all_possible_redirect_source_dashboards(target_dashboard_id) -> set:
    # 获取所有可能跳转到这个报告的起跳报告
    ret = set()

    def collect_data(inner_dashboard_id):
        # 必须从已经发布的数据中取数据
        sql = """
        SELECT dashboard_id, jump
        FROM dashboard_released_snapshot_chart
        WHERE jump LIKE %(like_str)s
        """
        result = repository.get_data_by_sql(sql, {"like_str": f'"target": {inner_dashboard_id}'}) or []
        for data in result:
            dashboard_id = data.get('dashboard_id') or ''
            if dashboard_id and dashboard_id not in ret:
                ret.add(dashboard_id)
                collect_data(dashboard_id)

    collect_data(inner_dashboard_id=target_dashboard_id)
    return ret


def get_skyline_call_biz_code_from_path(path: str) -> str:
    """
    从path中取出应用的code
    /api/7000
    /api/7200
    /pub/7200
    /pub/7000
    取出 7000和7200
    Args:
        path: 路由path
    Returns:

    """
    if path.startswith('/api/'):
        biz_code = path[5:9]
    elif path.startswith('/pub/'):
        biz_code = path[5:9]
    else:
        biz_code = ""
    return biz_code


def get_skyline_call_host_from_user_setting(appcode: str, biz_code: str) -> str:
    """
    优先获取用户配置的配置中心地址
    用于解决环境中存在多个配置中心
    Returns: 用户配置的host
    """
    from system.services.system_setting_service import get_system_setting_item
    if not appcode or not biz_code:
        return ""
    data = get_system_setting_item(system_category="skyline_call", system_item=f"{appcode}-{biz_code}") or {}
    if not data:
        return ""
    return (data.get("value") or "").rstrip("/")
