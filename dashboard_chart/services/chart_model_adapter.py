#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/7/17 14:52
# <AUTHOR> caoxl
# @File     : chart_model_adapter.py
from base.enums import FieldSortFieldSource, DatasetFieldType, DatasetVarValueSource, ColTypes
from dashboard_chart.models import ChartDataModel, ChartDimModel, ChartPaginationModel
from dataset import external_query_service


class ChartModelAdapter:
    """
    ChartModel适配器
    用于将前端传入的非标准参数适配为后台所需的标准参数
    后续扩展适配方法 方法名均以"adapt_"开头
    """

    def __init__(self, chart_data_model: ChartDataModel):
        self._chart_data_model = chart_data_model

    def __call__(self, *args, **kwargs):
        return self.adapt()

    def adapt(self):
        """
        运行所有适配方法
        所有适配方法均以 "adapt_" 开头
        :return:
        """
        for method in self._get_adapt_methods():
            func = getattr(self, method)
            func()
        return self._chart_data_model

    def adapt_aggregation(self):
        """
        适配聚合
        此前无聚合的组件均将logic_type更改为 "default"
        聚合选项 "aggregation" 更改为 0
        :return:
        """
        if self._chart_data_model.chart_code in ('statistic_table', 'pagination_table_yk', 'pagination_table_yk_warn'):
            self._chart_data_model.data_logic_type_code = 'default'
            self._chart_data_model.aggregation = 0
        elif self._chart_data_model.data_logic_type_code not in ['column']:
            self._chart_data_model.aggregation = 1

    def _convert_original_comparisons2dims(self):
        # comparisons转dim时，rank值要比dim的rank值大。这样当相当于comparison放在了dim后面
        # 同时需要将排序转换为维度排序
        start_rank = 0
        if self._chart_data_model.original_dims:
            start_rank = max(dim.get('rank') for dim in self._chart_data_model.original_dims) + 1
        comparison_field_ids = []
        self._chart_data_model.original_dims = self._chart_data_model.original_dims or []
        for comparison in self._chart_data_model.original_comparisons:
            comparison_field_ids.append(comparison.get("dataset_field_id"))
            dim = ChartDimModel(
                **{
                    "id": "",
                    "dashboard_chart_id": comparison.get("dashboard_chart_id"),
                    "dim": comparison.get("dataset_field_id"),
                    "alias": comparison.get("alias"),
                    "content": comparison.get("content"),
                    "formula_mode": comparison.get("formula_mode"),
                    "rank": start_rank + comparison.get("rank"),
                    "sort": comparison.get("sort"),
                    "dataset_field_id": comparison.get("dataset_field_id"),
                }
            ).get_dict()
            # 此处注释
            # dim['dataset_field_id'] = comparison.get("dataset_field_id")
            self._chart_data_model.original_dims.append(dim)
        self._chart_data_model.original_comparisons = None

    def _convert_real_comparisons2dims(self):
        # comparisons转dim时，rank值要比dim的rank值大。这样当相当于comparison放在了dim后面
        # 同时需要将排序转换为维度排序
        start_rank = 0
        if self._chart_data_model.dims:
            start_rank = max(dim.get('rank') for dim in self._chart_data_model.dims) + 1
        comparison_field_ids = []
        self._chart_data_model.dims = self._chart_data_model.dims or []
        for comparison in self._chart_data_model.comparisons:
            comparison_field_ids.append(comparison.get("dataset_field_id"))
            dim = ChartDimModel(
                **{
                    "id": "",
                    "dashboard_chart_id": comparison.get("dashboard_chart_id"),
                    "dim": comparison.get("dataset_field_id"),
                    "alias": comparison.get("alias"),
                    "content": comparison.get("content"),
                    "formula_mode": comparison.get("formula_mode"),
                    "rank": start_rank + comparison.get("rank"),
                    "sort": comparison.get("sort"),
                    "dataset_field_id": comparison.get("dataset_field_id"),
                }
            ).get_dict()
            # 此处注释
            # dim['dataset_field_id'] = comparison.get("dataset_field_id")
            self._chart_data_model.dims.append(dim)
        self._chart_data_model.comparisons = None
        if not self._chart_data_model.field_sorts:
            return
        # 将排序转换为维度
        for field_sort in self._chart_data_model.field_sorts:
            if (
                field_sort.get("field_source") == FieldSortFieldSource.Comparisons.value
                and field_sort.get("dataset_field_id") in comparison_field_ids
            ):
                field_sort['field_source'] = FieldSortFieldSource.Dims.value

    def _convert_comparisons2dims(self):
        # comparisons转dim时，rank值要比dim的rank值大。这样当相当于comparison放在了dim后面
        # 同时需要将排序转换为维度排序
        self._convert_original_comparisons2dims()
        self._convert_real_comparisons2dims()

    def adapt_no_aggregation_and_comparison(self):
        """
        适配聚合和对比维度
        当非聚合时将对比维度当作维度处理(维度和对比维度不会重复)
        :return:
        """
        if not self._chart_data_model.aggregation and self._chart_data_model.comparisons:
            self._convert_comparisons2dims()

    def adapt_aggregation_and_comparison(self):
        """
        处理对比维度没有数值的情况
        场景1, 若有对比维度 且为聚合 但是值为空 则 对比维度按照普通维度处理
        场景2, 有对比维度，且为聚合，同时原始的普通维度为空，对比维度就按普通维度处理
        :return:
        """
        if (
            self._chart_data_model.aggregation
            and self._chart_data_model.comparisons
            and (not self._chart_data_model.nums or not self._chart_data_model.dims)
        ):
            self._convert_comparisons2dims()

    def _sort_column_display(self):
        self._chart_data_model.column_display = sorted(
            self._chart_data_model.column_display, key=lambda item: item.get("order")
        )

    def adapt_no_aggregation_column_display(self):
        # 明细模式下 且 原始对比维度有值 （若原来顺序有变化则需要将对比维度调至最后，否则放在原位）
        self._sort_column_display()
        if not self._chart_data_model.aggregation and self._chart_data_model.column_display:
            original_dim_and_num_order = []
            new_display_columns = []
            column_display_dim_and_num_order, column_display_map, insert_pos = self._get_column_display_data()
            for dim in self._chart_data_model.dims:
                key = tuple([ColTypes.Dim.value, dim.get("dim")])
                if key in column_display_map:
                    original_dim_and_num_order.append(tuple([ColTypes.Dim.value, dim.get("dim")]))
                else:
                    new_display_columns.append(
                        {
                            "alias_name": dim.get("alias"),
                            "col_name": self._chart_data_model.dataset_field_dict.get(dim.get("dim"), {}).get(
                                "col_name"
                            ),
                            "col_type": ColTypes.Dim.value,
                            "dataset_field_id": dim.get("dim"),
                            "dataset_id": dim.get("dataset_id"),
                            "is_show": 1,
                            "group": None,
                            "order": 0,
                        }
                    )
            if not new_display_columns:
                return
            original_dim_and_num_order = original_dim_and_num_order + [
                tuple([ColTypes.Num.value, num.get("num")]) for num in self._chart_data_model.nums
            ]
            # 若顺序发生变化则放在最后 否则放到维度后面
            # 若顺序未发生变化则需要判断维度是否有分组，若有分组则需要放置在该分组后面，反之放到该维度后面
            insert_index = None
            if tuple(original_dim_and_num_order) != tuple(column_display_dim_and_num_order):
                insert_index = len(self._chart_data_model.column_display)
            else:
                insert_index = insert_pos
            self._chart_data_model.column_display = [
                *self._chart_data_model.column_display[0:insert_index],
                *new_display_columns,
                *self._chart_data_model.column_display[insert_index:],
            ]
            # 重新为order赋值
            self._reset_column_display_order()

    def _reset_column_display_order(self):
        for idx, item in enumerate(self._chart_data_model.column_display):
            item["order"] = idx

    def _get_column_display_data(self):
        column_display_map = {}
        column_display_dim_and_num_order = []
        last_dim_index = None
        last_dim_group = None
        idx = len(self._chart_data_model.column_display) - 1
        op_last_dim = False
        while idx >= 0:
            column = self._chart_data_model.column_display[idx]
            if column.get("col_type") == ColTypes.Comparison.value:
                column["col_type"] = ColTypes.Dim.value
            tuple_key = tuple([column.get("col_type"), column.get("dataset_field_id")])
            column_display_dim_and_num_order.insert(0, tuple_key)
            column_display_map[tuple_key] = column
            if last_dim_index is None and column.get("col_type") == ColTypes.Dim.value:
                last_dim_index = idx
                last_dim_group = None if not column.get("group") else column.get("group").get("name")
            # 如果最后一个维度有分组 则需要往前面找 直到下一个字段和当前分组不一致为止
            if not op_last_dim and last_dim_index is not None and last_dim_group is not None:
                op_last_dim = True
                last_dim_index = self._op_last_dim_with_group(idx, last_dim_index, last_dim_group)
            idx -= 1
        if last_dim_index is None:
            last_dim_index = len(self._chart_data_model.column_display) - 1
        return column_display_dim_and_num_order, column_display_map, last_dim_index + 1

    def _op_last_dim_with_group(self, idx, last_dim_index, last_dim_group):
        for count, item in enumerate(self._chart_data_model.column_display[idx + 1 :]):
            current_group = None if not item.get("group") else item.get("group").get("name")
            if current_group == last_dim_group:
                last_dim_index += count + 1
            else:
                break
        return last_dim_index

    def _get_adapt_methods(self):
        return filter(lambda item: item.startswith("adapt_") and callable(getattr(self, item)), dir(self))

    def adapt_pagination(self):
        """
        适配 按列返回
        按列返回没有传page_size时自行获取
        (此为零时方案，最终方案为前端传值)
        :return:
        """
        if (
            self._chart_data_model.data_logic_type_code in ('column')
            and self._chart_data_model.pagination.page_size
            and not self._chart_data_model.pagination.page
        ):
            self._chart_data_model.pagination.page = 1

    def adapt_column_display(self):
        """
        适配列隐藏，当所有列都隐藏时，第一列需要显示
        :return:
        """
        if self._chart_data_model.column_display:
            unshow_count = len([item for item in self._chart_data_model.column_display if not item.get("is_show")])
            field_count = len(self._chart_data_model.dims) + len(self._chart_data_model.nums)
            if field_count <= unshow_count:
                self._chart_data_model.column_display[0]['is_show'] = 1

    def _get_item_query_vars(self, field_ids: list):
        """
        获取字段依赖引用的变量
        此处变量都取默认值
        :param field_ids:
        :return:
        """
        query_vars = []
        dataset_include_vars = external_query_service.batch_get_dataset_include_vars(
            [self._chart_data_model.dataset_id]
        )

        var_id_list = [i.get("var_id") for i in dataset_include_vars] if dataset_include_vars else []
        var_external_info = external_query_service.batch_get_dataset_var_info(
            var_id_list, self._chart_data_model.dashboard_id
        )

        for include_var in dataset_include_vars:
            if include_var.get("field_id") in field_ids:
                var_external_info = var_external_info.get(include_var.get("id"), {})
                include_var["value_source"] = (
                    var_external_info.get("value_source")
                    if var_external_info
                    else DatasetVarValueSource.Userdefined.value
                )
                if include_var.get("value") is None:
                    include_var["value"] = include_var.get("default_value")
                query_vars.append(include_var)
        return query_vars

    def _get_filter_field_id_list(self):
        """
        获取穿透条件中的筛选字段id
        :return:
        """
        filter_field_id_list = []
        for opetrate_condition in self._chart_data_model.penetrate_conditions:
            dim = opetrate_condition.get("dim", {})
            dataset_field_id = dim.get("dataset_field_id") or dim.get("dim") if dim else ""
            if dataset_field_id:
                filter_field_id_list.append(dataset_field_id)
        return filter_field_id_list

    def _get_advanced_field_id_list(self, filter_field_id_list):
        """
        获取需要收集变量的高级字段id
        :param filter_field_id_list:
        :return:
        """
        advanced_field_id_list = []
        for opetrate_field_id in filter_field_id_list:
            field_info = self._chart_data_model.dataset_field_dict.get(opetrate_field_id, {})
            if field_info.get("type") in [
                DatasetFieldType.Calculate.value,
                DatasetFieldType.Customer.value,
                DatasetFieldType.Indicator.value,
                DatasetFieldType.CalculateIndicator.value,
            ]:
                advanced_field_id_list.append(opetrate_field_id)
        return advanced_field_id_list

    def adapt_assign_query_vars_for_penetrate_filter(self):
        """
        同数据集的穿透筛选，首次取数如果关联的字段是引用变量的高级字段，会缺少query_vars，需要兼容此场景
        :return:
        """
        if self._chart_data_model.penetrate_conditions:
            filter_field_id_list = self._get_filter_field_id_list()
            advanced_filed_id_list = self._get_advanced_field_id_list(filter_field_id_list)
            external_query_vars = self._get_item_query_vars(advanced_filed_id_list)
            external_query_var_ids = [i.get("var_id") for i in external_query_vars] if external_query_vars else []
            current_query_var_ids = (
                [i.get("var_id") for i in self._chart_data_model.query_vars]
                if self._chart_data_model.query_vars
                else []
            )
            add_query_var_ids = set(external_query_var_ids) - set(current_query_var_ids)
            add_query_vars = (
                list(filter(lambda i: i.get("var_id") in add_query_var_ids, external_query_vars))
                if add_query_var_ids
                else []
            )
            self._chart_data_model.query_vars = (
                self._chart_data_model.query_vars + add_query_vars
                if self._chart_data_model.query_vars
                else add_query_vars
            )

    def adapt_sort_dims_nums_comparisons(self):
        """
        统一对dims nums comparions 排序，后面无需再排序，避免重复排序
        :return:
        """
        if self._chart_data_model.dims:
            self._chart_data_model.dims = sorted(self._chart_data_model.dims, key=lambda x: x.get("rank", 0) or 0)
        if self._chart_data_model.nums:
            self._chart_data_model.nums = sorted(self._chart_data_model.nums, key=lambda x: x.get("rank", 0) or 0)
        if self._chart_data_model.comparisons:
            self._chart_data_model.comparisons = sorted(
                self._chart_data_model.comparisons, key=lambda x: x.get("rank", 0)
            )

    def adapt_no_aggregation_new_order(self):
        """
        适配明细模式下排序
        此时若有对比维度，前端不会转换为维度，仍然传送对比维度，后台匹配，此时需要将对比维度转换为维度
        :return:
        """
        if not self._chart_data_model.aggregation and self._chart_data_model.new_order:
            for order in self._chart_data_model.new_order:
                if order.get("field_source") == FieldSortFieldSource.Comparisons.value:
                    order["field_source"] = FieldSortFieldSource.Dims.value

    def adapt_y1_aggregation_new_order(self):
        """
        适配聚合模式下排序(无数值)
        聚合且有对比维度但无数值，前端不会转换为维度，仍然传送对比维度，后台匹配，此时需要将对比维度转换为维度
        :return:
        """
        if self._chart_data_model.aggregation and self._chart_data_model.new_order and not self._chart_data_model.nums:
            for order in self._chart_data_model.new_order:
                if order.get("field_source") == FieldSortFieldSource.Comparisons.value:
                    order["field_source"] = FieldSortFieldSource.Dims.value

    def adapt_y2_aggregation_new_order(self):
        """
        # 如果新排序前端有传 则须将检查横向排序序列(对比维度)，若数据库中有但是新排序序列中没有，则需要将数据库中横向序列加入
        :return:
        """
        if self._chart_data_model.comparisons and self._chart_data_model.aggregation and self._chart_data_model.nums:
            db_comparison_sorts = {
                field_sort["dataset_field_id"]: field_sort
                for field_sort in self._chart_data_model.field_sorts
                if field_sort.get("field_source") == FieldSortFieldSource.Comparisons.value
            }
            new_order_comparison_sorts = {
                order["dataset_field_id"]: order
                for order in self._chart_data_model.new_order
                if order.get("field_source") == FieldSortFieldSource.Comparisons.value
            }
            for field_id, sort in db_comparison_sorts.items():
                if field_id in new_order_comparison_sorts:
                    continue
                self._chart_data_model.new_order.append(sort)

    def adapt_pre_comparision(self):
        # 自助分析支持对比维度后置功能
        # if (self._chart_data_model.chart_code == 'analysis_table' and
        #         self._chart_data_model.comparisons and
        #         len(self._chart_data_model.comparisons) > 0 and
        #         min([item.get("rank", 0) for item in self._chart_data_model.comparisons]) != 0
        # ):
        #     self._chart_data_model.pre_comparison = 0
        pass


def adapt_chart_data_model(model: ChartDataModel):
    chart_model_adapter = ChartModelAdapter(model)
    return chart_model_adapter()


def adapt_get_data_model(model: ChartDataModel):
    model.chart_code = '$323122wqrwr23)_{:KYU'  # 占位
    model.data_logic_type_code = 'default'
    model.aggregation = 1
    if model.pagination.page is None:
        # 前端没传递分页参数，默认取全部，传了取前端传的分页参数
        model.pagination = ChartPaginationModel(**{'page': 1, 'page_size': 1000000000})  # 取出全部
