#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    dmp
    <NAME_EMAIL> on 2018/03/27.
"""
import json
import os

from dashboard_chart.repositories import component_repository
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_component.services import component_service
from dashboard_chart.models import ComponentMenuModel, ComponentModel
from dmplib.redis import conn_custom_prefix as conn_redis
from components.redis_utils import RCache
from dashboard_chart.utils import dashboard_cache
from dmplib.hug import g


from typing import Any, Dict, List, Tuple


def get_component(package):
    """
    获取单个组件
    :param package:
    :return:
    """
    if not package:
        raise UserError(message='请指定组件！')
    return component_repository.get_component(package)


def get_menu(menu_id):
    """
    获取菜单
    :param menu_id:
    :return:
    """
    return component_repository.get_menu(menu_id)


def get_data_logic_type(code):
    """
    获取数据逻辑类型
    :param code:
    :return:
    """
    return component_repository.get_data_logic_type(code)


def validate_package_version(new_version, origin_version):
    """
    验证包版本号
    :param new_version:
    :param origin_version:
    :return:
    """
    new_version_arr = new_version.split('.')
    origin_version_arr = origin_version.split('.')
    origin_version_max_index = len(origin_version) - 1
    for index, item in enumerate(new_version_arr):
        if index > origin_version_max_index:
            break
        if index == origin_version_max_index:
            if float(item) <= float(origin_version_arr[index]):
                return False, '包版本号必须大于最后一次修改的版本号！'
        else:
            if float(item) < float(origin_version_arr[index]):
                return False, '包版本号必须大于最后一次修改的版本号！'
    return True, ''


def _build_menu_tree(level, parent_id, menus, components_with_levelcode):
    tree = []
    for menu in menus:
        if menu.get('level_code').count('-') != level:
            continue
        if menu.get('parent_id') == parent_id:
            _current_id = menu.get('id')
            _current_menu_level = menu.get('level_code')
            menu['icon'] = config.get('Component.component_menuicon_endpoint') + menu.get('icon')
            _menu_model = ComponentMenuModel(**menu)
            _menu_model.components = []
            if _current_menu_level in components_with_levelcode:
                for each in components_with_levelcode[_current_menu_level].values():
                    _menu_model.components.append(ComponentModel(**each))
            _menu_model.children = _build_menu_tree(
                level=level + 1, parent_id=_current_id, menus=menus, components_with_levelcode=components_with_levelcode
            )
            _menu_model.children.reverse()
            tree.append(_menu_model)
    return tree


def _assign_component_remote_data(distribute_components, components_with_levelcode):
    for distribute_component in distribute_components:
        menu_level_code = distribute_component.get('menu_level_code')
        package = distribute_component.get('package')
        distribute_component['endpoint'] = config.get('Component.component_endpoint')
        if menu_level_code in components_with_levelcode:
            distribute_component['next_version'] = distribute_component['version']
            if package in components_with_levelcode[menu_level_code]:
                _component = components_with_levelcode.get(menu_level_code).get(package)
                distribute_component['md5version'] = _component.get('md5version')
                distribute_component['md5RunTimeversion'] = _component.get('md5RunTimeversion')
                distribute_component['version'] = _component.get('version')
            else:
                distribute_component['md5version'] = distribute_component.get('md5version')
                distribute_component['md5RunTimeversion'] = distribute_component.get('md5RunTimeversion')
                distribute_component['version'] = distribute_component.get('version')
        else:
            components_with_levelcode[menu_level_code] = {}
            distribute_component['next_version'] = distribute_component['version']
        components_with_levelcode[menu_level_code][package] = distribute_component
    return components_with_levelcode


def get_components_with_menu():
    """
    获取组件和相应菜单
    :return:
    """
    # 开启自动升级则进行升级
    msg = auto_upgrade_components()
    components = component_repository.get_components()
    req_result, distribute_components = component_service.get_distributions()
    components_with_levelcode = {}
    for component in components:
        component['operation'] = None
        component['endpoint'] = config.get('Component.component_endpoint')
        menu_level_code = component.get('menu_level_code')
        package = component.get('package')
        if menu_level_code not in components_with_levelcode:
            components_with_levelcode[menu_level_code] = {}
        component['next_version'] = component['version']
        components_with_levelcode[menu_level_code][package] = component
    if req_result:
        components_with_levelcode = _assign_component_remote_data(distribute_components, components_with_levelcode)
    else:
        msg += '\r\n' + distribute_components
    menus = component_repository.get_all_menus()
    result = _build_menu_tree(level=1, parent_id='', menus=menus, components_with_levelcode=components_with_levelcode)
    result.reverse()
    return msg, result


def deal_with_component_refresh_flag(refresh_flag: bool) -> bool:
    """
    处理组件刷新标志位
    :param bool refresh_flag: True：已刷新 False：未刷新
    :return:
    """
    refresh_flag_key = dashboard_cache.get_components_refresh_flag_cache_key()
    # redis_cache = conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE'))
    redis_cache = RCache(os.environ.get('CONFIG_AGENT_CLIENT_CODE'))
    cache_orig_refresh_flag = redis_cache.get(refresh_flag_key)

    # 有组件更新行为时才需要操作缓存
    if not refresh_flag:
        return bool(cache_orig_refresh_flag)

    # 如果已经存在这个组件更新的缓存key，则清空所有报告旧的缓存key
    # keys = redis_cache._connection.keys(pattern="*" + refresh_flag_key + "*")
    # if keys:
    #     new_keys = [key.decode() for key in keys]
    #     redis_cache._connection.delete(*new_keys)
    redis_cache.del_by_scan(pattern="*" + refresh_flag_key + "*")
    redis_cache.set_data(refresh_flag_key, 1 if refresh_flag else 0)
    return refresh_flag


def auto_upgrade_components() -> str:
    """
    自动升级组件 test
    :return:
    """
    # 如果用户未登录，直接返回
    if not hasattr(g, 'code') or not g.code:
        return '用户未登录'
    msg = ''
    errors = list()
    # 加锁防止同个环境下多个租户重复升级组件
    identity = component_service.acquire_component_lock('upgrade')
    if not identity:
        return msg
    # 开启自动初始化
    if int(config.get('Component.init_components')) == 1:
        result, error = component_service.init_components()
        if not result:
            errors.append(error)
    # 开启升级
    if int(config.get('Component.auto_upgrade')) == 1:
        upgrade_errors = component_service.auto_upgrade_components()
        if len(upgrade_errors) > 0:
            errors.extend(upgrade_errors)
    component_service.release_component_lock('upgrade', identity)
    if errors:
        msg = '\n'.join(errors)

    return msg


def get_installed_components():
    """
    获取已安装组件
    :return:
    """
    # 自动升级组件
    msg = auto_upgrade_components()
    cache_key = dashboard_cache.get_installed_components_cache_key()
    redis_cache = conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE'))
    components = redis_cache.get_data(cache_key)
    if components:
        return msg, components
    components = component_repository.get_components()
    if components:
        for component in components:
            component['endpoint'] = config.get('Component.component_endpoint')
            component['operation'] = None
            component['next_version'] = component['version']
        redis_cache.set_data(cache_key, components)
    return msg, components


def get_editor_components(op_runterminal) -> Tuple[str, List[Dict[str, Any]]]:
    """
    获取编辑器组件列表(无分类)
    :return:
    """
    # 开启自动升级则进行升级
    msg = auto_upgrade_components()
    components = component_repository.get_components()
    # 只返回特定runterminal类型的组件
    components = get_terminal_components(op_runterminal, components)
    req_result, distribute_components = component_service.get_distributions()
    components_with_package = {}
    for component in components:
        component["operation"] = None
        component["endpoint"] = config.get("Component.component_endpoint")
        component["layout"] = component.get("layout").split(",")
        component["layout_preview_image"] = (
            json.loads(component.get("layout_preview_image")) if component.get("layout_preview_image") else None
        )
        component["next_version"] = component["version"]
        components_with_package[component.get("package")] = component
    if req_result:
        for distribute_component in distribute_components:
            distribute_component["endpoint"] = config.get("Component.component_endpoint")
            distribute_component["layout"] = distribute_component.get("layout").split(",")
            distribute_component["next_version"] = distribute_component.get("version")
            distribute_component["layout_preview_image"] = (
                json.loads(distribute_component.get("layout_preview_image"))
                if distribute_component.get("layout_preview_image")
                else None
            )
            package = distribute_component.get("package")
            if package in components_with_package:
                _component = components_with_package.get(package)
                distribute_component["md5version"] = _component.get("md5version")
                distribute_component["md5RunTimeversion"] = _component.get("md5RunTimeversion")
                distribute_component["version"] = _component.get("version")
            components_with_package[package] = distribute_component
    else:
        msg += "\r\n" + str(distribute_components)
    return msg, list(components_with_package.values())


def check_not_include_terminal(runterminal: List[str], not_include_terminals: List[Any]) -> bool:
    """
    校验是否有不包含的终端类型组件
    :param runterminal:
    :param not_include_terminals:
    :return:
    """
    valid_flag = True
    if not runterminal:
        return valid_flag
    for item in not_include_terminals:
        if item in runterminal and len(runterminal) == 1:
            valid_flag = False
            break
    return valid_flag


def check_include_terminal(runterminal: List[str], include_terminals: List[Any]) -> bool:
    """
    校验是否有包含的终端类型组件
    :param runterminal:
    :param include_terminals:
    :return:
    """
    valid_flag = False
    if not runterminal:
        return valid_flag
    for item in include_terminals:
        if item in runterminal:
            valid_flag = True
            break
    return valid_flag


def get_terminal_components(op_runterminal: str, components: List[Any]) -> List[Any]:
    """
    获取特定终端类型的组件列表
    :param op_runterminal:
    :param components:
    :return:
    """
    result = []
    not_include_terminals = []
    include_terminals = []
    if not components:
        return result
    if not op_runterminal:
        not_include_terminals.append("mobile_screen")
    else:
        include_terminals.append(op_runterminal)
    for component in components:
        valid_flag = True
        runterminal = json.loads(component.get("runTerminal")) if component.get("runTerminal") else []
        if not_include_terminals:
            valid_flag = check_not_include_terminal(runterminal, not_include_terminals)
        elif include_terminals:
            valid_flag = check_include_terminal(runterminal, include_terminals)
        if valid_flag:
            result.append(component)
    return result
