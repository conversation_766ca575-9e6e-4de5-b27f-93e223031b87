#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/06/05
# pylint: disable=R1719

"""
    多屏报告模块
"""
# ---------------- 标准模块 ----------------
import logging
from functools import wraps

# ---------------- 业务模块 ----------------
from base import repository
from base.enums import DashboardType, DashboardTypeStatus, DashboardPlatforms
from dashboard_chart.repositories import dashboard_repository
from dashboard_chart.models import DashboardModel, ScreenDashboardModel, ScreenModel
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from rbac.services.data_permissions import data_permission_edit_filter
from dashboard_chart.services import chart_service, dashboard_service, dashboard_extra_service
from user.repositories.user_repository import get_user_project_profile


def _validate_screen_dashboard(dashboard, screen):
    if not dashboard:
        raise UserError(message='单屏(id=%s)不存在' % screen['screen_id'])
    if dashboard['type'] == DashboardType.Folder.value:
        raise UserError(message='不能添加文件夹')
    if dashboard['is_multiple_screen'] == 1:
        raise UserError(message='不能嵌套多屏!')
    if dashboard.get('platform') == DashboardPlatforms.Mobile.value:
        raise UserError(message='多屏不能包含移动报告!')


@data_permission_edit_filter('multiple_screen-edit')
def screen_save(model: ScreenModel):
    """
    :保存多屏
    :param ScreenModel model:
    :return:
    """
    screen_models = {}
    unique_models_check = {}
    rank = 0
    if len(model.screens) > 20:
        raise UserError(message="最多只能使用20个单屏报告")
    for screen in model.screens:
        unique_models_check[screen] = 1
        rank += 1
        _screen = {"screen_id": screen, "type": DashboardTypeStatus.Draft.value}
        screen_models[rank] = ScreenDashboardModel(**_screen)
        screen_models[rank].validate()
        screen_models[rank].id = seq_id()
        screen_models[rank].rank = str(rank)
        dashboard = dashboard_service.get_dashboard_advanced(_screen['screen_id'])
        _validate_screen_dashboard(dashboard, screen)
    logging.info(model)
    model.validate()

    # 判断是否有重复的screen
    if len(unique_models_check) != len(model.screens):
        raise UserError(message="重复选择了相同的单屏报告")

    # (多屏只能包含非移动报告)
    if model.id is not None and model.id != "":
        # 更新
        dashboard = dashboard_service.get_dashboard_advanced(model.id)
        kwargs = {
            'id': model.id,
            'name': model.name,
            'description': model.description,
            'type': DashboardType.File.value,
            'platform': dashboard.get('platform')
            if dashboard.get('platform') != DashboardPlatforms.Mobile.value
            else DashboardPlatforms.PC.value,
            'refresh_rate': model.refresh_rate,
            'auto_play': model.auto_play,
        }
        dashboard_model = DashboardModel(**kwargs)
        if not dashboard:
            raise UserError(message="多屏报告数据不存在")
        if dashboard['is_multiple_screen'] == 0:
            raise UserError(message="单屏不允许在多屏进行编辑")
        dashboard_service.update_dashboard.set_permissions('multiple_screen-edit')
        dashboard_service.update_dashboard(dashboard_model)
    else:
        kwargs = {
            "name": model.name,
            "description": model.description,
            "parent_id": '',
            "is_multiple_screen": 1,
            "type": DashboardType.File.value,
            # 多屏不能用于移动报告 目前设置为PC
            'platform': DashboardPlatforms.PC.value,
            'refresh_rate': model.refresh_rate,
            'auto_play': model.auto_play,
        }
        dashboard_model = DashboardModel(**kwargs)
        model.id = dashboard_service.add_dashboard(dashboard_model)
        # 新增
    # 删除所有和dashboard相关联的单屏报告
    repository.delete_data("screen_dashboard", {"dashboard_id": model.id, "type": DashboardTypeStatus.Draft.value})
    for screen in screen_models:
        screen_models[screen].dashboard_id = model.id
        repository.add_data(
            "screen_dashboard", screen_models[screen].get_dict(["id", "dashboard_id", "screen_id", "type", "rank"])
        )
    return model.id


def get_dashboard_info(dashboard_id):
    """
    获取报告详情，包括报告信息，单图列表
    :param dashboard_id:
    :return:
    """
    dashboard_info = screen_detail(dashboard_id)
    dashboard_info['chart_list'] = chart_service.get_chart_list(dashboard_id, True, True)
    return dashboard_info


def screen_detail(dashboard_id):
    """
    :报告详情数据
    :param string dashboard_id:
    :return:
    """
    result = {}

    dashboard = dashboard_service.get_dashboard_advanced(dashboard_id)
    if not dashboard:
        raise UserError(message="报告数据获取失败")

    result['dashboard'] = dashboard
    result['screens'] = []
    if dashboard['is_multiple_screen'] == 1:
        # 多屏
        screens = repository.get_data(
            "screen_dashboard",
            {"dashboard_id": dashboard_id, "type": DashboardTypeStatus.Draft.value},
            ["screen_id", "rank"],
            True,
            [('rank', 'asc')],
        )

        if len(screens) > 0:
            screens_detail = dashboard_service.get_dashboards([line['screen_id'] for line in screens])
            # 按顺序输出
            for screen in screens:
                if screen['screen_id'] in screens_detail:
                    result['screens'].append(screens_detail[screen['screen_id']])

    else:
        # 单屏
        result['screens'].append(dashboard)
    result['dashboard']['user_groups'] = (
        dashboard_repository.get_dashboard_usergroups(dashboard_id)
        if int(dashboard["type_access_released"]) == 2
        else []
    )
    # 检查已发布报告是否被门户引用标记
    function_info = repository.get_data('`function`', {'url': dashboard_id}, ['id'], multi_row=False)
    result['is_quoted_application'] = True if function_info else False

    # 有无发布后未提交的修改
    extra_info = dashboard_extra_service.get_dashboard_extra_data(dashboard_id)
    if extra_info:
        result.update(extra_info)

    return result


def has_project_dashboard_permission(project_dashboard_type: str, edit_dashboard_type: str) -> bool:
    return True if edit_dashboard_type in project_dashboard_type else False


def get_dashboard_type(platform: str, new_layout_type: str):
    if platform == 'pc':
        return 'large' if new_layout_type == 'free' else 'dashboard'
    return 'large' if new_layout_type == 'free' else 'mobile'


def get_project_profile():
    return get_user_project_profile()


def check_project_edit_permission(metadata):
    try:
        layout = metadata['dashboard']['layout']
    except KeyError:
        raise UserError(code=400, message="元数据数据缺失")
    platform = layout['platform']
    new_layout_type = layout['mode']
    edit_dashboard_type = get_dashboard_type(platform, new_layout_type)
    project_dashboard_type = get_project_profile()['allow_dashboard_type']
    # TODO(Judy): 自助报表不检查权限
    application_type = metadata.get('dashboard', {}).get('application_type', 0)
    if application_type in [1, '1']:
        return
    if not has_project_dashboard_permission(project_dashboard_type, edit_dashboard_type):
        raise UserError(code=403, message="无报告编辑权限, 请联系管理员开通")


def get_dashboard_type_by_id(dashboard_id: str):
    """large mobile belongs to large"""
    data = repository.get_one(
        'dashboard', {'id': dashboard_id}, fields=['platform', 'new_layout_type', 'is_multiple_screen']
    )
    if not data:
        raise UserError(code=404, message="报告不存在")
    is_multiple_screen = data['is_multiple_screen']
    if data['platform'] == 'pc':
        return ('large', is_multiple_screen) if data['new_layout_type'] == 0 else ('dashboard', is_multiple_screen)
    return 'mobile', is_multiple_screen


def verify_project_permission(func):
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        dashboard_id = kwargs.get('id', '')
        edit_dashboard_type, is_multiple_screen = get_dashboard_type_by_id(dashboard_id)
        if not is_multiple_screen:
            project_info = get_project_profile()
            project_dashboard_type = (
                project_info['allow_dashboard_type'] if 'allow_dashboard_type' in project_info else ''
            )
            if not has_project_dashboard_permission(project_dashboard_type, edit_dashboard_type):
                raise UserError(code=403, message="无报告编辑权限, 请联系管理员开通")
        return func(request, *args, **kwargs)

    return wrapper
