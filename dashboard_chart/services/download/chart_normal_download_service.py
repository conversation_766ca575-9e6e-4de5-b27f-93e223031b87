#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/6 11:27
# <AUTHOR> caoxl
# @File     : chart_normal_download_service.py
# pylint:disable=R0201
import functools
import logging
import random
import string
from datetime import datetime
import json
import os
import time
from typing import Callable
import app_celery
from base import repository
from base.enums import FlowType, FlowInstanceStatus, TableDownloadFrom
from celery_app.celery import get_task_id
from dashboard_chart.services.download.chart_download_common_service import ChartDownloadCommon
from dashboard_chart.services.download.download_utils import generate_message_title
from dmplib.hug import g
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from download.models import DownloadFieldModel
from flow.models import FlowModel as CustomerFlowModel, FlowInstanceModel
from flow.services import flow_service, flow_instance_service
from message.models import MessageModel
from message.services import message_service
from dashboard_chart.models import ChartDownloadModel, ChartPaginationModel
from dmplib.hug.context import DBContext
from components.oss import OSSFileProxy
from dashboard_chart.models import ChartDataModel as NewChartDataModel
from dashboard_chart.services import chart_service, dashboard_service, released_dashboard_service
from dashboard_chart.services.download import download_utils
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from queue import Empty, Queue
from threading import Thread

import tablib
import csv

logger = logging.getLogger(__name__)


def generate_download_task(model):
    """
    生成下载任务
    :param model: ChartDataModel实例
    :return:
    """

    # flow and instance
    flow_id = seq_id()
    flow = CustomerFlowModel(id=flow_id, name='表格数据下载', type=FlowType.Download.value)
    flow_service.add_flow(flow)
    flow_instance = FlowInstanceModel(flow_id=flow.id, name=flow.name, type=flow.type)
    flow_instance_id = flow_instance_service.add_instance(flow_instance)
    repository.update_data('instance', {'status': FlowInstanceStatus.Created.value}, {'id': flow_instance_id})
    repository.update_data('flow', {'run_status': FlowInstanceStatus.Created.value}, {'id': flow_instance.flow_id})

    # dashboard name
    dashboard_data = repository.get_data('dashboard', {'id': model.dashboard_id}, ['name'])
    if not dashboard_data:
        raise UserError(message="查询不到对应报告数据，报告id：{id}".format(id=model.dashboard_id))
    dashboard_name = dashboard_data.get('name', '报告')
    # chart name
    chart_data = repository.get_data('dashboard_chart', {'id': model.id, 'dashboard_id': model.dashboard_id}, ['name'])
    if not chart_data:
        logger.error(msg="查询不到对应单图数据，单图id：{id}".format(id=model.id), exc_info=True)
        return '', {}
    chart_name = chart_data.get('name', '图表')

    filename = f'{dashboard_name}_{chart_name}'
    filename = download_utils.get_valid_filename(filename)

    # message
    _message = {
        'source_id': filename + '_' + datetime.now().strftime("%Y%m%d%H%M%S"),
        'user_id': g.userid,
        'source': '通知',
        'type': '个人消息',
        'title': download_utils.generate_message_title(
            msg_type='generate', filename=filename, download_from=model.download_from
        ),
        'url': '/flow/ops',
    }
    message_service.message_add(MessageModel(**_message))

    # 外部user_id存入下载记录表中，用于返回给云客数据
    external_user_id = None
    if hasattr(g, 'external_params') and g.external_params:
        external_user_id = g.external_params.get('user_id')

    # download task
    download_id = seq_id()
    download_model = ChartDownloadModel(
        download_id=download_id,
        dashboard_id=model.dashboard_id,
        dashboard_chart_id=model.id,
        flow_id=flow_id,
        download_url='',
        external_user_id=external_user_id,
    )
    data = download_model.get_dict(
        ['download_id', 'dashboard_id', 'dashboard_chart_id', 'flow_id', 'download_url', 'external_user_id']
    )
    repository.add_data('dashboard_chart_download_task', data)

    # celery task
    task_id = get_task_id('dashboard_chart_data', 'chart_download', g.code, download_id)
    repository.update_data('dashboard_chart_download_task', {'status': 1}, {'download_id': download_id})
    dumped_chart_params = json.dumps(model.get_dict())
    app_celery.async_download_chart_data.delay(
        task_id=task_id,
        flow_instance_id=flow_instance_id,
        download_id=download_id,
        filename=filename,
        dumped_chart_params=dumped_chart_params,
        code=g.code,
        account=g.account,
        cookie=g.cookie,
        userid=g.userid,
        external_params=g.external_params,
        snap_id=getattr(g, 'snap_id', None)
    )

    result = dict()
    result['download_id'] = download_model.download_id
    return '', result


def handle_g(func: Callable) -> Callable:
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        parent_thread_g = args[-1]
        thread_local_g = _AppCtxGlobals()
        thread_local_g.code = parent_thread_g.code
        thread_local_g.account = getattr(parent_thread_g, 'account', None)
        thread_local_g.userid = getattr(parent_thread_g, 'userid', None)
        thread_local_g.cookie = getattr(parent_thread_g, 'cookie', None)
        thread_local_g.external_params = getattr(parent_thread_g, 'external_params', None)
        thread_local_g.snap_id = getattr(parent_thread_g, 'snap_id', None)
        thread_local_g.dataset_version_id = getattr(parent_thread_g, 'dataset_version_id', None)
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()

    return wrapper


class DownloadChart(ChartDownloadCommon):
    def __init__(self):
        self.download_file_list = []
        self.queue_size = 10
        self.consumer_size = 5
        self.done = False
        self.file_prefix = str(int(round(time.time() * 1000)) + random.randint(0, 1000))

    def _get_formatted_field(self, field):
        """
        获取格式化之后的field数据
        :param field:
        :return:DownloadFieldModel.get_dict()
        """
        return DownloadFieldModel(
            **{
                "dataset_id": field.dataset_id,
                "dataset_field_id": field.dataset_field_id,
                "col_name": field.col_name,
                "detail_col_name": field.detail_col_name,
            }
        ).get_dict()

    @handle_g
    # pylint: disable=unused-argument
    def producer(self, queue, download_model, model, parent_thread_g=None):
        """
        多线程下载分页表格数据生产者
        :param queue.Queue queue: 队列类实例
        :param dashboard.models.ChartDownloadModel download_model: ChartDownloadModel类实例
        :param dashboard_chart.models.ChartDataModel model: ChartDataModel类实例
        :return:
        """
        page_num = 1
        while True:
            # noinspection PyBroadException
            try:
                model.download_flag = True
                model.pagination = ChartPaginationModel(
                    **{"page": page_num, "page_size": download_model.limit_per_file}
                )
                query_result = {}
                if model.download_from == TableDownloadFrom.Preview.value:
                    query_result = chart_service.get_chart_result(model)
                elif model.download_from == TableDownloadFrom.Released.value:
                    query_result = released_dashboard_service.get_released_chart_result(model)
                query_data = query_result.get("data", [])
                if not len(query_data):
                    self.done = True
                    break

                # 将数据放入队列
                queue.put({"data": query_data, "index": page_num})

                page_num += 1

                # 清理数据
                del query_data, query_result
            except Exception:
                self.done = True
                logger.error(msg="producer occur a error", exc_info=True)
                return

    def _op_consumer_write(self, output_file_type, query_data, show_header_list, match_header_list, real_file_path):
        if output_file_type == 'csv':
            # 获取到的数据写入文件
            write_status = self.write_to_csv(query_data, show_header_list, match_header_list, real_file_path)
            if write_status:
                self.download_file_list.append(real_file_path)
        elif output_file_type == 'xls':
            write_status = self.write_to_excel(query_data, show_header_list, match_header_list, real_file_path)
            if write_status:
                self.download_file_list.append(real_file_path)

    @handle_g
    # pylint: disable=unused-argument
    def consumer(self, queue, filename, download_model, model, parent_thread_g=None):
        """
        多线程下载分页表格数据消费者
        :param model:
        :param queue.Queue queue: 队列类实例
        :param str filename: 文件名称
        :param dashboard.models.ChartDownloadModel download_model: ChartDownloadModel类实例
        :return:
        """
        while not self.done:
            # noinspection PyBroadException
            try:
                data = queue.get(timeout=0.2)
                query_data = data.get("data")
                page_num = data.get("index")
                if not query_data:
                    self.download_file_list = []
                    queue.task_done()
                    continue
                # 获取表格表头
                data_header_dict = query_data[0]

                # 需要导出的文件类型
                output_file_type = download_model.output_file_type
                sub_filename = self.file_prefix + filename + "_" + str(page_num) + "." + output_file_type
                real_file_path = os.path.join(download_model.file_temp_dir, sub_filename)
                show_header_list, match_header_list = self.get_sorted_header_for_excel(
                    model, list(data_header_dict.keys())
                )
                self._op_consumer_write(
                    output_file_type, query_data, show_header_list, match_header_list, real_file_path
                )
                queue.task_done()
                # 清理变量
                del query_data, show_header_list, match_header_list, real_file_path
            except Empty:
                pass
            except Exception:
                logger.error(msg="分页表格组装成csv文件错误", exc_info=True)
                return

    @staticmethod
    def write_to_csv(data, show_headers, match_headers, file_path):
        """
        数据写入csv
        :param data:
        :param show_headers:
        :param match_headers:
        :param file_path:
        :return:
        """
        with open(file_path, "w", encoding="utf-8-sig") as f:
            spam_writer = csv.writer(f)
            spam_writer.writerow(show_headers)
            for row in data:
                data_list = []
                if not row:
                    continue
                data_list.append([row.get(n, "") for n in match_headers])
                spam_writer.writerow(data_list)
                del data_list
            del spam_writer
        return True

    @staticmethod
    def write_to_excel(data, show_headers, match_headers, file_path):
        """
        数据写入excel
        :param data:
        :param show_headers:
        :param match_headers:
        :param file_path:
        :return:
        """
        data_list = list()
        if not show_headers or not match_headers:
            return False
        for row in data:
            if not row:
                continue
            data_list.append([row.get(n, "") for n in match_headers])
            del row
        excel_data = tablib.Dataset(*data_list, headers=show_headers)
        with open(file_path, "wb") as f:
            f.write(excel_data.xls)
        del excel_data, data_list
        return True

    def run_thread_task(self, download_model, model, filename):
        """
        运行多线程任务
        :param dashboard.models.ChartDownloadModel download_model: ChartDownloadModel类实例
        :param dashboard.models.ChartDataModel model:
        :param str filename: 文件名称
        :return:
        """

        # 开启多线程中的队列
        queue = Queue(10)

        # 开启生产者线程
        producer = Thread(target=self.producer, args=(queue, download_model, model, _app_ctx_stack.top))
        producer.start()

        # 开启生产者线程
        consumers = []
        for _ in range(self.consumer_size):
            consumer = Thread(target=self.consumer, args=(queue, filename, download_model, model, _app_ctx_stack.top))
            consumer.start()
            consumers.append(consumer)

        producer.join()

        for consumer in consumers:
            consumer.join()

    def exec_async_download_chart_data(
        self, task_id, flow_instance_id, download_id, filename, dumped_chart_params, user_id
    ):
        """
        异步执行表格数据下载
        :param str task_id: taskid
        :param str flow_instance_id: flowid
        :param str download_id: 下载id
        :param str filename: 文件名称
        :param dumped_chart_params:
        :param str user_id: 用户id 目前用于个人日志记录
        :return:
        """
        download_model = ChartDownloadModel()
        if not os.path.exists(download_model.file_temp_dir):
            os.mkdir(download_model.file_temp_dir)
        # 下载任务信息
        download_data = repository.get_data('dashboard_chart_download_task', {'download_id': download_id})
        flow_id = download_data.get('flow_id') if download_data else None
        model = NewChartDataModel(**(json.loads(dumped_chart_params)))

        # 更新flow表状态
        self._update_flow(flow_id, FlowInstanceStatus.Running.value, startup_time=self.get_now_time())

        # 更新instance表状态
        self._update_instance(flow_instance_id, FlowInstanceStatus.Running.value, startup_time=self.get_now_time())

        # 开始多线程运行数据组装任务
        self.run_thread_task(download_model, model, filename)

        # 文件打包
        export_filename, export_file_path = self._file_to_zip(download_model, filename)

        # 上传oss
        result_data = self._upload_file_to_oss(download_model, export_filename, export_file_path)

        # 更新flow表状态
        self._update_flow(flow_id, FlowInstanceStatus.Successful.value, end_time=self.get_now_time())

        # 更新instance表状态
        self._update_instance(flow_instance_id, FlowInstanceStatus.Successful.value, end_time=self.get_now_time())

        # 添加message数据
        self._send_message(filename, result_data.get("oss_file_url"), user_id, model.download_from)

        # 更新任务状态
        repository.update_data(
            "dashboard_chart_download_task",
            {"download_url": result_data["oss_file_url"], "status": 2},
            {"download_id": download_id},
        )

        # 上传oss成功，删除临时文件
        self._delete_temp_files(export_filename, download_model.file_temp_dir)

        # 记录日志
        logger.info("async_download_chart_data task finished. task_id:%s", task_id)

    def _file_to_zip(self, download_model, filename):
        """
        文件打包
        :param download_model:
        :param str filename: 文件名称
        :return:
        """
        export_filename = filename + ".zip"
        export_file_path = os.path.join(download_model.file_temp_dir, export_filename)
        z = dashboard_service.ZipFile(export_file_path, mode="w", basedir=download_model.file_temp_dir)
        # logger.error("_file_to_zip file_list: %s", str(len(self.download_file_list)))
        z.addfiles(self.download_file_list)
        z.close()
        return export_filename, export_file_path

    @staticmethod
    def _upload_file_to_oss(download_model, export_filename, export_file_path):
        """
        上传文件
        :param download_model:
        :param export_filename:
        :param export_file_path:
        :return:
        """
        result_data = dict()
        random_number = ''.join(random.sample(string.digits, 4))
        upload_filename = os.path.join(download_model.oss_upload_dir, export_filename, random_number)
        result_data["oss_file_url"] = OSSFileProxy().upload(
            open(export_file_path, "rb"), file_name=upload_filename, private=True
        )
        return result_data

    @staticmethod
    def get_now_time():
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    @staticmethod
    def _update_flow(flow_id, run_status, startup_time=None, end_time=None):
        if not flow_id:
            return False
        for_update_data = dict()
        for_update_data['run_status'] = run_status
        if startup_time:
            for_update_data['startup_time'] = startup_time
        if end_time:
            for_update_data['end_time'] = end_time
        repository.update_data("flow", for_update_data, {"id": flow_id})
        return True

    @staticmethod
    def _update_instance(flow_instance_id, status, startup_time=None, end_time=None):
        if not flow_instance_id:
            return False
        for_update_data = dict()
        for_update_data['status'] = status
        if startup_time:
            for_update_data['startup_time'] = startup_time
        if end_time:
            for_update_data['end_time'] = end_time
        repository.update_data("instance", for_update_data, {"id": flow_instance_id})
        return True

    @staticmethod
    def _send_message(filename, oss_file_url, user_id, download_from):
        """
        添加消息表数据
        :param str filename: 文件名称
        :param oss_file_url:
        :param user_id:
        :param download_from:
        :return:
        """
        _message = {
            "source_id": filename + "_" + datetime.now().strftime("%Y%m%d%H%M%S"),
            "source": "导出数据",
            "user_id": user_id,
            "type": "个人消息",
            "title": generate_message_title(msg_type='successful', filename=filename, download_from=download_from),
            "url": "{}".format(oss_file_url),
        }
        message_service.message_add(MessageModel(**_message))

    def _delete_temp_files(self, export_filename, file_temp_dir):
        """
        删除临时文件
        :param export_filename:
        :param file_temp_dir:
        :return:
        """
        self.download_file_list.append(export_filename)
        for f in self.download_file_list:
            if os.path.exists(os.path.join(file_temp_dir, f)):
                os.remove(os.path.join(file_temp_dir, f))
