#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=R1718,R0201

import copy
import functools
from collections import defaultdict

from base.enums import ColTypes
from dashboard_chart.models import ChartDataModel


class ChartResultTransform:
    """
    表格单图取数结果转换为导出excel所需的二维数组（对应二维单元格元素）
    """

    def __init__(self, chart_model: ChartDataModel, chart_result: dict, start_serial_number: int):
        self.chart_model = chart_model
        self.chart_result = chart_result
        self.logic_type = chart_model.data_logic_type_code
        self.data = chart_result.get('data')
        self.subtotal = chart_result.get('subtotal')
        # 表格头部列编号映射
        self.header_mapping = {}
        self.header_length = 0
        self.dim_count = 0
        self.col_summary = []
        self.col_subtotal_insert_mapping = {}
        self.start_serial_number = start_serial_number
        self.hidden_col_number_set = set()
        self.has_display_group = False

    def _get_header_sort_key(self, headers: list) -> tuple:
        header_length = len(headers)
        # 普通头
        if header_length <= 1:
            item = headers[0]
            return tuple([item["col_type"], item["field_id"]])
        # 对比维度头
        return tuple([ColTypes.Comparison.value, None])

    def _sort_data_by_display_order_v2(self, data):
        if not self.chart_model.column_display:
            return data
        display_sort_map = self._get_display_sort_map()

        def _compare_func(x, y):
            x_order_value = display_sort_map.get(self._get_header_sort_key(x["header"]), {}).get("order")
            y_order_value = display_sort_map.get(self._get_header_sort_key(y["header"]), {}).get("order")
            if x_order_value is None or y_order_value is None:
                return 0
            if x_order_value > y_order_value:
                return 1
            if x_order_value < y_order_value:
                return -1
            return 0

        return sorted(data, key=functools.cmp_to_key(_compare_func))

    def _get_display_sort_key(self, column_item: dict) -> tuple:
        if self.chart_model.comparisons and column_item["col_type"] == ColTypes.Num.value:
            return tuple([ColTypes.Comparison.value, None])
        return tuple([column_item["col_type"], column_item["dataset_field_id"]])

    def _get_display_sort_map(self) -> dict:
        attr_name = "_display_sort_map"
        if not hasattr(self, attr_name):
            sort_map = {}
            for column in self.chart_model.column_display:
                sort_key = self._get_display_sort_key(column)
                if sort_key not in sort_map:
                    sort_map[sort_key] = column
            setattr(self, attr_name, sort_map)
        return getattr(self, attr_name)

    def _adapt_display_column(self):
        """
        适配有对比维度的排序
        若数值字段未在一起，则进行适配，将数值字段按照度量的顺序排在一起
        :return:
        """
        # 没有对比维度 不需要处理
        if not self.chart_model.comparisons:
            return
        if not self.chart_model.column_display:
            return
        nums_count = len(self.chart_model.nums)
        column_count = len(self.chart_model.column_display)
        if column_count <= nums_count:
            return
        if nums_count <= 1:
            return
        # 是否需要重新排序
        if not self._need_resort(column_count, nums_count):
            return
        # 重新进行排序
        self.chart_model.column_display = self._resort_column_display()

    def _resort_column_display(self):
        has_sorted = False
        order_value = 0
        new_column_display = []
        for item in self.chart_model.column_display:
            if item.get("col_type") == ColTypes.Num.value:
                if not has_sorted:
                    num_column_display, order_value = self._get_sorted_num_column(order_value)
                    new_column_display.extend(num_column_display)
                    has_sorted = True
                else:
                    continue
            else:
                order_value += 1
                item["order"] = order_value
                new_column_display.append(item)
        return new_column_display

    def _get_sorted_num_column(self, order_start_val: int) -> (list, int):
        num_order_map = {num.get("num"): idx for idx, num in enumerate(self.chart_model.nums)}
        num_column_display = [
            column for column in self.chart_model.column_display if column.get("col_type") == ColTypes.Num.value
        ]
        num_column_display = sorted(
            num_column_display, key=lambda column: num_order_map.get(column.get("dataset_field_id"), 0)
        )
        for idx, item in enumerate(num_column_display):
            order_start_val = order_start_val + idx + 1
            item['order'] = order_start_val
        return num_column_display, order_start_val

    def _need_resort(self, column_count: int, nums_count: int) -> bool:
        resort = True
        for idx, item in enumerate(self.chart_model.column_display):
            idx_end = idx + nums_count - 1
            if idx_end > column_count - 1:
                break
            has_num = False
            col_type_set = set()
            for column in self.chart_model.column_display[idx : idx + nums_count]:
                _col_type = column.get("col_type")
                col_type_set.add(column.get("col_type"))
                if _col_type == ColTypes.Num.value:
                    has_num = True
            if not has_num:
                continue
            if len(col_type_set) == 1:
                resort = False
                break
        return resort

    def process(self):
        header_length = self.get_header_length()
        if not header_length:
            return [], [], [], 0

        self.header_length = header_length
        self.mock_empty_subtotal_dim_nodes = self._mock_empty_sutotal_dim_nodes(self.data)
        # 根据前端传入参数 调整列展示顺序
        self.data = self._sort_data_by_display_order_v2(self.data)
        data_2dim_array = self.transform_data_to_2dim_array(header_length)
        data_count = len(data_2dim_array)
        data_2dim_array = self.insert_subtotal_data_to_2dim_array(data_2dim_array)
        self._pop_cell_by_col_numbers(data_2dim_array)
        headers = data_2dim_array[:header_length]
        records = data_2dim_array[header_length:]
        return headers, records, self.col_summary, data_count

    def _mock_empty_sutotal_dim_nodes(self, data):
        """
        模拟小计空维度节点
        :param data:
        :return:
        """
        empty_dim_nodes = []
        for item in data:
            if len(item["header"]) == 1 and item["header"][0]["col_type"] == ColTypes.Dim.value:
                empty_dim_node = {"header": copy.deepcopy(item["header"]), "col_value": None}
                empty_dim_nodes.append(empty_dim_node)
        empty_dim_nodes = self._sort_data_by_display_order_v2(empty_dim_nodes)
        return empty_dim_nodes

    @staticmethod
    def _get_hidden_config_key(field_id, field_type):
        return str(field_id) + "_" + str(field_type)

    def _support_only_one_column_for_hidden(self):
        """
        当所有列都被设置隐藏时，最左边的那一列数据需要显示
        :return:
        """
        dim_and_num_field_array = []
        if self.chart_model.dims:
            for x in self.chart_model.dims:
                dim_and_num_field_array.append(self._get_hidden_config_key(x.get("dim"), "dim"))
        if self.chart_model.nums:
            for x in self.chart_model.nums:
                dim_and_num_field_array.append(self._get_hidden_config_key(x.get("num"), "num"))

        hidden_field_array = []
        if self.chart_model.column_hidden_config and isinstance(self.chart_model.column_hidden_config, dict):
            for hidden_field, hidden_flag in self.chart_model.column_hidden_config.items():
                if not hidden_flag and hidden_field in dim_and_num_field_array:
                    hidden_field_array.append(hidden_field)
        if len(dim_and_num_field_array) <= len(hidden_field_array):
            self.chart_model.column_hidden_config[dim_and_num_field_array[0]] = True

    def insert_serial_number_to_2dim_array(self, data_2dim_array: list):
        if not self.chart_model.serial_number_config.is_show:
            return data_2dim_array
        inserted_serial_number_array = []
        current_serial_number = self.start_serial_number
        for index, r in enumerate(data_2dim_array):
            if index == 0:
                r.insert(0, self.chart_model.serial_number_config.display_name)
            elif index < self.header_length:
                r.insert(0, None)
            else:
                r.insert(0, current_serial_number)
                current_serial_number += 1
            inserted_serial_number_array.append(r)
        return inserted_serial_number_array

    def _get_display_group_map(self):
        if not hasattr(self, '_display_group_map'):
            _group_map = {}
            for item in self.chart_model.column_display:
                group = item.get("group", {})
                name = None
                if isinstance(group, dict):
                    name = group.get("name")
                    self.has_display_group = True
                _group_map[tuple([item.get("dataset_field_id"), item.get("col_type")])] = name
            setattr(self, '_display_group_map', _group_map)
        return getattr(self, '_display_group_map')

    def get_header_length(self):
        """
        获取表格单图的表格表头的行数
        :return:
        """
        data = self.data
        if not data:
            return 0

        continuous_count = 100
        count_dict = {}
        max_header_length = 0
        display_group_map = self._get_display_group_map()
        for r in data:
            l = len(r['header'])
            display_group = display_group_map.get(
                tuple([r['header'][0].get("field_id"), r['header'][0].get("col_type")])
            )
            if display_group is not None:
                l += 1
            if l not in count_dict:
                count_dict[l] = 1
            else:
                count_dict[l] += 1
            if count_dict[l] > continuous_count:
                break

            if l > max_header_length:
                max_header_length = l

        return max_header_length

    def _get_need_hidden_field_array(self):
        """
        解析前端传入的列隐藏配置信息
        :return:
        """
        need_hidden_field_array = []
        if self.chart_model.column_hidden_config and isinstance(self.chart_model.column_hidden_config, dict):
            need_hidden_field_array = [
                hidden_field
                for hidden_field, flag in self.chart_model.column_hidden_config.items()
                if not flag and hidden_field
            ]
        return need_hidden_field_array

    def _get_need_hidden_field_array_v2(self):
        """
        解析前端传入的列隐藏配置信息
        :return:
        """
        need_hidden_field_array = []
        if self.chart_model.column_display and isinstance(self.chart_model.column_display, list):
            need_hidden_field_array = [
                tuple([column.get("dataset_field_id"), column.get("col_type")])
                for column in self.chart_model.column_display
                if not column.get("is_show")
            ]
        return need_hidden_field_array

    def _assign_hidden_col_number_set(self, h, col_number, need_hidden_field_array):
        match_key = self._get_hidden_config_key(h.get("field_id", ""), h.get("col_type", ""))
        if need_hidden_field_array and h['col_type'] in ['dim', 'num'] and match_key in need_hidden_field_array:
            self.hidden_col_number_set.add(col_number)

    def _assign_hidden_col_number_set_v2(self, h, col_number, need_hidden_field_array):
        match_key = tuple([h.get("field_id", ""), h.get("col_type", "")])
        if need_hidden_field_array and match_key in need_hidden_field_array:
            self.hidden_col_number_set.add(col_number)

    def transform_data_to_2dim_array(self, header_length: int):
        """
        将取数的data转为二维数组
        :param header_length: 表头的行数
        :return:
        """
        data = self.data
        if not data:
            return []
        # 数据是否是按列返回的
        is_column = self.logic_type == 'column'
        result = []
        header_mapping = {}
        dim_count = 0
        need_hidden_field_array = self._get_need_hidden_field_array_v2()
        display_group_map = self._get_display_group_map()
        for col_number, r in enumerate(data):
            row = [self._get_header_value(h) for h in r['header']]
            # 增加分组表头
            if len(r['header']) == 1 and self.has_display_group:
                display_group = display_group_map.get(tuple([r['header'][0]['field_id'], r['header'][0]['col_type']]))
                row.insert(0, display_group)
            # 获取表头字段对应的列编号
            header_alias_names = []
            for h in r['header']:
                header_alias_names.append(h['alias'])
                if h['col_type'] == 'dim':
                    dim_count += 1
                self._assign_hidden_col_number_set_v2(h, col_number, need_hidden_field_array)

            header_key = ('%s' * len(header_alias_names)) % tuple(header_alias_names)
            header_mapping[header_key] = col_number

            for i in range(header_length - len(row)):
                row.append(None)
            row.extend(r['rows'])
            result.append(row)

        self.header_mapping = header_mapping
        self.dim_count = dim_count

        if not is_column:
            return result

        # 进行行列转换
        new_result = []
        for i in range(len(result[0])):
            row = []
            for r in result:
                row.append(r[i])
            new_result.append(row)
        return new_result

    @staticmethod
    def _get_header_value(header_data: dict):
        col_type = header_data['col_type']
        if col_type != 'comparison':
            return header_data['alias'] or header_data['col_name']
        else:
            return header_data['col_value']

    def _pop_summary_cell(self):
        if self.col_summary:
            for summary_array in self.col_summary:
                max_idx = len(summary_array) - 1
                range_array = sorted(self.hidden_col_number_set, reverse=True) if self.hidden_col_number_set else []
                for idx in range_array:
                    if idx <= max_idx:
                        summary_array.pop(idx)
            # 增加总计别名

    def _pop_cell_by_col_numbers(self, data_2dim_array):
        """
        根据前端传入的隐藏列配置剔除单元格
        :param data_2dim_array:
        :return:
        """
        if not data_2dim_array or not self.hidden_col_number_set:
            return data_2dim_array
        if self.chart_model.serial_number_config.is_show:
            self.hidden_col_number_set = set([i + 1 for i in self.hidden_col_number_set])
        for records in data_2dim_array:
            max_idx = len(records) - 1
            range_array = sorted(self.hidden_col_number_set, reverse=True) if self.hidden_col_number_set else []
            for idx in range_array:
                if idx <= max_idx:
                    records.pop(idx)
        self._pop_summary_cell()
        return data_2dim_array

    def _get_real_header_values(self, origin_row_data, dim_index):
        if self.chart_model.serial_number_config.is_show:
            header_values = tuple([origin_row_data[i + 1] for i in dim_index])
        else:
            header_values = tuple([origin_row_data[i] for i in dim_index])
        return header_values

    def insert_subtotal_data_to_2dim_array(self, data_2dim_array: list):
        """
        插入行列汇总统计数据到二维表格数据中
        :param data_2dim_array:
        :return:
        """
        subtotal = self.subtotal
        if not subtotal:
            return self.insert_serial_number_to_2dim_array(data_2dim_array)

        new_data_2dim_array = []
        col_subtotal, col_summary = self.handle_subtotal_col(data_2dim_array)
        headers, cols_data, subtotal_cols_data, subtotal_summary_cols_data = self.handle_subtotal_row()
        col_summary.extend(subtotal_summary_cols_data)
        if col_summary:
            if self.chart_model.serial_number_config.is_show:
                col_summary.insert(0, None)
            self.col_summary.append(col_summary)

        dim_indexes = cols_data.keys()
        data_2dim_array = self.insert_serial_number_to_2dim_array(data_2dim_array)
        for index, origin_row_data in enumerate(data_2dim_array):
            # 插入行小计头
            if index in headers:
                origin_row_data.extend(headers[index])
            if index < self.header_length:
                new_data_2dim_array.append(origin_row_data)
                continue
            # 插入行小计
            for dim_index in dim_indexes:
                header_values = self._get_real_header_values(origin_row_data, dim_index)
                if not header_values in cols_data[dim_index]:
                    continue
                values = cols_data[dim_index][header_values]
                origin_row_data.extend(values)

            records = self._insert_col_subtotal(col_subtotal, index, origin_row_data, subtotal_cols_data)
            new_data_2dim_array.extend(records)

        return new_data_2dim_array or data_2dim_array

    def _add_serial_number(self, insert_data):
        """
        小计行填充序号
        :param insert_data:
        :return:
        """
        for r in insert_data:
            if self.chart_model.serial_number_config.is_show:
                r['row_values'].insert(0, None)

    def _insert_col_subtotal(self, col_subtotal, index, origin_row_data, subtotal_cols_data):
        if index not in col_subtotal:
            return [origin_row_data]

        records = []
        insert_data = col_subtotal[index]
        if index in subtotal_cols_data:
            data = subtotal_cols_data[index]
            for r in insert_data:
                dim_length = r['dim_length']
                if dim_length not in data:
                    continue
                r['row_values'].extend(data[dim_length])
                if self.chart_model.serial_number_config.is_show:
                    r['row_values'].insert(0, None)

        if not subtotal_cols_data:
            self._add_serial_number(insert_data)
        if self.chart_model.subtotal_config.col_pos == 'head':
            insert_data.sort(key=lambda r: r['dim_length'])
            records.extend([r['row_values'] for r in insert_data])
            records.append(origin_row_data)
        else:
            insert_data.sort(key=lambda r: r['dim_length'], reverse=True)
            records.append(origin_row_data)
            records.extend([r['row_values'] for r in insert_data])
        return records

    def _get_data_headers(self):
        attr_name = "_data_headers"
        if not hasattr(self, attr_name):
            data_headers = [{"header": copy.deepcopy(item["header"]), "col_value": None} for item in self.data]
            setattr(self, attr_name, data_headers)
        return getattr(self, attr_name)

    def _get_header_key(self, headers):
        return tuple([tuple([header["col_type"], header["field_id"], header["col_value"]]) for header in headers])

    def _get_subtotal_col_values_map(self, subtotal_cols):
        values_map = {}
        for subtotal_col in subtotal_cols:
            values_map[self._get_header_key(subtotal_col["header"])] = subtotal_col['col_value']
        return values_map

    def handle_subtotal_col_data_v2(self, subtotal_col_records: list):
        """
        列小计或行小计数据转换结果： dict {小计维度查找key -> 小计数值字段的列表数据}
        :param subtotal_records:
        :return:
        """
        result = {}
        cate_field_nums = set()
        for r in subtotal_col_records:
            if r['has_next']:
                continue
            l = len(r['cate_fields'])
            cate_field_nums.add(l)
            subtotal_col_values_map = self._get_subtotal_col_values_map(r['cols'])
            # 模拟列小计
            col_values = []
            for data_header in self._get_data_headers():
                header_key = self._get_header_key(data_header["header"])
                col_values.append(subtotal_col_values_map.get(header_key))
            key_format = '%s' * l
            dim_values = col_values[:l]
            key = key_format % tuple(dim_values)

            result[key] = col_values[l:]
        return result, sorted(list(cate_field_nums), reverse=True)

    def handle_subtotal_col_data(self, subtotal_col_records: list):
        """
        列小计或行小计数据转换结果： dict {小计维度查找key -> 小计数值字段的列表数据}
        :param subtotal_records:
        :return:
        """
        result = {}
        cate_field_nums = set()
        for r in subtotal_col_records:
            if r['has_next']:
                continue
            l = len(r['cate_fields'])
            cate_field_nums.add(l)
            col_values = list(map(lambda x: x['col_value'], r['cols']))
            key_format = '%s' * l
            dim_values = col_values[:l]
            key = key_format % tuple(dim_values)

            result[key] = col_values[l:]
        return result, sorted(list(cate_field_nums), reverse=True)

    def handle_subtotal_col_summary_v2(self):
        """
                处理列总计数据
                :return:
                """
        summary = []
        subtotal_col = self.subtotal.get('subtotal_col')

        if not subtotal_col or not (subtotal_col.get('summary') and subtotal_col['summary'].get('cols')):
            return summary

        col_summary_alias = self.chart_model.subtotal_config.col_summary_alias
        if not self.chart_model.dims:
            col_summary_alias = ''
        # 模拟总计(将维度加入其中进行排序)
        mock_empty_subtotal_dim_nodes = copy.deepcopy(self.mock_empty_subtotal_dim_nodes)
        # 将第一个维度设置为总计
        display_sort_map = self._get_display_sort_map()
        for dim_node in mock_empty_subtotal_dim_nodes:
            map_key = tuple([dim_node["header"][0]["col_type"], dim_node["header"][0]["field_id"]])
            is_show = display_sort_map.get(map_key, {}).get("is_show", 1)
            if is_show != 0:
                dim_node["col_value"] = col_summary_alias
                break
        mock_summary = mock_empty_subtotal_dim_nodes + subtotal_col['summary']['cols']
        mock_summary = self._sort_data_by_display_order_v2(mock_summary)
        summary = [r['col_value'] for r in mock_summary]
        return summary

    def handle_subtotal_col_summary(self):
        """
        处理列总计数据
        :return:
        """
        summary = []
        subtotal_col = self.subtotal.get('subtotal_col')

        if not subtotal_col or not (subtotal_col.get('summary') and subtotal_col['summary'].get('cols')):
            return summary

        col_summary_alias = [self.chart_model.subtotal_config.col_summary_alias]
        if not self.chart_model.dims:
            col_summary_alias = []
        summary = (
            col_summary_alias
            + [None] * (self.dim_count - 1)
            + [r['col_value'] for r in subtotal_col['summary']['cols']]
        )
        return summary

    def handle_subtotal_col(self, data_2dim_array: list):
        """
        处理列汇总
        :param data_2dim_array:
        :return:
        """
        col_subtotal = defaultdict(list)
        col_summary = self.handle_subtotal_col_summary_v2()

        subtotal_col = self.subtotal.get('subtotal_col')
        if not subtotal_col or not subtotal_col.get('rows'):
            return col_subtotal, col_summary

        mappings, cate_field_nums = self.handle_subtotal_col_data_v2(subtotal_col['rows'])
        key_start_indexes = dict()
        subtotal_key_cache = dict()
        for index, r in enumerate(data_2dim_array):
            if index < self.header_length:
                continue

            keys = set()
            for l in cate_field_nums:
                dim_values = r[:l]
                key_format = '%s' * l
                key = key_format % tuple(dim_values)
                keys.add(key)

                if key not in key_start_indexes:
                    key_start_indexes[key] = index
                else:
                    subtotal_key_cache[key] = dim_values

            subtotal_keys = list(set(subtotal_key_cache.keys()) - keys)
            if not subtotal_keys:
                continue
            self.process_to_be_insert_subtotal_v2(
                col_subtotal, subtotal_keys, mappings, key_start_indexes, subtotal_key_cache, index - 1
            )

        if subtotal_key_cache:
            self.process_to_be_insert_subtotal_v2(
                col_subtotal,
                list(subtotal_key_cache.keys()),
                mappings,
                key_start_indexes,
                subtotal_key_cache,
                len(data_2dim_array) - 1,
            )
        return col_subtotal, col_summary

    def process_to_be_insert_subtotal_v2(
        self, result, subtotal_keys, num_values_mapping, key_start_indexes, subtotal_key_cache, end_index
    ):
        """
        整理插入的列小计的数据，插入位置 -> 整理后的列小计的完整行的值
        :param result:
        :param subtotal_keys:
        :param num_values_mapping:
        :param key_start_indexes:
        :param subtotal_key_cache:
        :param end_index:
        :return:
        """
        for key in subtotal_keys:
            if key not in num_values_mapping:
                continue

            origin_dim_value = subtotal_key_cache[key]
            dim_length = len(origin_dim_value)
            new_row_value = copy.deepcopy(origin_dim_value)
            new_row_value[-1] = self.chart_model.subtotal_config.col_alias
            new_row_value.extend(num_values_mapping[key])

            insert_data = {'dim_length': dim_length, 'row_values': new_row_value}
            if self.chart_model.subtotal_config.col_pos == 'head':
                insert_index = key_start_indexes[key]
            else:
                insert_index = end_index
            result[insert_index].append(insert_data)

            self.col_subtotal_insert_mapping[tuple(origin_dim_value)] = insert_index
            key_start_indexes.pop(key)
            subtotal_key_cache.pop(key)

    def process_to_be_insert_subtotal(
        self, result, subtotal_keys, num_values_mapping, key_start_indexes, subtotal_key_cache, end_index
    ):
        """
        整理插入的列小计的数据，插入位置 -> 整理后的列小计的完整行的值
        :param result:
        :param subtotal_keys:
        :param num_values_mapping:
        :param key_start_indexes:
        :param subtotal_key_cache:
        :param end_index:
        :return:
        """
        for key in subtotal_keys:
            if key not in num_values_mapping:
                continue

            origin_dim_value = subtotal_key_cache[key]
            dim_length = len(origin_dim_value)
            new_row_value = copy.deepcopy(origin_dim_value)
            new_row_value[-1] = self.chart_model.subtotal_config.col_alias
            if dim_length < self.dim_count:
                new_row_value.extend([None] * (self.dim_count - dim_length))
            new_row_value.extend(num_values_mapping[key])

            insert_data = {'dim_length': dim_length, 'row_values': new_row_value}
            if self.chart_model.subtotal_config.col_pos == 'head':
                insert_index = key_start_indexes[key]
            else:
                insert_index = end_index
            result[insert_index].append(insert_data)

            self.col_subtotal_insert_mapping[tuple(origin_dim_value)] = insert_index
            key_start_indexes.pop(key)
            subtotal_key_cache.pop(key)

    def handle_subtotal_row(self):
        """
        处理行汇总
        :param data_2dim_array:
        :return:
        """
        headers = defaultdict(list)
        cols_data = defaultdict(dict)
        subtotal_cols_data = defaultdict(dict)
        subtotal_summary_cols_data = []

        subtotal_row = self.subtotal.get('subtotal_row')
        if not subtotal_row:
            return headers, cols_data, subtotal_cols_data, subtotal_summary_cols_data

        for r in subtotal_row:
            header_values = [self._get_header_value(h) for h in r['header']]
            for index, header_value in enumerate(header_values):
                headers[index].append(header_value)

            if r.get('cols'):
                self.handle_subtotal_row_data(cols_data, r['cols'])
            if r.get('subtotal_cols'):
                self.handle_subtotal_row_col_data(subtotal_cols_data, r['subtotal_cols'])
            if r.get('subtotal_summary_cols'):
                self.handle_subtotal_row_col_summary_data(subtotal_summary_cols_data, r['subtotal_summary_cols'])
        return headers, cols_data, subtotal_cols_data, subtotal_summary_cols_data

    def handle_subtotal_row_data(self, result: defaultdict, row_records: list):
        """
        处理行小计
        :param result:
        :param row_records:
        :return:
        """
        key_set = set()
        for r in row_records:
            header_indexes = []
            header_values = []
            for s in r['siblings']:
                header_data = s['header']
                header_str = ('%s' * len(header_data)) % tuple([h['alias'] for h in header_data])
                header_indexes.append(self.header_mapping[header_str])
                header_values.append(s['col_value'])

            header_indexes = tuple(header_indexes)
            header_values = tuple(header_values)
            if header_values not in result[header_indexes]:
                result[header_indexes][header_values] = []

            key = tuple(header_indexes + header_values)
            if key in key_set:
                continue
            key_set.add(key)
            result[header_indexes][header_values].append(r['col_value'])

    def handle_subtotal_row_col_data(self, result: defaultdict, row_col_subtotal_data: list):
        """
        处理列小计的行小计
        :param result:
        :param row_col_subtotal_data:
        :return:
        """
        for sr in row_col_subtotal_data:
            dim_values = tuple([sb['col_value'] for sb in sr['siblings']])
            if dim_values not in self.col_subtotal_insert_mapping:
                continue

            dim_length = len(dim_values)
            insert_index = self.col_subtotal_insert_mapping[dim_values]
            if not dim_length in result[insert_index]:
                result[insert_index][dim_length] = []
            result[insert_index][dim_length].append(sr['col_value'])

    @staticmethod
    def handle_subtotal_row_col_summary_data(result: list, subtotal_summary_cols_data: list):
        """
        处理列总计的行小计
        :return:
        """
        for r in subtotal_summary_cols_data:
            result.append(r['col_value'])
