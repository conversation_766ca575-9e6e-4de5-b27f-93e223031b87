#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
分页表格下载
"""
import logging
from base import repository
from dmplib import config
from dmplib.hug import g
from dmplib.saas.project import set_correct_project_code
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDownloadModel
from dashboard_chart.services import dashboard_service
from dashboard_chart.repositories import download_repository

logger = logging.getLogger(__name__)


def get_dashboard_page_chart_download_list(**kwargs):
    """
    获取分页表格下载记录
    :return:
    """
    page = kwargs.get('page') if kwargs.get('page') else 1
    page_size = kwargs.get('per_page') if kwargs.get('per_page') else 100
    user_id = kwargs.get('user_id')
    if kwargs.get('is_admin') and kwargs.get('is_admin') in ["1", 1]:
        user_id = None
    log_list = download_repository.get_dashboard_page_chart_download_list(page, page_size, user_id)
    if log_list.get('data'):
        status_dict = {'0': '创建中', '1': '已创建', '2': '成功', '3': '失败'}
        for log in log_list.get('data'):
            log['status'] = (
                status_dict.get(str(log.get('status')))
                if status_dict.get(str(log.get('status')))
                else log.get('status')
            )
    return log_list


def get_download_message(token, request_data):
    """
    获取下载结果
    :param token:
    :param request_data:
    :return:
    """
    check_flag, errmsg, data = dashboard_service.check_token(token)
    if not check_flag:
        return check_flag, errmsg, None

    g.cookie = {'token': token}
    g.account = data.get("account")
    g.userid = data.get("id")
    code = data.get('tenant_code') if data.get('tenant_code') else request_data.get('code')
    set_correct_project_code(code)
    if not g.code:
        return False, '租户代码不能为空', None
    download_model = ChartDownloadModel(**request_data)
    msg, result = get_download_task_data(download_model)
    return True, msg, result


def get_download_task_data(download_model):
    """
    获取下载进度
    :return:
    """
    result_data = repository.get_data(
        'dashboard_chart_download_task', {'download_id': download_model.download_id},
        ['status', 'download_url', 'flow_id']
    )
    percent = 0
    if not result_data:
        msg = "下载任务不存在:{}".format(download_model.download_id)
        raise UserError(message=msg)
    download_status = result_data.get('status', 0)
    msg = ''
    if download_status == 0:
        msg = '下载异常，请稍后重试'
    elif download_status == 1:
        msg = '单图数据生成中，完成后会提示您下载'
        flow_data = repository.get_data(
            'flow', {'id': result_data.get('flow_id', '')}, ['run_status']
        )
        if not flow_data:  # 说明任务没有创建
            percent = 10
        else:
            percent_obj = {'已创建': 20, '运行中': 40, '已成功': 90}
            if flow_data.get('run_status') in percent_obj:
                percent = percent_obj[flow_data.get('run_status')]
    elif download_status == 2:
        msg = '单图数据下载文件已生成，点击下载'
        percent = 100
    elif download_status == 3:
        msg = '单图数据下载文件生成失败，请重试'
    # 替换download_url
    if result_data.get("download_url") and result_data.get("download_url").find("oss-cn") != -1:
        current_domain = config.get('Domain.dmp')
        result_data['download_url'] = "{domain}/api/download/oss?url={url}".format(
            domain=current_domain, url=result_data.get("download_url")
        )
    percent_obj = {0: "下载异常，请稍后重试", 10: "导出任务已创建，前方任务较多，等待调度执行...", 20: "导出任务启动...", 40: "开始进行报表数据采集...",
                   90: "数据采集完成，文件传输中...", 100: "单图数据下载文件已生成"}
    result_data['percent'] = percent
    result_data['percent_msg'] = percent_obj[percent]
    return msg, result_data
