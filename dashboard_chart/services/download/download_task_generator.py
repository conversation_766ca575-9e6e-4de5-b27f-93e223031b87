#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/6 10:59
# <AUTHOR> caoxl
# @File     : download_factory.py
import json

from dashboard_chart.services import dashboard_service
from dashboard_chart.services.download import (
    chart_column_download_service,
    chart_api_download_service,
    chart_normal_download_service,
)
from dmplib.hug import g
from dmplib.saas.project import set_correct_project_code
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDataModel as NewChartDataModel
from user_log.models import UserLogModel


def generate_download_task(token, request_data):
    """
    生成分页表格异步下载任务
    :param token:
    :param request_data:
    :return:
    """
    if not request_data:
        raise UserError(message='非法请求参数')

    check_flag, errmsg, data = dashboard_service.check_token(token)
    if not check_flag:
        return check_flag, errmsg, None

    g.cookie = {'token': token}
    g.account = data.get("account")
    g.userid = data.get("id")
    code = data.get('tenant_code') if data.get('tenant_code') else request_data.get('code')
    set_correct_project_code(code)
    if not g.code:
        raise UserError(message='租户代码不能为空')

    # 判断是否能下载
    # dmp 登录，默认可以下载(云客使用报告地址跳转的参数中带有dashboard_id)
    flag = dashboard_service.check_permission(request_data.get("dashboard_id"), data, "download")
    if not flag:
        return False, "没有下载权限", {}

    # 将外部参数放入共享变量中
    g.external_params = data.get('external_params')
    chart_params_list = request_data.get('chart_params')
    chart_params = chart_params_list[0] if len(chart_params_list) else {}
    chart_params["external_subject_ids"] = request_data.get('external_subject_ids') or []
    model = NewChartDataModel(**chart_params)
    # 通用表格
    if model.data_logic_type_code == 'column':
        success, msg, result = chart_column_download_service.generate_download_task(model)
        return success, msg, result
    # api 数据集
    is_api_dataset, success, msg, result = chart_api_download_service.generate_download_task(
        model, chart_params.get("row_number", 0)
    )
    if is_api_dataset:
        return success, msg, result
    msg, result = chart_normal_download_service.generate_download_task(model)
    return True, msg, result


def export_record_log(request, request_data, is_release=False):
    """
    表格数据导出记录操作日志

    :param request:
    :param request_data:
    :param is_release:
    :return:
    """
    dashboard_id = request_data.get('dashboard_id')
    chart_params_list = request_data.get('chart_params')
    chart_params = chart_params_list[0] if len(chart_params_list) else {}
    chart_id = chart_params.get("id")

    dashboard_info = get_dashboard_and_chart_name(dashboard_id, chart_id, is_release)
    dashboard_name = dashboard_info.get("name")
    chart_name = dashboard_info.get("chart_name")
    extra = {'is_release': is_release}

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'dashboard_chart',
            'id': dashboard_id,
            'content': f'导出【{dashboard_name}】-【{chart_name}】的数据',
            'extra': json.dumps(extra, ensure_ascii=False)
        },
    )


def get_dashboard_and_chart_name(dashboard_id, chart_id, is_release=False):
    return dashboard_service.get_dashboard_and_chart_name(dashboard_id, chart_id, is_release)
