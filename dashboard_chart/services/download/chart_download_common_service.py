#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/6 15:10
# <AUTHOR> caoxl
# @File     : chart_download_common_service.py
# pylint:disable=R0201
from dashboard_chart.repositories import chart_repository
from base.enums import ChartNumFormulaStringMode


class ChartDownloadCommon:
    @staticmethod
    def assign_show_header(show_header, formula_mode):
        """
        拼装显示用的表头
        :param show_header:
        :param formula_mode:
        :return:
        """
        add_str = ""
        if not show_header or not formula_mode:
            return show_header
        split_formula_mode = formula_mode
        if split_formula_mode == "count":
            add_str = ChartNumFormulaStringMode.Count.value
        elif split_formula_mode == "sum":
            add_str = ChartNumFormulaStringMode.Sum.value
        elif split_formula_mode == "avg":
            add_str = ChartNumFormulaStringMode.Avg.value
        elif split_formula_mode == "max":
            add_str = ChartNumFormulaStringMode.Max.value
        elif split_formula_mode == "min":
            add_str = ChartNumFormulaStringMode.Min.value
        elif split_formula_mode == "distinct":
            add_str = ChartNumFormulaStringMode.Distinct.value
        elif split_formula_mode == "year":
            add_str = ChartNumFormulaStringMode.Year.value
        elif split_formula_mode == "month":
            add_str = ChartNumFormulaStringMode.Month.value
        elif split_formula_mode == "day":
            add_str = ChartNumFormulaStringMode.Day.value
        elif split_formula_mode == "hour":
            add_str = ChartNumFormulaStringMode.Hour.value
        elif split_formula_mode == "minute":
            add_str = ChartNumFormulaStringMode.Minute.value
        elif split_formula_mode == "second":
            add_str = ChartNumFormulaStringMode.Second.value
        new_show_header = "{0}({1})".format(show_header, add_str) if add_str else show_header
        return new_show_header

    def get_sorted_header_for_excel(self, model, orig_data_header_list):
        """
        获取排序后的表头数组
        :param model:
        :param orig_data_header_list:
        :return:
        """
        sorted_field_list = list()
        sorted_match_header_list = list()
        sorted_show_header_list = list()

        # 获取已排序的dim和num
        sorted_field_list.extend(chart_repository.get_rank_dims(model.id))
        sorted_field_list.extend(chart_repository.get_rank_nums(model.id))

        # 获取字段的数据集数据
        sorted_field_list = self._assign_dataset_data_to_sorted_fields(model, sorted_field_list)

        # 获取排序后的表头
        for single_field in sorted_field_list:
            for orig_header in orig_data_header_list:
                col_name = single_field.get('col_name')
                dataset_alias = single_field.get('dataset_alias')
                alias_name = single_field.get('alias_name')
                alias = single_field.get('alias')
                formula_mode = single_field.get('formula_mode', '')
                regex_flag = self.check_header(formula_mode, col_name, dataset_alias, alias_name, orig_header)
                if regex_flag:
                    show_header = alias or alias_name or col_name or ""
                    if not show_header:
                        continue
                    sorted_match_header_list.append(orig_header)
                    sorted_show_header_list.append(self.assign_show_header(show_header, formula_mode))
                    break
        return sorted_show_header_list, sorted_match_header_list

    @staticmethod
    def check_header(formula_mode, col_name, dataset_alias, alias_name, orig_header):
        """
        精确匹配表头
        :param formula_mode:
        :param col_name:
        :param dataset_alias:
        :param alias_name:
        :param orig_header:
        :return:
        """
        check_flag = False
        for item in [col_name, dataset_alias, alias_name]:
            if not item:
                continue
            for_check_header = '{}_{}'.format(formula_mode, item) if formula_mode else item
            if for_check_header == orig_header:
                return True
        return check_flag

    def _assign_dataset_data_to_sorted_fields(self, model, sorted_field_list):
        _fields = ['col_name', 'alias', 'alias_name']
        dataset_field_dict = model.dataset_field_dict
        for single_field in sorted_field_list:
            field_id = single_field.get('field_id')
            field_data = dataset_field_dict.get(field_id)
            if not field_data:
                continue
            for k, v in field_data.items():
                if k in _fields:
                    # 避免alias字段被重置
                    k = 'dataset_alias' if k == 'alias' else k
                    single_field.update({k: v})
        return sorted_field_list
