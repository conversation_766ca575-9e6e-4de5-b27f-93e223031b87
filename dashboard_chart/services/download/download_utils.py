#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/6 14:46
# <AUTHOR> caoxl
# @File     : download_utils.py
import re
from base.enums import TableDownloadFrom
from dmplib import config


def substr_by_byte_length(string, byte_length):
    """
    根据字节长度截取字符串
    :param string:
    :param byte_length:
    :return:
    """
    string1 = string.encode("utf-8")
    string1_byte_length = len(string1)
    if byte_length > string1_byte_length:
        return string
    count = byte_length
    while True:
        if count <= 0:
            return ""
        new_str = string1[0:count]
        try:
            real_str = new_str.decode()
            return real_str
        except Exception:
            count -= 1


def get_valid_filename(s, default_name='未命名文件'):
    """
    Return the given string converted to a string that can be used for a clean
    filename. Remove leading and trailing spaces; convert other spaces to
    underscores; and remove anything that is not an alphanumeric, dash,
    underscore, or dot.
    'johns_portrait_in_2004.jpg'
    """
    s = str(s).strip().replace(' ', '_')
    # 其他不合法的需要替换的字符
    invalid_filename_chars = config.get("Function.chart_download_invalid_filename_chars")
    if invalid_filename_chars:
        filename = re.sub(
            r'(?u)[^-\w]|[{invalid_filename_chars}]'.format(invalid_filename_chars=invalid_filename_chars), '', s
        )
    else:
        filename = re.sub(r'(?u)[^-\w]', '', s)
    if not filename or filename == '_':  # 名称为_表示单图名称和报告名称都被过滤特殊字符后为空
        return default_name
    return filename


def generate_message_title(msg_type, filename, download_from):
    """
    组装消息title
    :param msg_type:
    :param filename:
    :param download_from:
    :return:
    """
    download_from_str = '预览态数据下载'
    if download_from == TableDownloadFrom.Released.value:
        download_from_str = '发布态数据下载'

    title = ''
    if msg_type == 'generate':
        title = '{}，正在生成数据【{}】，请稍候...'.format(download_from_str, filename)
    elif msg_type == 'successful':
        title = '{}，成功生成数据【{}】，可点击下方按钮再次下载数据'.format(download_from_str, filename)
    elif msg_type == 'fail':
        title = '{}，异常错误，数据【{}】生成失败，请联系管理员'.format(download_from_str, filename)
    return title
