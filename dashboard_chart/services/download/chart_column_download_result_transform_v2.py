#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/15 15:44
# <AUTHOR> caoxl
# @File     : chart_column_download_result_transform_v2.py
# pylint: disable=R1718,R0201,W0105,R1710
import copy
import functools
import json

from dashboard_chart.models import ChartDataModel
from base.enums import ColTypes, DownloadHeaderDataSource


class ChartResultTransformV2:
    def __init__(self, chart_model: ChartDataModel, chart_result: dict, start_serial_number: int, is_last_page: bool):
        self.chart_model = chart_model
        # 结果数据
        self.data = chart_result.get('data')
        # 小计数据
        self.subtotal = chart_result.get('subtotal')
        # 开始的列序号
        self.start_serial_number = start_serial_number
        # 是否是最后一页
        self.is_last_page = is_last_page

    def process(self) -> [list, list]:
        """
        处理详细内容
        :return:
        """
        """"
        step 1: 初始化表头，初始化数据 = 列序号 + 普通数据
        step 2: 获取完整数据和表头
        step 3: 调整列顺序
        step 4: 构建总计
        step 5: 隐藏列
        step 6: 处理表头，如 分组 等
        step 7: 返回二维数据
        """
        if not self.data:
            return [], []
        # step 1
        data_map = self._init_data_and_headers()
        # step 2
        subtotal_summary_map = self._build_complate_subtotal_data(data_map)
        # step 3
        data_map = self._sort_data_map_by_display_order(data_map)
        # step 4
        self._build_summary(data_map, subtotal_summary_map)
        # step 5
        self._remove_unshow_columns(data_map)
        # 如果是自助分析, 需要对配置了百分比格式的数据进行处理
        if self.chart_model.chart_code == 'analysis_table':
            self._format_num_data(data_map)
        # step 6
        self._op_table_header(data_map)
        # step 7
        return self._get_table_data(data_map)

    def _get_display_format(self, headers):
        for header in headers:
            col_type = header.get('col_type')
            if col_type != 'num':
                continue
            field_id = header.get('field_id') or header.get('dataset_field_id')
            num = next((num for num in self.chart_model.original_nums if num.get('num') == field_id), None)
            try:
                display_format = json.loads(num.get('display_format'))
                return display_format or {}
            except Exception:
                return None
        return None

    def _get_formula_mode(self, headers):
        for header in headers:
            col_type = header.get('col_type')
            if col_type != 'num':
                continue
            field_id = header.get('field_id') or header.get('dataset_field_id')
            num = next((num for num in self.chart_model.original_nums if num.get('num') == field_id), None)
            return num.get('formula_mode') or None
        return None

    def _format_percentage_num_row(self, column, fixed):
        rows = column.get('new_rows', [])
        for idx in range(0, len(rows)):
            row = rows[idx]
            if row is None:
                continue
            if not (isinstance(row, int) or isinstance(row, float)):
                continue
            fstr = f'%.{fixed}f%%'
            rows[idx] = fstr % (row * 100)

    def _format_num_data(self, data_map):
        for column in data_map.values():
            headers = column.get('header') or []
            formula_mode = self._get_formula_mode(headers)
            if formula_mode == 'ratio':
                fixed = 2
                self._format_percentage_num_row(column, fixed)
                continue

            display_format = self._get_display_format(headers)
            if display_format and display_format.get('display_mode') == 'percentage':
                fixed = display_format.get('fixed_decimal_places') or 0
                self._format_percentage_num_row(column, fixed)

    def _build_complate_subtotal_data(self, data_map: dict) -> dict:
        """
        构建完整行列小计数据
        完整数据包括 原始数据 行小计 列小计 列总计 行总计
        """
        """"
        step 1: 获取行列小计及总计信息
        step 2: 增加行小计头和初始化数据
        step 3: 遍历每一行原始数据
        step 4: 拼接行小计数据
        step 4.1: 如果有行小计表头，则获取当前数据的sbulings的key, 然后将行小计数据拼接上
        step 5: new_rows 从 rows 中复制一条原始数据增加进入
        step 6: 拼接列小计数据
        step 6.1: 获取每组分类汇总对应的上一条数据的tuple集合，和当前一条数据每组分类汇总对应的数据的tuple集合做对比，如
                  果两条数据不一致则考虑增加小计
        step 6.1.1: 满足上2个条件则增加小计，1:)各组分类汇总数据不止一条 2:)下一页没有，即has_next为0
        """
        # step 1
        (
            subtotal_row_header_map,
            sibling_headers,
            subtotal_row_map,
            subtotal_col_map,
            subtotal_cate_fields,
            subtotal_summary_map,
        ) = self._get_complete_subtotal_data(data_map)
        # step 2
        self._add_subtotal_row_data_and_headers(data_map, subtotal_row_header_map)
        # step 3
        data_count = len(self.data[0]['rows']) if self.data else 0
        for row_index in range(data_count):
            # step 4
            self._build_subtotal_row(data_map, row_index, sibling_headers, subtotal_row_header_map, subtotal_row_map)
            # step 5
            for _, data in data_map.items():
                data["new_rows"].append(data["rows"][row_index])
            # step 6
            self._build_subtotal_col(data_map, row_index, subtotal_cate_fields, subtotal_col_map)
        # 删除无用数据 减小内存 将小计的单元格整体往右移一位
        target_value = self.chart_model.subtotal_config.col_alias
        pre_replace_index = []
        pre_replace_values = []
        col_pos = self.chart_model.subtotal_config.col_pos
        for _, data in data_map.items():
            del data["rows"]
            new_rows = data.get('new_rows') or []
            replace_index = [index for index, value in enumerate(new_rows) if value == target_value]
            for index in replace_index:
                if index - 1 < 0 and col_pos == 'tail':
                    continue
                new_rows[index] = new_rows[index - 1] if col_pos == 'tail' else new_rows[index + 1]
            for r_index in pre_replace_index:
                replace_values = pre_replace_values[r_index-1] if col_pos == 'tail' else pre_replace_values[r_index + 1]
                if new_rows[r_index] is None:
                    new_rows[r_index] = target_value if not self.chart_model.subtotal_config.show_classify_name else '{}{}'.format(replace_values, target_value)
            pre_replace_index = copy.deepcopy(replace_index)
            pre_replace_values = copy.deepcopy(new_rows)
        return subtotal_summary_map

    def _get_table_data(self, data_map: dict) -> [list, list]:
        """
        获取二维表格数据
        :param data_map:
        :return:
        """
        table_header, table_content = None, None
        for header_key, data in data_map.items():
            header_display_words = data["header_display_words"]
            if table_header is None:
                table_header = []
                for _ in header_display_words:
                    table_header.append([])
            for idx, header_display_word in enumerate(header_display_words):
                table_header[idx].append(header_display_word)
            if table_content is None:
                table_content = []
                for _ in data["new_rows"]:
                    table_content.append([])
            for row_index, value in enumerate(data["new_rows"]):
                table_content[row_index].append(value)
        return table_header, table_content

    def _remove_unshow_columns(self, data_map: dict):
        """
        移除不需要展示的列(此处需要区分排序，排序中对比维度不支持排序，但是对比维度却和支持隐藏指定数据列)
        :param data_map:
        :return:
        """
        display_map = self._get_display_map()
        header_keys = copy.deepcopy(list(data_map.keys()))
        for header_key in header_keys:
            # 行小计不受影响
            display_map_key = self._get_header_display_key(data_map[header_key]["header"])
            column_display = display_map.get(display_map_key)
            if column_display and not column_display["is_show"]:
                del data_map[header_key]

    def _get_header_group_name(self, header: list, header_key: tuple):
        group_name = None
        # 行小计无分组名称
        if self._is_subtotal_row_header(header_key):
            return group_name
        display_group_map = self._get_display_group_map()
        # 对于有多个子表头的，排序和分组暂不支持(透视表数值表头)
        if len(header) == 1:
            column = header[0]
            column["dataset_field_id"] = column["field_id"]
            group_map_key = self._get_display_sort_key(column)
            group_name = display_group_map.get(group_map_key)
        return group_name

    def _op_table_header(self, data_map: dict):
        """
        增加表头信息
        :param data_map:
        :return:
        """
        max_header_length = 0
        for header_key, data in data_map.items():
            header_display_words = []
            group_name = data.get("group_name")
            for header_item in data["header"]:
                display_word = header_item["alias"]
                if header_item.get("col_type") == ColTypes.Comparison.value:
                    display_word = header_item["col_value"]
                header_display_words.append(display_word)
            data["header_display_words"] = header_display_words
            current_header_length = len(header_display_words)
            if group_name:
                current_header_length += 1
            max_header_length = (
                current_header_length if current_header_length > max_header_length else max_header_length
            )
        self._generate_header_display_words(data_map, max_header_length)

    def _generate_header_display_words(self, data_map: dict, max_header_length: int):
        """
        获取最终的表头展示名称
        :param data_map:
        :param max_header_length:
        :return:
        """
        has_group = self._has_real_group()
        for header_key, data in data_map.items():
            header_display_words = data["header_display_words"]
            data["header_display_words"] = header_display_words
            if len(header_display_words) < max_header_length:
                # 如果有分组 则维度等一律显示在最下面，否则维度等显示在最上面
                if has_group:
                    data["header_display_words"] = [
                        *([None] * (max_header_length - len(header_display_words))),
                        *data["header_display_words"],
                    ]
                else:
                    data["header_display_words"] = [
                        *data["header_display_words"],
                        *([None] * (max_header_length - len(header_display_words))),
                    ]
            if data["group_name"]:
                data["header_display_words"][0] = data["group_name"]

    def _sort_data_map_by_display_order(self, data_map: dict) -> dict:
        """
        为展示列排序（此处需要和隐藏列区分，隐藏列可以支持指定对比维度数值列隐藏，但是对比维度数值列不支持排序）
        :param data_map:
        :return:
        """
        if not self.chart_model.column_display:
            return data_map
        display_sort_map = self._get_display_sort_map()

        def _compare_func(x, y):
            x_order_value = display_sort_map.get(self._get_header_sort_key(x["header"]), {}).get("order")
            y_order_value = display_sort_map.get(self._get_header_sort_key(y["header"]), {}).get("order")
            if x_order_value is None or y_order_value is None:
                return 0
            if x_order_value > y_order_value:
                return 1
            if x_order_value < y_order_value:
                return -1
            return 0

        # 行小计不受字段顺序调整影响
        serial_number_headers, data_headers, subtotal_row_headers = [], [], []
        for header_key, data in data_map.items():
            header_item = {"header": data["header"], "header_key": header_key}
            if self._is_subtotal_row_header(header_key):
                subtotal_row_headers.append(header_item)
            elif self._is_serial_number_header(header_key):
                serial_number_headers.append(header_item)
            else:
                data_headers.append(header_item)
        ordered_headers = sorted(data_headers, key=functools.cmp_to_key(_compare_func))
        # 插入行小计头
        self._insert_subtotal_row_headers(data_map, ordered_headers, subtotal_row_headers)
        if serial_number_headers:
            ordered_headers = serial_number_headers + ordered_headers
        return {
            ordered_header["header_key"]: data_map[ordered_header["header_key"]] for ordered_header in ordered_headers
        }

    def _insert_subtotal_row_headers(self, data_map, ordered_headers, subtotal_row_headers):
        if subtotal_row_headers:
            subtotal_row_insert_index = self._get_subtotal_row_insert_index(ordered_headers, data_map)
            for idx, subtotal_row_header in enumerate(subtotal_row_headers):
                ordered_headers.insert(subtotal_row_insert_index + idx, subtotal_row_header)

    def _get_subtotal_row_insert_index(self, data_headers: list, data_map: dict) -> int:
        if self.chart_model.subtotal_config.row_pos == 'head':
            # 若在前面，若无透视则在第一个数值或数值分组的前面， 否则在透视前面
            return self._get_subtotal_row_insert_index_for_head(data_headers, data_map)
        else:
            # 若在后面，若无透视则在最后一个数值或数值分组的后面， 否则在透视后面面
            return self._get_subtotal_row_insert_index_for_tail(data_headers, data_map)

    def _get_subtotal_row_insert_index_for_head(self, data_headers, data_map):
        """
        获取行小计在前面展示的插入位置
        :param data_headers:
        :return:
        """
        insert_index = 0
        if self.chart_model.comparisons:
            for idx, item in enumerate(data_headers):
                if len(item["header"]) > 1:
                    insert_index = idx
                    break
        else:
            for idx, item in enumerate(data_headers):
                if item["header"][0]["col_type"] == ColTypes.Num.value:
                    insert_index = idx
                    insert_index = self._get_insert_index_with_group_for_head(
                        data_headers, data_map, insert_index, item
                    )
                    break
        return insert_index

    def _get_insert_index_with_group_for_head(self, data_headers, data_map, insert_index, item):
        data = data_map[item["header_key"]]
        group = data["group_name"]
        remain_data = data_headers[0:insert_index]
        for idx1, item1 in enumerate(remain_data[::-1]):
            current_group = data_map[item1["header_key"]]["group_name"]
            if group is None and current_group is None:
                break
            if group != current_group:
                break
            insert_index = insert_index - idx1 - 1
        return insert_index

    def _get_subtotal_row_insert_index_for_tail(self, data_headers, data_map):
        """
        获取行小计在后面展示的插入位置
        :param data_headers:
        :return:
        """
        data_length = len(data_headers)
        insert_index = data_length
        count = data_length - 1
        if self.chart_model.comparisons:
            while count > 0:
                item = data_headers[count]
                if len(item["header"]) > 1:
                    insert_index = count + 1
                    break
                count -= 1
        else:
            while count > 0:
                item = data_headers[count]
                if item["header"][0]["col_type"] == ColTypes.Num.value:
                    insert_index = count + 1
                    insert_index = self._get_insert_index_with_group_for_tail(
                        data_headers, data_map, insert_index, item
                    )
                    break
                count -= 1
        return insert_index

    def _get_insert_index_with_group_for_tail(self, data_headers, data_map, insert_index, item):
        data = data_map[item["header_key"]]
        group = data["group_name"]
        for idx1, item1 in enumerate(data_headers[insert_index:]):
            current_group = data_map[item1["header_key"]]["group_name"]
            if group is None and current_group is None:
                break
            if group != current_group:
                break
            insert_index = insert_index + idx1 + 1
        return insert_index

    def _is_dim_header(self, header: list) -> bool:
        """
        判断是否是dim的头
        :param header:
        :return:
        """
        is_dim_header = False
        if len(header) == 1 and header[0]["col_type"] == ColTypes.Dim.value:
            is_dim_header = True
        return is_dim_header

    def _is_subtotal_row_header(self, header_key: tuple) -> bool:
        """
        判断是否是行小计的头
        :param header_key:
        :return:
        """
        is_subtotal_row_header = False
        for each in header_key:
            if each[len(each) - 1] == DownloadHeaderDataSource.SubtotalRow.value:
                is_subtotal_row_header = True
                break
        return is_subtotal_row_header

    def _is_serial_number_header(self, header_key: tuple) -> bool:
        """
        判断是否是列序号的头
        :param header_key:
        :return:
        """
        is_serial_number_header = False
        for each in header_key:
            if each[len(each) - 1] == DownloadHeaderDataSource.SerialNumber.value:
                is_serial_number_header = True
                break
        return is_serial_number_header

    def _get_display_map(self) -> dict:
        if not hasattr(self, "_display_map"):
            display_map = {}
            for column in self.chart_model.column_display:
                display_key = self._get_display_key(column)
                if display_key not in display_map:
                    display_map[display_key] = column
            setattr(self, "_display_map", display_map)
        return getattr(self, "_display_map")

    def _get_display_key(self, column_item: dict) -> tuple:
        return tuple([column_item["col_type"], column_item["dataset_field_id"]])

    def _get_display_sort_map(self) -> dict:
        if not hasattr(self, "_display_sort_map"):
            sort_map = {}
            for column in self.chart_model.column_display:
                sort_key = self._get_display_sort_key(column)
                if sort_key not in sort_map:
                    sort_map[sort_key] = column
            setattr(self, "_display_sort_map", sort_map)
        return getattr(self, "_display_sort_map")

    def _get_display_group_map(self) -> dict:
        if not hasattr(self, "_display_group_name"):
            group_map = {}
            has_group = False
            for item in self.chart_model.column_display:
                group = item.get("group", {})
                name = None
                if isinstance(group, dict):
                    name = group.get("name")
                    if name:
                        has_group = True
                if self.chart_model.comparisons and item["col_type"] == ColTypes.Num.value:
                    group_map_key = tuple([ColTypes.Comparison.value, None])
                else:
                    group_map_key = tuple([item["col_type"], item["dataset_field_id"]])
                group_map[group_map_key] = name
            setattr(self, "_has_group", has_group)
            setattr(self, "_display_group_name", group_map)
        return getattr(self, "_display_group_name")

    def _has_real_group(self) -> bool:
        """
        是否有分组
        是否有分组会影响表头展示位置
        :return:
        """
        if not hasattr(self, "_has_group"):
            self._get_display_group_map()
        return getattr(self, "_has_group")

    def _get_header_sort_key(self, headers: list) -> tuple:
        header_length = len(headers)
        # 普通头
        if header_length <= 1:
            item = headers[0]
            return tuple([item["col_type"], item["field_id"]])
        # 对比维度头
        return tuple([ColTypes.Comparison.value, None])

    def _get_header_display_key(self, headers: list) -> tuple:
        """
        获取表头展示key
        注意行小计表头和普通数值列是一致的，但是行小计不不参与调整顺序和是否隐藏
        :param headers:
        :return:
        """
        header_length = len(headers)
        # 普通头
        if header_length == 1:
            item = headers[0]
            if item["alias_name"] and item["alias_name"].startswith("subtotal_row"):
                return tuple([None, None])
            return tuple([item["col_type"], item["field_id"]])
        # 对比维度头
        for header_item in headers:
            if header_item["col_type"] == ColTypes.Num.value:
                return tuple([header_item["col_type"], header_item["field_id"]])
        return tuple([None, None])

    def _get_display_sort_key(self, column_item: dict) -> tuple:
        if self.chart_model.comparisons and column_item["col_type"] == ColTypes.Num.value:
            return tuple([ColTypes.Comparison.value, None])
        return tuple([column_item["col_type"], column_item["dataset_field_id"]])

    def _build_summary(self, data_map: dict, subtotal_summary_map: dict):
        """
        构建总计
        :param data_map:
        :param subtotal_summary_map:
        :return:
        """
        if not subtotal_summary_map:
            return
        insert_index = 0
        data_length = None
        display_map = self._get_display_map()
        assign_summary_word = False
        for header_key, col in data_map.items():
            col_summary = subtotal_summary_map.get(header_key)
            data_length = len(col["new_rows"]) if data_length is None else data_length
            col_value = col_summary["col_value"]
            # 加入总计展示字段（没有排序前，第一个不隐藏的维度字段）
            column_display = display_map.get(self._get_header_display_key(col["header"]))
            # 行小计表头和总计无关 (序号列不能插入总计)
            if (
                not self._is_subtotal_row_header(header_key)
                and ((not column_display or column_display.get("is_show")) and self._is_dim_header(col["header"]))
                and not assign_summary_word
                and not self._is_serial_number_header(header_key)
            ):
                col_value = self.chart_model.subtotal_config.col_summary_alias
                assign_summary_word = True
            # 需要区分总计展示在前方还是在后方
            if self.chart_model.subtotal_config.col_summary_pos == 'head' and self.start_serial_number == 1:
                insert_index = 0
                col["new_rows"].insert(insert_index, col_value)
            elif self.chart_model.subtotal_config.col_summary_pos == 'tail' and self.is_last_page:
                insert_index = data_length
                col["new_rows"].insert(insert_index, col_value)

    def _build_subtotal_col(self, data_map: dict, row_index: int, subtotal_cate_fields: list, subtotal_col_map: dict):
        """
        构建列小计
        :param data_map:
        :param row_index:
        :param subtotal_cate_fields:
        :param subtotal_col_map:
        :return:
        """
        """"
        subtotal_cate_fields 数据结构
        [
            tuple([
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
            ]),
            tuple([
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
            ])
        ]
        """
        # 小计拼接的时候需要反向拼接，即先拼接级别较低的
        subtotal_cate_fields_copy = copy.deepcopy(subtotal_cate_fields)
        subtotal_cate_fields_copy = (
            subtotal_cate_fields_copy
            if self.chart_model.subtotal_config.col_pos == 'head'
            else subtotal_cate_fields_copy[::-1]
        )
        for group in subtotal_cate_fields_copy:
            subtotal_cate_fields_key = []
            subtotal_cate_fields_headers = []
            for header_key in group:
                col_value = data_map[header_key]["rows"][row_index]
                subtotal_cate_fields_key.append(tuple([col_value, header_key]))
                subtotal_cate_fields_headers.append(header_key)
            subtotal_col = subtotal_col_map.get(tuple(subtotal_cate_fields_key))
            # 符合拼接条件才进行拼接
            if not self._need_build_subtotal_col(data_map, row_index, subtotal_cate_fields_headers, subtotal_col):
                continue
            self._insert_subtotal_cols(data_map, subtotal_col)

    def _insert_subtotal_cols(self, data_map: dict, subtotal_col: dict) -> int:
        """
        插入列小计的列
        :param data_map:
        :param subtotal_col:
        :return:
        """
        insert_index = 0
        for subtotal_col_header_key, subtotal_col_data in subtotal_col.items():
            # key 中有 has_next 等字段 只有tuple 才为数据的key
            if not isinstance(subtotal_col_header_key, tuple):
                continue
            # 对于超过100列表格数据，目前暂时有限制，这个限制会导致col_header不完整
            # 所以这里对于不存在的直接跳过
            # 后续如果限制被放开，这里需要取消这个条件
            if subtotal_col_header_key not in data_map:
                continue
            # 需要区分拼接在上面还是下面
            if self.chart_model.subtotal_config.col_pos == 'head':
                insert_index = len(data_map[subtotal_col_header_key]["new_rows"]) - 1
            else:
                insert_index = len(data_map[subtotal_col_header_key]["new_rows"])
            data_map[subtotal_col_header_key]["new_rows"].insert(insert_index, subtotal_col_data["col_value"])
        return insert_index

    def _need_build_subtotal_col(
        self, data_map: dict, row_index: int, subtotal_cate_fields_headers: list, subtotal_col: dict
    ) -> bool:
        """
        是否需要拼接小计
        :param data_map:
        :param row_index:
        :param subtotal_cate_fields_headers:
        :param subtotal_col:
        :return:
        """
        """
        拼接小计条件
        1. 分类汇总对应的小计存在
        2. 分类汇总对应的小计没有下一条 即 has_next 为 0
        3. 分类汇总对应的当前数据和下一条数据不相等
        4. 分类汇总对应的数据至少有两条，即当前数据和上条数据必须相等
        """
        # 小于3条时无法确定是否要拼接小计
        if not subtotal_col:
            return False
        # 区分显示位置
        if self.chart_model.subtotal_config.col_pos == 'head':
            return self._need_build_subtotal_col_head(data_map, row_index, subtotal_cate_fields_headers, subtotal_col)
        else:
            return self._need_build_subtotal_col_tail(data_map, row_index, subtotal_cate_fields_headers, subtotal_col)

    def _need_build_subtotal_col_head(
        self, data_map: dict, row_index: int, subtotal_cate_fields_headers: list, subtotal_col: dict
    ) -> bool:
        """
        头部拼接小计条件
        1. 分类汇总对应的小计存在
        2. 分类汇总对应的小计没有下一条 即 has_next 为 0
        3. 分类汇总对应的当前数据和上一条数据不相等
        4. 分类汇总对应的数据至少有两条，即当前数据和下条数据必须相等
        """
        pre_cate_values = self._get_subtotal_cate_values(data_map, row_index - 1, subtotal_cate_fields_headers)
        current_cate_values = self._get_subtotal_cate_values(data_map, row_index, subtotal_cate_fields_headers)
        next_cate_values = self._get_subtotal_cate_values(data_map, row_index + 1, subtotal_cate_fields_headers)
        # 如果值个数与拖入的维度个数一致的话则不需要小计：
        if len(current_cate_values) == len(self.chart_model.dims):
            return False
        # 如果有列小计 则进行拼接
        if (
                subtotal_col
                and not subtotal_col["has_prev"]
                and current_cate_values != pre_cate_values
                and (current_cate_values == next_cate_values or not next_cate_values)
        ):
            return True
        return False

    def _need_build_subtotal_col_tail(
        self, data_map: dict, row_index: int, subtotal_cate_fields_headers: list, subtotal_col: dict
    ) -> bool:
        """
        尾部拼接小计条件
        1. 分类汇总对应的小计存在
        2. 分类汇总对应的小计没有下一条 即 has_next 为 0
        3. 分类汇总对应的当前数据和下一条数据不相等
        4. 分类汇总对应的数据至少有两条，即当前数据和上条数据必须相等
        """
        pre_cate_values = self._get_subtotal_cate_values(data_map, row_index - 1, subtotal_cate_fields_headers)
        current_cate_values = self._get_subtotal_cate_values(data_map, row_index, subtotal_cate_fields_headers)
        next_cate_values = self._get_subtotal_cate_values(data_map, row_index + 1, subtotal_cate_fields_headers)
        # 如果值个数与拖入的维度个数一致的话则不需要小计：
        if len(current_cate_values) == len(self.chart_model.dims):
            return False
        # 如果有列小计 则进行拼接
        if (
                subtotal_col
                and not subtotal_col["has_next"]
                and current_cate_values != next_cate_values
                and current_cate_values == pre_cate_values
        ):
            return True
        return False

    def _get_subtotal_cate_values(self, data_map: dict, row_index: int, subtotal_cate_fields_headers: list) -> list:
        """
        获取分类汇总字段对应的值的集合
        """
        subtotal_cate_values = []
        if row_index < 0:
            return subtotal_cate_values
        for subtotal_cate_fields_header in subtotal_cate_fields_headers:
            max_row_index = len(data_map[subtotal_cate_fields_header]["rows"]) - 1
            if row_index > max_row_index:
                return subtotal_cate_values
            subtotal_cate_values.append(data_map[subtotal_cate_fields_header]["rows"][row_index])
        return subtotal_cate_values

    def _build_subtotal_row(
        self,
        data_map: dict,
        row_index: int,
        sibling_headers: list,
        subtotal_row_header_map: dict,
        subtotal_row_map: dict,
    ):
        """
        拼接行小计
        :param data_map:
        :param row_index:
        :param sibling_headers:
        :param subtotal_row_header_map:
        :param subtotal_row_map:
        :return:
        """
        # 如果有header 则代表有行小计
        """"
        sibling_headers 的结构为
        [
            tuple([
               tuple(["field_id", "col_type", "col_value", "source来源"]),
               tuple(["field_id", "col_type", "col_value", "source来源"]),
            ]),
            tuple([
               tuple(["field_id", "col_type", "col_value", "source来源"]),
               tuple(["field_id", "col_type", "col_value", "source来源"]),
            ])
        ]
        """
        if subtotal_row_header_map:
            siblings_header_key = []
            for header_key in sibling_headers:
                siblings_header_key.append(tuple([data_map[header_key]["rows"][row_index], header_key]))
            siblings_header_key = tuple(siblings_header_key)
            subtotal_row_data = subtotal_row_map.get(siblings_header_key, {})
            for header_key, header in subtotal_row_header_map.items():
                subtotal_row_value = subtotal_row_data.get(header_key, {}).get("col_value")
                data_map[header_key]["rows"][row_index] = subtotal_row_value

    def _init_data_and_headers(self):
        """
        初始化数据头
        """
        """
        data_map 的数据结构
        {
            tuple([
                ["field_id", "col_type", "col_value", "source来源"],
                ["field_id", "col_type", "col_value", "source来源"]
            ]) :
            {
                "rows": [],
                "new_rows": [],
                "header": [],
                "header_display_words": []
            }
        }
        """
        data_map = {}
        data_length = len(self.data[0]['rows']) if self.data else 0
        # 列序号表头&数据
        if self.chart_model.serial_number_config.is_show:
            serial_rows = [self.start_serial_number + number for number in range(data_length)]
            serial_header = [
                {
                    "alias": self.chart_model.serial_number_config.display_name,
                    "alias_name": None,
                    "col_name": None,
                    "col_type": None,
                    "col_value": None,
                    "dataset_id": None,
                    "field_id": None,
                }
            ]
            self._init_data_col(data_map, self._get_serial_number_header_key(), serial_rows, [], serial_header)
        # 普通表头&数据
        for col in self.data:
            header_key = self._generate_header_tuple(col["header"])
            self._init_data_col(data_map, header_key, col["rows"], [], col["header"])
        return data_map

    def _add_subtotal_row_data_and_headers(self, data_map: dict, subtotal_row_header_map: dict):
        # 行小计表头&初始化数据
        data_length = len(self.data[0]['rows']) if self.data else 0
        for header_key, subtotal_row_header in subtotal_row_header_map.items():
            init_rows = [None] * data_length
            self._init_data_col(data_map, header_key, init_rows, [], subtotal_row_header)

    def _init_data_col(self, data_map: dict, header_key: tuple, rows: list, header_display_words: list, header: list):
        data_map[header_key] = {
            "rows": rows,
            "new_rows": [],
            "header_display_words": header_display_words,
            "header": header,
            "group_name": self._get_header_group_name(header, header_key),
        }

    def _get_serial_number_header_key(self) -> tuple:
        """
        获取列序号的headr_key
        列序号的header比较特殊 field_id  col_type col_value 都为None
        :return:
        """
        return tuple([tuple([None, None, None, DownloadHeaderDataSource.SerialNumber.value])])

    def _get_complete_subtotal_data(self, data_map: dict) -> [dict, list, dict, dict, list, dict]:
        """
        获取完整的小计数据
        :return:
        """
        # subtotal_row_header_map 行小计头
        # subtotal_row_map 原始数据的行小计
        # subtotal_col_map_of_row 列小计的行小计
        # summary_map_of_row 总计部分的行小计
        subtotal_row_header_map, sibling_headers, subtotal_row_map, subtotal_col_map_of_row, summary_map_of_row = (
            self._get_subtotal_row_data_map()
        )
        # 拼接列小计的行小计部分, 需要为列小计部分添加行小计的头
        # subtotal_col_map  列小计
        # subtotal_cate_fields 列小计分类汇总
        subtotal_col_map, subtotal_cate_fields = self._get_subtotal_col_data(data_map)
        if subtotal_col_map_of_row:
            empty_subtotal_row_data = {}
            for subtotal_row_header_key, subtotal_row_header in subtotal_row_header_map.items():
                empty_subtotal_row_data[subtotal_row_header_key] = {"col_value": None, "header": subtotal_row_header}
            for cate_fields_header_key, col_data in subtotal_col_map.items():
                # 增加行小计数据及头
                subtotal_row_data = subtotal_col_map_of_row.get(cate_fields_header_key)
                if subtotal_row_data is None:
                    subtotal_row_data = empty_subtotal_row_data
                col_data.update(subtotal_row_data)
        # 拼接列总计的行总计部分
        subtotal_summary_map = self._get_subtotal_summary_map(data_map)
        if summary_map_of_row and subtotal_summary_map:
            subtotal_summary_map.update(summary_map_of_row)
        return (
            subtotal_row_header_map,
            sibling_headers,
            subtotal_row_map,
            subtotal_col_map,
            subtotal_cate_fields,
            subtotal_summary_map,
        )

    def _get_subtotal_col_data(self, data_map: dict) -> [dict, list]:
        """
        获取列小计的数据（该方法获取并非完整，只包含列小计部分，列小计的行小计部分需要在_get_complete_subtotal_data方法中组装）
        :return:
        """
        subtotal_col_map = {}
        subtotal_cates = set()
        """
        subtotal_col_map 的数据格式为:
        {
            # 根据cate_fields映射的header作为key
            tuple(
                [
                    tuple([
                        "col_value",
                        tuple(["field_id 字段ID" ,"col_type 字段类型", "col_value header的值", "source来源"]),
                        tuple(["field_id 字段ID" ,"col_type 字段类型","col_value header的值", "source来源"])
                    ]),
                    tuple([
                        "col_value",
                        tuple(["field_id 字段ID" ,"col_type 字段类型","col_value header的值", "source来源"]),
                        tuple(["field_id 字段ID" ,"col_type 字段类型","col_value header的值", "source来源"])
                    ])
                ]
            ) : {
                # 根据header作为key
                tuple(
                    [
                        tuple(["field_id", "col_type", "col_value", "source来源"]),
                        tuple(["field_id", "col_type", "col_value", "source来源"]),
                    ]
                ) : {
                    "col_value": "值",
                    "header": [
                        {
                            "alias": "前端展示名称",
                            "alias_name": "后端别名",
                            "col_name": "字段名",
                            "col_type": "字段类型",
                            "col_value": "值",
                            "field_id": "字段ID",
                            "dataset_id": "数据集ID",
                        }
                    ]
                },
                "has_next": 0
            }
        }
        subtotal_cate_fields 数据结构
        [
           tuple([
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
            ]),
            tuple([
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
                tuple([
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                    tuple(["field_id", "col_type", "col_value", "source来源other/subtotal_row/serial_number"]),
                ]),
            ])
        ]
        """
        subtotal_cate_fields = []
        data_header_map = {header_key: data["header"] for header_key, data in data_map.items()}
        for row in self.subtotal.get("subtotal_col").get("rows"):
            cate_fields_map = {}
            last_cate_field_id = None
            for cate_field in row.get("cate_fields"):
                last_cate_field_id = cate_field["field_id"]
                cate_fields_map[cate_field["field_id"]] = None
            subtotal_cates.add(tuple(list(cate_fields_map.keys())))
            col_map = {self._generate_header_tuple(col["header"]): copy.deepcopy(col) for col in row["cols"]}
            col_map["has_next"] = row.get("has_next", 0)
            col_map["has_prev"] = row.get("has_prev", 0)
            self._op_subtotal_cols_of_row(cate_fields_map, col_map, data_header_map, last_cate_field_id)
            # 创建分类汇总数据
            subtotal_cate_field = tuple(self._generate_cate_fields(cate_fields_map, subtotal_cates))
            if subtotal_cate_field not in subtotal_cate_fields:
                subtotal_cate_fields.append(subtotal_cate_field)
            # 创建列小计的字典
            subtotal_col_map[
                tuple(
                    [
                        tuple([col_data["col_value"], self._generate_header_tuple(col_data["header"])])
                        for field_id, col_data in cate_fields_map.items()
                    ]
                )
            ] = col_map
        return subtotal_col_map, list(subtotal_cate_fields)

    def _op_subtotal_cols_of_row(
        self, cate_fields_map: dict, col_map: dict, data_header_map: dict, last_cate_field_id: str
    ):
        none_before = False
        for header_key, header in data_header_map.items():
            col = col_map.get(header_key)
            # 原始数据中的表头
            if not col:
                if self.chart_model.chart_code == 'analysis_table' and not none_before:
                    col = {"col_value": self.chart_model.subtotal_config.col_alias, "header": header}
                    none_before = True
                else:
                    col = {"col_value": None, "header": header}
                col_map[header_key] = col
            first_header_item = col["header"][0]
            if first_header_item["col_type"] == ColTypes.Dim.value and first_header_item["field_id"] in cate_fields_map:
                cate_fields_map[first_header_item["field_id"]] = {
                    "col_value": col["col_value"],
                    "header": col["header"],
                }
            if (
                first_header_item["field_id"] == last_cate_field_id
                and first_header_item["col_type"] == ColTypes.Dim.value
                and not self.chart_model.chart_code == 'analysis_table'
            ):
                col["col_value"] = self.chart_model.subtotal_config.col_alias

    def _generate_cate_fields(self, cate_fields_map: dict, subtotal_cates: set) -> list:
        """
        生成分类汇总分组数据
        :param cate_fields_map:
        :param subtotal_cates:
        :return:
        """
        """
        cate_fields_map 数据结构
        {
            "field_id": {
                "col_value": "",
                "header": [
                    {
                        "alias": "前端展示名",
                        "alias_name": "后端别名",
                        "col_name": "字段名",
                        "col_type": "字段类型",
                        "col_value": "表头值",
                        "dataset_id": "数据集ID",
                        "field_id": "字段ID",
                    }
                ]
            }
        }
        """
        subtotal_cate_fields = []
        for cates in subtotal_cates:
            for field_id in cates:
                header = cate_fields_map.get(field_id, {}).get("header")
                if not header:
                    continue
                header_data = self._generate_header_tuple(header)
                if header_data not in subtotal_cate_fields:
                    subtotal_cate_fields.append(header_data)
        return subtotal_cate_fields

    def _get_subtotal_summary_map(self, data_map: dict) -> dict:
        """
        获取列总计部分，该总计未包含行总计部分，需要在方法_get_complete_subtotal_data进行组装
        :return:
        """
        summary = self.subtotal.get("subtotal_col").get("summary")
        """
        summary_map 的数据结构
        {
            # 根据header作为key
            tuple(
                [
                    tuple(["field_id", "col_type", "col_value", "source来源"])
                ]
            ) : {
                "col_value": "值",
                "header": [
                    {
                        "alias": "前端展示名称",
                        "alias_name": "后端别名",
                        "col_name": "字段名",
                        "col_type": "字段类型",
                        "col_value": "值",
                        "field_id": "字段ID",
                        "dataset_id": "数据集ID",
                    }
                ]
            }
        }
        """
        summary_map = {}
        if not summary or not summary.get("cols"):
            return summary_map
        summary_value_map = {self._generate_header_tuple(col["header"]): col["col_value"] for col in summary["cols"]}
        for header_key, data in data_map.items():
            summary_map[header_key] = {"col_value": summary_value_map.get(header_key), "header": data["header"]}
        return summary_map

    def _get_subtotal_row_data_map(self) -> [dict, list, dict, dict, dict]:
        subtotal_row_header_map, subtotal_row_map, subtotal_col_map, summary_map = {}, {}, {}, {}
        """
        subtotal_row_header_map的数据格式为
        {
            tuple(
                [
                    tuple(["field_id", "col_type", "value", "source来源"])
                ]
            ) : {
                [
                    {
                            "alias": "前端展示名称",
                            "alias_name": "后端别名",
                            "col_name": "字段名",
                            "col_type": "字段类型",
                            "col_value": "值",
                            "field_id": "字段ID",
                            "dataset_id": "数据集ID",
                        }
                ]
            }
        }
        subtotal_row_map的数据格式为(原始数据的行小计)
        {
            #以sublings 的header和value为key
            tuple(
                    [
                        tuple([
                                "col_value",
                                tuple(["field_id", "col_type", "value", "source来源"]),
                                tuple(["field_id", "col_type", "value", "source来源"])
                        ]),
                    ]
                ) : {
                # 根据header作为key
                tuple(
                    [
                        tuple(["field_id", "col_type", "col_value", "source来源"])
                    ]
                ) : {
                    "col_value": "值",
                    "header": [
                        {
                            "alias": "前端展示名称",
                            "alias_name": "后端别名",
                            "col_name": "字段名",
                            "col_type": "字段类型",
                            "col_value": "值",
                            "field_id": "字段ID",
                            "dataset_id": "数据集ID",
                        }
                    ]
                }
            }
        }
        summary_map的数据格式为
        {
            # 根据header作为key
            tuple(
                [
                    tuple(["field_id", "col_type", "col_value", "source来源"])
                ]
            ) : {
                    "col_value": "值",
                    "header": [
                        {
                            "alias": "前端展示名称",
                            "alias_name": "后端别名",
                            "col_name": "字段名",
                            "col_type": "字段类型",
                            "col_value": "值",
                            "field_id": "字段ID",
                            "dataset_id": "数据集ID",
                        }
                    ]
                }
        }
        subtotal_col_map的数据格式为(列小计的行小计)
        {
            #以sublings 的header及col_value作为key
            tuple(
                tuple([
                    tuple([
                        "col_value",
                        tuple(["field_id", "col_type", "value", "source来源"]),
                        tuple(["field_id", "col_type", "value", "source来源"])
                    ])
                ]),
                tuple([
                    tuple([
                        "col_value",
                        tuple(["field_id", "col_type", "value", "source来源"]),
                        tuple(["field_id", "col_type", "value", "source来源"])
                    ])
                ])
            ) : {
                # 根据header作为key
                tuple(
                    [
                        tuple(["field_id", "col_type", "col_value", "source来源"])
                    ]
                ) : {
                    "col_value": "值",
                    "header": [
                        {
                            "alias": "前端展示名称",
                            "alias_name": "后端别名",
                            "col_name": "字段名",
                            "col_type": "字段类型",
                            "col_value": "值",
                            "field_id": "字段ID",
                            "dataset_id": "数据集ID",
                        }
                    ]
                }
            }
        }
        sibling_headers 的结构为
        [
            tuple([
               tuple(["field_id", "col_type", "col_value", "source来源"]),
               tuple(["field_id", "col_type", "col_value", "source来源"]),
            ]),
            tuple([
               tuple(["field_id", "col_type", "col_value", "source来源"]),
               tuple(["field_id", "col_type", "col_value", "source来源"]),
            ])
        ]
        """
        for cols in self.subtotal.get("subtotal_row"):
            header_key = self._generate_header_tuple(cols["header"], DownloadHeaderDataSource.SubtotalRow.value)
            subtotal_row_header_map[header_key] = cols["header"]
            if cols["subtotal_summary_cols"]:
                summary_map[header_key] = {
                    "col_value": cols["subtotal_summary_cols"][0]["col_value"],
                    "header": cols["header"],
                }
            if cols["subtotal_cols"]:
                self._get_siblings_data(cols["subtotal_cols"], cols["header"], header_key, subtotal_col_map)
            self._get_siblings_data(cols["cols"], cols["header"], header_key, subtotal_row_map)
        sibling_headers = self._get_siblings_headers(self.subtotal.get("subtotal_row"))
        return subtotal_row_header_map, sibling_headers, subtotal_row_map, subtotal_col_map, summary_map

    def _get_siblings_headers(self, subtotal_row: list) -> list:
        """
        获取兄弟节点的表头(只取数据第一行即可)
        :param subtotal_row:
        :return:
        """
        if not subtotal_row:
            return []
        first_row = subtotal_row[0]["cols"][0]["siblings"] if subtotal_row else []
        if not first_row:
            return []
        return [self._generate_header_tuple(col["header"]) for col in first_row]

    def _get_siblings_data(self, cols_list: list, cols_header: list, cols_header_key: tuple, col_map: dict):
        for subtotal_col in cols_list:
            siblings_key = []
            for sibling in subtotal_col["siblings"]:
                items = tuple([sibling["col_value"], self._generate_header_tuple(sibling["header"])])
                siblings_key.append(items)
            siblings_key = tuple(siblings_key)
            if siblings_key not in col_map:
                col_map[siblings_key] = {}
            col_map[siblings_key][cols_header_key] = {"col_value": subtotal_col["col_value"], "header": cols_header}

    def _generate_header_tuple(
        self, header: list, header_data_source: str = DownloadHeaderDataSource.Other.value
    ) -> tuple:
        return tuple(
            [tuple([item["field_id"], item["col_type"], item["col_value"], header_data_source]) for item in header]
        )
