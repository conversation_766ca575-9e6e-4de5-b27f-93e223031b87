#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/2/6 11:26
# <AUTHOR> caoxl
# @File     : chart_api_download_service.py
# pylint:disable=R0201,W1202
import json
import logging

from base import repository
from dashboard_chart.services.download import download_utils
from data_source.models import DataSourceModel, APIConnStrModel
from dmplib.hug import g
from dmplib.utils.crypt import AESCrypt
from dmplib.utils.errors import UserError
from dashboard_chart.models import ChartDownloadModel, ChartPaginationModel
from dashboard_chart.models import ChartDataModel as NewChartDataModel
from base.enums import DatasetType, TableDownloadFrom
from dashboard_chart.services import chart_service, released_dashboard_service
from components.message_queue import RabbitMQ
from dmplib import config
from dashboard_chart.services.download.chart_download_common_service import ChartDownloadCommon
from dmplib.utils.strings import seq_id
from message.services import message_service
from celery_app.celery import get_task_id
from message.models import MessageModel
from datetime import datetime

logger = logging.getLogger(__name__)


def generate_download_task(model, row_number: int):
    """
    生成下载任务
    :param model : ChartDataModel实例:
    :return:
    """
    chart_data = (
        repository.get_data('dashboard_chart', {'id': model.id, 'dashboard_id': model.dashboard_id}, ['source']) or {}
    )
    if not chart_data.get("source"):
        return False, True, '', {}
    # 区分是否为api数据集
    dataset = repository.get_data("dataset", {"id": chart_data.get("source")}, fields=["type", "content"]) or {}
    if dataset.get("type") != DatasetType.Api.value:
        return False, True, '', {}
    query_token = get_query_token_by_content(dataset.get("content"))
    msg, result = async_download_chart_data_go(model, query_token, is_number=row_number)
    return True, True, msg, result


def async_download_chart_data_go(model, query_token, is_number=0):
    """
    使用dmp-proc导出数据
    :param model: ChartDataModel实例
    :param query_token: ChartDataModel实例
    :param is_number: 是否开启序号（默认否）
    :return:
    """
    # dashboard name
    dashboard_data = repository.get_data('dashboard', {'id': model.dashboard_id}, ['name'])
    if not dashboard_data:
        logger.info(msg="查询不到对应报告数据，报告id：{id}".format(id=model.dashboard_id), exc_info=True)
    dashboard_name = dashboard_data.get('name', '报告')
    # chart name
    chart_data = repository.get_data('dashboard_chart', {'id': model.id, 'dashboard_id': model.dashboard_id}, ['name'])
    if not chart_data:
        logger.info(msg="查询不到对应单图数据，单图id：{id}".format(id=model.id), exc_info=True)
        return '', {}
    chart_name = chart_data.get('name', '图表')
    filename = '{dashboard_name}_{chart_name}'.format(dashboard_name=dashboard_name, chart_name=chart_name)
    filename = download_utils.get_valid_filename(filename)

    # message
    _message = {
        'source_id': filename + '_' + datetime.now().strftime("%Y%m%d%H%M%S"),
        'user_id': g.userid,
        'source': '通知',
        'type': '个人消息',
        'title': download_utils.generate_message_title(
            msg_type='generate', filename=filename, download_from=model.download_from
        ),
        'url': '',
    }
    message_service.message_add(MessageModel(**_message))

    # 外部user_id存入下载记录表中，用于返回给云客数据
    external_user_id = None
    if hasattr(g, 'external_params') and g.external_params:
        external_user_id = g.external_params.get('user_id')

    # download task
    download_id = seq_id()
    download_model = ChartDownloadModel(
        download_id=download_id,
        dashboard_id=model.dashboard_id,
        dashboard_chart_id=model.id,
        download_url='',
        external_user_id=external_user_id,
    )
    data = download_model.get_dict(
        ['download_id', 'dashboard_id', 'dashboard_chart_id', 'download_url', 'external_user_id']
    )
    repository.add_data('dashboard_chart_download_task', data)

    # celery task
    task_id = get_task_id('dashboard_chart_data', 'chart_download', g.code, download_id)
    repository.update_data('dashboard_chart_download_task', {'status': 1}, {'download_id': download_id})
    if model.subtotal_display:
        model.subtotal_display = model.subtotal_display.get_dict()
    dumped_chart_params = json.dumps(model.get_dict())

    try:
        # 新的下载
        message = {
            "task_id": task_id,
            "filename": filename,
            "download_id": download_id,
            "user_id": g.userid,
            "query_token": query_token,
            "is_number": is_number,
        }
        download_service = DownloadChart(dumped_chart_params, message)
        download_service.run()
    # UserError 不写logger.error
    except UserError as e:
        logger.info("执行{task_id}任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()), exc_info=True)
        raise
    except Exception as e:
        logger.info("执行{task_id}任务错误，错误内容：{e}".format(task_id=task_id, e=e.__str__()), exc_info=True)
        repository.update_data("dashboard_chart_download_task", {"status": 3}, {"download_id": download_id})
        _message = {
            'source_id': '',
            'source': '通知',
            'user_id': g.userid,
            'type': '个人消息',
            'title': download_utils.generate_message_title(
                msg_type='fail', filename=filename, download_from=model.download_from
            ),
            'url': '',
        }
        message_service.message_add(MessageModel(**_message))

    result = dict()
    result['download_id'] = download_model.download_id
    return '', result


def get_query_token_by_content(content):
    try:
        content = json.loads(content)
    except:
        raise UserError(message='解析content错误')
    data_source_id = content.get("data_source_id")
    if not data_source_id:
        raise UserError(message='没有找到data_source_id')
    result = repository.get_data(
        "data_source",
        {"id": data_source_id},
        fields=["id", "name", "code", "description", "type", "conn_str", "is_buildin", "icon"],
    )
    result["conn_str"] = json.loads(result.get("conn_str"))
    data_source = DataSourceModel(**result)
    data_source.conn_str_to_model()
    token_str = g.cookie.get('token') if g.cookie.get('token') else 'adfasd'
    access_secret = APIConnStrModel(**{"access_secret": data_source.conn_str.access_secret}).decrypt()
    query_token = AESCrypt(access_secret).encrypt(token_str)
    return query_token


class DownloadChart(ChartDownloadCommon):
    def __init__(self, dumped_chart_params, message):
        """
        使用go查询下载生成表格数据下载，dmp这里需要给go服务提供struct结构
        :param dumped_chart_params:
        :param dict message: 发送消息数据
        （包含：task_id: taskid download_id: 下载id filename: 文件名称 user_id: 用户id 目前用于个人日志记录）
        :return:
        """
        self.dumped_chart_params = dumped_chart_params
        self.message = message

    def run(self):
        download_model = ChartDownloadModel()
        # 下载任务信息
        model = NewChartDataModel(**(json.loads(self.dumped_chart_params)))
        result = self.get_query_struct(download_model, model)
        if result:
            self.send_message_go(model, result, self.message)

    def get_query_struct(self, download_model, model):
        """
        获取查询的struct
        :return:
        """
        page_num = 0
        model.pagination = ChartPaginationModel(**{"page": page_num, "page_size": download_model.limit_per_file})
        query_result = {}
        if model.download_from == TableDownloadFrom.Preview.value:
            query_result = chart_service.get_chart_query_struct(model)
        elif model.download_from == TableDownloadFrom.Released.value:
            query_result = released_dashboard_service.get_chart_query_struct(model)
        if query_result.get("msg_code"):
            raise UserError(message="获取取数结构异常，错误信息: {msg}".format(msg=query_result.get("msg")))
        query_struct = query_result.get("query_structure", {})
        select_fields = query_result.get("select_fields", [])
        subtotal_summary_struct, subtotal_cate_struct = self._get_subtotal_struct(query_result.get("subtotal", {}))

        # 获取表头
        data_header_keys = [select_field.alias or select_field.field for select_field in select_fields]
        show_header_list, match_header_list = self.get_sorted_header_for_excel(model, data_header_keys)
        return {
            "query_struct": query_struct,
            "show_header_list": show_header_list,
            "match_header_list": match_header_list,
            "subtotal": {
                "subtotal_summary_struct": subtotal_summary_struct,
                "subtotal_cate_struct": subtotal_cate_struct,
            },
        }

    @staticmethod
    def send_message_go(model, result, message):
        # 发送给rabbitmq, dmp-proc处理
        # 发送执行消息
        data = {
            'project_code': getattr(g, 'code'),
            'dataset_id': model.dataset_id,
            'task_id': message.get("task_id"),
            'user_id': message.get("user_id"),
            'external_params': getattr(g, 'external_params'),
            'download_id': message.get("download_id"),
            'filename': message.get("filename"),
            'query_struct': result.get("query_struct"),
            'show_header_list': result.get("show_header_list"),
            'match_header_list': result.get("match_header_list"),
            'subtotal': result.get("subtotal"),
            'subtotal_display': model.subtotal_display.get_dict() if model.subtotal_display else "",
            'token': message.get("query_token"),
            'is_number': message.get("is_number"),
        }
        # todo 替换新的queue_name
        queue_name = config.get('RabbitMQ.queue_name_download', 'Download')
        rabbit_mq = RabbitMQ()
        res = rabbit_mq.send_message(
            queue_name, json.dumps(data, ensure_ascii=False), durable=False, headers={"_dmp_message_uuid": seq_id()}
        )
        logger.info("send message. download_id:%s. res:%s", message.get("download_id"), '成功' if not res else '失败')

    def _get_subtotal_struct(self, original_subtotal):
        """
        获取小计的结构
        :param original_subtotal:
        :return:
        """
        subtotal_cate_struct = []
        subtotal_summary_struct = {}
        for item in original_subtotal.get("subtotal_cate", []):
            if item.get("code"):
                # 如果解析有问题跳过
                continue
            subtotal_cate_struct.append(
                {
                    "query_structure": item.get("query_structure"),
                    "subtotal_cate_fields": item.get("subtotal_cate_fields"),
                    "select_fields": item.get("select_fields"),
                    "sql": item.get("sql"),
                }
            )
        original_summary = original_subtotal.get("subtotal_summary", {})
        if original_summary:
            subtotal_summary_struct = {
                "query_structure": original_summary.get("query_structure"),
                "sql": original_summary.get("sql"),
                "select_fields": original_summary.get("select_fields"),
            }
        return subtotal_summary_struct, subtotal_cate_struct
