import os
import copy
import random
import string
import time
import logging
import shutil
import queue
import threading
from billiard.context import Process
from datetime import datetime
import dashboard_chart.services.download.download_utils
from dmplib.hug import g
from dmplib.hug.context import DBContext
from dmplib.redis import conn
from dmplib import config
from base import repository
from base.enums import FlowInstanceStatus, TableDownloadFrom
from base.errors import UserError
from base.global_context_handler import handle_g
from dmplib.hug.globals import _app_ctx_stack
from components.oss import OSSFileProxy
from components.excel_writer import ExcelWriter
from message.models import MessageModel
from message.services import message_service
from dashboard_chart.models import ChartDownloadContext, ChartDataModel, ChartDownloadModel, ChartPaginationModel
from dashboard_chart.services import chart_service, dashboard_service, released_dashboard_service
from dashboard_chart.services.download import (
    chart_column_download_result_transform,
    chart_column_download_result_transform_v2,
)
from components.storage_setting import get_storage_type
from base.enums import DatasetStorageType


logger = logging.getLogger(__name__)


# 报告单图数据多进程下载进程数限制,默认为2,最大不超过cpu的核数
DOWNLOAD_PROCESS_LIMIT = int(config.get('Function.chart_download_process_limit', 2))
if DOWNLOAD_PROCESS_LIMIT > os.cpu_count():
    DOWNLOAD_PROCESS_LIMIT = os.cpu_count()
# 报告单图下载单个文件的记录数据限制， 默认为20000, 必须为DOWNLOAD_LIMIT_PAGE_SIZE的整数倍
DOWNLOAD_LIMIT_PER_FILE = int(config.get('Function.chart_download_limit_per_file', 20000))
# 报告单图下载单个文件的记录数据限制(无小计)， 默认为100000, 必须为DOWNLOAD_LIMIT_PAGE_SIZE的整数倍
DOWNLOAD_NO_SUBTOTAL_LIMIT_PER_FILE = int(config.get('Function.chart_download_no_subtotal_limit_per_file', 1000000))
# 报告单图下载单个进程的分页取数是的单页记录限制， 默认为500,
DOWNLOAD_LIMIT_PAGE_SIZE = int(config.get('Function.chart_download_limit_page_size', 500))
# 报告单图下载单个进程的分页取数是的单页记录限制(无小计)， 默认为100000,
DOWNLOAD_NO_SUBTOTAL_LIMIT_PAGE_SIZE = int(config.get('Function.chart_download_no_subtotal_limit_page_size', 100000))


class NoneExcelFileGeneratedException(Exception):
    pass


class AsyncChartDownloadService:
    def __init__(self, download_context: ChartDownloadContext):
        self.context = download_context
        self.set_global_context()
        # 此处需要获取完整的chart_model以防后面发生逻辑错误
        self.chart_model = self.assign_dashboard_chart_model(ChartDataModel(**self.context.chart_params))
        chart_download_data = {
            'download_id': self.context.download_id,
            'dashboard_id': self.chart_model.dashboard_id,
            'dashboard_chart_id': self.chart_model.id,
            'flow_id': self.context.flow_id,
        }
        self.download_cfg = ChartDownloadModel(**chart_download_data)
        self.download_cfg.limit_per_file = self._get_limit_per_file()
        self.download_cfg.page_size = self._get_page_size()
        self.init()

    @staticmethod
    def assign_dashboard_chart_model(chart_model):
        """
        为chart_model赋值，获取完整的chart_model
        :param chart_model:
        :return:
        """
        if chart_model.download_from == TableDownloadFrom.Preview.value:
            chart_service.assign_dashboard_chart_model(chart_model)
        elif chart_model.download_from == TableDownloadFrom.Released.value:
            released_dashboard_service.assign_release_dashboard_chart_model(chart_model)
        if chart_model.export_subtotal_config:
            export_enable_subtotal_col = chart_model.export_subtotal_config.get('enable_subtotal_col')
            export_enable_subtotal_col_summary = chart_model.export_subtotal_config.get('enable_subtotal_col_summary')
            chart_model.enable_subtotal_col = export_enable_subtotal_col if export_enable_subtotal_col is not None else chart_model.enable_subtotal_col
            chart_model.enable_subtotal_col_summary = export_enable_subtotal_col_summary if export_enable_subtotal_col_summary is not None else chart_model.enable_subtotal_col_summary
        return chart_model

    def _get_page_size(self):
        """
        获取下载page_size 有小计和无小计时不一样
        :return:
        """
        if (
            self.chart_model.enable_subtotal_col
            or self.chart_model.enable_subtotal_col_summary
            or self.chart_model.enable_subtotal_row
            or self.chart_model.enable_subtotal_row_summary
        ):
            return DOWNLOAD_LIMIT_PAGE_SIZE
        return DOWNLOAD_NO_SUBTOTAL_LIMIT_PAGE_SIZE

    def _get_limit_per_file(self):
        """
        有小计时和无小计时每页查询不一致
        :return:
        """
        if (
            self.chart_model.enable_subtotal_col
            or self.chart_model.enable_subtotal_col_summary
            or self.chart_model.enable_subtotal_row
            or self.chart_model.enable_subtotal_row_summary
        ):
            return DOWNLOAD_LIMIT_PER_FILE
        return DOWNLOAD_NO_SUBTOTAL_LIMIT_PER_FILE

    def init(self):
        random_number = ''.join(random.sample(string.digits, 4))
        tmp_download_dir = '/tmp/%s/' % self.context.download_id
        if not os.path.exists(tmp_download_dir):
            os.makedirs(tmp_download_dir)
        self.download_cfg.file_temp_dir = tmp_download_dir
        self.download_cfg.output_zip_filename = f'{self.context.filename}_{random_number}.zip'
        self.download_cfg.output_xlsx_filename = f'{self.context.filename}_{random_number}.xlsx'

    @staticmethod
    def clear_db_connection():
        db_ctx = DBContext.instance(g)
        if db_ctx:
            db_ctx.close_all()
        db_ctx.stash_data = {}

    def set_global_context(self):
        g.code = self.context.tenant_code
        g.account = self.context.account
        g.userid = self.context.userid
        g.external_params = self.context.external_params
        g.cookie = self.context.cookie

    def run(self):
        try:
            logger.info(msg='======== start chart download task, download id: %s ========' % self.context.download_id)
            self.update_flow_status(FlowInstanceStatus.Running.value, is_start=True)
            self.download()
            local_filepath, upload_filename = self.zip_file()
            oss_file_url = self.upload_file_to_oss(local_filepath, upload_filename)
            # 清空连接，解决游标关闭的问题
            self.clear_db_connection()
            self.update_flow_status(FlowInstanceStatus.Successful.value, is_start=False)
            title = dashboard_chart.services.download.download_utils.generate_message_title(
                "successful", self.context.filename, self.chart_model.download_from
            )
            self.send_result_notify_message(title, oss_file_url)
            self.update_download_task_status({"download_url": oss_file_url, "status": 2})
            logger.info(
                msg="======== success process chart download task. download_id: %s ========" % self.context.download_id
            )
        except Exception as e:
            self.clear_db_connection()
            self.update_download_task_status({"status": 3})
            title = dashboard_chart.services.download.download_utils.generate_message_title(
                "fail", self.context.filename, self.chart_model.download_from
            )
            self.send_result_notify_message(title, source='通知')
            self.update_flow_status(FlowInstanceStatus.Failed.value, is_start=False)

            if isinstance(e, (UserError, NoneExcelFileGeneratedException)):
                logger.warning(msg="执行表格下载异步任务错误，download_id: %s" % self.context.download_id, exc_info=True)
            else:
                logger.error(
                    msg="failed to process chart download task, download_id: %s" % self.context.download_id,
                    exc_info=True,
                )

    @staticmethod
    def now_date():
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def update_flow_status(self, status: str, is_start: bool):
        update_field = 'startup_time' if is_start else 'end_time'
        repository.update_data(
            'flow', {'run_status': status, update_field: self.now_date()}, {'id': self.context.flow_id}
        )
        repository.update_data(
            'instance', {'status': status, update_field: self.now_date()}, {'id': self.context.flow_instance_id}
        )

    def update_download_task_status(self, update_data: dict):
        repository.update_data("dashboard_chart_download_task", update_data, {"download_id": self.context.download_id})

    def send_result_notify_message(self, title: str, url: str = '/flow/ops', source: str = '导出数据'):
        message = {
            'source_id': self.context.filename + '_' + datetime.now().strftime("%Y%m%d%H%M%S"),
            'user_id': g.userid,
            'source': source,
            'type': '个人消息',
            'title': title,
            'url': url,
        }
        message_service.message_add(MessageModel(**message))

    def download(self):
        ChartDownloadManager(self).process()

    def zip_file(self):
        """
        文件打包
        :param download_model:
        :param str filename: 文件名称
        :return:
        """
        file_path = self.download_cfg.file_temp_dir
        files = []
        logger.info(msg='output file path: %s' % file_path)
        for f_name in os.listdir(file_path):
            files.append(os.path.join(file_path, f_name))
        logger.info(msg='output files list: %s' % files)
        if not files:
            raise NoneExcelFileGeneratedException

        if len(files) == 1:
            return files[0], self.download_cfg.output_xlsx_filename

        zip_filepath = os.path.join(file_path, self.download_cfg.output_zip_filename)
        z = dashboard_service.ZipFile(zip_filepath, mode="w", basedir=file_path)
        z.addfiles(files)
        z.close()
        return zip_filepath, self.download_cfg.output_zip_filename

    def upload_file_to_oss(self, local_filepath: str, upload_filename: str):
        upload_filepath = os.path.join(self.download_cfg.oss_upload_dir, upload_filename)
        with open(local_filepath, "rb") as fd:
            oss_file_url = OSSFileProxy().upload(fd, file_name=upload_filepath, private=True)
        logger.info(msg='upload file to oss success！url: %s' % upload_filepath)
        shutil.rmtree(self.download_cfg.file_temp_dir)
        logger.info(msg='remove tmp dir file success! %s' % self.download_cfg.file_temp_dir)
        return oss_file_url


class ProcessContext:
    def __init__(self, **kwargs):
        self.page_index = kwargs.get('page_index')
        self.limit_per_file = kwargs.get('limit_per_file')
        self.page_size = kwargs.get("page_size")
        self.chart_model = kwargs.get('chart_model')
        self.file_temp_dir = kwargs.get('file_temp_dir')
        self.filename = kwargs.get('filename')
        self.total_count = kwargs.get('total_count')
        self.is_last_page = False


class ChartDownloadManager:

    # 进程数量，采用进程池处理大数据量的分多个文件处理
    PROCESS_NUM = DOWNLOAD_PROCESS_LIMIT

    def __init__(self, service: AsyncChartDownloadService):
        self.service = service
        self.chart_model = self.service.chart_model
        self.download_cfg = self.service.download_cfg
        self.context = self.service.context
        self.current_page = 0

    def process(self):
        try:
            total_count = 0
            if self.chart_model.download_from == TableDownloadFrom.Preview.value:
                total_count = chart_service.get_total(model=self.chart_model, reassign_model=False)
            elif self.chart_model.download_from == TableDownloadFrom.Released.value:
                total_count = released_dashboard_service.get_released_chart_total(
                    model=self.chart_model, reassign_model=False
                )
            logger.info(msg='记录总数: %s' % total_count)
            if total_count < 1:
                # 获取表头
                header = [[item.get('alias_name') for item in self.chart_model.column_display]]
                excel_file_path = '{}.xlsx'.format(self.context.filename)
                excel_file_path = os.path.join(self.download_cfg.file_temp_dir, excel_file_path)
                excel_writer = ExcelWriter(excel_file_path, strings_to_urls=False, strings_to_formulas=False)
                excel_writer.write(header)
                excel_writer.save()
                return

            if total_count > 2000 and get_storage_type(g.code) == DatasetStorageType.DatasetStorageOfLocal.value:
                # 如果是本地存储，且数量达到2000，加大请求数量，减少分页次数
                self.download_cfg.page_size = 5000
                self.download_cfg.limit_per_file = 200000

            divisor, remainder = divmod(total_count, self.download_cfg.limit_per_file)
            task_count = divisor if remainder == 0 else divisor + 1
            logger.info(msg='文件总数：%s' % task_count)
            logger.info(msg='每页记录数：%s' % self.download_cfg.page_size)
            process_num = task_count if task_count < self.PROCESS_NUM else self.PROCESS_NUM

            key = 'dashboard_chart_download:%s:current_page' % self.download_cfg.download_id
            conn().set(key, self.current_page, 24 * 60 * 60)
            process = []
            context = ProcessContext(
                limit_per_file=self.download_cfg.limit_per_file,
                chart_model=self.chart_model,
                file_temp_dir=self.download_cfg.file_temp_dir,
                page_size=self.download_cfg.page_size,
                filename=self.context.filename,
                total_count=total_count,
            )

            end_page = self.current_page + task_count
            if process_num < 2:
                logger.info(msg='开始主进程下载......')
                multi_process_control(copy.deepcopy(context), end_page, key, _app_ctx_stack.top)
            else:
                logger.info(msg='开始多进程下载，进程数量: %s......' % process_num)
                for _ in range(process_num):
                    p = Process(
                        target=multi_process_control, args=(copy.deepcopy(context), end_page, key, _app_ctx_stack.top)
                    )
                    p.start()
                    process.append(p)

                for p in process:
                    p.join()

            logger.info(msg='****** 成功生成全部文件 ******')
        except:
            logger.error('下载发生异常. 报错信息:', exc_info=True)
            raise


def multi_process_control(process_context, end_page, key, parent_thread_g):
    while True:
        page_index = conn().incr(key)
        if page_index > end_page:
            break

        process_context.page_index = page_index
        if page_index == end_page:
            process_context.is_last_page = True
        if not process_single_file_task(process_context, parent_thread_g):
            break


@handle_g
# pylint: disable=unused-argument
def process_single_file_task(process_context: ProcessContext, parent_thread_g=None):
    return ChartDownloadHandler(process_context).process()


class ChartDownloadHandler:
    def __init__(self, context: ProcessContext):
        self.context = context
        # 单次分页取数的记录数
        self.EACH_PAGE_SIZE = self.context.page_size
        excel_filename = '%s_%s.xlsx' % (self.context.filename, self.format_index_number(self.context.page_index))
        self.excel_filepath = os.path.join(self.context.file_temp_dir, excel_filename)
        self.done = False
        self.result_queue = queue.Queue()
        self.finish_signal = object()
        divisor, remainder = divmod(self.context.total_count, self.EACH_PAGE_SIZE)
        self.global_end_page_num = divisor if remainder == 0 else divisor + 1
        self.offset_serial_number = 0

    @staticmethod
    def format_index_number(index: int):
        if index < 10:
            return '00%d' % index
        elif index < 100:
            return '0%s' % index
        return str(index)

    @staticmethod
    def format_time(time_delta: float):
        return round(time_delta, 3)

    def transform_data_and_write_excel_thread_v2(
        self,
    ):
        is_write_head = False
        excel_writer = ExcelWriter(self.excel_filepath, strings_to_urls=False, strings_to_formulas=False)
        while True:
            chart_result = self.result_queue.get()
            if not chart_result or chart_result is self.finish_signal:
                break
            start = time.time()
            is_last_page = self.offset_serial_number >= ((self.global_end_page_num - 1) * self.EACH_PAGE_SIZE + 1)
            transform = chart_column_download_result_transform_v2.ChartResultTransformV2(
                self.context.chart_model, chart_result, self.offset_serial_number, is_last_page
            )
            headers, records = transform.process()
            middle = time.time()
            logger.info(msg='解析耗时: %s s' % self.format_time(middle - start))
            if not is_write_head:
                excel_writer.write(headers)
                is_write_head = True
            excel_writer.write(records)
            logger.info(msg='写EXCEL耗时: %s s' % self.format_time(time.time() - middle))
            self.offset_serial_number += self.EACH_PAGE_SIZE

            del chart_result, transform
        excel_writer.save()

    def transform_data_and_write_excel_thread(self):
        is_write_head = False
        is_write_col_summary = False
        col_summary = None
        excel_writer = ExcelWriter(self.excel_filepath, strings_to_urls=False, strings_to_formulas=False)
        while True:
            chart_result = self.result_queue.get()
            if not chart_result or chart_result is self.finish_signal:
                break

            start = time.time()
            transform = chart_column_download_result_transform.ChartResultTransform(
                self.context.chart_model, chart_result, self.offset_serial_number
            )
            headers, records, tails, data_count = transform.process()
            middle = time.time()
            logger.info(msg='解析耗时: %s s' % self.format_time(middle - start))
            if col_summary is None:
                col_summary = tails
            if not is_write_head:
                excel_writer.write(headers)
                is_write_head = True
            if (
                not is_write_col_summary
                and col_summary
                and any(col_summary)
                and self.context.chart_model.subtotal_config.col_summary_pos == 'head'
            ):
                excel_writer.write(col_summary)
            excel_writer.write(records)
            logger.info(msg='写EXCEL耗时: %s s' % self.format_time(time.time() - middle))
            self.offset_serial_number += self.EACH_PAGE_SIZE

            del chart_result, transform

        if (
            self.context.chart_model.subtotal_config.col_summary_pos == 'tail'
            and col_summary
            and any(col_summary)
            and self.context.is_last_page
        ):
            excel_writer.write(col_summary)
        excel_writer.save()

    def process(self):
        try:
            logger.info(msg='正在生成第【%s】个文件......' % self.context.page_index)
            write_excel_thread = threading.Thread(target=self.transform_data_and_write_excel_thread_v2)
            write_excel_thread.start()
            # 标志是否还有数据需要处理
            has_next = True
            logger.info(msg='单文件记录数限制: %s, 分页下载记录数限制: %s' % (self.context.limit_per_file, self.EACH_PAGE_SIZE))
            offset_page_num = (self.context.page_index - 1) * self.context.limit_per_file // self.EACH_PAGE_SIZE + 1
            end_page_num = offset_page_num + self.context.limit_per_file // self.EACH_PAGE_SIZE - 1
            if end_page_num > self.global_end_page_num:
                end_page_num = self.global_end_page_num
            logger.info(msg='开始页码: %s, 结束页码: %s' % (offset_page_num, end_page_num))
            self.offset_serial_number = (offset_page_num - 1) * self.EACH_PAGE_SIZE + 1
            for page in range(offset_page_num, end_page_num + 1):
                pagination = ChartPaginationModel(page=page, page_size=self.EACH_PAGE_SIZE)
                self.context.chart_model.pagination = pagination
                logger.info(msg='正在取数第%s页......' % page)
                start = time.time()
                if self.context.chart_model.download_from == TableDownloadFrom.Preview.value:
                    chart_result = chart_service.get_chart_result(model=self.context.chart_model, reassign_model=False)
                elif self.context.chart_model.download_from == TableDownloadFrom.Released.value:
                    chart_result = released_dashboard_service.get_released_chart_result(
                        model=self.context.chart_model, reassign_model=False
                    )
                else:
                    logger.error(msg='ERROR异常，生成第【%s】个文件失败，无效的下载来源' % self.context.page_index, exc_info=True)
                    has_next = False
                    break
                logger.info(msg='取数第%s页耗时: %s s' % (page, self.format_time(time.time() - start)))

                if not chart_result or not chart_result.get('data'):
                    logger.info(msg='-----取数结束-----,page: %s, page_size: %s' % (page, self.EACH_PAGE_SIZE))
                    has_next = False
                    break

                # 将单图取数的结果转换为需要写入excel的二维数据(行列单元格)
                self.result_queue.put(chart_result)

            self.result_queue.put(self.finish_signal)
            write_excel_thread.join()
            logger.info(msg='成功生成第【%s】个文件, 是否有下个文件: %s' % (self.context.page_index, has_next))
            return has_next

        except:
            logger.error(msg='ERROR异常，生成第【%s】个文件失败，报错信息：' % self.context.page_index, exc_info=True)
        finally:
            logger.info(msg='生成第 【%s】 个文件程序结束！' % self.context.page_index)
