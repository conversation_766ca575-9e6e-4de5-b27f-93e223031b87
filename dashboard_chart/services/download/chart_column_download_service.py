# pylint:disable=R0201,W0613,<PERSON><PERSON>23
import json
import logging
from datetime import datetime

import app_celery
from dashboard_chart.services.download import download_utils
from dmplib.utils.strings import seq_id
from dmplib.hug import g
from dmplib.saas.project import set_correct_project_code
from base.enums import FlowType, FlowInstanceStatus, TableDownloadFrom
from base.errors import UserError
from base import repository
from base.models import BaseModelEncoder
from message.models import MessageModel
from message.services import message_service
from dashboard_chart.models import ChartDataModel, ChartDownloadModel, ChartDownloadContext
from dashboard_chart.services import dashboard_service
from flow.services import flow_service, flow_instance_service
from flow.models import FlowModel, FlowInstanceModel


logger = logging.getLogger(__name__)


def generate_download_task(model):
    """
    生成下载任务
    :param model:
    :return:
    """
    download_id = ChartDownloadControlService(model).run()
    return True, '成功', {'download_id': download_id}


def dashboard_chart_data_download(token, request_data):
    """
    报告单图表格下载
    :param token:
    :param request_data:
    :return:
    """
    if not request_data:
        raise UserError(message='非法请求参数')

    check_flag, errmsg, data = dashboard_service.check_token(token)
    if not check_flag:
        return check_flag, errmsg, None

    g.cookie = {'token': token}
    g.account = data.get("account")
    g.userid = data.get("id")
    code = data.get('tenant_code') if data.get('tenant_code') else request_data.get('code')
    set_correct_project_code(code)
    if not g.code:
        raise UserError(message='租户代码不能为空')

    # 判断是否能下载
    # dmp 登录，默认可以下载(云客使用报告地址跳转的参数中带有dashboard_id)
    flag = dashboard_service.check_permission(request_data.get("dashboard_id"), data, "download")
    if not flag:
        return False, "没有下载权限", {}

    # 将外部参数放入共享变量中
    g.external_params = data.get('external_params')
    chart_params_list = request_data.get('chart_params')
    chart_params = chart_params_list[0] if len(chart_params_list) else {}
    model = ChartDataModel(**chart_params)
    # 区分API数据集
    # TODO
    download_id = ChartDownloadControlService(model).run()
    return True, '成功', download_id


class ChartDownloadControlService:
    def __init__(self, chart_model: ChartDataModel):
        self.chart_model = chart_model
        self.chart_id = self.chart_model.id
        self.dashboard_id = self.chart_model.dashboard_id
        self.dashboard_data = {}
        self.chart_data = {}
        # 上下文对象，便于参数的统一传递
        self.context = ChartDownloadContext()
        self.init()
        self.init_context()

    def init(self):
        dashboard_data = repository.get_data('dashboard', {'id': self.dashboard_id})
        if not dashboard_data:
            raise UserError(message='报告不存在, ID: %s' % self.dashboard_id)
        self.dashboard_data = dashboard_data
        self._get_chart_data()

    def _get_chart_data(self):
        # 区分预览页和发布页单图数据, 修复删除预览页单图导致发布页单图无法下载问题
        # 这里要注意如果后续下载进程有引用单图数据self.chart_data, 要注意预览和发布页单图数据格式是有区别的
        # 目前下载没有引用self.chart_data
        if self.chart_model.download_from == TableDownloadFrom.Preview.value:
            self.chart_data = repository.get_data('dashboard_chart', {'id': self.chart_id})
            if not self.chart_data:
                raise UserError(message='预览页单图不存在，ID: %s' % self.chart_id)
        elif self.chart_model.download_from == TableDownloadFrom.Released.value:
            self.chart_data = repository.get_data('dashboard_released_snapshot_chart', {'id': self.chart_id})
            if not self.chart_data:
                raise UserError(message='发布页单图不存在，ID: %s' % self.chart_id)

    def init_context(self):
        self.context.tenant_code = g.code
        self.context.account = g.account
        self.context.cookie = g.cookie
        self.context.userid = g.userid
        self.context.external_params = g.external_params
        self.context.chart_params = self.chart_model.get_dict()
        self.context.filename = self.filename

    @property
    def filename(self):
        if not hasattr(self, '_filename'):
            dashboard_name = self.dashboard_data.get('name', '报告')
            chart_name = self.chart_data.get('name', '图表')
            prefix = '{dashboard_name}_{chart_name}'.format(dashboard_name=dashboard_name, chart_name=chart_name)
            prefix = download_utils.substr_by_byte_length(prefix, 200)
            filename = '{prefix}_{now_time}'.format(prefix=prefix, now_time=datetime.now().strftime("%Y%m%d%H%M%S"))
            setattr(self, '_filename', download_utils.get_valid_filename(filename))
        return getattr(self, '_filename')

    def _set_context(self, context_data: dict):
        for key, value in context_data.items():
            setattr(self.context, key, value)

    def run(self):
        try:
            self.add_flow()
            self.send_start_notify_message()
            self.add_download_task()
            self.async_download()
        except Exception as e:
            raise UserError(message='创建下载任务失败：%s' % str(e))
        return self.context.download_id

    def add_flow(self):
        flow_id = seq_id()
        flow_data = {
            'id': flow_id,
            'name': '表格数据下载',
            'type': FlowType.Download.value,
            'run_status': FlowInstanceStatus.Created.value,
        }
        flow_service.add_flow(FlowModel(**flow_data))
        flow_instance_data = {
            'flow_id': flow_id,
            'name': flow_data['name'],
            'type': flow_data['type'],
            'status': FlowInstanceStatus.Created.value,
        }
        flow_instance_id = flow_instance_service.add_instance(FlowInstanceModel(**flow_instance_data))
        self._set_context({'flow_id': flow_id, 'flow_instance_id': flow_instance_id})

    def send_start_notify_message(self):

        title = download_utils.generate_message_title("generate", self.filename, self.chart_model.download_from)
        message = {
            'source_id': self.filename + '_' + datetime.now().strftime("%Y%m%d%H%M%S"),
            'user_id': g.userid,
            'source': '通知',
            'type': '个人消息',
            'title': title,
            'url': '/flow/ops',
        }
        message_service.message_add(MessageModel(**message))

    def add_download_task(self):
        # 外部user_id存入下载记录表中，用于返回给云客数据
        external_user_id = None
        if hasattr(g, 'external_params') and g.external_params:
            external_user_id = g.external_params.get('user_id')

        self.context.chart_params = json.dumps(self.context.chart_params, cls=BaseModelEncoder)

        # download task
        download_id = seq_id()
        download_model = ChartDownloadModel(
            download_id=download_id,
            dashboard_id=self.dashboard_id,
            dashboard_chart_id=self.chart_id,
            flow_id=self.context.flow_id,
            download_url='',
            external_user_id=external_user_id,
            status=1,
        )
        data = download_model.get_dict(
            [
                'download_id',
                'dashboard_id',
                'dashboard_chart_id',
                'flow_id',
                'download_url',
                'external_user_id',
                'status'
            ]
        )
        self._set_context({'download_id': download_id})
        data['params'] = json.dumps(self.context.get_dict())
        repository.add_data('dashboard_chart_download_task', data)

    def async_download(self):
        # self.context.chart_params = json.dumps(self.context.chart_params, cls=BaseModelEncoder)
        # app_celery.async_download_chart_data_new.apply_async(
        #     kwargs={'download_context': self.context.get_dict()}, queue='download'
        # )
        snap_id = getattr(g, 'snap_id', None)
        app_celery.async_download_chart_data_new.apply_async(
            kwargs={'download_id': self.context.download_id, 'project_code': self.context.tenant_code, 'snap_id': snap_id}, queue='download'
        )
