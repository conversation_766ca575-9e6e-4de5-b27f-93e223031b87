#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
authority service

"""

# ---------------- 标准模块 ----------------
import json
import io

import binascii
import logging

# pylint:disable=E0401
import falcon
import jwt

# pylint:disable=E0401
from falcon import request_helpers
from hug.authentication import authenticator
import urllib.parse

# ---------------- 业务模块 ----------------
from jwt import DecodeError, ExpiredSignatureError

from base.enums import DashboardTypeAccessReleased, DashboardType
from components.grayscale import check_grayscale
from dashboard_chart.services import dashboard_service
from dmplib.redis import conn as redis_conn
from dmplib import config
from dmplib.hug import g, debugger
from dmplib.utils.errors import UserError, UserErrorWithExtraInfo
from dmplib.utils.jwt_login import LoginToken
from open_api.services import screen_auth_service
from rbac.services import data_permissions
from . import dashboard_cache_service
from . import metadata_service
from dashboard_chart.utils.decorators import auto_no_cache
from app_menu.services import external_service as app_menu_external_service
from app_menu.services import application_auth_service
from user.services import user_service
from dmplib.saas.errors import EmptyProjectCodeError
from dmplib.saas.project import set_correct_project_code
from user.services.reporting_sso_service import set_cookies_samesite_property
from dmplib.hug.application import auth_cookies_replacement
from base.enums import TokenAuthFrom, ApplicationTypeAccessReleasedSourceStr, ApplicationType
from components.mip_auth_application import ApplicationDashboardOperation

logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)


def verify_user_role(token, code, dashboard, token_data=None):
    extra_error_info = _get_error_info(dashboard)
    if not token:
        raise UserErrorWithExtraInfo(code=401, message="请先登录", extra_info=extra_error_info)
    data = jwt.decode(token, config.get('JWT.secret'), algorithms="HS256")
    if data and data.get('id') and data.get('email'):
        token = LoginToken().create_token(data.get('id'), extra=data, code=code)
    user = LoginToken().verify_token(token)
    user = token_data if not user else user
    if not user or not user.get('code'):
        raise UserErrorWithExtraInfo(code=401, message="请先登录", extra_info=extra_error_info)
    if code != user.get('code'):
        raise UserErrorWithExtraInfo(code=408, message="访问受限（组织机构代码不一致），请联系管理员", extra_info=extra_error_info)
    g.userid = user.get('id')
    g.account = user.get('account')
    # TODO(Judy): 自助分析数据权限暂不检查
    if dashboard.get('application_type') in [1, '1']:
        return

    # erp暂不检查
    if dashboard.get('need_ignore') == 1:
        return

    root_id = dashboard.get("id")
    if not dashboard.get("is_multiple_screen"):
        root_id = get_root_dashboard_id(dashboard).get('id')
    data_type = get_dashboard_data_type(dashboard)
    if not data_permissions.check_has_data_permission(data_type, 'view', data_id=root_id):
        raise UserError(code=407, message="您所在的角色访问受限，请联系管理员")


def get_dashboard_data_type(dashboard):
    """
    获取校验报表的权限类型
    """
    if dashboard.get('application_type') == ApplicationType.LargeScreen.value:
        return 'large_screen'
    elif dashboard.get("is_multiple_screen"):
        return 'multiple_screen'
    else:
        return 'dashboard'


def get_root_dashboard_id(dashboard):
    from dashboard_chart.services.dashboard_service import get_child_dashboard  # pylint: disable=C0415

    # 元数据接口优化
    if dashboard.get('type') == DashboardType.File.value:
        return dashboard
    all_dashboard = get_child_dashboard(dashboard.get('id'))
    for dashboard_ in all_dashboard:
        if dashboard_.get('type') == DashboardType.File.value:
            return dashboard_
    return None


def verify_dashboard(request, response, dashboard, token, code, external_params):
    # 全部可见
    if dashboard.get('type_access_released') == DashboardTypeAccessReleased.NoLimited.value:
        # 检测已登录用户是否有权限
        check_grayscale(request, response, code)
        try:
            verify_user_role(token, code, dashboard)
        except (UserError, UserErrorWithExtraInfo):
            token = LoginToken().create_token("")
            response.set_cookie(name='token', value=token, domain=request.host, path='/', secure=False, http_only=False)

    # 根据角色
    elif dashboard.get('type_access_released') == DashboardTypeAccessReleased.UserRole.value:
        verify_user_role(token, code, dashboard)

    # 第三方系统可见
    elif dashboard.get('type_access_released') == DashboardTypeAccessReleased.ThirdParty.value:
        # 从external_params中获取数据，校验是否有查看权限
        _handle_third_party_base_except(external_params, dashboard)

    else:
        raise UserError(code=403, message="报告数据不合法，缺少查看已发布报告方式。")


def verify_dashboard_pwd(request, response, dashboard, pwd, token_data):
    # 验证密码
    if not pwd:
        raise UserError(code=409, message="缺少查看密码")

    if pwd != dashboard.get("share_secret_key"):
        raise UserError(code=409, message="密码不正确，请重试")

    if pwd == dashboard.get("share_secret_key"):
        token_data['pwd'] = pwd
        if '_flag' in token_data:
            token_data.pop('_flag')
        token = LoginToken().create_token(token_data.get('id'), token_data)
        response.set_cookie(name='token', value=token, domain=request.host, path='/', secure=False, http_only=False)


def _clear_dashboard_id(original_dashboard_id, dashboard_filter_id=None):
    dashboard_id = original_dashboard_id
    if not original_dashboard_id and dashboard_filter_id:
        dashboard_filter = dashboard_service.get_dashboard_filter(dashboard_filter_id)
        if dashboard_filter:
            dashboard_id = dashboard_filter.get('dashboard_id')
    return dashboard_id


def set_cookies_tenant_share_page(dashboard, request, response):
    """
    适用于公开、密码访问
    """
    if dashboard.get('type_access_released') in [DashboardTypeAccessReleased.NoLimited.value, DashboardTypeAccessReleased.Passwd.value]:
        response.set_cookie(name='tenant_code', value=g.code, domain=request.host, path='/', secure=False, http_only=False)


def _analyze_token(token, request, code, dashboard_id, response, secret, pwd):
    """
    解析token数据
    :param token:
    :param request:
    :param code:
    :param dashboard_id:
    :param response:
    :param secret:
    :param pwd:
    :return:
    """
    token_data = {}
    dashboard_cache = dashboard_cache_service.DashboardReleaseCache(code, dashboard_id, redis_conn())
    if not token:
        dashboard = dashboard_cache.get_dashboard_data()
        if not dashboard:
            raise UserError(code=404, message="报告不存在")
        token_data['dashboard'] = dashboard
        account = ''
        userid = ''
        external_params = ''
        portal_id = ''
        screen_id = ''
        if dashboard.get('type_access_released') != DashboardTypeAccessReleased.UserGroups.value:
            # 这个地方是公开、密码访问时候第一次screens_metadata设置cookies的逻辑
            token = jwt.encode(token_data, secret)
            response.set_cookie(name='token', value=token, domain=request.host, path='/', secure=False, http_only=False)
            set_cookies_tenant_share_page(dashboard, request=request, response=response)
            # 发布后报告支持iframe跨域
            if request.path == '/api/released_dashboard_chart/v2/screens_metadata':
                set_cookies_samesite_property(response=response)
    else:
        token_data = jwt.decode(token, secret, algorithms="HS256")
        dashboard = token_data.get('dashboard')
        if (
            (not dashboard)
            or (dashboard.get('type_access_released') == DashboardTypeAccessReleased.Passwd.value)
            or (not dashboard.get('type'))
        ):
            dashboard = dashboard_cache.get_dashboard_data()
            token_data['dashboard'] = dashboard
            token_data['pwd'] = pwd if pwd else token_data.get('pwd')
        external_params = token_data.get('external_params')
        account = token_data.get('account')
        userid = token_data.get('id')
        portal_id = token_data.get('portal_id')
        screen_id = token_data.get('screen_id')

    # from erp
    if (
        '__from=erpdashbordapi' in request.headers.get('REFERER', '')
        or token_data.get('__from') == 'erpdashbordapi'
    ):
        dashboard['need_ignore'] = 1

    token_result_dict = {
        "dashboard": dashboard,
        "external_params": external_params,
        "account": account,
        "userid": userid,
        "token_data": token_data,
        "pwd": pwd if pwd else token_data.get('pwd'),
        "portal_id": portal_id,
        "screen_id": screen_id,
    }
    return token_result_dict


def dashboard_verify_of_mit_user_auth(dashboard: dict, mit_user_auth: dict, request=None):
    """
    mit_user_auth: {
        "xxxxxxx": "view,download",
        "xxxxxxx": "view"
    }
    """
    if not dashboard:
        return False
    root_id = dashboard.get("id")
    if not dashboard.get("is_multiple_screen"):
        root_id = get_root_dashboard_id(dashboard).get('id')

    # source_from=dashboard_mip的场景需要实时判断权限
    if request.params.get('source_from') == ApplicationTypeAccessReleasedSourceStr.DashboardMipAuth.value:
        verify_dashboard_of_mip(root_id)
        return True

    user_auth = ''
    if mit_user_auth and isinstance(mit_user_auth, dict):
        user_auth = mit_user_auth.get(root_id) or ""
    if not user_auth or user_auth.find('view') == -1:
        # 不存在则从redis中获取权限
        user_auth = redis_conn().get(f'mit_user_auth:{g.account}:{root_id}')
        if user_auth and isinstance(user_auth, bytes):
            user_auth = user_auth.decode()

    if not user_auth or user_auth.find('view') == -1:
        raise UserError(code=403, message="当前用户没有该报告的查看权限")
    return True


def _check_third_party(token, code, dashboard, token_data, request=None):
    """
    第三方访问鉴权
    :param token:
    :param code:
    :param dashboard:
    :param token_data:
    :param request:
    :return:
    """
    # 检测已登录用户是否有权限，如果有，不走第三方系统认证权限
    try:
        if token_data.get("mit_user_auth") or request.params.get('source_from') in [
            ApplicationTypeAccessReleasedSourceStr.DashboardMipAuth.value,
            ApplicationTypeAccessReleasedSourceStr.DashboardUserAuth.value
        ]:
            # 集成平台的权限单独校验，免登时会从基础平台获取主报表、子报表、跳转报表的权限，写在cookie的mit_user_auth字段
            # 主要用于区分三云通过dashboard/login集成的场景
            dashboard_verify_of_mit_user_auth(dashboard, token_data.get("mit_user_auth"), request)
        else:
            verify_user_role(token, code, dashboard, token_data=token_data)
    except UserErrorWithExtraInfo as e:
        _handle_third_party_except_with_info(e)
    except UserError:
        # 从external_params中获取数据，校验是否有查看权限
        external_params = token_data.get('external_params')
        _handle_third_party_base_except(external_params, dashboard)


def _verify_passwd_access(request, response, **kwargs):
    # 检测已登录用户是否有权限，如果有，免密码登录
    token = kwargs.get("token")
    code = kwargs.get("code")
    dashboard = kwargs.get("dashboard")
    pwd = kwargs.get("pwd")
    token_data = kwargs.get("token_data")
    try:
        verify_user_role(token, code, dashboard)
    except (UserError, UserErrorWithExtraInfo):
        verify_dashboard_pwd(request, response, dashboard, pwd if pwd else token_data.get('pwd'), token_data)


@authenticator
@auto_no_cache
def verify_released_handle(request: falcon.Request, response, verify_user, **kwargs):  # pylint:disable=W0613
    """
    校验发布报告接口
    :param request:
    :param response:
    :return:
    """
    auth_cookies_replacement(request)

    secret = config.get('JWT.secret')

    if request.method == 'POST':
        body = request.bounded_stream.read()
        data = json.loads(body.decode('utf-8'))
        code = data.get('code')
        pwd = data.get('pwd')
        dashboard_id = data.get("multi_dashboard_id", data.get("dashboard_id", data.get("id")))
        dashboard_filter_id = data.get('dashboard_filter_id')
        request._bounded_stream = request_helpers.BoundedStream(io.BytesIO(body), request.content_length or 0)
    else:
        code = request.get_param('code')
        dashboard_id = request.get_param(
            name="multi_dashboard_id", default=request.get_param(name="dashboard_id", default=request.get_param('id'))
        )
        dashboard_filter_id = request.get_param('dashboard_filter_id')
        pwd = request.get_param('pwd')

    # 赋值code
    if not code:
        raise UserError(message="缺少企业代码")

    # 修复云空间取数串租户问题
    if request.cookies.get('tenant_code') and request.cookies.get('tenant_code') != code:
        raise UserError(code=408, message="访问受限, 组织机构代码不一致, 请联系管理员处理！")

    # _analyze_token中查询报告信息需查询db，在这里需先设置g.code
    try:
        set_correct_project_code(code, request)
    except EmptyProjectCodeError as e:
        raise UserError(code=408, message="访问受限（组织机构代码不一致），请联系管理员") from e

    #  清洗dashboard_id
    if dashboard_filter_id is not None and ',' in dashboard_filter_id:
        filter_ids = dashboard_filter_id.split(',')
        dashboard_filter_id = filter_ids[0]
    dashboard_id = _clear_dashboard_id(dashboard_id, dashboard_filter_id)
    # 跳转关系不存在不做校验，改为由业务接口处理
    if not dashboard_id:
        return True

    token = request.cookies.get('token')
    token_result_dict = _analyze_token(token, request, code, dashboard_id, response, secret, pwd)
    token_data = token_result_dict.get("token_data")
    dashboard = token_result_dict.get("dashboard", {})
    external_params = token_result_dict.get("external_params") or {}
    handle_external_params(external_params, token_data)
    account = token_result_dict.get("account")
    g.account = account
    userid = token_result_dict.get("userid")
    pwd = token_result_dict.get("pwd") if not pwd else pwd
    portal_id = token_result_dict.get("portal_id", "")
    screen_id = token_result_dict.get("screen_id", "")

    code = _verify_request_tenant_code(token_data.get("code", ""), code)
    token_data['code'] = code

    if not dashboard:
        metadata_service.trans_jump_error_info(UserError(code=404), request)
        raise UserError(code=404, message="报告不存在")
    if not dashboard.get("status") or int(dashboard.get("status")) == 0:
        raise UserError(code=403, message="报告未发布，请联系管理员")

    _debugger.log({"token_data": token_data})

    # 使用基础数据平台权限校验
    if is_released_metadata_api(request):  # 必须是元数据接口
        if (
            request.params.get('source_from') == ApplicationTypeAccessReleasedSourceStr.MipAuth.value  # 门户url标识
            or token_data.get('auth_from') == TokenAuthFrom.MipAuth.value  # 集成登录设置的标识
        ):
            source_func = request.params.get('source_func') or ''  # 来源菜单id
            _verify_dashboard_auth_from_mip(dashboard_id, source_func)
            return True

    if portal_id and external_params:
        _verify_portal(portal_id, dashboard_id)
        _verify_portal_dashboard_auth(dashboard_id, external_params, token_data)
        set_g_params(token=token, account=account, userid=userid, external_params=external_params)
        return True

    # 第三方多屏报告授权
    if screen_id and external_params:
        _verify_third_screen_auth(screen_id, external_params)
        set_g_params(token=token, account=account, userid=userid, external_params=external_params)
        return True

    elif dashboard.get('type_access_released') == DashboardTypeAccessReleased.Passwd.value:
        # 检测已登录用户是否有权限，如果有，免密码登录
        check_grayscale(request, response, code)
        _verify_passwd_access(
            request,
            response,
            **{"code": code, "token": token, "dashboard": dashboard, "pwd": pwd, "token_data": token_data}
        )
    elif dashboard.get('type_access_released') == DashboardTypeAccessReleased.ThirdParty.value and not screen_id:
        _check_third_party(token, code, dashboard, token_data, request)
    else:
        verify_dashboard(request, response, dashboard, token, code, external_params)
    set_g_params(token=token, account=account, userid=userid, external_params=external_params)
    try:
        _url_path = urllib.parse.urlparse(request.url)
        if (
            userid
            and "/v2/screens_metadata" in _url_path.path
            and not external_params
            and "user_org" not in request.cookies
        ):
            _debugger.log("发布页元数据设置cookie参数")
            user_service.write_ty_cookie(request.host, response, userid)

    except Exception as e:
        logger.error(msg="设置cookie参数`user-log`异常：{}".format(str(e)))
    return True


def verify_dashboard_of_mip(report_id):
    from components.ingrate_platform import IngratePlatformApi

    mit_user_auth = IngratePlatformApi(g.code).check_report_is_grant(g.account, [report_id])

    if 'view' not in mit_user_auth.get(report_id, ''):
        raise UserError(message="用户没有该报告的可见权限")


def is_released_metadata_api(request):
    # 是否是运行时元数据接口
    return 'api/released_dashboard_chart/v2/screens_metadata' in request.path


def handle_external_params(external_params, token_data):
    if token_data.get('customize_roles'):
        external_params['customize_roles'] = token_data.get('customize_roles')
    if token_data.get('external_user_id'):
        external_params['external_user_id'] = token_data.get('external_user_id')


@authenticator
def verify_data_handle(request, response, verify_user, **kwargs):  # pylint:disable=W0613
    """
    released发布模块取数校验接口
    :param request:
    :param response:
    :param verify_user:
    :param kwargs:
    :return:
    """
    auth_cookies_replacement(request)

    token = request.cookies.get('token')
    if not token:
        raise UserError(code=403, message="未授权")

    verified = True
    errmsg = ''
    data = None
    try:
        data = jwt.decode(token, config.get('JWT.secret'), algorithms='HS256')
    except DecodeError:
        verified, errmsg = False, '无效的签名'
    except ExpiredSignatureError:
        verified, errmsg = False, '签名过期'
    except binascii.Error:
        verified, errmsg = False, '非法签名'
    except Exception as e:
        verified, errmsg = False, str(e)

    if not verified or not data:
        # return False, errmsg, None
        raise UserError(code=403, message=f"token异常: {errmsg}")

    g.cookie = {'token': token}
    g.account = data.get("account")
    g.userid = data.get("id")
    # 将外部参数放入共享变量中
    g.external_params = data.get('external_params')
    g.auth_from = data.get("auth_from", '')
    # 兼容外部指定角色
    # 通过api/user/sso登录的token没有external_params
    if not g.external_params:
        g.customize_roles = data.get('customize_roles')
        g.external_user_id = data.get('external_user_id')
    if g.external_params and g.external_params.get('customize_roles'):
        g.customize_roles = g.external_params.get('customize_roles')
    if g.external_params and g.external_params.get('external_user_id'):
        g.external_user_id = g.external_params.get('external_user_id')
    if request.get_param("dataset_version_date"):
        # TODO 零时方案，后面要删除这个代码
        g.dataset_version_date = request.get_param("dataset_version_date")

    # 修复取数串租户问题
    if (
        g.external_params
        and request.get_param('code')
        and g.external_params.get('project_code') != request.get_param('code')
    ):
        raise UserError(code=408, message="访问受限, 组织机构代码不一致, 请联系管理员处理！")

    return True


def _get_error_info(dashboard):
    """
    获取额外需抛出的异常信息
    :param dashboard:
    :return:
    """
    redirect_url = ""
    try:
        if (
            dashboard
            and dashboard.get('type_access_released') == DashboardTypeAccessReleased.ThirdParty.value
            and dashboard.get("id")
        ):
            redirect_url = dashboard_service.get_custom_redirect_url(
                dashboard.get("id"), need_normal_share_url_flag=False
            )
    except Exception:
        redirect_url = ""
    return {"redirect_url": redirect_url}


def _handle_third_party_base_except(external_params, dashboard):
    """
    处理第三方访问基本校验抛错
    :param external_params:
    :return:
    """
    # 三云第三方
    if external_params:
        user_auth = external_params.get('user_auth')
        customize_roles = external_params.get('customize_roles')
        if customize_roles:
            return True
        if not user_auth or user_auth.find('view') == -1:
            raise UserError(code=403, message="第三方系统认证失败")
        return True
    # # 基础数据平台的权限
    # if _verify_dashboard_auth_from_mip(dashboard_id=dashboard.get('id', '')):
    #     return True
    raise UserError(code=403, message="第三方系统认证失败")


def _handle_third_party_except_with_info(e: UserErrorWithExtraInfo):
    """
    处理第三方访问校验错误，并返回info信息
    :param e:
    :return:
    """
    message = "第三方系统认证失败, 需重新登录" if e.extra_info and e.extra_info.get("redirect_url") else "第三方系统认证失败, 请联系系统管理员"
    raise UserErrorWithExtraInfo(code=403, message=message, extra_info=e.extra_info)


def set_g_params(**kwargs):
    token = kwargs.get('token')
    account = kwargs.get('account')
    userid = kwargs.get('userid')
    external_params = kwargs.get('external_params')

    g.cookie = {'token': token}
    g.account = account
    g.userid = userid
    # 将外部参数放入共享变量中
    g.external_params = external_params

    if external_params and isinstance(external_params, dict):
        if external_params.get('customize_roles'):
            setattr(g, 'customize_roles', external_params.get('customize_roles'))
        if external_params.get("external_user_id"):
            setattr(g, 'external_user_id', external_params.get('external_user_id'))


def _verify_portal(portal_id, dashboard_id):
    """
    来自门户第三方鉴权访问方式的检查
    :param portal_id:
    :param dashboard_id:
    :return:
    """
    _debugger.log("门户访问报告-第三方鉴权====> start...")
    check_flag = app_menu_external_service.check_dashboard_in_application(portal_id, dashboard_id)
    _debugger.log({'check_flag': check_flag})
    if not check_flag:
        raise UserError(code=403, message="第三方系统认证失败")


def _verify_third_screen_auth(screen_id, external_params):
    """
    第三方提供了session_id的场景 需要校验报告的访问权限
    :param portal_id:
    :param dashboard_id:
    :return:
    """
    # pylint: disable=C0415
    from dashboard_chart.services.released_dashboard_service import get_dashboard_ids_by_screen_id

    session_id = external_params.get("session_id") if external_params else ""
    if not session_id:
        return True
    dashboard_ids = get_dashboard_ids_by_screen_id(screen_id)
    for dashboard_id in dashboard_ids:
        auth_res = screen_auth_service.get_third_auth_cache(session_id, dashboard_id)
        _debugger.log({"session_id": session_id, "dashboard_id": dashboard_id, "dashboard_auth_res": auth_res})
        if auth_res and "view" in auth_res:
            return True
    raise UserError(code=407, message="无报告查看权限")


def _verify_portal_dashboard_auth(dashboard_id, external_params, token_data):
    """
    第三方提供了session_id的场景，则需要校验门户内报告的访问权限
    :param portal_id:
    :param dashboard_id:
    :return:
    """
    # if token_data.get('auth_from') == TokenAuthFrom.MipAuth.value:
    #     # 只有是来自于第三方门户集成且授权来自基础数据平台
    #     _verify_dashboard_auth_from_mip(dashboard_id)
    session_id = external_params.get("session_id") if external_params else ""
    if not session_id:
        return True
    auth_res = application_auth_service.get_auth_cache(session_id, dashboard_id)
    _debugger.log({"session_id": session_id, "dashboard_id": dashboard_id, "portal_auth_res": auth_res})
    if not auth_res or "view" not in auth_res:
        raise UserError(code=407, message="无报告查看权限")
    return True


def _verify_dashboard_auth_from_mip(dashboard_id, source_func):
    """
    校验来自于基础数据平台授权的报告
    这里只实现了门户打开报告
    """
    # from app_menu.repositories.function_repository import get_function_ids_by_urls

    # 基础数据平台实际上授权的是数见的菜单
    # 这里校验的核心思路是，查询所有挂载了这个报告的菜单，然后请求基础数据，只要有一个菜单有查看权限，那么这个报告就有查看权限
    # if not ApplicationDashboardOperation.mip_config_is_exist():
    #     return False

    _debugger.log({'报告将使用基础数据平台进行鉴权': {'dashboard_id': dashboard_id, 'source_func': source_func}})

    # all_ids = set()
    # # 1. 子报表
    # root_id = dashboard_service.get_root_dashboard_by_dashboard_id(dashboard_id)
    # if root_id and root_id.get('id'):
    #     all_ids.add(root_id.get('id'))
    # # 2. 跳转
    # redirect_ids = dashboard_service.get_all_possible_redirect_source_dashboards(dashboard_id)
    # all_ids.update(redirect_ids)
    # all_ids.add(dashboard_id)
    # parent_funcs = get_function_ids_by_urls(list(all_ids))
    # parent_func_ids = [f.get('id', '') for f in parent_funcs if f.get('id', '')]
    # if not parent_func_ids:
    if not source_func:
        raise UserError(code=407, message="无基础数据平台报告查看权限，来源菜单为空")

    # 3. 获取基础数据平台的权限
    # report_ids = parent_func_ids
    report_ids = [source_func]
    permission_data = ApplicationDashboardOperation().get_reports_permission_data(report_ids=report_ids) or {}
    _debugger.log({'基础数据平台取回门户集成报告的权限数据': json.dumps(permission_data)})

    real_auth = ','.join([str(val) for val in permission_data.values()]) or ''
    # 这里目前实际上只限制了查看
    if 'view' not in real_auth:
        raise UserError(code=407, message="无基础数据平台报告查看权限")
    return True


def _verify_request_tenant_code(token_tenant_code, request_tenant_code):
    """
    校验用户传入的code和token加密串中的code
    :param token_tenant_code:
    :param request_tenant_code:
    :return:
    """
    _debugger.log("校验用户传入code token中的code: {}, 用户传入的code: {}".format(token_tenant_code, request_tenant_code))

    # step1 对比token中的租户code和用户传入的租户code
    if token_tenant_code and token_tenant_code == request_tenant_code:
        return request_tenant_code
    if hasattr(g, "code"):
        return g.code
    try:
        # step 2 尝试获取缓存中的租户code
        # step 3 缓存获取失败则db查询获取正确的租户code
        correct_project_code = set_correct_project_code(request_tenant_code)
        _debugger.log("成功获取正确租户code: {}".format(correct_project_code))
    except EmptyProjectCodeError as e:
        raise UserError(code=408, message="访问受限（组织机构代码不一致），请联系管理员") from e
    except Exception as e:
        raise UserError(message="用户权限鉴权异常") from e
    _debugger.log("校验用户传入code 校验完成")
    return correct_project_code


def verify_third_dashboard_download_auth(dashboard_id, external_params):
    """
    第三方提供了session_id的场景 需要校验报告的访问权限
    :param portal_id:
    :param dashboard_id:
    :return:
    """
    session_id = external_params.get("session_id") if external_params else ""
    if not session_id:
        return True
    auth_res = screen_auth_service.get_third_auth_cache(session_id, dashboard_id)
    _debugger.log({"session_id": session_id, "dashboard_id": dashboard_id, "dashboard_auth_res": auth_res})
    if not auth_res or "download" not in auth_res:
        return False
    return True
