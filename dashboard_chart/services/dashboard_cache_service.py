"""dashboard相关业务缓存
"""
from dmplib.hug import debugger
from components.wait_lock import Wait<PERSON>ocker
from components.object_caching import ObjectCache
from base import repository

from dmplib.redis import RedisCache

_debugger = debugger.Debug(__name__)


class DashboardCache(ObjectCache):

    redis_client = None
    _prop_version_name = "version"

    # ------------ properties -------------- #
    prop_info = 'info'
    prop_screens_detail = 'screens'
    prop_chart_code_list = 'chart_code_list'
    prop_deliver_timestamp = 'deliver_timestamp'
    prop_chart_source_dict = 'chart_source_dict'
    # ------------------------------------- #

    # 只缓存与发布动作相关的数据,包括权限验证方式

    def __init__(self, project_code: str, dashboard_id: str, redis_conn: RedisCache) -> None:
        super().__init__("dashboard", project_code, dashboard_id, redis_conn)

    @staticmethod
    def get_chart_cache_key(dashboard_id, chart_id, suffix="meta"):
        """
        获取单图缓存key
        :param dashboard_id:
        :param chart_id:
        :param suffix:
        :return:
        """
        dashboard_chart_cache_key = (
            "{}_{}_{}".format(dashboard_id, chart_id, suffix) if suffix else "{}_{}".format(dashboard_id, chart_id)
        )
        return dashboard_chart_cache_key

    def get_chart(self, dashboard_id, chart_id, suffix="meta"):  # pylint: disable=unused-argument
        """
        获取单图缓存数据
        :param suffix:
        :param str dashboard_id: 报告id
        :param str chart_id: 单图id
        :return:
        """
        dashboard_chart_cache_key = self.get_chart_cache_key(dashboard_id, chart_id)
        return self.get_prop(dashboard_chart_cache_key)

    def batch_get_charts(self, dashboard_id, chart_id_list):
        """
        批量获取单图缓存
        :param dashboard_id:
        :param chart_id_list:
        :return:
        """
        if not chart_id_list:
            return None
        chart_cache_key_list = [
            self.get_chart_cache_key(dashboard_id, single_chart_id) for single_chart_id in chart_id_list
        ]
        return self.hmget(chart_cache_key_list)

    def get_dashboard_data(self, dashboard_id, key=''):
        """
        获取单个报告缓存数据
        :param dashboard_id:
        :param key:
        :return:
        """
        result = None
        screens = self.get_prop(self.prop_screens_detail)
        if not screens:
            return result
        for single_dashboard in screens:
            if not single_dashboard:
                continue
            if single_dashboard.get('id') == dashboard_id:
                if not key:
                    return single_dashboard
                result = single_dashboard.get(key, None)
                return result
        return result

    def get_released_on(self):
        """
        获取报告的发布时间
        :return:
        """
        info = self.get_prop(self.prop_info)
        return info.get("modified_on", "") if info else ""


class DashboardReleaseCache(ObjectCache):

    def __init__(self, project_code: str, dashboard_id: str, redis_conn: RedisCache) -> None:
        super().__init__("dashboard_release_info", project_code, dashboard_id, redis_conn)

    def get_dashboard_data(self):
        key = self.cache_key
        fields = [
            "id",
            "status",
            "type",
            "is_multiple_screen",
            "type_access_released",
            "share_secret_key",
            "application_type",
        ]

        data = self.redis_client.get_data(key)
        # 新增type缓存, 兼容旧的缓存数据
        if (not data) or (not data.get('type')):
            _debugger.log('get dashboard release data from db. id:%s', self.object_id)
            with WaitLocker(key, 2) as locker:
                if locker.lock(1):
                    data = repository.get_data("dashboard", {"id": self.object_id, "build_in": 0}, fields)

                    self.redis_client.set_data(key, data)
                else:
                    data = repository.get_data("dashboard", {"id": self.object_id, "build_in": 0}, fields)
        _debugger.log({'release_data': data})
        return data

    def delete_dashboard_data(self) -> None:
        key = self.cache_key
        with WaitLocker(key, 2) as locker:
            locker.lock(1)
            self.redis_client.delete(key)

    def remove(self):
        super().remove()
        self.delete_dashboard_data()
