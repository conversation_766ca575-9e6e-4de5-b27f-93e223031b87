#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2017/4/10.
"""
import datetime
import functools
import json
import logging
import re
import hashlib
import traceback
from collections import Iterable
from copy import deepcopy
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

from base import repository
from base.enums import (
    DashboardDataMsgCode,
    DashboardSQLExecuteStatus,
    ChartPenetrateRelationType,
    ChartNumSubtotalFormulaMode,
    DashboardType
)
from base.errors import ChartQueryExceptionError, SqlProcessingException
from base.dmp_constant import EXTRA_NUMS_SUFFIX, AES_DEFAULT_KEY
from components.versioned_query import CachedProperty
from components.storage_setting import get_setting as get_user_system_setting
from dashboard_chart.data_query.charts import chart_factory
from dashboard_chart.logics.chart_definition_logic import ChartDefinitionLogic
from dashboard_chart.utils.common import add_api_dataset_params, set_data_index
from dashboard_chart.models import (
    ChartDataModel, DashboardComponentFilterModel,
    DashboardComponentFilterFieldModel, SubtotalColFormulaExpressionCheckModel
)
from dashboard_chart.repositories import chart_repository
from dashboard_chart.repositories import dashboard_repository
from dashboard_chart.services import proxy_dataset_service, dashboard_service
from dashboard_chart.utils import chart_utils
from dataset import external_query_service
from dmplib.hug import debugger
from dmplib.hug import g
from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.saas.project import get_db
from dmplib.utils.crypt import AESCrypt
from dmplib.utils.errors import UserError, FriendlyError
from dmplib.utils.strings import seq_id
from dmplib import config
from dashboard_chart.services.chart_model_adapter import adapt_chart_data_model
# from dashboard_chart.metadata.dashboard_preview_metadata_models import DashboardVarFilterNodeModel
from dataset.services.advanced_field_service import (
    validate_expression as ad_validate_expression,
    handle_expression_advance_vars
)
from dataset.models import DatasetFieldModel
from dataset.common import advance_field_helper
from dashboard_chart.services.dashboard_released_design_service import (
    check_dashboard_released_design_record,
    get_root_dashboard_by_level_code
)
from components.analysis_time import AnalysisTimeUtils
from components.versioned_query import cached as versioned_cached, disable_current_request_versioned
from components.fast_logger import FastLogger
from components.utils import spawn_background_task, space_saving_json

logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)


def __copy_g_property(to_g, **g_kwargs):
    # 在不同的协程或者线程中，需要处理g的传递（g只能保证线程程安全）
    for key, val in g_kwargs.items():
        setattr(to_g, key, val)
    to_g.customize_roles = (to_g.customize_roles or []) if hasattr(to_g, 'customize_roles') else []


def get_g_property():
    # 获取g的关键属性
    return {key: getattr(g, key, None) for key in dir(g) if not (key.startswith('__') and key.endswith('__'))}


def handle_g_total(func: Callable) -> Callable:
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread_local_g = _AppCtxGlobals()
        g_kwargs = kwargs.pop('g', {})
        __copy_g_property(thread_local_g, **g_kwargs)  # noqa
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()

    return wrapper


def handle_g(func: Callable) -> Callable:
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread_local_g = _AppCtxGlobals()
        thread_local_g.code = kwargs.pop('code', None)
        thread_local_g.account = kwargs.pop('account', None)  # getattr(parent_thread_g, 'account', None)
        thread_local_g.userid = kwargs.pop('userid', None)  # getattr(parent_thread_g, 'userid', None)
        thread_local_g.cookie = kwargs.pop('cookie', None)  # getattr(parent_thread_g, 'cookie', None)
        thread_local_g.is_developer = kwargs.pop('is_developer', 0)
        thread_local_g.external_params = kwargs.pop('external_params', None)
        thread_local_g.customize_roles = kwargs.pop('customize_roles', [])
        thread_local_g.external_user_id = kwargs.pop('external_user_id', None)
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()

    return wrapper


from gevent.pool import Pool


# pool = Pool(500)


def batch_get_chart_data(chart_params: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    批量获取单图展示数据
    :param chart_params:
    :return:
    """
    results = {}
    if not chart_params or not isinstance(chart_params, Iterable):
        return results

    def single_chart_result(param, results):
        model = ChartDataModel(**param)
        AnalysisTimeUtils.enter(chart_id=model.id)
        try:
            chart_result = get_chart_result(model)
            AnalysisTimeUtils.append_analysis_data(result=chart_result)
            results[model.id] = chart_result
        finally:
            AnalysisTimeUtils.exit()

    g_kwargs = get_g_property()
    length_chart_params = len(chart_params)
    # 单个请求不用gevent.pool
    if length_chart_params == 1:
        for param in chart_params:
            # get_chart_data = handle_g_total(single_chart_result)
            single_chart_result(param, results)
    else:
        pool_num = min(len(chart_params), 10)
        pool = Pool(pool_num)
        for param in chart_params:
            get_chart_data = handle_g_total(single_chart_result)
            # external_params = getattr(g, 'external_params', None)
            # cookie = getattr(g, 'cookie', None)
            # customize_roles = getattr(g, 'customize_roles', [])
            # external_user_id = getattr(g, 'external_user_id', None)
            pool.spawn(
                get_chart_data,
                param,
                results,
                **{'g': g_kwargs}
            )
        pool.join()
    return results


def get_chart_result(model: ChartDataModel, no_data: bool = True, reassign_model: bool = True) -> Dict[str, Any]:
    """
    单个获取单图展示数据
    :param dashboard_chart.models.ChartDataModel model:
    :param bool no_data:
    :param reassign_model:
    :return:
    """
    st = AnalysisTimeUtils.now()
    result = None
    try:
        model.validate()
        # 若model已经为完整的model, 可以不用为model重新赋值， 否则此处将为model重新赋值
        if reassign_model:
            assign_dashboard_chart_model(model, no_data=no_data)
        if not model.dataset_id:
            result = _get_exception_result_struct("没有配置数据集", DashboardDataMsgCode.NullDatasetData.value)
        elif not model.dims and not model.nums:
            result = _get_exception_result_struct("没有配置维度和度量", DashboardDataMsgCode.NullDatasetData.value)
        else:
            chart_class = chart_factory.ChartFactory.create_chart(model.id, model)
            # 记录时间
            AnalysisTimeUtils.record(
                step=AnalysisTimeUtils.step_type.ready_query.value, sql=None, db_type=None, start_time=st,
                extra={}, need_type_inference=False
            )
            if no_data:
                result = chart_class.get_chart_data()
            else:
                result = chart_class.get_variable_chart_data()
    # 取数异常 error code 5004
    except ChartQueryExceptionError as e:
        logger.error(f"取数异常: {e}, 错误信息: {traceback.format_exc()}")
        result = _get_exception_result_struct("获取数据异常，请重新检查单图字段配置。", e.code)
        return result
    except SqlProcessingException as e:
        logger.error(f"sql处理异常: {e}, 错误信息: {traceback.format_exc()}")
        result = _get_exception_result_struct(str(e), DashboardDataMsgCode.QueryException.value)
    except Exception as e:
        # traceback.print_exc()
        logger.error(f"取数过程未知异常: {e}, 错误信息: {traceback.format_exc()}")
        if not isinstance(e, UserError):
            logger.exception(msg="单图取数接口异常，异常信息: " + str(e))
        result = _get_exception_result_struct(str(e), DashboardDataMsgCode.NullDatasetData.value)
    finally:
        if result and isinstance(result, dict) and isinstance(result.get('msg_code'), int) \
                and result.get('msg_code', 0) > 5000:
            logger.error(f"单图预览取数异常, code<{result.get('msg_code', 0)}>, {result.get('msg', 'unknown')}")
        result = deal_scheduled_dataset_query_info(result)
    return result


def deal_scheduled_dataset_query_info(origin_result):
    # 处理调度数据集取数表还没落地的提示信息
    if isinstance(origin_result, dict):
        _deal_result(origin_result)
    if isinstance(origin_result, list):
        for result in origin_result:
            _deal_result(result)
    return origin_result


def _deal_result(result):
    if result.get('execute_status') == 404:
        result['msg_code'] = 5004
        result['origin_msg'] = result['msg']
        result['msg'] = "数据集正在调度中或调度失败，请检查数据集"
    # 加密SQL
    encrypt_return_sql(result)
    return result


def encrypt_return_sql(result):
    sql = result.get('sql')
    if not sql:
        return
    try:
        key = AES_DEFAULT_KEY
        enc = AESCrypt(key).encrypt(sql)
        result['sql'] = enc
    except:
        pass


def get_chart_query_struct(model):
    """
    单个获取单图展示数据
    :param dashboard_chart.models.ChartDataModel model:
    :return:
    """
    try:
        model.validate()
        assign_dashboard_chart_model(model, no_data=True)
        if not model.dataset_id:
            struct = _get_exception_struct_struct("没有配置数据集", DashboardDataMsgCode.NullDatasetData.value)
        elif not model.dims and not model.nums:
            struct = _get_exception_struct_struct("没有配置维度和度量", DashboardDataMsgCode.NullDatasetData.value)
        else:
            chart_class = chart_factory.ChartFactory.create_chart(model.id, model)
            struct = chart_class.get_query_struct()
    except Exception as e:
        if not isinstance(e, UserError):
            logger.exception(msg="单图取数接口异常，异常信息: " + str(e))
        struct = _get_exception_struct_struct(str(e), DashboardDataMsgCode.NullDatasetData.value)
    return struct


def _get_item_query_vars(model: ChartDataModel, field_ids: list):
    """
    获取item依赖的变量
    此处变量都取默认值
    :param model:
    :param field_ids:
    :return:
    """
    query_vars = []
    dataset_include_vars = external_query_service.batch_get_dataset_include_vars([model.dataset_id])
    for include_var in dataset_include_vars:
        if include_var.get("field_id") in field_ids:
            if include_var.get("value") is None:
                include_var["value"] = include_var.get("default_value")
            query_vars.append(include_var)
    return query_vars


def get_item_list(model, dataset_field_ids=None):
    """
    单个获取单图筛选列表数据
    此处变量由后端自行获取，不需要根据前端传入
    :param dataset_field_ids:
    :param dashboard_chart.models.ChartDataModel model:
    :return:
    """
    if dataset_field_ids and isinstance(dataset_field_ids, list):
        field_id_item_map = {}
        merge_get_field_items(dataset_field_ids, field_id_item_map)
        return field_id_item_map

    try:
        if model.dataset_field_id:
            dim = proxy_dataset_service.get_formatted_fields_by_field_id(model.dataset_field_id)
            if dim:
                dim["dim"] = dim.get("id")
            model.dims = [dim] if dim else []
            model.dataset_id = dim.get("dataset_id")
        else:
            model.dims = _get_chart_section_data(model, section_type="dims", no_data=False)
            if model.dims:
                model.dataset_id = model.dims[0].get("dataset_id")
        if not model.dims:
            raise UserError(message="无法获取单图维度信息！")
        field_ids = [dim.get("dim") for dim in model.dims]
        # 数据集引用的变量
        model.query_vars = _get_item_query_vars(model, field_ids)
        model.dataset_field_dict = proxy_dataset_service.get_dataset_field_dict_with_field_id(model.dataset_id)
        model.data_logic_type_code = model.data_logic_type_code or "default"
        model.dataset = proxy_dataset_service.get_dataset(model.dataset_id)
        # 适配chart_data_model
        adapt_chart_data_model(model)
        chart_class = chart_factory.ChartFactory.create_chart(model.id, model)
        result = chart_class.get_item_list()

    except Exception as e:
        if not isinstance(e, UserError):
            logger.exception(msg="单图取数接口异常，异常信息: " + str(e))
        result = _get_exception_result_struct(str(e), DashboardDataMsgCode.NullDatasetData.value)
    return result


def merge_get_field_items(dataset_field_ids, field_id_item_map):
    for field_id in dataset_field_ids:
        model = ChartDataModel(**{"dataset_field_id": field_id})
        try:
            if model.dataset_field_id:
                dim = proxy_dataset_service.get_formatted_fields_by_field_id(model.dataset_field_id)
                if dim:
                    dim["dim"] = dim.get("id")
                model.dims = [dim] if dim else []
                model.dataset_id = dim.get("dataset_id")
            else:
                model.dims = _get_chart_section_data(model, section_type="dims", no_data=False)
                if model.dims:
                    model.dataset_id = model.dims[0].get("dataset_id")
            if not model.dims:
                raise UserError(message="无法获取单图维度信息！")
            field_ids = [dim.get("dim") for dim in model.dims]
            # 数据集引用的变量
            model.query_vars = _get_item_query_vars(model, field_ids)
            model.dataset_field_dict = proxy_dataset_service.get_dataset_field_dict_with_field_id(model.dataset_id)
            model.data_logic_type_code = model.data_logic_type_code or "default"
            model.dataset = proxy_dataset_service.get_dataset(model.dataset_id)
            # 适配chart_data_model
            adapt_chart_data_model(model)
            chart_class = chart_factory.ChartFactory.create_chart(model.id, model)
            result = chart_class.get_item_list()

        except Exception as e:
            if not isinstance(e, UserError):
                logger.exception(msg="单图取数接口异常，异常信息: " + str(e))
            result = _get_exception_result_struct(str(e), DashboardDataMsgCode.NullDatasetData.value)
        field_id_item_map[field_id] = result


def get_total(model, reassign_model: bool = True):
    """
    单个获取单图取数总数量
    :param dashboard_chart.models.ChartDataModel model:
    :param reassign_model:
    :return:
    """
    try:
        model.validate()

        if reassign_model:
            assign_dashboard_chart_model(model, no_data=True)
        if not model.dataset_id:
            raise UserError(message="没有配置数据集")
        chart_class = chart_factory.ChartFactory.create_chart(model.id, model)
        # api数据集日志上报使用
        add_api_dataset_params(g, sql_from="design_viewreport", dataset_id=model.dataset_id,
                               report_id=model.dashboard_id)
        return chart_class.query_count()
    except Exception as e:
        if not isinstance(e, UserError):
            logger.exception(msg="单图取数接口异常，异常信息: " + str(e))
        raise UserError(message="获取总数失败，错误信息: {msg}".format(msg=str(e)))


def _get_exception_result_struct(msg: str, code: int) -> Dict[str, Union[str, int, Dict[str, str]]]:
    """
    数据异常时返回数据结构
    :param str msg: 错误信息
    :param str code: code标识
    :return:
    """
    return {
        "data": [],
        "marklines": [],
        "conditions": [],
        "msg": msg,
        "msg_code": code,
        "execute_status": DashboardSQLExecuteStatus.NOExectue.value,
        "pagination": {},
        "sql": "",
        "sql_execute_time": "",
        "dataset_versions": {"data_version": "", "version": ""},
    }


def _get_exception_struct_struct(msg, code):
    """
    获取结构异常时返回数据结构
    :param str msg: 错误信息
    :param str code: code标识
    :return:
    """
    return {
        "dataset_name": "",
        "msg_code": code,
        "msg": msg,
        "meta_version": "",
        "execute_status": DashboardSQLExecuteStatus.NOExectue.value,
        "query_structure": "",
    }


def _assign_dataset_field_dict(model: ChartDataModel):
    """
    将dashboard_filters内的数据集字段数据也放入dataset_field_dict
    :param model:
    :return:
    """
    dataset_field_dict = model.dataset_field_dict
    dashboard_filters = model.dashboard_filters
    if not dashboard_filters or not dataset_field_dict:
        return dataset_field_dict
    for single_filter in dashboard_filters:
        field_id = single_filter.get("main_dataset_field_id")
        if field_id:
            dataset_field_dict.update({field_id: single_filter})
    return dataset_field_dict


def assign_dashboard_chart_model(model: ChartDataModel, no_data: bool = True) -> None:
    """
    对chart_model进行赋值，用于单图数据查询接口
    :param dashboard_chart.models.ChartDataModel model:
    :param dict chart: 单图数据
    :param bool no_data:
    :return:
    """
    dashboard = repository.get_data(
        "dashboard",
        {"id": model.dashboard_id},
        ["id", "main_external_subject_id", "name", "application_type", "platform", "new_layout_type", "type",
         "status", "type_access_released", "layout"]
    )
    if not dashboard:
        raise UserError(message="报告不存在")

    # 设置data_index
    set_data_index(dashboard.get("layout"))

    chart = repository.get_data(
        "dashboard_chart",
        {"id": model.id},
        [
            "id",
            "source",
            "chart_code",
            "display_item",
            "page_size",
            "parent_id",
            "level_code",
            "enable_subtotal",
            "enable_summary",
            "enable_subtotal_col",
            "enable_subtotal_col_summary",
            "enable_subtotal_row",
            "enable_subtotal_row_summary",
            "subtotal_row_summary_formula_mode",
            "aggregation",
            "pre_comparison",
            "layout_extend",
        ],
    )
    if not chart:
        raise UserError(message="单图不存在")
    if no_data:
        model.dataset_id = chart.get("source")
        model.enable_subtotal = chart.get("enable_subtotal")
        model.enable_summary = chart.get("enable_summary")
        model.enable_subtotal_col = chart.get("enable_subtotal_col")
        model.enable_subtotal_col_summary = chart.get("enable_subtotal_col_summary")
        model.enable_subtotal_row = chart.get("enable_subtotal_row")
        model.enable_subtotal_row_summary = chart.get("enable_subtotal_row_summary")
        model.subtotal_row_summary_formula_mode = chart.get("subtotal_row_summary_formula_mode")
        model.aggregation = chart.get("aggregation")
        model.pre_comparison = chart.get("pre_comparison")
    if model.pagination.page_size and model.pagination.page_size > 0:
        page_size = model.pagination.page_size
    else:
        page_size = chart.get("page_size")

    model.external_subject_ids = _sort_external_subject_ids(
        dashboard["main_external_subject_id"], model.external_subject_ids
    )
    model.dataset_field_dict = _dataset_field_dict(model.dataset_id, model.external_subject_ids)
    model.dataset = proxy_dataset_service.get_dataset(model.dataset_id)
    # 当前报告，数据集记录到变量g中用于日志记录
    add_api_dataset_params(g, sql_from="design_viewreport", dataset_id=model.dataset_id, report_id=model.dashboard_id,
                           dashboard=dashboard, dataset=model.dataset)
    model.dims = _get_chart_section_data(model, section_type="dims", no_data=no_data)
    model.original_dims = chart_repository.get_dims_by_chart_id(model.id)
    model.comparisons = _get_chart_section_data(model, section_type="comparisons", no_data=no_data)
    if model.comparisons and model.dataset.get('external_type') == "pulsar_indicator":
        model.aggregation = 1
    model.original_comparisons = chart_repository.get_comparisons_by_chart_id(model.id)
    model.nums = _get_chart_section_data(model, section_type="nums", no_data=no_data)
    model.extra_nums = _get_chart_section_data(model, section_type="extra_nums", no_data=no_data)
    model.extra_nums = ExtraNumsTool.mark_model_extra_nums(model.extra_nums)
    ExtraNumsTool.push_extra_num_to_model_nums(model)
    model.original_nums = chart_repository.get_nums_by_chart_id(model.id)
    model.desires = _get_chart_section_data(model, section_type="desires", no_data=no_data)
    model.original_desires = chart_repository.get_desires_by_chart_id(model.id)
    model.chart_code = model.chart_code if model.chart_code else chart.get("chart_code")
    model.marklines = _get_marklines_for_result(model.id)
    model.pagination.page_size = page_size
    model.display_item = _get_display_item(chart, model)
    dashboard_filters = dashboard_repository.get_dashboard_filter_dataset_fields(model.dashboard_id)
    dashboard_filters = dashboard_service.batch_get_operators(dashboard_filters)
    dashboard_filter_relations = dashboard_repository.get_dashboard_filter_relation_dataset_fields(model.dashboard_id)
    dashboard_filter_relations = dashboard_service.batch_get_operators(dashboard_filter_relations)
    model.dashboard_filters = dashboard_service.get_dashboard_filters_v2(
        current_dataset_id=model.dataset_id,
        dashboard_filters=dashboard_filters,
        dashboard_filter_relations=dashboard_filter_relations,
    )
    # dashboard_var_filter = DashboardVarFilterNodeModel(dashboard_id=model.dashboard_id, node_name='dashboard_var_filters')
    # model.dashboard_var_filters = json.dumps(dashboard_var_filter.init_data())
    model.filters = _get_chart_section_data(model, section_type="filters", no_data=no_data)
    (
        model.penetrate_relation,
        model.penetrate_filter_relation,
        model.penetrate_var_filter_relation,
    ) = _get_penetrate_relation(model.id)
    # 如果是穿透层则获取穿透单图顶层filter_relation给当前穿透层
    if chart.get("parent_id"):
        model.filter_relation = repository.get_value(
            "dashboard_chart", {"level_code": chart.get("level_code")[0:7]}, "filter_relation"
        )
    # 单图的字段排序配置
    field_sorts = chart_repository.get_field_sorts_by_chart_id(model.id)
    model.field_sorts = [] if not field_sorts else field_sorts
    # 处理new_order
    assign_new_order(field_sorts, model)
    # 提取间接访问条件
    model.indirect_query_map = chart_utils.extract_indirect_query_map(model.chart_filter_conditions, model.query_vars)
    model.filter_selector_dict = _get_filter_selector_dict(model.chart_filter_conditions, model.id)
    model.common_datetime_selector_dict = _get_filter_selector_dict(model.common_datetime_conditions, model.id)
    model.linkage_selector_dict = _get_linkage_selector_dict(model.chart_linkage_conditions, model.id)
    model.keyword_values = chart_utils.batch_get_keyword_values(dashboard_filters, model.filters)
    model.layout_extend = json.loads(chart.get('layout_extend')) if chart.get('layout_extend') else {}
    # 适配model
    adapt_chart_data_model(model)


class ExtraNumsTool:
    __already_in_nums_flag__ = 'extra_num_already_in_nums'

    @staticmethod
    def generate_extra_suffix(data: dict):
        data = deepcopy(data)
        data = sorted(data.items(), key=lambda x: x[0])
        to_encrypted = '&'.join([f'{val[0]}={val[1]}' for val in data])
        dynamic_suffix = hashlib.md5(to_encrypted.encode()).hexdigest()[8:14]
        return f'{EXTRA_NUMS_SUFFIX}_{dynamic_suffix}'

    @staticmethod
    def mark_model_extra_nums(extra_nums):
        # 给extra_nums打上独特的标记
        if not extra_nums:
            return
        for extra_num in extra_nums:
            extra_suffix = ExtraNumsTool.generate_extra_suffix(extra_num)
            extra_num['extra_suffix'] = extra_suffix
            extra_num['subtotal_row_formula_mode'] = ChartNumSubtotalFormulaMode.No.value  # 不计算行小计
            extra_num['alias'] = f"{extra_num['alias']}{extra_suffix}"
        return extra_nums

    @staticmethod
    def deal_dataset_dict(model, extra_num, extra_suffix):
        # 生成一个额外字段的dataset_dict
        origin_num_id = extra_num.get('dataset_field_id')
        new_num_id = f'{origin_num_id}{extra_suffix}'
        extra_num['num'] = extra_num['dataset_field_id'] = new_num_id
        origin_dataset_dict = model.dataset_field_dict.get(origin_num_id) or {}
        if origin_dataset_dict:
            new_dataset_dict = deepcopy(origin_dataset_dict)
            new_dataset_dict['id'] = new_num_id
            new_dataset_dict['alias_name'] = f'{origin_dataset_dict["alias_name"]}{extra_suffix}'
            # 如额外字段已经有同一个聚合方式在nums里面
            # 这里不会在取数SQL里面再加一个，直接用nums里面的
        else:
            new_dataset_dict = {}
        new_dataset_dict['extra_suffix'] = extra_suffix
        model.dataset_field_dict[new_num_id] = new_dataset_dict

    @staticmethod
    def generate_unique_nums_map(nums) -> dict:
        return {
            f'{num.get("num", "").replace(num.get("extra_suffix", ""), "")}-{num.get("formula_mode")}': num
            for num in nums
        }

    @staticmethod
    def make_extra_nums_dataset_flag(model, unique_nums_map, unique_extra_nums_map):
        # 给虚拟出来的数据打标记，标记这个额外字段在原本的nums中存在
        not_in_nums = []
        for key, val in unique_extra_nums_map.items():
            if key in unique_nums_map:
                dataset_dict = model.dataset_field_dict.get(val.get('num')) or {}
                dataset_dict[ExtraNumsTool.__already_in_nums_flag__] = True
            else:
                not_in_nums.append(val)
        return not_in_nums

    @staticmethod
    def push_extra_num_to_model_nums(model):
        # 将额外的度量取数放入现在的取数中
        model.nums = model.nums or []
        model.extra_nums = model.extra_nums or []
        for extra_num in model.extra_nums:
            extra_suffix = extra_num.get('extra_suffix')
            if not extra_suffix:
                extra_suffix = ExtraNumsTool.generate_extra_suffix(extra_num)
                extra_num['extra_suffix'] = extra_suffix
            # 生成一个dataset_dict
            ExtraNumsTool.deal_dataset_dict(model, extra_num, extra_suffix)

        # 只额外请求不在已有的nums中的字段
        unique_nums_map = ExtraNumsTool.generate_unique_nums_map(model.nums)
        unique_extra_nums_map = ExtraNumsTool.generate_unique_nums_map(model.extra_nums)
        not_in_nums = ExtraNumsTool.make_extra_nums_dataset_flag(model, unique_nums_map, unique_extra_nums_map)
        model.nums.extend(not_in_nums)


def _sort_external_subject_ids(main_external_subject_id, external_subject_ids):
    """
    对外部主题排序，主要主题排在第一位
    :param main_external_subject_id:
    :param external_subject_ids:
    :return:
    """
    return sorted(
        external_subject_ids, key=lambda external_subect_id: int(external_subect_id != main_external_subject_id)
    )


def _dataset_field_dict(dataset_id: str, external_subject_ids: List[str]):
    if external_subject_ids:
        return proxy_dataset_service.get_external_subjects_dataset_field_dict(external_subject_ids)
    return proxy_dataset_service.get_dataset_field_dict_with_field_id(dataset_id)


def assign_new_order(field_sorts, model):
    # 如果新排序前端未传 则从dashboard_chart_field_sort中查找并赋值
    if not model.new_order:
        model.new_order = field_sorts


def _get_var_relations(chart_id: str) -> list:
    return repository.get_data("dashboard_dataset_vars_relation", {""})


def _get_penetrate_relation(chart_id: str) -> Tuple[List[Any], List[Any], List[Any]]:
    """
    获取穿透关联关系、穿透后筛选关联关系数据
    :param str chart_id:
    :return:
    """
    penetrate_relation = []
    pefilter_relation = []
    var_pefilter_relation = []
    relations = chart_repository.get_penetrate_relation_by_chart_id(chart_id)
    if not relations:
        return penetrate_relation, pefilter_relation, var_pefilter_relation
    for relation in relations:
        item = {
            'parent_chart_field_id': relation['parent_chart_field_id'],
            'child_chart_field_id': relation['child_chart_field_id'],
        }
        if relation.get('type') == ChartPenetrateRelationType.PenetrateType.value:
            penetrate_relation.append(item)
        elif relation.get('type') == ChartPenetrateRelationType.PenetrateFilterType.value:
            pefilter_relation.append(item)
        elif relation.get('type') == ChartPenetrateRelationType.VarPenetrateFilterType.value:
            item['parent_chart_var_id'] = relation.get('parent_chart_var_id', '')
            var_pefilter_relation.append(item)

    return penetrate_relation, pefilter_relation, var_pefilter_relation


def _get_display_item(chart: Dict[str, Union[int, str]], model: ChartDataModel) -> Optional[Dict[str, str]]:
    """
    获取分页值数据
    :param chart:
    :param dashboard_chart.models.ChartDataModel model:
    :return:
    """
    # 注意：此功能在单图内筛选器上强依赖，所以单图内筛选器需要前端临时指定display_item，否则后端不知道是单图内筛选器取数场景
    # 优先使用当前传入的display_item，它在model里面
    if model.display_item:
        model.display_item = (
            json.loads(model.display_item) if isinstance(model.display_item, str) else model.display_item
        )
        if model.display_item.get("top_head") or model.display_item.get("top_tail"):
            return model.display_item
    # 再去取单图对应的display_item
    return json.loads(chart.get("display_item")) if chart.get("display_item") else None


def _match_field_info(
        query_data: List[Dict[str, Union[str, int, None]]], field_data_dict: Dict[str, Dict[str, Union[str, int, None]]]
) -> None:
    """
    匹配对应数据集字段信息
    :param query_data:
    :param field_data_dict:
    :return:
    """
    for single_query_data in query_data:
        dataset_field_id = single_query_data.get("dataset_field_id", "")
        if not dataset_field_id:
            continue
        field_data = field_data_dict.get(dataset_field_id)
        if not field_data:
            continue
        for k, v in field_data.items():
            if k not in ['rank']:
                single_query_data.update({k: v})


def _get_chart_section_data(
        model: ChartDataModel, section_type: Optional[str] = None, no_data: bool = True
) -> List[Dict[str, Union[str, int, None]]]:
    """
    获取单图组成部分的数据
    :param dashboard_chart.models.ChartDataModel model:
    :param section_type:
    :param bool no_data:
    :return:
    """
    func_dict = {
        "dims": {"func": chart_repository.get_dims_by_chart_id, "model_data": model.dims},
        "nums": {"func": chart_repository.get_nums_by_chart_id, "model_data": model.nums},
        "extra_nums": {"func": None, "model_data": model.extra_nums},
        "comparisons": {"func": chart_repository.get_comparisons_by_chart_id, "model_data": model.comparisons},
        "desires": {"func": chart_repository.get_desires_by_chart_id, "model_data": model.desires},
        "filters": {"func": chart_repository.get_chart_filter_by_chart_id, "model_data": model.filters},
    }
    # 如果传了model_data，维度字段使用前端传的维度字段
    function = func_dict.get(section_type).get("func")
    model_data = func_dict.get(section_type).get("model_data") or []

    if (section_type == 'dims' and model_data) or (not no_data):
        query_data = model_data
    elif section_type == 'extra_nums':
        query_data = model_data
    else:
        # 2021-0508 处理跳转隐藏字段传参需求：需要隐藏的字段需要前端通过nums字段传递
        query_data = []
        if (section_type == 'nums' and model_data) or (not no_data):
            query_data.extend(model_data)
        query_data.extend(function(model.id) or [])

    if section_type == "filters":
        query_data = split_operators(query_data)

    if not query_data:
        return query_data

    # 批量获取数据集字段的表数据
    field_id_list = list()
    for item in query_data:
        if "dataset_field_id" not in list(item.keys()):
            item["dataset_field_id"] = item.get("dim") if item.get("dim") else item.get("num")
        field_id_list.append(item.get("dataset_field_id"))
    field_data_dict = proxy_dataset_service.batch_get_formatted_fields_by_field_id(field_id_list)
    # 数据集字段数据组装到单图数据dict中
    _match_field_info(query_data, field_data_dict)

    return query_data


def _get_marklines_for_result(dashboard_chart_id: str) -> List[Any]:
    """
    为result接口组装marklines参数
    :param string dashboard_chart_id: 单图id
    :return:
    """
    marklines = chart_repository.get_dataset_markline_by_chart_id(dashboard_chart_id)
    for markline in marklines:
        markline["markline"] = markline["num"]
        markline["nums"] = chart_repository.get_num_by_field_id(markline["num"], markline["dashboard_chart_id"])
    return marklines


def get_union_field_data(chart_responder_id, field_responder_id, chart_data_model):
    """
    获取
    :param chart_data_model:
    :param chart_responder_id:
    :param field_responder_id:
    :return:
    """
    data = dict()
    if chart_data_model.dataset_id:
        data = proxy_dataset_service.get_formatted_fields_by_field_id(field_responder_id)
        if data and data.get("id"):
            data["dim"] = data["id"]
        data.update({"dashboard_chart_id": chart_responder_id})
    return data


def get_chart_selector_data(chart_initiator_id, chart_responder_id, orig_dataset_field_id):
    """

    :param chart_initiator_id:
    :param chart_responder_id:
    :param orig_dataset_field_id:
    :return:
    """
    selector_list = chart_repository.get_chart_selector(chart_initiator_id, chart_responder_id)
    if not selector_list or not len(selector_list):
        return list()
    for single_selector in selector_list:
        field_responder_id = single_selector.get("field_responder_id", "")
        if single_selector.get("is_same_dataset") and orig_dataset_field_id:
            field_responder_id = orig_dataset_field_id
        extra_field_dict = proxy_dataset_service.get_formatted_fields_by_field_id(field_responder_id)
        for k, v in extra_field_dict.items():
            single_selector.update({k: v})
    return selector_list


def get_selector_data_dict(conditions, chart_responder_id):
    """
    获取selector数据
    :param conditions:
    :param chart_responder_id:
    :return:
    """
    selector_data_dict = dict()
    if not conditions or not isinstance(conditions, list):
        return selector_data_dict
    for single_condition in conditions:
        dim = single_condition.get("dim", "")
        chart_initiator_id = dim.get("dashboard_chart_id", "") if dim else ""  # 主动联动单图id
        orig_field_id = dim.get("dataset_field_id", "") if dim.get("dataset_field_id", "") else dim.get("dim", "")
        if not chart_initiator_id:
            break
        if chart_initiator_id not in list(selector_data_dict.keys()):
            selector_data_dict.update({chart_initiator_id: list()})
        selector_list = get_chart_selector_data(chart_initiator_id, chart_responder_id, orig_field_id)
        selector_data_dict[chart_initiator_id] = selector_list
    return selector_data_dict


def get_new_linkage_filter_data_dict(conditions, chart_responder_id, get_data_func):
    '''获取新版联动数据'''
    linkage_data_dict = {}
    for single_condition in conditions:
        if not single_condition:
            continue
        chart_initiator_id = single_condition["chart_id"]  # 发起者单图id
        if not chart_initiator_id:
            continue
        linkage_data_dict.setdefault(chart_initiator_id, [])
        linkage_data = get_data_func(chart_initiator_id, chart_responder_id)
        for single_data in linkage_data:
            field_responder_id = single_data.get("field_responder_id", "")
            extra_field_dict = proxy_dataset_service.get_formatted_fields_by_field_id(field_responder_id)
            for k, v in extra_field_dict.items():
                single_data.update({k: v})

        linkage_data_dict[chart_initiator_id] = linkage_data

    return linkage_data_dict


def get_new_linkage_data_dict(conditions, chart_responder_id):
    return get_new_linkage_filter_data_dict(conditions, chart_responder_id, chart_repository.get_new_chart_linkage)


def get_new_filter_data_dict(conditions, chart_responder_id):
    filter_data = get_new_linkage_filter_data_dict(
        conditions, chart_responder_id, chart_repository.get_available_new_chart_filter
    )
    return filter_data


def get_penetrate_relation_dict_by_chart_id(chart_id):
    """
    获取穿透关联关系
    :param chart_id:
    :return:
    """
    parent_field_id2_child_field_id = {}
    penetrate_field_list = chart_repository.get_penetrate_relation_by_chart_id(chart_id)
    if penetrate_field_list:
        for item in penetrate_field_list:
            parent_field_id2_child_field_id[item["parent_chart_field_id"]] = item["parent_chart_field_id"]
    return parent_field_id2_child_field_id


def get_operators_by_filter_id(dashboard_chart_filter_id: str) -> List[Any]:
    """
    从新的子表获取operators数据
    :param dashboard_chart_filter_id:
    :return:
    """
    if not dashboard_chart_filter_id:
        return list()
    # 获取新表数据
    operators = chart_repository.get_dashboard_chart_filter_relation(dashboard_chart_filter_id)
    return list() if not operators else operators


def batch_get_operators(dashboard_chart_filters: List[Dict[str, str]]) -> List[Dict[str, str]]:
    """
    批量获取单图筛选operators数据
    :param dashboard_chart_filters:
    :return:
    """
    if not dashboard_chart_filters:
        return dashboard_chart_filters
    for dashboard_chart_filter in dashboard_chart_filters:
        # 从新的子表中获取筛选条件
        dashboard_chart_filter["operators"] = get_operators_by_filter_id(dashboard_chart_filter["filter_id"])
    return dashboard_chart_filters


def split_operators(data_list: List[Any], filter_source: str = "chart_filters") -> List[Any]:
    """
    拆分operators数据
    :param data_list:
    :param filter_source:
    :return:
    """
    operators_key = "operators"
    new_data_list = list()
    if not data_list:
        return data_list
    for single_item in data_list:
        operators = single_item.get(operators_key)
        if not operators or not single_item:
            new_data_list.append(single_item)
            continue
        for single_operator in operators:
            new_single_item = deepcopy(single_item)
            if operators_key in new_single_item.keys():
                new_single_item.pop(operators_key)
            new_single_item["operator"] = single_operator.get("operator")
            new_single_item["col_value"] = single_operator.get("col_value")
            if filter_source == "dashboard_filters":
                new_single_item["select_all_flag"] = single_operator.get("select_all_flag", 0)
            new_data_list.append(new_single_item)
    return new_data_list


def get_chart_list(
        dashboard_id: str,
        include_details: Optional[bool] = None,
        is_show_zaxis: bool = False,
        need_extra_chart_flag: bool = False,
) -> List[Dict[str, Union[str, None, int, List[Dict[str, Union[str, int, None]]]]]]:
    """
    获取看板下所有单图
    :param str dashboard_id:
    :param bool include_details:
    :param bool is_show_zaxis: 是否区分显示次轴数据
    :param bool need_extra_chart_flag: 是否需要获取穿透内的单图
    :return:
    """
    data = chart_repository.get_chart_id_list(dashboard_id)
    if not data:
        return []
    if not include_details:
        return data
    chart_list = ChartDefinitionLogic().get_charts(
        dashboard_id, include_details=True, is_show_zaxis=is_show_zaxis, need_extra_chart_flag=need_extra_chart_flag
    )
    return chart_list


def delete_chart(chart_id):
    """
    删除单图
    :param chart_id:
    :return:
    """
    return ChartDefinitionLogic().delete_chart(chart_id)


# pylint: disable=W0613
def upgrade_dashboard_chart_filter(task_id):
    """
    （临时处理）升级筛选器配置数据到新表中
    :param task_id:
    :return:
    """
    sql = 'select id, filter_config from dashboard_chart_backup where upgrade_status=0'
    params = {}
    with get_db() as db:
        data = db.query(sql, params)
    if not data:
        return True
    id_list = []
    chart_id = None
    try:
        for chart in data:
            chart_id = chart.get('id')
            filter_config = chart.get('filter_config')
            update_chart_component_filter(filter_config, chart_id)
            id_list.append(chart_id)
            repository.update_data('dashboard_chart_backup', {'upgrade_status': 1}, {'id': chart_id})
    except Exception as e:
        logger.exception(e)
        repository.update_data(
            'dashboard_chart_backup', {'upgrade_status': 1, 'upgrade_msg': e.__str__()}, {'id': chart_id}
        )
    return True


def update_chart_component_filter(filter_config, chart_id):
    """
    （临时处理）将数据存入筛选器配置新表
    :param str chart_id: 筛选器单图id
    :param str filter_config: 筛选配置json数据
    """
    if not chart_id:
        raise UserError(message="单图id为空")
    # pylint: disable=too-many-nested-blocks
    db = get_db()
    db.begin_transaction()
    try:
        _debugger.log("chart_id:%s", chart_id)
        _debugger.log({"filter_config": filter_config})
        # 先删除原配置，再进行数据存储
        repository.delete_data('dashboard_component_filter', {'chart_initiator_id': chart_id})
        repository.delete_data('dashboard_component_filter_field_relation', {'chart_id': chart_id})

        # filter_config为空表示删除筛选组件配置数据
        if not filter_config:
            db.commit()
            return True

        filter_config_data = json.loads(filter_config)
        filter_list = filter_config_data.get('list', [])
        filter_field_dict = filter_config_data.get('relation', {})

        dataset_id = repository.get_data_scalar('dashboard_chart', {'id': chart_id}, 'source')
        dataset_field_list = proxy_dataset_service.get_dataset_field(dataset_id)
        init_dataset_field_dict = {}
        for dataset_field in dataset_field_list:
            init_dataset_field_dict[dataset_field.get('id')] = [
                dataset_field.get('col_name', ''),
                dataset_field.get('origin_col_name', ''),
                dataset_field.get('alias', ''),
            ]

        # 1. 保存chart_component_filter表
        _save_chart_component_filter(filter_list, chart_id, dataset_id, filter_field_dict, init_dataset_field_dict)

        db.commit()
        return True
    except Exception as e:
        db.rollback()
        if not isinstance(e, (UserError, FriendlyError)):
            logger.exception(e)
        raise UserError(message="筛选配置数据解析失败!")


def _op_component_filter_relation(
        chart_id, filter_id, filter_dict, init_dataset_field_dict, init_col_name, responder_dataset_id
):
    for key, item in filter_dict.items():
        if responder_dataset_id != key:
            continue
        # 如果active为false，则删除主表记录并跳过
        if filter_dict.get('active') is False:
            repository.delete_data('dashboard_component_filter', {'id': filter_id})
            continue
        field_responder_id = item.get('id')
        check_flag, field_initiator_id = get_field_initiator_id(init_col_name, init_dataset_field_dict)
        if not check_flag or not field_initiator_id:
            continue
        data = {
            'field_initiator_id': field_initiator_id,
            'field_responder_id': field_responder_id,
            'chart_id': chart_id,
            'filter_id': filter_id,
        }
        field_model = DashboardComponentFilterFieldModel(**data)
        field_model.id = seq_id()
        field_model.validate()
        repository.add_model(
            'dashboard_component_filter_field_relation', field_model, DashboardComponentFilterFieldModel.__slots__
        )
        _debugger.log({"component_filter_relations": field_model.get_dict()})


def _save_chart_component_filter(filter_list, chart_id, dataset_id, filter_field_dict, init_dataset_field_dict):
    """
    保存chart_component_filter表
    :param filter_list:
    :param chart_id:
    :param dataset_id:
    :param filter_field_dict:
    :return:
    """
    for chart_responder_id in filter_list:
        data = {'chart_initiator_id': chart_id, 'chart_responder_id': chart_responder_id}
        responder_dataset_id = repository.get_data_scalar('dashboard_chart', {'id': chart_responder_id}, 'source')
        if not responder_dataset_id:
            raise FriendlyError(message="获取dataset_id失败!单图id:{chart_id}".format(chart_id=chart_responder_id))
        data['dataset_id'] = responder_dataset_id
        data['is_same_dataset'] = 1 if dataset_id == responder_dataset_id else 0
        filter_model = DashboardComponentFilterModel(**data)
        filter_id = seq_id()
        filter_model.id = filter_id
        filter_model.validate()
        repository.add_model('dashboard_component_filter', filter_model, DashboardComponentFilterModel.__slots__)
        _debugger.log({"dashboard_component_filter": filter_model.get_dict()})

        # 2. 保存dashboard_component_filter_field_relation表
        for col_name, item_dict in filter_field_dict.items():
            _op_component_filter_relation(
                chart_id, filter_id, item_dict, init_dataset_field_dict, col_name, responder_dataset_id
            )


def get_field_initiator_id(checked_col_name, init_dataset_field_dict):
    """
    获取发起方字段id
    :param checked_col_name:
    :param init_dataset_field_dict:
    :return:
    """
    field_initiator_id = ""
    if not checked_col_name or not init_dataset_field_dict:
        return False, field_initiator_id
    for key, item in init_dataset_field_dict.items():
        if checked_col_name in item:
            return True, key
    return False, field_initiator_id


def _get_filter_selector_dict(conditions, chart_responder_id):
    filter_data_dict = {}
    if not conditions:
        return filter_data_dict
    for single_condition in conditions:
        if not single_condition:
            continue
        chart_initiator_id = single_condition["chart_id"]  # 发起者单图id
        if not chart_initiator_id:
            continue
        filter_data_dict.setdefault(chart_initiator_id, [])
        filter_data = _get_filter_data(chart_initiator_id, chart_responder_id)
        for single_data in filter_data:
            field_responder_id = single_data.get("field_responder_id", "")
            extra_field_dict = proxy_dataset_service.get_formatted_fields_by_field_id(field_responder_id)
            for k, v in extra_field_dict.items():
                single_data.update({k: v})

        filter_data_dict[chart_initiator_id] = filter_data

    return filter_data_dict


def _get_filter_data(chart_initiator_id, chart_responder_id):
    # 获取新版的单图筛选数据
    sql = '''
    select
        cdl.id,
        cdl.chart_id as chart_initiator_id,
        cdl.dataset_field_id as field_initiator_id,
        cdl.dataset_id as initiator_dataset_id,
        cdl.dashboard_id,
        cdlf.chart_responder_id,
        cdlf.dataset_responder_id as dataset_id,
        cdlf.field_responder_id
    from
        dashboard_filter_chart as cdl
        left join dashboard_filter_chart_relation as cdlf on cdl.id = cdlf.filter_id
    where
        cdl.chart_id = %(chart_initiator_id)s
        and cdl.available = 1
        and chart_responder_id = %(chart_responder_id)s
    '''

    params = {"chart_initiator_id": chart_initiator_id, "chart_responder_id": chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


def _get_linkage_selector_dict(conditions, chart_responder_id):
    linkage_data_dict = {}
    if not conditions:
        return linkage_data_dict
    for single_condition in conditions:
        if not single_condition:
            continue
        chart_initiator_id = single_condition["chart_id"]  # 发起者单图id
        if not chart_initiator_id:
            continue
        linkage_data_dict.setdefault(chart_initiator_id, [])
        linkage_data = _get_linkage_data(chart_initiator_id, chart_responder_id)
        for single_data in linkage_data:
            field_responder_id = single_data.get("field_responder_id", "")
            extra_field_dict = proxy_dataset_service.get_formatted_fields_by_field_id(field_responder_id)
            for k, v in extra_field_dict.items():
                single_data.update({k: v})

        linkage_data_dict[chart_initiator_id] = linkage_data

    return linkage_data_dict


def _deal_divisor_zero(expression):
    # 处理除数是0的情况，将除数的表达式进行替换
    divisor_functions = re.findall(r'/.*?(\w+\(.*?\))', expression)
    for func in divisor_functions:
        expression = expression.replace(func, f'(CASE {func} WHEN 0 THEN null ELSE {func} END)')
    return expression


def check_subtotal_col_formula_expression(kwargs):
    model = SubtotalColFormulaExpressionCheckModel(**kwargs)
    model.validate()

    dataset_id = model.dataset_id
    dataset_field_id = model.dataset_field_id
    subtotal_col_formula_expression = model.expression

    replaced_raw_exp = _deal_divisor_zero(subtotal_col_formula_expression)

    if not re.findall(r'(sum|max|min|avg|count|distinct)', subtotal_col_formula_expression, re.I):
        raise UserError(message='列小计计算表达必须包含聚合函数！')

    # 先替换高级字段中的数据集字段，再处理变量，解决变量中包含[]高级字段中引用字段的占位符导致的bug问题
    replaced_field_exp = advance_field_helper.expression_real_name(replaced_raw_exp, dataset_id)
    # 判断是否带有变量, 存在变量则需要替换变量值
    model = DatasetFieldModel(**dict(id=dataset_field_id, dataset_id=dataset_id))
    _, expression_advance_var = handle_expression_advance_vars(model, replaced_field_exp)

    try:
        is_valid, msg = ad_validate_expression(expression_advance_var, dataset_id)
        if not is_valid:
            raise UserError(message=msg)
    except Exception:
        logger.error('列小计计算表达式错误，%s' % traceback.format_exc())
        raise UserError(message="列小计计算表达式错误， 请检查列小计的表达式！")


def _get_linkage_data(chart_initiator_id, chart_responder_id):
    sql = '''
    select
        cdl.id,
        cdl.chart_id as chart_initiator_id,
        cdl.dataset_field_id as field_initiator_id,
        cdl.dataset_id as initiator_dataset_id,
        cdl.dashboard_id,
        cdlf.chart_responder_id,
        cdlf.dataset_responder_id as dataset_id,
        cdlf.field_responder_id
    from
        dashboard_linkage as cdl
        left join dashboard_linkage_relation as cdlf on cdl.id = cdlf.link_id
    where
        cdl.chart_id = %(chart_initiator_id)s
        and chart_responder_id = %(chart_responder_id)s
    '''

    params = {"chart_initiator_id": chart_initiator_id, "chart_responder_id": chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


# 元数据返回配置
class MetadataConfig:

    @CachedProperty
    def chart_fetch_num(self):
        # 获取取数接口一次取的数量
        try:
            return int(config.get('App.chart_batch_fetch', 6))
        except Exception as e:
            logger.error(f'获取组价取数数目异常：{str(e)}')
            return 6

    def get_filter_pre_load(self):
        try:
            filter_pre_load = get_user_system_setting(category='chart_filter', item='filter_pre_load') or '0'
            return int(filter_pre_load)
        except Exception as e:
            logger.error(f'获取组价filter_pre_load异常：{str(e)}')
            return 0

    def get_is_new_jump(self):
        from dmplib.saas.project import get_project_info
        try:
            is_new_jump = 0
            data = get_project_info(g.code) or {}
            if data and data.get('is_new_jump'):
                is_new_jump = data.get('is_new_jump')
        except Exception as e:
            logger.error(f'获取跳转配置异常：{str(e)}')
            is_new_jump = 0
        return is_new_jump

    @staticmethod
    def init_dashboard_data(dashboard_id):
        dashboard = repository.get_one('dashboard', fields=['*'], conditions={'id': dashboard_id}) or {}
        return dashboard

    @staticmethod
    def get_auto_snap(dashboard):
        return bool(dashboard.get('auto_snap', 0))

    @staticmethod
    def dashboard_has_snapshot(snap_id, dashboard_id):
        if not snap_id:
            return False
        return bool(versioned_cached.dashboard_has_snapshot(snap_id, dashboard_id))

    @staticmethod
    def get_root_dashboard_by_dashboard(dashboard):
        """
        获取当前报告的根报告
        :param dashboard:
        :return:
        """
        if not dashboard:
            return {}
        # 当前就是根报告，则不用查询
        if dashboard.get("type") == DashboardType.File.value:
            root_dashboard = dashboard
        else:
            root_dashboard = get_root_dashboard_by_level_code(dashboard.get('level_code', ''))
        return root_dashboard


METADATA_CONFIG = MetadataConfig()


# 元数据返回报告配置信息
def return_dashboard_chart_config(func):
    @functools.wraps(func)
    def wrapper(request, response, *args, **kwargs):
        snap_id = kwargs.get("snap_id")
        dashboard_id = kwargs.get("id")
        # logger.error()
        # 先校验报告的snap_id是不是和当前报告是匹配的
        # 不匹配的需要将当前的snap_id抹除掉，请求最新的发布数据
        is_correct_snap_id = METADATA_CONFIG.dashboard_has_snapshot(snap_id, dashboard_id)
        if not is_correct_snap_id:
            disable_current_request_versioned()
            kwargs.pop("snap_id", None)

        result = func(request, response, *args, **kwargs)

        if isinstance(result, (tuple, list)) and len(result) == 3:
            data = result[2]
            if isinstance(data, dict):
                dashboard = METADATA_CONFIG.init_dashboard_data(dashboard_id)
                data['chart_fetch'] = METADATA_CONFIG.chart_fetch_num
                data['is_new_jump'] = METADATA_CONFIG.get_is_new_jump()
                data['filter_pre_load'] = METADATA_CONFIG.get_filter_pre_load()
                data['enable_auto_snap'] = METADATA_CONFIG.get_auto_snap(dashboard)
                # 根据报告信息获取当前报告的根报告
                root_dashboard = METADATA_CONFIG.get_root_dashboard_by_dashboard(dashboard)
                # 返回标识，检查是否存在发布的设计时数据
                data['is_exists_released_design'] = check_dashboard_released_design_record(root_dashboard)
                # 是否是合法的snap_id
                data['is_correct_snap_id'] = is_correct_snap_id
                # 是否是重点大屏
                data['is_key'] = dashboard_service.check_dashboard_is_key(root_dashboard)
        return result

    return wrapper


# 记录组件元数据操作记录
def upload_metadata_log_to_aliyun(metadata):
    try:
        # 默认开启记录数据服务中心请求日志，指定租户可以不开启
        allowed_tenants = str(config.get('Product.record_metadata_op_tenants', ''))
        tenant = g.code if hasattr(g, 'code') else ""
        if tenant and tenant in allowed_tenants.split(','):
            metadata_json = metadata
            if isinstance(metadata, dict):
                metadata_json = json.dumps(metadata, ensure_ascii=False)

            log_dict = {
                'module_type': FastLogger.ModuleType.DASHBOARD_MOBILE_SCREEN,
                'biz_type': FastLogger.BizType.METADATA_EDIT,
                'biz_id': metadata.get("dashboard_id"),
                'biz_name': metadata.get("dashboard_name"),
                'is_success': 1,
                'error_msg': '记录组件元数据操作记录',
                'extra_info': metadata_json,
            }
            FastLogger.BizErrorFastLogger(**log_dict).record()
    except Exception as e:
        logger.error("记录元数据操作日志失败：" + str(e))


@spawn_background_task
def record_released_data(model):
    from dashboard_chart.services.metadata_service import get_screens_release_metadata_v2
    from dashboard_chart.services.released_dashboard_service import _get_all_release_dashboard
    try:

        all_release_dashboard = _get_all_release_dashboard(model)
        for dashboard in all_release_dashboard:
            dashboard_id = dashboard.get('dashboard', {}).get('id', '')
            dashboard_name = dashboard.get('dashboard', {}).get('name', '')
            _, meta = get_screens_release_metadata_v2(snapshot_id=dashboard_id)
            # 天眼有限制日志大小：单条日志内容不超过512KB，这里将元数据拆分记录
            charts = meta.get('first_report', {}).pop('charts', [])
            meta.pop('installed_component', None)

            log_dict = {
                'module_type': FastLogger.ModuleType.DASHBOARD_MOBILE_SCREEN,
                'biz_type': FastLogger.BizType.METADATA_RELEASED,
                'biz_id': dashboard_id,
                'biz_name': dashboard_name,
                'is_success': 1,
                'error_msg': '主要的元数据信息',
                'extra_info': space_saving_json(meta),
            }
            FastLogger.BizErrorFastLogger(**log_dict).record()

            batch_size = 50
            batches = [charts[i:i + batch_size] for i in range(0, len(charts), batch_size)]
            for idx, batch_charts in enumerate(batches):
                log_dict = {
                    'module_type': FastLogger.ModuleType.DASHBOARD_MOBILE_SCREEN,
                    'biz_type': FastLogger.BizType.METADATA_RELEASED,
                    'biz_id': dashboard_id,
                    'biz_name': dashboard_name,
                    'is_success': 1,
                    'error_msg': f'第{idx + 1}批{len(batch_charts)}个组件',
                    'extra_info': space_saving_json(batch_charts),
                }
                FastLogger.BizErrorFastLogger(**log_dict).record()
    except Exception as e:
        logger.error("记录元数据发布日志失败：" + str(e))


def record_metadata_edit_data(metadata):
    try:
        del_models = getattr(g, 'del_models', [])
        update_models = getattr(g, 'update_models', [])
        list_insert_table2dict = getattr(g, 'list_insert_table2dict', [])
        result = {}
        # result['metadata'] = metadata
        result['dashboard_id'] = metadata.get('metadata', {}).get('dashboard', {}).get('id', '')
        result['dashboard_name'] = metadata.get('metadata', {}).get('dashboard', {}).get('name', '')
        result['change'] = {
            'delete': [{m.get_table_name(): m.get_dict()} for m in del_models],
            'update': [{m.get_table_name(): m.get_dict()} for m in update_models],
            'list_insert_table2dict': list_insert_table2dict,
        }
        record_user_edit_dashboard(metadata)
        upload_metadata_log_to_aliyun(result)
    except Exception as e:
        logger.error("记录元数据编辑日志失败：" + str(e))


def record_user_edit_dashboard(metadata):
    import app_celery
    dashboard_id = metadata.get('metadata', {}).get('dashboard', {}).get('id', '')
    kwargs = {
        'account': getattr(g, 'account', ''),
        'user_id': getattr(g, 'userid', ''),
        'code': getattr(g, 'code', ''),
        'dashboard_id': dashboard_id,
        'edit_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    app_celery.record_dashboard_edit.apply_async(kwargs=kwargs, queue='parser')
