#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/11/15
# pylint: disable=W1202
"""
dashboard lock service
"""

# ---------------- 标准模块 ----------------
import logging
import json

# ---------------- 业务模块 ----------------
from dmplib.redis import conn as conn_redis
from dashboard_chart.utils import dashboard_cache
from dmplib.hug import g
from user.repositories import user_repository
from dmplib import config


logger = logging.getLogger(__name__)


ret_info_dict = {
    '1001': {'code': 1001, 'msg': '成功'},
    '1002': {'code': 1002, 'msg': '当前报告无须解锁'},
    '5001': {'code': 5001, 'msg': '当前报告已锁定，请稍后重试'},
    '5002': {'code': 5002, 'msg': '操作异常'},
    '5003': {'code': 5003, 'msg': '非法请求，获取用户信息失败'},
}


def get_ret_info(code, msg='', account='', user_name=''):
    """
    统一返回参数
    :param code:
    :param msg:
    :param account:
    :param user_name:
    :return:
    """
    ret_info = ret_info_dict.get(code)
    ret_info['msg'] = msg if msg else ret_info['msg']
    ret_info['account'] = account if account else ''
    ret_info['user_name'] = user_name if user_name else ''
    return ret_info


def get_user(account):
    """
    获取用户信息
    :param account:
    :return:
    """
    # 获取用户信息
    user = user_repository.get_user_info(account)
    user_name = user.get('name') if user else ''
    return user_name


def get_config_lock_seconds() -> int:
    """
    报告锁定缓存过期秒数
    :return:
    """
    minutes = config.get('Function.dashboard_lock_time', 30)
    try:
        seconds = int(minutes) * 60
    except Exception as e:
        logger.error('获取报告锁定时长配置信息异常,异常信息:{error_msg}'.format(error_msg=str(e)))
        seconds = 30 * 60
    return seconds


def get_dashboard_lock_cache_key(dashboard_id):
    """
    获取报告锁定缓存key
    :param dashboard_id:
    :return:
    """
    metadata_cache_key_prefix = dashboard_cache.get_metadata_cache_key_prefix()
    return '{prefix}:{business}:{dashboard_id}'.format(
        prefix=metadata_cache_key_prefix, business='dashboard_lock', dashboard_id=str(dashboard_id)
    )


def get_dashboard_lock_cache_data(cache_key, conn):
    """
    获取报告锁缓存数据
    :param cache_key:
    :param conn:
    :return:
    """
    cache_dashboard_lock_data = conn.get(cache_key)
    cache_dashboard_lock_data = (
        json.loads(cache_dashboard_lock_data.decode(encoding='utf-8')) if cache_dashboard_lock_data else dict()
    )
    return cache_dashboard_lock_data

def get_dashboard_lock_data(dashboard_id):
    """
    获取报告锁定状态
    :param dashboard_id:
    :return:
    """
    conn = conn_redis()
    cache_key = get_dashboard_lock_cache_key(dashboard_id)
    return get_dashboard_lock_cache_data(cache_key, conn)

def checkin_dashboard(dashboard_id):
    """
    报告登记锁定
    :param dashboard_id:
    :return:
    """
    try:
        # 加锁功能关闭的情况
        config_lock_seconds = get_config_lock_seconds()
        if not config_lock_seconds or (config_lock_seconds is not None and config_lock_seconds <= 0):
            return get_ret_info('1001')
        lock_data = dict()
        conn = conn_redis()
        cache_key = get_dashboard_lock_cache_key(dashboard_id)
        cache_dashboard_lock_data = get_dashboard_lock_cache_data(cache_key, conn)
        cache_account = cache_dashboard_lock_data.get('account', '')
        cache_user_name = cache_dashboard_lock_data.get('user_name', '')
        account = str(g.account) if g.account else ''

        if not account:
            return get_ret_info('5003')

        # 情况1，当前报告没有锁，新增锁并设置过期时间
        if not cache_dashboard_lock_data:
            user_name = get_user(account)
            lock_data['account'] = account
            lock_data['user_name'] = user_name
            conn.set(cache_key, json.dumps(lock_data), config_lock_seconds)
            return get_ret_info('1001', account=account, user_name=user_name)

        # 情况2，且当前操作用户是锁定用户，则刷新过期时间
        if cache_dashboard_lock_data and account == cache_account:
            conn.set(cache_key, json.dumps(cache_dashboard_lock_data), config_lock_seconds)
            return get_ret_info('1001', account=cache_account, user_name=cache_user_name)

        # 情况3，当前操作用户不是锁定用户，则禁止操作当前报告
        if cache_dashboard_lock_data and account != cache_account:
            return get_ret_info('5001', account=cache_account, user_name=cache_user_name)

        return get_ret_info('5001')
    except Exception as e:
        logger.error('报告锁定登记异常，异常信息:{error_msg}'.format(error_msg=str(e)))
        return get_ret_info('5002', msg=str(e))


def unlock_dashboard(dashboard_id):
    """
    报告解锁
    :param dashboard_id:
    :return:
    """
    try:
        # 加锁功能关闭的情况
        config_lock_seconds = get_config_lock_seconds()
        if not config_lock_seconds or (config_lock_seconds is not None and config_lock_seconds <= 0):
            return get_ret_info('1001')
        conn = conn_redis()
        cache_key = get_dashboard_lock_cache_key(dashboard_id)
        cache_dashboard_lock_data = get_dashboard_lock_cache_data(cache_key, conn)
        cache_account = cache_dashboard_lock_data.get('account', '')
        cache_user_name = cache_dashboard_lock_data.get('user_name', '')
        account = str(g.account) if g.account else ''

        if not account:
            return get_ret_info('5003')

        # 情况1，当前报告没有锁
        if not cache_dashboard_lock_data:
            return get_ret_info('1002')

        # 情况2，且当前操作用户是锁定用户，则解锁
        if cache_dashboard_lock_data and account == cache_account:
            conn.delete(cache_key)
            return get_ret_info('1001')

        # 情况3，当前操作用户不是锁定用户，则禁止操作当前报告
        if cache_dashboard_lock_data and account != cache_account:
            return get_ret_info('5001', account=cache_account, user_name=cache_user_name)

        return get_ret_info('5001')
    except Exception as e:
        logger.error('报告解锁异常，异常信息:{error_msg}'.format(error_msg=str(e)))
        return get_ret_info('5002', msg=str(e))
