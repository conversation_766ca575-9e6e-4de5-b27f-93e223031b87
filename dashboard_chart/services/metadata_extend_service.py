#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
services
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from dashboard_chart.repositories import metadata_repository
from base.enums import DashboardStatus
from healthy.services import dashboard_metadata_storage
from healthy.repositories import healthy_repository


def _op_redirects_data(redirects_data):
    dashboard_filter_info_dict = dict()
    for single_data in redirects_data:
        chart_redirect = single_data.get("chart_redirect")
        if not chart_redirect:
            continue
        for single_chart_redirect in chart_redirect:
            relations = single_chart_redirect.get("relations")
            if not relations:
                continue
            for single_relation in relations:
                if single_relation.get("dashboard_filter_id"):
                    dashboard_filter_info_dict[single_relation.get("dashboard_filter_id")] = dict()
    return dashboard_filter_info_dict


def collect_redirect_dashboard_filter_info(redirects_data):
    """

    :param redirects_data:
    :return:
    """
    if not redirects_data:
        return dict()
    dashboard_filter_info_dict = _op_redirects_data(redirects_data)

    # 查询出id对应的配置数据
    query_dashboard_filter_data = None
    if len(dashboard_filter_info_dict.keys()):
        query_dashboard_filter_data = metadata_repository.batch_get_dashboard_filter_config_info(
            list(dashboard_filter_info_dict.keys())
        )
    if query_dashboard_filter_data:
        for item in query_dashboard_filter_data:
            dashboard_filter_info_dict[item.get("id")] = item
    return dashboard_filter_info_dict


def extend_preview_metadata(orig_metadata):
    """
    获取预览元数据扩展数据
    :param orig_metadata:
    :return:
    """
    if not orig_metadata:
        return orig_metadata
    metadata_storage = dashboard_metadata_storage.DashboardMetadataStorage(orig_metadata)
    dashboard_id = metadata_storage.get_dashboard_id()
    first_report = metadata_storage.get_first_report()

    # 扩展跳转关系用到的报告级筛选配置信息
    redirects_data = metadata_storage.get_redirect_data()
    first_report["jump_dashboards_data"] = healthy_repository.get_jump_dashboards(dashboard_id)
    first_report["redirect_extend_data"] = collect_redirect_dashboard_filter_info(redirects_data)

    orig_metadata["first_report"] = first_report
    return orig_metadata


def extend_released_metadata(orig_metadata):
    """
    获取已发布元数据扩展数据
    :param orig_metadata:
    :return:
    """
    if not orig_metadata:
        return orig_metadata
    metadata_storage = dashboard_metadata_storage.DashboardMetadataStorage(orig_metadata)
    dashboard_id = metadata_storage.get_dashboard_id()
    first_report = metadata_storage.get_first_report()

    # 扩展跳转关系用到的报告级筛选配置信息
    redirects_data = metadata_storage.get_redirect_data()
    first_report["jump_dashboards_data"] = healthy_repository.get_jump_dashboards(dashboard_id)
    first_report["redirect_extend_data"] = collect_redirect_dashboard_filter_info(redirects_data)

    orig_metadata["first_report"] = first_report
    return orig_metadata


def assign_metadata(orig_metadata, is_released):
    """
    获取元数据需要扩展的数据
    :param orig_metadata:
    :param is_released:
    :return:
    """
    if is_released == DashboardStatus.Drafted.value:
        extended_metadata = extend_preview_metadata(orig_metadata)
    elif is_released == DashboardStatus.Released.value:
        extended_metadata = extend_released_metadata(orig_metadata)
    else:
        extended_metadata = orig_metadata
    return extended_metadata
