#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401

import base64
import json
import time
import random
import urllib.parse
from copy import deepcopy

import binascii
import jwt
from jwt import DecodeError, ExpiredSignatureError

from base import repository
from base.dmp_constant import SELF_SERVICE_VIRTUAL_USER_ID, EXTERNAL_USER_ACCOUNT
from base.enums import ApiParamSysValue, AddFuncType, LoginFrom, TokenAuthFrom
from components.grayscale import set_grayscale_project
from components.url import url_add_param
from dashboard_chart.services.dashboard_service import get_application_type
from dmplib import config
from dmplib.hug import g
from dmplib.saas import project
from ..repositories import dashboard_repository
from user.services.user_service import get_user_by_account
from dashboard_chart.services import dashboard_service
from app_menu.services import application_service
from rbac import external_service as external_rbac_service
from base.errors import UserError
from dmplib.saas.errors import EmptyProjectCodeError

default_aes_key = 'mysoft5a2fc34a9e340'


class DashboardLoginService:
    """
    登录服务
    """

    def __init__(self):
        super().__init__()
        # 看板单点登录cookie key
        self.token_cookie_key = 'dashboard_sso_token'
        self._alg = 'HS256'

        # 与第三方集成约定的key
        self.sso_dashboard_secret = config.get('JWT.sso_dashboard_secret', default_aes_key)

        # dmp内部登录使用的key
        self.dmp_secret = config.get('JWT.secret')

    def encode_data(self, payload):

        return base64.b64encode(jwt.encode(payload, self.sso_dashboard_secret, self._alg).encode()).decode('utf-8')

    def verify_sso_login_token(self, signature):
        if not signature:
            raise UserError(message='缺少签名')
        try:
            jwt_token = base64.b64decode(signature)
            data = jwt.decode(jwt_token, self.sso_dashboard_secret, algorithms=self._alg)
        except DecodeError as e:
            raise UserError(message='无效的签名') from e
        except ExpiredSignatureError as e:
            raise UserError(message='签名过期') from e
        except binascii.Error as e:
            raise UserError(message='非法签名') from e
        return data

    @staticmethod
    def _generate_url_by_terminal_type(dashboard_id, project_code, terminal_type):
        """
        生成报告访问链接
        :param dashboard_id:
        :param project_code:
        :param terminal_type:
        """
        return dashboard_service.generate_base_url_by_terminal_type(dashboard_id, project_code, terminal_type)

    @staticmethod
    def _generate_portal_page_url(portal_id, portal_platform):
        """
        生成门户访问链接
        :param portal_id:
        :param portal_platform:
        """
        return application_service.generate_path_by_platform(portal_id, portal_platform)

    def verify_dmp_cookie_token(self, token):
        try:
            data = jwt.decode(token, self.dmp_secret, algorithms=self._alg)
            userid = data.get('userid')
            org_code = data.get('org_code')
            dashboard_id = data.get('dashboard_id')
            if not userid or not org_code or not dashboard_id:
                return False, '签名数据内容缺失'
        except DecodeError:
            return False, '无效的签名'
        except ExpiredSignatureError:
            return False, '签名过期'
        except binascii.Error:
            return False, '非法签名'
        return True, data

    @staticmethod
    def _set_project_code(data):
        code = data.get(ApiParamSysValue.ProjectCode.value)
        try:
            project_code = project.set_correct_project_code(code)
        except EmptyProjectCodeError:
            raise UserError(message='{}在数见系统中不存在'.format(code))
        if not project_code:
            raise UserError(message='project_code不能为空')
        g.code = project_code
        return project_code

    def set_user_info(self, data):
        return self._set_user_info(data)

    @staticmethod
    def _set_user_info(data):
        # 设置用户相关信息
        # 大致有3种情况, 1. 使用内部account登录, 2. 使用customize_role特殊登录, 3. 使用外部account登录
        user_info = {
            'id': '',
            'user_id': data.get(ApiParamSysValue.UserId.value, ''),
            'account': EXTERNAL_USER_ACCOUNT,
            'group_id': '',
            'user_name': '',
            'customize_roles': [],
            'external_user_id': data.get(ApiParamSysValue.ExternalUserId.value, ''),
            'external_user_account': '',
            'external_user_name': data.get(ApiParamSysValue.UserName.value, ''),
        }

        user_account = data.get(ApiParamSysValue.UserAccount.value)
        customize_roles = data.get('customize_roles', [])
        # 判断account是否存在
        # 两种情况
        # 1. account在账号系统中存在, 设置user_id, user_group_id, account, 并返回
        # 2. account在账号系统中不存在, 设置external_user_account(保存外部账号信息供天眼使用)
        if user_account and not customize_roles:
            user = get_user_by_account(user_account, ['id', 'group_id', 'name'])
            if not user:
                # account不存在, account作为外部account
                user_info['external_user_account'] = user_account
            else:
                # account存在
                user_info['id'] = user['id']
                user_info['group_id'] = user['group_id']
                user_info['account'] = user_account
                user_info['user_name'] = user['name']

                return user_info

        # 兼容自助报表第三方登录无用户id
        if customize_roles:
            setattr(g, 'customize_roles', customize_roles)
            db_roles_map = {role.get("id"): role for role in external_rbac_service.get_all_roles()}
            for role in customize_roles:
                if role not in db_roles_map:
                    raise UserError(message=f'角色:{role}不存在')
            user_info['customize_roles'] = customize_roles
            user_info['id'] = SELF_SERVICE_VIRTUAL_USER_ID
        # https://www.tapd.cn/********/prong/stories/view/11********001182488
        user_info['_account'] = user_info['account']
        user_info['account'] = user_account
        return user_info

    @staticmethod
    def get_report_and_type(biz_code: str):
        """
        获取报告
        """
        if biz_code.startswith("ppt_"):
            return {"id": biz_code.replace('ppt_', '')}, AddFuncType.Ppt.value
        elif biz_code.startswith("ar_"):
            return {"id": biz_code.replace('ar_', '')}, AddFuncType.ActiveReport.value
        else:
            row = dashboard_repository.get_dashboard_by_biz_code(biz_code)
            return row, 'dashboard'

    def login(self, signature, redirect_url=None):
        """
        集成登录
        :param signature: 登录的签名数据
        :param redirect_url: 重定向的页面地址.
        优先根据biz_code跳到报告的地址, 否则跳到redirect_url或首页
        :return:
        """
        data = self.verify_sso_login_token(signature)
        project_code = self._set_project_code(data)
        user_info = self._set_user_info(data)

        biz_code = data.get(ApiParamSysValue.BizCode.value)
        portal_id = data.get(ApiParamSysValue.PortalId.value)
        screen_id = data.get(ApiParamSysValue.ScreenId.value)
        session_id = data.get(ApiParamSysValue.SessionId.value)
        extend_yl_params = data.get(ApiParamSysValue.ExtendYlParams.value, '')
        snap_id = data.get(ApiParamSysValue.SnapId.value)
        flag = int(time.time()) + random.randint(1, 1000)
        if biz_code:
            row, report_type = self.get_report_and_type(biz_code)
            if not row:
                raise UserError(message='无效的报告代码')
            report_id = row['id']
            ori_data = deepcopy(data)
            data = {
                '_flag': flag,
                'dashboard_id': report_id,
                'code': project_code,
                'account': user_info['account'],
                'id': user_info['id'],
                'group_id': user_info['group_id'],
                'external_params': data,
                'customize_roles': user_info['customize_roles'],
                'external_user_id': user_info['external_user_id'],
                'extend_yl_params': extend_yl_params,
            }
            if report_type in [AddFuncType.Ppt.value, AddFuncType.ActiveReport.value]:
                from ppt.services.ppt_service import build_ppt_redirect_url
                g.userid = user_info['id'] or user_info['user_id']
                g.account = user_info['account']
                g.code = project_code
                page_url = build_ppt_redirect_url(
                    "/login_by_jwt",
                    from_type=report_type,
                    params={'id': report_id},
                    extra={"login_from": LoginFrom.DashboardSso.value, "data": ori_data},
                    backend=False   # 统计报表前台地址
                )
            else:
                # 根据报告类型获取访问链接
                page_url = self._generate_url_by_terminal_type(
                    report_id, project_code, row.get("terminal_type")
                )
                # 云空间报告支持指定拍照id访问
                if snap_id:
                    page_url = url_add_param(page_url, {"snap_id": snap_id})
                application_type = get_application_type(report_id)
                if application_type in ['1', 1]:
                    page_url = f'/intelligent-report/share/{report_id}?code={project_code}'
            return data, page_url, user_info
        elif portal_id:
            portal = application_service.get_application_info(portal_id)
            if not portal:
                raise UserError(message='无效的门户ID')
            data = {
                '_flag': flag,
                'portal_id': portal_id,
                'code': project_code,
                'account': user_info['account'],
                'id': user_info['id'],
                'group_id': user_info['group_id'],
                'session_id': session_id,
                'external_params': data,
                'customize_roles': user_info['customize_roles'],
                'extend_yl_params': extend_yl_params,
                'auth_from': TokenAuthFrom.ThirdCloudAuth.value,
            }
            portal_platform = portal.get('platform', 'pc')
            portal_page_url = self._generate_portal_page_url(portal_id, portal_platform)
            return data, portal_page_url, user_info

        elif screen_id:
            if not repository.data_is_exists("dashboard", {"id": screen_id}):
                raise UserError(message='无效的多屏ID')
            user_account = user_info['account'] if user_info['account'] else 'external_user'
            data = {
                '_flag': flag,
                'screen_id': screen_id,
                'code': project_code,
                'account': user_account,
                'id': user_info['id'],
                'group_id': user_info['group_id'],
                'session_id': session_id,
                'external_params': data,
                'customize_roles': user_info['customize_roles'],
                'extend_yl_params': extend_yl_params,
            }
            screen_page_url = self._generate_screen_page_url(screen_id, project_code)
            return data, screen_page_url, user_info
        elif user_info['account'] != EXTERNAL_USER_ACCOUNT:
            # 使用redirect_url跳转
            data = {
                '_flag': flag,
                'code': project_code,
                'account': user_info['account'],
                'group_id': user_info['group_id'],
                'id': user_info['id'],
                'external_params': data,
                'customize_roles': user_info['customize_roles'],
                'extend_yl_params': extend_yl_params,
            }
            return data, redirect_url, user_info
        raise UserError(message='只支持biz_code或帐号的方式登录')

    def verify_token(self, request):
        token = request.cookies.get(self.token_cookie_key)
        if not token:
            return False
        success, data = self.verify_dmp_cookie_token(token)
        if not success:
            return False
        return True

    @staticmethod
    def _generate_screen_page_url(screen_id, project_code):
        access_url = '/dataview/share/%s?code=%s'
        return access_url % (screen_id, project_code)

    @staticmethod
    def set_cookies(request, response, data):
        domain = request.host
        secret = config.get('JWT.secret')
        token = jwt.encode(data, secret)
        response.set_cookie(name='token', value=token, domain=domain, path='/', secure=False, http_only=False)

        # 加入标识来自第三方
        response.set_cookie(
            name='_from', value='login_dashboard', domain=domain, path='/', secure=False, http_only=False
        )
        # 外部参数
        external_params = ','.join("{}={}".format(k, v) for k, v in data.get("external_params", {}).items())
        response.set_cookie(
            name='external_params',
            value=json.dumps(external_params),
            domain=domain,
            path='/',
            secure=False,
            http_only=False,
        )

        # 用urllib.parse.quote编码的外部参数
        response.set_cookie(
            name="external_params1",
            value=urllib.parse.quote(external_params),
            domain=domain,
            path="/",
            secure=False,
            http_only=False,
        )
        extend_yl_params = data.get('extend_yl_params', '')
        response.set_cookie(
            name='extend_yl_params', value=extend_yl_params, domain=domain, path='/', secure=False, http_only=False
        )

        if data:
            response.set_cookie(
                name='tenant_code', value=data.get('code'), domain=domain, path='/', secure=False, http_only=False
            )
        if data.get('code'):
            set_grayscale_project(request, response, data.get('code'))
        return token
