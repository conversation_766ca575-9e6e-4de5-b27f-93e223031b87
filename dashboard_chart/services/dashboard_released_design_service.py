#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
dashboard release design service
"""

# ---------------- 标准模块 ----------------
import json
from loguru import logger
# ---------------- 业务模块 ----------------
from dmplib import config
from dmplib.hug import g
from dmplib import redis
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from base.enums import DashboardTypeStatus

from celery_app.celery import CeleryTask
from async_task.services import async_task_service
from dashboard_chart.repositories import dashboard_released_design_repository
from exports.services.export_dashboard_service import get_one_issue_data_v2


def add_dashboard_released_design_data(dashboard_model, release_dashboard_list):
    """
    报告发布时，记录报告的设计时表数据
    :param dashboard_model:
    :param release_dashboard_list:
    :return:
    """
    dashboard_id = dashboard_model.id
    dashboard_data = get_dashboard_released_design_data(release_dashboard_list)
    return dashboard_released_design_repository.add_dashboard_released_design_data(dashboard_id, dashboard_data)


def get_dashboard_released_design_data(release_dashboard_list):
    """
    获取报告的设计时数据表数据
    :param release_dashboard_list:
    :return:
    """
    dashboard_data = dict()
    for release_dashboard in release_dashboard_list:
        model = release_dashboard.get('model')
        dashboard_id = model.id
        one_issue_data = get_one_issue_data_v2(dashboard_id, False)
        if one_issue_data and dashboard_id not in dashboard_data.keys():
            dashboard_data.update({dashboard_id: one_issue_data})
    return dashboard_data


def del_dashboard_released_design_record(dashboard_id):
    """
    删除报告最近一次发布的设计时数据
    :param dashboard_id:
    :return:
    """
    return dashboard_released_design_repository.del_dashboard_released_design(dashboard_id)


def check_dashboard_released_design_record(root_dashboard):
    """
    查询报告最近一次发布的设计时数据
    :param root_dashboard:
    :return:
    """
    is_exists = False
    try:
        design_data = {}
        if root_dashboard:
            design_data = get_dashboard_released_design_record(root_dashboard.get('id'))
        # 报告为已发布状态且存在发布设计时数据，则可还原
        is_exists = True if design_data and root_dashboard.get('status') == DashboardTypeStatus.Release.value else False
    except Exception as e:
        logger.error("查询报告最近一次发布的设计时数据异常，errs:"+str(e))
    return is_exists


def get_dashboard_released_design_record(dashboard_id):
    """
    查询报告最近一次发布的设计时数据
    :param dashboard_id:
    :return:
    """
    return dashboard_released_design_repository.get_dashboard_released_design(dashboard_id)


def restore_dashboard_released_design_data(dashboard_model):
    """
    将报告最近一次发布的运行时数据，还原为设计时
    :param dashboard_model:
    :return:
    """
    dashboard_id = dashboard_model.id
    params = {"dashboard_id": dashboard_id}
    design_data = get_dashboard_released_design_record(dashboard_id)
    if not design_data:
        raise UserError(message="报告不存在运行时数据，不能还原")
    if design_data:
        # 检查任务是否正在执行
        task_id = get_restore_task_id(dashboard_id)
        task_rs = get_task_data(task_id)
        if task_rs:
            raise UserError(message="报告还原任务正在执行，请勿重复操作")
        params["task_id"] = task_id

        # 跳转的模式
        from dashboard_chart.services.chart_service import METADATA_CONFIG
        params["is_new_jump"] = METADATA_CONFIG.get_is_new_jump()

        # 报告还原的任务
        async_restore_task(**params)
        del params["is_new_jump"]
        logger.error("报告还原任务信息："+json.dumps(params))
    return params


def get_task_data(task_id):
    """
    获取redis的task数据
    :param task_id:
    :return:
    """
    data = redis.conn().get_data(task_id)
    return data


def get_restore_task_id(flow_id=None):
    """
    获取报告还原的任务id
    :param flow_id:
    :return:
    """
    flow_id = flow_id if flow_id else seq_id()
    task_id = async_task_service.get_task_id(
        module_name="dashboard", task_name="restore", project_code=g.code, flow_id=flow_id
    )
    return task_id


def async_restore_task(**kwargs):
    """
    异步将报告运行时数据还原到设计时（启动admin的celery执行）
    :param dashboard_id: 报告id
    :param task_id: 任务id
    :param is_new_jump: 报告跳转模式
    :return:
    """
    from celery_app.celery import get_backend

    backend = get_backend(config.get('Redis.admin_celery_db') or 6)

    CELERY_APP_NAME = "dmp_admin_celery"
    CELERY_TASK_NAME = "app_celery.restore_dashboard_design_data"
    ADMIN_CELERY_QUEUE_NAME = "admin-celery"

    celery_app = CeleryTask(name=CELERY_APP_NAME, backend=backend, queue_name=ADMIN_CELERY_QUEUE_NAME)
    celery_app.celery.send_task(
        CELERY_TASK_NAME,
        kwargs={
            'project_code': g.code,
            'dashboard_id': kwargs.get('dashboard_id'),
            'task_id': kwargs.get('task_id'),
            'is_new_jump': kwargs.get('is_new_jump'),
            'modified_account': getattr(g, "account", None)
        },
    )


def get_root_dashboard_by_level_code(dashboard_level_code):
    """
    按层级code获取报告的主报告
    :param dashboard_level_code:
    :return:
    """
    root_dashboard = dashboard_released_design_repository.get_root_dashboard_by_level_code(dashboard_level_code) or {}
    return root_dashboard
