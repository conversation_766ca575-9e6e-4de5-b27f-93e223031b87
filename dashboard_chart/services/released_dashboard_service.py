#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=W1202

import hashlib
import traceback

import jwt
import time
import base64

import requests
import regex

# ---------------- 标准模块 ----------------
import json
from json import JSONDecodeError
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from functools import lru_cache
from copy import deepcopy

# ---------------- 业务模块 ----------------
from base import repository
from base.enums import (
    DashboardDataMsgCode,
    DashboardStatus,
    DashboardTypeStatus,
    DashboardTypeAccessReleased,
    DashboardSQLExecuteStatus,
    TableDownloadFrom,
    DashboardType,
)
from base.models import BaseModelEncoder
from components.versioned_query import current_request_is_enable_versioned, cached as snap_cache, shield_versioned
from dashboard_chart.data_query.charts.chart_factory import ChartFactory
from dashboard_chart.metadata.schema_validator import MetadataValidator, DashboardMetadataValidator
from dashboard_chart.models import ChartDataModel, ChartModel, ReleaseModel, ReleasedDashboardModel, ReleasedChartModel
from dashboard_chart.repositories import chart_repository
from dashboard_chart.services import (
    dashboard_service,
    proxy_dataset_service,
    chart_service,
    dashboard_extra_service,
    metadata_service,
    components_service,
)
from dashboard_chart.services.dashboard_cache_service import DashboardCache, DashboardReleaseCache
from dashboard_chart.utils import dashboard_cache, chart_utils
from dataset import external_query_service
from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib.hug import g
from dmplib.redis import conn as conn_redis
from components.redis_utils import RCache
from dmplib.saas.project import get_db, get_project_info
from dmplib.utils.errors import FriendlyError, UserError
from dmplib.utils.strings import seq_id
from dmplib import config
from rbac.services.data_permissions import data_permission_edit_filter
from user import external_user_service
from user.services import user_service
from user_group.repositories.user_group_repository import check_group_exists
from dashboard_chart.services.chart_model_adapter import adapt_chart_data_model
from dataset.query.result_data import QueryDataError
from dashboard_chart.services.dashboard_released_design_service import (
    add_dashboard_released_design_data,
    restore_dashboard_released_design_data
)
from app_menu.services import application_auth_service
from open_api.services import screen_auth_service
from dmplib.redis import conn as redis_conn
from components.analysis_time import AnalysisTimeUtils
from base.dmp_constant import __ANALYSIS_DOCTOR_KEYWORD__


logger = logging.getLogger(__name__)


def collect_single_chart_result(model, results):
    request_data = getattr(g, 'request_data', {})
    request_params = request_data.get('params', {})
    refer = request_data.get('headers', {}).get('REFERER', '')
    # 现在的客户环境业务上没有性能医生，现在在调试模式下开启性能医生数据
    if (
            (__ANALYSIS_DOCTOR_KEYWORD__ in request_params)
            or (__ANALYSIS_DOCTOR_KEYWORD__ in refer)
    ):
        request_params[__ANALYSIS_DOCTOR_KEYWORD__] = 1
        AnalysisTimeUtils.enter(chart_id=model.id)
        try:
            chart_result = get_released_chart_result(model)
            AnalysisTimeUtils.append_analysis_data(result=chart_result)
            results[model.id] = chart_result
        finally:
            AnalysisTimeUtils.exit()
    else:
        results[model.id] = get_released_chart_result(model)


def batch_get_released_chart_result(chart_params):
    """

    :param chart_params:
    :return:
    """
    results = dict()
    chart_params = chart_params or []
    for param in chart_params:
        model = ChartDataModel(**param)
        try:
            collect_single_chart_result(model, results)
        except UserError as e:
            if not isinstance(e, UserError):
                logger.error("单图取数接口异常", exc_info=True)
            results[model.id] = {
                "errmsg": str(e),
                "data": [],
                "marklines": [],
                "conditions": [],
                "msg": "单图取数接口异常，异常信息: " + str(e),
                "msg_code": e.code,
                "error_code": QueryDataError.AnyError.value,
                "pagination": {},
                "sql": "",
                "sql_execute_time": "",
                "dataset_versions": {"data_version": "", "version": ""},
            }
        except Exception as e:
            if not isinstance(e, UserError):
                logger.error("单图取数接口异常", exc_info=True)
            results[model.id] = {
                "errmsg": str(e),
                "data": [],
                "marklines": [],
                "conditions": [],
                "msg": "单图取数接口异常，异常信息: " + str(e),
                "msg_code": DashboardDataMsgCode.QueryException.value,
                "error_code": QueryDataError.AnyError.value,
                "pagination": {},
                "sql": "",
                "sql_execute_time": "",
                "dataset_versions": {"data_version": "", "version": ""},
            }
        finally:
            for result in results.values():
                if (
                        result
                        and isinstance(result, dict)
                        and isinstance(result.get('msg_code'), int)
                        and result.get('msg_code', 0) > 5000
                ):
                    logger.error(f"单图发布页取数异常, code<{result.get('msg_code', 0)}>, {result.get('msg', 'unknown')}")
            results[model.id] = chart_service.deal_scheduled_dataset_query_info(results[model.id])
    return results


def get_released_chart_total(model, reassign_model: bool = True):
    """
    从数据集获取数据总数
    :param model:
    :return:
    """
    try:
        # 支持拍照取数
        get_snap_dataset_version_id(model)
        obj = _generate_get_data_chart_class(model, reassign_model)
        return obj.query_count()
    except Exception as e:
        if not isinstance(e, UserError):
            logger.exception(msg="单图取数接口异常，异常信息: " + str(e))
        raise UserError(message="获取总数失败，错误信息: {msg}".format(msg=str(e)))


def _generate_get_data_chart_class(model, reassign_model: bool = True):
    model.validate()
    if reassign_model:
        assign_release_dashboard_chart_model(model)
    if not model.dataset_id:
        raise UserError(message="没有配置数据集", code=DashboardDataMsgCode.NullDatasetData.value)

    # 兼容没有配置dim或num的异常情况
    if not model.dims and not model.nums:
        raise UserError(message="没有配置维度和度量", code=DashboardDataMsgCode.NullDatasetData.value)
    # 适配chart_data_model
    adapt_chart_data_model(model)
    return ChartFactory.create_chart(chart_id=model.id, model=model)


def assign_release_dashboard_chart_model(model):
    # 对于钻取模式, 如果前端有传维度, 使用前端传递的维度, 先对维度进行保存
    dims = model.dims if model.dims else None
    # 对于隐藏字段, 如果前端有传度量, 使用前端传递的度量, 先对度量进行保存
    nums = model.nums if model.nums else None

    snapshot_id = model.dashboard_id
    AnalysisTimeUtils.recode_time_node('疑似代码位置-0')
    # 校验报告分发时间戳
    check_deliver_flag = metadata_service.check_deliver_timestamp(snapshot_id)
    data = dict()
    AnalysisTimeUtils.recode_time_node('疑似代码位置-1')
    # 只有校验通过才尝试去缓存获取
    if check_deliver_flag:
        # 先从缓存获取报告数据，如果没有则查库获取数`据
        data = assign_chart_data_model_from_cache(snapshot_id, model.id, model)
    AnalysisTimeUtils.recode_time_node('疑似代码位置-2')
    if not data:
        # 没有缓存数据情况
        assign_chart_data_model_by_snapshot_data(snapshot_id, model.id, model)

    # 对于钻取模式, 如果前端有传维度, 使用前端传递的维度, 对维度进行恢复
    model.dims = _assist_add_field_id(dims) if dims else model.dims
    model.nums = _assist_add_field_id(nums + model.nums) if nums else model.nums


def get_snap_dataset_version_id(model):
    """
    获取拍照数据集的version_id
    :param model:
    :return:
    """
    if all([
        hasattr(model, 'dataset_id'),
        current_request_is_enable_versioned() and snap_cache.has_snapshot(g.snap_id)
    ]):
        dataset_id = repository.get_data_scalar(
            "dashboard_released_snapshot_chart",
            {"id": model.id, "snapshot_id": model.dashboard_id},
            col_name="source"
        )

        dataset_version_id = repository.get_data_scalar(
            "dataset_snapshot_relation",
            {"snap_id": g.snap_id, "dataset_id": dataset_id},
            col_name="dataset_version_id"
        )
        setattr(g, "dataset_id", dataset_id)
        if dataset_version_id:
            setattr(g, "dataset_version_id", dataset_version_id)
        return dataset_version_id
    return None


def get_released_chart_result(model, reassign_model: bool = True):
    """
    从数据集中获取单图数据结果集
    :param model:
    :param reassign_model:
    :return:
    """

    # 支持拍照取数
    dataset_version_id = get_snap_dataset_version_id(model)
    AnalysisTimeUtils.recode_time_node('开始构造取数model')
    obj = _generate_get_data_chart_class(model, reassign_model)
    AnalysisTimeUtils.recode_time_node('结束构造取数model')
    result = obj.get_chart_data(dataset_version_id)
    if result and isinstance(result, dict):
        _pop_query_structure(result)
    elif result and isinstance(result, list):
        for item in result:
            _pop_query_structure(item)
    return result


def _pop_query_structure(data):
    """
    pop item
    :param data:
    :return:
    """
    if data and isinstance(data, dict) and data.get("query_structure"):
        data.pop("query_structure")


def get_dashboard_info_data_from_cache(snapshot_id, dashboard_id):
    """
    从cache中获取报告的info数据
    :param str snapshot_id: 快照id
    :param str dashboard_id: 报告id
    :return:
    """
    # 拍照的历史版本根本不会存在Redis中， 这里直接从snapshot_dashboard_released_snapshot_dashboard中取
    if current_request_is_enable_versioned():
        with shield_versioned():
            chart_dict = repository.get_one("snapshot_dashboard_released_snapshot_dashboard",
                                            {"snap_id": g.snap_id, "snapshot_id": dashboard_id})
            return chart_dict or {}

    AnalysisTimeUtils.recode_time_node('开始查询报告缓存')
    cache = DashboardCache(g.code, snapshot_id, conn_redis())
    dashboard_info = cache.get_dashboard_data(dashboard_id)
    AnalysisTimeUtils.recode_time_node('完成查询报告缓存')
    if not dashboard_info:
        return dict()
    return dashboard_info


def assign_chart_data_model_from_cache(snapshot_id, chart_id, model: ChartDataModel):
    """
    从cache中获取发布模块数据并赋值给model
    :param str snapshot_id: 快照id
    :param str chart_id: 单图id
    :param dashboard.models.ChartDataModel model:
    :return:
    """
    cache = DashboardCache(g.code, snapshot_id, conn_redis())

    dashboard_info = get_dashboard_info_data_from_cache(snapshot_id, model.report_id)

    if dashboard_info:
        # 判断是否为多屏，多屏需要查询出单图对应的报告id
        if dashboard_info.get("is_multiple_screen") == 1:
            chart = repository.get_data(
                "dashboard_released_snapshot_chart", {"id": chart_id, "snapshot_id": snapshot_id}
            )
            if chart:
                model.dashboard_id = chart.get("dashboard_id")
            else:
                raise FriendlyError(message="未找到单图对应的报告")

        # 获取单图缓存数据
        chart_dict = cache.get_chart(model.dashboard_id, chart_id)
        if chart_dict:
            return _set_chart_data_model(model, dashboard_info, chart_dict, snapshot_id)
    return False


# pylint:disable=unused-argument
def assign_chart_data_model_by_snapshot_data(dashboard_id, chart_id, model: ChartDataModel):
    """
    根据快照里面的报告备份数据重新对model的各个跟查询数据有关的属性进行赋值，以恢复查询报告编辑前的展示数据
    :param str dashboard_id: 报告id
    :param str chart_id: 单图id
    :param dashboard.models.ChartDataModel model:
    :return:
    """
    if str(getattr(g, 'request_data', {}).get('params', {}).get('test_mode')) == '1':
        repository.get_data_by_sql('select 1', params={})
    chart_dict = repository.get_data("dashboard_released_snapshot_chart", {"id": model.id, "snapshot_id": dashboard_id})
    dashboard = repository.get_data("dashboard_released_snapshot_dashboard", {"id": model.dashboard_id})
    if not dashboard:
        raise UserError(message="报告不存在")
    if not chart_dict:
        raise UserError(message="单图不存在")

    if dashboard_id is None or dashboard_id == "":
        raise UserError(message="缺少报告ID")

    # 如果dashboard_id是多屏id， 则model.dashboard_id不等于当前单图的报告id，将单图报告id赋值给model
    if chart_dict.get("dashboard_id") and model.dashboard_id != chart_dict.get("dashboard_id"):
        model.dashboard_id = chart_dict.get("dashboard_id")

    _set_chart_data_model(model, dashboard, chart_dict, dashboard_id)

    return model


def _set_chart_data_model(model, dashboard, chart_dict, snapshot_id):
    """
    设置model属性值
    :param dashboard_chart.models.ChartDataModel model:
    :param dict dashboard: 报告数据
    :param dict chart_dict: 单图数据
    :return:
    """
    try:
        section_dict = _assign_section_data(chart_dict)
        model.dims = section_dict.get("dims", [])
        model.original_dims = _json_decode_list(chart_dict.get("dims")) or []
        model.layout_extend = _json_decode_dict(chart_dict.get("layout_extend")) or {}
        model.comparisons = section_dict.get("comparisons", [])
        model.original_comparisons = _json_decode_list(chart_dict.get("comparisons")) or []
        model.nums = section_dict.get("nums", [])
        model.zaxis = section_dict.get("zaxis", [])
        model.nums = ChartModel.merge_nums_zaxis(model.nums, model.zaxis)
        _original_nums = _json_decode_list(chart_dict.get("nums")) or []
        _original_zaxis = _json_decode_list(chart_dict.get("zaxis")) or []
        model.original_nums = [*_original_nums, *_original_zaxis]
        model.desires = section_dict.get("desires", [])
        model.original_desires = _json_decode_list(chart_dict.get("desires")) or []
        model.chart_code = model.chart_code if model.chart_code else chart_dict.get("chart_code")
        model.penetrate_relation = section_dict.get('penetrate_relation', [])
        model.penetrate_filter_relation = section_dict.get('penetrate_filter_relation', [])
        model.penetrate_var_filter_relation = section_dict.get('penetrate_var_filter_relation', [])
        model.data_logic_type_code = _get_logic_type_code(chart_dict, model)
        model.filters = json.loads(chart_dict.get("filters")) if chart_dict.get("filters") else []
        model.default_value = chart_dict.get("default_value") if chart_dict.get("default_value") else []
        model.column_order = json.loads(chart_dict.get("column_order")) if chart_dict.get("column_order") else []
        page_size = _get_page_size(chart_dict, model)
        model.page_size = page_size
        model.pagination.page_size = page_size
        # marklines特殊处理
        marklines = json.loads(chart_dict.get("marklines")) if chart_dict.get("marklines") else []
        model.marklines = _get_marklines_by_snapchot(marklines)
        model.dataset_id = chart_dict.get("source")
        model.filter_relation = chart_dict.get("filter_relation")
        model.enable_subtotal = chart_dict.get("enable_subtotal")
        model.enable_summary = chart_dict.get("enable_summary")
        model.enable_subtotal_col = chart_dict.get("enable_subtotal_col")
        model.enable_subtotal_col_summary = chart_dict.get("enable_subtotal_col_summary")
        model.enable_subtotal_row = chart_dict.get("enable_subtotal_row")
        model.enable_subtotal_row_summary = chart_dict.get("enable_subtotal_row_summary")
        model.subtotal_row_summary_formula_mode = chart_dict.get("subtotal_row_summary_formula_mode")
        model.aggregation = chart_dict.get("aggregation")
        model.pre_comparison = chart_dict.get("pre_comparison")
        model.external_subject_ids = _sort_external_subject_ids(
            dashboard["main_external_subject_id"], model.external_subject_ids
        )
        model.dataset_field_dict = _dataset_field_dict(model.dataset_id, model.external_subject_ids)
        model.dataset = proxy_dataset_service.get_dataset(model.dataset_id)
        if model.comparisons and model.dataset.get('external_type') == "pulsar_indicator":
            model.aggregation = 1
        dashboard_info = get_dashboard_info_data_from_cache(snapshot_id, model.report_id)
        model.dashboard_var_filters = dashboard_info.get('dashboard_var_filters') if dashboard_info else dict()
        table_dashboard_filters = dashboard_info.get('dashboard_filters') if dashboard_info else dict()
        dashboard_filters, dashboard_filter_relations = _get_dashboard_filters_and_releations(table_dashboard_filters)
        model.dashboard_filters = dashboard_service.get_dashboard_filters_v2(
            current_dataset_id=model.dataset_id,
            dashboard_filters=dashboard_filters,
            dashboard_filter_relations=dashboard_filter_relations,
        )
        chart_responder_id, dashboard_id = model.id, model.dashboard_id
        model.filter_selector_dict = _get_filter_selector_dict(model.chart_filter_conditions, chart_responder_id,
                                                               dashboard_id)
        model.common_datetime_selector_dict = _get_filter_selector_dict(model.common_datetime_conditions,
                                                                        chart_responder_id, dashboard_id)
        model.linkage_selector_dict = _get_linkage_selector_dict(model)
        if chart_dict.get("display_item"):
            model.display_item = json.loads(chart_dict.get("display_item"))

        # 处理单图筛选的operators
        model.filters = chart_service.split_operators(model.filters)

        # 如果是穿透层则获取穿透单图顶层filter_relation给当前穿透层
        model.parent_id = chart_dict.get("parent_id")
        model.level_code = chart_dict.get("level_code")
        if model.parent_id:
            model.filter_relation = _get_top_chart_filter_relation(snapshot_id, model.id)

        # 多字段排序
        model.field_sorts = section_dict.get("field_sorts")
        # 处理新版排序
        chart_service.assign_new_order(section_dict.get("field_sorts"), model)
        # 提取间接访问条件
        model.indirect_query_map = chart_utils.extract_indirect_query_map(
            model.chart_filter_conditions, model.query_vars
        )
        # 条件使用关键字的特殊处理（关键字需要实时取值）
        model.keyword_values = chart_utils.batch_get_keyword_values(dashboard_filters, model.filters)

        # 处理额外字段
        model.extra_nums = chart_service._get_chart_section_data(model, section_type="extra_nums", no_data=True)
        model.extra_nums = chart_service.ExtraNumsTool.mark_model_extra_nums(model.extra_nums)
        chart_service.ExtraNumsTool.push_extra_num_to_model_nums(model)
        return model
    except Exception as e:
        logger.error(f'解析报告发布缓存数据出错, 错误信息：{traceback.format_exc()}')
        raise FriendlyError(message="解析报告发布缓存数据出错, 错误信息：" + str(e)) from e


def _sort_external_subject_ids(main_external_subject_id, external_subject_ids):
    """
    对外部主题排序，主要主题排在第一位
    :param main_external_subject_id:
    :param external_subject_ids:
    :return:
    """
    return sorted(
        external_subject_ids, key=lambda external_subect_id: int(external_subect_id != main_external_subject_id)
    )


def _dataset_field_dict(dataset_id, external_subject_ids):
    if external_subject_ids:
        return proxy_dataset_service.get_external_subjects_dataset_field_dict(external_subject_ids)
    return proxy_dataset_service.get_dataset_field_dict_with_field_id(dataset_id)


def _get_page_size(chart_dict, model):
    if model.pagination.page_size and model.pagination.page_size > 0:
        page_size = model.pagination.page_size
    else:
        page_size = chart_dict.get("page_size")
    return page_size


def _get_logic_type_code(chart_dict, model):
    return model.data_logic_type_code if model.data_logic_type_code else chart_dict.get("data_logic_type_code")


def _get_top_chart_filter_relation(snapshot_id, chart_id):
    """
    获取到当前单图最顶层的父级单图的filter_relation
    :param snapshot_id:
    :param chart_id:
    :return: filter_relation
    """
    filter_relation = 0
    if not snapshot_id or not chart_id:
        return filter_relation
    query_snapshot_data = repository.get_data(
        "dashboard_released_snapshot_chart",
        {"snapshot_id": snapshot_id},
        ["id", "parent_id", "filter_relation"],
        multi_row=True,
    )
    if not query_snapshot_data:
        return filter_relation
    total_data_dict = {item.get("id"): item for item in query_snapshot_data}
    chart_rank_dict = {item.get("id"): item.get("parent_id") for item in query_snapshot_data if item.get("id")}
    parent_id = chart_rank_dict.get(chart_id)
    if not total_data_dict:
        return filter_relation
    # 没有父级的情况
    if not parent_id:
        chart_data = total_data_dict.get(chart_id)
        return chart_data.get("filter_relation") if chart_data else filter_relation
    # 有父级的情况，需要递归找层级最高的父级单图的filter_relation字段值
    parent_id = _recurison_get_top_parent_id(chart_id, chart_rank_dict)
    if parent_id:
        parent_data = total_data_dict.get(parent_id)
        return parent_data.get("filter_relation") if parent_data else filter_relation
    return filter_relation


def _recurison_get_top_parent_id(operate_id, chart_rank_dict):
    """
    递归获取顶层的单图id
    :param operate_id:
    :param chart_rank_dict:
    :return:
    """
    parent_id = chart_rank_dict.get(operate_id)
    if parent_id and chart_rank_dict.get(parent_id):
        return _recurison_get_top_parent_id(parent_id, chart_rank_dict)
    return parent_id


def _get_dashboard_filters_and_releations(release_filed):
    """
    从发布字段里获取报告筛选和关联关系
    :param release_filed:
    :return:
    """
    dashboard_filters = list()
    dashboard_filter_relations = list()
    if not release_filed:
        return dashboard_filters, dashboard_filter_relations
    try:
        data = json.loads(release_filed)
    except Exception as e:
        logger.exception(msg="报告级筛选数据json解析异常，异常信息: " + str(e))
        return dashboard_filters, dashboard_filter_relations
    if not data:
        return dashboard_filters, dashboard_filter_relations
    for each in data:
        # 赋值operators字段
        operators = each.get('operators')
        filter_relations = each.get('filter_relations')
        if filter_relations:
            for single_relation in filter_relations:
                if not single_relation:
                    continue
                single_relation['operators'] = operators
            dashboard_filter_relations.append(filter_relations)
        if 'filter_relations' in each:
            del each['filter_relations']
        dashboard_filters.append(each)
    return dashboard_filters, dashboard_filter_relations


def get_released_snapshot_chart_fields() -> List[str]:
    """
    返回dashboard_released_snapshot_chart表字段列表
    :return:
    """
    return [
        "id",
        "snapshot_id",
        "dashboard_id",
        "name",
        "chart_code",
        "chart_type",
        "content",
        "source",
        "level_code",
        "parent_id",
        "display_item",
        "refresh_rate",
        "penetrate",
        "sort_method",
        "position",
        "percentage",
        "layout",
        "layout_extend",
        "config",
        "filter_config",
        "data_modified_on",
        "dims",
        "nums",
        "desires",
        "filters",
        "filter_relation",
        "marklines",
        "penetrates",
        "layers",
        "zaxis",
        "data_logic_type_code",
        "chart_visible_triggers",
        "chart_visible",
        "source_type",
        "source_content",
        "label_id",
        "label_tmpl_id",
        "label_org_id",
        "chart_params",
        "default_value",
        "page_size",
        "column_order",
        "comparisons",
        "jump",
        "component_filter",
        "chart_params_jump",
        "penetrate_relation",
        "penetrate_filter_relation",
        "penetrate_var_filter_relation",
        "chart_filter",  # 新版单图间筛选
        "chart_linkage",  # 新版单图间联动
        "chart_default_value",  # 新版筛选单图默认值
        "chart_vars",  # 单图变量
        "var_relations",  # 变量关系
        "enable_subtotal",
        "enable_summary",
        "enable_subtotal_col",
        "enable_subtotal_col_summary",
        "enable_subtotal_row",
        "enable_subtotal_row_summary",
        "subtotal_row_summary_formula_mode",
        "reset_field_sort",
        "field_sorts",  # 字段排序
        "aggregation",  # 聚合
        "pre_comparison",  # 对比维度是否前置
        "export_type",
        "parent_chart_id",
        "asset_id",
        "child_rank",
        "external_subject_id",
        "fixed_data_mode",
        "fixed_manual_value",
        "is_highdata",
        "close_detail_mode",
        "layout_extend",
    ]


def _get_marklines_by_snapchot(marklines):
    """
    根据快照中的marklines数据重组model的marklines属性
    :param marklines: 快照中的marklines数据备份
    :return:
    """
    if marklines:
        for markline in marklines:
            markline["markline"] = markline["num"]
            markline["nums"] = chart_repository.get_num_by_field_id(markline["num"], markline["dashboard_chart_id"])
    return marklines


def _json_decode_list(data_list):
    if isinstance(data_list, str):
        try:
            data_list = json.loads(data_list) if data_list else []
        except Exception:
            return None
    return data_list


def _json_decode_dict(data):
    if isinstance(data, str):
        try:
            data = json.loads(data) if data else {}
        except Exception:
            return {}
    return data


def _assist_add_field_id(data_list):
    """
    添加dataset_field_id
    :param data_list:
    :return:
    """
    data_list = _json_decode_list(data_list)
    if not data_list:
        return data_list
    for single_data in data_list:
        if "dataset_field_id" not in list(single_data.keys()):
            single_data["dataset_field_id"] = (
                single_data.get("dim") if single_data.get("dim") else single_data.get("num")
            )
    return data_list


def _assign_section_data(chart_dict):
    """
    获取chart中转换后的组成数据
    :param chart_dict:
    :return:
    """
    section_dict = dict()
    section_dict["dims"] = _assist_add_field_id(chart_dict.get("dims"))
    section_dict["comparisons"] = _assist_add_field_id(chart_dict.get("comparisons"))
    section_dict["nums"] = _assist_add_field_id(chart_dict.get("nums"))
    section_dict["zaxis"] = _assist_add_field_id(chart_dict.get("zaxis"))
    section_dict["desires"] = _assist_add_field_id(chart_dict.get("desires"))
    section_dict['penetrate_relation'] = _assist_add_field_id(chart_dict.get('penetrate_relation'))
    section_dict['penetrate_filter_relation'] = _assist_add_field_id(chart_dict.get('penetrate_filter_relation'))
    section_dict['penetrate_var_filter_relation'] = _assist_add_field_id(
        chart_dict.get('penetrate_var_filter_relation')
    )
    section_dict["field_sorts"] = _assist_add_field_id(chart_dict.get('field_sorts'))
    return section_dict


def get_snapshot_chart_list(model, snapshot_id):
    """
    获取单图列表快照
    :param dashboard.models.ChartDataModel model:
    :param snapshot_id:
    :return:
    """
    snapshot_chart_list = repository.get_data(
        "dashboard_released_snapshot_chart",
        {"snapshot_id": snapshot_id, "dashboard_id": model.dashboard_id},
        get_released_snapshot_chart_fields(),
        multi_row=True,
    )
    for snapshot_chart in snapshot_chart_list:
        snapshot_chart['enable_subtotal'] = snapshot_chart.get('enable_subtotal_col')
        snapshot_chart['enable_summary'] = snapshot_chart.get('enable_subtotal_col_summary')
    return snapshot_chart_list


def get_snapshot_dashboard(model, snapshot_id):
    """
    获取dashboard快照表数据
    :param dashboard.models.ChartDataModel model:
    :param snapshot_id:
    :return:
    """
    snapshot_dashboard_dict = repository.get_data(
        "dashboard_released_snapshot_dashboard", {"snapshot_id": snapshot_id, "id": model.dashboard_id}, multi_row=False
    )
    return snapshot_dashboard_dict


def _get_dashboard_filters(snapshot_dashboard):
    """
    获取报告筛选条件json对象数据
    :param dict snapshot_dashboard:
    :return:
    """
    if snapshot_dashboard.get("dashboard_filters"):
        try:
            dashboard_filters = json.loads(snapshot_dashboard.get("dashboard_filters"))
            return dashboard_filters
        except Exception as e:
            raise UserError(message="报告筛选json数据解析失败，错误信息：" + str(e)) from e
    return []


def get_released_dashboard_modified_on(snapshot_id):
    """
    根据快照id获取报告发布的最新时间
    :param str snapshot_id: 报告发布快照id（如果是多屏则为多屏id，如果是单个报告则为报告id）
    :return:
    """
    modified_on = repository.get_data_scalar(
        "dashboard_released_snapshot_dashboard", {"snapshot_id": snapshot_id}, "modified_on"
    )
    return modified_on


def check_component_refresh(snapshot_id):
    """
    校验组件更新
    :param snapshot_id:
    :return:
    """
    result_refresh_flag = 0
    try:
        # 判断是否有组件更新行为，有的话则后端刷新元数据版本号
        _, refresh_flag = components_service.auto_upgrade_components()

        redis_cache = conn_redis()

        if refresh_flag:
            # 组件有更新行为的情况下，再查询单个报告的
            refresh_flag_key = dashboard_cache.get_components_refresh_flag_cache_key()
            dashboard_refresh_flag_key = '{0}_id_{1}'.format(refresh_flag_key, snapshot_id)
            cache_dashboard_refresh_flag = redis_cache.get_data(dashboard_refresh_flag_key)
            if not cache_dashboard_refresh_flag:
                result_refresh_flag = 1
                redis_cache.set_data(dashboard_refresh_flag_key, result_refresh_flag)

                # 刷新元数据缓存版本号
                cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
                if cache_instance:
                    cache_instance.refresh_version()
    except Exception as e:
        logger.error('判断是否有组件更新行为异常:{}'.format(str(e)))
        result_refresh_flag = 1

    return result_refresh_flag


def get_item_list(model: ChartDataModel):
    return chart_service.get_item_list(model)


def check_release_status(dashboard_id):
    """
    # todo 3.0迁移时需要使用此新方法
    校验当前报告是否在已发布多屏或单屏中
    :param dashboard_id:
    :return:
    """
    try:
        operate_snapshot_id_list = list()
        if not dashboard_id:
            return True
        conditions = {"id": dashboard_id, "status": DashboardStatus.Released.value}
        dashboard_record = repository.get_data(
            'dashboard_released_snapshot_dashboard', conditions, fields=['snapshot_id'], multi_row=True
        )
        # 如果修改的当前报告已存在发布表中，需要重新刷新元数据缓存版本
        if dashboard_record:
            operate_snapshot_id_list = [
                item.get('snapshot_id')
                for item in dashboard_record
                if item.get('snapshot_id') not in operate_snapshot_id_list
            ]
            for single_snapshot_id in list(set(operate_snapshot_id_list)):
                if single_snapshot_id:
                    # check_dataset_refresh(single_snapshot_id)
                    cache_instance = DashboardCache(g.code, single_snapshot_id, conn_redis())
                    if cache_instance:
                        cache_instance.refresh_version()

        return True
    except Exception as e:
        message = '元数据缓存操作失败，异常信息：{}'.format(str(e))
        logger.error(message)
        return False


def delete_metadata_dashboard_cache(dashboard):
    """
    删除元数据相关的缓存
    :param dashboard:
    :return:
    """
    # 删除报告元数据缓存
    metadata_service.delete_release_metadata(dashboard.get('id', ''))
    metadata_service.delete_release_metadata_cache(dashboard.get('id', ''))


def get_mark_image_text(request, kwargs):
    text = get_mark_image_account_text(request, kwargs)
    has_date = kwargs.get('date')
    if not has_date:
        return text

    # 计算文本长度和日期长度并设置居中
    alnum_size = 154 * 123 * 44
    cn_size = 154 * 123 * 77
    con_size = 154 * 77 * 44
    space_size = 123 * 77 * 44

    account_text_len_cn = (len(text.encode()) - len(text)) // 2
    account_text_len_en = len(text) - account_text_len_cn
    account_text_len = account_text_len_cn * cn_size + account_text_len_en * alnum_size
    date = datetime.now().strftime("%Y-%m-%d")
    date_len = 8 * alnum_size + 2 * con_size
    # 设置居中
    if (account_text_len - date_len) >= 2 * space_size:
        left_space = ' ' * ((account_text_len - date_len) // (2 * space_size))
        text = f'{text}\n{left_space}{date}'
    elif (date_len - account_text_len) >= 2 * space_size:
        left_space = ' ' * ((date_len - account_text_len) // (2 * space_size))
        text = f'{left_space}{text}\n{date}'
    else:
        text = f'{text}\n{date}'
    return text


def get_mark_image_account_text(request, kwargs):
    """
    根据用户登录情况获取水印图片文字数据
    :param request:
    :param kwargs:
    :return:
    """
    user_id = get_cur_user_id(request)
    token = request.cookies.get('token', '')
    token_data = {}
    if token:
        _, _, token_data = dashboard_service.check_token(token)
        user_id = token_data.get('id')
        if token_data.get('account'):
            g.account = token_data.get('account')

    # 1. 用户名称+账号
    if user_id:
        text = get_text_from_user_info(user_id)
        if text:
            return text

    # 2. 用户账号或名称
    req_account = g.account if hasattr(g, 'account') and g.account else ''
    if req_account and request.cookies.get('_account') != 'external_user':
        user = user_service.get_user_by_account(req_account)
        if user:
            text = '{account}{user_name}'.format(user_name=user.get('name') or '', account=req_account or '')
            if text:
                return text
        else:
            return req_account

    # 3. 第三方鉴权访问报告的场景，优化显示第三方参数中的`user_name`
    external_user_name = get_specific_external_param('user_name')
    if external_user_name:
        return external_user_name

    # 4. 第三方没有用户信息的场景，显示`external_user`
    external_account = token_data.get('account', '')
    if external_account:
        return external_account

    # 5. 密码访问且无用户信息的场景，显示租户代码@域名
    text = get_text_from_project_code(request, kwargs.get('code'))
    return text


def get_cur_user_id(request):
    """
    获取用户id
    :param request:
    :return:
    """
    return g.userid if hasattr(g, 'userid') else ''


def get_text_from_user_info(user_id):
    """
    g.account为空的场景，尝试通过user_id获取账号，姓名
    :param user_id:
    :return:
    """
    user_info = external_user_service.get_user_by_id(user_id, ['name', 'account'])
    text = ''
    if user_info:
        text = '{}{}'.format(user_info.get('account') or '', user_info.get('name') or '')
    return text


def get_text_from_project_code(request, req_code):
    """
    密码访问且无用户信息的场景，显示租户代码@域名
    :param request:
    :param req_code:
    :return:
    """
    project_code = g.code if hasattr(g, 'code') and g.code else ''
    if not project_code and request.cookies.get('tenant_code') or req_code:
        project_code = request.cookies.get('tenant_code') or req_code or ''
    text = '{code}@{domain}'.format(code=project_code, domain=request.host) if project_code else request.host
    return text


def get_specific_external_param(param):
    """
    取第三方跳转的external_params中的参数
    :return:
    """
    external_params = g.external_params if hasattr(g, "external_params") else {}
    return external_params.get(param, "") if external_params else ""


def get_mark_image_options_by_params(layout_type, platform):
    """
    根据参数获取不同字体大小
    :param layout_type:
    :param platform:
    :return:
    """
    options = {"font_size": 20, "width_offset": 180, "height_offset": 80}
    if layout_type == "free" and platform == 'mobile':
        options["font_size"] = 28
        options["width_offset"] = 210
        options["height_offset"] = 110
    if layout_type == "free" and platform == 'pc':
        options["font_size"] = 20
        options["width_offset"] = 180
        options["height_offset"] = 80
    if layout_type == 'grid' and platform == 'pc':
        options["font_size"] = 14
        options["width_offset"] = 100
        options["height_offset"] = 40
    return options


@data_permission_edit_filter("dashboard-edit")
def release_with_process(current_model: ReleaseModel) -> bool:
    """
    发布报告
    1，发布过程整合为一个sql事务提交，发布过程中出现异常将不会对原有数据进行写入
    2，加入报告元数据的获取和校验逻辑，元数据获取逻辑参考query_preview_metadata_model.py文件，具体校验逻辑参考schema_validator.py文件
    :param ReleaseModel current_model:
    :return:
    """
    return release_dashboard(current_model)


def release_dashboard(current_model: ReleaseModel) -> bool:
    """
    发布报告、取消发布报告
    1，发布过程整合为一个sql事务提交，发布过程中出现异常将不会对原有数据进行写入
    2，加入报告元数据的获取和校验逻辑，元数据获取逻辑参考query_preview_metadata_model.py文件，具体校验逻辑参考schema_validator.py文件
    :param ReleaseModel current_model:
    :return:
    """
    all_release_dashboard = _get_all_release_dashboard(current_model)
    with get_db() as conn:
        try:
            for release_dashboard in all_release_dashboard:
                model = release_dashboard.get('model')
                dashboard = release_dashboard.get('dashboard')
                _handle_release_dashboard(conn, model, dashboard)
            # 6. 全部ok则统一提交
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise UserError(message="数据报告发布异常：{}".format(str(e))) from e
    # 实物提交之后处理
    after_handle_release_dashboard(all_release_dashboard)
    # 记录报告发布时刻的设计时数据
    try:
        # 报告发布则记录，报告取消发布则不记录
        if int(current_model.status) == DashboardTypeStatus.Release.value:
            add_dashboard_released_design_data(current_model, all_release_dashboard)
    except Exception as e:
        logger.exception(msg="报告发布时设计时数据存储错误，异常信息: " + str(e))
    return True


def _get_all_release_dashboard(current_model: ReleaseModel):
    # 获取所有子报告，包括已经被标记删除的
    all_dashboard = dashboard_service.get_child_dashboard(current_model.id, with_deleted=True)
    all_release_dashboard = []
    for dashboard in all_dashboard:
        model = ReleaseModel(**current_model.get_dict())  # 复制
        model.id = dashboard.get('id')
        model.validate()
        dashboard = dashboard_service.get_dashboard_advanced(model.id)
        if not dashboard:
            raise UserError(message="报告不存在，报告id:{id}".format(id=model.id))
        dashboard["status"] = model.status
        dashboard["share_secret_key"] = model.view_passwd
        dashboard["type_access_released"] = model.type_access_released
        all_release_dashboard.append({'model': model, 'dashboard': dashboard})
    return all_release_dashboard


def _get_screens_data(screens, dashboards, model):
    data_list = list()
    screen_dict = dict()
    for screen in screens:
        data_list.append(
            {
                "screen_id": screen["screen_id"],
                "type": DashboardTypeStatus.Release.value,
                "dashboard_id": model.id,
                "rank": screen["rank"],
                "id": seq_id(),
            }
        )

        # 3. 把单图的数据都抓出来保存到发布表
        tmp_screen = dashboard_service.get_dashboard_advanced(screen["screen_id"])
        if tmp_screen:
            dashboards["screens"].append(tmp_screen)
        chart_list = chart_service.get_chart_list(screen["screen_id"], True, True, need_extra_chart_flag=True)
        if chart_list:
            screen_dict[screen["screen_id"]] = chart_list
    return data_list, screen_dict


def _handle_release_dashboard(conn, model: ReleaseModel, dashboard):
    """
    报告发布主逻辑
    :param conn:
    :param model:
    :param dashboard:
    :return:
    """
    commit = False

    # 删除旧的快照表数据
    conn.delete("dashboard_released_snapshot_dashboard", {"snapshot_id": model.id}, commit=commit)
    conn.delete("dashboard_released_snapshot_chart", {"snapshot_id": model.id}, commit=commit)

    # 1. 删除旧的多屏关系表
    conn.delete(
        "screen_dashboard", {"dashboard_id": model.id, "type": DashboardTypeStatus.Release.value}, commit=commit
    )

    # 处理用户组查看权限
    _deal_with_user_group_permission(model, conn, commit)

    # 如果在更新发布过程中，发现报告是子报告，且已经被标记删除
    # 将报告标记为未发布, 在发布逻辑执行后删除报告, 每次发布时都有机会进行删除, 不做进事务
    if dashboard.get('is_deleted') and dashboard.get('type') == DashboardType.CHILD_FILE.value:
        conn.update("dashboard", {"status": DashboardStatus.Drafted.value}, {"id": model.id}, commit=commit)
        return

    # Save dashboard status
    dashboard_status_update_conditions = {
        "status": model.status,
        "share_secret_key": model.view_passwd,
        "type_access_released": model.type_access_released,
    }
    conn.update("dashboard", dashboard_status_update_conditions, {"id": model.id}, commit=commit)

    # 删除缓存
    dashboard_release_cache = DashboardReleaseCache(g.code, model.id, conn_redis())
    dashboard_release_cache.delete_dashboard_data()

    # 如果是发布则走以下流程
    if int(model.status) != 0:

        # 2. 找到保存草稿的数据
        dashboards = {"dashboard": dashboard, "screens": []}

        if dashboard["is_multiple_screen"] == 0:
            _screens = [{"dashboard_id": dashboard["id"], "screen_id": dashboard["id"], "rank": 1}]
        else:
            _screens = repository.get_data(
                "screen_dashboard",
                {"dashboard_id": model.id, "type": DashboardTypeStatus.Draft.value},
                ["dashboard_id", "screen_id", "rank"],
                True,
                [("rank", "asc")],
            )
        screens, screen_dict = _get_screens_data(_screens, dashboards, model)

        # 存入screen_dashboard（单个报告和多屏都存入）
        if len(screens) > 0:
            conn.insert_multi_data(
                "screen_dashboard", screens, ["id", "dashboard_id", "screen_id", "type", "rank"], commit=commit
            )

        # 4. Save dashboard_released_snapshot表数据
        save_released_snapshot_dashboard_from_dashboard_list(dashboards, commit=commit, conn=conn)
        save_released_snapshot_chart(screen_dict, model.id, dashboard, commit=commit, conn=conn)


def after_handle_release_dashboard(all_release_dashboard):
    # pylint: disable=C0415
    from app_celery import create_dataset_data_index

    for release_dashboard in all_release_dashboard:
        model = release_dashboard.get('model')
        dashboard = release_dashboard.get('dashboard')
        # 7. 删除已发布报告缓存
        _delete_metadata_dashboard_cache(dashboard)

        # 如果是子报告，且被标记删除，在这里执行实际的删除操作
        if dashboard.get('is_deleted') and dashboard.get('type') == DashboardType.CHILD_FILE.value:
            dashboard_service.delete_child_dashboard(model.id)

        # 取消发布的情况，需要清理dashboard_extra的登记时间
        if int(model.status) == DashboardTypeStatus.Draft.value:
            dashboard_extra_service.reset_edit_and_released_on(model.id)
        # 报告发布的情况，需要登记当前报告的发布时间
        elif int(model.status) == DashboardTypeStatus.Release.value:
            dashboard_extra_service.reset_edit_or_released_on(model.id, "edit_on")
            dashboard_extra_service.update_dashboard_released_on(model.id)

        # 异步处理数据集收集需要索引字段
        create_dataset_data_index.apply_async(kwargs={"dashboard_id": model.id, "project_code": g.code})


def _deal_with_user_group_permission(model: ReleaseModel, conn: SimpleMysql, commit: bool) -> None:
    """
    处理用户组查看权限
    :param model:
    :param conn:
    :param commit:
    :return:
    """
    if int(model.type_access_released) == DashboardTypeAccessReleased.UserGroups.value:
        conn.delete("user_group_dashboard", {"dashboard_id": model.id}, commit=commit)

        # 校验group是否存在
        if not check_group_exists(model.user_groups):
            raise UserError(message="提交的用户组不存在")
        dashboard_acccess_user_groups = []
        for group in model.user_groups:
            dashboard_acccess_user_groups.append({"group_id": group, "dashboard_id": model.id})
        if len(dashboard_acccess_user_groups) > 0:
            conn.insert_multi_data(
                "user_group_dashboard", dashboard_acccess_user_groups, ["group_id", "dashboard_id"], commit=commit
            )


def save_released_snapshot_dashboard_from_dashboard_list(
        dashboard_dict: Dict[
            str,
            Union[
                Dict[str, Union[str, int, Dict[str, Union[int, str]], Dict[str, Union[bool, str]], None]],
                List[Dict[str, Union[str, int, Dict[str, Union[int, str]], Dict[str, Union[bool, str]], None]]],
            ],
        ],
        commit: bool = True,
        conn: Optional[SimpleMysql] = None,
) -> bool:
    """
    根据传入的dashboard的多屏和多屏下的报告数据数组添加数据到dashboard_released_snapshot_dashboard快照表
    :param dict dashboard_dict: dashboard的多屏和多屏下的报告数据数组
    :param bool commit: 是否提交写入sql标志位
    :param instance conn: sql连接实例
    :return: dict 结构跟dashboard_dict类似但里面的数据由新生成的表id替换的数据
    """
    if not dashboard_dict or not isinstance(dashboard_dict, dict):
        raise UserError(message="报告快照数据为空")

    dashboard = dashboard_dict["dashboard"]
    snapshot_id = dashboard.get("id")

    single_save_released_snapshot_dashboard(dashboard, snapshot_id, data_type=0, commit=commit, conn=conn)

    for dashboard in dashboard_dict.get("screens"):
        single_save_released_snapshot_dashboard(dashboard, snapshot_id, data_type=1, commit=commit, conn=conn)
    return True


def single_save_released_snapshot_dashboard(
        dashboard: Dict[str, Union[str, int, Dict[str, Union[int, str]], Dict[str, Union[bool, str]], None]],
        snapshot_id: str,
        data_type: int = 0,
        commit: bool = True,
        conn: Optional[SimpleMysql] = None,
) -> str:
    """
    根据报告表单个数据数组添加数据到dashboard_released_snapshot_dashboard快照表
    [标记] 发布时保存报告信息
    :param dict dashboard: 报告表单条记录
    :param str snapshot_id: 快照表id
    :param int data_type: 0 表示是dashboard（多屏数据）1表示是screens（多屏下每个报告）数据
    :param bool commit: 是否sql写入提交标志位
    :param instance conn: sql连接实例
    :return: str 新生成的id
    """
    dashboard["snapshot_id"] = snapshot_id
    dashboard["data_type"] = data_type
    dashboard["dashboard_filters"] = (
        json.dumps(dashboard.get("dashboard_filters")) if dashboard.get("dashboard_filters") else []
    )
    dashboard["global_params"] = (
        json.dumps(dashboard.get("global_params")) if dashboard.get("global_params") else []
    )
    dashboard["dashboard_var_filters"] = (
        json.dumps(dashboard.get("dashboard_var_filters")) if dashboard.get("dashboard_var_filters") else []
    )
    dashboard["selectors"] = json.dumps(dashboard.get("selectors")) if dashboard.get("selectors") else {}
    dashboard["background"] = json.dumps(dashboard.get("background")) if dashboard.get("background") else None
    dashboard["layout"] = json.dumps(dashboard.get("layout")) if dashboard.get("layout") else None
    dashboard['refresh_rate'] = '' if not dashboard.get('refresh_rate') else dashboard.get('refresh_rate')
    dashboard['var_value_sources'] = json.dumps(dashboard.get('var_value_sources', []))
    dashboard_model = ReleasedDashboardModel(**dashboard)
    if commit:
        repository.add_model(
            "dashboard_released_snapshot_dashboard", dashboard_model, _get_released_snapshot_dashboard_fields()
        )
    else:
        fields = _get_released_snapshot_dashboard_fields()
        add_data = dashboard_model.get_dict(fields)
        conn.insert("dashboard_released_snapshot_dashboard", add_data, commit=commit)
    return dashboard.get("id")


def _get_released_snapshot_dashboard_fields() -> List[str]:
    """
    返回dashboard_released_snapshot_dashboard表字段列表
    :return:
    """
    return [
        "id",
        "snapshot_id",
        "name",
        "type",
        "data_type",
        "level_code",
        "platform",
        "is_multiple_screen",
        "status",
        "user_group_id",
        "cover",
        "selectors",
        "dashboard_filters",
        "dashboard_var_filters",
        "share_secret_key",
        "layout",
        "scale_mode",
        "background",
        "biz_code",
        "type_access_released",
        "theme",
        "refresh_rate",
        "grid_padding",
        "create_type",
        "new_layout_type",
        "is_show_mark_img",
        "var_value_sources",
        "terminal_type",
        "main_external_subject_id",
        "application_type",
        "external_subject_ids",
        "line_height",
        "smart_beauty_status",
        "dataset_id",
        "parent_id",
        "asset_id",
        "analysis_type",
        "auto_play",
        "global_params",
    ]


def _assign_params_jump_to_field(chart_params_jump_with_index, fields, field_id_key):
    """
    为维度或度量赋值参数跳转配置
    :param chart_params_jump_with_index:
    :param fields:
    :return:
    """
    for field in fields:
        field["chart_params_jump"] = []
        if field.get(field_id_key) in chart_params_jump_with_index:
            field["chart_params_jump"] = chart_params_jump_with_index.get(field.get(field_id_key))
    return fields


def _save_released_single_snapshot_chart(
        released_dashboard_id, chart_list, chart_params_jump_with_index, fields, conn, commit
):
    for chart in chart_list:
        chart["snapshot_id"] = released_dashboard_id
        chart["chart_params_jump"] = []
        # 维度度量添加参数跳转配置
        if chart.get("id") in chart_params_jump_with_index:
            _assign_params_jump_to_field(chart_params_jump_with_index[chart.get("id")], chart.get("dims"), "dim")
            _assign_params_jump_to_field(chart_params_jump_with_index[chart.get("id")], chart.get("nums"), "num")
            for _id, _each in chart_params_jump_with_index[chart.get('id')].items():
                chart['chart_params_jump'] += _each
        chart_model = ReleasedChartModel(**chart)
        chart_model.component_filter = _transform_to_dumped_data(chart_model.component_filter)
        chart_model.chart_params = _transform_to_dumped_data(chart_model.chart_params)
        chart_model.chart_params_jump = _transform_to_dumped_data(chart_model.chart_params_jump)
        chart_model.dims = _transform_to_dumped_data(chart_model.dims)
        chart_model.comparisons = _transform_to_dumped_data(chart_model.comparisons)
        chart_model.nums = _transform_to_dumped_data(chart_model.nums)
        chart_model.zaxis = _transform_to_dumped_data(chart_model.zaxis)
        chart_model.desires = _transform_to_dumped_data(chart_model.desires)
        chart_model.filters = _transform_to_dumped_data(chart_model.filters)
        chart_model.marklines = _transform_to_dumped_data(chart_model.marklines)
        chart_model.penetrates = _transform_to_dumped_data(_filter_penetrate_field(chart_model.penetrates))
        chart_model.layers = _transform_to_dumped_data(chart_model.layers)
        chart_model.jump = _transform_to_dumped_data(chart_model.jump)
        chart_model.penetrate_relation = _transform_to_dumped_data(chart_model.penetrate_relation)
        chart_model.penetrate_filter_relation = _transform_to_dumped_data(chart_model.penetrate_filter_relation)
        chart_model.penetrate_var_filter_relation = _transform_to_dumped_data(chart_model.penetrate_var_filter_relation)
        chart_model.chart_filter = _transform_to_dumped_data(chart_model.chart_filters)
        chart_model.chart_linkage = _transform_to_dumped_data(chart_model.chart_linkages)
        chart_model.chart_default_value = _transform_to_dumped_data(chart_model.chart_default_value)
        # 变量相关的两个字段
        chart_model.var_relations = _transform_to_dumped_data(chart_model.var_relations)
        chart_model.chart_vars = _transform_to_dumped_data(chart_model.chart_vars)
        chart_model.field_sorts = _transform_to_dumped_data(chart_model.field_sorts)
        chart_model.chart_visible_triggers = _transform_to_dumped_data(chart_model.chart_visible_triggers)
        if commit:
            repository.add_model("dashboard_released_snapshot_chart", chart_model, fields)
        else:
            add_data = chart_model.get_dict(fields)
            conn.insert("dashboard_released_snapshot_chart", add_data, commit=commit)


def save_released_snapshot_chart(screen_dict, released_dashboard_id, dashboard, commit=True, conn=None):
    """

    :param screen_dict:
    :param released_dashboard_id:
    :param dashboard:
    :param commit:
    :param conn:
    :return:
    """
    if not screen_dict or not isinstance(screen_dict, dict):
        raise UserError(message=f"报告【{dashboard.get('name')}】不存在任何组件，请检查后发布")
    fields = get_released_snapshot_chart_fields()

    for dashboard_id, chart_list in screen_dict.items():
        chart_id_list = [chart.get("id") for chart in chart_list]
        # 所有图的参数跳转配置
        chart_params_jump = chart_repository.batch_get_chart_params_jump(chart_id_list)
        chart_params_jump_with_index = _get_chart_params_jump_with_index(chart_params_jump)
        del chart_params_jump
        _save_released_single_snapshot_chart(
            released_dashboard_id, chart_list, chart_params_jump_with_index, fields, conn, commit
        )
    return True


def _transform_to_dumped_data(data: Optional[List[Dict[str, Union[str, int, None]]]]) -> str:
    """
    json.dumps处理
    :param data:
    :return:
    """
    return json.dumps(data) if data else []


def _get_chart_params_with_index(chart_params: List[Any]) -> Dict[Any, Any]:
    """

    :param chart_params:
    :return:
    """
    chart_params_with_index = dict()
    for chart_param in chart_params:
        if chart_param.get("dashboard_chart_id") not in chart_params_with_index:
            chart_params_with_index[chart_param.get("dashboard_chart_id")] = []
        chart_params_with_index[chart_param.get("dashboard_chart_id")].append(chart_param)
    return chart_params_with_index


def _get_chart_params_jump_with_index(chart_params_jump: List[Any]) -> Dict[Any, Any]:
    """

    :param chart_params_jump:
    :return:
    """
    chart_params_jump_with_index = dict()
    for jump in chart_params_jump:
        if jump.get("dashboard_chart_id") not in chart_params_jump_with_index:
            chart_params_jump_with_index[jump.get("dashboard_chart_id")] = {}
        if jump.get("source_id") not in chart_params_jump_with_index.get(jump.get("dashboard_chart_id")):
            chart_params_jump_with_index[jump.get("dashboard_chart_id")][jump.get("source_id")] = []
        chart_params_jump_with_index[jump.get("dashboard_chart_id")][jump.get("source_id")].append(jump)
    return chart_params_jump_with_index


def validate_metadata(dashboard: Dict[str, Union[str, int, None]], conn: SimpleMysql) -> Tuple[bool, str]:
    """
    校验报告元数据,包括多屏下的报告
    :param dashboard:
    :param conn:
    :return:
    """
    # pylint: disable=C0415
    from dashboard_chart.metadata.query_release_metadata_model import ReleasedDashboardMetadataQueryModel

    screen_metadata = ReleasedDashboardMetadataQueryModel(
        snapshot_id=dashboard['id'], conn=conn
    ).combine_screen_metadata()
    screen_metadata_data = screen_metadata.get_data()
    validate_code, validate_msg = MetadataValidator(data=screen_metadata_data).validate_screens_metadata()
    if not validate_code:
        return validate_code, validate_msg
    # 多屏下的报告
    screens = screen_metadata_data.get('screens')
    if screens and len(screens):
        for single_dashboard in screens:
            dashboard_id = single_dashboard.get('dashboard_id')
            snapshot_id = single_dashboard.get('snapshot_id')
            single_released_dashboard = metadata_service.get_released_dashbaoard_data_with_conn(
                snapshot_id, dashboard_id, conn
            )
            dashboard_metadata_data = ReleasedDashboardMetadataQueryModel(conn=conn).combine_dashboard_metadata(
                operate_dashboard_id=dashboard_id,
                operate_snapshot_id=snapshot_id,
                single_released_dashboard=single_released_dashboard,
            )
            validate_code, validate_msg = DashboardMetadataValidator(data=dashboard_metadata_data).validate_metadata()
            if not validate_code:
                return validate_code, validate_msg
    return validate_code, validate_msg


def _delete_metadata_dashboard_cache(dashboard: Dict[str, Union[str, int, None]]) -> None:
    """
    删除元数据相关的缓存
    :param dashboard:
    :return:
    """
    # 删除报告元数据缓存
    metadata_service.delete_release_metadata(dashboard.get('id', ''))
    metadata_service.delete_release_metadata_cache(dashboard.get('id', ''))
    # 删除报告取数缓存, 重点大屏才需要清除重点大屏缓存
    if dashboard.get('is_key'):
        metadata_service.delete_last_version_cache(dashboard.get('id', ''))


# def get_released_on_and_check_component_refresh(snapshot_id):
#     """
#     获取报告发布最新时间和校验是否有组件更新行为(已弃用)
#     :param snapshot_id:
#     :return:
#     """
#     # 返回报告发布的最新时间，前端根据这个时间判断是否重新加载元数据缓存
#     modified_on = get_released_dashboard_modified_on(snapshot_id)
#
#     # 20190329 兼容modified_on字段被无故更新的情况
#     result_refresh_flag = _del_screen_metadata(snapshot_id, modified_on)
#
#     # 校验组件更新
#     if not result_refresh_flag:
#         result_refresh_flag = check_component_refresh(snapshot_id)
#
#     # 校验数据集有无更新，有更新的情况下需要重新拉取数据
#     versions_map = get_dataset_versions_of_charts(snapshot_id)
#
#     # 0没有组件更新行为 1有组件更新行为
#     return {
#         "modified_on": modified_on,
#         "component_refresh_flag": result_refresh_flag,
#         "data_refresh_flag": 0,  # deprecated
#         "dataset_versions": versions_map,
#     }


def get_dashboard_release_info(snapshot_id, req_meta_version, download_flag):
    """
    获取已发布报告的相关信息
    :param snapshot_id:
    :param req_meta_version:
    :return:
    """
    # 元数据是否刷新标志位
    meta_fresh_flag = 0

    # 组件的自动更新
    components_service.auto_upgrade_components()

    # # 校验数据集有无更新，有更新的情况下需要重新拉取数据
    # versions_map, version_in_md5 = get_md5_version_by_snapshot_id(snapshot_id)
    # 不再校验数据集
    versions_map, version_in_md5 = {}, ''

    # 元数据etag版本号不相等的情况，通知前端重新获取报告元数据
    if not metadata_service.compare_metadata_etag(
            req_meta_version, snapshot_id, version_in_md5, download_flag=download_flag
    ):
        meta_fresh_flag = 1

    return {"data_refresh_flag": 0, "dataset_versions": versions_map, "meta_fresh_flag": meta_fresh_flag}


def _del_screen_metadata(snapshot_id, cur_modified_on):
    """
    删除元数据缓存
    ps:兼容快照表的modified_on字段被更新的情况，需要删除元数据缓存，并通知前端重新拉取元数据，方法返回result_refresh_flag=1
    :param snapshot_id:
    :return:
    """
    result_refresh_flag = 0
    try:
        if not cur_modified_on:
            return result_refresh_flag
        cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
        dashboard_info = cache_instance.get_prop(cache_instance.prop_info)
        if not dashboard_info:
            return result_refresh_flag
        cache_modified_on = dashboard_info.get("modified_on")
        if not cache_modified_on:
            return result_refresh_flag
        if not isinstance(cache_modified_on, datetime):
            cache_modified_on = datetime.strptime(cache_modified_on, "%Y-%m-%d %H:%M:%S")
        if not isinstance(cur_modified_on, datetime):
            cur_modified_on = datetime.strptime(cur_modified_on, "%Y-%m-%d %H:%M:%S")
        # 删除元数据缓存，并返回标志位1
        if cur_modified_on != cache_modified_on:
            metadata_service.delete_release_metadata_cache(snapshot_id)
            result_refresh_flag = 1
            return result_refresh_flag
        return result_refresh_flag
    except Exception:
        return result_refresh_flag


def get_dataset_versions_of_charts(snapshot_id):
    """
    校验数据集更新
    :param snapshot_id:
    :return:
    """
    cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
    # 兼容还没有报告内数据集版本缓存的，需要拉取生成一次
    operate_chart_source_list = list()
    screens_cache_data = cache_instance.get_prop(cache_instance.prop_screens_detail)
    if not screens_cache_data:
        return None

    # 获取当前快照的全部报告id(可能是多屏?)
    operate_dashboard_ids = [item.get('id') for item in screens_cache_data]
    if not len(operate_dashboard_ids):
        return None

    # 目前限制一个chart最多只对应一个dataset
    charts_dataset_map = dict()
    for single_dashboard in operate_dashboard_ids:

        # 获取单图数据, 缓存key与ReleasedDashboardMetadataQueryModel中保持一致
        # TODO: 把缓存的key提取到DashboardCache
        chart_id_list_key = "{}_chart_id_list".format(single_dashboard)
        chart_id_list_cache_data = cache_instance.get_prop(chart_id_list_key)
        if not chart_id_list_cache_data:
            continue

        # 获取数据集ids
        for single_chart_id in chart_id_list_cache_data:
            charts_dataset_map[single_chart_id] = None
            chart_cache_data = cache_instance.get_chart(single_dashboard, single_chart_id)
            if chart_cache_data and chart_cache_data.get('source'):
                operate_chart_source_list.append(chart_cache_data.get('source'))
                charts_dataset_map[single_chart_id] = chart_cache_data.get('source')

    if operate_chart_source_list:
        dataset_versions = external_query_service.get_dataset_versions(list(set(operate_chart_source_list)))
        return {
            chart_id: dataset_versions[dataset_id] if dataset_id else None
            for chart_id, dataset_id in charts_dataset_map.items()
        }

    return None


# def get_dataset_versions_of_charts_by_test1(snapshot_id):
#     """
#     校验数据集更新
#     :param snapshot_id:
#     :return:
#     """
#     cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
#     # 兼容还没有报告内数据集版本缓存的，需要拉取生成一次
#     operate_chart_source_list = list()
#     screens_cache_data = cache_instance.get_prop(cache_instance.prop_screens_detail)
#     if not screens_cache_data:
#         return None
#
#     # 获取当前快照的全部报告id(可能是多屏?)
#     operate_dashboard_ids = [item.get('id') for item in screens_cache_data]
#     if not len(operate_dashboard_ids):
#         return None
#
#     # 目前限制一个chart最多只对应一个dataset
#     charts_dataset_map = dict()
#     sql = 'SELECT id, source FROM `dashboard_released_snapshot_chart` WHERE dashboard_id in %(ids)s'
#     datas = repository.get_data_by_sql(sql, params={'ids': operate_dashboard_ids or ['']}) or []
#
#     for data in datas:
#         operate_chart_source_list.append(data['source'])
#         charts_dataset_map[data['id']] = data['source']
#
#     if operate_chart_source_list:
#         operate_chart_source_list = [i for i in operate_chart_source_list if i]
#         dataset_versions = external_query_service.get_dataset_versions(list(set(operate_chart_source_list)))
#         return {
#             chart_id: dataset_versions[dataset_id] if dataset_id else None
#             for chart_id, dataset_id in charts_dataset_map.items()
#         }
#
#     return None
#
#
# def get_dataset_versions_of_charts_by_test2(snapshot_id):
#     """
#     校验数据集更新
#     :param snapshot_id:
#     :return:
#     """
#     cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
#     # 兼容还没有报告内数据集版本缓存的，需要拉取生成一次
#     operate_chart_source_list = list()
#     screens_cache_data = cache_instance.get_prop(cache_instance.prop_screens_detail)
#     if not screens_cache_data:
#         return None
#
#     # 获取当前快照的全部报告id(可能是多屏?)
#     operate_dashboard_ids = [item.get('id') for item in screens_cache_data]
#     if not len(operate_dashboard_ids):
#         return None
#
#     # 目前限制一个chart最多只对应一个dataset
#     charts_dataset_map = dict()
#     for single_dashboard in operate_dashboard_ids:
#
#         # 获取单图数据, 缓存key与ReleasedDashboardMetadataQueryModel中保持一致
#         # TODO: 把缓存的key提取到DashboardCache
#         chart_id_list_key = "{}_chart_id_list".format(single_dashboard)
#         chart_id_list_cache_data = cache_instance.get_prop(chart_id_list_key)
#         if not chart_id_list_cache_data:
#             continue
#
#         datas =  cache_instance.batch_get_charts(single_dashboard, chart_id_list_cache_data)
#         for data in datas:
#             operate_chart_source_list.append(data['source'])
#             charts_dataset_map[data['id']] = data['source']
#
#     if operate_chart_source_list:
#         operate_chart_source_list = [i for i in operate_chart_source_list if i]
#         dataset_versions = external_query_service.get_dataset_versions(list(set(operate_chart_source_list)))
#         return {
#             chart_id: dataset_versions[dataset_id] if dataset_id else None
#             for chart_id, dataset_id in charts_dataset_map.items()
#         }
#
#     return None


def get_dataset_versions_in_md5(versions_map):
    """
    获取MD5后的数据集版本号数据
    :param versions_map:
    :return:
    """
    sorted_map_keys = sorted(versions_map.keys()) if versions_map else []
    str_version_list = []
    for chart_id in sorted_map_keys:
        item = versions_map.get(chart_id, {})
        dataset_version = item.get("version", "") if item else ""
        dataset_version = "" if dataset_version is None else dataset_version
        str_version_list.append(chart_id + "=" + dataset_version)
    str_version = '&'.join(str_version_list)
    return hashlib.md5(str_version.encode('utf-8')).hexdigest()[8:-8]


def get_md5_version_by_snapshot_id(snapshot_id):
    """
    获取数据集版本数据和md5值
    报告的元数据etag计算不再使用数据集信息
    :param snapshot_id:
    :return:
    """
    versions_map = get_dataset_versions_of_charts(snapshot_id)
    version_in_md5 = get_dataset_versions_in_md5(versions_map)
    return versions_map, version_in_md5


def _delete_single_dashboard_cache(dashboard, cache):
    dashboard_charts = chart_repository.get_chart_id_list(dashboard.get("id"))
    if dashboard_charts:
        for dashboard_chart in dashboard_charts:
            key = get_released_redis_key(dashboard_chart.get("id"))
            cache.del_by_scan(pattern="*" + key + "*")
            # keys = cache._connection.keys(pattern="*" + key + "*")
            # if keys:
            #     cache._connection.delete(*keys)


def _delete_screen_dashboard_cache(screens, cache):
    for screen in screens:
        chart_list = chart_repository.get_chart_id_list(screen.get("screen_id"))
        if chart_list:
            for chart_item in chart_list:
                key = get_released_redis_key(chart_item.get("id"))
                cache.del_by_scan(pattern="*" + key + "*")
                # keys = cache._connection.keys(pattern="*" + key + "*")
                # if keys:
                #     cache._connection.delete(*keys)


def _delete_released_dashboard_cache(dashboard, screens):
    """
    删除已发布报告的缓存
    :param dict dashboard: 报告/多屏数据
    :param list screens: 报告下的单图数组
    :return:
    """
    cache = RCache()
    if dashboard["is_multiple_screen"] == 0:
        _delete_single_dashboard_cache(dashboard, cache)
    else:
        _delete_screen_dashboard_cache(screens, cache)


def get_released_redis_key(chart_id, conditions=None):
    """
    返回报告发布的redis缓存中的单图缓存key
    :param str chart_id: 单图id
    :param list conditions: 条件参数
    :return:
    """
    if conditions:
        token = hashlib.md5()
        token.update(str(conditions).encode("utf-8"))
        chart_id = chart_id + "_" + token.hexdigest()
    return "dmp:screen:released:" + chart_id


def assign_params_for_download(**kwargs):
    """
    补充参数数据
    :param kwargs:
    :return:
    """
    chart_params_list = kwargs.get('chart_params')
    if not chart_params_list:
        return kwargs
    single_chart_params = chart_params_list[0] if len(chart_params_list) else {}
    single_chart_params["download_from"] = TableDownloadFrom.Released.value
    kwargs["chart_params"] = [single_chart_params]
    return kwargs


def _get_exception_struct_struct(msg, code):
    """
    获取结构异常时返回数据结构
    :param str msg: 错误信息
    :param str code: code标识
    :return:
    """
    return {
        "dataset_name": "",
        "msg_code": code,
        "msg": msg,
        "meta_version": "",
        "execute_status": DashboardSQLExecuteStatus.NOExectue.value,
        "query_structure": "",
    }


def get_chart_query_struct(model):
    """
    单个获取单图展示数据
    :param dashboard_chart.models.ChartDataModel model:
    :return:
    """
    try:
        model.validate()
        obj = _generate_get_data_chart_class(model)
        if not model.dataset_id:
            struct = _get_exception_struct_struct("没有配置数据集", DashboardDataMsgCode.NullDatasetData.value)
        elif not model.dims and not model.nums:
            struct = _get_exception_struct_struct("没有配置维度和度量", DashboardDataMsgCode.NullDatasetData.value)
        else:
            struct = obj.get_query_struct()
    except Exception as e:
        if not isinstance(e, UserError):
            logger.exception(msg="单图取数接口异常，异常信息: " + str(e))
        struct = _get_exception_struct_struct(str(e), DashboardDataMsgCode.NullDatasetData.value)
    return struct


def _filter_penetrate_field(data: list) -> list:
    # 发布的时候穿透的数据只需要一部分
    if not data:
        return data
    ret = []
    fields = ("id", "parent_id", "penetrate_relation", "penetrate_filter_relation", "penetrate_var_filter_relation")
    for chart in data:
        item = {}
        for field in fields:
            if field in chart:
                item[field] = chart[field]
        ret.append(item)
    return ret


def get_dashboard_release_url(dashboard_id: str):
    info = repository.get_one('dashboard', {'id': dashboard_id}, "platform,terminal_type")
    if not info:
        raise UserError(code=404, message="找不到报告")
    url_path = config.get("Domain.dmp")
    # 没有考第三方鉴权
    if info["platform"] == "mobile" and info["terminal_type"] == "mobile_screen":
        url_path += f"/dataview-mobile/view/{dashboard_id}?code={g.code}"
    else:
        url_path += f"/dataview/share/{dashboard_id}?code={g.code}"
    return url_path


def _get_filter_selector_dict(conditions, chart_responder_id, dashboard_id):
    filter_data_dict = {}
    if not conditions:
        return filter_data_dict
    chart_filter_map = {}
    if conditions:
        chart_initiator_ids = [condition['chart_id'] for condition in conditions]
        chart_filter_map = _get_release_snapshot_chart_filter_map(chart_initiator_ids)
    for single_condition in conditions:
        if not single_condition:
            continue
        chart_initiator_id = single_condition["chart_id"]  # 发起者单图id
        if not chart_initiator_id:
            continue
        filter_data_dict.setdefault(chart_initiator_id, [])
        filter_data = _get_release_filter_data_v2(
            chart_initiator_id, chart_responder_id, dashboard_id, chart_filter_map
        )
        for single_data in filter_data:
            field_responder_id = single_data.get("field_responder_id", "")
            extra_field_dict = proxy_dataset_service.get_formatted_fields_by_field_id(field_responder_id)
            for k, v in extra_field_dict.items():
                single_data.update({k: v})

        filter_data_dict[chart_initiator_id] = filter_data

    return filter_data_dict


def _get_release_filter_data(chart_initiator_id, chart_responder_id):
    # 获取新版的单图筛选数据
    sql = '''
    select
        cdl.id,
        cdl.chart_id as chart_initiator_id,
        cdl.dataset_field_id as field_initiator_id,
        cdl.dataset_id as initiator_dataset_id,
        cdl.dashboard_id,
        cdlf.chart_responder_id,
        cdlf.dataset_responder_id as dataset_id,
        cdlf.field_responder_id
    from
        released_dashboard_filter_chart as cdl
        left join released_dashboard_filter_chart_relation as cdlf on cdl.id = cdlf.filter_id
    where
        cdl.chart_id = %(chart_initiator_id)s
        and chart_responder_id = %(chart_responder_id)s
    '''

    params = {"chart_initiator_id": chart_initiator_id, "chart_responder_id": chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


def _get_linkage_selector_dict(model):
    conditions, chart_responder_id, dashboard_id = model.chart_linkage_conditions, model.id, model.dashboard_id
    linkage_data_dict = {}
    if not conditions:
        return linkage_data_dict
    chart_filter_map = {}
    if conditions:
        chart_initiator_ids = [condition['chart_id'] for condition in conditions]
        chart_filter_map = _get_release_snapshot_chart_filter_map(chart_initiator_ids)
    for single_condition in conditions:
        if not single_condition:
            continue
        chart_initiator_id = single_condition["chart_id"]  # 发起者单图id
        if not chart_initiator_id:
            continue
        linkage_data_dict.setdefault(chart_initiator_id, [])
        linkage_data = _get_release_linkage_data_v2(
            chart_initiator_id, chart_responder_id, dashboard_id, chart_filter_map
        )
        for single_data in linkage_data:
            field_responder_id = single_data.get("field_responder_id", "")
            extra_field_dict = proxy_dataset_service.get_formatted_fields_by_field_id(field_responder_id)
            for k, v in extra_field_dict.items():
                single_data.update({k: v})

        linkage_data_dict[chart_initiator_id] = linkage_data

    return linkage_data_dict


def _get_release_linkage_data(chart_initiator_id, chart_responder_id):
    sql = '''
    select
        cdl.id,
        cdl.chart_id as chart_initiator_id,
        cdl.dataset_field_id as field_initiator_id,
        cdl.dataset_id as initiator_dataset_id,
        cdl.dashboard_id,
        cdlf.chart_responder_id,
        cdlf.dataset_responder_id as dataset_id,
        cdlf.field_responder_id
    from
        released_dashboard_linkage as cdl
        left join released_dashboard_linkage_relation as cdlf on cdl.id = cdlf.link_id
    where
        cdl.chart_id = %(chart_initiator_id)s
        and chart_responder_id = %(chart_responder_id)s
    '''

    params = {"chart_initiator_id": chart_initiator_id, "chart_responder_id": chart_responder_id}
    with get_db() as db:
        return db.query(sql, params)


def _get_release_filter_data_v2(chart_initiator_id, chart_responder_id, dashboard_id, chart_filter_map):
    filter_data = []
    chart_filter_data = chart_filter_map.get(chart_initiator_id) or {}
    filter_list = chart_filter_data.get('chart_filter') or []
    for filter_dict in filter_list:
        # 兼容老的发布页筛选数据可能没有'available'字段, 按照available=1处理
        if "available" in filter_dict and not filter_dict.get("available"):
            continue
        relate_data = _get_chart_related_dict(filter_dict['related_list'], chart_responder_id)
        if not relate_data:
            continue
        temp_dict = {}
        temp_dict['id'] = filter_dict['id']
        temp_dict['chart_initiator_id'] = filter_dict['chart_initiator_id']
        temp_dict['field_initiator_id'] = filter_dict['field_initiator_id']
        temp_dict['initiator_dataset_id'] = filter_dict['dataset_id']
        temp_dict['dashboard_id'] = dashboard_id
        temp_dict['chart_responder_id'] = relate_data['chart_responder_id']
        temp_dict['dataset_id'] = relate_data['related_dataset_id']
        temp_dict['field_responder_id'] = relate_data['field_responder_id']
        filter_data.append(temp_dict)
    return filter_data


def _get_release_linkage_data_v2(chart_initiator_id, chart_responder_id, dashboard_id, chart_filter_map):
    linkage_data = []
    chart_filter_data = chart_filter_map.get(chart_initiator_id)
    filter_list = chart_filter_data.get('chart_linkage') or []
    for filter_dict in filter_list:
        relate_data = _get_chart_related_dict(filter_dict['related_list'], chart_responder_id)
        if not relate_data:
            continue
        temp_dict = {}
        temp_dict['id'] = filter_dict['id']
        temp_dict['chart_initiator_id'] = filter_dict['chart_initiator_id']
        temp_dict['field_initiator_id'] = filter_dict['field_initiator_id']
        temp_dict['initiator_dataset_id'] = filter_dict['dataset_id']
        temp_dict['dashboard_id'] = dashboard_id
        temp_dict['chart_responder_id'] = relate_data['chart_responder_id']
        temp_dict['dataset_id'] = relate_data['related_dataset_id']
        temp_dict['field_responder_id'] = relate_data['field_responder_id']
        linkage_data.append(temp_dict)
    return linkage_data


def _get_release_snapshot_chart_filter_map(chart_ids):
    """
        {
            "chart_id": {
                "chart_filter": [],
                "chart_linkage": []
            },
            "chart_id":
        }
    :param chart_ids:
    :return:
    """
    chart_filter_map = {}
    conditions = {'id': chart_ids}
    fields = ["id", "chart_linkage", "chart_filter"]
    data_list = repository.get_list("dashboard_released_snapshot_chart", conditions, fields)
    if data_list:
        json_load = json.loads
        try:
            chart_filter_map = {
                data['id']: {
                    "chart_filter": json_load(data["chart_filter"]),
                    "chart_linkage": json_load(data["chart_linkage"]),
                }
                for data in data_list
            }
        except JSONDecodeError as e:
            err_msg = "获取发布页筛选联动数据出错, 错误信息: {}".format(str(e))
            logging.error(err_msg)
            raise UserError(message=err_msg) from e
    return chart_filter_map


def _get_chart_related_dict(related_list, chart_responder_id):
    for relate_data in related_list:
        if relate_data['chart_responder_id'] == chart_responder_id:
            return relate_data
    return {}


def _dashboard_release_message_push(params, retry=3):
    url = config.get("ThirdParty.message_push_api")
    logger.info("message push url: {url}, params: {params}".format(url=url, params=params))
    if not url:
        raise UserError("消息推送URL不能为空")
    while retry > 0:
        params = json.dumps(params, cls=BaseModelEncoder)
        response = requests.post(url, json=json.loads(params))
        logger.info("message push url response status code: {}".format(response.status_code))
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info("message push url response data: {}".format(data))
            except Exception as e:
                logger.error("message push api decode json err: {}".format(str(e)))
                data = {}
            if data.get('result') and data.get('msg') == 'success':
                return data
        retry -= 1
    raise UserError(
        message='url：{url},error:{error},'
                'status_code:{status_code}'.format(url=url, error=response.reason, status_code=response.status_code)
    )


def _get_jwt_token(project_code: str, expires: int = 60):
    try:
        jwt_secret = config.get("JWT.sso_dashboard_secret")
        jwt_alg = 'HS256'
        exp = int(time.time()) + expires
        payload = {"tenant_code": project_code, "exp": exp}
        jwt_token = jwt.encode(payload, jwt_secret, jwt_alg)
        access_token = base64.b64encode(jwt_token.encode()).decode('utf-8')
    except Exception as e:
        raise UserError(message="获取token失败, 异常信息: %s" % str(e)) from e
    return access_token


def _get_dashboard_relation(dashboard: dict):
    dashboard_id = dashboard.get('id')
    level_code = dashboard.get('level_code')
    if not level_code:
        return []
    top_level = level_code.split('-')[0]
    fields = ['id', 'parent_id']
    conditions = {"level_code like": top_level + '%'}
    dashboard_relations = repository.get_list('dashboard', conditions, fields)
    relation_map = {r['id']: r['parent_id'] for r in dashboard_relations}
    relations = []
    relations = _get_relation_list(relation_map, dashboard_id, relations)
    return relations[::-1]


def get_download_flag(token_data, snapshot_id, kwargs) -> bool:
    # 加入/api/dashboard_chart/check_permission接口校验逻辑
    dashboard_cache = DashboardReleaseCache(g.code, snapshot_id, redis_conn())
    dashboard = dashboard_cache.get_dashboard_data()
    if dashboard is None:
        raise UserError(message='报告不存在')
    if dashboard.get('type_access_released') == DashboardTypeAccessReleased.NoLimited.value:
        return True

    # 目前默认是下载权限
    session_id = token_data.get('session_id')
    portal_id = token_data.get("portal_id", "")
    screen_id = token_data.get("screen_id", "")
    if portal_id and session_id:
        # 门户权限
        flag = application_auth_service.check_portal_permission(session_id, snapshot_id, 'download')
    elif screen_id and session_id:
        # 第三方多屏报告权限
        flag = screen_auth_service.check_third_permission(session_id, snapshot_id, 'download')
    else:
        # 普通权限
        flag = dashboard_service.check_permission(snapshot_id, token_data, kwargs.get("action", "download"))

    return flag


def _get_relation_list(relation_map, dashboard_id, relations):
    relations.append(dashboard_id)
    if not relation_map.get(dashboard_id):
        return relations
    parent_id = relation_map.get(dashboard_id)
    return _get_relation_list(relation_map, parent_id, relations)


def handle_message_push_params(status, dashboard: dict, project_code):
    access_token = _get_jwt_token(project_code)
    is_release = bool(int(status))
    data = dashboard
    relation = _get_dashboard_relation(dashboard)
    params = dict(access_token=access_token, data=data, is_release=is_release, relation=relation)
    return _dashboard_release_message_push(params)


def get_dashboard_ids_by_screen_id(screen_id):
    return repository.get_columns("dashboard_released_snapshot_dashboard", {"snapshot_id": screen_id}, "id") or []


def get_snapshot_metadata_by_id(snap_id, dashboard_id):
    return repository.get_one("snapshot_dashboard_metadata", {"snap_id": snap_id, "dashboard_id": dashboard_id},
                              fields=['metadata']) or {}


DASHBOARD_COMPILE = regex.compile(r'(?<="dashboard_id":\s+|"dashboard_id":)"([^"]+)"')
DATASET_COMPILE = regex.compile(r'(?<="dataset_id":\s+|"dataset_id":)"([^"]+)"')
DATASET_FIELD_COMPILE = regex.compile(r'(?<="dataset_field_id":\s+|"dataset_field_id":)"([^"]+)"')
DASHBOARD_TARGET_COMPILE = regex.compile(r'(?<="target":\s+|"target":)"([^"]+)"')
UUID_COMPILE = regex.compile(r'\w{8}(-\w{4}){3}-\w{12}')


def _get_ids(snapshot_charts):  # NOSONAR
    dataset_ids, dashboard_ids, dataset_field_ids = [], [], []
    for chart in snapshot_charts:
        chart.get("source") and dataset_ids.append(chart.get("source"))
        chart.get("dims") and dataset_ids.extend([i.get("dataset_id") for i in json.loads(chart.get("dims"))])
        chart.get("nums") and dataset_ids.extend([i.get("dataset_id") for i in json.loads(chart.get("nums"))])
        chart.get("filters") and dataset_ids.extend([i.get("dataset_id") for i in json.loads(chart.get("filters"))])
        penetrates = chart.get("penetrates")
        if penetrates:
            for item in json.loads(penetrates):
                dataset_ids.append(item.get("source"))
                dataset_ids.extend([i.get("dataset_id") for i in item.get("nums", [])])
                dataset_ids.extend([i.get("dataset_id") for i in item.get("dims", [])])
                dashboard_ids.append(item.get("dashboard_id"))
        chart_params = chart.get("chart_params")
        if chart_params:
            for item in json.loads(chart_params):
                dashboard_ids.append(item.get("dashboard_id"))
                dataset_field_ids.append(item.get("dataset_field_id"))
        jump = chart.get("jump")
        if jump:
            for item in json.loads(jump):
                dashboard_ids.append(item.get("dashboard_id"))
                dataset_field_ids.append(item.get("dataset_field_id"))
                if item.get("target_type") == "dashboard":
                    dashboard_ids.append(item.get("target"))
                for i in item.get("jump_relation") or []:
                    dashboard_ids.append(i.get("dashboard_id"))
                    dataset_field_ids.append(i.get("dataset_field_id"))
        component_filter = chart.get("component_filter")
        component_filter and dataset_ids.extend([i.get("dataset_id") for i in json.loads(component_filter)])
        chart_params_jump = chart.get("chart_params_jump")
        if chart_params_jump:
            for i in json.loads(chart_params_jump):
                dashboard_ids.append(i.get("dashboard_id"))
                dataset_field_ids.append(i.get("chart_dataset_field_id"))
                dataset_field_ids.append(i.get("param_dataset_field_id"))
        penetrate_relation = chart.get('penetrate_relation')
        penetrate_relation and dataset_field_ids.extend(
            [i.get("parent_chart_field_id") for i in json.loads(penetrate_relation)])
        penetrate_relation and dataset_field_ids.extend(
            [i.get("child_chart_field_id") for i in json.loads(penetrate_relation)])
        penetrate_filter_relation = chart.get('penetrate_filter_relation')
        penetrate_filter_relation and dataset_field_ids.extend(
            [i.get("parent_chart_field_id") for i in json.loads(penetrate_filter_relation)])
        penetrate_filter_relation and dataset_field_ids.extend(
            [i.get("child_chart_field_id") for i in json.loads(penetrate_filter_relation)])
        penetrate_var_filter_relation = chart.get('penetrate_var_filter_relation')
        penetrate_var_filter_relation and dataset_field_ids.extend(
            [i.get("parent_chart_field_id") for i in json.loads(penetrate_var_filter_relation)])
        penetrate_var_filter_relation and dataset_field_ids.extend(
            [i.get("child_chart_field_id") for i in json.loads(penetrate_var_filter_relation)])
        var_relations = chart.get("var_relations")
        if var_relations:
            for item in json.loads(var_relations):
                dataset_ids.append(item.get("dataset_id"))
                dashboard_ids.append(item.get("dashboard_id"))
        chart.get("chart_vars") and dataset_ids.extend(
            [i.get("dataset_id") for i in json.loads(chart.get("chart_vars"))])
    return set(dataset_ids), set(dashboard_ids), set(dataset_field_ids)


def get_all_dashboard_and_dataset_of_need_snapshot(root_dashboard_ids, dataset_ids, dashboard_ids):
    """
    获取所有关联的报告id和数据集id
    :param root_dashboard_ids:
    :param dataset_ids:
    :param dashboard_ids:
    :return:
    """
    if isinstance(root_dashboard_ids, list):
        root_dashboard_ids = set(root_dashboard_ids)
    if isinstance(dataset_ids, list):
        dataset_ids = set(dataset_ids)
    if isinstance(dashboard_ids, list):
        dashboard_ids = set(dashboard_ids)
    _dashboard_ids = set([])
    _dataset_ids = set([])
    _dataset_field_ids = set([])
    for _id in root_dashboard_ids:
        snapshot_charts = repository.get_list('dashboard_released_snapshot_chart', {"dashboard_id": _id})
        sub_dataset_ids, sub_dashboard_ids, sub_dataset_field_ids = _get_ids(snapshot_charts)
        _dataset_ids = _dataset_ids.union(sub_dataset_ids)
        _dashboard_ids = _dashboard_ids.union(sub_dashboard_ids)
        _dataset_field_ids = _dataset_field_ids.union(sub_dataset_field_ids)
    if _dataset_field_ids:
        _dataset_ids = _dataset_ids.union(
            set([item.get("dataset_id") for item in
                 repository.get_list("dataset_field", {"id": list(_dataset_field_ids)}, ['dataset_id']) or []])
        )
    # 去掉已遍历过的id， 避免死循环
    _dashboard_ids = _dashboard_ids - dashboard_ids
    dashboard_ids = dashboard_ids.union(_dashboard_ids).union(root_dashboard_ids)
    dataset_ids = list(dataset_ids.union(_dataset_ids))
    dashboard_ids = list(dashboard_ids)
    # 只获取调度模式的数据集
    for _, _id in enumerate(deepcopy(dataset_ids)):
        if _is_schedule_of_dataset(_id) is False:
            dataset_ids and dataset_ids.remove(_id)
    if not _dashboard_ids:
        return [i for i in dashboard_ids if i], dataset_ids
    else:
        return get_all_dashboard_and_dataset_of_need_snapshot(_dashboard_ids, dataset_ids, dashboard_ids)


def get_all_dashboard_and_dataset_of_need_snapshot_all(root_dashboard_ids, dataset_ids, dashboard_ids):
    """
    返回所有的数据集
    :param root_dashboard_ids:
    :param dataset_ids:
    :param dashboard_ids:
    :return:
    """
    if isinstance(root_dashboard_ids, list):
        root_dashboard_ids = set(root_dashboard_ids)
    if isinstance(dataset_ids, list):
        dataset_ids = set(dataset_ids)
    if isinstance(dashboard_ids, list):
        dashboard_ids = set(dashboard_ids)
    _dashboard_ids = set([])
    _dataset_ids = set([])
    _dataset_field_ids = set([])
    for _id in root_dashboard_ids:
        snapshot_charts = repository.get_list('dashboard_released_snapshot_chart', {"dashboard_id": _id})
        sub_dataset_ids, sub_dashboard_ids, sub_dataset_field_ids = _get_ids(snapshot_charts)
        _dataset_ids = _dataset_ids.union(sub_dataset_ids)
        _dashboard_ids = _dashboard_ids.union(sub_dashboard_ids)
        _dataset_field_ids = _dataset_field_ids.union(sub_dataset_field_ids)
    if _dataset_field_ids:
        _dataset_ids = _dataset_ids.union(
            set([item.get("dataset_id") for item in
                 repository.get_list("dataset_field", {"id": list(_dataset_field_ids)}, ['dataset_id']) or []])
        )
    # 去掉已遍历过的id， 避免死循环
    _dashboard_ids = _dashboard_ids - dashboard_ids
    dashboard_ids = dashboard_ids.union(_dashboard_ids).union(root_dashboard_ids)
    dataset_ids = list(dataset_ids.union(_dataset_ids))
    dashboard_ids = list(dashboard_ids)

    if not _dashboard_ids:
        return [i for i in dashboard_ids if i], dataset_ids
    else:
        return get_all_dashboard_and_dataset_of_need_snapshot_all(_dashboard_ids, dataset_ids, dashboard_ids)


@lru_cache(maxsize=None)
def _is_schedule_of_dataset(dataset_id):
    """
    判断当前数据集是否是调度数据集
    :param dataset_id:
    :return:
    """
    if not dataset_id:
        return False
    connect_type = repository.get_data_scalar("dataset", {"id": dataset_id}, col_name='connect_type')
    return connect_type in ["", None]


def query_project_info():
    """
    获取project信息
    :return:
    """
    return get_project_info(g.code)


@data_permission_edit_filter("dashboard-edit")
def restore_with_process(current_model: ReleaseModel) -> bool:
    """
    报告最近一次发布的数据还原到设计时
    :param ReleaseModel current_model:
    :return:
    """
    return restore_dashboard_released_design_data(current_model)


def edit_runtime_chart_config(chart_id, config, update_runtime):
    chart = repository.get_one('dashboard_chart', fields=['*'], conditions={'id': chart_id}) or {}
    if not chart:
        raise UserError(message='当前组件不存在！')

    if chart.get('chart_code') not in ['rich_text_next']:
        raise UserError(message=f'当前组件[{chart.get("chart_code")}]暂时不支持修改！')

    def _update_chart_config(table_name, commit):
        repository.update_data(
            table_name, data={'config': config}, condition={'id': chart_id}, commit=commit
        )

    def _get_chart_config(table_name):
        return repository.get_data(
            table_name, fields=['config'], conditions={'id': chart_id}
        )

    update_status = False
    dashboard = dashboard_service.get_dashbaord_info(chart.get('dashboard_id', '')) or {}
    extra_data = dict(
        dashboard_name=dashboard.get('name', ''),
        chart_name=chart.get('name', ''),
    )

    with get_db() as conn:
        try:
            # 修改设计时数据
            commit = False
            extra_data['old_design_config'] = _get_chart_config(table_name='dashboard_chart')
            _update_chart_config(table_name='dashboard_chart', commit=commit)
            extra_data['now_design_config'] = config

            # 修改运行时数据
            if update_runtime == '1':
                update_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                repository.update_data('dashboard_released_snapshot_dashboard', {'modified_on': update_time}, {'snapshot_id': dashboard.get('id')})
                extra_data['old_released_config'] = _get_chart_config(table_name='dashboard_released_snapshot_chart')
                _update_chart_config(table_name='dashboard_released_snapshot_chart', commit=commit)
                _delete_metadata_dashboard_cache(dashboard)
                extra_data['now_released_config'] = config

            conn.commit()
            update_status = True
        except Exception as e:
            conn.rollback()
            logger.error(f'更新运行时组件失败！ 原因是：{traceback.format_exc()}')
            raise UserError(message="更新运行时组件失败：{}".format(str(e))) from e

    return extra_data if update_status else dict()


def _test_mysql(kwargs):
    repository.get_data_by_sql('select 2', params={})
    repository.get_data_by_sql('select 3', params={})
    repository.get_data_by_sql('select 4', params={})
    try:
        t = int(kwargs.get('time'))
    except:
        t = 0
    time.sleep(t)
    return {}


def test_mysql(kwargs):
    results = {}
    request_data = getattr(g, 'request_data', {})
    request_params = request_data.get('params', {})
    refer = request_data.get('headers', {}).get('REFERER', '')
    model_id = seq_id()

    if (
            (__ANALYSIS_DOCTOR_KEYWORD__ in request_params)
            or (__ANALYSIS_DOCTOR_KEYWORD__ in refer)
    ):
        request_params[__ANALYSIS_DOCTOR_KEYWORD__] = 1
        AnalysisTimeUtils.enter(chart_id=model_id)
        try:
            chart_result = _test_mysql(kwargs)
            AnalysisTimeUtils.append_analysis_data(result=chart_result)
            results[model_id] = chart_result
        finally:
            AnalysisTimeUtils.exit()
    else:
        results[model_id] = _test_mysql(kwargs)
    return results


def chart_warning(kwargs):
    """
    前端组件渲染错误的日志记录和预警
    :param kwargs:
    :return:
    """
    try:
        # 业务类型获取
        platform = kwargs.get("platform")
        new_layout_type = kwargs.get("new_layout_type")
        if platform == 'pc':
            biz_type = '仪表板' if new_layout_type == 1 else '大屏'
        elif platform in ['new_mobile', 'mobile']:
            biz_type = '移动报表'
        view_from = kwargs.get("from")
        is_key = int(kwargs.get("is_key", 0))
        # 日志记录天眼
        from components.fast_logger import FastLogger
        log_data = {
            "org_code": g.code,
            "module_type": FastLogger.ModuleType.DASHBOARD_MOBILE_SCREEN,
            "biz_type": biz_type,
            "biz_id": kwargs.get("dashboard_id"),
            "biz_name": kwargs.get("dashboard_name"),
            "exec_from": view_from,
            "error_type": FastLogger.ErrorType.CHART_DATA_ERROR,
            "error_lable1": "前端组件渲染异常",
            "error_lable2": kwargs.get("error_type", "js错误"),
            "error_data_id": kwargs.get("chart_id"),
            "error_msg": kwargs.get("error_msg"),
            "error_traceback": kwargs.get("traceback"),
            "extra_info": json.dumps(kwargs.get("extra"), ensure_ascii=False),
            "is_key": is_key,
        }
        FastLogger.BizErrorFastLogger(**log_data).record()
        # 消息预警
        if view_from == 'viewreport' and is_key == 1:
            import app_celery
            app_celery.dashboard_error_alarm.apply_async(kwargs=log_data)
        return True
    except Exception as e:
        err_msg = "前端错误日志记录和预警接口异常，异常信息: " + str(e)
        logger.exception(err_msg)
        raise UserError(message=err_msg)
