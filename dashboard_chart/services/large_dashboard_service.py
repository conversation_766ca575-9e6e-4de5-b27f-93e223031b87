import copy
import json
import traceback
import uuid
from datetime import datetime
from typing import Dict, List
from collections import OrderedDict

from loguru import logger

from base import repository
from base.enums import (
    ApplicationType, FunctionReportType, ApplicationReportType,
    MoveDashboardAsLargeScreenStatus, DistributeType
)
from dashboard_chart.services import dashboard_service
from dmplib.hug import g
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from components.utils import space_saving_json
# from rbac.services.data_permissions import execute_function_with_new_permission
from dashboard_chart.repositories.dashboard_repository import get_move_large_screen_task_data, \
    get_move_large_screen_count, has_running_task
from dmplib import config
from components.data_compress import DataCompress

OLD_BIG_SCREEN_FILTER = lambda x: (
        x.get('platform') == 'pc'
        and x.get('new_layout_type') == 0
        and x.get('type') == 'FILE'
        and x.get('application_type') == ApplicationType.Dashboard.value
)

DEFAULT_FOLDER_NAME = '大屏系统分发报告文件夹'
DEFAULT_FOLDER_ID = '00000000-1111-0000-2222-02420a0c0002'


def compress_log_data(data):
    # 日志记录压缩，报表过多，记录的日志太大，日志只用于后期问题排查分析使用
    compress_data = DataCompress.compress(space_saving_json(data))
    return compress_data


# class MoveOldDashboardAsLargeScreen:
#     def __init__(self, dashboard_id: str, target_folder_id: str, request=None):
#         self.request = request
#         self.dashboard_id = dashboard_id
#         self.target_folder_id = target_folder_id.strip()
#         self.dashboard: Dict[str, Dict] = {}
#         self.target_dashboard: Dict[str, Dict] = {}  # 目标文夹信息
#         self.old_dashboards: Dict[str, Dict] = {}  # 没有移动的时候所有父子报告的关键信息
#         self.op_records: Dict[str, Dict[str, List]] = {}  # 操作的表记录
# 
#     def _append_op_records(self, operate, table, old_data, new_data):
#         """
#         手动记录所有的数据库操作改动
#         """
#         if operate not in self.op_records:
#             self.op_records[operate] = {}
#         if table not in self.op_records[operate]:
#             self.op_records[operate][table] = []
#         data = {'old': old_data, 'new': new_data}
#         self.op_records[operate][table].append(data)
# 
#     def __get_dashboard_data_by_level_code(self, level_code):
#         fields = ['id', 'level_code', 'application_type']
#         level_code_data = repository.get_list(
#             'dashboard', fields=fields, conditions={'level_code like': f'{level_code}%'}
#         ) or []
#         return {data.get('id', ''): data for data in level_code_data}
# 
#     def _dashboard_type_check(self):
#         """
#         1. 报表类型检测，检查是不是旧的大屏报表
#         2. 设置一些对象基础数据
#         """
#         # 1. 起始报告检测
#         dashboard = repository.get_one('dashboard', conditions={'id': self.dashboard_id}) or {}
#         if not dashboard:
#             raise UserError(message='报告不存在！')
#         platform = dashboard.get('platform', '')
#         new_layout_type = dashboard.get('new_layout_type', '')
#         type = dashboard.get('type', '')
#         application_type = dashboard.get('application_type', '')
#         if application_type == ApplicationType.LargeScreen.value:
#             raise UserError(message='报告已经是酷炫大屏了，不能重新迁移到到酷炫大屏！')
#         if platform != 'pc' or new_layout_type != 0 or type != 'FILE':
#             raise UserError(message=f'报告不是大屏！platform：{platform}，new_layout_type：{new_layout_type}, type: {type}')
#         self.dashboard = dashboard
# 
#         # 2. 获取所有的报告的关键信息
#         level_code = self.dashboard.get('level_code', '')
#         if not level_code:
#             raise UserError(message='父报告的level_code异常为空！')
#         self.old_dashboards = self.__get_dashboard_data_by_level_code(level_code)
# 
#         # 3. 目标文件夹检测
#         if self.target_folder_id in ['root', '']:
#             return
#         target_dashboard = repository.get_one('dashboard', conditions={'id': self.target_folder_id}) or {}
#         if not target_dashboard:
#             raise UserError(message='目标文件夹不存在！')
#         application_type = target_dashboard.get('application_type')
#         if application_type != ApplicationType.LargeScreen.value:
#             raise UserError(message=f'移动的文件夹不是大屏文件夹！application_type: {application_type}')
#         self.target_dashboard = target_dashboard
# 
#     def _change_dashboard_type(self):
#         """
#         2. 将旧的大屏报表标识改成新的标识
#         """
#         data = {'application_type': ApplicationType.LargeScreen.value}
#         tables = [
#             'dashboard',  # 1. 设计时表
#             'dashboard_released_snapshot_dashboard',  # 运行时表
#             'snapshot_dashboard',  # 拍照表
#             'snapshot_dashboard_released_snapshot_dashboard',  # 拍照表
#         ]
#         # TODO 报告还原的类型处理需要再导入的时候进行处理
#         self.log(f'开始处理报告的标识，报告数：{len(list(self.old_dashboards.keys()))}')
#         for table in tables:
#             for dashboard_id, dashboard_data in self.old_dashboards.items():
#                 old_data = {'application_type': dashboard_data.get('application_type')}
#                 condition = {'id': dashboard_id}
#                 repository.update_data(table, condition=condition, data=data.copy(), commit=False)
#                 # 记录报表类型数据的变化
#                 self._append_op_records(
#                     'update', table,
#                     old_data=dict(condition, **old_data), new_data=dict(condition, **data)
#                 )
# 
#     @execute_function_with_new_permission(dashboard_service.move_dashboard, 'large_screen-edit')
#     def _move_old_dashboard_to_new_folder(self):
#         """
#         # 3. 移动旧的大屏到新的目录文件，重算层级
#         """
#         # TODO 新酷炫大屏的权限操作
#         self.log(f'开始移动<{self.dashboard_id}>到酷炫大屏目录：{self.target_folder_id}')
#         dashboard_service.move_dashboard(self.dashboard_id, self.target_folder_id, use_pymysql=False)
#         # 再查出新的层级数据
#         now_dashboard = repository.get_one('dashboard', conditions={'id': self.dashboard_id}) or {}
#         now_level_code_data = self.__get_dashboard_data_by_level_code(now_dashboard.get('level_code', ''))
# 
#         # 记录层级的变化
#         for dashboard_id, now_dashboard_data in now_level_code_data.items():
#             old_level_code = self.old_dashboards.get(dashboard_id, {}).get('level_code')
#             now_level_code = now_dashboard_data.get('level_code')
#             old_data = {'id': dashboard_id, 'level_code': old_level_code}
#             new_data = {'id': dashboard_id, 'level_code': now_level_code}
#             self._append_op_records('update', 'dashboard', old_data=old_data, new_data=new_data)
# 
#     def _change_dashboard_authority(self):
#         """
#         4. 改变报表授权，迁移到酷炫大屏的授权
#         """
#         dashboard_auth_table = 'user_role_data_permission'
#         # 该表的这4个字段是联合主键
#         fields = ['role_id', 'data_id', 'data_type', 'data_action_code']
#         auths = repository.get_list(
#             dashboard_auth_table,
#             conditions={'data_id': self.dashboard_id}, fields=fields
#         ) or []
#         self.log(f'开始处理报告的权限数据，找到授权数据条数：{len(auths)}')
#         for auth in auths:
#             old_data = auth
#             condition = auth
#             data = {'data_type': 'large_screen'}
#             repository.update_data(dashboard_auth_table, condition=condition.copy(), data=data.copy(), commit=False)
#             # 记录菜单类型数据的变化
#             self._append_op_records(
#                 'update', dashboard_auth_table,
#                 old_data=old_data.copy(), new_data=dict(condition, **data)
#             )
# 
#     def _change_application_functions_dashboard_link(self):
#         """
#         5. 菜单挂接数据修改，从仪表板挂接换到酷炫大屏挂接
#         """
#         functions = repository.get_list('function', conditions={'url': self.dashboard_id}) or []
#         self.log(f'开始处理报告的菜单挂接数据，找到数据条数：{len(functions)}')
#         for function in functions:
#             old_data = {'report_type': function.get('report_type')}
#             data = {'report_type': FunctionReportType.LargeScreen.value}
#             condition = {'id': function.get('id')}
#             repository.update_data('function', condition=condition, data=data.copy(), commit=False)
#             # 记录菜单类型数据的变化
#             self._append_op_records(
#                 'update', 'function',
#                 old_data=dict(condition, **old_data), new_data=dict(condition, **data)
#             )
# 
#     def move(self):
#         success = False
#         # 1. 报表类型检查
#         self._dashboard_type_check()
#         try:
#             # 2. 报表修改报表标识
#             self._change_dashboard_type()
#             # 3. 移动大屏到新目录，重算层级
#             self._move_old_dashboard_to_new_folder()
#             # 4. 权限数据修改
#             self._change_dashboard_authority()
#             # 5. 菜单挂接数据修改
#             self._change_application_functions_dashboard_link()
#             success = True
#         finally:
#             request = self.mock_request() if self.request is None else self.request
#             UserLogModel.log_setting(
#                 request=request,
#                 log_data={
#                     'action': 'move_dashboard',
#                     'id': self.dashboard_id,
#                     'content': '[ {dash_name} ] 移动到酷炫大屏 [ {target_name} ] {status}'.format(
#                         dash_name=self.dashboard.get('name', ''),
#                         status='成功' if success else '失败',
#                         target_name=(
#                             '根目录'
#                             if self.target_folder_id in ['root', ''] else
#                             self.target_dashboard.get('name', '')
#                         ),
#                     ),
#                     'extra': compress_log_data({
#                         'dashboard_id': self.dashboard_id,
#                         'target_folder_id': self.target_folder_id,
#                         'op_records': self.op_records,
#                     })
#                 },
#             )
# 
#     def mock_request(self):
#         from hug import Request
#         import sys
#         env = {
#             'SERVER_PROTOCOL': 'protocol',
#             'SERVER_SOFTWARE': 'gunicorn/0.17.0',
#             'SCRIPT_NAME': '',
#             'REQUEST_METHOD': 'method',
#             'PATH_INFO': '',
#             'QUERY_STRING': '',
#             'HTTP_USER_AGENT': 'curl/7.24.0 (x86_64-apple-darwin12.0)',
#             'REMOTE_PORT': '65133',
#             'RAW_URI': '/',
#             'REMOTE_ADDR': '127.0.0.1',
#             'SERVER_NAME': '',
#             'SERVER_PORT': '',
#             'kwargs': {},
#             'wsgi.version': (1, 0),
#             'wsgi.url_scheme': 'scheme',
#             'wsgi.input': 'body',
#             'wsgi.errors': sys.stderr,
#             'wsgi.multithread': False,
#             'wsgi.multiprocess': True,
#             'wsgi.run_once': False
#         }
#         return Request(env)

class LogUtil:
    LOG_DATA = {}

    @staticmethod
    def log(id, msg):
        if msg:
            logger.opt(depth=1).error(msg)
        LogUtil.LOG_DATA[id] = LogUtil.LOG_DATA.get(id, '') + '\n' + msg

    @staticmethod
    def get_log(id):
        return LogUtil.LOG_DATA.get(id, '')

    @staticmethod
    def remove(id):
        return LogUtil.LOG_DATA.pop(id, None)


class MoveOldDashboardAsLargeScreen:
    def __init__(self, dashboard_id: str, target_folders: List, task_id, request=None):
        self.request = request
        self.dashboard_id = dashboard_id
        self.target_folders = target_folders
        self.target_folder_id = ''
        self.task_id = task_id
        self.dashboard: Dict[str, Dict] = {}
        self.target_folder: Dict[str, Dict] = {}  # 目标文夹信息
        self.old_dashboards: Dict[str, Dict] = {}  # 没有移动的时候所有父子报告的关键信息
        self.op_records: Dict[str, Dict[str, List]] = {}  # 操作的表记录

    def log(self, msg):
        LogUtil.log(self.task_id, msg)

    def _append_op_records(self, operate, table, old_data, new_data):
        """
        手动记录所有的数据库操作改动
        """
        if operate not in self.op_records:
            self.op_records[operate] = {}
        if table not in self.op_records[operate]:
            self.op_records[operate][table] = []
        data = {'old': old_data, 'new': new_data}
        self.op_records[operate][table].append(data)

    def __get_dashboard_data_by_level_code(self, level_code):
        fields = ['id', 'level_code', 'application_type']
        level_code_data = repository.get_list(
            'dashboard', fields=fields, conditions={'level_code like': f'{level_code}%'}
        ) or []
        return {data.get('id', ''): data for data in level_code_data}

    def _dashboard_type_check(self):
        """
        1. 报表类型检测，检查是不是旧的大屏报表
        2. 设置一些对象基础数据
        """
        # 1. 起始报告检测
        dashboard = repository.get_one('dashboard', conditions={'id': self.dashboard_id}) or {}
        if not dashboard:
            raise UserError(message='报告不存在！')
        platform = dashboard.get('platform', '')
        new_layout_type = dashboard.get('new_layout_type', '')
        type = dashboard.get('type', '')
        application_type = dashboard.get('application_type', '')
        if application_type == ApplicationType.LargeScreen.value:
            raise UserError(message='报告已经是酷炫大屏了，不能重新迁移到到酷炫大屏！')
        if platform != 'pc' or new_layout_type != 0 or type != 'FILE':
            raise UserError(message=f'报告不是大屏！platform：{platform}，new_layout_type：{new_layout_type}, type: {type}')
        self.dashboard = dashboard

        # 2. 获取所有的报告的关键信息
        level_code = self.dashboard.get('level_code', '')
        if not level_code:
            raise UserError(message='父报告的level_code异常为空！')
        self.old_dashboards = self.__get_dashboard_data_by_level_code(level_code)

    def __get_screen_distribute_folder(self):
        """
        返回创建一个酷炫大屏的系统分发文件夹
        """
        distribute_folder = repository.get_one('dashboard', conditions={'id': DEFAULT_FOLDER_ID}, fields=['id'])
        if distribute_folder:
            return distribute_folder
        else:
            # 创建一个大屏系统分发文件夹
            biz_code = str(uuid.uuid4()).replace('-', '')
            distribute_folder = {
                'id': DEFAULT_FOLDER_ID,
                'name': DEFAULT_FOLDER_NAME,
                'parent_id': '',
                'type': 'FOLDER',
                'theme': 'tech_blue',
                'application_type': ApplicationType.LargeScreen.value,
                'level_code': dashboard_service.generate_level_code(parent_id=''),
                'biz_code': biz_code
            }
            distribute_folder['user_group_id'] = '00000000-0000-0000-1111-000000000000'
            distribute_folder['distribute_type'] = DistributeType.Distribute.value
            repository.add_data('dashboard', data=distribute_folder.copy(), commit=False)
            self._append_op_records('insert', 'dashboard', old_data={}, new_data=distribute_folder.copy())
            self.log(f'创建了系统分发文件夹：<{DEFAULT_FOLDER_NAME}>, id: {distribute_folder.get("id", "")}')
            return distribute_folder

    def _recursive_create_screen_folders(self):
        """
        2. 递归创建酷迁移的炫大屏的文件夹
        """
        folders = self.target_folders
        if not folders:
            # 就在根目录的报告
            target_folder_id = 'root'
            target_folder = {}
        else:
            # 先获取酷炫大屏的目录树形
            target_folder = {}
            curr_parent_folder = {}
            for folder in folders:
                level_code = folder.get('level_code') or ''
                name = folder.get('name') or ''
                if not curr_parent_folder:
                    parent_id = ''
                else:
                    parent_id = curr_parent_folder.get('id', '')
                # 寻找相同层级下的是否已经有了文件夹，有就直接使用
                condition = {
                    "length(level_code) - length(REPLACE(level_code, '-', '')) =": level_code.count('-'),
                    "application_type": ApplicationType.LargeScreen.value,
                    "parent_id": parent_id,
                    "name": name,
                    "type": 'FOLDER'
                }
                if name == '系统分发报告文件夹':
                    existed_folder = self.__get_screen_distribute_folder()
                else:
                    existed_folder = repository.get_one('dashboard', conditions=condition, fields=None) or {}
                if existed_folder:
                    # 使用存在的文件夹
                    target_folder = existed_folder.copy()
                    self.log(f'使用已经存在的文件夹：<{name}>, id: {target_folder.get("id", "")}')
                else:
                    # 新建一个文件夹
                    target_folder = folder.copy()
                    target_folder['id'] = seq_id()
                    target_folder['application_type'] = ApplicationType.LargeScreen.value
                    target_folder.pop('created_by', None)
                    target_folder.pop('created_on', None)
                    target_folder.pop('modified_on', None)
                    target_folder.pop('modified_by', None)
                    target_folder.pop('sub', None)
                    # 生成level_code
                    target_folder['level_code'] = dashboard_service.generate_level_code(parent_id=parent_id)
                    target_folder['parent_id'] = parent_id
                    repository.add_data('dashboard', data=target_folder.copy(), commit=False)
                    self._append_op_records('insert', 'dashboard', old_data={}, new_data=target_folder.copy())
                    self.log(f'创建新的文件夹：<{name}>, id: {target_folder.get("id", "")}')
                curr_parent_folder = target_folder.copy()
            target_folder_id = target_folder.get('id', '')
        self.target_folder = target_folder
        self.target_folder_id = target_folder_id

    def _change_dashboard_type(self):
        """
        3. 将旧的大屏报表标识改成新的标识
        """
        data = {'application_type': ApplicationType.LargeScreen.value}
        tables = [
            'dashboard',  # 1. 设计时表
            'dashboard_released_snapshot_dashboard',  # 运行时表
            'snapshot_dashboard',  # 拍照表
            'snapshot_dashboard_released_snapshot_dashboard',  # 拍照表
        ]
        self.log(f'开始处理报告<{self.dashboard_id}>的类型标识，父子报告总数：{len(list(self.old_dashboards.keys()))}')
        for table in tables:
            for dashboard_id, dashboard_data in self.old_dashboards.items():
                old_data = {'application_type': dashboard_data.get('application_type')}
                condition = {'id': dashboard_id}
                repository.update_data(table, condition=condition, data=data.copy(), commit=False)
                # 记录报表类型数据的变化
                self._append_op_records(
                    'update', table,
                    old_data=dict(condition, **old_data), new_data=dict(condition, **data)
                )

    def _move_old_dashboard_to_new_folder(self):
        """
        # 4. 移动旧的大屏到新的目录文件，重算层级
        """
        self.log(f'开始移动<{self.dashboard_id}>到酷炫大屏目录：{self.target_folder_id}')
        dashboard_service.adapt_function_permission(dashboard_service.move_dashboard, ApplicationType.LargeScreen.value,
                                                    'edit')
        # dashboard_service.move_dashboard(self.dashboard_id, self.target_folder_id, use_pymysql=False)
        dashboard_service.move_dashboard(self.dashboard_id, self.target_folder_id, use_pymysql=False, is_open_api=True)
        # 再查出新的层级数据
        now_dashboard = repository.get_one('dashboard', conditions={'id': self.dashboard_id}) or {}
        now_level_code_data = self.__get_dashboard_data_by_level_code(now_dashboard.get('level_code', ''))

        # 记录层级的变化
        for dashboard_id, now_dashboard_data in now_level_code_data.items():
            old_level_code = self.old_dashboards.get(dashboard_id, {}).get('level_code')
            now_level_code = now_dashboard_data.get('level_code')
            old_data = {'id': dashboard_id, 'level_code': old_level_code}
            new_data = {'id': dashboard_id, 'level_code': now_level_code}
            self._append_op_records('update', 'dashboard', old_data=old_data, new_data=new_data)

    def _change_dashboard_authority(self):
        """
       5. 改变报表授权，迁移到酷炫大屏的授权
        """
        dashboard_auth_table = 'user_role_data_permission'
        # 该表的这4个字段是联合主键
        fields = ['role_id', 'data_id', 'data_type', 'data_action_code']
        auths = repository.get_list(
            dashboard_auth_table,
            conditions={'data_id': self.dashboard_id}, fields=fields
        ) or []
        self.log(f'开始处理报告<{self.dashboard_id}>的权限数据，找到授权数据条数：{len(auths)}')
        for auth in auths:
            old_data = auth
            condition = auth
            data = {'data_type': 'large_screen'}
            repository.update_data(dashboard_auth_table, condition=condition.copy(), data=data.copy(), commit=False)
            # 记录菜单类型数据的变化
            self._append_op_records(
                'update', dashboard_auth_table,
                old_data=old_data.copy(), new_data=dict(condition, **data)
            )

    def _change_application_functions_dashboard_link(self):
        """
        6. 菜单、单门户挂接数据修改，从仪表板挂接换到酷炫大屏挂接
        """
        table_list = [
            {'table': 'application', 'type': ApplicationReportType.LargeScreen.value},
            {'table': 'release_application', 'type': ApplicationReportType.LargeScreen.value},
            {'table': 'function', 'type': FunctionReportType.LargeScreen.value},
            {'table': 'release_function', 'type': FunctionReportType.LargeScreen.value},
        ]
        for data in table_list:
            table = data['table']
            new_report_type = data['type']
            self.__change_link(table, new_report_type)

    def __change_link(self, table, new_report_type):
        """
        菜单挂接数据修改，从仪表板挂接换到酷炫大屏挂接
        """
        functions = repository.get_list(table, conditions={'url': self.dashboard_id}) or []
        self.log(f'开始处理报告<{self.dashboard_id}><{table}>的菜单挂接数据，找到数据条数：{len(functions)}')
        for function in functions:
            old_data = {'report_type': function.get('report_type')}
            data = {'report_type': new_report_type}
            condition = {'id': function.get('id')}
            repository.update_data(table, condition=condition, data=data.copy(), commit=False)
            # 记录菜单类型数据的变化
            self._append_op_records(
                'update', table,
                old_data=dict(condition, **old_data), new_data=dict(condition, **data)
            )

    def move(self):
        from user_log.models import UserLogModel

        success = False
        error = ''
        # 1. 报表类型检查
        self._dashboard_type_check()
        with get_db() as db:
            db.begin_transaction()
            try:
                # 2. 递归创建酷炫大屏的文件夹
                self._recursive_create_screen_folders()
                # 3. 报表修改报表标识
                self._change_dashboard_type()
                # 4. 移动大屏到新目录，重算层级
                self._move_old_dashboard_to_new_folder()
                # 5. 权限数据修改
                self._change_dashboard_authority()
                # 6. 菜单挂接数据修改
                self._change_application_functions_dashboard_link()
                success = True
                db.commit()
            except Exception as e:
                # self.log(f'移动大屏<{self.dashboard.get("name", "")}>失败：{traceback.format_exc()}')
                db.rollback()
                error = traceback.format_exc()
                raise e
            finally:
                request = self.mock_request() if self.request is None else self.request
                UserLogModel.log_setting(
                    request=request,
                    log_data={
                        'application_type': ApplicationType.LargeScreen.value,
                        'action': 'move_dashboard',
                        'id': self.dashboard_id,
                        'content': '[ {dash_name} ] 迁移到酷炫大屏 [ {target_name} ] {status}'.format(
                            dash_name=self.dashboard.get('name', ''),
                            status='成功' if success else '失败',
                            target_name=(
                                '根目录'
                                if self.target_folder_id in ['root', ''] else
                                self.target_folder.get('name', '')
                            ),
                        ),
                        'extra': compress_log_data({
                            'task_id': self.task_id,
                            'dashboard_id': self.dashboard_id,
                            'target_folder_id': self.target_folder_id,
                            'op_records': self.op_records,
                            'traceback': error,
                        })
                    },
                )

    def mock_request(self):
        from hug import Request
        import sys
        env = {
            'SERVER_PROTOCOL': 'protocol',
            'SERVER_SOFTWARE': 'gunicorn/0.17.0',
            'SCRIPT_NAME': '',
            'REQUEST_METHOD': 'method',
            'PATH_INFO': '',
            'QUERY_STRING': '',
            'HTTP_USER_AGENT': 'celery worker',
            'REMOTE_PORT': '65133',
            'RAW_URI': '/',
            'REMOTE_ADDR': '127.0.0.1',
            'SERVER_NAME': '',
            'SERVER_PORT': '',
            'kwargs': {},
            'wsgi.version': (1, 0),
            'wsgi.url_scheme': 'celery',
            'wsgi.input': 'body',
            'wsgi.errors': sys.stderr,
            'wsgi.multithread': False,
            'wsgi.multiprocess': True,
            'wsgi.run_once': False
        }
        return Request(env)


class FolderMoveOldScreenUtil:
    def __init__(self, task_id=''):
        self.dashboards: List = []
        self.task_id = task_id or seq_id()
        self.dashboards_map: Dict[str, Dict] = {}
        self.old_folder_map: Dict[str, List] = {}

    def log(self, msg):
        LogUtil.log(self.task_id, msg)

    def _collect_all_dashboards(self):
        """1. 获取当前仪表版的树形，以及检查其中的大屏情况"""
        dashboards, dashboards_map, old_folder_map = DashboardUtil().collect_all_dashboards_data()
        if not list(filter(OLD_BIG_SCREEN_FILTER, dashboards)):
            message = f'租户<{g.code}>没有需要迁移的大屏！'
            self.log(message)
            return message
        self.dashboards = dashboards
        self.dashboards_map = dashboards_map
        # self.old_folder_map = old_folder_map
        self.old_folder_map = self.__simple_folder_data(old_folder_map)

    def _init_task_status(self, tasks_data, total):
        """
        初始化任务状态
        """
        self.init_task_status(self.task_id, tasks_data, total)

    @staticmethod
    def init_task_status(task_id, tasks_data, total):
        """
        初始化任务状态
        """
        task = repository.get_one('move_dashboard_task', conditions={'id': task_id}) or {}
        if not task:
            data = {
                'id': task_id,
                'task_data': compress_log_data(tasks_data),
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': MoveDashboardAsLargeScreenStatus.Init.value,
                'total_count': total,
            }
            repository.add_data('move_dashboard_task', data=data)
        else:
            data = {
                'task_data': compress_log_data(tasks_data),
                'total_count': total,
            }
            repository.update_data('move_dashboard_task', data=data, condition={'id': task_id})

    def _update_task_status(self, data):
        """
        更新任务状态
        """
        repository.update_data('move_dashboard_task', data=data, condition={'id': self.task_id})

    def __simple_folder_data(self, data):
        """简化迁移任务信息，便于存储， 并且按照创建的level—code排序"""
        new_data = copy.deepcopy(data)
        for folders in new_data.values():
            for folder in folders:
                folder.pop('sub', None)
        # 复刻仪表版的文件夹排序
        sorted_data = OrderedDict()
        sorted_tmp = sorted(new_data.items(), key=lambda x: ''.join([i.get('level_code') or '' for i in x[1]]))
        for key, val in sorted_tmp:
            sorted_data[key] = val
        return sorted_data

    def move_all_screens(self):
        # 1. 获取所有仪表板的报告，检查其中的大屏报告
        self._collect_all_dashboards()
        total = len(self.old_folder_map)
        self._init_task_status(tasks_data=self.old_folder_map, total=total)
        idx = 1
        error_count = 0
        for need_move_dash_id, folders in self.old_folder_map.items():
            # folders: 大屏上面所有的父级信息list
            dashboard_name = self.dashboards_map.get(need_move_dash_id, {}).get('name', '')
            try:
                progress_str = f'租户<{g.code}>开始处理{idx}/{total}, id: {need_move_dash_id}, name: {dashboard_name}, 已失败: {error_count}'
                self.log(progress_str)
                self._update_task_status({
                    'progress_str': progress_str,
                    'status': MoveDashboardAsLargeScreenStatus.Progressing.value
                })
                mover = MoveOldDashboardAsLargeScreen(
                    dashboard_id=need_move_dash_id, target_folders=folders, task_id=self.task_id
                )
                mover.move()
                self._update_task_status({'succ_count': idx - error_count, 'failed_count': error_count})
            except:
                curr_error = f'移动到酷炫大屏<{need_move_dash_id}><{dashboard_name}>失败， 原因：{traceback.format_exc()}'
                self.log(curr_error)
                error_count += 1
            finally:
                idx += 1
                self.log('')  # 日志记录分隔
        self.record_move_progress_log(error_count, total)

    def record_move_progress_log(self, error_count, total):
        """
        将整个过程日志记录到表中
        """
        try:
            # 更新任务状态
            if error_count <= 0:
                # 全部成功
                progress_str = f'用户<{g.account}>完成处理{total}/{total}, 全部成功'
                self._update_task_status({
                    'progress_str': progress_str,
                    'status': MoveDashboardAsLargeScreenStatus.Success.value,
                    'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'failed_count': error_count,
                    'succ_count': total - error_count,
                    'messages': LogUtil.get_log(self.task_id)
                })
            else:
                # 部份失败
                progress_str = f'用户<{g.account}>完成处理{total}/{total}, 其中{total - error_count}个成功，{error_count}个失败'
                self._update_task_status({
                    'progress_str': progress_str,
                    'status': MoveDashboardAsLargeScreenStatus.Failed.value,
                    'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'succ_count': total - error_count,
                    'failed_count': error_count,
                    'messages': LogUtil.get_log(self.task_id)
                })
        finally:
            LogUtil.remove(self.task_id)


class DashboardUtil:

    def get_screen_dashboards_data(self):
        """获取当前酷炫的树形"""
        kwargs = {'application_type': ApplicationType.LargeScreen.value}
        dashboard_tree = dashboard_service.get_dashboard_list_not_permission(**kwargs) or {}
        dashboard_tree = dashboard_tree.get('tree') or []
        return dashboard_tree

    def collect_all_dashboards_data(self):
        """获取当前仪表版的树形，以及检查其中的大屏情况"""
        kwargs = {'application_type': ApplicationType.Dashboard.value}
        dashboard_tree = dashboard_service.get_dashboard_list_not_permission(**kwargs) or {}
        dashboard_tree = dashboard_tree.get('tree') or []
        # 1. 平铺树形
        dashboards = self._tile_tree_shape(dashboard_tree)
        dashboards_map = {d.get('id'): d for d in dashboards}
        # 2. 收集旧的大屏信息
        old_folder_map = self._extra_old_big_screen_data(dashboards, dashboards_map)

        return dashboards, dashboards_map, old_folder_map

    def _tile_tree_shape(self, dashboard_tree: List[Dict]) -> List[Dict]:
        """平铺树形"""
        result: List = []
        self.__tile(dashboard_tree, result)
        return copy.deepcopy(result)

    def __tile(self, dashboard_tree: List[Dict], result: List):
        for dashboard in dashboard_tree:
            subs = dashboard.get('sub') or []
            result.append(dashboard)
            if not subs:
                continue
            else:
                self.__tile(subs, result)

    def _extra_old_big_screen_data(self, dashboards, dashboards_map) -> Dict[str, List]:
        """
        1.1 提取就大屏的数据
        """
        # platform == 'pc' or new_layout_type == 0
        old_big_screens = list(filter(OLD_BIG_SCREEN_FILTER, dashboards))
        c_old_big_screens, c_dashboards_map = copy.deepcopy(old_big_screens), copy.deepcopy(dashboards_map)
        return {s.get('id'): self._get_all_parent_folder(s, c_dashboards_map) for s in c_old_big_screens}

    def _get_all_parent_folder(self, dashboard, dashboards_map) -> List[Dict]:
        """
        根据报告获取所有的父级目录文件夹
        """
        # 不根据parent——id来找父级。根据level_code
        level_code_map = {d.get('level_code'): d for d in dashboards_map.values()}
        result = []
        # self.__get_parent_folder(dashboard, dashboards_map, result)
        # result.reverse()
        self.__get_parent_folder_by_level_code(dashboard, level_code_map, result)
        return result

    def _get_all_parent_level_code(self, level_code):
        # 获取所有的父级level_code
        p_codes = level_code.strip('-').split('-')
        p_level_codes = [p_codes[0:i + 1] for i in range(len(p_codes))][:-1]  # 不算自己
        p_level_codes = ['-'.join(i) + '-' for i in p_level_codes]
        return p_level_codes

    def __get_parent_folder_by_level_code(self, dashboard, level_code_map, result):
        level_code = dashboard.get('level_code') or ''
        p_level_codes = self._get_all_parent_level_code(level_code)
        for level in p_level_codes:
            parent_dashboard = level_code_map.get(level)
            if parent_dashboard and parent_dashboard.get('type') == 'FOLDER':
                result.append(parent_dashboard)

    def __get_parent_folder(self, dashboard, dashboards_map, result):
        """递归往上一直获取父级目录，直到根目录"""
        parent_id = dashboard.get('parent_id')
        parent_dashboard = dashboards_map.get(parent_id)
        if parent_dashboard and parent_dashboard.get('type') == 'FOLDER':
            result.append(parent_dashboard)
            self.__get_parent_folder(parent_dashboard, dashboards_map, result)

    @staticmethod
    def is_developer():
        from dmplib.saas.project import get_project_info
        project_info = get_project_info(g.code)
        is_rdc_auth = project_info.get('is_rdc_auth')
        if is_rdc_auth and getattr(g, 'is_developer', 0) == 1:
            return True
        else:
            return False

    @staticmethod
    def get_can_move_count():
        msg = {'reason': '', 'move_count': 0, 'can_move': True}
        if str(config.get('Function.enable_large_screen_move')) not in ['1', 'true']:
            msg['reason'] = '环境没有开启酷炫大屏配置，检查Function.enable_large_screen_move'
            msg['can_move'] = False
            return msg

        is_admin = dashboard_service._is_current_user_is_admin()
        if not any([is_admin, DashboardUtil.is_developer()]):
            msg['reason'] = '不是管理员或者开发者'
            msg['can_move'] = False
            return msg

        move_count = get_move_large_screen_count()
        msg['move_count'] = move_count
        if move_count <= 0:
            msg['reason'] = '没有要迁移的报告'
            msg['can_move'] = False
            return msg

        # running = repository.get_data('move_dashboard_task',
        #                               conditions={'status': MoveDashboardAsLargeScreenStatus.Progressing.value},
        #                               fields=['id']) or []
        # if running:
        #     msg['reason'] = '有正在迁移中的任务'
        #     msg['can_move'] = False
        #     return msg

        return msg


def get_move_task_data(task_id):
    """
    获取迁移大屏的任务状态信息
    """
    if not task_id:
        return {}
    return get_move_large_screen_task_data(task_id=task_id)


def check_has_running_task():
    tasks = has_running_task()
    if tasks:
        return tasks[0]
    else:
        return {}
