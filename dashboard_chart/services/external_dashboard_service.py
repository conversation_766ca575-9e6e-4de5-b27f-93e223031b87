#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/12/26

"""
services
ps：此文件存放为数据集调用的对外方法或服务
"""

# ---------------- 标准模块 ----------------
import json
import logging
import time
from collections import defaultdict

# ---------------- 业务模块 ----------------
from base import repository
from base.enums import ProjectValueAddedFunc
from dmplib.utils.strings import seq_id
from dmplib.redis import conn as conn_redis
from components.qywx_robot import QyRobotApi
from dataset.repositories import dataset_repository
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.repositories import external_dashboard_repository, chart_repository, dashboard_repository

logger = logging.getLogger(__name__)


def get_related_report_by_dataset_id(dataset_id):
    """
    根据数据集id获取关联的复杂报表
    """
    report_list = external_dashboard_repository.get_related_report_by_dataset_id(dataset_id)
    for item in report_list:
        item['type'] = ProjectValueAddedFunc.REPORT_CENTER.value
    return report_list


def get_related_dashboard_by_dataset_id(dataset_id):
    """
    校验报告单图是否有关联数据集id
    :param dataset_id:
    :return: dashboard_list [{
                        "dashboard_chart_id": "016d5b7d-0a7b-11e9-860c-b93151187785",
                        "dashboard_level_code": "0642-0020-0030-0020-",
                        "dashboard_name": "自由布局10086",
                        "dashboard_type": "FILE",
                        "parent_id": "39e9b72b-3f72-3c9f-f561-a252cecc1281",
                        "folder": "zq-test/caocj测试/单图取数bug修复",
                        "dashboard_id": "39ead91b-2db0-7591-184d-34d00333f10e"
                    }]
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    # 暂时只查dashboard_chart
    dashboard_list = external_dashboard_repository.get_related_dashboard_by_dataset_id(dataset_id)
    if not dashboard_list:
        return list()

    dashboard_dict = dict()
    query_level_code_list = set()
    for dashboard_data in dashboard_list:
        dashboard_dict[dashboard_data.get('dashboard_id')] = dashboard_data
        query_level_code_list.add(dashboard_data.get('dashboard_level_code')[0:5])

    level_code_dashboard_dict = dict()
    level_code_dashboards = external_dashboard_repository.get_dashboard_by_level_code(query_level_code_list)
    for data in level_code_dashboards:
        level_code_dashboard_dict[data.get('level_code')] = data.get('name')

    for dashboard_data in dashboard_list:
        if not dashboard_data.get('dashboard_level_code'):
            dashboard_data['folder'] = '/'
            continue
        folder_paths = []
        dashboard_level_code = dashboard_data.get('dashboard_level_code')
        level_codes = dashboard_level_code[0: len(dashboard_level_code) - 5].split('-')
        prev_level_code = ''
        for level_code in level_codes:
            prev_level_code = prev_level_code + level_code + '-'
            if level_code and level_code_dashboard_dict.get(prev_level_code):
                folder_paths.append(level_code_dashboard_dict.get(prev_level_code))
        dashboard_data['folder'] = '/'.join(folder_paths)

    return dashboard_list


def batch_get_related_dashboard_by_field_id(dataset_field_ids):
    """
    校验报告是否有关联数据集字段id
    ps: 暂不校验报告四种筛选条件的字段关联
    :param dataset_field_ids:
    :return: result [{
                "dashboard_name": "测试报告锁定",
                "dashboard_id": "39ead67a-f88e-8d47-2a3e-6f1da9a45bf7",
                "dashboard_chart_name": "滚动堆叠柱形图",
                "dataset_field_id": "39e51292-5a0c-d295-b271-2d3a4b7173d6"
            }]
    """
    if not dataset_field_ids:
        raise UserError(message="数据集字段ID列表不能为空")
    if not isinstance(dataset_field_ids, list):
        raise UserError(message="数据集字段ID列表类型异常")

    result = list()
    # dim
    query_data = external_dashboard_repository.get_related_dashboard_by_field_ids(
        dataset_field_ids=dataset_field_ids, query_table_name='dashboard_chart_dim', field_key='dim'
    )
    if query_data:
        result.extend(query_data)
    # num and zaxis
    query_data = external_dashboard_repository.get_related_dashboard_by_field_ids(
        dataset_field_ids=dataset_field_ids, query_table_name='dashboard_chart_num', field_key='num'
    )
    if query_data:
        result.extend(query_data)
    # chart_filter
    query_data = external_dashboard_repository.get_related_dashboard_by_field_ids(
        dataset_field_ids=dataset_field_ids, query_table_name='dashboard_chart_filter', field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)
    # chart_params
    query_data = external_dashboard_repository.get_related_dashboard_by_field_ids(
        dataset_field_ids=dataset_field_ids, query_table_name='dashboard_chart_params', field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)
    # comparison
    query_data = external_dashboard_repository.get_related_dashboard_by_field_ids(
        dataset_field_ids=dataset_field_ids, query_table_name='dashboard_chart_comparison', field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)
    # desire
    query_data = external_dashboard_repository.get_related_dashboard_by_field_ids(
        dataset_field_ids=dataset_field_ids, query_table_name='dashboard_chart_desire', field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)
    # markline
    query_data = external_dashboard_repository.get_related_dashboard_by_field_ids(
        dataset_field_ids=dataset_field_ids, query_table_name='dashboard_chart_markline', field_key='num'
    )
    if query_data:
        result.extend(query_data)

    return result


def get_dataset_fields_by_dashboard(dashboard_id):
    if not dashboard_id:
        raise UserError(message="dashboard_id不能为空")

    result = list()

    # 查询 dashboard_filter
    query_data = external_dashboard_repository.get_dataset_fields_by_dashboard_id(
        'dashboard_filter', dashboard_id, field_key='main_dataset_field_id'
    )
    if query_data:
        result.extend(query_data)

    # 查询 dashboard_filter_chart
    query_data = external_dashboard_repository.get_dataset_fields_by_dashboard_id(
        'dashboard_filter_chart', dashboard_id, field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)

    # 查询 dashboard_chart_dim
    query_data = external_dashboard_repository.get_dataset_fields_by_chart_dashboard_id(
        'dashboard_chart_dim', dashboard_id, field_key='dim'
    )
    if query_data:
        result.extend(query_data)

    # 查询 dashboard_chart_num (与dim表不同，这里只取有排序的字段)
    query_data = external_dashboard_repository.get_dataset_fields_by_chart_dashboard_id_where(
        'dashboard_chart_num', dashboard_id, field_key='num'
    )
    if query_data:
        result.extend(query_data)

    # 查询 dashboard_chart_desire ， 图表目标值
    query_data = external_dashboard_repository.get_dataset_fields_by_chart_dashboard_id(
        'dashboard_chart_desire', dashboard_id, field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)

    # 查询 dashboard_chart_filter
    query_data = external_dashboard_repository.get_dataset_fields_by_chart_dashboard_id(
        'dashboard_chart_filter', dashboard_id, field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)

    # 查询 dashboard_chart_filter_relation
    query_data = external_dashboard_repository.get_dataset_fields_by_chart_dashboard_id(
        'dashboard_chart_filter_relation', dashboard_id, field_key='dashboard_chart_filter_id'
    )
    if query_data:
        result.extend(query_data)

    # 查询 dashboard_jump_relation
    query_data = external_dashboard_repository.get_dataset_fields_by_dashboard_id(
        'dashboard_jump_relation', dashboard_id, field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)

    # dashboard_linkage
    query_data = external_dashboard_repository.get_dataset_fields_by_dashboard_id(
        'dashboard_linkage', dashboard_id, field_key='dataset_field_id'
    )
    if query_data:
        result.extend(query_data)

    # 组装成{'dataset_id': [col1, col2, col3]}
    result_dict = defaultdict(list)
    for row in result:
        if row.get("col_name") not in result_dict.get(row.get("dataset_id"), []):
            result_dict[row.get("dataset_id")].append(row.get("col_name"))
    return result_dict


def get_related_dashboard_by_var_id(dataset_var_id):
    """
    获取提供的变量id相关联到的报告
    :param dataset_var_id:
    :return:
    """
    if not dataset_var_id:
        return list()
    query_data = external_dashboard_repository.get_related_dashboard_by_var_id(dataset_var_id)
    related_dashboard_list = (
        [i.get("dashboard_id") for i in query_data if i.get("dashboard_id")] if query_data else list()
    )
    return related_dashboard_list


def dashboard_error_alarm(**kwargs):
    biz_id = kwargs.get('biz_id', None)
    if not biz_id:
        logger.error("业务id为空")
        return
    dashboard = dashboard_repository.get_dashboard(biz_id)
    if not dashboard:
        logger.error("看板不存在")
        return
    error_data_id = kwargs.get('error_data_id', None)
    if not error_data_id:
        logger.error("组件id为空")
        return
    chart = chart_repository.get_chart_by_chart_id(error_data_id)
    if not chart:
        logger.error(f"对应组件不存在: {error_data_id}")
        return

    key = f'dashboard_chart_alarm_barrier:{error_data_id}'
    redis_conn = conn_redis()
    if redis_conn.exists(key):
        logger.error(f"组件{error_data_id}预警一分钟内已发送过")
        return
    kwargs['biz_name'] = dashboard.get('name')
    kwargs['chart_name'] = chart.get('name')
    dataset_id = chart.get('source')
    dataset_name = ''
    if dataset_id:
        dataset = dataset_repository.get_dataset(dataset_id)
        dataset_name = dataset.get('name') if dataset else ''

    kwargs['dataset_name'] = dataset_name
    url = config.get('Domain.dmp') + f'/dataview/share/{biz_id}'
    kwargs['url'] = url
    msg = format_dashboard_alarm_msg(**kwargs)
    webhook = config.get('Alarm.dashboard_alarm_wx_webhook')
    if webhook is None or webhook == 'unused':
        logger.error("大屏异常预警webhook未配置")
        return
    resp = QyRobotApi(webhook).request(msg, msg_type='markdown')
    send_state = resp.get('errcode')
    if send_state is None or send_state != 0:
        logger.error(f'企业微信消息发送失败：{resp}')
    else:
        redis_conn.set(key, time.time(), 60)


def format_dashboard_alarm_msg(**kwargs):
    template = """## 重点大屏异常预警
            >租户名称: %(org_code)s
            >看板名称: %(biz_name)s
            >组件名称: %(chart_name)s
            >数据集名称: %(dataset_name)s
            >预警类型: %(error_type)s
            >错误信息: <font color='red'>%(error_msg)s</font>
            >[看板地址](%(url)s)
    """
    return template % kwargs


def save_params(params: dict):
    if params:
        key = seq_id()
        data = json.dumps(params, ensure_ascii=False)
        repository.add_data('jump_params_keys', {'id': key, 'params': data})
        return key


def get_params(key: str):
    from keywords.utils.keyword_utils import get_data_by_keyword
    if key:
        # 获取昨天的日期
        date = get_data_by_keyword('昨日')
        # 清除前一天日期的数据
        repository.delete_data('jump_params_keys', {'created_on<': date})
        data = repository.get_data_scalar('jump_params_keys', {'id': key}, 'params')
        data = json.loads(data) if data else ''
        return data


def set_dashboard_distribute_type(dashboard_id, distribute_type):
    if dashboard_id:
        # 查询报表是否存在
        data = repository.get_one('dashboard', {'id': dashboard_id}, ['id'])
        if not data:
            raise UserError(message='报告不存在')
        repository.update_data('dashboard', {'distribute_type': distribute_type}, {'id': dashboard_id})
    return True
