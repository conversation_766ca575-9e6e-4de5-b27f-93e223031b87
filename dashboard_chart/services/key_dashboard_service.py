#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
key dashboard service
重点大屏的数据统计service
"""
from datetime import datetime
import traceback
from loguru import logger

from dmplib import config
from dmplib.hug import g
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from dmplib.db.mysql_wrapper import get_db as get_master_db
from dmplib.saas.project import get_db

from base.enums import FlowType, FlowStatus, FlowNodeType
from base import repository
from components.fast_logger import FastLogger

from flow.models import FlowModel, FlowNodeModel
from flow.services.flow_service import add_flow, update_flow, check_env_task, update_flow_env_schedule


def init_key_dashboard_stat_task():
    """
    重点大屏统计上报任务初始化
    :return:
    """
    project_code = g.code
    task_id = '00000000-1111-2222-3333-000000000000'
    task_name = f'重点大屏统计上报任务-{project_code}'
    cron = '0 0 3 ? * * *'

    try:
        task_flow_model = _get_key_dashboard_flow(task_id, task_name, cron)

        _check_env_rundeck_task(task_flow_model)

        _add_key_dashboard_stat_flow(task_flow_model)

        update_flow_env_schedule(task_id, command=_get_key_dashboard_stat_command(task_id))

    except Exception as e:
        err_msg = "初始化重点大屏统计上报任务错误，errs："+str(e)
        logger.error(err_msg)
        logger.error(traceback.print_exc())
        raise UserError(message=err_msg)
    logger.error("初始化重点大屏统计上报任务完成！")


def _check_env_rundeck_task(task_id):
    """
    检查环境级任务是否存在
    :param task_id:
    :return:
    """
    exists_task = check_env_task(task_id)
    if exists_task:
        raise UserError(message="重点大屏统计上报任务已存在")
    return True


def _get_key_dashboard_flow(task_id, task_name, cron):
    data = {'id': task_id, 'name': task_name, 'description': '重点大屏统计任务，记录日志到天眼', 'schedule': cron}
    flow = FlowModel(**data)
    flow.status = FlowStatus.Enable.value
    flow.type = FlowType.Collector.value
    flow.nodes = [FlowNodeModel(name=task_name, type=FlowNodeType.Collector.value)]
    return flow


def _get_key_dashboard_stat_command(flow_id, queue_name='celery'):
    """
    获取rundeck执行celery的command命令
    :param flow_id:
    :param queue_name:
    :return:
    """
    celery_task_name = "app_celery.key_dashboard_stat"
    cmd_template_snap = config.get(
        "Rundeck.cmd_template_celery",
        "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
    )
    command = '%s %s %s %s %s' % (cmd_template_snap, g.code, celery_task_name, flow_id, queue_name)
    return command


def _add_key_dashboard_stat_flow(flow_model):
    flow = repository.get_data("flow", {"id": flow_model.id})
    if flow:
        update_flow(flow_model, False)
    else:
        add_flow(flow_model)
    return flow


def key_dashboard_stat(params):
    """
    重点大屏的数据统计以及日志记录
    :param params:
    :return:
    """
    logger.error("开始执行重点大屏数据统计，params:" + str(params))
    page = 1
    while True:
        code_list = _get_project_by_page(page)
        if not code_list:
            break
        _stat_key_dashboard(code_list)
        page += 1

def key_dashboard_mem_usage(codes):
    from dmplib.redis import get_redis_conn
    #查询当前环境所有重点大屏租户
    codes = _get_key_dashboard_project(codes)
    if not codes:
        return {}
    #查询当前环境redis版本
    redis_conn = get_redis_conn()
    version_ = redis_conn.info()['redis_version']
    main_version = int(version_.split('.')[0])
    if main_version < 4:
        raise UserError(message=f'当前版本:{version_}，不支持查询key占用空间[version>=4.0.0]')
    dashboards = {}
    for c in codes:
        list = _get_key_dashboard_id(c)
        info = {}
        total = 0
        for d in list:
            id_ = d['id']
            pattern = c + f':last_version:{id_}:*'
            exist_key = redis_conn.keys(pattern)
            d['keys'] = []
            if not exist_key:
                continue
            for ek in exist_key:
                usage = redis_conn.execute_command('MEMORY USAGE', ek)
                usage = round(usage / 1024, 2)
                total += usage
                d['keys'].append({'key': str(ek, 'UTF-8'), 'usage': str(usage) + 'kb'})
        info['total'] = str(total) + 'kb'
        info['dashboards'] = list
        dashboards[c] = info
    return dashboards


def _get_key_dashboard_project(codes):
    sql = 'SELECT `code` FROM `project` where is_key_screen = 1'
    if codes:
        sql += ' and code in %(codes)s'
    with get_master_db() as db:
        return db.query_columns(sql, {"codes":codes})


def _get_project_by_page(page=1, page_size=100):
    skip = ((int(page) if page else 1) - 1) * (int(page_size) if page_size else 100)
    sql = 'SELECT `code` FROM `project`'
    with get_master_db() as db:
        sql += ' LIMIT ' + str(skip) + ',' + str(page_size)
        return db.query_columns(sql)


def _stat_key_dashboard(code_list):
    for code in code_list:
        _stat_key_dashboard_by_code(code)


def _stat_key_dashboard_by_code(code):
    dashboard_num = 0
    try:
        dashboard_num = _get_key_dashboard_num(code)
    except Exception as e:
        logger.error(f"{code}租户查询失败，errs:"+str(e))
    # 写入日志
    _record_key_dashboard_log_data(code, dashboard_num)


def _record_key_dashboard_log_data(code, dashboard_num):
    log_data = {
        "date": datetime.strftime(datetime.now(), "%Y-%m-%d"),
        "key_dashboard_num": dashboard_num,
        "org_code": code,
    }
    # 测试输出
    logger.error(log_data)
    FastLogger.KeyDashboardFastLogger(**log_data).record()


def _get_key_dashboard_num(code):
    sql = """select count(*) as c from dashboard where build_in=0 and type = 'FILE' and platform = 'pc' 
    and is_key=1 and application_type = 0"""
    with get_db(code) as db:
        c = db.query_scalar(sql)
        # 手动关闭连接
        db.end()
        return c


def _get_key_dashboard_id(code):
    sql = """select id,name from dashboard where build_in=0 and type = 'FILE' and platform = 'pc' 
    and is_key=1 and application_type = 0"""
    with get_db(code) as db:
        c = db.query(sql)
        # 手动关闭连接
        db.end()
        return c
