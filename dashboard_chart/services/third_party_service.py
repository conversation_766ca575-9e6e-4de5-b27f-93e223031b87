import logging
from dmplib.utils.errors import UserError
from dashboard_chart.third_patry.print_jump import PrintJump
from dashboard_chart.third_patry.erp_report_jump import ERPAdminSiteJump

logger = logging.getLogger(__name__)


class ThirdPartyService:
    """
        业务应用的操作实现类
        """
    BIZ_MAP = {
        # 套打服务
        "print": PrintJump,
        "erp_report": ERPAdminSiteJump,
    }

    def __init__(self, request, **kwargs):
        self._type = kwargs.get("type")
        self.jump_biz = ThirdPartyService.BIZ_MAP.get(self._type)(request, **kwargs)

    def get_tree_data(self):
        return self.jump_biz.get_tree_data()

    def view(self):
        # if not self.jump_biz.check_role():
        #     raise UserError(message="暂无权限使用该功能")
        return self.jump_biz.redirect('PRINT_FILE_VIEW')

    def template_list(self):
        # if not self.jump_biz.check_role():
        #     raise UserError(message="暂无权限使用该功能")
        return self.jump_biz.redirect('PRINT_TEMPLATE_PAGE')
