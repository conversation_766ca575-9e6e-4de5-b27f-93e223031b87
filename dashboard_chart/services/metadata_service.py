#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/22
# pylint: skip-file

"""
services
"""

# ---------------- 标准模块 ----------------
import itertools
import logging
import os
import time

import ujson as json
from collections import defaultdict
import collections
import hashlib

# ---------------- 业务模块 ----------------
from base import repository
from dmplib import config
from base.dmp_constant import USER_LAST_ACTIVE, COMPONENT_LOGIC_CODE_KEY
from dmplib.utils.errors import UserError
from base.enums import (
    DashboardTypeStatus,
    DashboardJumpConfigStatus,
    DatasetVarValueSource,
    JumpConfigSourceType,
    ChartFilterInitiatorSouce,
    DashboardJumpType,
)
from components.redis_utils import RCache
from dashboard_chart.repositories import metadata_repository
from dashboard_chart.repositories import chart_repository
from dashboard_chart.services import proxy_dataset_service as dataset_service, released_dashboard_service
from dashboard_chart.metadata.query_preview_metadata_model import DashboardMetadataQueryModel
from dashboard_chart.metadata.query_release_metadata_model import ReleasedDashboardMetadataQueryModel
from dmplib.redis import conn as conn_redis, RedisCache, conn_custom_prefix
from dashboard_chart.metadata.dashboard_preview_metadata_models import DashboardInstalledComponentNodeModel
from dmplib.hug import g
from dashboard_chart.services.dashboard_cache_service import DashboardCache, DashboardReleaseCache
from dashboard_chart.services import dashboard_service
from dashboard_chart.utils import decorators
from dashboard_chart.utils.common import LAST_VERSION_KEYS
from dashboard_component.services import component_service
from dashboard_chart.metadata.metadata_builder.builder import MetadataBuilder
from dashboard_chart.utils.common import add_api_dataset_params, set_data_index

from datetime import datetime
from dmplib.db.mysql_wrapper import SimpleMysql
from typing import Any, Dict, List, Optional, Tuple, Union

logger = logging.getLogger(__name__)


def _get_released_snapshot_dashboard_fields() -> List[str]:
    """
    返回dashboard_released_snapshot_dashboard表字段列表
    :return:
    """
    return [
        'id',
        'snapshot_id',
        'name',
        'type',
        'data_type',
        'level_code',
        'platform',
        'is_multiple_screen',
        'status',
        'cover',
        'dashboard_filters',
        'dashboard_var_filters',
        'share_secret_key',
        'layout',
        'scale_mode',
        'background',
        'biz_code',
        'type_access_released',
        'theme',
        'selectors',
        'modified_on',
        'refresh_rate',
        'rank',
        'grid_padding',
        'create_type',
        'new_layout_type',
        'is_show_mark_img',
        'parent_id',
        'var_value_sources',
        'terminal_type',
        'main_external_subject_id',
        'application_type',
        'external_subject_ids',
        'line_height',
        'smart_beauty_status',
        "dataset_id",
        "auto_play",
        "global_params",
        "analysis_type"
    ]


def get_released_dashbaoard_data(snapshot_id, dashboard_id):
    """
    获取已发布报告数据
    :param snapshot_id:
    :param dashboard_id:
    :return:
    """

    fields = _get_released_snapshot_dashboard_fields()
    dashboard_data = repository.get_data(
        'dashboard_released_snapshot_dashboard', {'snapshot_id': snapshot_id, 'id': dashboard_id}, fields
    )
    return dashboard_data


def get_released_dashbaoard_data_with_conn(
    snapshot_id: str, dashboard_id: str, conn: SimpleMysql
) -> Dict[str, Union[str, int, datetime]]:
    """
    获取已发布报告数据
    :param snapshot_id:
    :param dashboard_id:
    :return:
    """
    fields = _get_released_snapshot_dashboard_fields()
    dashboard_data = metadata_repository.get_released_dashboard_data_with_conn(fields, snapshot_id, dashboard_id, conn)
    return dashboard_data


def batch_get_released_dashbaoard_data(snapshot_id):
    """
    批量获取已发布报告数据
    :param snapshot_id:
    :return:
    """

    fields = _get_released_snapshot_dashboard_fields()
    dashboard_data = repository.get_data(
        'dashboard_released_snapshot_dashboard', {'snapshot_id': snapshot_id}, fields, multi_row=True
    )
    return dashboard_data


def batch_get_released_dashbaoard_data_with_conn(
    snapshot_id: str, conn: Optional[SimpleMysql] = None
) -> List[Dict[str, Union[str, int, datetime]]]:
    """
    批量获取已发布报告数据
    :param snapshot_id:
    :param conn:
    :return:
    """
    fields = _get_released_snapshot_dashboard_fields()
    dashboard_data = metadata_repository.batch_get_released_dashbaoard_data_with_conn(fields, snapshot_id, conn)
    return dashboard_data


def get_released_chart_data(snapshot_id, screen_id):
    """
    获取已发布单图配置数据
    :param snapshot_id:
    :param screen_id:
    :return:
    """
    if not screen_id:
        conditions = {'dashboard_id': snapshot_id, 'type': DashboardTypeStatus.Release.value}
        data = repository.get_data('screen_dashboard', conditions, ['screen_id'], False, [('rank', 'ASC')])
        if data:
            screen_id = data.get('screen_id')
    snapshot_chart_list = repository.get_data(
        'dashboard_released_snapshot_chart',
        {'snapshot_id': snapshot_id, 'dashboard_id': screen_id},
        released_dashboard_service.get_released_snapshot_chart_fields(),
        multi_row=True,
    )
    return snapshot_chart_list


def get_released_chart_data_with_conn(
    snapshot_id: str, screen_id: str, conn: SimpleMysql
) -> List[Dict[str, Union[str, int, None]]]:
    """
    获取已发布单图配置数据
    :param snapshot_id:
    :param screen_id:
    :param conn:
    :return:
    """
    fields = released_dashboard_service.get_released_snapshot_chart_fields()
    snapshot_chart_list = metadata_repository.get_released_chart_data_with_conn(fields, snapshot_id, screen_id, conn)
    # 由于老的chart_var 没有value_source 和 value_identifier
    # 因此需要对老的手动赋值
    if isinstance(snapshot_chart_list, (list, set)):
        for chart in snapshot_chart_list:
            chart_vars = chart.get("chart_vars", list())
            if isinstance(chart_vars, str):
                chart_vars = json.loads(chart_vars)
            for chart_var in chart_vars:
                if "value_source" not in chart_var:
                    chart_var["value_source"] = DatasetVarValueSource.Userdefined.value
                    chart_var["value_identifier"] = ""
            chart["chart_vars"] = chart_vars
    return snapshot_chart_list


def get_snapshot_chart_data_by_fields(snapshot_id, chart_id, fields):
    """
    获取快照chart
    :param snapshot_id:
    :param chart_id:
    :param fields:
    :return:
    """
    data = repository.get_data(
        'dashboard_released_snapshot_chart', {'snapshot_id': snapshot_id, 'id': chart_id}, fields
    )
    return data


def get_release_metadata_redis_key(snapshot_id: str) -> str:
    """
    获取元数据缓存key
    :return:
    """
    if not snapshot_id or not isinstance(snapshot_id, str):
        return snapshot_id
    return 'release_dashboarrd_metadata' + '_' + str(snapshot_id)


def delete_release_metadata(snapshot_id: str) -> Tuple[str, bool]:
    """
    根据id删除已发布元数据缓存
    :param snapshot_id:
    :return:
    """
    msg = ''
    redis_cache = conn_redis()
    if not snapshot_id:
        return msg, False
    redis_metadata_key = get_release_metadata_redis_key(snapshot_id)
    try:
        redis_cache.del_data(redis_metadata_key)
    except Exception as e:
        return str(e), False
    return '删除成功', False


def delete_release_metadata_cache(snapshot_id: str) -> Tuple[str, bool]:
    """
    根据id删除已发布元数据缓存
    :param snapshot_id:
    :return:
    """
    msg = ""
    if not snapshot_id:
        return msg, False
    try:
        main_cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
        if main_cache_instance:
            screens_cache_key = "screens"
            screens_cache_data = main_cache_instance.get_prop(screens_cache_key)
            if screens_cache_data:
                for single_dashboard in screens_cache_data:
                    dashboard_id = single_dashboard.get("id")
                    # 报告单独的缓存，与元数据的来源数据缓存是相独立的
                    single_dashboard_cache_instance = DashboardReleaseCache(g.code, dashboard_id, conn_redis())
                    single_dashboard_cache_instance.delete_dashboard_data()
            # 清除元数据缓存
            main_cache_instance.remove()
            # 清除报告表数据缓存
            main_dashboard_cache_instance = DashboardReleaseCache(g.code, snapshot_id, conn_redis())
            main_dashboard_cache_instance.delete_dashboard_data()
        return "删除成功", True
    except Exception as e:
        logger.exception(e)
        return "删除失败", False


def delete_last_version_cache(dashboard_id):
    try:
        # 清空redis缓存
        cache_keys = LAST_VERSION_KEYS.format(dashboard_id=dashboard_id)
        cache = RCache()
        keys = cache.lrange(cache_keys, 0, -1) or []
        for key in keys:
            cache.delete(key)
        # 清空内存缓存
        from dashboard_chart.utils.decorators import has_last_version
        func = has_last_version
        if func and hasattr(func, "lru_cache"):
            func.lru_cache.cache_clear()
        return "删除成功", True
    except Exception as e:
        logger.exception(e)
        return "删除失败", False


def get_cache_version(snapshot_id=""):
    """
    获取缓存version
    :return:
    """
    version_cache_data = ""
    object_id = ""
    if not snapshot_id:
        raise ValueError("报告ID不能为空")
    if snapshot_id:
        object_id = snapshot_id
    cache_instance = DashboardCache(g.code, object_id, conn_redis())
    if cache_instance:
        version_cache_data = cache_instance.get_version()
    return version_cache_data


def get_needed_installed_component(installed_component, snapshot_id):
    """
    按需获取已安装组件
    :param installed_component:
    :param snapshot_id:
    :return:
    """
    result_installed_component = list()
    chart_code_list_cache_key = "chart_code_list"
    cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
    cache_chart_code_list = cache_instance.get_prop(chart_code_list_cache_key)
    # 默认全部返回
    if not cache_chart_code_list or not installed_component:
        return installed_component
    cache_chart_code_list += ['hd_mediator', 'hd_wrapper']
    # 按需返回
    for single_chart_code in cache_chart_code_list:
        for single_component in installed_component:
            if not isinstance(single_component, dict):
                continue
            package = single_component.get('package')
            if package == single_chart_code:
                result_installed_component.append(single_component)
                break
    return result_installed_component


def get_dashboard_runterminal(meta):
    """
    获取报告终端类型
    :param meta:
    :return:
    """
    dashboard = meta.get("dashboard") if meta else {}
    return dashboard.get("terminal_type", "") if dashboard else ""


@decorators.try_except_for_metadata
def get_screens_preview_metadata_v2(dashboard_id: str):
    """
    获取多屏元数据-预览
    注：区别于原先的元数据格式
    :param dashboard_id:
    :return:
    """
    msg = ''
    main_metadata_model = DashboardMetadataQueryModel(dashboard_id=dashboard_id).combine_screen_metadata()
    result = dict()
    if main_metadata_model:
        # 获取报告终端类型
        runterminal = get_dashboard_runterminal(main_metadata_model.get_data())
        # 已安装组件信息单独获取
        component_metadata_model = DashboardInstalledComponentNodeModel(
            node_name='installed_component', runterminal=runterminal
        )
        main_metadata_model.batch_add_sub_node([component_metadata_model])
        result = main_metadata_model.get_data()
    return msg, result


@decorators.try_except_for_metadata
def get_screens_release_metadata_v2(snapshot_id, token_data=None):
    """
    获取多屏元数据-已发布
    注：区别于原先的元数据格式
    :param token_data:
    :param snapshot_id:
    :return:
    """
    msg = ''
    main_metadata_model = ReleasedDashboardMetadataQueryModel(snapshot_id=snapshot_id).combine_screen_metadata(
        token_data=token_data
    )
    main_metadata_data = main_metadata_model.get_data()
    if not main_metadata_data:
        raise UserError(code=404, message='ID{}对应报告数据不存在'.format(snapshot_id))
    # 已安装组件信息单独获取
    installed_component = list()
    if main_metadata_data:
        # 获取报告终端类型
        runterminal = get_dashboard_runterminal(main_metadata_data)
        component_metadata_model = DashboardInstalledComponentNodeModel(
            node_name='installed_component', runterminal=runterminal
        )
        installed_component = component_metadata_model.get_data()

    # 已安装组件按需返回
    installed_component = get_needed_installed_component(installed_component, snapshot_id)

    # 兼容高级字段会被更新的情况，需要另外更新col_name
    first_report = main_metadata_data.get('first_report')
    if first_report:
        chart_list = first_report.get('charts')
        if chart_list:
            main_metadata_data['first_report']['charts'] = _deal_with_advanced_field(chart_list)

    main_metadata_data.update({'installed_component': installed_component})
    # # 缓存的version
    # cache_version = get_cache_version(snapshot_id)
    return msg, main_metadata_data


@decorators.try_except_for_metadata
def get_screens_release_metadata_without_comment(snapshot_id):
    """
    只获取元数据，不包含组件信息
    """
    main_metadata_model = ReleasedDashboardMetadataQueryModel(snapshot_id=snapshot_id).combine_screen_metadata()
    main_metadata_data = main_metadata_model.get_data()
    if not main_metadata_data:
        raise UserError(code=404, message='ID{}对应报告数据不存在'.format(snapshot_id))
    return main_metadata_data


@decorators.try_except_for_metadata
def get_screens_preview_metadata_v2_without_coment(dashboard_id):
    main_metadata_model = DashboardMetadataQueryModel(dashboard_id=dashboard_id).combine_screen_metadata()
    return main_metadata_model.get_data()


@decorators.try_except_for_metadata
def get_dashboard_preview_metadata_v2(dashboard_id):
    """
    获取单个报告元数据-预览
    :param dashboard_id:
    :return:
    """
    msg = ''
    result = DashboardMetadataQueryModel().combine_dashboard_metadata(operate_dashboard_id=dashboard_id)
    return msg, result


@decorators.try_except_for_metadata
def get_dashboard_release_metadata_v2(dashboard_id, snapshot_id):
    """
    获取单个报告元数据-已发布
    :param dashboard_id:
    :param snapshot_id:
    :return:
    """
    msg = ''
    result = ReleasedDashboardMetadataQueryModel().combine_dashboard_metadata(
        operate_dashboard_id=dashboard_id, operate_snapshot_id=snapshot_id
    )
    # # 缓存version
    # cache_version = get_cache_version(snapshot_id)
    return msg, result


@decorators.try_except_for_metadata
def get_screens_preview_metadata(dashboard_id: str):
    """
    新版-多屏元数据获取
    :param dashboard_id:
    :return:
    """
    return MetadataBuilder(dashboard_id=dashboard_id).build_for_screen()


@decorators.try_except_for_metadata
def get_dashboard_preview_metadata(dashboard_id: str):
    """
    新版-报告元数据获取
    :param dashboard_id:
    :return:
    """
    return MetadataBuilder(dashboard_id=dashboard_id).build_for_dashboard()


def _update_advanced_field_col_name(data_list, section_type, advanced_field_id_dict):
    """
    更新高级字段下的col_name，对普通字段也需要做处理
    :param data_list:
    :param section_type:
    :param advanced_field_id_dict:
    :return:
    """
    for single_data in data_list:
        field_id = single_data.get(section_type)
        old_col_name = single_data.get('col_name')
        if field_id and field_id in list(advanced_field_id_dict.keys()):
            new_col_name = advanced_field_id_dict.get(field_id)
            if old_col_name != new_col_name and new_col_name:
                single_data['col_name'] = new_col_name
    return data_list


def _get_section_field_id_by_key(section_data, for_query_field_id_list, get_key="dataset_field_id"):
    """
    根据指定key获取数据集字段id
    :param section_data:
    :param for_query_field_id_list:
    :param get_key:
    :return:
    """
    if not section_data:
        return for_query_field_id_list
    for single_data in section_data:
        if single_data.get(get_key):
            for_query_field_id_list.append(single_data.get(get_key))


def _collect_for_query_field_id_list(orig_chart_list):
    """
    收集数据集字段id列表
    :param orig_chart_list:
    :return:
    """
    for_query_field_id_list = list()
    for single_chart in orig_chart_list:
        chart_data = single_chart.get('data', None)
        if not chart_data:
            continue
        indicator = chart_data.get('indicator', None)
        if not indicator:
            continue
        # 收集dim
        dims = indicator.get('dims')
        if dims:
            _get_section_field_id_by_key(dims, for_query_field_id_list, get_key="dim")
        # 收集num
        nums = indicator.get('nums')
        if nums:
            _get_section_field_id_by_key(nums, for_query_field_id_list, get_key="num")
        # 收集desires
        desires = indicator.get('desires')
        if desires:
            _get_section_field_id_by_key(desires, for_query_field_id_list)
        # 收集chart_params
        chart_params = indicator.get('chart_params')
        if chart_params:
            _get_section_field_id_by_key(chart_params, for_query_field_id_list)
    return for_query_field_id_list


def _deal_with_advanced_field(orig_chart_list):
    """
    处理高级字段，对普通字段也需要做处理
    :param orig_chart_list:
    :return:
    """
    advanced_field_id_dict = dict()
    if not orig_chart_list:
        return orig_chart_list

    # 先收集字段
    for_query_field_id_list = _collect_for_query_field_id_list(orig_chart_list)

    # 获取高级字段的col_name参数
    query_field_data = dataset_service.get_advanced_field_by_ids(['id', 'col_name'], list(set(for_query_field_id_list)))
    if query_field_data:
        for single_query_field in query_field_data:
            advanced_field_id_dict.update({single_query_field.get('id'): single_query_field.get('col_name')})

    # 更新元数据中是高级字段的col_name
    for single_chart in orig_chart_list:
        chart_data = single_chart.get('data', {})
        indicator = chart_data.get('indicator', None)
        if not indicator:
            continue
        dims = indicator.get('dims')
        if dims:
            single_chart['data']['indicator']['dims'] = _update_advanced_field_col_name(
                dims, 'dim', advanced_field_id_dict
            )
        nums = indicator.get('nums')
        if nums:
            single_chart['data']['indicator']['nums'] = _update_advanced_field_col_name(
                nums, 'num', advanced_field_id_dict
            )
        desires = indicator.get('desires')
        if desires:
            single_chart['data']['indicator']['desires'] = _update_advanced_field_col_name(
                desires, 'dataset_field_id', advanced_field_id_dict
            )
        chart_params = indicator.get('chart_params')
        if chart_params:
            single_chart['data']['indicator']['chart_params'] = _update_advanced_field_col_name(
                chart_params, 'dataset_field_id', advanced_field_id_dict
            )

    return orig_chart_list


def get_dashboard_filters_by_id(dashboard_id: str) -> List[Any]:
    """
    根据报告ID获取报告级筛选条件
    :param dashboard_id:
    :return:
    """
    fields = ['id', 'dashboard_id', 'operator', 'col_value', 'main_dataset_field_id', 'select_all_flag', 'filter_relation']
    dashboard_filters = repository.get_data('dashboard_filter', {'dashboard_id': dashboard_id}, fields, multi_row=True)
    if not dashboard_filters:
        return dashboard_filters
    for item in dashboard_filters:
        item['operators'] = dashboard_service.get_operators_by_filter_id(item.get('id'))
    return dashboard_filters


def get_dashboard_filter_relations_by_id(dashboard_id: str) -> List[Any]:
    """
    根据报告ID获取报告级筛选关联关系
    :param dashboard_id:
    :return:
    """
    fields = ['id', 'dashboard_id', 'main_dataset_field_id', 'related_dataset_field_id']
    return repository.get_data(
        'dashboard_dataset_field_relation', {'dashboard_id': dashboard_id}, fields, multi_row=True
    )


def get_dashboard_var_filter_relations_by_id(dashboard_id: str) -> List[Any]:
    """
    根据报告ID获取报告级筛选关联关系
    :param dashboard_id:
    :return:
    """
    fields = ['id', 'dashboard_id', 'var_name', 'related_dataset_field_id', 'related_dataset_id']
    return repository.get_data(
        'dashboard_filter_vars_relation_field', {'dashboard_id': dashboard_id}, fields, multi_row=True
    )


def get_dashboard_value_sources_by_id(dashboard_id: str) -> List[Any]:
    """
    根据报告ID获取取值来源配置数据
    :param dashboard_id:
    :return:
    """
    return dashboard_service.get_dashboard_value_sources_by_id(dashboard_id)


def get_single_chart_by_id(chart_id: str):
    """
    根据id获取单图的表数据
    :param chart_id:
    :return:
    """
    fields = [
        'id',
        'name',
        'chart_type',
        'position',
        'config',
        'default_value',
        'source',
        'display_item',
        'refresh_rate',
        'data_logic_type_code',
        'chart_code',
        'data_modified_on',
        'sort_method',
        'percentage',
        'column_order',
        'page_size',
        'filter_relation',
        "export_type",
        "chart_visible",
    ]
    return repository.get_data('dashboard_chart', {'id': chart_id}, fields)


def get_single_chart_default_value(chart_id: str) -> List[Any]:
    fields = ["id", "dataset_field_id", "operator", "value", "select_all", "extend_data"]
    return repository.get_data("dashboard_filter_chart_default_values", {"chart_id": chart_id}, fields, multi_row=True)


def get_chart_by_dashboard_id(dashboard_id: str) -> List[Dict[str, Union[str, int, None]]]:
    """
    根据报告id获取单图数组
    :param dashboard_id:
    :return:
    """
    fields = [
        'id',
        'name',
        'chart_type',
        'position',
        'config',
        'default_value',
        'source',
        'display_item',
        'refresh_rate',
        'data_logic_type_code',
        'chart_code',
        'data_modified_on',
        'sort_method',
        'percentage',
        'column_order',
        'level_code',
        'page_size',
        'filter_relation',
        'aggregation',
        'enable_subtotal',
        'enable_summary',
        'enable_subtotal_col',
        'enable_subtotal_col_summary',
        'enable_subtotal_row',
        'enable_subtotal_row_summary',
        'subtotal_row_summary_formula_mode',
        'reset_field_sort',
        'pre_comparison',
        "export_type",
        "parent_chart_id",
        "external_subject_id",
        "fixed_data_mode",
        "fixed_manual_value",
        "is_highdata",
        "close_detail_mode",
        "chart_visible",
        "asset_id",
        "layout_extend",
    ]
    charts = repository.get_data(
        'dashboard_chart', {'dashboard_id': dashboard_id}, fields, order_by=[("created_on", "asc")], multi_row=True
    )
    return charts


def batch_get_chart_nums_and_zaxis(
    dashboard_id: str,
) -> Union[Tuple[List[Any], List[Any]], Tuple[List[Dict[str, Union[str, int, None]]], List[Any]]]:
    """
    根据dashboard_id获取单图的nums和zaxis数据
    :param dashboard_id: 报告id
    :return:
    """
    data = metadata_repository.batch_get_nums_and_zaxis(dashboard_id)
    nums, zaxis = list(), list()
    for item in data:
        if item.get('axis_type'):
            zaxis.append(item)
        else:
            nums.append(item)
    return nums, zaxis


def batch_get_chart_nums_and_zaxis_with_field_data(
    dashboard_id: str,
) -> Union[Tuple[List[Any], List[Any]], Tuple[List[Dict[str, Union[str, int, None]]], List[Any]]]:
    """
    批量获取
    :param dashboard_id:
    :return:
    """
    chart_nums_data, chart_zaxis_data = batch_get_chart_nums_and_zaxis(dashboard_id)
    _fields = [
        'id',
        'dataset_id',
        'alias_name',
        'col_name',
        'data_type',
        'visible',
        'field_group',
        'expression',
        'type',
    ]
    for single_nums in itertools.chain(chart_nums_data, chart_zaxis_data):
        num = single_nums.get('num', '')
        # id 要保存不变
        num_id = single_nums.get('id')
        field_data = dataset_service.get_fields_by_field_id(_fields, num)
        single_nums.update(field_data)
        single_nums['id'] = num_id
    return chart_nums_data, chart_zaxis_data


def get_chart_section_data(_fields, field_key_name, data=None, field_for_change_dict=None):
    """
    获取单图组成部分的数据
    :param _fields:
    :param field_key_name:
    :param data:
    :param field_for_change_dict:
    :return:
    """
    if _fields and 'alias' not in _fields:
        _fields.append('alias')
    query_data = data if data else list()
    if not query_data:
        return query_data
    for single_data in query_data:
        dataset_field_id = single_data.get(field_key_name, '')
        if not dataset_field_id:
            continue
        # 将alias优先赋值给col_name
        field_data = dataset_service.switch_to_alias_name(
            dataset_service.get_fields_by_field_id(_fields, dataset_field_id, field_for_change_dict)
        )
        for k, v in field_data.items():
            single_data.update({k: v})
    return query_data


def get_chart_relation_tree_data(query_data):
    """
    获取单图树形数据
    :param query_data:
    :return:
    """
    chart_relation_dict = dict()
    for item in query_data:
        chart_initiator_id = item.get('chart_initiator_id', '')
        chart_responder_id = item.get('chart_responder_id', '')
        if chart_initiator_id and chart_initiator_id not in chart_relation_dict:
            chart_relation_dict[chart_initiator_id] = {}
        responder_dict = chart_relation_dict.get(chart_initiator_id, {})
        if chart_responder_id and chart_responder_id not in responder_dict:
            chart_relation_dict[chart_initiator_id][chart_responder_id] = []
        chart_relation_dict[chart_initiator_id][chart_responder_id].append(item)
    return chart_relation_dict


def convert_relation_to_dict(query_data: List[Any], source="") -> Dict[Any, Any]:
    """
    将单图的关系型数据转换为层级数据
    :param query_data:
    :param source:
    :return:
    """
    chart_relation_dict = dict()
    fixed_value_ids = []
    for item in query_data:
        linkage_id = item["chart_linkage_id"]
        relation_info = {
            "id": item["chart_linkage_relation_id"],
            "chart_responder_id": item["chart_responder_id"],
            "related_dataset_id": item["chart_responder_dataset_id"],
            "field_responder_id": item["chart_responder_field_id"],
            "is_same_dataset": 1 if item["chart_initiator_dataset_id"] == item["chart_responder_dataset_id"] else 0,
        }
        if linkage_id not in chart_relation_dict:
            indicator_dim_obj = json.loads(item.get("indicator_dim_obj")) if item.get("indicator_dim_obj") else {}
            single_relation = {
                "id": linkage_id,
                "chart_initiator_id": item["chart_initiator_id"],
                "dataset_id": item["chart_initiator_dataset_id"],
                "field_initiator_id": item["chart_initiator_filed_id"],
                "related_list": [],
                "indicator_dim_obj": indicator_dim_obj,
            }
            _op_chart_filter_relation(fixed_value_ids, item, single_relation, source)
            chart_relation_dict[linkage_id] = single_relation
        chart_relation_dict[linkage_id]["related_list"].append(relation_info)
    # 为固定值赋值
    if fixed_value_ids:
        fixed_values_dict = _get_fixed_values_dict(fixed_value_ids)
        for _, item in chart_relation_dict.items():
            if item.get("initiator_source") == ChartFilterInitiatorSouce.Fixedvalue.value:
                item["fixed_value_data"] = fixed_values_dict.get(item.get("field_initiator_id"))
    return chart_relation_dict


def _op_chart_filter_relation(fixed_value_ids, item, single_relation, source):
    if source == "chart_filter":
        single_relation["filter_type"] = item.get("filter_type")
        single_relation["available"] = item.get("available", 1)
        single_relation["initiator_source"] = item.get("initiator_source", ChartFilterInitiatorSouce.Datasetfield.value)
        single_relation["fixed_value_data"] = None
        # 如果发起方是固定值
        if single_relation["initiator_source"] == ChartFilterInitiatorSouce.Fixedvalue.value:
            fixed_value_ids.append(item["chart_initiator_filed_id"])


def _get_fixed_values_dict(fixed_value_ids: List) -> Dict:
    fixed_values = metadata_repository.get_filter_chart_fixed_values(fixed_value_ids)
    return {fixed_value["id"]: fixed_value for fixed_value in fixed_values}


def get_structure_component_filter_data(chart_relation_tree_data: Dict[Any, Any]) -> List[Any]:
    """
    获取组件筛选元数据
    :param chart_relation_tree_data:
    :return:
    """
    data_list = list()
    if not chart_relation_tree_data:
        return data_list
    for (key_chart_initiator_id, value_chart_initiator) in chart_relation_tree_data.items():
        single_data_dict = dict()
        related_list = list()
        dataset_id = ''
        for (key_chart_responder_id, value_chart_responder) in value_chart_initiator.items():
            single_responder_dict = dict()
            relations = list()
            filter_id = None
            for item in value_chart_responder:
                single_relation_dict = dict()
                dataset_id = item.get('dataset_id')
                field_initiator_id = item.get('field_initiator_id')
                field_responder_id = item.get('field_responder_id')
                is_same_dataset = item.get('is_same_dataset')
                filter_id = item.get('filter_id')
                single_relation_dict['field_initiator_id'] = field_initiator_id
                single_relation_dict['field_responder_id'] = field_responder_id
                single_relation_dict['is_same_dataset'] = is_same_dataset
                single_relation_dict['id'] = item.get('filter_relation_id')
                relations.append(single_relation_dict)

            single_responder_dict['chart_responder_id'] = key_chart_responder_id
            single_responder_dict['related_dataset_id'] = dataset_id
            single_responder_dict['relations'] = relations
            single_responder_dict['id'] = filter_id
            related_list.append(single_responder_dict)

        initiator_chart = chart_repository.get_chart_by_chart_id(key_chart_initiator_id)
        single_data_dict['chart_initiator_id'] = key_chart_initiator_id
        single_data_dict['dataset_id'] = initiator_chart.get("source")
        single_data_dict['related_list'] = related_list
        data_list.append(single_data_dict)
    return data_list


def get_structure_linkages_data(chart_relation_tree_data: Dict[Any, Any]) -> List[Any]:
    """
    获取层级数据
    :param chart_relation_tree_data:
    :return:
    """
    data_list = list()
    if not chart_relation_tree_data:
        return data_list
    for (key_chart_initiator_id, value_chart_initiator) in chart_relation_tree_data.items():
        single_data_dict = dict()
        related_list = list()
        dataset_id = ''
        for (key_chart_responder_id, value_chart_responder) in value_chart_initiator.items():
            single_responder_dict = dict()
            relations = list()
            selector_id = None
            for item in value_chart_responder:
                single_relation_dict = dict()
                dataset_id = item.get('dataset_id')
                field_initiator_id = item.get('field_initiator_id')
                field_responder_id = item.get('field_responder_id')
                selector_id = item.get('selector_id')

                is_same_dataset = item.get('is_same_dataset')
                single_relation_dict['field_initiator_id'] = field_initiator_id
                single_relation_dict['field_responder_id'] = field_responder_id
                single_relation_dict['is_same_dataset'] = is_same_dataset
                single_relation_dict['id'] = item.get('selector_field_id')
                relations.append(single_relation_dict)

            single_responder_dict['chart_responder_id'] = key_chart_responder_id
            single_responder_dict['related_dataset_id'] = dataset_id
            single_responder_dict['relations'] = relations
            single_responder_dict['id'] = selector_id
            related_list.append(single_responder_dict)

        initiator_chart = chart_repository.get_chart_by_chart_id(key_chart_initiator_id)
        single_data_dict['chart_initiator_id'] = key_chart_initiator_id
        single_data_dict['dataset_id'] = initiator_chart.get("source")
        single_data_dict['related_list'] = related_list
        data_list.append(single_data_dict)
    return data_list


def get_jump_relations(dashboard_chart_id, jump_config_id, target_type=None):
    """
    组装获取普通跳转关系
    :param dashboard_chart_id:
    :param jump_config_id:
    :param target_type:
    :return:
    """
    results = list()
    jump_relation = metadata_repository.get_jump_relaitons(dashboard_chart_id, jump_config_id)
    if jump_relation:
        for item in jump_relation:
            # if not item.get('dashboard_filter_id') and target_type != 'url':
            #     continue
            single_relation = dict()
            field_initiator_id = item.get('dataset_field_id', '')
            single_relation['relation_type'] = 0
            single_relation['field_initiator_id'] = field_initiator_id
            single_relation['dashboard_filter_id'] = item.get('dashboard_filter_id', '')
            single_relation['global_params_id'] = item.get('global_params_id', '')
            single_relation['initiator_alias'] = dataset_service.get_initiator_alias(field_initiator_id)
            single_relation['filter_type'] = item.get('filter_type', 0)
            single_relation['dashboard_filter_alias'] = _get_dashboard_var_filter(item.get('dashboard_filter_id', ''))
            _get_field_relation_info(single_relation, item)
            results.append(single_relation)
    return results


def _get_dashboard_var_filter(id):
    """
    :param id:
    :return:
    """
    if not id:
        return ""
    field = repository.get_data("dashboard_filter_vars_relation_field", conditions={"id": id}, fields=["var_name"])
    return field.get("var_name") if field else ""


def _get_field_relation_info(single_relation, item, is_released=False):
    """
    增加跳转关联对象字段信息
    :param single_relation:
    :param item:
    :return:
    """
    if not item:
        return
    single_relation['relation_field_type'] = item.get('relation_field_type')
    single_relation['target_filter_id'] = item.get('target_filter_id', '')
    single_relation['target_filter_field_id'] = item.get('target_filter_field_id', '')
    # 跳转升级的历史数据获取
    global_params_name = item.get('global_params_name')
    global_params_alias_name = item.get('global_params_alias_name')
    if not global_params_name:
        if is_released:
            global_params_name, global_params_alias_name = get_released_global_params_name(item)
        else:
            global_params_name, global_params_alias_name = get_unreleased_global_params_name(single_relation)
    single_relation['global_params_name'] = global_params_name
    single_relation['global_params_alias_name'] = global_params_alias_name


def get_unreleased_global_params_name(single_relation):
    # 获取设计时的全局参数
    global_params = get_global_params(single_relation.get('global_params_id', ''))
    global_params_name = global_params.get('name', '')
    global_params_alias_name = global_params.get('alias_name', '')
    return global_params_name, global_params_alias_name


def get_released_global_params_name(item):
    # 获取运行时的全局参数
    released_target_global_params = item.get('target_global_params') or {}
    global_params_name = released_target_global_params.get('name', '')
    global_params_alias_name = released_target_global_params.get('alias_name', '')
    return global_params_name, global_params_alias_name


def get_global_params(global_params_id):
    # 运行时的全局参数也会获取的设计时的数据，原因是数据与被跳报告有关，起跳报告发布时，被跳报告不一定也发布
    if not global_params_id:
        return {}
    else:
        return repository.get_data(
            "dashboard_jump_global_params", conditions={"id": global_params_id}, fields=[], multi_row=False
            ) or {}


def get_var_jump_relations(jump_config_id):
    """
    组装获取普通跳转关系
    :param jump_config_id:
    :return:
    """
    results = list()
    jump_relation = metadata_repository.get_var_jump_relaitons(jump_config_id)
    for item in jump_relation:
        # if not item.get('dashboard_filter_id'):
        #     continue
        single_relation = dict()
        field_initiator_id = item.get('dataset_field_id', '')
        single_relation['relation_type'] = 2
        single_relation['dataset_id'] = item.get('dataset_id', '')
        single_relation['var_id'] = item.get('var_id', '')
        single_relation['field_initiator_id'] = field_initiator_id
        single_relation['global_params_id'] = item.get('global_params_id', '')
        single_relation['dashboard_filter_id'] = item.get('dashboard_filter_id', '')
        single_relation['initiator_alias'] = dataset_service.get_initiator_alias(field_initiator_id)
        single_relation['filter_type'] = item.get('filter_type', 0)
        single_relation['dashboard_filter_alias'] = _get_dashboard_var_filter(item.get('dashboard_filter_id', ''))
        _get_field_relation_info(single_relation, item)
        results.append(single_relation)
    return results


def get_fixed_var_jump_relations(dashboard_chart_id, jump_config_id):
    """
    组装获取固定值跳转关系
    :param jump_config_id:
    :return:
    """
    results = list()
    fixed_var_jump_relation = metadata_repository.get_fixed_var_jump_relaitons(dashboard_chart_id, jump_config_id)
    for item in fixed_var_jump_relation:
        # if not item.get('dashboard_filter_id'):
        #     continue
        single_relation = dict()
        single_relation['relation_type'] = 3
        single_relation['initiator_alias'] = item.get('var_name', '')
        single_relation['initiator_value'] = item.get('var_value', '')
        single_relation['field_initiator_id'] = item.get('dataset_field_id', '')
        single_relation['dashboard_filter_id'] = item.get('dashboard_filter_id', '')
        single_relation['global_params_id'] = item.get('global_params_id', '')
        single_relation['filter_type'] = item.get('filter_type', 0)
        single_relation['dashboard_filter_alias'] = _get_dashboard_var_filter(item.get('dashboard_filter_id', ''))
        _get_field_relation_info(single_relation, item)
        results.append(single_relation)
    return results


def get_filter_chart_jump_relations(dashboard_chart_id, jump_config_id):
    """
    组装获取固定值跳转关系
    :param jump_config_id:
    :return:
    """
    results = list()
    jump_relation = metadata_repository.get_filter_chart_jump_relaitons(dashboard_chart_id, jump_config_id)
    for item in jump_relation:
        # if not item.get('dashboard_filter_id'):
        #     continue
        single_relation = dict()
        field_initiator_id = item.get('dataset_field_id', '')
        single_relation['relation_type'] = 4
        single_relation['field_initiator_id'] = field_initiator_id
        single_relation['filter_chart_id'] = item.get('filter_chart_id', '')
        single_relation['date_filter_chart_flag'] = item.get('date_filter_chart_flag', '')
        single_relation['global_params_id'] = item.get('global_params_id', '')
        single_relation['initiator_alias'] = dataset_service.get_initiator_alias(field_initiator_id)
        _get_field_relation_info(single_relation, item)
        results.append(single_relation)
    return results


def get_dashboard_global_params_jump_relations(dashboard_chart_id, jump_config_id):
    """
    组装全局筛起跳
    :param jump_config_id:
    :return:
    """
    results = list()
    jump_relation = metadata_repository.get_global_params_jump_relaitons(dashboard_chart_id, jump_config_id)
    for item in jump_relation:
        # if not item.get('dashboard_filter_id'):
        #     continue
        single_relation = dict()
        single_relation['relation_type'] = 5
        single_relation['initiator_global_params_id'] = item.get('initiator_global_params_id', '')
        single_relation['global_params_id'] = item.get('global_params_id', '')
        _get_field_relation_info(single_relation, item)
        results.append(single_relation)
    return results



def get_param_jump_relations(dashboard_chart_id, source_id):
    """
    组装获取参数跳转关系
    :param dashboard_chart_id:
    :param source_id:
    :return:
    """
    results = list()
    param_jump_relation = metadata_repository.get_param_jump_relaitons(dashboard_chart_id, source_id)
    if param_jump_relation:
        for item in param_jump_relation:
            # if not item.get('dashboard_filter_id'):
            #     continue
            single_relation = dict()
            field_initiator_id = item.get('param_dataset_field_id', '')
            single_relation['relation_type'] = 1
            single_relation['field_initiator_id'] = field_initiator_id
            single_relation['dashboard_filter_id'] = item.get('dashboard_filter_id', '')
            single_relation['global_params_id'] = item.get('global_params_id', '')
            single_relation['initiator_alias'] = dataset_service.get_initiator_alias(field_initiator_id)
            single_relation['filter_type'] = item.get('filter_type', 0)
            single_relation['dashboard_filter_alias'] = _get_dashboard_var_filter(item.get('dashboard_filter_id', ''))
            _get_field_relation_info(single_relation, item)
            results.append(single_relation)
    return results


def get_dashboard_name(dashboard_id):
    """
    获取报告名称
    :param dashboard_id:
    :return:
    """
    dashboard_data = repository.get_data('dashboard', {'id': dashboard_id}, ['name'])
    return dashboard_data.get('name') if dashboard_data else ''


def batch_get_dashboard_name(dashboard_id_list):
    """
    批量获取报告名称
    :param dashboard_id_list:
    :return:
    """
    if not dashboard_id_list:
        return {}
    query_data = metadata_repository.batch_get_dashboard_name(dashboard_id_list)
    if not query_data:
        return {}
    return {item.get("id"): item.get("name") for item in query_data}


def get_structure_redirect_data(dashboard_id: str) -> List[Any]:
    """
    获取跳转
    [标记]设计时返回跳转
    :return:
    """
    query_chart_jump_config_data = repository.get_data(
        'dashboard_jump_config', {'dashboard_id': dashboard_id}, multi_row=True
    )

    if not query_chart_jump_config_data:
        return []
    dashboard_id_list = [
        item.get("target", "")
        for item in query_chart_jump_config_data
        if item.get("target_type") in [
            DashboardJumpType.Dashboard.value,
            DashboardJumpType.Child_file.value,
            DashboardJumpType.SelfService.value
        ] and item.get("target")
    ]
    dashboard_name_dict = batch_get_dashboard_name(dashboard_id_list) if dashboard_id_list else {}
    chart_redirect_dict = dict()
    for single_chart_jump_config in query_chart_jump_config_data:
        relations = list()
        dashboard_chart_id = single_chart_jump_config.get('dashboard_chart_id', '')
        source_id = single_chart_jump_config.get('source_id', '')
        source_type = single_chart_jump_config.get('source_type', 0)
        jump_config_id = single_chart_jump_config.get('id', '')

        # 初始化结构
        if dashboard_chart_id and dashboard_chart_id not in chart_redirect_dict:
            chart_redirect_dict.update({dashboard_chart_id: []})

        single_field_data_dict = dict()
        single_field_data_dict['id'] = single_chart_jump_config.get('id')
        single_field_data_dict['title'] = single_chart_jump_config.get('title')
        single_field_data_dict['dataset_field_id'] = source_id
        single_field_data_dict['has_token'] = single_chart_jump_config.get('has_token')
        single_field_data_dict['with_params'] = single_chart_jump_config.get('with_params')
        single_field_data_dict['target'] = single_chart_jump_config.get('target')
        single_field_data_dict['target_type'] = single_chart_jump_config.get('target_type')
        single_field_data_dict['open_way'] = single_chart_jump_config.get('open_way')
        single_field_data_dict['sort'] = single_chart_jump_config.get('sort', 0)
        single_field_data_dict['unbound_related_dims'] = single_chart_jump_config.get('unbound_related_dims')
        single_field_data_dict['redirect_window_config'] = single_chart_jump_config.get('redirect_window_config')
        single_field_data_dict['type'] = source_type
        condition_jump = single_chart_jump_config.get('condition_jump') or []
        single_field_data_dict['condition_jump'] = pre_deal_with_section_data(condition_jump)
        single_field_data_dict['status'] = single_chart_jump_config.get('status')
        single_field_data_dict['is_default'] = single_chart_jump_config.get('is_default')

        # 被跳转的是报告则需要报告名称
        dashboard_name = ''
        if single_chart_jump_config.get('target_type') in [
            DashboardJumpType.Dashboard.value,
            DashboardJumpType.Child_file.value,
            DashboardJumpType.SelfService.value
        ] and single_chart_jump_config.get('target'):
            dashboard_name = (
                dashboard_name_dict.get(single_chart_jump_config.get("target"), "") if dashboard_name_dict else ""
            )
        single_field_data_dict['dashboard_name'] = dashboard_name

        # 获取设计时组件的字段跳转关系
        jump_relations = get_jump_relations(
            dashboard_chart_id, jump_config_id, single_chart_jump_config.get('target_type')
        )
        relations = [*relations, *jump_relations]

        # 获取变量跳转关系
        var_jump_relations = get_var_jump_relations(jump_config_id)
        relations = [*relations, *var_jump_relations]

        # 获取参数跳转关系
        param_jump_relations = get_param_jump_relations(dashboard_chart_id, source_id)
        relations = [*relations, *param_jump_relations]

        # 获取固定值跳转关系
        param_jump_relations = get_fixed_var_jump_relations(dashboard_chart_id, jump_config_id)
        relations = [*relations, *param_jump_relations]

        # 获取筛选器跳转关系
        filter_chart_jump_relations = get_filter_chart_jump_relations(dashboard_chart_id, jump_config_id)
        relations = [*relations, *filter_chart_jump_relations]

        # 获取全局筛选起跳的跳转关系
        dashboard_global_params_jump_relations = get_dashboard_global_params_jump_relations(dashboard_chart_id, jump_config_id)
        relations = [*relations, *dashboard_global_params_jump_relations]

        single_field_data_dict['relations'] = relations
        chart_redirect_dict[dashboard_chart_id].append(single_field_data_dict)

    # 转换结构
    redirects = list()
    for chart_id, chart_redirect_list in chart_redirect_dict.items():
        normal_redirects, complex_redirects = [], []
        for single_redirect in chart_redirect_list:
            if single_redirect.get('type') == JumpConfigSourceType.Title.value:
                if 'dataset_field_id' in single_redirect:
                    single_redirect.pop('dataset_field_id')
                complex_redirects.append(single_redirect)
            else:
                normal_redirects.append(single_redirect)
        normal_redirects =_sort_redirect_by_jump_config_sort(normal_redirects)
        complex_redirects =_sort_redirect_by_jump_config_sort(complex_redirects)
        redirects.append(
            {'chart_id': chart_id, 'chart_redirect': normal_redirects, 'complex_redirect': complex_redirects}
        )
    return redirects


def _sort_redirect_by_jump_config_sort(redirect_list):
    # 根据jump_config的sort字段排序返回的顺序
    return sorted(redirect_list, key=lambda jump_config: jump_config.get('sort', 0))


def _extend_chart_data(chart_data, data_key, container, extend_method):
    data = chart_data.get(data_key)
    if data:
        data = json.loads(data)
        if data and extend_method == 'append':
            container.append(data)
        if data and extend_method == 'extend':
            container.extend(data)
    return container


def regroup_chart_data(
    released_chart_data_list: list,
) -> Dict[str, Union[Dict[str, Dict[str, Union[str, int, None]]], List[List[Any]]]]:
    """
    重组单图数据
    :param released_chart_data_list:
    :return:
    """
    results = dict()
    snapshot_chart_dict = dict()
    component_filters = list()
    redirects = list()
    chart_visible_triggers = list()
    chart_params_jump = list()
    chart_linkages = []
    chart_filters = []
    var_relations = list()

    for item in released_chart_data_list:
        chart_id = item.get('id', '')
        snapshot_chart_dict.update({chart_id: item})
        component_filters = _extend_chart_data(item, "component_filter", component_filters, "append")
        redirects = _extend_chart_data(item, "jump", redirects, "append")
        chart_visible_triggers = _extend_chart_data(item, "chart_visible_triggers", chart_visible_triggers, "extend")
        chart_params_jump = _extend_chart_data(item, "chart_params_jump", chart_params_jump, "append")
        chart_linkages = _extend_chart_data(item, "chart_linkage", chart_linkages, "extend")
        chart_filters = _extend_chart_data(item, "chart_filter", chart_filters, "extend")
        var_relations = _extend_chart_data(item, "var_relations", var_relations, "extend")
    results.update(
        {
            'snapshot_chart_dict': snapshot_chart_dict,
            'component_filters': component_filters,
            'redirects': redirects,
            'chart_params_jump': chart_params_jump,
            "chart_linkage": chart_linkages,
            "chart_filter": chart_filters,
            "var_relations": var_relations,
            "chart_visible_triggers": chart_visible_triggers,
        }
    )
    return results


def _get_single_chart_params_jump(single_jump, chart_params_jump_dict):
    for single_single_jump in single_jump:
        dashboard_chart_id = single_single_jump.get('dashboard_chart_id')
        source_id = single_single_jump.get('source_id') or single_single_jump.get('chart_dataset_field_id')
        param_dataset_field_id = single_single_jump.get('param_dataset_field_id')

        if dashboard_chart_id not in chart_params_jump_dict:
            chart_params_jump_dict[dashboard_chart_id] = defaultdict(dict)

        if source_id not in chart_params_jump_dict[dashboard_chart_id]:
            chart_params_jump_dict[dashboard_chart_id][source_id] = defaultdict(dict)

        if param_dataset_field_id not in chart_params_jump_dict[dashboard_chart_id][source_id]:
            chart_params_jump_dict[dashboard_chart_id][source_id][param_dataset_field_id] = defaultdict(dict)

        chart_params_jump_dict[dashboard_chart_id][source_id][param_dataset_field_id] = single_single_jump
    return chart_params_jump_dict


def turn_chart_params_jump_list_to_dict(chart_params_jump: list):
    """
    参数跳转数据转换为嵌套dict结构
    :param chart_params_jump: 快照表中原始的参数跳转关系数据
    :return:
    """
    chart_params_jump_dict = defaultdict(dict)
    if not chart_params_jump or not len(chart_params_jump):
        return chart_params_jump_dict
    for single_jump in chart_params_jump:
        if not len(single_jump):
            continue
        chart_params_jump_dict = _get_single_chart_params_jump(single_jump, chart_params_jump_dict)
    return chart_params_jump_dict


def _get_component_filter_related_data(snapshot_id, single_chart_filters, snapshot_chart_dict):
    related_list = list()
    initiator_dataset_id = ''
    chart_initiator_id = ''
    for single_relation in single_chart_filters:
        single_relation_dict = dict()
        related_dataset_id = single_relation.get('dataset_id')
        if not related_dataset_id:
            continue
        chart_initiator_id = single_relation.get('chart_initiator_id')
        is_same_dataset = single_relation.get('is_same_dataset')
        relation = single_relation.get('relation')
        # 获取initiator单图关联的来源数据集
        if snapshot_chart_dict.get(chart_initiator_id):
            single_snapshot_dict = snapshot_chart_dict.get(chart_initiator_id)
            initiator_dataset_id = single_snapshot_dict.get('source', '')
        if not initiator_dataset_id:
            initiator_chart_data = get_snapshot_chart_data_by_fields(snapshot_id, chart_initiator_id, ['source'])
            initiator_dataset_id = initiator_chart_data.get('source', '')

        single_relation_dict['chart_responder_id'] = single_relation.get('chart_responder_id')
        single_relation_dict['related_dataset_id'] = related_dataset_id
        for item in relation:
            item['is_same_dataset'] = is_same_dataset
        single_relation_dict['relations'] = relation
        related_list.append(single_relation_dict)
    return chart_initiator_id, initiator_dataset_id, related_list


def get_release_structure_filter_data(orig_component_filters_data: list, snapshot_id: str, snapshot_chart_dict: dict):
    """
    构造component_filters数据
    :param orig_component_filters_data:
    :param snapshot_id:
    :param snapshot_chart_dict:
    :return:
    """
    component_filters_list = list()
    if not orig_component_filters_data:
        return component_filters_list
    for single_chart_filters in orig_component_filters_data:
        if not single_chart_filters:
            continue
        if not isinstance(single_chart_filters, list):
            single_chart_filters = json.loads(single_chart_filters)
        single_filter_dict = dict()
        if not single_chart_filters:
            continue
        chart_initiator_id, initiator_dataset_id, related_list = _get_component_filter_related_data(
            snapshot_id, single_chart_filters, snapshot_chart_dict
        )
        single_filter_dict['chart_initiator_id'] = chart_initiator_id
        single_filter_dict['dataset_id'] = initiator_dataset_id
        single_filter_dict['related_list'] = related_list
        component_filters_list.append(single_filter_dict)

    return component_filters_list


def fillup_fields_in_list(orig_data: list, fillup_fields: dict):
    """
    针对一些发布态数据缺少的字段，设置字段默认值，保持和预览态元数据一致
    :param orig_data:
    :param fillup_fields:
    :return:
    """
    if not orig_data or not isinstance(orig_data, list) or not fillup_fields:
        return orig_data
    for item in orig_data:
        if not item or not isinstance(item, dict):
            continue
        for fillup_field, field_default_value in fillup_fields.items():
            if fillup_field not in item:
                item[fillup_field] = field_default_value
    return orig_data


def get_release_structure_linkages_data(
    orig_linkage_data: str, snapshot_chart_dict: Dict[str, Dict[str, Union[str, int, None]]]
) -> List[Any]:
    """
    重新构造联动数据
    :param orig_linkage_data:
    :param snapshot_chart_dict:
    :return:
    """
    linkage_data_list = list()
    if not orig_linkage_data:
        return linkage_data_list
    if not isinstance(orig_linkage_data, dict):
        orig_linkage_data = json.loads(orig_linkage_data)
    for key, value in orig_linkage_data.items():
        single_linkage_dict = dict()
        related_list = list()
        single_linkage_dict['chart_initiator_id'] = key
        source = ''
        if snapshot_chart_dict and key in list(snapshot_chart_dict.keys()):
            single_snapshot_chart = snapshot_chart_dict[key]
            source = single_snapshot_chart.get('source', '')
        single_linkage_dict['dataset_id'] = source if source else ''
        for item in value:
            field_relations_list = list()
            fields = item['fields']
            for field_item in fields:
                field_relations_list.append(
                    {
                        'field_initiator_id': field_item.get('initiator_id'),
                        'field_responder_id': field_item.get('responder_id'),
                        'is_same_dataset': item.get('is_same_dataset'),
                    }
                )
            related_list.append(
                {
                    'chart_responder_id': item.get('chart_id'),
                    'related_dataset_id': item.get('dataset_id'),
                    'relations': field_relations_list,
                }
            )
        single_linkage_dict['related_list'] = related_list
        linkage_data_list.append(single_linkage_dict)
    return linkage_data_list


def get_released_param_jump_relations(chart_initiator_id, source_initiator_id, orig_chart_params_jump_data):
    """
    组装参数跳转关系数据
    :param chart_initiator_id: 起跳方的单图id
    :param source_initiator_id: 起跳方的字段id
    :param orig_chart_params_jump_data: 嵌套dict结构的参数跳转关系数据
    :return:
    """
    result = []
    all_chart_params_relations = [one for ones in orig_chart_params_jump_data for one in ones]

    for chart_params_relation in all_chart_params_relations:
        if not (
                chart_initiator_id == chart_params_relation.get('dashboard_chart_id') and
                source_initiator_id == chart_params_relation.get('source_id')
        ):
            continue
        relation = dict()
        param_dataset_field_id = chart_params_relation.get('param_dataset_field_id', '')
        relation['relation_type'] = 1
        relation['field_initiator_id'] = param_dataset_field_id
        relation['dashboard_filter_id'] = chart_params_relation.get('dashboard_filter_id', '')
        relation['global_params_id'] = chart_params_relation.get('global_params_id', '')

        # initiator_alias
        relation['initiator_alias'] = dataset_service.get_initiator_alias(
            param_dataset_field_id)
        relation['filter_type'] = relation.get('filter_type', 0)
        relation['dashboard_filter_alias'] = _get_dashboard_var_filter(chart_params_relation.get('dashboard_filter_id', ''))

        _get_field_relation_info(relation, chart_params_relation, is_released=True)
        result.append(relation)

    return result


def get_released_normal_dim_jump_relations(jump_relations):
    result = []
    for item in jump_relations:
        jump_relations_dict = dict()
        jump_relations_dict['relation_type'] = 0
        jump_relations_dict['initiator_alias'] = dataset_service.get_initiator_alias(item.get('dataset_field_id', ''))
        jump_relations_dict['field_initiator_id'] = item.get('dataset_field_id', '')
        jump_relations_dict['dashboard_filter_id'] = item.get('dashboard_filter_id', '')
        jump_relations_dict['date_filter_chart_flag'] = item.get('date_filter_chart_flag', '')
        jump_relations_dict['filter_type'] = item.get('filter_type', 0)
        jump_relations_dict['global_params_id'] = item.get('global_params_id', '')
        jump_relations_dict['dashboard_filter_alias'] = _get_dashboard_var_filter(item.get('dashboard_filter_id', ''))
        _get_field_relation_info(jump_relations_dict, item, is_released=True)
        result.append(jump_relations_dict)
    return result


def get_released_fixed_value_relations(fixed_var_jump_relations):
    result = []
    for item in fixed_var_jump_relations:
        var_jump_relation_dict = dict()
        var_jump_relation_dict['relation_type'] = 3
        var_jump_relation_dict['initiator_alias'] = item.get('var_name', '')
        var_jump_relation_dict['initiator_value'] = item.get('var_value')
        var_jump_relation_dict['field_initiator_id'] = item.get('dataset_field_id', '')
        var_jump_relation_dict['filter_type'] = item.get('filter_type', 0)
        var_jump_relation_dict['dashboard_filter_id'] = item.get('dashboard_filter_id', '')
        var_jump_relation_dict['global_params_id'] = item.get('global_params_id', '')
        var_jump_relation_dict['dashboard_filter_alias'] = _get_dashboard_var_filter(item.get('dashboard_filter_id', ''))
        _get_field_relation_info(var_jump_relation_dict, item, is_released=True)
        result.append(var_jump_relation_dict)
    return result


def get_released_filter_chart_relations(filter_chart_relations):
    result = []
    for item in filter_chart_relations:
        relation_dict = dict()
        param_dataset_field_id = item.get('dataset_field_id', '')
        relation_dict['relation_type'] = 4
        relation_dict['initiator_alias'] = dataset_service.get_initiator_alias(param_dataset_field_id)
        relation_dict['field_initiator_id'] = param_dataset_field_id
        relation_dict['filter_chart_id'] = item.get('filter_chart_id', '')
        relation_dict['global_params_id'] = item.get('global_params_id', '')
        _get_field_relation_info(relation_dict, item, is_released=True)
        result.append(relation_dict)
    return result


def get_released_dashbaord_global_params_relations(relations):
    result = []
    for item in relations:
        relation_dict = dict()
        relation_dict['relation_type'] = 5
        relation_dict['initiator_global_params_id'] = item.get('initiator_global_params_id', '')
        relation_dict['global_params_id'] = item.get('global_params_id', '')
        _get_field_relation_info(relation_dict, item, is_released=True)
        result.append(relation_dict)
    return result


def get_released_var_jump_relations(var_jump_relations):

    """
    :param var_jump_relations:
    :return:
    """
    result = []
    for item in var_jump_relations:
        var_jump_relation_dict = dict()
        var_jump_relation_dict['relation_type'] = 2
        var_jump_relation_dict['initiator_alias'] = dataset_service.get_initiator_alias(
            item.get('dataset_field_id', '')
        )
        var_jump_relation_dict['var_id'] = item.get('var_id')
        var_jump_relation_dict['field_initiator_id'] = item.get('dataset_field_id', '')
        var_jump_relation_dict['dataset_id'] = item.get('dataset_id', '')
        var_jump_relation_dict['dashboard_filter_id'] = item.get('dashboard_filter_id', '')
        var_jump_relation_dict['filter_type'] = item.get('filter_type', 0)
        var_jump_relation_dict['global_params_id'] = item.get('global_params_id', '')
        var_jump_relation_dict['dashboard_filter_alias'] = _get_dashboard_var_filter(item.get('dashboard_filter_id', ''))
        _get_field_relation_info(var_jump_relation_dict, item, is_released=True)
        result.append(var_jump_relation_dict)
    return result


class RedirectRelationKeys:
    chart_params_key = 'chart_params_jump'
    other_keys = [
        'jump_relation', # 维度跳转
        'fixed_var_jump_relation', # 固定值跳转
        'filter_chart_jump_relation', # 筛选跳转
        'global_params_jump_relation', # 全局参数跳转
        'var_jump_relation', # 变量跳转
    ]

    @staticmethod
    def get_except_params_jump_relation(relation):
        # 获取除开参数跳转以外的所有跳转
        except_params_jump_relation = [relation.get(key) or [] for key in __class__.other_keys] # noqa
        return except_params_jump_relation


def _get_single_redirects(single_redirects_data, orig_chart_params_jump_data, dashboard_name_map):
    # [标记]运行时返回跳转
    single_redirect_list = list()
    chart_initiator_id = ''
    for single_relation in single_redirects_data:
        if not single_relation:
            continue
        if single_relation.get('status') == DashboardJumpConfigStatus.Invalid.value:
            continue
        single_relation_dict = dict()
        jump_relations_list = list()
        chart_initiator_id = single_relation.get('dashboard_chart_id', '')
        source_initiator_id = single_relation.get('source_id', '') or single_relation.get('dataset_field_id', '')
        (
            jump_relation, fixed_var_relation,
            filter_chart_jump_relation, global_params_jump_relation,
            var_jump_relation
        ) = RedirectRelationKeys.get_except_params_jump_relation(single_relation)
        # jump_relation = single_relation.get('jump_relation', [])
        # fixed_var_relation = single_relation.get('fixed_var_jump_relation', [])
        # filter_chart_jump_relation = single_relation.get('filter_chart_jump_relation', [])
        # global_params_jump_relation = single_relation.get('global_params_jump_relation', [])
        # var_jump_relation = single_relation.get('var_jump_relation', [])
        # 组装维度跳转关系
        normal_dim_jump_relations_list = get_released_normal_dim_jump_relations(jump_relation)
        # 组装参数跳转关系
        param_jump_relations_list = get_released_param_jump_relations(
            chart_initiator_id, source_initiator_id, orig_chart_params_jump_data
        )
        # 组装固定值跳转关系
        fixed_var_jump_relations_list = get_released_fixed_value_relations(fixed_var_relation)
        # 组装筛选器跳转关系
        filter_chart_jump_relations_list = get_released_filter_chart_relations(filter_chart_jump_relation)
        # 组装变量跳转关系
        var_jump_relations_list = get_released_var_jump_relations(var_jump_relation)
        # 组装全局筛选作为起跳关系
        global_params_jump_relations_list = get_released_dashbaord_global_params_relations(global_params_jump_relation)

        jump_relations_list.extend(normal_dim_jump_relations_list)
        jump_relations_list.extend(param_jump_relations_list)
        jump_relations_list.extend(fixed_var_jump_relations_list)
        jump_relations_list.extend(filter_chart_jump_relations_list)
        jump_relations_list.extend(var_jump_relations_list)
        jump_relations_list.extend(global_params_jump_relations_list)

        # dashboard_name
        dashboard_name = ''
        if single_relation.get('target_type') in [
            DashboardJumpType.Dashboard.value,
            DashboardJumpType.Child_file.value,
            DashboardJumpType.SelfService.value
        ] and single_relation.get('target'):
            dashboard_name = dashboard_name_map.get(single_relation.get('target'), '')
        source_type = single_relation.get('source_type')
        if source_type is None:
            source_type = single_relation.get('dataset_field_type')

        single_relation_dict['id'] = single_relation.get('id', "")
        single_relation_dict['title'] = single_relation.get('title', "")
        single_relation_dict['status'] = single_relation.get('status', "")
        single_relation_dict['type'] = source_type
        single_relation_dict['has_token'] = single_relation.get('has_token', 0)
        single_relation_dict['with_params'] = single_relation.get('with_params', 0)
        single_relation_dict['target'] = single_relation.get('target', '')
        single_relation_dict['target_type'] = single_relation.get('target_type', '')
        single_relation_dict['open_way'] = single_relation.get('open_way', 0)
        single_relation_dict['dataset_field_id'] = source_initiator_id
        single_relation_dict['dashboard_name'] = dashboard_name
        single_relation_dict['relations'] = jump_relations_list
        single_relation_dict['unbound_related_dims'] = single_relation.get('unbound_related_dims', "")
        single_relation_dict['redirect_window_config'] = single_relation.get('redirect_window_config', "")
        single_relation_dict['sort'] = single_relation.get('sort', 0)
        single_relation_dict['is_default'] = single_relation.get('is_default')
        condition_jump = single_relation.get('condition_jump') or []
        single_relation_dict['condition_jump'] = pre_deal_with_section_data(condition_jump)

        single_redirect_list.append(single_relation_dict)

    normal_redirects, complex_redirects = [], []
    for item in single_redirect_list:
        if item.get('type') == JumpConfigSourceType.Title.value:
            if 'dataset_field_id' in item:
                item.pop('dataset_field_id')
            complex_redirects.append(item)
        else:
            normal_redirects.append(item)
    normal_redirects = _sort_redirect_by_jump_config_sort(normal_redirects)
    complex_redirects = _sort_redirect_by_jump_config_sort(complex_redirects)
    return chart_initiator_id, normal_redirects, complex_redirects


def get_release_structure_redirects_data(orig_redirects_data: list, orig_chart_params_jump_data: dict):
    """
    构造redirects数据
    :param orig_redirects_data: 快照表的跳转关系数据
    :param orig_chart_params_jump_data: 嵌套dict结构的参数跳转关系数据
    :return:
    """
    redirects_data = list()
    if not orig_redirects_data:
        return redirects_data

    processed_redirects_data = []
    dashboard_ids = set()

    # preprocess
    for single_redirects_data in orig_redirects_data:
        if not single_redirects_data:
            continue
        if not isinstance(single_redirects_data, list):
            single_redirects_data = json.loads(single_redirects_data)
        processed_redirects_data.append(single_redirects_data)
        for single_relation in single_redirects_data:
            if not single_relation:
                continue
            if single_relation.get('status') == DashboardJumpConfigStatus.Invalid.value:
                continue
            if single_relation.get('target_type') in ['dashboard'] and single_relation.get('target'):
                dashboard_ids.add(single_relation.get('target'))

    dashboard_name_map = batch_get_dashboard_name(list(dashboard_ids))

    for single_redirects_data in processed_redirects_data:
        single_redirect_dict = dict()
        # 运行时获取每个组件的跳转配置
        chart_initiator_id, normal_redirects, complex_redirects = _get_single_redirects(
            single_redirects_data, orig_chart_params_jump_data, dashboard_name_map
        )
        if chart_initiator_id:
            single_redirect_dict['chart_id'] = chart_initiator_id
            single_redirect_dict['chart_redirect'] = normal_redirects
            single_redirect_dict['complex_redirect'] = complex_redirects
        if single_redirect_dict:
            redirects_data.append(single_redirect_dict)
    return redirects_data


def get_field_data_by_fields(
    data: Dict[str, Union[str, int, None]], fields: List[Union[str, Tuple[str, str]]]
) -> Dict[str, Union[str, int, None]]:
    """
    获取field字段数据
    :param data:
    :param fields:
    :return:
    """
    result_dict = dict()
    for single_field in fields:
        real_field, alias_field = single_field, single_field
        if isinstance(single_field, tuple) and len(single_field) == 2:
            real_field, alias_field = single_field[0], single_field[1]
        if real_field in list(data.keys()):
            result_dict.update({alias_field: data[real_field]})
    return result_dict


def _loads_key_for_section_data(loads_keys: List[str], item: Dict[str, Union[str, int, None]]) -> None:
    """
    json序列化指定字段
    :param loads_keys:
    :param item:
    :return:
    """
    if not loads_keys:
        return
    for single_load_key in loads_keys:
        if (
            single_load_key in list(item.keys())
            and item.get(single_load_key)
            and isinstance(item.get(single_load_key), str)
        ):
            try:
                item[single_load_key] = json.loads(item.get(single_load_key))
            except Exception:
                pass


def _pop_key_for_section_data(
    pop_keys: List[str], item: Dict[str, Union[str, int, None, Dict[str, Union[int, str]]]]
) -> None:
    """
    删除指定字段
    :param pop_keys:
    :param item:
    :return:
    """
    if not pop_keys:
        return
    for single_pop_key in pop_keys:
        if single_pop_key in list(item.keys()):
            try:
                item.pop(single_pop_key)
            except Exception:
                pass


def pre_deal_with_section_data(
    data_list: Union[List[Dict[str, Union[int, str]]], str, List[Dict[str, Union[str, int, None]]]],
    loads_keys: Optional[List[str]] = None,
    pop_keys: Optional[List[str]] = None,
) -> Union[
    List[Dict[str, Union[int, str]]],
    List[Dict[str, Union[str, int, None, Dict[str, Union[int, str]]]]],
    List[Dict[str, Union[str, int, None]]],
]:
    """
    预处理
    :param data_list: 来源数据
    :param loads_keys: 需要loads处理的字段
    :param pop_keys: 需要删除的字段
    :return:
    """
    result = list()
    if not data_list:
        return data_list
    if isinstance(data_list, str):
        data_list = json.loads(data_list)
    if not loads_keys and not pop_keys:
        return data_list
    for item in data_list:
        _loads_key_for_section_data(loads_keys, item)
        _pop_key_for_section_data(pop_keys, item)
        result.append(item)
    return result


def batch_get_component_data(chart_code_list: List[str]) -> Dict[str, str]:
    """
    批量获取logic_type_code
    :param field_list:
    :param chart_code_list:
    :return:
    """
    chart_code_dict = dict()
    if not len(chart_code_list):
        return chart_code_dict
    chart_code_list = list(set(chart_code_list))
    logic_code_list = conn_custom_prefix(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).hmget(
        COMPONENT_LOGIC_CODE_KEY, chart_code_list
    )
    # 过滤logic_code_list为[None]，这种场景
    logic_code_list = list(filter(lambda x: isinstance(x, str) and x, logic_code_list))
    if not logic_code_list:
        chart_code_dict = _get_chart_code_dict_from_database(chart_code_dict)
        # 当环境是新开的环境，没有组件的话，会出现chart_code_dict=[]，这样会导致hmset一个[]报错
        # 本来元数据接口调用时候会触发组件初始化（dashboard_chart/services/metadata_service.py:379）
        # 但是调用这个动作晚于此时，所以这里加上为空[]的时候，尝试初始化组件
        if not chart_code_dict:
            result, error = component_service.init_components()
            if not result:
                logger.error(f'尝试初始化组件失败： {str(error)}')
            else:
                chart_code_dict = _get_chart_code_dict_from_database(chart_code_dict)
        conn_custom_prefix(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).hmset(COMPONENT_LOGIC_CODE_KEY, chart_code_dict)
        return chart_code_dict
    for package, logic_code in zip(chart_code_list, logic_code_list):
        chart_code_dict[package] = logic_code

    return chart_code_dict


def _get_chart_code_dict_from_database(chart_code_dict: dict):
    component_data_list = metadata_repository.batch_get_component_data()
    for item in component_data_list:
        chart_code_dict[item.get('package')] = item.get('data_logic_type_code')
    return chart_code_dict


def batch_fill_data_logic_type_code(
    chart_data_list: List[Dict[str, Union[str, int, None]]]
) -> List[Dict[str, Union[str, int, None]]]:
    """
    批量补充data_logic_type_code
    :param chart_data_list:
    :return:
    """
    # 获取chart_code列表
    chart_code_list = list()
    for n in chart_data_list:
        if n and n.get('chart_code'):
            chart_code_list.append(n.get('chart_code'))
    chart_code_dict = batch_get_component_data(chart_code_list)
    for single_chart_data in chart_data_list:
        # 补充data_logic_type_code
        if not single_chart_data.get('data_logic_type_code'):
            chart_code = single_chart_data.get('chart_code', '')
            single_chart_data['data_logic_type_code'] = chart_code_dict.get(chart_code, '')
    return chart_data_list


def get_chart_code_by_snapshot_id(snapshot_id):
    """
    从快照单图获取所有必需的chart_code
    :param snapshot_id:
    :return:
    """
    chart_code_list = list()
    if not snapshot_id:
        return chart_code_list
    query_chart_code_data = metadata_repository.batch_get_chart_code_by_snapshot_id(snapshot_id)
    if query_chart_code_data:
        for single_data in query_chart_code_data:
            chart_code = single_data.get('chart_code')
            if chart_code:
                chart_code_list.append(chart_code)
    return chart_code_list


def batch_get_global_params(global_params_ids):
    if not global_params_ids:
        return []
    return metadata_repository.batch_get_global_params(global_params_ids)


def tmp_deal_with_chart_filters(chart_filters: List[Any]) -> List[Any]:
    """
    临时处理col_name问题，后续需要迁移或改造
    :param chart_filters:
    :return:
    """
    if not chart_filters:
        return chart_filters
    for single_filter in chart_filters:
        dataset_field_id = single_filter.get("dataset_field_id")
        if not dataset_field_id:
            continue
        field_data = dataset_service.get_fields_by_field_id(['col_name'], dataset_field_id)
        for k, v in field_data.items():
            single_filter.update({k: v})
    return chart_filters

from components.analysis_time import AnalysisTimeUtils

def check_deliver_timestamp(snapshot_id):
    """
    校验报告分发时间戳
    :param snapshot_id:
    :return: true 校验正常，可以拉取缓存 false 校验不通过，需要删除缓存
    """
    conn = conn_redis()
    AnalysisTimeUtils.recode_time_node('疑似代码位置-0-1')
    dashbord_cache_instance = DashboardCache(g.code, snapshot_id, conn)
    older_deliver_timestamp = dashbord_cache_instance.get_prop(DashboardCache.prop_deliver_timestamp)
    AnalysisTimeUtils.recode_time_node('疑似代码位置-0-2')

    # 报告信息用于日志记录
    dashboard_info = dashbord_cache_instance.get_prop(DashboardCache.prop_info) or {}
    add_api_dataset_params(g, dashboard=dashboard_info)
    set_data_index(dashboard_info.get('layout'))

    cache_key = 'dmp_admin:deliver_dashboard_info'
    lastest_deliver_timestamp = conn._connection.hget(cache_key, g.code)

    # 当前租户没有被分发的情况，校验通过
    if not lastest_deliver_timestamp:
        return True
    try:
        lastest_deliver_timestamp = int(lastest_deliver_timestamp)
    except Exception:
        return True
    # 租户有被分发报告的情况下，当前报告还没设置过分发时间戳，或是没刷新过
    if not older_deliver_timestamp:
        delete_release_metadata_cache(snapshot_id)
        dashbord_cache_instance.set_prop(DashboardCache.prop_deliver_timestamp, lastest_deliver_timestamp)
        return False
    # 当前报告已经设置过分发时间戳，则对比时间
    else:
        if int(older_deliver_timestamp) != lastest_deliver_timestamp:
            delete_release_metadata_cache(snapshot_id)
            dashbord_cache_instance.set_prop(DashboardCache.prop_deliver_timestamp, lastest_deliver_timestamp)
            return False
        return True


def get_new_linkages(dashboard_id):
    """
    获取新联动数据
    :param dashboard_id:
    :return:
    """
    return metadata_repository.get_new_linkage_by_dashboard_id(dashboard_id)


def get_new_filters(dashboard_id):
    """
    获取新筛选数据
    :param dashboard_id:
    :return:
    """
    return metadata_repository.get_new_filter_by_dashboard_id(dashboard_id)


def get_penetrates(dashboard_id):
    """
    获取穿透数据
    :param dashboard_id:
    :return:
    """
    return metadata_repository.get_penetrates_by_dashboard_id(dashboard_id)


def get_metadata_etag(snapshot_id, orig_version_in_md5="", download_flag=0):
    """
    组装ETag
    ETag生成规则：元数据缓存的最后更新时间 + "_" + 组件的最后更新时间 + "_" + 数据集版本号md5值 + "_" + 下载权限 （已废弃）
    新：ETag生成规则：元数据缓存的最后更新时间 + "_" + 组件的最后更新时间 + "_" + 下载权限
    eg. 20190101125959_20190601120101_ac59075b964b0715
    :param snapshot_id:
    :param orig_version_in_md5:
    :return:
    """
    # 获取元数据最新发布时间
    cache_instance = DashboardCache(g.code, snapshot_id, conn_redis())
    released_on = cache_instance.get_released_on() if cache_instance else ""
    metadata_released_on = ""
    # 报告数据还未写入redis则取不到报告发布时间
    if released_on:
        metadata_released_on = datetime.strptime(released_on, "%Y-%m-%d %H:%M:%S").strftime("%Y%m%d%H%M%S")

    # 获取组件最后更新时间
    component_update_on = component_service.get_component_update_on()

    gray_projects = config.get('Grayscale.project_list', '')
    gray_projects = gray_projects.split(',') if gray_projects else []
    if g.code in gray_projects:
        version = metadata_released_on + "_" + str(component_update_on) + '_' + str(download_flag)
    else:
        # # 需要另外获取数据集版本号md5值的场景（已废弃）
        # versions_in_md5 = orig_version_in_md5
        # if not versions_in_md5:
        #     _, versions_in_md5 = released_dashboard_service.get_md5_version_by_snapshot_id(snapshot_id)
        # version = (
        #     metadata_released_on + "_" + str(component_update_on) + "_" + versions_in_md5 + '_' + str(download_flag)
        # )
        version = metadata_released_on + "_" + str(component_update_on) + '_' + str(download_flag)

    # 需要转换为弱etag，背景：nginx有规则，开启gzip模块和etag头有冲突，会被nginx去掉etag头，字符串前面加上W/可以跳过此规则
    weak_version = f'W/"{version}"'
    return weak_version


def compare_metadata_etag(req_etag, snapshot_id, version_in_md5="", download_flag=0):
    """
    对比ETag
    true：etag相等，false：etag不相等
    :param req_etag:
    :param snapshot_id:
    :param version_in_md5:
    :return:
    """
    # 报告发布页的第一次元数据请求，接口入参etag都为空
    if not req_etag:
        return False

    # 对比etag内容
    return req_etag == get_metadata_etag(snapshot_id, version_in_md5, download_flag)


def _recursion_rank_field(orig_data):
    """
    递归对字段排序
    :param orig_data:
    :return:
    """
    new_data = collections.OrderedDict()
    for k in sorted(orig_data.keys()):
        new_value = orig_data[k]
        if isinstance(orig_data[k], dict):
            new_value = _recursion_rank_field(orig_data[k])
        elif isinstance(orig_data[k], list):
            new_list = list()
            for i in orig_data[k]:
                new_i = _recursion_rank_field(i) if isinstance(i, dict) else i
                new_list.append(new_i)
            new_value = new_list
        new_data.update({k: new_value})
    return new_data


def get_rank_data(data):
    """
    排序后的数据
    :param data:
    :return:
    """
    if "installed_component" in data:
        data.pop("installed_component")

    ranked_json_data = collections.OrderedDict()
    for key in sorted(data.keys()):
        new_value = data[key]
        if isinstance(new_value, dict):
            new_value = _recursion_rank_field(new_value)
        ranked_json_data.update({key: new_value})
    return ranked_json_data


def compare_metadata(dashboard_id):
    """
    对比元数据
    :param dashboard_id:
    :return:
    """
    _, old_meta = get_screens_preview_metadata_v2(dashboard_id)
    new_meta = get_screens_preview_metadata(dashboard_id)

    rank_old_meta = get_rank_data(old_meta)
    rank_new_meta = get_rank_data(new_meta)

    md5_old_meta = hashlib.md5(json.dumps(rank_old_meta).encode("utf-8")).hexdigest()
    md5_new_meta = hashlib.md5(json.dumps(rank_new_meta).encode("utf-8")).hexdigest()

    result = {
        "rank_old_meta": rank_old_meta,
        "rank_new_meta": rank_new_meta,
        "md5_old_meta": md5_old_meta,
        "md5_new_meta": md5_new_meta,
    }

    if md5_old_meta != md5_new_meta:
        result["compare_status"] = False
    elif md5_old_meta == md5_new_meta:
        result["compare_status"] = True
    return result


def get_chart_children(chart_id, dashboard_id):
    fields = ['id']
    children = repository.get_column(
        'dashboard_chart',
        {'parent_chart_id': chart_id, 'dashboard_id': dashboard_id},
        fields=fields,
        order_by=[('child_rank', 'asc')],
    )
    return children if children else []


def get_released_chart_children(chart_id):
    fields = ['id']
    children = repository.get_column(
        'dashboard_released_snapshot_chart',
        {'parent_chart_id': chart_id},
        fields=fields,
        order_by=[('child_rank', 'asc')],
    )
    return children if children else []


def get_release_parent_children_map(release_chart_id_list, snapshot_id):
    parent_children_map = {}
    if not release_chart_id_list:
        return {}
    data_list = repository.get_list(
        'dashboard_released_snapshot_chart', {'parent_chart_id': release_chart_id_list, 'snapshot_id': snapshot_id}
    )
    data_list = sorted(data_list, key=lambda x: x.get('child_rank'))
    for data in data_list:
        if not parent_children_map.get(data.get('parent_chart_id')):
            parent_children_map[data.get('parent_chart_id')] = []
        parent_children_map[data.get('parent_chart_id')].append(data.get('id'))

    return parent_children_map


def get_preview_parent_children_map(chart_id_list, dashboard_id):
    parent_children_map = {}
    if not chart_id_list:
        return {}
    data_list = repository.get_list('dashboard_chart', {'parent_chart_id': chart_id_list, 'dashboard_id': dashboard_id})
    data_list = sorted(data_list, key=lambda x: x.get('child_rank'))
    for data in data_list:

        if not parent_children_map.get(data.get('parent_chart_id')):
            parent_children_map[data.get('parent_chart_id')] = []
        parent_children_map[data.get('parent_chart_id')].append(data.get('id'))

    return parent_children_map


def set_user_last_active_time(userid, account):
    # 保存用户最后活跃时间, redis需要加前缀
    setattr(g, "cache", RedisCache())
    cache = conn_redis()
    last_active_time = int(time.time())
    cache.hset(USER_LAST_ACTIVE, userid, last_active_time)
    conditions = {"id": userid, "account": account, "last_active_time <": last_active_time}
    return repository.update("user", {"last_active_time": last_active_time}, conditions)


def set_user_active_time(code):
    import app_celery

    try:
        userid = getattr(g, 'userid', '')
        account = getattr(g, 'account', '')
        if userid and account:
            app_celery.async_set_user_active_time.delay(code, userid, account)
    except Exception as e:
        logger.error("更新用户活跃时间出错, 错误信息: {}".format(str(e)))


def format_dashboard_jump_error_info(func):
    def wrapper(request, response, **kwargs):
        try:
            return func(request, response, **kwargs)
        except UserError as e:
            trans_jump_error_info(e, request)
            raise e
    return wrapper


def trans_jump_error_info(e, request):
    # 将跳转的报告不存在的信息特殊处理
    # 如果被跳转的报告不存在，就会出现这个转换的跳转错误信息
    if (
        isinstance(e, UserError)
        and e.code == 404
        and 'type=reportjump' in (request.referer or '')   # noqa
    ):
        jump_err = """跳转页面不存在，请检查页面配置。可能是由于跳转的仪表板被删除或导入时未导入跳转的仪表板导致"""
        raise UserError(message=jump_err, code=e.code)
