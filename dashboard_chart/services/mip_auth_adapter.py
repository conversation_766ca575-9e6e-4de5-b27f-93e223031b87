import copy
from typing import Dict, List


# 基础数据平台门户的授权是门户末级菜单的id(并不是真正的报表id)(真正报告的父级菜单id)
# 该类就是将技术数据平台的报告授权数据(末级菜单id)转成真正的报告授权数据
class MIPApplicationAdapter:

    def get_application_tree(self, application_id):
        # 获取门户所有的菜单
        from app_menu.services.function_service import get_function_list, generate_func_tree
        func_list = get_function_list(application_id=application_id)
        func_tree = generate_func_tree(func_list) or []
        return func_tree

    def search_node(self, result: list, funcs: List[Dict]):
        for func in funcs:
            sub = func.get('sub', [])
            if not sub:
                # 叶子节点
                result.append(func)
            else:
                # 继续找
                self.search_node(result, sub)

    def get_all_node_func_id(self, application_id):
        # 获取门户所有叶子节点的菜单id
        if not application_id:
            return []

        result = []
        func_tree = self.get_application_tree(application_id=application_id)
        self.search_node(result, func_tree)
        return result

    def get_all_node_func_id_by_all_funcs(self, funcs):
        # 获取门户所有叶子节点的菜单id
        from app_menu.services.function_service import generate_func_tree

        if not funcs:
            return []

        funcs = copy.deepcopy(funcs)
        result = []
        func_tree = generate_func_tree(funcs) or []
        self.search_node(result, func_tree)
        return result

    def convert_to_real_dashboard_data(self, permission_data: dict, node_funcs: list):
        # 将技术数据平台的叶子节点菜单转换成真实的报告权限数据
        if not permission_data:
            return {}
        real_data = {}
        ref_func_map = {f.get('id'): f for f in node_funcs}
        for func_id, auth in permission_data.items():
            dashboard_id = ref_func_map.get(func_id, {}).get('url', '')
            if dashboard_id:
                real_data[dashboard_id] = auth
        return real_data
