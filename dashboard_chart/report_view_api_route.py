import hug
from hug.authentication import authenticator
from dmplib.hug import APIWrapper
from user.services.assistant_service import SuperAppServiceOAuthService
from loguru import logger


@authenticator
# pylint: disable=W0613
def _verify_auth_key_handle(request, response, verify_user, **kwargs):
    """

    :param request:
    :param response:
    :return:
    """
    _from = request.get_param('__CLIENT_BIZ_TYPE') or request.get_param("__from")
    token = request.get_param('access_token')
    if _from not in ["cyjs", "gzt", "cyjsjc", "erpapp", "cyjsapp"] or not token:
        logger.error(f"verify error: {request.params}")
        return False
    return True


class ReportViewAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_open_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._open_route = None
        self.api.http.base_url = '/release-report-view'

    @property
    def open_route(self):
        if not self._open_route:
            # pylint: disable=E1120
            self._open_route = hug.http(api=self.api, requires=_verify_auth_key_handle(None))
        return self._open_route


api = ReportViewAPIWrapper(__name__)


@api.open_route.get('/index')
def release_report_view(request, response, **kwargs):
    """
    https://www.tapd.cn/38229611/prong/stories/view/1138229611001223384?action_entry_type=stories
    """
    auth_service = SuperAppServiceOAuthService(request, response)
    return auth_service.jump()