#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    <NAME_EMAIL> 2018/09/08
"""
import json
from collections import Iterable

from base.enums import (
    DashboardType,
    DashboardPlatforms,
    DashboardTheme,
    ChartFilterOperator,
    DataSourceOrigin,
    DashboardTypeAccessReleased,
    DashboardStatus,
    ChartNumFormulaMode,
    ChartDimFormulaMode,
    ChartMarklineMode,
    ChartMarklineFormulaMode,
    DashboardTypeStatus,
    ChartNumSubtotalFormulaMode,
    TableDownloadFrom,
    # DashboardGlobalParamsType
)

from base.models import BaseModel, QueryBaseModel, RankModel

from typing import Dict, List, Tuple, Union

from dmplib.utils.errors import UserError


class DashboardModel(BaseModel):
    __slots__ = [
        "id",
        "name",
        "layout",
        "platform",
        "background",
        "parent_id",
        "type",
        "description",
        "level_code",
        "cover",
        "is_multiple_screen",
        "type_selector",
        "biz_code",
        "theme",
        "is_show_mark_img",
        "terminal_type",
        "application_type",
        "main_external_subject_id",
        "external_subject_ids",
        "line_height",
        "smart_beauty_status",
        "dataset_id",
        "auto_play",
        "analysis_type",
        "external_url"
    ]

    fields = [
        'id',
        'theme',
        'name',
        'platform',
        'user_group_id',
        'description',
        'type',
        'parent_id',
        'level_code',
        'is_multiple_screen',
        'cover',
        'layout',
        'background',
        'type_selector',
        'created_by',
        'modified_by',
        'refresh_rate',
        'create_type',
        'new_layout_type',
        'is_show_mark_img',
        'terminal_type',
        'application_type',
        'main_external_subject_id',
        'external_subject_ids',
        'line_height',
        'smart_beauty_status',
        'dataset_id',
        'scale_mode',
        'analysis_type',
        'auto_play',
        'asset_id',
        "external_url"
    ]

    def __init__(self, **kwargs) -> None:
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.name = None
        self.layout = None
        self.background = None
        self.parent_id = None
        self.type = None
        self.level_code = None
        self.description = None
        self.cover = None
        self.is_multiple_screen = 0
        self.charts = None
        self.type_selector = 1
        self.selectors = None
        self.biz_code = None
        self.scale_mode = None
        self.platform = DashboardPlatforms.PC.value
        self.created_by = None
        self.modified_by = None
        self.refresh_rate = None
        self.theme = DashboardTheme.TechBlue.value
        self.create_type = None
        self.new_layout_type = None
        self.grid_padding = None
        self.distribute_type = None
        self.is_show_mark_img = 0
        self.terminal_type = ""
        self.application_type = 0
        self.main_external_subject_id = None
        self.external_subject_ids = ""
        self.line_height = 40
        self.smart_beauty_status = 0
        self.auto_play = 0
        self.asset_id = 0
        self.dataset_id = ""
        self.analysis_type = ""
        self.external_url = ""
        super().__init__(**kwargs)

    def rules(self) -> List[Union[Tuple[str, str, Dict[str, int]], Tuple[str, str, Dict[str, List[str]]]]]:
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append(("name", "string", {"max": 45}))
        # rules.append(("theme", "in_range", {"range": [e.value for e in DashboardTheme.__members__.values()]}))
        rules.append(("type", "in_range", {"range": [e.value for e in DashboardType.__members__.values()]}))
        rules.append(
            ("platform", "in_range", {"range": [item.value for item in DashboardPlatforms.__members__.values()]})
        )
        return rules


class ResultVariableDataModel(BaseModel):
    def __init__(self, **kwargs):
        self.data = None  # 结果
        self.marklines = []  # 辅助线
        self.conditions = []  # 查询条件
        self.msg = ''  # 查询信息
        self.msg_code = 0  # 查询结果状态
        self.execute_status = 200  # SQL执行结果状态
        self.pagination = {}  # 分页信息
        self.sql = ''  # 查询语句
        self.sql_execute_time = 0  # SQL执行时间
        self.subtotal = {}  # 小计
        super().__init__(**kwargs)


class ResultDataModel(BaseModel):
    def __init__(self, **kwargs) -> None:
        self.data = None  # 结果
        self.extra_data = []  # 结果
        self.data_last_update_time = None  # 数据最后更新时间
        self.marklines = []  # 辅助线
        self.conditions = []  # 查询条件
        self.orderby = []  # 排序顺序
        self.msg = ''  # 查询信息
        self.msg_code = 0  # 查询结果状态
        self.execute_status = 200  # SQL执行结果状态
        self.pagination = {}  # 分页信息
        self.sql = ''  # 查询语句
        self.sql_execute_time = 0  # SQL执行时间
        self.dataset_versions = {}  # 数据集版本
        self.query_structure = {}  # 查询结构
        self.subtotal = {}  # 小计
        self.data_limit = None  # 单图取数限制
        self.error_code = None  # 取数错误类型
        self.dataset_query_type = None  # 数据集类型
        super().__init__(**kwargs)


class ResultOrderbyModel(BaseModel):
    def __init__(self, **kwargs) -> None:
        self.dataset_field_id = None  # 字段ID
        self.weight = 0  # 权重
        self.field_source = None  # 字段来源
        self.sort = None  # 排序方式
        self.col_name = None  # 排序字段
        self.content = None  # 内容
        super().__init__(**kwargs)


class ColumnHeaderModel(BaseModel):
    __slots__ = ["col_name", "alias_name", "alias", "dataset_id", "field_id", "col_type", "col_value"]

    def __init__(self, **kwargs):
        """
        单图
        :param kwargs:
        """
        self.col_name = None
        self.alias_name = None
        self.alias = None
        self.dataset_id = None
        self.field_id = None
        self.col_type = None
        self.col_value = None
        super().__init__(**kwargs)


class ChartModel(BaseModel):
    __slots__ = [
        "id",
        "dashboard_id",
        "name",
        "chart_code",
        "chart_type",
        "content",
        "source",
        "dims",
        "nums",
        "zaxis",
        "comparisons",
        "map",
        "sort_method",
        "colours",
        "filters",
        "marklines",
        "refresh_rate",
        "display_item",
        "desired_value",
        "penetrates",
        "layers",
        "percentage",
        "style_type",
        "default_value",
        "layout",
        "layout_extend",
        "config",
        "chart_params",
        "column_order",
        "enable_subtotal",
        "enable_summary",
        "enable_subtotal_col",
        "enable_subtotal_col_summary",
        "enable_subtotal_row",
        "enable_subtotal_row_summary",
        "subtotal_row_summary_formula_mode",
        "is_highdata",
        "close_detail_mode",
    ]

    def __init__(self, **kwargs):
        """
        单图
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.name = None
        self.chart_code = None
        self.chart_type = None
        self.content = None
        self.source = None
        self.dims = None
        self.comparisons = None
        self.nums = None
        self.zaxis = None
        self.desires = None
        self.map = None
        self.colours = None
        self.filters = None
        self.marklines = None
        self.sort_method = None
        self.refresh_rate = None
        self.display_item = None
        self.desired_value = None
        self.penetrates = None
        self.layers = None
        self.percentage = None
        self.style_type = None
        self.default_value = None
        self.layout = None
        self.layout_extend = None
        self.position = None
        self.level_code = None
        self.content = None
        self.config = None
        self.filter_config = None
        self.data_logic_type_code = None
        self.chart_params = None
        self.page_size = None
        self.column_order = None
        self.penetrate_relation = None
        self.penetrate_filter_relation = None
        self.enable_subtotal = 0
        self.enable_summary = 0
        self.enable_subtotal_col = 0
        self.enable_subtotal_col_summary = 0
        self.enable_subtotal_row = 0
        self.enable_subtotal_row_summary = 0
        self.subtotal_row_summary_formula_mode = None
        self.is_highdata = None
        self.close_detail_mode = 0
        super().__init__(**kwargs)
        self.nums = self.merge_nums_zaxis(self.nums, self.zaxis)

    @staticmethod
    def merge_nums_zaxis(nums, zaxis):
        """
        合并nums和zaxis两个属性数据为不同类型的nums
        :return:
        """
        result = []
        if nums is not None and isinstance(nums, Iterable):
            for num in nums:
                num["axis_type"] = 0
                result.append(num)
        if zaxis is not None and isinstance(zaxis, Iterable):
            for z in zaxis:
                z["axis_type"] = 1
                result.append(z)
        return result

    def rules(self):
        rules = super().rules()
        rules.append((["id", "dashboard_id"], "string", {"max": 36}))
        return rules


class ChartDataModel(BaseModel):
    def __init__(self, **kwargs) -> None:
        self.id = None
        self.chart_code = None
        self.data_logic_type_code = None
        self.dataset_id = None
        self.dataset_field_dict = None
        self.dataset = None
        self.dashboard_id = None
        self.report_id = None
        self.conditions = None  # 联动
        self.filters = None  # 单图筛选
        self.filter_relation = 0  # 单图筛选间关系值and-0，or-1
        self.penetrate_conditions = None  # 穿透
        self.penetrate_relation = None  # 穿透关联关系
        self.penetrate_filter_conditions = None  # 穿透筛选、联动等条件参数
        self.penetrate_filter_relation = None  # 穿透筛选关联关系
        self.penetrate_var_filter_relation = None  # 变量穿透筛选关联关系
        self.filter_conditions = None  # 组件筛选
        self.dashboard_filters = None  # 报告级筛选
        self.dashboard_var_filters = None  # 报告级参数筛选
        self.dashboard_conditions = None  # 跳转
        # TODO 兼容order旧参数传递方式
        self.order = None  # 用于单图表头排序
        self.new_order = []  # 新版排序
        self.dims = None
        self.original_dims = None  # 原始维度 read_only（由于dims会在业务中发生变化 因此无法获取到原始的alias之类的信息）
        self.comparisons = None
        self.original_comparisons = None  # 原始对比维度 read_only（由于comparisons会在业务中发生变化 因此无法获取到原始的alias之类的信息）
        self.nums = None
        self.original_nums = None  # 原始对比数值 read_only（由于nums会在业务中发生变化 因此无法获取到原始的alias之类的信息）
        self.zaxis = None
        self.desires = None
        self.original_desires = None  # 原始对比数值 read_only（由于desires会在业务中发生变化 因此无法获取到原始的alias之类的信息）
        self.marklines = None
        self.display_item = None
        self.data_limit = None  # 适配个别组件取数突破1500限制, 区别于分页
        self.page_size = 0  # page_size参数(前端传入则根据传入的值 老的)
        self.pagination = ChartPaginationModel()  # pagination参数
        self.bootstrap_flag = False  # 是否是前端自主取数
        self.dataset_field_id = None  # 用于没有数据集也没有单图只有一个字段id查询数据的情况
        self.chart_filter_conditions = None  # 新版筛选条件
        self.chart_linkage_conditions = None  # 新版联动条件
        self.query_vars = None  # 数据集变量
        self.parent_id = None
        self.level_code = None
        self.enable_subtotal = 0  # 是否开启小计
        self.enable_summary = 0  # 是否开启小计列汇总(只有开启小计功能后该值才生效)
        self.enable_subtotal_col = 0  # 是否开启列小计
        self.enable_subtotal_col_summary = 0  # 是否开启列汇总
        self.enable_subtotal_row = 0  # 是否开启行小计
        self.enable_subtotal_row_summary = 0  # 是否开启行汇总
        self.subtotal_row_summary_formula_mode = 0  # 行总计计算方法
        self.subtotal_display = SubtotalDisplayModel()  # 小计展示
        self.subtotal_config = ChartSubtotalConfigModel()  # 行列小计配置
        self.serial_number_config = DownloadSerialNumberConfig()
        self.column_hidden_config = None
        self.field_sorts = None  # 单图字段排序数据
        self.label_filter_conditions = None  # 标签筛选条件
        self.aggregation = None  # 是否聚合
        self.pre_comparison = None  # 是否前置对比维度(只有有对比维度时该值才生效 默认前置)
        self.download_from = TableDownloadFrom.Preview.value  # 下载来源
        self.column_display = []  # 列展示和顺序配置
        self.indirect_query_map = {}  # 间接访问字典
        self.external_subject_ids = []  # 外部主题ID
        self.drill_conditions = None  # 下钻条件
        self.is_highdata = None
        self.self_service_conditions = None  # 自助报告跳转条件
        self.common_datetime_conditions = None  # 自助分析公共时间筛选条件
        self.keyword_values = {}  # 自助分析公共时间筛选条件
        self.extra_nums = []  # 额外单独传过来的度量
        self.filter_val_empty_mode = 0  # 是否返回空值
        self.is_subtotal_col_query = False  # 是否是列小计查询
        self.is_subtotal_query = False  # 是否是列总计查询
        self.origin_select_fields = None # 小计查询时候，原本的select
        self.export_subtotal_config = {}  # 导出小计配置
        self.layout_extend = None
        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[str, str, Dict[str, int]]]:
        rules = super().rules()
        rules.append(("dashboard_id", "string", {"max": 36, "required": True}))
        self.subtotal_display.validate()
        return rules


class ColumnDisplayModel(BaseModel):
    """
    列展示(顺序，是否展示)
    """

    __slots__ = ["dataset_id", "dataset_field_id", "col_name", "alias", "order", "group", "col_type", "is_show"]

    def __init__(self, **kwargs) -> None:
        self.dataset_id = None
        self.dataset_field_id = None
        self.col_name = None
        self.alias = None
        self.order = None
        self.group = None
        self.col_type = None
        self.is_show = 1
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(("dataset_id", "string", {"max": 36}))
        rules.append(("dataset_field_id", "string", {"max": 36}))
        rules.append(("col_name", "string"))
        rules.append(("alias", "string"))
        rules.append(("col_type", "string"))
        rules.append(('is_show', 'in_range', {'range': (0, 1)}))
        return rules


class SubtotalDisplayModel(BaseModel):
    """
    小计展示
    """

    __slots__ = ["subtotal_position", "summary_position", "subtotal_alias", "summary_alias"]

    def __init__(self, **kwargs) -> None:
        self.subtotal_position = None
        self.summary_position = None
        self.subtotal_alias = None
        self.summary_alias = None
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(("subtotal_position", "in_range", {"range": ["head", "tail"], "required": False}))
        rules.append(("summary_position", "in_range", {"range": ["head", "tail"], "required": False}))
        rules.append(("subtotal_alias", "string", {"required": False}))
        rules.append(("summary_alias", "string", {"required": False}))
        return rules


class ChartPaginationModel(BaseModel):
    """
    单图分页类
    """

    __slots__ = ["page", "page_size"]

    def __init__(self, **kwargs) -> None:
        self.page_size = None
        self.page = None
        super().__init__(**kwargs)


class PdfDownloadModel(BaseModel):
    __slots__ = ['download_id', 'dashboard_id', 'flow_id', 'status', 'download_url', 'release_url', 'user_token']

    def __init__(self, **kwargs):
        """
        pdf下载任务model
        """
        self.download_id = None
        self.dashboard_id = None
        self.flow_id = None
        self.status = 0
        self.download_url = None
        self.release_url = None
        self.user_token = None
        self.external_conditions = None
        self.export_type = 0
        super().__init__(**kwargs)


class ChartDownloadModel(BaseModel):
    def __init__(self, **kwargs):
        """
        表格下载任务model
        :param kwargs:
        """
        self.download_id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.flow_id = None
        self.status = 0
        self.download_url = None
        self.dashboard_conditions = None
        self.file_extension = 'xls'
        self.limit_per_file = 50000
        self.file_temp_dir = '/tmp/'
        self.output_zip_filename = ''
        self.output_xlsx_filename = ''
        self.page_size = 500
        self.oss_upload_dir = '/dashboard/download/chart/'
        self.output_file_type = 'xls'
        self.external_user_id = None
        self.params = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('download_id', 'string', {'max': 36}))
        rules.append(('dashboard_id', 'string', {'max': 36, 'required': True}))
        rules.append(('dashboard_chart_id', 'string', {'max': 225, 'required': True}))
        rules.append(('flow_id', 'string', {'max': 36, 'required': True}))
        rules.append(('download_url', 'string', {'max': 255}))
        return rules


class ChartFilterModel(BaseModel):
    __slots__ = ['id', 'dashboard_chart_id', 'col_name', 'operator', 'col_value', 'dataset_field_id']

    def __init__(self, **kwargs):
        """
        单图过滤模型
        :param kwargs:
        """
        self.id = None
        self.dashboard_chart_id = None
        self.col_name = None
        self.operator = None
        self.col_value = None
        self.dataset_field_id = None
        self.dataset_field = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['dashboard_chart_id', 'dataset_field_id'], 'string', {'max': 36}))
        rules.append((['col_name', 'operator'],))
        rules.append(('col_value', 'validate_col_value', {'required': False}))
        rules.append(
            (
                'operator',
                'in_range',
                {'range': [e.value for e in ChartFilterOperator.__members__.values()], 'required': False},
            )
        )
        return rules


class ComponentMenuModel(BaseModel):
    def __init__(self, **kwargs):
        """
        组件model
        :param kwargs:
        """
        self.id = None
        self.parent_id = None
        self.icon = None
        self.level_code = None
        self.children = []
        self.rank = 0
        self.name = None
        self.components = []
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('parent_id', 'string', {'max': 36, 'required': False}))
        rules.append(('icon', 'string', {'max': 225}))
        rules.append(('level_code', 'string', {'max': 50}))
        rules.append(('name', 'string', {'max': 100}))
        rules.append(('rank', "match", {'pattern': "^[0-9]+$", 'required': False}))
        return rules


class ComponentModel(BaseModel):
    def __init__(self, **kwargs):
        """
        组件model
        :param kwargs:
        """
        self.menu_id = None
        self.menu_level_code = None
        self.menu_icon = None
        self.menu_parent_id = None
        self.menu_name = None
        self.chart_type = None
        self.name = None
        self.icon = None
        self.package = None
        self.preview_image = None
        self.description = None
        self.version = None
        self.next_version = None
        self.data_logic_type_code = None
        self.status = None
        self.is_build_in = 0
        self.md5version = None
        self.md5RunTimeversion = None
        self.operation = None
        self.endpoint = None
        self.data_source_origin = 'none'
        self.indicator_rules = ''
        self.indicator_description = ''
        self.sortable = 0
        self.penetrable = 0
        self.linkage = 0
        self.can_linked = 0
        self.has_zaxis = 0
        self.has_desiredvalue = 0
        self.dims_report_redirect = 0
        self.nums_report_redirect = 0
        self.contain_css = 1
        self.contain_mapgallery = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('menu_id', 'string', {'max': 36}))
        rules.append(('menu_parent_id', 'string', {'max': 36}))
        rules.append(('menu_level_code', 'string', {'max': 100}))
        rules.append(('name', 'string', {'max': 50}))
        rules.append(('menu_name', 'string', {'max': 50}))
        rules.append(('icon', 'string', {'max': 225}))
        rules.append(('menu_icon', 'string', {'max': 225, 'required': False}))
        rules.append(('package', 'string', {'max': 50}))
        rules.append(('preview_image', 'string', {'max': 225}))
        rules.append(('endpoint', 'string', {'max': 225}))
        rules.append(('description', 'string', {'max': 1000}))
        rules.append(('allow_versions', 'string', {'max': 225}))
        rules.append(('version', 'string', {'max': 50}))
        rules.append(('next_version', 'string', {'max': 50}))
        rules.append(('status', 'in_range', {'range': [0, 1, 2, 3]}))
        rules.append(('is_build_in', 'in_range', {'range': [0, 1]}))
        rules.append(('md5version', 'string', {'max': 50}))
        rules.append(('md5RunTimeversion', 'string', {'max': 50}))
        rules.append(
            (
                'data_source_origin',
                'in_range',
                {'range': (data.value for data_key, data in DataSourceOrigin.__members__.items())},
            )
        )
        rules.append(('indicator_rules', 'string'))
        rules.append(('sortable', 'in_range', {'range': (0, 1)}))
        rules.append(('penetrable', 'in_range', {'range': (0, 1)}))
        rules.append(('linkage', 'in_range', {'range': (0, 1)}))
        rules.append(('can_linked', 'in_range', {'range': (0, 1)}))
        rules.append(('has_zaxis', 'in_range', {'range': (0, 1)}))
        rules.append(('has_desiredvalue', 'in_range', {'range': (0, 1)}))
        rules.append(('dims_report_redirect', 'in_range', {'range': (0, 1)}))
        rules.append(('nums_report_redirect', 'in_range', {'range': (0, 1)}))
        return rules


class PdfExportModel(BaseModel):
    __slots__ = ['dashboard_id', 'dashboard_url', 'external_conditions', 'export_type']

    def __init__(self, **kwargs) -> None:
        self.dashboard_id = None
        self.dashboard_url = None
        self.external_conditions = None
        self.export_type = 0
        super().__init__(**kwargs)

    def rules(self) -> List[Union[Tuple[str, str, Dict[str, int]], Tuple[str, str, Dict[str, List[int]]]]]:
        rules = super().rules()
        rules.append(('dashboard_id', "string", {'max': 36}))
        return rules


class ReleaseModel(BaseModel):
    __slots__ = [
        'id',
        'status',
        'view_passwd',
        'dashboard',
        'screens',
        'is_multiple_screen',
        'type_access_released',
        'user_groups',
    ]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.status = None
        self.view_passwd = None
        self.dashboard = None
        self.screens = None
        self.is_multiple_screen = None
        self.type_access_released = None
        self.user_groups = None

        super().__init__(**kwargs)

    def rules(self) -> List[Union[Tuple[str, str, Dict[str, int]], Tuple[str, str, Dict[str, List[int]]]]]:
        # logging.info(self)
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('status', 'in_range', {'range': [e.value for e in DashboardStatus.__members__.values()]}))
        rules.append(('view_passwd', "string", {'max': 100, 'required': False}))
        rules.append(
            (
                'type_access_released',
                'in_range',
                {'range': [e.value for e in DashboardTypeAccessReleased.__members__.values()]},
            )
        )
        return rules


class ReleasedDashboardModel(BaseModel):
    __slots__ = [
        'id',
        'dashboard_id',
        'snapshot_id',
        'name',
        'type',
        'data_type',
        'level_code',
        'platform',
        'is_multiple_screen',
        'status',
        'user_group_id',
        'cover',
        'selectors',
        'dashboard_filters',
        'share_secret_key',
        'layout',
        'scale_mode',
        'background',
        'biz_code',
        'type_access_released',
        'border',
        'default_show',
        'theme',
        'refresh_rate',
        'grid_padding',
        'is_show_mark_img',
        'var_value_sources',
        "terminal_type",
        'application_type',
        "external_subject_ids",
        "main_external_subject_id",
        "line_height",
        "smart_beauty_status",
        "dataset_id",
    ]

    def __init__(self, **kwargs) -> None:
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.snapshot_id = None
        self.name = None
        self.type = None
        self.data_type = None
        self.level_code = None
        self.platform = DashboardPlatforms.PC.value
        self.theme = DashboardTheme.TechBlue.value
        self.is_multiple_screen = 0
        self.status = None
        self.user_group_id = None
        self.cover = None
        self.selectors = None
        self.global_params = None
        self.dashboard_filters = None
        self.dashboard_var_filters = None
        self.share_secret_key = None
        self.layout = None
        self.scale_mode = None
        self.background = None
        self.biz_code = None
        self.type_access_released = None
        self.created_by = None
        self.modified_by = None

        self.border = None
        self.default_show = None
        self.type_selector = None
        self.description = None
        self.refresh_rate = None
        self.grid_padding = None
        self.create_type = None
        self.new_layout_type = None
        self.is_show_mark_img = None
        self.var_value_sources = None
        self.terminal_type = ""
        self.application_type = 0
        self.main_external_subject_id = None
        self.external_subject_ids = ""
        self.line_height = 40
        self.smart_beauty_status = 0
        self.auto_play = 0
        self.asset_id = 0
        self.dataset_id = ""
        self.analysis_type = ""
        self.parent_id = ""
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 45}))
        rules.append(('type', 'in_range', {'range': [e.value for e in DashboardType.__members__.values()]}))
        rules.append(
            ('platform', 'in_range', {'range': [item.value for item in DashboardPlatforms.__members__.values()]})
        )
        # rules.append(('theme', 'in_range', {'range': [item.value for item in DashboardTheme.__members__.values()]}))
        return rules


class ReleasedChartModel(BaseModel):
    __slots__ = [
        'id',
        'chart_id',
        'snapshot_id',
        'dashboard_id',
        'name',
        'chart_code',
        'chart_type',
        'content',
        'source',
        'level_code',
        'parent_id',
        'display_item',
        'refresh_rate',
        'penetrate',
        'sort_method',
        'position',
        'percentage',
        'layout',
        'layout_extend',
        'config',
        'filter_config',
        'data_modified_on',
        'dims',
        'nums',
        'desires',
        'filters',
        'filter_relation',
        'marklines',
        'penetrates',
        'layers',
        'zaxis',
        'data_logic_type_code',
        'source_type',
        'source_content',
        'label_id',
        'label_tmpl_id',
        'chart_params',
        'component_filter',
        'jump',
        'chart_params_jump',
        'penetrate_relation',
        'penetrate_filter_relation',
        'penetrate_var_filter_relation',
        "chart_filters",
        "chart_linkages",
        "chart_default_value",
        "enable_subtotal",
        "enable_summary",
        "enable_subtotal_col",
        "enable_subtotal_col_summary",
        "enable_subtotal_row",
        "enable_subtotal_row_summary",
        "subtotal_row_summary_formula_mode",
        "reset_field_sort",
        "aggregation",
        "pre_comparison",
        "parent_chart_id",
        "child_rank",
        "external_subject_ids",
        "fixed_data_mode",
        "fixed_manual_value",
        "is_highdata",
        "close_detail_mode",
    ]

    def __init__(self, **kwargs) -> None:
        """
        单图
        :param kwargs:
        """
        self.id = None
        self.chart_id = None
        self.snapshot_id = None
        self.dashboard_id = None
        self.name = None
        self.chart_code = None
        self.chart_type = None
        self.content = None
        self.source = None
        self.level_code = None
        self.parent_id = None
        self.display_item = None
        self.refresh_rate = None
        self.penetrate = None
        self.sort_method = None
        self.position = None
        self.percentage = None
        self.layout = None
        self.layout_extend = None
        self.config = None
        self.filter_config = None
        self.component_filter = None
        self.data_modified_on = None
        self.dims = None
        self.comparisons = None
        self.nums = None
        self.jump = None
        self.desires = None
        self.zaxis = None
        self.filters = None
        self.filter_relation = None
        self.marklines = None
        self.penetrates = None
        self.layers = None
        self.data_logic_type_code = None
        self.source_type = None
        self.source_content = None
        self.label_id = None
        self.label_tmpl_id = None
        self.chart_params = None
        self.chart_params_jump = None
        self.column_order = None
        self.page_size = None
        self.default_value = None
        self.penetrate_relation = None
        self.penetrate_filter_relation = None
        self.penetrate_var_filter_relation = None
        self.chart_filters = None
        self.chart_linkages = None
        self.chart_default_value = None
        self.var_relations = None
        self.chart_vars = None
        self.enable_subtotal = 0
        self.asset_id = 0
        self.enable_summary = 0
        self.enable_subtotal_col = 0
        self.enable_subtotal_col_summary = 0
        self.enable_subtotal_row = 0
        self.enable_subtotal_row_summary = 0
        self.subtotal_row_summary_formula_mode = None
        self.reset_field_sort = 1
        self.field_sorts = None
        self.aggregation = 1
        self.pre_comparison = 1
        self.export_type = ""
        self.parent_chart_id = ''
        self.child_rank = 0
        self.external_subject_ids = []
        self.fixed_data_mode = None
        self.fixed_manual_value = None
        self.is_highdata = None
        self.chart_visible_triggers = None
        self.close_detail_mode = 0
        self.chart_visible = 1
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['id', 'dashboard_id', 'snapshot_id'], 'string', {'max': 36}))
        rules.append(("enable_subtotal", "in_range", {"range": [0, 1]}))
        rules.append(("enable_summary", "in_range", {"range": [0, 1]}))
        rules.append(("enable_subtotal_col", "in_range", {"range": [0, 1]}))
        rules.append(("enable_subtotal_col_summary", "in_range", {"range": [0, 1]}))
        rules.append(("enable_subtotal_row", "in_range", {"range": [0, 1]}))
        rules.append(("enable_subtotal_row_summary", "in_range", {"range": [0, 1]}))
        rules.append(("aggregation", "in_range", {"range": [0, 1]}))
        rules.append(("pre_comparison", "in_range", {"range": [0, 1]}))
        rules.append(
            (
                "subtotal_row_summary_formula_mode",
                "in_range",
                {"range": [e.value for e in ChartDimFormulaMode.__members__.values()]},
            )
        )
        rules.append(("reset_field_sort", "in_range", {"range": [0, 1]}))
        return rules


class ChartDimModel(BaseModel):
    __slots__ = [
        'id',
        'dashboard_chart_id',
        'dim',
        'dataset_field_id',
        'alias',
        'content',
        'formula_mode',
        'rank',
        'sort',
    ]

    def __init__(self, **kwargs):
        """
        单图维度
        :param kwargs:
        """
        self.id = None
        self.dashboard_chart_id = None
        self.dim = None
        self.alias = None
        self.content = None
        self.formula_mode = ''
        self.rank = None
        self.sort = None
        self.dashboard_jump_config = None
        self.note = None
        self.is_subtotal_cate = 0
        self.dataset_field_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_chart_id', 'string', {'max': 36}))
        rules.append((['dim', 'alias', 'rank'],))
        rules.append(("is_subtotal_cate", "in_range", {"range": [0, 1]}))
        rules.append(
            (
                'formula_mode',
                'in_range',
                {'range': [e.value for e in ChartDimFormulaMode.__members__.values()], 'required': False},
            )
        )
        return rules


class HeatMapContentModel(BaseModel):
    __slots__ = ['center', 'zoom', 'max']

    def __init__(self, **kwargs):
        """
        热力图内容
        :param kwargs:
        """
        self.center = None
        self.zoom = None
        self.max = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['center', 'zoom', 'max'],))
        return rules


class ChartComparisonModel(BaseModel):
    __slots__ = ['id', 'dashboard_chart_id', 'dataset_field_id', 'alias', 'content', 'formula_mode', 'rank', 'sort']

    def __init__(self, **kwargs):
        """
        单图对比（列）
        :param kwargs:
        """
        self.id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.alias = None
        self.content = None
        self.formula_mode = ''
        self.rank = None
        self.sort = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_chart_id', 'string', {'max': 36}))
        rules.append((['dataset_field_id', 'alias', 'rank'],))
        rules.append(
            (
                'formula_mode',
                'in_range',
                {'range': [e.value for e in ChartDimFormulaMode.__members__.values()], 'required': False},
            )
        )
        return rules


class ChartLayersModel(BaseModel):
    __slots__ = ['dashboard_chart_id', 'dim', 'alias', 'content', 'formula_mode', 'rank']

    def __init__(self, **kwargs):
        """
        单图维度
        :param kwargs:
        """
        self.dashboard_chart_id = None
        self.dim = None
        self.alias = None
        self.content = None
        self.formula_mode = ''
        self.rank = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_chart_id', 'string', {'max': 36}))
        rules.append((['dim', 'alias', 'rank'],))
        rules.append(
            (
                'formula_mode',
                'in_range',
                {'range': [e.value for e in ChartDimFormulaMode.__members__.values()], 'required': False},
            )
        )
        return rules


class SubtotalResult(BaseModel):
    __slots__ = ['subtotal_col', 'subtotal_row']

    def __init__(self, **kwargs):
        """
        小计总结果
        :param kwargs:
        """
        self.subtotal_col = None
        self.subtotal_row = None
        super().__init__(**kwargs)


class SubtotalRowModel(BaseModel):
    __slots__ = ['header', 'cols', 'subtotal_cols', 'subtotal_summary_cols']

    def __init__(self, **kwargs):
        """
        行小计小计返回结果model
        :param kwargs:
        """
        self.header = []
        self.cols = []
        self.subtotal_cols = []
        self.subtotal_summary_cols = []
        super().__init__(**kwargs)


class SubtotalRowColumnModel(BaseModel):
    __slots__ = ['col_value', 'siblings']

    def __init__(self, **kwargs):
        """
        行小计列数据model
        :param kwargs:
        """
        self.col_value = None
        self.siblings = []
        super().__init__(**kwargs)


class SubtotalColModel(BaseModel):
    __slots__ = ['rows', 'summary']

    def __init__(self, **kwargs):
        """
        列小计返回结果model
        :param kwargs:
        """
        self.rows = []
        self.summary = {}
        super().__init__(**kwargs)


class SubtotalColSummaryModel(BaseModel):
    __slots__ = ['cols']

    def __init__(self, **kwargs):
        """
        列小计返回结果model
        :param kwargs:
        """
        self.cols = []
        super().__init__(**kwargs)


class SubtotalColRowModel(BaseModel):
    __slots__ = ['has_next', 'has_prev', 'cate_fields', 'cols']

    def __init__(self, **kwargs):
        """
        列小计返回结果model
        :param kwargs:
        """
        self.has_next = 0
        self.has_prev = 0
        self.cate_fields = []
        self.cols = []
        super().__init__(**kwargs)


class SubtotalColCateFieldModel(BaseModel):
    __slots__ = ['dataset_id', 'field_id', 'rank', 'col_name', 'alias']

    def __init__(self, **kwargs):
        """
        列小计返回结果model
        :param kwargs:
        """
        self.dataset_id = ''
        self.field_id = ''
        self.rank = 0
        self.col_name = ''
        self.alias = ''
        self.detail_col_name = ''
        super().__init__(**kwargs)


class SubtotalColColOfRowModel(BaseModel):
    __slots__ = ['header', 'col_value']

    def __init__(self, **kwargs):
        """
        列小计返回结果model
        :param kwargs:
        """
        self.header = []
        self.col_value = None
        super().__init__(**kwargs)


class SubtotalHeaderModel(BaseModel):
    __slots__ = ['col_name', 'alias_name', 'col_value', 'dataset_id', 'field_id', 'alias', 'col_type']

    def __init__(self, **kwargs):
        """
        列小计返回结果model
        :param kwargs:
        """
        self.col_name = ''
        self.alias_name = ''
        self.col_value = None
        self.dataset_id = ''
        self.field_id = ''
        self.alias = ''
        self.col_type = ''
        super().__init__(**kwargs)


class ChartNumModel(BaseModel):
    __slots__ = [
        'id',
        'dashboard_chart_id',
        'num',
        'alias',
        'formula_mode',
        'rank',
        'display_format',
        'sort',
        'axis_type',
        'chart_code',
        'calc_null',
        'subtotal_formula_mode',
        'subtotal_col_formula_mode',
        'subtotal_row_formula_mode',
    ]

    def __init__(self, **kwargs):
        """
        单图数值
        :param kwargs:
        """
        self.id = None
        self.dashboard_chart_id = None
        self.num = None
        self.alias = None
        self.formula_mode = ''
        self.chart_code = None
        self.rank = None
        self.display_format = ChartNumDisplayFormatModel()
        self.sort = None
        self.axis_type = None
        self.dashboard_jump_config = None
        self.calc_null = 0  # 默认不计算null值，即不使用ifnull
        self.note = None
        self.subtotal_formula_mode = None
        self.subtotal_col_formula_mode = None
        self.subtotal_row_formula_mode = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_chart_id', 'string', {'max': 36}))
        rules.append((['num', 'alias', 'rank'],))
        rules.append(
            (
                'formula_mode',
                'in_range',
                {'range': [e.value for e in ChartNumFormulaMode.__members__.values()], 'required': False},
            )
        )
        rules.append(
            (
                'subtotal_formula_mode',
                'in_range',
                {'range': [e.value for e in ChartNumSubtotalFormulaMode.__members__.values()], 'required': False},
            )
        )
        rules.append(
            (
                'subtotal_col_formula_mode',
                'in_range',
                {'range': [e.value for e in ChartNumSubtotalFormulaMode.__members__.values()], 'required': False},
            )
        )
        rules.append(
            (
                'subtotal_row_formula_mode',
                'in_range',
                {'range': [e.value for e in ChartNumSubtotalFormulaMode.__members__.values()], 'required': False},
            )
        )
        return rules


class ChartNumDisplayFormatModel(BaseModel):
    __slots__ = ['display_mode', 'thousand_point_separator', 'fixed_decimal_places', 'unit', 'column_unit_name']

    def __init__(self, **kwargs):
        self.display_mode = None
        self.thousand_point_separator = 1
        self.fixed_decimal_places = 0
        self.unit = None
        self.column_unit_name = None
        super().__init__(**kwargs)


class ChartDesireModel(BaseModel):
    """
    目标值模型
    """

    __slots__ = [
        'id',
        'dashboard_chart_id',
        'dataset_field_id',
        'mode',
        'value',
        'alias',
        'formula_mode',
        'rank',
        'display_format',
        'sort',
        'axis_type',
    ]

    def __init__(self, **kwargs):
        """
        单图目标值
        :param kwargs:
        """
        self.id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.mode = 0
        self.value = ''
        self.alias = None
        self.formula_mode = ''
        self.rank = None
        self.display_format = ChartNumDisplayFormatModel()
        self.sort = None
        self.axis_type = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_chart_id', 'string', {'max': 36, 'required': False}))
        rules.append(
            (
                'formula_mode',
                'in_range',
                {'range': [e.value for e in ChartNumFormulaMode.__members__.values()], 'required': False},
            )
        )
        return rules


class ChartMapModel(BaseModel):
    __slots__ = ['dashboard_chart_id', 'address', 'alias', 'sub_chart_code', 'content']

    def __init__(self, **kwargs):
        """
        单图地图类型
        :param kwargs:
        """
        self.dashboard_chart_id = None
        self.address = None
        self.alias = None
        self.sub_chart_code = None
        self.content = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['dashboard_chart_id', 'address'], 'string', {'max': 36}))
        rules.append(('alias',))
        return rules


class ChartColourModel(BaseModel):
    """
    单图颜色处理模型
    """

    __slots__ = ['id', 'dashboard_chart_id', 'colour_content', 'num']

    def __init__(self, **kwargs):
        """
        单图柱状颜色
        :param kwargs:
        """
        self.id = None
        self.dashboard_chart_id = None
        self.colour_content = None
        self.num = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_chart_id', 'string', {'max': 36}))
        # rules.append((['num', 'colour_content'],))
        rules.append((['colour_content'],))
        return rules


class DashboardComponentFilterModel(BaseModel):
    __slots__ = ['id', 'chart_initiator_id', 'chart_responder_id', 'dataset_id', 'is_same_dataset']

    def __init__(self, **kwargs):
        """
        单图组件过滤器模型
        :param kwargs:
        """
        self.id = None
        self.chart_initiator_id = None
        self.chart_responder_id = None
        self.dataset_id = None
        self.is_same_dataset = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['chart_initiator_id', 'chart_responder_id', 'dataset_field_id'], 'string', {'max': 36}))
        return rules


class DashboardComponentFilterFieldModel(BaseModel):
    __slots__ = ['id', 'field_initiator_id', 'field_responder_id', 'chart_id', 'filter_id']

    def __init__(self, **kwargs):
        """
        单图组件过滤器字段关系模型
        :param kwargs:
        """
        self.id = None
        self.field_initiator_id = None
        self.field_responder_id = None
        self.chart_id = None
        self.filter_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['field_initiator_id', 'field_responder_id', 'chart_id', 'filter_id'], 'string', {'max': 36}))
        return rules


class DashboardChartFilterRelationModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = None
        self.dashboard_chart_id = None
        self.dashboard_chart_filter_id = None
        self.operator = None
        self.col_value = None
        super().__init__(**kwargs)


class ChartMarklineModel(BaseModel):
    __slots__ = ['id', 'dashboard_chart_id', 'name', 'mode', 'num', 'formula_mode', 'value', 'rank']

    def __init__(self, **kwargs):
        """
        单图辅助线模型
        :param kwargs:
        """
        self.id = None
        self.dashboard_chart_id = None
        self.name = None
        self.mode = None
        self.num = None
        self.formula_mode = None
        self.value = None
        self.rank = None
        self.axis_type = None
        self.color = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_chart_id', 'string', {'max': 36}))
        rules.append((['name', 'mode'],))
        rules.append(
            (
                'mode',
                'in_range',
                {'range': [e.value for e in ChartMarklineMode.__members__.values()], 'required': False},
            )
        )
        rules.append(
            (
                'formula_mode',
                'in_range',
                {'range': [e.value for e in ChartMarklineFormulaMode.__members__.values()], 'required': False},
            )
        )
        return rules


class DashboardPenetrateRelation(BaseModel):
    """
    穿透字段关联关系
    """

    __slots__ = ['id', 'dashboard_chart_id', 'dashboard_id', 'parent_chart_field_id', 'child_chart_field_id', 'type']

    def __init__(self, **kwargs):
        for v in self.__slots__:
            setattr(self, v, None)
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_id', 'string', {'max': 36}))
        rules.append(('dashboard_chart_id', 'string', {'max': 36}))
        rules.append(('parent_chart_field_id', 'string', {'max': 36}))
        rules.append(('child_chart_field_id', 'string', {'max': 36}))
        rules.append(('type', 'in_range', {'range': [0, 1]}))


class DashboardChartParamsModel(QueryBaseModel):
    __slots__ = ['dashboard_id', 'dashboard_chart_id', 'dataset_field_id', 'alias', 'rank', 'param_id']

    def __init__(self, **kwargs):
        """
        报告中单图参数模型类
        :param kwargs:
        """
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.alias = None
        self.rank = None
        self.param_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['param_id'], 'string', {'required': False, 'max': 36}))
        rules.append((['dataset_field_id', 'dashboard_id', 'dashboard_chart_id'], 'string', {'max': 36}))
        return rules


class DashboardChartParamsJumpModel(QueryBaseModel):
    __slots__ = [
        'dashboard_id',
        'dashboard_chart_id',
        'param_dataset_field_id',
        'source_id',
        'dashboard_filter_id',
        'rank',
    ]

    def __init__(self, **kwargs):
        """
        报告中单图参数跳转模型类
        :param kwargs:
        """
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.param_dataset_field_id = None
        self.source_id = None
        self.dashboard_filter_id = None
        self.rank = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(
            (
                ['dashboard_id', 'dashboard_chart_id', 'param_dataset_field_id', 'dashboard_filter_id'],
                'string',
                {'max': 36},
            )
        )
        return rules


class DashboardSortModel(RankModel):
    def __init__(self, **kwargs):
        """
        应用排序
        :param kwargs:
        """
        super().__init__(**kwargs)
        self.table_name = 'dashboard'
        self.rank_col_name = 'rank'
        self.id_col_name = 'id'
        self.parent_id = kwargs.get('parent_id') or ''


class ScreenModel(BaseModel):
    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.description = None
        self.screens = None
        self.refresh_rate = None
        self.auto_play = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36, 'required': False}))
        rules.append(('name', 'string', {'max': 255}))
        rules.append(('auto_play', 'in_range', {'range': [0, 1], 'required': False}))
        rules.append(('description', 'string', {'max': 1000, 'required': False}))
        rules.append(('screens', None, {'required': False}))
        return rules


class ScreenDashboardModel(BaseModel):
    def __init__(self, **kwargs):
        self.dashboard_id = None
        self.screen_id = None
        self.rank = None
        self.type = None
        self.id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36, 'required': False}))
        rules.append(('dashboard_id', 'string', {'max': 36, 'required': False}))
        rules.append(('screen_id', 'string', {'max': 36}))
        rules.append(('rank', "match", {'pattern': "^[0-9]+$", 'required': False}))
        rules.append(
            (
                'type',
                'in_range',
                {'range': [e.value for e in DashboardTypeStatus.__members__.values()], 'required': False},
            )
        )
        return rules


class ChartFieldSortModel(BaseModel):
    __slots__ = [
        'id',
        'dashboard_id',
        'dashboard_chart_id',
        'dataset_field_id',
        'field_source',
        'sort',
        'content',
        'weight',
    ]

    def __init__(self, **kwargs) -> None:
        """
        单图维度
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.dashboard_chart_id = None
        self.dataset_field_id = None
        self.field_source = None
        self.sort = None
        self.content = None
        self.weight = None
        super().__init__(**kwargs)


class FieldSortModel(BaseModel):
    __slots__ = ['dataset_field_id', 'name', 'items', 'sort']

    def __init__(self, **kwargs) -> None:
        """
        单图维度
        :param kwargs:
        """
        self.dataset_field_id = None
        self.name = None
        self.items = []
        self.dataset_field_id = None
        self.sort = None
        super().__init__(**kwargs)


class FieldExtDataModel(BaseModel):
    __slots__ = ['dataset_id', 'dataset_field_id', 'col_name', 'detail_col_name']

    def __init__(self, **kwargs):
        """
        字段ext_data数据
        :param kwargs:
        """
        self.dataset_id = kwargs.get("dataset_id")
        self.dataset_field_id = kwargs.get("dataset_field_id")
        self.col_name = kwargs.get("col_name")
        self.detail_col_name = kwargs.get("detail_col_name")


class DashboardOpenAPIQueryModel(QueryBaseModel):
    __slots__ = [
        'dashboard_ids',
        'type',
        'status',
        'platform',
        'created_by',
        'build_in',
        'is_multiple_screen',
        'parent_id',
        'level_deep'
    ]

    def __init__(self, **kwargs):
        self.dashboard_ids = None
        self.type = None
        self.status = None
        self.platform = None
        self.created_by = None
        self.build_in = None
        self.is_multiple_screen = None
        self.parent_id = None
        # level_deep=1 系统/非系统 只返回parent_id下的1级
        self.level_deep = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_ids', 'array', {'required': False}))
        rules.append(('status', 'in_range', {'range': [0, 1, '0', '1'], 'required': False}))
        rules.append(('type', 'in_range', {'range': ['FILE', 'FOLDER'], 'required': False}))
        rules.append(('platform', 'in_range', {'range': ['pc', 'mobile'], 'required': False}))
        rules.append(('build_in', 'in_range', {'range': [0, 1, '0', '1'], 'required': False}))
        rules.append(('is_multiple_screen', 'in_range', {'range': [0, 1, '0', '1'], 'required': False}))
        rules.append(('parent_id', 'string', {'required': False}))
        return rules


class DashboardOpenAPIEmbeddedQueryModel(QueryBaseModel):
    __slots__ = [
        'dashboard_ids',
        'type',
        'status',
        'platform',
        'created_by',
        'build_in',
        'is_multiple_screen',
        'dashboard_type',
        'terminal_type',
        'parent_id',
    ]

    def __init__(self, **kwargs):
        self.dashboard_ids = None
        self.type = None
        self.status = None
        self.platform = None
        self.created_by = None
        self.build_in = None
        self.is_multiple_screen = None
        self.dashboard_type = None
        self.terminal_type = None
        self.parent_id = None
        self.with_filter = 1  # 是否过滤只包含组件的报告
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_ids', 'array', {'required': False}))
        rules.append(('status', 'in_range', {'range': [0, 1, '0', '1'], 'required': False}))
        rules.append(('type', 'in_range', {'range': ['FILE', 'FOLDER', 'CHILD_FILE'], 'required': False}))
        rules.append(('platform', 'in_range', {'range': ['pc', 'mobile'], 'required': False}))
        rules.append(('build_in', 'in_range', {'range': [0, 1, '0', '1'], 'required': False}))
        rules.append(('is_multiple_screen', 'in_range', {'range': [0, 1, '0', '1'], 'required': False}))
        rules.append(('parent_id', 'string', {'required': False}))
        return rules


class ChartDownloadContext(BaseModel):
    __slots__ = [
        'flow_id',
        'flow_instance_id',
        'download_id',
        'tenant_code',
        'account',
        'cookie',
        'userid',
        'external_params',
        'filename',
    ]

    def __init__(self, **kwargs):
        self.chart_params = {}
        self.flow_id = ''
        self.flow_instance_id = ''
        self.download_id = ''
        self.tenant_code = ''
        self.account = ''
        self.cookie = ''
        self.userid = ''
        self.external_params = {}
        self.filename = ''
        super().__init__(**kwargs)


class ChartSubtotalConfigModel(BaseModel):
    __slots__ = [
        'col_pos',
        'col_summary_pos',
        'col_alias',
        'col_summary_alias',
        'row_pos',
        'row_summary_pos',
        'row_alias',
        'row_summary_alias',
        'show_classify_name',
    ]

    def __init__(self, **kwargs):
        self.col_pos = 'tail'
        self.col_summary_pos = 'tail'
        self.col_alias = '小计'
        self.col_summary_alias = '总计'
        self.row_pos = 'tail'
        self.row_summary_pos = 'tail'
        self.row_alias = ''
        self.row_summary_alias = '总计'
        self.show_classify_name = False
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(("col_pos", "in_range", {"range": ["head", "tail"], "required": False}))
        rules.append(("col_summary_pos", "in_range", {"range": ["head", "tail"], "required": False}))
        rules.append(("col_alias", "string", {"required": False}))
        rules.append(("col_summary_alias", "string", {"required": False}))
        rules.append(("row_pos", "in_range", {"range": ["head", "tail"], "required": False}))
        rules.append(("row_summary_pos", "in_range", {"range": ["head", "tail"], "required": False}))
        rules.append(("row_alias", "string", {"required": False}))
        rules.append(("row_summary_alias", "string", {"required": False}))
        return rules


class DownloadSerialNumberConfig(BaseModel):
    __slots__ = ['is_show', 'display_name']

    def __init__(self, **kwargs):
        self.is_show = False
        self.display_name = '序号'
        super().__init__(**kwargs)


class SnapshotModel(BaseModel):
    __slots__ = ['snapshot_id', ]

    def __init__(self, **kwargs):
        self.snapshot_id = ''
        super().__init__(**kwargs)


class SnapshotDashboardMetadataModel(BaseModel):
    __slots__ = ['snapshot_id', 'dashboard_id', 'metadata']

    def __init__(self, **kwargs):
        self.snapshot_id = ''
        self.metadata = ''
        self.dashboard_id = ''
        super().__init__(**kwargs)


class SnapshotDashboardModel(DashboardModel):
    __slots__ = DashboardModel.__slots__ + ['snapshot_id']

    def __init__(self, **kwargs) -> None:
        self.snapshot_id = ''
        super().__init__(**kwargs)
        if hasattr(self, 'data_type'):
            delattr(self, 'data_type')


class SubtotalColFormulaExpressionCheckModel(BaseModel):
    __slots__ = [
        'dataset_id',
        'dataset_field_id',
        'expression',
    ]

    def __init__(self, **kwargs):
        self.dataset_id = ''
        self.dataset_field_id = ''
        self.expression = ''
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(("dataset_id", "string", {"required": True}))
        rules.append(("dataset_field_id", "string", {"required": True}))
        rules.append(("expression", "string", {"required": True}))


class DashboardGlobalParamsModel(BaseModel):
    __slots__ = ['id', 'dashboard_id', 'name', 'type']
    __tablename__ = 'dashboard_jump_global_params'

    def __init__(self, **kwargs):
        self.id = ''
        self.name = ''
        self.dashboard_id = ''
        super().__init__(**kwargs)

    def rules(self) -> List[Union[Tuple[str, str, Dict[str, int]], Tuple[str, str, Dict[str, List[str]]]]]:
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append(("dashboard_id", "string", {"max": 36}))
        rules.append(("name", "string", {"max": 128}))
        # rules.append(("type", "in_range", {"range": [e.value for e in DashboardGlobalParamsType.__members__.values()]}))
        return rules


class RuntimeEditParamsModel(BaseModel):
    __slots__ = ['chart_id', 'config', 'update_runtime']

    def __init__(self, **kwargs):
        """
        报告中单图参数模型类
        :param kwargs:
        """
        self.chart_id = ''
        self.config = ''
        self.update_runtime = []
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['chart_id'], 'string', {'required': False, 'max': 36}))
        rules.append((['config'], 'json_check', {'required': False}))
        rules.append(
            (
                'update_runtime',
                'in_range',
                {'range': ['0', '1', ''], 'required': False},
            )
        )
        return rules

    def json_check(self, config, attr_name, value):
        try:
            json.loads(value)
        except:
            msg = config.get('msg')
            raise UserError(message=msg if msg else attr_name + 'config不是标准json问题！')
        return True
