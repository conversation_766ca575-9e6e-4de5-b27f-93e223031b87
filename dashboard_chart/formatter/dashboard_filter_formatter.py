#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/15
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from collections import defaultdict

from base.errors import UserError
from dashboard_chart.formatter.formatter import Formatter
from base.enums import (
    DatasetFieldDataType,
    ChartDimFormulaMode,
    DashboardFilterSelectType,
    SqlWhereOperator,
    ChartConditionType,
)

logger = logging.getLogger(__name__)


class DashboardFilterFormatter(Formatter):
    """
    报告级筛选
    :param self.dataset_field_dict: 数据集数据字典
    :param self.dashboard_filters: 报告级筛选
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.dashboard_filters = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.dataset_field_dict = self.chart_data_model.dataset_field_dict
        self.dashboard_filters = self.chart_data_model.dashboard_filters

    @staticmethod
    def _deal_with_select_all_flag(dashboard_dataset_filter):
        """
        处理全选标志位
        :param dashboard_dataset_filter:
        :return:
        """
        # 处理全选标志位
        select_all_flag = dashboard_dataset_filter.get('select_all_flag', None)
        operator = dashboard_dataset_filter.get('operator', '')
        try:
            if select_all_flag and int(select_all_flag) in [DashboardFilterSelectType.SelectAll.value]:
                if str(operator).upper() in SqlWhereOperator.In.value:
                    return False, dashboard_dataset_filter
                elif str(operator).upper() in SqlWhereOperator.Notin.value:
                    # dashboard_dataset_filter['operator'] = SqlWhereOperator.In.value
                    # dashboard_dataset_filter['col_value'] = json.dumps([''])
                    # 优化为is null
                    dashboard_dataset_filter['operator'] = 'is null'
                    dashboard_dataset_filter['col_value'] = ''
            return True, dashboard_dataset_filter
        except Exception:
            return True, dashboard_dataset_filter

    def format(self):
        """
        执行格式化
        :return:
        """
        if not self.dashboard_filters or not len(self.dashboard_filters):
            return self
        # TODO: 临时方案, 自助分析筛选器不受全局过滤筛选
        if self.chart_data_model.chart_code == 'select_filter_new' and self.chart_data_model.external_subject_ids:
            return self

        filter_relation_dict = defaultdict(list)
        for single_dashboard_filter in self.dashboard_filters:
            field_id = single_dashboard_filter.get('field_id')
            if not field_id:
                field_id = single_dashboard_filter.get('main_dataset_field_id')
            cur_dataset_field_data = self.dataset_field_dict.get(field_id)
            if not cur_dataset_field_data:
                raise UserError(message='数据异常，无法获取数据集！')

            # 处理全选标志位
            deal_with_flag, single_dashboard_filter = self._deal_with_select_all_flag(single_dashboard_filter)
            if not deal_with_flag:
                continue

            # 以外层数据集字段分组
            filter_relation_dict[field_id].append(single_dashboard_filter)

        for dataset_field_id, filter_relation_list in filter_relation_dict.items():

            where_field, complex_where = self.generate_where_fields(dataset_field_id, filter_relation_list)
            if not where_field:
                continue

            # 处理或与且的关系
            where_field.logic = self.get_logic(filter_relation_list)

            if complex_where:
                where_field.complex = complex_where

            self.where_conditions.append(where_field)

        return self

    def get_logic(self, filter_relation_list):
        # filter_relation: 0 and逻辑
        # filter_relation: 1 or逻辑
        if not filter_relation_list:
            return 'AND'
        if not all([f.get('filter_relation') == 1 for f in filter_relation_list]):
            return 'AND'
        return 'OR'

    def generate_where_fields(self, field_id, filter_relation_list):
        where_field = None
        complex_where = []
        cur_dataset_field_data = self.dataset_field_dict.get(field_id)

        for single_dashboard_filter in filter_relation_list:
            # 处理formula_mode
            formula_mode = (
                ChartDimFormulaMode.Day.value
                if cur_dataset_field_data.get("data_type") == DatasetFieldDataType.Datetime.value
                else None
            )
            formula_result_dict = self.deal_with_formula_params(
                formula_mode,
                single_dashboard_filter.get('data_type'),
                single_dashboard_filter.get('operator'),
                single_dashboard_filter.get('col_value'),
            )

            # 处理替换关键字的值
            field_value = single_dashboard_filter.get('col_value')
            if field_value and self.is_keyword_value(field_value):
                field_value = self._deal_keyword_value(
                    cur_dataset_field_data.get('alias_name'),
                    single_dashboard_filter.get('operator'), field_value
                )
                formula_result_dict['col_value'] = field_value

            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.DashboardFilter.value,
            )
            where_item = self.generate_where_field_by_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
            )
            if not where_item:
                continue
            else:
                # 内层的条件关系默认为and
                where_item.logic = 'AND'
            if where_field:
                complex_where.append(where_item)
                where_field.dn.append(where_item.dn)
            else:
                where_field = where_item
                where_field.dn = [where_item.dn]

        return where_field, complex_where
