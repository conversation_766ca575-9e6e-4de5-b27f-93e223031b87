#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
chart linkage
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from base.enums import ChartConditionType
from dashboard_chart.formatter.formatter import Formatter
from dashboard_chart.services import chart_service


logger = logging.getLogger(__name__)


class ChartLinkageFormatter(Formatter):
    """
    联动
    :param self.chart_responder_id: 单图id
    :param self.request_conditions: 条件数组
    :param self.selector_data_dict: db中的selectot数据
    """

    def __init__(self, model):
        self.chart_responder_id = None
        self.request_conditions = None
        self.selector_data_dict = None
        self.dataset_field_dict = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.chart_responder_id = self.chart_data_model.id
        self.request_conditions = self.chart_data_model.conditions
        self.selector_data_dict = self.chart_data_model.linkage_selector_dict
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )

    def _op_selector_list(self, single_condition, dim, selector_list):
        """
        处理selector_list
        :param single_condition:
        :param dim:
        :param selector_list:
        :return:
        """
        for idx, selector in enumerate(selector_list):
            # 相同数据集的情况
            if selector.get('is_same_dataset'):
                # 处理formula_mode
                formula_mode = dim.get('formula_mode')
                formula_result_dict = self.deal_with_formula_params(
                    formula_mode,
                    single_condition.get('data_type'),
                    single_condition.get('operator'),
                    single_condition.get('col_value'),
                )
                dim_data = dict(
                    alias_name=dim.get('alias_name'),
                    col_name=dim.get('col_name'),
                    col_value=formula_result_dict.get('col_value'),
                    operator=formula_result_dict.get('operator'),
                    dataset_field_id=dim.get('dim'),
                    data_type=single_condition.get('data_type'),
                    format=dim.get('format'),
                    formula_mode=formula_mode,
                    condition_type=ChartConditionType.LinkageFilter.value,
                )
                self.append_single_condition(
                    field_value=formula_result_dict.get('col_value'),
                    formula_result_dict=formula_result_dict,
                    cur_dataset_field_data=selector,
                    dn=dim_data,
                )
                continue
            if selector.get('field_initiator_id') == dim.get('dim'):
                dim_extra_data = chart_service.get_union_field_data(
                    self.chart_responder_id, selector.get('field_responder_id'), self.chart_data_model
                )
                # 处理formula_mode
                formula_result_dict = self.deal_with_formula_params(
                    dim.get('formula_mode'),
                    single_condition.get('data_type'),
                    single_condition.get('operator'),
                    single_condition.get('col_value'),
                )
                dim_data = dict(
                    alias_name=dim_extra_data.get('alias_name'),
                    col_name=dim_extra_data.get('col_name'),
                    col_value=formula_result_dict.get('col_value'),
                    operator=formula_result_dict.get('operator'),
                    dataset_field_id=dim_extra_data.get('id'),
                    data_type=dim_extra_data.get('data_type'),
                    format=dim.get('format'),
                    formula_mode=dim.get('formula_mode'),
                    condition_type=ChartConditionType.LinkageFilter.value,
                )
                self.append_single_condition(
                    field_value=formula_result_dict.get('col_value'),
                    formula_result_dict=formula_result_dict,
                    cur_dataset_field_data=selector,
                    dn=dim_data,
                )
                selector_list.pop(idx)

    def format(self):
        """
        执行格式化
        :return:
        """
        if not self.is_bulitin_list(self.request_conditions) or len(self.request_conditions) <= 0:
            return self
        for single_condition in self.request_conditions:
            dim = single_condition.get('dim', '')
            dashboard_chart_id = dim.get('dashboard_chart_id', '') if dim else ''  # 主动联动单图id
            selector_list = self.selector_data_dict.get(dashboard_chart_id, None)
            if not single_condition.get('chart_id') or not selector_list:
                continue
            # 校验是否有全选标志位
            select_all_flag = self._check_select_all_type(single_condition.get('select_type'))
            if select_all_flag:
                continue
            self._op_selector_list(single_condition, dim, selector_list)
        return self
