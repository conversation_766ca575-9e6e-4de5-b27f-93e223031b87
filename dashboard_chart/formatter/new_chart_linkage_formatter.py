import logging

from base.enums import ChartConditionType
from dashboard_chart.formatter.chart_linkage_formatter import Chart<PERSON>inkageFormatter
from dashboard_chart.services import chart_service

logger = logging.getLogger(__name__)


class NewChartLinkageFormatter(ChartLinkageFormatter):
    def __init__(self, model):
        self.chart_responder_id = None
        self.request_conditions = None
        self.selector_data_dict = None
        self.dataset_field_dict = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        self.chart_responder_id = self.chart_data_model.id
        self.request_conditions = self.chart_data_model.chart_linkage_conditions
        self.selector_data_dict = self.chart_data_model.linkage_selector_dict
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )

    # pylint: disable=R1710
    def format(self):

        if not self.is_bulitin_list(self.request_conditions) or len(self.request_conditions) <= 0:
            return self

        for single_condition in self.request_conditions:
            initiator_data = single_condition.get("dim")
            responder_list = self.selector_data_dict.get(initiator_data["dashboard_chart_id"])

            for idx, responder in enumerate(responder_list):
                if (
                    initiator_data["dataset_id"] == responder["dataset_id"]
                    and responder["field_initiator_id"] == initiator_data["dim"]
                ):  # 相同数据集的情况下

                    formula_mode = initiator_data.get("formula_mode")
                    formula_result_dict = self.deal_with_formula_params(
                        formula_mode,
                        single_condition.get("data_type"),
                        single_condition.get("operator"),
                        single_condition.get("col_value"),
                    )
                    dim_data = dict(
                        alias_name=initiator_data.get("alias_name"),
                        col_name=initiator_data.get("col_name"),
                        col_value=formula_result_dict.get("col_value"),
                        operator=formula_result_dict.get("operator"),
                        dataset_field_id=initiator_data.get("dim"),
                        data_type=initiator_data.get("data_type"),
                        format=initiator_data.get("format"),
                        formula_mode=formula_mode,
                        condition_type=ChartConditionType.LinkageFilter.value,
                    )
                    self.append_single_condition(
                        field_value=formula_result_dict.get("col_value"),
                        formula_result_dict=formula_result_dict,
                        cur_dataset_field_data=responder,
                        dn=dim_data,
                    )

                if (
                    responder["field_initiator_id"] == initiator_data["dim"]
                    and initiator_data["dataset_id"] != responder["dataset_id"]
                ):  # 不同数据集的情况

                    dim_extra_data = chart_service.get_union_field_data(
                        self.chart_responder_id, responder.get("field_responder_id"), self.chart_data_model
                    )

                    formula_result_dict = self.deal_with_formula_params(
                        initiator_data.get("formula_mode"),
                        single_condition.get("data_type"),
                        single_condition.get("operator"),
                        single_condition.get("col_value"),
                    )

                    dim_data = {
                        "alias_name": dim_extra_data.get('alias_name'),
                        "col_value": formula_result_dict.get("col_value"),
                        "col_name": single_condition.get("col_name"),
                        "operator": formula_result_dict.get("operator"),
                        "dataset_field_id": dim_extra_data.get("id"),
                        "data_type": dim_extra_data.get("data_type"),
                        "format": initiator_data.get("format"),
                        "formula_mode": initiator_data.get("formula_mode"),
                        "condition_type": ChartConditionType.LinkageFilter.value,
                    }
                    self.append_single_condition(
                        field_value=formula_result_dict.get("col_value"),
                        formula_result_dict=formula_result_dict,
                        cur_dataset_field_data=responder,
                        dn=dim_data,
                    )
