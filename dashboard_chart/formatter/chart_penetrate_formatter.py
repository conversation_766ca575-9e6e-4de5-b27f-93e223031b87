#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
chart linkage
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from base.enums import ChartConditionType
from dashboard_chart.formatter.formatter import Formatter
from dashboard_chart.services import chart_service
from dashboard_chart.services import proxy_dataset_service


logger = logging.getLogger(__name__)


class ChartPenetrateFormatter(Formatter):
    """
    穿透
    :param self.chart_responder_id: 单图id
    :param self.conditions: 条件数组
    """

    def __init__(self, model):
        self.chart_responder_id = None
        self.penetrate_conditions = None
        self.dataset_field_dict = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.chart_responder_id = self.chart_data_model.id
        self.penetrate_conditions = self.chart_data_model.penetrate_conditions
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )
        # 父单图的数据集字段ID对应到子单图的字段ID，这里不能查询数据库。查库是有问题的，不知道是发布后的数据还是编辑状态的数据
        self.old_field2_new_field = dict()
        if self.chart_data_model.penetrate_relation:
            for item in self.chart_data_model.penetrate_relation:
                self.old_field2_new_field[item['parent_chart_field_id']] = item['child_chart_field_id']

    def format(self):
        if not self.penetrate_conditions:
            return self
        for single_condition in self.penetrate_conditions:
            dim = single_condition.get('dim', '')
            if not dim:
                continue
            field_id = dim.get('dim', '')
            # single_dataset_field = self.dataset_field_dict.get(field_id)
            # if not single_dataset_field:
            # continue

            # 通过联动传chart_id穿透不传这个特性区分联动与穿透
            if single_condition.get('chart_id'):
                continue
            # 校验是否有全选标志位
            select_all_flag = self._check_select_all_type(single_condition.get('select_type'))
            if select_all_flag:
                continue
            new_field_id = self.old_field2_new_field.get(field_id)
            if new_field_id and new_field_id in self.dataset_field_dict:
                # 存在字段映射，并且在穿透表中存在字段
                real_field_id = new_field_id
            elif field_id in self.dataset_field_dict:
                real_field_id = field_id
            else:
                continue
            dim_extra_data = chart_service.get_union_field_data(
                self.chart_responder_id, real_field_id, self.chart_data_model
            )
            operator = single_condition.get('operator')
            formula_mode = dim.get('formula_mode')
            formula_result_dict = self.deal_with_formula_params(
                formula_mode, single_condition.get('data_type'), operator, single_condition.get('col_value')
            )
            dim_data = dict(
                alias_name=dim_extra_data.get('alias_name'),
                col_name=dim_extra_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=dim_extra_data.get('id'),
                data_type=dim_extra_data.get('data_type'),
                format=dim.get('format'),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.Penetrate.value,
            )
            selector = proxy_dataset_service.get_formatted_fields_by_field_id(real_field_id)
            self.append_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=selector,
                dn=dim_data,
            )
        return self
