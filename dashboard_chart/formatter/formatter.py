#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
formatter

"""

# ---------------- 标准模块 ----------------
import copy
import json
from abc import ABCMeta, abstractmethod
import re
import logging

from dmplib.hug import debugger
from dmplib.hug import g
from base.errors import SqlProcessingException
from base.dmp_constant import FILTER_KEYWORD_PREFIX
from components.utils import mysql_escape_string


_debugger = debugger.Debug(__name__)

# ---------------- 业务模块 ----------------
from base.enums import (
    DatasetFieldDataType,
    ChartDimFormulaMode,
    DashboardFilterSelectType,
    SqlWhereOperator,
    FieldValueType,
)
from dashboard_chart.convertor.field_types import FieldObj, WhereField
from components import functions


class Formatter:
    """
    格式化查询条件数据基类
    :param self.chart_data_mode dashboard_chart.models.ChartDataModel: 单图数据model实例
    :param self.condition_models: 条件对象数组
    :param self.where_conditions: where条件
    eg.
        chart_filters = ChartFilterFormatter(model=ChartDataModel()).conditions()
        chart_linkages = ChartLinkageFormatter(model=ChartDataModel()).conditions()
        conditions = chart_filters + chart_linkages
    """

    __metaclass__ = ABCMeta
    section_map = {
        "between": [list],
        "not between": [list],
        "in": [list],
        "not in": [list]
    }  # 范围区间的条件

    def __init__(self, model):
        self.chart_data_model = model
        self.condition_models = list()
        self.where_conditions = list()
        self.label_dataset_key = "big_lab_dataset"

    @abstractmethod
    def format(self):
        """
        获取格式化的查询model(子类强制覆盖实现此方法)
        :return:
        """
        raise NotImplementedError("Please Implement this method")

    @staticmethod
    def _check_select_all_type(select_type):
        """
        处理选择类型（单选select，多选select，全选checkedAll）
        :param select_type: 0 or 1
        :return: bool
        """
        if select_type and int(select_type) in [DashboardFilterSelectType.SelectAll.value]:
            return True
        return False

    @staticmethod
    def is_bulitin_string(value):
        """
        判断是否内置的字符串类型
        :param value:
        :return:
        """
        return isinstance(value, str)

    @staticmethod
    def is_bulitin_list(value):
        """
        判断是否内置的列表类型
        :param value:
        :return:
        """
        return isinstance(value, list)

    @staticmethod
    def is_bulitin_int(value):
        """
        判断是否内置的数值类型
        :param value:
        :return:
        """
        return isinstance(value, int)

    @staticmethod
    def is_bulitin_float(value):
        """
        判断是否内置的浮点类型
        :param value:
        :return:
        """
        return isinstance(value, float)

    @staticmethod
    def is_keyword_value(value):
        """
        判断是否是带关键字标记的值
        :param value:
        :return:
        """
        return FILTER_KEYWORD_PREFIX in str(value)

    @staticmethod
    def match_formula_mode(col_value):
        """
        正则表达式匹配时间格式
        :param col_value:
        :return:
        """
        try:
            if not col_value:
                return None
            pattern_dict = functions.get_formula_mode_patterns()
            for pattern, formula_mode in pattern_dict.items():
                res = re.compile(pattern).match(col_value)
                if res and len(res.groups()):
                    return formula_mode
        except Exception as e:
            logging.exception(msg=f"单图取数时间格式条件正则匹配异常：{str(e)}")
        return None

    @staticmethod
    def deal_with_formula_params(orig_formula_mode, dataset_data_type, orig_operator, orig_data_value):
        """
        处理转换格式参数
        :param orig_formula_mode: 原始的fomula_model
        :param dataset_data_type: 数值类型
        :param orig_operator:  操作符
        :param orig_data_value:  原始值
        :return: dict()
        """
        field_func, field_func_format = "", ""
        operator = orig_operator
        result_dict = dict()

        # 已有formula_mode参数
        if orig_formula_mode and (getattr(g, 'data_index', 0) != 1):
            field_func = "date_format"
            field_func_format = functions.get_time_formula_mode(orig_formula_mode)
        else:
            if dataset_data_type == DatasetFieldDataType.Datetime.value and (getattr(g, 'data_index', 0) != 1):
                field_func = "date_format"
                field_func_format = functions.get_time_formula_mode(ChartDimFormulaMode.Day.value)
        if field_func and not field_func_format:
            field_func = ""

        new_data_value = copy.deepcopy(orig_data_value)
        # 统一转换=和!=操作符
        if field_func == "date_format":
            if orig_operator == SqlWhereOperator.Eq.value and orig_data_value is not None:
                operator = SqlWhereOperator.Like.value
            elif orig_operator == SqlWhereOperator.Neq.value and orig_data_value is not None:
                operator = SqlWhereOperator.Nlike.value
            elif orig_operator == SqlWhereOperator.Eq.value and orig_data_value is None:
                operator = SqlWhereOperator.IsNull.value
                new_data_value = None
            elif orig_operator == SqlWhereOperator.Neq.value and orig_data_value is None:
                operator = SqlWhereOperator.IsNotNull.value
                new_data_value = None

        result_dict["field_func"] = field_func
        result_dict["field_func_format"] = field_func_format
        result_dict["operator"] = operator
        result_dict["col_value"] = new_data_value
        return result_dict

    @staticmethod
    def _deal_with_operator_null_or_not_null(operator, field_value, value_type):
        """
        支持操作符为空，不为空
        此方法自行组装查询用的操作符以及字段值
        :param operator:
        :param field_value:
        :param value_type:
        :return:
        """
        if not isinstance(operator, str):
            return operator, field_value, value_type
        if operator.upper() in [SqlWhereOperator.IsNull.value, SqlWhereOperator.IsNotNull.value]:
            split_data = str(operator).split(' ')
            if len(split_data) == 2:
                operator = split_data[0]
                field_value = split_data[-1]
            elif len(split_data) == 3:
                operator = ' '.join(split_data[:2])
                field_value = split_data[-1]
            value_type = FieldValueType.Column.value
        return operator, field_value, value_type

    @staticmethod
    def get_field_value_type(operator, value):
        """
        获取条件值数据类型
        :param str operator: 操作符 < > 等
        :param str|bool|int|float|list value:
        :return:
        """
        if operator:
            operator = str(operator).lower()
        operator_dict = {
            ">": FieldValueType.Number.value,
            ">=": FieldValueType.Number.value,
            "<": FieldValueType.Number.value,
            "<=": FieldValueType.Number.value,
            "=": FieldValueType.String.value,
            "!=": FieldValueType.String.value,
            "<>": FieldValueType.String.value,
            "like": FieldValueType.String.value,
            "not like": FieldValueType.String.value,
            "between": FieldValueType.List.value,
            "not between": FieldValueType.List.value,
            "in": FieldValueType.List.value,
            "not in": FieldValueType.List.value,
            "far": FieldValueType.Datetime.value,
            "far_yesterday": FieldValueType.Datetime.value,
            "from_week": FieldValueType.Datetime.value,
            "from_month": FieldValueType.Datetime.value,
            "from_quarter": FieldValueType.Datetime.value,
            "from_year": FieldValueType.Datetime.value,
            "today": FieldValueType.Datetime.value,
            "yesterday": FieldValueType.Datetime.value,
            "lastweek": FieldValueType.Datetime.value,
            "lastmonth": FieldValueType.Datetime.value,
            "lastquarter": FieldValueType.Datetime.value,
            "lastyear": FieldValueType.Datetime.value,
            "day": FieldValueType.Datetime.value,
            "hour": FieldValueType.Datetime.value,
            "minute": FieldValueType.Datetime.value,
            "second": FieldValueType.Datetime.value,
            "is": FieldValueType.Null.value,
            "is not": FieldValueType.Null.value,
        }
        if isinstance(value, (int, float)):
            return FieldValueType.Number.value
        return operator_dict.get(operator)

    @staticmethod
    def string_to_escape(data):
        """
        转义
        :param data:
        :return:
        """
        try:
            data = mysql_escape_string(data)
        except Exception:
            return data
        return data

    def generate_where_field(self, **kwargs):
        where_field = WhereField()
        field_value = kwargs.get("field_value")
        formula_result_dict = kwargs.get("formula_result_dict", {})
        dn = kwargs.get('dn')
        operator = formula_result_dict.get('operator')
        cur_dataset_field_data = kwargs.get("cur_dataset_field_data")

        # 没有操作符，筛选条件则不生效
        if not operator:
            return None

        # 判断字段的value_type
        main_value_type = kwargs.get("main_value_type") or FieldValueType.String.value
        if field_value is not None or formula_result_dict.get("field_func"):
            main_value_type = FieldValueType.Column.value

        # 需要支持操作符为空，不为空，另外转换操作符
        operator, field_value, main_value_type = self._deal_with_operator_null_or_not_null(
            operator, field_value, main_value_type
        )

        # 处理字段值
        value_type, field_value = self._deal_with_field_value(operator, field_value, dn)

        # 字段值也是一个condition
        field_value_kwargs = {
            "col_name": field_value,
            "value_type": value_type,
            "table": cur_dataset_field_data.get('table_name'),
        }
        field_obj = FieldObj(**field_value_kwargs)
        field_obj.value_type = value_type
        where_field.field_value = field_obj

        where_field.operator = operator
        where_field.dn = dn
        where_field.value_type = main_value_type
        where_field.field = cur_dataset_field_data.get('col_name', '')
        where_field.field_type = cur_dataset_field_data.get('type', '')
        where_field.field_ref = cur_dataset_field_data.get('id', '')
        where_field.expressions = cur_dataset_field_data.get('expressions', '')
        where_field.table = cur_dataset_field_data.get('table_name', '')
        where_field.alias = ""
        where_field.logic = kwargs.get('logic', 'AND')
        where_field = self._deal_with_advanced_field(where_field, formula_result_dict.get("field_func"))
        return where_field

    def append_single_condition(self, *args, **kwargs):
        """
        追加新条件
        ps：优化后的方法，在此方法中统一加上数据集字段的相关数据
        :param args:
        :param kwargs:
        :return:
        """
        where_field_obj = self.generate_where_field_by_single_condition(**kwargs)
        if where_field_obj == False:
            return self.where_conditions
        self.where_conditions.append(where_field_obj)
        return self.where_conditions

    def generate_where_field_by_single_condition(self, **kwargs):
        """
        通过single_condition 生成 where_field
        :param kwargs:
        :return WhereField|False 如果不满足生成条件 则返回False 否则返回字段对象:
        """
        field_value = kwargs.get("field_value")
        formula_result_dict = kwargs.get("formula_result_dict")
        operator = formula_result_dict.get('operator')
        if field_value == "*" and operator in [
            SqlWhereOperator.Gt.value,
            SqlWhereOperator.Lt.value,
            SqlWhereOperator.Eq.value,
            SqlWhereOperator.Gte.value,
            SqlWhereOperator.Lte.value,
            SqlWhereOperator.Neq.value,
            SqlWhereOperator.Like.value,
        ]:
            return False
        where_field = self.generate_where_field(**kwargs)
        where_field = self.deal_between_syntax_with_asterisk(where_field, **kwargs)
        if where_field and isinstance(where_field, WhereField):
            return where_field
        return False

    def append_single_condition_by_complex(self, *args, **kwargs):
        """
        使用complex方式追加where条件
        :param args:
        :param kwargs:
        :return:
        """
        field_value = kwargs.get("field_value")
        formula_result_dict = kwargs.get("formula_result_dict")
        operator = formula_result_dict.get('operator')
        if not self.is_bulitin_list(self.where_conditions):
            return self.where_conditions
        if field_value == "*" and operator in [
            SqlWhereOperator.Gt.value,
            SqlWhereOperator.Lt.value,
            SqlWhereOperator.Eq.value,
            SqlWhereOperator.Gte.value,
            SqlWhereOperator.Lte.value,
            SqlWhereOperator.Neq.value,
            SqlWhereOperator.Like.value,
        ]:
            return
        where_field = self.generate_where_field(**kwargs)
        where_field = self.deal_between_syntax_with_asterisk(where_field, **kwargs)
        return where_field

    def deal_between_syntax_with_asterisk(self, where_field, **kwargs):
        """
        # 处理between时间类型的语法
        :param args:
        :param kwargs:
        :return:
        """
        field_value = kwargs.get("field_value", '')
        formula_result_dict = kwargs.get("formula_result_dict", {})
        operator = formula_result_dict.get('operator', '')
        if (
                "*" in str(field_value)
                and operator.upper() == SqlWhereOperator.Between.value
                # and formula_result_dict.get('field_func') == 'date_format'
        ):
            new_kwargs = self._deal_between_syntax_with_asterisk(kwargs, field_value)
            if not new_kwargs:
                return
            new_where_field = self.generate_where_field(**new_kwargs)
            if new_kwargs.pop('__from_mock__', False):
                new_where_field.field_value.value_type = FieldValueType.String.value
            return new_where_field
        else:
            return where_field

    def _deal_between_syntax_with_asterisk(self, kwargs, field_value):
        # 处理 between '["*","today"]' 语法，*表示全部时间，
        # 将该格式降级为 >= 或者 <=
        field_value_list = self._load_list(field_value)
        if not field_value_list:
            return kwargs
        start, end = field_value_list[0], field_value_list[1]
        if start == "*" and end != "*":
            real_operator = SqlWhereOperator.Lte.value
            real_field_value = end
            kwargs = self.mock_kwargs(kwargs, real_operator, real_field_value)
        elif start != "*" and end == "*":
            real_operator = SqlWhereOperator.Gte.value
            real_field_value = start
            kwargs = self.mock_kwargs(kwargs, real_operator, real_field_value)
        elif start == "*" and end == "*":
            return {}
        return kwargs

    def mock_kwargs(self, kwargs: dict, real_operator, real_field_value):
        new_kwargs = copy.deepcopy(kwargs)
        new_kwargs['operator'] = real_operator
        new_kwargs['field_value'] = real_field_value
        formula_result_dict = new_kwargs.get("formula_result_dict", {})
        formula_result_dict['operator'] = real_operator
        formula_result_dict['col_value'] = real_field_value
        dn = new_kwargs.get("dn", {})
        dn['operator'] = real_operator
        dn['col_value'] = real_field_value
        new_kwargs['__from_mock__'] = True
        return new_kwargs

    def _load_list(self, value):
        if isinstance(value, list):
            return value
        elif isinstance(value, str):
            try:
                return json.loads(value)
            except Exception as e:
                logging.error(f'转换between的value<{value}>出错：{str(e)}')
                return []
        return []

    def string_to_int(self, value):
        """
        转换为整数类型
        :param value:
        :return:
        """
        try:
            return int(value)
        except Exception:
            return self.string_to_escape(value)

    def string_to_float(self, value):
        """
        转换为浮点数类型
        :param value:
        :return:
        """
        try:
            return float(value)
        except Exception:
            return self.string_to_escape(value)

    @staticmethod
    def _check_in_regular_expression(data, pattern):
        """
        正则表达式匹配
        :param data:
        :param pattern:
        :return:
        """
        if re.compile(pattern).search(data):
            return True
        return False

    def _deal_with_number_type(self, value, dataset_data_type):
        """
        特殊处理数值类型的查询条件值
        :param value:
        :param dataset_data_type:
        :return:
        """
        # 只处理本身是字符串类型的条件值
        if not value or not self.is_bulitin_string(value):
            return value

        # 正负整数
        if dataset_data_type in [DatasetFieldDataType.Number.value] and self._check_in_regular_expression(
                value, pattern="^-?0$|^-?[1-9]\d*$"
        ):
            value = self.string_to_int(value)
        # 正负浮点数
        elif dataset_data_type in [DatasetFieldDataType.Number.value] and self._check_in_regular_expression(
                value, pattern="^-?0\.\d+$|^-?[1-9]\d*\.\d+$"
        ):
            value = self.string_to_float(value)
        # 字符串
        elif value and self.is_bulitin_string(value):
            value = self.string_to_escape(value)
        return value

    @staticmethod
    def _str_to_loads(value_type, field_value):
        """
        处理json字符串类型的字段值
        :param value_type:
        :param field_value:
        :return:
        """
        # 处理字段值string转义
        if value_type == FieldValueType.List.value and isinstance(field_value, str):
            try:
                field_value = json.loads(field_value)
            except Exception as e:
                _debugger.log('加载col_value失败，错误信息: %s', str(e))
        return field_value

    def _deal_with_field_value(self, operator, field_value, dn):
        """
        处理字段值
        :param operator:
        :param field_value:
        :return:
        """
        # 判断值的value_type
        value_type = self.get_field_value_type(operator, field_value)

        # 处理字段值string转义
        field_value = self._str_to_loads(value_type, field_value)

        # 根据数据集字段定义的数据类型，对数值类型的查询条件值做下类型转换
        dataset_data_type = dn.get("data_type") if dn else ""

        if field_value and self.is_bulitin_string(field_value):
            field_value = self._deal_with_number_type(field_value, dataset_data_type)

        elif field_value and self.is_bulitin_list(field_value):
            for idx, data in enumerate(field_value):
                field_value[idx] = self._deal_with_number_type(data, dataset_data_type)

        # 模糊查询需要加上%%
        if operator.upper() in [SqlWhereOperator.Like.value] and self.is_bulitin_string(field_value):
            field_value = '%' + field_value + '%'
        elif operator.upper() in [SqlWhereOperator.Like.value] and (
                self.is_bulitin_int(field_value) or self.is_bulitin_float(field_value)
        ):
            field_value = '%' + self.string_to_escape(str(field_value)) + '%'

        return value_type, field_value

    def _deal_keyword_value(self, col_name, operator, field_value):
        # 处理条件的值是关键字的场景
        keyword_id = Formatter.get_keyword_id(operator, field_value)
        keyword_id = keyword_id.replace(FILTER_KEYWORD_PREFIX, '').strip()
        keyword_values = self.chart_data_model.keyword_values or {}
        real_value = keyword_values.get(keyword_id, [None])

        # scalar 如果只有一个值，直接提取出来
        if operator not in Formatter.section_map and isinstance(real_value, list) and len(real_value) == 1:
            real_value = real_value[0]

        self._check_condition_match(col_name, operator, real_value)
        return real_value

    @staticmethod
    def get_keyword_id(operator, col_value):
        if operator in Formatter.section_map:
            #  提取区间的关键字
            try:
                real_col_value = json.loads(col_value)
                real_col_value = real_col_value[0]
            except:
                real_col_value = col_value
        else:
            real_col_value = col_value
        return real_col_value

    @staticmethod
    def _check_condition_match(col_name, operator, real_value):
        match_rules = {
            ">": [str, int],
            ">=": [str, int],
            "<": [str, int],
            "<=": [str, int],
            "=": [str, int],
            "!=": [str, int],
            "<>": [str, int],
            "like": [str],
            "not like": [str],
            "between": [list],
            "not between": [list],
            "in": [list],
            "not in": [list],
            "is": [type(None)],
            "is not": [type(None)],
        }
        rule = match_rules.get(operator)
        if not rule:
            formatted = Formatter._format_real_value(real_value)
            raise SqlProcessingException(
                code=503, message=f'条件匹配错误：[{col_name}]字段过滤条件【{operator}出现未知错误，关键字值：【{formatted}】!'
            )
        if type(real_value) not in rule:
            formatted = Formatter._format_real_value(real_value)
            raise SqlProcessingException(
                code=503, message=f'条件匹配错误：[{col_name}]字段过滤条件【{operator}】不支持与关键字多值【{formatted}】一起使用!'
            )

    @staticmethod
    def _format_real_value(real_value):
        if isinstance(real_value, (list, dict, str, int, float)):
            formatted = json.dumps(real_value).strip('[').strip(']')
        else:
            formatted = str(real_value)
        return formatted

    def _deal_with_advanced_field(self, single_where_field: WhereField, field_func=""):
        """
        处理高级字段拼装
        :param single_where_field:
        :return:
        """
        dn = single_where_field.dn
        single_dataset_field = self.chart_data_model.dataset_field_dict.get(dn.get("dataset_field_id"))
        expression = single_where_field.expressions if single_where_field.expressions else None

        if not dn or not single_dataset_field:
            return single_where_field
        # 不同步标签控件字段名称需要添加formula_mode
        if dn.get("formula_mode") and self.chart_data_model.dataset.get(self.label_dataset_key):
            col_name = dn.get("formula_mode") + "_" + single_dataset_field.get("col_name")
            single_where_field.field = col_name
        elif dn.get("formula_mode") and field_func:
            single_where_field.field = single_dataset_field.get("col_name")
            single_where_field.expressions = None
            single_where_field.get_field(
                ['date_format'], [{'field': functions.get_time_formula_mode(dn.get("formula_mode"))}], expression
            )
        else:
            single_where_field.field = single_dataset_field.get("col_name")
        return single_where_field

    def conditions(self):
        """
        获取条件对象数组
        :return:
        """
        # 先执行format方法
        self.format()

        # format后没有条件的直接返回空数组
        if not self.where_conditions or not len(self.where_conditions):
            return self.condition_models

        # 组装格式化后的条件model
        for single_where_field in self.where_conditions:
            if not single_where_field:
                continue

            # 统一调用此方法实例化一个条件model
            self.condition_models.append(single_where_field)
        return self.condition_models

    @staticmethod
    def get_logic(filter_relation):
        """
        根据filter_relation值返回与或连接值 0：and 1：or
        :param filter_relation: 0， 1
        :return:
        """
        return 'OR' if filter_relation else 'AND'
