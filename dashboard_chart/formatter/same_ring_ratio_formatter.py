#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/9/15
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from base.errors import UserError
from dashboard_chart.formatter.formatter import Formatter
from base.enums import (
    ChartConditionType,
)

logger = logging.getLogger(__name__)


class SRRatioFormatter(Formatter):
    """
    同环比计算中生成自定义where条件
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.fields_list = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.dataset_field_dict = self.chart_data_model.dataset_field_dict

    def set_condition(self, fields_list):
        self.fields_list = fields_list

    def format(self):
        """
        执行格式化
        :return:
        """
        if not self.fields_list:
            return self
        for single_condition in self.fields_list:
            field_id = single_condition.get('field_id')
            if not field_id:
                raise UserError(message='error field_id!')
            cur_dataset_field_data = self.dataset_field_dict.get(field_id)
            if not cur_dataset_field_data:
                raise UserError(message='数据异常，无法获取数据集！')

            # 处理formula_mode
            formula_mode = single_condition.get('formula_mode', None)
            _created_by = single_condition.get('_created_by', '')
            formula_result_dict = self.deal_with_formula_params(
                formula_mode,
                single_condition.get('data_type'),
                single_condition.get('operator'),
                single_condition.get('col_value'),
            )
            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.DashboardFilter.value,
                _created_by=_created_by,
            )
            self.append_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
            )
        return self
