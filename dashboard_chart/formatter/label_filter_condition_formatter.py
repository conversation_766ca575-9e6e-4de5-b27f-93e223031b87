#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
label filter
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------

from base.errors import UserError
from dashboard_chart.formatter.formatter import Formatter
from base.enums import ChartConditionType

logger = logging.getLogger(__name__)


class LabelFilterFormatter(Formatter):
    """
    列表筛选
    :param self.dataset_field_dict: 数据集数据字典
    :param self.filters: 原始筛选条件数组
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.label_filter_conditions = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )
        self.label_filter_conditions = (
            self.chart_data_model.label_filter_conditions if self.chart_data_model.label_filter_conditions else list()
        )

    def format(self):
        """
        执行格式化
        :return:
        """
        if not self.label_filter_conditions or not len(self.label_filter_conditions):
            return self
        for single_label_filter in self.label_filter_conditions:
            operate_dim_data = single_label_filter.get("dim", {})
            cur_dataset_field_data = self.dataset_field_dict.get(single_label_filter.get("dataset_field_id", ""))
            if not cur_dataset_field_data:
                raise UserError(message='数据异常，无法获取数据集！')

            formula_result_dict = self.deal_with_formula_params(
                operate_dim_data.get("formula_mode"),
                single_label_filter.get('data_type'),
                single_label_filter.get('operator'),
                single_label_filter.get('col_value'),
            )
            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=operate_dim_data.get("formula_mode"),
                condition_type=ChartConditionType.LabelFilter.value,
            )
            self.append_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
            )
        return self
