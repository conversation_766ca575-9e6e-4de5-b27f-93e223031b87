#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
chart filter
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from collections import defaultdict

from base.errors import UserError
from dashboard_chart.models import ChartFilterModel
from dashboard_chart.formatter.formatter import Formatter
from base.enums import DatasetFieldDataType, ChartDimFormulaMode, ChartConditionType

logger = logging.getLogger(__name__)


class ChartFilterFormatter(Formatter):
    """
    单图筛选
    :param self.dataset_field_dict: 数据集数据字典
    :param self.filters: 原始筛选条件数组
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.filters = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )
        self.filters = self.chart_data_model.filters if self.chart_data_model.filters else list()

    def _format_filters(self, filter_relation_list, dataset_field_id, where_field, complex_where):
        filter_relation = 0
        for single_filter in filter_relation_list:
            filter_relation = single_filter.get('filter_relation')
            cur_dataset_field_data = self.dataset_field_dict.get(dataset_field_id)
            if not cur_dataset_field_data:
                raise UserError(message='未查询到对应数据集字段')

            # 处理formula_mode
            formula_mode = (
                ChartDimFormulaMode.Day.value
                if cur_dataset_field_data.get("data_type") == DatasetFieldDataType.Datetime.value
                else None
            )
            formula_result_dict = self.deal_with_formula_params(
                None, single_filter.get('data_type'), single_filter.get('operator'), single_filter.get('col_value')
            )

            # 处理替换关键字的值
            field_value = single_filter.get('col_value')
            if field_value and self.is_keyword_value(field_value):
                field_value = self._deal_keyword_value(
                    cur_dataset_field_data.get('alias_name'),
                    single_filter.get('operator'), field_value
                )
                formula_result_dict['col_value'] = field_value

            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.InnerFilter.value,
            )
            where_item = self.append_single_condition_by_complex(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
                logic='AND',
            )
            if not where_item:
                continue
            if where_field:
                complex_where.append(where_item)
                where_field.dn.append(where_item.dn)
            else:
                where_field = where_item
                where_field.dn = [where_item.dn]
        return where_field, filter_relation

    def format(self):
        """
        执行格式化
        :return:
        """
        if not self.filters:
            return self
        filter_relation_dict = defaultdict(list)
        for single_filter in self.filters:
            chart_filter_model = ChartFilterModel(**single_filter)
            dataset_field_id = (
                chart_filter_model.dataset_field.get("id")
                if chart_filter_model.dataset_field
                else chart_filter_model.dataset_field_id
            )
            if not dataset_field_id:
                continue
            filter_relation_dict[dataset_field_id].append(single_filter)

        for dataset_field_id, filter_relation_list in filter_relation_dict.items():
            complex_where = []
            where_field = None
            where_field, filter_relation = self._format_filters(
                filter_relation_list, dataset_field_id, where_field, complex_where
            )

            if not where_field:
                continue
            where_field.logic = self.get_logic(filter_relation)
            if complex_where:
                where_field.complex = complex_where

            self.where_conditions.append(where_field)
        return self
