#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
chart filter
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from base.errors import UserError
from dashboard_chart.formatter.formatter import Formatter
from base.enums import ChartConditionType, FieldValueType

logger = logging.getLogger(__name__)


class DrillFormatter(Formatter):
    """
    单图钻取
    :param self.dataset_field_dict: 数据集数据字典
    :param self.filters: 原始筛选条件数组
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.drill_conditions = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )
        self.drill_conditions = (
            self.chart_data_model.drill_conditions if self.chart_data_model.drill_conditions else list()
        )

    def format(self):
        """
        执行格式化
        :return:
        """
        if not self.drill_conditions or not len(self.drill_conditions):
            return self
        for condition in self.drill_conditions:
            operate_dim_data = condition.get("dim", {})
            dataset_field_id = operate_dim_data.get("dataset_field_id", '')
            cur_dataset_field_data = self.dataset_field_dict.get(dataset_field_id)
            if not cur_dataset_field_data:
                raise UserError(message=f'数据异常，无法获取字段<{dataset_field_id}>！')
            formula_result_dict = self.deal_with_formula_params(
                operate_dim_data.get("formula_mode"),
                condition.get('data_type'),
                condition.get('operator'),
                condition.get('col_value'),
            )
            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=condition.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=operate_dim_data.get("formula_mode"),
                condition_type=ChartConditionType.DrillFilter.value,
            )
            self.append_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
                main_value_type=FieldValueType.Column.value,
            )

        return self
