#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
component filter
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from base.enums import ChartConditionType
from dashboard_chart.formatter.formatter import Formatter


logger = logging.getLogger(__name__)


class ComponentFilterFormatter(Formatter):
    """
    组件筛选
    :param self.dataset_field_dict: 数据集数据字典
    :param self.filter_conditions: 原始筛选条件数组
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.filter_conditions = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.filter_conditions = (
            self.chart_data_model.filter_conditions if self.chart_data_model.filter_conditions else []
        )
        self.dataset_field_dict = self.chart_data_model.dataset_field_dict

    def format(self):
        """
        执行格式化
        :return:
        """
        if not len(self.filter_conditions):
            return self
        for single_filter_condition in self.filter_conditions:
            _field_id = single_filter_condition.get('field_id')
            if not _field_id:
                continue
            if _field_id not in self.dataset_field_dict:
                continue
            dim = single_filter_condition.get('dim')
            if not dim:
                continue
            cur_dataset_field_data = self.dataset_field_dict.get(_field_id)

            # 校验是否有全选标志位
            select_all_flag = self._check_select_all_type(single_filter_condition.get('select_type'))
            if select_all_flag:
                continue

            # 处理formula_mode
            formula_mode = dim.get('formula_mode')
            formula_result_dict = self.deal_with_formula_params(
                formula_mode,
                dim.get('data_type'),
                single_filter_condition.get('operator'),
                single_filter_condition.get('col_value'),
            )

            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.ComponentFilter.value,
            )
            self.append_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
            )
        return self
