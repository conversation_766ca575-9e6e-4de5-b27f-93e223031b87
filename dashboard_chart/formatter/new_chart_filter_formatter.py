import logging
import math

from base.enums import ChartConditionType, SqlWhereOperator, ChartDimFormulaMode, FixedValueType, DatasetFieldDataType
from dashboard_chart.formatter.chart_linkage_formatter import ChartLinkageFormatter
from dashboard_chart.services import chart_service
from dmplib.utils.errors import UserError
from dashboard_chart.convertor.field_types import WhereField
from components import functions

logger = logging.getLogger(__name__)

MAX_IN_CODITION_SIZE = 2000  # in 条件的最大值

# pylint: disable=R0201


class NewChartFilterFormatter(ChartLinkageFormatter):
    def __init__(self, model):
        self.chart_responder_id = None
        self.request_conditions = None
        self.selector_data_dict = None
        self.dataset_field_dict = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        self.chart_responder_id = self.chart_data_model.id
        # todo 此处逻辑有漏洞 不应该查库 应该直接从model中获取
        self.selector_data_dict = self.chart_data_model.filter_selector_dict
        self.request_conditions = self.chart_data_model.chart_filter_conditions
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )

    # pylint: disable=R1710
    def format(self):

        logger.debug("enter filter formatter")

        if not self.is_bulitin_list(self.request_conditions) or len(self.request_conditions) <= 0:
            return self

        logger.debug("filter formatter selector dict:%s", self.selector_data_dict)

        for single_condition in self.request_conditions:
            where_fields = self._transform_single_condition2where_fields(single_condition)
            if not where_fields:
                continue
            # 如果单个条件转换后的where_fields 为多个 则这些where_field形成一组，该组的logic 是 and
            if len(where_fields) > 1:
                where_field = WhereField()
                where_field.logic = "AND"
                where_field.complex = where_fields
            else:
                where_field = where_fields[0]
            self.where_conditions.append(where_field)

    def _convert_fixed_value_type2dim_data_type(self, fixed_value_type):
        if fixed_value_type == FixedValueType.String.value:
            dim_data_type = DatasetFieldDataType.Description.value
        elif fixed_value_type == FixedValueType.Number.value:
            dim_data_type = DatasetFieldDataType.Number.value
        elif fixed_value_type == FixedValueType.Datetime.value:
            dim_data_type = DatasetFieldDataType.Datetime.value
        else:
            dim_data_type = ""
        return dim_data_type

    def _get_fixed_value_formula_info(self, fixed_value_type):
        formula_mode, data_type, func_format = "", "", functions.get_time_formula_mode(ChartDimFormulaMode.Second.value)
        if fixed_value_type == FixedValueType.Datetime.value:
            formula_mode = ChartDimFormulaMode.Second.value
            data_type = DatasetFieldDataType.Datetime.value
        elif fixed_value_type == FixedValueType.String.value:
            formula_mode, data_type, func_format = '', '', ''
        return formula_mode, data_type, func_format

    def _transform_single_condition2where_fields(self, single_condition):
        """
        转换单个condition
        此处单个condition有可能被转换为多个 若为多个condition 则这些condition要为一组 使用 () 包含 形成复杂where_field
        该where_field 的logic 为 and
        :param single_condition:
        :return:
        """
        transformed_conditions = self._transform_indirect_condition(single_condition)
        where_fields = []
        tmp_where_str_sql = []
        for transformed_condition in transformed_conditions:
            initiator_data = transformed_condition.get("dim") or transformed_condition.get("initiator")
            responder_list = self.selector_data_dict.get(transformed_condition.get("chart_id"))
            logger.debug(
                "filter formatter single_condition:%s, responder_list:%s", transformed_condition, responder_list
            )
            for idx, responder in enumerate(responder_list):
                where_field = self._generate_where_field_for_each_responder(
                    initiator_data, responder, transformed_condition
                )
                if where_field is not None and \
                        self.add_dataset_conditions(where_field, initiator_data, tmp_where_str_sql):
                    where_fields.append(where_field)
        return where_fields

    def add_dataset_conditions(self, where_field, initiator_data, tmp_where_str_sql: list):
        # 过滤同数据集的where条件
        if initiator_data.get('same_dataset_col_value') != 1:
            # 不是同数据集的情况，直接添加
            return True

        # 同数据集的情况会出现重复的条件
        # https://www.tapd.cn/38229611/prong/tasks/view/1138229611001282302
        from base.query_builder.query import Query
        from dashboard_chart.agent.query_agent import QueryAgent

        # 通过最后生成的whereSQL来判断条件是否重复
        data = {
            "where": [where_field],
            "dataset_field_dict": self.chart_data_model.dataset_field_dict,
        }
        query_agent = QueryAgent()
        where = query_agent._convert_where(data)
        if not where:
            return True
        query = Query()
        query.where(where)
        where_struct = query.build(output_format="json")
        if where_struct not in tmp_where_str_sql:
            tmp_where_str_sql.append(where_struct)
            return True
        return False

    def _generate_where_field_for_each_responder(self, initiator_data, responder, transformed_condition):
        where_field = None
        # 单值模式
        if transformed_condition.get("initiator"):
            # 非当前条件 跳出
            if initiator_data.get("id") != responder["field_initiator_id"]:
                return where_field
            fixed_value = transformed_condition.get("initiator", {})
            if not fixed_value:
                return where_field
            # TODO(Judy): 暂时后端特殊处理, 以前对时间筛选器的优化, 去除了date_format, 但这在其他引擎可能会有问题
            # 正确的做法应该是前端传递正确的value_type, 后端针对引擎和条件类型进行优化
            value_type = fixed_value.get("value_type")
            if self.chart_data_model.external_subject_ids and fixed_value.get('identifier') == 'date_range':
                value_type = 'datetime'
            formula_mode, data_type, func_format = self._get_fixed_value_formula_info(value_type)
            formula_result_dict = self.deal_with_formula_params(
                formula_mode, data_type, transformed_condition.get("operator"), transformed_condition.get("col_value")
            )
            dim_extra_data = chart_service.get_union_field_data(
                self.chart_responder_id, responder.get("field_responder_id"), self.chart_data_model
            )
            dn = dict(
                alias_name=dim_extra_data.get('alias_name'),
                col_name=dim_extra_data.get('col_name'),
                col_value=formula_result_dict.get("col_value"),
                operator=formula_result_dict.get("operator"),
                dataset_field_id=dim_extra_data.get("id"),
                data_type=dim_extra_data.get("data_type"),
                format=func_format,
                formula_mode=formula_mode,
                condition_type=ChartConditionType.ComponentFilter.value,
            )
            where_field = self.generate_where_field_by_single_condition(
                field_value=formula_result_dict.get("col_value"),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=responder,
                dn=dn,
                logic=transformed_condition.get("logic", "AND"),
            )
        elif (
            initiator_data["dataset_id"] == responder["dataset_id"]
            and (responder["field_initiator_id"] == initiator_data["dim"] or initiator_data.get("same_dataset_col_value") == 1)
        ):  # 相同数据集的情况下
            formula_mode = initiator_data.get("formula_mode")
            formula_result_dict = self.deal_with_formula_params(
                formula_mode,
                transformed_condition.get("data_type"),
                transformed_condition.get("operator"),
                transformed_condition.get("col_value"),
            )
            dim_data = dict(
                alias_name=initiator_data.get("alias_name"),
                col_name=initiator_data.get("col_name"),
                col_value=formula_result_dict.get("col_value"),
                operator=formula_result_dict.get("operator"),
                dataset_field_id=initiator_data.get("dim"),
                data_type=transformed_condition.get("data_type"),
                format=initiator_data.get("format"),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.ComponentFilter.value,
            )
            where_field = self.generate_where_field_by_single_condition(
                field_value=formula_result_dict.get("col_value"),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=responder,
                dn=dim_data,
                logic=transformed_condition.get("logic", "AND"),
            )
        elif (
            responder["field_initiator_id"] == initiator_data["dim"]
            and initiator_data["dataset_id"] != responder["dataset_id"]
        ):  # 不同数据集的情况

            dim_extra_data = chart_service.get_union_field_data(
                self.chart_responder_id, responder.get("field_responder_id"), self.chart_data_model
            )

            formula_result_dict = self.deal_with_formula_params(
                initiator_data.get("formula_mode"),
                transformed_condition.get("data_type"),
                transformed_condition.get("operator"),
                transformed_condition.get("col_value"),
            )

            dim_data = {
                "alias_name": dim_extra_data.get('alias_name'),
                "col_value": formula_result_dict.get("col_value"),
                "col_name": transformed_condition.get("col_name"),
                "operator": formula_result_dict.get("operator"),
                "dataset_field_id": dim_extra_data.get("id"),
                "data_type": dim_extra_data.get("data_type"),
                "format": initiator_data.get("format"),
                "formula_mode": initiator_data.get("formula_mode"),
                "condition_type": ChartConditionType.ComponentFilter.value,
            }
            logger.debug("in filter formatter, dim data:%s", dim_data)
            where_field = self.generate_where_field_by_single_condition(
                field_value=formula_result_dict.get("col_value"),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=responder,
                dn=dim_data,
                logic=transformed_condition.get("logic", "AND"),
            )
        return where_field

    def _transform_indirect_condition(self, single_condition) -> list:
        """
        转换间接查询, 模拟为和普通查询一致的参数
        :param single_condition:
        :return:
        """
        conditions = []
        if single_condition["col_value"] is None and single_condition.get("value_from"):
            chart_id = single_condition.get("chart_id")
            indirect_data = self.chart_data_model.indirect_query_map.get(chart_id)
            if not indirect_data:
                raise UserError(message="无法获取到间接查询单图 {chart_id} 条件值".format(chart_id=chart_id))
            dataset_field_id = single_condition.get("dataset_field_id")
            initiator_field_info = indirect_data["dataset_field_dict"].get(dataset_field_id)
            if not initiator_field_info:
                raise UserError(
                    message="无法获取到间接查询发起字段 {dataset_field_id} 字段信息 ".format(dataset_field_id=dataset_field_id)
                )
            # 进行字段转换 模拟dim
            responder_list = self.selector_data_dict.get(chart_id)
            for responder in responder_list:
                if responder["chart_initiator_id"] == chart_id and responder["field_initiator_id"] == dataset_field_id:
                    self._transform2conditions(
                        chart_id, conditions, indirect_data, initiator_field_info, single_condition
                    )
                    return conditions
        return [single_condition]

    def _transform2conditions(self, chart_id, conditions, indirect_data, initiator_field_info, single_condition):
        # 有别名的情况下优先取别名
        data_key = initiator_field_info.get("alias") or initiator_field_info.get("col_name")
        mock_dim = {
            "dashboard_chart_id": chart_id,
            "alias": initiator_field_info["alias_name"],
            "col_name": data_key,
            "data_type": initiator_field_info["data_type"],
            "dataset_field_id": initiator_field_info["id"],
            "dataset_id": initiator_field_info["dataset_id"],
            "dim": initiator_field_info["id"],
            "dim_type": 0,
            "expression": initiator_field_info.get("expression", None),
            "field_group": initiator_field_info["field_group"],
            "formula_mode": "",
            "id": None,
            "is_subtotal_cate": 0,
            "rank": initiator_field_info["rank"],
            "sort": "",
            "type": initiator_field_info["type"],
            "visible": initiator_field_info["visible"],
        }
        data = indirect_data.get("result") or {}
        col_data = data.get(data_key, {})
        if not col_data or not col_data.get("data"):
            raise UserError(message="筛选关联无法获取到值！")
        col_value = col_data["data"]
        has_null = col_data.get("has_null", 0)
        page_size = MAX_IN_CODITION_SIZE
        for num in range(math.ceil(len(col_value) / page_size)):
            logic = "AND" if num == 0 else "OR"
            conditions.append(
                {
                    "chart_id": chart_id,
                    "col_name": data_key,
                    "col_value": col_value[num * page_size : (num + 1) * page_size],
                    "operator": single_condition["operator"],
                    "dim": mock_dim,
                    "logic": logic,
                }
            )
        # 有null值
        if has_null:
            conditions.append(
                {
                    "chart_id": chart_id,
                    "col_name": data_key,
                    "col_value": None,
                    "operator": SqlWhereOperator.IsNull.value,
                    "dim": mock_dim,
                    "logic": "OR",
                }
            )
