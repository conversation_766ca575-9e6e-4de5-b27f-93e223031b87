#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
chart penetrate filter

"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from collections import defaultdict

from base.enums import ChartConditionType, ChartDimFormulaMode, DatasetFieldDataType, DatasetVarValueSource
from dashboard_chart.convertor.field_types import WhereField
from dashboard_chart.formatter.formatter import Formatter
from dashboard_chart.services import chart_service
from dashboard_chart.services import proxy_dataset_service
from dmplib.utils.errors import UserError
from dataset import external_query_service

logger = logging.getLogger(__name__)


class ChartPenetrateFilterFormatter(Formatter):
    """
    穿透后筛选、联动模块
    :param self.chart_responder_id: 单图id
    :param self.conditions: 条件数组
    """

    def __init__(self, model):
        self.chart_responder_id = None
        self.penetrate_filter_conditions = None
        self.dataset_field_dict = None
        self.field_var_dict = {}
        self.used_field_list = []
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.chart_responder_id = self.chart_data_model.id
        self.penetrate_filter_conditions = self.chart_data_model.penetrate_filter_conditions
        self.dataset_field_dict = (
            self.chart_data_model.dataset_field_dict if self.chart_data_model.dataset_field_dict else dict()
        )

    def _format_chart_filter(self, chart_filter_list):
        where_fields = []
        filter_conditions = []
        if chart_filter_list:
            filter_relation = self.chart_data_model.filter_relation
            condition_type = ChartConditionType.InnerFilter.value
            items, filter_conditions = self.get_parent_chart_filter(chart_filter_list, filter_relation, condition_type)
            where_fields.extend(items)
            if filter_conditions:
                filter_conditions.extend(filter_conditions)

        if where_fields:
            where_field = WhereField()
            where_field.complex = where_fields
            where_field.dn = filter_conditions
            self.where_conditions.append(where_field)
        return where_fields

    def _format_linkage(self, linkage_filter_list):
        linkage_where_fields = []
        linkage_conditions = []
        where_fields = []
        if linkage_filter_list:
            condition_type = ChartConditionType.LinkageFilter.value
            items, filter_conditions = self.get_parent_chart_filter(linkage_filter_list, 0, condition_type)
            linkage_where_fields.extend(items)
            if filter_conditions:
                linkage_conditions.extend(filter_conditions)
        if linkage_where_fields:
            where_field = WhereField()
            where_field.complex = linkage_where_fields
            where_field.dn = linkage_conditions
            self.where_conditions.append(where_field)
            where_fields.append(where_field)
        return where_fields

    def _format_component_filter(self, component_filter_list):
        component_where_fields = []
        component_conditions = []
        where_fields = []
        if component_filter_list:
            condition_type = ChartConditionType.ComponentFilter.value
            items, filter_conditions = self.get_parent_chart_filter(component_filter_list, 0, condition_type)
            component_where_fields.extend(items)
            if filter_conditions:
                component_conditions.extend(filter_conditions)
        if component_where_fields:
            where_field = WhereField()
            where_field.complex = component_where_fields
            where_field.dn = component_conditions
            self.where_conditions.append(where_field)
            where_fields.append(where_field)
        return where_fields

    def _format_jump(self, jump_filter_list):
        # 处理跳转
        jump_where_fields = []
        jump_conditions = []
        where_fields = []
        if jump_filter_list:
            condition_type = ChartConditionType.DashboardJumpFilter.value
            items, filter_conditions = self.get_parent_chart_filter(jump_filter_list, 0, condition_type)
            jump_where_fields.extend(items)
            if filter_conditions:
                jump_conditions.extend(filter_conditions)
        if jump_where_fields:
            where_field = WhereField()
            where_field.complex = jump_where_fields
            where_field.dn = jump_conditions
            self.where_conditions.append(where_field)
            where_fields.append(where_field)
        return where_fields

    def format(self):
        if not self.penetrate_filter_conditions:
            return self
        chart_filter_list = []
        component_filter_list = []
        jump_filter_list = []
        linkage_filter_list = []
        where_fields = []
        conditions = []

        # 预先收集父级单图的变量id及其信息
        self._pre_assign_field_var()

        for single_condition in self.penetrate_filter_conditions:
            refs = {
                ChartConditionType.InnerFilter.value: chart_filter_list,
                ChartConditionType.LinkageFilter.value: linkage_filter_list,
                ChartConditionType.ComponentFilter.value: component_filter_list,
                ChartConditionType.DashboardJumpFilter.value: jump_filter_list,
            }
            # 单图内筛选需单独处理
            # 联动单独处理
            # 组件筛选单独处理
            # 跳转单独处理
            if single_condition.get('condition_type') in (
                ChartConditionType.InnerFilter.value,
                ChartConditionType.LinkageFilter.value,
                ChartConditionType.ComponentFilter.value,
                ChartConditionType.DashboardJumpFilter.value,
            ):
                refs[single_condition.get('condition_type')].append(single_condition)
                continue
            field_id = single_condition.get('dataset_field_id', '')
            # 如果传入的已经是转换过的字段id，则不再进行字段id转换
            real_field_id = (
                field_id if field_id in self.dataset_field_dict else self._get_current_dataset_field_id(field_id)
            )

            if not real_field_id:
                real_field_id = self._match_field_from_var(single_condition)
                self._assign_db_num_formula_mode(real_field_id, single_condition)
                # 是否通过变量关联匹配出来的字段标志位
            if not real_field_id:
                continue

            dim_extra_data = chart_service.get_union_field_data(
                self.chart_responder_id, real_field_id, self.chart_data_model
            )
            operator = single_condition.get('operator')
            formula_mode = single_condition.get('formula_mode')
            formula_result_dict = self.deal_with_formula_params(
                formula_mode, single_condition.get('data_type'), operator, single_condition.get('col_value')
            )
            dim_data = dict(
                alias_name=dim_extra_data.get('alias_name'),
                col_name=dim_extra_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=operator,
                dataset_field_id=dim_extra_data.get('id'),
                data_type=dim_extra_data.get('data_type'),
                format=single_condition.get('format'),
                formula_mode=single_condition.get('formula_mode'),
                condition_type=single_condition.get('condition_type') or ChartConditionType.PenetrateFilter.value,
            )
            selector = proxy_dataset_service.get_formatted_fields_by_field_id(real_field_id)
            where_fields.append(
                self.append_single_condition(
                    field_value=formula_result_dict.get('col_value'),
                    formula_result_dict=formula_result_dict,
                    cur_dataset_field_data=selector,
                    dn=dim_data,
                    not_append=True,
                )
            )
            conditions.append(dim_data)
        # 单独处理单图内筛选
        chart_filter_where_fields = self._format_chart_filter(chart_filter_list)
        if chart_filter_where_fields:
            where_fields.extend(chart_filter_where_fields)

        # 处理联动
        self._format_linkage(linkage_filter_list)

        # 处理组件筛选
        self._format_component_filter(component_filter_list)

        # 处理跳转
        self._format_jump(jump_filter_list)

        return self

    def _get_parent_chart_filter_single_where_field(
        self, filter_relation_list, complex_where, conditions, condition_type, dataset_field_id
    ):
        where_field = None
        for single_filter in filter_relation_list:
            cur_dataset_field_data = self.dataset_field_dict.get(dataset_field_id)
            if not cur_dataset_field_data:
                raise UserError(message='未查询到对应数据集字段')

            # 处理formula_mode
            formula_mode = (
                single_filter.get('formula_mode', ChartDimFormulaMode.Day.value)
                if cur_dataset_field_data.get("data_type") == DatasetFieldDataType.Datetime.value
                else None
            )
            formula_result_dict = self.deal_with_formula_params(
                formula_mode,
                single_filter.get('data_type'),
                single_filter.get('operator'),
                single_filter.get('col_value'),
            )

            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=formula_mode,
                condition_type=condition_type,
            )
            where_item = self.append_single_condition_by_complex(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
                logic='AND',
            )
            if not where_item:
                continue
            if where_field:
                complex_where.append(where_item)
                conditions.append(where_item.dn)
            else:
                where_field = where_item
        return where_field

    def get_parent_chart_filter(self, chart_filters, filter_relation, condition_type):
        """
        获取父类筛选条件并转换为当前单图的筛选条件
        :param list chart_filter_list:
        :param int filter_relation: 0 or 1
        :param int condition_type: 1-6
        :return:
        """
        if not chart_filters:
            return False
        where_fields = []
        filter_relation_dict = defaultdict(list)
        for single_filter in chart_filters:
            field_id = single_filter.get('dataset_field_id', '')
            # 这里不能进行字段id转换，防止字段关联配置时不同字段关联相同字段，如{a:c, b:c}
            filter_relation_dict[field_id].append(single_filter)

        conditions = []
        for field_id, filter_relation_list in filter_relation_dict.items():
            # 如果传入的已经是转换过的字段id，则不再进行字段id转换
            dataset_field_id = (
                field_id if field_id in self.dataset_field_dict else self._get_current_dataset_field_id(field_id)
            )
            if not dataset_field_id:
                continue
            complex_where = []
            where_field = self._get_parent_chart_filter_single_where_field(
                filter_relation_list, complex_where, conditions, condition_type, dataset_field_id
            )

            if not where_field:
                continue
            where_field.logic = self.get_logic(filter_relation)
            if complex_where:
                where_field.complex = complex_where

            where_fields.append(where_field)
        return where_fields, conditions

    def _get_current_dataset_field_id(self, parent_field_id):
        """
        根据当前父级字段id获取关联字段id
        :param str parent_field_id: 字段id
        :return:
        """
        current_dataset_field_id = None
        if self.chart_data_model.penetrate_filter_relation:
            for item in self.chart_data_model.penetrate_filter_relation:
                if (
                    item.get('parent_chart_field_id') == parent_field_id
                    and item.get('child_chart_field_id') in self.dataset_field_dict
                ):
                    current_dataset_field_id = item.get('child_chart_field_id')

        return current_dataset_field_id

    def _get_current_dataset_field_id_from_var(self, parent_var_id):
        """
        根据父级变量id获取关联字段id
        :param str parent_var_id: 父级单图的变量id
        :return:
        """
        current_dataset_field_id = None
        if not parent_var_id:
            return current_dataset_field_id
        if self.chart_data_model.penetrate_var_filter_relation:
            for item in self.chart_data_model.penetrate_var_filter_relation:
                if (
                    item.get('parent_chart_var_id') == parent_var_id
                    and item.get('child_chart_field_id') in self.dataset_field_dict
                    and item.get('child_chart_field_id') not in self.used_field_list
                ):
                    current_dataset_field_id = item.get('child_chart_field_id')
                    self.used_field_list.append(current_dataset_field_id)
                    break

        return current_dataset_field_id

    def _pre_assign_field_var(self):
        """
        预先获取父级单图过来的变量及其信息
        :return:
        """
        tmp_var_list = []
        for single_condition in self.penetrate_filter_conditions:
            if single_condition and single_condition.get('var_id'):
                tmp_var_list.append(single_condition.get('var_id'))
        self.field_var_dict = external_query_service.batch_get_dataset_var_info(
            tmp_var_list, self.chart_data_model.dashboard_id
        )

    def _assign_db_num_formula_mode(self, real_field_id, single_condition):
        """
        获取配置的formula_mode值
        :param real_field_id:
        :param single_condition:
        :return:
        """
        nums = self.chart_data_model.nums
        if nums:
            for n in nums:
                single_condition["formula_mode"] = (
                    n.get("formula_mode")
                    if n and n.get("dataset_field_id") == real_field_id
                    else single_condition.get("formula_mode")
                )

    def _match_field_from_var(self, single_condition):
        """
        通过变量匹配关联的字段
        :param single_condition:
        :return:
        """
        # 没有匹配到关联字段，则尝试从变量id匹配
        real_field_id = self._get_current_dataset_field_id_from_var(single_condition.get('var_id'))
        # 处理变量为context类型的情况
        single_var_info = self.field_var_dict.get(single_condition.get('var_id'), {})
        if single_var_info.get('value_source') == DatasetVarValueSource.Context.value:
            single_condition['col_value'] = single_var_info.get('parsed_value')
        return real_field_id
