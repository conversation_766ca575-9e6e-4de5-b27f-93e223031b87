#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
dashboard jump formatter
"""

# ---------------- 标准模块 ----------------
import logging
import json

# ---------------- 业务模块 ----------------
from base.errors import UserError
from dashboard_chart.formatter.formatter import Formatter
from base.enums import DatasetFieldDataType, ChartConditionType

logger = logging.getLogger(__name__)


class DashboardVarJumpFormatter(Formatter):
    """
    跳转
    :param self.dataset_field_dict: 数据集数据字典
    :param self.dashboard_conditions: 条件数据
    :param self.dashboard_filters: db中报告级筛选
    """

    def __init__(self, model):
        self.dataset_field_dict = None
        self.dashboard_conditions = None
        self.dashboard_var_filters = None
        super().__init__(model=model)
        self.__initialize_data()

    def __initialize_data(self):
        """
        初始化
        :return:
        """
        self.dataset_field_dict = self.chart_data_model.dataset_field_dict
        self.dashboard_conditions = self.chart_data_model.dashboard_conditions
        self.dashboard_var_filters = json.loads(self.chart_data_model.dashboard_var_filters or "[]")

    @staticmethod
    def _get_match_check_flag(item, item_key, col_name, check_flag):
        if check_flag:
            return check_flag
        if item.get(item_key) and item.get(item_key) == col_name:
            check_flag = True
        return check_flag

    def match_col_name(self, col_name):
        """
        匹配跳转条件中的col_name
        ps: 数据集有可能是直连，API数据集等，所以需要和多个字段值进行匹配
        :param col_name:
        :return:
        """
        check_flag = False
        if not col_name or not self.dashboard_var_filters:
            return check_flag, None
        for filters in self.dashboard_var_filters:
            for item in filters.get("filter_relations", []):
                check_flag = self._get_match_check_flag(item, "col_name", col_name, check_flag)
                check_flag = self._get_match_check_flag(item, "alias_name", col_name, check_flag)
                if check_flag:
                    return check_flag, item
        return check_flag, None

    def format(self):
        """
        获取转换后的报告条件
        :return:
        """
        # 如果该数据集本身有报告级别筛选报告条件才生效
        if not self.dashboard_conditions or not self.dashboard_var_filters:
            return self

        for single_dashboard_condition in self.dashboard_conditions:
            check_flag, relate_filter = self.match_col_name(single_dashboard_condition.get("col_name"))
            if not check_flag or not relate_filter:
                continue
            cur_dataset_field_data = self.dataset_field_dict.get(relate_filter.get('related_dataset_field_id'))
            if not cur_dataset_field_data:
                raise UserError(message='数据异常，无法获取数据集！')

            # 处理formula_mode
            formula_mode = None
            if relate_filter.get('data_type') == DatasetFieldDataType.Datetime.value:
                formula_mode = self.match_formula_mode(single_dashboard_condition.get("col_value"))

            formula_result_dict = self.deal_with_formula_params(
                formula_mode,
                cur_dataset_field_data.get('data_type'),
                single_dashboard_condition.get('operator'),
                single_dashboard_condition.get('col_value'),
            )

            dim_data = dict(
                alias_name=cur_dataset_field_data.get('alias_name'),
                col_name=cur_dataset_field_data.get('col_name'),
                col_value=formula_result_dict.get('col_value'),
                operator=formula_result_dict.get('operator'),
                dataset_field_id=cur_dataset_field_data.get('id'),
                data_type=cur_dataset_field_data.get('data_type'),
                format=cur_dataset_field_data.get('format'),
                formula_mode=formula_mode,
                condition_type=ChartConditionType.DashboardJumpFilter.value,
            )
            self.append_single_condition(
                field_value=formula_result_dict.get('col_value'),
                formula_result_dict=formula_result_dict,
                cur_dataset_field_data=cur_dataset_field_data,
                dn=dim_data,
            )
        return self
