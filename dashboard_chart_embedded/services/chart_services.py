#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : token_services.py
# @Author: guq  
# @Date  : 2021/11/19
# @Desc  :

from copy import deepcopy

import falcon

from dashboard_chart_embedded.repositories.chart_repositories import (
    # get_dashboard_ids_by_chart_ids,
    get_dashboard_id_by_chart_id,
    get_released_chart_and_dashboard_data
)
from dashboard_chart import released_api_route
from dashboard_chart.services import authority_service, metadata_service
from dmplib.utils.errors import UserError
from boot_verb.api_route import copy_property


# 获取多个元数据
# from gevent.pool import Pool
# pool = Pool(500)
# from dmplib.hug import g
# from dashboard_chart.services.chart_service import handle_g
# from dashboard_chart.services.released_dashboard_service import batch_get_released_chart_result
# from typing import Any, Callable, Dict, List, Optional, Tuple, Union
# from collections import Iterable

# def batch_get_released_metadata_data_v2(chart_ids: list, request, response, kwargs) -> Dict[str, Any]:
#     """
#     批量获取元数据
#     :param chart_params:
#     :return:
#     """
#     results = {}
#     if not chart_ids or not isinstance(chart_ids, Iterable):
#         return results
#
#     dashboard_ids = get_dashboard_ids_by_chart_ids(chart_ids=chart_ids)
#     parsed_exchanged_token = request._parsed_exchanged_token
#
#     def single_chart_metadata(dashboard_id, results, request, response, kwargs):
#         results[dashboard_id] = _get_metadata_by_metadata_service(dashboard_id, request, response, kwargs)
#         return dashboard_id
#
#     for dashboard_id in dashboard_ids:
#         get_chart_metadata = handle_g(single_chart_metadata)
#         # external_params = getattr(g, 'external_params', None)
#         cookie = getattr(g, 'cookie', None)
#         # customize_roles = getattr(g, 'customize_roles', [])
#         # external_user_id = getattr(g, 'external_user_id', None)
#         pool.spawn(
#             get_chart_metadata,
#             dashboard_id,
#             results,
#             request,
#             response,
#             kwargs,
#             code=parsed_exchanged_token['code'],
#             account=parsed_exchanged_token['account'],
#             userid=parsed_exchanged_token['id'],
#             cookie=cookie,
#             external_params=parsed_exchanged_token.get('external_params', None),
#             customize_roles=parsed_exchanged_token.get('customize_roles', []),
#             external_user_id=parsed_exchanged_token.get('external_user_id', None),
#         )
#     pool.join()
#     return results

# def get_metadata_result(request, response, kwargs):
#     # return released_api_route.get_screens_metadata_v2(request, response, **kwargs)
#     return released_api_route.get_screens_metadata_v2(request, response, **kwargs)


def mock_hug_request(request: falcon.request.Request) -> falcon.request.Request:
    mocked_request = falcon.request.Request(request.env, request.options)
    copy_property(request, mocked_request)
    return mocked_request


def mock_hug_response(response: falcon.response.Response) -> falcon.response.Response:
    mocked_response = falcon.response.Response()
    copy_property(response, mocked_response)
    return mocked_response


def _get_metadata_by_metadata_service(dashboard_id, request, response, kwargs):
    # 目前下面这些参数不走只为走原来的各种校验
    # 由于是顺序循环，可以不考虑可变对象被重复修改
    exchanged_token = request._exchanged_token  # noqa
    parsed_exchanged_token = request._parsed_exchanged_token  # noqa

    request, response, kwargs = mock_hug_request(request), mock_hug_response(response), deepcopy(kwargs)
    kwargs['code'] = parsed_exchanged_token['code']
    request.params['code'] = parsed_exchanged_token['code']
    request.params['id'] = dashboard_id
    request.cookies['token'] = exchanged_token
    # 走原来接口的装饰器校验（身份+权限校验）
    authority_service.verify_released_handle(None)(request, response)

    msg, meta = metadata_service.get_screens_release_metadata_v2(snapshot_id=dashboard_id, token_data=None)
    if not meta:
        raise UserError(message='[%r]元数据为空！' % dashboard_id)
    return meta


def batch_get_released_metadata_data_v1(chart_id, request, response, kwargs):
    # 获取元数据
    dashboard_id = get_dashboard_id_by_chart_id(chart_id)
    if not dashboard_id:
        raise UserError(message='[%r]未找到组件的报表！' % chart_id)
    return _get_metadata_by_metadata_service(dashboard_id, request, response, kwargs)


def get_released_chart_data(query, request, response, kwargs):
    # 组件取数据
    exchanged_token = request._exchanged_token  # noqa
    parsed_exchanged_token = request._parsed_exchanged_token  # noqa

    # 走原来接口的装饰器校验（身份+权限校验）
    mocked_request, mocked_response = mock_hug_request(request), mock_hug_response(response)
    mocked_request.cookies['token'] = exchanged_token
    authority_service.verify_data_handle(None)(mocked_request, mocked_response)

    # 模拟以前的请求参数
    # g.code = parsed_exchanged_token['code']
    kwargs['chart_params'] = [query]
    kwargs['code'] = parsed_exchanged_token['code']
    request.cookies['tenant_code'] = parsed_exchanged_token['code']

    _, _, result = released_api_route.released_chart_data(request, response, **kwargs)
    # _bool_val, msg, result = released_api_route.released_chart_data(request, response, **kwargs)
    return result.get(query['id'], {})  # 只取当前组件


def get_released_charts():
    # 获取已发布的组件
    released_charts = get_released_chart_and_dashboard_data()
    return released_charts
