#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : token_services.py
# @Author: guq  
# @Date  : 2021/11/19
# @Desc  :
import functools
import json
from urllib import parse

import base64
import falcon
import jwt
import traceback
from loguru import logger

from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from user.services import user_service
from user.services.reporting_sso_service import ReportingSSOService
from user.services.assistant_service import SuperAppServiceOAuthService
from base.dmp_constant import SELF_SERVICE_VIRTUAL_USER_INFO, KEEP_LOGIN_DEFAULT_EXPIRE
from dmplib.utils.jwt_login import conn_redis, LoginToken
from dashboard_chart.services import authority_service, dashboard_cache_service
from dmplib.redis import conn as redis_conn


def is_service_way(request):
    if request.params.get("__CLIENT_BIZ_TYPE") or request.params.get("__from"):
        return True
    return False


def get_service_code_and_account(request, response):
    if request.params.get("__CLIENT_BIZ_TYPE") or request.params.get("__from"):
        service = SuperAppServiceOAuthService(request, response)
        service.load_config()
        success, result = service.get_user_account()
        if not success:
            raise UserError(400, result)
        return result
    return {}


class EmbeddedTokenSSOService(ReportingSSOService):
    login_caching_id = ''

    ####
    # 原有的集成登陆校验流程： token校验->换取新token->设置到cookies中->第二次请求带上cookies访问->第二次请求的token校验
    # 此token校验为组件集成到前端的jssdk中设计，jssdk会带上token访问
    # token校验流程： token校验->换取新token->设置到请求的token校验中->原先的请求顺利走完剩下的流程
    # 在一次请求中实现token换取，设置对应的校验token
    # 换取新token会有redis缓存，不会每次都执行换取逻辑
    ####
    def get_exchanged_token(self, signature, request, response):
        service_way_flag = is_service_way(request)
        if not service_way_flag:
            # 数见原有的集成方式访问
            success, result = self._decode_data(signature)
            if not success:
                return False, result, {}
            code = result.get('tenant_code') or result.get("code")
            account = result.get('account')
            customize_roles = result.get('customize_roles', [])
            external_user_id = result.get('external_user_id')
            user_id = result.get('user_id')
        else:
            # 以服务商方式访问
            result = get_service_code_and_account(request, response)
            code = result.get("channel_code")
            account = result.get("user_code")
            user_id = result.get("user_guid")
            customize_roles = []
            external_user_id = None
        logger.info(f'service_way_flag: {service_way_flag}, result: {result}')
        if not account or not code:
            return False, '缺少帐号数据', {}

        g.code = code
        g.account = account

        # 获取报表id
        report_id = get_dashboard_id(request.params)

        # 获取报表类型
        from user.repositories import ingrate_repository
        share_type = ingrate_repository.get_dashboard_share_type(report_id)

        if share_type == 4:
            # 兼容自助报表第三方登录无用户id, 使用虚拟用户
            if customize_roles:
                # 若指定了角色，此时可以无需指定用户
                user = SELF_SERVICE_VIRTUAL_USER_INFO
                user['account'] = account
                user['name'] = account
                group_ids = []
            else:
                user = user_service.get_user_by_account(account, ['pwd', 'id', 'group_id'])
                if not user:
                    # return False, '用户:%s不存在' % account, {}
                    return False, f'用户[{code}:{account}]不存在', {}
                group_ids = user_service.get_cur_user_group_ids()
            g.group_ids = group_ids
            g.customize_roles = customize_roles
            g.userid = user['id']
        else:
            from user.services.ingrate_service import auto_login

            # auto_login没有处理的cookies，包含挂载方式为公开分享以及授权码方式的报告
            # 下面单独处理
            if share_type in [0, 1]:
                secret = config.get('JWT.secret')
                if share_type == 1:
                    # 帮他填密码
                    pwd = get_dashboard_share_secret_key(report_id, code)
                else:
                    pwd = ''
                token_data = authority_service._analyze_token(
                    token='', request=request, code=code, dashboard_id=report_id, response=response,
                    secret=secret, pwd=pwd
                )
                parsed_token = token_data.get('token_data') or {}
                parsed_token['code'] = code
                parsed_token['pwd'] = pwd
                exchanged_token = jwt.encode(parsed_token, secret)
            else:
                user_auth = request.params.get('user_auth', '')
                exchanged_token = auto_login(
                    request, response, report_id, code, account, user_id, share_type, user_auth=user_auth
                )
                parsed_token = jwt.decode(exchanged_token, self.secret, algorithms="HS256", options={'verify_signature': False})
            relation_login_data = {
                'key': LoginToken()._caching_id(user_id, exchanged_token),
                'token': exchanged_token,
                'parsed_token': parsed_token,
                'cookie': {val.key: parse.unquote(val.value) for _, val in dict(response._cookies).items()}
            }

            return True, exchanged_token, relation_login_data

        # 记录当前租户的code与已经生成的token的对应关系
        login_token = LoginToken()
        login_cache_redis = self.get_login_redis()
        relation_expires = 3 * 60
        # noqa redis key: logindmp:jwt:token:embedded_token_relation_key:uitest:uitest
        relation_key = f'dmp:jwt:token:embedded_token_relation_key:{code}:{account}'
        relation_login_data = self._str_redis_value(login_cache_redis.get(relation_key))
        relation_login_data = json.loads(relation_login_data) if relation_login_data else {}

        # 没有设置关联key
        if not relation_login_data:
            ####
            # 流程中本来不需要去操作response，设置cookies啥的，但是为了复用之前的生成token逻辑，
            # 直接调用之前集成接口里的设置cookies逻辑得到token，
            # 虽然此时操作了response对象的cookies，但是会在装饰器里将cookies抹掉
            ####
            exchanged_token = user_service.set_login_status(
                response,
                request.host,
                code,
                user['id'],
                account,
                group_ids,
                **{"customize_roles": customize_roles, "external_user_id": external_user_id}
            )
            login_caching_id = login_token._caching_id(user['id'], exchanged_token)
            parsed_token = jwt.decode(exchanged_token, self.secret, algorithms="HS256", options={'verify_signature': False})
            relation_login_data = {
                'key': login_caching_id,
                'token': exchanged_token,
                'parsed_token': parsed_token,
                'cookie': {val.key: parse.unquote(val.value) for _, val in dict(response._cookies).items()}
            }

            login_cache_redis.set(relation_key, space_saving_json(relation_login_data), relation_expires)
            # 即使关系key失效原先的token也有剩余可用时间，避免relation_key失效瞬间有请求进来，使用到了这个关联的key
            login_cache_redis.expire(login_caching_id, relation_expires + 30)
        else:
            login_caching_id = relation_login_data['key']
            exchanged_token = relation_login_data['token']

        setattr(self, 'login_caching_id', login_caching_id)
        return True, exchanged_token, relation_login_data

    def get_login_redis(self):
        prefix = LoginToken._LoginToken__login_cache_prefix  # noqa
        return conn_redis(prefix)

    def _str_redis_value(self, value):
        if value is None:
            return None
        if isinstance(value, bytes):
            return value.decode()
        return value


def get_dashboard_share_secret_key(dashboard_id, code):
    dashboard_cache = dashboard_cache_service.DashboardReleaseCache(code, dashboard_id, redis_conn())
    dashboard = dashboard_cache.get_dashboard_data()
    return dashboard.get('share_secret_key', '')


def space_saving_json(data):
    # 返回一个节省空间的json
    return json.dumps(data, separators=(',', ':'), ensure_ascii=False)


def update_request_cookies(request: falcon.request.Request, cookies_dict: dict):
    for key, val in cookies_dict.items():
        request.cookies[key] = val


def wrapper_cb_result(result, jsonp=True):
    # 支持json/jsonp两种形式
    if jsonp:
        cb = g.cb
        response = g.response  # type: falcon.response.Response
        if isinstance(result, (dict, list)):
            result = space_saving_json(result)
        response.set_header('Content-Type', 'application/javascript')
        response.body = f'{cb}({result});'
    else:
        response = g.response  # type: falcon.response.Response
        if isinstance(result, (dict, list)):
            result = space_saving_json(result)
        response.set_header('Content-Type', 'application/json')
        response.body = result


def record_sso_message(response, message):
    response.set_header('sso-message', parse.quote(message))


def get_dashboard_id(kwargs: dict):
    query = kwargs.get('query', '{}')
    chart_id = ""
    if query:
        try:
            query = json.loads(query)
            dashboard_id = query.get("dashboard_id") or query.get("report_id")
            if dashboard_id:
                return dashboard_id
            chart_id = query.get("id")
        except:
            raise UserError(message="参数错误")
    chart_id = chart_id or kwargs.get('chart_ids', '{}')
    if chart_id:
        from dashboard_chart_embedded.repositories.chart_repositories import (
            get_dashboard_id_by_chart_id
        )
        dashboard_id = get_dashboard_id_by_chart_id(chart_id)
        return dashboard_id
    return None


# 嵌入组件接口的token校验器
def embedded_token_authenticator(jsonp=True):
    def token_authenticator(func):
        @functools.wraps(func)
        def wrapper(request, response, *args, **kwargs):
            token = request.get_param('access_token', '')
            cb = request.get_param('cb', '')
            ttl = False
            ets = EmbeddedTokenSSOService()

            try:
                if request.get_param('token'):
                    token = base64.b64encode(request.get_param('token').encode()).decode()
                    ets.secret = config.get("PPT.sso_secret")
                setattr(g, 'cb', cb)
                setattr(g, 'response', response)

                result, exchanged_token, relation_login_data = ets.get_exchanged_token(
                    token, request, response
                )
                if not result:
                    err_msg = exchanged_token
                    record_sso_message(response, err_msg)
                    return False, err_msg, {}

                cookie = relation_login_data['cookie']
                parsed_exchanged_token = relation_login_data['parsed_token']
                g.cookie = cookie  # 某些地方貌似用到了cookies，这里预先设置cookies
                update_request_cookies(request, cookie)
                setattr(request, '_exchanged_token', exchanged_token)
                setattr(request, '_parsed_exchanged_token', parsed_exchanged_token)
                if ets.login_caching_id:
                    ttl = ets.get_login_redis().ttl(ets.login_caching_id) or 1

                func_result = func(request, response, *args, **kwargs)
                setattr(response, '_cookies', {})  # 不设置cookies， 抹掉所有cookies
                record_sso_message(response, f'success; token={exchanged_token}; parsed={parsed_exchanged_token}')
                return func_result
            except falcon.http_status.HTTPStatus as fe:
                record_sso_message(response, str(fe))
                raise fe
            except UserError as ue:
                record_sso_message(response, ue.message)
                logger.error('组件嵌入错误：%s' % traceback.format_exc())
                return wrapper_cb_result({"result": False, "msg": ue.message, "data": {}}, jsonp=jsonp)
            except Exception as e:
                record_sso_message(response, str(e))
                logger.error('组件嵌入未知错误：%s' % traceback.format_exc())
                return wrapper_cb_result({"result": False, "msg": '未知错误', "data": {}}, jsonp=jsonp)
            finally:
                # 走原来的权限装饰器校验会把用户的login_caching_id给刷新延时，但是这种场景并不需要延时，过期就好
                if ttl is not False:
                    ets.get_login_redis().expire(ets.login_caching_id, ttl)

        return wrapper

    return token_authenticator
