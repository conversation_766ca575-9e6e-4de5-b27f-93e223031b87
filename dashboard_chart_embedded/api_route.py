#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : fff.py
# @Author: guq
# @Date  : 2021/11/19
# @Desc  :
import json

from dmplib.hug import APIWrapper
import hug
from hug.routing import URLRouter
from dashboard_chart_embedded.services.token_services import embedded_token_authenticator, wrapper_cb_result
from dashboard_chart_embedded.services.chart_services import (
    batch_get_released_metadata_data_v1,
    get_released_chart_data,
    get_released_charts

)


# 提供组件嵌入jssdk的api
class EmbeddedAPIWrapper(APIWrapper):
    __slots__ = ["_route", "_embedded_route"]

    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._route = hug.http(api=self.api)
        self._embedded_route = None

    @property
    def embedded_route(self) -> URLRouter:
        if not self._embedded_route:
            # pylint: disable=E1120
            self._embedded_route = hug.http(api=self.api, requires=None)
        return self._embedded_route


api = EmbeddedAPIWrapper(__name__)


@api.embedded_route.get('/released_charts')
@embedded_token_authenticator(jsonp=True)
def released_charts_tree(request, response, **kwargs):
    """
    提供已发布的组件可选组件的树形结构
    """
    # 这个接口只是提供给前端调试用，不是组件嵌入的对外接口
    result = get_released_charts()
    return wrapper_cb_result({"result": True, "msg": 'ok', "data": result})


@api.embedded_route.get('/metadata')
@embedded_token_authenticator(jsonp=True)
def released_charts_metadata(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {get} /api/dashboard_chart_embedded/metadata 获取元数据
    @apiParam query {string}  access_token token
    @apiParam query {string}  chart_ids 组件ID
    @apiParam query {string}  cb callback函数名
    @apiGroup  dashboard_chart_embedded
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {
        }
    }
    **/
    """
    # 现在改成只支持单个组件取元数据
    chart_id = kwargs.get('chart_ids', '')
    result = batch_get_released_metadata_data_v1(chart_id, request, response, kwargs)
    return wrapper_cb_result({"result": True, "msg": 'ok', "data": result})


@api.embedded_route.get('/chart/data')
@embedded_token_authenticator(jsonp=True)
def released_charts_data(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {get} /api/dashboard_chart_embedded/metadata 组件取数据
    @apiParam query {string}  access_token token
    @apiParam query {string}  query 取数参数（url编码）
    @apiParam query {string}  cb callback函数名
    @apiGroup  dashboard_chart_embedded
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {
        }
    }
    **/
    """

    # 现在只传单个组件参数，不多个组件同时取数
    query = kwargs.get('query', '{}')
    try:
        query = json.loads(query)
    except:
        return wrapper_cb_result({"result": False, "msg": 'json反序列化失败', "data": {}})

    result = get_released_chart_data(query, request, response, kwargs)
    return wrapper_cb_result({"result": True, "msg": 'ok', "data": result})
