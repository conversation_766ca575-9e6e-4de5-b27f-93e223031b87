# import unittest
# import json
# from dmplib.tests.base_test import BaseTest
# from integrate.services import high_data_service
#
#
# class TestHighDataService(BaseTest):
#     """
#     Our basic test class
#     """
#
#     def __init__(self, method_name="runTest"):
#         super().__init__(method_name, code='test', account='admin')
#
#     def test_get_app(self):
#         """
#         获取素有的产品列表
#         """
#         high_data_obj = high_data_service.HighDataService()
#         result = high_data_obj.get_app()
#         rs = json.dumps(result)
#         print(rs)
#
#     def test_add_report(self):
#         """
#         报表添加
#         """
#         high_data_obj = high_data_service.HighDataService()
#         try:
#             # 删除报表
#             delete_rs = high_data_obj.delete_report(report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c011", report_type="dashboard")
#             self.assertEqual(delete_rs.get("isSuccess"), 1)
#             # 写入报表
#             result = high_data_obj.add_report(report_name="cesaaaa", appcode="1000.0011", report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c011", report_type="dashboard")
#             self.assertEqual(result.get("result").get("dashboard_id"), '39fa30ef-e0b9-c71c-5d5d-1715f687c011')
#         except Exception as e:
#             error_e_str = '请求HighData接口错误：报表创建失败'
#             self.assertEqual(error_e_str, str(e))
#
#     def test_rename_report_name(self):
#         """
#         报表重命名
#         """
#         high_data_obj = high_data_service.HighDataService()
#         # 删除报表
#         delete_rs = high_data_obj.delete_report(report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c022",
#                                                 report_type="dashboard")
#         self.assertEqual(delete_rs.get("isSuccess"), 1)
#         # 报表添加
#         result = high_data_obj.add_report(report_name="cesaaaa", appcode="1000.0011",
#                                           report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c022", report_type="dashboard")
#         self.assertEqual(result.get("result").get("dashboard_id"), '39fa30ef-e0b9-c71c-5d5d-1715f687c022')
#         # 报表重命名
#         rename_rs = high_data_obj.rename_report_name(report_name="测试测试", report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c022", report_type="dashboard")
#         self.assertEqual(rename_rs.get("isSuccess"), 1)
#
#     def test_delete_report(self):
#         """
#         删除报表不存在错误场景
#         """
#         high_data_obj = high_data_service.HighDataService()
#         try:
#             # 删除报表
#             high_data_obj.delete_report(report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c000", report_type="dashboard")
#         except Exception as e:
#             error_e_str = '请求HighData接口错误：仪表板不存在，删除失败'
#             self.assertEqual(error_e_str, str(e))
#
#     def test_publish_report(self):
#         """
#         报表发布失败场景
#         """
#         high_data_obj = high_data_service.HighDataService()
#         try:
#             # 删除报表
#             delete_rs = high_data_obj.delete_report(report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c033",
#                                                     report_type="dashboard")
#             self.assertEqual(delete_rs.get("isSuccess"), 1)
#             # 写入报表
#             result = high_data_obj.add_report(report_name="测试报表33", appcode="1000.0011", report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c033", report_type="dashboard")
#             self.assertEqual(result.get("result").get("dashboard_id"), '39fa30ef-e0b9-c71c-5d5d-1715f687c033')
#             # 发布报表
#             high_data_obj.publish_report(report_id="39fa30ef-e0b9-c71c-5d5d-1715f687c033", report_type="dashboard")
#
#         except Exception as e:
#             error_e_str = '请求HighData接口错误：仪表板不存在组件，不能发布'
#             self.assertEqual(error_e_str, str(e))
#
#
# if __name__ == '__main__':
#     unittest.main()
