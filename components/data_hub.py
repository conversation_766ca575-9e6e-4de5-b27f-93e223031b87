#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2018/3/22.
"""
from hashlib import md5

import jwt
import re
import requests
from datetime import datetime, timedelta

from urllib.parse import urlparse

from requests.exceptions import RequestException

from dmplib.utils.errors import UserError


class CollectAPI:
    __slots__ = ['host', 'access_secret']

    APP_ID = 'DMP'
    COLLECT_API_PATH = 'api/handle'

    def __init__(self, host, access_secret):
        """
        :param str host:接口地址
        :param str access_secret: 秘钥
        """
        _url = urlparse(host)
        self.host = _url.scheme + '://' + _url.netloc
        self.access_secret = access_secret

    def ping(self, db_code=None):
        """
        接口是否正常
        :return:
        """
        data = self._api_request({'api': 'api.ping', 'db_code': db_code}, 5)
        if data and isinstance(data, dict) and data.get('data'):
            return True
        return False

    def send_config(self, _data):
        """
        下发配置接口
        :return:
        """
        data = self._api_request(_data, 5)
        if data and isinstance(data, dict) and data.get('data'):
            return True
        return False

    def get_params(self):
        """
        获取参数
        :return:
        """
        params = {'api': 'api.db.list'}
        return self._api_request(params)

    def get_tables(self, keyword=None, page=1, page_size=30, db_code=None):
        """
        获取ERP数据表
        :param db_code:
        :param keyword:
        :param page:
        :param page_size:
        :return:
        """
        params = {'api': 'api.table.list', 'page': page, 'page_size': page_size, 'db_code': db_code}
        if keyword:
            params['keyword'] = keyword
        return self._api_request(params)

    def get_table_columns(self, table_name, db_code=None):
        """
        根据表名获取表结构
        :param table_name:
        :return:
        """
        params = {'api': 'api.column.list', 'db_code': db_code}
        if table_name:
            params['table_name'] = table_name
        return self._api_request(params)

    def get_sql_count(self, sql, db_code=None):
        """
        根据sql获取总数
        :param sql:
        :return:
        """
        sql = 'select count(1) as total from ({sql}) a '.format(sql=sql)
        params = {
            'api': 'api.sql.count',
            'sql': sql,
            'db_code': db_code,
            'sign': self._sign_sql(sql)
        }
        return self._api_request(params)

    def get_sql_list(self, sql, is_download='0', queue_name='', timeout=None, db_code=None):
        """
        根据sql语句获取数据
        :param str sql:
        :param int is_download:
        :param str queue_name:
        :param int timeout:
        :return:
        """
        params = {
            'api': 'api.sql.list',
            'sql': sql,
            'is_download': is_download,
            'queue_name': queue_name,
            'db_code': db_code,
            'sign': self._sign_sql(sql)
        }
        return self._api_request(params, timeout=timeout)

    def _sign_sql(self, sql):
        return md5((md5(sql.encode()).hexdigest() + self.access_secret).encode()).hexdigest()

    def _api_request(self, parameters=None, timeout=None):
        """
        请求API
        :param parameters:
        :param int timeout:
        :return:
        """
        if not self.validation_url(self.host):
            raise UserError(message='无效的url：' + self.host)

        try:
            self.host = self.host[: len(self.host) - 1] if self.host.endswith('/') else self.host
            url = '%s/%s' % (self.host, self.COLLECT_API_PATH)
            access_secret_param = {"appid": self.APP_ID, "exp": self.get_exp()}
            token = jwt.encode(access_secret_param, self.access_secret)
            headers = {"Authorization": "Bearer " + token}

            response = requests.post(url, json=parameters, headers=headers, timeout=timeout or 30)
            if response.status_code != 200:
                raise UserError(
                    message=' 状态：' + str(response.status_code) + ' , ' + response.reason + '。' + response.text
                )
            return response.json()
        except RequestException as e:
            raise UserError(message='连接失败:' + str(e))
        except Exception as be:
            raise UserError(message='连接失败:' + str(be))

    @staticmethod
    def get_exp():
        """
        获取exp时间
        :return:
        """
        # 获取当前时间
        d1 = datetime.now()
        # 当前时间加上180秒
        d2 = d1 + timedelta(seconds=180)
        return d2

    @staticmethod
    def validation_url(url):
        """
        校验url合法性
        :param url:
        :return:
        """
        if re.match(r'^https?:/{2}\w.+$', url):
            return True
        else:
            return False
