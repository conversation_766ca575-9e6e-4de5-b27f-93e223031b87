from base import repository
from components.app_hosts import AppHosts
from dmplib.hug import g
from dmplib.redis import RedisCache
import json
from loguru import logger
from dmplib import config
from dmplib.saas.project import get_project_info
from dmplib.utils.errors import UserError
from base.enums import ThirdPartyAppCode, SkylineApps
from components.mip_ad_service import MDCMIPService
from components.dynamics_config import DynamicConfig


def is_hd_mode():
    dmp_env_sign = repository.get_data_scalar('project', {'code': g.code}, 'dmp_env_sign', from_config_db=True)
    return dmp_env_sign == 'hd'


def get_cloud_apps():
    cache = RedisCache('Config:')
    cache_key = 'CloudApps'
    cloud_apps = cache.get_data(cache_key)
    if cloud_apps:
        return cloud_apps
    else:
        fields = ['app_id', 'channel_id', 'app_code', 'app_name', 'api_host', 'get_org_api', 'get_user_api', 'is_mysoft_app', 'icon',
                  'data_resolve_instance']
        cloud_apps = repository.get_list('cloud_apps', {}, fields, order_by='app_name ASC',
                                         from_config_db=True)
        cache.set(cache_key, json.dumps(cloud_apps), 7200)
        return cloud_apps


def expire_func_auth(func_code, action=None):
    data = get_project_info(g.code)
    if func_code in ['user-group', 'user-role', 'data-permission']:
        return True
    if data.get('is_expire') == 1 and action != 'view':
        raise UserError(message='租户已过期，不能使用该功能，请续费后重试')


def sync_tenant_status(tenant_code=None):
    dy_config = DynamicConfig()
    if dy_config.get('IngratePlatform.mdc_mip_enable_auth', 0) not in [1, '1']:
        return
    tenant_code = tenant_code or g.code
    if not tenant_code:
        return
    # 租户是否在白名单中
    if is_white_tenant(tenant_code):
        return '白名单租户不需要同步租户状态'
    # 获取当前租户是否为SAAS客户
    saas_info = repository.get_one('erp_api_info', {'erp_api_type': 'saas'}) or []
    if saas_info:
        return 'saas租户不需要同步租户状态'
    mip_service = MDCMIPService(tenant_code)
    status = mip_service.tenant_is_expire()
    if status is None:
        return '没有获取到状态信息，跳过租户状态同步'
    is_expire = 1 if status else 0
    repository.update_data('project', {'is_expire': is_expire}, {'code': tenant_code}, from_config_db=True)
    cache = RedisCache()
    cache_key = 'Project:Detail:Info:' + tenant_code
    cache.delete(cache_key)
    return '租户状态同步成功'


def is_white_tenant(tenant_code):
    dy_config = DynamicConfig()
    white_tenant_list = []
    white_tenant = dy_config.get('IngratePlatform.white_tenants') or ''
    if white_tenant and isinstance(white_tenant, str):
        white_tenant_list = white_tenant.split(',')
    return tenant_code in white_tenant_list


def check_superportal_host(superportal_host):
    """
    检查超级APP host配置是否与环境级一致，如一致则不用写入租户库
    :param superportal_host:
    :return:
    """
    env_superportal_host = get_env_superportal_host()
    if superportal_host == env_superportal_host:
        return True
    return False


def get_superportal_host(cloud_app_info=None, query_system_setting=True):
    """
    获取最终的超级APP host
    首先查找租户级的，租户级不存在则找环境级的。租户级->环境配置->环境数据库cloud_apps
    :param cloud_app_info: cloud_apps 记录
    :param query_system_setting: 是否查询租户库
    :return:
    """
    superportal_info = {}
    # 租户级配置
    if query_system_setting:
        rs = repository.get_data('system_setting', {"category": "integrate", "item": "superportal_info"}) or {}
        if rs:
            value = rs.get("value") or ''
            try:
                superportal_info = json.loads(value)
            except Exception as e:
                logger.error(f"超级APP superportal_info参数反序列化错误，errs{str(e)}")
    superportal_host = superportal_info.get("host")
    if not superportal_info or not superportal_host:
        superportal_host = get_env_superportal_host(cloud_app_info)
    return superportal_host


def get_env_superportal_host(cloud_app_info=None):
    """
    获取环境级的超级APP host
    优先取环境级配置，其次取数据库配置
    :param cloud_app_info:
    :return:
    """
    if cloud_app_info is None:
        cloud_app_info = {}

    superportal_host = AppHosts.get(SkylineApps.WORKBENCH)
    if not superportal_host:
        if not cloud_app_info:
            cond = {
                "app_code": [ThirdPartyAppCode.SuperWork.value, ThirdPartyAppCode.MYCYJG.value]
            }
            cloud_app_info = repository.get_data('cloud_apps', cond, from_config_db=True) or {}
        superportal_host = cloud_app_info.get('api_host') or ''

    superportal_host = superportal_host.strip().strip('/')
    return superportal_host
