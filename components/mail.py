#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2017/5/13.
"""
import re
import smtplib
import logging
import time
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from urllib import request
from email.header import Header
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from email.utils import formataddr, parseaddr
from dmplib import config
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)


class Mail(object):
    __slots__ = ['subject', 'body', 'sender', 'receiver']

    def __init__(self, **kwargs):
        self.subject = kwargs.get('subject')
        self.body = kwargs.get('body')
        self.sender = kwargs.get('from', MailContact())
        self.receiver = kwargs.get('receiver', [])


class MailContact(object):
    __slots__ = ['name', 'mail']

    def __init__(self, **kwargs):
        self.name = kwargs.get('name', config.get('Email.name'))
        self.mail = kwargs.get('mail', config.get('Email.account'))

    def __str__(self):
        return '%s<%s>' % (self.name, self.mail)


def _format_email_address(mc):
    """

    :param mc: MailContact
    :return:
    """
    name, address = parseaddr('<%s>' % mc.mail)
    if name == '':
        name = mc.name
    return formataddr((Header(name, 'utf-8').encode(), address))


def validate_mail(mail: Mail):
    if not mail.subject:
        raise UserError(message='缺少邮件主题')
    if not mail.body:
        raise UserError(message='缺少邮件内容')
    if not mail.receiver or not isinstance(mail.receiver, list):
        raise UserError(message='缺少收件人')
    for r in mail.receiver:
        if not isinstance(r, MailContact):
            raise UserError(message='收件人类型错误')
        if not r.mail:
            raise UserError(message='缺少收件人邮箱地址')
    if not mail.sender:
        raise UserError(message='缺少发件人')
    if not mail.sender.mail:
        raise UserError(message='缺少发件人邮箱地址')


def send(mail_obj, subtype='plain', retried=0):
    """
    发送邮件
    :param retried:
    :param components.mail.Mail mail_obj:
    :param str subtype:
    :return:
    """
    deployment = config.get('App.deployment', 'cloud')
    if deployment == 'local':
        return True
    mail = mail_obj
    validate_mail(mail)
    try:
        msg = MIMEMultipart('mixed')
        mail.body = add_img(mail.body, msg)
        msg_text = MIMEText(mail.body, subtype, 'utf-8')
        msg.attach(msg_text)
        msg['From'] = _format_email_address(mail.sender)
        msg['To'] = ','.join([_format_email_address(t) for t in mail.receiver])
        msg['Subject'] = Header(mail.subject, 'utf-8').encode()
        server = config.get('Email.smtp_server')
        port = int(config.get('Email.smtp_port'))
        use_ssl = int(config.get('Email.smtp_enable_ssl')) == 1
        retry_max = 3  # 重试次数
        try:
            server = smtplib.SMTP_SSL(server, port, timeout=5) if use_ssl else smtplib.SMTP(server, port, timeout=5)
            server.login(mail.sender.mail, config.get('Email.password'))
            server.sendmail(mail.sender.mail, [t.mail for t in mail.receiver], msg.as_string())
            server.quit()
        except smtplib.SMTPServerDisconnected as smt_e:
            retried += 1
            if retried >= retry_max:
                raise smt_e

            time.sleep(0.3)
            send(mail_obj, subtype, retried)

    except Exception as e:
        logger.error('发送邮件失败, subject: %s', mail.subject, exc_info=True)
        raise UserError(message='邮件发送失败,请稍候再试: {}'.format(e))


def add_img(body, msg):
    """
    添加图片
    :param body:
    :param msg:
    :return:
    """
    img_url_data = re.findall('(http.*?\.jpg)|(http.*?\.png)', body, flags=re.I)
    num = 1
    for img_url in img_url_data:
        for i in range(len(img_url)):
            if img_url[i]:
                msg_image = MIMEImage(request.urlopen(img_url[i]).read())
                msg_image.add_header('Content-ID', 'image' + str(num))
                msg.attach(msg_image)
                body = body.replace(img_url[i], 'cid:image' + str(num))
                num += 1
    return body


def replace_content(content, replace_dict):
    """
    替换邮件内容
    :param content:
    :param replace_dict:
    :return:
    """
    for k, v in replace_dict.items():
        content = content.replace(k, v)
    return content
