import traceback
from loguru import logger
from components.rundeck import CommonRunDeckScheduler
from dmplib import config


class EnvTask:

    def __init__(self, job_id='', celery_task_name='', schedule='', queue_name='celery', job_name=''):
        self.job_id = job_id
        self.celery_task_name = celery_task_name
        self.schedule = schedule
        self.queue_name = queue_name or 'celery'
        self.job_name = job_name or f'环境级任务-{celery_task_name}'
        self.load_config()
        if not self.job_id or not self.celery_task_name or not self.schedule:
            raise Exception('参数错误，环境级celery任务注册失败')

    def load_config(self):
        pass

    def get_command(self):
        cmd_template = config.get(
            "Rundeck.cmd_template_celery",
            "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
        )
        return '%s %s %s %s %s' % (cmd_template, "env_task", self.celery_task_name, self.job_id, self.queue_name)

    def add_job(self):
        try:
            logger.info(f"{type(self).__name__} 环境级celery任务开始注册")
            scheduler = CommonRunDeckScheduler()
            command = self.get_command()
            scheduler.upset_job(self.job_id, self.job_name, self.schedule, command)
        except Exception as e:
            logger.error(f"{type(self).__name__}注册环境级任务异常，errs：{str(e)}")
            logger.error(traceback.print_exc())


class SubscribeUsedDatasetTask(EnvTask):
    def load_config(self):
        self.job_id = '00000000-1000-0000-0001-000000000000'
        self.celery_task_name = 'app_celery.feed_used_dataset_stat'
        self.schedule = '0 1 3,16 ? * * *'
        self.job_name = '简讯使用的调度数据集查找异步任务'


class SyncDataToFastTask(EnvTask):
    def load_config(self):
        self.job_id = '00000000-1000-0000-0002-000000000000'
        self.celery_task_name = 'app_celery.async_sync_data_to_fast'
        self.schedule = '0 1 2 ? * * *'
        self.job_name = '定时将报表相关数据写出到天眼文件中'
        self.queue_name = 'upgrade'


class CleanFlowLogTask(EnvTask):
    def load_config(self):
        self.job_id = '00000000-1000-0000-0003-000000000000'
        self.celery_task_name = 'app_celery.clean_flow_log'
        self.schedule = '0 30 2 ? * * *'
        self.job_name = '定时清理dmp-flow-log'
        self.queue_name = 'celery'


class CleanRundeckLogTask(EnvTask):
    def load_config(self):
        self.job_id = '00000000-1000-0000-0004-000000000000'
        self.schedule = '0 10 3 ? * * *'
        self.job_name = '定时清理rundeck日志'
        self.celery_task_name = 'CleanRundeckLogTask'

    def get_command(self):
        return "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/clear_rundeck.py rundeck"


class SyncAppTask(EnvTask):
    def load_config(self):
        self.job_id = '00000000-1000-0000-0005-0appsynctask'
        self.schedule = '0 0/10 * * * ? *'
        self.job_name = '定时重新发布门户'
        self.celery_task_name = 'app_celery.resync_application_task'
        self.queue_name = 'celery'




class RegEnvTask:

    """
    环境级的celery任务注册
    """
    TASK_MAP = [
        SubscribeUsedDatasetTask,
        SyncDataToFastTask,
        CleanFlowLogTask,
        CleanRundeckLogTask,
        SyncAppTask
    ]

    @classmethod
    def reg(cls):
        for task in cls.TASK_MAP:
            task().add_job()
