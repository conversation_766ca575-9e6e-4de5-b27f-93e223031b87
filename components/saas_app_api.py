#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import base64
import hashlib
import hmac
import json
import time
import os
from urllib.parse import urlparse, parse_qsl, urlunparse, urlencode
import requests
import traceback
from loguru import logger
from abc import ABCMeta, abstractmethod
from copy import deepcopy
from datetime import datetime

from components import auth_util
from dmplib.redis import conn as conn_redis
from dmplib.utils.errors import HttpError, UserError
from dmplib.utils.strings import seq_id
from dmplib import config
from base.enums import ThirdPartyAppCode

from components.url import url_add_param
from dashboard_chart.external_service import get_custom_redirect_url
from components.ingrate_platform import IngratePlatformApi


class BaseApi(metaclass=ABCMeta):

    def __init__(self, data):
        self.data = data

        api_host = data.get("api_host")
        _url = urlparse(api_host)
        self.api_host = _url.scheme + '://' + _url.netloc + _url.path.rstrip('/')
        # 秘钥信息
        self.app_id = data.get("app_id")
        self.secret = data.get("app_secret")

    @abstractmethod
    def get_user_group_tree(self):
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def get_user_list(self):
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def send_msg(self):
        raise NotImplementedError("Please Implement this method")

    @staticmethod
    def url_add_param(url: str, params: dict, is_url_encode=False):
        """
        url中增加参数
        :param url:  原始url
        :param params: 要增加的参数
        :param is_url_encode: 是否 url encode
        :return:
        """
        url_parts = list(urlparse(url))
        query = dict(parse_qsl(url_parts[4]))
        query.update(params)
        if query:
            if is_url_encode:
                query = urlencode(query)
            else:
                query = "&".join("{}={}".format(*i) for i in query.items())
            url_parts[4] = query
        return urlunparse(url_parts)


class SaaSAppApi(BaseApi):
    """
    云服务SaaS接口
    已应用服务：
    云空间
    文档接口地址：
    https://ipaas.mingyuanyun.com/cloud/portal/doc/docDetail?path=ykj-mip-test
    """
    TOKEN_VAILD_TIME = 7200

    ACCESS_TOKEN_PATH = 'access/token'

    def __init__(self, app_info):
        super().__init__(app_info)
        self.saas_tenant_code = app_info.get("corp_id")

    def _get_token_signature(self):
        data = dict()
        data['timestamp'] = timestamp = self.get_timestamp()
        base_str = '%s|%s' % (self.app_id, timestamp)
        h = hmac.new(str.encode(self.secret), str.encode(base_str), hashlib.sha1)
        # core = str.encode(h.hexdigest())
        core = h.digest()
        data['sign'] = bytes.decode(base64.b64encode(core)).strip().replace('+', '-').replace('/', '_').replace('=', '')
        return data

    def _access_token_cache_key(self):
        env_code = os.environ.get('CONFIG_AGENT_CLIENT_CODE', '')
        return 'Saas:Mip:Access:Token:' + env_code + ':' + self.app_id + ':' + self.secret

    def get_access_token(self):
        """
        获取请求mip集成平台的access token
        :return:
        """
        # 优先从缓存中读取token
        cache_key = self._access_token_cache_key()
        cache = conn_redis()
        access_token = cache.get(cache_key)
        if access_token:
            return bytes.decode(access_token)

        # 获取秘钥信息
        params = self._get_token_signature()
        timestamp = params.get("timestamp")
        sign = params.get("sign")
        url_path = f"{self.api_host}/{self.ACCESS_TOKEN_PATH}"
        url = f'{url_path}?id=%s&timestamp=%s&valid_time=%s&sign=%s' \
              % (self.app_id, timestamp, self.TOKEN_VAILD_TIME, sign)
        try:
            logger.error(f"云空间请求accesstoken URL: {url}")
            response = requests.get(url, timeout=30)
            if response.status_code != 200:
                raise UserError(message=f'请求异常，status_code:{response.status_code}')
            if response.text:
                rs = response.json()
                if rs.get("code") != 0:
                    raise UserError(message='接口返回错误，err：'+rs.get('message'))
                # 设置缓存
                access_token = rs.get('data')
                self._set_token_cache(access_token)
                logger.error(f"result: {response.text}")
                return access_token
            else:
                raise UserError(message='接口返回数据为空')
        except Exception as e:
            raise UserError(message='获取AccessToken失败:' + str(e))

    def _set_token_cache(self, access_token):
        """
        设置access_token缓存
        :param access_token:
        :return:
        """
        cache_key = self._access_token_cache_key()
        cache = conn_redis()
        expires_in = self.TOKEN_VAILD_TIME
        cache.set(cache_key, access_token, expires_in)

    @classmethod
    def get_timestamp(cls):
        return int(time.time())

    def send_msg(self, user_id, biz_code, snap_id, content_info):
        """
        消息发送到云空间应用
        :param user_id:
        :param biz_code:
        :param snap_id:
        :param content_info:
        :return:
        """
        # 个人简讯订阅类型  1 文本简讯 2 图文简讯，默认为1
        msg_subscribe_config = content_info.get("msg_subscribe_config")
        msg_type = msg_subscribe_config.get("msg_type") \
            if "msg_type" in msg_subscribe_config and msg_subscribe_config.get("msg_type") else 1
        msg_type = int(msg_type)

        # 删除配置节点
        content_info.pop("msg_subscribe_config")
        # 文本
        if msg_type == 1:
            msg_type_name = 'textcard'
        elif msg_type == 2:
            # 图文
            msg_type_name = 'news'
            content_info["picurl"] = msg_subscribe_config.get("msg_pic_url")
        # 简讯内容参数
        content_data_params = {
            msg_type_name: content_info
        }
        send_params = {
            "tenant_code": self.saas_tenant_code,
            "touser": user_id,
            "report_id": biz_code,
            "snap_id": snap_id,
            "msgtype": msg_type_name,
        }
        return self.send_textcard(send_params, content_data_params)

    def send_textcard(self, send_params, content_data_params):
        """
        消息发送
        :param send_params:
        :param content_data_params:
        :return:
        """
        if content_data_params:
            send_params.update(content_data_params)
        action_name = 'dmp-brief/brief/send'
        return self.request_action(action_name, send_params)

    def get_user_group_tree(self, page=1, page_size=10000):
        """
        公司列表接口获取
        :param page:
        :param page_size:
        :return:
        """
        action_name = 'bms/security-acccess/organization/get-organization-list'
        result = self.request_action(action_name, {"page": page, "pageSize": page_size}, 'get')
        return result.get('data')

    def get_user_list(self, params):
        """
        用户列表接口获取
        :param params:
        :return:
        """
        action_name = 'bms/security-acccess/user/get-user-list'
        return self.request_action(action_name, params, 'get')

    def request_action(self, action_name, params=None, request_type='post'):
        """
        接口请求
        :param action_name:
        :param params:
        :param request_type:
        :return:
        """
        try:
            if params is None:
                params = dict()
            result = self.api_request(action_name, params, request_type)
            if result.get('errcode') and result.get('errcode') > 0:
                raise UserError(message=result.get('errmsg'))
            return result
        except HttpError as he:
            raise UserError(message=f"网络异常：{he.description}，status_code：{he.status}")
        except UserError as ue:
            raise ue
        except Exception as e:
            logger.error(traceback.print_exc())
            raise UserError(message=f"程序执行异常：{str(e)}")

    def api_request(self, action_name, params: dict, request_type='post'):
        host = self.api_host
        host = host[: len(host) - 1] if host.endswith('/') else host
        url = '%s/%s' % (host, action_name)
        access_token = self.get_access_token()
        try:
            header_info = dict()
            header_info['Authorization'] = f"Bearer {access_token}"

            logger.error(f"云空间请求URL: {url}")
            logger.error(f"params: {json.dumps(params, ensure_ascii=False)}")
            logger.error(f"header: {json.dumps(header_info, ensure_ascii=False)}")

            if request_type.lower() == 'post':
                response = requests.post(url, json=params, timeout=60, headers=header_info)
            else:
                response = requests.get(url, params=params, timeout=60, headers=header_info)

            result = response.json()
            logger.error(f"result: {json.dumps(result, ensure_ascii=False)}")
        except Exception as be:
            msg = "请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(params))
            raise HttpError(message=msg, status=response.status_code)

        if response.status_code == 200:
            return result
        else:
            if not result.get("success"):
                msg = result.get('message') or result.get('Message')
                raise UserError(message=msg)
            msg = "错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
            raise HttpError(message=msg, status=response.status_code)


class SaaSYLKfApi(BaseApi):
    """
    智慧客服公共组实现的
    云链智慧客服第三方
    接口实现
    文档接口地址：
    2.基础数据-组织用户
    2.1获取组织架构列表接口
    https://open.mysre.cn/#/api-manage/api-doc-preview?api_id=3a075e66-4376-990a-3398-4a23710a7ada&env_code=test
    2.2获取组织用户列表
    https://open.mysre.cn/#/api-manage/api-doc-preview?api_id=3a075f01-7e25-62d4-ba1d-51130a523122&env_code=test
    3.消息中心接口
    3.1新增“待办|未读”消息
    https://open.mysre.cn/#/api-manage/api-doc-preview?api_id=3a001f4c-6814-bc41-e981-92a9928487ce&env_code=test
    3.2修改消息状态为“已办|已读”
    https://open.mysre.cn/#/api-manage/api-doc-preview?api_id=3a002026-26af-aae2-7ab3-f84160ac71e0&env_code=test
    """
    TOKEN_VALID_TIME = 7000

    ACCESS_TOKEN_PATH = 'access_token'

    def __init__(self, app_info):
        super().__init__(app_info)
        self.project_code = app_info.get("project_code")
        # self.project_code = 'retesting'

    def _access_token_cache_key(self):
        env_code = os.environ.get('CONFIG_AGENT_CLIENT_CODE', '')
        return 'Saas:YL_KF:Access:Token:' + env_code + ':' + self.app_id + ':' + self.secret

    def get_access_token(self):
        """
        获取请求mip集成平台的access token
        :return:
        """
        # 优先从缓存中读取token
        cache_key = self._access_token_cache_key()
        cache = conn_redis()
        access_token = cache.get(cache_key)
        if access_token:
            return bytes.decode(access_token)
        # 私有化厦门联发的api_host 可能是这样带上路径的 https://test.open-api.mysre.cn/xmlfjt 而不是纯域名，但获取token的必须从域名获取
        get_token_host = self._get_token_host(self.api_host)
        url_path = f"{get_token_host}/{self.ACCESS_TOKEN_PATH}"
        url = f'{url_path}?appid=%s&secret=%s' % (self.app_id, self.secret)
        try:
            logger.error(f"智慧客服请求access_token URL: {url}")
            response = requests.get(url, timeout=30)
            if response.status_code != 200:
                raise UserError(message=f'请求异常，status_code:{response.status_code}')
            if response.text:
                rs = response.json()
                if rs.get("errcode") != 0:
                    raise UserError(message='接口返回错误，err：'+rs.get('errmsg'))
                # 设置缓存
                data = rs.get('data')
                access_token = data.get('access_token')
                if access_token:
                    self._set_token_cache(access_token)
                logger.error(f"response data: {json.dumps(rs, ensure_ascii=False)}")
                return access_token
            else:
                raise UserError(message='接口返回数据为空')
        except Exception as e:
            raise UserError(message='获取AccessToken失败:' + str(e))

    @staticmethod
    def _get_token_host(api_host):
        """
        去除url中的path
        https://test.open-api.mysre.cn/xmlfjt/ 转换为 https://test.open-api.mysre.cn
        :param api_host:
        :return:
        """
        _url = urlparse(api_host)
        if _url.path:
            api_host = _url.scheme + '://' + _url.netloc
        return api_host

    def _set_token_cache(self, access_token):
        """
        设置access_token缓存
        :param access_token:
        :return:
        """
        cache_key = self._access_token_cache_key()
        cache = conn_redis()
        expires_in = self.TOKEN_VALID_TIME
        cache.set(cache_key, access_token, expires_in)

    @classmethod
    def get_timestamp(cls):
        return int(time.time())

    def send_msg(self, user_info, feed_model, content_info):
        """
        消息发送到云链智慧客服
        :param user_info:
        :param feed_model:
        :param content_info:
        :return:
        """
        send_params = self._send_msg_params(user_info, feed_model, content_info)
        return self.send(send_params)

    def _send_msg_params(self, user_info, feed_model, content_info):
        """
        获取智慧客服的简讯消息发送参数
        app_code = ["5100", "1000", "1001"]
        :param user_info:
        :param feed_model:
        :param content_info:
        :return:
        """
        user_id = user_info.get("id")

        # 个人简讯订阅类型  1 文本简讯 2 图文简讯，默认为1
        msg_subscribe_config = content_info.get("msg_subscribe_config")
        msg_type = msg_subscribe_config.get("msg_type") \
            if "msg_type" in msg_subscribe_config and msg_subscribe_config.get("msg_type") else 1
        msg_type = int(msg_type)
        # 删除配置节点
        content_info.pop("msg_subscribe_config")
        # 图文类型的图片url
        pic_url = msg_subscribe_config.get("msg_pic_url", "")

        content_data = {
            "msg_type": msg_type,
            "title": content_info.get("title"),
            "description": content_info.get("description"),
            "pic_url": pic_url,
        }
        if feed_model.app_code == ThirdPartyAppCode.YL_KF.value:
            send_params = self._get_msg_params_by_yl_kf(user_id, feed_model.dashboard_id, content_data)
        elif feed_model.app_code in [ThirdPartyAppCode.KF_APP.value, ThirdPartyAppCode.KF_WEWORK.value]:
            send_params = self._get_msg_params_by_kf(feed_model.app_code, user_info, feed_model.dashboard_id,
                                                     content_data)
        return send_params

    def _get_msg_params_by_yl_kf(self, user_id, dashboard_id, content_data):
        """
        获取云链公共客服的渠道简讯消息发送参数
        :param user_id:
        :param dashboard_id:
        :param content_data:
        :return:
        """
        msg_type = content_data.get("msg_type")
        title = content_data.get("title")
        description = content_data.get("description")
        pic_url = content_data.get("pic_url", "")

        # 消息结果
        content = {
            "description": description
        }
        # 文本
        if msg_type == 1:
            msg_type_name = 'TEXT_CARD'
        elif msg_type == 2:
            # 图文
            msg_type_name = 'NEWS'
            content["picurl"] = pic_url
        send_params = {
            "tenant_code": self.project_code,
            "content": content,
            "msg_id": seq_id(),
            "title": title,
            "redirect_url": self._get_redirect_url(dashboard_id),
            "source": "DMP",
            "target": "QIWEI",
            "type": msg_type_name,
            "receiver_type": "USER",
            "receiver_id": user_id
        }
        return send_params

    def _get_msg_params_by_kf(self, app_code, user_info, dashboard_id, content_data):
        """
        获取公共客服的渠道（1000,1001）简讯消息发送参数
        :param app_code:
        :param user_info:
        :param dashboard_id:
        :param content_data:
        :return:
        """

        user_id = user_info.get("id")
        user_account = user_info.get("account")

        msg_type = content_data.get("msg_type")
        title = content_data.get("title")
        description = content_data.get("description")
        pic_url = content_data.get("pic_url", "")
        source = 'DMP'

        # 智慧客服APP
        if app_code == ThirdPartyAppCode.KF_APP.value:
            target = 'SUPER_APP'
            content = description
            # 智慧客服APP只支持文本
            msg_type_name = 'TEXT'
            business_type = '知会消息'
        else:
            # 智慧客服企微通
            target = 'WEWORK'
            content = {"description": description}
            business_type = '数据简报'
            # 文本
            if msg_type == 1:
                msg_type_name = 'TEXT'
            elif msg_type == 2:
                # 图文
                msg_type_name = 'NEWS'
                content["picurl"] = pic_url
                # 图文不支持br标签
                if description.find('<br>') > -1:
                    content["description"] = description.replace('<br>', '\n')

        redirect_url = self._get_redirect_kf_url(dashboard_id)
        send_params = {
            "tenant_code": self.project_code,
            "msg_id": seq_id(),
            "receiver_id": user_id,
            # 报告url
            "redirect_url": redirect_url,
            "target": target,
            "source": source,
            "type": msg_type_name,
            "title": title,
            "content": content,
            "business_type": business_type
        }
        if redirect_url:
            # 免登信息
            send_params["sso_data"] = {
                "sso_host": self._get_third_party_passport_sso_url(),
                "sso_center_login_type": "SIGNATURE",
                "app_code": "xmlfjtKfappSso",
                "account": user_account,
            }

        # 智慧客服APP特别参数，传固定值
        if app_code == ThirdPartyAppCode.KF_APP.value:
            send_params["navbar"] = 1
            send_params["classify"] = "通知"
            send_params["icon"] = "https://img.myysq.com.cn/ylys/myapplication/2022/03/10/3a02852f-5f6b-5832-77fb-4e62bb9f0475_orig.png"
        return send_params

    @staticmethod
    def _get_third_party_passport_sso_url():
        third_party_passport_sso_url = config.get("ThirdParty.third_party_passport_sso_url", "")
        logger.error(f"third_party_passport_sso_url：{third_party_passport_sso_url}")
        return third_party_passport_sso_url

    def _get_redirect_url(self, dashboard_id):
        """
        获取云链智慧客服APP中报告跳转的集成url
        :param dashboard_id:
        :return:
        """
        # 未关联报告则无需获取集成url
        if not dashboard_id:
            return ""
        third_party_passport_sso_url = self._get_third_party_passport_sso_url()
        if not third_party_passport_sso_url:
            raise UserError(message="应用APP免登集成url不存在")
        auth_url = self._build_auth_url(dashboard_id)
        return self._build_sso_url(third_party_passport_sso_url, auth_url, self.project_code)

    @staticmethod
    def _build_auth_url(dashboard_id):
        auth_url = get_custom_redirect_url(dashboard_id)
        params = {
            "ticket": "{ticket}",
            "sid": "{sid}",
            "o": "{o}",
        }
        auth_url = url_add_param(auth_url, params)
        return auth_url

    @staticmethod
    def _build_sso_url(third_party_passport_sso_url, return_url, project_code):
        params = {
            "ssoCenterLoginType": "THIRD_TOKEN",
            "appCode": "qiweiSso",
            "tenantCode": project_code,
            "returnUrl": return_url,
        }
        return url_add_param(third_party_passport_sso_url, params)

    def _get_redirect_kf_url(self, dashboard_id):
        """
        获取智慧客服APP，企微客服通中报告跳转的url
        :param dashboard_id:
        :return:
        """
        # 未关联报告则无需获取集成url
        if not dashboard_id:
            return ""

        auth_url = get_custom_redirect_url(dashboard_id)
        params = {
            "ticket": "{ticket}",
            "sid": "{sid}",
            "o": "{o}",
        }
        auth_url = self.url_add_param(auth_url, params)
        return auth_url

    def send(self, send_params):
        """
        消息发送
        :param send_params:
        :return:
        """
        action_name = 'message/create'
        return self.request_action(action_name, send_params)

    def get_user_group_tree(self, page=1, page_size=10000):
        """
        公司列表接口获取
        :param page:
        :param page_size:
        :return:
        """
        action_name = 'business/organization/get-list'
        result = self.request_action(action_name, {"page": page, "page_size": page_size}, 'get')
        return result.get('data')

    def get_user_list(self, params):
        """
        用户列表接口获取
        :param params:
        :return:
        """
        action_name = 'business/organization/get-user-list'
        return self.request_action(action_name, params, 'get')

    def request_action(self, action_name, params=None, request_type='post'):
        """
        接口请求
        :param action_name:
        :param params:
        :param request_type:
        :return:
        """
        try:
            if params is None:
                params = dict()
            result = self.api_request(action_name, params, request_type)
            if result.get('errcode') and result.get('errcode') > 0:
                raise UserError(message=result.get('errMsg'))
            return result
        except HttpError as he:
            raise UserError(message=f"网络异常：{he.description}，status_code：{he.status}")
        except UserError as ue:
            raise ue
        except Exception as e:
            logger.error(traceback.print_exc())
            raise UserError(message=f"程序执行异常：{str(e)}")

    def api_request(self, action_name, params: dict, request_type='post'):
        host = self.api_host
        host = host[: len(host) - 1] if host.endswith('/') else host
        access_token = self.get_access_token()
        url = '%s/%s?access_token=%s&tenant_code=%s' % (host, action_name, access_token, self.project_code)
        try:
            if request_type.lower() == 'post':
                response = requests.post(url, json=params, timeout=60)
            else:
                response = requests.get(url, params=params, timeout=60)
            logger.error(f"云链智慧客服请求URL: {response.url}")
            logger.error(f"params: {json.dumps(params, ensure_ascii=False)}")
            result = response.json()
            data = result.get("data")
            log_result = deepcopy(result)
            # 避免数据太多
            if data and len(data) > 1000:
                log_result["data"] = data[0:1000]
            logger.error(f"result: {json.dumps(log_result, ensure_ascii=False)}")
        except Exception as be:
            msg = "请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(params))
            raise HttpError(message=msg, status=response.status_code)

        if response.status_code == 200:
            return result
        else:
            if result.get("errcode"):
                msg = result.get('message') or result.get('errmsg')
                raise UserError(message=msg)
            msg = "错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
            raise HttpError(message=msg, status=response.status_code)


class SuperWorkApi(BaseApi):
    """
    超级工作台、产业建管服务商模式的调用API
    """

    # 消息发送接口
    MSG_SEND_API = 'openapi/v1/msg/send'

    API_SALT = 'party'

    SUPER_WORK_APP_CODE = 'shujian-msg'

    def __init__(self, app_info):
        self.project_code = app_info.get("project_code")
        # 开发环境测试
        # self.project_code = "dev"
        self.api_host = app_info.get("api_host")
        self.app_code = app_info.get("app_code")
        self.channel_app_id = app_info.get("channel_app_id")
        self.channel_app_secret = app_info.get("channel_app_secret")
        # 基础数据平台接口请求实例，用于选择用户
        self.ingrate_platform_api = IngratePlatformApi(self.project_code)

    def get_user_group_tree(self, page=1, page_size=10000):
        """
        获取基础数据平台的组织列表接口
        :param page:
        :param page_size:
        :return:
        """
        return self.ingrate_platform_api.get_user_group_list(page, page_size)

    def get_user_list(self, params):
        """
        获取基础数据平台的用户信息列表接口
        :param params:
        :return:
        """
        res = self.ingrate_platform_api.get_user_list(params)
        return {"data": res.get("Results", {}), "total": res.get("TotalCount", 0)}

    def get_request_params(self):
        """
        获取请求消息发送接口的token
        :return:
        """
        timestamp = self._get_timestamp()
        signature = self.get_signature(timestamp)
        return signature, timestamp

    def get_signature(self, timestamp):
        encode_str = str(timestamp) + self.channel_app_secret + self.API_SALT + str(self.channel_app_id)
        signature = self.encrypt(encode_str, 'sha1')
        return signature

    @staticmethod
    def _get_timestamp():
        return int(datetime.timestamp(datetime.now()))

    @staticmethod
    def encrypt(src, alg):
        alg = alg.lower()
        if alg not in ['sha1', 'sha256', 'md5']:
            raise Exception('未支持的加密算法: %s' % alg)
        encryptor = getattr(hashlib, alg)()
        encryptor.update(src if isinstance(src, bytes) else bytes(src, encoding='utf-8'))
        return str(encryptor.hexdigest())

    def send_msg(self, user_id, data):
        """
        超级工作台服务商发送消息调用
        :param user_id:
        :param data:
            data = {
                "title": new_title,
                "description": message,
                "url": feed_model.release_url,
                "btntxt": "查看详情",
                "msg_subscribe_config": msg_subscribe_config
            }
        :return:
        """
        # 个人简讯订阅类型  1 文本简讯 2 图文简讯，默认为1
        msg_subscribe_config = data.get("msg_subscribe_config")
        msg_type = msg_subscribe_config.get("msg_type") \
            if "msg_type" in msg_subscribe_config and msg_subscribe_config.get("msg_type") else 1
        msg_type = int(msg_type)

        # 删除配置节点
        data.pop("msg_subscribe_config")

        if msg_type == 1:
            return self.send_text(user_id, data)
        elif msg_type == 2:
            data["picurl"] = msg_subscribe_config.get("msg_pic_url")
            return self.send_news(user_id, data)

    def send_text(self, user_id, data):
        """
        超级工作台服务商-文本提醒消息
        :param user_id:
        :param data:
        :return:
        """
        params = {
            "cid": user_id,
            "msg": {
                "msg_type": "text",
                "text": {
                    "title": data.get("title"),
                    "description": data.get("description"),
                    "url": data.get("url")
                }
            },
        }
        common_params = self.__common_send_params()
        params.update(common_params)
        return self.send_request(self.MSG_SEND_API, params)

    def send_news(self, user_id, data):
        """
        超级工作台服务商-图文待办消息
        :param user_id:
        :param data:
        :return:
        """
        params = {
            "cid": user_id,
            "msg": {
                "msg_type": "news",
                "articles": [
                    {
                        "description": data.get("description"),
                        "pic_url": data.get("picurl"),
                        "title": data.get("title"),
                        "url": data.get("url")
                    }
                ]
            },
        }
        common_params = self.__common_send_params()
        params.update(common_params)
        return self.send_request(self.MSG_SEND_API, params)

    def __common_send_params(self):
        params = {
            "cid_type": 1,
            "msg_channel": 1,
            "type": 1,
            "app_code": self.SUPER_WORK_APP_CODE,
            "channel_code": self.project_code,
            "notify_to": ["app"]
        }
        return params

    def get_api_url(self, api_path):
        host = self.api_host
        host = host[: len(host) - 1] if host.endswith('/') else host
        header = {}
        signature, timestamp = self.get_request_params()
        #统一应用认证
        if auth_util.is_enable_skyline_auth(self.project_code):
            token = auth_util.gen_auth_token({'channel_id': self.channel_app_id})
            header[auth_util.AUTHORIZATION_KEY] = token
            header[auth_util.TENANT_KEY] = self.project_code
        url = '%s/%s?timestamp=%s&channel_id=%s&signature=%s' % (host, api_path, timestamp,
                                                             self.channel_app_id, signature)
        return url, header

    def send_request(self, api_path, params=None):
        """
        消息发送请求方法
        :param api_path:
        :param params:
        :return:
        """
        params = {} if params is None else params
        url, header = self.get_api_url(api_path)
        # 返回结果为list数据
        rs_list = self.api_request(url, params, header_info=header)
        msg_info = {
            'errcode': 0,
            'errmsg': 'ok'
        }
        if rs_list:
            msg_info['data'] = rs_list[0]
        else:
            msg_info['errcode'] = 1
            msg_info['errmsg'] = '超级工作台发送返回为空'
        return msg_info

    @staticmethod
    def api_request(request_url, params: dict, request_type='post', header_info=None):
        if header_info is None:
            header_info = dict()
        try:
            logger.error('超级工作台服务商接口请求 request_url:'+request_url)
            logger.error('超级工作台服务商接口请求 params:' + json.dumps(params, ensure_ascii=False))
            if request_type.lower() == 'post':
                response = requests.post(request_url, json=params, timeout=60, headers=header_info)
            else:
                response = requests.get(request_url, params=params, timeout=60, headers=header_info)
            result = response.json()
            logger.error('超级工作台服务商接口请求 result:' + json.dumps(result, ensure_ascii=False))
        except Exception as be:
            msg = "请检查超级工作台服务商接口请求网络是否正常:{}|url:{}".format(str(be), request_url)
            status_code = response.status_code if 'response' in locals().keys() else 500
            logger.exception(msg)
            raise UserError(message=msg, code=status_code)

        if response.status_code == 200:
            if result.get("errcode") != 0:
                msg = '超级工作台服务商接口请求错误，errs：' + result.get('errmsg')
                logger.error(msg)
                raise UserError(message=msg)
            else:
                return result.get("data")
        else:
            raise UserError(message='超级工作台服务商接口请求异常，text：'+response.text, code=response.status_code)
