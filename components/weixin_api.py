#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    企业微信消息发送类
    <NAME_EMAIL> on 2018/3/22.
"""
import json

import requests
import re
from requests import RequestException
from loguru import logger

from base import repository
from dmplib.utils.errors import UserError
from dmplib.redis import RedisCache


class WeiXinAPI:
    """
    企业微信消息发送API
    """

    __slots__ = ['corpid', 'corpsecret', 'agentid', 'access_token', 'retry']

    def __init__(self, corpid, corpsecret, agentid):
        """
        :param str corpid: 企业ID
        :param str corpsecret : 应用的凭证密钥
        :param str agentid: 应用的ID
        """
        self.corpid = corpid
        self.corpsecret = corpsecret
        self.agentid = agentid
        self.access_token = None
        self.retry = 0

    def get_access_token(self):
        """
        获取AccessToken
        返回数据格式：
        data={'access_token': 'DdMyl/NVeOZilVgdLNZAVw==', 'expires_in': 3600}
        :return:
        """
        # access_token 添加缓存
        cache = RedisCache(key_prefix="access_token")
        key = f"{self.corpid}:{self.corpsecret}"
        access_token = cache.get(key)
        if not access_token:
            url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}".format(
                corpid=self.corpid, corpsecret=self.corpsecret
            )

            try:
                response = requests.get(url, timeout=10)
                if response.status_code != 200:
                    raise UserError(message='获取access_token失败:' + response.text)
                res = response.json()
                if res.get('errcode') != 0:
                    raise UserError(400, res.get('errmsg'))
                access_token, expires_in = res.get("access_token"), res.get("expires_in")
                # 设置缓存， 缓存时间为过期时间的一半儿
                cache.set(key, access_token, int(int(expires_in) / 2))
            except RequestException as e:
                raise UserError(message='获取access_token失败:' + str(e))
        if isinstance(access_token, bytes):
            access_token = access_token.decode()
        return access_token

    def send_message(self, touser, textcard_data):
        """
        消息发送到企业微信
        :param touser:
        :param textcard_data:
        :return:
        """
        # 个人简讯订阅类型  1 文本简讯 2 图文简讯，默认为1
        msg_subscribe_config = textcard_data.get("msg_subscribe_config")
        msg_type = msg_subscribe_config.get("msg_type") \
            if "msg_type" in msg_subscribe_config and msg_subscribe_config.get("msg_type") else 1
        msg_type = int(msg_type)

        # 删除配置节点
        textcard_data.pop("msg_subscribe_config")
        if msg_type == 1:
            return self.send_textcard(touser, textcard_data)
        elif msg_type == 2:
            textcard_data["picurl"] = msg_subscribe_config.get("msg_pic_url")
            return self.send_news(touser, textcard_data)

    def send_textcard(self, touser, textcard_data):
        """
        企业微信-文本卡片模式
        :param touser:
        :param textcard_data:
        :return:
        """
        params = {
            "touser": touser,
            "toparty": "",
            "totag": "",
            "msgtype": "textcard",
            "agentid": self.agentid,
            "textcard": textcard_data,
        }
        return self.api_request(params)

    def send_text(self, touser, text):
        """
        企业微信-文本模式
        :param touser:
        :param text:
        :return:
        """
        params = {
            "touser": touser,
            "toparty": "",
            "totag": "",
            "msgtype": "text",
            "agentid": self.agentid,
            "text": {
                "content": text
            },
        }
        return self.api_request(params)

    def send_news(self, touser, textcard_data):
        """
        企业微信-图文格式
        :param touser:
        :param textcard_data:
        :return:
        """
        # 图文格式不支持br标签，需要替换为\n
        desc = textcard_data.get("description")
        if desc.find('<br>') != -1:
            desc = re.sub('<br>', '\n', desc)
        article_data = {
            "title": textcard_data.get("title"),
            "description": desc,
            "url": textcard_data.get("url"),
            "picurl": textcard_data.get("picurl"),
        }
        params = {
            "touser": touser,
            "msgtype": "news",
            "agentid": self.agentid,
            "news": {
                "articles": [article_data]
            },
        }
        return self.api_request(params)

    def api_request(self, parameters):
        """
        请求API
        :param parameters:
        :return:
        """
        if not self.access_token:
            self.access_token = self.get_access_token()

        url = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}".format(
            access_token=self.access_token
        )
        logger.error(f"简讯weixin请求url：{url}")
        logger.error(f"简讯weixin请求参数：{json.dumps(parameters, ensure_ascii=False)}")
        try:
            response = requests.post(url, json=parameters)
        except Exception as be:
            msg = "企业微信接口请求错误，请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(parameters))
            raise UserError(message=msg)

        if response.status_code == 200:
            result = response.json()
            if result.get("errcode") == 40014 and self.retry < 3:
                self.retry += 1
                self.access_token = self.get_access_token()
                self.api_request(parameters)

            logger.error(f"简讯weixin响应结果：{json.dumps(result, ensure_ascii=False)}")
            return result
        else:
            msg = "企业微信拒绝发送，错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
            raise UserError(message=msg)


def userid_to_openuserid(corp_id, app_secret, userid_list):
    wx = WeiXinAPI(corp_id, app_secret, None)
    access_token = wx.get_access_token()
    url = "https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token={access_token}".format(
        access_token=access_token
    )
    params = {'userid_list': userid_list}
    try:
        response = requests.post(url, json=params)
        return json.loads(str(response.content, 'UTF-8'))
    except Exception as e:
        msg = "企业微信接口请求错误，请检查网络是否正常:{}|url:{}|parameters{}".format(str(e), url, str(params))
        raise UserError(message=msg)


def send_msg_by_wechat(**kwargs):
    from feed.services.wechat_service import WechatService
    project_code = kwargs.get("code")
    channel = kwargs.get("channel")
    to_user = kwargs.get("to_user")
    body = kwargs.get("body")
    # if isinstance(to_user, list):
    #     to_user = "|".join(to_user)
    # 获取秘钥信息
    from dmplib.saas.project import get_db

    with get_db(project_code) as db:
        sql = """
        select tp.corp_id, tpa.app_id, tpa.app_secret, tp.id as third_party_id from third_party_app as tpa inner join third_party as tp 
        on tp.id = tpa.third_party_id where tp.app_code = %(app_code)s
        """
        third = db.query_one(sql, params={"app_code": channel})
    if not third:
        raise UserError(message="企业微信渠道信息不存在")
    third_party_id = third.get("third_party_id")
    third_user_map_list = WechatService.send_user_convert(third_party_id, to_user)
    users = []
    for i in to_user:
        users.append(WechatService.get_send_user_account(third_user_map_list, i.get("account")))
    users = "|".join(users)
    wechat = WeiXinAPI(third.get("corp_id"), third.get("app_secret"), third.get("app_id"))
    return wechat.send_text(
        users, body
    )
