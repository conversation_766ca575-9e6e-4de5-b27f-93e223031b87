#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401,W0613,E1120

"""
common admin route
test
"""

# ---------------- 标准模块 ----------------
import hug
from hug.authentication import authenticator
import jwt
from jwt import DecodeError

# ---------------- 业务模块 ----------------
from dmplib.utils.errors import UserError, UserErrorWithExtraInfo
from dmplib.hug import APIWrapper, g, application, debugger
from dmplib import config
from app_menu.services import application_service
from dmplib.hug.application import auth_cookies_replacement


_debugger = debugger.Debug(__name__)


class CommonProxyAPIWrapper(APIWrapper):
    __slots__ = ["_admin_with_third_party_route"]

    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._route = hug.http(api=self.api)
        self._admin_with_third_party_route = None
        self.api.http.base_url = "/api"

    @property
    def admin_with_third_party_route(self):
        if not self._admin_with_third_party_route:
            self._admin_with_third_party_route = hug.http(
                api=self.api, requires=_token_with_third_party_verify_handle(None)
            )
        return self._admin_with_third_party_route


@authenticator
def _token_with_third_party_verify_handle(request, response, verify_user, **kwargs):
    """
    当前handle为兼容用户登录态鉴权和其他鉴权方式
    """
    auth_cookies_replacement(request)
    _debugger.log("通用登录态鉴权====> start...")
    # 默认的用户登录态鉴权
    default_verify_result = _token_verify_handle(request, response)
    if default_verify_result:
        _debugger.log("通用登录态鉴权====> finish...")
        return default_verify_result

    default_extra_info = {"redirect_url": ""}
    token = request.cookies.get("token")
    _debugger.log({"token": token})
    if not token:
        raise UserErrorWithExtraInfo(code=401, message="请先登录", extra_info=default_extra_info)
    try:
        token_data = jwt.decode(token, config.get("JWT.secret"), algorithms="HS256")
    except DecodeError:
        raise UserErrorWithExtraInfo(code=401, message="请先登录", extra_info=default_extra_info)
    _debugger.log({"token_data": token_data})
    if not token_data:
        raise UserErrorWithExtraInfo(code=401, message="请先登录", extra_info=default_extra_info)

    external_params = token_data.get("external_params", {})
    g.external_params = external_params
    g.extend_yl_params = token_data.get("extend_yl_params", {})
    g.code = token_data.get("code")
    g.account = token_data.get("account", "") or external_params.get("user_account", "")
    g.cookie = {"token": token}
    g.auth_from = token_data.get("auth_from")

    # 门户列表鉴权，没有门户id
    report_type = external_params.get("report_type")
    if report_type == 'portal_list':
        return True

    # 1, 门户第三方鉴权
    _debugger.log("门户第三方鉴权====> start...")
    portal_id = external_params.get("portal_id", "")
    if portal_id:
        if not token_data.get('code'):
            default_extra_info['redirect_url'] = application_service.get_custom_redirect_url(portal_id, False)
            raise UserErrorWithExtraInfo(code=401, message="请先登录", extra_info=default_extra_info)
        portal_info = application_service.get_application_info(portal_id)
        if not portal_info:
            raise UserError(code=404, message="访问DMP门户不存在")
        _debugger.log("门户第三方鉴权====> finish...")
        return True
    return False


def _token_verify_handle(request, response):
    """
    方法来源于dmplib/hug/application/_token_verify_handle
    todo: 将dmplib中的_token_verify_handle抽离为公用方法，替换当前方法
    """
    cookie = request.cookies
    g.cookie = cookie
    g.group_ids = []
    g.group_id = ''
    token = request.cookies.get('token')
    if not token:
        token = request.get_header('Authorization')
        token = token.split(' ')[-1] if token else ''
    if token:
        verified_token = application.verify_token(token)
        if verified_token:
            request.context['jwt_data'] = verified_token
            g.userid = verified_token.get('id')
            g.account = verified_token.get('account')
            if verified_token.get('code'):
                g.code = verified_token.get('code')
            if verified_token.get('group_id'):
                g.group_id = verified_token.get('group_id')
                g.group_ids = [g.group_id]
            if verified_token.get('group_ids'):
                g.group_ids = verified_token.get('group_ids')
            return verified_token
        else:
            return False
    return False
