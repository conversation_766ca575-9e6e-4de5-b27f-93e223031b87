#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by wangl10 on 2017/2/24.
"""
import datetime
import logging
import time

from dmplib import config
from dmplib.db.mysql_wrapper import SimpleMysql


class MysqlHandler(logging.Handler):
    def __init__(self, rds, project_code, flow_instance_id, node_id=None, log_start_time=None, level=logging.NOTSET):
        super().__init__(level)
        self.rds = rds
        self.project_code = project_code
        self.flow_instance_id = flow_instance_id
        self.node_id = node_id
        self.table_name = (log_start_time if log_start_time else datetime.datetime.now()).strftime('%Y%m%d')
        self.conn = None
        self.cursor = None
        self._create_log_table()

    def _get_connection(self):
        return SimpleMysql(
            host=self.rds.get('host'),
            port=int(self.rds.get('port')),
            database=self.rds.get('database'),
            user=self.rds.get('username'),
            password=self.rds.get('password'),
        )

    def _create_log_table(self):
        sql = 'SELECT COUNT(1) AS is_exists FROM information_schema.TABLES ' 'WHERE table_schema= DATABASE() AND `table_name`=%(table_name)s '
        with self._get_connection() as db:
            result = db.query_scalar(sql, {'table_name': self.table_name})
        if not result:
            sql = (
                'CREATE TABLE IF NOT EXISTS `{table_name}` ( '
                'id INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT,'
                '`project_code` varchar(255) DEFAULT \'\','
                '`flow_instance_id` char(36) DEFAULT \'\','
                '`node_id` char(36) DEFAULT \'\','
                '`level_name` varchar(10) DEFAULT \'INFO\','
                '`level_no` int DEFAULT 0,'
                '`line_no` int DEFAULT 0,'
                '`created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,'
                '`file_name` varchar(255) DEFAULT \'\','
                '`module` varchar(255) DEFAULT \'\','
                '`func_name` varchar(255) DEFAULT \'\','
                '`path_name` varchar(511) DEFAULT \'\','
                '`process` int DEFAULT 0,'
                '`process_name` varchar(255) DEFAULT \'\','
                '`thread` bigint DEFAULT 0,'
                '`thread_name` varchar(255) DEFAULT \'\','
                '`exc_text` varchar(8191) DEFAULT \'\','
                '`message` longtext,'
                '`created_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,'
                'KEY `idx_flow` (`project_code`,`flow_instance_id`),'
                'KEY `idx_node` (`project_code`,`flow_instance_id`,`node_id`))'.format(table_name=self.table_name)
            )
            with self._get_connection() as db:
                db.exec(sql)

    def emit(self, record):
        try:
            if record.levelname == 'INFO' and record.name == 'requests.packages.urllib3.connectionpool':
                return
            sql = (
                'INSERT INTO `{table_name}` ('
                '`project_code`,'
                '`flow_instance_id`,'
                '`node_id`,'
                '`level_name`,'
                '`level_no`,'
                '`created`,'
                '`file_name`,'
                '`module`,'
                '`func_name`,'
                '`path_name`,'
                '`line_no`,'
                '`process`,'
                '`process_name`,'
                '`thread`,'
                '`thread_name`,'
                '`exc_text`,'
                '`message`) '
                'VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)'.format(table_name=self.table_name)
            )

            params = [
                self.project_code,
                self.flow_instance_id,
                record.node_id if 'node_id' in dir(record) else (self.node_id if self.node_id else ''),
                record.levelname,
                record.levelno if record.levelno else 0,
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(record.created)),
                record.filename,
                record.module,
                record.funcName,
                record.pathname,
                record.lineno if record.lineno else 0,
                record.process if record.process else 0,
                record.processName,
                record.thread if record.thread else 0,
                record.threadName,
                record.exc_text,
                record.getMessage(),
            ]
            with self._get_connection() as db:
                db.exec(sql, params)
        except Exception as ex:
            record.msg = record.getMessage() + ' WriteLogException:' + str(ex)
            self.handleError(record)


def remove_mysql_log():
    logger = logging.getLogger()
    if logger.hasHandlers():
        for handler in logger.handlers:
            if isinstance(handler, MysqlHandler):
                logger.removeHandler(handler)


def init_mysql_log(project_code, flow_instance_id, node_id=None, log_start_time=None):
    logging.basicConfig(level=config.get('Log.level'))
    logger = logging.getLogger()
    if logger.hasHandlers():
        for handler in logger.handlers:
            if isinstance(handler, MysqlHandler):
                return
    rds = {
        'host': config.get('DB.host'),
        'port': int(config.get('DB.port')),
        'database': config.get('DB.flow_log_database'),
        'username': config.get('DB.user'),
        'password': config.get('DB.password'),
    }
    mysql_handler = MysqlHandler(rds, project_code, flow_instance_id, node_id, log_start_time)
    logger.addHandler(mysql_handler)
