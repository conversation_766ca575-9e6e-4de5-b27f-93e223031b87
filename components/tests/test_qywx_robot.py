# -*- coding: utf-8 -*-
# pylint: skip-file
import os

from components.qywx_robot import QyRobotApi
from tests.base import BaseTest


class TestSchedule(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')
        os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')

    def test_qyrobot(self):
        # env_code = "cmsk"
        tenant = "test"
        indicator_name = "总营销额"
        detail_url = "https://dmp-test.mypaas.com.cn/"
        content = QyRobotApi.gen_alarm_content(tenant, indicator_name, detail_url)

        robot_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7a6dbd74-37cf-40e6-9ff4-eb5533c97bd7"
        res = QyRobotApi(robot_url).request(content)
        assert 'ok' == res.get("errmsg")

        res2 = QyRobotApi(robot_url).send_alarm_message(tenant, indicator_name, detail_url, "DMP测试环境")
        assert 'ok' == res2.get("errmsg")
