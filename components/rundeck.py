#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/22.
"""
import copy
import json

from dmplib.hug import g
import xmltodict
from requests.exceptions import HTTPError
from loguru import logger
import requests
from urllib.parse import urljoin

from rundeck.client import Rundeck
from base.enums import FlowStatus
from dmplib import config
from dmplib.utils.errors import UserError

from base.models import BaseModel
from components import dynamics_config


class Job(BaseModel):
    __slots__ = [
        'id',
        'name',
        'description',
        'group',
        'schedule',
        'sequence',
        'retry',
        'dispatch',
        'logging',
        'schedule_enabled',
        'execution_enabled',
        'nodes_selected_by_default',
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.description = None
        self.group = None
        self.schedule = None
        self.sequence = None
        self.retry = 1
        self.dispatch = JobDispatch()
        self.logging = JobLog()
        self.schedule_enabled = True
        self.execution_enabled = True
        self.nodes_selected_by_default = True
        super().__init__(**kwargs)
        self.schedule_convert_model()

    def schedule_convert_model(self):
        if not isinstance(self.schedule, str) or isinstance(self.schedule, JobSchedule):
            return
        self.schedule = JobSchedule(crontab=self.schedule)

    def get_definition_xml_str(self):
        job = copy.deepcopy(self)
        if job.sequence and isinstance(job.sequence, JobSequence):
            if isinstance(job.sequence.command, JobCommand):
                job.sequence.command = job.sequence.command.get_dict()
            job.sequence = job.sequence.get_dict()
        if job.schedule and isinstance(job.schedule, JobSchedule):
            job.schedule = job.schedule.get_definition_dict()
        if job.dispatch and isinstance(job.dispatch, JobDispatch):
            job.dispatch = job.dispatch.get_definition_dict()

        tmp_dict = job.get_dict(['id', 'name', 'description', 'group', 'schedule', 'sequence', 'retry', 'dispatch'])
        tmp_dict['uuid'] = job.id
        tmp_dict['scheduleEnabled'] = str(job.schedule_enabled).lower()
        tmp_dict['executionEnabled'] = str(job.execution_enabled).lower()
        tmp_dict['nodesSelectedByDefault'] = str(job.nodes_selected_by_default).lower()

        if job.logging and isinstance(job.logging, JobLog):
            tmp_dict['logging'] = job.logging.get_definition_dict()
            tmp_dict['loglevel'] = job.logging.level
            job.logging = job.logging.get_definition_dict()
        tmp_dict['logging'] = {'limit': '100M', 'limitAction': 'halt'}

        return xmltodict.unparse({'joblist': {'job': tmp_dict}})


class JobSequence(BaseModel):
    __slots__ = ['command']

    def __init__(self, **kwargs):
        self.command = None
        super().__init__(**kwargs)


class JobCommand(BaseModel):
    __slots__ = ['exec', 'description']

    def __init__(self, **kwargs):
        """
        命令内容
        :param kwargs:
        """
        self.exec = None
        self.description = None
        super().__init__(**kwargs)


class JobSchedule(BaseModel):
    __slots__ = ['crontab']

    def __init__(self, **kwargs):
        """
        Job调度
        :param kwargs:
        """
        self.crontab = None
        super().__init__(**kwargs)

    def get_definition_dict(self):
        """
        crontab需要设置为xml 属性
        :return:
        """
        return {'@crontab': self.crontab}


class JobLog(BaseModel):
    __slots__ = ['level', 'limit', 'limit_action']

    def __init__(self, **kwargs):
        self.level = 'INFO'
        self.limit = '10M'
        self.limit_action = 'halt'
        super().__init__(**kwargs)

    def get_definition_dict(self):
        return {'limit': self.limit, 'limitAction': self.limit_action}


class JobDispatch(BaseModel):
    __slots__ = ['exclude_precedence', 'keep_going', 'rank_order', 'thread_count']

    def __init__(self, **kwargs):
        self.exclude_precedence = True
        self.keep_going = True
        self.rank_order = 'ascending'
        self.thread_count = 1
        super().__init__(**kwargs)

    def get_definition_dict(self):
        return {
            'excludePrecedence': str(self.exclude_precedence).lower(),
            'keepgoing': str(self.keep_going).lower(),
            'rankOrder': self.rank_order,
            'threadcount': self.thread_count,
        }


class RundeckNew:

    def __init__(self, rundeck_url, token=None, api_version=18):
        self.token = token
        self.rundeck_url = rundeck_url
        self.api_version = api_version

    def __request(
        self, method, url, params=None, upload_file=None, format="xml"
    ):
        """
        rundck接口文档：https://docs.rundeck.com/docs/api/rundeck-api.html
        """
        url = url.strip() if url else url
        if not url.startswith("http"):
            url = urljoin(self.rundeck_url, url)
        logger.info("{} {} Params: {}".format(method, url, params))
        h = {
            "Content-Type": "application/{}".format(format),
            "X-Rundeck-Auth-Token": self.token,
        }
        options = {
            "headers": h,
        }
        if method == "GET":
            options["params"] = params
        elif upload_file is not None:
            options["data"] = upload_file
            options["headers"]["Content-Type"] = "octet/stream"
        else:
            options["data"] = params

        r = requests.request(method, url, **options)
        logger.debug(r.text)
        r.raise_for_status()
        if format == "json":
            try:
                return r.json()
            except ValueError as e:
                logger.error(e)
                return r.text
        else:
            return r.text

    def __get(self, url, params=None, format="xml"):
        valid_format = ["json", "xml", "yaml"]
        if format not in valid_format:
            raise ValueError(
                "Invalid Format. Possible Values are: {}".format(
                    " ,".join(valid_format)
                )
            )
        return self.__request("GET", url, params, format=format)

    def __post(self, url, params=None, upload_file=None):
        return self.__request("POST", url, params, upload_file, format='xml')

    def __delete(self, url, params=None):
        return self.__request('DELETE', url, params, format='xml')

    def export_job(self, job_id):
        return self.__get(url=f"/api/11/job/{job_id}")

    def import_job(self, job_definition, project, dupeOption='update'):
        return self.__post(
            url=f"/api/14/project/{project}/jobs/import?dupeOption={dupeOption}",
            params=job_definition.encode("utf-8")
        )

    def delete_job(self, job_id):
        return self.__delete(url=f"/api/11/job/{job_id}")

    def get_project_history(self, project):
        return self.__get(url=f"/api/14/project/{project}/history", format='json')

    def system_info(self):
        return self.__get(url='/api/14/system/info', format='json')

    def run_job(self, job_id):
        return self.__post(url=f"/api/11/job/{job_id}/run")

    def list_job_executions(self, job_id):
        return self.__get(url=f"/api/11/job/{job_id}/executions")


def get_rundeck_client():
    server = config.get('Rundeck.server')
    port = config.get('Rundeck.port')
    token = config.get('Rundeck.token')
    if config.get("Rundeck.is_new_version"):
        client = RundeckNew(
            rundeck_url=f"http://{server}:{port}",
            token=token
        )
    else:
        client = Rundeck(
            server=config.get('Rundeck.server'),
            port=int(config.get('Rundeck.port')),
            api_token=config.get('Rundeck.token'),
        )
    return client


class RundeckScheduler:
    __slots__ = ['flow_model', 'project_code', 'rundeck_project_name', 'rundeck', 'queue_name']

    def __init__(self, flow_model):
        """
        Rundeck调度器
        :param flow.models.FlowModel flow_model:
        """
        self.flow_model = flow_model
        self.flow_model.name = self.format_name(self.flow_model.name)
        self.project_code = getattr(g, 'code')
        self.queue_name = ''
        self.rundeck_project_name = config.get('Rundeck.project_name')
        self.rundeck = get_rundeck_client()

    @staticmethod
    def format_name(name):
        return name.replace("/", '_').replace("\\", '_')

    @property
    def job_id(self):
        return self.project_code + '_' + self.flow_model.id

    def job_is_exists(self):
        """
        调度任务是否存在
        :return:
        """
        try:
            self.rundeck.export_job(self.job_id)
            return True
        except HTTPError as e:
            if e.response.status_code == 404:
                return False
            raise UserError(message='Rundeck接口调用失败 ' + str(e))
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))

    def _import_job(self, dupe_option=None, command=None):
        """
        调度Job处理
        :param dupe_option:
        :return:
        """
        if not dupe_option:
            dupe_option = 'create'
        job = Job(**self.flow_model.get_dict(['name', 'description', 'schedule']))
        job.id = self.job_id
        job.group = self.project_code
        job.schedule_enabled = self.flow_model.status == FlowStatus.Enable.value
        if not command:
            if self.queue_name:
                command = '%s %s %s %s' % (
                    config.get('Rundeck.cmd_template'),
                    self.project_code,
                    self.flow_model.id,
                    self.queue_name,
                )
            else:
                command = '%s %s %s' % (config.get('Rundeck.cmd_template'), self.project_code, self.flow_model.id)
        job.sequence = JobSequence(command=JobCommand(description=self.flow_model.description, exec=command))
        self._rundec_job_to_java(job, self.project_code)
        try:
            self.rundeck.import_job(
                job.get_definition_xml_str(), project=self.rundeck_project_name, dupeOption=dupe_option
            )
        except HTTPError as e:
            raise UserError(message='添加调度任务失败 ' + str(e))
        except Exception as e:
            raise UserError(message='添加调度任务失败 ' + str(e))

    def _rundec_job_to_java(self, job, project_code):
        java_tenant = dynamics_config.get("Rundeck.java_plugin_tenant", '') or ''
        if not java_tenant or ','+project_code+',' not in java_tenant:
            return
        _command = job.sequence.command
        exec_command = _command.exec
        cmd_template = config.get('Rundeck.cmd_template') + " "
        if exec_command.find(cmd_template) >= 0:
            exec_command = exec_command.replace(cmd_template, "")
            params = exec_command.split(" ")
            if len(params) == 2:
                _command = self._get_node_step_plugin_job_sequence(params[0], params[1], '', '')
            elif len(params) == 3:
                _command = self._get_node_step_plugin_job_sequence(params[0], params[1], params[2], '')
            elif len(params) == 7:
                _command = self._get_node_step_plugin_job_sequence(params[0], params[1], params[2], params[3], params[4], params[5], params[6])
            job.sequence = JobSequence(command=_command)

    @staticmethod
    def _get_node_step_plugin_job_sequence(project_code, flow_id, queue_name, dataset_subject_id, download=None,
                                            force_update=None, new_version=None):
        return {
            "node-step-plugin": {
                "@type": "app_producer_node_step_plugin",
                "configuration": {
                    "entry": [
                        {
                            "@key": "project_code",
                            "@value": project_code
                        },
                        {
                            "@key": "flow_id",
                            "@value": flow_id
                        },
                        {
                            "@key": "queue_name",
                            "@value": queue_name
                        },
                        {
                            "@key": "dataset_subject_id",
                            "@value": dataset_subject_id
                        },
                        {
                            "@key": "download",
                            "@value": download
                        },
                        {
                            "@key": "force_update",
                            "@value": force_update
                        },
                        {
                            "@key": "new_version",
                            "@value": new_version
                        }
                    ]
                }
            }
        }

    def add_job(self, command=None):
        """
        添加调度任务
        :return:
        """
        if self.job_is_exists():
            return
        self._import_job(command=command)

    def update_job(self, command=None):
        """
        修改调度任务
        :return:
        """
        if not self.job_is_exists():
            raise UserError(message='调度任务不存在')
        self._import_job('update', command=command)

    def delete_job(self):
        """
        删除调度任务
        :return:
        """
        if not self.job_is_exists():
            return True
        try:
            self.rundeck.delete_job(self.job_id)
            return True
        except AttributeError as e:
            # 客户端内部bug，实际任务已经被删除
            print(str(e))
            return True
        except HTTPError as e:
            if e.response.status_code == 404:
                return True
            raise UserError(message='Rundeck接口调用失败 ' + str(e))
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))

    def run_job(self, job_id=None):
        """
        立即执行job
        :param job_id:
        :return:
        """
        if not job_id:
            return False
        try:
            res = self.rundeck.run_job(job_id)
            return res
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))


class CommonRunDeckScheduler:
    """
    通用注册RunDeck Job类
    """

    def __init__(self):
        self.rundeck_project_name = config.get('Rundeck.project_name')
        self.rundeck = get_rundeck_client()

    def job_is_exists(self, job_id):
        """
        调度任务是否存在
        :return:
        """
        try:
            self.rundeck.export_job(job_id)
            return True
        except HTTPError as e:
            if e.response.status_code == 404:
                return False
            raise UserError(message='Rundeck接口调用失败 ' + str(e))
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))

    def delete_job(self, job_id):
        """
        删除调度任务
        :return:
        """
        if not self.job_is_exists(job_id):
            return True
        try:
            self.rundeck.delete_job(job_id)
            return True
        except AttributeError as e:
            # 客户端内部bug，实际任务已经被删除
            print(str(e))
            return True
        except HTTPError as e:
            if e.response.status_code == 404:
                return True
            raise UserError(message='Rundeck接口调用失败 ' + str(e))
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))

    def upset_job(self, job_id, name, schedule, command, schedule_enabled: bool = True, **kwargs):
        """
        注册或更新RunDeck Job定时调度任务
        :param id: 唯一ID，标识Job
        :param name: Job名称
        :param schedule: 调度信息，如每个月的1号凌晨2点定时执行："0 0 2 1 * ? *"
        :param command: 调度执行的命令
        :param schedule_enabled: 是否开启调度: True开启, False禁用
        :param kwargs:
            description : str
                Job描述
            group: str
                Job分组
        :return:
        """

        dupe_option = 'update' if self.job_is_exists(job_id) else 'create'
        job_data = {
            'id': job_id,
            'name': name,
            'group': kwargs.get('group', '') or name,
            'description': kwargs.get('description', '') or name,
            'schedule': schedule,
            'schedule_enabled': schedule_enabled,
        }
        job = Job(**job_data)
        job.sequence = JobSequence(command=JobCommand(description=job_data['description'], exec=command))
        try:
            self.rundeck.import_job(
                job.get_definition_xml_str(), project=self.rundeck_project_name, dupeOption=dupe_option
            )
        except HTTPError as e:
            raise UserError(message='添加调度任务失败 ' + str(e))
        except Exception as e:
            raise UserError(message='添加调度任务失败 ' + str(e))

    def run_job(self, job_id=None):
        """
        立即执行job
        :param job_id:
        :return:
        """
        if not job_id:
            return False
        try:
            res = self.rundeck.run_job(job_id)
            return res
        except Exception as e:
            raise UserError(message='Rundeck接口调用失败 ' + str(e))


class EnvRundeckScheduler(RundeckScheduler):
    """
    rundeck 环境级任务操作处理
    任务id不带租户信息
    任务id在环境中不可重复
    """

    @property
    def job_id(self):
        return self.flow_model.id
