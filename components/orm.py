#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2018/3/22.
"""
from urllib import parse
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from dmplib.utils.errors import UserError
from dmplib.utils.sql_util import Description


class OrmConnection:
    __slots__ = ['host', 'user', 'password', 'database', 'port', 'db_type', 'engine']

    def __init__(self, **kwargs) -> None:
        self.host = kwargs.get('host', None)
        self.user = kwargs.get('user', None)
        self.password = kwargs.get('password', None)
        self.database = kwargs.get('database', None)
        self.port = kwargs.get('port', None)
        self.db_type = kwargs.get('db_type', None)
        self.engine = kwargs.get('engine', None)


class OrmDb:
    __slots__ = ['connection', 'connection_str']

    def __init__(self, connection: OrmConnection):
        """
        :param OrmConnection connection:连接信息
        """
        self.connection = connection
        self.connection.password = parse.quote_plus(self.connection.password)
        self.connection_str = self.get_connection_str()

    def get_connection_str(self):
        host = self.connection.host + ':' + self.connection.port if self.connection.port else self.connection.host
        connection_str = '{}+{}://{}:{}@{}/{}'.format(
            self.connection.db_type, self.connection.engine, self.connection.user,
            self.connection.password, host, self.connection.database
        )
        return connection_str

    def get_db(self):
        try:
            engine = create_engine(self.connection_str, connect_args={'charset': 'utf8'})
            session = sessionmaker(bind=engine)
            return session()
        except Exception as e:
            raise UserError(message='创建数据库连接对象错误：' + str(e))

    def test_connection(self):
        conn = None
        try:
            conn = create_engine(self.connection_str).connect()
        except Exception as e:
            raise UserError(message='数据库连接失败：' + str(e))
        finally:
            if conn:
                conn.close()

    def get_connect(self):
        try:
            engine = create_engine(self.connection_str)
            return engine
        except Exception as e:
            raise UserError(message='创建数据库连接对象错误：' + str(e))

    @staticmethod
    def data_format(data):
        columns = data.keys()
        data = data.all()
        if data:
            data = [{column: value for column, value in zip(columns, row)} for row in data]
        return data


class MssqlDescription(Description):

    col_data_type = {
        1: 'nvarchar',
        2: 'binary',
        3: 'int',
        4: 'datetime',
        5: 'decimal',
        34: 'image',
        35: 'text',
        36: 'uniqueidentifier',
        37: 'varbinary',
        38: 'int',
        39: 'varchar',
        40: 'date',
        41: 'time',
        42: 'datetime',
        47: 'char',
        48: 'int',
        52: 'int',
        56: 'int',
        59: 'real',
        60: 'money',
        61: 'datetime',
        62: 'float',
        98: 'sql_variant',
        106: 'decimal',
        108: 'numeric',
        111: 'datetime',
        127: 'bigint',
        175: 'char',
        189: 'timestamp',
        231: 'nvarchar',
    }

    @property
    def data_type(self):
        if not self._data_type:
            self._data_type = self.col_data_type.get(self.type_code, '')
        return self._data_type

    def _get_data_length(self):
        if self.type_code in [1, 39, 175, 231]:
            return str(self.internal_size) if self.internal_size else 'max'
        if self.type_code in [47, 175]:
            return str(self.internal_size) if self.internal_size else '50'
        elif self.type_code in [5]:
            # 解决decimal长度超过65创建表报错的问题
            if self._data_type == 'decimal' and self.precision and int(self.precision) > 65:
                return '65,' + str(self.scale)
            elif self.precision and self.scale:
                return str(self.precision) + ',' + str(self.scale)
            else:
                return '18,4'
