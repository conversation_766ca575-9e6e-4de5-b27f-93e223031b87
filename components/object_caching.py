"""dashboard相关业务缓存
"""

import random
import time
from dashboard_chart.utils import dashboard_cache
from dmplib.redis import RedisCache
from components.versioned_query import shield_redis_cache


class BaseCache:
    redis_client = None

    def __init__(self, redis_conn: RedisCache, cache_key):
        # if self.redis_client is None:
        self.redis_client = redis_conn

        self.cache_key = cache_key

    @shield_redis_cache
    def remove(self):
        return self.redis_client.delete(self.cache_key)

    @shield_redis_cache
    def getall(self):
        return self.redis_client.hgetall(self.cache_key)

    @shield_redis_cache
    def hmget(self, props):
        return self.redis_client.hmget(self.cache_key, props)

    @shield_redis_cache
    def hget(self, prop):
        if not prop:
            raise ValueError('prop')
        return self.redis_client.hget(self.cache_key, prop)

    @shield_redis_cache
    def hset(self, prop, value):
        self.redis_client.hset(self.cache_key, prop, value)

    @shield_redis_cache
    def hdel(self, prop):
        self.redis_client.hdel(self.cache_key, prop)

    @shield_redis_cache
    def set_expires(self, expire_time=7 * 24 * 60 * 60):
        if isinstance(expire_time, int) and expire_time:
            self.redis_client.expire(self.cache_key, expire_time)

    def hincrby(self, prop, amount=1):
        def c_hincrby(client: RedisCache, key, prop, amount):
            key = client._wrapper_key(key)
            return client._connection.hincrby(key, prop, amount)

        return c_hincrby(self.redis_client, self.cache_key, prop, amount)


class VersionHashCache(BaseCache):
    _prop_version_name = 'version'

    @shield_redis_cache
    def set_prop(self, prop, value, expire_flag=False):
        self.redis_client.hset(self.cache_key, prop, value)
        if expire_flag:
            self.set_expires()
        return self.refresh_version()

    @shield_redis_cache
    def get_prop(self, prop):
        if not prop:
            raise ValueError('prop')
        return self.redis_client.hget(self.cache_key, prop)

    @shield_redis_cache
    def set_mprop(self, mapping, expire_flag=False):
        if not isinstance(mapping, dict):
            raise ValueError("mapping should be dict")
        self.redis_client.hmset(self.cache_key, mapping)
        if expire_flag:
            self.set_expires()
        return self.refresh_version()

    @shield_redis_cache
    def del_prop(self, prop):
        self.redis_client.hdel(self.cache_key, prop)
        return self.refresh_version()

    @shield_redis_cache
    def get_version(self):
        return self.redis_client.hget(self.cache_key, self._prop_version_name)

    def refresh_version(self):
        new_version = '%s_%s' % (time.strftime('%Y%m%d%H%M%S'), random.randint(1, 1000))
        self.redis_client.hset(self.cache_key, self._prop_version_name, new_version)
        return new_version


class ObjectCache(VersionHashCache):
    def __init__(self, object_class_name, project_code, object_id, redis_conn):
        """hash缓存对象
        Args:
            object_class_name (str): 对象的分类名称
            project_code (str): 项目代码
            object_id (str): 标识对象实例的id
            redis_conn (RedisCache): dmplib.redis.RedisCache
        """

        if not project_code:
            raise ValueError('project_code')
        if not object_id:
            raise ValueError('object_id')

        self.project_code = project_code
        self.object_id = object_id
        self._object_class_name = object_class_name
        metadata_cache_key_prefix = dashboard_cache.get_metadata_cache_key_prefix()
        cache_key = '{prefix_key}:{object_name}:{object_id}'.format(
            prefix_key=metadata_cache_key_prefix, object_name=object_class_name, object_id=object_id
        )
        super().__init__(redis_conn, cache_key)
