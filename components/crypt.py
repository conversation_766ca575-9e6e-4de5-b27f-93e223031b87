#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import base64
import re
from Crypto.Cipher import AES, DES
from binascii import b2a_hex, a2b_hex

from Crypto.Hash import MD5

from dmplib import config


class PBECrypt:
    _iterations = 20
    _salt_length = 12

    def __init__(self, key="ecology"):
        self.key = key

    def cal_param_spec(self, salt):
        hasher = MD5.new()
        hasher.update(self.key.encode())
        hasher.update(salt)
        param_spec = hasher.digest()
        for _ in range(1, self._iterations):
            hasher = MD5.new()
            hasher.update(param_spec)
            param_spec = hasher.digest()
        return param_spec

    def encrypt(self, text: str):
        md = MD5.new()
        md.update(self.key.encode())
        digest = md.digest()
        salt = digest[0:8]
        param_spec = self.cal_param_spec(salt)

        encoder = DES.new(param_spec[:8], DES.MODE_CBC, param_spec[8:16])
        padding = 8 - len(text) % 8
        text += chr(padding) * padding
        encrypted = encoder.encrypt(text.encode())

        encrypted_string = str(base64.b64encode(encrypted), 'utf-8')
        salt_string = str(base64.b64encode(salt), 'utf-8')
        return salt_string + encrypted_string

    def decrypt(self, encrypted: str):
        salt = base64.b64decode(encrypted[:self._salt_length].encode())
        encrypted = base64.b64decode(encrypted[self._salt_length:].encode())
        param_spec = self.cal_param_spec(salt)

        decoder = DES.new(param_spec[:8], DES.MODE_CBC, param_spec[8:])
        d = str(decoder.decrypt(encrypted), 'utf-8')
        return re.sub(r'[\x01-\x08]', '', d)



class AESCrypt:
    def __init__(self, key=None):
        self.key = key or config.get('Security.dmp_crypt_key')
        self.iv = ""
        if self.key:
            self.iv = self._generate_iv()
            self.key = self.key.encode('utf-8')
        self.key_length = len(self.key)
        self.mode = AES.MODE_CBC

    def _generate_iv(self):
        """
        iv的值为密钥前16位字符，如果密钥不足16位则为密钥本身
        :return:
        """
        return (self.key[:16] if len(self.key) >= 16 else self.key).encode("utf-8")

    def encrypt(self, text):
        """
        加密
        :param str text:
        :return str:
        """
        cipher = AES.new(self.key, mode=self.mode, IV=self.iv)
        text = text.encode('utf-8')
        add = self.key_length - (len(text) % self.key_length)
        text = text + b'\0' * add
        cipher_text = cipher.encrypt(text)
        return b2a_hex(cipher_text).decode('ASCII')

    def decrypt(self, text):
        """
        解密
        :param str text:
        :return str:
        """
        cipher = AES.new(self.key, mode=self.mode, IV=self.iv)
        plain_text = cipher.decrypt(a2b_hex(text))
        return plain_text.rstrip(b'\0').decode('utf-8')



class MysqlAESCrypt:
    """aes-128-ecb，效果同mysql默认aes加密"""
    def __init__(self, key):
        self.key = key.encode()
        self.mode = AES.MODE_ECB
        self.keylen = len(self.key)

    def encrypt(self, text):
        cipher = AES.new(self.key, self.mode)
        padding = self.keylen - len(text) % self.keylen
        text += chr(padding) * padding
        cipher_text = cipher.encrypt(text.encode())
        return b2a_hex(cipher_text).decode().upper()

    def decrypt(self, text):
        cipher = AES.new(self.key, mode=self.mode)
        plain_text = cipher.decrypt(a2b_hex(text))
        return re.sub(r'[\x01-\x08]', '', plain_text.decode())
