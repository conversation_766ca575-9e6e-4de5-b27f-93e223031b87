#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    钉钉消息发送类
"""
import requests
from dmplib.utils.errors import UserError
from dmplib.redis import RedisCache
from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauth2_1_0Client
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalkoauth_2__1__0_models
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util.client import Client as UtilClient
from base import repository


class DingTalkAPI:
    """
    企业微信消息发送API
    """

    __slots__ = ['corp_id', 'client_id', 'client_secret', 'access_token', 'auth_code', 'agent_id', 'retry']

    def __init__(self, corp_id, client_secret, client_id, agent_id, auth_code=None):
        """
        :param str corpid: 企业ID
        :param str corpsecret : 应用的凭证密钥
        :param str agentid: 应用的ID
        """
        self.corp_id = corp_id
        self.agent_id = agent_id
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.auth_code = auth_code
        self.retry = 0

    def get_access_token(self):
        """
        获取AccessToken
        返回数据格式：
        data={'access_token': 'DdMyl/NVeOZilVgdLNZAVw==', 'expires_in': 3600}
        :return:
        """
        # access_token 添加缓存
        cache = RedisCache(key_prefix="access_token")
        key = f"{self.client_id}:{self.client_secret}"
        access_token = cache.get(key)
        if not access_token:
            config = open_api_models.Config()
            config.protocol = 'https'
            config.region_id = 'central'
            client = dingtalkoauth2_1_0Client(config)
            get_access_token_request = dingtalkoauth_2__1__0_models.GetAccessTokenRequest(
                app_key=self.client_id,
                app_secret=self.client_secret
            )
            try:
                res = client.get_access_token(get_access_token_request)
                access_token, expires_in = res.body.access_token, res.body.expire_in
                # 设置缓存， 缓存时间为过期时间的一半儿
                cache.set(key, access_token, int(int(expires_in) / 2))
            except Exception as e:
                if not UtilClient.empty(e.code) and not UtilClient.empty(e.message):
                    raise UserError(message='获取access_token失败:' + str(e))
        if isinstance(access_token, bytes):
            access_token = access_token.decode()
        self.access_token = access_token
        return access_token

    def get_user_info(self):
        try:
            response = self.api_request("https://oapi.dingtalk.com/topapi/v2/user/getuserinfo", {"code": self.auth_code})
            return True, response.get('result').get('userid')
        except Exception as e:
            if not UtilClient.empty(e.code) and not UtilClient.empty(e.message):
                return False, '获取用户信息失败:' + str(e)

    def send_message(self, touser, textcard_data):
        """
        消息发送到钉钉
        :param touser:
        :param textcard_data:
        :return:
        """
        if repository.data_is_exists('user', {"account": touser}):
            raise UserError(message=f"消息发送失败，当前用户{touser}在钉钉不存在")
        # 个人简讯订阅类型  1 文本简讯 2 图文简讯，默认为1
        msg_subscribe_config = textcard_data.get("msg_subscribe_config")
        msg_type = msg_subscribe_config.get("msg_type") \
            if "msg_type" in msg_subscribe_config and msg_subscribe_config.get("msg_type") else 1
        msg_type = int(msg_type)

        # 格式化内容
        description = textcard_data.get("description")
        description = self._format_message(description)
        textcard_data["description"] = description
        # 删除配置节点
        textcard_data.pop("msg_subscribe_config")
        if msg_type == 1:
            return self.send_textcard(touser, textcard_data)
        elif msg_type == 2:
            textcard_data["picurl"] = msg_subscribe_config.get("msg_pic_url")
            return self.send_news(touser, textcard_data)

    @staticmethod
    def _format_message(description):
        if not description:
            return description
        if description.find('<br>') > -1:
            description = description.replace('<br>', '\n')
        return description

    def send_textcard(self, touser, textcard_data):
        """
        钉钉-文本卡片模式
        :param touser:
        :param textcard_data:
        :return:
        """
        msg = {
            "msgtype": "oa",
            'oa': {
                "message_url": textcard_data.get("url"),
                "pc_message_url": textcard_data.get("url"),
                "head": {
                    "bgcolor": 'FFBBBBBB',
                    "text": textcard_data.get("title")
                },
                "body": {
                    "content": textcard_data.get("description"),
                    "title": textcard_data.get("title"),
                }
            }}

        return self.send_ding_talk_message(touser, msg)

    def send_news(self, touser, textcard_data):
        """
        钉钉-文本卡片模式
        :param touser:
        :param textcard_data:
        :return:
        """
        msg = {
            "msgtype": "oa",
            'oa': {
                "message_url": textcard_data.get("url"),
                "pc_message_url": textcard_data.get("url"),
                "head": {
                    "bgcolor": 'FFBBBBBB',
                    "text": textcard_data.get("title")
                },
                "body": {
                    "title": textcard_data.get("title"),
                    "content": textcard_data.get("description"),
                    "image": textcard_data.get("picurl")
                }
            }}
        return self.send_ding_talk_message(touser, msg)

    def send_ding_talk_message(self, touser, msg):
        url = 'https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2'
        params = {
            "agent_id": self.agent_id,
            "userid_list": touser,
            "to_all_user": "false",
            "msg": msg
        }
        return self.api_request(url, params)

    def api_request(self, url, parameters):
        """
        请求API
        :param url:
        :param parameters:
        :return:
        """
        if not self.access_token:
            self.access_token = self.get_access_token()
        try:
            response = requests.post(url + f"?access_token={self.access_token}", json=parameters)
        except Exception as be:
            msg = "钉钉接口请求错误，请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(parameters))
            raise UserError(message=msg)

        if response.status_code == 200:
            result = response.json()
            if result.get("sub_code") == '40014' and self.retry < 3:
                self.retry += 1
                self.access_token = self.get_access_token()
                self.api_request(url, parameters)
            if result.get('errcode') > 0:
                raise UserError(message=result.get('errmsg'))
            return result
        else:
            msg = "请求钉钉接口报错，错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
            raise UserError(message=msg)
