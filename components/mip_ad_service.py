import copy
import json
import traceback
import requests
from loguru import logger
import curlify

from dmplib import config
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from base import repository

import re


# 兼容现有域名服务调用逻辑
class MIPADServiceClient:

    def __init__(self, service_url):
        self.service_url = service_url
        self.app_key = config.get('LoginWebServer.AppKey')
        self.app_secret = config.get('LoginWebServer.AppSecret')
        self.default_timeout = 15
        self.__check_args()
        access_token = self.__init_service_token()
        self.mip_headers = self.__build_mip_headers(access_token)
        # self.service = MIPADService(client=self)
        self.api_gate = f'{self.service_url}/apigate'

    def __check_args(self):
        self.service_url = self.service_url.strip('/')
        if not self.service_url:
            raise Exception('配置域服务地址缺失！')
        if not self.app_key:
            raise Exception('配置域服务认证AppKey缺失！')
        if not self.app_secret:
            raise Exception('配置域服务认证AppSecret缺失！')

    def __build_mip_headers(self, access_token):
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }

    def __build_default_kwargs(self, kwargs):
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.default_timeout
        if 'json' not in kwargs:
            kwargs['json'] = {}  # mip要求空数据传空json
        if 'headers' not in kwargs:
            kwargs['headers'] = {
                'Content-Type': 'application/json',
            }
        return kwargs

    def send(self, method='POST', return_json=True, **kwargs):
        # 交互文档
        # https://www.showdoc.com.cn/2221231897132631/9969925637728338

        kwargs = self.__build_default_kwargs(kwargs)
        try:
            logger.info(f'开始请求域服务的参数({method})：\n{self.hidde_and_format_data(kwargs)}')
            response = getattr(requests, method.lower())(**kwargs)
            logger.info(f'请求域服务的参数的结果：\n{response.text}')
            if return_json:
                return response.json()
            else:
                return response.content.decode()
        except Exception as e:
            logger.error(f'请求域服务失败，原因: {traceback.format_exc()}')
            raise Exception(f'请求域服务失败，原因: {str(e)}')

    def hidde_and_format_data(self, kwargs):
        format_data = copy.deepcopy(kwargs)
        if 'sPassWord' in format_data.get('json', {}):
            format_data['json']['sPassWord'] = '************'
        return json.dumps(format_data, ensure_ascii=False)

    def __init_service_token(self):
        kwargs = {
            "url": f"{self.service_url}/MIPApiAuth/Jwt",
            "json": {
                "AppKey": self.app_key,
                "AppSecret": self.app_secret,
            }
        }
        result = self.send(**kwargs)
        access_token = result.get('access_token', '')
        if not access_token:
            raise Exception('从域服务获取access_token失败！')
        return access_token

    def get_function(self, item):
        # mip神奇的逻辑，这个接口要多加个api，与其他接口规则不一致，不知道为啥
        if item == 'GetUserList':
            item = 'api/GetUserList'
        kwargs = {
            "url": f'{self.api_gate}/{item}',
            "headers": self.mip_headers
        }
        return self.callable_function(item, kwargs)

    def callable_function(self, item, pre_kwargs):
        def function(return_json=True, **real_kwargs):
            pre_kwargs['json'] = real_kwargs
            result = self.send(return_json=return_json, **pre_kwargs)
            if return_json:
                if result.get('code') == '404':
                    raise Exception(f'域服务函数{item}不存在！')
            return result

        return function


class MIPADService:
    def __init__(self, service_url):
        self.client = MIPADServiceClient(service_url)

    def __getattr__(self, item):
        return self.client.get_function(item)

    def UserLoginForDomain(self, sUserName, sPassWord):
        # 校验用户信息，有点特殊，返回的不是json, mip返回的是字符串的True, 单独处理
        result = self.__getattr__('UserLoginForDomain')(return_json=False, **{
            "sUserName": sUserName,
            "sPassWord": sPassWord,
        })
        return result == 'True'


class MDCMIPService:
    def __init__(self, tenant_code):
        self.mip_url = config.get('IngratePlatform.mdc_mip_host') or 'https://mdcmip.mingyuanyun.com'
        self.token_route = '/MIPApiAuth/Token'
        self.tenant_info_route = '/apigate/api/ProductYw/GetKhProductYwInfoByKHGUID'
        self.timeout = 30
        self.code = tenant_code
        self.cache = RedisCache()
        self.cache_token_key = 'mdc_mip:auth_token'
        self.client_id = config.get('IngratePlatform.mdc_mip_client_id') or '9548643bb828429eadd59de189ea5c99'
        self.client_secret = config.get('IngratePlatform.mdc_mip_client_secret') or '73aaf151dbd648a0ae5b62731eee1493'

    def get_access_token(self):
        access_token = self.cache.get(self.cache_token_key)
        if not access_token:
            result = self._do_request(
                method="post",
                url='{}{}'.format(self.mip_url, self.token_route),
                params={
                    "client_id": self.client_id,
                    "client_secret": self.client_secret
                },
                header={'Content-Type': 'application/json'}
            )
            access_token = result.get("access_token")
            if not access_token:
                message = "mip平台获取access_token错误：{}".format(result)
                logger.error(message)
                return ''
            expires_in = int(result.get("expires_in", 3600))
            self.cache.set(self.cache_token_key, access_token, int(expires_in / 2))
        else:
            access_token = access_token.decode()
        return access_token

    def get_tenant_info(self):
        tenant_info = {}
        customer_id = self._get_customer_id()
        if not customer_id:
            return tenant_info
        result = self._do_request(
            method="get",
            url='{}{}'.format(self.mip_url, self.tenant_info_route),
            params={"khguid": customer_id, "source": '数见平台'},
            header=self.headers
        )
        success = result.get('success')
        if success:
            return result.get('data', {})
        else:
            logger.error(result.get('message'))
            return tenant_info

    def tenant_is_expire(self):
        tenant_info = self.get_tenant_info()
        status = tenant_info.get('KhYwStatus', None)
        product_info = tenant_info.get('products', []) or []
        if not status:
            return None
        if status in ['休眠期', '客户丢失']:
            return True
        if not product_info:
            return None
        if status not in ['休眠期', '客户丢失'] and product_info:
            for product in product_info:
                if product.get('Rate', 0) in [0.05, 0.15]:
                    return False
            return True
        return None

    def _get_customer_id(self):
        customer_id = ''
        if self.code:
            customer_id = repository.get_data_scalar('project_yzs_config', {'code': self.code}, 'customer_id', from_config_db=True) or ''
        return customer_id

    @property
    def headers(self):
        return {
            'Content-Type': 'application/json',
            "Authorization": "Bearer " + self.get_access_token()
        }

    def _do_request(self, method, url, params, header):
        try:
            if method.upper() == "GET":
                res = requests.get(url, params=params, headers=header, timeout=self.timeout)
            else:
                res = requests.post(url, json=params, headers=header, timeout=self.timeout)
            logger.info(f"MIP平台请求 curl: {curlify.to_curl(res.request, compressed=True)}, res: {res.text}")
            return res.json()
        except Exception as e:
            message = "MIP平台错误: {}".format(str(e))
            logger.error(message)
            return {}

