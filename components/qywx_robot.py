#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import requests
import logging
import os

from base.errors import RequestWyWxRobotError
from dmplib import config
from dmplib.utils.errors import UserError


ENV_CODE_MAP = {
    "cmsk": "招商",
    "xhl": "新华联",
    "zzkqdc": "康桥",
    "ldxg": "绿地香港",
    "ldjx": "绿地江西",
    "jfdc": "俊发",
    "rongsheng": "荣盛",
    "agile": "雅居乐",
    "sgdc": "山钢",
    "bcdmp": "北辰",
}


class QyRobotApi:
    """
    企业微信请求类
    调用示例：
    res = QyRobotApi(robot_url).send_alarm_message(tenant, indicator_name, detail_url)
    """

    def __init__(
        self,
        robot_url: str = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7a6dbd74-37cf-40e6-9ff4-eb5533c97bd7",
    ):
        # 支持dmp_management配置，或者调用方指定
        self.robot_url = robot_url or config.get("Qywx.RobotURL")
        if not self.robot_url:
            raise UserError(message="企业微信机器人URL未指定或未配置")

    def send_alarm_message(self, env_name, tenant, indicator_name, detail_url, timeout=None, retry=3):
        content = self.gen_alarm_content(env_name, tenant, indicator_name, detail_url)
        return self.request(content, timeout=timeout, retry=retry)

    def request(self, content, timeout=None, retry=3, msg_type: str = 'text'):
        data = {"msgtype": msg_type, msg_type: {"content": content}}
        response = requests.post(self.robot_url, json=data, timeout=timeout or 30)
        if response.status_code != 200:
            # 记录日志
            logging.error("调用企业微信机器人接口失败,还有%s次机会重试", (retry - 1))
            if retry > 0:
                return self.request(content, timeout=timeout, retry=retry - 1)
            # 3次重试还是失败，报错
            raise RequestWyWxRobotError(message="调用企业微信机器人接口失败，请检查配置或联系管理员!")
        return response.json()

    @staticmethod
    def gen_alarm_content(env_name, tenant, indicator_name, detail_url):
        return f"""指标预警异常
环境：{env_name}
租户：{tenant}
【{indicator_name}】预警存在异常，详情：{detail_url}""".encode(
            "utf-8"
        )


class BaseWechatRobot:
    """
    https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=907f6315-3978-4d2a-8d95-199242697514
    """

    def __init__(self, robot_url=None):
        self.robot_url = robot_url or config.get("Qywx.RobotURL")
        if not self.robot_url:
            raise UserError(message="企业微信机器人URL未指定或未配置")

    def request(self, content, timeout=None, retry=3):
        data = {"msgtype": "text", "text": {"content": content}}
        response = requests.post(self.robot_url, json=data, timeout=timeout or 30)
        if response.status_code != 200:
            # 记录日志
            logging.error("调用企业微信机器人接口失败,还有%s次机会重试", (retry - 1))
            if retry > 0:
                return self.request(content, timeout=timeout, retry=retry - 1)
            # 3次重试还是失败，报错
            raise UserError(message="调用企业微信机器人接口失败，请检查配置或联系管理员!")
        return response.json()


class SubscribeRobot(BaseWechatRobot):
    def __init__(
        self,
        robot_url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=907f6315-3978-4d2a-8d95-199242697514",
        client=None,
        tenant=None,
        subscribe_name=None,
        detail_url=None,
        sub_type=None,
    ):
        super(SubscribeRobot, self).__init__(robot_url=robot_url)
        self.client = client if client else get_client()
        self.tenant = tenant
        self.subscribe_name = subscribe_name
        self.detail_url = detail_url
        self.sub_type = sub_type if sub_type else ''

    def send_message(self, timeout=None, retry=3):
        content = self.gen_subscribe_alarm_content()

        return self.request(content=content, timeout=timeout, retry=retry)

    def gen_subscribe_alarm_content(self):
        return f"""{self.sub_type}订阅异常
环境：{self.client}
租户：{self.tenant}
【{self.subscribe_name}】订阅存在异常，详情：{self.detail_url}"""


def get_client():
    client_code = os.environ['CONFIG_AGENT_CLIENT_CODE'] = 'test'
    return ENV_CODE_MAP[client_code] if client_code in ENV_CODE_MAP else '未知环境'
