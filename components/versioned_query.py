#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : versioned_query.py
# @Author: guq
# @Date  : 2021/8/31
# @Desc  :
import re
from contextlib import contextmanager
from datetime import datetime, timedelta
import functools
import pymysql

from .snapshot_service import SNAPSHOT_SERVICES
from base import repository, enums
from dmplib.hug import g, debugger
from dmplib.db.mysql_wrapper import get_db as _get_db
from dmplib import config
from components.utils import CachedProperty

_debugger = debugger.Debug(__name__)

MYSQL_CLIENT_VERSION = pymysql.version_info[:3]


# LRU TTL cache
def timed_cache(**timedelta_kwargs):
    def _wrapper(f):
        update_delta = timedelta(**timedelta_kwargs)
        next_update = datetime.utcnow() + update_delta

        cached_f = functools.lru_cache(None)(f)
        # 给函数加个属性，方便后续调用
        if not hasattr(f, 'lru_cache'):
            setattr(f, 'lru_cache', cached_f)

        @functools.wraps(f)
        def _wrapped(*args, **kwargs):
            nonlocal next_update
            now = datetime.utcnow()
            if now >= next_update:
                f.lru_cache.cache_clear()
                next_update = now + update_delta
            return f.lru_cache(*args, **kwargs)

        return _wrapped

    return _wrapper


# class CachedProperty(object):
#     def __init__(self, factory):
#         self._attr_name = factory.__name__
#         self._factory = factory
#
#     def __get__(self, instance, owner):
#         attr = self._factory(instance)
#         setattr(instance, self._attr_name, attr)
#         return attr


class VersionedCachedObject:
    code_database_cache = {}

    @CachedProperty
    def origin_tables(self):
        tables = [i.name for i in SNAPSHOT_SERVICES.registered_services.values()]
        return tables

    @CachedProperty
    def get_origin_object_list_by_origin_table(self):
        object_dict = {}
        for service in SNAPSHOT_SERVICES.registered_services.values():
            object_dict.update({service.name: service})
        return object_dict

    @CachedProperty
    def origin_tables_no_dataset(self):
        return [t for t in self.origin_tables if not t.startswith('dataset')]

    @CachedProperty
    def pattern_from_regex(self):
        table = '|'.join(self.origin_tables)
        regex = f'((from)\s+`?({table})\\b`?)\s*(\w+)?\s*(\w+)?\s*(\w+)?'
        return regex

    @CachedProperty
    def pattern_from_regex_no_dataset(self):
        table = '|'.join(self.origin_tables_no_dataset)
        regex = f'((from)\s+`?({table})\\b`?)\s*(\w+)?\s*(\w+)?\s*(\w+)?'
        return regex

    @CachedProperty
    def pattern_join_regex(self):
        table = '|'.join(self.origin_tables)
        regex = f'((join)\s+`?({table})\\b`?)\s*(\w+)?\s*(\w+)?\s*(\w+)?'
        return regex

    @CachedProperty
    def pattern_join_regex_no_dataset(self):
        table = '|'.join(self.origin_tables_no_dataset)
        regex = f'((join)\s+`?({table})\\b`?)\s*(\w+)?\s*(\w+)?\s*(\w+)?'
        return regex

    @CachedProperty
    def pattern_exist_table(self):
        table = '|'.join(self.origin_tables)
        regex = f'(join|from)\\s+`?({table})\\b`?'
        return regex

    @CachedProperty
    def pattern_exist_table_no_dataset(self):
        table = '|'.join(self.origin_tables_no_dataset)
        regex = f'(join|from)\\s+`?({table})\\b`?'
        return regex

    @CachedProperty
    def sql_table_relation_keywords(self):
        return {'group', 'where', 'limit', 'order', 'join', 'left', 'right', 'union', 'on'}

    @CachedProperty
    def is_enable_versioned_query(self):
        return config.get('Function.enable_versioned_query') not in ('False', 'false', '0', 0)

    @CachedProperty
    def moz_keywords(self):
        from mo_sql_parsing import keywords
        return {'from', 'union'} | keywords.join_keywords

    @CachedProperty
    def moz_sql_parser(self):
        # 在外面导包会引发debug错误，原因还在调查
        import mo_sql_parsing as moz_sql_parser
        return moz_sql_parser

    def get_tenant_database(self, code):
        if code not in self.code_database_cache:
            with _get_db() as db, shield_versioned():
                sql = (
                    'SELECT p.db_name AS `database` FROM project AS p '
                    'LEFT JOIN rds AS r ON p.rds_id=r.id '
                    'WHERE p.code=%s '
                )
                config = db.query_one(sql, (code,)) or {}
                database = config.get('database', '')
                self.code_database_cache[code] = database
                return database
        return self.code_database_cache[code]

    @timed_cache(minutes=1)
    def has_snapshot(self, snap_id):
        # 快照状态
        with shield_versioned():
            return repository.get_one(
                table_name='snapshot',
                conditions={'snap_id': snap_id, 'status': enums.SnapshotStatus.SUCCESS.value},
                fields=['snap_id', 'modified_on']
            )

    @timed_cache(minutes=1)
    def has_snapshot_dataset(self, snap_id, dataset_id):
        # 是否做过数据集快照， 主要是考虑一次拍照中既有直连又有调度（混合模式）
        with shield_versioned():
            if dataset_id:
                conditions = {'snap_id': snap_id, 'dataset_id': g.dataset_id}
            else:
                conditions = {'snap_id': snap_id}
            return repository.get_one('snapshot_dataset_field', conditions=conditions, fields=['snap_id'])

    @timed_cache(minutes=5)
    def dashboard_has_snapshot(self, snap_id, dashboard_id):
        with shield_versioned():
            return repository.get_one(
                table_name='snapshot_dashboard_released_snapshot_dashboard',
                conditions={'snap_id': snap_id, 'id': dashboard_id},
                fields=['snap_id']
            )


cached = VersionedCachedObject()


# 清除版本查询中的一些lru缓存
# builtins库lru_cache无法实现删除指定key，只能删除所有的函数缓存
def clear_cached_snapshot_status():
    funcs = [cached.has_snapshot, cached.has_snapshot_dataset, cached.dashboard_has_snapshot]
    for func in funcs:
        if hasattr(func, 'lru_cache'):
            func.lru_cache.cache_clear()


# 使用该上下文管理器会屏蔽版本查询的影响
@contextmanager
def shield_versioned():
    if hasattr(cached, 'is_enable_versioned_query'):
        has_flag = True
    else:
        has_flag = False
    status = getattr(cached, 'is_enable_versioned_query') if has_flag else None
    try:
        cached.is_enable_versioned_query = False
        yield
    finally:
        if has_flag:
            setattr(cached, 'is_enable_versioned_query', status)
        else:
            del cached.is_enable_versioned_query


# extract_params = lambda url, key: dict(urllib.parse.parse_qsl(urllib.parse.urlsplit(url).query)).get(key, '')


# 实现对版本查询的支持的装饰器
def versioned_query_support(func):
    def wrapper(request, response, *args, **kwargs):
        # 先从URL里面取， 取不到再从来源取
        # snap_id = extract_params(request.url, 'snap_id') or extract_params(request.referer, 'snap_id')
        # snap_id = extract_params(request.url, 'snap_id')
        snap_id = kwargs.get('snap_id', '')
        if snap_id:
            # 此时g还没有code，无法直接去查询snapshot表
            setattr(g, 'snap_id', snap_id)
        else:
            # if hasattr(g, 'snap_id'):
            #     del g.snap_id
            disable_current_request_versioned()
        return func(request, response, *args, **kwargs)

    return wrapper


def disable_current_request_versioned():
    if hasattr(g, 'snap_id'):
        del g.snap_id


# 请求级别的判断是否要版本查询
def current_request_is_enable_versioned():
    # 1. 全局总开关
    if not cached.is_enable_versioned_query:
        return False

    # 2. 前端标记
    if (not hasattr(g, 'snap_id')) or (not getattr(g, 'snap_id')):
        return False

    return True


# 屏蔽redis缓存
def shield_redis_cache(func):  # NOSONAR
    def _wrapper(*args, **kwargs):
        if current_request_is_enable_versioned() and cached.has_snapshot(g.snap_id):

            if func.__name__ == "hmget":
                res = func(*args, **kwargs)
                if isinstance(res, (list, tuple)):
                    return [None for _ in range(len(res))]
            return None
        return func(*args, **kwargs)

    return _wrapper


class VersionedQueryBase:

    def __init__(self, sql, params, db):
        self.origin_sql = sql
        self.sql = sql
        self.params = params
        self.db = db
        self._services = SNAPSHOT_SERVICES
        self._formatted_sql = self.formatted_sql()
        # self.snap_id = '39feadb3-b3b6-0123-1fd8-195b8eec2124'
        self.snap_id = ''

    def _set_snapshot_dataset_flag(self, snap_id):
        # 有些报告可能是直连和调度模式的， 拍照只拍调度的，那么查询直连的时候不查询数据集拍照
        g.has_snapshot_dataset = False
        dataset_id = getattr(g, "dataset_id", None)
        if cached.has_snapshot_dataset(snap_id, dataset_id):
            g.has_snapshot_dataset = True

    @property
    def need_deal(self):
        # 是否需要处理SQL

        if not current_request_is_enable_versioned():
            return False

        # 3. SELECT
        if not self._formatted_sql.upper().strip().startswith('SELECT'):
            return False

        # 4. 当前SQL是来自于租户库
        if not hasattr(g, 'code') or not getattr(g, 'code') or self.db.db != cached.get_tenant_database(g.code):
            return False

        self._set_snapshot_dataset_flag(g.snap_id)

        # 5. sql中存在要替换的表
        if not re.findall(
                cached.pattern_exist_table if g.has_snapshot_dataset else cached.pattern_exist_table_no_dataset,
                self._formatted_sql, re.I
        ):
            return False

        # 6. 拍照记录是否存在
        if not cached.has_snapshot(g.snap_id):
            return False

        setattr(self, 'snap_id', g.snap_id)

        return True

    def get_target_table_by_origin_table(self, name):
        return self._services.get(name)

    def formatted_sql(self):
        sql = self.sql.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
        sql = ' '.join(filter(lambda x: bool(x), sql.split(' ')))
        return sql

    def pattern_restructure(self, *args, **kwargs):
        raise NotImplementedError

    def versioned_format(self, *args, **kwargs):
        raise NotImplementedError


class VersionedQueryRegex(VersionedQueryBase):
    # 基于正则的实现

    def get_from_table_relation_index(self, matched):
        keys = cached.sql_table_relation_keywords
        for index, item in enumerate(matched[3:]):
            if item.lower() in keys:
                return index + 3
        return -1

    def pattern_restructure(self, sql, mode):
        # 处理FROM的表名替换, 需要处理一下几种情况
        # 1. select * from tableA
        # 2. select * from tableA a  别名a
        # 3. select * from tableA as a
        # 4. select * from tableA as a where id=1
        # 5. select * from tableA where id=1
        # 6. select * from tableA a where id=1
        # .....
        # 处理连表的表名替换, 需要处理一下几种情况
        # 1. select * from tableA join tableB
        # 2. select * from tableA join tableB b
        # 3. select * from tableA join tableB as b
        # 4. select * from tableA join tableB on tableA.id=tableB.id
        # 5. select * from tableA join tableB as b on tableA.id=b.id
        # 也就是最长往后取三位 中间 就是别名信息
        if mode == 'from':
            regex = cached.pattern_from_regex if g.has_snapshot_dataset else cached.pattern_from_regex_no_dataset
        elif mode == 'join':
            regex = cached.pattern_join_regex if g.has_snapshot_dataset else cached.pattern_join_regex_no_dataset
        else:
            raise RuntimeError('model错误！')
        matched = re.findall(regex, sql, re.I)
        # matched = cached.pattern_from_regex.findall(sql, re.I)

        for match in matched:
            # ('FROM/JOIN `dashboard', 'from/join', 'dashboard', 'a', 'where/on', 'id')
            if len(match) == 6:
                has_alias = False
                marked, _mode, replace_table, _, _, _ = match
                key_index = self.get_from_table_relation_index(match)
                if key_index == -1:
                    # 单表， 没有where 啥的， select * from tableA
                    if len(list(filter(lambda x: x, match))) > 3:
                        # ('FROM/JOIN `dashboard', 'from/join', 'dashboard', 'a', '', '')  有别名
                        has_alias = True
                elif key_index == 3:
                    # ('FROM/JOIN `dashboard', 'from/join', 'dashboard', 'where/on', 'id', '') 没有别名
                    pass
                elif key_index >= 4:
                    # ('FROM/JOIN `dashboard', 'from/join', 'dashboard', 'a', 'where/on', '')  有别名
                    # ('FROM/JOIN `dashboard', 'from/join', 'dashboard', 'as', 'a', 'where/on')  有别名
                    has_alias = True

                if has_alias:
                    # 有别名不处理别名，只是替换表名
                    replace_str = f'{_mode} (SELECT * FROM `%s` WHERE `snap_id`=%r)' % (
                        cached.get_origin_object_list_by_origin_table.get(replace_table).target_table,
                        self.snap_id,
                    )
                else:
                    # 没有有别名将新子表as回原表名
                    replace_str = f'{_mode} (SELECT * FROM `%s` WHERE `snap_id`=%r) as %s' % (
                        cached.get_origin_object_list_by_origin_table.get(replace_table).target_table,
                        self.snap_id,
                        replace_table
                    )
                sql = sql.replace(marked, replace_str)
        return sql

    def versioned_format(self):
        sql = self._formatted_sql
        sql = self.pattern_restructure(sql, 'from')
        sql = self.pattern_restructure(sql, 'join')
        return sql, self.params


class VersionedQueryMozParser(VersionedQueryBase):
    # 基于moz_sql_parser的实现
    def replace_condition(self, parent_keyword, table):
        # 检测是否时需要替换表
        return (
                parent_keyword in cached.moz_keywords and
                table in (
                    cached.origin_tables if g.has_snapshot_dataset else cached.origin_tables_no_dataset
                )
        )

    def generate_replaced_table_json(self, origin_table):
        target_table = cached.get_origin_object_list_by_origin_table.get(origin_table).target_table
        return {'select': '*', 'from': target_table, 'where': {'eq': ['snap_id', {'literal': self.snap_id}]}}

    def parsed_walk(self, parsed_dict, last='from', last_dict={}):  # NOSONAR
        # 遍历解析出来的json，完成目标表的替换
        if not last_dict:
            last_dict = parsed_dict

        for key, value in parsed_dict.items():

            if key in ['where', 'group', 'limit', 'order', 'on']:
                continue

            if isinstance(value, str):
                if self.replace_condition(last, value):
                    if isinstance(last_dict[last], str):
                        # select * from A
                        # {'select': '*', 'from': 'A'}
                        parsed_dict[last] = {}
                        parsed_dict[last]['value'] = self.generate_replaced_table_json(value)
                        if 'name' not in parsed_dict:
                            parsed_dict[last]['name'] = value  # 别名回原表名
                    elif isinstance(last_dict[last], dict):
                        # select * from A as a
                        # {'select': '*', 'from': {'name': 'A', 'value': 'a'}}
                        parsed_dict['value'] = self.generate_replaced_table_json(value)
                    elif isinstance(last_dict[last], list):
                        #  select * from A as a join B  处理右表
                        #  {'select': '*', 'from': ['A', {'join': 'B'}]}
                        parsed_dict[key] = {}
                        parsed_dict[key]['value'] = self.generate_replaced_table_json(value)
                        if 'name' not in parsed_dict:
                            # 上级有表名就不用再别名
                            parsed_dict[key]['name'] = value  # 别名回原表名

                elif self.replace_condition(key, value):
                    # SELECT * FROM (SELECT * FROM A)
                    if isinstance(key, str):  # NOSONAR
                        parsed_dict[key] = {}
                        parsed_dict[key]['value'] = self.generate_replaced_table_json(value)
                        if 'name' not in parsed_dict:
                            # 上级有表名就不用再别名
                            parsed_dict[key]['name'] = value  # 别名回原表名

            elif isinstance(value, dict):
                self.parsed_walk(value, key, parsed_dict)

            elif isinstance(value, list):
                for idx, item in enumerate(value.copy()):
                    if isinstance(item, str):
                        if self.replace_condition(last, item):
                            #  select * from tableA join tableB  处理左表
                            # {'select': '*', 'from': ['tableA', {'join': 'tableB'}]}
                            value[idx] = {}
                            value[idx]['value'] = self.generate_replaced_table_json(item)
                            value[idx]['name'] = item  # 别名回原表名
                    elif isinstance(item, dict):
                        self.parsed_walk(item, key, parsed_dict)
            # else:
            #     raise NotImplementedError

    def pattern_restructure(self, sql):
        # 处理FROM的表名替换, 需要处理一下几种情况
        # 1. select * from tableA
        # 2. select * from tableA a  别名a
        # 3. select * from tableA as a
        # 4. select * from tableA as a where id=1
        # 5. select * from tableA where id=1
        # 6. select * from tableA a where id=1
        # .....
        # 处理连表的表名替换, 需要处理一下几种情况
        # 1. select * from tableA join tableB
        # 2. select * from tableA join tableB b
        # 3. select * from tableA join tableB as b
        # 4. select * from tableA join tableB on tableA.id=tableB.id
        # 5. select * from tableA join tableB as b on tableA.id=b.id
        moz_sql_parser = cached.moz_sql_parser

        parsed_json = moz_sql_parser.parse(sql)
        self.parsed_walk(parsed_json)
        should_quote_rule = lambda x: False if x == '*' else True
        return moz_sql_parser.format(parsed_json, should_quote=should_quote_rule, ansi_quotes=False)
        # return moz_sql_parser.format(parsed_json)

    def pre_format_sql_v1(self, query, args):
        # 方法来自MYSQLDb模块中底层方法, moz_sql_parser不支持sql里面有格式化占位符，所以将格式化提前
        # 来自 MySQLdb.version_info (1, 3, 13, 'final', 0)
        PY2 = False

        if not self.db.conn:
            self.db.connect()
        db = self.db.cur._get_db()
        if args is not None:
            if isinstance(args, dict):
                args = dict((key, db.literal(item)) for key, item in args.items())
            else:
                args = tuple(map(db.literal, args))
            if not PY2 and isinstance(query, (bytes, bytearray)):
                query = query.decode(db.encoding)
            query = query % args

        return query

    def pre_format_sql_v2(self, query, args):
        # 方法来自MYSQLDb模块中底层方法, moz_sql_parser不支持sql里面有格式化占位符，所以将格式化提前
        # 不同mysqlclient版本的预先格式化方法不一样。
        # 来自 MySQLdb.version_info (2, 1, 1, 'final', 0)
        if not self.db.conn:
            self.db.connect()
        db = self.db.cur._get_db()
        if isinstance(query, str):
            query = query.encode(db.encoding)

        if args is not None:
            if isinstance(args, dict):
                nargs = {}
                for key, item in args.items():
                    if isinstance(key, str):
                        key = key.encode(db.encoding)
                    nargs[key] = db.literal(item)
                args = nargs
            else:
                args = tuple(map(db.literal, args))
            query = query % args

        if isinstance(query, bytes):
            query = query.decode()
        return query

    def versioned_format(self):
        sql = self._formatted_sql
        # moz_sql 不支持sql中有特殊字符，需要提前格式化SQL
        if MYSQL_CLIENT_VERSION > (2, 0, 0):
            sql = self.pattern_restructure(self.pre_format_sql_v2(sql, self.params))
        else:
            sql = self.pattern_restructure(self.pre_format_sql_v1(sql, self.params))
        # 已经提前格式化了, 不用在再次格式化
        return sql, None


# VersionedQuery = VersionedQueryRegex
VersionedQuery = VersionedQueryMozParser
