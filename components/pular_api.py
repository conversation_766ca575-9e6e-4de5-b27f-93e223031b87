import time

from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter
from urllib3 import Retry
from urllib.parse import urljoin
import requests
from loguru import logger
import json
import os
import re
from requests.exceptions import Timeout

from base.enums import DataSourceType, SkylineApps
from components import auth_util
from components.app_hosts import AppHosts
from dmplib.utils.errors import UserError
from base import repository
from dmplib import config
from dmplib.hug import g
from dmplib.redis import RedisCache

class PulsarApi(object):

    def __init__(self, _from="datasource", host="", app_key="", app_secret="", pulsar_key="", pulsar_code="",
                 set_token_cache=True):
        self._from = _from
        self.host = host or self.shuxin_conn_str.get("api_host")
        self.app_key = app_key
        self.app_secret = app_secret
        self.pulsar_key = pulsar_key
        self.pulsar_code = pulsar_code
        self.cache = RedisCache(key_prefix="indicator_model")
        self.pattern = r"(?P<value>\$\{(.*?)\})"
        self.__check_params()
        self.set_api_attr()
        # 请求接口是否缓存token
        self.set_token_cache = set_token_cache

    def __check_params(self):
        if not self.host:
            raise UserError(message="缺少数芯地址参数")
        self.host = re.sub(self.pattern, self.replace_func, self.host) or self.host

    @staticmethod
    def __get_secret_of_project():
        data = repository.get_data(
            'project', {'code': g.code}, ['pulsar_app_key', 'pulsar_app_secret'],
            from_config_db=True
        )
        key = config.get('SelfService.app_key') if not data.get('pulsar_app_key') else data.get('pulsar_app_key')
        secret = config.get('SelfService.app_secret') if not data.get('pulsar_app_secret') else data.get(
            'pulsar_app_secret')
        return key, secret, ""

    def __get_secret_of_datasource(self):
        app_id = self.shuxin_conn_str.get("app_id")
        app_secret = self.shuxin_conn_str.get("app_secret")
        app_key = self.shuxin_conn_str.get("app_key")
        return app_id, app_secret, app_key

    @staticmethod
    def replace_func(match):
        key = match.group(2) or ''
        value = config.get('ThirdDatasource.{}'.format(key))
        if value:
            return value

    @property
    def shuxin_conn_str(self):
        cache = RedisCache()
        key = 'MysoftShuXin:data_source'
        data_source = cache.get(key)
        if data_source:
            data_source = json.loads(data_source.decode())
        else:
            data_source = repository.get_one("data_source", {'type': DataSourceType.MysoftShuXin.value})
        if not data_source:
            raise UserError(message="未配置数芯数据源")
        cache.set(key=key, value=json.dumps(data_source), time=3600)
        try:
            conn_str = json.loads(data_source.get("conn_str"))
            return conn_str
        except Exception as e:
            raise UserError(message="数芯数据源conn_str配置错误") from e

    def set_api_attr(self):
        if self._from == 'datasource':
            app_id, app_secret, app_key = self.__get_secret_of_datasource()
        elif self._from in ["test_connect", "del_source"]:
            app_id, app_secret, app_key = self.app_key, self.app_secret, self.pulsar_key
        else:
            app_id, app_secret, app_key = self.__get_secret_of_project()

        self.app_key = app_id
        self.app_secret = app_secret
        self.pulsar_key = app_key
        # # 做替换操作
        self.app_key = re.sub(self.pattern, self.replace_func, self.app_key) or self.app_key
        self.app_secret = re.sub(self.pattern, self.replace_func, self.app_secret) or self.app_secret
        self.pulsar_key = re.sub(self.pattern, self.replace_func, self.pulsar_key) or self.pulsar_key

    @property
    def api_token(self):
        # key = f"{self.pulsar_code}:pulsar_api:{self.app_key}:token"
        key = f"{self.pulsar_code}:pulsar_api{self.app_key}:{self.pulsar_key}:token"
        token = self.cache.get(key)
        if token:
            return token.decode()

        params = {
            "app_key": self.pulsar_key,
            "app_secret": self.app_secret,
            "app_id": self.app_key
        }
        session = self.__retry_session(max_retry=2)
        try:
            timeout = int(config.get("External.pulsar_api_timeout", 30) or 30)
        except Exception as e:
            logger.error(f"get timeout error: {e}")
            timeout = 30
        try:
            rsp = session.get(
                url=f'{self.host}/open-api/tag/archive-data/token_api/get',
                params=params, timeout=timeout,
                headers=self.headers)
            rsp.encoding = "utf-8"
            res = rsp.json()
            token, expires_in = res.get('data', {}).get("token"), int(res.get("data", {}).get("expires_in"))
            # token是否缓存。在数芯数据源编辑时，可能会输入错误的秘钥，这是不缓存token值
            # 默认设置缓存
            if self.set_token_cache:
                self.cache.set(key, token, int(expires_in / 2))
            return token
        except Exception as e:
            logger.error(f"请求数芯老接口open-api/tag/archive-data/token_api/get报错: {e}，切换到api_token_v15接口")
            return self.api_token_v15
    @property
    def api_token_v15(self):
        # key = f"{self.pulsar_code}:pulsar_api:{self.app_key}:token_v15"
        key = f"{self.pulsar_code}:pulsar_api{self.app_key}:{self.pulsar_key}:token_v15"
        token = self.cache.get(key)
        if token:
            return token.decode()
        params = {
            "app_key": self.pulsar_key,
            "app_secret": self.app_secret,
            "app_id": self.app_key
        }

        session = self.__retry_session(max_retry=2)
        try:
            timeout = int(config.get("External.pulsar_api_timeout", 30) or 30)
        except Exception as e:
            logger.error(f"get timeout error: {e}")
            timeout = 30
        host = self.host
        if auth_util.is_env_enable_skyline_auth():
            host = AppHosts.get(SkylineApps.DAP)
        rsp = session.get(
            url=f'{host}/token_api/get',
            params=params, timeout=timeout,
            headers=self.headers)
        rsp.encoding = "utf-8"
        res = rsp.json()
        token, expires_in = res.get('data', {}).get("token"), int(res.get("data", {}).get("expires_in"))

        # token是否缓存。在数芯数据源编辑时，可能会输入错误的秘钥，这是不缓存token值
        # 默认设置缓存
        if self.set_token_cache:
            self.cache.set(key, token, int(expires_in / 2))
        return token
    @staticmethod
    def add_params(params: dict, **kwargs):
        """
        添加参数
        :param params:
        :param kwargs:
        :return:
        """
        if params is None:
            params = {}
        for key, value in kwargs.items():
            if not key or not value:
                continue
            if isinstance(value, list):
                value = ",".join(value)
            params[key] = value

    @property
    def headers(self):
        return {
            "Content-Type": "application/json"
        }

    @staticmethod
    def __retry_session(max_retry: int = 2):
        retry = Retry(
            total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1,
            status_forcelist=(500, 503)
        )
        adapter = HTTPAdapter(max_retries=retry)
        session = requests.session()
        session.mount('https://', adapter)
        session.mount('http://', adapter)
        return session

    def __get(self, url: str, params: dict, cookies: dict = None, max_retry: int = 2, timeout: int = None, token_version: str = "v1"):
        result = ''
        is_success = False
        st = time.time()
        if not params:
            params = {}
        try:
            _token = self.api_token_v15 if token_version == 'v1.5' else self.api_token
            params.update({"jwt": _token})
            if not timeout:
                timeout = int(config.get("External.pulsar_api_timeout", 30) or 30)
            session = self.__retry_session(max_retry)
            rsp = session.get(url=url, params=params, cookies=cookies, timeout=timeout, headers=self.headers)
            rsp.encoding = "utf-8"
            if rsp.status_code != 200:
                msg = f"call pulsar api error: {rsp.text}"
                logger.exception(msg)
                raise UserError(message=msg)
            result = rsp.text
            res_data = rsp.json()
            if res_data.get("errMsg") != "成功" or res_data.get('errCode') not in [0, "0", 1]:
                raise UserError(message=f"pulsar错误：{res_data}")
            is_success = True
            return res_data
        except UserError as e:
            is_success = False
            result = str(e)
            raise e from e
        except Exception as e:
            is_success = False
            msg = f'调用Pulsar接口失败, 错误原因: {str(e)}'
            logger.exception(msg)
            result = msg
            raise UserError(message=msg) from e
        finally:
            ed = time.time()
            self.fast_logger_record(url, params, cookies, result, is_success, st, ed)

    def __post(self, url: str, params: dict = None, body: dict = None, cookies: dict = None, max_retry: int = 2,
               timeout: int = None, _headers: dict = None, token_version: str = "v1"):
        from dataset.query.result_data import DatasetQueryTimeOutException, DatasetQueryException, QueryDataError

        result = ''
        is_success = False
        args = {}
        st = time.time()
        try:
            if not params:
                params = {}
            _token = self.api_token_v15 if token_version == 'v1.5' else self.api_token
            args = {'jwt': _token}
            args.update(params)
            if _headers:
                self.headers.update(_headers)

            if not timeout:
                timeout = int(config.get("External.pulsar_api_timeout", 30) or 30)
            session = self.__retry_session(max_retry)
            rsp = session.post(url=url, params=args, json=body, cookies=cookies, timeout=timeout, headers=self.headers)
            result = rsp.text
            # logger.info(f"recv pulsar api response, data: {rsp.text}")  # pylint: disable=W1203
            is_success = True
            res_data = rsp.json()
            if res_data.get("errMsg") != "成功" or res_data.get('errCode') not in [0, "0", 1]:
                raise UserError(message=f"pulsar错误：{res_data}")
            return res_data
        except UserError as e:
            is_success = False
            result = str(e)
            raise e from e
        except Timeout as e:
            is_success = False
            result = f'接口超时：{str(e)}'
            raise DatasetQueryTimeOutException(msg=result, error_code=QueryDataError.TimeoutError.value) from e
        except Exception as e:
            is_success = False
            msg = f'调用数芯接口外部错误, 错误原因: {str(e)}'
            logger.exception(msg)
            result = msg
            raise DatasetQueryException(msg=msg, error_code=QueryDataError.OtherError.value) from e
        finally:
            ed = time.time()
            # 拼接url参数
            if args:
                from components.url import url_add_param
                url = url_add_param(url, args)
            self.fast_logger_record(url, body, cookies, result, is_success, st, ed)

    def indicator_event_notify(self, event: int = 0):
        """
        新增、删除数据源需要通知数芯开启和关闭同步回调
        :param event:0-新增,1-删除
        :return:
        """
        # 通知接口不要影响主流程(兼容数芯配置失效的问题)
        try:
            from dataset.api_pulsar_route import PULSAR_CALLBACK_KEY

            if event not in [0, 1]:
                raise UserError(message="不支持的事件")
            body = {
                "env_code": os.environ.get('CONFIG_AGENT_CLIENT_CODE') or 'dev-wh',
                "event": event,
                "key": PULSAR_CALLBACK_KEY,
                # "key": self.shuxin_conn_str.get("key"),
                "project_code": self.pulsar_code or self.shuxin_conn_str.get("project_code"),
                "url": f"{config.get('Domain.dmp')}/api/pulsar_api/indicator_model/callback"
                # "url": self.shuxin_conn_str.get("url")
            }
            res = self.__post(
                url=f"{self.host}/openapi/indicator/indicator_common/callback",
                body=body)
            if res.get("errMsg") != "成功":
                raise UserError(message=f"indicator_event_notify error: {res}")
            return res
        except Exception as e:
            logger.error(f"通知数芯回调失败：{e}")

    def get_all_indicator_class(self, class_id: str = None):
        """
        获取所有类目
        https://apifox.com/apidoc/shared-e9726831-ba0c-4de9-934d-2600ae8b1e7f/api-25841738
        :param class_id:
        :return:
        """
        self.pulsar_code = self.pulsar_code or self.shuxin_conn_str.get("project_code")
        params = {
            "project_code": self.pulsar_code or self.shuxin_conn_str.get('project_code'),
            # "app_id": self.app_key,
            "tenant_code": g.code
        }
        if class_id:
            params['id'] = class_id
        res = self.__get(
            # url=f"{self.host}/dmp_api/indicator/class",
            url=f"{self.host}/openapi/indicator/indicator_common/class_list",
            params=params
        )
        return res.get("data") or []

    def get_all_indicator(self, indicator_id: str = None, class_id: str = None):
        """
        获取所有指标
        :param indicator_id:
        :param class_id:
        :return:
        """
        self.pulsar_code = self.pulsar_code or self.shuxin_conn_str.get("project_code")
        params = {
            "project_code": self.pulsar_code,
            "tenant_code": g.code
        }
        if indicator_id:
            params['id'] = indicator_id
            params['indicator_code'] = indicator_id
        if class_id:
            params['class_id'] = class_id
        res = self.__get(
            # url=f"{self.host}/dmp_api/indicator/get_indicator",
            url=f"{self.host}/openapi/indicator/indicator_common/indicator_list",
            params=params
        )
        return res.get("data") or []

    def get_all_common_indicator(self, indicator_code=None, class_id=None, keyword=None, back_data_type=1):
        """
        按业务空间获取所有租户指标相同信息指标列表接口
        https://apifox.com/apidoc/shared-e9726831-ba0c-4de9-934d-2600ae8b1e7f/api-85676379
        :param indicator_code:指标code, 可传多个, 按逗号分割
        :param class_id:指标类目id,可传多个,按逗号分割
        :param keyword:指标关键字,可传多个,按逗号分割,传指标英文名或指标中文名或指标类目名(最后一级类目)
        :param back_data_type:需要返回的数据，0-全部返回(默认) 1-只返回指标基础数据和指标可分析维度 2-只返回指标基础数据和指标依赖模型表字段(数见的建议传1,其他业务对接建议传2,可减少返回的数据量)
        :return:
        """
        params = {
            "project_code": self.pulsar_code or self.shuxin_conn_str.get("project_code")
        }
        self.add_params(
            params, indicator_code=indicator_code, class_id=class_id, keyword=keyword, back_data_type=back_data_type)

        data = self.__get(
            url=urljoin(self.host, "/openapi/indicator/indicator_common/indicator_common_info_list"),
            params=params
        )
        return data.get('data') or []

    def get_all_dynamic_dim(self):
        """
        获取指标动态可分析维度
        https://apifox.com/apidoc/shared-e9726831-ba0c-4de9-934d-2600ae8b1e7f/api-85677804
        :param codes:指定要获取指标的code列表,当列表为空时返回所有有动态列指标的内容
        :return:
        """
        body = {
            "project_code": self.pulsar_code or self.shuxin_conn_str.get("project_code"),
            "tenant_code": g.code,
            "codes": []
        }
        result = self.__post(
            url=urljoin(self.host, "/openapi/indicator/indicator_common/get_indicator_dynamic_dimension"),
            body=body
        )
        return result.get('data') or {}

    def check_url_exist(self):
        """
        校验接口是否存在
        :return:
        """
        url = urljoin(self.host,
                      f"/openapi/indicator/indicator_common/get_indicator_dynamic_dimension?jwt={self.api_token}")

        body = {
            "project_code": self.pulsar_code or self.shuxin_conn_str.get("project_code"),
            "tenant_code": g.code,
            "codes": []
        }

        try:
            response = requests.post(url=url, json=body, timeout=10, headers=self.headers)
            logger.info(f"接口校验, http_code:{response.status_code}")
            return response.status_code
        except Exception as e:
            logger.error(f"校验接口是否存在报错：{e}")
            return 404

    def get_model_common_info(self, code='', category=''):
        """
        数见获取模型明细公共信息
        https://apifox.com/apidoc/shared-6ab9df1d-2242-431a-b412-7809fdc30d5e/api-85693629
        :param code:
        :param category:
        :return:
        """
        params = {
            "project_code": self.pulsar_code or self.shuxin_conn_str.get("project_code")
        }
        self.add_params(params, code=code, category=category)
        result = self.__get(
            url=urljoin(self.host, "/openapi/model/get_model_common_info"),
            params=params
        )
        return result.get('data') or []

    def get_model_dynamic_info(self):
        """
        数见获取模型明细动态字段信息
        https://apifox.com/apidoc/shared-6ab9df1d-2242-431a-b412-7809fdc30d5e/api-85693637
        :return:
        """
        body = {
            "project_code": self.pulsar_code or self.shuxin_conn_str.get("project_code"),
            "tenant_code": g.code,
            "codes": []
        }
        result = self.__post(
            url=urljoin(self.host, "/openapi/model/get_model_dynamic_info"),
            body=body
        )
        return result.get('data') or {}

    def query_data(self, body: dict, cookies: dict = None):
        """
        pulsar取数接口
        :param body:
        :param cookies:
        :return:
        """
        from dataset.query.result_data import DatasetQueryException, QueryDataError

        self.pulsar_code = self.pulsar_code or self.shuxin_conn_str.get("project_code")
        res = self.__post(
            url=f'{self.host}/dmp_api/indicator/query',
            body=body,
            cookies=cookies
        )
        if res.get('errCode') != 0:
            raise DatasetQueryException(msg=f"请求pulsar错误: {res}", error_code=QueryDataError.OtherError.value)
        return res

    def get_pulsar_code_list(self):
        """
        获取数芯code
        :return:
        """
        res = self.__get(url=f'{self.host}/dmp_api/get_project_list', params={})
        return res.get('data') or []

    def get_engine_list(self):
        """
        获取数芯查询引擎
        :return:
        """
        self.pulsar_code = self.pulsar_code or self.shuxin_conn_str.get("project_code")
        res = self.__get(url=f'{self.host}/dmp_api/get_query_engine_list', params={'pulsar_code': self.pulsar_code})
        data = res.get('data') or {}
        if not isinstance(data, dict):
            raise UserError(message="数芯返回查询引擎列表格式错误")
        return data.get("resource_type_list") or []

    def get_model_brief_info(self, category: str = None, code: str = None):
        """
        获取模型信息
        api文档：https://www.apifox.cn/apidoc/shared-c861d355-3dd0-4862-9596-42a0d047c08c/api-50429122
        :param code: 模型code
        :param category: 模型类型
        :return:
        """
        self.pulsar_code = self.pulsar_code or self.shuxin_conn_str.get("project_code")
        params = {
            'project_code': self.pulsar_code,
            'category': category,
            'tenant_code': g.code
        }
        if code:
            params['code'] = code
        res = self.__get(
            url=urljoin(self.host, '/openapi/model/get_model_brief_info'),
            params=params
        )
        if res.get('errCode') not in [0, "0"]:
            raise UserError(message=f"获取模型接口错误：{res}")
        return res.get('data') or []

    def get_indicator_permissions(self, class_id, user_account, code):
        self.pulsar_code = self.pulsar_code or self.shuxin_conn_str.get("project_code")
        params = {
            'tenant_code': code,
            'class_id': class_id,
            'user_account': user_account,
            'project_code': self.pulsar_code
        }
        resp = self.__get(
            url=urljoin(self.host, '/openapi/indicator/indicator_common/user/indicator_permissions'),
            params=params
        )
        if resp.get('errCode') not in [1, "1"]:
            raise UserError(message=f"获取模型接口错误：{resp}")
        return resp.get('data') or []

    @staticmethod
    def fast_logger_record(url, params, cookies, result, is_success, st, ed):
        """
        日志记录天眼
        :param url:
        :param params:
        :param cookies:
        :param result:
        :param is_success:
        :return:
        """
        try:
            start_time = int(st * 1000)
            end_time = int(ed * 1000)
            duration = end_time - start_time

            log_data = {
                "action": "request_pulsar",
                "api_url": url,
                "start_time": str(start_time),
                "end_time": str(end_time),
                "duration": str(duration),
                "api_param": json.dumps(params, ensure_ascii=False) if params else '',
                "is_success": "1" if is_success else "0",
                "api_result": result,
                "cookies": json.dumps(cookies, ensure_ascii=False) if cookies else '',
                "org_code": g.code if hasattr(g, 'code') else "",
                "account": g.account if hasattr(g, 'account') else "",
                "dashboard_id": getattr(g, 'dashboard_id_of_query', ''),
                "dashboard_name": getattr(g, 'log_dashboard').get('name', '') if getattr(g, 'log_dashboard',
                                                                                         '') else '',
                "dataset_id": getattr(g, 'dataset_id_of_query', ''),
                "trace_id": getattr(g, 'trace_id_of_query', ''),
                "sql_from": getattr(g, 'sql_from', 'viewreport') or 'viewreport'
            }
            is_record_log = int(config.get('Product.record_getdata_api_log', 0))
            is_yunqing = int(config.get('App.is_deploy_in_yunqing', 0))
            if is_record_log and is_yunqing:
                log_data["env_code"] = str(os.environ.get('CONFIG_AGENT_CLIENT_CODE'))
                log_data["app_name"] = config.get("App.name", "")
                from app_celery import upload_log_to_aliyun
                upload_log_to_aliyun.apply_async(
                    kwargs={
                        "log_data": [tuple([k, v]) for k, v in log_data.items()]
                    },
                    queue='celery'
                )
            from components.fast_logger import FastLogger
            FastLogger.ApiFastLogger(**log_data).record()
        except Exception as e:
            logger.error("记录API请求日志失败：" + str(e))
 

if __name__ == "__main__":
    from dmplib.hug.globals import _app_ctx_stack, _AppCtxGlobals
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()
    g.code = 'indicator_test'
    _app_ctx_stack.push(g)
    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)

    # 测试获取类目
    api = PulsarApi(
        _from='test_connect',
        host='https://bigdata-test-openapi.mypaas.com',
        # app_key='0142569090928657',
        app_secret='wZEYkDPQdwqeMORORRGcCakpButzLdzf',
        # pulsar_key='00000000-1111-1111-1111-000000000001',
        pulsar_key='0569031685710799',
        app_key='00000000-1111-1111-1111-000000000001',
        pulsar_code='model_zn'
    )
    # api = PulsarApi()
    # class_list = api.get_all_indicator_class()
    # print("class_list: ", class_list)
    #
    # api = PulsarApi()
    # indicator_list = api.get_all_indicator(indicator_id='c3d0b955-d73f-4d36-b3a2-35b5746a5a9f')
    # print("indicator_list: ", indicator_list)

    # res = api.indicator_event_notify()
    # print(res)

    res = api.get_indicator_permissions('e06ed1cb-3a4b-11ee-a29a-0255ac1000a0', 'zhang4', 'model_zn')
    print(res)
