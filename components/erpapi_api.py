#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

from dmplib.saas.project import get_mysoft_erp
from dmplib.utils.errors import UserError
from components.mysoft import CloudAPI
from base.enums import DatasetEditMode, DataSourceType, ThirdPartyAppCode
from base import repository

# 明源接口管家集成方案或者调用接口管家的请求
from user.repositories.user_repository import decrypt_field


class MysoftERPAPI:
    def __init__(self, corp_id=None, corpsecret=None, agentid=None, third_party_id=None):
        """
        :param str corpid: 企业ID
        :param str corpsecret : 应用的凭证密钥
        :param str agentid: 应用的ID
        """
        self.corp_id = corp_id
        self.corpsecret = corpsecret
        self.agentid = agentid
        self.access_token = None
        self.retry = 0
        self.third_party_param = self.get_third_party_param(corp_id, third_party_id)

    send_erpapi_msg_path = 'api/Public/SendErpApiMsg.asmx'
    get_erp_api_auth_path = 'api/Public/GetErpApiAuth.asmx'

    @staticmethod
    def get_third_party_param(corp_id, third_party_id):
        where = {'app_code': ThirdPartyAppCode.JKGJ.value}
        if corp_id:
            where['corp_id'] = corp_id
        if third_party_id:
            where['id'] = third_party_id
        third_party_info = repository.get_one('third_party', where)
        param_dic = {}
        if third_party_info:
            param_list = repository.get_list('third_party_param', {'third_party_id': third_party_info.get('id')})
            for param in param_list:
                param_dic[param.get("key")] = param.get('value')
        return param_dic

    def get_erp_api_auth(self, request_param):
        # 获取cloud_app配置
        login_apiurl = self.third_party_param.get('auth_url', '')
        if not login_apiurl:
            raise UserError(message='单点登录自定义接口地址未配置')
        request_param['login_apiurl'] = login_apiurl
        # 用户参数配置 {"param_key":"param_value","_header_option":{"appid":"xxx","appkey":"xxx"}}
        user_param = self.third_party_param.get('user_param', '')
        if not user_param:
            user_param = '{"erpapiauth_paramname":"erpusercode"}'
        user_param_dic = json.loads(user_param)
        request_param.update(user_param_dic)
        return self.post_erpapi_request(self.get_erp_api_auth_path, request_param)

    def post_erpapi_request(self, requset_path: str, params: dict = None, erp_site=None):
        """
        请求数据服务中心公共方法
        :param action_name 接口名称
        :param params 接口参数
        :param erp_site 接口参数
        """
        if params is None:
            params = dict()
        # 获取接口管家地址
        erp_site = get_mysoft_erp() if erp_site is None else erp_site
        if erp_site['erpapi_host'] is None:
            raise UserError(message='未配置接口管家地址!')
        erp_site['erpapi_access_id'] = '' if erp_site['erpapi_access_id'] is None else erp_site['erpapi_access_id']
        erp_site['erpapi_access_secret'] = (
            '' if erp_site['erpapi_access_secret'] is None else erp_site['erpapi_access_secret']
        )
        api = CloudAPI(
            erp_site['erpapi_host'],
            erp_site['erpapi_access_id'],
            erp_site['erpapi_access_secret'],
            requset_path,
        )
        # 请求接口
        return api.post_erpapi_request({'data': params})

    def send_message(self, touser, textcard_data):
        """
        消息发送到接口管家
        :param touser:
        :param textcard_data:
        :return:
        """
        param = self.get_send_msg_param(touser, textcard_data)
        ret = self.post_erpapi_request(self.send_erpapi_msg_path, param)
        if not ret.get('success', False) or ret.get('success', False) == '0':
            errcode = 5001
            errmsg = ret.get('msg', '')
            invalid_user = ret.get('trace', '')
            return {'errcode': errcode, 'errmsg': errmsg + invalid_user, 'invaliduser': ''}
        else:
            return {'errcode': 0, 'errmsg': 'ok', 'invaliduser': ''}

    def get_send_msg_param(self, touser, textcard_data):
        if not self.third_party_param:
            raise UserError(message='未配置接口管家集成配置')
        sendmsg_apiurl = self.third_party_param.get('send_msg_url', '')
        if not sendmsg_apiurl:
            raise UserError(message='发送消息自定义接口地址未配置')
        # 用户参数配置 {"param_key":"param_value","_header_option":{"appid":"xxx","appkey":"xxx"}}
        api_param = self.third_party_param.get('user_param', '')
        if not api_param:
            api_param = '{"erpapiauth_paramname":"erpusercode"}'
        user_info = repository.get_one('user', {'account': touser})
        if not user_info:
            raise UserError(message='发送的用户不存在')
        param_user_info = {
            'user_guid': user_info.get('id'),
            'user_name': user_info.get('name'),
            'user_code': touser,
            'mobile_tel': decrypt_field(user_info.get('mobile')),
            'offcie_tel': '',
            'home_tel': '',
            'email': user_info.get('email'),
            'is_disabled': user_info.get('is_disabled')
        }
        param_msg_info = {
            "biz_id": None,
            "tenant_id": None,
            "appcode": "3021",
            "openid": None,
            "msgtype": "oa",
            "content": {
                "body": {
                    "title": textcard_data.get("title"),
                    "content": textcard_data.get("description")
                },
                "message_url": textcard_data.get("url")
            },
            "status": 1,
            "safe": None,
            "sendto": None,
            "no_disturbing": None,
            "traceId": None,
            "recv_time": None,
            "mns_messageid": None
        }
        return {
            "handleType": "sendmsg",
            "msgInfo": json.dumps(param_msg_info, ensure_ascii=False),
            "userInfo": json.dumps(param_user_info, ensure_ascii=False),
            "paramInfo": api_param,
            "sendmsgApiurl": sendmsg_apiurl
        }
