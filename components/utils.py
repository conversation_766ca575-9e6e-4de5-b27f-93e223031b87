# -*- coding: utf-8 -*-
import base64
import hmac
import json
import platform
from hashlib import md5
import functools
from typing import Callable

import jwt
from alibabacloud_openapi_util.sm3 import Sm3
from jwt import algorithms, register_algorithm
from jwt.algorithms import HMACAlgorithm
from gmssl import sm4, sm3

import hug
import datetime
from datetime import timedelta
import logging
from loguru import logger
import gevent

from dmplib.hug import g, debugger
import pymysql
from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.redis import conn as redis_conn
from base.dmp_constant import REDIS_TEMPORARY_LOG_STORE_KEY
from dmplib import config
from functools import wraps, lru_cache
from components.redis_utils import stale_cache


class DateUtil:
    __slots__ = ['now']

    def __init__(self):
        self.now = datetime.datetime.now()

    def get_from_today(self, day):
        # 距今天
        from_day = self.now - timedelta(days=day)
        return from_day.strftime("%Y-%m-%d")

    def get_today(self):
        # 今天
        today = self.now
        return today.strftime("%Y-%m-%d")

    def get_yesterday(self):
        # 昨天
        yesterday = self.now - timedelta(days=1)
        return yesterday.strftime("%Y-%m-%d")

    def get_tomorrow(self):
        # 明天
        tomorrow = self.now + timedelta(days=1)
        return tomorrow.strftime("%Y-%m-%d")

    def get_this_week(self):
        # 本周第一天和最后一天
        this_week_start = self.now - timedelta(days=self.now.weekday())
        this_week_end = self.now + timedelta(days=6 - self.now.weekday())
        return this_week_start.strftime("%Y-%m-%d"), this_week_end.strftime("%Y-%m-%d")

    def get_last_week(self):
        # 上周第一天和最后一天
        last_week_start = self.now - timedelta(days=self.now.weekday() + 7)
        last_week_end = self.now - timedelta(days=self.now.weekday() + 1)
        return last_week_start.strftime("%Y-%m-%d"), last_week_end.strftime("%Y-%m-%d")

    def get_this_month(self):
        # 本月第一天和最后一天
        this_month_start = datetime.datetime(self.now.year, self.now.month, 1)
        this_month_end = datetime.datetime(self.now.year, self.now.month + 1, 1) - timedelta(days=1)
        return this_month_start.strftime("%Y-%m-%d"), this_month_end.strftime("%Y-%m-%d")

    def get_last_month(self):
        # 上月第一天和最后一天
        this_month_start = datetime.datetime(self.now.year, self.now.month, 1)
        last_month_end = this_month_start - timedelta(days=1)
        last_month_start = datetime.datetime(last_month_end.year, last_month_end.month, 1)
        return last_month_start.strftime("%Y-%m-%d"), last_month_end.strftime("%Y-%m-%d")

    def get_this_quarter(self):
        # 本季第一天和最后一天
        month = (self.now.month - 1) - (self.now.month - 1) % 3 + 1
        this_quarter_start = datetime.datetime(self.now.year, month, 1)
        this_quarter_end = datetime.datetime(self.now.year, month + 3, 1) - timedelta(days=1)
        return this_quarter_start.strftime("%Y-%m-%d"), this_quarter_end.strftime("%Y-%m-%d")

    def get_last_quarter(self):
        # 上季第一天和最后一天
        month = (self.now.month - 1) - (self.now.month - 1) % 3 + 1
        this_quarter_start = datetime.datetime(self.now.year, month, 1)
        last_quarter_end = this_quarter_start - timedelta(days=1)
        last_quarter_start = datetime.datetime(last_quarter_end.year, last_quarter_end.month - 2, 1)
        return last_quarter_start.strftime("%Y-%m-%d"), last_quarter_end.strftime("%Y-%m-%d")

    def get_this_year(self):
        # 本年第一天和最后一天
        this_year_start = datetime.datetime(self.now.year, 1, 1)
        this_year_end = datetime.datetime(self.now.year + 1, 1, 1) - timedelta(days=1)
        return this_year_start.strftime("%Y-%m-%d"), this_year_end.strftime("%Y-%m-%d")

    def get_last_year(self):
        # 去年第一天和最后一天
        this_year_start = datetime.datetime(self.now.year, 1, 1)
        last_year_end = this_year_start - timedelta(days=1)
        last_year_start = datetime.datetime(last_year_end.year, 1, 1)
        return last_year_start.strftime("%Y-%m-%d"), last_year_end.strftime("%Y-%m-%d")


def debugger_and_logger(name=__name__, prefix=''):
    _debugger = debugger.Debug(name)

    def debuglog(message, level='info', *args, **kwargs):
        _debugger.log(message)
        msg = message.format(args, kwargs) if isinstance(message, str) else str(message)
        getattr(logger.opt(depth=1), level)(f'{prefix}{msg}')  # 跳到实际被调用的调用栈输出日志

    return debuglog


class CachedProperty(object):
    def __init__(self, factory):
        self._attr_name = factory.__name__
        self._factory = factory

    def __get__(self, instance, owner):
        attr = self._factory(instance)
        setattr(instance, self._attr_name, attr)
        return attr


def str_equal(obj_a, obj_b):
    """
    比较两个对象的字符串形式是否相等
    """
    return str(obj_a) == str(obj_b)


def mysql_escape_string(data):
    try:
        data = pymysql.converters.escape_string(str(data).encode(encoding='utf-8').decode(encoding='utf-8'))
        if isinstance(data, bytes):
            data = data.decode(encoding='utf-8')
    except Exception as e:
        logging.error(f'mysql_escape_string error: {str(e)}')
        return data
    return data


def redirect_to_error_page(msg_str: str):
    if not isinstance(msg_str, str):
        msg_str = str(msg_str)
    msg = parse.quote(msg_str, safe=":\"")
    return hug.redirect.to(f'/static/errorTip.html?msg={msg}')


def hash_k(k):
    return md5(str(k).encode("utf-8")).hexdigest()


def split_array(array, size):
    return [array[i:i + size] for i in range(0, len(array), size)]


def array_generator(array):
    for element in array:
        yield element


def remove_temporary_files(*files):
    """移除临时文件"""
    for file in files:
        try:
            os.remove(file)
        except:
            pass


def set_temporary_log(uuid, data, cache_time=5 * 60):
    """
    设置一个临时日志记录
    使用/api/monitor/tmp_log?uuid={uuid}可以进行日志查询
    """
    conn = redis_conn()
    key = f'{REDIS_TEMPORARY_LOG_STORE_KEY}:{uuid}'
    conn.set(key, data, cache_time)


def get_temporary_log_url(uuid, raw=0):
    domain = config.get('Domain.dmp', '')
    return f'{domain}/api/monitor/tmp_log?uuid={uuid}&raw={raw}'


def set_g_property(to_g, **g_kwargs):
    # 在不同的协程或者线程中，需要处理g的传递（g只能保证线程程安全）
    # 有些属性不能直接带过去，特别是在同时在不同的租户之间切换的时候
    ignores = ['DBContext', 'cache', 'get', 'pop', 'redis_conns', 'storage']
    for key, val in g_kwargs.items():
        if key in ignores:
            continue
        setattr(to_g, key, val)
    to_g.customize_roles = (to_g.customize_roles or []) if hasattr(to_g, 'customize_roles') else []


def get_g_property():
    # 获取g的关键属性
    return {key: getattr(g, key, None) for key in dir(g) if not (key.startswith('__') and key.endswith('__'))}


def handle_g_auto(func: Callable) -> Callable:
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread_local_g = _AppCtxGlobals()
        g_kwargs = kwargs.pop('g', {})
        set_g_property(thread_local_g, **g_kwargs)  # noqa
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()

    return wrapper


# 返回一个节省空间的json
def space_saving_json(data):
    return json.dumps(data, separators=(',', ':'), ensure_ascii=False)


# 后台执行某个任务，不关心执行结果
def spawn_background_task(func):
    def wrapper(*args, **kwargs):
        logger.info(f'开始后台任务：{func.__qualname__}')
        kwargs['g'] = get_g_property()
        h_func = handle_g_auto(func)
        task = gevent.spawn(h_func, *args, **kwargs)
        task.start()
        return task

    return wrapper


def timed_lru_cache(seconds: int = 120, maxsize: int = 128):
    def wrapper_cache(func):
        func = lru_cache(maxsize=maxsize)(func)
        func.lifetime = timedelta(seconds=seconds)
        func.expiration = datetime.datetime.utcnow() + func.lifetime

        if not hasattr(func, 'lru_cache'):
            setattr(func, 'lru_cache', func)

        @wraps(func)
        def wrapped_func(*args, **kwargs):
            if datetime.datetime.utcnow() >= func.expiration:
                func.cache_clear()
                func.expiration = datetime.datetime.utcnow() + func.lifetime
            return func(*args, **kwargs)

        return wrapped_func

    return wrapper_cache


def system_arch():
    return 1 if platform.machine() == 'aarch64' else 0


def sm4_encode(key: str, value: bytes):
    """
    原文文本编码:UTF-8
    模式: CBC
    IV: 取密钥的前16位
    填充模式: PKCS#7
    块大小(密钥长度): 128位(16字节)
    :param key:
    :param value:
    :return:
    """
    iv = key[:16].encode()  # bytes类型
    crypt_sm4 = sm4.CryptSM4()
    crypt_sm4.set_key(key, sm4.SM4_ENCRYPT)
    return crypt_sm4.crypt_cbc(iv, value)  # bytes类型


def sm4_decode(key: str, value: bytes):
    """
    原文文本编码:UTF-8
    模式: CBC
    IV: 取密钥的前16位
    填充模式: PKCS#7
    块大小(密钥长度): 128位(16字节)
    :param key:
    :param value:
    :return:
    """
    iv = key[:16].encode()  # bytes类型
    crypt_sm4 = sm4.CryptSM4()
    crypt_sm4.set_key(key, sm4.SM4_DECRYPT)
    return crypt_sm4.crypt_cbc(iv, value)  # bytes类型


def sm3_encode(key, src_data):
    try:
        key = fill_padding(key.decode())
        key = base64.urlsafe_b64decode(key)
        mac = hmac.new(key, digestmod=Sm3)
        mac.update(src_data)
        return mac.digest()
    except Exception as e:
        logger.info("Failed to encrypt with key: %s", e)
        raise RuntimeError("Exception occurred during encryption with key")


def fill_padding(s):
    miss = 4 - len(s) % 4
    if miss:
        s += '=' * miss
    return s


class SM4Algorithm(HMACAlgorithm):

    def __init__(self, hash_alg=None):
        super().__init__(hash_alg)

    def sign(self, msg, key):
        return sm4_encode(key, msg)

    def verify(self, msg, key, sig):
        return sig == self.sign(msg, key)


class SM3Algorithm(HMACAlgorithm):

    def __init__(self, hash_alg=None):
        super().__init__(hash_alg)

    def sign(self, msg, key):
        return sm3_encode(key, msg)

    def verify(self, msg, key, sig):
        return sig == self.sign(msg, key)



from urllib import parse
ori_urljoin = parse.urljoin

def new_urljoin(base, url, allow_fragments=True):
    if not base.endswith('/'):
       base = f"{base}/"
    url = url.lstrip('/')
    return ori_urljoin(base, url, allow_fragments)


def jwt_patch():
    try:
        register_algorithm('SM4', SM4Algorithm())
        register_algorithm('SM3', SM3Algorithm())
    except Exception as e:
        logger.error(f'注册jwt算法:{str(e)}')

    parse.urljoin = new_urljoin


@stale_cache(prefix='get_default_account', expire=1800)
def get_default_account(code):
    from base import repository

    if not code:
        return 'admin'

    return repository.get_data_scalar(
        'project', {'code': code}, col_name='default_account', from_config_db=True
    ) or 'admin'


# -- 判断字符串是否是数值
def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        pass
    try:
        import unicodedata
        unicodedata.numeric(s)
        return True
    except Exception as e:
        pass
    return False


if __name__ == '__main__':
    import os

    os.environ['prometheus_multiproc_dir'] = '/tmp'
    jwt_patch()
    s = '***************************************************************************************************************.***************************************************************************************************************.tOSVR6KtckO06l1eLFTmLH6aw_F-6b610PeZAEFnC38'
    k = '3a11017d47b1e6fp8oy6934lnnvnj1rf'
    # s = jwt.decode(s, k, algorithms='SM3')
    # print(s)
    payload = {
        'sub': 'dmp',
        'nbf': int((datetime.datetime.now() - timedelta(hours=1)).timestamp()),
        'exp': int((datetime.datetime.now() + timedelta(hours=1)).timestamp()),
        'jti': 'aaaaaa',
    }
    headers = {'kid': '1709088242', 'k': k}
    token = jwt.encode(payload, headers=headers, key=k, algorithm='SM3')
    print(token)
    decode = jwt.decode(token, k, algorithms='SM3')
    print(decode)

