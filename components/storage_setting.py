#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2021/3/22.
"""
import sys
from typing import Text
import json
import datetime

from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.db.mysql_wrapper import get_db
from dmplib.hug import g
from base.models import BaseModel
from base import repository
from dmplib.saas.project import get_project_info

from base.enums import DatasetStorageType, DatasetType, DataSourceType

PREFIX = "dmp:storage"
PREFIX_ENV = "dmp:dmp_env_sign"


def get_storage_type(project_code: Text) -> Text:
    """
    获取数据集拍照存储配置
    params: project_code
    """
    # 先从缓存中获取
    # return "local"
    cache = RedisCache(key_prefix=f"{PREFIX}:")
    storage_type = cache.get(key=project_code)
    # 缓存中不存在，则从数据库中获取
    if not storage_type:
        with get_db() as db:
            sql = "select storage_type from project where code = %(project_code)s"
            storage_type = db.query_scalar(sql, params={"project_code": project_code})
            # 设置缓存 7天过期： 60 * 60 * 24 * 7
            cache.set(key=project_code, value=storage_type, time=604800)
    if isinstance(storage_type, bytes):
        storage_type = storage_type.decode()
    return storage_type


def get_dmp_env_sign(project_code: Text) -> Text:
    """
    获取数据集拍照存储配置
    params: project_code
    """
    # 先从缓存中获取
    cache = RedisCache(key_prefix=f"{PREFIX_ENV}:")
    dmp_env_sign = cache.get(key=project_code)
    # 缓存中不存在，则从数据库中获取
    if not dmp_env_sign:
        with get_db() as db:
            sql = "select dmp_env_sign from project where code = %(project_code)s"
            dmp_env_sign = db.query_scalar(sql, params={"project_code": project_code})
            # 设置缓存 7天过期： 60 * 60 * 24 * 7
            cache.set(key=project_code, value=dmp_env_sign, time=604800)
    if isinstance(dmp_env_sign, bytes):
        dmp_env_sign = dmp_env_sign.decode()
    return dmp_env_sign


def set_storage_type(project_code: Text, storage: Text) -> None:
    """
    更新数据集拍照存储配置
    params: project_code
    params: storage
    """
    # 更新数据库配置
    with get_db() as db:
        sql = "update project set storage_type = %(storage)s where code = %(project_code)s"
        db.exec_sql(sql, {"storage": storage, "project_code": project_code})
        # 更新缓存
        RedisCache(key_prefix=f"{PREFIX}:").set(key=project_code, value=storage, time=604800)


def set_dmp_env_sign(project_code: Text, dmp_env_sign: Text) -> None:
    """
    更新数据集拍照存储配置
    params: project_code
    params: storage
    """
    # 更新数据库配置
    with get_db() as db:
        sql = "update project set dmp_env_sign = %(dmp_env_sign)s where code = %(project_code)s"
        db.exec_sql(sql, {"dmp_env_sign": dmp_env_sign, "project_code": project_code})
        # 更新缓存
        RedisCache(key_prefix=f"{PREFIX_ENV}:").set(key=project_code, value=dmp_env_sign, time=604800)


def is_local_storage(project_code=None):
    if not project_code and hasattr(g, "code"):
        project_code = g.code
    if not project_code:
        raise UserError(message="参数 project_code 为None")
    if (
        getattr(g, "datasource_type", "") == DataSourceType.MysoftNewERP.value
    ) or (
        get_storage_type(project_code) == DatasetStorageType.DatasetStorageOfLocal.value and
        getattr(g, "dataset_type", "") in [DatasetType.Excel.value, DatasetType.Union.value]
    ) or (
        getattr(g, "running_way", "") == "local"
    ):
        if getattr(g, "running_way", "") == "cloud":
            return False
        return True
    return False


def compatible_local_type(func):
    def _wrapper(self, *args, **kwargs):
        if is_local_storage(g.code):
            result = getattr(self, "{}_local".format(func.__name__))(*args, **kwargs)
        else:
            result = func(self, *args, **kwargs)
        return result
    return _wrapper


def compatible_local_type_no_self(func):
    def _wrapper(*args, **kwargs):
        if is_local_storage(g.code):
            mod = sys.modules[func.__module__]
            result = getattr(mod, "{}_local".format(func.__name__))(*args, **kwargs)
        else:
            result = func(*args, **kwargs)
        return result
    return _wrapper


def get_setting(item, category):
    from dmplib.saas.project import get_db
    with get_db() as db:
        return db.query_scalar(
            "select `value` from system_setting where `category`=%(category)s and `item`=%(item)s",
            {
                "category": category,
                "item": item
            }
        )


def get_project_setting(key, default_value):
    project_code = getattr(g, "code")
    from dmplib.db.mysql_wrapper import get_db
    with get_db() as db:
        val = db.query_scalar('select `value` from `project_setting` where `code`=%(code)s and `key`=%(key)s', {
            'code': project_code,
            'key': key
        })
        if not val:
            return default_value
        return val


def save_project_setting(project, key, value, account):
    from dmplib.db.mysql_wrapper import get_db
    with get_db() as db:
        sql_args = {
            'code': project,
            'key': key,
            'value': value,
            'account': account,
            'time': datetime.datetime.now()
        }
        rows = db.exec_sql('''update `project_setting` 
            set `value`=%(value)s,`modified_by`=%(account)s,`modified_on`=%(time)s 
            where `code`=%(code)s and `key`=%(key)s''', sql_args)
        if rows == 0:
            sql_args['id'] = seq_id()
            db.exec_sql('''insert into `project_setting` (`id`, `code`, `key`, `value`, description,
             created_on, created_by, modified_on, modified_by) values (%(id)s,%(code)s,%(key)s,%(value)s,null,
             %(time)s,%(account)s,null,null)''', sql_args)


def is_history_dataset(project_code, erp_api_info_id=None, conn_str=None):  # NOSONAR
    if not erp_api_info_id and conn_str:
        if isinstance(conn_str, str):
            conn_str = json.loads(conn_str)
        elif isinstance(conn_str, BaseModel):
            conn_str = conn_str.get_dict()
        erp_api_info_id = conn_str.get('erp_api_info_id') or ''
    if erp_api_info_id:
        return repository.get_data_scalar("erp_api_info", {"id": erp_api_info_id}, col_name='local_dataset_clean_max_time_type') in [0, None]
    project = get_project_info(project_code)
    if not project:
        raise UserError(message='租户不存在')
    return project.get("local_dataset_clean_max_time_type") in [0, None]
