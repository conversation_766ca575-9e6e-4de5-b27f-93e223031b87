#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : snapshot_service.py
# @Author: guq
# @Date  : 2021/8/23
# @Desc  :
import json
import traceback
from datetime import datetime

from dmplib import config
from base import repository
from base.enums import SnapshotStatus, DatasetStorageType
from dmplib.utils.logs import logger
from dmplib.utils.strings import seq_id
from dmplib.hug import g
from dmplib.saas.project import get_db, get_data_db
from components.storage_setting import get_storage_type


class SnapshotServices:
    __instance = None

    def __init__(self):
        self.__services = dict()

    @staticmethod
    def get_instance():
        if SnapshotServices.__instance:
            return SnapshotServices.__instance
        SnapshotServices.__instance = SnapshotServices()
        return SnapshotServices.__instance

    @property
    def registered_names(self):
        return list(self.__services.keys())

    @property
    def target_tables(self):
        return [t.target_table for t in self.__services.values()]

    @property
    def registered_services(self):
        return self.__services

    def register(self, name, service):
        self.__services[name] = service

    def has_registered(self, name):
        return name in self.__services

    def get(self, name, default=None):
        return self.__services.get(name, default)


SNAPSHOT_SERVICES = SnapshotServices.get_instance()


def register_snapshot_service(cls):
    if not issubclass(cls, SnapShotBase):
        raise RuntimeError('%s必须继承于%s' % (str(cls), 'SnapShotBase'))

    if not hasattr(cls, 'name') or cls.name == 'base':
        raise RuntimeError('%s必须指定%s' % (str(cls), 'name'))

    if SNAPSHOT_SERVICES.has_registered(cls.__name__):
        raise RuntimeError('%s已经被注册于%s' % (cls.__name__, str(SNAPSHOT_SERVICES.get(cls.__name__))))

    SNAPSHOT_SERVICES.register(cls.__name__, cls)

    return cls


class SnapShotBase:
    name = 'base'

    def __init__(self, snapshot_id, **kwargs):
        self.snapshot_id = snapshot_id
        self.skip_logging = False
        self.kwargs = kwargs
        self._inject_property(kwargs)

    def _inject_property(self, kwargs: dict):
        for k, v in kwargs.items():
            setattr(self, k, v)

    def pre_action(self):
        pass

    def logging(self, content, level='info'):
        getattr(logger, level.lower())('[%s|%s] %s' % (self.snapshot_id, self.kwargs, content))

    @staticmethod
    def _dumps(data):
        return json.dumps(data, ensure_ascii=False, separators=(',', ':'))

    def combine_data(self):
        raise NotImplementedError

    def do_snapshot(self, *args, **kwargs):
        raise NotImplementedError


SNAPSHOT_TABLE = 'snapshot'


class Snapshot(object):
    __slots__ = ['params', 'snap_id', 'snap_type', 'mode_map', 'code', 'dataset_ids', 'dashboard_ids', 'application_ids', 'data_map']

    def __init__(self, code='', snap_type='', params=None):
        self.dataset_ids = params and params.get("dataset_ids") or []
        self.dashboard_ids = params and params.get("dashboard_ids") or []
        self.application_ids = params and params.get("application_ids") or []
        self.mode_map = {"dashboard": "dashboard_id", "dataset": "dataset_id", "application": "application_id"}
        self.data_map = {'dashboard_id': self.dashboard_ids, 'dataset_id': self.dataset_ids, 'application_id': self.application_ids}
        self.code = code
        self.snap_type = snap_type
        self.snap_id = params.get("snap_id")

    @staticmethod
    def generate_snap_record(code, snap_type="", params={}, snap_id=None):
        """
        生成snapshot记录
        :return:
        """
        with get_db(code=code) as db:
            snap_id = seq_id() if snap_id is None else snap_id
            db.insert(SNAPSHOT_TABLE, data={
                "snap_id": snap_id,
                "snap_type": snap_type,
                "param": json.dumps(params),
                'created_on': datetime.now(),
                "created_by": code,
                'modified_on': datetime.now(),
                "modified_by": code
            })
            return snap_id

    def __record_snap_status(self, status, snap_type, message):
        """
        记录拍照状态
        :param status:
        :return:
        """
        with get_db(code=self.code) as db:
            db.update(SNAPSHOT_TABLE, {'status': status, 'type': snap_type, 'message': message},
                      {"snap_id": self.snap_id})

    @staticmethod
    def __check_mode(mode):
        if mode not in ['dashboard', 'dataset', 'application']:
            raise RuntimeError('model错误')

    def __snapshot(self, mode, ids, snap_type_list):
        """
        单个业务拍照
        :param mode:
        :param ids:
        :return:
        """
        if self.snap_id is None:
            return RuntimeError('请先创建快照记录')
        self.__check_mode(mode)
        if not ids:
            return
        snap_type_list.append(mode)
        if not isinstance(ids, list):
            ids = list(ids)
        for _id in ids:
            executor = SnapShotExecutor(snapshot_id=self.snap_id, mode=mode, **{self.mode_map.get(mode): _id})
            executor.execute_all()

    def do(self):  # NOSONAR
        status = SnapshotStatus.INIT.value
        snap_type_list = []
        message = ""
        with get_db() as db:
            try:
                db.begin_transaction()
                # 数据集拍照, 如果没有传dataset_ids则不执行拍照
                self.__snapshot(mode="dataset", ids=self.dataset_ids, snap_type_list=snap_type_list)
                # 数据报告拍照, 如果没有传dashboard_ids则不执行拍照
                self.__snapshot(mode="dashboard", ids=self.dashboard_ids, snap_type_list=snap_type_list)
                status = SnapshotStatus.SUCCESS.value
                db.commit()
            except Exception as e:
                logger.error(traceback.format_exc())
                status = SnapshotStatus.FAIL.value
                db.rollback()
                message = str(e)
                raise RuntimeError(f"数据拍照失败，错误原因: {e}") from e
            finally:
                # 记录拍照状态记录
                self.__record_snap_status(status, ",".join(snap_type_list), message)


class SnapShotExecutor:
    def __init__(self, snapshot_id='', mode='dashboard', **kwargs):
        self._snapshot_services = SNAPSHOT_SERVICES
        self._executors = []
        self.snapshot_id = snapshot_id
        self.has_recorded = False
        self.kwargs = kwargs
        self.mode = mode
        self.check_mode(mode)

    def check_mode(self, mode):
        if mode not in ['dashboard', 'dataset', 'application']:
            raise RuntimeError('model错误')

    def logging(self, content, level='info'):
        getattr(logger, level.lower())('[%s|%s] %s' % (self.snapshot_id, self.kwargs, content))

    def process_check(self):
        # 必要性校验
        if self.mode == 'dashboard':
            dashboard_id = self.kwargs.get('dashboard_id')
            data = repository.get_one(
                'dashboard_released_snapshot_dashboard',
                conditions={'id': dashboard_id}, fields=['id']
            )
            if not data:
                raise RuntimeError('该报告[%s]未发布过！' % dashboard_id)
        elif self.mode == 'application':
            application_id = self.kwargs.get('application_id')
            data = repository.get_one(
                'application', conditions={'id': application_id, 'enable': 1}, fields=['id']
            )
            if not data:
                raise RuntimeError('门户状态为已发布的才能拍照！' % application_id)

    def use(self, service_names: list):
        # 指定需要执行的任务
        if not isinstance(service_names, list):
            raise RuntimeError('service_names必须是list, eg: [dashboard_metadata, ]')

        for service_name in service_names:
            if not self._snapshot_services.has_registered(service_name):
                raise RuntimeError('该服务[%s]未被注册过！' % service_name)

            if str(service_name).lower().startswith(self.mode):
                # raise RuntimeError('必须使用同一类拍照服务[%s]-%s！' % (service_name, self.mode))
                self._executors.append(self._snapshot_services.get(service_name))

        return self

    def use_mode_service(self, mode_list: list):
        for service_name in self._snapshot_services.registered_names:
            for mode in mode_list:
                if str(service_name).lower().startswith(mode):
                    self._executors.append(self._snapshot_services.get(service_name))
        return self

    def execute(self):
        cur = SnapShotBase
        try:
            for cls in self._executors:
                cur = cls
                # 第一次需要记录拍照记录信息
                if not self.has_recorded:
                    self.process_check()
                    self.has_recorded = True

                snapshot = cls(self.snapshot_id, **self.kwargs)  # type: SnapShotBase
                # 添加拍照前的预处理
                snapshot.pre_action()
                # 执行拍照逻辑
                snapshot.do_snapshot(snapshot.combine_data())
                if not snapshot.skip_logging:
                    self.logging('%s 拍照完成！' % (cls.name,))
            return True
        except:
            self.logging('%s 拍照失败！原因: %s' % (cur.name, traceback.format_exc()), level='error')
            raise RuntimeError('%s %s 拍照失败！' % (self.kwargs, cur.name))

    def execute_all(self):
        # 执行所有注册的服务
        return self.use(self._snapshot_services.registered_names).execute()


# 删除快照表数据
# 删除条件为指定版本的dashboard或者dataset数据
class DropSnapShotTables:
    def __init__(self, dashboard_ids: list = [], dataset_ids: list = [], snap_ids=[]):  # NOSONAR
        self.dashboard_ids = dashboard_ids
        self.snap_ids = snap_ids
        self.dataset_ids = dataset_ids
        self._services = SNAPSHOT_SERVICES.registered_services.values()

    @property
    def dashboard_models(self):
        return [i for i in self._services if i.target_table.startswith('snapshot_dashboard')]

    @property
    def dataset_models(self):
        return [i for i in self._services if i.target_table.startswith('snapshot_dataset')]

    def _get_model_condition_key(self, model):
        return list(model.filter_conditions.keys())[0]

    def drop_snapshot_dashboard_data(self):
        for dashboard_id in self.dashboard_ids:
            for snap_id in self.snap_ids:
                for model in self.dashboard_models:
                    repository.delete_data(
                        table_name=model.target_table,
                        condition={self._get_model_condition_key(model): dashboard_id, 'snap_id': snap_id},
                        commit=True
                    )
        repository.delete_data("dashboard_snapshot_record", {"snap_id": self.snap_ids}, commit=True)
        return True

    def drop_snapshot_dataset_data(self):
        for dataset_id in self.dataset_ids:
            for snap_id in self.snap_ids:
                for model in self.dataset_models:
                    repository.delete_data(
                        table_name=model.target_table,
                        condition={self._get_model_condition_key(model): dataset_id, 'snap_id': snap_id},
                        commit=True
                    )
        # 删除数据集版本相关数据
        self.drop_snapshot_dataset_version_data()
        return True

    def drop_snapshot_dataset_version_data(self):
        """
        dataset_snapshot_relation, dataset_version, 数据集
        """
        if not self.dataset_ids:
            return
        version_ids = None
        for snap_id in self.snap_ids:
            versions = repository.get_list(
                "dataset_snapshot_relation", {"snap_id": snap_id}, fields=["dataset_version_id"])
            if not versions:
                continue
            version_ids = [item.get("dataset_version_id") for item in versions]
            version_tables = repository.get_list(
                "dataset_version", {"id": version_ids}, fields=["table_name"]
            ) or []
            if get_storage_type(g.code) == DatasetStorageType.DatasetStorageOfCloud.value:
                with get_data_db() as db:
                    for item in version_tables:
                        db.exec_sql(f"DROP TABLE  IF EXISTS {item.get('table_name')}")

        repository.delete_data("dataset_snapshot_relation", {"snap_id": self.snap_ids}, commit=True)
        if version_ids:
            repository.delete_data("dataset_version", {"id": version_ids}, commit=True)

    def update_snapshot(self):
        repository.update("snapshot", {"status": SnapshotStatus.DELETED.value}, {"snap_id": self.snap_ids})
        return True

    def clear_cache(self):
        from .versioned_query import clear_cached_snapshot_status
        clear_cached_snapshot_status()

    def do(self):  # NOSONAR
        r1 = self.drop_snapshot_dashboard_data()
        r2 = self.drop_snapshot_dataset_data()
        r3 = self.update_snapshot()
        self.clear_cache()
        return r1 and r2 and r3


# 单表拍照基类
# 如果是单表的数据的拍照可以直接继这个基类，只需要指定表名条件即可，要求是两张表的字段一致
class SnapShotSingleTableBase(SnapShotBase):
    name = 'base'
    target_table = 'base'
    exclude_fields = ['increment_id', ]  # 目标表需要过滤的一些字段
    filter_conditions = {}

    def generate_fields(self):
        sql = 'show columns from %s' % self.target_table
        columns_data = repository.get_data_by_sql(sql=sql, params=())  # noqa
        columns = [i['Field'] for i in columns_data]
        return [i for i in columns if i not in self.exclude_fields]

    def __transform_conditions(self):
        if not self.filter_conditions:
            raise RuntimeError('%s 请先指定查询条件！' % self.name)
        # 转换self*特殊标记为self.
        wrapper = lambda x: getattr(self, x.split('self*')[-1]) if isinstance(x, str) and x.startswith('self*') else x
        return {
            k: wrapper(v)
            for k, v in self.filter_conditions.items()
        }

    def combine_data(self) -> list:
        # 单表关联的数据
        conditions = self.__transform_conditions()
        data = repository.get_list(self.name, conditions=conditions, fields='*')  # noqa
        return data

    def do_snapshot(self, data):
        if not data:
            self.logging('%s 查询到的表数据为空！跳过备份' % self.name, level='error')
            self.skip_logging = True
            return
        target_fields = self.generate_fields()
        target_id_name = 'snap_id'
        for item in data:
            # 插入单表数据
            item[target_id_name] = self.snapshot_id
            # 过滤不对应的数据
            item = {k: v for k, v in item.items() if k in target_fields}
            repository.add_data(table_name=self.target_table, data=item, commit=False)


def start_snapshot(code, snap_id=None, dashboard_ids=None, dataset_ids=None) -> str:
    """
    执行拍照
    :param code:
    :param snap_id:
    :param dashboard_ids:
    :param dataset_ids:
    :return:
    """
    # 添加拍照记录表
    snap_id = Snapshot.generate_snap_record(code, snap_id)
    # 异步调用拍照服务
    from app_celery import snapshot_service
    snapshot_service.apply_async(kwargs={
        "project_code": code, "snap_id": snap_id, "dashboard_ids": dashboard_ids, "dataset_ids": dataset_ids
    }, queue='celery-slow')
    return snap_id


class ApplicationSnapshot(object):
    __slots__ = ['snap_id', 'snap_type', 'application_id', 'data_map', 'mode_list']

    def __init__(self, application_id, snap_id, code):
        self.application_id = application_id
        g.code = code
        self.snap_type = '门户'
        self.snap_id = snap_id or seq_id()
        self.mode_list = ['application']
        self.init_data(application_id)

    def _get_application_dashboard_and_dataset_ids(self, application_id):
        from dashboard_chart.services.released_dashboard_service import get_all_dashboard_and_dataset_of_need_snapshot
        #  获取门户直接挂接的报告
        application_dashboard_url = repository.get_column('release_application', {'url !=': '', 'report_type': [0, 2], 'id': application_id}, ['url']) or []
        # 获取门户下菜单挂接的报告ID
        func_url_list = repository.get_column('release_function', {'url !=': '', 'report_type': [0, 2, 6], 'application_id': application_id}, ['url']) or []
        url_list = application_dashboard_url + func_url_list
        dashboard_ids = []
        dataset_ids = []
        for url in url_list:
            if not str(url).startswith('http') and len(str(url)) == 36:
                dashboard_ids.append(url)
        if dashboard_ids:
            dashboard_ids, dataset_ids = get_all_dashboard_and_dataset_of_need_snapshot(
                dashboard_ids, [], dashboard_ids
            )
        if dataset_ids:
            self.mode_list.append('dataset')
        if dashboard_ids:
            self.mode_list.append('dashboard')
        param = repository.get_data_scalar('snapshot', {'snap_id': self.snap_id}, 'param') or ''
        self.data_map = {'dashboard_id': dashboard_ids, 'dataset_id': dataset_ids, 'application_id': self.application_id, 'filter_config': param}
        logger.info('需要拍照的相关数据：{}'.format(json.dumps(self.data_map)))

    def init_data(self, application_id):
        self._get_application_dashboard_and_dataset_ids(application_id)

    def generate_snap_record(self, title, snap_id):
        """
        生成snapshot记录
        :return:
        """
        with get_db() as db:
            data = {
                "snap_id": snap_id or seq_id(),
                "title": title,
                "type": 'application,dashboard,dataset',
                "snap_type": '门户',
                "param": json.dumps(self.data_map),
                'created_on': datetime.now(),
                'modified_on': datetime.now(),
                'service_id': self.application_id,
            }
            db.insert(SNAPSHOT_TABLE, data)
            return {"snap_id": self.snap_id, "title": title, 'application_id': self.application_id}

    def record_snap_status(self, status, message=''):
        """
        记录拍照状态
        :param status:
        :param message:
        :return:
        """
        with get_db() as db:
            db.update(SNAPSHOT_TABLE, {'status': status, 'type': 'dashboard,dataset,application', 'message': message},
                      {"snap_id": self.snap_id})

    def snapshot(self):
        message = ""
        repository.update_data('snapshot', {'status': SnapshotStatus.RUNNING.value, 'param': json.dumps(self.data_map)}, {'snap_id': self.snap_id})
        logger.info('开始门户拍照')
        with get_db() as db:
            try:
                db.begin_transaction()
                executor = SnapShotExecutor(snapshot_id=self.snap_id, mode='application', **self.data_map)
                executor.use_mode_service(self.mode_list).execute()
                status = SnapshotStatus.SUCCESS.value
                db.commit()
            except Exception as e:
                logger.error(traceback.format_exc())
                status = SnapshotStatus.FAIL.value
                db.rollback()
                message = str(e)
                raise RuntimeError(f"数据拍照失败，错误原因: {e}") from e
            finally:
                self.record_snap_status(status, message)

    @staticmethod
    def delete_application_snapshot(snap_id: list):
        mode_list = ['application', 'dataset', 'dashboard']
        service_dict = SNAPSHOT_SERVICES.registered_services
        for key, service in service_dict.items():
            for mode in mode_list:
                if str(key).lower().startswith(mode):
                    repository.delete_data(service.target_table, {'snap_id': snap_id}, commit=False)
        repository.delete_data(SNAPSHOT_TABLE, {'snap_id': snap_id}, commit=True)
        drop_table = DropSnapShotTables(dataset_ids=snap_id, snap_ids=snap_id)
        drop_table.drop_snapshot_dataset_version_data()
        drop_table.clear_cache()

    def deal_snapshot_version(self):
        """
        拍照版本数量控制
        """
        records = repository.get_list(
            "snapshot",
            {"service_id": self.application_id, "snap_type": "门户", 'status': 1},
            fields=["snap_id"],
            order_by="created_on desc"
        )
        max_version_num = int(config.get("Function.dashboard_max_version_num", 24))
        if len(records) > max_version_num:
            snap_ids = [item.get("snap_id") for item in records[max_version_num:]]
            # 删除拍照数据
            self.delete_application_snapshot(snap_ids)
