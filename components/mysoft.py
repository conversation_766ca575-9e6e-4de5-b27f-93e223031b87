#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/27.
"""
import base64
import hashlib
import hmac
import requests
from urllib3 import Retry
import logging
import json
from datetime import datetime
import traceback

from urllib.parse import urlparse
from requests.adapters import HTTPAdapter

import times
from requests.exceptions import RequestException, Timeout

from dmplib import config
from dmplib.utils.errors import UserError, DatasetQueryError
from base.enums import QueryDataError

from typing import Dict, Union, Optional
from components import deshelper
from dmplib.redis import conn as conn_redis
from dmplib.hug import g
import time
import os

logger = logging.getLogger(__name__)


class CloudAPI:
    __slots__ = ['host', 'host_without_path', 'access_id', 'access_secret', 'app_name', 'api_timeout', 'db_str']

    ACCESS_TOKEN_PATH = 'platform/api/sys/GetAccessToken.ashx'

    DATA_CENTER_PATH = 'api/MobileReports.DataCenterRequest.asmx'

    def __init__(self, host: str, access_id: str, access_secret: str, app_name: str, db_str: dict = None) -> None:
        """
        :param str host:接口地址
        :param str access_id:
        :param str access_secret:
        :param str app_name:接口管家应用名称，如：mybigdata,myfuwu
        """
        _url = urlparse(host)
        # 数据服务中心接口api访问，需要加上路径
        self.host = _url.scheme + '://' + _url.netloc + _url.path.rstrip('/')  # ip+端口+路径，请求数据服务中心数据时使用
        self.host_without_path = _url.scheme + '://' + _url.netloc  # ip+端口，仅提供给get_access_token使用
        self.access_id = access_id
        self.access_secret = access_secret
        self.app_name = app_name.strip() if app_name else ''
        self.api_timeout = int(config.get('External.api_timeout', 120))
        self.db_str = db_str

    def _get_auth_center_url(self):
        token_path = 'api/sys/GetAccessToken.ashx'
        url = urlparse(self.host)
        route = url.path.lstrip('/').split("/")
        route[0] = "platform"
        route.append(token_path)
        return "/".join(route)

    @staticmethod
    def __retry_session(max_retry: int = 3):
        retry = Retry(
            total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1,
            status_forcelist=(500, 503, 504)
        )
        adapter = HTTPAdapter(max_retries=retry)
        session = requests.session()
        session.mount('https://', adapter)
        session.mount('http://', adapter)
        return session

    def _get_access_token(self):
        """
        获取AccessToken
        返回数据格式：data={'access_token': 'DdMyl/NVeOZilVgdLNZAVw==', 'expires_in': 3600}
        :return:
        """
        # 开发环境不用获取accesstoken
        if config.get('App.runtime') == 'dev':
            return ""

        cache_key = self._access_token_cache_key()
        if cache_key:
            cache = conn_redis()
            access_token = cache.get(cache_key)
            if access_token:
                if isinstance(access_token, bytes):
                    return access_token.decode()
                return access_token

        # 获取token，host不需要附带路径
        host = self.host_without_path
        url = '%s/%s?appid=%s&timestamp=%s' % (host, self._get_auth_center_url(), self.access_id, self.get_timestamp())
        try:
            headers = {
                "Content-Type": 'application/json',
                "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.124 Safari/537.36 Edg/102.0.1245.44'
            }
            # response = requests.get(url, headers=headers, timeout=10)
            session = self.__retry_session()
            response = session.get(url, headers=headers, timeout=10, verify=False)

            if response.status_code != 200:
                logging.info({'url': url, 'error': response.reason})
                return None
            if response.text:
                access_token = response.json().get('access_token')
                # 查询后设置缓存
                if cache_key:
                    cache = conn_redis()
                    expires_in = response.json().get('expires_in')
                    cache.set(cache_key, access_token, int(expires_in/2))
                return access_token
            else:
                raise UserError(message='接口返回数据为空')
        except Exception as e:
            raise UserError(message='获取AccessToken失败:' + str(e))

    def _access_token_cache_key(self):
        code = getattr(g, 'code', None)
        if code:
            return f'Erp:Access:Token:{code}:{self.host_without_path}'
        return None

    def _get_signature(self):
        """
        根据AccessToken与Timestamp进行签名
        :return:
        """
        base_str = '%s#%s' % (self._get_access_token(), self.get_timestamp())
        h = hmac.new(str.encode(self.access_secret), str.encode(base_str), hashlib.sha1)
        core = str.encode('%s.%s' % (h.hexdigest(), base_str))
        return bytes.decode(base64.b64encode(core)).strip().replace('+', '-').replace('/', '_').replace('=', '')

    def ping(self):
        """
        接口是否正常
        :return:
        """
        data = self._api_request({'api': 'api.ping'})
        if data and isinstance(data, dict) and data.get('data'):
            return True
        return False

    def get_tables(self, keyword: None = None, page: int = 1, page_size: int = 30, table_name_prefix: None = None):
        """
        获取ERP数据表
        :param keyword:
        :param page:
        :param page_size:
        :param table_name_prefix:
        :return:
        """
        params = {'api': 'api.table.list', 'page': page, 'page_size': page_size}
        if keyword:
            params['keyword'] = keyword
        if table_name_prefix:
            params['table_name_prefix'] = table_name_prefix
        return self._api_request(params)

    def get_table_columns(self, table_name):
        """
        根据表名获取表结构
        :param table_name:
        :return:
        """
        params = {'api': 'api.column.list'}
        if table_name:
            params['table_name'] = table_name
        return self._api_request(params)

    def get_environment_id(self):
        """
        获取环境标识
        :param table_name:
        :return:
        """
        params = {'api': 'api.getenvironment'}
        return self._api_request(params)

    def get_data_list(self, table_name, col_name, max_count, timestamp_col_name, last_timestamp):
        """
        获取表数据
        :param str table_name:
        :param str col_name:列名之间以英文逗号分隔
        :param int max_count:
        :param str timestamp_col_name:
        :param str last_timestamp:
        :return:
        """
        params = {'api': 'api.data.list'}
        if table_name:
            params['table_name'] = table_name
        if col_name:
            params['col_name'] = col_name
        if max_count:
            params['max_count'] = max_count
        if timestamp_col_name:
            params['timestamp_col_name'] = timestamp_col_name
        if last_timestamp:
            params['last_timestamp'] = last_timestamp
        return self._api_request(params)

    def get_sql_list(self, sql, is_download='0', queue_name=''):
        """
        根据sql语句获取数据
        :param str sql:
        :param int is_download:
        :param str queue_name:
        :return:
        """
        params = {'api': 'api.sql.list', 'sql': sql, 'is_download': is_download, 'queue_name': queue_name}

        return self._api_request(params)

    def _api_request(self, parameters: Optional[Dict[str, Union[any]]] = None):
        """
        请求API
        :param parameters:
        :return:
        """
        url = '%s/%s?appid=%s&signature=%s' % (self.host, self.app_name, self.access_id, self._get_signature())
        if self.db_str:
            parameters['db_str'] = self.db_str
        try:
            request_start = time.time()
            # response = requests.post(url, parameters, timeout=self.api_timeout or 30)
            session = self.__retry_session()
            response = session.post(url, parameters, timeout=self.api_timeout or 30, verify=False)
            response.encoding = 'utf-8'
            if response.status_code != 200:
                raise UserError(message=' 状态：' + str(response.status_code) + ' , ' + response.reason)
            result = response.json()
            upload_data_center_log_to_aliyun(url, parameters, True, result, request_start)
            return result
        except RequestException as e:
            msg = '连接失败:' + str(e)
            upload_data_center_log_to_aliyun(url, parameters, False, msg, request_start)
            raise UserError(message=msg)
        except Exception as be:
            msg = '连接失败:' + str(be)
            upload_data_center_log_to_aliyun(url, parameters, False, msg, request_start)
            raise UserError(message=msg)

    @staticmethod
    def get_timestamp() -> str:
        return datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')

    def datacenter_request(self, params, request_type: str = 'get'):
        date_center_path = self.DATA_CENTER_PATH
        self.app_name = date_center_path
        if request_type.lower() == 'get':
            encode_data = base64.b64encode(json.dumps(params).encode('utf-8'))
            data = {
                'action_name': params.get('action_name'),
                'encode_data': str(encode_data, 'utf-8'),
                'IsDataCenter': params.get('IsDataCenter', False)
            }
            return self._get_datacenter_request(data)
        else:
            # des 对称加密
            encode_data = json.dumps(params)
            encode_data = deshelper.encrypt(encode_data)
            data = {
                'action_name': params.get('action_name'),
                'encode_data': encode_data,
                'encode_type': 2,
                'IsDataCenter': params.get('IsDataCenter', False)
            }
            post_data = {"data": data}
            return self._post_datacenter_request(post_data)

    def _get_datacenter_request(self, params):
        url = '%s/%s?appid=%s&signature=%s&action_name=%s&encode_data=%s' % (
            self.host, self.app_name, self.access_id,
            self._get_signature(), params.get('action_name'), params.get('encode_data')
        )
        request_start = time.time()
        try:
            # 部分接口管家有对user-agent的限制，requests默认user-agent会返回403
            headers = {
                "Content-Type": 'application/json',
                "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.124 Safari/537.36 Edg/102.0.1245.44'
            }
            # response = requests.get(url, headers=headers, timeout=self.api_timeout or 10)
            session = self.__retry_session()
            response = session.get(url, headers=headers, timeout=self.api_timeout or 10, verify=False)
            # 解决中文乱码问题
            response.encoding = 'utf-8'
            if response.status_code != 200:
                raise UserError(message=' 状态：' + str(response.status_code) + ' , ' + response.reason)
            try:
                receive_data = json.loads(response.text)
            except Exception as e:
                raise UserError(
                    message=f'json loads error: {str(e)}, response.status: {response.status_code}, response.text: {response.text}'
                )
            if receive_data['errmsg'] is not None:
                err_code_str = ''
                if 'errcode' in receive_data:
                    err_code_str = ',错误代码：' + receive_data['errcode']
                raise UserError(message='错误信息：' + receive_data['errmsg'] + err_code_str)
            if 'data' not in receive_data or not isinstance(receive_data.get('data'), (list, dict)):
                raise UserError(message='数据服务中心异常，{}'.format(response.text))
            if "errmsg" in receive_data['data']:
                raise UserError(message='数据服务中心异常，{}'.format(receive_data.get("data", {}).get("errmsg")))
            if ('Success' in receive_data['data']['0']) and not receive_data['data']['0']['Success']:
                raise UserError(message='错误信息：' + receive_data['data']['0']['Message'])
            data = receive_data['data']['0']['Data']
            upload_data_center_log_to_aliyun(url, data, True, receive_data, request_start)
            return data
        except RequestException as e:
            msg = f'连接失败: {str(e)}, traceback: \n {traceback.format_exc()}'
            upload_data_center_log_to_aliyun(url, params, False, msg, request_start)
            raise UserError(message=msg)
        except UserError as e:
            msg = '响应异常：' + str(e.message)
            upload_data_center_log_to_aliyun(url, params, False, msg, request_start)
            raise UserError(message=msg)
        except Exception as be:
            msg = '未知异常:' + str(be)
            upload_data_center_log_to_aliyun(url, params, False, msg, request_start)
            raise UserError(message=msg)

    def _post_datacenter_request(self, data):
        url = '%s/%s?appid=%s&signature=%s' % (
            self.host, self.app_name, self.access_id, self._get_signature()
        )
        request_start = time.time()
        try:
            # 部分接口管家有对user-agent的限制，requests默认user-agent会返回403
            headers = {
                "Content-Type": 'application/json',
                "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.124 Safari/537.36 Edg/102.0.1245.44'
            }
            # response = requests.post(url, json=data, headers=headers, timeout=self.api_timeout or 10)
            session = self.__retry_session()
            response = session.post(url, json=data, headers=headers, timeout=self.api_timeout or 10, verify=False)
            # 解决中文乱码问题
            response.encoding = 'utf-8'
            if response.status_code != 200:
                raise UserError(message=' 状态：' + str(response.status_code) + ' , ' + response.reason)
            try:
                receive_data = json.loads(response.text)
            except Exception as e:
                raise UserError(
                    message=f'json loads error: {str(e)}, response.status: {response.status_code}, response.text: {response.text}'
                )
            if receive_data.get('errmsg') is not None:
                err_code_str = ''
                if 'errcode' in receive_data:
                    err_code_str = ',错误代码：' + receive_data['errcode']
                raise UserError(message='错误信息：' + receive_data['errmsg'] + err_code_str)
            if 'data' not in receive_data or not isinstance(receive_data.get('data'), (list, dict)):
                raise UserError(message='错误的响应体: {}'.format(response.text))
            if "errmsg" in receive_data['data']:
                raise UserError(message=receive_data.get("data", {}).get("errmsg"))
            if ('Success' in receive_data['data']['0']) and not receive_data['data']['0']['Success']:
                raise UserError(message='错误信息：' + receive_data['data']['0']['Message'])
            result = receive_data['data']['0']['Data']
            upload_data_center_log_to_aliyun(url, data, True, receive_data, request_start)
            return result
        except Timeout as e:
            msg = f"接口超时：{e}"
            upload_data_center_log_to_aliyun(url, data, False, msg, request_start)
            raise DatasetQueryError(message=msg, error_code=QueryDataError.TimeoutError.value)
        except RequestException as e:
            msg = f'外部接口错误: {str(e)}, traceback: \n {traceback.format_exc()}'
            upload_data_center_log_to_aliyun(url, data, False, msg, request_start)
            raise DatasetQueryError(message=msg, error_code=QueryDataError.ExternalError.value)
        except UserError as e:
            msg = '数据服务中心异常：' + str(e.message)
            upload_data_center_log_to_aliyun(url, data, False, msg, request_start)
            raise DatasetQueryError(message=msg, error_code=QueryDataError.OtherError.value)
        except Exception as be:
            msg = '未知异常:' + str(be)
            upload_data_center_log_to_aliyun(url, data, False, msg, request_start)
            raise DatasetQueryError(message=msg, error_code=QueryDataError.AnyError.value)

    def post_erpapi_request(self, data):
        url = '%s/%s?appid=%s&signature=%s' % (
            self.host, self.app_name, self.access_id, self._get_signature()
        )
        request_start = time.time()
        try:
            # 部分接口管家有对user-agent的限制，requests默认user-agent会返回403
            headers = {
                "Content-Type": 'application/json',
                "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.124 Safari/537.36 Edg/102.0.1245.44'
            }
            # response = requests.post(url, json=data, headers=headers, timeout=self.api_timeout or 10)
            session = self.__retry_session()
            response = session.post(url, json=data, headers=headers, timeout=self.api_timeout or 10, verify=False)
            # 解决中文乱码问题
            response.encoding = 'utf-8'
            if response.status_code != 200:
                raise UserError(message=' 状态：' + str(response.status_code) + ' , ' + response.reason)
            try:
                receive_data = json.loads(response.text)
            except Exception as e:
                raise UserError(message='{}'.format(response.text))
            if receive_data.get('errmsg') is not None:
                err_code_str = ''
                if 'errcode' in receive_data:
                    err_code_str = ',错误代码：' + receive_data['errcode']
                raise UserError(message='错误信息：' + receive_data['errmsg'] + err_code_str)
            if 'data' not in receive_data or not isinstance(receive_data.get('data'), (list, dict)):
                raise UserError(message='{}'.format(response.text))
            if "errmsg" in receive_data['data']:
                raise UserError(message=receive_data.get("data", {}).get("errmsg"))
            result = receive_data['data'].get('data')
            upload_data_center_log_to_aliyun(url, data, True, receive_data, request_start)
            return result
        except RequestException as e:
            msg = '连接失败:' + str(e)
            upload_data_center_log_to_aliyun(url, data, False, msg, request_start)
            raise UserError(message=msg)
        except Exception as be:
            msg = '未知异常:' + str(be)
            upload_data_center_log_to_aliyun(url, data, False, msg, request_start)
            raise UserError(message=msg)


def upload_data_center_log_to_aliyun(url, params, is_success, result, start_time):
    try:
        end_time = time.time()
        duration_time = str(int((end_time - start_time) * 1000)) + "ms"
        start_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time))
        end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(end_time))
        if isinstance(params, dict):
            params = json.dumps(params)
        if isinstance(result, dict):
            result = json.dumps(result)

        log_data = {
            "action": "request_erp",
            "api_url": url,
            "start_time": start_time,
            "end_time": end_time,
            "duration": duration_time,
            "api_param": params,
            "is_success": "1" if is_success else "0",
            "api_result": result,
            "org_code": g.code if hasattr(g, 'code') else "",
            "account": g.account if hasattr(g, 'account') else "",
            "dashboard_id": getattr(g, 'dashboard_id_of_query', ''),
            "dashboard_name": getattr(g, 'log_dashboard').get('name','') if getattr(g, 'log_dashboard', '') else '',
            "dataset_id": getattr(g, 'dataset_id_of_query', ''),
            "trace_id": getattr(g, 'trace_id_of_query', ''),
            "sql_from": getattr(g, 'sql_from', 'viewreport') or 'viewreport'
        }
        # 默认开启记录数据服务中心请求日志，指定租户可以不开启
        is_record_log = int(config.get('Product.record_data_center_request_api_log', 1))
        is_yunqing = int(config.get('App.is_deploy_in_yunqing', 0))
        if is_record_log and is_yunqing:
            log_data["env_code"] = str(os.environ.get('CONFIG_AGENT_CLIENT_CODE'))
            log_data["app_name"] = config.get("App.name", "")
            from app_celery import upload_log_to_aliyun
            upload_log_to_aliyun.apply_async(
                kwargs={
                    "log_data": [tuple([k, v]) for k, v in log_data.items()]
                },
                queue='celery'
            )
        # 日志记录天眼
        from components.fast_logger import FastLogger
        FastLogger.ApiFastLogger(**log_data).record()
    except Exception as e:
        logger.error("记录API请求日志失败：" + str(e))


def _get_auth_center_url(host):
    TOKEN_PATH = 'api/sys/GetAccessToken.ashx'
    host = urlparse(host.rstrip('/'))
    url = urlparse(host.path)
    route = url.path.lstrip('/').split("/")
    route[0] = "platform"
    route.append(TOKEN_PATH)

    return "/".join(route)


import random


def query_dc(index):
    url = "https://jkgj-pre.mingyuanyun.com/cypt_ydbb/m_0IcmqgAbp4MG7A/api/MobileReports.DataCenterRequest.asmx?appid=Xj4dhT_8IgWob3pe&signature=ZWZiYmMxOTk4NzUxMzA1MTJmZjg3M2IyOWFjYmRhZTI4Zjk2ZjczYi5KSGV2SUdIamhEazVTR25TUklON0p3PT0jMjAyMi0wNC0wOVQxNzo1MDoyNFo"
    data0 = {"data": {"action_name": "DMPDataset/GetData",
                     "encode_data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
                     "encode_type": 2}}
    data1 = {"data": {"action_name": "DMPDataset/GetData", "encode_data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encode_type": 2}}
    try:
        # print(index)
        datas = [data0, data1]
        _ = requests.post(url, json=datas[index], timeout=10)
        print("suc")
    except RequestException as e:
        msg = 'req 连接失败:' + str(e)
        print(msg)
    except Exception as be:
        msg = '连接失败:' + str(be)
        print(msg)


def query_dc_new(url, data, result):
    try:
        start = time.time()
        res = requests.post(url, json=data, timeout=10).json()
        res = res.get("data")["0"]
        # print("duration:", int((time.time() - start)*1000), "接口管家:", res.get("EndTime") - res.get("FunctionStartTime"))
        result.append({
            "duration": int((time.time() - start)*1000),
            "mdc": res.get("EndTime") - res.get("FunctionStartTime")
        })
    except RequestException as e:
        msg = 'req 连接失败:' + str(e)
        print(msg)
    except Exception as be:
        msg = '连接失败:' + str(be)
        print(msg)


def multi():
    # from gevent import monkey; monkey.patch_socket()
    from gevent.pool import Pool
    import os
    file_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "aliyunlog.txt"))

    pool = Pool(500)

    print("start")

    result = []
    with open(file_path, 'r') as f:
        datas = json.loads(f.read()).get("data").get("logs")
        start = time.time()
        for item in datas:
            pool.spawn(
                query_dc_new,
                item.get("api_url"),
                json.loads(item.get("api_param")),
                result
            )
    pool.join()
    print("end: ", int((time.time() - start)*1000))
    print("total: ", sum([i.get('duration') for i in result]))
    return result


if __name__ == "__main__":
    headers = {
        "Content-Type": 'application/json',
        "User-Agent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.124 Safari/537.36 Edg/102.0.1245.44'
    }
    data = {"data": {"action_name": "DMPDataset/ExecuteDatasetClean", "encode_data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encode_type": 2}}

    res = requests.post(
        url='https://qjpct.fdcyun.com:9000/ydbb/api/MobileReports.DataCenterRequest.asmx?appid=AGXaUeW_M5KcDxxf&signature=N2NlN2NjMDZhZTBjZDMxN2RkM2UzMjE1M2JjNGJlYTljNjIyNjAyNy4zeW1hQ3IwUVNMeXl4amk0dC8wNG1nPT0jMjAyMi0wNy0yMVQxODo0MDoyM1o',
        headers=headers,
        json=data
    )
    print(res.json())
