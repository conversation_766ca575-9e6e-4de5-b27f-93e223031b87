import base64
import binas<PERSON><PERSON>
import os

from Crypto.Cipher import AES
from cryptography.hazmat.primitives.ciphers.aead import AESGCM


class PasswordV3Utils:
    V3_AES_KEY = "Mysoft95938@2022"
    AES_ALGORITHM_GCM = "AES-GCM"
    GCM_NONCE_LENGTH = 12
    GCM_TAG_LENGTH = 16

    @staticmethod
    def encryptGCM(data, key):
        iv = PasswordV3Utils.generateRandomNonce()
        cipher = AESGCM(key)
        encryptedBytes = cipher.encrypt(iv, data.encode(), None)
        combinedBytes = iv + encryptedBytes
        encodedBytes = base64.b64encode(combinedBytes)
        return encodedBytes.decode()

    @staticmethod
    def decryptGCM(encryptedData, key):
        combinedBytes = base64.b64decode(encryptedData)
        iv = combinedBytes[:PasswordV3Utils.GCM_NONCE_LENGTH]
        encryptedBytes = combinedBytes[PasswordV3Utils.GCM_NONCE_LENGTH:]
        cipher = AESGCM(key)
        decryptedBytes = cipher.decrypt(iv, encryptedBytes, None)
        return decryptedBytes.decode()

    @staticmethod
    def generateRandomNonce():
        return os.urandom(PasswordV3Utils.GCM_NONCE_LENGTH)


class PasswordV2Utils:
    ALGORITHM = "AES"
    KEY_AES = "AES"
    V1_AES_KEY = "mysoft1234567890"
    V2_AES_KEY = "Mysoft95938@2022"

    @staticmethod
    def encrypt(src, key):
        key_bytes = key.encode()
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        padded_data = PasswordV2Utils.pad_data(src)
        encrypted_bytes = cipher.encrypt(padded_data)
        encrypted_hex = binascii.hexlify(encrypted_bytes).decode()
        return encrypted_hex

    @staticmethod
    def decrypt(src, key):
        key_bytes = key.encode()
        cipher = AES.new(key_bytes, AES.MODE_ECB)
        encrypted_bytes = binascii.unhexlify(src)
        decrypted_bytes = cipher.decrypt(encrypted_bytes)
        decrypted_data = decrypted_bytes.decode().rstrip('\x00')
        decrypted_data = decrypted_data.replace('\x04', '')
        return decrypted_data

    @staticmethod
    def pad_data(data):
        block_size = 16
        padding_size = block_size - (len(data) % block_size)
        padding = bytes([padding_size]) * padding_size
        return data.encode() + padding

    @staticmethod
    def unpad_data(padded_data):
        padding_size = padded_data[-1]
        return padded_data[:-padding_size].decode()


if __name__ == '__main__':
    pwd = '69A9D84154AADB399690C2EF2C376075'
    s = None
    try:
        s = PasswordV3Utils.decryptGCM(pwd, PasswordV3Utils.V3_AES_KEY.encode())
        print(s)
    except Exception as e:
        print(e)

    try:
        s = PasswordV2Utils.decrypt(pwd, PasswordV2Utils.V2_AES_KEY)
        print(s)
    except Exception as e:
        print(e)

    try:
        s = PasswordV2Utils.decrypt(pwd, PasswordV2Utils.V1_AES_KEY)
        print(s)
    except Exception as e:
        print(e)
