#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2018/1/26.
"""
import json
import os
import re
import traceback
from datetime import datetime
import jwt

import pybase64
import requests
from requests.exceptions import Timeout

from base.enums import ApiParamType, ApiParamSysValue
from base import repository
from components.redis_utils import stale_cache
from components.utils import get_default_account
from dmplib import config
from dmplib.hug import g
from dmplib.utils.crypt import AESCrypt
from components.crypt import AESCrypt as AESCrypt_new
from components.url import url_add_param
from dmplib.utils.errors import UserError
from typing import Any, Dict, List, Optional
from dmplib.constants import ADMINISTRATORS_ID
import logging
from integrate.external_service import get_user_by_third_party_id_and_user_info
from components.common_external_api_query_deal import deal_external_api_biz_params
from components.analysis_time import AnalysisTimeUtils

logger = logging.getLogger(__name__)


class ExternalAPI:
    """
    第三方业务系统接口类
    """

    __slots__ = ['host', 'access_secret', 'cookie', 'tenant_code', 'third_party_id']

    def __init__(self, host: str, access_secret: str, cookie: Dict[str, str],
                 tenant_code: str = "", third_party_id: str = "") -> None:
        """
        :param str host:接口地址
        :param str access_secret:
        :param str cookie:
        """
        self.host = host
        self.access_secret = access_secret
        self.cookie = cookie
        # 【【路劲】API数据源支持指定传入租户code】https://www.tapd.cn/38229611/prong/stories/view/1138229611001103913
        self.tenant_code = tenant_code
        self.third_party_id = third_party_id
        self._get_host_and_secret()

    def _add_sys_biz_params(self, params):
        """
        添加系统自定义变量（celery环境增加是否屏蔽电话号码）
        :param params:
        :return:
        """
        if not config.get("ThirdParty.biz_shield_privacy"):
            return
        params["shield_privacy"] = 0
        if os.environ.get('RUNC', "") == "celery":
            params["shield_privacy"] = 1

    def get_biz_params(self, params):
        """
        获取业务参数
        :param params:
        :return:
        """
        biz_params = {}
        # 参数优先使用配置值
        for param in params:
            if param.get('name'):
                if param.get('type') == ApiParamType.Sys.value:
                    if param.get('key') == ApiParamSysValue.ProjectCode.value:
                        biz_params[ApiParamSysValue.ProjectCode.value] = self.tenant_code or g.code
                else:
                    biz_params[param.get('name')] = param.get('value')

        # 如果第三次调用传入参数，就替换业务参数值
        if hasattr(g, 'external_params') and g.external_params:
            external_params = g.external_params
            for key, value in external_params.items():
                biz_params[key] = value
        else:
            if self.third_party_id:
                third_user_info = get_user_by_third_party_id_and_user_info(self.third_party_id)
                if third_user_info:
                    biz_params[ApiParamSysValue.ProjectCode.value] = self.tenant_code or g.code
                    biz_params[ApiParamSysValue.UserId.value] = third_user_info.get('user_id', '')
                    biz_params[ApiParamSysValue.UserName.value] = third_user_info.get('name', '')

        # 增加系统自定义变量
        self._add_sys_biz_params(biz_params)
        return biz_params

    def ping(self, biz_params: List[Any]):
        """
        接口是否正常
        :return:
        """
        data = self.get_tables(biz_params)
        if data and isinstance(data, dict) and data.get('data'):
            return True
        return False

    def get_params(self, param: None):
        """
        获取接口参数值
        :param param:
        :return:
        """
        route_url = '/get-biz-params'
        params = {}
        if param:
            params['param'] = param

        return self._api_request(route_url, params)

    def get_tables(
        self,
        biz_params: List[Any],
        keyword: None = None,
        page: int = 1,
        page_size: int = 30,
        table_name_prefix: None = None,
    ):
        """
        获取接口数据表
        :param biz_params:
        :param keyword:
        :param page:
        :param page_size:
        :param table_name_prefix:
        :return:
        """
        route_url = '/get-objects'
        params = {'page': page, 'page_size': page_size}
        if biz_params:
            params['biz_params'] = self.get_biz_params(biz_params)
        if keyword:
            params['keyword'] = keyword
        if table_name_prefix:
            params['table_name_prefix'] = table_name_prefix
        return self._api_request(route_url, params)

    def get_row_count(self, biz_params, tables):
        route_url = '/get-row-count'
        params = {
            'biz_params': self.get_biz_params(biz_params),
            'tables': tables
        }
        return self._api_request(route_url, params)


    def get_table_columns(self, biz_params, table_name):
        """
        根据表名获取表结构
        :param biz_params:
        :param table_name:
        :return:
        """
        route_url = '/get-object-structs'
        params = {}
        if biz_params:
            params['biz_params'] = self.get_biz_params(biz_params)
        if table_name:
            params['obj_name'] = table_name
        return self._api_request(route_url, params)

    def get_data_list(self, biz_params, query_structure, table_names=None, timeout=None, complex_params=None, third_user_info=None):
        """
        获取表数据
        :param str biz_params:业务参数
        :param str query_structure:查询结构
        :param [] table_names:表名集合
        :param int timeout:接口请求超时
        :return:
        """
        route_url = '/get-data'
        params = {}
        if biz_params:
            params['biz_params'] = self.get_biz_params(biz_params)
        if query_structure:
            params['query_structure'] = query_structure
        if table_names:
            params['tables'] = table_names
        if complex_params:
            params['complex_params'] = {
                'complex': complex_params.get('complex'),
                'complex_limit': int(config.get('Function.api_sql_complex_limit', 10000 * 10000 * 10000)),
            }
        if third_user_info:
            params['third_user_info'] = third_user_info or {'id': '', 'account': ''}
        return self._api_request(route_url, params, timeout=timeout)

    def get_data_debug(self, biz_params, query_structure):
        """
        获取表数据
        :param str biz_params: 业务参数
        :param str query_structure:查询结构
        :return:
        """
        route_url = '/get-data-debug'
        params = {}
        if biz_params:
            params['biz_params'] = self.get_biz_params(biz_params)
        if query_structure:
            params['query_structure'] = query_structure
        return self._api_request(route_url, params)

    def set_biz_params(self, third_user_info):
        biz_params = {}
        if third_user_info:
            biz_params[ApiParamSysValue.ProjectCode.value] = self.tenant_code or g.code
            biz_params[ApiParamSysValue.UserId.value] = third_user_info.get('user_id', '')
            biz_params[ApiParamSysValue.UserName.value] = third_user_info.get('name', '')
        return biz_params

    @staticmethod
    @stale_cache(prefix="api_params")
    def _get_dataset_name(dataset_id):
        if not dataset_id:
            return ''
        return repository.get_data_scalar('dataset', {'id': dataset_id}, col_name="name")

    @staticmethod
    @stale_cache(prefix="api_params")
    def _get_report_name(report_id):
        if not report_id:
            return ''
        return repository.get_data_scalar('dashboard', {'id': report_id}, col_name="name")

    def _get_api_query_info(self):
        try:
            dataset_id = getattr(g, 'dataset_id_of_query', '')
            report_id = getattr(g, 'dashboard_id_of_query', '')
            sql_from = getattr(g, 'sql_from', 'viewreport') or 'viewreport'
            return {
                "report_id": report_id,
                "dataset_id": dataset_id,
                "dataset_name": self._get_dataset_name(dataset_id),
                "report_name": self._get_report_name(report_id),
                "sql_from": sql_from
            }
        except:
            return {}

    def _api_request(self, route_url: str, parameters: Optional[Dict[str, int]] = None, timeout: None = None):  # NOSONAR
        """
        请求API
        :param parameters:
        :param int timeout:
        :return:
        """
        parameters['project_code'] = 'yingyansysadmin'
        response = None
        if not self.validation_url(self.host):
            raise UserError(message='无效的url：' + self.host)
        try:
            self.host = self.host[: len(self.host) - 1] if self.host.endswith('/') else self.host
            token_str = default_token_str = self._encode_data_in_jwt()
            deal_external_api_biz_params(biz_params=parameters.get('biz_params', {}))
            if self.cookie:
                token_str = self.check_token(self.cookie.get('token')) if self.cookie.get('token')\
                    else default_token_str
            else:
                if not parameters.get('biz_params', None) and self.third_party_id:
                    third_user_info = get_user_by_third_party_id_and_user_info(self.third_party_id)
                    parameters['biz_params'] = self.set_biz_params(third_user_info)
                    token_str = self.set_token(third_user_info) or token_str
            logger.debug(parameters)
            token = self._get_token(token_str)
            token4aes = self._get_token(default_token_str)
            cookies = {'token': token, 'token4aes': token4aes}
            base64_parameters = str(pybase64.b64encode(json.dumps(parameters).encode('utf-8')), encoding="utf-8")

            st = AnalysisTimeUtils.now()

            # 云空间的数据集api需要支持灰度发布。他们的网关只能通过配置get参数来识别租户
            ykj_params = {"o": self.tenant_code or getattr(g, 'code', '')}

            log_params = self._get_api_query_info()
            log_params and ykj_params.update(log_params)

            response = requests.post(
                self.host + route_url,
                params=ykj_params,
                json=base64_parameters,
                timeout=timeout or 300,
                cookies=cookies,
            )
            ed = AnalysisTimeUtils.now()

            AnalysisTimeUtils.record(
                step=None, sql=None, db_type=AnalysisTimeUtils.db_type.API.value, start_time=st,
                extra={'response': response}, need_type_inference=True
            )

            try:
                if response.status_code != 200:
                    api_result = response.reason
                    is_success = "0"
                else:
                    api_result = response.text
                    is_success = "1"
                start_time = int(st * 1000)
                end_time = int(ed * 1000)
                duration = end_time - start_time

                log_data = {
                    "action": "request_api",
                    "org_code": g.code if hasattr(g, 'code') else "",
                    "api_url": url_add_param(self.host + route_url, ykj_params),
                    "token": token_str,
                    "api_param": base64_parameters,
                    "api_result": api_result,
                    "is_success": is_success,
                    "start_time": str(start_time),
                    "end_time": str(end_time),
                    "duration": str(duration),
                    "account": g.account if hasattr(g, 'account') else "",
                    "dashboard_id": getattr(g, 'dashboard_id_of_query', ''),
                    "dashboard_name": getattr(g, 'log_dashboard').get('name','') if getattr(g, 'log_dashboard', '') else '',
                    "dataset_id": getattr(g, 'dataset_id_of_query', ''),
                    "trace_id": getattr(g, 'trace_id_of_query', ''),
                    "sql_from": getattr(g, 'sql_from', 'viewreport') or 'viewreport'
                }
                # 支持在指定环境记录三云API的请求日志
                is_record_log = int(config.get('Product.record_getdata_api_log', 0))
                is_yunqing = int(config.get('App.is_deploy_in_yunqing', 0))
                if is_record_log and is_yunqing:
                    log_data["env_code"] = str(os.environ.get('CONFIG_AGENT_CLIENT_CODE'))
                    log_data["app_name"] = config.get("App.name", "")
                    from app_celery import upload_log_to_aliyun
                    upload_log_to_aliyun.apply_async(
                        kwargs={
                            "log_data": [tuple([k, v]) for k, v in log_data.items()]
                        },
                        queue='celery'
                    )
                # 日志记录天眼
                from components.fast_logger import FastLogger
                FastLogger.ApiFastLogger(**log_data).record()
            except Exception as e:
                logger.error("记录API请求日志失败：" + str(e))

            if response.status_code != 200:
                try:
                    error = response.json().get('msg')
                except:
                    error = response.reason
                raise UserError(
                    message='url：{url},error:{error},'
                    'status_code:{status_code}'.format(
                        url=self.host, error=error, status_code=response.status_code
                    )
                )
            return response.json()

        except ValueError as ve:
            raise UserError(message='返回json数据格式错误：{}，返回数据为：{}'.format(str(ve), response.text if response else ""))
        except Exception as be:
            if isinstance(be, Timeout):
                raise UserError(message='请求接口超时:' + str(be))
            else:
                logger.error(f'api unexcepted error: {traceback.format_exc()}')
                raise UserError(message='连接失败:' + str(be))

    def check_token(self, dmp_token):
        from user.services.developer_service import Developer
        from dashboard_chart.utils.common import check_dashboard_release_access
        try:
            verified_token = jwt.decode(dmp_token, config.get('JWT.secret'), algorithms="HS256")
            account = verified_token.get('account', None)
            is_developer = Developer.is_developer_by_account(account)
            verified_token['code'] = self.tenant_code or verified_token.get('code', '') or g.code
            if is_developer or check_dashboard_release_access(g):
                verified_token['account'] = get_default_account(g.code)
                verified_token['id'] = ADMINISTRATORS_ID
            dmp_token = jwt.encode(verified_token, config.get('JWT.secret'))
            if verified_token and not verified_token.get('external_params') and self.third_party_id:
                third_user_info = get_user_by_third_party_id_and_user_info(self.third_party_id)
                if third_user_info:
                    dmp_token = self.set_token(third_user_info, verified_token)
        except Exception as e:
            logger.error(f"jwt decode error: {e}")
        return dmp_token

    def set_token(self, third_user_info, token=None):
        if not token:
            token = dict()
        params = dict()
        if third_user_info:
            params[ApiParamSysValue.ProjectCode.value] = self.tenant_code or g.code
            params[ApiParamSysValue.UserId.value] = third_user_info.get('user_id', '')
            params[ApiParamSysValue.UserName.value] = third_user_info.get('name', '')
            params[ApiParamSysValue.ExternalUserId.value] = third_user_info.get('user_id', '')
            token['external_params'] = params
            token[ApiParamSysValue.ExternalUserId.value] = third_user_info.get('user_id', '')
            token = jwt.encode(token, config.get('JWT.secret'))
        return token if token else ''

    @staticmethod
    def get_timestamp():
        return datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')

    @staticmethod
    def validation_url(url: str) -> bool:
        """
        校验url合法性
        :param url:
        :return:
        """
        return bool(re.match(r'^https?:/{2}\w.+$', url))

    def _encode_data_in_jwt(self, data=None):
        data = data or {
            'account': get_default_account(getattr(g, 'code')),
            'id': ADMINISTRATORS_ID,
            'code': self.tenant_code or getattr(g, 'code')
        }
        jwt_secret = config.get('JWT.secret')
        return jwt.encode(data, jwt_secret)

    def _get_token(self, token_str):
        """
        获取加密token
        :param token_str:
        :return:
        """
        if self.access_secret and len(self.access_secret) >= 32:
            token = AESCrypt_new(self.access_secret).encrypt(token_str)
        else:
            token = AESCrypt(self.access_secret).encrypt(token_str)
        return token

    def _get_host_and_secret(self):
        pattern = r"(?P<value>\$\{(.*?)\})"
        if self.host:
            self.host = re.sub(pattern, self.replace_func, self.host) or self.host
        if self.access_secret:
            self.access_secret = re.sub(pattern, self.replace_func, self.access_secret) or self.access_secret

    @staticmethod
    def replace_func(match):
        key = match.group(2) or ''
        value = config.get('ThirdDatasource.{}'.format(key))
        if value:
            return value


if __name__ == "__main__":

    def get_token(token_s):
        access_secret = 'PvZiduLJ@#2KhEKM'

        if access_secret and len(access_secret) >= 32:
            token = AESCrypt_new(access_secret).encrypt(token_s)
        else:
            token = AESCrypt(access_secret).encrypt(token_s)
        return token

    token_s = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7cdBKgwnfwbppcX_sSIpOF-jJYxkspYZW5_whbMrmhI"
    # token_s = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Xp7uocLt_PzJLHas-fJMc-PvoHFg6UECZmAU1O99W2M"
    default_token_str = token_s
    token = get_token(token_s)
    token4aes = get_token(default_token_str)
    cookies = {'token': token, 'token4aes': token4aes}

    # base64_parameters = "eyJiaXpfcGFyYW1zIjogeyJ0YWJsZV9hY2Nlc3MiOiAie1wiZHdfempfZmFjdF9jaGVja3F1YWxpdHlfcHJvYmxlbV9jaGVjazphXCI6XCJlbmRfcHJval9pZFwifSIsICJwcm9qZWN0X2NvZGUiOiAieWx5cyIsICJwdWxzYXJfcHJvamVjdCI6ICJ5bF9zYWFzX3ByZCIsICJlbmdpbmUiOiAiU3RhclJvY2tzU2FhUyIsICJzaGllbGRfcHJpdmFjeSI6IDB9LCAicXVlcnlfc3RydWN0dXJlIjogeyJncm91cF9ieSI6IFtdLCAiaGF2aW5nIjogW10sICJsaW1pdCI6IHsib2Zmc2V0IjogbnVsbCwgInJvdyI6IG51bGx9LCAib2JqZWN0IjogW3siYWxpYXMiOiAiIGEgIiwgImpvaW5fdHlwZSI6IG51bGwsICJuYW1lIjogIiAoc2VsZWN0XG5cdGEuZmlyc3RfY29ycF9uYW1lIGFzICdcdTRlMDBcdTdlYTdcdTUxNmNcdTUzZjgnXG4gICAgLGEuc2Vjb25kX2NvcnBfbmFtZSBhcyAnXHU0ZThjXHU3ZWE3XHU1MTZjXHU1M2Y4J1xuICAgICxhLnRoaXJkX2NvcnBfbmFtZSBhcyAnXHU0ZTA5XHU3ZWE3XHU1MTZjXHU1M2Y4J1xuICAgICxhLmNvcnBfaWQgYXMgJ1x1NjcyYlx1N2VhN1x1NTE2Y1x1NTNmOGlkJ1xuICAgICxhLmNvcnBfbmFtZSBhcyAnXHU2NzJiXHU3ZWE3XHU1MTZjXHU1M2Y4J1xuICAgICxhLnBhcmVudF9wcm9qX2lkIGFzICdcdTRlMDBcdTdlYTdcdTk4NzlcdTc2ZWVpZCdcbiAgICAsYS5wYXJlbnRfcHJval9uYW1lIGFzICdcdTRlMDBcdTdlYTdcdTk4NzlcdTc2ZWUnXG4gICAgLGEuZW5kX3Byb2pfaWQgYXMgJ1x1NjcyYlx1N2VhN1x1OTg3OVx1NzZlZWlkJ1xuICAgICxhLmVuZF9wcm9qX25hbWUgYXMgJ1x1NjcyYlx1N2VhN1x1OTg3OVx1NzZlZSdcbiAgICAsYS5iaWRzZWN0aW9uX2lkIGFzICdcdTY4MDdcdTZiYjVpZCdcbiAgICAsYS5iaWRzZWN0aW9uX25hbWUgYXMgJ1x1NjgwN1x1NmJiNSdcblx0LGNvdW50KGEuaWQpIGFzIFx1NjcwOVx1NjU0OFx1OTVlZVx1OTg5OFx1NjU3MFxuICAgICxjb3VudChjYXNlIHdoZW4gYS5pc19yZWN0aWZpZWQ9MSB0aGVuIGEuaWQgZWxzZSBudWxsIGVuZCkgYXMgXHU2NTc0XHU2NTM5XHU5NWVlXHU5ODk4XHU2NTcwXG5mcm9tIGR3X3pqX2ZhY3RfY2hlY2txdWFsaXR5X3Byb2JsZW1fY2hlY2sgYVxud2hlcmUgYS5iYXRjaF9wdXJwb3NlPSdcdTZiNjNcdTVmMGYnICBcbmFuZCBhLnN0YXR1czw+J1x1NWRmMlx1NTE3M1x1OTVlZCdcbiAgICBncm91cCBieSBhLmZpcnN0X2NvcnBfbmFtZSBcbiAgICAsYS5zZWNvbmRfY29ycF9uYW1lIFxuICAgICxhLnRoaXJkX2NvcnBfbmFtZSBcbiAgICAsYS5jb3JwX2lkIFxuICAgICxhLmNvcnBfbmFtZSBcbiAgICAsYS5wYXJlbnRfcHJval9pZCBcbiAgICAsYS5wYXJlbnRfcHJval9uYW1lIFxuICAgICxhLmVuZF9wcm9qX2lkIFxuICAgICxhLmVuZF9wcm9qX25hbWUgXG4gICAgLGEuYmlkc2VjdGlvbl9pZCBcbiAgICAsYS5iaWRzZWN0aW9uX25hbWUgXG4pICIsICJyZWZfY2xhdXNlIjogW119XSwgIm9yZGVyX2J5IjogW10sICJzZWxlY3QiOiBbeyJhbGlhcyI6ICJcdTRlMDBcdTdlYTdcdTUxNmNcdTUzZjgiLCAiY29uZGl0aW9ucyI6IFtdLCAiZnVuYyI6IG51bGwsICJvYmpfbmFtZSI6IG51bGwsICJvcGVyYXRvciI6IG51bGwsICJwcm9wX25hbWUiOiAiXHU0ZTAwXHU3ZWE3XHU1MTZjXHU1M2Y4IiwgInByb3BfcmF3IjogbnVsbCwgInByb3BfcmVmIjogbnVsbCwgInByb3BzIjogW10sICJzcGVjaWZpZXIiOiBudWxsLCAidmFsdWUiOiBudWxsfV0sICJ2YXJzIjogW10sICJ3aGVyZSI6IFtdfSwgInRhYmxlcyI6IFsiZHdfempfZmFjdF9jaGVja3F1YWxpdHlfcHJvYmxlbV9jaGVjayJdLCAidGhpcmRfdXNlcl9pbmZvIjogeyJpZCI6ICIiLCAiYWNjb3VudCI6ICIifSwgInByb2plY3RfY29kZSI6ICJ5aW5neWFuc3lzYWRtaW4ifQ=="
    base64_parameters = "eyJiaXpfcGFyYW1zIjp7InRhYmxlX2FjY2VzcyI6IntcImR3X3pqX2ZhY3RfY2hlY2txdWFsaXR5X3Byb2JsZW1fY2hlY2s6YVwiOlwiZW5kX3Byb2pfaWRcIn0iLCJwcm9qZWN0X2NvZGUiOiJ5bHlzIiwicHVsc2FyX3Byb2plY3QiOiJ5bF9zYWFzX3ByZCIsImVuZ2luZSI6IlN0YXJSb2Nrc1NhYVMiLCJwYXNzcG9ydCI6IiIsInVzZXJfbmFtZSI6Iui2hee6p+eUqOaIt3lseXMiLCJ1c2VyX2lkIjoiMjJiMTFkYjQtZTkwNy00ZjFmLTg4MzUtYjlkYWFiNmUxZjIzIiwieWxfdXNlcl9pZCI6IjIyYjExZGI0LWU5MDctNGYxZi04ODM1LWI5ZGFhYjZlMWYyMyIsInVzZXJfYXV0aCI6InZpZXcsZG93bmxvYWQiLCJiaXpfY29kZSI6IjA0NzVjZGRiZjNkYTRiMTQ4ZTlkOGM3M2VmNGIzMmZiIiwiZXhwIjoxNjU1OTcyNzE5LCJzaGllbGRfcHJpdmFjeSI6MH0sInF1ZXJ5X3N0cnVjdHVyZSI6eyJncm91cF9ieSI6W10sImhhdmluZyI6W10sImxpbWl0Ijp7Im9mZnNldCI6bnVsbCwicm93IjpudWxsfSwib2JqZWN0IjpbeyJhbGlhcyI6IiBhICIsImpvaW5fdHlwZSI6bnVsbCwibmFtZSI6IiAoc2VsZWN0XG5cdGEuZmlyc3RfY29ycF9uYW1lIGFzICfkuIDnuqflhazlj7gnXG4gICAgLGEuc2Vjb25kX2NvcnBfbmFtZSBhcyAn5LqM57qn5YWs5Y+4J1xuICAgICxhLnRoaXJkX2NvcnBfbmFtZSBhcyAn5LiJ57qn5YWs5Y+4J1xuICAgICxhLmNvcnBfaWQgYXMgJ+acq+e6p+WFrOWPuGlkJ1xuICAgICxhLmNvcnBfbmFtZSBhcyAn5pyr57qn5YWs5Y+4J1xuICAgICxhLnBhcmVudF9wcm9qX2lkIGFzICfkuIDnuqfpobnnm65pZCdcbiAgICAsYS5wYXJlbnRfcHJval9uYW1lIGFzICfkuIDnuqfpobnnm64nXG4gICAgLGEuZW5kX3Byb2pfaWQgYXMgJ+acq+e6p+mhueebrmlkJ1xuICAgICxhLmVuZF9wcm9qX25hbWUgYXMgJ+acq+e6p+mhueebridcbiAgICAsYS5iaWRzZWN0aW9uX2lkIGFzICfmoIfmrrVpZCdcbiAgICAsYS5iaWRzZWN0aW9uX25hbWUgYXMgJ+agh+autSdcblx0LGNvdW50KGEuaWQpIGFzIOacieaViOmXrumimOaVsFxuICAgICxjb3VudChjYXNlIHdoZW4gYS5pc19yZWN0aWZpZWQ9MSB0aGVuIGEuaWQgZWxzZSBudWxsIGVuZCkgYXMg5pW05pS56Zeu6aKY5pWwXG5mcm9tIGR3X3pqX2ZhY3RfY2hlY2txdWFsaXR5X3Byb2JsZW1fY2hlY2sgYVxud2hlcmUgYS5iYXRjaF9wdXJwb3NlPSfmraPlvI8nICBcbmFuZCBhLnN0YXR1czw+J+W3suWFs+mXrSdcbiAgICBncm91cCBieSBhLmZpcnN0X2NvcnBfbmFtZSBcbiAgICAsYS5zZWNvbmRfY29ycF9uYW1lIFxuICAgICxhLnRoaXJkX2NvcnBfbmFtZSBcbiAgICAsYS5jb3JwX2lkIFxuICAgICxhLmNvcnBfbmFtZSBcbiAgICAsYS5wYXJlbnRfcHJval9pZCBcbiAgICAsYS5wYXJlbnRfcHJval9uYW1lIFxuICAgICxhLmVuZF9wcm9qX2lkIFxuICAgICxhLmVuZF9wcm9qX25hbWUgXG4gICAgLGEuYmlkc2VjdGlvbl9pZCBcbiAgICAsYS5iaWRzZWN0aW9uX25hbWUgXG4pICIsInJlZl9jbGF1c2UiOltdfV0sIm9yZGVyX2J5IjpbXSwic2VsZWN0IjpbeyJhbGlhcyI6IuS4gOe6p+WFrOWPuCIsImNvbmRpdGlvbnMiOltdLCJmdW5jIjpudWxsLCJvYmpfbmFtZSI6bnVsbCwib3BlcmF0b3IiOm51bGwsInByb3BfbmFtZSI6IuS4gOe6p+WFrOWPuCIsInByb3BfcmF3IjpudWxsLCJwcm9wX3JlZiI6bnVsbCwicHJvcHMiOltdLCJzcGVjaWZpZXIiOm51bGwsInZhbHVlIjpudWxsfV0sInZhcnMiOltdLCJ3aGVyZSI6W119LCJ0YWJsZXMiOlsiZHdfempfZmFjdF9jaGVja3F1YWxpdHlfcHJvYmxlbV9jaGVjayJdLCJ0aGlyZF91c2VyX2luZm8iOnsiaWQiOiIiLCJhY2NvdW50IjoiIn0sInByb2plY3RfY29kZSI6Inlpbmd5YW5zeXNhZG1pbiJ9"

    # 云空间的数据集api需要支持灰度发布。他们的网关只能通过配置get参数来识别租户
    response = requests.post(
        "https://pulsar-openapi.myyscm.com/dmp_api/serve?r=/get-data",
        params={"o": "ylys"},
        json=base64_parameters,
        timeout=300,
        cookies=cookies,
    )
    print(response.json())

    # access_secret = 'ec56f8415c68b35d2b2d0a99e5b81dc33a30df66313d3795922c3ff3000b62c1'
    # print(AESCrypt().decrypt(access_secret))
