#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""

    <NAME_EMAIL> on 2017/8/9.
"""
import socket
import struct
from pika import spec
from pika.adapters.blocking_connection import BlockingConnection
from pika.connection import ConnectionParameters
from pika.credentials import PlainCredentials
from dmplib import config
from dmplib.utils.errors import UserError


class RabbitMQ:
    def __init__(self, host=None, port=None, user=None, password=None, is_priority=False):
        self.host = host or config.get('RabbitMQ.host', self._loopback_address)
        self.port = port or int(config.get('RabbitMQ.port', 5672))
        self.user = user or config.get('RabbitMQ.user', 'guest')
        self.password = password or config.get('RabbitMQ.password', 'guest')
        self.vhost = config.get('RabbitMQ.vhost', 'vhost_tj_sj')
        self.priority_args = {"x-max-priority": 10} if bool(self.vhost != '/') or is_priority else None

    @property
    def _loopback_address(self):
        return socket.inet_ntoa(struct.pack('!I', socket.INADDR_LOOPBACK))

    def get_connection(self):
        """
        获取BlockingConnection
        :return:pika.adapters.blocking_connection.BlockingConnection
        """
        try:
            credentials = PlainCredentials(self.user, self.password)
            return BlockingConnection(
                parameters=ConnectionParameters(self.host, self.port, credentials=credentials, heartbeat=0, virtual_host=self.vhost)
            )
        except Exception as e:
            msg = "rabbitmq connection error " + str(e)
            raise UserError(message=msg)

    def send_message(self, queue, body, durable=None, headers=None, auto_delete=False, arguments=None):
        """
        发送消息
        :param headers:
        :param str queue: 队列名称
        :param str body: 消息内容
        :param bool durable: 消息是否持久
        :param bool auto_delete: 消息是否自动删除
        :param dict arguments: 消息存活参数设置，例如：{'x-message-ttl': 60000} 消息的存活时间是60秒
        :return:
        """
        if isinstance(body, str):
            body = body.encode()
        if arguments and self.priority_args:
            arguments.update(self.priority_args)
        elif not arguments and self.priority_args:
            arguments = self.priority_args

        if queue in [config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op') or 'Flow-erp-op']:
            arguments = arguments or {}
            arguments['x-max-priority'] = 10
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_declare(queue=queue, durable=durable or False, auto_delete=auto_delete, arguments=arguments)
            properties = spec.BasicProperties()
            properties.headers = headers
            return channel.basic_publish(exchange='', routing_key=queue, body=body, properties=properties)

    def receive_message(self, queue, consumer_callback, durable=None):
        """
        接收消息
        :param str queue: 队列名称
        :param function consumer_callback:处理消息函数
        :param bool durable: 消息是否持久
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_declare(queue=queue, durable=durable or False, arguments=self.priority_args)
            channel.basic_qos(prefetch_count=1)
            channel.basic_consume(on_message_callback=consumer_callback, queue=queue, auto_ack=False)
            channel.start_consuming()

    def queue_delete(self, queue):
        """
        删除队列
        :param str queue:队列名称
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_delete(queue=queue)

    def get_consumer_count(self, queue):
        """
        获取队列总数
        :param queue:
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            if queue in [config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op') or 'Flow-erp-op']:
                arguments = {"x-max-priority": 10}
            else:
                arguments = None
            arguments = arguments or self.priority_args
            this = channel.queue_declare(queue=queue, arguments=arguments)
            return this.method.consumer_count

    def get_message_count(self, queue):
        """
        获取指定队列的消息总数
        :param queue:
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            if queue in [config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op') or 'Flow-erp-op']:
                arguments = {"x-max-priority": 10}
            else:
                arguments = None
            arguments = arguments or self.priority_args
            this = channel.queue_declare(queue=queue, arguments=arguments)
            return this.method.message_count

    def purge_message_of_queue(self, queue):
        """
        清理队列
        :param queue:
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            return channel.queue_purge(queue)
