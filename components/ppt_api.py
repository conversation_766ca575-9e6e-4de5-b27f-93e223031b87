#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    请求访问在线报告，明细报告的接口调用类
    Author: lul05
    Time: 2022-5-17 14:49:44
"""
import json
from loguru import logger
import time

import requests
import jwt

from dmplib.utils.errors import UserError
from dmplib import config
from base.enums import ProjectValueAddedFunc


class PptApi:

    # 业务系统的域名
    BIZ_DOMAIN_MAP = {
        ProjectValueAddedFunc.PPT.value: 'PPT.domain',
        ProjectValueAddedFunc.ACTIVE_REPORT.value: 'PPT.active_report_domain',
    }

    # 业务系统api path 路由
    BIZ_API_PATH_MAP = {
        ProjectValueAddedFunc.PPT.value: 'open/api/dmp/distribution',
        ProjectValueAddedFunc.ACTIVE_REPORT.value: 'api/open/dmp',
    }

    # 业务系统名称
    BIZ_NAME_MAP = {
        ProjectValueAddedFunc.PPT.value: '在线报告',
        ProjectValueAddedFunc.ACTIVE_REPORT.value: '统计报告',
    }

    def __init__(self, biz_type, project_code, user_id, account, user_name=""):
        self.biz_type = biz_type.lower()
        self.project_code = project_code if project_code else ""
        self.user_id = user_id if user_id else ""
        self.account = account
        self.user_name = user_name
        self.api_host = self._get_host()
        self.api_path = PptApi.BIZ_API_PATH_MAP.get(self.biz_type, "")
        self.retry = 0
        self.biz_name = PptApi.BIZ_NAME_MAP.get(self.biz_type, "")

    def _get_host(self):
        """
        获取业务系统的接口请求host
        :return:
        """
        domain = ""
        # ppt在线报告接口请求支持内网host
        if self.biz_type == ProjectValueAddedFunc.PPT.value:
            domain = config.get('PPT.private_host')
        if not domain:
            host_key = PptApi.BIZ_DOMAIN_MAP.get(self.biz_type, "")
            domain = config.get(host_key) if host_key else ""
        return domain

    def get_token(self):
        data = {
            "user_id": self.user_id,
            "code": self.project_code,
            "account": self.account,
            "name": self.user_name
        }
        sso_secret = config.get('PPT.sso_secret')
        if not sso_secret:
            raise UserError(message="业务系统访问秘钥配置为空")
        return jwt.encode(data, sso_secret)

    def get(self, action_name, params=None):
        """
        get请求数据获取
        :param action_name:
        :param params:
        :return:
        """
        return self.api_request(action_name, params, request_type='get')

    def post(self, action_name, params):
        """
        get请求数据获取
        :param action_name:
        :param params:
        :return:
        """
        return self.api_request(action_name, params)

    def api_request(self, action_name, params: None, request_type='post', header_info=dict()):
        if not self.api_host:
            raise UserError(message=f"{self.biz_name}服务域名不存在，无法请求")
        if params is None:
            params = {}
        host = self.api_host
        host = host[: len(host) - 1] if host.endswith('/') else host
        if self.api_path:
            host = '%s/%s' % (host, self.api_path)
        url = '%s/%s?token=%s' % (host, action_name, self.get_token())
        status_code = 500
        try:
            logger.error(f"请求url:{url}")
            logger.error(f"请求参数：{json.dumps(params, ensure_ascii=False)}")
            if request_type.lower() == 'post':
                response = requests.post(url, json=params, timeout=30, headers=header_info)
            else:
                response = requests.get(url, params=params, timeout=30, headers=header_info)
            result = response.json()
            status_code = response.status_code
            logger.error(f"请求结果：{json.dumps(result, ensure_ascii=False)} 请求状态：{status_code}")
        except Exception as be:
            msg = f"{self.biz_name}业务系统服务请求发生异常:{str(be)} url:{url} parameters:{json.dumps(params, ensure_ascii=False)}"
            logger.error(msg)
            raise UserError(message=f"{self.biz_name}业务系统服务请求发生异常({status_code}) errs:{str(be)}",
                            code=status_code)

        if response.status_code == 200:
            if not result.get("result"):
                msg = self.biz_name + '业务系统接口请求失败，errs:'+result.get('msg') + \
                      ' data:'+json.dumps(result.get('data'), ensure_ascii=False)
                logger.error(msg)
                show_msg = self.biz_name + '错误，'+result.get('msg')
                raise UserError(message=show_msg)
            else:
                return result.get('data')
        else:
            # 重试3次
            if self.retry < 3:
                self.retry += 1
                time.sleep(1)
                logger.error(f"第{self.retry}次重试")
                self.api_request(action_name, params, request_type, header_info)

            msg = f'{self.biz_name}业务系统接口响应异常({response.status_code})'
            logger.error(msg)
            raise UserError(message=msg, code=response.status_code)
