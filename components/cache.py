#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :cache.py
# @Time      :2022/4/2 14:25
# <AUTHOR>

import hashlib
import json
import functools

from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.saas.project import get_data_db
from dmplib.redis import RedisCache
from components.data_compress import DataCompress


def hash_key(func):
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        new_args = list(args)
        new_args[0] = self._hash_key(args[0])
        return func(self, *new_args, **kwargs)
    return wrapper


class DataCenterCache(object):
    """
    key hash处理
    value 压缩处理
    """

    def __init__(self, code='', cache_type='m'):
        self.code = code
        # m: mysql  r: redis, mysql下设置过期时间需要开启定时器
        self.cache_type = cache_type
        self.__check_params()

    def __check_params(self):
        g.code = self.code or g.code
        if not g.code:
            raise UserError(message="缺少租户code参数")
        if self.cache_type not in ["r", "m"]:
            raise UserError(message="类型错误")

    def set(self, key, value, exp):
        return getattr(self, f"{self.cache_type}_set")(key, value, exp)

    def set_data(self, key, value, dataset_id, exp):
        return getattr(self, f"{self.cache_type}_set_data")(key, value, dataset_id, exp)

    def get(self, key):
        return getattr(self, f"{self.cache_type}_get")(key)

    def get_data(self, key):
        return getattr(self, f"{self.cache_type}_get_data")(key)

    def delete(self, key, dataset_id):
        return getattr(self, f"{self.cache_type}_del")(key, dataset_id)

    @staticmethod
    def _hash_key(key):
        key = f"{g.code}:{key}"
        return hashlib.md5(key.encode("utf-8")).hexdigest()

    @property
    def mysql_db(self):
        return get_data_db()

    @property
    def redis_db(self):
        return RedisCache(connect_timeout=2)

    @staticmethod
    def _dumps(value):
        return json.dumps(value, ensure_ascii=False)

    @staticmethod
    def _compress(value):
        return DataCompress.compress(value)

    @staticmethod
    def _decompress(value):
        return DataCompress.decompress(value)

    @staticmethod
    def _loads(value):
        return json.loads(value)

    def _insert_data(self, key, value, dataset_id, exp=None):
        with self.mysql_db as db:
            sql = """
            replace into cache_storage(`key`, `value`, `is_exp`, `dataset_id`, `exp`) 
            values (%(key)s, %(value)s, %(is_exp)s, %(dataset_id)s,%(exp)s)
            """
            params = {
                "key": key,
                "value": self._compress(value),
                "dataset_id": dataset_id,
                "is_exp": 0 if exp is None else 1,
                "exp": exp
            }
            return db.exec_sql(sql, params)

    def _query_data(self, key):
        with self.mysql_db as db:
            value = db.query_scalar("select value from cache_storage where `key`=%(key)s", {"key": key})
            return self._decompress(value) if value else None

    @hash_key
    def m_set(self, key, value, dataset_id, exp=None):
        self._insert_data(key, value, dataset_id, exp)

    @hash_key
    def m_set_data(self, key, value, dataset_id, exp=None):
        self._insert_data(key, self._dumps(value), dataset_id, exp)

    @hash_key
    def m_del(self, key, dataset_id):
        # 删除当前数据集下所有缓存
        with self.mysql_db as db:
            return db.delete("cache_storage", {"dataset_id": dataset_id})

    @hash_key
    def m_get(self, key):
        return self._query_data(key)

    @hash_key
    def m_get_data(self, key):
        value = self._query_data(key)
        if value:
            value = json.loads(value)
        return value

    @hash_key
    def r_set(self, key, value, dataset_id, exp=None):
        value = self._compress(value)
        return self.redis_db.set(key, value, exp)

    @hash_key
    def r_set_data(self, key, value, dataset_id, exp=None):
        value = self._compress(self._dumps(value))
        return self.redis_db.set(key, value, exp)

    @hash_key
    def r_del(self, key, dataset_id=None):
        return self.redis_db.delete(key)

    @hash_key
    def r_get(self, key):
        value = self.redis_db.get(key)
        if not value:
            return None
        return self._decompress(value)

    @hash_key
    def r_get_data(self, key):
        value = self.redis_db.get(key)
        if value:
            return self._loads(self._decompress(value))
        return None


if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext
    g = _AppCtxGlobals()
    _app_ctx_stack.push(g)
    db_ctx = DBContext()
    db_ctx.inject(g)
    cache = DataCenterCache("uitest")
    hash_res = cache._hash_key("select * from dataset from adfasdfasdfasdfasdfasdfasdfasfd")
    print(hash_res, type(hash_res))
