# -*- coding: utf-8 -*-
# pylint: skip-file
"""
结构化sql解析及阿里云函数计算
"""
import functools
import json
import re

from datetime import date, datetime
from base.enums import DBEngine
from copy import deepcopy
from typing import Any, Dict, List, Union
from base.errors import UserError

YEAR_COMPILE = re.compile(r"YEAR\((.*?)\)")
QUARTER_COMPILE = re.compile(r"QUARTER\((.*?)\)")
WEEK_COMPILE = re.compile(r"WEEK\((.*?)\)")
MONTH_COMPILE = re.compile(r"MONTH\((.*?)\)")
DAY_COMPILE = re.compile(r"DAY\((.*?)\)")
DAY_DIFF_COMPILE = re.compile(r"TIMESTAMPDIFF\(DAY,(.*)\)")
HOUR_DIFF_COMPILE = re.compile(r"TIMESTAMPDIFF\(HOUR,(.*)\)")
MINUTE_DIFF_COMPILE = re.compile(r"TIMESTAMPDIFF\(MINUTE,(.*)\)")
DIFF_COMPILE = re.compile(r"TIMESTAMPDIFF\((.*?),(.*?),(.*?)\)")
PLACEHOLDER_COMPILE = re.compile(r".*?`(.*?)`.*")


def dict_to_model(data):
    """
    将字典转换成model对象
    :param data:
    :return:
    """
    top = Model(**data)
    seqs = tuple, list, set, frozenset
    for i, j in data.items():
        if isinstance(j, dict):
            setattr(top, i, dict_to_model(j))
        elif isinstance(j, seqs):
            setattr(top, i, type(j)(dict_to_model(sj) if isinstance(sj, dict) else sj for sj in j))
        else:
            setattr(top, i, j)
    return top


class Model:
    def __init__(self, **kwargs) -> None:
        self.set_attributes(**kwargs)

    def set_attributes(self, **kwargs) -> None:
        """
        将dict数据初始化到对象属性
        :param kwargs:
        :return:
        """
        if not kwargs:
            return
        dirs = [a for a in dir(self) if a[0:1] != '_' and not callable(getattr(self, a))]
        dirs = set(dirs).intersection(kwargs.keys())
        if not dirs:
            return
        for k in dirs:
            attribute = getattr(self, k)
            val = kwargs.get(k)
            if isinstance(attribute, Model):
                if not val:
                    continue
                if isinstance(val, dict):
                    attribute.set_attributes(**val)
                elif isinstance(val, str):
                    attribute.set_attributes(**json.loads(val))
                else:
                    setattr(self, k, val)
            elif isinstance(attribute, list) and isinstance(val, list):
                self.set_custom_attributes(k, val)
            else:
                setattr(self, k, val)

    def set_custom_attributes(self, k: str, val: Any) -> None:
        # 属性里面有list对象需要在这转换
        if k == "select":
            new_val = [Select(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "object":
            new_val = [Object(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "where":
            new_val = [Where(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "group_by":
            new_val = [Group(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "having":
            new_val = [Having(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "order_by":
            new_val = [Order(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "props":
            new_val = [Prop(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "conditions":
            new_val = [Conditions(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "ref_clause":
            new_val = [RefClause(**v) for v in val]
            setattr(self, k, new_val)
        elif k == "vars":
            new_val = [Var(**v) for v in val]
            setattr(self, k, new_val)
        else:
            setattr(self, k, val)

    def get_dict(self, attributes: None = None) -> Dict[str, Any]:
        """
        获取属性字典
        注：当申明__slots__之后 self.__dict__将为空，必须使用dir
        :param list attributes:
        :return dict:
        """
        attr_dict = {}
        dirs = dir(self)
        if attributes:
            dirs = list(set(dirs).intersection(set(attributes)))
        for attribute in dirs:
            if attribute[0:1] == '_':
                continue
            value = getattr(self, attribute)
            if callable(value):
                continue
            attr_dict[attribute] = value
        return attr_dict

    def __str__(self):
        return json.dumps(self.get_dict(), cls=ModelEncoder, indent=4, sort_keys=True)

    def __repr__(self):
        return json.dumps(self.get_dict(), cls=ModelEncoder, indent=4, sort_keys=True)


class SqlObject(Model):
    __slots__ = ['name', 'alias', 'join_type', 'ref_clause', 'where', 'sub_query']

    def __init__(self, **kwargs) -> None:
        self.name = None
        self.alias = None
        self.join_type = None
        self.ref_clause = []
        self.where = []
        self.sub_query = None
        super().__init__(**kwargs)


class ObjectWithExtra(SqlObject):
    __slots__ = ['obj_prefix']

    def __init__(self, **kwargs) -> None:
        self.obj_prefix = None
        super().__init__(**kwargs)


class Limit(Model):
    __slots__ = ['offset', 'row']

    def __init__(self, **kwargs) -> None:
        self.offset = None
        self.row = None
        super().__init__(**kwargs)


class QueryStructure(Model):
    __slots__ = ['select', 'object', 'where', 'group_by', 'having', 'order_by', 'limit', 'vars']

    def __init__(self, **kwargs) -> None:
        self.select = []
        self.object = []
        self.where = []
        self.group_by = []
        self.having = []
        self.order_by = []
        self.limit = Limit()
        self.vars = []
        super().__init__(**kwargs)


class Prop(Model):
    __slots__ = [
        'obj_name',
        'prop_name',
        'alias',
        'func',
        'props',
        'specifier',
        'operator',
        'value',
        'prop_ref',
        'prop_raw',
        'value_type',  # 过滤值是固定值还是参数类型，参数类型需要进行参数替换
    ]

    def __init__(self, **kwargs) -> None:
        self.obj_name = None
        self.prop_name = None
        self.prop_ref = None
        self.prop_raw = None
        self.alias = None
        self.func = None
        self.props = []
        self.specifier = None
        self.operator = None
        self.conditions = []
        self.value = None
        self.value_type = None
        super().__init__(**kwargs)


class Select(Prop):
    pass


class SelectWithExtra(Select):
    __slots__ = ['convert_field_type', 'obj_prefix']

    def __init__(self, **kwargs) -> None:
        self.convert_field_type = None
        self.obj_prefix = None
        super().__init__(**kwargs)


class Object(Model):
    __slots__ = ['name', 'alias', 'join_type', 'ref_clause']

    def __init__(self, **kwargs) -> None:
        self.name = None
        self.alias = None
        self.join_type = None
        self.ref_clause = []
        super().__init__(**kwargs)


class RefClause(Model):
    __slots__ = ['logical_relation', 'left', 'operator', 'right', 'conditions']

    def __init__(self, **kwargs):
        self.logical_relation = None
        self.left = Prop()
        self.operator = None
        self.right = Prop()
        self.conditions = []
        super().__init__(**kwargs)


class Where(Model):
    __slots__ = ['logical_relation', 'left', 'operator', 'right', 'conditions', 'prop_ref', 'prop_raw']

    def __init__(self, **kwargs) -> None:
        self.logical_relation = None
        self.left = Prop()
        self.operator = None
        self.prop_ref = None
        self.prop_raw = None
        self.right = Prop()
        self.conditions = []
        super().__init__(**kwargs)


class Conditions(Where):
    pass


class Group(Prop):
    pass


class Having(Prop):
    pass


class Order(Prop):
    __slots__ = ['method']

    def __init__(self, **kwargs) -> None:
        self.method = None
        super().__init__(**kwargs)


class Var(Model):
    __slots__ = [
        'var_type',
        'value_type',
        'value_source',
        'value_identifier',
        'value',
        'default_value',
        'default_value_type',
        'var_id',
        'external_content',
    ]

    def __init__(self, **kwargs):
        self.var_type = None
        self.value_type = None
        self.value = None
        self.var_id = None
        self.value_source = None
        self.value_identifier = None
        self.default_value = None
        self.default_value_type = None
        self.external_content = None
        super().__init__(**kwargs)


class BadQueryDefinition(Exception):
    pass


class BadConditionDefinition(BadQueryDefinition):
    pass


class BadColumnDefinition(BadQueryDefinition):
    pass


class BadCaseWhenDefinition(BadQueryDefinition):
    pass


class Token:
    def __init__(self, kind, take):
        self.take = take
        self.kind = kind


class SQLexer:
    def __init__(self, sql):
        self.sql = sql
        self.index = 0

    def lex(self):
        i = -1
        token_begin = 0
        kind = None
        tokens = []

        for c in self.sql:
            i += 1

            if kind is None:

                if c == '`':
                    kind = 'quotation'
                    token_begin = i

                elif c == "'":
                    kind = 'string'
                    token_begin = i

                elif c == "\"":
                    kind = 'string2'
                    token_begin = i

            elif kind == 'quotation' and c == "`":
                token_end = i
                tokens.append(Token('quotation', take=[token_begin, token_end]))
                kind = None
                token_begin = 0

            elif kind == 'string' and c == "'":
                token_end = i
                tokens.append(Token('string', take=[token_begin, token_end]))
                kind = None
                token_begin = 0

            elif kind == 'string2' and c == "\"":
                token_end = i
                tokens.append(Token('string2', take=[token_begin, token_end]))
                kind = None
                token_begin = 0

        return tokens


def replace_quotation(sqltext, replace=''):
    lx = SQLexer(sqltext)
    result = lx.lex()
    # pylint:disable=unnecessary-comprehension
    sqltext_list = [i for i in sqltext]
    for token in result:
        if token and token.kind in ['quotation']:
            sqltext_list[token.take[0]] = sqltext_list[token.take[1]] = replace
    sql = ''.join(sqltext_list)
    return sql


def _wrapper_value(val: int) -> str:
    if val is None:
        return 'NULL'

    if isinstance(val, (int, float)):
        return str(val)
    # 2021.11.04 不加引号的原始字符串，适配某些数据的函数名， 只是适配sqlserver的特殊语法场景
    # 此处实质上是修改了对json_structure中prop.value的解析，api数据集的api服务在不更新对应服务的情况下，是不能支持这种语法的
    # 但是现在api数据服务现有的情况下，是不会有sqlserver的，所以最好只在sqlserver的场景下使用这种语法
    if isinstance(val, str) and val.startswith('raw:'):
        return val.split('raw:')[-1]
    return "'{}'".format(val)


def _alias(col_expression, alias):
    if alias:
        return '%s AS %s' % (col_expression, alias)
    return col_expression


def encode_column_dremio(column: Prop) -> str:
    result = ''
    specifier = column.specifier.upper() if column.specifier else ''
    if specifier:
        result = '%s ' % specifier

    if column.operator:
        result += column.operator.upper()

    if column.func:
        result += column.func.upper()

    if specifier == 'WHEN':
        if not column.conditions:
            raise BadCaseWhenDefinition("WHEN缺少conditions")

        result += ' %s ' % encode_condition_dremio(column.conditions)

        if not column.props or len(column.props) != 1:
            raise BadCaseWhenDefinition("THEN的props必须有且只有一个元素")

        prop = column.props[0]
        result += 'THEN %s' % encode_column_dremio(prop)

    elif specifier == 'CASE':
        if column.props:
            for prop in column.props:
                result += ' %s' % encode_column_dremio(prop)
    else:
        if column.props or column.func:
            result += '('
            for prop in column.props:
                result += encode_column_dremio(prop)
            result += ')'

    result = _encode_prop(column, result)

    if specifier == 'CASE':
        result += ' END'
    if column.alias:
        result += ' AS `%s`' % column.alias
    return result


def encode_column(column: Prop) -> str:
    result = ''
    specifier = column.specifier.upper() if column.specifier else ''
    if specifier:
        result = '%s ' % specifier

    if column.operator:
        result += column.operator.upper()

    if column.func:
        result += column.func.upper()

    if specifier == 'WHEN':
        if not column.conditions:
            raise BadCaseWhenDefinition("WHEN缺少conditions")

        result += ' %s ' % encode_condition(column.conditions)

        if not column.props or len(column.props) != 1:
            raise BadCaseWhenDefinition("THEN的props必须有且只有一个元素")

        prop = column.props[0]
        result += 'THEN %s' % encode_column(prop)

    elif specifier == 'CASE':
        if column.props:
            for prop in column.props:
                result += ' %s' % encode_column(prop)
    else:
        if column.props or column.func:
            result = _encode_column_props_and_func(result, column)

    result = _encode_prop(column, result)

    if specifier == 'CASE':
        result += ' END'
    if column.alias:
        result += ' AS `%s`' % column.alias
    return result


def _encode_column_props_and_func(result, column):
    result += '('
    # mysql group_concat函数内需要使用distinct, 但是oracle,mssql不需要，所以特殊处理
    if column.func == "group_concat":
        result += 'distinct('
    for prop in column.props:
        result += encode_column(prop)
    if column.func == "group_concat":
        result += ')'
    result += ')'
    return result


def _encode_presto_column_props_and_func(result, column):
    result += '('
    # mysql group_concat函数内需要使用distinct, 但是oracle,mssql不需要，所以特殊处理
    if column.func == "group_concat":
        result += 'distinct('
    for prop in column.props:
        result += encode_presto_column(prop)
    if column.func == "group_concat":
        result += ')'
    result += ')'
    return result


def _trans_date_time(value):
    if value == "yyyy-mm-dd hh24:mi:ss":
        return "varchar(19),"
    elif value == "yyyy-mm-dd hh24:mi":
        return "varchar(16),"
    elif value == "yyyy-mm-dd hh24":
        return "varchar(13),"
    elif value == "yyyy-mm-dd":
        return "varchar(10),"
    elif value == "yyyy-mm":
        return "varchar(7),"
    elif value == "yyyy":
        return "varchar(4),"
    return None


def encode_mssql_column(column: Prop) -> str:
    result = ''

    column_copy = deepcopy(column)
    specifier = column.specifier.upper() if column.specifier else ''
    if specifier:
        result = '%s ' % specifier

    if column.operator:
        result += column.operator.upper()

    if column.func:
        result += column.func.upper()

    if specifier == 'WHEN':
        if not column.conditions:
            raise BadCaseWhenDefinition("WHEN缺少conditions")

        result += ' %s ' % encode_mssql_condition(column.conditions)

        if not column.props or len(column.props) != 1:
            raise BadCaseWhenDefinition("THEN的props必须有且只有一个元素")

        prop = column.props[0]
        result += 'THEN %s' % encode_mssql_column(prop)

    elif specifier == 'CASE':
        if column.props:
            for prop in column.props:
                result += ' %s' % encode_mssql_column(prop)
    else:
        if column.props or column.func:
            result += '('
            for prop in column_copy.props:
                if column.func == "CONVERT" and prop.value:
                    result = result.replace("CONVERT(", f"CONVERT({_trans_date_time(prop.value)}")
                    prop.value = 120
                result += encode_mssql_column(prop)
            result += ')'

    result = _encode_mssql_prop(column_copy, result)

    if specifier == 'CASE':
        result += ' END'
    if column.alias:
        result += ' AS [%s]' % column.alias
    return result


def encode_oracle_column(column: Prop) -> str:
    result = ''
    specifier = column.specifier.upper() if column.specifier else ''
    if specifier:
        result = '%s ' % specifier

    if column.operator:
        result += column.operator.upper()

    if column.func:
        result += column.func.upper()

    if specifier == 'WHEN':
        if not column.conditions:
            raise BadCaseWhenDefinition("WHEN缺少conditions")

        result += ' %s ' % encode_oracle_condition(column.conditions)

        if not column.props or len(column.props) != 1:
            raise BadCaseWhenDefinition("THEN的props必须有且只有一个元素")

        prop = column.props[0]
        result += 'THEN %s' % encode_oracle_column(prop)

    elif specifier == 'CASE':
        if column.props:
            for prop in column.props:
                result += ' %s' % encode_oracle_column(prop)
    else:
        if column.props or column.func:
            result += '('
            for prop in column.props:
                result += encode_oracle_column(prop)
            result += ')'
            # 兼容不容版本的oracle
            if column.func == "wm_concat":
                result = f"to_char({result})"

    result = _encode_oracle_prop(column, result)

    if specifier == 'CASE':
        result += ' END'
    if column.alias:
        result += ' AS "%s"' % column.alias
    return result


def encode_presto_column(column: SelectWithExtra) -> str:
    result = ''
    specifier = column.specifier.upper() if column.specifier else ''
    if specifier:
        result = '%s ' % specifier

    if column.operator:
        result += column.operator.upper()

    if column.func:
        result += column.func.upper()

    if specifier == 'WHEN':
        if not column.conditions:
            raise BadCaseWhenDefinition("WHEN缺少conditions")

        result += ' %s ' % encode_presto_condition(column.conditions)

        if not column.props or len(column.props) != 1:
            raise BadCaseWhenDefinition("THEN的props必须有且只有一个元素")

        prop = column.props[0]
        result += 'THEN %s' % encode_presto_column(prop)

    elif specifier == 'CASE':
        if column.props:
            for prop in column.props:
                result += ' %s' % encode_presto_column(prop)
    else:
        if column.props or column.func:
            result = _encode_presto_column_props_and_func(result, column)

    result = _encode_presto_prop(column, result)

    if specifier == 'CASE':
        result += ' END'
    if column.alias:
        result += ' AS "%s"' % column.alias
    return result


def deal_column_specifier(specifier: str, column: Prop, result: str) -> str:
    if specifier == 'WHEN':
        if not column.conditions:
            raise BadCaseWhenDefinition("WHEN缺少conditions")

        result += ' %s ' % encode_condition(column.conditions)

        if not column.props or len(column.props) != 1:
            raise BadCaseWhenDefinition("THEN的props必须有且只有一个元素")

        prop = column.props[0]
        result += 'THEN %s' % encode_presto_column(prop)

    elif specifier == 'CASE':
        if column.props:
            for prop in column.props:
                result += ' %s' % encode_presto_column(prop)
    else:
        result = deal_normal_column(column, result)
    return result


def deal_normal_column(column: Prop, result: str) -> str:
    if column.props or column.func:
        result += '('
        for prop in column.props:
            result += encode_presto_column(prop)
        result += ')'
    return result


def _encode_presto_condition(_condition: Conditions) -> str:
    column_left, column_right, operator, logical_relation, conditions = (
        _condition.left,
        _condition.right,
        _condition.operator,
        _condition.logical_relation,
        _condition.conditions,
    )

    result = ''
    if logical_relation:
        result += ' {} '.format(logical_relation.upper())

    if conditions:
        result += '('
        for i, cond in enumerate(conditions):
            if i == 0 and cond.logical_relation:
                raise BadConditionDefinition("conditions第一个对象不能有logical_relation")
            result += _encode_presto_condition(cond)
        result += ')'

        return result

    if column_left is None or column_right is None:
        raise BadConditionDefinition("left, right不能为空")

    left = encode_presto_column(column_left)
    right = encode_presto_column(column_right)

    return result + '{left} {opt} {right}'.format(left=left, opt=operator or '', right=right)


def _encode_prop(column: Prop, result: str) -> str:
    # 先判断prop_raw是否有值，优先取prop_raw

    if hasattr(column, 'prop_raw') and column.prop_raw:
        result += getattr(column, 'prop_raw', '')
    elif column.prop_name:
        if column.obj_name:
            result += '`{tb}`.'.format(tb=column.obj_name)
        if column.prop_name == '*':
            result += '*'
        else:
            result += '`%s`' % column.prop_name
    elif not column.func and not column.props:
        if column.value is None:
            result += ' NULL'
        elif isinstance(column.value, (list, tuple)):
            if column.operator and column.operator.upper() == 'AND':
                if len(column.value) != 2:
                    raise BadConditionDefinition("between的数组长度必须等于2")
                result = '%s %s %s' % (_wrapper_value(column.value[0]), result, _wrapper_value(column.value[1]))
            else:
                # # mysql in item限制最多2000个
                # column.value = column.value[:2000]
                result += '(%s)' % ','.join([_wrapper_value(v) for v in column.value])
        elif column.value_type == '变量':  # 变量不做处理，调用的时候会统一使用参数替换掉
            result += column.value
        else:
            result += _wrapper_value(column.value)

    return result


def _encode_presto_prop(column: Prop, result: str) -> str:
    # 先判断prop_raw是否有值，优先取prop_raw

    if hasattr(column, 'prop_raw') and column.prop_raw:
        result += getattr(column, 'prop_raw', '')
    elif column.prop_name:
        if column.obj_name:
            result += '"{tb}".'.format(tb=column.obj_name)
        if column.prop_name == '*':
            result += '*'
        else:
            result += '"%s"' % column.prop_name
    elif not column.func and not column.props:
        if column.value is None:
            result += ' NULL'
        elif isinstance(column.value, (list, tuple)):
            if column.operator and column.operator.upper() == 'AND':
                if len(column.value) != 2:
                    raise BadConditionDefinition("between的数组长度必须等于2")
                result = '%s %s %s' % (_wrapper_value(column.value[0]), result, _wrapper_value(column.value[1]))
            else:
                result += '(%s)' % ','.join([_wrapper_value(v) for v in column.value])
        elif column.value_type == '变量':  # 变量不做处理，调用的时候会统一使用参数替换掉
            result += column.value
        else:
            result += _wrapper_value(column.value)

    return result


def deal_column_not_func_and_props(column: Prop, result: str) -> str:
    if column.value is None:
        result += ' NULL'
    elif isinstance(column.value, (list, tuple)):
        if column.operator and column.operator.upper() == 'AND':
            if len(column.value) != 2:
                raise BadConditionDefinition("between的数组长度必须等于2")
            result = '%s %s %s' % (_wrapper_value(column.value[0]), result, _wrapper_value(column.value[1]))
        else:
            result += '(%s)' % ','.join([_wrapper_value(v) for v in column.value])
    elif column.value_type == '变量':  # 变量不做处理，调用的时候会统一使用参数替换掉
        result += column.value
    else:
        result += _wrapper_value(column.value)
    return result


def deal_column_has_prop_name(column: Prop, result: str) -> str:
    if column.obj_name:
        result += '`{tb}`.'.format(tb=column.obj_name)
    if column.prop_name == '*':
        result += '*'
    else:
        result += '`%s`' % column.prop_name
    return result


def _encode_oracle_prop(column: Prop, result: str) -> str:
    # 先判断prop_raw是否有值，优先取prop_raw

    if hasattr(column, 'prop_raw') and column.prop_raw:
        result += getattr(column, 'prop_raw', '')
    elif column.prop_name:
        if column.obj_name:
            result += '"{tb}".'.format(tb=column.obj_name)
        if column.prop_name == '*':
            result += '*'
        else:
            result += '"%s"' % column.prop_name
    elif not column.func and not column.props:
        if column.value is None:
            result += ' NULL'
        elif isinstance(column.value, (list, tuple)):
            if column.operator and column.operator.upper() == 'AND':
                if len(column.value) != 2:
                    raise BadConditionDefinition("between的数组长度必须等于2")
                result = '%s %s %s' % (_wrapper_value(column.value[0]), result, _wrapper_value(column.value[1]))
            else:
                result += '(%s)' % ','.join([_wrapper_value(v) for v in column.value])
        elif column.value_type == '变量':  # 变量不做处理，调用的时候会统一使用参数替换掉
            result += column.value
        else:
            result += _wrapper_value(column.value)

    return result


def _encode_mssql_prop(column: Prop, result: str) -> str:
    # 先判断prop_raw是否有值，优先取prop_raw

    if hasattr(column, 'prop_raw') and column.prop_raw:
        result += getattr(column, 'prop_raw', '')
    elif column.prop_name:
        if column.obj_name:
            result += '[{tb}].'.format(tb=column.obj_name)
        if column.prop_name == '*':
            result += '*'
        else:
            # 处理子查询问题
            if column.prop_name.strip().startswith("("):
                result = '%s' % column.prop_name
            else:
                result += '[%s]' % column.prop_name
    elif not column.func and not column.props:
        if column.value is None:
            result += ' NULL'
        elif isinstance(column.value, (list, tuple)):
            if column.operator and column.operator.upper() == 'AND':
                if len(column.value) != 2:
                    raise BadConditionDefinition("between的数组长度必须等于2")
                result = '%s %s %s' % (_wrapper_value(column.value[0]), result, _wrapper_value(column.value[1]))
            else:
                # # sqlserver in item 限制最多 1000个
                # column.value = column.value[:1000]
                result += '(%s)' % ','.join([_wrapper_value(v) for v in column.value])
        elif column.value_type == '变量':  # 变量不做处理，调用的时候会统一使用参数替换掉
            result += column.value
        else:
            result += _wrapper_value(column.value)

    return result


def _encode_condition(_condition: Conditions) -> str:
    column_left, column_right, operator, logical_relation, conditions = (
        _condition.left,
        _condition.right,
        _condition.operator,
        _condition.logical_relation,
        _condition.conditions,
    )

    result = ''
    if logical_relation:
        result += ' {} '.format(logical_relation.upper())

    if conditions:
        result += '('
        for i, cond in enumerate(conditions):
            if i == 0 and cond.logical_relation:
                raise BadConditionDefinition("conditions第一个对象不能有logical_relation")
            result += _encode_condition(cond)
        result += ')'

        return result

    if column_left is None or column_right is None:
        raise BadConditionDefinition("left, right不能为空")

    left = encode_column(column_left)
    right = encode_column(column_right)

    return result + '{left} {opt} {right}'.format(left=left, opt=operator or '', right=right)


def _encode_mssql_condition(_condition: Conditions) -> str:
    column_left, column_right, operator, logical_relation, conditions = (
        _condition.left,
        _condition.right,
        _condition.operator,
        _condition.logical_relation,
        _condition.conditions,
    )

    result = ''
    if logical_relation:
        result += ' {} '.format(logical_relation.upper())

    if conditions:
        result += '('
        for i, cond in enumerate(conditions):
            if i == 0 and cond.logical_relation:
                raise BadConditionDefinition("conditions第一个对象不能有logical_relation")
            result += _encode_mssql_condition(cond)
        result += ')'

        return result

    if column_left is None or column_right is None:
        raise BadConditionDefinition("left, right不能为空")

    left = encode_mssql_column(column_left)
    right = encode_mssql_column(column_right)

    return result + '{left} {opt} {right}'.format(left=left, opt=operator or '', right=right)


def _encode_oracle_condition(_condition: Conditions) -> str:
    column_left, column_right, operator, logical_relation, conditions = (
        _condition.left,
        _condition.right,
        _condition.operator,
        _condition.logical_relation,
        _condition.conditions,
    )

    result = ''
    if logical_relation:
        result += ' {} '.format(logical_relation.upper())

    if conditions:
        result += '('
        for i, cond in enumerate(conditions):
            if i == 0 and cond.logical_relation:
                raise BadConditionDefinition("conditions第一个对象不能有logical_relation")
            result += _encode_oracle_condition(cond)
        result += ')'

        return result

    if column_left is None or column_right is None:
        raise BadConditionDefinition("left, right不能为空")

    left = encode_oracle_column(column_left)
    right = encode_oracle_column(column_right)

    return result + '{left} {opt} {right}'.format(left=left, opt=operator or '', right=right)


def _encode_condition_dremio(_condition: Conditions, exist_conditons_data: list = None) -> [str, list]:
    """
    对dremio单独进行修改
    dremio 不能有两个一模一样的IN条件(字段，IN 条件顺序和值完全一样的情况)，此时过滤将失效，因此需要去除一个
    :param _condition:
    :param exist_conditons_data:
    :return:
    """
    column_left, column_right, operator, logical_relation, conditions = (
        _condition.left,
        _condition.right,
        _condition.operator,
        _condition.logical_relation,
        _condition.conditions,
    )
    exist_conditons_data = exist_conditons_data or []
    result = ''

    if conditions:
        if logical_relation:
            result += ' {XX_LOGICAL_RELATION} '
        result += '{XX_LEFT_BRACKET}'
        compare_result = result
        for i, cond in enumerate(conditions):
            if i == 0 and cond.logical_relation:
                raise BadConditionDefinition("conditions第一个对象不能有logical_relation")
            re_, exist_conditons_data = _encode_condition_dremio(cond, exist_conditons_data)
            result += re_
        if compare_result != result:
            result = result.format(XX_LOGICAL_RELATION=logical_relation.upper(), XX_LEFT_BRACKET="(")
            result += "{XX_RIGHT_BRACKET}".format(XX_RIGHT_BRACKET=")")
        else:
            result = result.format(XX_LOGICAL_RELATION=" ", XX_LEFT_BRACKET=" ")
        return result, exist_conditons_data

    if column_left is None or column_right is None:
        raise BadConditionDefinition("left, right不能为空")

    left = encode_column_dremio(column_left)
    right = encode_column_dremio(column_right)

    if operator == 'IN':
        condition_identifier = tuple([left, right])
        if condition_identifier in exist_conditons_data:
            return result, exist_conditons_data
        exist_conditons_data.append(condition_identifier)
    if logical_relation:
        result += ' {} '.format(logical_relation.upper())
    return result + '{left} {opt} {right}'.format(left=left, opt=operator or '', right=right), exist_conditons_data


def encode_condition(conditions: List[Conditions]) -> str:
    if not conditions:
        return ''

    result = ''
    for cond in conditions:
        result += _encode_condition(cond)

    return result


def encode_mssql_condition(conditions: List[Conditions]) -> str:
    if not conditions:
        return ''

    result = ''
    for cond in conditions:
        result += _encode_mssql_condition(cond)

    return result


def encode_oracle_condition(conditions: List[Conditions]) -> str:
    if not conditions:
        return ''

    result = ''
    for cond in conditions:
        result += _encode_oracle_condition(cond)

    return result


def encode_presto_condition(conditions: List[Conditions]) -> str:
    if not conditions:
        return ''

    result = ''
    for cond in conditions:
        result += _encode_presto_condition(cond)

    return result


def encode_condition_dremio(conditions: List[Conditions]) -> str:
    if not conditions:
        return ''

    result = ''
    exist_conditons_data = []
    for cond in conditions:
        re_, exist_conditons_data = _encode_condition_dremio(cond, exist_conditons_data)
        result += re_
    return result


def encode_select(columns: List[Select]) -> str:
    """
    [ref#Column]
    """

    return 'SELECT %s' % ','.join([encode_column(col) for col in columns])


def encode_mssql_select(columns: List[Select]) -> str:
    """
    [ref#Column]
    """

    return 'SELECT %s' % ','.join([encode_mssql_column(col) for col in columns])


def encode_oracle_select(columns: List[Select]) -> str:
    """
    [ref#Column]
    """

    return 'SELECT %s' % ','.join([encode_oracle_column(col) for col in columns])



def encode_presto_select(columns: List[Select]) -> str:
    """
        [ref#Column]
        """

    return 'SELECT %s' % ','.join([encode_presto_column(col) for col in columns])


def encode_from(from_objects: List[Object]) -> str:
    """
    [{
        "name": "表名1",
        "join_type": "",
        "ref_clause": ref#Condition
    }]
    """

    if not from_objects:
        return ''
    obj = from_objects[0]
    object_name = '%s%s' % (obj.name, ' `%s`' % obj.alias if obj.alias else '')
    result = 'FROM %s ' % object_name

    if len(from_objects) == 1:
        return result

    for from_ in from_objects[1:]:
        # 先判断prop_raw是否有值，优先取prop_raw
        if hasattr(from_, 'prop_raw') and from_.prop_raw:
            name = getattr(from_, 'prop_raw', '')
        else:
            name = from_.name
        result += ' {join_type} JOIN {tb}{alias} ON {left_condition}'.format(
            join_type=from_.join_type.upper(),
            tb=name,
            alias=' `%s`' % from_.alias if from_.alias else '',
            left_condition=encode_condition(from_.ref_clause),
        )

    return result


def encode_presto_from(from_objects: List[Object]) -> str:
    """
    [{
        "name": "表名1",
        "join_type": "",
        "ref_clause": ref#Condition
    }]
    """

    if not from_objects:
        return ''
    obj = from_objects[0]
    object_name = '%s%s' % (obj.name, ' `%s`' % obj.alias if obj.alias else '')
    result = 'FROM %s ' % object_name

    if len(from_objects) == 1:
        return result

    for from_ in from_objects[1:]:
        # 先判断prop_raw是否有值，优先取prop_raw
        if hasattr(from_, 'prop_raw') and from_.prop_raw:
            name = getattr(from_, 'prop_raw', '')
        else:
            name = from_.name
        result += ' {join_type} JOIN {tb}{alias} ON {left_condition}'.format(
            join_type=from_.join_type.upper(),
            tb=name,
            alias=' `%s`' % from_.alias if from_.alias else '',
            left_condition=encode_condition(from_.ref_clause),
        )

    return result


def encode_presto_groupby(columns: List[Group]) -> str:
    if not columns:
        return ''

    return 'GROUP BY %s' % ','.join([encode_presto_column(col) for col in columns])


def encode_presto_having(conditions):
    if not conditions:
        return ''
    return 'HAVING %s' % encode_presto_condition(conditions)


def encode_presto_orderby(orders: List[Order]) -> str:
    if not orders:
        return ''

    return 'ORDER BY %s' % ','.join(
        [
            '{col} {sort}'.format(col=encode_presto_column(order), sort=order.method.upper() if order.method else "ASC")
            for order in orders
        ]
    )


def encode_mssql_from(from_objects: List[Object]) -> str:
    """
    [{
        "name": "表名1",
        "join_type": "",
        "ref_clause": ref#Condition
    }]
    """

    if not from_objects:
        return ''
    obj = from_objects[0]
    object_name = '%s%s' % (obj.name, ' [%s]' % obj.alias if obj.alias else '')
    result = 'FROM %s ' % object_name

    if len(from_objects) == 1:
        return result

    for from_ in from_objects[1:]:
        # 先判断prop_raw是否有值，优先取prop_raw
        if hasattr(from_, 'prop_raw') and from_.prop_raw:
            name = getattr(from_, 'prop_raw', '')
        else:
            name = from_.name
        result += ' {join_type} JOIN {tb}{alias} ON {left_condition}'.format(
            join_type=from_.join_type.upper(),
            tb=name,
            alias=' [%s]' % from_.alias if from_.alias else '',
            left_condition=encode_mssql_condition(from_.ref_clause),
        )

    return result


def encode_oracle_from(from_objects: List[Object]) -> str:
    """
    [{
        "name": "表名1",
        "join_type": "",
        "ref_clause": ref#Condition
    }]
    """

    if not from_objects:
        return ''
    obj = from_objects[0]
    if obj.name.strip().startswith("("):
        object_name = '%s%s' % (' %s ' % obj.name, ' "%s"' % obj.alias if obj.alias else '')
    else:
        object_name = '%s%s' % (' "%s" ' % obj.name, ' "%s"' % obj.alias if obj.alias else '')
    result = 'FROM %s ' % object_name

    if len(from_objects) == 1:
        return result

    for from_ in from_objects[1:]:
        # 先判断prop_raw是否有值，优先取prop_raw
        if hasattr(from_, 'prop_raw') and from_.prop_raw:
            name = getattr(from_, 'prop_raw', '')
        else:
            name = from_.name
        result += ' {join_type} JOIN {tb}{alias} ON {left_condition}'.format(
            join_type=from_.join_type.upper(),
            tb=name,
            alias=' "%s"' % from_.alias if from_.alias else '',
            left_condition=encode_oracle_condition(from_.ref_clause),
        )

    return result


def encode_groupby(columns: List[Group]) -> str:
    if not columns:
        return ''

    return 'GROUP BY %s' % ','.join([encode_column(col) for col in columns])


def encode_mssql_groupby(columns: List[Group]) -> str:
    if not columns:
        return ''

    return 'GROUP BY %s' % ','.join([encode_mssql_column(col) for col in columns])


def encode_oracle_groupby(columns: List[Group]) -> str:
    if not columns:
        return ''

    return 'GROUP BY %s' % ','.join([encode_oracle_column(col) for col in columns])


def encode_having(conditions):
    if not conditions:
        return ''
    return 'HAVING %s' % encode_condition(conditions)


def encode_mssql_having(conditions):
    if not conditions:
        return ''
    return 'HAVING %s' % encode_mssql_condition(conditions)


def encode_oracle_having(conditions):
    if not conditions:
        return ''
    return 'HAVING %s' % encode_oracle_condition(conditions)


def encode_orderby(orders: List[Order]) -> str:
    if not orders:
        return ''

    return 'ORDER BY %s' % ','.join(
        [
            '{col} {sort}'.format(col=encode_column(order), sort=order.method.upper() if order.method else "ASC")
            for order in orders
        ]
    )


def encode_mssql_orderby(orders: List[Order]) -> str:
    if not orders:
        return ''

    return 'ORDER BY %s' % ','.join(
        [
            '{col} {sort}'.format(col=encode_mssql_column(order), sort=order.method.upper() if order.method else "ASC")
            for order in orders
        ]
    )


def encode_oracle_orderby(orders: List[Order]) -> str:
    if not orders:
        return ''

    return 'ORDER BY %s' % ','.join(
        [
            '{col} {sort}'.format(col=encode_oracle_column(order), sort=order.method.upper() if order.method else "ASC")
            for order in orders
        ]
    )


def verification_query(query: QueryStructure) -> None:
    if not query.select or not isinstance(query.select, list):
        raise BadColumnDefinition("缺少select或select不是list")

    if not query.object or not isinstance(query.object, list):
        raise BadQueryDefinition("缺少object或object不是list")

    if query.group_by and not isinstance(query.group_by, list):
        raise BadQueryDefinition("group_by不是list")

    if query.having and not isinstance(query.having, list):
        raise BadQueryDefinition("having不是list")

    if query.order_by and not isinstance(query.order_by, list):
        raise BadQueryDefinition("order_by不是list")


class ModelEncoder(json.JSONEncoder):
    def default(  # pylint: disable=E0202
            self, o: Union[Limit, QueryStructure, Conditions, Object, Prop]
    ) -> Union[str, Any]:
        if isinstance(o, Model):
            return o.get_dict()
        elif isinstance(o, datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(o, date):
            return o.strftime('%Y-%m-%d')
        else:
            return o


SELECT_MAP = {
    DBEngine.MSSQL.value: encode_mssql_select,
    DBEngine.ORACLE.value: encode_oracle_select,
    DBEngine.DM.value: encode_oracle_select,
    DBEngine.SqlServer.value: encode_mssql_select,
    DBEngine.RDS.value: encode_select,
    DBEngine.Dremio.value: encode_select,
    DBEngine.PG.value: encode_select,
    DBEngine.PRESTO.value: encode_presto_select,
}

FROM_MAP = {
    DBEngine.MSSQL.value: encode_mssql_from,
    DBEngine.ORACLE.value: encode_oracle_from,
    DBEngine.DM.value: encode_oracle_from,
    DBEngine.SqlServer.value: encode_mssql_from,
    DBEngine.RDS.value: encode_from,
    DBEngine.Dremio.value: encode_from,
    DBEngine.PG.value: encode_from,
    DBEngine.PRESTO.value: encode_presto_from,
}

WHERE_MAP = {
    DBEngine.MSSQL.value: encode_mssql_condition,
    DBEngine.ORACLE.value: encode_oracle_condition,
    DBEngine.DM.value: encode_oracle_condition,
    DBEngine.SqlServer.value: encode_mssql_condition,
    DBEngine.RDS.value: encode_condition,
    DBEngine.Dremio.value: encode_condition_dremio,
    DBEngine.PG.value: encode_condition,
    DBEngine.PRESTO.value: encode_presto_condition,
}

GROUP_BY_MAP = {
    DBEngine.MSSQL.value: encode_mssql_groupby,
    DBEngine.ORACLE.value: encode_oracle_groupby,
    DBEngine.DM.value: encode_oracle_groupby,
    DBEngine.SqlServer.value: encode_mssql_groupby,
    DBEngine.RDS.value: encode_groupby,
    DBEngine.Dremio.value: encode_groupby,
    DBEngine.PRESTO.value: encode_presto_groupby,
}

HAVING_MAP = {
    DBEngine.MSSQL.value: encode_mssql_having,
    DBEngine.ORACLE.value: encode_oracle_having,
    DBEngine.DM.value: encode_oracle_having,
    DBEngine.SqlServer.value: encode_mssql_having,
    DBEngine.RDS.value: encode_having,
    DBEngine.Dremio.value: encode_having,
    DBEngine.PG.value: encode_having,
    DBEngine.PRESTO.value: encode_presto_having,
}

ORDER_BY_MAP = {
    DBEngine.MSSQL.value: encode_mssql_orderby,
    DBEngine.ORACLE.value: encode_oracle_orderby,
    DBEngine.DM.value: encode_oracle_orderby,
    DBEngine.SqlServer.value: encode_mssql_orderby,
    DBEngine.RDS.value: encode_orderby,
    DBEngine.Dremio.value: encode_orderby,
    DBEngine.PG.value: encode_orderby,
    DBEngine.PRESTO.value: encode_presto_orderby,
}


def encode_query(query: QueryStructure, db_engine: str = 'rds') -> str:
    """encode QueryStructures to sql
    Args:
        query (query_models.QueryStructure): structure
        db_engine
    """
    # 'select', 'object', 'where', 'group_by', 'having', 'order_by', 'limit'

    # SELECT is_end + activity.is_end as c FROM activity GROUP BY c  HAVING c>= 2 ORDER BY c DESC LIMIT 0, 2

    # 校验数据完整性
    verification_query(query)
    if db_engine in [DBEngine.RDS.value, DBEngine.Mysql.value]:
        query = MysqlEngineTransfer().adapter_mysql_engine(query)
    elif db_engine == DBEngine.PG.value:
        query = PostGreSqlEngineTransfer().adapter_postgresql_engine(query)
    elif db_engine == DBEngine.Dremio.value:
        query = DremioEngineTransfer().adapter_engine(query)
    elif db_engine in [DBEngine.MSSQL.value, DBEngine.SqlServer.value]:
        query = MssqlEngineTransfer().adapter_mssql_engine(query)
    elif db_engine == DBEngine.ORACLE.value:
        query = OracleEngineTransfer().adapter_oracle_engine(query)
    elif db_engine == DBEngine.DM.value:
        query = DMEngineTransfer().adapter_dm_engine(query)
    elif db_engine == DBEngine.PRESTO.value:
        query = PrestoEngineTransfer().adapter_engine(query)

    parties = [
        SELECT_MAP.get(db_engine, encode_select)(query.select),
        FROM_MAP.get(db_engine, encode_from)(query.object),
    ]
    if query.where:
        parties.append('WHERE %s' % WHERE_MAP.get(db_engine, encode_condition)(query.where))

    if query.group_by:
        parties.append(GROUP_BY_MAP.get(db_engine, encode_groupby)(query.group_by))

    if query.having:
        parties.append(HAVING_MAP.get(db_engine, encode_having)(query.having))

    if query.order_by:
        parties.append(ORDER_BY_MAP.get(db_engine, encode_orderby)(query.order_by))

    if query.limit and hasattr(query.limit, 'row') and query.limit.row:
        if not isinstance(query.limit.row, int) or int(query.limit.row) <= 0:
            raise BadQueryDefinition("limit.row必须是正整数")
        query.limit.offset = 0 if query.limit.offset is None else int(query.limit.offset)
        if db_engine in (DBEngine.PG.value, DBEngine.Dremio.value):
            parties.append('LIMIT %d offset %d' % (query.limit.row, query.limit.offset))
        elif db_engine == DBEngine.PRESTO.value:
            if query.order_by:
                parties.append('OFFSET %d FETCH NEXT %d ROWS WITH TIES' % (query.limit.offset, query.limit.row))
            else:
                parties.append('OFFSET %d FETCH NEXT %d ROWS ONLY' % (query.limit.offset, query.limit.row))
        elif db_engine in [DBEngine.ORACLE.value, DBEngine.MSSQL.value, DBEngine.SqlServer.value, DBEngine.DM.value]:
            # oracle没有limit offset, 使用rownum在where条件中处理
            pass
        else:
            parties.append('LIMIT %d, %d' % (query.limit.offset, query.limit.row))

    sql = ' '.join(parties)
    sql = process_db_engine_sql_compatible(sql, query, db_engine)
    # oracle rownum需要特殊处理，不能加双引号
    if db_engine == DBEngine.ORACLE.value or db_engine == DBEngine.DM.value:
        sql = sql.replace('"rownum"', 'rownum')
    return sql


def process_db_engine_sql_compatible(sql: str, query_structure: QueryStructure, db_engine: str = 'rds'):
    if db_engine in (DBEngine.PG.value, DBEngine.MSSQL.value, DBEngine.Dremio.value, DBEngine.PRESTO.value):
        if db_engine == DBEngine.PRESTO.value:
            sql = replace_quotation(sql, '"')
        else:
            sql = replace_quotation(sql)
        # dremio 子查询时不需要进行替换双引号
        if db_engine in [DBEngine.Dremio.value, DBEngine.PRESTO.value]:
            return sql
        table_alias_names = []
        for r in query_structure.object:
            table_alias_names.append(r.alias or r.name)
        for alias_name in table_alias_names:
            if not alias_name or alias_name.find('(') < 0:
                continue
            sql = sql.replace(alias_name, '"' + alias_name + '"')
    if all(
            [
                db_engine in [DBEngine.MSSQL.value, DBEngine.SqlServer.value],
                query_structure.limit,
                hasattr(query_structure.limit, 'row') and query_structure.limit.row,
            ]
    ):
        _sql = sql
        sql = re.sub(r"^SELECT[\s\n]+", f"SELECT TOP {query_structure.limit.row} ", sql)
        if hasattr(query_structure.limit, 'offset'):
            sql = generate_mssql_paginate_sql(query_structure, _sql)

    return sql


def generate_mssql_paginate_sql(query_structure, sql) -> str:
    # 通过row_number()函数分页，随便根据一个字段排序
    limit = query_structure.limit.row
    offset = query_structure.limit.offset
    # 提取出原先的select
    origin_select = ', '.join(['[%s]' % (s.alias or s.prop_name) for s in query_structure.select])
    # order_column = '[%s]' % query_structure.select[0].alias
    # inner_table = re.sub(r"^SELECT[\s\n]+",
    # "SELECT TOP (100) PERCENT ROW_NUMBER() OVER(Order by %s) AS RowId, " % order_column, _sql)
    if query_structure.order_by:
        order_by = encode_mssql_orderby(query_structure.order_by)
    else:
        order_by = "ORDER BY (select 0)"
    inner_table = re.sub(
        r"^SELECT[\s\n]+", "SELECT TOP (100) PERCENT ROW_NUMBER() OVER(" + order_by + ") AS RowId, ", sql
    )
    sql_with_row_number = """select %s from (%s) as wrapped_table where RowId > %s and RowId <= %s""" % (
        origin_select,
        inner_table,
        offset,
        offset + limit,
    )
    return sql_with_row_number


def handler(environ, start_response):
    """
    阿里云函数计算
    :param environ:
    :param start_response:
    :return:
    """
    try:
        request_body_size = int(environ.get('CONTENT_LENGTH', 0))
    except ValueError:
        request_body_size = 0
    request_body = environ['wsgi.input'].read(request_body_size)

    status = '200 OK'
    response_headers = [('Content-type', 'application/json')]
    query_structure_json = request_body.decode('utf-8')

    errmsg = ''
    sql_str = ''
    _query_structure = None
    try:
        data = json.loads(query_structure_json)
        _query_structure = dict_to_model(data)
    except Exception as e:
        errmsg = "json格式错误: %s" % str(e)

    if errmsg == '' and _query_structure is not None:
        # try:
        sql_str = encode_query(_query_structure)
        # except Exception as e:
        # errmsg = "解析结构到sql失败: %s" % str(e)

    result = {"errmsg": errmsg, "sql": sql_str}

    start_response(status, response_headers)
    return [json.dumps(result).encode('utf-8')]


def parser_date_format(regex, format_str):
    # 统一使用mysql的格式标准进行查找
    # eg: regex: r'%w  format: "%Y-%W-%y"
    #     return: [('%Y-', 1), ('%W', 2), ('-%y', 1)]
    # 0: 纯字符 1：能够使用date_format的 2：周、季度这种特殊类型
    position = []
    matched_str = []
    matched = re.finditer(regex, format_str)
    for m in matched:
        matched_str.append(m.group())
        position.extend(list(m.span()))
    position.insert(0, 0)
    position.append(len(format_str))

    split_str_list = [format_str[position[idx]: position[idx + 1]] for idx in range(0, len(position) - 1)]
    split_str_list = [s for s in split_str_list if s != '']
    result = []
    for split_str in split_str_list:
        # week quarter 这种特殊格式的
        if split_str in matched_str:
            result.append((split_str, 2))
        # 纯字符
        elif '%' not in split_str:
            result.append((split_str, 0))
        # 能够使用date_format的
        else:
            result.append((split_str, 1))
    return result


def revise_props_specifier(props: list):
    # 统一修正props中的specifier
    if props:
        # CONCAT(, DATE_FORMAT(`modified_on`, '%Y-'), QUARTER(`modified_on`), DATE_FORMAT(`modified_on`, '-%y'))
        # 抹掉第一个参数的符号, 不然生成的sql有语法错误
        # 只有第一个参数specifier为空，其他都是,
        props[0].specifier = ''
        revise_props_specifier(props[0].props)

        for other in props[1:]:
            other.specifier = ','
            revise_props_specifier(other.props)


# 区分时间格式化中周、季度
# 因为有的数据库不支持data_format同时格式化年月日周季，所以需要将周季单独区分开
def distinguish_datetime(func):
    @functools.wraps(func)
    def wrapper(data, *args, **kwargs):
        cls_name = func.__qualname__.split('.')[0]
        if not isinstance(data, tuple) and hasattr(data, 'props') and len(data.props) == 2:
            format_str = data.props[1].value
            has_set = False
            if isinstance(format_str, str):
                if '%W' in format_str:
                    data.func = 'week'
                    has_set = True
                elif '%Q' in format_str:
                    data.func = 'quarter'
                    has_set = True

            if has_set and cls_name not in ['MysqlEngineTransfer', 'OracleEngineTransfer', 'DMEngineTransfer', 'MssqlEngineTransfer']:
                raise UserError(code=504, message='当前数据源暂不支持时间维度[%s]周、季度格式化' % data.props[0].prop_name)
                # raise UserError(code=504, message='当前数据源暂不支持时间维度对周、季度格式化')

        return func(data, *args, **kwargs)

    return wrapper


def formatted_sql(sql):
    # 格式化去掉SQL中的多余空格
    sql = sql.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    pis = filter(lambda x: bool(x), sql.split(' '))
    return ' '.join(pis)


class ConstructSelectSubLimit1Query():
    """
    提供公共的构造select列limit1子查询的基础方法
    """

    def __init__(self, engine, data, query: QueryStructure):
        self.data = data
        self.query = query
        self.engine_class = engine.__class__
        self.engine = engine
        self.limit_table_alias = 'limit1_alias'
        self.condition_func = None
        self.column_func = None
        self._set_encode_func()
        self.stop_word_map = {
            MysqlEngineTransfer: ['`', '`'],
            MssqlEngineTransfer: ['[', ']'],
        }

    def _set_encode_func(self):
        m = {
            MysqlEngineTransfer: {'condition': encode_condition, 'column': encode_column},
            MssqlEngineTransfer: {'condition': encode_mssql_condition, 'column': encode_mssql_column}
        }
        if self.engine_class not in m:
            raise UserError(code=504, message='暂时不支持当前的数据源[%s]进行子查询构造' % str(self.engine))

        self.condition_func = m[self.engine_class]['condition']
        self.column_func = m[self.engine_class]['column']

    def update_object_of_where(self, where: list, obj_name: str):
        """
        更新where对象的object
        :param where:
        :param obj_name:
        :return:
        """
        if not where:
            return ''

        for cond in where:
            if cond.left and cond.right:
                cond.left.obj_name = obj_name
                cond.right.obj_name = obj_name
            if cond.conditions:
                self.update_object_of_where(cond.conditions, obj_name)
        return where

    def get_select_field_alias(self, prop_name):
        # 获取字段的别名
        for select in self.query.select or []:
            if select.prop_name == prop_name:
                return select.alias
        return ''

    @property
    def where_sql(self):
        """
        构造where条件，条件来源于外层的where+groupby之和
        :return:
        """
        query = self.query
        left, right = self.stop_word_map[self.engine_class]

        condition_chips = []
        if query.where:
            where = deepcopy(query.where)
            where = self.update_object_of_where(where, self.limit_table_alias)
            condition_chips.append(f"{self.condition_func(where)}")
        # 将外层group by字段转化为子查询的where条件
        if query.group_by:
            for column in query.group_by:
                # 函数型的字段可能会出现异常，函数式的字段会出现多层prop嵌套，不能直接处理
                # 暂时不考虑这种
                # self.engine.date_format(column)
                if column.func:
                    continue
                if column.obj_name:
                    table_alias = column.obj_name
                else:
                    table_alias = query.object[0].alias if query.object[0].alias else query.object[0].name
                prop_name = column.prop_raw if column.prop_raw else column.prop_name
                chip = f"{left}{self.limit_table_alias}{right}.{left}{prop_name}{right}={left}{table_alias}{right}.{left}{prop_name}{right}"
                condition_chips.append(chip)
                # prop_name = column.prop_raw if column.prop_raw else column.prop_name
                # condition_chips.append(f"{self.column_func(column)}={left}{self.get_select_field_alias(prop_name)}{right}")
        if condition_chips:
            where_sql = ' and '.join(condition_chips)
            return f"where {where_sql}"
        else:
            return ''

    @property
    def order_sql(self):
        # 外层的orderby移到子查询上
        query = self.query
        if query.order_by:
            order_by = deepcopy(query.order_by)
            if self.engine_class == MssqlEngineTransfer:
                order_by = encode_mssql_orderby(order_by)
            else:
                order_by = encode_orderby(order_by)
        else:
            order_by = ''
        return order_by

    @property
    def table_name(self):
        # 子查询的表名
        query = self.query
        left, right = self.stop_word_map[self.engine_class]
        return f"{query.object[0].name} as {left}{self.limit_table_alias}{right}"

    @property
    def column_name(self):
        # 子查询的字段名
        if self.data.props[0].prop_raw:
            # 是高级字段
            column_name = self.data.props[0].prop_raw
        else:
            # 是普通字段
            column_name = self.data.props[0].prop_name
        return column_name


class MysqlEngineTransfer:

    def _set_query_structure(self, query_structure):
        if not hasattr(self, 'query_structure'):
            setattr(self, 'query_structure', query_structure)

    def adapter_mysql_engine(self, query_structure):
        """
        适配Mysql查询引擎
        :return:
        """
        self._set_query_structure(query_structure)

        if query_structure.select:
            for row in query_structure.select:
                self.adapter_prop(row)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.adapter_prop(row)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.adapter_prop(row)
        if query_structure.where:
            for row in query_structure.where:
                self.adapter_prop(row)

        return query_structure

    def adapter_prop(self, data):
        """
        适配prop
        :param data:
        :return:
        """

        if data:
            self.date_format(data)
            self.select_field_limit1(data)

            if getattr(data, "props", None):
                for prop in enumerate(data.props):
                    self.adapter_prop(prop)
            if getattr(data, "left", None):
                self.adapter_prop(data.left)
            if getattr(data, "right", None):
                self.adapter_prop(data.right)
            if getattr(data, "conditions", None):
                for condition in data.conditions:
                    self.adapter_prop(condition)

    def select_field_limit1(self, data):
        """
        select字段支持取聚合第一行
        :param data:
        :return:
        """
        query = self.query_structure  # noqa

        if getattr(data, "func", None) and data.func and data.func == "limit1":
            # 构造出select列子查询
            cssq = ConstructSelectSubLimit1Query(self, data, query)
            sub_sql = f"""
            (select `{cssq.column_name}` from {cssq.table_name} {cssq.where_sql} {cssq.order_sql} limit 1)
            """.replace('``', '`')
            data.func = ""
            data.props = []
            data.prop_raw = formatted_sql(sub_sql)
            data.prop_name = ''

    @staticmethod
    @distinguish_datetime
    def date_format(data):
        """
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func in ['week', 'quarter']:
            # %Y-%Q 支持任意格式的格式化，会自动分开组装拼接，生成拼接SQL
            # SQL需要使用字符串拼接, mysql不支持直接格式化
            MysqlEngineTransfer.dynamic_concat_datetime(
                data, format=data.props[1].value, unsupported_format=r'%[WQ]'
            )

    @staticmethod
    def dynamic_concat_datetime(origin_data, format, unsupported_format):
        # 动态的时间格式拼接
        # 对于不能一次完成时间格式化的进行动态的时间props构造和格式化
        # 为什么这样设计，是因为这样支持date_format与week,quarter的动态组合，即一个格式化样式支持动态的去生成不同函数的拼接
        _origin_data = deepcopy(origin_data)

        def mock_raw(val='-'):
            # 独立的无格式的纯字符
            mocked_sep = deepcopy(_origin_data)
            mocked_sep.value = val
            mocked_sep.func = ''
            # mocked_sep.specifier = ','
            mocked_sep.props = []
            mocked_sep.alias = ''
            return mocked_sep

        def mock_week_quarter_col(format_str):
            # 不能直接用date_format格式化的时间格式
            # week/quarter函数只有一个参数，函数不需要参数，先抹掉components/functions.py:17的参数
            mapping = {'%W': 'week', "%Q": 'quarter'}
            mocked_origin = deepcopy(_origin_data)
            mocked_origin.alias = ''
            mocked_origin.func = mapping[format_str]
            # mocked_origin.specifier = ','
            mocked_origin.props = mocked_origin.props[:1]  # 只是取第一个参数，剩下的抹掉, 因为week/quarter可以只传递一个参数
            return mocked_origin

        def mock_date_format_col(format_str):
            # 能直接用date_format格式化的时间格式
            mocked_date_format = deepcopy(_origin_data)
            mocked_date_format.alias = ''
            # mocked_date_format.specifier = ','
            mocked_date_format.func = 'date_format'
            mocked_date_format.props[1].value = format_str
            return mocked_date_format

        split_str_result = parser_date_format(unsupported_format, format)
        origin_data.props = []
        origin_data.func = 'concat'
        for each, _type in split_str_result:
            # week quarter
            if _type == 2:
                origin_data.props.append(mock_week_quarter_col(each))
            # date_format
            elif _type == 1:
                origin_data.props.append(mock_date_format_col(each))
            # 纯字符
            elif _type == 0:
                origin_data.props.append(mock_raw(each))

        # %W %Q （纯date_format或纯week/quarter场景）
        # 处理 CONCAT(QUARTER(`modified_on`)) AS `quarter_MODIFIEDON_4863233286`
        # 如果只有一个prop，并且不是纯字符，就去掉外层的CONCAT
        if len(origin_data.props) == 1 and origin_data.props[0].func:
            _props = origin_data.props[0]
            origin_data.func = _props.func
            origin_data.props = _props.props

        revise_props_specifier(origin_data.props)


class MssqlEngineTransfer:
    def adapter_mssql_engine(self, query_structure):
        """
        适配mssql查询引擎
        :return:
        """
        if query_structure.select:
            for row in query_structure.select:
                self.adapter_prop(row, query_structure)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.adapter_prop(row, query_structure)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.adapter_prop(row, query_structure)
        if query_structure.where:
            for row in query_structure.where:
                self.adapter_prop(row, query_structure)
        return query_structure

    def adapter_prop(self, data, query_structure=None):
        """
        适配prop
        :param data:
        :return:
        """
        if data:
            self.date_format(data)
            self.group_concat(data, query_structure)
            self.select_field_limit1(data, query_structure)

            if getattr(data, "prop_raw", None):
                self.adapter_prop_raw(data)

            if getattr(data, "props", None):
                for prop in data.props:
                    self.adapter_prop(prop)
            if getattr(data, "left", None):
                self.adapter_prop(data.left)
            if getattr(data, "right", None):
                self.adapter_prop(data.right)
            if getattr(data, "conditions", None):
                for condition in data.conditions:
                    self.adapter_prop(condition)

    @staticmethod
    def adapter_prop_raw(data):
        # placeholder = PLACEHOLDER_COMPILE.findall(data.prop_raw)
        # if placeholder:
        #     data.prop_raw = data.prop_raw.replace(f'`{placeholder[0]}`', f"[{placeholder[0]}]")
        week = WEEK_COMPILE.findall(data.prop_raw)
        if week:
            field = week[0]
            data.prop_raw = f"DATEPART(week, {field})"
        quarter = QUARTER_COMPILE.findall(data.prop_raw)
        if quarter:
            field = quarter[0]
            data.prop_raw = f"DATEPART(quarter, {field})"
        date_diff = DAY_DIFF_COMPILE.findall(data.prop_raw)
        if date_diff:
            field = date_diff[0]
            data.prop_raw = f"DATEDIFF(day, {field})"
        hour_diff = HOUR_DIFF_COMPILE.findall(data.prop_raw)
        if hour_diff:
            field = hour_diff[0]
            data.prop_raw = f"DATEDIFF(hh, {field})"
        minute_diff = MINUTE_DIFF_COMPILE.findall(data.prop_raw)
        if minute_diff:
            field = minute_diff[0]
            data.prop_raw = f"DATEDIFF(mi, {field})"
        data.prop_raw = data.prop_raw.replace("CURDATE()", 'convert(varchar(10), getdate(), 121)')
        # sqlserver 双引号代表字段名
        if data.prop_raw.find("REPLACE") != -1:
            data.prop_raw = data.prop_raw.replace('"', "'")
        data.prop_raw = data.prop_raw.replace("SUBSTR", "SUBSTRING")

    def select_field_limit1(self, data, query):
        """
        select字段支持取聚合第一行
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "limit1":
            # 构造出select列子查询
            cssq = ConstructSelectSubLimit1Query(self, data, query)
            sub_sql = f"""
            (select top 1 {cssq.column_name} from {cssq.table_name} {cssq.where_sql} {cssq.order_sql})
            """
            data.func = ""
            data.props = []
            data.prop_raw = formatted_sql(sub_sql)
            data.prop_name = ''

    # @staticmethod
    def group_concat(self, data, query):  # NOSONAR
        """
        group_concat -> 子查询
        :param data: {
            "alias": "sum_ID_3420391965",
            "conditions": [],
            "func": "sum",
            "obj_name": "",
            "operator": "",
            "prop_name": "",
            "prop_raw": "",
            "prop_ref": "",
            "props": [
                {
                    "alias": "",
                    "conditions": [],
                    "func": "",
                    "obj_name": null,
                    "operator": "",
                    "prop_name": "id",
                    "prop_raw": "",
                    "prop_ref": "",
                    "props": [],
                    "specifier": "",
                    "value": ""
                }
            ],
            "specifier": "",
            "value": ""
        }
        :return:
        """
        alias_a = 'group_concat_alias_a'
        group_concat_alias = 'concat_name'

        def deal_cond_obj_name(cond, obj_name):
            if not cond:
                return
            cond.obj_name = obj_name
            if cond.props:
                for prop in cond.props:
                    prop.obj_name = obj_name

        def update_object_of_where(where: list, obj_name: str):
            """
            更新where对象的object
            :param where:
            :param obj_name:
            :return:
            """
            if not where:
                return ''

            for cond in where:
                if cond.left:
                    deal_cond_obj_name(cond.left, obj_name)
                if cond.right:
                    deal_cond_obj_name(cond.right, obj_name)
                if cond.conditions:
                    update_object_of_where(cond.conditions, obj_name)
            return where

        def _generate_where_sql():
            """
            构造where条件
            :return:
            """
            s = ""
            if query.where:
                where = deepcopy(query.where)
                for row in where:
                    self.adapter_prop(row)
                where = update_object_of_where(where, alias_a)
                s += f" and {encode_mssql_condition(where)}"
            # 将group by字段转化为where条件
            if query.group_by:
                for column in query.group_by:
                    if column.obj_name:
                        table_alias = column.obj_name
                    else:
                        table_alias = query.object[0].alias if query.object[0].alias else query.object[0].name
                    if column.func:
                        # 处理嵌套prop结构, 不是纯数据集的字段，先生成外层的函数SQL，内层的函数SQL直接替换表名即可
                        if column.func == 'date_format':
                            e_column = deepcopy(column)
                            self.date_format(e_column)  # 需要单独处理额外的转化函数
                        else:
                            e_column = column
                        outer_column_str = encode_mssql_column(e_column)
                        inner_column_str = outer_column_str.replace(table_alias, alias_a)
                        s += f" and {inner_column_str}={outer_column_str}"
                    else:
                        prop_name = column.prop_raw if column.prop_raw else column.prop_name
                        s += f" and [{alias_a}].[{prop_name}]=[{table_alias}].[{prop_name}]"
            return s

        if getattr(data, "func", None) and data.func and data.func == "group_concat":
            # 构造子查询
            sub_sql = (
                f"(select TOP 1 stuff((select ','+convert(varchar(512),{data.props[0].prop_name}) "
                f"from {query.object[0].name} as {alias_a} WHERE 1=1 {_generate_where_sql()}"
                f" for xml path('')),1,1,'') as {group_concat_alias})"
            ).replace('[[', '[').replace(']]', ']')
            data.func = ""
            data.props = []
            data.prop_name = sub_sql

    @staticmethod
    @distinguish_datetime
    def date_format(data):
        """
        date_format ->  CONVERT(VARCHAR(10),GETDATE(),110)
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "date_format":
            data.func = "CONVERT"
            for prop in data.props:
                if prop.prop_name:
                    # prop.prop_name += "::timestamp"
                    continue
                if prop.value == "%Y-%m-%d %H:%i:%S":
                    prop.value = "yyyy-mm-dd hh24:mi:ss"
                elif prop.value == "%Y-%m-%d %H:%i":
                    prop.value = "yyyy-mm-dd hh24:mi"
                elif prop.value == "%Y-%m-%d %H":
                    prop.value = "yyyy-mm-dd hh24"
                elif prop.value == "%Y-%m-%d":
                    prop.value = "yyyy-mm-dd"
                elif prop.value == "%Y-%m":
                    prop.value = "yyyy-mm"
                elif prop.value == "%Y":
                    prop.value = "yyyy"

        elif getattr(data, "func", None) and data.func and data.func in ["week", 'quarter']:
            # %Y-%Q 支持任意时间与周季度格式的格式化，会自动分开组装拼接，生成拼接SQL
            # SQL需要使用字符串拼接, mssql不支持直接格式化
            MssqlEngineTransfer.dynamic_concat_datetime(
                data, format=data.props[1].value, unsupported_format=r'%[WQ]'
            )
            # 完成日期的转换替换
            deal_mssql_date_format_trans(data.props)

    @staticmethod
    def dynamic_concat_datetime(origin_data, format, unsupported_format='%W'):
        # 动态的时间格式拼接,构造props
        # 对于不能一次完成时间格式化的进行动态的时间props构造和格式化
        _origin_data = deepcopy(origin_data)

        def mock_raw(val='-'):
            # 独立的无格式的纯字符
            mocked_sep = deepcopy(_origin_data)
            mocked_sep.value = val
            mocked_sep.func = ''
            # mocked_sep.specifier = ','
            mocked_sep.props = []
            mocked_sep.alias = ''
            return mocked_sep

        def mock_week_quarter_col(format_str):
            # 不能直接用date_format格式化的时间格式
            mapping = {'%W': 'week', "%Q": 'quarter'}
            mocked_origin = deepcopy(_origin_data)
            mocked_origin.func = 'datepart'
            mocked_origin.alias = ''
            # sqlserver的week,quarter的函数参数位置与mysql相反，调换下位置
            mocked_origin.props[0], mocked_origin.props[1] = mocked_origin.props[1], mocked_origin.props[0]
            # mocked_origin.props[0].specifier = ''
            # mocked_origin.props[1].specifier = ','
            mocked_origin.props[1].alias = ''
            # sqlserver的datepart的语法中， 第一个参数为关键字，不能带引号
            mocked_origin.props[0].value = f'raw:{mapping[format_str]}'
            return mocked_origin

        def mock_date_format_col(format_str):
            # 能直接用date_format格式化的时间格式
            # -> FORMAT(CONVERT(datetime, [CreatedOn]), 'yyyy-')
            mocked_date_format = deepcopy(_origin_data)
            mocked_date_format.alias = ''
            # mocked_date_format.specifier = ','
            mocked_date_format.func = 'format'

            # -------> CONVERT(datetime, [CreatedOn])
            # 再套一层convert的原因是，项目中时间可能不是datetime类型的，直接是字符串的时间，如果直接format就会报错，先统一换成datetime
            mocked_convert = deepcopy(_origin_data)
            mocked_convert.func = 'convert'
            mocked_convert.alias = ''
            mocked_convert_p1 = mock_raw('raw:datetime')
            mocked_convert_p2 = mocked_convert.props[0]
            mocked_convert.props = [mocked_convert_p1, mocked_convert_p2]

            mocked_date_format.props = [mocked_convert, mock_raw(format_str)]
            return mocked_date_format

        split_str_result = parser_date_format(unsupported_format, format)
        origin_data.props = []
        origin_data.func = 'concat'
        for each, _type in split_str_result:
            # week quarter
            if _type == 2:
                origin_data.props.append(mock_week_quarter_col(each))
            # date_format
            elif _type == 1:
                origin_data.props.append(mock_date_format_col(each))
            # 纯字符
            elif _type == 0:
                origin_data.props.append(mock_raw(each))

        # %W %Q （纯date_format或纯week/quarter场景）
        # CONCAT(DATEPART(week, [CreatedOn])) AS [week_CREATEDON_51202656]
        # 如果只有一个prop，并且不是纯字符，就去掉外层的CONCAT
        if len(origin_data.props) == 1 and origin_data.props[0].func:
            _props = origin_data.props[0]
            origin_data.func = _props.func
            origin_data.props = _props.props

        revise_props_specifier(origin_data.props)


def deal_mssql_date_format_trans(props):
    to_replaced = {'%Y': 'yyyy', '%m': 'mm', '%d': 'dd', '%H': 'hh24', '%i': 'mi', '%S': 'ss'}

    for prop in props:
        if prop.prop_name:
            continue
        # 非字段的参数
        for key, val in to_replaced.items():
            if isinstance(prop.value, str):
                prop.value = prop.value.replace(key, val)
        if prop.props:
            deal_mssql_date_format_trans(prop.props)


class PostGreSqlEngineTransfer:
    def adapter_postgresql_engine(self, query_structure):
        """
        适配postgresql查询引擎
        :return:
        """
        if query_structure.select:
            for row in query_structure.select:
                self.adapter_prop(row)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.adapter_prop(row)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.adapter_prop(row)
        if query_structure.where:
            for row in query_structure.where:
                self.adapter_prop(row)

        return query_structure

    def adapter_prop(self, data):
        """
        适配prop
        :param data:
        :return:
        """

        if data:
            self.alias_convert(data)
            self.prop_name_convert(data)
            self.date_format(data)


            if getattr(data, "props", None):
                for prop in enumerate(data.props):
                    self.adapter_prop(prop)
            if getattr(data, "left", None):
                self.adapter_prop(data.left)
            if getattr(data, "right", None):
                self.adapter_prop(data.right)
            if getattr(data, "conditions", None):
                for condition in data.conditions:
                    self.adapter_prop(condition)

    @staticmethod
    def alias_convert(data):
        """
        alias -> "alias"
        :param data:
        :return:
        """
        if getattr(data, "alias", None) and data.alias:
            data.alias = '"' + data.alias + '"'

    @staticmethod
    def prop_name_convert(data):
        """
        prop_name -> "prop_name"
        :param data:
        :return:
        """
        if getattr(data, "prop_name", None) and data.prop_name:
            data.prop_name = '"' + data.prop_name + '"'

    @staticmethod
    @distinguish_datetime
    def date_format(data):
        """
        date_format ->  to_char
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "date_format":
            data.func = "to_char"
            for prop in data.props:
                if prop.prop_name:
                    prop.prop_name += "::timestamp"
                    continue

                if prop.value == "%Y-%m-%d %H:%i:%S":
                    prop.value = "yyyy-mm-dd hh24:mi:ss"
                elif prop.value == "%Y-%m-%d %H:%i":
                    prop.value = "yyyy-mm-dd hh24:mi"
                elif prop.value == "%Y-%m-%d %H":
                    prop.value = "yyyy-mm-dd hh24"
                elif prop.value == "%Y-%m-%d":
                    prop.value = "yyyy-mm-dd"
                elif prop.value == "%Y-%m":
                    prop.value = "yyyy-mm"
                elif prop.value == "%Y":
                    prop.value = "yyyy"


class DremioEngineTransfer:
    name = DBEngine.Dremio.value

    def adapter_engine(self, query_structure):
        """
        适配dremio查询引擎
        :return:
        """
        if query_structure.select:
            for row in query_structure.select:
                self.adapter_prop(row)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.adapter_prop(row)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.adapter_prop(row)
        if query_structure.where:
            for row in query_structure.where:
                self.adapter_prop(row)
        if query_structure.object:
            for obj in query_structure.object:
                for ref in obj.ref_clause:
                    self.adapter_prop(ref)
        return query_structure

    def adapter_tuple(self, data):
        for prop in data:
            if isinstance(prop, int):
                continue
            self.adapter_prop(prop)

    def adapter_prop(self, data):
        """
        适配prop
        :param data:
        :return:
        """

        if data:
            # order by 情况下是元祖，也需要替换
            if isinstance(data, tuple):
                self.adapter_tuple(data)
            self.alias_convert(data)
            self.date_format(data)
            self.expression_convert(data)

            if getattr(data, "props", None):
                for prop in enumerate(data.props):
                    self.adapter_prop(prop)
            if getattr(data, "left", None):
                self.adapter_prop(data.left)
            if getattr(data, "right", None):
                self.adapter_prop(data.right)
            if getattr(data, "conditions", None):
                for condition in data.conditions:
                    self.adapter_prop(condition)

    @staticmethod
    def alias_convert(data):
        """
        alias -> "alias"
        :param data:
        :return:
        """
        if getattr(data, "alias", None) and data.alias and not data.alias.startswith("\""):
            data.alias = '"' + data.alias + '"'
        # 处理dremio字段，避免包含特殊字符导致报错
        if getattr(data, "prop_name", None) and data.prop_name and not data.prop_name.startswith("\""):
            data.prop_name = '"' + data.prop_name + '"'

    @staticmethod
    def expression_convert(data):
        """
        除法转换，除法需要转换为case when
        :param data:
        :return:
        """
        if hasattr(data, "props") and data.props:
            for idx, prop in enumerate(data.props):
                if not hasattr(prop, "prop_raw") or not isinstance(prop.prop_raw, (str)):
                    continue
                result = re.findall(
                    r'[^\t\s(){}/\\=<>+\-*\^\"\'\[\]\`#\|&%]+.'
                    r'[^\t\s(){}/\\=<>+\-*\^\"\'\[\]\`#\|&%]/[^\t\s(){}/\\=<>+\-*\^\"\'\[\]\`#\|&%]+.'
                    r'[^\t\s(){}/\\=<>+\-*\^\"\'\[\]\`#\|&%]',
                    prop.prop_raw,
                )
                if not result:
                    continue
                for item in result:
                    slices = str.split(item, '/')
                    if len(slices) < 2:
                        continue
                    new_item = f" CASE  WHEN {slices[1]} = 0 THEN 0 ELSE {slices[0]} / {slices[1]} END "
                    prop.prop_raw = prop.prop_raw.replace(item, new_item)
                    data.props[idx] = prop

    @staticmethod
    @distinguish_datetime
    def date_format(data):
        """
        date_format ->  to_char
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func.lower() == "date_format":
            data.func = "to_char"
            for prop in data.props:

                if prop.value == "%Y-%m-%d %H:%i:%S" or prop.value == "%Y-%m-%d %H:%M:%S":
                    prop.value = "YYYY-MM-DD HH24:MI:SS"
                elif prop.value == "%Y-%m-%d %H:%M" or prop.value == "%Y-%m-%d %H:%i":
                    prop.value = "YYYY-MM-DD HH24:MI"
                elif prop.value == "%Y-%m-%d %H":
                    prop.value = "YYYY-MM-DD HH24"
                elif prop.value == "%Y-%m-%d":
                    prop.value = "YYYY-MM-DD"
                elif prop.value == "%Y-%m":
                    prop.value = "YYYY-MM"
                elif prop.value == "%Y":
                    prop.value = "YYYY"


class OracleEngineTransfer:
    def adapter_oracle_engine(self, query_structure):
        """
        适配oracle查询引擎
        :return:
        """
        if query_structure.select:
            for row in query_structure.select:
                self.adapter_prop(row)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.adapter_prop(row)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.adapter_prop(row)
        if query_structure.where:
            for row in query_structure.where:
                self.adapter_prop(row)
        # oracle rownum需要放在where最后一个条件处理
        if query_structure.limit and hasattr(query_structure.limit, 'row') and query_structure.limit.row:
            rownum_where = Where(
                left=Prop(prop_name="rownum"),
                right=Prop(value=query_structure.limit.row),
                operator="<",
                logical_relation="",
            )
            if not query_structure.where:
                query_structure.where = [rownum_where]
            else:
                rownum_where.logical_relation = "and"
                query_structure.where.append(rownum_where)
            query_structure.limit = None

        return query_structure

    def adapter_prop(self, data):
        """
        适配prop
        :param data:
        :return:
        """

        if data:
            self.date_format(data)
            self.group_concat(data)

            if getattr(data, "prop_raw", None):
                self.adapter_prop_raw(data)

            if getattr(data, "props", None):
                for prop in data.props:
                    self.adapter_prop(prop)
            if getattr(data, "left", None):
                self.adapter_prop(data.left)
            if getattr(data, "right", None):
                self.adapter_prop(data.right)
            if getattr(data, "conditions", None):
                for condition in data.conditions:
                    self.adapter_prop(condition)

    @staticmethod
    def adapter_prop_raw(data):
        # placeholder = PLACEHOLDER_COMPILE.findall(data.prop_raw)
        # if placeholder:
        #     data.prop_raw = data.prop_raw.replace(f'`{placeholder[0]}`', f'"{placeholder[0]}"')
        week = WEEK_COMPILE.findall(data.prop_raw)
        if week:
            field = week[0]
            data.prop_raw = f'to_char({field}, \'WW\')'
        quarter = QUARTER_COMPILE.findall(data.prop_raw)
        if quarter:
            field = quarter[0]
            data.prop_raw = f'to_char({field}, \'Q\')'
        day = DAY_COMPILE.findall(data.prop_raw)
        if day:
            field = day[0]
            data.prop_raw = f'to_char({field}, \'DD\')'
        month = MONTH_COMPILE.findall(data.prop_raw)
        if month:
            field = month[0]
            data.prop_raw = f'to_char({field}, \'MM\')'
        year = YEAR_COMPILE.findall(data.prop_raw)
        if year:
            field = year[0]
            data.prop_raw = f'to_char({field}, \'yyyy\')'
        data.prop_raw = data.prop_raw.replace("CURDATE()", "to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd')")
        diff = DIFF_COMPILE.findall(data.prop_raw)
        if diff and len(diff[0]) == 3:
            diff = diff[0]
            date_map = {"DAY": "", "HOUR": " * 24", "MINUTE": " * 24 * 60"}
            data.prop_raw = f'ROUND(TO_NUMBER({diff[1]} - {diff[2]}){date_map.get(diff[0], "")})'

    @staticmethod
    def group_concat(data):
        """
        group_concat -> to_char
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "group_concat":
            data.func = "wm_concat"

    @staticmethod
    @distinguish_datetime
    def date_format(data):
        """
        date_format ->  to_char
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "date_format":
            data.func = "to_char"
            for prop in data.props:
                if prop.prop_name:
                    # prop.prop_name += "::timestamp"
                    continue

                if prop.value == "%Y-%m-%d %H:%i:%S":
                    prop.value = "yyyy-mm-dd hh24:mi:ss"
                elif prop.value == "%Y-%m-%d %H:%i":
                    prop.value = "yyyy-mm-dd hh24:mi"
                elif prop.value == "%Y-%m-%d %H":
                    prop.value = "yyyy-mm-dd hh24"
                elif prop.value == "%Y-%m-%d":
                    prop.value = "yyyy-mm-dd"
                elif prop.value == "%Y-%m":
                    prop.value = "yyyy-mm"
                elif prop.value == "%Y":
                    prop.value = "yyyy"

        elif getattr(data, "func", None) and data.func and data.func in ["week", 'quarter']:
            data.func = "to_char"

            # oracle to_char 原本支持周、季度格式化，只需要完成格式的替换
            if len(data.props) == 2:
                deal_oracle_date_format_trans(data.props)


def deal_oracle_date_format_trans(props):
    to_replaced = {'%Y': 'yyyy', '%m': 'mm', '%d': 'dd', '%H': 'hh24', '%i': 'mi', '%S': 'ss', '%W': 'WW', '%Q': 'Q'}

    for prop in props:
        if prop.prop_name:
            continue
        # 非字段的参数
        for key, val in to_replaced.items():
            if isinstance(prop.value, str):
                prop.value = prop.value.replace(key, val)
        if prop.props:
            deal_oracle_date_format_trans(prop.props)


class DMEngineTransfer:

    def _set_query_structure(self, query_structure):
        if not hasattr(self, 'query_structure'):
            setattr(self, 'query_structure', query_structure)

    def adapter_dm_engine(self, query_structure):
        """
        适配DM查询引擎
        :return:
        """

        self._set_query_structure(query_structure)

        if query_structure.select:
            for row in query_structure.select:
                self.adapter_prop(row)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.adapter_prop(row)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.adapter_prop(row)
        if query_structure.where:
            for row in query_structure.where:
                self.adapter_prop(row)
        # dm rownum需要放在where最后一个条件处理
        if query_structure.limit and hasattr(query_structure.limit, 'row') and query_structure.limit.row:
            rownum_where = Where(
                left=Prop(prop_name="rownum"),
                right=Prop(value=query_structure.limit.row),
                operator="<",
                logical_relation="",
            )
            if not query_structure.where:
                query_structure.where = [rownum_where]
            else:
                rownum_where.logical_relation = "and"
                query_structure.where.append(rownum_where)
            query_structure.limit = None

        return query_structure

    def adapter_prop(self, data):
        """
        适配prop
        :param data:
        :return:
        """

        if data:
            self.date_format(data)
            self.group_concat(data)
            self.select_field_limit1(data)

            if getattr(data, "prop_raw", None):
                self.adapter_prop_raw(data)

            if getattr(data, "props", None):
                for prop in data.props:
                    self.adapter_prop(prop)
            if getattr(data, "left", None):
                self.adapter_prop(data.left)
            if getattr(data, "right", None):
                self.adapter_prop(data.right)
            if getattr(data, "conditions", None):
                for condition in data.conditions:
                    self.adapter_prop(condition)

    def select_field_limit1(self, data):
        """
        select字段支持取聚合第一行
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "limit1":
            # 构造出select列子查询
            data.func = ""
            self.query_structure.limit = 1

    @staticmethod
    def adapter_prop_raw(data):
        # placeholder = PLACEHOLDER_COMPILE.findall(data.prop_raw)
        # if placeholder:
        #     data.prop_raw = data.prop_raw.replace(f'`{placeholder[0]}`', f'"{placeholder[0]}"')
        week = WEEK_COMPILE.findall(data.prop_raw)
        if week:
            field = week[0]
            data.prop_raw = f'to_char({field}, \'WW\')'
        quarter = QUARTER_COMPILE.findall(data.prop_raw)
        if quarter:
            field = quarter[0]
            data.prop_raw = f'to_char({field}, \'Q\')'
        day = DAY_COMPILE.findall(data.prop_raw)
        if day:
            field = day[0]
            data.prop_raw = f'to_char({field}, \'DD\')'
        month = MONTH_COMPILE.findall(data.prop_raw)
        if month:
            field = month[0]
            data.prop_raw = f'to_char({field}, \'MM\')'
        year = YEAR_COMPILE.findall(data.prop_raw)
        if year:
            field = year[0]
            data.prop_raw = f'to_char({field}, \'yyyy\')'
        data.prop_raw = data.prop_raw.replace("CURDATE()", "to_date(to_char(sysdate,'yyyy-mm-dd'),'yyyy-mm-dd')")
        diff = DIFF_COMPILE.findall(data.prop_raw)
        if diff and len(diff[0]) == 3:
            diff = diff[0]
            date_map = {"DAY": "", "HOUR": " * 24", "MINUTE": " * 24 * 60"}
            data.prop_raw = f'ROUND(TO_NUMBER({diff[1]} - {diff[2]}){date_map.get(diff[0], "")})'

    @staticmethod
    def group_concat(data):
        """
        group_concat -> to_char
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "group_concat":
            data.func = "wm_concat"

    @staticmethod
    @distinguish_datetime
    def date_format(data):
        """
        date_format ->  to_char
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "date_format":
            data.func = "to_char"
            for prop in data.props:
                if prop.prop_name:
                    # prop.prop_name += "::timestamp"
                    continue

                if prop.value == "%Y-%m-%d %H:%i:%S":
                    prop.value = "yyyy-mm-dd hh24:mi:ss"
                elif prop.value == "%Y-%m-%d %H:%i":
                    prop.value = "yyyy-mm-dd hh24:mi"
                elif prop.value == "%Y-%m-%d %H":
                    prop.value = "yyyy-mm-dd hh24"
                elif prop.value == "%Y-%m-%d":
                    prop.value = "yyyy-mm-dd"
                elif prop.value == "%Y-%m":
                    prop.value = "yyyy-mm"
                elif prop.value == "%Y":
                    prop.value = "yyyy"

        elif getattr(data, "func", None) and data.func and data.func in ["week", 'quarter']:
            data.func = "to_char"

            # dm to_char 原本支持周、季度格式化，只需要完成格式的替换
            if len(data.props) == 2:
                deal_oracle_date_format_trans(data.props)


class PrestoEngineTransfer:
    name = DBEngine.PRESTO.value

    convert_func_map = {
        "time": "TO_TIME(%s, 'yyyy-mm-dd hh24:mi:ss')",
        "timestamp": "TO_TIMESTAMP(%s, 'yyyy-mm-dd hh24:mi:ss')",
        "date": "TO_DATE(%s, 'yyyy-mm-dd')",
        "integer": "CAST(%s as integer)",
        "bigint": "CAST(%s as bigint)",
        "double": "CAST(%s as double)",
    }

    def adapter_engine(self, query_structure):
        """
        适配presto
        :return:
        """
        if query_structure.select:
            for row in query_structure.select:
                self.adapter_prop(row)
        if query_structure.object:
            for row in query_structure.object:
                self.adapter_prop(row)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.adapter_prop(row)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.adapter_prop(row)
        if query_structure.where:
            for row in query_structure.where:
                self.adapter_prop(row)
                # 对每一个条件中的字段还需要处理
                if row.conditions:
                    for condition in row.conditions:
                        self.adapter_prop(condition)
        return query_structure

    def adapter_prop(self, data):
        """
        适配prop
        :param data:
        :return:
        """
        # data

        if data:
            # data 本身可能为数组
            if isinstance(data, tuple):
                for prop in data:
                    self.adapter_prop(prop)
            self.date_format(data)
            self.convert_func(data)

            if getattr(data, "props", None):
                for prop in enumerate(data.props):
                    self.adapter_prop(prop)
            if getattr(data, "left", None):
                self.adapter_prop(data.left)
            if getattr(data, "right", None):
                self.adapter_prop(data.right)
            if getattr(data, "conditions", None):
                for condition in data.conditions:
                    self.adapter_prop(condition)
            if getattr(data, "ref_clause", None):
                for ref in data.ref_clause:
                    self.adapter_prop(ref)

    @staticmethod
    @distinguish_datetime
    def date_format(data):
        """
        date_format ->  to_char
        :param data:
        :return:
        """
        if getattr(data, "func", None) and data.func and data.func == "date_format":
            data.func = "to_char"
            for prop in data.props:

                if prop.value == "%Y-%m-%d %H:%i:%S" or prop.value == "%Y-%m-%d %H:%M:%S":
                    prop.value = "yyyy-mm-dd hh24:mi:ss"
                elif prop.value == "%Y-%m-%d %H:%i":
                    prop.value = "yyyy-mm-dd hh24:mi"
                elif prop.value == "%Y-%m-%d %H":
                    prop.value = "yyyy-mm-dd hh24"
                elif prop.value == "%Y-%m-%d":
                    prop.value = "yyyy-mm-dd"
                elif prop.value == "%Y-%m":
                    prop.value = "yyyy-mm"
                elif prop.value == "%Y":
                    prop.value = "yyyy"

    def convert_func(self, data):
        if getattr(data, 'prop_name', None) and data.prop_name and getattr(data, 'convert_field_type', None):
            if data.convert_field_type not in self.convert_func_map:
                raise BadQueryDefinition('非法的目标转换字段类型: %s' % data.convert_field_type)
            # 单表查询模式下用prop_raw存放查询转换函数
            data.prop_raw = self.convert_func_map[data.convert_field_type] % data.prop_name
