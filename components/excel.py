#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> 2018/3/2
"""
import codecs
import csv
import tempfile
import urllib
from enum import Enum, unique
import re
import time
import xlwt
from urllib.parse import quote_plus


@unique
class ExcelType(Enum):
    Csv = 'csv'
    Xlsx = 'xlsx'
    Xls = 'xls'
    Doc = 'doc'
    Docx = 'docx'


class Excel(object):
    """
    Excel文件导出、导入通用类
    """

    @staticmethod
    def _valid_filename(filename):
        value = re.compile(r'^[\u4E00-\u9FA5A-Za-z0-9_\.]+$')
        result = value.match(filename)
        if result:
            return True
        return False

    @staticmethod
    def export_csv(filename, data, title=None, mode='w', charset='utf_8_sig', response=None):
        """
        导出Excel为csv格式
        :param filename: 文件名
        :param data: 数据 data数据示例：
        [
        {"name":"jack","age":"20","sex":"1","address":"色卡卡1死的发送到"},
        {"name":"jack1","age":"30","sex":"2","address":"色卡卡2死的发送到"},
        {"name":"jack2","age":"40","sex":"3","address":"色卡卡3死的发送到"},
        ]
        :param title: 标题 title数据示例：
        {"name":"姓名", "age":"年龄", "sex":"性别","address":"地址"}
        :param mode: 模式，一般为‘w’即只是写的模式打开文件
        :param charset: 字符编码
        :return:
        """
        # 使用tempfile来处理文件
        fake_namefile = tempfile.NamedTemporaryFile(delete=True)
        with codecs.open(fake_namefile.name, mode, charset) as fake_csv:
            writer = csv.writer(fake_csv)
            keys = []
            if title is not None:
                keys = title.keys()
                writer.writerow([title.get(key) for key in keys])
            result_list = []
            for item in data:
                if keys == []:
                    keys = item.keys()
                result_list.append([item.get(key) for key in keys])
            writer.writerows(result_list)
        if not Excel._valid_filename(filename):
            filename = 'temp_' + str(int(time.time())) + '.csv'
        if response:
            response.set_header('Content-Type', 'application/octet-stream;charset={charset}'.format(charset=charset))
            response.set_header('Content-disposition', 'attachment; filename=' + quote_plus(filename))
            with open(fake_namefile.name, 'rb') as file:
                response.body = file.read()
        else:
            return fake_namefile.name

    @staticmethod
    def export_xls(filename, data, title=None, sheet_name='sheet1', cell_overwrite_ok=True, response=None):
        """
        导出Excel为xls格式
        :param filename: 文件名
        :param data: 数据 data数据示例：
        [
        {"name":"jack","age":"20","sex":"1","address":"色卡卡1死的发送到"},
        {"name":"jack1","age":"30","sex":"2","address":"色卡卡2死的发送到"},
        {"name":"jack2","age":"40","sex":"3","address":"色卡卡3死的发送到"},
        ]
        :param title: 标题 title数据示例：
        {"name":"姓名", "age":"年龄", "sex":"性别","address":"地址"}
        :param sheet_name: sheet的名称title
        :param cell_overwrite_ok: 默认为True
        :return:
        """
        workbook = xlwt.Workbook(encoding='utf-8')
        sheet = workbook.add_sheet(sheet_name, cell_overwrite_ok)

        keys = []
        x = 0
        if title is not None:
            keys = title.keys()
            index = 0
            for key in keys:
                sheet.write(0, index, title.get(key))
                index += 1
            x = 1

        # 获取并写入数据段信息
        for row in data:
            if keys == []:
                keys = row.keys()
            y = 0
            for key in keys:
                sheet.write(x, y, u'%s' % row.get(key))
                y += 1
            x += 1
        fake_namefile = tempfile.NamedTemporaryFile(delete=True)
        workbook.save(fake_namefile.name)
        if response:
            response.set_header('Content-Type', 'application/octet-stream;charset=utf_8_sig')
            response.set_header('Content-disposition', 'attachment; filename=' + quote_plus(filename))
            with open(fake_namefile.name, 'rb') as file:
                response.body = file.read()
        else:
            return fake_namefile.name

    @staticmethod
    def export_xlsx(filename, data, title=None, sheet_name='sheet1'):
        # TODO 尚未实现导出为xlsx格式文件的方法，因此格式需要安装openpyxl扩展
        pass

    @staticmethod
    def import_file():
        # TODO 尚未实现导入Excel文件方法
        pass
