import jwt
from loguru import logger

from dmplib.hug import g, debugger

from dmplib import config
from base.dmp_constant import SELF_SERVICE_VIRTUAL_USER_ID

_debugger = debugger.Debug(__name__)


# 处理外部api形式的数据源，处理body中biz_params的身份信息
# 请求api数据集有两个地方谁传递用户信息
# 1. 请求中的token，这个token直接取的是数见cookies中的token（透传）
# {
#   "_flag": **********,
#   "dashboard_id": "3a0b0b51-bb41-6afb-7810-aaa277e50bf2",
#   "code": "myyl",
#   "account": "myyl",
#   "id": "********-0001-0001-0001-0000********",
#   "group_id": "",
#   "external_params": {
#     "biz_code": "4790112d309b4e408d70a3d5e02ee49b",
#     "user_name": "chenqj023333",
#     "user_auth": "view,download",
#     "user_id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
#     "yl_user_id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
#     "project_code": "myyl",
#     "user_account": "myyl",
#   },
#   "customize_roles": [
#     "********-0000-0000-0000-0000********"
#   ],
#   "external_user_id": "",
#   "extend_yl_params": ""
# }
#    多云可能获取token中的id, external_params中的user_id（或者yl_user_id）标记用户
#
# 2. 请求body中的biz_params，例如：
# "biz_params": {
#     "project_code": "zhhy",
#     "pulsar_project": "yl_saas_prd",
#     "engine": "StarRocksSaaS",
#     "passport": "",
#     "user_name": "许炜星【工程管理部】",
#     "user_account": "***********",
#     "user_id": "39e77a46-65b1-6348-80c8-101bfdd3c18b",
#     "yl_user_id": "39e77a46-65b1-6348-80c8-101bfdd3c18b",
#     "user_auth": "view,download",
#     "biz_code": "a8ccbe9ecfdd4617b51a4a90a5843e0b",
#     "exp": **********,
#     "shield_privacy": 0
# }
#  多云也可能获取biz_params中的user_id（或者yl_user_id）标记用户
def deal_external_api_biz_params(biz_params: dict):
    # return
    try:
        dmp_token = getattr(g, 'cookie', {}).get('token', '')
        token_dict = jwt.decode(dmp_token, config.get('JWT.secret'), algorithms="HS256")
    except Exception as e:
        logger.error(f"deal_external_api_biz_params jwt decode error: {e}")
        token_dict = {}

    userid = getattr(g, 'userid', '') or ''  # type:str
    external_params = getattr(g, 'external_params', {}) or {}  # type:dict

    # 特殊处理/api/user/sso，场景为：角色登录使用自助报表的外部api取数（api数据集和指标数据集）
    # ！！！ SELF_SERVICE_VIRTUAL_USER_ID 这个id可能是/api/user/sso设置的，也可能是/api/dashboard/login设置的
    # 现在/api/user/sso进来的有问题，/api/dashboard/login进来的是正常的
    if userid == SELF_SERVICE_VIRTUAL_USER_ID:
        # 这个外部id是多云集成传进来的
        external_user_id = token_dict.get('external_user_id') or ''
        if not external_user_id:
            if not external_params:
                # /api/user/sso 登录
                logger.error(f'集成自助报表制作的api取数external_user_id为空！')
                _debugger.log("集成自助报表制作的api取数external_user_id为空!")
            else:
                # /api/dashboard/login 登录
                # 这种情况下id是虚拟固定，但是external_params中有正确的用户信息，现在就能正常请求数据，不处理
                # {
                #     "_flag": **********,
                #     "dashboard_id": "3a0b0b51-bb41-6afb-7810-aaa277e50bf2",
                #     "code": "myyl",
                #     "account": "myyl",
                #     "id": "********-0001-0001-0001-0000********",
                #     "group_id": "",
                #     "external_params": {
                #         "biz_code": "4790112d309b4e408d70a3d5e02ee49b",
                #         "user_name": "chenqj023333",
                #         "user_auth": "view,download",
                #         "user_id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                #         "yl_user_id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                #         "project_code": "myyl",
                #         "user_account": "myyl",
                #     },
                #     "customize_roles": [
                #         "********-0000-0000-0000-0000********"
                #     ],
                #     "external_user_id": "",
                #     "extend_yl_params": ""
                # }
                pass
            return

        # 此时cookies中token是没有external_params参数的
        mock_user_info_params = {
            "project_code": getattr(g, 'code', ''),
            "user_account": getattr(g, 'account', ''),
            "user_id": external_user_id,
            # "yl_user_id": external_user_id,
        }

        # 1. 处理biz_params
        merge_data(mock_user_info_params, biz_params)
        merge_data(mock_user_info_params, external_params)
        g.external_params = external_params

        # 2. 替换token中的id以及external_params
        token_dict['id'] = external_user_id
        token_dict['external_params'] = external_params
        regenerate_token = jwt.encode(token_dict, config.get('JWT.secret'))
        if hasattr(g, 'cookie'):
            g.cookie['token'] = regenerate_token

        _debugger.log({
            "替换的的external_params": external_params,
            "mock_user_info_params": mock_user_info_params,
            "重新处理的token_dict": token_dict,
            "重新处理的token": regenerate_token,
        })


def merge_data(from_data: dict, to_data: dict):
    for key, val in from_data.items():
        if key not in to_data:
            to_data[key] = val
