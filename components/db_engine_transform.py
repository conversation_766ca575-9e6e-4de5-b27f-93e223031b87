#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    不同db之间的字段类型转换到mysql 适配器
    class
    <NAME_EMAIL>  2018年08月24日
"""
import re
from base import repository
from base.enums import DatasetFieldDataType, DatasetFieldGroup, DBEngine, DatasetStorageType
from components.dateset_generate_col_name import generate_new_col_name
from components.storage_setting import get_storage_type, is_local_storage
from dmplib.saas.project import get_data_db
from dmplib.hug import g
from dmplib.utils import strings, sql_util
from dmplib.utils.errors import UserError
from dmplib.utils.strings import uniqid, is_number

import logging

logger = logging.getLogger(__name__)


mysql_field_type_bytes = {
    'tinyint': 1,
    'smallint': 2,
    'mediumint': 3,
    'int': 4,
    'integer': 4,
    'bigint': 8,
    'float': 4,
    'double': 8,
    'real': 8,
    'date': 3,
    'time': 3,
    'year': 1,
    'datetime': 8,
    'timestamp': 4,
}


def get_field_type_bytes(field_type: str, db_encoding: str = 'utf8'):
    """
    根据mysql字段类型，获取字段类型对应的数据库存储的字节长度
    :param field_type: 字段类型，如:decimal(10,2)
    :return: 该类型对应的字节长度，int类型
    """
    type_meta = field_type.split('(')
    l = int(type_meta[1].split(',')[0].strip().replace(')', '')) if len(type_meta) > 1 else None
    field_type = type_meta[0].lower()

    if field_type in mysql_field_type_bytes:
        b_l = mysql_field_type_bytes[field_type]
    elif field_type in ('numeric', 'decimal'):
        b_l = (l or 10) + 2
    elif field_type in ('bit',):
        b_l = (l or 1) // 8 + 1
    elif field_type in ('binary', 'varbinary'):
        b_l = l or 1
    elif field_type in ('char', 'varchar'):
        char_len = l or 1
        encode_byte_size = {'utf8': 3}
        b_l = encode_byte_size[db_encoding] * char_len
    elif field_type.find('text') >= 0 or field_type.find('blob') >= 0:
        b_l = 0
    else:
        b_l = l or 8
    return b_l


def get_dmp_data_type(mysql_field_type: str):
    return DbTransformService.get_mysql_data_type(mysql_field_type)


def get_dmp_field_group(mysql_field_type: str):
    return DbTransformService.get_mysql_field_group(mysql_field_type)


def get_dmp_data_type_by_db_engine(field_type: str, db_engine: str = DBEngine.RDS.value):
    transform_mapping = {
        DBEngine.RDS.value: DbTransformService,
        DBEngine.Mysql.value: DbTransformService,
        DBEngine.MSSQL.value: MssqlToMysqlTransformService,
        DBEngine.SqlServer.value: MssqlToMysqlTransformService,
        DBEngine.PG.value: PostgresqlToMysqlTransformService,
        DBEngine.ADS.value: ADSTransformService,
        DBEngine.ORACLE.value: OracleToMysqlTransformService,
        DBEngine.DM.value: OracleToMysqlTransformService,
        DBEngine.PRESTO.value: PrestoToMysqlTransformService,
    }
    if db_engine not in transform_mapping:
        raise UserError(message='尚未支持的数据库引擎类型')

    transform_cls = transform_mapping[db_engine]
    mysql_field_type = transform_cls(None).get_data_type(field_type)
    return get_dmp_data_type(mysql_field_type)


class DbTransformService:
    """
    统一数据库适配mysql基类，（提供其他db字段转mysql、字段统一生成col_name、生成create_table语句，创建临时表结构服务）
    """

    __slots__ = ["dataset_id"]

    def __init__(self, dataset_id):
        self.dataset_id = dataset_id

    @staticmethod
    def get_mysql_data_type(mysql_field_type: str):
        """
        获取mysql字段类型
        :type mysql_field_type: str
        :return:
        """
        if not mysql_field_type:
            return DatasetFieldDataType.Description.value

        col_type = mysql_field_type.split("(")[0]
        if col_type.lower() in sql_util.measure_type:
            return DatasetFieldDataType.Number.value
        elif col_type.lower() in sql_util.date_type:
            return DatasetFieldDataType.Datetime.value
        return DatasetFieldDataType.Description.value

    @staticmethod
    def get_mysql_field_group(mysql_field_type: str):
        if mysql_field_type.lower() in sql_util.measure_type:
            return DatasetFieldGroup.Measure.value
        return DatasetFieldGroup.Dimension.value

    def create_tmp_table(self, origin_columns, old_col_names, create_table=True):
        """
        创建数据集临时表
        :return:(table_name,new_columns,sql)
        """
        tmp_table_name = uniqid("dataset_tmp_")
        create_sql = ""
        try:
            # 重新生成数据集字段名（兼容：老数据字段名不变），并且进行其他db的字段类型转换为mysql字段类型
            new_fields = self.identity_dataset_fields_new(old_col_names, origin_columns)
            new_fields = self.transfer_fields_length(new_fields)
            create_sql = self._build_create_table_sql(tmp_table_name, new_fields)

            # 本地存储模式不创建临时表
            if create_table:
                with get_data_db() as db:
                    db.exec_sql(create_sql)
                    sql = "drop table if exists {table_name}".format(table_name=tmp_table_name)
                    db.exec_sql(sql)
            return tmp_table_name, new_fields, create_sql
        except Exception as ex:
            logger.exception(str(ex), exc_info=True)
            raise UserError(message="测试创建数据集结构失败：" + str(ex) + "，创建sql：" + create_sql)

    @staticmethod
    def _build_create_table_sql(table_name, new_fields):
        """
        组装建表sql
        :return:
        """
        mysql_columns = []
        for column in new_fields:
            # 由于前端 莫名更改了data_type 传值 导致编辑模式出现报错,片面修复，只是不让报错
            data_type = column.get("data_type")
            if data_type in ('日期', '数值', '字符串') and column.get("type"):
                data_type = column.get("type")
                # 数芯1.5现在的类型转换有点奇怪，没有走以前的转换逻辑，这里是string类型的临时补丁
                if column.get("type") == 'string':
                    data_type = "text"
            mysql_columns.append("`%s` %s " % (column.get("col_name"), data_type))
        sql = (
            "create table if not exists {table_name} ("
            "{cols}"
            ")".format(table_name=table_name, cols=",".join([col for col in mysql_columns]))
        )
        return sql

    def get_data_type(self, data_type):
        """
        获取mysql的字段类型（不同类型数据库需要实现转换）
        :param data_type:
        :return:
        """
        # 由于数据库编码问题，如果目标库是utf编码，varchar的长度会变长为3倍，字段过多的话，会导致Row size too large
        # varchar大于1000时，用text替换
        if not data_type:
            return "text"
        data_type = data_type.lower()
        params = data_type.split("(")
        if len(params) > 1:
            if (
                params[0] == 'varchar'
                and is_number(params[1].replace(")", ""))
                and int(params[1].replace(")", "")) >= 1000
            ):
                return "text"
            # char大于255的时候，创建表会报错，给最大限制255（根本原因是db.cur.description中internal_size默认✖️3了）
            elif (
                params[0] == 'char' and is_number(params[1].replace(")", "")) and int(params[1].replace(")", "")) > 255
            ):
                return "char(255)"
            elif params[0] == 'datetime':
                return "datetime"
            elif params[0] == 'timestamp':
                return "timestamp"
            elif params[0] == 'time':
                return "time"
            elif params[0] == 'double':
                return "double"
            elif params[0] == 'float':
                return "float"
            elif params[0] == 'integer':
                return "int"
            elif params[0] == 'varbinary':
                return "text"
            elif params[0] == 'boolean':
                return "varchar(255)"
            elif params[0] == 'real':
                return "float"
        else:
            if params[0] == 'varchar':
                return "text"
            elif params[0] == 'char':
                return "char(255)"
            elif params[0] == 'integer':
                return "int"
            elif params[0] == 'varbinary':
                return "text"
            elif params[0] == 'boolean':
                return "varchar(255)"
            elif params[0] == 'real':
                return "float"
            elif params[0] == "decimal":
                return "decimal(18, 6)"
            elif params[0] == "double":
                return "double(18, 6)"
            elif params[0] == "string":
                return "text"
        return data_type

    def identity_dataset_fields(self, old_col_names, queried_fields):
        """
        生成能代表数据集字段的标识. 使用方法参数单元测试
        :param list old_col_names: 数据集已有的字段名称
        :param list queried_fields: 的字段list
        :return:
        """
        if not queried_fields:
            raise UserError(message="查询字段不能为空")
        if not self.dataset_id:
            raise UserError(message="缺少参数：dataset_id")

        # 为了能找回原来dataset_field_id，需要查询dataset_field_delete表
        has_delete_fields = repository.get_data(
            'dataset_field_delete', {'dataset_id': self.dataset_id}, ['col_name'], multi_row=True
        )

        all_old_col_names = (old_col_names or []) + ([row.get('col_name') for row in has_delete_fields] or [])
        new_fields = []
        for field in queried_fields:
            field["old_col_name"] = field.get("col_name")
            field["origin_col_name"] = field.get("col_name")

            if field.get("col_name") not in all_old_col_names:
                # 生成新的col_name
                # 视图模式下,api字段中需要添加表前缀区分
                new_field_name = generate_new_col_name(
                    self.dataset_id,
                    field.get("col_name"),
                    table_name=field.get("origin_table_alias_name")
                    if field.get("origin_table_alias_name")
                    else field.get("table_name"),
                )
                if len(new_field_name) > 64:
                    raise UserError(message="字段名【{}】长度超过最大64位限制，请修改字段名长度。".format(field.get("col_name")))
                field["col_name"] = new_field_name

            if field.get("data_type"):
                field["data_type"] = self.get_data_type(field.get("data_type"))
            else:
                field["data_type"] = "text"

            new_fields.append(field)
        return new_fields

    def identity_dataset_fields_new(self, old_col_names, queried_fields):
        """
        生成能代表数据集字段的标识. 使用方法参数单元测试
        :param list old_col_names: 数据集已有的字段名称
        :param list queried_fields: 的字段list
        :return:
        """
        if not queried_fields:
            raise UserError(message="查询字段不能为空")
        if not self.dataset_id:
            raise UserError(message="缺少参数：dataset_id")

        # 为了能找回原来dataset_field_id，需要查询dataset_field_delete表
        has_delete_fields = repository.get_data(
            'dataset_field_delete', {'dataset_id': self.dataset_id},
            ['col_name', 'origin_col_name'], multi_row=True
        )

        all_old_col_names = ([row.get('col_name') for row in old_col_names]) + ([row.get('col_name') for row in has_delete_fields])
        new_fields = []
        for field in queried_fields:
            field["old_col_name"] = field.get("col_name")
            field["origin_col_name"] = field.get("origin_col_name") if field.get("origin_col_name") else field.get("col_name")
            if field.get("col_name") not in all_old_col_names:
                is_exist = self.update_col_name(old_col_names, field)
                if is_exist is False:
                    new_field_name = generate_new_col_name(
                        self.dataset_id,
                        field.get("col_name"),
                        table_name=field.get("origin_table_alias_name")
                        if field.get("origin_table_alias_name")
                        else field.get("table_name"),
                    )
                    if len(new_field_name) > 64:
                        raise UserError(
                            message="字段名【{}】长度超过最大64位限制，请修改字段名长度。".format(field.get("col_name")))
                    field["col_name"] = new_field_name

            if field.get("data_type"):
                field["data_type"] = self.get_data_type(field.get("data_type"))
            else:
                field["data_type"] = "text"

            new_fields.append(field)
        return new_fields

    def update_col_name(self, old_col_names, field):
        for old_col_name in old_col_names:
            tabel_name = field.get("origin_table_alias_name") if field.get(
                "origin_table_alias_name") else field.get("table_name")
            if tabel_name:
                old_table_name = old_col_name.get("origin_table_alias_name") if old_col_name.get(
                    "origin_table_alias_name") else old_col_name.get("origin_table_name")

                if old_table_name and tabel_name.lower() == old_table_name.lower() \
                        and field.get("origin_col_name") and old_col_name.get("origin_col_name") \
                        and field.get("origin_col_name").lower() == old_col_name.get("origin_col_name").lower():
                    field["col_name"] = old_col_name.get("col_name")
                    return True
            elif field.get("origin_col_name") and old_col_name.get("origin_col_name") \
                    and field.get("origin_col_name").lower() == old_col_name.get("origin_col_name").lower():
                field["col_name"] = old_col_name.get("col_name")
                return True
        return False

    def transfer_fields_length(self, new_fields: list, type_field_name: str = 'data_type'):
        """
        处理mysql单表的单行字段长度限制Row size too large
        :param new_fields: 转化后的mysql字段类型
        :param type_field_name: new_fields的字段类型的字典键名称
        :return:
        """
        col_index_mapping = {field['col_name']: index for index, field in enumerate(new_fields)}
        filtered_func = lambda r: r[type_field_name].find('text') < 0 or r[type_field_name].find('blob') < 0
        filtered_fields = list(filter(filtered_func, new_fields))
        for field in filtered_fields:
            data_type = field[type_field_name]
            # 报错的情况，不影响主流程。默认给36长度
            try:
                byte_length = get_field_type_bytes(data_type)
            except Exception:
                byte_length = 36
            field['db_bytes'] = byte_length

        filtered_fields.sort(key=lambda r: r['db_bytes'], reverse=True)

        total_bytes = sum([r['db_bytes'] for r in filtered_fields])
        cur = 0
        while total_bytes >= 65535:
            data_type = filtered_fields[cur][type_field_name]
            if not re.match(r'.*char.*', data_type, re.IGNORECASE):
                cur += 1
                continue

            col_name = filtered_fields[cur]['col_name']
            new_fields[col_index_mapping[col_name]][type_field_name] = 'text'
            total_bytes -= filtered_fields[cur]['db_bytes']
            cur += 1

        return new_fields


class PostgresqlToMysqlTransformService(DbTransformService):
    """
    postgresql 转换mysql业务类
    """

    __slots__ = ["postgresql_to_mysql"]

    def __init__(self, dataset_id):
        super().__init__(dataset_id)

        self.postgresql_to_mysql = {
            # 数值
            'bigint': 'bigint',
            'bigserial': 'bigint auto_increment',
            'integer': 'int',
            'int': 'int',
            'smallint': 'smallint',
            'serial': 'int auto_increment',

            'double precision': 'double',
            'double': 'double',
            'numeric': 'decimal',
            'real': 'float',

            'boolean': 'tinyint(1)',
            'character varying': 'varchar',
            'text': 'text',
            'bit': 'bit',
            'inet': 'varchar(45)',

            'timestamp without time zone': 'datetime',
            'timestamp(6) without time zone': 'datetime',
            'date': 'date',
            'time without time zone': 'time',

        }

    def get_data_type(self, data_type):
        """
        sqlserver对应mysql字段类型
        :param data_type:
        :return:
        """
        data_type = data_type.lower()
        if data_type == 'timestamp(6) without time zone':
            return 'datetime'
        params = data_type.split("(")
        data = self.postgresql_to_mysql.get(params[0])
        if data:
            if len(params) == 1:
                if data == 'varchar':
                    return "text"
                else:
                    return data
            else:
                if data == 'varchar':
                    try:
                        length = int(params[1].replace(")", "")) * 4
                        if length <= 512:
                            return "{d}({p})".format(d=data, p=str(length))
                        else:
                            return "text"
                    except ValueError:
                        return "text"
                else:
                    return "{d}({p}".format(d=data, p=params[1])
        else:
            return "text"


class MssqlToMysqlTransformService(DbTransformService):
    """
    sqlserver 转换mysql业务类
    """

    __slots__ = ["different_data_type", "sqlserver_to_mysql", "special_data_type"]

    def __init__(self, dataset_id):
        super().__init__(dataset_id)

        # sqlserver字段映射mysql
        self.sqlserver_to_mysql = {
            # 数值
            "tinyint": "int",
            "smallint": "int",
            "int": "int",
            "bigint": "bigint",
            "decimal": "decimal",
            "numeric": "numeric",
            "smallmoney": "DECIMAL(10,4)",
            "money": "DECIMAL(19,4)",
            "float": "float",
            "real": "real",
            #  日期
            "datetime": "varchar(30)",
            "datetime2": "varchar(30)",
            "smalldatetime": "varchar(30)",
            "date": "varchar(30)",
            "time": "varchar(30)",
            "datetimeoffset": "varchar(30)",
            "timestamp": "varchar(30)",
            #  字符串
            "char": "char",
            "varchar": "varchar",
            "text": "text",
            "nchar": "varchar",
            "nvarchar": "varchar",
            "ntext": "text",
            "bit": "varchar(100)",
            "binary": "text",
            "varbinary": "varchar",
            "image": "text",
            "sql_variant": "text",
            "uniqueidentifier": "char(50)",
            "xml": "text",
            "cursor": "text",
            "table": "text",
        }
        # 本地存储模式对日期字段单独处理， 避免影响datahub
        if get_storage_type(g.code) == DatasetStorageType.DatasetStorageOfLocal.value:
            local_storage_data_type = {
                "datetime": "datetime",
                "date": "datetime",
                "time": "datetime",
                "timestamp": "datetime",
            }
            self.sqlserver_to_mysql.update(local_storage_data_type)
        # 差别字段映射
        self.different_data_type = {
            # 数值
            "smallmoney": "DECIMAL(10,4)",
            "money": "DECIMAL(19,4)",
            # 日期
            "datetime2": "datetime",
            "smalldatetime": "datetime",
            "datetimeoffset": "datetime",
            # 字符串
            "ntext": "text",
            "image": "text",
            "sql_variant": "text",
            "uniqueidentifier": "BINARY(16)",
            "xml": "text",
            "cursor": "text",
            "table": "text",
        }

        # 特殊字段映射
        self.special_data_type = {
            "varchar(max)": "text", "nvarchar(max)": "text",
            "varbinary(max)": "text", "nvarchar(-1)": "text"
        }

    def get_data_type(self, data_type):
        """
        sqlserver对应mysql字段类型
        :param data_type:
        :return:
        """
        data_type = data_type.lower()
        if data_type in self.special_data_type:
            data_type = self.special_data_type.get(data_type)
        params = data_type.split("(")
        data = self.sqlserver_to_mysql.get(params[0])
        if data:
            diff = self.different_data_type.get(params[0])
            if diff or len(params) == 1:
                return data
            else:
                if data == 'varchar':
                    # sqlserver的可以写varchar(max)
                    try:
                        length = int(params[1].replace(")", "")) * 4
                        if length <= 512:
                            return "{d}({p})".format(d=data, p=str(length))
                        else:
                            return "text"
                    except ValueError:
                        return "text"
                else:
                    return "{d}({p}".format(d=data, p=params[1])
        else:
            return "varchar(500)"


class OracleToMysqlTransformService(DbTransformService):
    """
    oracle 转换mysql业务类
    """

    __slots__ = ["oracle_to_mysql", "special_data_type"]

    def __init__(self, dataset_id):
        super().__init__(dataset_id)

        # oracle字段映射mysql
        self.oracle_to_mysql = {
            # 数值
            'number': 'decimal',
            'integer': 'int',
            'decimal': 'decimal',
            'float': 'float',
            'binary_float': 'float',
            'binary_double': 'double',
            'bigint': 'bigint',
            'double': 'double',
            'numeric': 'decimal',
            'real': 'real',
            'smallint': 'smallint',
            'tinyint': 'int',
            'int': 'int',
            # 日期
            'date': 'datetime',
            'timestamp': 'timestamp',
            # 字符串
            'char': 'varchar',
            'varchar': 'varchar',
            'varchar2': 'varchar',
            'nchar': 'varchar',
            'nvarchar': 'varchar',
            'nvarchar2': 'varchar',
            'clob': 'text',
            'nclob': 'text',
            'tinytext': 'text',
            'blob': 'varchar',
            'mediumblob': 'varchar',
            'mediumtext': 'varchar',
            'longblob': 'text',
            'longtext': 'text',
            'long': 'text',
        }

        # 特殊字段映射
        self.special_data_type = {
            "varchar(max)": "text", "nvarchar(max)": "text",
            "varbinary(max)": "text", "nvarchar(-1)": "text"
        }

    def get_data_type(self, data_type):
        """
        oracle对应mysql字段类型
        :param data_type:
        :return:
        """

        if not data_type:
            return "text"
        data_type = data_type.lower()
        if data_type in self.special_data_type:
            data_type = self.special_data_type.get(data_type)
        params = data_type.split("(")
        if params[0] in self.oracle_to_mysql:
            data = self.oracle_to_mysql.get(params[0])
        else:
            data = ""

        if data:
            if data == "text" or len(params) == 1:
                return data
            elif data == "decimal":
                return self.getDecimal(params[1])
            else:
                if data == 'varchar':
                    length = int(params[1].replace(")", "")) * 4
                    if length <= 512:
                        return "{d}({p})".format(d=data, p=str(length))
                    else:
                        return "text"
                else:
                    return "{d}({p}".format(d=data, p=params[1])
        else:
            return "text"

    def getDecimal(self, param):
        if param and ',' in param:
            try:
                places = int(param.split(",")[1].replace(")", ""))
            except ValueError:
                places = -1

            if places < 0:
                return "decimal(65,4)"
            elif places > 30:
                return "decimal(65,30)"
            else:
                return "decimal(65," + param.split(",")[1]
        else:
            return "decimal(65)"


class ExcelToMysqlTransformService(DbTransformService):
    """
    EXCEL转mysql业务类
    """

    def __init__(self, dataset_id):
        super().__init__(dataset_id)

    def get_data_type(self, data_type, data_length=None):
        """
        EXCEL对应mysql字段类型
        :param data_type:
        :param data_length:
        :return:
        """
        if data_type == '数值':
            if data_length is not None:
                if data_length == 0:
                    return 'DECIMAL(18,4)'
                else:
                    return 'DECIMAL({M},{D})'.format(M=30, D=str(data_length))
            else:
                return 'DECIMAL(18,4)'
        elif data_type == '字符串':
            if data_length is not None:
                if data_length == 0:
                    return 'VARCHAR(2)'
                else:
                    return 'TEXT' if data_length * 4 > 5000 else 'VARCHAR({length})'.format(length=str(data_length * 4))
            else:
                return 'varchar(200)'
                # return 'text'
        elif data_type == '日期':
            return 'DATETIME'
        elif data_type == '地址':
            return 'varchar(64)'
        elif data_type == '枚举':
            return 'VARCHAR(100)'
        else:
            return "text"

    def identity_dataset_fields(self, old_fields, queried_fields):
        """
        生成能代表数据集字段的标识. 使用方法参数单元测试
        :param list old_fields: 数据集已有的字段名称
        :param list queried_fields: 的字段list
        :return:
        """
        if not queried_fields:
            raise UserError(message="查询字段不能为空")
        if not self.dataset_id:
            raise UserError(message="缺少参数：dataset_id")

        old_fields = old_fields if old_fields else []
        new_fields = []
        for field in queried_fields:
            field["old_col_name"] = field.get("col_name")
            field["origin_col_name"] = field.get("col_name")
            if field.get("alias_name") not in old_fields:
                # 生成新的field
                new_field_name = "{}_{}".format(
                    strings.get_first_pinyin_hanzi(field.get("alias_name")),
                    strings.fletcher32(self.dataset_id + ":" + field.get("alias_name")),
                )
                field["col_name"] = new_field_name

            if field.get("data_type"):
                field["data_type"] = self.get_data_type(field.get("data_type"))
                # 保留如果是remark或者content还是使用text字段
                if field.get("alias_name").lower() == "remark" or field.get("alias_name").lower() == "content":
                    field["data_type"] = "text"
            else:
                field["data_type"] = "text"

            new_fields.append(field)

        return new_fields

    def identity_dataset_fields_new(self, old_fields, queried_fields):
        """
        生成能代表数据集字段的标识. 使用方法参数单元测试
        :param list old_fields: 数据集已有的字段名称
        :param list queried_fields: 的字段list
        :return:
        """
        old_fields = [row.get('alias_name') for row in old_fields]
        return self.identity_dataset_fields(old_fields, queried_fields)


class ADSTransformService(DbTransformService):
    def get_data_type(self, data_type):
        if data_type.find('time') >= 0 and data_type.find('timestamp') < 0:
            data_type = 'varchar(30)'
        # ADB中的varchar对应MySQL中的char、varchar、text、mediumtext或者longtext。
        # 这里直接使用text, ! varchar在ads中使用db.cur.description，只会返回长度1024， ('xxx', 253, 13, 1024, 1024, 0, 1)
        # 这里把所有的varchar处理成text
        if data_type.startswith('varchar'):
            return 'text'
        # 将bigint(1024) -> bigint(11), bigint展示最大位数限制只能是512
        col = re.findall(r'(\w+)\((\d+)\)', data_type)
        if col:
            col_type, length = col[0][0], int(col[0][1])
            if length > 512 and col_type in ['int', 'bigint', 'tinyint', 'smallint']:
                return f'{col_type}(11)'
        return data_type

    def create_tmp_table(self, origin_columns, old_col_names, create_table=False):
        # ADS数据集没有做好data_type转换，直连不测试创建表
        return super(ADSTransformService, self).create_tmp_table(origin_columns, old_col_names, create_table=False)


class PrestoToMysqlTransformService(DbTransformService):
    """
    Presto 转换mysql业务类
    mysql                                    presto
    test_tingyint        测试tingying        TINYINT
    test_smallint        测试smallint        SMALLINT
    test_mediumint        测试mediumint        INTEGER
    test_int        测试int        INTEGER
    test_bigint        测试bigint        BIGINT
    test_bit        测试bit        VARCHAR
    test_double        测试double        DOUBLE
    test_float        测试float        REAL
    test_decimal        测试decimal        DOUBLE
    test_char        测试char        CHAR
    test_varchar        测试varchar        VARCHAR
    test_date        测试date        VARCHAR
    test_time        测试time        VARCHAR
    test_year        测试year        INTEGER
    test_timestamp        测试_timestamp        TIMESTAMP(3)
    test_datetime        测试datetime        TIMESTAMP(3)
    test_tinyblob        测试_tinyblob        VARCHAR
    test_blob        测试blob        VARCHAR
    test_tinytext        测试tinytext        VARCHAR
    test_text        测试text        VARCHAR
    test_enum        测试_enum        VARCHAR
    test_binary        测试binary        VARCHAR
    test_longtext        测试longtext        VARCHAR
    """

    __slots__ = ["presto_to_mysql"]

    def __init__(self, dataset_id):
        super().__init__(dataset_id)

        # presto字段映射mysql
        self.presto_to_mysql = {
            # 布尔值
            'boolean': 'varchar',
            # 数值
            'integer': 'int',
            'decimal': 'decimal',
            'bigint': 'bigint',
            'double': 'double',
            'real': 'float',
            'smallint': 'smallint',
            'tinyint': 'tinyint',
            # 日期
            'date': 'datetime',
            'timestamp': 'timestamp',
            'time': 'timestamp',
            'time with time zone': 'text',
            'timestamp with time zone': 'text',
            # 字符串
            'char': 'char',
            'varchar': 'varchar',
            'varbinary': 'text',
            'json': 'text',
        }

    def get_data_type(self, data_type):
        """
        oracle对应mysql字段类型
        :param data_type:
        :return:
        """

        if not data_type:
            return "text"
        data_type = data_type.lower()
        params = data_type.split("(")
        if params[0] in self.presto_to_mysql:
            data = self.presto_to_mysql.get(params[0])
        else:
            data = ""

        if data:
            if data == "text" or len(params) == 1:
                return data
            else:
                if data == 'varchar':
                    length = int(params[1].replace(")", "")) * 4
                    if length <= 512:
                        return "{d}({p})".format(d=data, p=str(length))
                    else:
                        return "text"
                else:
                    return "{d}({p}".format(d=data, p=params[1])
        else:
            return 'text'
