from functools import wraps
import errno
import os
import signal
import threading

from typing import Callable


class CustomTimeoutError(Exception):
    pass


# # 使用信号来实现函数执行超时限制，会有一定的限制，比如不能在多线程中使用。
# def timeout(seconds: int = 10, error_message: str = os.strerror(errno.ETIME)) -> Callable:
#     def decorator(func):
#         def _handle_timeout(signum, frame):
#             raise CustomTimeoutError(error_message)
#
#         def wrapper(*args, **kwargs):
#             signal.signal(signal.SIGALRM, _handle_timeout)
#             signal.setitimer(signal.ITIMER_REAL, seconds)  # used timer instead of alarm
#             try:
#                 result = func(*args, **kwargs)
#             finally:
#                 signal.alarm(0)
#             return result
#
#         return wraps(func)(wrapper)
#
#     return decorator


# 以下是使用多线程的实现
class InterruptableThread(threading.Thread):
    def __init__(self, func, *args, **kwargs):
        threading.Thread.__init__(self)
        self._func = func
        self._exception = None
        self._args = args
        self._kwargs = kwargs
        self._result = None

    def run(self):
        try:
            self._result = self._func(*self._args, **self._kwargs)
        except Exception as e:
            self._exception = e

    @property
    def result(self):
        return self._result

    @property
    def exception(self):
        return self._exception


class timeout(object):  # NOSONAR
    def __init__(self, sec):
        self._sec = sec

    def __call__(self, f):
        def wrapped_f(*args, **kwargs):
            it = InterruptableThread(f, *args, **kwargs)
            it.start()
            it.join(self._sec)
            if it.exception:
                raise it.exception
            if not it.is_alive():
                return it.result
            raise CustomTimeoutError(f'{f.__qualname__} Timer expired')

        return wrapped_f
