#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
from urllib.parse import quote, unquote, urlparse
from builtins import staticmethod

import curlify
import hug
import jwt
import requests
from loguru import logger

from base.enums import SkylineApps
from components import auth_util
from components.app_hosts import AppHosts
from dmplib.utils.errors import UserError
from user.models import WorkbenchTokenData, WorkbenchTokenCheck, WorkbenchJwt


class AuthConstants(object):
    # 固定值
    CLIENT_ID = 'dmp'

    # 指定授权模式。可以是 authorization_code（授权码模式）、或 refresh_token（刷新令牌）。
    AUTHORIZATION_GRANT_TYPE = 'authorization_code'
    REFRESH_GRANT_TYPE = 'refresh_token'

    # 要检查的令牌类型的参数，可以是 access_token 或 refresh_token
    ACCESS_TOKEN_TYPE = 'access_token'
    REFRESH_TOKEN_TYPE = 'refresh_token'

    # 获取认证服务器地址信息
    GET_BASIC_INFO_URI = '/oauth2/.well-known/oauth-authorization-server'
    # 跳转工作台登录
    DO_AUTHORIZATION_URI = '/oauth2/authorize'
    # 获取jwt秘钥
    GET_JWKS_URI = '/oauth2/jwks'
    # 获取token
    GET_TOKEN_URI = '/oauth2/token'
    # 验证token
    DO_CHECK_URI = '/oauth2/introspect'
    # 注销token
    DO_LOGOUT_URI = '/oauth2/revoke'
    # 免密登录
    DO_LOGIN_WITHOUT_PWD = '/login/login-without-pwd'


class WorkbenchAuth:
    _instance = None

    def __init__(self):
        self.host = AppHosts.get(SkylineApps.WORKBENCH_AUTH)
        self.host = self.host.rstrip('/')
        # if self.host.rstrip('/').endswith('/auth'):
        #     self.host = self.host[0:-5]
        self._config()

    def _config(self):
        # 获取认证服务地址
        resp = requests.get(self.host + AuthConstants.GET_BASIC_INFO_URI)
        if resp.status_code != 200:
            raise UserError(message=f'获取工作台登录服务信息异常:{str(resp.text)}')
        data = json.loads(resp.text)
        self.auth_host = data.get('issuer')
        self.do_authorization_url = data.get('authorizationEndpoint',
                                             self.auth_host + AuthConstants.DO_AUTHORIZATION_URI)
        self.get_token_url = data.get('tokenEndpoint', self.auth_host + AuthConstants.GET_TOKEN_URI)
        self.get_jwks_url = data.get('jwksUri', self.auth_host + AuthConstants.GET_JWKS_URI)
        self.do_check_url = data.get('introspectEndpoint', self.auth_host + AuthConstants.DO_CHECK_URI)
        self.do_logout_url = data.get('revokeEndpoint', self.auth_host + AuthConstants.DO_LOGOUT_URI)
        self.do_login_without_pwd_url = data.get('revokeEndpoint', self.auth_host + AuthConstants.DO_LOGIN_WITHOUT_PWD)

    def do_login_without_pwd(self, tenant_code, account, source = 'pc'):
        jwt = auth_util.gen_auth_token()
        params = {
            "clientId": "dmp",
            # "clientSecret": "278c12e4c856cf575fc75cbf4133325c",
            "clientSecret": jwt,
            "source": source,
            "tenantCode": tenant_code,
            "userCode": account
        }
        data = self._do_request('POST', self.do_login_without_pwd_url, json=params)
        token = data.get('accessToken')
        return token


    def do_auth_redirect(self, redirect_uri, state, tenant_code='', response_type='code'):
        # 重定向到登录服务登录页
        params = f'client_id={AuthConstants.CLIENT_ID}&redirect_uri={self._encode_url(redirect_uri)}&state={state}&response_type={response_type}'
        if tenant_code:
            params = params + f'&tenant_code={tenant_code}'
        url = self.do_authorization_url + '?' + params
        logger.error(f'跳转至天际统一身份认证登录页:{tenant_code}, {url}')
        return hug.redirect.to(url)

    def get_jwk(self):
        # 获取jwk
        # data = self._do_request('GET', self.get_jwks_url)
        # keys = data.get('keys')
        # return keys[0]
        return auth_util.get_recently_jwk()

    def get_token(self, grant_type, auth_code=None, redirect_uri=None, refresh_token=None):
        # 根据授权码获取token
        jwt = auth_util.gen_auth_token()
        params = {
            'grant_type': grant_type,
            'code': auth_code,
            'redirect_uri': redirect_uri,
            'refresh_token': refresh_token,
            'client_id': AuthConstants.CLIENT_ID,
            'client_secret': jwt
        }
        # data = {'access_token': 'eyJraWQiOiI4NDM1Yjc5ZS1kMDhmLTRhOTQtODA1NS1iMjk4NzIxMWUwNjAiLCJhbGciOiJSUzI1NiJ9.eyJuYmYiOjE3MDQ2OTkwNTcsInVzZXJfY29kZSI6ImFkbWluIiwidXNlcl9uYW1lIjoi57O757uf566h55CG5ZGYIiwiaXNzIjoid29ya2JlbmNoIiwidXNlcl9ndWlkIjoiNDIzMGJjNmUtNjllNi00NmE5LWEzOWUtYjkyOWEwNmE4NGU4Iiwic291cmNlIjoicGMiLCJleHAiOjE3MDQ3ODU0NTcsInRva2VuX3R5cGUiOiJhY2Nlc3NfdG9rZW4iLCJpYXQiOjE3MDQ2OTkwNTcsImp0aSI6IjM2NWE2YjhjLTg2NTAtNGI5ZC05N2FiLTg3OGQ5MmZmZWQ4OSIsInRlbmFudF9jb2RlIjoidGVzdDA4MjAifQ.CvLW7bgGvetMqE6WXQ3y4EthwGn7psUslHx31_Rk7Y_X7qpQO1S0almdj1tgeLLyt1D2ZxhUssOP3ft9Z19T4N0x_J-a4flYXC6U_fVchkoNewb0NSwXpNRx_LJahHD3EM2VOqHqLDTGx2bIKDrxhn_cOZ2sEsFAv0zhS-Hlm6CvgUJwP3uV3sY0UsIhy-U7VZEIG8Wu6rsQxG7mAErvnel-b5cvarPEcqMkjPa5jxfZGanMvz4tQj9YsbNjTlKadd6a7YBpD_zU5JXzx8ciglLRyAElSEYBX8AKI2dY5pYZMlGrOUwxWRRQwQiS2S7m_HZ1qVa3jKitJLkOShN_rw', 'token_type': 'Bearer', 'expires_in': None, 'refresh_token': '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'}
        data = self._do_request('POST', self.get_token_url, params)
        token_data = WorkbenchTokenData(**data)
        token_data.access_payload = WorkbenchJwt(**self._parse_jwt(token_data.access_token))
        token_data.refresh_payload = WorkbenchJwt(**self._parse_jwt(token_data.refresh_token))
        return token_data

    def logout(self, token, type):
        data = self._do_request('POST', self.do_logout_url, {'token': token, 'token_type_hint': type})
        return data

    @staticmethod
    def _encode_url(url):
        # decoded_url = unquote(url)
        # if decoded_url != url:
        #     return url
        return quote(url)

    @staticmethod
    def _parse_jwt(token):
        header = jwt.get_unverified_header(token)
        payload = jwt.decode(token, '', algorithms=header['alg'], options={'verify_signature': False})
        if not payload:
            return {}
        return payload

    def check_token(self, token, type):
        # 检查token是否有效
        params = {
            'token': token,
            'token_type_hint': type
        }
        data = self._do_request('POST', self.do_check_url, params)
        model = WorkbenchTokenCheck(**data)
        model.is_logged_out_by_other = data.get('isLoggedOutByOther', False)
        model.token_type = data.get('tokenType')
        return model

    def _do_request(self, method, url, params=None, json=None, headers={}):
        try:
            if method.upper() == "GET":
                res = requests.get(url, params=params, headers=headers)
            else:
                if json:
                    headers['Content-Type'] = 'application/json'
                else:
                    headers['Content-Type'] = 'application/x-www-form-urlencoded'
                res = requests.post(url, params=params, json=json, headers=headers)
            result = res.text
            curl_info = curlify.to_curl(res.request, compressed=True)
            logger.error(f"工作台登录服务请求 curl: {curl_info}, params: {params or json}, res: {result}")
            if res.status_code != 200:
                raise UserError(message=f'请求异常:{result}')
            return res.json()
        except Exception as e:
            message = "工作台登录服务请求错误: {}".format(str(e))
            logger.error(message)
            raise UserError(message=message) from e

    @staticmethod
    def get():
        if not WorkbenchAuth._instance:
            WorkbenchAuth._instance = WorkbenchAuth()
        return WorkbenchAuth._instance
