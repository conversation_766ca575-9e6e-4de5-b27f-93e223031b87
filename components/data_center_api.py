#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2021/3/22.
"""
import json
import time
from loguru import logger

from components.app_hosts import AppHosts
from dmplib.saas.project import get_mysoft_erp
from dmplib.utils.errors import UserError
from components.mysoft import CloudAPI
from base.enums import DatasetEditMode, DataSourceType, SkylineApps
from base import repository
from base.models import BaseModel
from dmplib.constants import ADMINISTRATORS_ID
from components.utils import get_default_account
from dmplib.hug import g
from base.enums import MysoftNewERPDataFromType, MysoftNewERPDataBaseType
from data_source.models import DataSourceModel, MysoftNewERPConnStrModel, MysoftNewERPConfigType
from data_source.enums.mysoft_new_erp_enums import MysoftNewERPAPI
from components.storage_setting import is_history_dataset
from components.redis_utils import stale_cache


def request_data_center(action_name: str, params: dict = None, erp_site=None, erp_api_info_id=None,
                        dataset_content=None):
    """
    请求数据服务中心公共方法
    :param action_name 接口名称
    :param params 接口参数
    :param erp_site 接口参数
    :param erp_api_info_id 接口参数
    :param dataset_content 接口参数
    """
    if params is None:
        params = dict()
    params['action_name'] = action_name
    # 获取接口管家地址
    try:
        if not erp_api_info_id:
            conn_str = params.get('DataInfo', {}).get('DataSourceConnStr') or {}
            if isinstance(conn_str, str):
                conn_str = json.loads(conn_str)
            elif isinstance(conn_str, BaseModel):
                conn_str = conn_str.get_dict()
            erp_api_info_id = conn_str.get('erp_api_info_id') or ''
    except Exception as e:
        logger.info(f"未获取到 数据源绑定的接口管家: {e}")
        erp_api_info_id = ''
    finally:
        try:
            del params['DataInfo']['DataSourceConnStr']
        except:
            pass
    params['IsDataCenter'] = is_history_dataset(g.code, erp_api_info_id=erp_api_info_id)
    erp_site = get_mysoft_erp(erp_api_info_id=erp_api_info_id,
                              dataset_content=dataset_content) if erp_site is None else erp_site
    if not erp_site['erpapi_host']:
        raise UserError(message='未配置接口管家地址!')
    erp_site['erpapi_access_id'] = '' if erp_site['erpapi_access_id'] is None else erp_site['erpapi_access_id']
    erp_site['erpapi_access_secret'] = (
        '' if erp_site['erpapi_access_secret'] is None else erp_site['erpapi_access_secret']
    )
    api = CloudAPI(
        erp_site['erpapi_host'],
        erp_site['erpapi_access_id'],
        erp_site['erpapi_access_secret'],
        CloudAPI.DATA_CENTER_PATH,
    )
    # 请求接口
    return api.datacenter_request(params, 'post')


def get_data_by_sql(sql: str, data_source_model: DataSourceModel = None,
                    is_need_column_struct: bool = False, sql_type=1, table_place_holder=None,
                    table_text="", sql_target_text="", dataset_id=""):
    """
    MysoftNewERP数据源，通过SQL获取结果
    :param table_place_holder 复杂sql的临时表名称
    :param sql_type 1：简单，2：复杂
    :param sql 执行的SQL语句
    :param data_source_model 数据源对象
    :param is_need_column_struct 是否需要返回查询SQL列字段信息
    :param table_text
    :param sql_target_text
    :param dataset_id
    """
    if data_source_model is None or not isinstance(data_source_model, DataSourceModel):
        raise UserError(message='数据源对象异常，获取结果失败')

    data_info = {"DataInfo": {
        "SqlText": sql,
        "SqlType": sql_type,
        "TableText": table_text,
        "SqlTargetText": sql_target_text,
        "TablePlaceholder": table_place_holder,
        "IsNeedColumnStruct": is_need_column_struct,
        "DataSourceModel": get_data_source_info(data_source_model.conn_str),
        "DataSourceConnStr": data_source_model.conn_str,
        "DMPDatasetColumnModels": get_dataset_and_fields(dataset_id),
        "IsNeedProcedure": get_dataset_need_procedure(dataset_id),
    }}
    user_info = get_third_user_info()
    data_info.update(user_info)
    return request_data_center(MysoftNewERPAPI.GetData.value, data_info)


def data_clean_by_sql(sql, data_source_model: DataSourceModel = None, table_name="", dataset={},
                      fields=None):
    """
    执行数据服务中心sql
    :param sql:
    :param data_source_model:
    :param table_name:
    :param dataset:
    :param fields:
    :return:
    """
    params = {
        "DataInfo": {
            "DataSetGUID": dataset.get("id", ""),
            "DataSetName": dataset.get("name", ""),
            "SqlText": sql,
            "DMPDatasetColumnModels": get_field_mapping(
                dataset.get("edit_mode"), fields, is_sync=True),
            "ResultTableName": table_name,
            "DataSourceModel": get_data_source_info(data_source_model.conn_str),
            "DataSourceConnStr": data_source_model.conn_str
        }
    }
    user_info = get_third_user_info()
    params.update(user_info)
    result = request_data_center(MysoftNewERPAPI.DatasetClean.value, params)
    # 轮询判断
    if not is_history_dataset(g.code, conn_str=data_source_model.conn_str):
        start = time.time()
        clear_time = 30
        while start + int(clear_time) >= time.time():
            res = query_dataset_clear_status(
                result.get('DatasetGUID'), result.get('DatasetVersion'), conn_str=data_source_model.conn_str
            )
            # 0:未执行 1:执行中 2:已完成未异常 3:已完成有异常
            status = int(res.get("Status") or 0)
            if status == 2:
                return
            elif status in [0, 1]:
                time.sleep(2)
                continue  # NOSONAR
            else:
                raise UserError(message=f"数据服务中心清洗失败: {res.get('ExceptionInfo')}")
        raise UserError(message=f"数据集清洗超时，超时时间{clear_time}")


def get_field_mapping(edit_mode, dataset_fields, is_sync=False):
    """
    :param edit_mode:
    :param dataset_fields:
    :param is_sync:
    :return:
    """
    new_dataset_fields = []
    for field in dataset_fields:
        if field.get('type') != "普通":
            continue
        if is_sync:
            new_dataset_fields.append({
                "OldColumnName": field.get("col_name"),
                "ColumnName": field.get("col_name"),
                "OriginColName": field.get("origin_col_name") or field.get("alias_name"),
                "Rank": field.get("rank") or '',
                "DataType": field.get("data_type")
            })
        else:
            new_dataset_fields.append({
                "OldColumnName": field.get(
                    "col_name") if edit_mode == DatasetEditMode.Relation.value else field.get(
                    "origin_col_name"),
                "ColumnName": field.get("col_name"),
                "OriginColName": field.get("origin_col_name") or field.get("alias_name"),
                "Rank": field.get("rank") or '',
                "DataType": field.get("data_type")
            })
    return new_dataset_fields


def get_data_source_info(conn_str: MysoftNewERPConnStrModel):
    """
    构建MysoftNewERP类型数据源的链接信息
    :param conn_str 链接信息对象
    """
    if conn_str.DataFrom not in [MysoftNewERPDataFromType.MYSOFT_ERP.value,
                                 MysoftNewERPDataFromType.MYSOFT_SAAS.value, MysoftNewERPDataFromType.NO_MYSOFT.value]:
        raise UserError(message='数据源类型异常，获取结果失败')

    if not isinstance(conn_str, MysoftNewERPConnStrModel):
        raise UserError(message='数据源类型错误，获取结果失败')
    params = dict()
    if conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_ERP.value:
        params = _mysoft_erp_data_source_params(conn_str)
    elif conn_str.DataFrom == MysoftNewERPDataFromType.MYSOFT_SAAS.value:
        params = _mysoft_saas_data_source_params(conn_str)
    elif conn_str.DataFrom == MysoftNewERPDataFromType.NO_MYSOFT.value:
        params = _no_mysoft_data_source_params(conn_str)
    # 用户信息
    db_type_info = {"ConfigType": conn_str.ConfigType, "DbType": conn_str.DbType, "AppLevelCode": conn_str.AppLevelCode}
    params.update(db_type_info)
    return params


def _mysoft_erp_data_source_params(conn_str: MysoftNewERPConnStrModel):
    """
    明源erp系统数据源请求参数
    :param conn_str:
    :return:
    """
    params = dict()
    if conn_str.ConfigType == MysoftNewERPConfigType.CONFIG_CENTER.value:
        params = {
            "AppId": conn_str.AppId,
            "SiteGroupKey": conn_str.SiteGroupKey,
            "EnvironmentId": conn_str.EnvironmentId,
            "IsShuXin": conn_str.IsShuXin,
            "ProjectCode": conn_str.ProjectCode
        }
    elif conn_str.ConfigType == MysoftNewERPConfigType.MANUAL_INPUT.value:
        params = {
            "Server": conn_str.Server,
            "Port": conn_str.Port,
            "Database": conn_str.Database,
            "Uid": conn_str.Uid,
            "Pwd": conn_str.Pwd
        }
    return params


def _mysoft_saas_data_source_params(conn_str: MysoftNewERPConnStrModel):
    """
    明源三云数据源请求参数
    :param conn_str:
    :return:
    """
    params = dict()
    db_type = conn_str.DbType.lower()
    # 三云api连接
    if db_type == MysoftNewERPDataBaseType.Cloud_Mysql.value:
        params = {
            "Server": conn_str.Server,
            "Port": conn_str.Port,
            "Database": conn_str.Database,
            "AppLevelCode": conn_str.AppLevelCode,
            "Pwd": conn_str.Pwd
        }
    elif db_type == MysoftNewERPDataBaseType.Mysql.value:
        # 三云mysql连接
        params = {
            "Server": conn_str.Server,
            "Port": conn_str.Port,
            "Database": conn_str.Database,
            "Uid": conn_str.Uid,
            "Pwd": conn_str.Pwd
        }
    return params


def _no_mysoft_data_source_params(conn_str: MysoftNewERPConnStrModel):
    """
    明源异构系统请求参数
    :param conn_str:
    :return:
    """
    db_type = conn_str.DbType.lower()
    # mysql、SqlServer默认参数
    params = {
        "Server": conn_str.Server,
        "Port": conn_str.Port,
        "Database": conn_str.Database,
        "Uid": conn_str.Uid,
        "Pwd": conn_str.Pwd
    }
    if db_type == MysoftNewERPDataBaseType.Oracle.value.lower():
        params['DbRole'] = conn_str.DbRole
    return params


def get_preview_data_of_sql(sql: str, data_source_model: DataSourceModel = None, is_need_column_struct: bool = False,
                            top_number=100, disable_procedure=0):
    """
    数据集预览接口
    {
    "ActionName":"DMPDataset/GetPreviewData",
    "Token":"07257012F3025D808DB9B61FA086DB11",
    "Timestamp":"",
    "Data":{
        "DataInfo":{
            "SqlText":"declare @a uniqueidentifier select '1' as ceshi from mdc_table",
            "IsNeedColumnStruct":true,
            "TopNumber":100,
            "DataSourceModel":{
                "ConfigType":2,
                "DbType":"sqlserver",
                "AppLevelCode":"1000.1401",
                "AppId":"erp60",
                "SiteGroupKey":"v2.0",
                "EnvironmentId":"product"
                }
            }
        }
    }
    """
    if data_source_model is None or not isinstance(data_source_model, DataSourceModel):
        raise UserError(message='数据源对象异常，获取结果失败')

    data_info = {
        "DataInfo": {
            "SqlText": sql,
            "IsNeedColumnStruct": is_need_column_struct,
            "TopNumber": top_number,
            "DataSourceModel": get_data_source_info(data_source_model.conn_str),
            "DataSourceConnStr": data_source_model.conn_str,
            "IsProcedureDisabled": True if disable_procedure else False
        }}
    user_info = get_third_user_info()
    data_info.update(user_info)
    return request_data_center(MysoftNewERPAPI.GetPreviewData.value, data_info)


def get_third_user_info():
    """
    获取第三方服务的用户信息
    :return:
    """
    # 为了测试环境通过云空间接口访问，写上默认admin用户和id
    userid = ADMINISTRATORS_ID
    account = get_default_account(getattr(g, 'code', None))
    user_info = {
        "UserInfo": {"UserCode": account,
                     "ThirdUserCode": {
                         "8011": account,
                         "80111": account,
                         "80112": account,
                         "80113": account,
                         "80114": account,
                         "8006": account,
                     },
                     "ThirdUserId": {
                         "8011": userid,
                         "80111": userid,
                         "80112": userid,
                         "80113": userid,
                         "80114": userid,
                         "8006": userid,
                     }}
    }
    return user_info


def get_hd_dataset_node(subject_id, plan_start_time, plan_end_time):
    """
    主题数据集推送模式状态获取
    """
    data_info = {
        "subject_id": subject_id,
        "plan_start_time": plan_start_time,
        "plan_end_time": plan_end_time
    }
    return request_data_center(MysoftNewERPAPI.GetPackageStatus.value, params=data_info)


def hd_dataset_blood_parse(subject_id, target_time):
    """
    主题数据集推送模式状态获取
    """
    data_info = {
        "subject_id": subject_id,
        "target_time": target_time
    }
    return request_data_center(MysoftNewERPAPI.GetPackageBlood.value, params=data_info)


@stale_cache(prefix='erp_datasource', expire=60)
def get_new_erp_datasource_model(dataset_content=None, conn_str=None, safe_mode: bool = None):
    """
    获取数据服务中心数据源
    参数说明：
    mysoftnewerp数据源的数据集需要传 data_source_content参数
    excel、union数据集需要传 dataset_content (组合数据集新增、编辑时需要根据引用的数据集设置对应的bind_source_id)
    :return:
    """
    # MysoftNewERP需要获取特定的数据源
    # 获取mysoftNewErpSource
    from data_source.services.data_source_service import load_data_source_conn_str

    bind_source_id = None
    erp_api_info_id = None
    data = None

    if dataset_content:
        try:
            if isinstance(dataset_content, str):
                dataset_content = json.loads(dataset_content)
            bind_source_id = dataset_content.get("bind_source_id")
        except Exception as e:
            logger.error(f"json loads dataset_content error: {e}")

    if not bind_source_id and conn_str:
        try:
            if isinstance(conn_str, BaseModel):
                conn_str = conn_str.get_dict()
            if isinstance(conn_str, str):
                conn_str = json.loads(conn_str)
            erp_api_info_id = conn_str.get("erp_api_info_id")
        except Exception as e:
            logger.error(f"json loads dataset_content error: {e}")

    if bind_source_id:
        data = repository.get_data("data_source", {'id': bind_source_id})

    if not data and erp_api_info_id:
        data = repository.get_data(
            'data_source',
            {
                'type': DataSourceType.MysoftNewERP.value,
                'app_level_code': "1000.1401",
                'conn_str like': f'%{erp_api_info_id}%'
            },
            None
        )

    if not data:
        data = get_master_local_db_new_erp_data_source()
    if not data:
        return None
    model = DataSourceModel(**data)
    load_data_source_conn_str(model, safe_mode)
    return model


@stale_cache(prefix="master_local_db", expire=60, random_time=5)
def get_master_local_db_new_erp_data_source():
    data = None
    data_list = repository.get_list(
        'data_source',
        {
            'type': DataSourceType.MysoftNewERP.value,
            'app_level_code': "1000.1401",
        },
        None
    ) or []
    if not data_list:
        raise UserError(message="未配置数据服务中心数据源")
    elif len(data_list) == 1:
        data = data_list[0]
    else:
        for i in data_list:
            if json.loads(i.get("conn_str")).get('is_master_local_db') in [1, '1']:
                data = i
                break
        data = data or data_list[0]
    return data


@stale_cache(prefix="dataset_bind_source_id", expire=60, random_time=5)
def get_local_data_bind_source_id(data_source):
    from data_source.services.data_source_service import load_data_source_conn_str

    if data_source and isinstance(data_source, dict):
        data_source = DataSourceModel(**data_source)
        load_data_source_conn_str(data_source)

    if data_source and data_source.app_level_code == '1000.1401':
        bind_source_id = data_source.id
        return bind_source_id
    else:
        if data_source and data_source.conn_str.erp_api_info_id:
            data = repository.get_data(
                'data_source',
                {'conn_str like': '%{}%'.format(data_source.conn_str.erp_api_info_id), 'app_level_code': "1000.1401"},
                ['id']
            )
            if not data:
                return None
            return data.get('id')
        else:
            bind_source_id = None
            data_source_list = repository.get_list(
                'data_source', {'type': DataSourceType.MysoftNewERP.value, 'app_level_code': "1000.1401"},
                ['id', 'conn_str'])
            if len(data_source_list) == 1:
                bind_source_id = data_source_list[0].get('id')
            else:
                for data_source in data_source_list:
                    try:
                        conn = json.loads(data_source.get('conn_str'))
                    except:
                        conn = {}
                    if conn.get('is_master_local_db') == 1:
                        bind_source_id = data_source.get('id')
                        break
            return bind_source_id


def query_dataset_clear_status(task_id, dataset_version, conn_str=''):
    """
    获取数据集清洗状态的接口
    """

    params = {
        "DataInfo": {
            "DataSourceModel": get_data_source_info(conn_str),
            "DatasetGUID": task_id,
            "DatasetVersion": dataset_version,
            "DataSourceConnStr": conn_str
        }
    }
    return request_data_center(MysoftNewERPAPI.QueryDatasetTaskClean.value, params)


def local_execute_sql(sql, conn_str=None, data_content=None):
    data_source = get_new_erp_datasource_model(conn_str=conn_str)
    if data_source is None:
        raise UserError(message='数据源对象异常，获取结果失败')
    return request_data_center(
        MysoftNewERPAPI.DMPDatasetExecuteSql.value,
        params={
            "DataInfo": {
                "DataSourceModel": get_data_source_info(data_source.conn_str),
                "ExecuteSql": sql,
                "DataSourceConnStr": conn_str,
            }
        },
        dataset_content=data_content
    )


def get_dataset_and_fields(dataset_id: str):
    if not dataset_id:
        return []
    fields = ['col_name', 'origin_col_name', 'alias_name', 'rank', 'data_type', 'type']
    dataset_field = repository.get_list('dataset_field', {'dataset_id': dataset_id}, fields)
    if not dataset_field:
        return []
    return get_field_mapping('', dataset_field, is_sync=True)


def get_dataset_need_procedure(dataset_id: str):
    if not dataset_id:
        return None
    is_need_procedure = repository.get_data_scalar('dataset', {'id': dataset_id}, 'is_need_procedure') or 2
    return True if is_need_procedure == 1 else False


def get_erp_site_url():
    """
    获取erp系统的站点url接口
    """
    from components import auth_util

    erp_site_list = []
    try:
        if auth_util.is_env_enable_skyline_auth():
            erp_site_list = assign_erp_url()
        else:
            erp_site_list = request_data_center(MysoftNewERPAPI.ConfigGetERPSiteUrl.value)
    except Exception as e:
        logger.error("获取erp系统的站点url接口错误，errs:" + str(e))

    if erp_site_list:
        for item in erp_site_list:
            if "appCode" in item:
                item["app_code"] = item.get("appCode")
                del item["appCode"]
            if "appName" in item:
                item["app_name"] = item.get("appName")
                del item["appName"]
    return erp_site_list


def assign_erp_url():
    erp_site_list = []
    url = AppHosts.get(SkylineApps.APAAS, False)
    apps = repository.get_list('product_application_relation', {'is_erp': 1}, ['app_code', 'app_name'],
                               from_config_db=True)
    for item in apps:
        app_code = item.get('app_code')
        if app_code == '0000':
            continue
        erp_site_list.append({
            'app_code': app_code,
            'app_name': item.get('app_name'),
            'url': url})
    erp_site_list.append({'app_code':'0000', 'app_name':'建模平台', 'url': url})
    erp_site_list.append({'app_code':'9999', 'app_name':'其它ERP子系统', 'url': url})
    return erp_site_list