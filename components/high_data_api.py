#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    HighData相关接口调用api
    Author: lul05
    Time: 2021-1-19 15:11:10
"""
import requests
import jwt
import base64
from hashlib import md5
from requests import RequestException
import json
from loguru import logger

from components.erpapi_manager_api import get_corpcode
from dmplib import config
from dmplib.utils.errors import HttpError, UserError
from datetime import datetime, timedelta
from integrate import integrate_constant
from base.dmp_constant import YZS_CHANNEL_ID


class HighDataApi:
    """
    HighData接口调用API
    """

    __slots__ = ['hd_tenant_id']

    API_PATH = 'report/api/dmp-report'
    ERP_API_PATH = '/api/get-corp-tenants-code-by-yzs-tenant-code'

    def __init__(self, hd_tenant_id):
        super().__init__()
        self.hd_tenant_id = hd_tenant_id

    def api_request(self, action_name, params: dict):
        """
        请求API
        :param action_name
        :param params:
        :return:
        """
        host = config.get('Yzs.domain', 'https://qy-ci.fdccloud.com')
        host = host[: len(host) - 1] if host.endswith('/') else host
        url = '%s/%s/%s' % (host, self.API_PATH, action_name)
        params["token"] = self.get_token()
        try:
            logger.info(f"HighData请求url：{url}")
            logger.info(f"HighData请求参数：{json.dumps(params, ensure_ascii=False)}")
            response = requests.post(url, json=params, timeout=30)
        except Exception as be:
            msg = "请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(params))
            raise HttpError(message=msg, status=response.status_code)

        logger.info(f"HighData请求响应：{response.text}")
        if response.status_code == 200:
            result = response.json()
            if not result.get('isSuccess'):
                raise UserError(message=result.get('message'))
            return result
        else:
            msg = "错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
            raise HttpError(message=msg, status=response.status_code)

    def get_token(self, time_out=300):
        """
        获取秘钥
        :return string:
        """
        try:
            hd_tenant_id = self.hd_tenant_id
            if not hd_tenant_id:
                raise Exception("未找到HighData租户ID信息")

            payload = {"exp": self.get_exp(time_out), "tenant_id": hd_tenant_id, "channel_id": YZS_CHANNEL_ID}
            secret_key = self._get_secret_key()

            safe_secret = base64.urlsafe_b64encode(secret_key.encode('utf-8')).decode('utf-8')
            safe_secret = safe_secret.replace('=', '')
            token = jwt.encode(payload, safe_secret)

            return token
        except RequestException as e:
            raise UserError(message='获取jwt token失败:' + str(e))

    def _get_secret_key(self):
        """
        获取jwt 的秘钥
        :return string:
        """
        secret_key = integrate_constant.HIGH_DATA_SECRET_KEY
        key = self.hd_tenant_id + secret_key + YZS_CHANNEL_ID
        return md5(key.encode("utf-8")).hexdigest()

    @staticmethod
    def get_exp(time_out=300):
        """
        获取exp时间
        :return:
        """
        # 获取当前时间
        d1 = datetime.now()
        # 当前时间加上300秒
        d2 = d1 + timedelta(seconds=time_out)
        return d2

    @staticmethod
    def get_hd_tenant_info_by_code(tenant_code):
        corp_list = get_corpcode(tenant_code, 3)
        for item in corp_list:
            if item and 'prod_code' in item and item['prod_code'] == 'fdccloud':
                return item
        return []
