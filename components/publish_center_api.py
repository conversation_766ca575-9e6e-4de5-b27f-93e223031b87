import jwt
import requests
import re

from components import auth_util
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError


def get_tenant_develop_mode():
    res = PublishCenterApi().get_develop_mode()
    return res


class PublishCenterApi:

    def __init__(self):
        self.host = config.get('PublishCenter.host')
        self.key = ''
        return

    def sync_template_data(self, files):
        route_url = '/openapi/distribution/daily_auto_sync'
        params = {"tenant": g.code, 'env': auth_util.get_env_name(), 'files': files}
        try:
            res = self._api_request(route_url, params)
        except Exception as e:
            raise UserError(message=str(e))
        if res.get('code') == 200:
            return res.get('data')
        else:
            raise UserError(message=str(res.get('message')))

    def get_develop_mode(self):
        route_url = '/openapi/env/get_develop_mode'
        params = {"tenantCode": g.code, 'env': auth_util.get_env_name()}
        try:
            res = self._api_request(route_url, params)
        except Exception as e:
            raise UserError(message=str(e))
        if res.get('code') == 200:
            return res.get('data')
        else:
            raise UserError(message=str(res.get('message')))


    def set_develop_mode(self, develop_mode):
        route_url = '/openapi/env/set_develop_mode'
        params = {"tenantCode": g.code, 'env': auth_util.get_env_name(), 'developMode': develop_mode.upper()}
        try:
            res = self._api_request(route_url, params)
        except Exception as e:
            raise UserError(message=str(e))
        if res.get('code') == 200:
            return res.get('data')
        else:
            raise UserError(message=str(res.get('message')))


    def _api_request(self, route_url, parameters=None, timeout=None, request_type='POST'):
        """
        请求API
        :param parameters:
        :param int timeout:
        :return:
        """

        if not self._validation_url(self.host):
            raise UserError(message='无效的url：' + self.host)
        try:
            account = getattr(g, 'account', 'SYSTEM')
            auth = jwt.encode({'account': account}, config.get("PublishCenter.secret"), 'HS256')
            headers = {'Authorization': f'Bearer {auth}'}
            self.host = self.host[:len(self.host) - 1] if self.host.endswith('/') else self.host
            if request_type == 'POST':
                response = requests.post(self.host + route_url, json=parameters, headers=headers, timeout=timeout or 30)
            else:
                response = requests.get(self.host + route_url, params=parameters, headers=headers, timeout=timeout or 30)
            if response.status_code != 200:
                raise UserError(message='url：{url},error:{error},'
                                        'status_code:{status_code}'.format(url=self.host + route_url,
                                                                           error=response.reason,
                                                                           status_code=response.status_code))
            return response.json()
        except Exception as e:
            raise UserError(message='连接失败:' + str(e))

    @staticmethod
    def _validation_url(url):
        """
        校验url合法性
        :param url:
        :return:
        """
        if re.match(r'^https?:/{2}\w.+$', url):
            return True
        else:
            return False
