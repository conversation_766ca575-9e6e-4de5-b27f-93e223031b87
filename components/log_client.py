#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

from dmplib import config
from requests import Response


def is_completed():
    pass


def get_body():
    return []


class LocalLogClient(object):
    def get_log(self, project, logstore, from_time, to_time, topic=None,  # NOSONAR
                query=None, reverse=False, offset=0, size=100):       # NOSONAR
        response = Response()
        response.body = ''
        setattr(response, 'is_completed', is_completed)
        setattr(response, 'get_body', get_body)
        return response

    def put_logs(self, request):
        return True


deployment = config.get('App.deployment', 'cloud')

LogClient = LocalLogClient



