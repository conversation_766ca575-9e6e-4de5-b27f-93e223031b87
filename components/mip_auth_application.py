import traceback

from loguru import logger

from components import auth_util
from dmplib import config
from dmplib.utils.errors import UserError
from dmplib.hug import g
from app_menu.repositories import application_repository
from app_menu.repositories import function_repository
from components.ingrate_platform import IngratePlatformApi
from dmplib.hug import debugger

_debugger = debugger.Debug(__name__)


def mip_error(func):
    def wrapper(self, *args, **kwargs):
        try:
            if not self.mip_config_is_exist():
                _debugger.log("该租户没有配置基础数据平台配置，不会进行交互")
                logger.error("该租户没有配置基础数据平台配置，不会进行交互")
                return
            return func(self, *args, **kwargs)
        except UserError as ue:
            logger.error(f'数据到基础平台建交互失败<{func.__qualname__}>：{ue.message}')
        except Exception:
            logger.error(f'数据到基础平台建交互未知错误<{func.__qualname__}>：{traceback.format_exc()}')

    return wrapper


class MIPOperationBase:
    id_templ = '11111111-0000-0000-0000-000000000000'

    def __init__(self, *args, **kwargs):
        self.api = IngratePlatformApi(g.code)

    def mip_config_is_exist(self):
        """获取基础平台门户信息时配置是否存在"""
        enabled = auth_util.is_env_enable_skyline_auth()
        if enabled:
            return True
        return self.api.portal_check_ingrate_platform_is_exist()

    @staticmethod
    def trans_dmp_portal_id_tp_mip_portal_id(dmp_portal_id: str):
        """
        将dmp的门户id转成erp形式的code
        """
        if dmp_portal_id.startswith(MIPOperationBase.id_templ[:-4]):
            return dmp_portal_id[-4:]
        else:
            return dmp_portal_id

    @staticmethod
    def trans_mip_portal_id_tp_dmp_portal_id(mip_portal_id: str):
        """
        将erp形式的code转成dmp的门户id
        """
        if len(mip_portal_id) == 4:
            return f'{MIPOperationBase.id_templ[:-4]}{mip_portal_id}'
        else:
            return mip_portal_id


# 门户操作
class ApplicationOperation(MIPOperationBase):

    def __init__(self, application_id):
        self.application_id = application_id
        super().__init__(application_id)

    def get_application(self):
        data = application_repository.get_application_by_id_list([self.application_id])
        if not data:
            raise UserError(message=f'门户不存在： {self.application_id}')
        application = data[0]
        return application

    def collect_application_data(self):
        from dashboard_chart.services.mip_auth_adapter import MIPApplicationAdapter
        # 1. 门户数据
        application = self.get_application()

        # 2. 菜单数据
        # 叶子节点菜单（报告）
        node_funcs = MIPApplicationAdapter().get_all_node_func_id(application_id=self.application_id)
        node_funcs_map = {node.get('id'): node for node in node_funcs}
        # 所有的菜单
        all_node_funcs = function_repository.get_function_list(application_id=self.application_id)
        all_node_funcs_map = {node.get('id'): node for node in all_node_funcs}
        return application, all_node_funcs_map, node_funcs_map

    @mip_error
    def upload_data_to_mip(self):
        # 1. 收集数据
        application, all_node_funcs_map, node_funcs_map = self.collect_application_data()

        # 2. 构建mip的接口格式
        upload_funcs = []
        application_id = application.get('id', '')
        application_name = application.get('name', '')
        application_id = self.trans_dmp_portal_id_tp_mip_portal_id(application_id)

        for node_func in all_node_funcs_map.values():
            report_id = node_func.get('id', '')
            report_name = node_func.get('name', '')
            if report_id in node_funcs_map:
                report_kind = 2
            else:
                report_kind = 1
            report_parent_id = node_func.get('parent_id', '')
            node = {
                "Id": report_id,  # 分组/报表ID（必须GUID）
                "Name": report_name,  # 报表名称
                "Kind": report_kind,  # 报表类型:  1目录 2报表
                "ParentId": report_parent_id,  # 报表父目录ID
                "ApplicationCode": application_id,
                "ApplicationName": application_name,
                "LevelCode": node_func.get('level_code', ''),
                "ReportType": node_func.get('report_type', 0),
            }
            upload_funcs.append(node)

        applications = [{
            "Code": application_id,
            "Name": application_name,
            "Rank": application.get('rank', ''),
            "Type": application.get('platform', ''),
        }]
        # 3. 调用接口
        self.api.portal_release_sync_to_mip(applications=applications, reports=upload_funcs)
        logger.error(f'同步门户<{self.application_id}>数据到基础数据平台成功！')

    @mip_error
    def upload_status_to_mip(self, operation_type, application={}):
        """门户状态同步基础数据平台"""
        # "OperationType": 1, // 1 = 上线、2 = 下线、3 = 删除
        status_map = {
            1: '上线',
            2: '下线',
            3: '删除',
        }
        if not application:
            application = self.get_application()
        application_id = application.get('id', '')
        application_name = application.get('name', '')
        rank = application.get('rank', -1)
        application_id = self.trans_dmp_portal_id_tp_mip_portal_id(application_id)
        self.api.portal_status_sync_to_mip(operation_type, application_id, application_name, rank)
        logger.error(f'同步门户状态<{self.application_id}>到基础数据平台成功！状态: {status_map.get(operation_type)}')

    @mip_error
    def upload_application_rank_to_mip(self):
        """门户状态同步基础数据平台"""
        # "OperationType": 1, // 1 = 上线、2 = 下线、3 = 删除、4 更新
        from user_group.services.user_group_service import buildin_application_id
        applications = application_repository.get_application_by_id_list([]) or []

        result = []
        for application in applications:
            application_id = application.get('id', '')
            if application_id == buildin_application_id:
                continue
            application_id = self.trans_dmp_portal_id_tp_mip_portal_id(application_id)
            data = {
                "Code": application_id,
                "Name": application.get('name', ''),
                "OperationType": 4,
                "Rank": application.get('rank', ''),
            }
            result.append(data)
        self.api.portal_status_rank_sync_to_mip(result)
        logger.error(f'同步门户排序到基础数据平台成功！')


# 可用门户
class ApplicationsAvailable(MIPOperationBase):

    @mip_error
    def get_mip_available_portals(self):
        """
        获取基础数据平台可用门户
        [{
             "Code":"0011",
             "Name":"销售系统"
        }]
        """
        datas = self.api.mip_available_portals(g.account)
        return [
            {
                'id': ApplicationOperation.trans_mip_portal_id_tp_dmp_portal_id(data.get('Code') or ''),
                'name': data.get('Name') or ''
            } for data in datas if data.get('Code')
        ]


# 门户报告操作
class ApplicationDashboardOperation(MIPOperationBase):

    @mip_error
    def get_reports_permission_data(self, report_ids):
        permission_data = self.api.portal_check_reports_is_grant(g.account, report_ids=report_ids) or {}
        return permission_data
