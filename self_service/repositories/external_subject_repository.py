#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2020/9/21.
"""
from typing import List

from base import repository
from base.dmp_constant import EXTERNAL_SNOW_SUBJECT_DATASET_ID, EXTERNAL_MULTI_DIM_SUBJECT_DATASET_ID
from base.enums import DatasetFieldType, DatasetType
import logging

from dataset.models import DatasetFieldModel, DatasetExternalSubjectModel, DatasetExternalSpaceModel, DatasetModel
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError

TABLE = 'dataset_external_subject'
FIELDS = ['id', 'name', 'description', 'tables', 'relations', 'table_fields', 'table_field_relations']


# 获取所有主题
def get_space_list():
    return repository.get_list('dataset_external_space', {}, ['id', 'name'], order_by='created_on DESC')


def get_subject_brief_list(subject_type=None, extra=None):
    if extra is None:
        extra = []
    if subject_type:
        return repository.get_list('dataset_external_subject', {"type": subject_type}, ['id', 'external_space_id', 'name', *extra])
    return repository.get_list('dataset_external_subject', {}, ['id', 'external_space_id', 'name', *extra])


# 根据主题ID加载左侧的树型菜单
def get_subject_detail(external_subject_id):
    if not external_subject_id:
        raise ValueError('外部主题不能为空')
    return repository.get_one(TABLE, {'id': external_subject_id}, FIELDS)


def get_subjects_detail(external_subject_ids: List[str]):
    return repository.get_list(TABLE, {'id': external_subject_ids}, FIELDS)


# 修改主题
def update_external_subject(data, conditions):
    return repository.update(TABLE, data, conditions)


def load_external_subject(external_subject_id: str) -> DatasetExternalSubjectModel:
    subject = get_subject_detail(external_subject_id)
    if subject is None:
        raise UserError(message='主题不存在')

    return DatasetExternalSubjectModel.convert_from_db(subject)


def batch_load_external_subject(ids: List[str]) -> List[DatasetExternalSubjectModel]:
    subjects = get_subjects_detail(ids)
    if len(subjects) != len(ids):
        raise UserError(message=f'主题不存在, <{ids}>, <{[item.get("id") for item in subjects]}>')

    return [DatasetExternalSubjectModel.convert_from_db(subject) for subject in subjects]


# 从数芯同步数据到自助分析
def sync_model(dataset, list_external_subject, list_field):
    """
    删除高级字段
    :return:
    """
    dataset_id = dataset.id
    result = dataset_id
    try:
        # 先删后插
        repository.delete_data('dataset', condition={'id': dataset_id})
        repository.exec_sql('truncate table dataset_external_subject;')
        repository.delete_data(
            'dataset_field', condition={'dataset_id': dataset_id, 'type': DatasetFieldType.Normal.value}
        )
        if dataset:
            repository.add_model('dataset', dataset, fields=['id', 'name', 'type', 'content', 'level_code'])
            repository.add_list_model(
                'dataset_external_subject',
                list_external_subject,
                fields=[
                    'id',
                    'name',
                    'description',
                    'tables',
                    'relations',
                    'table_fields',
                    'created_on',
                    'modified_on',
                    'created_by',
                    'modified_by',
                ],
            )
            repository.add_list_model(
                'dataset_field',
                list_field,
                fields=[
                    'id',
                    'dataset_id',
                    'alias_name',
                    'note',
                    'origin_field_type',
                    'rank',
                    'origin_table_id',
                    'origin_table_name',
                    'col_name',
                    'origin_col_name',
                    'data_type',
                    'field_group',
                    'type',
                    'created_on',
                    'modified_on',
                    'created_by',
                    'modified_by',
                ],
            )
    except Exception as e:
        msg = "同步模型数据失败：" + str(e)
        logging.error(msg, exc_info=True)
        result = ""

    return result


# 通过数据集ID获取数据集字段
def get_dataset_field_list(dataset_id):
    return repository.get_list('dataset_field', {'dataset_id': dataset_id})


def get_subject_table_fields():
    return repository.get_list(TABLE, {}, ['id', 'name', 'table_fields'])


def is_table_related_by_other_subject(tb_code, except_subject_id):
    sql = '''
select 1 from dataset_external_subject
where find_in_set(%(tb_code)s, table_code_set) and
      id != %(except_subject_id)s limit 1'''
    db = get_db()
    rows = db.query(sql, {'tb_code': tb_code, 'except_subject_id': except_subject_id})
    return len(rows) > 0


def update_dataset(dataset: DatasetModel, commit=True):
    repository.replace_list_data(
        'dataset', [dataset.get_dict()], ['id', 'name', 'description', 'type', 'level_code', 'content'], commit=commit
    )


def update_space(new_space: DatasetExternalSpaceModel, commit: bool = True):
    repository.replace_list_data(
        'dataset_external_space', [new_space.get_dict()], ["id", "name", "description", "created_on"], commit=commit
    )


def update_subject(new_subject: DatasetExternalSubjectModel, commit: bool = True):
    repository.replace_list_data(
        'dataset_external_subject',
        [new_subject.get_dict()],
        [
            "id",
            "name",
            "external_space_id",
            "description",
            "tables",
            "relations",
            "table_fields",
            "table_code_set",
            "table_field_relations",
            'created_on',
            'type'
        ],
        commit=commit,
    )


def delete_table_fields(table_cods, dataset_id, commit=True):
    if table_cods:
        repository.delete_data(
            'dataset_field',
            {'dataset_id': dataset_id, 'origin_table_id': table_cods},
            commit=commit,
        )


def delete_subject(subject_id, commit=True):
    repository.delete_data('dataset_external_subject', {'id': subject_id}, commit=commit)


def delete_space(space_id, commit=True):
    repository.delete_data('dataset_external_space', {'id': space_id}, commit=commit)


def update_table_dataset_fields(tables: List[str], dataset_fields: List[DatasetFieldModel], is_multi_dim: bool = False, commit=True):
    dataset_id = EXTERNAL_SNOW_SUBJECT_DATASET_ID if not is_multi_dim else EXTERNAL_MULTI_DIM_SUBJECT_DATASET_ID
    repository.delete_data(
        'dataset_field', {'origin_table_id': tables, 'dataset_id': dataset_id}, commit=False
    )
    return repository.replace_list_data(
        'dataset_field',
        [field.get_dict() for field in dataset_fields],
        [
            'id',
            'dataset_id',
            'alias_name',
            'note',
            'origin_table_alias_name',
            'origin_table_comment',
            'origin_field_type',
            'origin_table_id',
            'origin_table_name',
            'col_name',
            'origin_col_name',
            'data_type',
            'visible',
            'field_group',
            'rank',
            'type',
            'group_type',
            'expression_advance',
            'format',
        ],
        commit=commit,
    )


def clear_all(commit=True):
    dataset_ids = repository.get_data('dataset', {'type': 'EXTERNAL_SUBJECT'}, ['id'], multi_row=True)
    repository.delete_data('dataset', {'type': 'EXTERNAL_SUBJECT'}, commit=False)
    repository.delete_data('dataset_external_space', {'id like': "%"}, commit=False)
    if dataset_ids:
        repository.delete_data('dataset_field', {'dataset_id': [item.get('id') for item in dataset_ids]}, commit=False)
    repository.delete_data('dataset_external_subject', {'id like': "%"}, commit=commit)


def is_external_subject_dataset_exist():
    sql = 'select 1 from dataset where id=%(id)s'
    db = get_db()
    rows = db.query(sql, {'id': EXTERNAL_SNOW_SUBJECT_DATASET_ID})
    return len(rows) > 0


def is_external_subject(dataset_id: str):
    return repository.data_is_exists("dataset", {"id": dataset_id, "type": DatasetType.ExternalSubject.value})


def delete_dataset(dataset_id, commit=True):
    return repository.delete_data(
            'dataset',
            {'id': dataset_id},
            commit=commit,
        )


def is_multi_dim_subject_dataset_exist():
    sql = 'select 1 from dataset where id=%(id)s'
    db = get_db()
    rows = db.query(sql, {'id': EXTERNAL_MULTI_DIM_SUBJECT_DATASET_ID})
    return len(rows) > 0


def load_config(external_subject_id):
    return repository.get_data_scalar('external_subject_config', {'external_subject_id': external_subject_id}, 'filter_config', from_config_db=True)


def save_config(external_subject_id, filter_config):
    repository.replace_list_data('external_subject_config', [{'external_subject_id': external_subject_id, 'filter_config': filter_config}],
                                 fields=['external_subject_id', 'filter_config'],
                                 from_config_db=True)
