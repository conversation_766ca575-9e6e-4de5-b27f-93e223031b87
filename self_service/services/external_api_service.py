#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import requests
import logging
import jwt
import time
import base64

from requests.adapters import HTTPAdapter
from urllib3 import Retry

from dmplib import config
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)


class ExternalApi:
    """
    session = ExternalApi()
    session.get(url, params)

    """

    def __init__(self, headers=None, timeout=10, retry=3):
        self.headers = headers or {'content-type': 'application/json'}
        self.timeout = timeout
        self.retry = retry

    def get(self, url, params: dict):
        logger.debug(f"send self service api request, url: {url}, params: {params}")
        try:
            max_retry = self.retry
            retry = Retry(
                total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1, status_forcelist=(500, 503)
            )
            adapter = HTTPAdapter(max_retries=retry)
            session = requests.session()
            session.mount('https://', adapter)
            session.mount('http://', adapter)

            rsp = session.get(url=url, params=params, timeout=self.timeout, headers=self.headers)
            logger.debug(f"recv self service api response, data: {rsp.text}")
            data = rsp.json()
            return data
        except UserError as e:
            raise e from e
        except Exception as e:
            logger.exception("调用外部接口异常，异常信息: %s", str(e))
            raise UserError(message=f'调用外部接口失败, 错误原因: {str(e)}')

    def post(self, url, params: dict):
        logger.debug(f"send self service api request, url: {url}, params: {params}")
        try:
            max_retry = self.retry
            retry = Retry(
                total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1, status_forcelist=(500, 503)
            )
            adapter = HTTPAdapter(max_retries=retry)
            session = requests.session()
            session.mount('https://', adapter)
            session.mount('http://', adapter)

            rsp = session.post(url=url, json=params, timeout=self.timeout, headers=self.headers)
            logger.debug(f"recv self service api response, data: {rsp.text}")
            data = rsp.json()
            return data
        except UserError as e:
            raise e from e
        except Exception as e:
            logger.exception("调用外部接口异常，异常信息: %s", str(e))
            raise UserError(message=f'调用外部接口失败, 错误原因: {str(e)}')

    @staticmethod
    def get_api_location(section, field):
        return config.get(section + '.' + field)

    @staticmethod
    def get_api_token(payload, jwt_secret=None, jwt_alg=None):

        # 生成的jwt标准token
        jwt_token = jwt.encode(payload, jwt_secret, jwt_alg)

        # 对jwt标准token进行base64生成最终: token
        return base64.b64encode(jwt_token.encode()).decode('utf-8')


def get_permission_fields(
    code: str,
    account: str,
    external_user_id: str = None,
    external_subject_id: str = None,
    external_subject_name: str = None,
):
    """
    通过第三方API获取权限字段
    :param external_user_id:
    :param external_subject_name: 外部主题名
    :param external_subject_id: 外部主题id
    :param account: 用户账号
    :param code: 租户代码
    :return:
    """
    # 用户登录payload数据
    exp = int(time.time()) + 60 * 60 * 24
    jwt_secret = config.get('SelfService.yl_api_secret')
    payload = {
        "tenant_code": code,
        "user_account": account,
        "external_user_id": external_user_id,
        "external_subject_id": external_subject_id,
        "external_subject_name": external_subject_name,
        "exp": exp,
    }
    session = ExternalApi()
    token = session.get_api_token(payload=payload, jwt_secret=jwt_secret, jwt_alg='HS256')
    params = {'token': token}
    url = session.get_api_location('SelfService', 'get_permission_fields_api')
    data = session.post(url, params)
    if int(data.get('code')) == 0:
        return data.get('data')
    raise UserError(code=data.get('code'), message=data.get('message'))


def get_permission_filter_values(
    code: str,
    account: str,
    external_user_id: str = None,
    external_subject_id: str = None,
    external_subject_name: str = None,
    is_external_subject: bool = False,
    identifier: str = None
):
    """
    通过第三方API获取权限过滤字段值
    :param identifier:
    :param is_external_subject:
    :param external_user_id:
    :param external_subject_name: 外部主题名
    :param external_subject_id: 外部主题id
    :param account: 用户账号
    :param code: 租户代码
    :return:
    """
    # 用户登录payload数据
    exp = int(time.time()) + 60
    jwt_secret = config.get('SelfService.yl_api_secret')
    if is_external_subject:
        payload = {
            "tenant_code": code,
            "user_account": account,
            "external_user_id": external_user_id,
            "external_subject_id": external_subject_id,
            "external_subject_name": external_subject_name,
            "identifier": identifier,
            "exp": exp,
        }
    else:
        payload = {
            "tenant_code": code,
            "user_account": account,
            "external_user_id": external_user_id,
            "identifier": identifier,
            "exp": exp,
        }
    logger.info("api permission params: {}".format(payload))
    session = ExternalApi()
    token = session.get_api_token(payload=payload, jwt_secret=jwt_secret, jwt_alg='HS256')
    params = {'token': token}
    url = session.get_api_location('SelfService', 'get_permission_filter_api')
    data = session.post(url, params)
    if int(data.get('code')) == 0:
        return data.get('data')
    raise UserError(code=data.get('code'), message=data.get('message'))
