import base64
import time
import jwt
from dmplib import config

from requests.adapters import HTTPAdapter
from urllib3 import Retry
import requests
from base import repository
from dmplib.hug import debugger, g
from dmplib.utils.errors import UserError
import logging
import json

logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)

pulsar_host = config.get('SelfService.host') or "/"
pulsar_host = pulsar_host[:-1] if pulsar_host[-1] == '/' else pulsar_host
pulsar_interface = {
    'get_all_subjects_api': f'{pulsar_host}/metadata_api/subjects',  # 获取主题列表
    'get_tables_and_relations_api': f'{pulsar_host}/metadata_api/models',  # 获取模型和关系
    'get_fields_api': f'{pulsar_host}/metadata_api/fields',  # 获取项目模型字段
    'get_data_api': f'{pulsar_host}/data_api/get_data_result',  # 获取数据
}


def get_app_key_and_secret():
    data = repository.get_one('project', {'code': g.code}, ['pulsar_app_key', 'pulsar_app_secret'], from_config_db=True)
    key = config.get('SelfService.app_key') if not data.get('pulsar_app_key') else data.get('pulsar_app_key')
    secret = config.get('SelfService.app_secret') if not data.get('pulsar_app_secret') else data.get('pulsar_app_secret')
    return key, secret


def get_extend_yl_params():
    # 云链的单独需求： 需要传递JWTtoken中extend_yl_params到数芯
    # 先从cookie中取，取不到再从token中取
    cookie = getattr(g, 'cookie', {})
    logger.debug(f"云链extend_yl_params: {cookie}")
    extend_yl_params1 = cookie.get('extend_yl_params', '')
    if extend_yl_params1:
        return extend_yl_params1

    token = cookie.get('token', '')
    try:
        raw_token = jwt.decode(token, '', algorithms="HS256", options={'verify_signature': False}) # 直接查看token中payload信息
        extend_yl_params2 = raw_token.get('extend_yl_params', '')
        if extend_yl_params2:
            return extend_yl_params2
        else:
            return ''
    except:
        import traceback
        logger.error(f"云链extend_yl_params从token中取失败: {traceback.format_exc()}")
        return ''


# 获取pulsar token
def get_pulsar_api_token():
    app_key, app_secret = get_app_key_and_secret()
    jwt_alg = "HS256"
    exp = int(time.time()) + 60 * 60 * 23
    payload = {"iss": app_key, "exp": exp}
    jwt_token = jwt.encode(payload, app_secret, jwt_alg)
    return jwt_token


def get_pulsar_project_code():
    """获取当前租户配置的数芯业务板块code"""
    pulsar_project_code = repository.get_data_scalar('project', {'code': g.code}, 'pulsar_project_code', from_config_db=True)
    if not pulsar_project_code:
        """获取数芯的业务板块code"""
        pulsar_project_code = config.get('SelfService.pulsar_project_code') or ''
        if not pulsar_project_code:
            raise UserError(message='未配置SelfService.pulsar_project_code')
    return pulsar_project_code


def get_pulsar_tenant_code():
    """获取数芯Saas模式下的租户代码"""
    convert_method = config.get('SelfService.pulsar_tenant_code_convert_method') or 'no_convert'
    if convert_method == 'no_convert':
        return g.code
    if convert_method == 'yl':
        return convert_dmp_tenant_code_to_pulsar_tenant_code(g.code)
    raise UserError(message='SelfService.pulsar_tenant_code_convert_method配置错误')

def convert_dmp_tenant_code_to_pulsar_tenant_code(dmp_tenant_code):
    """解决云链数芯租户code和dmp租户code不一致的问题，没有映射规则，通过请求云链业务系统进行code转换"""
    url = config.get('SelfService.yl_tenant_code_convert_url') or ''
    if not url:
        raise UserError(message='SelfService.yl_tenant_code_convert_url配置错误')
    secret = config.get('SelfService.yl_tenant_code_convert_secret') or ''
    if not secret:
        raise UserError(message='SelfService.yl_tenant_code_convert_secret')

    extend_yl_params = get_extend_yl_params()
    payload = {
        "permission_params": [{"key": "project_code", "value": dmp_tenant_code}],
        "permission_fields": ["db_name"],
        'extend_yl_params': extend_yl_params
    }
    token = jwt.encode(payload, secret, "HS256")

    try:
        rsp = requests.post(url, json={'token': token})
        logger.debug(f"请求数芯租户响应: {rsp.text}")
        if rsp.status_code != 200:
            raise UserError(message=f'请求数芯租户code失败, 状态码<{rsp.status_code}>')
        data = rsp.json()
        data = data.get('data') or {}
        permissions = data.get('permissions') or []
        for permission in permissions:
            if permission.get('field') in ['db_name', 'xt_tenant_code']:
                return permission.get('value')
        raise UserError(message='请求数芯租户code失败, 解析返回内容错误')
    except UserError as e:
        raise e from e
    except Exception as e:
        raise UserError(message='请求数芯租户code失败') from e


# http 请求封装
def get(url, params: dict):
    logger.debug(f"send pulsar api request, url: {url}, params: {params}")  # pylint: disable=W1203
    token = get_pulsar_api_token()
    args = {'jwt': token}
    args.update(params)
    try:
        max_retry = 2
        timeout = 10
        retry = Retry(total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1, status_forcelist=(500, 503))
        adapter = HTTPAdapter(max_retries=retry)
        session = requests.session()
        session.mount('https://', adapter)
        session.mount('http://', adapter)

        rsp = session.get(url=url, params=args, timeout=timeout)
        logger.debug(f"recv pulsar api response, data: {rsp.text}")  # pylint: disable=W1203
        data = parse_response(rsp)
        return data
    except UserError as e:
        raise e from e
    except Exception as e:
        logger.exception("调用Pulsar接口异常，异常信息: %s", str(e))
        raise UserError(message=f'调用Pulsar接口失败, 错误原因: {str(e)}') from e


def post(url, params: dict, body: dict, timeout: int = 10):
    logger.debug(f"send pulsar api request, url: {url}, params: {params}, body: {body}")  # pylint: disable=W1203
    token = get_pulsar_api_token()
    args = {'jwt': token}
    args.update(params)
    try:
        max_retry = 2
        retry = Retry(total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1, status_forcelist=(500, 503))
        adapter = HTTPAdapter(max_retries=retry)
        session = requests.session()
        session.mount('https://', adapter)
        session.mount('http://', adapter)

        rsp = session.post(url=url, params=args, json=body, timeout=timeout)
        logger.debug(f"recv pulsar api response, data: {rsp.text}")  # pylint: disable=W1203
        return rsp.json()
    except UserError as e:
        raise e from e
    except Exception as e:
        logger.exception("调用Pulsar接口异常，异常信息: %s", str(e))
        raise UserError(message=f'调用Pulsar接口失败, 错误原因: {str(e)}') from e


# 返回结果转换
def parse_response(r):
    """
    解析返回数据
    :param Response r: 返回的response对象
    :return:
    """
    try:
        data = json.loads(r.text)
        code = data.get('errCode')
        if code != 0:
            raise UserError(message=f'调用Pulsar接口失败, 错误代码: {code}, 错误信息: {data.get("errMsg")}')
        return data['data']
    except ValueError as e:
        raise UserError(message='解析http响应体失败，响应体内容:{content}'.format(content=r.text)) from e


def get_space_tables_and_relations_api(external_space_id):
    params = {"project": get_pulsar_project_code(), "subject_id": external_space_id}
    return get(pulsar_interface['get_tables_and_relations_api'], params)


def get_subject_tables_and_relations_api(external_space_id, external_subject_id, model_category):
    params = {"project": get_pulsar_project_code(), "subject_id": external_space_id, "model_code": external_subject_id, "model_category": model_category}
    return get(pulsar_interface['get_tables_and_relations_api'], params)


def get_space(external_space_id):
    params = {"project": get_pulsar_project_code(), "subject_id": external_space_id}
    return get(pulsar_interface['get_all_subjects_api'], params)


def get_spaces():
    params = {"project": get_pulsar_project_code()}
    return get(pulsar_interface['get_all_subjects_api'], params)


def get_table_fields(table_id, model_category):
    params = {"project": get_pulsar_project_code(), "model_code": table_id, "model_category": model_category}
    fields = get(pulsar_interface['get_fields_api'], params)
    return fields


def get_data(sql, timeout):
    body = {
        "t": int(time.time()),
        "query_sql": sql,
        "project": get_pulsar_project_code(),
        "tenant_code": get_pulsar_tenant_code()
    }
    return post(pulsar_interface['get_data_api'], {}, body=body, timeout=timeout)
