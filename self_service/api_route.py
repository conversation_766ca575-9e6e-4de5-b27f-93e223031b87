#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/10/16 16:19
# <AUTHOR> caoxl
# @File     : api_route.py
from components.common_admin_route import CommonProxyAPIWrapper
from dashboard_chart.services import dashboard_service
import logging
from dmplib.hug import APIWrapper
from self_service.services import external_subject_service, external_sync_service, config_service
from user.services import reporting_sso_service
from dashboard_chart.services.dashboard_service import request_args_setting

api = APIWrapper(__name__)
logger = logging.getLogger(__name__)
common_admin_api = CommonProxyAPIWrapper(__name__)


# 获取自助报告主题筛选配置
@api.admin_route.get('/get_config')
def get_config(**kwargs):
    return True, '', config_service.get_global_config(kwargs.get('external_subject_id'))


# 设置自助报告主题筛选配置
@api.admin_route.post('/set_config')
def set_config(**kwargs):
    return True, '', config_service.set_global_config(kwargs.get('external_subject_id'), kwargs.get('config'))


# 添加自助报告明细报告
@api.admin_route.post('/child_dashboard/add')
def add_child_dashboard(**kwargs):
    parent_dashboard_id = kwargs.get('id')
    parent_dashboard_chart_id = kwargs.get('chart_id')
    dataset_field_id = kwargs.get('dataset_field_id')
    name = kwargs.get('name')
    dashboard = kwargs.get('dashboard')
    return True, '', dashboard_service.add_self_service_child_dashboard(
        parent_dashboard_id, parent_dashboard_chart_id, dataset_field_id, name, dashboard
    )


@api.admin_route.post('/child_dashboard/delete')
def del_child_dashboard(**kwargs):
    child_dashboard_id = kwargs.get('id')
    dashboard_service.del_self_service_child_dashboard(child_dashboard_id)
    return True, '', ''


# 更改自助报告子报告名字
@api.admin_route.post('/child_dashboard/rename')
def rename_child_dashboard(**kwargs):
    dashboard_service.rename_self_service_child_dashboard_name(kwargs.get('id'), kwargs.get('name'))
    return True, '', ''


# 获取自助报告列表
@common_admin_api.admin_with_third_party_route.get('/get_dashboard_list')
@request_args_setting
def get_self_report_list(request, response, *args, **kwargs):
    return True, '', dashboard_service.get_self_report_list(**kwargs)


# 自助报表拖拽排序
@api.admin_route.post('/dashboard_list/sort')
def sort_report_list(**kwargs):
    return True, '', dashboard_service.sort_self_report_list(**kwargs)


# 根据创建者返回创建者的用户
@api.admin_route.get('/create_grouped_users')
def get_self_report_list(**kwargs):
    return True, '', dashboard_service.get_self_report_create_grouped_users(**kwargs)


# 获取所有主题
@common_admin_api.admin_with_third_party_route.get('/get_subject_list')
def get_subject_list(**kwargs):
    subject_type = kwargs.get("type")
    return True, '', external_subject_service.get_spaces(subject_type)


# 根据主题ID加载左侧的树型菜单
@common_admin_api.admin_with_third_party_route.post('/get_subject_detail')
def get_subject_detail(**kwargs):
    dataset_id = kwargs.get('dataset_id')
    main_external_subject_id = kwargs.get('main_external_subject_id')
    detail = kwargs.get('get_child_report_fields') or False
    return True, '', external_subject_service.get_subject_fields(dataset_id, main_external_subject_id, detail)


# 全量同步模型
@api.admin_route.get('/sync_model')
def sync_model():
    external_sync_service.sync_all()
    return True, '同步模型数据成功', ''


@api.route.get('/dashboard')
@reporting_sso_service.erp_sso_authenticator(need_redirect=True)
def dashboard(request, response, *args, **kwargs):
    """
    erp集成打开自助报告
    """
    return True, '', dashboard_service.open_self_service(kwargs.get('id'))


# 根据主题ID加载左侧的树型菜单
@api.admin_route.get('/get_table_relation')
def get_subject_detail(**kwargs):
    main_external_subject_id = kwargs.get('main_external_subject_id')
    return True, '', external_subject_service.get_table_relation(main_external_subject_id)
