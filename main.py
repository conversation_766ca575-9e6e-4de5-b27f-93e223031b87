# pylint: skip-file
import os

os.environ.setdefault("prometheus_multiproc_dir", ".")
import app

if __name__ == '__main__':

    try:
        from wsgiref.simple_server import make_server

        application = app.__hug_wsgi__
        with make_server('', 8000, application) as httpd:
            print("Serving on port 8000...")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("Goodbye!")
