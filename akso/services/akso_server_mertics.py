#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
查询所有的服务状态
"""
import math
import os
import traceback
from datetime import datetime
from loguru import logger
from dmplib.utils.errors import UserError

from dmplib import config

from akso.models import MerticsResult, ServerMerticsParams
from components.data_center_api import get_new_erp_datasource_model, get_data_by_sql
from components.message_queue import RabbitMQ
from components.oss import OSSFileProxy
from monitor.services import monitor_service
from system.services.env_service import EnvService


def server_mertics(params: ServerMerticsParams):
    """
    根据不同的服务类型，查询
    """
    server_code = params.serverCode
    fn = eval("_server_mertics_" + server_code)
    try:
        return fn(params)
    except Exception as e:
        traceback.print_exc()
        return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="ERROR",
                             errmsg=str(e)).get_simple_result()


def _server_mertics_001(params: ServerMerticsParams):
    """
    附件服务
    校验规则：
    尝试检测bucket是否存在通过，抛出异常的时候，则说明请求有问题
    """
    try:
        oss = OSSFileProxy()
        oss.object_list(root='/')
        return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS").get_simple_result()
    except Exception as e:
        traceback.print_exc()
        errmsg = str(e)
        logger.error(errmsg)
        return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="ERROR",
                             errmsg=errmsg).get_simple_result()


def _server_mertics_002(params: ServerMerticsParams):
    """
    数据集调度服务
    验证Rundeck是否正常调度
    """
    try:
        EnvService().check_rundeck()
    except UserError as e:
        traceback.print_exc()
        logger.error(e.message)
        return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="ERROR",
                             errmsg=e.message).get_simple_result()
    except Exception as e:
        traceback.print_exc()
        logger.error(str(e))
        return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="ERROR",
                             errmsg=str(e)).get_simple_result()

    return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS").get_simple_result()


def _server_mertics_003(params: ServerMerticsParams):
    """
    数据集清洗服务
    监听dmp_flow服务是否正常
    """
    mq = RabbitMQ()
    work_queue_name = config.get('RabbitMQ.queue_name_flow', 'flow')
    count = mq.get_consumer_count(work_queue_name)
    if count <= 0:
        return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="ERROR",
                             errmsg="数据清洗服务异常").get_simple_result()
    return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS").get_simple_result()


def _server_mertics_004(params: ServerMerticsParams):
    """
    消息服务
    """
    result = monitor_service.celery_queue_monitor("feeds")
    result_feeds = result.get("celery").get("feeds")

    message_count = result_feeds.get("message_count")
    mertics_result = MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS");
    if 'exception' == result_feeds.get("status"):
        mertics_result.status = "ERROR"
        mertics_result.errmsg = "celery服务异常：{0}".format(result_feeds.get("msg"))
    elif message_count > 20:
        mertics_result.status = "WARNING"
        mertics_result.errmsg = "未处理的消息数量为：{0},已经超出了预警数量(20)，请检查消息队列是否阻塞".format(str(message_count))
    return mertics_result.get_simple_result()


def _server_mertics_005(params: ServerMerticsParams):
    """
    CT监听服务
    """
    _sql = """
            select max(CheckTime) CheckTime from mdc_ServiceRunningStatus where ServiceName = '数据监听服务'
        """
    rows = _execute_sql(_sql)
    now = datetime.now()
    result = MerticsResult(startTime=now, endTime=now, status="SUCCESS")
    if not rows:
        result.status = "ERROR"
        result.errmsg = "未检测到数据监听服务心跳日志"
        return result
    check_time = rows[0].get("CheckTime")
    diff = math.floor(now.timestamp() - datetime.strptime(check_time, "%Y-%m-%d %H:%M:%S").timestamp())
    if diff > 15 * 60:
        result.status = "ERROR"
        result.errmsg = "数据监听服务异常，最近一次检测响应时间是{0},超出了15分钟的间隔时间".format(check_time)
    return result.get_simple_result()


def _server_mertics_006(params: ServerMerticsParams):
    """
    队列服务
    监听work_flow，flow消息通道内的数量
    """
    result = MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS", errmsg="")
    mq = RabbitMQ()
    work_flow_queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
    work_flow_count = mq.get_message_count(work_flow_queue_name)
    logger.debug("work_flow_count:" + str(work_flow_count))
    if work_flow_count > 30:
        result.errmsg = "work_flow_count消息通道中有：{0}条消息未消费，未消费的消息过多导致消息阻塞，请检查消费者是否存在;".format(str(work_flow_count))
        result.status = "WARNING"

    work_queue_name = config.get('RabbitMQ.queue_name_flow', 'flow')
    flow_count = mq.get_message_count(work_queue_name)
    logger.debug("flow_count:" + str(flow_count))
    if flow_count > 20:
        result.errmsg = result.errmsg + "flow消息通道中有：{0}条消息未消费，未消费的消息过多导致消息阻塞，请检查消费者是否存在".format(str(flow_count))
        result.status = "WARNING"

    return result.get_simple_result()


def _server_mertics_007(params: ServerMerticsParams):
    """
    数据清洗服务
    """
    _sql = """
        select max(CheckTime) CheckTime from mdc_ServiceRunningStatus 
        where ServiceName = '清洗调度服务' or ServiceName = '数据清洗服务'
    """
    rows = _execute_sql(_sql)
    now = datetime.now()
    result = MerticsResult(startTime=now, endTime=now, status="SUCCESS")
    if not rows:
        result.status = "ERROR"
        result.errmsg = "未检测到数据清洗服务心跳日志"
        return result.get_simple_result()
    check_time = rows[0].get("CheckTime")
    diff = math.floor(now.timestamp() - datetime.strptime(check_time, "%Y-%m-%d %H:%M:%S").timestamp())
    if diff > 15 * 60:
        result.status = "ERROR"
        result.errmsg = "数据清洗服务异常，最近一次检测响应时间是{},超出了15分钟的间隔时间".format(check_time)

    # _sql = """
    #                 SELECT top 3 a.name ,b.rows
    #             FROM sysobjects AS a
    #             INNER JOIN sysindexes AS b ON a.id = b.id
    #             WHERE (a.type = 'u')
    #             AND (b.indid IN ( 0, 1 ))
    #             AND b.rows > 1000000
    #             AND a.name LIKE 'mdc_%' and a.name not in ('mdc_TrackingDetail',
    #             'mdc_TrackingDetailtemp','mdc_Dataset_Package_Ref','mdc_TrackingDetailHistory',
    #             'Mdc_Dataset_ValidateDetailLog')
    #             """
    # rows = _execute_sql(_sql)
    # if rows:
    #     msg = ','.join([row.get("name") + '(' + str(row.get("rows")) + ')' for row in rows])
    #     if result.status != "ERROR":
    #         result.status = "WARNING"
    #     result.errmsg = result.errmsg + """     【异常信息】数据中台服务存在出厂的表数据超100万多行的情况：{}，请及时清理。
    #     请登记工单联系数据中台团队进行修复""".format(msg)

    return result.get_simple_result()


def _server_mertics_008(params: ServerMerticsParams):
    """
    CT队列服务
    1.检查队列中是否存在错误
    2.查询队列中是否存在待转换的
    """
    _sql = """
        SELECT count(1) num,MAX(ErrorMessage) ErrorMessage
        FROM mdc_TrackingNotifyData WITH (NOLOCK)
        WHERE ErrorTimes >= 3  AND CreateTime >= '{0}' AND CreateTime < '{1}'
    """.format(params.start, params.end)
    rows = _execute_sql(_sql)
    now = datetime.now()
    result = MerticsResult(startTime=now, endTime=now, status="SUCCESS")
    if rows and rows[0].get("num") > 0:
        num = rows[0].get("num")
        error_message = rows[0].get("ErrorMessage")
        result.status = "ERROR"
        result.errmsg = """数据中台服务转换队列在转换中出现异常，异常的转换数{0},错误原因{1}
            一般是数据库无法连接或者转换脚本超时,请对应修正环境或优化转换sql即可解决，
            特殊下情况不能解决请登记工单联系数据中台团队进行修复""".format(num, error_message)
        return result.get_simple_result()

    _sql = "SELECT COUNT(0) num FROM dbo.mdc_TrackingNotifyData WITH (NOLOCK) WHERE ErrorTimes=0"
    rows = _execute_sql(_sql)
    if rows and rows[0].get("num") > 2000:
        result.status = "WARNING"
        result.errmsg = "变更转换队列有{0}条待转换；".format(rows[0].get("num"))
    return result.get_simple_result()


def _server_mertics_009(params: ServerMerticsParams):
    """
    汇总宽表清洗服务
    """
    return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS").get_simple_result()


def _server_mertics_010(params: ServerMerticsParams):
    """
    主题包调度服务
    """
    return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS").get_simple_result()


def _server_mertics_011(params: ServerMerticsParams):
    """
    主题包清洗服务
    监听dmp_proc服务是否正常，主要是通过MQ的消费者数量来判断，如果没有消费者，则说明服务掉线
    """
    mq = RabbitMQ()
    work_flow_queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
    count = mq.get_consumer_count(work_flow_queue_name)
    if count <= 0:
        return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="ERROR",
                             errmsg="主题包清洗服务异常").get_simple_result()
    return MerticsResult(startTime=datetime.now(), endTime=datetime.now(), status="SUCCESS").get_simple_result()


def _execute_sql(sql: str):
    _model = get_new_erp_datasource_model()
    _result = get_data_by_sql(sql, _model)
    return _result.get("Data")
