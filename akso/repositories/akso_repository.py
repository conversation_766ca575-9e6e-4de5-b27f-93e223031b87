#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/6/29.
"""
from operator import itemgetter

from base import repository
from dmplib.saas.project import get_db


def get_instance_of_node(flow_ids, start_time, end_time):
    """
    获取指定时间段内的flow调度实例
    :param flow_ids:
    :param start_time:
    :param end_time:
    :return:
    """
    with get_db() as db:
        sql = """
        select 
            id, startup_time, end_time, status, flow_id, message 
        from instance 
        where flow_id in %(flow_ids)s and startup_time >= %(start_time)s and startup_time <= %(end_time)s 
        and startup_time is not null 
        order by FIELD(status,'已成功','运行中','已创建','已失败','已中止','已忽略'), startup_time desc 
        """
        return db.query(sql, {
            "flow_ids": flow_ids,
            "start_time": start_time,
            "end_time": end_time
        })


def get_instance_of_node_feed(flow_ids, start_time, end_time):
    """
    获取指定时间段内的flow调度实例
    :param flow_ids:
    :param start_time:
    :param end_time:
    :return:
    """
    with get_db() as db:
        sql = """
        select 
            i.id, startup_time, end_time, i.status, flow_id, message, send_result,d.status send_status 
        from instance i 
        left join dashboard_email_subscribe_detail d on d.id = i.id 
        where flow_id in %(flow_ids)s and startup_time >= %(start_time)s and startup_time <= %(end_time)s 
        and startup_time is not null  
        order by FIELD(i.status,'已成功','运行中','已创建','已失败','已中止','已忽略'), startup_time desc 
        """
        return db.query(sql, {
            "flow_ids": flow_ids,
            "start_time": start_time,
            "end_time": end_time
        })


def get_cost_time_of_instances(flow_id, percent, status="已成功"):
    """
    获取所有成功实例的执行时间
    """
    with get_db() as db:
        sql = """
            select * from (
            select @row_num:=@row_num+1 as row_num ,
            TIMESTAMPDIFF(SECOND, startup_time,end_time) cost_time 
            from `instance` i,(select @row_num:=0) b 
            where status  = %(status)s and flow_id = %(flow_id)s
            order by cost_time desc
            )base
            where base.row_num = CEIL(@row_num*%(percent)s)
        """
        return db.query_one(sql, {"status": status, "flow_id": flow_id, "percent": percent})


def get_diff_of_instances(flow_id, percent, max_valid_time_out, status="已成功"):
    """
    获取所有成功实例的执行时间
    """
    with get_db() as db:
        sql = """
            select * from (
            select @row_num:=@row_num+1 as row_num ,
            TIMESTAMPDIFF(SECOND, created_on,startup_time) diff 
            from `instance` i,(select @row_num:=0) b 
            where status  = %(status)s and flow_id = %(flow_id)s
                  and TIMESTAMPDIFF(SECOND, created_on,startup_time)<=%(max_valid_time_out)s
            order by diff desc
            )base
            where base.row_num = CEIL(@row_num*%(percent)s)
        """
        return db.query(sql, {"status": status, "flow_id": flow_id,
                              "percent": percent, "max_valid_time_out": max_valid_time_out})


def get_feed_subscribe(feed_id):
    """
    获取简讯执行时间 和 简讯信息
    """
    with get_db() as db:
        sql = """
        select s.dataset_ids, s.dashboard_id, s.subject_email as name, f.schedule from dashboard_email_subscribe as s inner join flow as f
        on f.id=s.id where s.id=%(feed_id)s
        """
        return db.query_one(sql, {"feed_id": feed_id})


def get_schedule_by_subject_id(subject_id: str):
    with get_db() as db:
        sql = """
        select schedule cron  from flow f where id in (select id from dataset dataset where content like %(subject_id)s and `type` ='FOLDER') 
        """
        return db.query(sql, {"subject_id": '%' + subject_id + '%'})


def get_dataset(dataset_id):
    """
    获取简讯执行时间 和 简讯信息
    """
    return repository.get_data("dataset", {"id": dataset_id},
                               ["id", "type", "connect_type", "name", "content", "parent_id"])


def get_subject_dataset(dataset_id):
    dataset = get_dataset(dataset_id)
    if dataset.get("parent_id") and dataset.get("type") != "FOLDER":
        return get_subject_dataset(dataset.get("parent_id"))
    else:
        return dataset


def get_schedule(dataset_id):
    """
    获取简讯执行时间 和 简讯信息
    """
    return repository.get_data_scalar("flow", {"id": dataset_id, "status": "启用"}, col_name="schedule")


def get_depend_flow(flow_id):
    """
    获取我依赖的流程
    """
    return repository.get_data_scalar("flow", {"id": flow_id}, col_name="depend_flow_id")


def get_depend_dataset(dataset_id):
    """
    获取我依赖的数据集
    """
    with get_db() as db:
        sql = """
        select dd.source_dataset_id from dataset_depend as dd inner join dataset as d on d.id=dd.source_dataset_id where 
        dd.depend_id=%(dataset_id)s and d.type != "FOLDER"
        """
        res = db.query(sql, params={"dataset_id": dataset_id})
        return [item.get('source_dataset_id') for item in res] if res else []


def find_subscribe_list():
    """
    查询简讯订阅列表
    """
    with get_db() as db:
        sql = """
        select e.id,e.subject_email as name 
        from dashboard_email_subscribe e
        left join flow f on f.id = e.id
        where e.send_frequency = 3 and f.status = '启用'
        """
        return db.query(sql)


def find_dashboard_list(platform):
    """
    获取大屏，小屏的数据列表
    """
    sql = """
            select id,name from dashboard 
            """
    params = {"platform": platform}
    wheres = ['`type` = "FILE"', 'platform=%(platform)s', 'status = 1']
    if platform == 'pc':
        wheres.append("(new_layout_type = 0 or new_layout_type=1)")
    if platform == 'mobile':
        wheres.append("(new_layout_type=1)")

    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' order by modified_by desc'
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_list(dataset_ids):
    """
    获取简讯执行时间 和 简讯信息
    """
    with get_db() as db:
        sql = """
        select d.id,d.type,d.connect_type,d.name,d.content,d.parent_id,d.use_cache,d.cache_flow_id,
        if(f.status = '启用',f.schedule,'') schedule 
        from dataset d left join flow f on d.id = f.id
        where d.id in ({0})
        """.format("'" + "','".join(dataset_ids) + "'")
        return db.query(sql)


def get_dashboard_info(dashboard_id):
    """
    获取仪表板信息
    """
    return repository.get_one("dashboard", {"id": dashboard_id}, ["id", "name"])


def get_dataset_user_tables(dataset_id):
    """
    获取dataset使用的数据集
    """
    return repository.get_columns("dataset_used_table", {"dataset_id": dataset_id}, "table_name")


def get_dataset_field(dataset_ids: str, field_type: str):
    """
    获取数据集字段
    :param dataset_id:
    :param field_type:
    :return:
    """

    with get_db() as db:
        sql = """select id, col_name,origin_col_name,origin_table_alias_name,origin_table_name,dataset_id 
         FROM dataset_field where dataset_id in %(dataset_ids)s and type = %(field_type)s"""
        return db.query(sql, {'dataset_ids': dataset_ids, 'field_type': field_type})
