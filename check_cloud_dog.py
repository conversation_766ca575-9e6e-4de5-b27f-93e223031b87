#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : check_cloud_dog.py
# @Author: guq  
# @Date  : 2021/7/21
# @Desc  :
import os

old_env = os.environ.get('prometheus_multiproc_dir', None)
if not old_env:
    os.environ['prometheus_multiproc_dir'] = '/tmp'
import logging
from random import choice
from string import ascii_uppercase
from dmplib.hug.lic import Licenser
from dmplib import config


class ConfigReverse(config.Config):
    def __init__(self, config_file_path=None):
        super().__init__(config_file_path)
        self.config_file_path = os.path.abspath(config_file_path)


def get(key, default=None):
    """
    获取配置
    :param str key: 格式 [section].[key] 如：app.name
    :param Any default: 默认值
    :return:
    """
    return ConfigReverse.get_instance().get(key, default)


setattr(config, 'get', get)


def check_cloud_dog():
    licenser = Licenser()

    ecs_id, vpc_id = licenser._get_server_fingerprint()

    product_code = licenser._get_product_code()
    expect_nonce = ''.join(choice(ascii_uppercase) for _ in range(12))
    cst_info = {
        "app": config.get('App.name') or 'dmp',
        "ecs_id": ecs_id,
        "vpc_id": vpc_id,
        "product": {"code": product_code, "functions": []},
        "nonce": expect_nonce,
    }

    license_data = licenser._get_server_license_data(cst_info)
    logging.exception('license data: %s' % str(license_data))
    print('license data: %s' % str(license_data))

    code, errmsg = licenser._verify_license_data(license_data, expect_nonce)
    logging.exception('license verify: %s,  %s' % (str(code), str(errmsg)))
    print('license verify: %s,  %s' % (str(code), str(errmsg)))

    return code, errmsg


if __name__ == '__main__':
    check_cloud_dog()
