# -*- coding: utf-8 -*-
"""
Created on 2017年9月21日

@author: zhangyx02
"""
import builtins
import unittest

from dmplib import config
from dmplib.db.mysql_wrapper import get_db, SimpleMysql
import logging

logger = logging.getLogger(__name__)

"""
统计所有租户运行中的流程
"""


class TestFlowInstanceStatistics(unittest.TestCase):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name)
        self.run_flow_instance_dict = {}

    def flow_instance_statistics(self, code):
        db = SimpleMysql(
            host=config.get('DB.host'),
            port=int(config.get('DB.port')),
            db='dmp_' + code,
            user=config.get('DB.user'),
            passwd=config.get('DB.password'),
        )
        sql = "select id,flow_id, name ,type, status,created_on  from `instance` where status= '运行中'"
        run_flow_instance_data = db.query(sql)
        self.run_flow_instance_dict[code] = run_flow_instance_data

    def test_statistics(self):
        with get_db() as db:
            sql = 'SELECT code FROM project'
            code_list = db.query(sql)

        for code in code_list:
            if code["code"] == 'myscrm' or code["code"] == 'mycaigou':
                continue

            builtins.code = code["code"]
            builtins.account = 'admin'
            self.flow_instance_statistics(code['code'])

        for key, value in self.run_flow_instance_dict.items():
            if value:
                logging.error("--------------------------------------")
                logging.error(key)
                for data in value:
                    logging.error(data)


if __name__ == '__main__':
    unittest.main()
