#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import os
import json

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)
from tests.base import BaseTest
from dmplib.utils.strings import seq_id
from dmplib.saas.project import get_db
from components.message_queue import RabbitMQ


class TestSysEvent(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')

    def test_send_mq(self):
        data = {"id": "***********", "name": "cleanup"}
        rabbit_mq = RabbitMQ()
        res = rabbit_mq.send_message(
            "my_sysevent", json.dumps(data), durable=False, headers={"_dmp_message_uuid": seq_id()}
        )
        print(res)

    def test_count_dmp_test_table_records_count(self):
        """
            获取项目库的数据库表的记录统计
        :return: 统计结果列表，按记录数降序排序
        """
        get_tables_sql = 'show tables'
        with get_db() as db:
            table_names = db.query(get_tables_sql)
        if not table_names:
            return

        table_names = [list(r.values())[0] for r in table_names if r.values()]
        table_counts = []
        with get_db() as db:
            for table_name in table_names:
                get_table_count_sql = 'select count(1) as record_count from %s' % table_name
                record_count = db.query_scalar(get_table_count_sql)
                table_counts.append({'table_name': table_name, 'record_count': record_count})
        table_counts.sort(key=lambda r: r['record_count'], reverse=True)

        with open('dmp_test_table_counts.json', 'w') as fd:
            fd.write(json.dumps(table_counts, indent=4, ensure_ascii=False))

        print('success')
