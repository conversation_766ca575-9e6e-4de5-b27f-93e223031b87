#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/13.
"""

from base.enums import FlowInstanceStatus
from dmplib.saas.project import get_db
from base import repository

from flow.models import FlowQueryModel


def running_flow_is_exists(flow_id):
    """
    是否存在运行中的实例
    :param flow_id:
    :return:
    """
    sql = 'SELECT id FROM instance WHERE flow_id=%(flow_id)s AND `status` IN (%(created)s,%(running)s) LIMIT 1 '
    with get_db() as db:
        return db.query_scalar(
            sql,
            {
                'flow_id': flow_id,
                'created': FlowInstanceStatus.Created.value,
                'running': FlowInstanceStatus.Running.value,
            },
        )


def get_flow_list(query_model: FlowQueryModel) -> FlowQueryModel:
    """
    获取flow列表
    :param flow.models.FlowQueryModel query_model:
    :return:
    """
    sql = (
        'SELECT `id` as `flow_id`,`name`,`type`,`startup_time`,`end_time`,`created_on`,'
        'TIMESTAMPDIFF(SECOND,startup_time,end_time) AS running_time, `run_status` as `status` '
        'FROM `flow` '
    )
    params = {}
    # 不查询没有运行过的流程（内置流程有可能没有运行过, 不显示）
    wheres = ['`run_status` is not null']
    if query_model.id:
        wheres.append('`id` = %(id)s')
        params['id'] = query_model.id
    if query_model.keyword:
        wheres.append('`name` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.type:
        wheres.append('`type` = %(type)s')
        params['type'] = query_model.type
    if query_model.status:
        wheres.append('`run_status` = %(run_status)s')
        params['run_status'] = query_model.status
    if query_model.begin_date:
        wheres.append('TO_DAYS(IFNULL(`startup_time`,`created_on`))>=TO_DAYS(%(begin_date)s)')
        params['begin_date'] = query_model.begin_date
    if query_model.end_date:
        wheres.append('TO_DAYS(IFNULL(`startup_time`,`created_on`))<=TO_DAYS(%(end_date)s)')
        params['end_date'] = query_model.end_date

    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    #  按照名称group by
    order_by = ' ORDER BY IFNULL(`startup_time`,`created_on`) DESC '
    sql += order_by
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_instance_list(query_model):
    """
    获取实例列表
    :param flow.models.FlowInstanceQueryModel query_model:
    :return:
    """
    sql = (
        'SELECT `id`,`name`,`flow_id`,`type`,`startup_time`,`end_time`,`status`,'
        'TIMESTAMPDIFF(SECOND,startup_time,end_time) AS running_time, `inspection_id`, `message` '
        'FROM `instance` '
    )
    params = {}
    wheres = []
    if query_model.flow_id:
        wheres.append('`flow_id` = %(flow_id)s')
        params['flow_id'] = query_model.flow_id
    if query_model.keyword:
        wheres.append('`name` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.type:
        wheres.append('`type` = %(type)s')
        params['type'] = query_model.type
    if query_model.status:
        wheres.append('`status` = %(status)s')
        params['status'] = query_model.status
    if query_model.begin_date:
        wheres.append('TO_DAYS(IFNULL(`startup_time`,`created_on`))>=TO_DAYS(%(begin_date)s)')
        params['begin_date'] = query_model.begin_date
    if query_model.end_date:
        wheres.append('TO_DAYS(IFNULL(`startup_time`,`created_on`))<=TO_DAYS(%(end_date)s)')
        params['end_date'] = query_model.end_date
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY IFNULL(`startup_time`,`created_on`) DESC '
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_instance_list_last_time(query_model):
    """
    获取实例列表
    :param flow.models.FlowInstanceQueryModel query_model:
    :return:
    """
    sql = (
        'select SQL_CALC_FOUND_ROWS id,flow_id,`name`, startup_time,end_time,`status`,'
        'TIMESTAMPDIFF(SECOND,startup_time,end_time) AS running_time,message,created_on from ('
        'SELECT flow_id,`name`, id, startup_time,end_time,`status`, message,created_on,'
        '@num := IF(@flow_new_id = flow_id, @num+1, 1) as row_num,@flow_new_id := flow_id '
        'FROM instance , (SELECT @flow_new_id := NULL, @num := 0) temp_table ORDER BY flow_id, startup_time DESC) '
        't WHERE t.row_num <= 1 '
    )
    params = {}
    wheres = []
    if query_model.flow_id:
        operator = ' = '
        if isinstance(query_model.flow_id, list):
            operator = ' IN '
        wheres.append(f'`flow_id` {operator} %(flow_id)s')
        params['flow_id'] = query_model.flow_id
    if query_model.keyword:
        wheres.append('`name` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.type:
        wheres.append('`type` = %(type)s')
        params['type'] = query_model.type
    if query_model.status:
        wheres.append('`status` = %(status)s')
        params['status'] = query_model.status
    if query_model.begin_date:
        wheres.append('TO_DAYS(IFNULL(`startup_time`,`created_on`))>=TO_DAYS(%(begin_date)s)')
        params['begin_date'] = query_model.begin_date
    if query_model.end_date:
        wheres.append('TO_DAYS(IFNULL(`startup_time`,`created_on`))<=TO_DAYS(%(end_date)s)')
        params['end_date'] = query_model.end_date
    sql += ('AND ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY IFNULL(`startup_time`,`created_on`) DESC '
    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
    with get_db() as db:
        query_model.items = db.query(sql, params)
        query_model.total = db.query_scalar('SELECT FOUND_ROWS() AS total;')
    return query_model


def get_today_running_status():
    """
    获取今日运行状态
    :return:
    """
    sql = (
        'SELECT COUNT((`status`=%(created)s) OR NULL) AS created,'
        'COUNT((`status`=%(running)s) OR NULL) AS running,'
        'COUNT((end_time BETWEEN CURDATE() AND NOW() AND `status`=%(successful)s) OR NULL) AS successful,'
        'COUNT((end_time BETWEEN CURDATE() AND NOW() AND`status`=%(failed)s) OR NULL) AS failed,'
        'COUNT((end_time BETWEEN CURDATE() AND NOW() AND`status`=%(aborted)s) OR NULL) AS aborted, '
        'COUNT((end_time BETWEEN CURDATE() AND NOW() AND`status`=%(ignored)s) OR NULL) AS ignored '
        'FROM instance '
        'WHERE created_on BETWEEN CURDATE()  AND NOW()'
    )
    params = {
        'created': FlowInstanceStatus.Created.value,
        'running': FlowInstanceStatus.Running.value,
        'successful': FlowInstanceStatus.Successful.value,
        'failed': FlowInstanceStatus.Failed.value,
        'aborted': FlowInstanceStatus.Aborted.value,
        'ignored': FlowInstanceStatus.Ignored.value,
    }
    with get_db() as db:
        return db.query_one(sql, params)


def get_today_running_time_top10():
    """
    获取今日运行时长前十的实例
    :return:
    """
    sql = (
        'SELECT id,flow_id,`name`,type,startup_time,end_time,'
        'TIMESTAMPDIFF(SECOND,startup_time,end_time) AS running_time,`status`  '
        'FROM instance '
        'WHERE end_time IS NOT NULL AND  end_time BETWEEN CURDATE() AND NOW() AND end_time>startup_time '
        'ORDER BY running_time DESC '
        'LIMIT 10'
    )
    with get_db() as db:
        return db.query(sql)


def get_per_hour_successful_times(begin_date, end_date):
    """
    获取从开始到结束日期中每小时任务完成次数
    :param str begin_date:
    :param str end_date:
    :return:
    """
    sql = (
        'SELECT '
        'DATE_FORMAT(end_time,\'%%Y-%%m-%%d\') AS `date` ,'
        'DATE_FORMAT(end_time,\'%%H\') AS `hour`,'
        'COUNT(1) AS `times` '
        'FROM instance '
        'WHERE `status`=%(status)s AND end_time IS NOT NULL '
        'AND end_time '
        'BETWEEN %(begin_date)s AND  DATE_SUB(DATE_SUB(%(end_date)s,INTERVAL -1 DAY) ,INTERVAL 1 SECOND)'
        'GROUP BY `date`,`hour`'
        'ORDER BY `date`,`hour`'
    )
    with get_db() as db:
        return db.query(
            sql, {'begin_date': begin_date, 'end_date': end_date, 'status': FlowInstanceStatus.Successful.value}
        )


def get_almost_one_month_error_times_top10():
    """
    近一个月出错次数最多前十个任务
    :return:
    """
    sql = (
        'SELECT i.flow_id,f.`name`,i.type,COUNT(1) AS error_times '
        'FROM instance AS i '
        'LEFT JOIN flow AS f '
        'ON i.flow_id=f.id '
        'WHERE i.end_time IS NOT NULL '
        'AND i.end_time BETWEEN DATE_SUB(CURDATE(),INTERVAL 29 DAY) and NOW() '
        'AND i.`status`=%(failed)s '
        'GROUP BY i.flow_id,f.`name`,i.type '
        'ORDER BY error_times DESC '
        'LIMIT 10'
    )
    with get_db() as db:
        return db.query(sql, {'failed': FlowInstanceStatus.Failed.value})


def get_last_flow_end_time(flow_id):
    """
    获取最后一次运行时间
    :param flow_id:
    :return:
    """
    sql = "select end_time, status from instance where flow_id=%(flow_id)s ORDER BY end_time desc limit 1"
    with get_db() as db:
        return db.query_one(sql, {'flow_id': flow_id})


def get_last_success_instance(flow_id):
    """
    获取上一次的instance
    :param flow.models.FlowInstanceQueryModel query_model:
    :return:
    """
    sql = """SELECT  `id`,`name`,`flow_id`,`type`,`startup_time`,`end_time`,
    `status`, `inspection_id`, `message` 
    FROM `instance` where flow_id=%(flow_id)s and status = '已成功'
    order by created_on desc limit 1"""
    with get_db() as db:
        return db.query_one(sql, {'flow_id': flow_id}) or {}
