#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/20.
"""
import json

from dmplib.hug import g

from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib import config
from base import repository


def get_flow_log_db() -> SimpleMysql:
    """
    获取日志db
    :return dmplib.db.mysql_wrapper.SimpleMysql:
    """
    return SimpleMysql(
        host=config.get('DB.host'),
        port=int(config.get('DB.port')),
        db=config.get('DB.flow_log_database'),
        user=config.get('DB.user'),
        passwd=config.get('DB.password'),
    )


def table_is_exists(table_name: str):
    """
    数据表是否存在
    :param table_name:
    :return:
    """
    sql = (
        'SELECT COUNT(1) AS is_exists FROM information_schema.TABLES '
        'WHERE table_schema= DATABASE() AND `table_name`=%(table_name)s '
    )
    with get_flow_log_db() as db:
        return db.query_scalar(sql, {'table_name': table_name})


def get_flow_log_list(query_model):
    """
    获取流程日志
    :param flow.models.FlowInstanceLogQueryModel query_model:
    :return:
    """
    sql = (
        'SELECT `level_name`,`level_no`,`line_no`,`created`,`file_name`,`module`,'
        '`func_name`,`path_name`,`process`,`process_name`,`thread`,`thread_name`,`exc_text`,`message` '
        ' FROM `{tab_name}` '
        'WHERE project_code=%(project_code)s '
        'AND flow_instance_id=%(instance_id)s '.format(tab_name=query_model.table_name)
    )

    params = {'project_code': getattr(g, 'code'), 'instance_id': query_model.instance_id}
    if query_model.node_id:
        sql += 'AND node_id=%(node_id)s '
        params['node_id'] = query_model.node_id
    sql += 'ORDER BY created_on '

    with get_flow_log_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += 'LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        data = db.query(sql, params)
        query_model.items = format_db_log(data)
    return query_model


def format_db_log(data):
    if not data:
        return data
    for item in data:
        message = item.get("message")
        message_data = {}
        if message:
            try:
                message_data = json.loads(message)
            except:
                pass
            if message_data:
                item["parent_id"] = message_data.get("parent_id", "")
                item["type"] = message_data.get("type", "")
                item["id"] = message_data.get("id", "")
                item["message"] = message_data.get("message", "")
    return data
