#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/4/18.
"""
from dmplib.saas.project import get_db


def delete_activity_by_flow_id(flow_id):
    """
    删除节点执行记录
    :param str flow_id:
    :return:
    """
    sql = 'DELETE FROM activity WHERE instance_id IN (SELECT id FROM instance WHERE  flow_id=%(flow_id)s)'
    with get_db() as db:
        return db.exec_sql(sql, {'flow_id': flow_id})


def get_activity_list(instance_id: str):
    """
    获取实例节点
    :param str instance_id:
    :return list:
    """
    sql = (
        'SELECT a.id,n.`name`,a.node_id,a.type,a.startup_time,a.end_time, '
        'TIMESTAMPDIFF(SECOND,a.startup_time,a.end_time) AS running_time,a.`status` '
        'FROM activity AS a '
        'LEFT JOIN node AS n ON a.node_id=n.id '
        'WHERE a.instance_id=%(instance_id)s '
        'ORDER BY a.startup_time ASC '
    )
    with get_db() as db:
        return db.query(sql, {'instance_id': instance_id})
