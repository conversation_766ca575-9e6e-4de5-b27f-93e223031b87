#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/28.
"""
import datetime

from base.models import QuerySortModel
from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id
from base import repository


from flow.models import FlowQueryModel


def get_flow_list(query_model: FlowQueryModel) -> FlowQueryModel:
    """
    获取数据源列表
    :param flow.models.FlowQueryModel query_model:
    :return tuple:
    """
    sql = (
        'SELECT f.id,f.`name`,f.description,f.type,f.build_in,f.`schedule`,f.`status`,'
        'f.run_status,f.depend_flow_id,df.`name` as depend_flow_name '
        'FROM flow as f '
        'LEFT JOIN flow as df on f.depend_flow_id=df.id '
    )
    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append('( f.`name` LIKE %(keyword)s  OR  f.`description` LIKE %(keyword)s)')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.type:
        wheres.append('f.`type` = %(type)s')
        params['type'] = query_model.type
    if query_model.build_in:
        wheres.append('f.`build_in` = %(build_in)s')
        params['build_in'] = query_model.build_in
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    order_by = ''
    if query_model.sorts:
        for sort in query_model.sorts:
            if not isinstance(sort, QuerySortModel):
                continue
            if sort.id == 'name':
                order_by = ' ORDER BY convert(f.`name` USING gbk) COLLATE gbk_chinese_ci ' + sort.method
                break
    sql += order_by or ' ORDER BY f.`created_on` DESC'
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_last_end_time(flow_id):
    """
    获取最后一次结束时间
    :return:
    """
    sql = 'select i.end_time from instance i where i.flow_id = %(flow_id)s ORDER BY  i.end_time desc limit 1;'
    with get_db() as db:
        return db.query_scalar(sql, {'flow_id': flow_id})


def get_flow_id(code, flow_id):
    """
    根据code获取流程id
    :param code:
    :param flow_id:
    :return:
    """
    sql = 'SELECT id FROM flow WHERE id=%(flow_id)s'
    with get_db(code=code) as db:
        return db.query_scalar(sql, {'flow_id': flow_id})


def get_flow(flow_id):
    """
    :param flow_id:
    :return:
    """
    sql = 'SELECT id,name,description,type,schedule,status  FROM flow WHERE id=%(flow_id)s'
    with get_db() as db:
        return db.query_one(sql, params={'flow_id': flow_id})


def get_replacement_dataset(replacement_id):
    sql = 'SELECT id,name,type,replacement_id FROM dataset ' ' WHERE replacement_id = %(replacement_id)s'
    with get_db() as db:
        return db.query_one(sql, params={"replacement_id": replacement_id})


def get_depend_flow(flow_id):
    sql = 'SELECT id,name,description,type,schedule,status,depend_flow_id FROM flow WHERE depend_flow_id = %(flow_id)s'
    with get_db() as db:
        return db.query(sql, params={"flow_id": flow_id})


def get_depend_dataset(flow_id):
    sql = (
        'SELECT id,name,description,type,schedule,status,depend_flow_id FROM flow '
        'INNER JOIN dataset_depend on depend_id = id  WHERE source_dataset_id = %(flow_id)s'
    )
    with get_db() as db:
        return db.query(sql, params={"flow_id": flow_id})


def create_flow_instance(flow_data):
    with get_db() as db:
        flow_instance_id = seq_id()
        flow_instance_data = {
            'id': flow_instance_id,
            'flow_id': flow_data.get('id'),
            'name': flow_data.get('name'),
            'type': flow_data.get('type'),
            'startup_time': datetime.datetime.now().strftime("%y-%m-%d %H:%M:%S"),
            'status': '已创建',
            'message': '',
        }
        db.insert("instance", flow_instance_data)
        return flow_instance_id


def get_flow_status(flow_id):
    # 目前flow的机制是有运行中的实例，再次发送消息，该消息标记为已终止
    # flow_run = 'select count(id) as total, id, flow_id, name, status from instance where flow_id=%(flow_id)s ' \
    #            'and status=%(status)s'
    # flow_order = 'select id, flow_id, name, status from instance where flow_id=%(flow_id)s order by end_time desc'
    flow_order = 'select id as flow_id, name, run_status as status from flow where id = %(flow_id)s'
    with get_db() as db:
        return db.query_one(flow_order, {'flow_id': flow_id})
