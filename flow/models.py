#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    流程所有相关Model
    <NAME_EMAIL> on 2017/3/16.
"""
import json

from base.enums import FlowType, FlowStatus, FlowNodeType, DataCollectMode, FlowInstanceStatus
from base.models import BaseModel, QueryBaseModel
from dmplib.utils.errors import UserError


from typing import Dict, List, Tuple


class FlowModel(BaseModel):
    """
    流程
    """

    __slots__ = [
        'id',
        'name',
        'description',
        'type',
        'build_in',
        'schedule',
        'status',
        'depend_flow_id',
        'depend_flow_name',
        'nodes',
        'lines',
        'state_trigger',
        'run_status',
    ]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.name = None
        self.description = None
        self.type = None
        self.build_in = 0
        self.schedule = None
        self.status = FlowStatus.Disable.value
        self.depend_flow_id = None
        self.depend_flow_name = None
        self.state_trigger = None
        # 流程节点
        self.nodes = None
        # 流程线
        self.lines = None
        self.run_status = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 1000}))
        rules.append(('type', 'in_range', {'range': [e.value for e in FlowType.__members__.values()]}))
        rules.append(('build_in', 'in_range', {'range': [0, 1]}))
        rules.append(('status', 'in_range', {'range': [e.value for e in FlowStatus.__members__.values()]}))
        rules.append(('description', 'string', {'max': 1000, 'required': False}))
        rules.append(('depend_flow_id', 'string', {'max': 36, 'required': False}))
        return rules

    def node_to_model(self):
        """
        将节点转换为Model
        :return:
        """
        if not self.nodes or not isinstance(self.nodes, list):
            return
        tmp = []
        for node in self.nodes:
            if isinstance(node, dict):
                model = FlowNodeModel(**node)
                model.validate()
                tmp.append(model)
            elif isinstance(node, FlowNodeModel):
                tmp.append(node)
        self.nodes = tmp

    def line_to_model(self):
        """
        将连线转换为Model
        :return:
        """
        if not self.lines or not isinstance(self.lines, list):
            return
        tmp = []
        for line in self.lines:
            if isinstance(line, dict):
                model = FlowNodeLineModel(**line)
                if not model.flow_id:
                    model.flow_id = self.id
                tmp.append(model)
            elif isinstance(line, FlowNodeLineModel):
                tmp.append(line)
        self.lines = tmp


class FlowQueryModel(QueryBaseModel):
    __slots__ = ['id', 'type', 'build_in', 'name', 'status', 'begin_date', 'end_date']

    def __init__(self, **kwargs) -> None:
        # 流程类型
        self.id = None
        self.type = None
        self.build_in = None
        self.name = None
        self.status = None
        self.begin_date = None
        self.end_date = None
        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(
            ('type', 'in_range', {'range': [e.value for e in FlowType.__members__.values()], 'required': False})
        )
        return rules


class FlowNodeLineModel(BaseModel):
    def __init__(self, **kwargs):
        """
        流程节点线
        :param kwargs:
        """
        self.id = None
        self.flow_id = None
        self.ahead_node_id = None
        self.behind_node_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['id', 'flow_id', 'ahead_node_id', 'behind_node_id'], 'string', {'max': 36}))
        return rules


class FlowNodeModel(BaseModel):
    """
    流程节点
    """

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.description = None
        self.flow_id = None
        self.is_start = 1
        self.is_end = 1
        self.type = None
        self.content = None
        self.position = NodePosition()
        super().__init__(**kwargs)
        self._content_to_model()

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 1000}))
        rules.append(('description', 'string', {'max': 1000, 'required': False}))
        rules.append(('flow_id', 'string', {'max': 36}))
        rules.append(('is_start', 'in_range', {'range': [0, 1]}))
        rules.append(('is_end', 'in_range', {'range': [0, 1]}))
        rules.append(('type', 'in_range', {'range': [e.value for e in FlowNodeType.__members__.values()]}))
        return rules

    def _content_to_model(self):
        if not self.content:
            return
        if isinstance(self.content, str):
            self.content = json.loads(self.content)
        if not isinstance(self.content, dict):
            return
        if self.type == FlowNodeType.Collector.value:
            model = CollectorNodeContentModel(**self.content)
        elif self.type == FlowNodeType.ODPS_SQL.value:
            model = ODPSSQLNodeContentModel(**self.content)
        elif self.type == FlowNodeType.Mapping.value:
            model = MappingNodeContentModel(**self.content)
        elif self.type == FlowNodeType.Sync.value:
            model = SyncNodeContentModel(**self.content)
        elif self.type == FlowNodeType.Image.value:
            model = ImageNodeContentModel(**self.content)
        elif self.type == FlowNodeType.Download.value:
            model = DownloadNodeContentModel(**self.content)
        elif self.type == FlowNodeType.REPLACEMENT.value:
            model = ReplacementNodeContentModel(**self.content)
        elif self.type == FlowNodeType.ERPORG.value:
            model = ERPSyncNodeContentModel(**self.content)
        elif self.type == FlowNodeType.UserSync.value:
            model = UserSyncNodeContentModel(**self.content)
        else:
            model = NodeContentModel(**self.content)
        model.validate()
        self.content = model


class NodeContentModel(BaseModel):
    pass


class NodePosition(BaseModel):
    __slots__ = ['x', 'y']

    def __init__(self, **kwargs):
        """
        节点在画布中显示位置
        :param kwargs:
        """
        self.x = 0
        self.y = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['x', 'y'],))
        return rules


class CollectorNodeContentModel(NodeContentModel):
    __slots__ = ['data_source_id', 'tables']

    def __init__(self, **kwargs):
        self.data_source_id = None
        self.tables = None
        super().__init__(**kwargs)
        self._tables_to_model()

    def _tables_to_model(self):
        if not self.tables:
            return
        if isinstance(self.tables, list):
            tmp = []
            for table in self.tables:
                if isinstance(table, dict):
                    model = CollectorTableModel(**table)
                    model.validate()
                    tmp.append(model)
                else:
                    tmp.append(table)
            self.tables = tmp

    def rules(self):
        rules = super().rules()
        rules.append(('data_source_id', 'string', {'max': 36}))
        rules.append(('tables',))
        return rules


class CollectorTableModel(BaseModel):
    """
    采集数据表
    """

    __slots__ = ['name', 'description', 'columns', 'mode', 'timestamp_col_name', 'task_mode']

    def __init__(self, **kwargs):
        self.name = None
        self.description = None
        self.columns = None
        self.mode = None
        self.timestamp_col_name = None
        self.task_mode = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['name', 'columns'],))
        rules.append(('mode', 'in_range', {'range': [e.value for e in DataCollectMode.__members__.values()]}))
        rules.append(('mode', 'validate_timestamp_col'))
        return rules

    def validate_timestamp_col(self, config, attr_name, value):
        if value == DataCollectMode.Incremental.value and not self.timestamp_col_name:
            msg = config.get('msg')
            raise UserError(message=attr_name + '为：' + value + ' timestamp_col_name不能为空' if not msg else msg)


class ODPSSQLNodeContentModel(NodeContentModel):
    __slots__ = ['sql']

    def __init__(self, **kwargs):
        self.sql = ''
        super().__init__(**kwargs)


class MappingNodeContentModel(NodeContentModel):
    __slots__ = ['table_name', 'mapping', 'regex_mapping']

    def __init__(self, **kwargs):
        """
        映射节点内容
        mapping = {'xb':{'1':'男','0':'女'}}
        :param kwargs:
        """
        self.table_name = ''
        self.mapping = {}
        self.regex_mapping = {}
        super().__init__(**kwargs)


class SyncNodeContentModel(NodeContentModel):
    __slots__ = ['source', 'target', 'error_limit']

    def __init__(self, **kwargs):
        self.source = None
        self.target = None
        self.error_limit = 0
        super().__init__(**kwargs)
        self._source_to_model()
        self._target_to_model()

    def _source_to_model(self):
        if not self.source or not isinstance(self.source, dict):
            return
        model = SyncSourceTableModel(**self.source)
        model.validate()
        self.source = model

    def _target_to_model(self):
        if not self.target or not isinstance(self.target, dict):
            return
        model = SyncTargetTableModel(**self.target)
        model.validate()
        self.target = model


class SyncSourceTableModel(BaseModel):
    __slots__ = ['source_id', 'source_name', 'column', 'table', 'partition']

    def __init__(self, **kwargs):
        self.source_id = None
        self.source_name = None
        self.column = None
        self.table = None
        # 多个分区以字典的方式存储
        self.partition = None
        super().__init__(**kwargs)


class SyncTargetTableModel(BaseModel):
    __slots__ = ['source_id', 'source_name', 'column', 'table', 'pre_sql', 'post_sql', 'write_mode', 'create_new_table']

    def __init__(self, **kwargs):
        self.source_id = None
        self.source_name = None
        self.column = None
        self.table = None
        self.pre_sql = None
        self.post_sql = None
        self.write_mode = 'replace'
        self.create_new_table = 0
        super().__init__(**kwargs)


class DownloadNodeContentModel(NodeContentModel):
    __slots__ = ['indicators']

    def __init__(self, **kwargs):
        self.indicators = None
        super().__init__(**kwargs)


class ReplacementNodeContentModel(NodeContentModel):
    __slots__ = ['file_name', 'oss_url', 'dataset_id', 'table_name']

    def __init__(self, **kwargs):
        self.file_name = None
        self.oss_url = None
        self.dataset_id = None
        self.table_name = None
        super().__init__(**kwargs)


class ERPSyncNodeContentModel(NodeContentModel):
    __slots__ = [
        'data_source_id',
        'organization_sql',
        'station_sql',
        'user_sql',
        'user_station_sql',
        'user_sql_with_isquit',
    ]

    def __init__(self, **kwargs):
        self.data_source_id = None
        self.organization_sql = None
        self.station_sql = None
        self.user_sql = None
        self.user_station_sql = None
        self.user_sql_with_isquit = None
        super().__init__(**kwargs)


class UserSyncNodeContentModel(NodeContentModel):
    __slots__ = ['item']

    def __init__(self, **kwargs):
        self.item = None
        super().__init__(**kwargs)


class ImageNodeContentModel(NodeContentModel):
    __slots__ = ['sync_table']

    def __init__(self, **kwargs):
        # 是否同步360数据表
        self.sync_table = True
        super().__init__(**kwargs)


class FlowInstanceModel(BaseModel):
    __slots__ = ['id', 'flow_id', 'name', 'type', 'status', 'startup_time', 'queue_message']

    def __init__(self, **kwargs):
        """
        流程实例
        :param kwargs:
        """
        self.id = None
        self.flow_id = None
        self.name = None
        self.type = None
        self.status = FlowInstanceStatus.Created.value
        self.startup_time = ""
        self.queue_message = ''
        super().__init__(**kwargs)


class FlowInstanceQueryModel(QueryBaseModel):
    __slots__ = ['flow_id', 'type', 'status', 'begin_date', 'end_date']

    def __init__(self, **kwargs):
        self.flow_id = None
        self.type = None
        self.status = None
        self.begin_date = None
        self.end_date = None
        super().__init__(**kwargs)


class FlowInstanceLogQueryModel(QueryBaseModel):
    __slots__ = ['instance_id', 'node_id', 'table_name', 'type', 'level_name']

    def __init__(self, **kwargs) -> None:
        self.instance_id = None
        self.node_id = None
        self.table_name = None
        self.type = None
        self.level_name = None
        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[str, str, Dict[str, int]]]:
        rules = super().rules()
        rules.append(('instance_id', 'string', {'max': 36}))
        return rules
