#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import base64
import logging


from components.rundeck import CommonRunDeckScheduler
from base.dmp_constant import RUNDECK_PRODUCE_COMMON_MQ_COMMAND_TEMPLATE
from base.errors import UserError
from flow.models import FlowModel
from components.rundeck import RundeckScheduler
from dmplib.hug import debugger

logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)


def upset_send_common_message_job(job_data, queue_name, message: dict = None):
    """
    注册或更新通用MQ定时调度任务
    :param job_data: 定时任务相关数据: dict
    :param queue_name: 消息队列名称：str，不能有空格，必须为连续字符的字符串
    :param message: 定时任务发起的MQ的消息体内容: dict字典类型, 默认消息体为: {'id': job_data['id'], 'name': job_data['name']}
    :return:
    """

    def handle_message_content(msg: dict):
        handled_str = base64.b64encode(json.dumps(msg).encode('utf-8')).decode('utf-8')
        return handled_str

    data = {
        'job_id': job_data['id'],
        'name': job_data['name'],
        'schedule': job_data['schedule'],
        'description': job_data.get('description', ''),
        'schedule_enabled': job_data.get('schedule_enabled', True),
        'group': job_data.get('group', ''),
    }
    message = message or {'id': job_data['id'], 'name': job_data['name']}
    command = RUNDECK_PRODUCE_COMMON_MQ_COMMAND_TEMPLATE % (queue_name, handle_message_content(message))
    data['command'] = command

    return CommonRunDeckScheduler().upset_job(**data)


def upset_sysevent_rundeck_job(data: dict):
    """
    注册DMP系统事件RunDeck Job调度任务
    :param data:
    :return:
    """

    def validate_external_params(data: dict, required: list):
        for key in required:
            if key not in data:
                raise UserError(message='参数%s必填' % key)
            if not data[key]:
                raise UserError(message='参数%s不能为空值' % key)

    if not isinstance(data, dict):
        raise UserError(message='参数错误')

    validate_external_params(data, ['jobs', 'queue_name'])
    if not isinstance(data['jobs'], list):
        raise UserError(message='flow必须为对列表类型')

    msg = f'开始注册DMP系统事件Job调度任务，任务列表：{json.dumps(data.get("jobs", []))}'
    logger.info(msg)
    _debugger.log(msg)

    queue_name = data['queue_name']
    for job_data in data['jobs']:
        validate_external_params(job_data, ['id', 'name', 'description', 'schedule'])
        if job_data and job_data.get('id') == 'DMP-Sysevent-clearrundeck':
            run_clear_rundeck_data_job(job_data)
        else:
            upset_send_common_message_job(job_data, queue_name)
    return True


def run_clear_rundeck_data_job(job_data):
    """
    启动清理rundeck数据的任务
    :param job_data:
    :return:
    """
    new_job_data = {
        "id": job_data.get("id", ""),
        "name": job_data.get("name", ""),
        "status": "启用",
        "schedule": job_data.get("schedule") or "0 0 22 ? * TUE *",
        "description": job_data.get("description", "RUNDECK历史数据清理任务"),
    }
    command = "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/clear_rundeck.py rundeck"
    model = FlowModel(**new_job_data)
    scheduler = RundeckScheduler(model)
    if scheduler.job_is_exists():
        _debugger.log("更新清理rundeck数据任务")
        scheduler.update_job(command)
    else:
        _debugger.log("新增清理rundeck数据任务")
        scheduler.add_job(command)
    return True, 'ok'
