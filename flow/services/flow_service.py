#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    流程服务
    <NAME_EMAIL> on 2017/3/16.
"""
import json
import os

from base import repository
from base.enums import FlowStatus, DatasetConnectType, OperateMode
from components.message_queue import RabbitMQ
from components.rundeck import RundeckScheduler, EnvRundeckScheduler
from dataset.models import DatasetModel
from dataset.repositories import dataset_repository, dataset_subject_repository
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from flow.models import FlowNodeModel, FlowModel, FlowNodeLineModel, FlowInstanceModel
from flow.repositories import flow_repository
from flow.services import flow_node_service, flow_line_service, flow_instance_service

from flow.models import FlowQueryModel


def add_flow(model):
    """
    添加流程
    :param flow.models.FlowModel model:
    :return bool:
    """
    if not model.id:
        model.id = seq_id()
    model.validate()
    fields = [
        'id',
        'name',
        'description',
        'type',
        'build_in',
        'schedule',
        'status',
        'depend_flow_id',
        'state_trigger',
        'run_status',
    ]
    result = repository.add_model('flow', model, fields)
    if not result:
        raise UserError(message='添加流程失败')
    try:
        save_nodes(model)
        save_lines(model)
    except UserError as e:
        delete_flow(model.id)
        raise UserError(message='添加失败：' + str(e.message))
    return model


def save_nodes(model):
    """
    保存流程节点
    :param flow.models.FlowModel model:
    :return:
    """
    if not model.nodes:
        flow_node_service.delete_node_by_flow_id(model.id)
        return
    model.node_to_model()
    node_id_list = flow_node_service.get_node_id_list_by_flow_id(model.id)
    for node_model in model.nodes:
        if not isinstance(node_model, FlowNodeModel):
            continue
        elif not node_model.id:
            node_model.flow_id = model.id
            flow_node_service.add_node(node_model)
        else:
            node_model.flow_id = model.id
            flow_node_service.update_node(node_model)
            if node_id_list and node_model.id in node_id_list:
                node_id_list.remove(node_model.id)
    if node_id_list:
        # 删除节点
        for node_id in node_id_list:
            flow_node_service.delete_node(node_id)


def save_lines(model):
    """
    保存流程线
    :param flow.models.FlowModel model:
    :param model:
    :return:
    """
    if not model.lines:
        flow_line_service.delete_line_by_flow_id(model.id)
        return
    model.line_to_model()
    line_id_list = flow_line_service.get_line_id_list_by_flow_id(model.id)
    for line_model in model.lines:
        if not isinstance(line_model, FlowNodeLineModel):
            continue
        elif not line_model.id:
            line_model.flow_id = model.id
            flow_line_service.add_line(line_model)
        else:
            line_model.flow_id = model.id
            flow_line_service.update_line(line_model)
            if line_model.id in line_id_list:
                line_id_list.remove(line_model.id)
    if line_id_list:
        # 删除节点连线
        for line_id in line_id_list:
            flow_line_service.delete_line(line_id)


def update_flow(model, is_schedule=True, queue_name=None, command=None):
    """
    更新流程
    :param flow.models.FlowModel model:
    :param str is_schedule:
    :param str queue_name:
    :param str command:
    :return:
    """
    model.validate()
    model.node_to_model()
    fields = ['name', 'description', 'schedule', 'depend_flow_id', 'status', 'state_trigger']
    condition = {'id': model.id}
    update_recode_count = repository.update_model('flow', model, condition, fields)
    save_nodes(model)
    save_lines(model)
    if is_schedule:
        update_flow_schedule(model.id, command=command, queue_name=queue_name)
    return update_recode_count


def update_flow_schedule(flow_id, command=None, queue_name=None):
    """
    更新流程调度
    :param str flow_id:
    :param str command:
    :param str queue_name:
    :return:
    """
    model = get_flow(flow_id)
    if not model:
        return False
    scheduler = RundeckScheduler(model)
    scheduler.queue_name = queue_name
    if scheduler.job_is_exists():
        if model.depend_flow_id:
            scheduler.delete_job()
        else:
            scheduler.update_job(command=command)
    elif not model.depend_flow_id:
        scheduler.add_job(command=command)
    return True


def delete_flow(flow_id, is_delete_scheduler=True):
    """
    删除流程
    :param str flow_id:
    :param bool is_delete_scheduler:
    :return:
    """
    model = get_flow(flow_id)
    if not model:
        return 0

    if int(model.build_in) == 1:
        raise UserError(message='内置流程不允许删除')
    if not model.depend_flow_id and is_delete_scheduler:
        RundeckScheduler(model).delete_job()
    record = repository.delete_data('flow', {'id': flow_id})
    record += flow_node_service.delete_node_by_flow_id(flow_id)
    # 团队商量后，一致决定不删除activity表数据
    # record += flow_node_activity_service.delete_activity_by_flow_id(flow_id)
    record += flow_line_service.delete_line_by_flow_id(flow_id)
    record += flow_instance_service.delete_instance_by_flow_id(flow_id)
    return record


def delete_flow_schedule(flow_id):
    """
    删除流程调度
    :param str flow_id:
    :return:
    """
    model = get_flow(flow_id)
    if not model:
        raise UserError(message='流程不存在')
    return RundeckScheduler(model).delete_job()


def get_flow(flow_id, include_nodes=None, include_lines=None):
    """
    获取流程
    :param str flow_id:
    :param bool include_nodes: 是否返回流程节点
    :param bool include_lines: 是否返回流程连线
    :return:
    """
    if not flow_id:
        return None
    fields = ['id', 'name', 'description', 'type', 'build_in', 'schedule', 'status', 'depend_flow_id', 'state_trigger']
    data = repository.get_data('flow', {'id': flow_id}, fields)
    if not data:
        return None
    model = FlowModel(**data)
    if model.depend_flow_id:
        model.depend_flow_name = repository.get_data_scalar('flow', {'id': flow_id}, 'name')
    if include_nodes:
        model.nodes = flow_node_service.get_nodes_by_flow_id(model.id)
        model.node_to_model()
    if include_lines:
        model.lines = flow_line_service.get_lines_by_flow_id(model.id)
        model.line_to_model()
    return model


def enable_flow(flow_id, command=None, queue_name=None):
    """
    启动流程
    :param str flow_id:
    :param str command:
    :param str queue_name:
    :return:
    """
    model = get_flow(flow_id)
    if not model:
        raise UserError(message='流程不存在')
    model.status = FlowStatus.Enable.value
    repository.update_data('flow', model.get_dict(['status']), {'id': model.id})
    update_flow_schedule(flow_id, command=command, queue_name=queue_name)
    return True


def disable_flow(flow_id, command=None, queue_name=None):
    """
    禁用流程
    :param str flow_id:
    :param str command:
    :param str queue_name:
    :return:
    """
    model = get_flow(flow_id)
    if not model:
        raise UserError(message='流程不存在')
    model.status = FlowStatus.Disable.value
    repository.update_data('flow', model.get_dict(['status']), {'id': model.id})
    update_flow_schedule(flow_id, command=command, queue_name=queue_name)
    return True


def run_flow(
        flow_id,
        is_continue=None,
        queue_name=None,
        instance_id=None,
        check_direct=False,
        need_log=False,
        extra_data: dict = None,
):
    """
    运行流程
    :param str flow_id:
    :param bool is_continue:
    :param str queue_name:
    :param str instance_id:
    :return:
    """
    if not flow_id:
        raise UserError(message='缺少flow_id')
    # 判断是否为直连模式
    if check_direct:
        dataset = dataset_repository.get_dataset(flow_id)
        if dataset.get('connect_type') == DatasetConnectType.Directly.value:
            raise UserError(message='直连模式只需刷新页面')
    flow = get_flow(flow_id)
    if not flow:
        raise UserError(message='该流程不存在')

    flow_instance_id = flow_instance_service.running_flow_is_exists(flow.id)
    if flow_instance_id:
        flow_instance_service.stop_instance(flow_instance_id)

    if instance_id:
        flow_instance_data = flow_instance_service.get_instance_by_id(instance_id)
        flow_instance = FlowInstanceModel(**flow_instance_data)
    else:
        flow_instance = FlowInstanceModel(flow_id=flow.id, name=flow.name, type=flow.type)
        flow_instance_service.add_instance(flow_instance)

    # 发送执行消息
    data = {
        'project_code': getattr(g, 'code'),
        'flow_id': flow.id,
        'flow_instance_id': flow_instance.id,
        'test_run': "0",
    }
    data.update(extra_data or {})
    # 立即运行默认走优先队列
    queue_name = queue_name if queue_name else config.get('RabbitMQ.queue_name_flow_priority')
    rabbit_mq = RabbitMQ()
    try:
        rabbit_mq.send_message(queue_name, json.dumps(data), durable=False, headers={"_dmp_message_uuid": seq_id()})
        repository.update_data('flow', {'run_status': flow_instance.status}, {'id': flow.id})
        # 手动更新需要添加操作日志, 记录flow_instance.id 和 其对应状态
        if need_log:
            from dataset.services.dataset_define_service import get_operate_record_model, get_data_source

            dataset = dataset_repository.get_dataset(flow_id)
            model = DatasetModel(**dataset)
            repository.add_data(
                'dataset_operate_record',
                get_operate_record_model(
                    flow_id,
                    OperateMode.Sync_data.value,
                    data_source=get_data_source(model),
                    instance_id=flow_instance.id,
                    run_status='已创建',
                ).get_dict(),
            )
    except Exception as e:
        repository.delete_data('instance', {'id': flow_instance.id})
        raise UserError(message=f'发送执行消息失败:{e}')


def update_flow_status(flow_id, run_status, startup_time=None, end_time=None):
    """
    修改流程运行状态
    :param flow_id:
    :param run_status:
    :param startup_time:
    :param end_time:
    :return:
    """
    if not flow_id:
        raise UserError(message='缺少流程ID')
    if not run_status:
        raise UserError(message='缺少流程状态')
    data = {'run_status': run_status}
    if startup_time:
        data['startup_time'] = startup_time
    if end_time:
        data['end_time'] = end_time
    repository.update_data('flow', data, {'id': flow_id})


def get_flow_list(query_model: FlowQueryModel) -> FlowQueryModel:
    """
    获取流程列表
    :param flow.models.FlowQueryModel query_model:
    :return:
    """
    return flow_repository.get_flow_list(query_model)


def get_flow_last_end_time(flow_id):
    """
    获取最后一次结束时间
    :return:
    """
    return flow_repository.get_last_end_time(flow_id)


def get_flow_id(code, flow_id):
    """
    根据code获取流程id
    :param code:
    :param flow_id:
    :return:
    """
    return flow_repository.get_flow_id(code, flow_id)

def send_depend_flow_message(flow_id):
    # 如果是主题下的数据集的话，使用主题的flow_id
    subject_dataset = dataset_subject_repository.is_subject_table(flow_id)
    flow_id = subject_dataset.get("id") if subject_dataset else flow_id
    current_flow_data = flow_repository.get_flow(flow_id)
    source_flow_id = flow_id
    if current_flow_data and current_flow_data.get("type") == "数据集":
        replacement_data = flow_repository.get_replacement_dataset(flow_id)
        if replacement_data and replacement_data.get("id"):
            source_flow_id = replacement_data.get("id")
    depend_flow_list = flow_repository.get_depend_flow(source_flow_id)
    depend_dataset_list = flow_repository.get_depend_dataset(source_flow_id)

    # 加入数据集依赖
    if depend_flow_list:
        depend_flow_list.extend(depend_dataset_list)
    else:
        depend_flow_list = depend_dataset_list

    if not depend_flow_list:
        return

    send_rabbitmq(flow_id, depend_flow_list)



def send_rabbitmq(flow_id, depend_flow_list):
    mq = RabbitMQ()
    for depend_flow in depend_flow_list:
        flow_data = flow_repository.get_flow(depend_flow.get('id'))
        if not flow_data:
            continue

        if depend_flow.get("type") == "数据集":
            queue_name = config.get('RabbitMQ.queue_name_flow', 'Flow')
        else:
            # 去掉offline
            # queue_name = os.environ.get("QUEUE_NAME_FLOW_OFFLINE", "Flow-offline")
            queue_name = config.get('RabbitMQ.queue_name_flow', 'Flow')

        flow_instance_id = flow_repository.create_flow_instance(flow_data)
        body = {
            'project_code': g.code,
            'flow_id': depend_flow.get("id"),
            'flow_instance_id': flow_instance_id,
            'test_run': "0",
            "root_flow_id": flow_id,
        }
        mq.send_message(queue_name, json.dumps(body), durable=False)


def check_env_task(flow_model: FlowModel):
    """
    检查环境级任务任务是否存在
    环境级任务：同一个任务id在一个环境中只有一个
    :param str flow_model:
    :return:
    """
    scheduler = EnvRundeckScheduler(flow_model)
    return scheduler.job_is_exists()


def update_flow_env_schedule(flow_id, command=None, queue_name=None):
    """
    更新流程调度（环境级任务，同一个任务id在一个环境中只有一个）
    :param str flow_id:
    :param str command:
    :param str queue_name:
    :return:
    """
    model = get_flow(flow_id)
    if not model:
        return False
    scheduler = EnvRundeckScheduler(model)
    scheduler.queue_name = queue_name
    if scheduler.job_is_exists():
        if model.depend_flow_id:
            scheduler.delete_job()
        else:
            scheduler.update_job(command=command)
    elif not model.depend_flow_id:
        scheduler.add_job(command=command)
    return True
