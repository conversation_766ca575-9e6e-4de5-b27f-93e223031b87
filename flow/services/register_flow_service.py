import json
import time
import traceback

from loguru import logger

from dmplib import config
from dmplib.redis import conn_custom_prefix
from base import repository
from base.enums import FlowType, ProjectAction
from base.dmp_constant import REDIS_TEMPORARY_LOG_STORE_KEY
from flow.services import flow_service
from dashboard_snapshot.services.dashboard_snapshot_service import get_command as get_dashboard_snapshot_command
from manual_filling.services.filling_template_service import ManualFillingService
from feed.services.dashboard_feeds_service import get_command as get_feed_command


def get_action_name(action):
    action_name = {
        ProjectAction.Init.value: "注册",
        ProjectAction.Enable.value: "启用",
        ProjectAction.Disable.value: "禁用",
    }
    return action_name.get(action) or '注册'


def get_msg_key(code, action):
    return f'reg_rundeck_task_{action}:{code}'


def reg_rundeck_task_do(code, action=None):
    """
    租户的调度任务注册
    :param code:
    :param action: 租户操作场景。init：开租户，enable：租户启用，disable：租户禁用
    :return:
    """
    # 默认是开租户的任务注册操作
    if not action:
        action = ProjectAction.Init.value
    action = action.lower()
    conn = conn_custom_prefix(REDIS_TEMPORARY_LOG_STORE_KEY)
    msg_list = []
    try:
        flow_list = _get_flow_list(code, msg_list, action)
        kwargs = {
            "code": code,
            "flow_list": flow_list,
            "msg_list": msg_list,
            "conn": conn,
            "action": action,
        }
        flow_rundeck_task_exec(**kwargs)
    except Exception as e:
        key = get_msg_key(code, action)
        msg_list.append(f'调度任务注册失败，errs：{str(e)} trace：{traceback.format_exc()}')
        conn.set(key, json.dumps(msg_list).encode(), 30 * 60)
        raise e


def _get_flow_list(code, msg_list: list, action):
    action_name = get_action_name(action)
    msg = f"code:{code} 开始调度任务{action_name}"
    logger.error(msg)
    msg_list.append(msg)
    if action == ProjectAction.Init.value:
        query_flow_sql = """select id,type from flow where status = '启用' and schedule is not null 
                        and type in ('数据集', '看板拍照', '手工填报', '订阅') order by type;"""
    else:
        query_flow_sql = """select id,type from flow where schedule is not null 
                        and type in ('数据集', '看板拍照', '手工填报', '订阅') order by type;"""
    flow_list = repository.get_data_by_sql(query_flow_sql, {}) or []
    flow_num = len(flow_list)
    start_msg = f'code:{code} 查找到{flow_num}个调度任务，开始任务{action_name}...'
    logger.error(start_msg)
    msg_list.append(start_msg)
    return flow_list


def flow_rundeck_task_exec(code, flow_list, msg_list, conn, action):
    """
    处理每个flow 任务
    :param code: 租户code
    :param flow_list: flow 列表
    :param msg_list: 消息列表
    :param conn: redis conn
    :param action: 租户操作
    :return:
    """
    key = get_msg_key(code, action)
    action_name = get_action_name(action)

    i = 1
    st = time.time()
    for flow in flow_list:
        flow_type = flow.get("type")
        flow_id = flow.get("id")
        msg = f"{flow_type} {flow_id} {action_name}"
        try:
            rundeck_task_exec(flow_id, flow_type, action)
            msg += "任务成功"
        except Exception as e:
            msg = f"任务失败，{str(e)}"
        log_msg = f"第{i}个 {msg}"
        print(log_msg)
        time.sleep(0.001)
        i += 1
        msg_list.append(log_msg)
        conn.set(key, json.dumps(msg_list).encode(), 30 * 60)

    dr = round(time.time() - st, 2)
    msg_list.append(f'任务处理完成！总耗时：{dr} S')
    conn.set(key, json.dumps(msg_list).encode(), 30 * 60)


def rundeck_task_exec(flow_id, flow_type, action):
    """
    开始rundeck 任务的启用或禁用
    :param flow_id:
    :param flow_type:
    :param action:
    :return:
    """
    if flow_type == FlowType.Dataset.value:
        queue_name = config.get('RabbitMQ.queue_name_flow', 'flow')
        update_flow(flow_id, action, queue_name=queue_name)

    elif flow_type == FlowType.DashboardSnap.value:
        update_flow(flow_id, action, command=get_dashboard_snapshot_command(flow_id, "celery-slow"))

    elif flow_type == FlowType.ManualFilling.value:
        update_flow(flow_id, action, command=ManualFillingService.get_command(flow_id))

    elif flow_type == FlowType.Feeds.value:
        update_flow(flow_id, action, command=get_feed_command(flow_id))


def update_flow(flow_id, action, command=None, queue_name=None):
    # 租户的初始化，启用操作需要开启flow 和注册调度任务
    if action in [ProjectAction.Init.value, ProjectAction.Enable.value]:
        flow_service.enable_flow(flow_id, command=command, queue_name=queue_name)
    else:
        flow_service.disable_flow(flow_id, command=command, queue_name=queue_name)
