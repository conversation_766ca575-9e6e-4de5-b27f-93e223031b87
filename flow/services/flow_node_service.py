#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/16.
"""
import json

from base import repository
from base.models import BaseModelEncoder
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from flow.models import NodeContentModel


def add_node(model):
    """
    添加流程节点
    :param flow.models.FlowNodeModel model:
    :return:
    """
    model.id = seq_id()
    model.validate()
    if isinstance(model.content, NodeContentModel):
        model.content = json.dumps(model.content.get_dict(), cls=BaseModelEncoder)
    else:
        model.content = ''
    fields = ['id', 'name', 'description', 'flow_id', 'is_start', 'is_end', 'type', 'content', 'position']
    return repository.add_model('node', model, fields)


def update_node(model):
    """
    更新流程节点
    :param flow.models.FlowNodeModel model:
    :return:
    """
    model.validate()
    if isinstance(model.content, NodeContentModel):
        model.content = json.dumps(model.content.get_dict(), cls=BaseModelEncoder)
    else:
        model.content = ''
    fields = ['name', 'description', 'is_start', 'is_end', 'type', 'content', 'position']
    return repository.update_model('node', model, {'id': model.id}, fields)


def delete_node(node_id):
    """
    删除流程节点
    :param node_id:
    :return:
    """
    if not node_id or not repository.data_is_exists('node', {'id': node_id}):
        raise UserError(message='流程节点不存在')
    return repository.delete_data('node', {'id': node_id})


def delete_node_by_flow_id(flow_id):
    """
    删除流程下所有节点
    :param str flow_id:
    :return:
    """
    return repository.delete_data('node', {'flow_id': flow_id})


def get_node_id_list_by_flow_id(flow_id):
    """
    获取流程下所有节点id
    :param str flow_id:
    :return list:
    """
    return repository.get_columns('node', {'flow_id': flow_id}, 'id')


def get_nodes_by_flow_id(flow_id):
    """
    获取流程下所有节点
    :param flow_id:
    :return:
    """
    fields = ['id', 'name', 'description', 'flow_id', 'is_start', 'is_end', 'type', 'content', 'position']
    return repository.get_data('node', {'flow_id': flow_id}, fields, True)
