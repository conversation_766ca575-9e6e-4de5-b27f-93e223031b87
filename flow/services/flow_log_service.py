#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2017/4/20.
"""
from dmplib.utils.errors import UserError
from flow.repositories import flow_log_repository
from flow.services import flow_instance_service


from flow.models import FlowInstanceLogQueryModel
from typing import Dict


def get_flow_log_list(query_model: FlowInstanceLogQueryModel):
    """
    获取流程日志
    :param flow.models.FlowInstanceLogQueryModel query_model:
    :return:
    """
    query_model.validate()
    instance = flow_instance_service.get_instance_by_id(query_model.instance_id)
    if not instance:
        raise UserError(message='流程实例不存在')
    startup_time = instance.get('startup_time')
    if not startup_time:
        raise UserError(message='实例未运行')
    query_model.table_name = startup_time.strftime('%Y%m%d')
    result = get_flow_log_list_logstore(query_model)
    if result.get('items'):
        return result
    if not flow_log_repository.table_is_exists(query_model.table_name):
        raise UserError(message='流程日志表不存在')
    return flow_log_repository.get_flow_log_list(query_model).get_result_dict()


def get_flow_log_list_logstore(query_model: FlowInstanceLogQueryModel) -> Dict[str, int]:
    """
    通过logstore获取流程日志
    :param flow.models.FlowInstanceLogQueryModel query_model:
    :return:
    """
    return {}
