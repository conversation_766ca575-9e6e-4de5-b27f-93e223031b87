import time

from base import repository
import json
import logging
from dmplib.utils.strings import seq_id
from base.enums import DataSourceType, MysoftNewERPConfigType, MysoftNewERPDataBaseType, MysoftNewERPDataFromType, \
    FlowType, FlowNodeType, ApplicationType, DashboardPlatforms
from shuxin15_upgrade.services.external_upgrade_service import \
    update_upgrade_err_log, update_upgrade_notice_log, update_upgrade_warning_log
from dmplib import config

logger = logging.getLogger(__name__)


def role_folder_sql_exec():
    sql = """insert into user_role_folder_data_permission(role_id,data_id,data_type,data_action_code,created_on,modified_on,created_by,modified_by)
select   
a.role_id, b.newid as data_id ,'large_screen' as data_type,data_action_code,NOW(),null ,'upgrade', null
from user_role_folder_data_permission  a
inner join (
	select '3a0f5b03-0500-9275-5422-2faf94a7bb60' as newid ,'3a0b3576-462d-134d-2561-f22c29fbe35c' as oldid union all
	select '3a0f5b03-0505-af5e-de39-27d23103a98d' as newid ,'3a0b3576-6e77-0f29-0013-4d5b92e61c48' as oldid union all
	select '3a0ec4fe-5ca2-9bfc-b674-289abbd28abb' as newid ,'3a0c7fff-57f1-6462-a9cd-d956e0bcdc85' as oldid union all
	select '3a0f5b02-fcf7-658d-025e-8e635bfe94f2' as newid ,'3a0c184e-3d85-0420-92d6-6c8bd8b20ae2' as oldid union all
	select '3a0f5b02-fcff-3f77-474b-6a6b438229ae' as newid ,'3a0c184e-875a-80ae-8ac4-e073b26a9940' as oldid union all
	select '3a0f5b03-03a1-bf8c-bb77-9100887b3b79' as newid ,'3a0b3afd-cabd-1eb3-c132-ac1dade7adff' as oldid union all
	select '3a0f5b03-03a6-fbff-984b-4fbcaffd444c' as newid ,'3a0be8b3-f2bf-bc84-9edb-2ea7704d6d60' as oldid union all
	select '3a0f5b02-fae6-329e-32c5-755f15ba8ae8' as newid ,'3a06566f-0af1-da78-89fd-6fc1dfa1a579' as oldid union all
	select '3a0f5b02-f9ef-f2b9-9c07-c64f6e711a5b' as newid ,'3a09dc4a-178e-3afb-c19c-f26c28c4c930' as oldid union all
	select '3a0f5b02-f9f6-a864-c851-b5fba7b09d03' as newid ,'3a09dc4a-40e8-9306-a136-30a7f35ee977' as oldid union all
	select '3a0f5b03-00cd-0478-15ad-6c3b75485155' as newid ,'3a0803f8-f22d-0983-ab0c-ed2af6c31e5f' as oldid union all
	select '3a0f5b02-fdff-de94-39e0-00c7403b52f7' as newid ,'3a07b559-2587-5b78-4eb0-71caf8cb48cf' as oldid union all
	select '3a0f5b02-fe09-240c-97dc-a5fdd362999c' as newid ,'3a07b560-3fa0-83fb-fc68-8024f1a241d9'
)b on a.data_id = b.oldid
where data_type ='dashboard'   and  not exists(
select 1 from user_role_folder_data_permission  dd  where dd.data_id in ('3a0f5b03-0500-9275-5422-2faf94a7bb60','3a0f5b03-0505-af5e-de39-27d23103a98d','3a0ec4fe-5ca2-9bfc-b674-289abbd28abb','3a0f5b02-fcf7-658d-025e-8e635bfe94f2','3a0f5b02-fcff-3f77-474b-6a6b438229ae','3a0f5b03-03a1-bf8c-bb77-9100887b3b79','3a0f5b03-03a6-fbff-984b-4fbcaffd444c','3a0f5b02-fae6-329e-32c5-755f15ba8ae8','3a0f5b02-f9ef-f2b9-9c07-c64f6e711a5b','3a0f5b02-f9f6-a864-c851-b5fba7b09d03','3a0f5b03-00cd-0478-15ad-6c3b75485155','3a0f5b02-fdff-de94-39e0-00c7403b52f7','3a0f5b02-fe09-240c-97dc-a5fdd362999c')
);"""
    repository.exec_sql(sql)
    return True, f'数据源升级成功{0}条,失败{0}条;'  # 数据升级成功
