import json
import logging
import time
import requests
import jwt
from dmplib.hug import g
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)


def log(msg):
    logger.error(msg)


def uninstall_data_center(**kwargs):
    tenant_code = g.code
    mks_client_code = kwargs.get('mksClientCode')

    if not tenant_code:
        raise UserError(message="租户code参数不可以为空")

    if not mks_client_code:
        raise UserError(message="mksClientCode参数不可以为空")

    mks_info = {
        "MksUrl": "https://starship-open.mypaas.com.cn",
        "MksAppid": "3a00d648-99aa-12d4-7893-7817c7f71de6",
        "MksSecret": "R#X5%btSP#o!sg8Joq6LQjo&aocUe1nx"
    }
    url = f'{mks_info.get("MksUrl")}/v2/appcenter/sjzt/uninstall'
    log(f'容器云卸载数据服务中心地址：{url}')
    post_data = {
        'customerCode': tenant_code,
        'mksClientCode': mks_client_code,
        'mksProductCode': 'sjfwm'
    }
    post_json = json.dumps(post_data)
    log(f'请求容器云服务请求json参数：{post_json}')
    json_str = post_mks(url, post_json, mks_info.get('MksAppid'), mks_info.get('MksSecret'))
    log(f'容器云卸载数据服务中心返回：{json_str}')


def post_mks(url, post_json, app_id, secret):
    headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}
    if app_id:
        headers['Appid'] = app_id
    payload = {'exp': int(time.time()) + 3600, 'iss': 'appship-auth', 'appid': app_id}
    token = jwt.encode(payload, secret, algorithm="HS256")
    headers['Authorization'] = 'Bearer ' + str(token)
    response = requests.post(url, data=post_json, headers=headers)
    return response.json()
