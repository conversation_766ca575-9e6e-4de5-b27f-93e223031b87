#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
import time
from rundeck.client import Rundeck
from components.rundeck import get_rundeck_client
from dmplib.utils.errors import UserError
from dmplib import config
from dmplib.redis import RedisCache
from base.enums import HdUpgradeModuleList
from hd_upgrade.services.external_upgrade_service import update_upgrade_err_log, update_upgrade_notice_log
from hd_upgrade.api.hd_upgrade_api import HdUpgradeApi
from components.data_center_api import request_data_center
from data_source.enums.mysoft_new_erp_enums import MysoftNewERPAPI

# 需要检查HighData租户参数的升级模块
CHECK_HD_CODE_MODULE_LIST = [HdUpgradeModuleList.ALL.value, HdUpgradeModuleList.DASHBOARD.value,
                             HdUpgradeModuleList.M_REPORT.value, HdUpgradeModuleList.ROLE.value]


def upgrade_env_check(module, params=None):
    """
    数据升级前环境检测
    :return:
    """
    check_list = {
        'Rundeck': "_check_rundeck()",
        'Redis': "RedisCache().add('_test_connect','test',1)",
        'jkgj': '_upgrade_jkgj()',
        # # api接口更新校验 #—_todo： 后续需要完善
        # 'api': f"_check_api('{module}', {params})",
    }
    check_name_map = {'celery': 'celery异步服务', 'Rundeck': 'rundeck调度服务',
                      'Redis': 'redis缓存服务', 'jkgj': '接口管家及MDC'}
    check_result = []
    allow_upgrade = True

    update_upgrade_notice_log('【环境检测】开始，开始时间：%s' % time.strftime('%Y-%m-%d %H:%M:%S') + '，参数：' + json.dumps(params))
    # 开始检测
    for k, v in check_list.items():
        check_result.append(_common_check_function(k, v))

    # 记录日志
    for item in check_result:
        status = item.get('status')
        status_info = '正常'
        item_name = check_name_map.get(item.get('module'))
        if not status:
            allow_upgrade = False
            status_info = '异常'
            err_msg = item.get('msg')
            update_upgrade_err_log(f'{item_name}检测结果：{status_info}！错误信息：{err_msg}')
        else:
            update_upgrade_notice_log(f'{item_name}检测结果：{status_info}！')

    if not allow_upgrade:
        msg = '【环境检测】完成，检测结果存在异常！不能升级！'
        update_upgrade_err_log(f'{msg}结束时间：%s' % time.strftime('%Y-%m-%d %H:%M:%S'))
        # raise UserError(message=msg)

    update_upgrade_notice_log('【环境检测】完成，检测结果正常！结束时间：%s' % time.strftime('%Y-%m-%d %H:%M:%S'))
    return allow_upgrade


def _common_check_function(module, test_function):
    """
    执行检测函数
    :param module:
    :param test_function:
    :return:
    """
    try:
        eval(test_function)
        return {'status': True, 'module': module, 'msg': ''}
    except Exception as e:
        return {'status': False, 'module': module, 'msg': str(e)}


def _upgrade_jkgj():
    """
    接口管家环境检测
    """
    try:
        request_data_center(MysoftNewERPAPI.GetEnvironmentId.value)
    except Exception as e:
        msg = '接口管家或数据服务中心异常，msg:' + str(e)
        raise UserError(message=msg) from e


def _check_rundeck():
    rundeck_server = config.get('Rundeck.server')
    if rundeck_server is None:
        raise UserError(message='Rundeck服务配置缺失')

    rundeck = get_rundeck_client()
    return rundeck.system_info()


def format_time(time_delta: float):
    return round(time_delta, 3)


def _check_hd_api(module: str, params=None):
    """
    HighData的api接口校验
    :param module:
    :param params:
    :return:
    """
    if params is None:
        params = dict()
    # 指定模块需要检测HD接口有效性
    if module in CHECK_HD_CODE_MODULE_LIST:
        if "tenant_code" not in params or not params.get("tenant_code"):
            raise UserError(message="HighData租户代码不能为空，操作失败")

        tenant_code = params.get("tenant_code")
        # hd接口检测
        yzs_domain = params.get('yzs_domain', None)
        hd_upgrade_api = HdUpgradeApi(tenant_code, yzs_domain)
        hd_upgrade_api.ping()
    return True
