import time

from base import repository
import json
import logging
from dmplib.utils.strings import seq_id
from base.enums import DataSourceType, MysoftNewERPConfigType, MysoftNewERPDataBaseType, MysoftNewERPDataFromType, \
    FlowType, FlowNodeType, ApplicationType, DashboardPlatforms, MysoftShuXin15UpgradeModuleNameList
from shuxin15_upgrade.services.external_upgrade_service import \
    update_upgrade_err_log, update_upgrade_notice_log, update_upgrade_warning_log
from dmplib import config

logger = logging.getLogger(__name__)

all_dashboard_folder_id_name_map = {}
all_dashboard_folder_id_parent_map = {}
task_id = None


def check_chart(params, check_type, is_active_report=False):
    global all_dashboard_folder_id_name_map
    global all_dashboard_folder_id_parent_map
    global task_id

    task_id = params.get('task_id')
    limit = params.get('limit', 9999)
    force_check = params.get('force_check', 0)
    if check_type == 'before' and not force_check and is_active_report == False:  # 升级前校验 只能一次，否则就不进行
        shuxin15_upgrade_task = repository.get_list('shuxin15_upgrade_task',
                                                    conditions={'module_name': '升级前组件取数检测', 'status': '完成'})
        if shuxin15_upgrade_task:
            return True, f'已经存在的升级前任务,不能重复执行;'  # 数据升级成功
    shuxin15_upgrade_dashboard_check_task = repository.get_one('shuxin15_upgrade_dashboard_check_task',
                                                               conditions={'status': '0'})
    if shuxin15_upgrade_dashboard_check_task:
        return True, f'已经存在正在检查的任务,不能并行进行;'  # 数据升级成功
    if is_active_report:
        all_dashboard = repository.get_list('dashboard', {"type": ['FILE', 'CHILD_FILE'], "status": 1,
                                                          "application_type": [5], },
                                            limit=limit)
    else:
        all_dashboard = repository.get_list('dashboard', {"type": ['FILE', 'CHILD_FILE'], "status": 1,
                                                          "application_type": [0, 8], },
                                            limit=limit)
    all_dashboard_folder = repository.get_list('dashboard', conditions={"type": ['FOLDER']})

    all_dashboard_folder_id_name_map = {folder.get('id'): folder.get('name') for folder in all_dashboard_folder}
    all_dashboard_folder_id_parent_map = {folder.get('id'): folder.get('parent_id') for folder in all_dashboard_folder}

    update_upgrade_notice_log('开始启动检测报表是否正常的异步作业')
    for dashboard in all_dashboard:  # 批量启动异步作业 ，计算各个报表的高度
        start_flow_check_chart(check_type, dashboard)
        # 等待异步作业完成,先不调用
    update_upgrade_notice_log(f'启动{len(all_dashboard)}个检测报表的异步作业')
    # all_dashboard_id_list = [dashboard.get('id') for dashboard in all_dashboard]
    # wait_flow_check_chart(all_dashboard_id_list, check_type)
    return True, f'启动{len(all_dashboard)}个检测报表的异步作业'  # 数据升级成功


def restart_timeout_check_chart(params):
    new_task_id = params.get('upgrade_task_id')
    status = params.get('status', 3)
    flow_id = params.get('flow_id')

    timeout_check_charts = []
    if flow_id:
        timeout_check_charts = repository.get_list('shuxin15_upgrade_dashboard_check_task',
                                                   conditions={'flow_id': flow_id})
    else:
        if new_task_id:
            timeout_check_charts = repository.get_list('shuxin15_upgrade_dashboard_check_task',
                                                       conditions={'status': status, 'task_id': new_task_id})
        else:
            timeout_check_charts = repository.get_list('shuxin15_upgrade_dashboard_check_task',
                                                       conditions={'status': status})
    for timeout_check_chart in timeout_check_charts:
        restart_flow_check_chart(timeout_check_chart)
    return True, f'启动{len(timeout_check_charts)}个超时检测看板的异步作业'


def start_flow_check_chart(check_type, dashboard):  #
    from flow.models import FlowModel, FlowNodeModel
    from flow.services import flow_service
    dashboard_id = dashboard.get('id')
    dashboard_name = dashboard.get('name')
    # 新增flow
    url = config.get('Domain.dmp')
    url = url if url.endswith('/') else url + "/"
    open_url = f'{url}/dataview/preview/{dashboard_id}'
    application_type = dashboard.get('application_type')
    platform = dashboard.get('platform')
    dashboard_type = 'dashboard'
    if application_type in (ApplicationType.Dashboard.value, ApplicationType.LargeScreen.value) and platform in (
            DashboardPlatforms.PC.value, DashboardPlatforms.TV.value):  # 大中屏
        open_url = f'{url}dataview/share/{dashboard_id}'
    elif application_type == ApplicationType.Dashboard.value and platform == DashboardPlatforms.Mobile.value:  # xiao屏
        open_url = f'{url}dataview-mobile/view/{dashboard_id}'
        dashboard_type = 'mobile'
    elif application_type == ApplicationType.ActiveReport.value:  # ar
        dashboard_type = 'activereport'
        open_url = f'{url}api/report_center/go_app?application_type=5&redirect_uri=%2Fpreview%2Findex.html%23%2F%3FreportId%3D{dashboard_id}%26is_report_center%3D1'

    flow_id = seq_id()
    data = {
        'flow_id': flow_id,
        'task_id': task_id,
        'open_url': open_url,
        'dashboard_id': dashboard_id,
        'dashboard_name': dashboard_name,
        'dashboard_type': dashboard_type,
        'open_usercode': 'admin',
        'status': 0,
        'check_type': check_type
    }
    # 创建flow和instance
    flow = FlowModel(id=flow_id, name=dashboard_name, type=FlowType.CheckDashboard.value)
    node = FlowNodeModel(
        name=flow.name,
        type=FlowNodeType.CheckDashboard.value,
    )
    flow.nodes = [node]
    flow_service.add_flow(flow)

    repository.add_data('shuxin15_upgrade_dashboard_check_task', data)
    # 执行流程
    flow_service.run_flow(flow_id, queue_name=config.get('RabbitMQ.queue_name_flow_feeds', 'Flow-Feeds'))


def restart_flow_check_chart(data):
    from flow.services import flow_service

    flow_id = data['flow_id']
    data['status'] = 0
    data['error_msg'] = ""
    data['start_time'] = None
    data['finish_time'] = None
    repository.update('shuxin15_upgrade_dashboard_check_task', data, conditions={'flow_id': flow_id})

    # 执行流程
    flow_service.run_flow(flow_id, queue_name=config.get('RabbitMQ.queue_name_flow_feeds', 'Flow-Feeds'))


# def wait_flow_check_chart(all_dashboard_id_list, check_type):
# update_upgrade_notice_log('开始等待检测报表是否正常的异步作业')
# num = len(all_dashboard_id_list)
# sum_second = 0
# for dashboard_id in all_dashboard_id_list:
#     where = {'dashboard_id': dashboard_id, "status": 1, "check_type": check_type}
#     i = 0
#     while i < 36 * num:
#         sum_second += 5
#         shuxin15chart_height = repository.get_list('shuxin15_upgrade_dashboard_check_task', where)
#         if shuxin15chart_height:
#             update_upgrade_notice_log(f'完成{dashboard_id}检测报表是否正常的异步作业，总耗时{i * 5}s')
#             break
#         update_upgrade_notice_log(f'等待检测报表是否正常：报表id:{dashboard_id},等待次数：{i} 次')
#         time.sleep(5)
#         i += 1
# update_upgrade_notice_log(f'完成检测报表是否正常的异步作业，总耗时{sum_second}s')


def get_check_result_compare(before_module_name, after_module_name, is_all=True):
    shuxin15_upgrade_task_before = repository.get_one('shuxin15_upgrade_task',
                                                      conditions={
                                                          'module_name': before_module_name,
                                                          'status': '完成'
                                                      },
                                                      order_by='  created_on asc ')
    if not shuxin15_upgrade_task_before:
        return False, '不存在已经完成的升级前检查任务'
    # 异步任务 检查是否完成：
    before_task_id = shuxin15_upgrade_task_before.get('id')
    dashboard_check_task_before = repository.get_one('shuxin15_upgrade_dashboard_check_task',
                                                     {"task_id": before_task_id, 'status': 0})
    if dashboard_check_task_before:
        return False, '升级前检查任务正在执行，请等待检查任务完成'

    shuxin15_upgrade_task_after = repository.get_one('shuxin15_upgrade_task',
                                                     conditions={
                                                         'module_name': after_module_name,
                                                         'status': '完成'},
                                                     order_by='  created_on desc ')
    if not shuxin15_upgrade_task_after and is_all:
        shuxin15_upgrade_task_after = repository.get_one('shuxin15_upgrade_task',
                                                         conditions={
                                                             'module_name': MysoftShuXin15UpgradeModuleNameList.ALL.value,
                                                             'status': '完成'},
                                                         order_by='  created_on desc ')
    if not shuxin15_upgrade_task_after:
        return False, '不存在已经完成的升级后组件取数检测任务'
    after_task_id = shuxin15_upgrade_task_after.get('id')
    dashboard_check_task_after = repository.get_one('shuxin15_upgrade_dashboard_check_task',
                                                    {"task_id": after_task_id, 'status': 0})
    if dashboard_check_task_after:
        return False, '升级后组件取数检测任务正在执行，请等待检查任务完成'

    dashboard_check_task_list_before = repository.get_list('shuxin15_upgrade_dashboard_check_task',
                                                           {"task_id": before_task_id, 'check_type': 'before'})
    dashboard_list_id_map = {dashboard.get('dashboard_id'): dashboard for dashboard in dashboard_check_task_list_before}

    for dashboard in dashboard_list_id_map.values():
        dashboard['count'] = 0
        dashboard['success'] = 0
        dashboard['error'] = 0
    chart_before_list = repository.get_list('shuxin15_upgrade_dashboard_chart_check_task',
                                            {"task_id": before_task_id, 'check_type': 'before'})

    chart_after_list = repository.get_list('shuxin15_upgrade_dashboard_chart_check_task',
                                           {"task_id": after_task_id, 'check_type': 'after'})
    dashboard_fields = ['id', 'name', 'type', 'parent_id', 'level_code']
    dashboard_all_list = repository.get_list('dashboard', {}, fields=dashboard_fields)

    dashboard_id_map = {dashboard.get('id'): dashboard for dashboard in dashboard_all_list}

    chart_after_id_map = {chart.get('chart_id'): chart for chart in chart_after_list}
    for chart_before in chart_before_list:
        dashboard_id = chart_before.get('dashboard_id')
        chart_after = chart_after_id_map.get(chart_before.get('chart_id'))
        # 前置存在该组件后置不存在该组件 判断为正常看板
        if not chart_after:
            dashboard_list_id_map[dashboard_id]['success'] += 1
            dashboard_list_id_map[dashboard_id]['count'] += 1
            continue
        is_success = chart_after.get('is_success')
        if (is_success != 1 and is_success != chart_before.get('is_success')):
            dashboard_list_id_map[dashboard_id]['error'] += 1
            dashboard_list_id_map[dashboard_id]['count'] += 1
            continue
        # 后置巡检该看板都正常
        dashboard_list_id_map[dashboard_id]['success'] += 1
        dashboard_list_id_map[dashboard_id]['count'] += 1
    count = len(dashboard_list_id_map)
    success = 0
    error_dashboard_list = []
    for dashboard in dashboard_list_id_map.values():
        if not dashboard:
            continue
        # 判断后置组件
        if dashboard.get('error') == 0:
            dashboard_id = dashboard.get('dashboard_id')
            chart_afters = [obj for obj in chart_after_list if obj['dashboard_id'] == dashboard_id]
            for chart_after in chart_afters:
                if chart_after.get('is_success') != 1:
                    dashboard['error'] += 1
                    dashboard['count'] += 1
        if dashboard.get('error') == 0:
            success += 1
        else:
            dashboard_map = dashboard_id_map.get(dashboard.get('dashboard_id'))
            if dashboard_map:
                error_dashboard = {'dashboard_id': dashboard_map.get('id'),
                                   'dashboard_name': dashboard_map.get('name'),
                                   'error': dashboard.get('error'),
                                   'open_url': dashboard.get('open_url')
                                   }
                if dashboard_map.get('type') == "FILE" or dashboard_map.get('type') == "CHILD_FILE":
                    parent_dashboard = dashboard_id_map.get(dashboard_map.get('parent_id'))
                    if parent_dashboard:
                        error_dashboard['parent_id'] = parent_dashboard.get('id')
                        error_dashboard['parent_name'] = parent_dashboard.get('name')
                error_dashboard_list.append(error_dashboard)
    error = count - success
    return True, f'巡检总数：{count}，成功：{success}，失败：{error}', error_dashboard_list


def get_check_result_info_before(module_name):
    shuxin15_upgrade_task_before = repository.get_one('shuxin15_upgrade_task',
                                                      conditions={'module_name': module_name,
                                                                  'status': '完成'},
                                                      order_by='  created_on desc ')
    if not shuxin15_upgrade_task_before:
        return False, '不存在已经完成的升级前检查任务', []
    # 异步任务 检查是否完成：
    before_task_id = shuxin15_upgrade_task_before.get('id')
    dashboard_check_task_before = repository.get_one('shuxin15_upgrade_dashboard_check_task',
                                                     {"task_id": before_task_id, 'status': 0})
    if dashboard_check_task_before:
        return False, '升级前检查任务正在执行，请等待检查任务完成', []
    dashboard_check_task_list_before = repository.get_list('shuxin15_upgrade_dashboard_check_task',
                                                           {"task_id": before_task_id, 'check_type': 'before'})
    dashboard_list_id_map = {dashboard.get('dashboard_id'): dashboard for dashboard in dashboard_check_task_list_before}

    for dashboard in dashboard_list_id_map.values():
        dashboard['count'] = 0
        dashboard['success'] = 0
        dashboard['error'] = 0
    chart_before_list = repository.get_list('shuxin15_upgrade_dashboard_chart_check_task',
                                            {"task_id": before_task_id, 'check_type': 'before'})
    for chart in chart_before_list:
        dashboard_id = chart.get('dashboard_id')
        if chart.get('is_success') and chart.get('is_success') == 1:
            dashboard_list_id_map[dashboard_id]['success'] += 1
        else:
            dashboard_list_id_map[dashboard_id]['error'] += 1
        dashboard_list_id_map[dashboard_id]['count'] += 1
    count = len(dashboard_list_id_map)
    success = 0
    for dashboard in dashboard_list_id_map.values():
        if dashboard.get('error') == 0:
            success += 1
    error = count - success
    return True, f'升级前组件取数检测完成：总报表数：{count}，正常报表数：{success}，异常报表数：{error}', chart_before_list


def get_check_result_info_after(module_name):
    shuxin15_upgrade_task_after = repository.get_one('shuxin15_upgrade_task',
                                                     conditions={'module_name': module_name, 'status': '完成'},
                                                     order_by='  created_on desc ')
    if not shuxin15_upgrade_task_after:
        shuxin15_upgrade_task_all = repository.get_one('shuxin15_upgrade_task',
                                                       conditions={'module_name': '全量升级',
                                                                   'status': '完成'},
                                                       order_by='  created_on desc ')
        if not shuxin15_upgrade_task_all:
            return False, '不存在已经完成的升级后组件取数检测任务', []
        else:
            after_task_id = shuxin15_upgrade_task_all.get('id')
    else:
        after_task_id = shuxin15_upgrade_task_after.get('id')
    dashboard_check_task_after = repository.get_one('shuxin15_upgrade_dashboard_check_task',
                                                    {"task_id": after_task_id, 'status': 0})
    if dashboard_check_task_after:
        return False, '升级后组件取数检测任务正在执行，请等待检查任务完成', []

    dashboard_check_task_list_after = repository.get_list('shuxin15_upgrade_dashboard_check_task',
                                                          {"task_id": after_task_id, 'check_type': 'after'})
    dashboard_list_id_map = {dashboard.get('dashboard_id'): dashboard for dashboard in dashboard_check_task_list_after}

    for dashboard in dashboard_list_id_map.values():
        dashboard['count'] = 0
        dashboard['success'] = 0
        dashboard['error'] = 0
    chart_after_list = repository.get_list('shuxin15_upgrade_dashboard_chart_check_task',
                                           {"task_id": after_task_id, 'check_type': 'after'})
    for chart in chart_after_list:
        dashboard_id = chart.get('dashboard_id')
        if chart.get('is_success') and chart.get('is_success') == 1:
            dashboard_list_id_map[dashboard_id]['success'] += 1
        else:
            dashboard_list_id_map[dashboard_id]['error'] += 1
        dashboard_list_id_map[dashboard_id]['count'] += 1
    count = len(dashboard_list_id_map)
    success = 0
    for dashboard in dashboard_list_id_map.values():
        if dashboard.get('error') == 0:
            success += 1
    error = count - success
    return True, f'升级后组件取数检测完成：总报表数：{count}，正常报表数：{success}，异常报表数：{error}', chart_after_list
