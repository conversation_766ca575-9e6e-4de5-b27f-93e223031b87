#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging

import time
import traceback
from datetime import datetime

import app_celery
from base import repository
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.redis import RedisCache
from dmplib.constants import ADMIN_ROLE_ID
from base.enums import MysoftShuXin15UpgradeModuleNameList
from shuxin15_upgrade import UPGRADE_STATUS_CREATED, UPGRADE_STATUS_RUNNING, UPGRADE_STATUS_FAILURE, \
    UPGRADE_STATUS_SUCCESS
from shuxin15_upgrade.repositories import upgrade_repository
from shuxin15_upgrade.services.chart_check_service import get_check_result_compare, get_check_result_info_before, \
    get_check_result_info_after
from shuxin15_upgrade.services.dataset_service import dataset_upgrade
from shuxin15_upgrade.services.keyword_service import keyword_upgrade
from shuxin15_upgrade.services.upgrade_application_function_service import upgrade_application_function, \
    delete_history_report_and_menu, get_empty_folder_id_list, get_old_report_data, log_info, log_error
from shuxin15_upgrade.services.datasource_service import datasource_upgrade
from shuxin15_upgrade.services.dashboard_service import dashboard_upgrade, delete_dashboard_sys_tmp_folder
from shuxin15_upgrade.services.role_folder_service import role_folder_sql_exec
from shuxin15_upgrade.services.upgrade_env_service import upgrade_env_check
from shuxin15_upgrade.services.upgrade_task_to_cache_service import upgrade_task_id_get_cache, \
    upgrade_task_id_write_cache, upgrade_task_id_del_cache, UPGRADE_EXEC_MAX_TIME
from shuxin15_upgrade.services.external_upgrade_service import update_upgrade_err_log, update_upgrade_notice_log, \
    update_upgrade_err_log_by_id

logger = logging.getLogger(__name__)

date_format = '%Y-%m-%d %H:%M:%S'


def create_sync_upgrade_task(module: str = ''):
    """
    同步执行创建一个任务
    :param module:
    :return:
    """
    return create_upgrade_task(module, status=UPGRADE_STATUS_SUCCESS)


def create_upgrade_task(module: str = '', status: str = ''):
    """
    创建升级任务
    :param module:
    :param status:
    :return:
    """
    if module == '':
        module = MysoftShuXin15UpgradeModuleNameList.ALL.value
    if status == '':
        status = UPGRADE_STATUS_CREATED
    upgrade_task_id = seq_id()
    s = "模块【" + module + "】任务id：" + upgrade_task_id + '\n'
    upgrade_repository.insert_upgrade_task(
        {
            'id': upgrade_task_id,
            'module': module,
            'module_name': module,
            'status': status,
            'log_data': '【手动同步执行】\nNotice: 【shuxin15数据升级任务开始】开始时间：%s\n %s' % (
                time.strftime(date_format), s),
            'completed_on': time.strftime(date_format)
        }
    )
    return upgrade_task_id


def trigger_upgrade(module: str, params=None):
    """
    shuxin15ata数据升级入口
    """
    _upgrade_check(module, params)

    module = module.lower()
    ok, upgrade_task_id = _create_upgrade_task(module)
    s = "模块【" + module + "】\n任务id：" + upgrade_task_id + " 参数：" + json.dumps(params)
    update_upgrade_notice_log(s)

    exec_bool = False
    if ok:
        exec_bool = True
        try:
            args = {'project_code': g.code, 'upgrade_task_id': upgrade_task_id, 'params': params, 'userid': 'admin'}
            app_celery.shuxin15_upgrade_data.apply_async(kwargs=args, queue='upgrade', time_limit=UPGRADE_EXEC_MAX_TIME)
        except Exception as e:
            logger.exception(e)
            update_upgrade_err_log(str(e))

    return exec_bool, upgrade_task_id


def _upgrade_check(module: str, params=None, is_async=True):
    """
    数据升级环境检测
    :param module:
    :param is_async: 是否异步，True：异步，False：同步
    :return:
    """
    if params is None:
        params = dict()

    if not module:
        raise UserError(400, message='无效的参数')

    if module not in [e.value for e in MysoftShuXin15UpgradeModuleNameList.__members__.values()]:
        raise UserError(message="错误的模块标识")

        # 检测是否已存在正在执行的任务
    exists_task = check_exists_upgrade_task(module)
    if exists_task:
        raise UserError(message="已有升级任务正在执行，操作失败")

    # reids检测
    try:
        RedisCache().add('_test_connect', 'test', 1)
    except Exception as e:
        raise UserError(message=f"Redis服务异常，Err:{str(e)}") from e

    # 非同步执行，检测异步服务是否正常
    # if is_async:
    #    try:
    #        check_celery()
    #    except Exception as e:
    #        raise UserError(message=str(e)) from e
    return True


def sync_upgrade(module: str, params=None):
    """
    shuxin15ata单个指定模块
    同步数据升级入口
    """

    _upgrade_check(module, params, False)
    ok, upgrade_task_id = _create_upgrade_task(module)
    s = "【手动同步执行】 模块【" + module + "】任务id：" + upgrade_task_id + " 参数：" + json.dumps(params)
    update_upgrade_notice_log(s)

    exec_bool = False
    if ok:
        exec_bool = True
        try:
            exec_upgrade_data_task(upgrade_task_id, params)
        except Exception as e:
            logger.exception(e)
            update_upgrade_err_log(str(e))

    return exec_bool, '执行完成', upgrade_task_id


def _create_upgrade_task(module: str = ''):
    """
    同步执行创建一个任务
    :param module:
    :return:
    """
    exists_task_id = upgrade_task_id_get_cache()
    if exists_task_id:
        raise UserError(message=f'升级异常，存在异常的任务id({exists_task_id})')

    upgrade_task_id = seq_id()
    ok = upgrade_repository.insert_upgrade_task(
        {
            'id': upgrade_task_id,
            'module': module,
            'module_name': module,
            'log_data': 'Notice: 【shuxin15数据升级任务开始】开始时间：%s\n' % time.strftime(date_format)
        }
    )
    # 任务id写入redis
    upgrade_task_id_write_cache(upgrade_task_id)
    return ok, upgrade_task_id


def check_celery():
    """
    celery异步服务检测
    :return:
    """
    # celery写入缓存需要加上租户code前缀
    key = 'test_celery_key_upgrade'
    celery_key = f'{g.code}:{key}'
    app_celery.test_celery.apply_async(kwargs={"task_tag": celery_key}, queue='upgrade')
    time.sleep(1)

    redis_cache = RedisCache()
    s = redis_cache.get(key)
    if s:
        msg = redis_cache.get(key).decode()
        redis_cache.delete(key)
        return msg
    raise UserError(message='异步服务没有运行，不能执行')


def check_user_permission():
    """
    检测当前用户是否有权限执行数据升级
    """
    from user.services.user_service import get_cur_role_id
    has_permission = False
    role_ids = get_cur_role_id()
    # 如果是管理员,不做处理
    if role_ids and ADMIN_ROLE_ID in role_ids:
        has_permission = True
    return has_permission


def check_exists_upgrade_task(module):
    """
    检测当前是否有任务正在执行
    :param module: str 模块标识
    """
    module_list = [module]
    if module != MysoftShuXin15UpgradeModuleNameList.ALL.value:
        module_list.append(MysoftShuXin15UpgradeModuleNameList.ALL.value)
    where = {"module": module_list, "status": [UPGRADE_STATUS_CREATED, UPGRADE_STATUS_RUNNING]}
    return upgrade_repository.get_upgrade_task_by_where(where)


def exec_upgrade_data_task(upgrade_task_id, params=None):
    """
     shuxin15ata数据升级task, 提供给celery
    :param upgrade_task_id:
    :param params:
    :return:
    """
    # 执行升级业务数据
    try:
        start_time = time.time()
        if params is None:
            params = dict()

        # 升级任务记录是否存在
        upgrade_task_data = upgrade_repository.get_upgrade_task_detail(upgrade_task_id)
        if not upgrade_task_data:
            logger.error("升级任务数据不存在，upgrade_task_id：" + upgrade_task_id)
            return upgrade_task_id

        if not upgrade_repository.update_upgrade_task(upgrade_task_id, {'status': UPGRADE_STATUS_RUNNING}):
            upgrade_repository.update_upgrade_task(
                upgrade_task_id, {'status': UPGRADE_STATUS_FAILURE, 'completed_on': time.strftime(date_format)}
            )
            s = "更新状态失败"
            logger.error(s)
            update_upgrade_err_log(s)
            return upgrade_task_id

        # 全量升级，依次处理每个模块的内容升级
        module = upgrade_task_data.get('module')
        # 升级环境检测
        upgrade_env_check(module, params)

        if module == MysoftShuXin15UpgradeModuleNameList.ALL.value:
            for e in MysoftShuXin15UpgradeModuleNameList.__members__.values():
                # 跳过"all"标识
                if e.value == MysoftShuXin15UpgradeModuleNameList.ALL.value:
                    continue
                elif e.value == MysoftShuXin15UpgradeModuleNameList.check_chart_before.value:  # '升级前跳过不执行'
                    continue
                elif e.value == MysoftShuXin15UpgradeModuleNameList.report_check_chart_before.value:  # '升级前报表跳过不执行'
                    continue
                elif e.value == MysoftShuXin15UpgradeModuleNameList.report_check_chart_after.value:  # '升级后报表跳过不执行'
                    continue
                elif e.value == MysoftShuXin15UpgradeModuleNameList.restart_check_chart.value:  # '重跑超时的取数检测'
                    continue
                exec_module_upgrade(e.value, upgrade_task_id, params)
        else:
            exec_module_upgrade(module, upgrade_task_id, params)

        # 执行完成
        upgrade_repository.update_upgrade_task(
            upgrade_task_id, {'status': UPGRADE_STATUS_SUCCESS, 'completed_on': time.strftime(date_format)}
        )
        update_upgrade_notice_log('【数据升级执行完成】完成时间：%s 总耗时：%s s' %
                                  (time.strftime(date_format), format_time(time.time() - start_time)))
        # 清除正在执行的  task id
        upgrade_task_id_del_cache()
    except Exception as e:
        logger.error(f"数据升级exception: {traceback.print_exc()}")
        upgrade_repository.update_upgrade_task(
            upgrade_task_id, {'status': UPGRADE_STATUS_FAILURE, 'completed_on': time.strftime(date_format)}
        )
        s = '【数据升级执行异常】错误信息:{}'.format(str(e))
        update_upgrade_err_log(s)
        # 清除正在执行的  task id
        upgrade_task_id_del_cache()
    return upgrade_task_id


def exec_module_upgrade(module: str, upgrade_task_id: str, params=None):
    """
    每个模块的升级逻辑
    module str: 模块标识
    upgrade_task_id str: 升级任务id
    params dict: 某个模块升级指定参数
    """
    if not isinstance(params, dict):
        params = {}

    start_time = time.time()
    # 执行升级业务数据
    try:
        update_upgrade_notice_log('【' + module + '】模块 数据升级开始，开始时间：%s'
                                  % time.strftime(date_format))
        params['task_id'] = upgrade_task_id
        # 处理每个模块的数据升级，同时传参 upgrade_task_id 和 params
        info = _module_data_upgrade(module, params)
        # 记录返回信息
        update_upgrade_notice_log('模块返回信息：' + json.dumps(info, ensure_ascii=False))
        update_upgrade_notice_log('【' + module + '】模块 数据升级完成，完成时间：%s 耗时：%s s'
                                  % (time.strftime(date_format), format_time(time.time() - start_time)))
        return
    except Exception as e:
        # 异常不修改状态为异常
        # upgrade_repository.update_upgrade_task(
        #     upgrade_task_id, {'status': UPGRADE_STATUS_FAILURE, 'completed_on': time.strftime(date_format)}
        # )
        s = '【' + module + '】模块 数据升级异常异常,错误信息:{}'.format(str(e))
        logger.exception(s)
        update_upgrade_err_log(s)
        return


def _module_data_upgrade(module: str, params=None):
    from shuxin15_upgrade.services.chart_check_service import check_chart, restart_timeout_check_chart

    module = module.lower()
    if module == MysoftShuXin15UpgradeModuleNameList.check_chart_before.value:  # 升级前组件取数检测
        return check_chart(params, check_type='before')
    elif module == MysoftShuXin15UpgradeModuleNameList.report_check_chart_before.value:  # 升级前报表取数检测
        return check_chart(params, check_type='before', is_active_report=True)
    elif module == MysoftShuXin15UpgradeModuleNameList.restart_check_chart.value:  # 重跑超时的取数检测
        return restart_timeout_check_chart(params)
    elif module == MysoftShuXin15UpgradeModuleNameList.datasource.value:  # 数据源
        return datasource_upgrade(params)
    elif module == MysoftShuXin15UpgradeModuleNameList.import_dashboard.value:  # 仪表板升级
        return dashboard_upgrade(params)
    elif module == MysoftShuXin15UpgradeModuleNameList.portal_upgrade.value:  # 门户替换
        return upgrade_application_function(params)
    elif module == MysoftShuXin15UpgradeModuleNameList.dataset_upgrade.value:  # 数据集
        return dataset_upgrade(params)
    elif module == MysoftShuXin15UpgradeModuleNameList.keyword.value:  # 关键字
        return keyword_upgrade(params)
    elif module == MysoftShuXin15UpgradeModuleNameList.check_chart_after.value:  # 升级后组件取数检测
        return check_chart(params, check_type='after')
    elif module == MysoftShuXin15UpgradeModuleNameList.report_check_chart_after.value:  # 升级后报表取数检测
        return check_chart(params, check_type='after', is_active_report=True)
    elif module == MysoftShuXin15UpgradeModuleNameList.role_folder.value:  # 目录权限补充
        return role_folder_sql_exec()


def format_time(time_delta: float):
    return round(time_delta, 3)


def get_module_list():
    data = dict()
    module_list = list()
    for k in MysoftShuXin15UpgradeModuleNameList.__members__:
        module = {
            "module": k,
            "module_name": MysoftShuXin15UpgradeModuleNameList[k].value
        }
        module_list.append(module)
    data['module_list'] = module_list
    # 获取最近一次的升级记录
    upgrade_record = upgrade_repository.get_last_upgrade_task()
    data['upgrade_record'] = upgrade_record if upgrade_record else dict()
    return data


def log_view(upgrade_task_id):
    if not upgrade_task_id:
        raise UserError(message='任务id不能为空')

    try:
        log = upgrade_repository.get_upgrade_task_log_data(upgrade_task_id)
        if not log:
            raise UserError(message='数据升级任务不存在，请重试')
        data = {
            'log': log.get('log_data'),
            'status': log.get('status'),
            'is_success': True if log.get('status') == UPGRADE_STATUS_SUCCESS else False
        }
        return data
    except ValueError:
        raise UserError(message='参数格式异常')


def last_log_view():
    try:
        log = upgrade_repository.get_last_upgrade_task_log_data()
        if not log:
            raise UserError(message='数据升级任务不存在，请先升级再查看日志')
        return log
    except Exception as e:
        raise UserError(message=f'获取升级任务异常：{e}')


def _check_task_status(upgrade_task_id):
    """
    检测任务的执行状态
    1、如果非成功或失败，那么前端会一直请求
    2、如果异步任务执行超过3小时，且状态非成功或失败，将任务标记为失败并给出提示信息
    :param upgrade_task_id:
    :return:
    """
    exec_time = UPGRADE_EXEC_MAX_TIME
    is_end = True
    # 检测状态，如果不是成功或失败，前端继续请求
    rs = upgrade_repository. \
        get_upgrade_task_detail(upgrade_task_id, field_list=['created_on', 'status'])
    if 'status' in rs and rs.get('status') not in [UPGRADE_STATUS_SUCCESS, UPGRADE_STATUS_FAILURE]:
        is_end = False
        create_time = rs.get('created_on')
        now_time = time.strftime(date_format)
        now_time_obj = datetime.strptime(now_time, date_format)
        diff = now_time_obj - create_time
        if diff.seconds > exec_time:
            update_upgrade_err_log_by_id(upgrade_task_id, '升级异常，异步服务执行超时！执行时间：%s s' % str(diff.seconds))
            upgrade_repository.update_upgrade_task(
                upgrade_task_id, {'status': UPGRADE_STATUS_FAILURE, 'completed_on': time.strftime(date_format)}
            )
            is_end = True

    return is_end


def fix_task():
    """
    清除已存在的异常任务
    :return:
    """
    row_count = upgrade_repository.update_upgrade_info(
        {'status': UPGRADE_STATUS_RUNNING},
        {'status': UPGRADE_STATUS_FAILURE}
    )
    exists_task_id = upgrade_task_id_get_cache()
    s = ''
    if exists_task_id:
        s = f'，清除异常任务id：{exists_task_id}'
        upgrade_task_id_del_cache()
    return True, f'执行完成！清除已存在的异常任务数：{row_count}{s}'


def get_check_result(type):
    if type == 'before':
        return get_check_result_info_before(MysoftShuXin15UpgradeModuleNameList.check_chart_before.value)
    if type == 'after':
        return get_check_result_info_after(MysoftShuXin15UpgradeModuleNameList.check_chart_after.value)
    else:
        return get_check_result_compare(MysoftShuXin15UpgradeModuleNameList.check_chart_before.value,
                                        MysoftShuXin15UpgradeModuleNameList.check_chart_after.value)


def get_report_check_result(type):
    if type == 'before':
        return get_check_result_info_before(MysoftShuXin15UpgradeModuleNameList.report_check_chart_before.value)
    if type == 'after':
        return get_check_result_info_after(MysoftShuXin15UpgradeModuleNameList.report_check_chart_after.value)
    else:
        return get_check_result_compare(MysoftShuXin15UpgradeModuleNameList.report_check_chart_before.value,
                                        MysoftShuXin15UpgradeModuleNameList.report_check_chart_after.value,
                                        is_all=False)


def delete_report_and_menu():
    # 1.删除门户中废弃的看板与报表 2.删除废弃的看板与报表
    delete_history_report_and_menu()
    # 删除系统级的看板目录 删除未被使用的系统级数据集 删除系统级的数据集目录
    folders = delete_dashboard_sys_tmp_folder()
    log_info(f'删除看板中系统级空文件夹：{json.dumps(folders)}')
    from dataset.services import dataset_service
    datasets = dataset_service.delete_unused_system_dataset()
    log_info(f'删除数据集：{json.dumps(datasets)}')
    # 删除系统分发数据集文件夹下文件夹数据集下的空目录
    folders = dataset_service.delete_dataset_tmp_folder()
    log_info(f'删除数据集文件夹：{json.dumps(folders)}')
    # 报表删除空文件夹
    drop_empty_folder()


def drop_empty_folder():
    with repository.get_db() as db:
        db.begin_transaction()
        empty_folders = get_empty_folder_id_list(db)
        while len(empty_folders) > 0:
            logger.info(f'开始删除空报表文件夹：{empty_folders}')
            db.exec_sql("delete from dashboard where id in %(id_list)s", {"id_list": empty_folders})
            db.exec_sql("delete from myrptgroup where MyRptGroupId in %(id_list)s", {"id_list": empty_folders})
            empty_folders = get_empty_folder_id_list(db)
        db.commit()


def get_report10_Publish_Status():
    try:
        report10_list = get_old_report_data()
    except Exception as e:
        raise UserError(message=f'获取报表列表错误:{e}')
    with repository.get_db() as db:
        function_report10_url = db.query('''SELECT id,name,url,application_id from `function` where url!='' and report_type='5'
                                            union
                                            SELECT id,name,url,application_id from `release_function` where url!='' and report_type='5' ''')
    error_report10_list = []
    for report10_url in function_report10_url:
        for obj in report10_list:
            if obj.get('id') == report10_url.get('url'):
                # 发布状态
                if obj.get('isPublish') == False:
                    report10_url['error_msg'] = '报表未发布'
                    report10_url['report10'] = obj
                    error_report10_list.append(report10_url)
                break
        else:
            report10_url['error_msg'] = '报表不存在'
            error_report10_list.append(report10_url)
    return error_report10_list
