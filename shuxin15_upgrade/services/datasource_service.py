from base import repository
import json
import logging

from dmplib.hug import g
from dmplib.utils.strings import seq_id
from data_source.enums.mysoft_new_erp_enums import MysoftNewERPAPI
from base.enums import DataSourceType, MysoftNewERPConfigType, MysoftNewERPDataBaseType, MysoftNewERPDataFromType
from shuxin15_upgrade.services.external_upgrade_service import \
    update_upgrade_err_log, update_upgrade_notice_log, update_upgrade_warning_log

from components.storage_setting import get_project_setting, save_project_setting

logger = logging.getLogger(__name__)

erp_site = {}


def datasource_upgrade(params):
    update_upgrade_notice_log('开始升级为数见1.5标识')
    update_is_data_cloud_1_5_enabled()
    update_upgrade_notice_log('完成升级为数见1.5标识')

    update_upgrade_notice_log('升级开启复杂报表标识')
    add_report_center_func()
    update_upgrade_notice_log('完成开启复杂报表标识')

    update_upgrade_notice_log('开始将数据集缓存storage_type修改为cloud云端存储')
    update_storage_type()  # 包含清空项目缓存
    update_upgrade_notice_log('完成数据集缓存storage_type修改为cloud云端存储')

    update_upgrade_notice_log('开始替换数据源')
    update_mysoftnewerp_shuxin()
    update_upgrade_notice_log('完成替换数据源')

    return True, f'数据源升级成功;'  # 数据升级成功


def update_mysoftnewerp_shuxin():
    # 更新MysoftNewERP
    from data_source.services import data_source_service
    data_source_list = data_source_service.get_data_source_list_by_type(type="MysoftNewERP")  # 主数据
    if data_source_list:
        for data_source in data_source_list:
            # 更新更新erp为数芯取数
            data_source.conn_str.IsShuXin = 1
            data_source.conn_str.ProjectCode = 'product_public_space'
            update_data_source(data_source)
            update_upgrade_notice_log(f'完成{data_source.name}({data_source.code})替换')


def update_data_source(model):
    """
        修改数据源
        :param data_source.models.DataSourceModel model:
        :return bool:
        """
    model.validate()
    model.conn_str_to_model()

    fields = ['name', 'code', 'description', 'conn_str', 'icon', 'inspect_api', 'third_party_id']
    if model.type == DataSourceType.MysoftShuXin15.value:
        # 清空数芯数据源缓存
        from dmplib.redis import RedisCache
        RedisCache().delete(key="MysoftShuXin15:data_source")

    from data_source.models import ConnStrModel
    if isinstance(model.conn_str, ConnStrModel):
        model.conn_str.encrypt()
        model.conn_str = json.dumps(model.conn_str.get_dict())
    result = repository.update_model('data_source', model, {'id': model.id}, fields)
    # 设置缓存
    from data_source.cache import data_source_meta_cache
    data_source_meta_cache.del_data_source_cache(model.id)
    data_source_meta_cache.get_data_source_cache(model.id)
    from base import event
    event.trigger(event.UpdateDataSourceEvent, model)
    return result, model.name


def update_is_data_cloud_1_5_enabled():
    dataset_is_combine_entrance = True if get_project_setting("is_data_cloud_1_5_enabled", "0") in (1, '1') else False
    if not dataset_is_combine_entrance:
        save_project_setting(g.code, 'is_data_cloud_1_5_enabled', 1, 'admin')
    return


def update_storage_type():
    project_code = getattr(g, "code")
    from dmplib.db.mysql_wrapper import get_db
    with get_db() as db:
        # 修改字段
        sql = "update `project` set `storage_type`=%(value)s where `code`=%(code)s"
        result = db.exec_sql(sql, {'value': 'cloud', 'code': project_code})
    # 清空缓存
    clear_project_cache(project_code)


def clear_project_cache(project_code):
    from dmplib.redis import RedisCache
    conn = RedisCache()
    cache_key = project_code + ':Project:DB:Config:' + project_code
    conn.delete(cache_key)
    conn.delete('Project:DB:Config:' + project_code)
    conn.delete('Project:Detail:Info:' + project_code)
    conn.delete('dmp:dmp_env_sign:' + project_code)
    conn.delete('dmp:storage:' + project_code)
    conn.delete(project_code + ':Project:Detail:Info:' + project_code)
    conn.delete(project_code + ':dmp:dmp_env_sign:' + project_code)
    conn.delete(project_code + ':dmp:storage:' + project_code)


def add_report_center_func():
    data = repository.get_one('project_value_added_func', {'project_code': g.code, 'func_code': 'report_center'},
                              from_config_db=True)
    if data:
        return
    func_dict = {'id': seq_id(), 'project_code': g.code, 'func_code': 'report_center'}
    return repository.add_data('project_value_added_func', func_dict, from_config_db=True)

