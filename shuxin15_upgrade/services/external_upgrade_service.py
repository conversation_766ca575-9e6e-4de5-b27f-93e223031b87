#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
from psycopg2 import Date
from dmplib.utils.strings import seq_id
from base.models import BaseModel
from shuxin15_upgrade.repositories import upgrade_repository

from shuxin15_upgrade.repositories.upgrade_repository import update_upgrade_task_log
from shuxin15_upgrade.services.upgrade_task_to_cache_service import upgrade_task_id_get_cache


def update_upgrade_err_log(msg: str):
    return update_upgrade_log(msg, 0)


def update_upgrade_notice_log(msg: str):
    return update_upgrade_log(msg, 1)


def update_upgrade_warning_log(msg: str):
    return update_upgrade_log(msg, 2)


def update_upgrade_log(msg: str, level_type):
    upgrade_task_id = upgrade_task_id_get_cache()
    if not upgrade_task_id:
        return upgrade_repository.insert_upgrade_task(
        {
            'id': seq_id(),
            'module': '',
            'module_name':'',
            'status': '运行中',
            'log_data': msg.replace("'",'').replace("'",''),
            'completed_on': time.strftime('%Y-%m-%d %H:%M:%S')
        }) #调试阶段日志记录

    msg = get_upgrade_level_log(msg, level_type)
    return update_upgrade_task_log(upgrade_task_id=upgrade_task_id, msg=msg)


def get_upgrade_level_log(msg: str, level_type):
    level_info = {1: 'Notice', 2: 'Warning', 0: 'Error'}
    if level_type in level_info and level_info.get(level_type):
        level = level_info.get(level_type)
        msg = f"{level}: {msg}"
    return msg


def update_upgrade_err_log_by_id(upgrade_task_id, msg: str):
    msg = get_upgrade_level_log(msg, 0)
    return update_upgrade_task_log(upgrade_task_id=upgrade_task_id, msg=msg)

