import logging
from datetime import datetime

from base import repository
from dmplib.hug import g
from dmplib.utils.model import BaseModel
from level_sequence.services import level_sequence_service
from shuxin15_upgrade.services.external_upgrade_service import update_upgrade_notice_log, update_upgrade_err_log

logger = logging.getLogger(__name__)


def delete_history_menu(db, report_id):
    tables = ['`function`', '`release_function`']
    delete_function_id_list = []
    for table in tables:
        exist_function_list = db.query(f"select * from {table} where `url`=%(url)s", {
            "url": report_id
        })
        for exist_function in exist_function_list:
            parent_id = exist_function.get("parent_id")
            delete_id = exist_function.get('id')
            db.exec_sql(f"delete from {table} where id=%(id)s", {
                "id": delete_id
            })
            if delete_id not in delete_function_id_list:
                delete_function_id_list.append(delete_id)
            log_info(f'{table}菜单已删除,报表id:{report_id},菜单id:{delete_id}')
            deep = 0
            max_deep = 2
            while deep < max_deep and parent_id:
                # 存在父级，且父级下没有其他子级菜单，则删除父级
                exist_parent_function = db.query_one(f"select * from {table} where `id`=%(id)s", {
                    'id': parent_id
                })
                if exist_parent_function and not db.query_scalar(f"select 1 from {table} where parent_id=%(parent_id)s",
                                                                 {
                                                                     "parent_id": exist_parent_function.get('id')
                                                                 }):
                    delete_id = exist_parent_function.get('id')
                    db.exec_sql(f"delete from {table} where id=%(id)s", {
                        "id": delete_id
                    })
                    if delete_id not in delete_function_id_list:
                        delete_function_id_list.append(delete_id)
                    log_info(f'{table}菜单存在父级，且父级下没有其他菜单，已删除父级菜单id:{delete_id}')
                    parent_id = exist_parent_function.get("parent_id")
                    deep = deep + 1
                else:
                    break
    for delete_id in delete_function_id_list:
        log_info(f'菜单{delete_id}开始清理额外信息')
        db.exec_sql(f"delete from application_guide_img where function_id=%(function_id)s", {'function_id': delete_id})
        db.exec_sql(f"delete from application_guide_user_log where function_id=%(function_id)s",
                    {'function_id': delete_id})
        db.exec_sql(f"delete from function_collection where function_id=%(function_id)s", {'function_id': delete_id})


def delete_history_report_and_menu():
    dashboard_id_list = ['39fdceca-0540-f41f-24d3-06c99afd0be3',
                         '3a0662f2-53aa-eb9e-02db-e8edb7f7cc30',
                         '3a00914c-2f4f-2f8d-b2fe-f349ee7d7056',
                         '3a00914c-7b52-d9ff-6f74-ae21fa79558c',
                         '3a068b0c-315e-8dcf-7be7-b69794a56b62',
                         '3a02b244-8d09-cb98-786b-7d79c5cf5380',
                         '3a009132-051f-39d9-4679-d347a872f7fe',
                         '3a065676-b6eb-5ab1-eae2-d5a9b96005c0',
                         '3a05e17b-0788-94c2-193f-f0b07f08f321',
                         '3a0159ac-197b-7029-d9f1-7a0be2033cad',
                         '3a011799-38e0-bb4f-f9b8-9363d4a9f3c6',
                         '3a032f8b-0772-4aad-32ff-5f0edbf8322f',
                         '3a0530bc-390a-f20b-8c9b-a372e7b1b2bb',
                         '39fffa9f-dea0-dddf-469c-7de1befbfff5',
                         '3a028dfe-bc60-6c37-dc16-9c3df3c03fa0',
                         '39fffaa0-6492-0ba9-bdfd-85a7b7fcda6b',
                         '39fffabb-49bc-9137-8c36-8656029385e9',
                         '39fb135b-0af0-b63a-fb56-a86a9948d880',
                         '39fb135a-86ee-c6e0-a725-19ee1b71f853',
                         '3a07fce3-1b05-c815-2a9d-2f4b2ef1a0e5',
                         '3a07fcd3-b1ee-8723-d6c9-e0eaf1da2876',
                         '3a07b557-f76b-8fe1-bde9-99ab3e9ad0b9',
                         '3a0801dc-32af-b110-7694-7baa343acdbf',
                         '3a07df43-5f26-b2ef-79a9-e41e35ec2811',
                         '3a0801db-f239-db86-8364-859f140735ee',
                         '3a068b5c-4c96-996f-3f41-8b55460e619a',
                         '3a066af9-0eca-4013-e0bf-24351e6b4fd8',
                         '3a028fb8-426c-645b-30cf-facc6c342cea',
                         '39fffa99-9f4a-7f97-e951-27b932bfdb14',
                         '3a028f7e-851b-2533-2834-533c32a28341',
                         '3a02932c-90a3-d38e-4abc-fd2cd5ff7489',
                         '39fffa9b-5111-d115-3a99-a2be99b7f941',
                         '39fffa9c-cb3a-595e-cfff-28f1b7da617a',
                         '3a028de6-c9b8-57ea-a3aa-9ee43a82c919',
                         '3a028fba-3eed-fa5f-d2cc-b96603e4f0ec',
                         '39fffa9a-8274-b677-7797-a8b0c0b8a40b',
                         '3a028f73-733b-fd4c-ecd9-3c088624b119',
                         '39fffae3-4676-1eff-c2f0-0db912da66b1']
    report_id_list = ['3a09f110-c7d2-ae6a-a30c-e581fbb101ae',
                      '73266f08-c5a0-4c7a-acf5-b554cdb01bcd',
                      '312625ee-0a02-4bc6-9576-78fabc3c7f7b',
                      'e51445ae-8a4d-4503-9718-508e37541bb3',
                      '2f718b54-7e54-4a9c-ae03-168f4cef6bee',
                      'ce042043-e770-4805-b4d9-5ad315083303',
                      'eebc6b2a-79a2-41aa-b963-91dcd620cea1',
                      '05d0a20a-79e7-40d6-9873-d01de56a89cd',
                      '86110b2e-443a-40cd-92c9-0f5e63e3c3e2',
                      'b41ccb71-9d31-4ed3-a245-2061503db85c']
    log_info(f'删除门户中的废弃报表菜单,id:{report_id_list + dashboard_id_list}')
    with repository.get_db() as db:
        db.begin_transaction()
        for report_id in report_id_list + dashboard_id_list:
            delete_history_menu(db, report_id)
        db.commit()
    is_report_install = repository.check_db_table_is_exist("myrptdetail_design")
    if is_report_install:
        log_info(f'更新报表为非系统级,id:{report_id_list}')
        repository.update('myrptdetail_design', {'IsSystem': 0}, {'myrptdetailid': report_id_list})
        repository.update('myrptdetail', {'IsSystem': 0}, {'myrptdetailid': report_id_list})
    from dashboard_chart.models import ReleaseModel
    from dashboard_chart.services.released_dashboard_service import release_dashboard
    from dashboard_chart.services.dashboard_service import execute_delete_dashboard
    for report_id in dashboard_id_list:
        dashboard = repository.get_one("dashboard", {"id": report_id})
        if dashboard:
            log_info(f'删除仪表板,id:{report_id}')
            param = {
                "id": report_id,
                "status": 0,
                "type_access_released": 3,
                "application_type": 0
            }
            release_model = ReleaseModel(**param)
            try:
                release_dashboard(release_model)
                execute_delete_dashboard(report_id)

            except Exception as e:
                log_error(f'删除仪表板报错,id:{report_id}：' + str(e))
                continue
    for report_id in report_id_list:
        dashboard = repository.get_one("dashboard", {"id": report_id})
        if dashboard:
            log_info(f'删除复杂报表,id:{report_id}')
            param = {
                "id": report_id,
                "status": 0,
                "type_access_released": 3,
                "application_type": 5
            }
            release_model = ReleaseModel(**param)
            try:
                release_dashboard(release_model)
                execute_delete_dashboard(report_id, "5")
            except Exception as e:
                log_error(f'删除复杂报表报错,id:{report_id}：' + str(e))
                continue


def get_enabled_app_code_list():
    sql = """ 
    select app_codes from project_create_record where status = 'COMPLETED' and code = %(code)s and source = 'ERP'
     """
    params = {'code': g.code}
    data = repository.get_data_by_sql(sql, params, from_config_db=True)
    app_codes = []
    for row in data:
        s = row.get('app_codes')
        if not s:
            continue
        app_codes += s.split(',')
    return list(set(app_codes))


def get_old_report_data():
    """
    # "id": "c6cb2399-99ad-433a-af5f-75ab1f2fd757",
    #             "parentId": null,
    #             "name": "销售系统",
    #             "type": "FOLDER",
    #             "isPublish": true,
    #             "levelCode": "0011"
    """
    from ppt.external_service import get_menu_erp_report_list
    g.account = g.code
    user = repository.get_data("user", conditions={"account": g.account}) or {}
    g.userid = user.get('id', '4230bc6e-69e6-46a9-a39e-b929a06a84e8')

    data, err = get_menu_erp_report_list()
    if err:
        raise Exception(err)
    return data


def log(msg):
    update_upgrade_notice_log(msg)
    logger.info(msg)


def log_info(msg):
    if g:
        dbcode = g.code
        if dbcode:
            logger.info(f"租户[{dbcode}]" + msg)
            return
    logger.info(msg)


def log_error(msg):
    if g:
        dbcode = g.code
        if dbcode:
            logger.error(f"租户[{dbcode}]报错：" + msg)
            return
    logger.error(msg)


def logerror(msg):
    update_upgrade_err_log(msg)
    logger.error(msg)


def get_application_id(app_code):
    return f'********-0000-0000-0000-********{app_code}'


def get_custom_application_id(app_code):
    return f'********-0000-0000-0000-********{app_code}'


def update_application_id(old_app_id, new_app_id):
    with repository.get_db() as db:
        db.begin_transaction()
        params = {
            "old_id": old_app_id,
            "new_id": new_app_id
        }
        # 更改门户id
        db.exec_sql("update application set id=%(new_id)s where id=%(old_id)s", params)
        db.exec_sql("update release_application set id=%(new_id)s where id=%(old_id)s", params)
        # 门户相关的表
        db.exec_sql("update `application_guide_img` set application_id=%(new_id)s where application_id=%(old_id)s",
                    params)
        db.exec_sql("update `application_guide_user_log` set application_id=%(new_id)s where application_id=%(old_id)s",
                    params)
        # 更改菜单
        db.exec_sql("update `function` set application_id=%(new_id)s where application_id=%(old_id)s", params)
        db.exec_sql("update `release_function` set application_id=%(new_id)s where application_id=%(old_id)s", params)
        # 菜单收藏
        db.exec_sql("update `function_collection` set application_id=%(new_id)s where application_id=%(old_id)s",
                    params)

        # 权限
        db.exec_sql("update `user_role_data_permission` set data_id=%(new_id)s "
                    "where data_id=%(old_id)s and data_type='application'", params)

        # 快照
        db.exec_sql("update `snapshot` set service_id=%(new_id)s where snap_type='门户' and service_id=%(old_id)s",
                    params)
        db.exec_sql("update `snapshot_release_application` set id=%(new_id)s where id=%(old_id)s",
                    params)
        db.exec_sql("update `snapshot_release_function` set application_id=%(new_id)s where application_id=%(old_id)s",
                    params)
        db.commit()


def create_application(app_code, app_name):
    log(f'开始处理门户{app_code},{app_name}')
    app_id = get_application_id(app_code)
    application = repository.get_one("application", {"id": app_id})
    if application and application.get('enable', False) and application.get('type_access_released', None) != 3:
        log(f'门户{app_code},{app_name}已存在，且发布类型不是第三方，开始替换门户id')
        # 存在门户，但门户的发布方式不是第三方，则将门户复制并修改为二开门户的id，同时修改相关内容
        update_application_id(app_id, get_custom_application_id(app_code))
        application = None

    if not application:
        log(f'门户{app_code},{app_name}不存在，开始创建门户')
        data = {
            "id": app_id,
            "name": app_name,
            "platform": "pc",
            "nav_type": 0,
            "menu_display_type": 1,  # 自动展开
            "description": "",
            "icon": "",
            "url": "",
            "target": "",
            "is_buildin": 0,
            "is_system": 1,  # 是否是系统门户
            "enable": 0,  # 未发布
            "type_access_released": 4,
            "created_by": g.code,
            "modified_by": g.code,
            "use_guide": 0,
            "theme": "浅色",
            "collapse": 1,
            "is_show_banner": 1,
            "is_cache": 1,
            "rank": repository.get_data_scalar_by_sql('select max(`rank`) from application', {}) or 0,
            "common_config": "{\"navLayoutType\":1}",
            "user_defined_style": "{\"width_mode\":\"large\"}",
        }
        repository.add_data('application', data)


def generate_level_code(parent_id, application_id):
    class LevelSequenceBaseModel(BaseModel):
        def __init__(self, **kwargs):
            self.table_name = ''
            self.table_level_id_field = 'id'
            self.table_level_code_field = 'level_code'
            self.level_id = ''
            self.attach_identify = ''
            self.unit_code_length = 4
            super().__init__(**kwargs)

    return level_sequence_service.generate_level_code(
        LevelSequenceBaseModel(level_id=parent_id, attach_identify=application_id, table_name='function')
    )


def create_menu(application, data):
    application_id = application.get('id')
    menu_id = data.get('id')
    menu_name = data.get('name')
    menu_parent = data.get('parent_id')
    menu_type = data.get('type')
    menu = repository.get_one("function",
                              {
                                  'id': menu_id,
                                  'application_id': application_id})
    if menu_type != "FILE" and not menu:
        menu = repository.get_one("function",
                                  {
                                      'name': menu_name,
                                      'application_id': application_id})
    if menu:
        return
    old_menu = repository.get_one("function", {'id': menu_id})
    if old_menu:
        logerror(
            f'存在菜单{old_menu.get("name")}[{old_menu.get("id")}],在门户[{old_menu.get("application_id")}]下,需要添加到门户[{application_id}]下')
        return

    repository.add_data("function", {
        'id': menu_id,
        'name': menu_name,
        'parent_id': menu_parent,
        'level_code': generate_level_code(menu_parent, application_id),
        'icon': 'dmpicon-column',
        'application_id': application_id,
        'url': menu_id if menu_type == 'FILE' else None,
        'report_type': 5 if menu_type == 'FILE' else 0,
    })


def exec_report_folder(folder):
    app_code = folder['app_code']
    application_id = f'********-0000-0000-0000-********{app_code}'
    application = repository.get_one('application',
                                     {'id': application_id})
    if application is None:
        log(f'{app_code},{folder["name"]},{folder["id"]}门户生成异常，跳过处理')
        return None
    create_menu(application, folder)
    return True


def exec_report(report, enabled_app_code_list):
    app_name = report['app_name']
    app_code = report['app_code']
    report_name = report['name']
    report_id = report['id']
    # log(f'开始处理{app_name},{app_code},{report_name},{report_id}')
    if app_code not in enabled_app_code_list:
        log_info(f'{app_name},{app_code},{report_name},{report_id}业务系统未开通，跳过处理')
        return None

    application_id = f'********-0000-0000-0000-********{app_code}'
    application = repository.get_one('application',
                                     {'id': application_id})
    if application is None:
        log(f'{app_name},{app_code},{report_name},{report_id}门户生成异常，跳过处理')
        return None

    if repository.data_is_exists("function",
                                 {
                                     'url': report_id,
                                     'application_id':
                                         f'********-0000-0000-0000-********{app_code}'}):
        log_info(f'报表{report_id},{report_name}已在门户中存在，跳过处理')
        return app_code

    for folder in report['folders']:
        create_menu(application, folder)

    create_menu(application, report)

    return app_code


def replace_report_2_0():
    log(f'开始替换1.0报表为2.0')
    system_report_id_list = ['3e39e649-4252-459a-9d25-b835e07e1587',
                             '62596bee-eb91-4785-b957-c51479d1600a',
                             'a57daa09-4a36-4d25-af88-6ac0b634e86c',
                             'e631d1f1-9f9d-4510-8a4d-af97fee18133',
                             '5dfef107-70aa-42aa-a98e-6f84ca4d10aa',
                             '286dab13-197c-4352-b86b-9aede3e26327',
                             '474c355d-1c86-4dff-b321-e8f19859ab38',
                             '525b5bae-b0b9-47bc-95d6-f200bb21f889',
                             'a20d5d7a-2761-49cd-9f79-af4eef6e7cc0',
                             'fa8c36b0-df95-44b1-8bda-6bbeda00a604',
                             '0ef29f8d-8664-4f5b-8a91-5932c1deb8b6',
                             'f9449649-5c42-4984-ad9a-bf50527d2bc1',
                             '12f6e4e4-2146-42b9-ae27-b039e5e6eef3',
                             '17d74f34-4f41-4fda-87e7-5444946ed594',
                             '451034f5-8feb-4e5e-a4f7-c7070b7856a6',
                             '7150b004-181b-45d4-bc85-c99488e181db',
                             '4ae8b7e2-e50f-419a-8fdf-1b036db8a5d5',
                             'bf57dbb3-b9db-4317-a88d-074c3bb08a2b',
                             'd886576e-c8ea-44d0-b1e9-f6a57d0abbda',
                             'ea5a0b0c-e4a1-4b47-98f0-2a1149d8369d',
                             '8229cada-d59f-4d35-9933-046bc6842f1a',
                             '96e48bd6-e2fe-466e-b890-a08c512c1719',
                             'c4ddba46-ddf4-4f6a-a656-3557f69f0800',
                             'eeadf9a5-2ba0-4ff0-a896-7722f4042e35',
                             '12d7b71a-faca-4947-8885-42fad31856af',
                             '75075994-d2ae-4f60-9ddc-1e467a8254b6',
                             '905c348d-9475-406e-8feb-e26e23d29fc6',
                             'cc3d5ebb-6ffc-444e-9ee7-cf784420a662',
                             'f200e9cf-9508-4368-8586-c2ab5c3dfc2b',
                             'c586e556-3e5f-4e98-afb2-e275f5bbb2e6',
                             '35c99fed-e7da-46c0-9251-ed908d3762ec',
                             '482b13d2-28f7-4b78-b628-73483371dcbc',
                             '48f0fb68-2d3f-4ea7-bc22-659e5d6a8b58',
                             '556d0fd6-d16f-4805-84cd-ff11b42f3379',
                             '6acce6cc-afcf-4e46-8e15-043900b9ebf6',
                             '8cccc0cd-a1d3-4a9f-a064-c57115f4314b',
                             '97b25a07-9813-4bcb-874f-08db97b38dc2',
                             'a52b45c3-5d79-43ce-954d-38267e174dfe',
                             'afffb743-f31e-4e03-874e-08db97b38dc2',
                             'c77754b4-7afc-4775-9599-d11d37fc4035',
                             'e2ebf0ff-68b8-4a65-bf64-565ce8449c20',
                             '34ab26b3-5585-4392-a7ba-4e2836dd7875',
                             '5b64180b-f8b8-49bf-9ba6-302e89cf8820',
                             '6a19fd7f-7280-4a4a-b739-f64bcfbbc8b6',
                             '1ab3f64a-cfab-4bca-8d04-62b2f4980b02',
                             '2bd4e7b0-0fad-459b-b05f-e89e7bf118a2',
                             '3d432d6d-8d4e-4a34-a386-f427909ea398',
                             '5910d23c-e31b-4476-9d97-ffcb9b2f6e3c',
                             '83488b8c-7256-4c68-9a75-0805cbc17e37',
                             'b4074e6d-aa15-4f00-86a3-a328ed179049',
                             'bd456777-1751-467d-b939-eee3583860df',
                             'e040a7f4-aa23-4dc4-b0cb-3c4b077e32ef',
                             'e6108bbc-f65e-4fdd-bc7b-be7673e98a23',
                             '1fa84633-cfa0-47ea-8cbf-a193b3efc952',
                             '2e92fbe4-6d07-498e-9767-9c36dd24dd82',
                             '5eca1158-ac45-440a-b62e-3c9feca3ed4e',
                             '78caae1f-63e8-463b-acd0-558bfdb54332',
                             '8e8d009a-2205-4999-96c5-74f582bd7e24',
                             'd6737f1c-f6ed-4e6c-adf4-6fa1aab41812',
                             '1fbbd0fc-9e94-4b07-8a7f-6f90d02f28d3',
                             '3a0f9e4e-f029-695b-cb9b-dd309de20c5b',
                             '666a5ce2-18c1-4acf-889e-89c8b7ee4de2',
                             '78b55200-3917-4398-8000-ac2220345fdb',
                             '20299669-18b2-4c3e-ae73-dec1242ec3ab',
                             '4eabc0b5-53a3-484e-a402-782246763b21',
                             'a33a1d67-0f4c-44ea-9b18-282db1271c4c',
                             'bf8f1aaf-72df-49ae-8379-c7b21c684a09',
                             'ef28e5d4-e3bf-4733-be8d-04464823fc54',
                             'cbed4d2c-66ae-495b-9697-f25373ca57b7',
                             '0871a05a-d1a6-4cc5-a441-97c74c11035e',
                             '7570d312-6f53-4137-a45d-6a810421a7e5',
                             'cb8a0d15-21b9-4a22-b05d-c8bd462e37fb',
                             'ecd20ad2-6089-4d37-874e-e194a7423ceb',
                             'fd9b1694-6106-4c31-9018-60fd8fdf8aa2']

    report_2_0_list = repository.get_list("myrptdetail_design", {},
                                          ["MyRptDetailId", "RptCName", "PublishStatus", "IsSystem"])
    report_2_0_system_id_list = [r['MyRptDetailId'] for r in
                                 filter(lambda item: item.get("PublishStatus") != 0 and (
                                         item["IsSystem"] == 1 or item["MyRptDetailId"] in system_report_id_list),
                                        report_2_0_list)]
    if report_2_0_system_id_list and len(report_2_0_system_id_list):
        rows = repository.update("function", {
            "report_type": 4
        }, {"url": report_2_0_system_id_list, "report_type": 5})
        log(f"2.0报表替换完成，数量:{rows}")
    else:
        log(f"2.0报表替换完成，数量:0")


def replace_dashboard():
    log(f'开始替换仪表板')
    replace_map = {'3a07fce3-1b05-c815-2a9d-2f4b2ef1a0e5': '3a0d4c65-db6a-6f41-e876-8439935f8b16',
                   '3a07fcd3-b1ee-8723-d6c9-e0eaf1da2876': '3a0d4c65-fe21-f1a0-0c8b-6d2cdaf2cbbe',
                   '3a07b557-f76b-8fe1-bde9-99ab3e9ad0b9': '3a0d4c66-3052-1a6d-ef14-eb5def7511ea',
                   '3a0801dc-32af-b110-7694-7baa343acdbf': '3a0d4c65-db6a-6f41-e876-8439935f8b16',
                   '3a07df43-5f26-b2ef-79a9-e41e35ec2811': '3a0d4c65-fe21-f1a0-0c8b-6d2cdaf2cbbe',
                   '3a0801db-f239-db86-8364-859f140735ee': '3a0d4c66-3052-1a6d-ef14-eb5def7511ea',
                   '3a068b5c-4c96-996f-3f41-8b55460e619a': '3a0cbc3b-a9d3-ee9f-7448-b37bdf7ae647',
                   '3a066af9-0eca-4013-e0bf-24351e6b4fd8': '3a0cbd18-3fcc-5ed9-97da-7760124fa0a2',
                   '3a028fb8-426c-645b-30cf-facc6c342cea': '3a0b59bd-aefc-7592-a197-3f90de1746ca',
                   '39fffa99-9f4a-7f97-e951-27b932bfdb14': '3a0b5edb-ba11-231f-bd3a-77eefffaec44',
                   '3a028f7e-851b-2533-2834-533c32a28341': '3a0b548d-df08-696d-cbf2-7bc0756e071d',
                   '3a02932c-90a3-d38e-4abc-fd2cd5ff7489': '3a0b5f47-bf22-d9cb-ac04-01413ab7dc85',
                   '39fffa9b-5111-d115-3a99-a2be99b7f941': '3a0b5985-a926-a8fe-509b-b101f96a06b4',
                   '39fffa9c-cb3a-595e-cfff-28f1b7da617a': '3a0b5ed6-1050-7582-8d5a-1a2c2cbb3f42',
                   '3a028de6-c9b8-57ea-a3aa-9ee43a82c919': '3a0b5f45-ca69-a34e-4552-7cdba0806d8d',
                   '3a028fba-3eed-fa5f-d2cc-b96603e4f0ec': '3a0b5f43-935f-acc2-c9e0-87587e5780f3',
                   '39fffa9a-8274-b677-7797-a8b0c0b8a40b': '3a0b5ee6-2039-0883-88aa-83e6deb8e4cd',
                   '3a028f73-733b-fd4c-ecd9-3c088624b119': '3a0b549d-0aa6-7674-a88b-3e3228d6d5ad',
                   '39fffae3-4676-1eff-c2f0-0db912da66b1': '3a0b549a-da47-3def-49c0-1553f55485a2'}
    for old_id in replace_map:
        new_id = replace_map[old_id]
        function_rows = repository.update("function", {"url": new_id}, {"url": old_id})
        release_function_rows = repository.update("release_function", {"url": new_id}, {"url": old_id})
        log_info(
            f'替换仪表板{old_id}为{new_id},设计时受影响行数{function_rows},运行时受影响行数{release_function_rows}')


def drop_empty_folder():
    with repository.get_db() as db:
        db.begin_transaction()
        empty_folders = get_empty_folder_id_list(db)
        while len(empty_folders) > 0:
            log(f'开始删除空报表文件夹:{empty_folders}')
            db.exec_sql("delete from dashboard where id in %(id_list)s", {"id_list": empty_folders})
            db.exec_sql("delete from myrptgroup where MyRptGroupId in %(id_list)s", {"id_list": empty_folders})
            empty_folders = get_empty_folder_id_list(db)
        db.commit()


def get_empty_folder_id_list(db):
    data = db.query('''
        select id from dashboard d1 
        where application_type in (5,6) and type='FOLDER' and NOT EXISTS (
        SELECT 1
        FROM dashboard d2
        WHERE d1.id = d2.parent_id and application_type in (5,6));''', {})
    return [item['id'] for item in data]


def upgrade_application_function(params):
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S%f')
    log(f'开始备份门户与菜单，时间戳为{timestamp}')
    repository.exec_sql(f"create table `application_bak_{timestamp}` select * from `application`;")
    repository.exec_sql(f"create table `function_bak_{timestamp}` select * from `function`;")

    try:
        report_1_0_list = get_old_report_data()
    except:
        log(f"获取1.0报表失败")
        report_1_0_list = []

    log(f'开始处理，总共{len(report_1_0_list)}个1.0报表')
    enabled_app_code_list = get_enabled_app_code_list()
    log(f'开户的app_code:{",".join(enabled_app_code_list)}')
    report_list = convert_report_list(report_1_0_list, enabled_app_code_list)

    # 准备要生成的门户列表
    app_code_map = {}
    for item in report_list:
        app_code = item.get("app_code", None)
        app_name = item.get("app_name", None)
        if app_code and app_name and app_code in enabled_app_code_list and app_code not in app_code_map:
            app_code_map[app_code] = app_name

    # 生成门户
    for item in app_code_map:
        create_application(item, app_code_map[item])

    # 挂接报表
    release_application_list = []

    # 预处理报表文件夹
    application_folders_map = build_report_folders_map(report_list)

    # 将报表文件夹按原来的levelCode排序，然后创建对应的菜单
    for app_code in application_folders_map:
        if app_code not in enabled_app_code_list:
            # 子系统未开通，跳过处理
            continue
        folders = application_folders_map[app_code]
        sorted_folders = sorted(folders, key=lambda r: r['level_code'])
        for folder in sorted_folders:
            result = exec_report_folder(folder)
            if result and app_code not in release_application_list:
                # 门户有变动，需要发布
                release_application_list.append(app_code)

    # 处理报表
    for report in report_list:
        app_code = exec_report(report, enabled_app_code_list)
        if app_code and app_code not in release_application_list:
            # 门户有变动，需要发布
            release_application_list.append(app_code)

    is_report_install = repository.check_db_table_is_exist("myrptdetail_design")
    if is_report_install:
        replace_report_2_0()

    replace_dashboard()

    if is_report_install:
        drop_empty_folder()

    for app_code in enabled_app_code_list:
        result = delete_invalid_menu(app_code, report_1_0_list)
        if result and app_code not in release_application_list:
            release_application_list.append(app_code)

    if len(release_application_list) > 0:
        for app_code in release_application_list:
            application_id = get_application_id(app_code)
            log(f'开始发布门户{application_id}')
            from app_menu.services.application_service import enable_application_and_sync_mip_internal
            enable_application_and_sync_mip_internal(application_id, 3)

    return True, f'门户升级成功;', None  # 门户升级成功


def delete_invalid_menu(app_code, report_1_0_list):
    log(f'开始删除1.0报表挂接菜单:{app_code}')
    application_id = get_application_id(app_code)
    valid_report_1_0_list = list(filter(lambda x: x['type'] == 'FILE' and x['isPublish'], report_1_0_list))
    valid_report_1_0_id_list = [x['id'] for x in valid_report_1_0_list]
    if len(valid_report_1_0_id_list) == 0:
        log(f'{app_code}无有效1.0报表，跳过删除处理')
        return 0
    delete_functions = repository.get_data_by_sql(
        "select id,name from `function` where `application_id` = %(application_id)s "
        "and `report_type`=5 "
        "and `url` != '' "
        "and `url` is not null "
        "and `url` not in %(valid_report_1_0_id_list)s",
        {
            'application_id': application_id,
            'valid_report_1_0_id_list': valid_report_1_0_id_list})
    from app_menu.services.function_service import delete_function, get_function
    for f in delete_functions:
        log(f'开始删除1.0报表挂接菜单:{f["id"]},{f["name"]}')
        function_model = get_function(f["id"])
        result = delete_function(function_model)
        log(f'结束删除1.0报表挂接菜单:{f["id"]},{f["name"]},result:{result}')

    return len(delete_functions)


def build_report_folders_map(report_list):
    application_folders_map = {}

    # 将报表文件夹按子系统分组
    for report in report_list:
        app_code = report['app_code']
        if app_code not in application_folders_map:
            application_folders_map[app_code] = []
        for folder in report['folders']:
            if not any(filter(lambda r: r['id'] == folder['id'], application_folders_map[app_code])):
                application_folders_map[app_code].append(folder)

    return application_folders_map


def build_report_menu(report, report_list, functions):
    folders = []
    parent_id = report['parentId']
    report_id = report['id']
    report_name = report['name']
    deep = 0
    app_code = None
    app_name = None
    while parent_id:
        deep = deep + 1
        if deep > 6:
            # 防止存在环
            raise Exception(f'报表{report_id},{report_name}目录层级存在问题')

        parent = next(filter(lambda r: r['id'] == parent_id, report_list), None)
        if parent:
            parent_id = parent['parentId']
            if parent_id:
                folders.append(parent)
            else:
                app_name = parent['name']
                app_code = parent['levelCode']

    valid_folders = []
    # 检查同名
    for folder in folders:
        same_name_func = next(
            filter(lambda r: r['id'] != folder['id'] and r['name'] == folder['name'] and r[
                'application_id'] == get_application_id(app_code), functions), None)
        if not same_name_func:
            # 没有同名的，直接加入有效文件夹列表
            valid_folders.append(folder)
        else:
            # 有同名的，将有效文件夹列表的最后一个或报表的parentId改成同名的id,并且跳出循环
            if len(valid_folders) > 0:
                valid_folders[len(valid_folders) - 1]['parentId'] = same_name_func['id']
            else:
                report['parentId'] = same_name_func['id']
            break

    valid_folders.reverse()
    return {
        'id': report_id,
        'name': report_name,
        'type': 'FILE',
        'parent_id': report['parentId'],
        'app_code': app_code,
        'app_name': app_name,
        'folders': [{
            'app_code': app_code,
            'app_name': app_name,
            'name': folder['name'],
            'id': folder['id'],
            'parent_id': folder['parentId'],
            'type': "FOLDER",
            'level_code': folder['groupLevelCode']
        } for folder in valid_folders]
    }


def convert_report_list(report_list, enabled_app_code_list):
    """
    准备要挂接的报表，过滤不使用的系统报表、子报表和未发布的报表
    """
    result = []
    functions = repository.get_list("function", {})
    for report in report_list:
        if report['type'] != 'FILE':
            continue
        if not report['isPublish']:
            continue
        parent_id = report['parentId']
        parent = list(filter(lambda r: r['id'] == parent_id and r['type'] == 'FOLDER', report_list))
        if len(parent) == 0:
            # 没有父级文件夹，则代表是子报表
            continue
        try:
            item = build_report_menu(report, report_list, functions)
            if item and item.get("app_code") in enabled_app_code_list:
                result.append(item)
        except Exception as e:
            logerror(f'转化1.0报表报错：{str(e)}')

    return sorted(result, key=lambda r: r['name'])
