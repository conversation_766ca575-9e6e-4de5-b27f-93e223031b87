import datetime

from base import repository
from dmplib.hug import g
import json
import logging
import time
from shuxin15_upgrade.services.external_upgrade_service import \
    update_upgrade_err_log, update_upgrade_notice_log, update_upgrade_warning_log

logger = logging.getLogger(__name__)

erp_site = {}


def dashboard_upgrade(params):
    update_upgrade_notice_log('开始看板升级')
    # 按购买系统分发
    res = deliver_dashboard()
    update_upgrade_notice_log('完成系统分发')

    # 删除看板中系统级空文件夹
    folders = delete_dashboard_sys_tmp_folder()
    update_upgrade_notice_log(f'删除看板中系统级空文件夹：{json.dumps(folders)}')

    return res


def delete_dashboard_sys_tmp_folder():
    """
        删除系统级空看板文件夹
    """
    try:
        from dashboard_chart.repositories.dashboard_repository import get_sys_tmp_folder
        folders = get_sys_tmp_folder()
    except Exception as e:
        logger.error(str(e))

    delete_folders = []

    for folder in folders:
        folder_id = folder.get("id")
        try:
            # 删除系统级空文件夹
            from dashboard_chart.services.dashboard_service import execute_delete_dashboard
            execute_delete_dashboard(folder_id)
        except Exception as e:
            logger.error(f"租户code[{g.code}]删除看板文件夹失败：" + str(e))
            continue
        delete_folders.append({"id": folder_id, "name": folder.get("name")})

    return delete_folders


def deliver_dashboard():
    code = g.code
    apps = get_completed_record_appcodes(code)
    app_codes = [{'app_code': app_code} for app_code in apps]
    update_upgrade_notice_log(f'增购app_code_list：{json.dumps(app_codes)}')
    if not code:
        update_upgrade_err_log(f'升级看板租户code为空')
        return False, f'升级看板租户code为空'
    if not apps and len(apps) == 0:
        update_upgrade_err_log(f'升级看板租户code为空')
        return True, f'无app_code_list不需要导入看板'
    dmp_params = {
        'code': code,
        'app_code_list': app_codes,
        'from': 'erpsaas',
        'action': 'update',  # create update
        'overwrite_1_5_app': 1,  # 增购时强制再覆盖一次
        'init_1_5_app': 1  # 取1.5模板增购
    }
    from open_data.services.open_data_service import to_open_tenant
    from components.date_utils import DateUtil
    res = to_open_tenant(**dmp_params)

    if res and len(res) > 2:
        time.sleep(10)
        task_id = res[2].get("task_id")
        update_upgrade_notice_log(f'看板导入中task_id为：{task_id}')
        times = 0
        if task_id:
            while True:
                result = get_open_tenant_status(task_id)
                update_upgrade_notice_log(
                    f'{DateUtil.get_now_str()}查询第{times}次，返回值：{json.dumps(result, ensure_ascii=False)}')
                if result:
                    status = result.get("status")
                    if status == 1:
                        return True, f'报表升级成功:{json.dumps(result, ensure_ascii=False)}'
                    elif status == 2:
                        return False, f'报表升级失败，开户返回值：{json.dumps(res, ensure_ascii=False)}，开户查询接口返回值：{json.dumps(result, ensure_ascii=False)}'
                    else:
                        logger.info(f"{DateUtil.get_now_str()}请求次數：{times}")
                        time.sleep(40)
                        times = times + 1
                        if times == 10:
                            return False, f'报表升级失败查询接口超过10次:{json.dumps(res, ensure_ascii=False)}，开户查询接口返回值：{json.dumps(result, ensure_ascii=False)}'  # 数据升级成功
        else:
            return False, f'报表升级失败:{json.dumps(res, ensure_ascii=False)}'  # 数据升级成功
    else:
        return False, f'报表升级失败返回格式错误:{json.dumps(res, ensure_ascii=False)}'  # 数据升级成功


def get_completed_record_appcodes(code):
    sql = """ select app_codes from project_create_record where status = 'COMPLETED' and code = %(code)s and source = 'ERP' """
    params = {'code': code}
    data = repository.get_data_by_sql(sql, params, from_config_db=True)
    app_codes = []
    for row in data:
        s = row.get('app_codes')
        if not s:
            continue
        app_codes += s.split(',')
    return list(set(app_codes))


def get_open_tenant_status(task_id):
    """
    获取开户状态
    """
    default = {'status': -1, 'err_msg': ''}
    sql = f""" select status,err_msg from tenant_open_log where task_id='{task_id}' limit 1"""
    result = repository.get_data_by_sql(sql, None, from_config_db=True)
    logger.info(f"开户状态：{result},task:{task_id},sql:{sql}")
    if result:
        return result[0] or default
    return default
