import json

from dmplib.hug import APIWrapper
from .services import shuxin15_upgrade_service, upgrade_application_function_service

api = APIWrapper(__name__)


@api.admin_route.get('/module_list')
def module_list():
    """
    /**
    @apiVersion 1.0.1
    @api  {get} /api/shuxin15_upgrade/module_list 数据升级模块列表
    @apiGroup  shuxin15数据升级
    @apiResponse 200 {
        "module_list":[
            {
                "module":"all",
                "module_name":"全量升级",
                "has_param":false
            },
            {
                "module":"datasource",
                "module_name":"创建数据源",
                "has_param":false
            },
            {
                "module":"standardlist",
                "module_name":"标准看板列表",
                "has_param":true
            },
            {
                "module":"importdashboard",
                "module_name":"导入覆盖看板",
                "has_param":true
            },
            {
                "module":"cleardataset",
                "module_name":"清理数据集",
                "has_param":true
            },
            {
                "module":"selfdatasethand",
                "module_name":"个性化数据集处理",
                "has_param":true
            }
        ],
        "upgrade_record":{
            "id":"39fc5c16-bb85-fd45-ee23-5383b2aa65ea",
            "module_name":"仪表板",
            "status":"完成",
            "completed_on":"2021-05-08 10:59:02"
        }
    }
    **/
    """
    return True, 'success', shuxin15_upgrade_service.get_module_list()


@api.admin_route.get('/log_view')
def log_view(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {get} /api/shuxin15_upgrade/log_view 数据升级模块列表
    @apiGroup  shuxin15数据升级
    @apiParam query {string}  upgrade_task_id 任务id
    @apiParam query {int}  index 索引值
    @apiResponse 200
    {
        "next_index":null,
        "log":"[shuxin15数据升级任务开始]
Notice:模块：全量升级 任务id：39fba39f-56c9-2cd4-3461-193d3bdfdd63 参数：[]
",
        "is_end":true
    }
    **/
    """
    upgrade_task_id = kwargs.get('upgrade_task_id')
    index = kwargs.get('index')
    return True, 'success', shuxin15_upgrade_service.log_view(upgrade_task_id)


@api.admin_route.get('/last_log_view')
def last_log_view(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {get} /api/shuxin15_upgrade/last_log_view 数据升级模块列表
    @apiGroup  shuxin15数据升级
    @apiParam query {string}  upgrade_task_id 任务id
    @apiParam query {int}  index 索引值
    @apiResponse 200
    {
        "next_index":null,
        "log":"[shuxin15数据升级任务开始]
Notice:模块：全量升级 任务id：39fba39f-56c9-2cd4-3461-193d3bdfdd63 参数：[]
",
        "is_end":true
    }
    **/
    """
    return True, 'success', shuxin15_upgrade_service.last_log_view()

@api.admin_route.get('/delete_report_and_menu')
def delete_report_and_menu(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {get} /api/shuxin15_upgrade/last_log_view 数据升级模块列表
    @apiGroup  shuxin15数据升级
    @apiParam query {string}  upgrade_task_id 任务id
    @apiParam query {int}  index 索引值
    @apiResponse 200
    {
        "next_index":null,
        "log":"[shuxin15数据升级任务开始]
Notice:模块：全量升级 任务id：39fba39f-56c9-2cd4-3461-193d3bdfdd63 参数：[]
",
        "is_end":true
    }
    **/
    """
    return True, 'success', shuxin15_upgrade_service.delete_report_and_menu()


@api.admin_route.post('/exec')
def upgrade_exec(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {post} /api/shuxin15_upgrade/exec shuxin15数据升级接口入口
    @apiGroup  shuxin15数据升级
    @apiBodyParam {
        "module": "模块标识",
        "params": [] // 附加参数（预留扩展，例如指定某些模块的业务id单独升级）
    }
    @apiResponse 200 {
        "result": true,
        "msg": "ok"
    }
    **/
    """
    params = kwargs.get('params', [])
    if params and not isinstance(params, dict):
        params = json.loads(params)
    status, upgrade_task_id = shuxin15_upgrade_service.trigger_upgrade(
        kwargs.get('module', 'all'),
        params
    )
    msg = '执行失败, 请稍候重试' if not status else '执行成功'
    return status, msg, upgrade_task_id


@api.admin_route.get('/exec')
def upgrade_exec(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {post} /api/shuxin15_upgrade/exec shuxin15数据升级接口入口
    @apiGroup  shuxin15数据升级
    @apiBodyParam {
        "module": "模块标识",
        "params": [] // 附加参数（预留扩展，例如指定某些模块的业务id单独升级）
    }
    @apiResponse 200 {
        "result": true,
        "msg": "ok"
    }
    **/
    """
    params = kwargs.get('params', [])
    if params and not isinstance(params, dict):
        params = json.loads(params)
    status, upgrade_task_id = shuxin15_upgrade_service.trigger_upgrade(
        kwargs.get('module', '全量升级'),
        params,
    )
    msg = '执行失败, 请稍候重试' if not status else '执行成功'
    return status, msg, upgrade_task_id


@api.admin_route.get('/sync_upgrade')
def sync_upgrade(request, **kwargs):
    """
    同步升级
    :return:
    """
    params = kwargs.get('params', [])
    if params and not isinstance(params, dict):
        params = json.loads(params)
    return shuxin15_upgrade_service.sync_upgrade(kwargs.get('module', '全量升级'), params)

@api.admin_route.get()
def get_report10_Publish_Status(request, **kwargs):
    """
        同步升级
        :return:
        """
    return shuxin15_upgrade_service.get_report10_Publish_Status()


@api.admin_route.get('/get_check_result')
def get_check_result(**kwargs):
    return shuxin15_upgrade_service.get_check_result(kwargs.get('module', 'compare'))


@api.admin_route.get('/get_report_check_result')
def get_check_result(**kwargs):
    return shuxin15_upgrade_service.get_report_check_result(kwargs.get('module', 'compare'))


@api.admin_route.get('/upgrade_application_function')
def upgrade_report_1_0(request, **kwargs):
    result, msg, data = upgrade_application_function_service.upgrade_application_function()
    return result, msg, data
