from loguru import logger
from dmplib.saas.project import get_db
from base import repository


def update_upgrade_task(upgrade_task_id, data_row):
    if not upgrade_task_id:
        return False
    return get_db().update('shuxin15_upgrade_task', data_row, {'id': upgrade_task_id})


def update_upgrade_info(condition, data_row):
    if not condition:
        return False
    return get_db().update('shuxin15_upgrade_task', data_row, condition)


def insert_upgrade_task(data_row):
    return get_db().insert('shuxin15_upgrade_task', data_row)


def update_upgrade_task_log(upgrade_task_id, msg: str = ''):
    if not upgrade_task_id or not msg:
        return False
    msg += "\n"
    sql = (
        'update `shuxin15_upgrade_task` '
        'set log_data = CONCAT(log_data, %(msg)s) WHERE id=%(id)s'
    )
    return get_db().exec_sql(sql, params={
        'msg': msg,
        'id': upgrade_task_id
    })


def get_upgrade_task_detail(upgrade_task_id, field_list=['*']):
    if not upgrade_task_id:
        return None
    return repository.get_one('shuxin15_upgrade_task', {'id': upgrade_task_id}, fields=field_list)


def get_upgrade_task_by_where(where):
    if not where:
        return None
    return repository.get_one('shuxin15_upgrade_task', conditions=where)


def get_upgrade_task_log_data(upgrade_task_id):
    if not upgrade_task_id:
        return None
    return repository.get_one('shuxin15_upgrade_task', {'id': upgrade_task_id}, ['log_data', 'status'])


def get_last_upgrade_task_log_data():
    return repository.get_one('shuxin15_upgrade_task',  conditions={},fields=['id', 'module_name', 'log_data', 'status'],
                              order_by='created_on desc, completed_on desc')


def get_last_upgrade_task():
    """
    获取最后一次的数据升级任务记录
    :return:
    """
    return repository.get_one('shuxin15_upgrade_task', conditions={},
                              fields=['id', 'module_name', 'status', 'completed_on'],
                              order_by='created_on desc, completed_on desc')


def batch_operate_editor_modes(list_insert_table2dict, del_table_list, del_id):
    """
    批量操作editor model  插入操作 (同一个事务) 
    :param list_insert_table2dict: 
    :return:
    """
    with get_db() as db:
        try:
            db.begin_transaction()
            commit = False
            for table in del_table_list:
                db.delete(table, condition={"dashboard_id": del_id}, commit=commit)
            # 清理历史数据
            db.delete('dashboard_metadata_history', condition={"dashboard_id": del_id}, commit=commit)
            for table, list_data in list_insert_table2dict.items():
                insert_list = []  # 由于插入的时候组件数量过大，插入会出现sql过大的情况，3条一批次
                for i, data_model in enumerate(list_data):
                    data_model_dic = data_model.get_dict()
                    insert_list.append(data_model_dic)
                    if (i + 1) % 10 == 0:
                        db.replace_multi_data(
                            table=table, list_data=insert_list, fields=list(data_model_dic.keys()), commit=commit
                        )
                        insert_list = []
                if len(insert_list) > 0:
                    db.replace_multi_data(table=table, list_data=insert_list, fields=list(data_model_dic.keys()),
                                          commit=commit)
            db.commit()
            return True, []
        except Exception as e:
            msg = '更新sql   失败' + table + str(insert_list) + str(e)
            logger.exception(msg)
            db.rollback()
            raise Exception(msg)
