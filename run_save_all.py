import requests
from datetime import datetime
from requests.adapters import HTTPAdapter
from urllib3 import Retry
import logging, sys

handlers = [logging.FileHandler('gen.log', mode='a', encoding='utf-8'), logging.StreamHandler(sys.stdout)]
logging.basicConfig(level=logging.INFO, handlers=handlers, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

TAG = '2020-12-31'
DURATION_SEC = 7200

class Client:

    def __init__(self):
        self.host = 'http://bi1.risesuncloud.cn'
        self.code = 'rongsheng'
        self.account = 'rongsheng'
        self.password = 'ac95a7ec8e3c8633dc0648cd77f62ff5295809fd'
        self.timeout = (20, 20)

        self.do_filter = False
        self.filter_dataset_ids = []

        max_retry = 2
        retry = Retry(total=max_retry, read=max_retry, connect=max_retry, backoff_factor=1, status_forcelist=(500, 503))
        adapter = HTTPAdapter(max_retries=retry)
        session = requests.session()
        session.mount('https://', adapter)
        session.mount('http://', adapter)

        self.session = session

    def login(self):
        logging.info("登录dmp")
        rsp = self.session.post(f'{self.host}/api/user/login', json={
            'account': self.account,
            'keep_alive': True,
            'password': self.password,
            'tenant_code': self.code
        })
        if rsp.status_code != 200:
            raise Exception('登录失败')

    def get_dataset(self):
        logging.info("获取数据集")
        rsp = self.session.get(f'{self.host}/api/dataset/tree', params={'EXCLUDE_TYPE': 'TEMPLATE,EXTERNAL_SUBJECT'})
        if rsp.status_code != 200 or rsp.json().get('result') != True:
            raise Exception('获取数据集列表失败')
        rv = rsp.json()
        self.dataset_list = self.tree_to_list(rv.get('data'))
        logging.info(f"获取数据集, 共{len(self.dataset_list)}个")
        self.dataset_list = self.filter_dataset(self.dataset_list)
        logging.info(f"需要调度的数据集, 共{len(self.dataset_list)}个")
        if self.do_filter:
            self.dataset_list = [item for item in self.dataset_list if item.get('id') in self.filter_dataset_ids]

        # needed = [item for item in self.dataset_list if item.get('id') in self.filter_dataset_ids]
        self.dataset_list = sorted(self.dataset_list, key=lambda item: item.get('created_on'))

        logging.info(f"过滤后, 共{len(self.dataset_list)}个")

    def schedule_all(self):
        logging.info("开始调度")
        failed_ids = []
        succ_ids = []
        skip_ids = []
        for dataset in self.dataset_list:
            try:
                if self.is_scheduled(dataset.get('id')):
                    skip_ids.append(dataset.get('id'))
                    continue
                self.schedule(dataset.get('id'))
                succ_ids.append(dataset.get('id'))
            except Exception as e:
                logging.error(f"调度数据集<{dataset.get('id')}, {dataset.get('name')}>失败, e: {str(e)}")
                failed_ids.append(dataset.get('id'))
                continue

        logging.info(f"调度结束, total: {len(self.dataset_list)}, failed: {len(failed_ids)}, succ: {len(succ_ids)}, skip: {len(skip_ids)}")
        logging.info(f"成功数据集: {succ_ids}")
        logging.info(f"失败数据集: {failed_ids}")
        logging.info(f"跳过数据集: {skip_ids}")


    def schedule(self, dataset_id):
        rsp = self.session.post(f'{self.host}/api/dataset/run', json={
            'id': dataset_id
        })
        if rsp.status_code != 200 or rsp.json().get('result') != True:
            raise Exception('调度数据集失败')


    def is_scheduled(self, dataset_id):
        rsp = self.session.get(f'{self.host}/api/dataset/version/list', params={'dataset_id': dataset_id})
        if rsp.status_code != 200:
            raise Exception(f'请求获取调度状态失败, status_code: {rsp.status_code}')
        if rsp.json().get('result') != True:
            logging.error(rsp.json())
            raise Exception(f'请求获取调度状态失败')


        rv = rsp.json().get('data')
        now = datetime.now()
        items = rv.get('items', [])
        for item in items:
            status = item.get('status')
            created_on = item.get('created_on')
            created_on = datetime.strptime(created_on, '%Y-%m-%d %H:%M:%S')
            if ((now - created_on).total_seconds() <= DURATION_SEC) and status in ['正常', '已成功']:
                return True
        return False


    def filter_dataset(self, dataset_list):
        dataset_list = [item for item in dataset_list
                if ((item.get('type') in ['UNION']) or
                        (item.get('type') == 'SQL' and item.get('connect_type') != '直连'))]
        return dataset_list

    def tree_to_list(self, data):
        rv = []
        for item in data:
            if item.get('type') == 'FOLDER':
                dataset_list = self.tree_to_list(item.get('sub', []))
                rv.extend(dataset_list)
            rv.append(item)
        return rv

    def save_all(self):
        failed_ids = []
        skip_ids = []
        succ_ids = []
        for dataset in self.dataset_list:
            try:
                if self.is_saved(dataset.get('id')):
                    skip_ids.append(dataset.get('id'))
                    continue
                self.save(dataset.get('id'))
                succ_ids.append(dataset.get('id'))
            except Exception as e:
                logging.error(f"保存数据集<{dataset.get('id')}, {dataset.get('name')}>失败, e: {str(e)}")
                failed_ids.append(dataset.get('id'))
                continue
        logging.info(f"保存结束, total: {len(self.dataset_list)}, failed: {len(failed_ids)}, succ: {len(succ_ids)}, skip: {len(skip_ids)}")
        logging.info(f"失败数据集: {failed_ids}")
        logging.info(f"跳过数据集: {skip_ids}")
        logging.info(f"成功数据集: {succ_ids}")


    def is_saved(self, dataset_id):
        rsp = self.session.get(f'{self.host}/api/dataset/version/list', params={'dataset_id': dataset_id})
        if rsp.status_code != 200:
            raise Exception(f'请求获取调度状态失败, status_code: {rsp.status_code}')
        if rsp.json().get('result') != True:
            logging.error(rsp.json())
            raise Exception(f'请求获取调度状态失败')

        rv = rsp.json().get('data')
        items = rv.get('items', [])
        for item in items:
            if item.get('version_name') == TAG and item.get('status') == '正常':
                return True
        return False

    def save(self, dataset_id):
        body = {
            'content': '12-31手动保存',
            'dataset_id': dataset_id,
            'version_name': TAG
        }
        rsp = self.session.post(f'{self.host}/api/dataset/version/add', json=body)

        if rsp.status_code != 200:
            raise Exception(f'请求保存失败, status_code: {rsp.status_code}')
        if rsp.json().get('result') != True:
            logging.error(rsp.json())
            raise Exception(f'请求保存失败')

    def check_all(self):
        succ_ids = []
        failed_ids = []
        running_ids = []
        fatal_ids = []
        created_ids = []
        for dataset in self.dataset_list:
            id = dataset.get('id')
            try:
                status = self.get_status(id)
                if status == 'succ':
                    succ_ids.append(id)
                if status == 'failed':
                    status = self.get_flow_status(id)
                    if status == 'created':
                        created_ids.append(id)
                        continue
                    if status == 'running':
                        running_ids.append(id)
                        continue
                    failed_ids.append(id)
                    logging.error(f"检查数据集<{dataset.get('id')}, {dataset.get('name')}>, 状态<{status}>")
                    continue
                if status == 'running':
                    running_ids.append(id)
                    continue
            except Exception as e:
                logging.error(f"检查数据集<{dataset.get('id')}, {dataset.get('name')}>获取状态失败, e: {str(e)}")
                fatal_ids.append(id)
                continue

        logging.info(f"成功: {len(succ_ids)}, 失败: {len(failed_ids)}, 未知: {len(fatal_ids)}, 运行中: {len(running_ids)}, 已创建: {len(created_ids)}")
        logging.info(f"成功: {succ_ids}")
        logging.info(f"失败: {failed_ids}")
        logging.info(f"未知: {fatal_ids}")
        logging.info(f"已创建: {created_ids}")
        logging.info(f"运行中: {running_ids}")

    def get_flow_status(self, dataset_id):
        rsp = self.session.get(f'{self.host}/api/dataset/flow_status', params={'dataset_id': dataset_id})
        if rsp.status_code != 200:
            raise Exception(f'请求获取调度状态失败, status_code: {rsp.status_code}')
        if rsp.json().get('result') != True:
            logging.error(rsp.json())
            raise Exception(f'请求获取调度状态失败')
        rv = rsp.json().get('data')
        if rv.get('status') == '已创建':
            return 'created'
        if rv.get('status') == '运行中':
            return 'running'
        return rv.get('status')

    def get_status(self, dataset_id):
        rsp = self.session.get(f'{self.host}/api/dataset/version/list', params={'dataset_id': dataset_id})
        if rsp.status_code != 200:
            raise Exception(f'请求获取调度状态失败, status_code: {rsp.status_code}')
        if rsp.json().get('result') != True:
            logging.error(rsp.json())
            raise Exception(f'请求获取调度状态失败')
        rv = rsp.json().get('data')
        now = datetime.now()
        items = rv.get('items', [])
        for item in items:
            status = item.get('status')
            created_on = item.get('created_on')
            created_on = datetime.strptime(created_on, '%Y-%m-%d %H:%M:%S')
            if ((now - created_on).total_seconds() <= DURATION_SEC) and status in ['正常', '已成功']:
                return 'succ'
        return 'failed'

def get_dataset_count():
    logging.info("=============================================================================")
    c = Client()
    c.login()
    c.get_dataset()

def schedule_all():
    logging.info("=============================================================================")
    c = Client()
    c.login()
    c.get_dataset()
    c.schedule_all()

def check_schedule_status():
    logging.info("=============================================================================")
    c = Client()
    c.login()
    c.get_dataset()
    c.check_all()

def save_all():
    logging.info("=============================================================================")
    c = Client()
    c.login()
    c.get_dataset()
    c.save_all()

if __name__ == '__main__':
    # get_dataset_count()
    # schedule_all()
    # check_schedule_status()
    save_all()


