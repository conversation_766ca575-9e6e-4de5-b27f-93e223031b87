#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/06/11
# pylint: skip-file

"""
（临时）发布报告模块数据升级代码
"""
import json
from base import repository
from base.enums import DashboardTypeStatus
from dashboard_chart.models import ReleaseModel, ScreenDashboardModel
from dashboard_chart.services import dashboard_service, chart_service, released_dashboard_service
from dmplib.utils.strings import seq_id
from dmplib.saas.project import get_db
import logging
from dmplib.utils.errors import UserError


def upgrade_released_dashboard(task_id):
    sql = (
        'select id, is_multiple_screen, share_secret_key, type_access_released,status '
        'from dashboard_backup_v1 where upgrade_status=0'
    )
    params = {}
    with get_db() as db:
        data = db.query(sql, params)
    if not data:
        return True
    i = 0
    id_list = []
    dashboard_id = None
    try:
        for dashboard in data:
            dashboard_id = dashboard.get('id')
            kwargs = {
                'id': dashboard_id,
                'status': dashboard.get('status'),
                'view_passwd': dashboard.get('share_secret_key'),
                'type_access_released': dashboard.get('type_access_released'),
                'user_groups': [],
            }
            logging.info('---执行{task_id}异步任务，参数内容：{args}'.format(task_id=task_id, args=json.dumps(kwargs)))
            release(ReleaseModel(**kwargs))
            i += 1
            id_list.append(dashboard_id)
            repository.update_data('dashboard_backup_v1', {'upgrade_status': 1}, {'id': dashboard_id})
        logging.info('====执行报告发布数据升级异步任务，总共执行{i}条数据, 对应报告id分别为：{ids}'.format(i=i, ids=json.dumps(id_list)))
    except Exception as e:
        repository.update_data(
            'dashboard_backup_v1', {'upgrade_status': 1, 'upgrade_msg': e.__str__()}, {'id': dashboard_id}
        )


def release(model):
    """
    发布报告
    :param ReleaseModel model:
    :return:
    """
    model.validate()

    dashboard = dashboard_service.get_dashboard(model.id)
    if not dashboard:
        raise UserError(message="报告不存在" % model.id)
    dashboard["status"] = model.status
    dashboard["share_secret_key"] = model.view_passwd
    dashboard["type_access_released"] = model.type_access_released

    _screens = []
    # 1. 删除原来的旧的关系表
    repository.delete_data("screen_dashboard", {"dashboard_id": model.id, "type": DashboardTypeStatus.Release.value})
    if int(model.status) != 0:
        # 2. 找到保存草稿的数据
        dashboards = {'dashboard': dashboard, 'screens': []}
        screen_dict = {}

        if dashboard['is_multiple_screen'] == 0:
            _screens = [{"dashboard_id": dashboard['id'], "screen_id": dashboard['id'], "rank": 1}]
        else:
            _screens = repository.get_data(
                "screen_dashboard",
                {"dashboard_id": model.id, "type": DashboardTypeStatus.Draft.value},
                ["dashboard_id", "screen_id", "rank"],
                True,
                [("rank", "asc")],
            )

        for screen in _screens:
            _screen = {
                "screen_id": screen['screen_id'],
                "type": DashboardTypeStatus.Release.value,
                "dashboard_id": model.id,
                "rank": screen["rank"],
                "id": seq_id(),
            }
            screen_model = ScreenDashboardModel(**_screen)
            repository.add_data(
                "screen_dashboard", screen_model.get_dict(["id", "dashboard_id", "screen_id", "type", "rank"])
            )

            # 3. 把单图的数据都抓出来保存到发布表
            tmp_screen = dashboard_service.get_dashboard(screen['screen_id'])
            if tmp_screen:
                dashboards['screens'].append(tmp_screen)
            chart_list = chart_service.get_chart_list(screen['screen_id'], True, True)
            if chart_list:
                screen_dict[screen['screen_id']] = chart_list

        # logging.error("===执行异步任务，screen_dict参数内容：{screen_dict}".format(screen_dict=json.dumps(screen_dict)))
        # 4. Save dashboard_released_snapshot表数据
        repository.delete_data("dashboard_released_snapshot_dashboard", {"snapshot_id": model.id})
        repository.delete_data("dashboard_released_snapshot_chart", {"snapshot_id": model.id})

        released_dashboard_service.save_released_snapshot_dashboard_from_dashboard_list(dashboards)
        if screen_dict and isinstance(screen_dict, dict):
            released_dashboard_service.save_released_snapshot_chart(screen_dict, model.id, dashboard)

    # 5. Save dashboard status
    repository.update_data(
        "dashboard",
        {
            "status": model.status,
            "share_secret_key": model.view_passwd,
            "type_access_released": model.type_access_released,
        },
        {"id": model.id},
    )

    # 6. 删除已发布报告缓存
    released_dashboard_service._delete_released_dashboard_cache(dashboard, _screens)
