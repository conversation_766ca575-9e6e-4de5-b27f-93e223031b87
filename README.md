#  大数据组DMP项目结构开发简介 2


**技术栈**

> python3.6.x + cython(dmp-lib)
> [hug](https://github.com/timothycrosley/hug), celery 
> golang, docker, mysql, redis


## 一、DMP产品项目组成

DMP后端项目主要 有以下 

1. **dmp**

dmp产品主应用的服务端(`API`/`celery`). git: `************************:dmp/dmp.git` 

2. **dmp-admin**

dmp的 多租户管理后台项目(`API`/`admin-celery`). git: `************************:dmp/dmp-admin.git`

3. **dmp-flow**

数据流程服务. git: `************************:dmp/dmp-flow.git`

4. dmp-lib

dmp项目的共公库. git: `************************:dmp/dmp-lib.git`


## 二、开发环境部署

注意：dmp线上运行环境为python3.5.3， 3.6.13开发工具环境(python3.6.x以上)

### 远程开发服务器

[参考](https://www.tapd.cn/38229611/documents/show/1138229611001001369?file_type=word)

### 本地python多环境管理

- [pyenv](https://github.com/pyenv/pyenv)

- [pyenv-install](https://github.com/pyenv/pyenv-installer)

> 使用pyenv管理多个python版本

- `pyenv install 3.6.7`
- `pyenv install 3.5.3`
- `pyenv rehash`
- `pyenv virtualenv 3.5.3 dmp35`
- `pyenv activate dmp35`

安装项目依赖: `pip3 install -r requirement.txt`

###  开发运行环境

## 本地使用pre-commit保证代码 质量

[**官方文档**](https://pre-commit.com/)

### 使用说明

1. 安装到git hook (项目只有第一次需要)

`pre-commit install`
2. 手动运行hook

`pre-commit run --all-files`
3. git commit 会自动执行`.pre-commit-config.yaml`文件中的hook.

**可以修改pylintrc文件自定义pylint参数**

1. 怎么忽略整个文件, 不执行检查? 适用于: 由于历史原因, 一些文件有太多需要fix.

在module文件头部添加`# pylint: skip-file`

```python

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

def foo():
    pass
```

2. 怎么忽略单行?

在行添加`# pylint: disable=<your rule>`

```python
 # pylint: disable=line-too-long
 pattern = 'phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|'

```

3. 怎么忽略某个规则?

将rule添加到pylintrc的disable属性下


### 准备环境

1. 下载docker运行环境
2. 拉取dmp各仓库代码(dmp, dmp-admin, dmp-web, dmp-admin-web，dmp-flow)
3. windows 环境下需要将项目源码放在用户目录下, 如c:/Users/<USER>/project/dmp


windows下建议开发目录结构:


```

c:/Users/<USER>/dev/dmp
c:/Users/<USER>/dev/dmp-web
c:/Users/<USER>/dev/dmp-admin
c:/Users/<USER>/dev/dmp-admin-web

```
### 特别注意：windows下挂载文件或者目录，必须要确定目标文件夹或文件要拥有 共享权限

### 1. 拉取镜像

```
docker login --username=mysoft_paas --password=mypaas123 --email=1  registry.cn-hangzhou.aliyuncs.com

docker pull registry.cn-hangzhou.aliyuncs.com/mic-dmp/dmp-libbase:beta

```


### 2. 运行应用镜像

- 运行dmp站点(要求8000端口)

```
docker stop dmp
docker rm dmp
docker run -d --restart=always \
        --name dmp \
        -p 8082:8000 \
        -e APP_BIND=0.0.0.0:8000 \
        -e APP_WORK_CLASS=sync \
        -e APP_WORKS=5 \
        -e TIMEOUT=120 \
        -e APP_LOG_LEVEL=INFO \
        -e CONFIG_AGENT_SERVER="http://dmp-management-test2.mypaas.com.cn" \
        -e CONFIG_AGENT_CLIENT_CODE="jzq_dmp" \
        -e CONFIG_AGENT_CLIENT_KEY=19fb18394faca43a \
        -e CONFIG_AGENT_APP_CODE="dmp" \
        -v /mic/code/dmp:/home/<USER>/webapp \
        registry.cn-hangzhou.aliyuncs.com/mic-dmp/dmp-libbase:beta
```

- 运行dmp-admin站点(要求8001端口)

```
docker run -d --restart=always --net=host --name dmp-admin -p 8001:8000 \
    -e APP_BIND=0.0.0.0:8000 \
    -e APP_WORKS=5 \
    -e APP_LOG_LEVEL=DEBUG \
    -e APP_WORK_CLASS=gevent \
    -v <project root>/dmp-admin:/home/<USER>/webapp \
    registry.cn-hangzhou.aliyuncs.com/mypaas/dmp:latest

```

`<project root>` 替换成你本地应用根目录的全路径, 例如: windows 下工作目录 /c/Users/<USER>/dev/

## 三、dmp镜像运行环境变量说明

- `APP_BIND`: gunicorn listen bind, 例如 0.0.0.0:8000
- `APP_WORKS`: gunicorn 进程数, 推荐(cpu核x2)+1
- `APP_LOG_LEVEL`: 日志级别(DEBUG,INFO,WARN,ERROR), 生产环境使用ERROR
- `TIMEOUT`: python gunicorn server 超时时长(秒)

### 镜像更新说明

1. 拉取最新镜像: `docker pull registry-internal.cn-hangzhou.aliyuncs.com/mypaas/dmp:latest`

### 所用到的镜像

- **Python Web 运行镜像**

公网地址:registry.cn-hangzhou.aliyuncs.com/mypaas/dmp

阿里云内网:registry-internal.cn-hangzhou.aliyuncs.com/mypaas/dmp

- **调度组件镜像**

公网地址:registry.cn-hangzhou.aliyuncs.com/mypaas/rundeck

阿里云内网:registry-internal.cn-hangzhou.aliyuncs.com/mypaas/rundeck

- **流程引擎运行镜像**

公网地址:registry.cn-hangzhou.aliyuncs.com/mypaas/flow


阿里云内网:registry-internal.cn-hangzhou.aliyuncs.com/mypaas/flow



### dmp-flow 镜像地址
 # 外部
 docker pull registry.cn-hangzhou.aliyuncs.com/mypaas/flow:latest
 docker run -d --name flow \
	-v /c/Users/<USER>/dmp-flow:/dmp-flow \
	-v /c/Users/<USER>/datax:/datax \
	registry.cn-hangzhou.aliyuncs.com/mypaas/flow:latest

## 四、开发相关类库、doc技术说明

1. 不同数据源之间数据迁移使用阿里云的 datax
文档：https://help.aliyun.com/document_detail/28291.html
datax 使用java 同步

2. 文件对象存储使用 oss

文档：https://help.aliyun.com/document_detail/32026.html?spm=5176.87240.400427.48.RdJW7b
SDK：http://aliyun-oss-python-sdk.readthedocs.io/en/stable/

## 五、接口性能分析

1. 运行接口代码

`pyinstrument  --renderer=json app.py > profile.json`

2.  上传显示profile

https://python-flame-chart.netlify.com/


## 构建
 
