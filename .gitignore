*.py[cod]
*.orig
.DS_Store
# C extensions
*.so

# Packages
*.egg
*.egg-info
*.egg-info/
build
eggs
parts
var
sdist
develop-eggs
.installed.cfg
lib
lib64
MANIFEST

# Installer logs
pip-log.txt
npm-debug.log
pip-selfcheck.json

# Unit test / coverage reports
.coverage
.tox
nosetests.xml
htmlcov
.cache
coverage.xml
testcase

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# SQLite
test_exp_framework

# npm
node_modules/

# dolphin
.directory
libpeerconnection.log

# setuptools
dist

# IDE Files
atlassian-ide-plugin.xml
.idea/
*.swp
*.kate-swp
.ropeproject/
launch.json
settings.json

# Python3 Venv Files
.venv/
bin/
include/
lib/
pyvenv.cfg
share/
venv/
runtime/
modules/data_center/test.py
# Cython
*.c
*.env
*__pycache__/
nginx/dmp.conf
nginx/nginx.conf
dmp.conf
nginx.conf
test.py
.tags_sorted_by_file
.tags
app.config
.vscode
license.xml
licenseDog.xml
dmplib/
dmplib
.pytest_cache/
/luke.py

prom-metrics-dir/
*.db
.mypy_cache/
.sonar*
.scannerwork/
*.sqlite*
celery_debug.py
script
.design_assets
.python-version