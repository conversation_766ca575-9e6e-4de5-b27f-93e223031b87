创建虚拟环境dd
---------------
  
## 动机 123   

很多时候需要同时部署多个版本并行开发或测试, 单纯的运行docker容器很难满足。
通过该虚拟环境方案可以为每个版本创建一个单独的服务器包括运行环境(lxc)。 

## 为分支sp-dev创建新的虚拟环境

**虚拟机的宿主机ip**: `**********` 
 f/v4.7.5-redirect-params-reconsitution 

### 步骤  
    1. 创建一个虚拟服务器
    
       例如: 在宿主机上运行命令创建名为`zhangsan`的虚拟服务器
    
       `sudo lxc copy ubuntu01/dmp zhangsan`

       > 查看ip等信息 
       `sudo lxc info zhangsan`

       假如看到的虚拟服务器ip为: ***********
[vm.md](vm.md)
    2. 在宿主机上创建一个代理指向虚拟服务器要使用的端口 

       可以通过修改配置文件(/etc/gobetween/gobetween.toml)或调用接口来设置。

       例如, 创建名为zhangsan的代理, 应用端口为8000, 通过宿主机的18000对外公开.

       > POST `http://**********:8888/servers/zhangsan`
       -d
       ```
       {
            "protocol": "tcp",
            "bind": "0.0.0.0:18001",
            "discovery": {
                "kind": "static",
                "static_list": ["***********:8000"]
            }
       }
       ```

    3. 在代码仓库根目录下创建.drone.yml文件, 文件模板参考dmp项目根目录下的.drone.yml文件,
      具体说明看官方文档[drone](https://github.com/drone/drone).

      例如:

      在.drone.yml文件中添加一个名为`publish-sp-dev`的pipeline

      ```.drone.yml
      publish-sp-dev:
        image: drillster/drone-rsync:latest
        hosts: [***********]
        user: ops
        source: .
        recursive: true
        target: /data/www/dmp
        exclude: [".env", "app.config", "dmplib", ".git"]
        script:
         - echo deploy
        secrets: [rsync_key]
        when:
          status: [ success ]
          event: [ push, deployment ]
          branch: sp-dev
      ```
    4. gitea平台添加项目仓库(公司的gitlab版本太低不支持)

       http://**********:3000

    5. 添加上游.

      `git remote add gitea git@**********:dmp/dmp.git`


    6. push分支到gitea触发构建

      `git push gitea sp-dev`


## 将某个分支发布到已存在的虚拟环境

   修改.drone.yml文件, 在发布的pipeline节点将hosts改为目标虚拟机环境ip

   0905 


