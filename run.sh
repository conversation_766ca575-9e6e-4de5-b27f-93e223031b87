#!/bin/bash
set -e
if [ "$DEPLOYMENT" == local ]
then
  echo local
else
  /dmp-agent/agent
fi
if [ $? == 0 ]
then
	if [ "$RUNC"x == celeryx ]
	then
    cd /home/<USER>/webapp
    celery -A app_celery worker --loglevel=INFO --concurrency=1 -n worker@%h
 	else
 	  cd /home/<USER>/webapp
 	  if [ "$FAST_ENABLE" == true ]
    then
      FastTracker_ConfigPath=FastTracker.json \
      FastTracker_Enable=${FAST_ENABLE} \
      FastTracker_EnvCode=${FAST_ENV_CODE:-prod} \
      FastTracker_ProductCode=${FAST_PRODUCT_CODE:-dmp} \
      FastTracker_AppCode=${FAST_APP_CODE} \
      fast-boot run-program \
      gunicorn app:__hug_wsgi__ --reload --timeout=$TIMEOUT --bind=$APP_BIND -w $APP_WORKS --max-requests 500 --log-level $APP_LOG_LEVEL -k gevent --pythonpath /home/<USER>/webapp
    else
      gunicorn app:__hug_wsgi__ --reload --timeout=$TIMEOUT --bind=$APP_BIND -w $APP_WORKS --max-requests 500 --log-level $APP_LOG_LEVEL -k gevent --pythonpath /home/<USER>/webapp
    fi
	fi
fi
