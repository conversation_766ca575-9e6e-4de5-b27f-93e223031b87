# pylint: skip-file
import logging, sys
sys.path.append('../')

from copy import deepcopy
from dmplib.db.mysql_wrapper import SimpleMysql
from gray_tools.configs import config, first_split, config_db_name, rundeck_db_name, gray_rundeck_db_name, gray_codes

handlers = [logging.FileHandler('gray.log', mode='a', encoding='utf-8'), logging.StreamHandler(sys.stdout)]
logging.basicConfig(
    level=logging.INFO, handlers=handlers, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

common_group_path = ['DMP-Sysevent', 'dmp-proc']

def get_gray_codes_from_db(db: SimpleMysql):
    rows = db.query(f'select code from gray_project')
    codes = [row.get('code') for row in rows]
    return ','.join(codes)

def get_gray_codes_from_config(_):
    return gray_codes

get_gray_codes = get_gray_codes_from_db

def get_scheduled_execution(db: SimpleMysql, codes):
    if first_split:
        all_group_path = f"{codes},{','.join(common_group_path)}"
    else:
        all_group_path = codes

    logging.info(f"获取租户下需要拆分的路径: {all_group_path}")
    rv = input(f"第一次拆分参数<{first_split}>, <{rundeck_db_name}> -> <{gray_rundeck_db_name}>, 确认执行(0/1): ")
    if rv not in ["0", "1"]:
        logging.info("输入错误，退出执行")
        exit(0)
    if rv == "0":
        logging.info("退出执行")
        exit(0)

    sql = f'''
      select id,version,arg_string,date_created,day_of_month,day_of_week,description,do_nodedispatch,exec_count,execution_enabled,filter,group_path,hour,job_name,last_updated,log_output_threshold,log_output_threshold_action,log_output_threshold_status,loglevel,minute,month,multiple_executions,next_execution,node_exclude,node_exclude_name,node_exclude_os_arch,node_exclude_os_family,node_exclude_os_name,node_exclude_os_version,node_exclude_precedence,node_exclude_tags,node_include,node_include_name,node_include_os_arch,node_include_os_family,node_include_os_name,node_include_os_version,node_include_tags,node_keepgoing,node_rank_attribute,node_rank_order_ascending,node_threadcount,nodes_selected_by_default,orchestrator_id,project,retry,schedule_enabled,scheduled,seconds,server_nodeuuid,timeout,total_time,rduser,user_role_list,uuid,workflow_id,year 
      from scheduled_execution 
      where find_in_set(group_path, '{all_group_path}') > 0
    '''
    rows = db.query(sql)
    logging.info(f"获取schedule_executions<{len(rows)}>")
    return rows

def set_foreign_key_checks(db, val):
    db.exec_sql(f'set FOREIGN_KEY_CHECKS={val}')


def get_workflows(db: SimpleMysql, scheduled_executions):
    workflow_ids = [item.get('workflow_id') for item in scheduled_executions]

    sql = f'''
      select id,version,keepgoing,strategy,threadcount
      from workflow 
      where id in %s;
    '''
    workflows = db.query(sql, params=[workflow_ids])
    logging.info(f"获取workflows<{len(workflows)}>")
    return workflows

def get_workflow_workflow_steps(db: SimpleMysql, workflows):
    workflow_commands_ids = [item.get('id') for item in workflows]

    sql = f'''
      select workflow_commands_id,workflow_step_id,commands_idx 
      from workflow_workflow_step 
      where workflow_commands_id in %s;
    '''
    workflow_workflow_steps = db.query(sql, params=[workflow_commands_ids])
    logging.info(f"获取workflow_workflow_steps<{len(workflow_workflow_steps)}>")
    return workflow_workflow_steps

def get_workflow_steps(db: SimpleMysql, workflow_workflow_step):
    workflow_step_ids = [item.get('workflow_step_id') for item in workflow_workflow_step]
    sql = f'''
      select id,version,description,error_handler_id,keepgoing_on_success,class,adhoc_execution,adhoc_filepath,adhoc_local_string,adhoc_remote_string,arg_string,file_extension,interpreter_args_quoted,script_interpreter,job_group,job_name,node_filter,node_keepgoing,node_rank_attribute,node_rank_order_ascending,node_step,node_threadcount,json_data,type
      from workflow_step 
      where id in %s;
    '''
    workflow_steps = db.query(sql, params=[workflow_step_ids])
    logging.info(f"获取workflow_steps<{len(workflow_steps)}>")
    return workflow_steps

def merge_data(scheduled_executions, workflows, workflow_workflow_steps, workflow_steps):
    logging.info(f"合并数据")
    workflow_map = {
        item.get('id'): item
        for item in workflows
    }

    workflow_workflow_steps_map = dict()
    for item in workflow_workflow_steps:
        steps = workflow_workflow_steps_map.setdefault(item.get('workflow_commands_id'), [])
        steps.append(item)

    workflow_step_map = {
        item.get('id'): item
        for item in workflow_steps
    }

    for se in scheduled_executions:
        workflow_id = se.get('workflow_id')
        workflow = workflow_map.get(workflow_id)
        assert workflow
        se['workflow'] = workflow
        step_relations = workflow_workflow_steps_map.get(workflow_id)
        assert step_relations
        workflow['steps_relations'] = step_relations
        workflow['steps'] = []
        for item in step_relations:
            step_id = item.get('workflow_step_id')
            step = workflow_step_map.get(step_id)
            assert step
            workflow['steps'].append(step)

def insert_scheduled_execution(db, se):
    insert_sql = "insert into scheduled_execution (version,arg_string,date_created,day_of_month,day_of_week,description,do_nodedispatch,exec_count,execution_enabled,filter,group_path,hour,job_name,last_updated,log_output_threshold,log_output_threshold_action,log_output_threshold_status,loglevel,minute,month,multiple_executions,next_execution,node_exclude,node_exclude_name,node_exclude_os_arch,node_exclude_os_family,node_exclude_os_name,node_exclude_os_version,node_exclude_precedence,node_exclude_tags,node_include,node_include_name,node_include_os_arch,node_include_os_family,node_include_os_name,node_include_os_version,node_include_tags,node_keepgoing,node_rank_attribute,node_rank_order_ascending,node_threadcount,nodes_selected_by_default,orchestrator_id,project,retry,schedule_enabled,scheduled,seconds,server_nodeuuid,timeout,total_time,rduser,user_role_list,uuid,workflow_id,year) values (%(version)s,%(arg_string)s,%(date_created)s,%(day_of_month)s,%(day_of_week)s,%(description)s,%(do_nodedispatch)s,%(exec_count)s,%(execution_enabled)s,%(filter)s,%(group_path)s,%(hour)s,%(job_name)s,%(last_updated)s,%(log_output_threshold)s,%(log_output_threshold_action)s,%(log_output_threshold_status)s,%(loglevel)s,%(minute)s,%(month)s,%(multiple_executions)s,%(next_execution)s,%(node_exclude)s,%(node_exclude_name)s,%(node_exclude_os_arch)s,%(node_exclude_os_family)s,%(node_exclude_os_name)s,%(node_exclude_os_version)s,%(node_exclude_precedence)s,%(node_exclude_tags)s,%(node_include)s,%(node_include_name)s,%(node_include_os_arch)s,%(node_include_os_family)s,%(node_include_os_name)s,%(node_include_os_version)s,%(node_include_tags)s,%(node_keepgoing)s,%(node_rank_attribute)s,%(node_rank_order_ascending)s,%(node_threadcount)s,%(nodes_selected_by_default)s,%(orchestrator_id)s,%(project)s,%(retry)s,%(schedule_enabled)s,%(scheduled)s,%(seconds)s,%(server_nodeuuid)s,%(timeout)s,%(total_time)s,%(rduser)s,%(user_role_list)s,%(uuid)s,%(workflow_id)s,%(year)s)"
    return db.exec_sql(insert_sql, params=se, commit=False)

def insert_workflow(db, workflow):
    insert_sql = "insert into workflow (version,keepgoing,strategy,threadcount) values (%(version)s,%(keepgoing)s,%(strategy)s,%(threadcount)s)"
    return db.exec_sql(insert_sql, params=workflow, commit=False)

def insert_workflow_step(db, workflow_step):
    insert_sql = "insert into workflow_step (version,description,error_handler_id,keepgoing_on_success,class,adhoc_execution,adhoc_filepath,adhoc_local_string,adhoc_remote_string,arg_string,file_extension,interpreter_args_quoted,script_interpreter,job_group,job_name,node_filter,node_keepgoing,node_rank_attribute,node_rank_order_ascending,node_step,node_threadcount,json_data,type) values (%(version)s,%(description)s,%(error_handler_id)s,%(keepgoing_on_success)s,%(class)s,%(adhoc_execution)s,%(adhoc_filepath)s,%(adhoc_local_string)s,%(adhoc_remote_string)s,%(arg_string)s,%(file_extension)s,%(interpreter_args_quoted)s,%(script_interpreter)s,%(job_group)s,%(job_name)s,%(node_filter)s,%(node_keepgoing)s,%(node_rank_attribute)s,%(node_rank_order_ascending)s,%(node_step)s,%(node_threadcount)s,%(json_data)s,%(type)s)"
    return db.exec_sql(insert_sql, params=workflow_step, commit=False)

def insert_relation(db, relation):
    insert_sql = "insert into workflow_workflow_step (workflow_commands_id,workflow_step_id,commands_idx) values (%(workflow_commands_id)s, %(workflow_step_id)s, %(commands_idx)s)"
    return db.exec_sql(insert_sql, params=relation, commit=False)

def insert_all(db: SimpleMysql, scheduled_executions):
    logging.info("等待插入数据")
    se_count = 0
    wf_count = 0
    r_count = 0
    ws_count = 0
    for idx, se in enumerate(scheduled_executions):
        logging.info(f"scheduled_executions: ({idx}/{len(scheduled_executions)})")
        workflow = se['workflow']
        wf_count += insert_workflow(db, workflow)
        workflow_id = db.cur.lastrowid
        se['workflow_id'] = workflow_id
        se_count += insert_scheduled_execution(db, se)
        steps = workflow['steps']
        relations = workflow['steps_relations']
        for idx, step in enumerate(steps):
            relation = relations[idx]
            ws_count += insert_workflow_step(db, step)
            step_id = db.cur.lastrowid
            relation['workflow_commands_id'] = workflow_id
            relation['workflow_step_id'] = step_id
            r_count += insert_relation(db, relation)

    db.commit()
    logging.info(f"插入scheduled_execution数量<{se_count}>")
    logging.info(f"插入workflow数量<{wf_count}>")
    logging.info(f"插入workflow_workflow_step数量<{r_count}>")
    logging.info(f"插入workflow_step数量<{ws_count}>")


def use(db: SimpleMysql, database):
    db.exec_sql(f'use {database}')

def delete_all(db: SimpleMysql, scheduled_execution_backup):
    logging.info("等待删除数据")
    data = [item for item in scheduled_execution_backup if item['group_path'] not in common_group_path]
    se_ids = [item.get('id') for item in data]
    count = db.exec_sql("delete from scheduled_execution where id in %s", params=[se_ids], commit=False)
    logging.info(f"删除scheduled_execution数量<{count}>")
    workflow_ids = [item.get('workflow_id') for item in data]
    count = db.exec_sql("delete from workflow where id in %s", params=[workflow_ids], commit=False)
    logging.info(f"删除workflow数量<{count}>")
    count = db.exec_sql("delete from workflow_workflow_step where workflow_commands_id in %s", [workflow_ids], commit=False)
    logging.info(f"删除workflow_workflow_step数量<{count}>")
    step_ids = [
        step.get('id')
        for se in data
        for step in se['workflow']['steps']
    ]
    count = db.exec_sql("delete from workflow_step where id in %s", [step_ids], commit=False)
    logging.info(f"删除workflow_step数量<{count}>")
    db.commit()


def keep_run():
    rv = input(f"是否继续(0/1): ")
    if rv not in ["0", "1"]:
        logging.info("输入错误，退出执行")
        exit(0)
    if rv == "0":
        logging.info("退出执行")
        exit(0)

def run():
    with SimpleMysql(**config) as db:
        set_foreign_key_checks(db, 0)
        use(db, config_db_name)
        codes = get_gray_codes(db)
        use(db, rundeck_db_name)
        scheduled_executions = get_scheduled_execution(db, codes)
        if not scheduled_executions:
            return
        keep_run()

        workflows = get_workflows(db, scheduled_executions)
        workflow_workflow_steps = get_workflow_workflow_steps(db, workflows)
        workflow_steps = get_workflow_steps(db, workflow_workflow_steps)
        merge_data(scheduled_executions, workflows, workflow_workflow_steps, workflow_steps)
        scheduled_execution_backup = deepcopy(scheduled_executions)
        use(db, gray_rundeck_db_name)
        insert_all(db, scheduled_executions)
        use(db, rundeck_db_name)
        delete_all(db, scheduled_execution_backup)
        set_foreign_key_checks(db, 1)


if __name__ == '__main__':
    run()
