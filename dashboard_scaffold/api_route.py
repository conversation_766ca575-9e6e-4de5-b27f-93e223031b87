#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    @desc demo api route
    @date 2018年1月31日
    <AUTHOR>
"""
from dashboard_chart.services import dashboard_service
from dmplib.constants import ADMINISTRATORS_GROUP_ID, ADMIN_ROLE_ID
from dmplib.hug import APIWrapper
from urllib.parse import urlparse
from rbac.services import role_service
from user.models import UserLoginModel
from user.models import UserModel
from user.services import user_service
from dmplib.hug import g

api = APIWrapper(__name__)


@api.route.post('/register_developer')
def register_developer(request, **kwargs):
    """
    注册组件 开发者
    :param falcon.Request request:
    :param kwargs:
    :return:
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc + '/'
    g.code = 'devtool'
    g.account = kwargs.get('account')
    model = UserModel(**kwargs)
    if model.group_id is None:
        model.group_id = ADMINISTRATORS_GROUP_ID
    model.name = model.account
    user_service.add_user(model, is_register_developer=True, send_mail=False)
    # 默认超级管理员
    model.role_ids = [ADMIN_ROLE_ID]
    role_service.set_roles_for_user(model.id, model.role_ids)
    dashboard_service.init_dev_user_dashboards(**kwargs)
    return True, '注册成功', model.id


@api.route.post('/user/login')
def login_developer(request, response, **kwargs):
    """
    用户登录
    :param falcon.Request request:
    :param falcon.Response response:
    :param kwargs:
    :return:
    """
    if kwargs.get('tenant_code') is None:
        kwargs['tenant_code'] = 'devtool'
    g.code = kwargs.get('tenant_code')
    model = UserLoginModel(**kwargs)
    model.request = request
    model.response = response
    user_id = user_service.login(model)
    g.account = kwargs.get('account')
    if user_id:
        # 由于之前没有移动报告，此处要进行判断，若没有移动报告则增加一个移动报告
        dashboard_service.set_dashboard_to_cookie_by_create_user(kwargs.get('account'), response, request)

    return True, '登录成功', user_id
