#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/02/27.
"""
from collections import Iterable
from base.models import BaseModel


class ChartDataModel(BaseModel):
    def __init__(self, **kwargs):
        self.condition_relation = None
        self.conditions = None
        self.filters = None
        self.orders = None
        self.dims = None
        self.nums = None
        self.zaxis = None
        self.desires = None
        self.desired_value = None
        self.marklines = None
        self.dataset_id = None
        self.source = None
        self.chart_code = None
        self.data_logic_type_code = None
        self.id = None
        self.display_item = None
        self.dashboard_conditions = None
        self.dashboard_id = None
        self.filter_conditions = None
        # filters: 过滤条件, fields: 可见字段id列表
        self.data_permission_filter = {'filters': [], 'fields': []}
        super().__init__(**kwargs)
        self.nums = ChartDataModel.merge_nums_zaxis(self.nums, self.zaxis)

    @staticmethod
    def merge_nums_zaxis(nums, zaxis):
        """
        合并nums和zaxis两个属性数据为不同类型的nums
        :return:
        """
        result = []
        if nums is not None and isinstance(nums, Iterable):
            for num in nums:
                num['axis_type'] = 0
                result.append(num)
        if zaxis is not None and isinstance(zaxis, Iterable):
            for z in zaxis:
                z['axis_type'] = 1
                result.append(z)
        return result

    def rules(self):
        rules = super().rules()
        rules.append(('dashboard_id', 'string', {'max': 36, 'required': True}))
        return rules
