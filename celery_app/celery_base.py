# pylint: disable=unused-argument

import logging
import random
from celery import Task
from celery.signals import task_prerun, task_postrun, celeryd_after_setup, heartbeat_sent

from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib.redis import RedisCache

logger = logging.getLogger(__name__)

redis_cache = RedisCache()
DMP_UNACKED = 'dmp_unacked'


def get_unacked_id(flow_id):
    return 'dmp:celery:unacked:1:{flow_id}'.format(flow_id=flow_id)


# Not needed for RabbitMQ、
def restore_all_unacknowledged_messages():
    for key in redis_cache.keys(get_unacked_id('') + '*'):
        unacked = redis_cache.get_data(key)
        ip_module = __import__('app_celery')
        k = getattr(ip_module, unacked.get('func_name'))
        k.delay(**unacked.get('params'))


@task_prerun.connect()
def task_prerun_handle(signal, sender, **kwargs):
    g = _AppCtxGlobals()
    _app_ctx_stack.push(g)
    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)


@task_postrun.connect()
def task_postrun_handle(signal, sender, **kwargs):
    g = _app_ctx_stack.pop()
    # close all connections
    db_ctx = DBContext.instance(g)
    if db_ctx:
        db_ctx.close_all()


@celeryd_after_setup.connect()
def celeryd_after_setup_handle(**kwargs):
    from components.rundeck import CommonRunDeckScheduler
    from components.utils import hash_k
    from dmplib import config

    logger.info("注册心跳任务")
    queue_name = kwargs.get('conf').CELERY_DEFAULT_QUEUE
    job_id = f"dmp_heartbeat_{queue_name}_{hash_k(queue_name)}"

    def get_command():
        celery_task_name = "app_celery.heartbeat_task"
        cmd_template = config.get(
            "Rundeck.cmd_template_celery",
            "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
        )
        return '%s %s %s %s %s' % (cmd_template, "heartbeat", celery_task_name, job_id, queue_name)

    scheduler = CommonRunDeckScheduler()

    minute = random.randint(5, 10)
    schedule = f"0 */{minute} * ? * * *"
    command = get_command()

    try:
        scheduler.upset_job(job_id, f"心跳任务{queue_name}", schedule, command)
    except Exception as e:
        logger.error(f'系统任务注册失败: {e}')


@celeryd_after_setup.connect()
def celeryd_after_setup_reg_env_task(**kwargs):
    # 30秒锁
    lock_key = "dmp:reg:env_task"
    if redis_cache.set_nx_ex(lock_key, 1, ex=30, nx=True):
        logger.info("触发注册环境级celery调度任务")
        from components.reg_env_task import RegEnvTask
        RegEnvTask.reg()


class BaseTask(Task):
    def run(self, *args, **kwargs):
        pass

    def on_success(self, retval, task_id, args, kwargs):
        logger.info('Task %s: success returned with progress: %s', task_id, retval)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logger.info(u'Task %s: failure returned', task_id)
