#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/21.
"""
import os
import sys
from loguru import logger

from celery import Celery  # pylint: disable=import-error
from raven.contrib.celery import register_signal, register_logger_signal
from raven import Client
from dmplib import config
from dmplib.utils.logs import init_logging  # noqa: E402
from dmplib.redis import redis_mode
from dmplib.constants import REDIS_SENTINEL, REDIS_CLUSTER


def _regist_sentry(dns):

    client = Client(dns)

    # register  a custom filter to filter out duplicate logs
    register_logger_signal(client)

    init_logging()

    # hook into the Celery error handler
    register_signal(client)

    # The register_signal function can also take an optional argument
    # `ignore_expected` which causes exception classes specified in Task.throws
    # to be ignored
    register_signal(client, ignore_expected=True)


class DMPCelery(Celery):
    def on_configure(self):  # pylint:disable=R0201, method-hidden
        # 注册sentry, https://docs.sentry.io/clients/python/integrations/celery/
        sentry_dsn = config.get('Log.sentry_dsn')
        log_handle = config.get('Log.handler', '')
        if not sentry_dsn or 'sentry' not in log_handle.split(','):
            return
        _regist_sentry(sentry_dsn)


def get_backend(db=5, is_result=False):
    if config.get('App.celery_broker') == 'rabbitmq' or redis_mode() == REDIS_CLUSTER:
        backend = "{transport}://{user}:{password}@{host}:{port}/{vhost}".format(
            transport='amqp' if not is_result else 'rpc',
            user=config.get('RabbitMQ.user'),
            password=config.get('RabbitMQ.password'),
            host=config.get('RabbitMQ.host'),
            port=config.get('RabbitMQ.port'),
            vhost=config.get('RabbitMQ.vhost')
        )
    else:
        if redis_mode() == REDIS_SENTINEL:
            backend = "sentinel://{username}:{password}@{host}:{port}/{db}".format(
                password=config.get('Redis.password') or '',
                username='',
                host=config.get('Redis.host'),
                port=config.get('Redis.port'),
                db=db,
            )
        else:
            backend = 'redis://{username}:{password}@{host}:{port}/{db}'.format(
                password=config.get('Redis.password') or '',
                username=config.get('Redis.username') or '',
                host=config.get('Redis.host'),
                port=config.get('Redis.port'),
                db=db,
            )
    return backend


class CeleryTask:
    # pylint:disable=unused-argument
    def __init__(
        self, name: None = None, backend: None = None, broker: None = None, **kwargs
    ) -> None:  # pylint:disable=unused-argument
        self.name = name or 'dmp_celery'
        self.backend = get_backend(config.get('Redis.celery_db') or 5, True)
        self.broker = backend or get_backend(config.get('Redis.celery_db') or 5)
        self._celery = None
        self.queue_name = kwargs.get('queue_name')

    @property
    def celery(self) -> DMPCelery:
        if self._celery:
            return self._celery
        self._celery = DMPCelery(self.name, backend=self.backend, broker=self.broker)
        broker_transport_options = {'visibility_timeout': 18000, "confirm_publish": True, "worker_max_tasks_per_child": 200}
        result_transport_options = {}
        if config.get('App.celery_broker') != 'rabbitmq' and redis_mode() == REDIS_SENTINEL:
            svc_name = config.get("Redis.svc_name") or "mymaster"
            broker_transport_options.update({'master_name': svc_name})
            result_transport_options.update({'master_name': svc_name})
        self._celery.conf.update(
            CELERY_REDIS_SCHEDULER_URL=self.backend,
            CELERY_IGNORE_RESULT=True,
            CELERYD_MAX_TASKS_PER_CHILD=200,
            BROKER_TRANSPORT_OPTIONS=broker_transport_options,
            CELERY_RESULT_BACKEND_TRANSPORT_OPTIONS=result_transport_options,
        )

        default_queue_name = self.queue_name or os.environ.get('CELERY_DEFAULT_QUEUE')
        if default_queue_name:
            self._celery.conf.update(CELERY_DEFAULT_QUEUE=default_queue_name)

        try:
            from pympler import asizeof

            # body大于10M则丢掉
            send_task_message = self._celery.amqp.send_task_message

            def new_send_task_message(producer, name, message, *args, **kwargs):
                size = asizeof.asizeof(message)/1024/1024
                logger.info(f"current celery message size: {size}M")
                limit = float(config.get('RabbitMQ.body_limit') or 10)
                if size > limit:
                    logger.error(f"current celery message size: {size}M, message: {message}")
                    return
                return send_task_message(producer, name, message, *args, **kwargs)

            self._celery.amqp.send_task_message = new_send_task_message
        except Exception as e:
            logger.error(f"add celery body limit error: {e}")

        return self._celery


def get_task_id(module_name: str, task_name: str, project_code: str, flow_id: str) -> str:
    return 'dmp:{module_name}:{task_name}:{project_code}:{flow_id}'.format(
        module_name=module_name, task_name=task_name, project_code=project_code, flow_id=flow_id
    )


celery = CeleryTask().celery
