#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/12/7.
"""

from dmplib.saas.project import get_db


def get_cur_level_code(model):
    """
    获取当前层级编码
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = 'SELECT `%s` FROM `%s` WHERE `%s`=%%(level_id)s LIMIT 1;' % (
        model.table_level_code_field,
        model.table_name.strip("`"),
        model.table_level_id_field,
    )
    with get_db() as db:
        return db.query_scalar(sql, {'level_id': model.level_id})


def add_sequence(model):
    """
    增加序列
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = """INSERT INTO `level_sequence`(`table_name`,`level_id`,`attach_identify`,`max_sequence`)
             SELECT `table_name`,`level_id`,`attach_identify`,`max_sequence` FROM  (
                    SELECT %(table_name)s AS `table_name` ,%(level_id)s AS `level_id` 
                    ,%(attach_identify)s AS `attach_identify`,0 as `max_sequence`
                  ) AS res 
             WHERE NOT EXISTS(SELECT 1 FROM `level_sequence` 
                      WHERE `table_name`=%(table_name)s AND `level_id`=%(level_id)s 
                      AND `attach_identify`=%(attach_identify)s
                  )"""
    with get_db() as db:
        return db.exec_sql(
            sql, {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
        )


def increase_sequence(model):
    """
    递增序列
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    sql = """UPDATE `level_sequence` SET `max_sequence` = (select @max_sequence:=`max_sequence`+1)
             WHERE `table_name`=%(table_name)s AND level_id=%(level_id)s AND `attach_identify`=%(attach_identify)s"""
    condition = {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
    with get_db() as db:
        db.exec_sql('SET @max_sequence := 0;')
        db.exec_sql(sql, condition)
        cur_sequence = db.query_scalar('SELECT @max_sequence;')
        return cur_sequence
