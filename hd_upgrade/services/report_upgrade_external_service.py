"""
    HighData报表升级的model，请求api等公共对象
"""
# pylint: skip-file

import uuid
import logging
from enum import Enum, unique

from base.enums import DashboardType, DashboardTheme, DashboardPlatforms, ChartFilterInitiatorSouce, \
    HdUpgradeModuleList, HdUpgradeModuleNameList
from base.models import BaseModel
from base.repository import _parse_select
from dashboard_chart.dashboard_editor.editor.models import EditorBaseModel
from dmplib.hug import g
from dmplib.saas.project import get_db
from typing import Dict, List, Optional, Tuple, Union, Any, Iterable

from level_sequence.models import DashboardLevelSequenceModel
from hd_upgrade.api.hd_upgrade_api import HdUpgradeApi
from dmplib.db import mysql_wrapper

logger = logging.getLogger(__name__)


@unique
class HdDashboardType(Enum):
    """
    仪表板类型
    """
    DASHBOARD = 'dashboard'
    M_REPORT = 'm_report'


class DmpReportDb:
    @staticmethod
    def add_model(table_name, model, fields=None):
        """
        添加数据
        :param str table_name:
        :param base.models.BaseModel model:
        :param list fields:
        :return bool:
        """
        data = model.get_dict(fields)
        return DmpReportDb.add_data(table_name, data)

    @staticmethod
    def add_data(table_name, data):
        """
        添加数据
        :param str table_name:
        :param dict data:
        :return bool:
        """
        if not data:
            return False
        db = get_dmp_db()
        return db.insert(table_name, data) == 1

    @staticmethod
    def add_list_model(table_name, list_model, fields):
        """
        添加数据
        :param table_name:
        :param list_model:
        :param fields:
        :return:
        """
        list_data = []
        for model in list_model:
            if not isinstance(model, BaseModel):
                continue
            list_data.append(model.get_dict(fields))
        if not list_data:
            return False
        return DmpReportDb.add_list_data(table_name, list_data, fields)

    @staticmethod
    def add_list_data(table_name, list_data, fields):
        """
        添加多行数据
        :param table_name:
        :param list_data:
        :param fields:
        :return:
        """
        if not list_data or not isinstance(list_data, list):
            return False
        with get_dmp_db() as db:
            return db.insert_multi_data(table_name, list_data, fields) > 0

    @staticmethod
    def replace_list_data(table_name, list_data, fields, commit=True):
        """
        替换多行数据
        :param table_name:
        :param list_data:
        :param fields:
        :param commit:
        :return:
        """
        if not list_data or not isinstance(list_data, list):
            return False
        flag = False
        with get_dmp_db() as db:
            insert_list = []  # 由于插入的时候组件数量过大，插入会出现sql过大的情况，10条一批次
            i = 0
            for data in list_data:
                insert_list.append(data)
                if (i + 1) % 10 == 0:
                    flag = db.replace_multi_data(table=table_name, list_data=insert_list, fields=fields,
                                                 commit=commit) > 0
                    insert_list = []
                i += 1
            if len(insert_list) > 0:
                flag = db.replace_multi_data(table=table_name, list_data=insert_list, fields=fields, commit=commit)
            return flag

    @staticmethod
    def replace_list_model(table_name, list_model, fields):
        """
        添加数据
        :param table_name:
        :param list_model:
        :param fields:
        :return:
        """
        list_data = []
        for model in list_model:
            if not isinstance(model, BaseModel):
                continue
            list_data.append(model.get_dict(fields))
        if not list_data:
            return False

        return DmpReportDb.replace_list_data(table_name, list_data, fields)

    @staticmethod
    def data_is_exists(
            table_name: str, condition: Optional[Dict[str, Union[int, str]]] = None, exclude_condition: None = None
    ):
        """
        数据是否存在
        :param str table_name:
        :param dict condition:
        :param dict exclude_condition: 不包括的条件
        :return:
        """
        sql = 'SELECT 1 FROM `%s` ' % (table_name,)
        where = []
        params = {}
        if condition:
            for k, v in condition.items():
                where.append('`{c}` = %({c})s'.format(c=k))
                params[k] = v
        if exclude_condition:
            for k, v in exclude_condition.items():
                where.append('`{c}`<> %({c})s '.format(c=k))
                params[k] = v
        sql += (' WHERE ' + ' AND '.join(where)) if where else ''
        sql += ' LIMIT 1 '
        with get_dmp_db() as db:
            return db.query_scalar(sql, params)

    @staticmethod
    def get_data(
            table_name: str,
            conditions: Dict[str, str],
            fields: Optional[List[str]] = None,
            multi_row: Optional[bool] = None,
            order_by: Optional[List[Tuple[str, str]]] = None,
            from_config_db: bool = False,
            pagination: None = None,
    ) -> Union[Dict, None, List[Any]]:
        """
        获取表数据
        :param Pagination pagination:
        :param from_config_db: 是否从配置库查询
        :param str table_name: 表名
        :param dict conditions: 条件
        :param list fields: 列名
        :param bool multi_row: 是否返回多行，默认返回单行
        :param list order_by: 排序字段
        :return:
        """
        sql = 'SELECT {col} FROM {table_name} ' ''.format(
            col='`' + '`,`'.join(fields) + '`' if fields else '*', table_name=table_name
        )
        if conditions and isinstance(conditions, dict):
            sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
        if order_by and isinstance(order_by, list):
            sql += ' ORDER BY ' + ','.join([ob[0] + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)])
        db = get_dmp_db(from_config_db)
        if multi_row:
            if pagination is not None:
                sql += 'LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)

    @staticmethod
    def get_list(
            table_name: str,
            conditions: Dict[str, str],
            fields: Optional[List[str]] = None,
            order_by: None = None,
            from_config_db: bool = False,
            limit: Optional[int] = None,
            **kwargs,
    ) -> List[Dict[str, Union[int, str]]]:
        """
        查询数据列表
        :param str table_name: 表名
        :param dict conditions: 查询条件
        :param str|list fields: 查询的字段
        :param str|list order_by: 排序
        :param from_config_db: 是否从配置库查询
        :param str limit: limit 限制记录数
        :param list page: 分页
        :param str join: 连表
        :param str|list group: 分组字段
        :param str having: 分组条件
        :return: list
        """
        join = kwargs.get('join')
        group = kwargs.get('group')
        having = kwargs.get('having')
        page = kwargs.get('page')
        db = get_dmp_db()
        sql = _parse_select(
            table_name, conditions, fields, order_by, limit=limit, page=page, join=join, group=group, having=having
        )
        return db.query(sql, conditions)

    @staticmethod
    def get_count(
            table_name: str, condition: Optional[Dict[str, Union[int, str]]] = None, exclude_condition: None = None
    ):
        """
        数据是否存在
        :param str table_name:
        :param dict condition:
        :param dict exclude_condition: 不包括的条件
        :return:
        """
        sql = 'SELECT count(*) FROM `%s` ' % (table_name,)
        where = []
        params = {}
        if condition:
            for k, v in condition.items():
                where.append('`{c}` = %({c})s'.format(c=k))
                params[k] = v
        if exclude_condition:
            for k, v in exclude_condition.items():
                where.append('`{c}`<> %({c})s '.format(c=k))
                params[k] = v
        sql += (' WHERE ' + ' AND '.join(where)) if where else ''
        sql += ' LIMIT 1 '
        with get_dmp_db() as db:
            return db.query_scalar(sql, params)

    @staticmethod
    def delete_data(
            table_name, condition=None, commit=True):
        """
        数据删除
        :param str table_name:
        :param dict condition:
        :param commit: 不包括的条件
        :return:
        """
        with get_dmp_db() as db:
            return db.delete(table_name, condition, commit)

    @staticmethod
    def exec_sql(sql, params=None):
        """
        执行SQL
        :param sql:
        :param params:
        :return:
        """
        with get_dmp_db() as db:
            return db.exec_sql(sql, params)

    @staticmethod
    def add_sequence(model):
        """
        增加序列
        :param level_sequence.models.LevelSequenceBaseModel model:
        :return:
        """
        sql = """INSERT INTO `level_sequence`(`table_name`,`level_id`,`attach_identify`,`max_sequence`)
                 SELECT `table_name`,`level_id`,`attach_identify`,`max_sequence` FROM  (
                        SELECT %(table_name)s AS `table_name` ,%(level_id)s AS `level_id`
                        ,%(attach_identify)s AS `attach_identify`,0 as `max_sequence`
                      ) AS res
                 WHERE NOT EXISTS(SELECT 1 FROM `level_sequence`
                          WHERE `table_name`=%(table_name)s AND `level_id`=%(level_id)s
                          AND `attach_identify`=%(attach_identify)s
                      )"""
        with get_dmp_db() as db:
            return db.exec_sql(
                sql,
                {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
            )

    @staticmethod
    def increase_sequence(model):
        """
        递增序列
        :param level_sequence.models.LevelSequenceBaseModel model:
        :return:
        """
        sql = """UPDATE `level_sequence` SET `max_sequence` = (select @max_sequence:=`max_sequence`+1)
                 WHERE `table_name`=%(table_name)s AND level_id=%(level_id)s AND `attach_identify`=%(attach_identify)s"""
        condition = {'table_name': model.table_name, 'level_id': model.level_id,
                     'attach_identify': model.attach_identify}
        with get_dmp_db() as db:
            db.exec_sql('SET @max_sequence := 0;')
            db.exec_sql(sql, condition)
            cur_sequence = db.query_scalar('SELECT @max_sequence;')
            return cur_sequence

    @staticmethod
    def generate_hd_level_code(model, separator=None):
        """
        生成层级编码
        :param level_sequence.models.LevelSequenceBaseModel model:
        :param separator: default "-"
        :return:
        """
        model.validate()
        if not DmpReportDb.data_is_exists(
                'level_sequence',
                {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify},
        ):
            DmpReportDb.add_sequence(model)
        cur_sequence = str(DmpReportDb.increase_sequence(model)).zfill(model.unit_code_length) + (separator or '-')
        cur_level_code = DmpReportDb.get_cur_level_code(model) or ''
        return cur_level_code + cur_sequence

    @staticmethod
    def get_cur_level_code(model):
        """
        获取当前层级编码
        :param level_sequence.models.LevelSequenceBaseModel model:
        :return:
        """
        sql = 'SELECT `%s` FROM `%s` WHERE `%s`=%%(level_id)s LIMIT 1;' % (
            model.table_level_code_field,
            model.table_name,
            model.table_level_id_field,
        )
        with get_dmp_db() as db:
            return db.query_scalar(sql, {'level_id': model.level_id})

    @staticmethod
    def get_level_max_sequence(model):
        """
        获取某条记录的序列值
        :param level_sequence.models.LevelSequenceBaseModel model:
        :return:
        """
        sql = 'SELECT `max_sequence` FROM `level_sequence` ' \
              'WHERE `table_name`=%(table_name)s AND level_id=%(level_id)s ' \
              'AND `attach_identify`=%(attach_identify)s LIMIT 1;'
        condition = {'table_name': model.table_name, 'level_id': model.level_id,
                     'attach_identify': model.attach_identify}
        with get_dmp_db() as db:
            return db.query_scalar(sql, condition)

    @staticmethod
    def generate_dashboard_level_code(parent_id=None):
        """
        根据父级组生成层级编码
        :param parent_id:
        :return:
        """
        return DmpReportDb.generate_hd_level_code(DashboardLevelSequenceModel(level_id=parent_id))

    @staticmethod
    def generate_code(type=1):
        """
        生成业务代码
        :param type: 1 为获取业务代码code
        :return:
        """
        if type:
            return uuid.uuid4().__str__().replace('-', '')
        return uuid.uuid4().__str__()

    @staticmethod
    def update_increase_sequence(model, val: int = 1):
        """
        更新level_code序列，按指定差额写入或更新记录
        :param level_sequence.models.LevelSequenceBaseModel model:
        :param val: 指定差额值
        :return:
        """
        sql = f"""UPDATE `level_sequence` SET `max_sequence` = (select @max_sequence:=`max_sequence`+{val}) 
        WHERE `table_name`=%(table_name)s AND level_id=%(level_id)s AND `attach_identify`=%(attach_identify)s"""
        condition = {'table_name': model.table_name, 'level_id': model.level_id,
                     'attach_identify': model.attach_identify}
        with get_dmp_db() as db:
            db.exec_sql('SET @max_sequence := 0;')
            db.exec_sql(sql, condition)
            cur_sequence = db.query_scalar('SELECT @max_sequence;')
            return cur_sequence

    @staticmethod
    def assign_add_sequence(model, val: int = 1):
        """
        按指定sequence增加序列
        :param level_sequence.models.LevelSequenceBaseModel model:
        :param val: 指定序列值
        :return:
        """
        sql = f"""INSERT INTO `level_sequence`(`table_name`,`level_id`,`attach_identify`,`max_sequence`)
                     SELECT `table_name`,`level_id`,`attach_identify`,`max_sequence` FROM  (
                            SELECT %(table_name)s AS `table_name` ,%(level_id)s AS `level_id`
                            ,%(attach_identify)s AS `attach_identify`,{val} as `max_sequence`
                          ) AS res
                     WHERE NOT EXISTS(SELECT 1 FROM `level_sequence`
                              WHERE `table_name`=%(table_name)s AND `level_id`=%(level_id)s
                              AND `attach_identify`=%(attach_identify)s
                          )"""
        with get_dmp_db() as db:
            return db.exec_sql(
                sql,
                {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify}
            )

    @staticmethod
    def update_level_code(model, val: int = 1):
        """
        更新层级编码
        :param level_sequence.models.LevelSequenceBaseModel model:
        :param val: 指定差额值
        :return:
        """
        model.validate()
        if not DmpReportDb.data_is_exists(
                'level_sequence',
                {'table_name': model.table_name, 'level_id': model.level_id, 'attach_identify': model.attach_identify},
        ):
            DmpReportDb.assign_add_sequence(model, val)

        return DmpReportDb.update_increase_sequence(model, val)


def get_dmp_db(from_config_db=False):
    if from_config_db:  # 从配置库取数
        return mysql_wrapper.get_db()
    else:
        return get_db(g.code)


class HdChartModel:
    pass


class DmpChartModel(BaseModel):
    __slots__ = [
        "id",
        "dashboard_id",
        "name",
        "chart_code",
        "chart_type",
        "content",
        "source",
        "dims",
        "nums",
        "zaxis",
        "comparisons",
        "map",
        "sort_method",
        "colours",
        "filters",
        "marklines",
        "refresh_rate",
        "display_item",
        "desired_value",
        "penetrates",
        "layers",
        "percentage",
        "style_type",
        "default_value",
        "layout",
        "layout_extend",
        "config",
        "chart_params",
        "column_order",
        "enable_subtotal",
        "enable_summary",
        "enable_subtotal_col",
        "enable_subtotal_col_summary",
        "enable_subtotal_row",
        "enable_subtotal_row_summary",
        "subtotal_row_summary_formula_mode",
        "is_highdata",
        "fixed_data_mode",
        "fixed_manual_value"
    ]

    def __init__(self, **kwargs):
        """
        单图
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.name = None
        self.chart_code = None
        self.chart_type = None
        self.content = None
        self.source = None
        self.dims = None
        self.comparisons = None
        self.nums = None
        self.zaxis = None
        self.desires = None
        self.map = None
        self.colours = None
        self.filters = None
        self.marklines = None
        self.sort_method = None
        self.refresh_rate = None
        self.display_item = None
        self.desired_value = None
        self.penetrates = None
        self.layers = None
        self.percentage = None
        self.style_type = None
        self.default_value = None
        self.layout = None
        self.layout_extend = None
        self.position = None
        self.level_code = None
        self.content = None
        self.config = None
        self.filter_config = None
        self.data_logic_type_code = None
        self.chart_params = None
        self.page_size = None
        self.column_order = None
        self.penetrate_relation = None
        self.penetrate_filter_relation = None
        self.enable_subtotal = 0
        self.enable_summary = 0
        self.enable_subtotal_col = 0
        self.enable_subtotal_col_summary = 0
        self.enable_subtotal_row = 0
        self.enable_subtotal_row_summary = 0
        self.subtotal_row_summary_formula_mode = None
        self.is_highdata = 0
        self.fixed_data_mode = None
        self.fixed_manual_value = None
        super().__init__(**kwargs)
        self.nums = self.merge_nums_zaxis(self.nums, self.zaxis)

    @staticmethod
    def merge_nums_zaxis(nums, zaxis):
        """
        合并nums和zaxis两个属性数据为不同类型的nums
        :return:
        """
        result = []
        if nums is not None and isinstance(nums, Iterable):
            for num in nums:
                num["axis_type"] = 0
                result.append(num)
        if zaxis is not None and isinstance(zaxis, Iterable):
            for z in zaxis:
                z["axis_type"] = 1
                result.append(z)
        return result

    def rules(self):
        rules = super().rules()
        rules.append((["id", "dashboard_id"], "string", {"max": 36}))
        return rules


class HdDashboardModel:
    pass


class DmpDashboardModel(BaseModel):
    __slots__ = [
        "id",
        "name",
        "layout",
        "platform",
        "background",
        "rank",
        "parent_id",
        "type",
        "description",
        "level_code",
        "cover",
        "is_multiple_screen",
        "type_selector",
        "biz_code",
        "theme",
        "is_show_mark_img",
        "terminal_type",
        "application_type",
        "main_external_subject_id",
        "external_subject_ids",
        "modified_on",
        "is_highdata",
        "erp_app_code",
        "line_height",
    ]

    def __init__(self, **kwargs) -> None:
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.name = None
        self.layout = None
        self.background = None
        self.rank = 0
        self.parent_id = None
        self.type = None
        self.level_code = None
        self.description = None
        self.cover = None
        self.is_multiple_screen = 0
        self.charts = None
        self.type_selector = 1
        self.selectors = None
        self.biz_code = None
        self.scale_mode = None
        self.platform = DashboardPlatforms.PC.value
        self.created_by = None
        self.modified_by = None
        self.refresh_rate = None
        self.theme = DashboardTheme.TechBlue.value
        self.create_type = None
        self.new_layout_type = None
        self.grid_padding = None
        self.distribute_type = None
        self.is_show_mark_img = 0
        self.terminal_type = ""
        self.application_type = 0
        self.main_external_subject_id = None
        self.external_subject_ids = ""
        self.modified_on = None
        self.is_highdata = 0
        self.erp_app_code = None
        self.line_height = 5
        super().__init__(**kwargs)

    def rules(self) -> List[Union[Tuple[str, str, Dict[str, int]], Tuple[str, str, Dict[str, List[str]]]]]:
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append(("name", "string", {"max": 45}))
        rules.append(("theme", "in_range", {"range": [e.value for e in DashboardTheme.__members__.values()]}))
        rules.append(("type", "in_range", {"range": [e.value for e in DashboardType.__members__.values()]}))
        rules.append(
            ("platform", "in_range", {"range": [item.value for item in DashboardPlatforms.__members__.values()]})
        )
        return rules


class HdChartConfigModel:
    pass


class DmpChartFitlerModel(EditorBaseModel):
    """筛选单图"""

    __slots__ = ["id", "dashboard_id", "chart_id", "dataset_field_id", "dataset_id", "filter_type", "available"]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.chart_id = None
        self.dataset_field_id = None
        self.dataset_id = None
        self.dashboard_id = None
        self.filter_type = None
        self.available = None
        self.initiator_source = ChartFilterInitiatorSouce.Datasetfield.value
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dashboard_filter_chart'

    def get_use_resource(self):
        if self.initiator_source == ChartFilterInitiatorSouce.Fixedvalue.value:
            return
        return {'dataset_field_ids': {self.dataset_field_id}, 'dataset_ids': {self.dataset_id}}


class DmpChartFilterRelation(EditorBaseModel):
    """单图之间的筛选关系"""

    __slots__ = ['id', 'filter_id', 'chart_responder_id', 'field_responder_id', 'dataset_responder_id', "dashboard_id",
                 "value_type"]

    def __init__(self, **kwargs):
        """
        :param kwargs:
        """
        self.id = None
        self.filter_id = None
        self.chart_responder_id = None
        self.field_responder_id = None
        self.dataset_responder_id = None
        self.dashboard_id = None
        self.value_type = ""
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dashboard_filter_chart_relation'


class ChartFitlerFixedValueModel(EditorBaseModel):
    """筛选单图固定值"""

    __slots__ = ["id", "dashboard_id", "chart_id", "name", "value_type", "identifier", "extra_data"]

    def __init__(self, **kwargs):
        """
        看板
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.chart_id = None
        self.name = None
        self.value_type = None
        self.identifier = None
        self.extra_data = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dashboard_filter_chart_fixed_value'


class VarRelationsModel(EditorBaseModel):
    __slots__ = [
        "id",
        "chart_initiator_id",
        "field_initiator_id",
        "dashboard_id",
        "var_id",
        "var_dataset_id",
        "initiator_type",
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.chart_initiator_id = None
        self.field_initiator_id = None
        self.dashboard_id = None
        self.var_id = None
        self.var_dataset_id = None
        self.initiator_type = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(
            (
                ['id', 'chart_initiator_id', 'dashboard_id', 'var_id', 'var_dataset_id'],
                'string',
                {'max': 36, 'required': True},
            )
        )
        return rules

    def get_table_name(self):
        return "dashboard_dataset_vars_relation"


class DmpDashboardFilterVarsRelation(EditorBaseModel):
    """报告筛选变量与字段关系表"""

    __slots__ = ['id', 'var_name', 'dashboard_id', 'data_type', 'related_dataset_field_id', "related_dataset_id"]

    def __init__(self, **kwargs):
        """
        :param kwargs:
        """
        self.id = None
        self.var_name = None
        self.dashboard_id = None
        self.data_type = None
        self.related_dataset_field_id = None
        self.related_dataset_id = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dashboard_filter_vars_relation_field'


class DmpDashboardValueSource(EditorBaseModel):
    """变量取值来源"""

    __slots__ = ['id', 'dashboard_id', 'value_source_name', 'value_source', 'value_identifier']

    def __init__(self, **kwargs):
        """
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.value_source_name = None
        self.value_source = None
        self.value_identifier = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dashboard_value_source'


class DmpDashboardVarsValueSourceRelation(EditorBaseModel):
    """报告变量取值来源绑定关系"""

    __slots__ = ['dashboard_id', 'var_id', 'value_source_id']

    def __init__(self, **kwargs):
        """
        :param kwargs:
        """
        self.dashboard_id = None
        self.var_id = None
        self.value_source_id = None
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dashboard_vars_value_source_relation'


class DmpChartDim(EditorBaseModel):
    """图表维度"""

    __slots__ = ['dashboard_chart_id', 'dim', 'dim_type', 'alias', 'id', "dashboard_id",
                 "display_format", "parent_id"]

    def __init__(self, **kwargs):
        """
        :param kwargs:
        """
        self.dashboard_chart_id = None
        self.dim = None
        self.dim_type = None
        self.alias = None
        self.id = None
        self.dashboard_id = None
        self.display_format = ""
        self.parent_id = ""
        super().__init__(**kwargs)

    def get_table_name(self):
        return 'dashboard_chart_dim'


class HdQueryApi:
    __slots__ = ['hd_api']

    def __init__(self, tenant_code, yzs_domain=None):
        self.hd_api = HdUpgradeApi(tenant_code, yzs_domain)

    def get_hd_list(
            self,
            table_name: str,
            conditions: Union[Dict[str, any], List[any]],
            fields: Optional[List[str]] = None,
            order_by: str = None,
            page=None,
            **kwargs
    ):
        """
        HighData数据包查询接口
        :param str table_name: 表名
        :param dict|list conditions: 查询条件，可以参考Yii2查询起操作符格式（列表）或哈希格式（字典）
        https://www.yiiframework.com/doc/guide/2.0/zh-cn/db-query-builder#operator-format
        :param str|list fields: 查询的字段
        :param str|list order_by: 排序，id desc, name asc
        :param list page: 分页
        :return: list
        """
        # 分组
        group = kwargs.get('group', list())
        # 每页数量
        page_size = kwargs.get('page_size', None)
        biz_params = {
            "conditions": conditions,
            "fields": fields,
            "order_by": order_by,
            "group": group,
            "page": page,
            "page_size": page_size,
        }
        return self.hd_api.query(table_name, biz_params)

    def get_hd_by_api(
            self,
            api_name: str,
            biz_params: Dict[str, str] = None
    ):
        """
        HighData数据包查询接口
        :param str api_name: 接口名称
        :param dict biz_params: 接口参数
        :return: list
        """
        return self.hd_api.query(api_name, biz_params)


class ReportInfo:
    """
    获取报表的数据信息
    包括表名，字段名
    """
    # 仪表板、移动报表数据信息
    REPORT_INFO_MAP = {
        HdUpgradeModuleList.DASHBOARD.value: {
            "name": HdUpgradeModuleNameList.DASHBOARD.value,
            "id_field": "dashboard_id",
            "name_field": "dashboard_name",
            "table_name": "dashboard",
            "chart_table_name": "dashboard_chart",
            # 门户和文件夹的报表类型，0：仪表板，1：移动报表
            "group_type": 0
        },
        HdUpgradeModuleList.M_REPORT.value: {
            "name": HdUpgradeModuleNameList.M_REPORT.value,
            "id_field": "m_report_id",
            "name_field": "m_report_name",
            "table_name": "m_report",
            "chart_table_name": "m_report_chart",
            "group_type": 1
        }
    }

    @staticmethod
    def get_report_info(report_type):
        report_info = dict()
        if report_type in ReportInfo.REPORT_INFO_MAP:
            report_info = ReportInfo.REPORT_INFO_MAP.get(report_type)
        return report_info

    @staticmethod
    def get_name(report_type):
        report_info = ReportInfo.get_report_info(report_type)
        return report_info.get("name")

    @staticmethod
    def get_id_field(report_type):
        report_info = ReportInfo.get_report_info(report_type)
        return report_info.get("id_field")

    @staticmethod
    def get_name_field(report_type):
        report_info = ReportInfo.get_report_info(report_type)
        return report_info.get("name_field")

    @staticmethod
    def get_table_name(report_type):
        report_info = ReportInfo.get_report_info(report_type)
        return report_info.get("table_name")

    @staticmethod
    def get_chart_table_name(report_type):
        report_info = ReportInfo.get_report_info(report_type)
        return report_info.get("chart_table_name")

    @staticmethod
    def get_group_type(report_type):
        report_info = ReportInfo.get_report_info(report_type)
        return report_info.get("group_type")
