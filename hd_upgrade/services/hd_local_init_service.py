#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import traceback
from itertools import groupby

from loguru import logger

from app_menu.models import ApplicationModel, FunctionModel
from app_menu.services import function_service
from app_menu.services.application_service import add_application, enable_application_and_sync_mip
from base import repository
from base.dmp_constant import TABLES_FOR_DELETE_DASHBOARD
from base.enums import DataSourceType, ThirdPartyAppCode, DashboardTypeAccessReleased, TemplateDataPublishWay, \
    ApplicationType, FunctionReportType, TenantFrom
from components.dateset_generate_col_name import generate_new_col_name
from components.db_engine_transform import DbTransformService
from dashboard_chart.models import ReleaseModel
from dashboard_chart.services import released_dashboard_service
from data_source.models import DataSourceModel
from dataset.models import DatasetModel
from dataset.services.dataset_define_service import add_dataset
from dmplib import config
from dmplib.constants import ADMINISTRATORS_ID
from dmplib.hug import g
from dmplib.utils.strings import seq_id, uniqid
from exports.services.biz_link_service import BizLinkService
from flow.services import user_sync_flow_service
from flow.services.user_sync_flow_service import import_flow_id, _add_import_flow_if_not_exists
from keywords.models import KeywordModel
from user_group.services.user_group_service import buildin_application_id


class HDLocalInit(object):

    def __init__(self, from_init='', app_code_list=None, init_1_5_app=None, others=None):
        self.app_code = '1401'
        self.app_level_code = '1000.1401'
        self.data_source_id = None
        self.user_dataset_id = None
        self.org_dataset_id = None
        self.init_1_5_app = init_1_5_app
        self.from_init = from_init
        self.app_code_list = [code.get('app_code') for code in app_code_list] if isinstance(app_code_list, list) else []
        self.app_list = app_code_list
        self.others = others

    def init(self):
        try:
            logging.info('开始初始化HD相关数据')
            # 初始化门户
            mobile_app_id = self.init_menu()
            # 大小屏挂到对应门户
            # self.dashboard_link_to_menu(pc_app_id, True)
            self.dashboard_link_to_menu(mobile_app_id, False)
            # 修改发布方式
            self.change_publish_way()
            # 初始化数据服务中心数据源
            self.data_source_id = self.init_datasource()
            # 初始化关键字
            self.init_keyword()
            # 初始化用户和组织数据集
            self.init_dataset()
            # 初始化用户渠道
            self.init_user_source()
            # 初始化增值功能权限
            # self.add_func()
            # 初始化云助手基础应用简讯需要的应用信息
            self.init_third_party_app()
            # 初始化 超级工作台服务商-产业建管 需要的应用信息 app_code=4
            self.init_cyjg_third_party_app()
            logging.info('HD相关数据初始化完成')
            return True
        except:
            logging.error('初始化失败：{}'.format(traceback.format_exc()))

    def __init_db_type(self):
        if self.from_init == "erpsaas":
            return "mysql"
        return "sqlserver"


    def init_datasource(self):
        #如果是运营平台直接调用开户，跳过初始化数据服务中心
        if self.init_1_5_app == 1:
            logging.info('1.5开户模式，跳过初始化数据服务中心')
            return None
        # 判断是否有数据服务中心数据源
        data_source_id = repository.get_data_scalar('data_source', {'app_level_code': self.app_level_code}, 'id')
        if data_source_id:
            logging.info('数据服务中心数据源已存在，跳过数据源初始化')
            return data_source_id
        data_source_id = "c5eea4aa-25b2-4ba1-b16c-4bbaedc5ff9a"
        conn_str = '{"is_master_local_db": 1, "AppCode": "1401", "AppId": null, "AppLevelCode": "1000.1401", "ConfigType": 2, "DataFrom": "MysoftErp", "Database": "", "DbRole": "", "DbType": ' + '"{db_type}"'.format(db_type=self.__init_db_type()) + ', "EnvironmentId": null, "FirstLevelCode": "1000", "IsErp": 0, "Port": null, "Pwd": "", "SaasLinkType": null, "Server": "", "SiteGroupKey": null, "Uid": ""}'
        data_source = {
            'id': data_source_id,
            'name': '数据服务中心数据源',
            'code': '1000_1401',
            'description': '',
            'type': DataSourceType.MysoftNewERP.value,
            'conn_str': conn_str,
            'is_buildin': 0,
            'db_type': self.__init_db_type(),
            'data_from': 'MysoftErp',
            'config_type': 2,
            'app_code': self.app_code,
            'app_level_code': self.app_level_code,
        }
        data_source_model = DataSourceModel(**data_source)
        repository.add_model('data_source', data_source_model)
        return data_source_id

    @staticmethod
    def init_dataset_run():
        from flow.services.flow_service import run_flow
        logging.info('开始触发数据集自动调度')
        from dataset.repositories.dataset_repository import get_not_directly_id_list
        dataset_list = get_not_directly_id_list()
        for dataset in dataset_list:
            try:
                run_flow(dataset.get('id'), queue_name=config.get('RabbitMQ.queue_name_flow', 'Flow'))
            except Exception as e:
                logging.error(str(e))
                continue
        logging.info('完成数据集自动调度')

    def init_keyword(self):
        keyword_name = ['本人', '我有权限的项目GUID', '我有权限的公司GUID']
        keyword_list = [
            {'keyword_name': '本人', 'sql_text': 'SELECT UserGUID FROM data_wide_mdm_MyUser WHERE UserCode= [key:本人账号]'},
            {'keyword_name': '我有权限的项目GUID', 'sql_text': """select ProjectId from vp_data_project p where 
p.ProjectId IN ( SELECT   ProjectId
                               FROM     vp_data_user2project
                               WHERE    UserGUID IN ( [key:本人] ) )
              OR EXISTS ( SELECT    *
                          FROM      data_wide_mdm_myUser myUser
                          WHERE     myUser.UserGUID IN ( [key:本人] )
                                    AND IsAdmin = 1 )"""},
            {'keyword_name': '我有权限的公司GUID', 'sql_text': """SELECT bu.BUGUID
FROM    data_wide_mdm_BusinessUnit bu
WHERE   BUType = 1
        AND IsEndCompany = 1
        AND ( bu.BUGUID IN ( SELECT  BUGUID
                                    FROM    vp_data_user2project
                                    WHERE   UserGUID IN ( [key:本人]) )
              OR EXISTS ( SELECT    *
                          FROM      data_wide_mdm_myUser myUser
                          WHERE     myUser.UserGUID IN ( [key:本人] )
                                    AND IsAdmin = 1 )
            )"""},
        ]
        if not self.data_source_id:
            logging.info('数据源没有初始化成功，跳过关键字初始化')
            return
        # 查询对应数据源是否有对应关键字
        data = repository.get_list('keyword', {'keyword_name': keyword_name, 'datasource_id': self.data_source_id}, ['keyword_name'])
        if data:
            logging.info('关键字已存在，跳过初始化')
            return
        model_list = []
        for item in keyword_list:
            item['id'] = seq_id()
            item['datasource_id'] = self.data_source_id
            item['keyword_type'] = 1
            item['data_type'] = '文本'
            item['is_system'] = 0
            model_list.append(KeywordModel(**item))
        if model_list:
            fields = list(keyword_list[0].keys())
            repository.add_list_model('keyword', model_list, fields)

    def init_dataset(self):
        try:
            if self.from_init != TenantFrom.ERPOP.value:
                logging.info('非OP模式不需要初始化用户相关数据集')
                return
            user_dataset_id = repository.get_data_scalar('dataset', {'name': '用户数据集'}, 'id')
            if user_dataset_id:
                logging.info('用户数据集已存在，跳过用户数据集初始化')
                self.user_dataset_id = user_dataset_id
            else:
                dataset_name = '用户数据集'
                sql = 'select UserGUID,UserCode,UserName,BUGUID,Email,MobilePhone,Password,IsDisabeld from data_wide_mdm_myuser'
                column = [
                    {'origin_col_name': 'UserGUID', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'UserGUID', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'UserCode', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'UserCode', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'UserName', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'UserName', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'BUGUID', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'BUGUID', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'Email', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'Email', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'MobilePhone', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'MobilePhone', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'Password', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'Password', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'IsDisabeld', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'IsDisabeld', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '度量', 'data_type': '数值'}
                ]
                schedule = '0 0 2 ? * * *'
                self.user_dataset_id = self.create_dataset(dataset_name, sql, column, schedule)
            org_dataset_id = repository.get_data_scalar('dataset', {'name': '组织数据集'}, 'id')
            if org_dataset_id:
                logging.info('组织数据集已存在，跳过组织数据集初始化')
                self.org_dataset_id = org_dataset_id
            else:
                dataset_name = '组织数据集'
                sql = 'select BUGUID,BUName,ParentGUID,HierarchyCode from data_wide_mdm_BusinessUnit'
                column = [
                    {'origin_col_name': 'BUGUID', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'BUGUID', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'BUName', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'BUName', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'ParentGUID', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'ParentGUID', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'},
                    {'origin_col_name': 'HierarchyCode', 'origin_table_id': None, 'origin_table_comment': None,
                     'origin_table_name': None, 'origin_table_alias_name': None, 'col_name': '',
                     'origin_field_type': None, 'alias_name': 'HierarchyCode', 'visible': 1, 'format': '', 'note': None,
                     'field_group': '维度', 'data_type': '字符串'}
                ]
                self.org_dataset_id = self.create_dataset(dataset_name, sql, column)
        except Exception as e:
            logging.error(f'初始化数据集异常')


    def create_dataset(self, dataset_name: str, sql: str, column: list, schedule='0 0 1 ? * * *'):
        dataset = {
            'connect_type': '', 'is_complex': 1, 'field': '', 'name': dataset_name, 'type': 'SQL', 'user_group_id': '', 'edit_mode': 'sql', 'id': seq_id(),
            'content': '', 'flow': {'schedule': schedule, 'depend_flow_id': '', 'status': '启用', 'state_trigger': 0},
            'use_cache': 0, 'cache_flow': {}, 'relation_content': {'nodeDataArray': [], 'linkDataArray': []}, 'filter_content': [], 'var_content': [],
        }
        content = {'data_source_id': self.data_source_id, 'sql': sql, 'tmp_table_name': uniqid("dataset_tmp_")}
        self._create_col_name(column, dataset.get('id'))
        dataset['field'] = column
        content.update({'create_table_sql': DbTransformService._build_create_table_sql(content.get('tmp_table_name'), column)})
        dataset['content'] = content
        dataset_id, table_name = add_dataset(DatasetModel(**dataset))
        repository.update('dataset', {'business_type': '用户同步'}, {'id': dataset_id})
        return dataset_id

    @staticmethod
    def _create_col_name(column: list, dataset_id: str):
        for col in column:
            col['col_name'] = generate_new_col_name(dataset_id, col.get('origin_col_name'))

    def init_user_source(self):
        if self.from_init != TenantFrom.ERPOP.value:
            logging.info('非OP模式不需要初始化用户相关数据集')
            return
        data = repository.get_one('user_source', {'data like': '%{}%'.format(self.user_dataset_id)}, ['id'])
        if data:
            logging.info('已存在用户同步渠道，跳过初始化')
            return
        user_source_id = seq_id()
        source_config = {"encrypt": "md5", "user_source_id": user_source_id, "source_type": "dataset"}
        source_config.update({"user": self.get_user_config(), 'user_group': self.get_org_config()})
        source_config = json.dumps(source_config)
        user_source = {'id': user_source_id, 'name': 'ERP渠道用户', 'data': source_config, 'type': 2, 'auto_sync': 1}
        repository.add_data('user_source', user_source)
        repository.delete_data('user_source', {'type': 1})
        import_user_id = import_flow_id(user_source)
        _add_import_flow_if_not_exists(user_source)
        repository.add_data("dataset_depend", {"source_dataset_id": user_source_id, "depend_id": import_user_id})

        self.add_default_group(user_source_id)
        self.add_default_admin_account(user_source_id)

        # 创建/更新依赖调度, 并执行调度
        user_sync_flow_service.create_or_update_flow(user_source, self.user_dataset_id, self.org_dataset_id, trigger_flow=True)

    def add_default_group(self, user_source_id):
        # 初始化一个默认的erp组织
        default_group_id = "11b11db4-e907-4f1f-8835-b9daab6e1f23"
        data = repository.get_one('user_group', {'id': default_group_id})
        if data:
            logging.info('已存在erp组织，跳过初始化默认erp组织')
            return
        try:
            from user_group.services.user_group_service import generate_group_code

            parent_id = "********-0000-0000-1000-************"
            code = generate_group_code(parent_id)
            repository.add_data(
                "user_group", {
                    "id": default_group_id,
                    "name": '集团',
                    "code": code,
                    "parent_id": parent_id,
                    "user_source_id": user_source_id,
                }
            )
        except Exception as e:
            logger.error('初始化默认erp组织失败，错误：%s' % str(e))

    def add_default_admin_account(self, user_source_id):
        # 初始化一个默认erp admin账号
        data = repository.get_one('user', {'account': 'admin'})
        if data:
            logging.info('已存在admin用户，跳过初始化默认admin账号')
            return
        try:
            default_group_id = "11b11db4-e907-4f1f-8835-b9daab6e1f23"
            user_id = seq_id()
            repository.add_data(
                "user", {
                    "id": user_id,
                    "name": '系统管理员',
                    "account": 'admin',
                    "pwd": 'e10adc3949ba59abbe56e057f20f883e',
                    "group_id": default_group_id,
                    "old_pwd": '1',
                    "email": '',
                    "add_mode": '自动',
                    "user_source_id": user_source_id,
                    "account_mode": 'SYNC'
                }
            )
            repository.add_data(
                "user_source_user", {
                    "id": seq_id(),
                    "user_source_id": user_source_id,
                    "user_id": user_id,
                    "name": '系统管理员',
                    "account": 'admin',
                    "pwd": 'e10adc3949ba59abbe56e057f20f883e',
                    "user_source_group_id": default_group_id,
                    "account_mode": 'SYNC'
                }
            )
            repository.add_data(
                "user_group_user", {
                    "id": seq_id(),
                    "user_id": user_id,
                    "group_id": default_group_id,
                    "user_source_id": user_source_id,
                }
            )
        except Exception as e:
            logger.error('初始化默认admin账号失败，错误：%s' % str(e))

    def get_user_config(self):
        user_fields = repository.get_list('dataset_field', {'dataset_id': self.user_dataset_id}, ['col_name', 'origin_col_name'])
        field_col_name_map = {user_field.get('origin_col_name'): user_field.get('col_name') for user_field in user_fields}
        user_field_map = {
            'name': 'UserName', 'id': 'UserGUID', 'account': 'UserCode', 'pwd': 'Password', 'group_id': 'BUGUID',
            'email': 'Email', 'mobile': 'MobilePhone', 'is_disabled': 'IsDisabeld'
        }
        user_config = {
            "source_dataset_id": self.user_dataset_id,
            "field_relation":
                {
                    "name": "", "id": "", "account": "", "pwd": "", "group_id": "", "email": "",
                    "mobile": "", "is_disabled": "", "account_mode": "", "user_source_group_id": ""
                }
        }
        field_relation = user_config['field_relation']
        for key in field_relation.keys():
            origin_col = user_field_map.get(key, '')
            col_name = field_col_name_map.get(origin_col, '')
            field_relation[key] = col_name
        return user_config

    def get_org_config(self):
        group_fields = repository.get_list('dataset_field', {'dataset_id': self.org_dataset_id}, ['col_name', 'origin_col_name'])
        field_col_name_map = {group_field.get('origin_col_name'): group_field.get('col_name') for group_field in group_fields}
        group_field_map = {
            'name': 'BUName', 'id': 'BUGUID', 'parent_id': 'ParentGUID', 'hierarchy': 'HierarchyCode'
        }
        group_config = {
            "source_dataset_id": self.org_dataset_id,
            "field_relation":
                {
                    "name": "", "id": "", "parent_id": "", "hierarchy": ""
                }
        }
        field_relation = group_config['field_relation']
        for key in field_relation.keys():
            origin_col = group_field_map.get(key, '')
            col_name = field_col_name_map.get(origin_col, '')
            field_relation[key] = col_name
        return group_config

    @staticmethod
    def init_menu():
        # 初始化移动门户
        mobile_app_id = HDLocalInit.init_mobile_application()
        return mobile_app_id
        # 模板库都有pc门户，不再创建默认pc门户
        # data = {'is_show_banner': 1, 'menu_display_type': 0, 'name': "PC门户", "description": "默认PC门户", 'nav_type': 0,
        #         'platform': "pc", 'theme': "深色"}
        # result = repository.get_data_scalar('application', {'name': data.get('name')}, 'id')
        # if result:
        #     logging.info('门户已存在，跳过门户初始化')
        #     return '', ''
        # model = ApplicationModel(**data)
        # pc_app_id = seq_id()
        # add_application(model, pc_app_id)
        # return pc_app_id, mobile_app_id

    @staticmethod
    def init_mobile_application():
        try:
            # 固定门户id
            app_id = "3a0a8579-3aac-e348-b3e7-2414d3d46077"
            result = repository.get_one('application', {'id': app_id}, ['id'])
            if result:
                logger.error(f"移动门户已存在，无需初始化 app_id: {app_id}")
                return
            mobile_application = {
                "platform": "mobile_screen",
                "nav_type": 3,
                "theme": "深色",
                "name": "移动门户",
                "description": "默认移动门户",
                "menu_display_type": 0,
                "is_show_banner": 1,
                "enable": 1
            }
            model = ApplicationModel(**mobile_application)
            add_application(model, app_id)
            logger.error(f"移动门户初始化成功！app_id: {app_id}")
            return app_id
        except Exception as e:
            logger.error("初始化移动门户错误，errs："+str(e))

    @staticmethod
    def add_func():
        logging.info('初始化数据填报模块')
        data = repository.get_one('project_value_added_func', {'project_code': g.code, 'func_code': 'manual_filling'}, from_config_db=True)
        if data:
            logging.info('数据填报权限已存在，跳出初始化')
            return
        func_dict = {'id': seq_id(), 'project_code': g.code, 'func_code': 'manual_filling'}
        return repository.add_data('project_value_added_func', func_dict, from_config_db=True)

    def init_third_party_app(self):
        """
        初始化云助手基础应用渠道
        初始化租户内简讯需要的应用信息
        :return:
        """
        try:
            if self.from_init == TenantFrom.ERPSAAS.value:
                logging.error('产业建管环境下不需要初始化云助手基础应用')
                return

            third_party_id = "fdb93f53-d846-11ed-96b8-02420a0c0011"
            third_party_app_id = "fdb93f53-d846-11ed-96b8-02420a0c0022"

            # 初始化cloud_apps
            HDLocalInit.init_yzs_cloud_app()

            third_party = {
                "id": third_party_id,
                "name": "云助手基础应用",
                "app_code": ThirdPartyAppCode.YZS_BUILTIN.value,
                "corp_id": g.code,
                "corp_secret": "",
                "user_secret": g.code,
                "is_system": 1
            }
            third_party_app = {
                    "id": third_party_app_id,
                    "third_party_id": third_party_id,
                    "app_id": "",
                    "app_secret": "",
                    "app_type": "2",
                    "name": "云助手基础应用-默认应用",
                    "msg_send_type": 0,
                    "app_service_type": 0,  # 应用模式
            }
            # 租户初始化第三方和应用记录
            repository.delete_data("third_party", {"id": third_party_id})
            repository.add_data("third_party", third_party)
            repository.delete_data("third_party_app", {"id": third_party_app_id})
            repository.add_data("third_party_app", third_party_app)
            logger.error("初始化云助手基础应用完成！")
        except Exception as e:
            logger.error("初始化云助手基础应用信息错误，errs：" + str(e))

    @staticmethod
    def init_yzs_cloud_app():
        cloud_app = repository.get_one(
            'cloud_apps', {'app_code': ThirdPartyAppCode.YZS_BUILTIN.value}, ['app_code'], from_config_db=True
        )
        if not cloud_app:
            data = {
                "app_id": "999812c0-dcf1-11ed-96b8-02420a0c0002",
                "app_code": ThirdPartyAppCode.YZS_BUILTIN.value,
                "app_name": "云助手基础应用",
                "api_host": "https://www.fdccloud.com",
                "is_mysoft_app": 1,
                "icon": "https://dmp-prod.oss-cn-hangzhou.aliyuncs.com/3a00110f-80e2-4659-8c0a-954a9fe63231.png",
                "channel_id": "1000",
                "channel_app_id": "3035",
                "channel_app_secret": "mysoft642e182f96763",
            }
            repository.add_data('cloud_apps', data, from_config_db=True)
            logger.error("初始化cloud_app 云助手基础应用渠道完成！")
        else:
            logger.error("cloud_app已存在云助手基础应用渠道！")

    def init_cyjg_third_party_app(self):
        """
        初始化 超级工作台服务商-产业建管 需要的应用信息
        初始化租户内简讯需要的应用信息
        :return:
        """
        try:
            if self.from_init != TenantFrom.ERPSAAS.value:
                logging.error('不是产业建管环境，不需要初始化超级工作台服务商-产业建管-默认应用')
                return

            third_party_id = "85a100db-2380-11ed-a6d0-02420a0c0011"
            third_party_app_id = "023857a5-2384-11ed-a6d0-02420a0c0022"

            # 初始化cloud_apps
            HDLocalInit.init_cyjg_cloud_app()

            third_party = {
                "id": third_party_id,
                "name": "超级工作台服务商-产业建管",
                "app_code": ThirdPartyAppCode.MYCYJG.value,
                "corp_id": "",
                "corp_secret": "",
                "user_secret": "",
                "is_system": 1
            }
            third_party_app = {
                "id": third_party_app_id,
                "third_party_id": third_party_id,
                "name": "超级工作台服务商-产业建管-默认应用",
                "app_id": "",
                "app_secret": "",
                "app_type": "2",
                "msg_send_type": 1,
                "app_service_type": 1,
            }
            # 租户初始化第三方和应用记录
            repository.delete_data("third_party", {"id": third_party_id})
            repository.add_data("third_party", third_party)
            repository.delete_data("third_party_app", {"id": third_party_app_id})
            repository.add_data("third_party_app", third_party_app)
            logger.error("初始化超级工作台服务商-产业建管应用完成！")
        except Exception as e:
            logger.error("初始化超级工作台服务商-产业建管应用信息错误，errs：" + str(e))

    @staticmethod
    def init_cyjg_cloud_app():
        cloud_app = repository.get_one(
            'cloud_apps', {'app_code': ThirdPartyAppCode.MYCYJG.value}, ['app_code'], from_config_db=True
        )
        if not cloud_app:
            data = {
                "app_id": seq_id(),
                "app_code": ThirdPartyAppCode.MYCYJG.value,
                "app_name": "明源产业建管",
                "api_host": "https://work.mypaas.com",
                "get_org_api": "api/party/erp?data2pull=department",
                "get_user_api": "api/party/erp?data2pull=user",
                "is_mysoft_app": 1,
                "icon": "https://dmp-prod.oss-cn-hangzhou.aliyuncs.com/3a034c96-468b-0c62-08b2-3106a85ccc2f.png",
                "channel_id": "1000",
                "channel_app_id": "10",
                "channel_app_secret": "7BC0AB8C-7669-40A3-B0A9-DB55F550B2DB",
            }
            repository.add_data('cloud_apps', data, from_config_db=True)
            logger.error("初始化cloud_app 明源产业建管渠道完成！")
        else:
            logger.error("cloud_app已存在明源产业建管应用渠道！")

    def init_dashboard(self):
        """
        deprecated 王斐确认方法已弃用，env_type逻辑不用调整
        :return:
        """
        env_type = config.get('Product.env_type') or ''
        if env_type.lower() not in ['erp', 'erp_saas']:
            logging.info('对应环境不需要处理报表！')
            return
        logging.info('初始化对应系统报告')
        if not self.app_code_list:
            logging.info('没有对应的所属系统参数,跳出报告初始化')
            return
        # 获取app_code对应的系统名称
        system_list = repository.get_column('erp_app', {'app_code': self.app_code_list}, ['application_name'], from_config_db=True)
        if not system_list:
            logging.info('没有获取到对应的系统,跳出报告初始化')
            return
        system_list = list(set(system_list))
        logging.info('需要初始化的系统报告：{}'.format(','.join(system_list)))
        # 获取根目录除开拥有系统文件夹的level_code
        level_code_list = repository.get_column('dashboard', {'name not': system_list, 'type': 'FOLDER', 'parent_id': ''}, ['level_code'])
        if not level_code_list:
            logging.info('没有获取到需要删除目录层级code,跳过报告清理')
            return
        logging.info('需要删除的目录level_code为：{}'.format(','.join(level_code_list)))
        dashboard_list = []
        # 循环删除对应目录下的所有文件
        for level_code in level_code_list:
            where = {'level_code like': '{}%'.format(level_code)}
            dashboard_id = repository.get_column('dashboard', where, ['id']) or []
            dashboard_list = dashboard_list + dashboard_id
        self._delete_dashboard(dashboard_list)
        logging.info('报告初始化完成')
        # 删除不需要的数据集
        self.delete_dataset()

    @staticmethod
    def _delete_dashboard(dashboard_list: list):
        if dashboard_list:
            logging.info('一共需要删除{}张报告'.format(len(dashboard_list)))
            for table_name in TABLES_FOR_DELETE_DASHBOARD:
                del_conditions = {"dashboard_id": dashboard_list}
                if table_name in ["dashboard"]:
                    del_conditions = {"id": dashboard_list}
                repository.delete_data(table_name, del_conditions)

    @staticmethod
    def delete_dataset():
        logging.info('开始初始化数据集')
        # 获取目前正在使用的数据集ID
        used_dataset_ids = repository.get_column('dashboard_chart', {'source !=': ''}, ['source']) or []
        used_dataset_ids = set(used_dataset_ids)
        # 获取所有的数据集ID
        all_dataset_ids = repository.get_column('dataset', {'type !=': 'FOLDER'}, ['id']) or []
        if not all_dataset_ids:
            logging.info('没有数据集信息，跳过数据集初始化')
            return
        delete_dataset_ids = list(set(all_dataset_ids).difference(used_dataset_ids))
        if not delete_dataset_ids:
            logging.info('没有需要删除的数据集，跳过数据集初始化')
        # 删除数据集
        repository.delete_data('dataset', {'id': delete_dataset_ids})
        delete_table = [
            'dataset_field', 'dataset_vars', 'dataset_field_include_vars', 'keyword_details',
            'dataset_used_table', 'dataset_operate_record', 'dataset_tables_collection', 'dataset_filter'
        ]
        # 删除数据集其他表
        for table in delete_table:
            repository.delete_data(table, {'dataset_id': delete_dataset_ids})
        # 删除flow中的调度信息
        from flow.services.flow_service import delete_flow
        for dataset_id in delete_dataset_ids:
            try:
                delete_flow(dataset_id)
            except Exception as e:
                logging.error(str(e))
                continue
        logging.info('数据集初始化完成')

    def other_func(self):
        """
        deprecated 王斐确认方法已弃用，env_type逻辑不用调整
        :return:
        """
        # 产业建管私有化环境去导入报告和数据集引入
        if config.get('Product.env_type') == 'cyjg_private':
            file_info = self.init_private_cyjg()
            if file_info:
                self.import_cyjg_data(file_info)

    def init_private_cyjg(self):
        logging.info('开始自动导入产业建管SAAS报告数据包')
        # 获取saas产业建管环境导出的文件包
        saas_url = self.others.get('saas_url')
        if not saas_url:
            logging.info('没有配置产业建管云端地址，请配置后重试')
            return False
        import requests
        params = {'sign': 'auto_export'}
        api = '''{}/api/upload/get_export_file'''.format(saas_url.rstrip('/'))
        response = requests.post(api, json=params, timeout=30)
        if response.status_code == 200:
            result = response.json() if response.text else {'result': 0, 'msg': '没有获取到数据'}
            if not result.get('result'):
                logging.info(result.get('msg'))
            return result.get('data') or {}
        logging.info('调用对应环境接口失败，请重试')
        return False

    @staticmethod
    def import_cyjg_data(file_info):
        from imports.services.dashboard_import_data_services import sync_deliver_data
        file_url = file_info.get('url') or ''
        if not file_url:
            logging.info('上传文件不存在')
            return False
        export_id = file_info.get('id')
        deliver_id = seq_id()
        data = {
            'id': deliver_id, "export_id": export_id, "title": '自动分发任务',
            "description": '自动分发任务', "source_url": file_url, "source_project": 'dmp_template',
            "dest_projects": g.code, "is_all_projects": 0, "distribute_type": 0, "replace_data_source": 0,
            "operate_type": 1, "is_lock_dataset": 0
        }
        repository.add_data('deliver_dashboard', data, from_config_db=True)
        try:
            sync_deliver_data(deliver_id)
            logging.info('创建自动分发任务成功')
            return True
        except Exception as e:
            logging.info('创建自动分发任务失败：{}'.format(str(e)))
            return False

    def change_publish_way(self):
        logging.info(f'开始更新发布应用')
        if self.from_init == TenantFrom.ERPSAAS.value:
            release_type = DashboardTypeAccessReleased.ThirdParty.value
        else:
            release_type = DashboardTypeAccessReleased.UserRole.value
        setattr(g, 'userid', ADMINISTRATORS_ID)
        # 门户
        _republish_applications(release_type)


    def dashboard_link_to_menu(self, app_id, is_pc: bool = True):
        if not self.app_list or not app_id:
            return
        platform = 'pc' if is_pc else 'mobile'
        data = repository.get_one("application", {'id': app_id}, ['id'])
        app_name = [x.get('app_name') for x in self.app_list]
        if not data:
            logging.error(f'{platform}门户[{app_id}]不存在')
        else:
            folder_list = repository.get_list("dashboard", {'name': app_name, 'type': 'FOLDER'}, ['level_code', 'name'])
            folder_group = _group_folder(folder_list)
            for key, val in folder_group.items():
                data = _get_dashboard_of_platform(platform, val)
                if not data:
                    logging.info(f'菜单[{key}]无数据')
                    continue
                _create_app_menu(data, key, app_id)


def _group_folder(folder_list):
    folder_dict = {}
    for item in folder_list:
        if folder_dict.get(item.get('name')):
            folder_dict[item.get('name')].append(item.get('level_code'))
        else:
            folder_dict[item.get('name')] = [item.get('level_code')]
    return folder_dict



def _republish_dashboards(release_type):
    logging.info(f'开始更新发布大小屏')
    ids = repository.get_list('dashboard', {'type': 'FILE'}, ['id'])
    for id in ids:
        try:
            kwargs = {"id": id, "status": 1, "type_access_released": release_type, "user_groups": [], "view_passwd": ""}
            model = ReleaseModel(**kwargs)
            released_dashboard_service.release_with_process.set_permissions('self_service-edit')
            released_dashboard_service.release_with_process(model)
        except Exception as e:
            logging.error(f'更新发布看板[{id}]异常: {str(e)}')

def _republish_applications(release_type):
    logging.info(f'开始更新发布门户')
    ids = repository.get_columns('application', {'is_buildin': 0}, 'id')
    # 排除内置门户
    for id in ids:
        if id == buildin_application_id:
            continue
        try:
            enable_application_and_sync_mip(id, release_type)
        except Exception as e:
            logging.error(f'更新发布门户[{id}]异常: {str(e)}')

def _republish_reports(release_type):
    logging.info(f'开始更新发布统计报表')
    try:
        biz_service = BizLinkService('active_reports', g.code, g.userid)
        result = biz_service.get_report_tree({"include_file": 1})
        if not result:
            return
        ids = []
        _ext_active_report(result, ids)
        for id in ids:
            params = {"id": id, "type_access_released": release_type}
            try:
                biz_service.release_active_report(params)
            except Exception as e:
                logging.error(f'更新发布统计报表[{id}]异常: {str(e)}')
    except Exception as e:
        logging.error(f'获取统计报表数据异常: {str(e)}')


def _ext_active_report(reports, ids:list):
    if not reports:
        return
    for item in reports:
        if item['type'] != 'FILE':
            if item['sub']:
                _ext_active_report(item['sub'], ids)
        else:
            ids.append(item['id'])


def _get_dashboard_of_platform(platform, level_codes):
    conds = [" level_code like '" + s + "%' " for s in level_codes]
    or_cond = 'or'.join(conds)
    sql = "select id,name,application_type from dashboard where type in ('FILE') and platform = '{platform}' and ({or_cond})"
    sql = sql.format(platform = platform, or_cond = or_cond)
    data = repository.get_data_by_sql(sql, params=None)
    return data


def _create_app_menu(dashboard_list, menu_name, app_id):
    func_id = seq_id()
    params = {"id": func_id, "parent_id": "", "sub": [], "target": "", "url": "",
              "icon": "dmpicon-column", "application_id": app_id, "name": menu_name,
              "level": [3], "guide_img": []}
    updated = function_service.add_function(FunctionModel(**params))
    if updated:
        for dashboard in dashboard_list:
            params = {"application_id": app_id, "guide_img": [], "icon": "dmpicon-column",
                      "id": seq_id(), "name": dashboard['name'], "parent_id": func_id,
                      "report_type": _application_and_report_type_map(dashboard['application_type']),
                      "target": "", "url": dashboard['id'], "url_info": ""}
            function_service.add_function(FunctionModel(**params))

def _application_and_report_type_map(application_type):
    if ApplicationType.Dashboard.value == application_type:
        return FunctionReportType.DASHBOARD.value
    elif ApplicationType.ActiveReport.value == application_type:
        return FunctionReportType.ACTIVE_REPORT.value
    elif ApplicationType.SimpleReport.value == application_type:
        return FunctionReportType.COMPLICATE_REPORT.value
    elif ApplicationType.HD_Dashboard.value == application_type:
        return FunctionReportType.HIGHDATA.value
    else:
        return 0
