"""
    HighData报表升级功能service
"""
# pylint: skip-file
import json
import logging
import re
from base import repository
from base.enums import FlowNodeType
from dmplib.utils.errors import UserError
from hd_upgrade.services.report_upgrade_external_service import HdQueryApi
from hd_upgrade.services.external_upgrade_service import (
    update_upgrade_err_log, update_upgrade_warning_log
)
from feed.models import DashboardFeedsFlowModel
from flow.models import FlowNodeModel
from flow.services.flow_service import add_flow, delete_flow

logger = logging.getLogger(__name__)


class MessageUpgrade(object):
    """
    简讯升级模块
    """

    def __init__(self, **kwargs):
        self.ids = kwargs.get('ids', None)
        self.tenant_code = kwargs.get('tenant_code', None)
        self.message = list()
        self.message_chapter = list()
        self.dmp_message = list()
        self.dmp_message_chapter = list()
        self.dmp_message_filter = list()
        self.dmp_field_format = list()
        self.dataset_ids = list()
        self.dataset_field = dict()
        self.flow = list()
        yzs_domain = kwargs.get('yzs_domain', None)
        self.hd_api = HdQueryApi(self.tenant_code, yzs_domain)

    def upgrade(self):
        """
        简讯升级
        :return:
        """
        self.init_message()
        self.upgrade_message()

    def init_message(self):
        where = {'is_enable': 1, 'msg_type': 0}
        fields = ['msg_id', 'msg_name', 'send_cycle', 'msg_link_type', 'msg_link', 'business_id', 'pic_url']
        if not self.ids:
            # 获取对应HIGHDATA租户的所有简讯信息
            self.message = self.hd_api.get_hd_list('release_msg', where, fields)
            self.ids = [message.get('msg_id') for message in self.message]
        else:
            where['msg_id'] = self.ids
            self.message = self.hd_api.get_hd_list('release_msg', where, fields)
        if not self.message:
            raise UserError(message='没有找到需要升级的简讯')
        # 获取简讯对应的段落信息
        fields = ['id', 'msg_id', 'dataset_id', 'is_get_new_data', 'config', 'filter_config', 'chapter_order']
        self.message_chapter = self.hd_api.get_hd_list(
            'release_msg_chapter', {'msg_id': self.ids}, fields, 'chapter_order asc'
        )
        # 将简讯和简讯段落合并
        self.merge_message()
        # 获取数据集对应的字段信息
        self.get_dataset_field()

    def upgrade_message(self):
        """
        升级简讯
        :return:
        """
        for message in self.message:
            self.format_message(message)
            self.format_chapter(message)
            self.format_filter(message)
            self.format_display_field(message)
            self.prepare_flow(message)
        if self.dmp_message:
            field = list(self.dmp_message[0].keys())
            repository.replace_list_data('dashboard_email_subscribe', self.dmp_message, field)
        if self.dmp_message_chapter:
            field = list(self.dmp_message_chapter[0].keys())
            repository.replace_list_data('mobile_subscribe_chapters', self.dmp_message_chapter, field)
        if self.dmp_message_filter:
            repository.delete_data('mobile_subscribe_filter', {'email_subscribe_id': self.ids})
            field = list(self.dmp_message_filter[0].keys())
            repository.add_list_data('mobile_subscribe_filter', self.dmp_message_filter, field)
        if self.dmp_field_format:
            repository.delete_data('dashboard_subscribe_display_format', {'subscribe_id': self.ids})
            field = list(self.dmp_field_format[0].keys())
            repository.add_list_data('dashboard_subscribe_display_format', self.dmp_field_format, field)
        if self.flow:
            for flow in self.flow:
                delete_flow(flow.id)
                add_flow(flow)

    def merge_message(self):
        chapter = {}
        for row in self.message_chapter:
            if row.get('dataset_id'):
                self.dataset_ids.append(row.get('dataset_id'))
            if row.get('msg_id') not in chapter.keys():
                chapter[row.get('msg_id')] = [row]
            else:
                chapter[row.get('msg_id')].append(row)
        if self.dataset_ids:
            self.dataset_ids = list(set(self.dataset_ids))
        for message in self.message:
            msg_id = message.get('msg_id')
            message['chapter'] = chapter.get(msg_id) if chapter.get(msg_id) else []

    def get_dataset_field(self):
        if self.dataset_ids:
            fields = ['id', 'dataset_id', 'origin_col_name', 'col_name']
            dataset_field = repository.get_list('dataset_field', {'dataset_id': self.dataset_ids}, fields)
            for field in dataset_field:
                dataset_id = field.get('dataset_id')
                col_name = field.get('origin_col_name')
                if not self.dataset_field.get(dataset_id):
                    self.dataset_field[dataset_id] = {}
                self.dataset_field[dataset_id].update({col_name: field})

    def format_message(self, message):
        message_chapter = message.get('chapter')
        message_str_model = '''<samp-chapter contenteditable="false" data-chapter-id="{}"></samp-chapter><br>'''
        dmp_message = {
            'id': message.get('msg_id'), 'subject_email': message.get('msg_name'), 'type': '简讯订阅',
            'dashboard_id': '', 'send_frequency': 2, 'message': '<div>{}</div>', 'recipients': '[]', 'addresser': '',
            'dataset_ids': '', 'msg_subscribe_config': '{"enable_send_rule": 0, "is_show_date": 0, "msg_pic_url": "", "msg_type": 1, "user_from": 1}'
        }
        message_str = ''
        dataset_ids = []
        for chapter in message_chapter:
            message_str += message_str_model.format(chapter.get('id'))
            if chapter.get('dataset_id'):
                dataset_ids.append(chapter.get('dataset_id'))
        dmp_message['message'] = dmp_message['message'].format(message_str)
        dmp_message['dashboard_id'] = message.get('business_id') if message.get('business_id') else ''
        if dataset_ids:
            dataset_ids = list(set(dataset_ids))
            dmp_message['dataset_ids'] = json.dumps(dataset_ids, ensure_ascii=False)
        self.dmp_message.append(dmp_message)

    def format_chapter(self, message):
        message_chapter = message.get('chapter') if message.get('chapter') else []
        sub_rule = r'</b>|<\\/b>|<b\b[^<>]*>|<font\b[^<>]*>|</font>|<\\/font>|<span\b[^<>]*>|</span>|<\\/span>'
        for index, chapter in enumerate(message_chapter):
            config = json.loads(chapter.get('config'))
            content = config.get('content') if config.get('content') else ''
            if content:
                content = re.sub(sub_rule, '', content)
                func = lambda x: self.format_message_chapter(x, chapter)
                content = re.sub(r'{([^:]+:[^:]+:[^:{}]+)}', func, content)
            dmp_message_chapter = {
                'id': chapter.get('id'), 'email_subscribe_id': chapter.get('msg_id'), 'dataset_id': chapter.get('dataset_id'),
                'chapter_name': '第{}段落'.format(index + 1), 'chapter_message': content
            }
            self.dmp_message_chapter.append(dmp_message_chapter)

    def format_message_chapter(self, match, chapter):
        model = '''<samp contenteditable="false" data-datasetid="{}" data-colid="{}">{}</samp>'''
        dataset_id = chapter.get('dataset_id')
        if match.group and match.group(1):
            col = match.group(1).split(':')
            col_id = self.dataset_field.get(dataset_id, {}).get(col[1], {}).get('id', '')
            model = model.format(dataset_id, col_id, col[0])
        return model

    def format_filter(self, message):
        message_chapter = message.get('chapter') if message.get('chapter') else []
        for chapter in message_chapter:
            dataset_id = chapter.get('dataset_id', '')
            filter_config = json.loads(chapter.get('filter_config')) if chapter.get('filter_config') else []
            for config in filter_config:
                if config.get('type') != '0':
                    continue
                field = config.get('name')
                dmp_message_filter = {
                    'email_subscribe_id': message.get('msg_id'), 'dataset_id': dataset_id,
                    'dataset_field_id': self.dataset_field.get(dataset_id, {}).get(field, {}).get('id', ''),
                    'col_name': self.dataset_field.get(dataset_id, {}).get(field, {}).get('col_name', ''),
                    'operator': config.get('operator', ''), 'col_value': config.get('value')
                }
                self.dmp_message_filter.append(dmp_message_filter)

    def format_display_field(self, message):
        message_chapter = message.get('chapter') if message.get('chapter') else []
        for chapter in message_chapter:
            config = json.loads(chapter.get('config'))
            dataset_id = chapter.get('dataset_id', '')
            number_formatter = config.get('number_formatter') if config.get('number_formatter') else {}
            for field, formatter in number_formatter.items():
                dmp_display_formatter = {
                    'subscribe_id': message.get('msg_id'), 'dataset_id': dataset_id
                }
                col = field.split(':')[1]
                dmp_display_formatter['field_id'] = self.dataset_field.get(dataset_id, {}).get(col, {}).get('id', '')
                dmp_display_formatter.update(self.get_field_formatter(formatter))
                self.dmp_field_format.append(dmp_display_formatter)

    @staticmethod
    def get_field_formatter(formatter: str):
        default = {'display_format': 1, 'digit': 0, 'use_thousands': 0}
        format_config = {
            'int_1': {'display_format': 1, 'digit': 0, 'use_thousands': 1},
            'int_2': {'display_format': 1, 'digit': 0, 'use_thousands': 0},
            'float_1': {'display_format': 1, 'digit': 1, 'use_thousands': 0},
            'float_2': {'display_format': 1, 'digit': 2, 'use_thousands': 0},
            'float_3': {'display_format': 1, 'digit': 4, 'use_thousands': 0},
            'percent_1': {'display_format': 2, 'digit': 1, 'use_thousands': 0},
            'percent_2': {'display_format': 2, 'digit': 2, 'use_thousands': 0},
            'percent_3': {'display_format': 2, 'digit': 4, 'use_thousands': 0},
            'percent_4': {'display_format': 2, 'digit': 0, 'use_thousands': 0},
        }
        return format_config.get(formatter) if format_config.get(formatter) else default

    def prepare_flow(self, message):
        flow_model = DashboardFeedsFlowModel()
        flow_model.id = message.get('msg_id')
        flow_model.name = message.get('msg_name')
        flow_model.schedule = '00 30 09 09 06 ? 2022'
        flow_model.nodes = [FlowNodeModel(name=message.get('msg_name'), type=FlowNodeType.EmailFeeds.value)]
        self.flow.append(flow_model)


def message_upgrade(params=None):
    """
    角色权限升级
    :param params: 升级的请求参数
    :return:
    """
    try:
        if not params.get('tenant_code'):
            msg = '租户信息为空，跳过简讯升级'
            logger.info(msg)
            return False, msg
        upgrade_obj = MessageUpgrade(**params)
        upgrade_obj.upgrade()
    except UserError as ue:
        err_msg = '简讯升级错误，错误信息：%s' % ue.message
        update_upgrade_warning_log(err_msg)
        logger.error(err_msg)
        return False, err_msg
    except Exception as e:
        err_str = str(e)
        err_msg = '简讯升级异常，异常信息：%s' % err_str
        logger.exception(err_str)
        update_upgrade_err_log(err_msg)
        return False, err_msg
    return True, f'简讯升级完成！'
