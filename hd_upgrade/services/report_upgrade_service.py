"""
    HighData报表升级功能service
"""
# pylint: skip-file

import json
import logging
import math
import time
from array import array
from datetime import datetime
import traceback

from base import repository
from components.high_data_api import HighDataApi
from dmplib import config
from base.enums import DashboardType, DashboardPlatforms, DashboardTypeAccessReleased, DashboardTheme, FlowType, \
    FlowNodeType
from base.models import BaseModel
from dashboard_chart.services import released_dashboard_service
from dashboard_chart.models import ReleaseModel

from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from flow.services import flow_service
from hd_upgrade.services import dataset_service
from level_sequence.services import level_sequence_service
from level_sequence.models import DashboardLevelSequenceModel, FunctionLevelSequenceModel, UserRoleLevelSequenceModel

from hd_upgrade.services.report_upgrade_external_service import HdQueryApi, DmpReportDb, HdDashboardType, \
    DmpDashboardModel, DmpDashboardFilterVarsRelation, DmpDashboardValueSource, DmpDashboardVarsValueSourceRelation, \
    ReportInfo
from hd_upgrade.services.report_chart_upgrade_service import DashboardChartUpgrade
from hd_upgrade.services.external_upgrade_service \
    import update_upgrade_err_log, update_upgrade_notice_log, update_upgrade_warning_log
from app_menu.models import ApplicationModel, FunctionModel
from app_menu.services import application_service
from flow.models import FlowModel, FlowNodeModel

logger = logging.getLogger(__name__)


class ReportUpgrade(BaseModel):
    """
    仪表板或仪表板的公共配置格式处理
    """

    __slots__ = ['ids', 'tenant_code', 'report_type', 'token', 'yzs_domain']

    def __init__(self, **kwargs):
        self.ids = kwargs.get('ids', None)
        self.tenant_code = kwargs.get('tenant_code', None)
        # 报表类型，dashboard：仪表板，m_report：移动报表
        self.report_type = kwargs.get('report_type', None)
        tenant_id = kwargs.get('tenant_id', None)
        self.token = self.get_hd_login_token(tenant_id)
        self.yzs_domain = kwargs.get('yzs_domain', None)
        self.hd_host = config.get('Yzs.domain', 'https://qy-ci.fdccloud.com/')
        if not self.yzs_domain:
            self.yzs_domain = config.get('Yzs.domain', 'https://qy-ci.fdccloud.com/')

        self.hd_api = HdQueryApi(self.tenant_code, self.yzs_domain)
        self.queue_name = config.get('RabbitMQ.queue_name_flow_feeds', 'Flow-Feeds')

    @staticmethod
    def hd_status2dmp_status(hd_dash_data):
        if int(hd_dash_data.get("is_enable")) == 0:
            return 0
        elif int(hd_dash_data.get("is_enable")) == 1:
            return 1 if int(hd_dash_data.get("status")) == 1 else 0
        return 0

    @staticmethod
    def get_hd_backgound(hd_dash_data):
        """
        {"show":true,"color":"RGBA(15,24,47,1)","image":"","size":"stretch","user_image":""}
        :param hd_dash_data:
        :return:
        """
        # 背景图片上传
        background_image = hd_dash_data.get('background_image')
        oss_host = 'http://mic-open.oss-cn-hangzhou.aliyuncs.com'
        if background_image and background_image.find('http') == -1:
            background_image = oss_host + background_image
        background = dict(show=True, color=hd_dash_data.get("background_color"), image=background_image, size="stretch",
                          user_image="")
        return json.dumps(background).encode("utf-8")

    @staticmethod
    def get_hd_water_mark(common_config):
        """
        获取水印
        :param common_config:
        :return:
        """
        water_mark = False
        if "water_mark" in common_config:
            water_mark = common_config.get("water_mark")
        if water_mark:
            is_show_mark_img = 1
        else:
            is_show_mark_img = 0
        return is_show_mark_img

    @staticmethod
    def get_hd_layout(hd_dash_data):
        """
        {"height": 1080, "ratio": "16:9", "grid": True, "width": "1920"}
        :param hd_dash_data:
        :return:
        """
        layout = dict(
            height=hd_dash_data.get("canvas_height"), width=hd_dash_data.get("canvas_width"), radio="free", grid=False
        )
        return json.dumps(layout).encode("utf-8")

    @staticmethod
    def get_hd_mobile_layout(hd_dash_data):
        """
        移动报表layout升级
        {"mode":"grid","platform":"mobile","toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}
        style 1:none 平铺 2：card 卡片  -> layout_mode
        card_radius 10
        margin_top -> slider_top
        :param hd_dash_data:
        :return:
        """
        style = hd_dash_data.get("style")
        layout_mode = None if style == 1 else "card"
        layout = dict(
            mode="grid", platform="mobile", toolbar="show", screenHeader="show",
            slider_top=int(hd_dash_data.get("margin_top")), layout_mode=layout_mode, card_radius=10
        )
        return json.dumps(layout).encode("utf-8")

    @staticmethod
    def generate_function_level_code(model):
        """
        :param app_menu.models.FunctionModel model:
        :return:
        """
        return level_sequence_service.generate_level_code(
            FunctionLevelSequenceModel(level_id=model.parent_id, attach_identify=model.application_id)
        )

    @staticmethod
    def get_publish_report_id(report_list: list = None):
        """
        获取首页报表中的报表id 列表
        :param report_list:
        :return:
        """
        report_id_list = list()
        if not report_list:
            return report_id_list
        for report in report_list:
            report_id_list.append(report.get('business_id'))
        return list(set(report_id_list))

    @staticmethod
    def hd_dash_catalog2dmp_dash_model(catalog, level_code, parent_id: str = '', platform='pc'):
        """
        HD报表文件夹model转换
        :param catalog:
        :param level_code:
        :param parent_id:
        :param platform:
        :return:
        """
        args = {
            "id": catalog.get("catalog_id"),
            "name": catalog.get("catalog_name"),
            "type": DashboardType.Folder.value,
            "level_code": level_code,
            # "rank": catalog.get("sort"),
            "parent_id": parent_id,
            "platform": platform,
            "is_highdata": 0,
            "create_type": 1
        }
        if platform == DashboardPlatforms.Mobile.value:
            args["terminal_type"] = "mobile_screen"
        # 文件夹修改时间写入
        if "sorttime" in catalog and catalog.get('sorttime'):
            sorttime = catalog.get('sorttime')
            if isinstance(sorttime, datetime):
                sorttime = sorttime.strftime('%Y-%m-%d %H:%M:%S')
            args['modified_on'] = sorttime
        return DmpDashboardModel(**args)

    @staticmethod
    def update_sequence_val(model_list: list):
        """
        将指定序列值更新到数据表
        :param model_list:
        :return:
        """
        if not model_list:
            return False
        for item in model_list:
            model = item.get('model')
            val = item.get('val')
            DmpReportDb.update_level_code(model, val)
        return True

    @staticmethod
    def update_all_parent_id_sequence(table_name, pid_map_list: dict, root_is_update=True, application_id: str = ''):
        """
        构建需要更新的仪表板或首页目录的sequence序列
        :param table_name:
        :param pid_map_list:
        :param root_is_update:根节点是否更新sequence，True：需要，False：不需要
        :param application_id: 目录的门户id
        :return:
        """
        if not pid_map_list:
            return False
        model_list = list()
        for k, v in pid_map_list.items():
            # 子报表场景下，根报表不需要构建序列，外部已经构建
            if not root_is_update and k == '':
                continue
            if table_name == 'dashboard':
                model = DashboardLevelSequenceModel(level_id=k)
            elif table_name == 'function':
                model = FunctionLevelSequenceModel(level_id=k, attach_identify=application_id)
            elif table_name == 'user_role':
                model = UserRoleLevelSequenceModel(level_id=k, attach_identify=application_id)
            data_model = {"model": model, 'val': v}
            model_list.append(data_model)
        return ReportUpgrade.update_sequence_val(model_list)

    def get_hd_tree_level_code(self, data_list, p_id, root_level_code, level_code_list,
                               pid_map_list, id_field='dashboard_id'):
        """
        构建HighData里面的树层级的levelcode值
        :param data_list: 仪表板和子报表
        :param p_id: 父层级pid，根报表id为空字符串
        :param root_level_code: 根报表需要挂载的文件夹id
        :param level_code_list: 报表id->levelcode的字典
        :param pid_map_list: 父层级pid->引用次数 的字典
        :param id_field: 主键id字段名称
        :return:
        """
        for data in data_list:
            field_id = data.get(id_field)
            # 父层级id字段默认名称
            parent_id = data.get("parent_id")
            # 父层级引用次数
            if parent_id == p_id:
                if parent_id in pid_map_list:
                    pid_map_list[parent_id] += 1
                else:
                    pid_map_list[parent_id] = 1

                count = pid_map_list[parent_id]
                # 如果是根节点，且root_level_code为空，则使用外部传入root_level_code
                if p_id == '' and root_level_code != '':
                    level_code = root_level_code
                else:
                    level_code = root_level_code + (4 - len(str(count))) * '0' + str(count) + "-"
                level_code_list[field_id] = level_code
                self.get_hd_tree_level_code(data_list, field_id, level_code, level_code_list, pid_map_list, id_field=id_field)

    def hd_report_catalog_upgrade(self):
        """
        报表的文件夹升级
        :return:
        """
        type_name = ReportInfo.get_name(self.report_type)
        group_type = ReportInfo.get_group_type(self.report_type)
        update_upgrade_notice_log(f'{type_name}文件夹开始升级')
        # 文件夹根节点升级
        root_catalog_list = self._hd_report_root_catalog(group_type)
        if not root_catalog_list:
            update_upgrade_warning_log(f'没有可需要升级的文件夹')
            return False

        # 非根节点文件夹升级
        conditions = ['and', 'group_type=' + str(group_type), "parent_id != ''"]
        catalog_list = self.hd_api.get_hd_list("catalog", conditions,
                                               fields=['catalog_id', 'catalog_name', 'parent_id', 'sort', 'sorttime'])
        if not catalog_list:
            update_upgrade_warning_log(f'没有可需要升级的子文件夹')
            return False
        # 清除无法匹配到父层级的文件夹
        catalog_list = ReportUpgrade.clear_no_parent_catalog(root_catalog_list, catalog_list)

        catalog_level_code_map = dict()
        pid_map_list = dict()
        # 遍历每一个根节点文件夹，对每个根节点进行递归
        for catalog in root_catalog_list:
            catalog_id = catalog.get('catalog_id')
            catalog_name = catalog.get('catalog_name')
            # 获取当前父层级的level_code
            root_level_code = DmpReportDb.get_cur_level_code(DashboardLevelSequenceModel(level_id=catalog_id))
            if not root_level_code:
                update_upgrade_warning_log(f'【{catalog_name}】文件夹无法匹配到父文件夹，不能升级！')
                continue

            if catalog_list:
                self.get_hd_tree_level_code(catalog_list, catalog_id, root_level_code,
                                            catalog_level_code_map, pid_map_list, id_field='catalog_id')
                # 生成文件夹的level_code维护到level_sequence
                ReportUpgrade.update_all_parent_id_sequence('dashboard', pid_map_list, False)

        editor_models = dict()
        platform = DashboardUpgrade._get_report_platform(self.report_type)
        for catalog in catalog_list:
            catalog_id = catalog.get('catalog_id')
            parent_id = catalog.get('parent_id')

            level_code = catalog_level_code_map.get(catalog_id)
            model = DashboardUpgrade.hd_dash_catalog2dmp_dash_model(catalog, level_code, parent_id, platform)
            editor_models.update({catalog_id: model})

        list_model = editor_models.values()
        self._add_folder(list_model)
        update_upgrade_notice_log(f'{type_name}文件夹升级完成！')

    def _hd_report_root_catalog(self, group_type):
        """
        首先写入文件夹的全部根节点
        :param group_type:
        :return:
        """
        max_seq = DmpReportDb.get_level_max_sequence(DashboardLevelSequenceModel(level_id=""))
        # dashboard不存在根节点的level seq，则需要写入
        if not max_seq and max_seq != 0:
            DmpReportDb.add_sequence(DashboardLevelSequenceModel(level_id=""))
            max_seq = DmpReportDb.get_level_max_sequence(DashboardLevelSequenceModel(level_id=""))
        max_seq += 1
        i = 0
        conditions = {"group_type": group_type, "parent_id": ""}
        root_catalog_list = self.hd_api.get_hd_list("catalog", conditions,
                                                    fields=['catalog_id', 'catalog_name', 'sort', 'sorttime'])
        if not root_catalog_list:
            return False
        editor_models = dict()
        platform = DashboardUpgrade._get_report_platform(self.report_type)
        for catalog in root_catalog_list:
            catalog_id = catalog.get('catalog_id')
            level_code = (4 - len(str(max_seq))) * '0' + str(max_seq) + "-"
            max_seq += 1
            i += 1
            model = DashboardUpgrade.hd_dash_catalog2dmp_dash_model(catalog, level_code, "", platform)
            editor_models.update({catalog_id: model})

        list_model = editor_models.values()
        # 依据序列差值更新max sequence
        pid_map_list = {"": i}
        ReportUpgrade.update_all_parent_id_sequence('dashboard', pid_map_list, True)
        self._add_folder(list_model)
        return root_catalog_list

    @staticmethod
    def _get_report_platform(report_type):
        """
        dmp报表的平台类型
        :param report_type:
        :return:
        """
        if report_type == HdDashboardType.DASHBOARD.value:
            platform = DashboardPlatforms.PC.value
        else:
            platform = DashboardPlatforms.Mobile.value
        return platform

    def _add_folder(self, list_model):
        """
        写入dashboard数据
        :param list_model:
        :return:
        """
        if not list_model:
            return False
        fields = ["id", "name", "type", "level_code", "platform", "terminal_type", "rank", "parent_id",
                  "modified_on", "is_highdata"]
        return DmpReportDb.replace_list_model("dashboard", list_model, fields)

    @staticmethod
    def clear_no_parent_catalog(root_catalog_list, catalog_list):
        """
        清除文件夹列表中没有匹配到父层级的数据
        :param root_catalog_list:
        :param catalog_list:
        :return:
        """
        all_catalog_list = root_catalog_list + catalog_list
        all_catalog_id = list()
        for catalog in all_catalog_list:
            catalog_id = catalog.get('catalog_id')
            all_catalog_id.append(catalog_id)

        new_catalog_list = list()
        f_c = 0
        # 子文件夹
        for i, catalog in enumerate(catalog_list):
            catalog_id = catalog.get('catalog_id')
            parent_id = catalog.get('parent_id')
            if parent_id != '' and parent_id not in all_catalog_id:
                catalog_name = catalog.get('catalog_name')
                update_upgrade_warning_log(f'子文件夹【{catalog_name}】无法匹配到父文件夹，不会升级。catalog_id:{catalog_id}')
                f_c += 1
            else:
                new_catalog_list.append(catalog)
        # 可升级的文件夹数
        s_c = len(root_catalog_list + new_catalog_list)
        update_upgrade_notice_log(f'待升级文件夹总数：{len(all_catalog_list)}，可升级文件夹数：{s_c}，不能升级文件夹数：{f_c}')
        return new_catalog_list

    @staticmethod
    def init_report_params(dashboard_list):
        """
        初始化报表的接收参数关系
        :param dashboard_list:
        :return:
        """
        # 获取指定仪表板id数据，批量写入仪表板（包括子仪表板）数据
        if not dashboard_list:
            update_upgrade_warning_log('报表不存在，接收参数不能升级')

        value_source_models = list()
        value_source_relation_models = list()

        filter_dataset_id_list = set()
        filters_list = list()
        for dashboard in dashboard_list:
            dashboard_id = dashboard.get('dashboard_id')
            param_config_str = dashboard.get("param_config")
            if not param_config_str:
                continue
            param_config = json.loads(param_config_str)
            for param_item in param_config:
                if not param_item:
                    continue
                link_key = param_item.get("link_key")
                # 获取所有报表的接收参数 filters 节点
                ReportUpgrade.get_report_param_filter(dashboard_id, link_key, param_item, filters_list,
                                                      filter_dataset_id_list)
                # 获取所有报表的接收参数 params 节点
                ReportUpgrade.get_report_param_params(dashboard_id, link_key, param_item,
                                                      value_source_models, value_source_relation_models)

        # 报表接收参数，参数数据写入
        ReportUpgrade.add_value_source_models(value_source_models, value_source_relation_models)

        # 报表接收参数，筛选关系数据写入
        filters_vars_relation_models = list()
        if filters_list:
            # 设置筛选关联的数据集字段id值
            filter_dashboard_id_list = ReportUpgrade.set_report_filter_dataset_id(filters_list, filter_dataset_id_list,
                                                                                  filters_vars_relation_models)
            # 删除报表筛选关系
            sql = "delete from dashboard_filter_vars_relation_field where dashboard_id in %(filter_dashboard_id_list)s"
            params = {"filter_dashboard_id_list": filter_dashboard_id_list}
            DmpReportDb.exec_sql(sql, params)
            #  ReportUpgrade.add_filters_vars_relation(filters_vars_relation_models)

    @staticmethod
    def add_value_source_models(value_source_models, value_source_relation_models):
        """
        数据写入报告筛选变量与字段关系表
        :return:
        """
        if not value_source_models:
            return False
        fields = ["id", "dashboard_id", "value_source_name", "value_source", "value_identifier"]
        DmpReportDb.replace_list_model('dashboard_value_source', value_source_models, fields)

        if value_source_relation_models:
            # 先删后插
            for relation_model in value_source_relation_models:
                if not relation_model.var_id:
                    continue
                condition = {"dashboard_id": relation_model.dashboard_id, "var_id": relation_model.var_id}
                DmpReportDb.delete_data("dashboard_vars_value_source_relation", condition=condition)
            fields = ["dashboard_id", "var_id", "value_source_id"]
            DmpReportDb.replace_list_model('dashboard_vars_value_source_relation', value_source_relation_models, fields)

    @staticmethod
    def add_filters_vars_relation(filters_vars_relation_models):
        """
        数据写入报告筛选变量与字段关系表
        :return:
        """
        if not filters_vars_relation_models:
            return False
        fields = ["id", "var_name", "dashboard_id", "data_type", "related_dataset_field_id", "related_dataset_id"]
        return DmpReportDb.replace_list_model('dashboard_filter_vars_relation_field',
                                              filters_vars_relation_models, fields)

    @staticmethod
    def set_report_filter_dataset_id(filters_list, filter_dataset_id_list, filters_vars_relation_models):
        dataset_field_map = {}
        filter_dataset_id_list = list(filter_dataset_id_list)
        dataset_field_list = DmpReportDb.get_list(
            'dataset_field', conditions={'dataset_id': filter_dataset_id_list, 'type': '普通'},
            fields=['id', 'origin_col_name', 'dataset_id'], multi_row=True
        )
        filter_dashboard_id_list = set()
        if dataset_field_list:
            for field in dataset_field_list:
                field_k = field.get('dataset_id') + '_' + field.get('origin_col_name')
                dataset_field_map[field_k.lower()] = field.get('id')

        if filters_list:
            for filter_item in filters_list:
                column_name = filter_item.get('column_name')
                dataset_id = filter_item.get('related_dataset_id')
                filter_id_k = (dataset_id + '_' + column_name).lower()
                dataset_field_id = ""
                if filter_id_k in dataset_field_map and dataset_field_map.get(filter_id_k):
                    dataset_field_id = dataset_field_map.get(filter_id_k)
                filter_item['related_dataset_field_id'] = dataset_field_id
                dashboard_id = filter_item.get('dashboard_id')
                # 全部的报表id
                filter_dashboard_id_list.add(dashboard_id)
                if not dataset_field_id:
                    var_name = filter_item.get('var_name')
                    update_upgrade_warning_log(f'报表筛选无法匹配关联字段id，报表id：{dashboard_id}，数据集id：{dataset_id}'
                                               f'，参数名：{var_name}，字段名：{column_name}')
                    continue
                # 转换为筛选字段关系对象
                model = ReportUpgrade.hd_report_filter2dmp_report_filter_model(filter_item)
                filters_vars_relation_models.append(model)
        if filter_dashboard_id_list:
            filter_dashboard_id_list = list(filter_dashboard_id_list)
        return filter_dashboard_id_list

    @staticmethod
    def get_report_param_filter(dashboard_id, link_key, param_item, filters_list: list, filter_dataset_id_list: set):
        """
        遍历每个接收参数的 filters 节点
        :param dashboard_id:
        :param link_key:
        :param param_item:
        :param filters_list:
        :param filter_dataset_id_list:
        :return:
        """
        filters = param_item.get("filters")
        for filter_info in filters:
            dataset_id = filter_info.get("dataset_id")
            # 获取所有的数据集id集合
            filter_dataset_id_list.add(dataset_id)
            dataset_field_list = filter_info.get("dataset_field_list")
            if not dataset_field_list:
                continue
            # 同一个数据集，作用多个字段，只会升级第一个字段关系。dmp模型只支持一个字段。
            first_dataset_field = dataset_field_list[0]
            filter_dict = {"var_name": link_key, "dashboard_id": dashboard_id, "related_dataset_id": dataset_id,
                           "column_name": first_dataset_field.get("ColumnName")}
            filters_list.append(filter_dict)

    @staticmethod
    def get_report_param_params(dashboard_id, link_key, param_item, value_source_model_list,
                                value_source_relation_model_list):  # NOSONAR
        """
        遍历每个接收参数的 params 节点
        :param dashboard_id:
        :param link_key:
        :param param_item:
        :param value_source_model_list:
        :param value_source_relation_model_list:
        :return:
        """
        params = param_item.get("params")
        if not params:
            return

        # 参数变量名称写入
        value_source_id = seq_id()
        value_source = {"id": value_source_id, "dashboard_id": dashboard_id, "value_source_name": link_key,
                        "value_source": "url", "value_identifier": link_key}
        value_source_model = ReportUpgrade.hd_report_params2dmp_value_source_model(**value_source)
        value_source_model_list.append(value_source_model)

        for params_info in params:
            params_field_list = params_info.get("params_field_list")
            if not params_field_list:
                continue
            dataset_id = params_info.get("dataset_id")
            for dataset_field in params_field_list:
                param_guid = dataset_field.get("ParamGUID")
                param_name = dataset_field.get("ParamName")
                if not param_guid:
                    # 获取变量的id
                    var_rs = DmpReportDb.get_data("dataset_vars",
                                                  {"dataset_id": dataset_id, "name": param_name}, fields=['id'])
                    if var_rs:
                        param_guid = var_rs.get('id')
                    else:
                        update_upgrade_warning_log(f'报表参数无法匹配到参数id，报表id：{dashboard_id}，'
                                                   f'数据集id：{dataset_id}，参数名：{link_key}，变量名：{param_name}')
                if not param_guid:  # to do 此处需要排查为什么paramguid为空
                    continue
                value_source_relation = {"dashboard_id": dashboard_id, "var_id": param_guid,
                                         "value_source_id": value_source_id}
                value_source_relation_model = ReportUpgrade. \
                    hd_report_params2dmp_value_source_relation_model(**value_source_relation)
                value_source_relation_model_list.append(value_source_relation_model)

    @staticmethod
    def hd_report_filter2dmp_report_filter_model(filter_item):
        """
        :param filter_item:
        :return:
        """
        args = {
            "id": seq_id(),
            "var_name": filter_item.get("var_name"),
            "dashboard_id": filter_item.get("dashboard_id"),
            "data_type": '字符串',
            "related_dataset_field_id": filter_item.get("related_dataset_field_id"),
            "related_dataset_id": filter_item.get("related_dataset_id"),
        }
        return DmpDashboardFilterVarsRelation(**args)

    @staticmethod
    def hd_report_params2dmp_value_source_model(**kwargs):
        return DmpDashboardValueSource(**kwargs)

    @staticmethod
    def hd_report_params2dmp_value_source_relation_model(**kwargs):
        return DmpDashboardVarsValueSourceRelation(**kwargs)

    def get_hd_login_token(self, tenant_id=None):
        hd_login_api = HighDataApi(self.tenant_code)
        if not tenant_id:
            tenant_info = hd_login_api.get_hd_tenant_info_by_code(self.tenant_code)
            if tenant_info:
                tenant_id = tenant_info.get('tenant_id')
            else:
                return ''
        hd_login_api.hd_tenant_id = tenant_id
        return hd_login_api.get_token(60 * 60 * 10)  # 10个小时过期


class DashboardUpgrade(ReportUpgrade):
    """
    仪表板的数据升级逻辑类
    """

    def hd_dash_data2dmp_dash_model(self, hd_dash_data, dmp_data):
        """
        :param hd_dash_data:
        :param dmp_data:
        :return:
        """
        common_config_json = hd_dash_data.get('common_config')
        common_config = dict()
        if common_config_json:
            common_config = json.loads(common_config_json)

        id_field = ReportInfo.get_id_field(self.report_type)
        name_field = ReportInfo.get_name_field(self.report_type)
        if self.report_type == HdDashboardType.DASHBOARD.value:
            layout_json = ReportUpgrade.get_hd_layout(hd_dash_data)
            if hd_dash_data.get('style', '') == 'white':
                theme = DashboardTheme.ColorfulWhite.value
            else:
                theme = DashboardTheme.TechBlue.value
        else:
            layout_json = ReportUpgrade.get_hd_mobile_layout(hd_dash_data)
            theme = DashboardTheme.ColorfulWhite.value
        args = {
            "id": hd_dash_data.get(id_field),
            "name": hd_dash_data.get(name_field),
            "type": dmp_data.get("dashboard_type"),
            "level_code": dmp_data.get("level_code"),
            "platform": dmp_data.get("platform"),
            "theme": theme,
            "terminal_type": dmp_data.get("terminal_type"),
            "icon": hd_dash_data.get("icon_url"),
            "status": ReportUpgrade.hd_status2dmp_status(hd_dash_data),
            "cover": hd_dash_data.get("cover_image"),
            "background": ReportUpgrade.get_hd_backgound(hd_dash_data),
            "is_show_mark_img": ReportUpgrade.get_hd_water_mark(common_config),
            "parent_id": dmp_data.get("parent_id"),
            "rank": hd_dash_data.get("sort"),
            "layout": layout_json,
            "new_layout_type": 0,
            "erp_app_code": hd_dash_data.get("app_code"),
            "is_highdata": 0,
            "create_type": 1
        }
        return DmpDashboardModel(**args)

    @staticmethod
    def hd_dash_publish_catalog2dmp_function_catalog_model(item, level_code, application_id):
        """
        仪表板目录转换为dmp的目录层级
        :param item:
        :param level_code:
        :param application_id:
        :return:
        """
        args = {
            "id": item.get("catalog_id"),
            "name": item.get("catalog_name"),
            "parent_id": item.get("parent_id"),
            "level_code": level_code,
            "icon": "dmpicon-book",
            "application_id": application_id,
        }
        return FunctionModel(**args)

    def hd_dash_publish_report2dmp_function_report_model(self, item, application_id, report_name_map):
        """
        仪表板目录转换为dmp的目录层级
        :param item:
        :param application_id:
        :param report_name_map: 报表id->报表名称 字段映射
        :return:
        """
        biz_id = item.get("business_id")
        if biz_id in report_name_map:
            biz_name = report_name_map.get(biz_id)
        else:
            biz_name = item.get("business_name")
        args = {
            "id": item.get("id"),
            "name": biz_name,
            "parent_id": item.get("catalog_id"),
            "url": biz_id,
            "application_id": application_id,
        }
        # 移动报表图标特别处理
        if self.report_type == HdDashboardType.M_REPORT.value:
            icon_url = item.get('icon_url')
            if icon_url:
                current_domain = config.get('Domain.dmp')
                args['icon_url'] = current_domain + icon_url
        else:
            args['icon'] = "dmpicon-report-01"
        return FunctionModel(**args)

    def dashboard_upgrade_exec(self, dashboard_ids_list):
        """
        仪表板升级执行入口
        将全部仪表板id分页执行
        :param dashboard_ids_list:
        :return:
        """
        type_name = ReportInfo.get_name(self.report_type)
        if not dashboard_ids_list:
            update_upgrade_warning_log(f'没有可以需要升级的{type_name}')
            return

        total_c = len(dashboard_ids_list)
        update_upgrade_notice_log(f'待升级{type_name}总数量：{str(total_c)}')
        page_size = 20
        page_count = math.ceil(total_c / page_size)
        success_c = fail_c = 0
        error_info = ''
        for p in range(1, page_count + 1):
            tmp_page_size = page_size if p < page_count else total_c - page_size * (page_count - 1)
            start = (p - 1) * page_size
            end = start + tmp_page_size
            tmp_dashboard_id_list = dashboard_ids_list[start:end]

            update_upgrade_notice_log(f'总数量：{str(total_c)}，当前升级{type_name}是第{str(start + 1)} - {str(end)}')
            for dashboard_id in tmp_dashboard_id_list:
                try:
                    update_upgrade_notice_log(f'{type_name}开始升级，报表id：[{dashboard_id}]')
                    # 报表升级业务逻辑
                    self.hd_dash2dmp_dash_v2(dashboard_id)
                    success_c += 1
                    update_upgrade_notice_log(f'{type_name}升级成功！')
                except UserError as ue:
                    update_upgrade_warning_log(f'{type_name}升级错误！报表id：[%s]，错误信息：%s %s '
                                               % (dashboard_id, traceback.format_exc(), str(ue)))
                    fail_c += 1
                    traceback.print_exc()
                    error_info = traceback.format_exc()
                except Exception as e:
                    logger.exception(e)
                    update_upgrade_err_log(f'{type_name}升级异常！报表id：[%s]，异常信息：%s %s'
                                           % (dashboard_id, traceback.format_exc(), e))
                    fail_c += 1
                    traceback.print_exc()
                    error_info = traceback.format_exc()

        msg = f'待升级{type_name}总数量：{str(total_c)}，成功数：{str(success_c)}，失败数：{str(fail_c)} {error_info}'
        update_upgrade_notice_log(msg)
        return msg

    def hd_dash2dmp_dash_v2(self, dashboard_id: str = ''):
        """
        仪表板升级的业务逻辑
        :param dashboard_id:
        :return:
        """
        # 仪表板数据升级
        all_dashboard_id_list = self.hd_dashboard_data_upgrade(dashboard_id)
        # 仪表板组件升级
        dashboard_chart_upgrade = DashboardChartUpgrade(self.tenant_code, self.report_type, self.hd_api)
        if not all_dashboard_id_list:
            raise UserError(message='报表id不能为空')
        if self.report_type == HdDashboardType.M_REPORT.value:
            for child_dashboard_id in all_dashboard_id_list:  # 批量启动异步作业 ，计算各个报表的高度
                self.start_flow_calc_dashboard_chart_height(child_dashboard_id)
            # 等待异步作业完成,先不调用
            self.wait_flow_calc_dashboard_chart_height(all_dashboard_id_list)
        for child_dashboard_id in all_dashboard_id_list:
            dashboard_chart_upgrade.hd_chart2dmp_chart_v2(child_dashboard_id)
        # 仪表板发布
        #self.hd_dashboard_publish(dashboard_id)
        # 导出不支持的组件清单

    # def export_no_support_chart_list(self, all_dashboard_id_list):
    #     no_support_chart_list = repository.get_list('not_support_chart_log', {'dashboard_id': all_dashboard_id_list})

    def hd_dashboard_publish(self, dashboard_id):
        """
        仪表板异步发布
        :param dashboard_id:
        :return:
        """
        try:
            kwargs = {
                "id": dashboard_id,
                "status": 1,
                "type_access_released": 0,
                "user_groups": [],
                "view_passwd": "",
            }
            model = ReleaseModel(**kwargs)
            released_dashboard_service.release_with_process.set_permissions('self_service-edit')
            released_dashboard_service.release_with_process(model)
        except Exception as e:
            msg = f'报告异步发布异常，异常信息：{str(e)}'
            raise UserError(message=msg)

    def _get_root_dashboard(self, dashboard_id, list_hd_data):
        """
        获取主仪表板
        :param dashboard_id:
        :param list_hd_data:
        :return:
        """
        root_dashboard = dict()
        for hd_dash_data in list_hd_data:
            if hd_dash_data.get('parent_id') == '' and dashboard_id == hd_dash_data.get('root_id'):
                root_dashboard = hd_dash_data
                break
        return root_dashboard

    def hd_dashboard_data_upgrade(self, dashboard_id: str = ''):  # NOSONAR
        """
        仪表板报表数据升级
        :param dashboard_id:
        :return:
        """
        table_name = ReportInfo.get_table_name(self.report_type)
        id_field = ReportInfo.get_id_field(self.report_type)
        name_field = ReportInfo.get_name_field(self.report_type)
        editor_models = {}
        # 获取指定仪表板id数据，批量写入仪表板（包括子仪表板）数据
        if not dashboard_id:
            raise UserError(message='参数错误，报表id为空')
        conditions = {"root_id": dashboard_id}
        order_by = '{} asc, sort asc'.format(id_field)
        list_hd_data = self.hd_api.get_hd_list(table_name, conditions, order_by=order_by)
        if not list_hd_data:
            raise UserError(message='报表不存在，不能升级')

        root_dashboard = self._get_root_dashboard(dashboard_id, list_hd_data)
        if not root_dashboard:
            raise UserError(message='根报表不存在，不能升级')
        dashboard_name = root_dashboard.get(name_field)
        update_upgrade_notice_log(f'报表名称【{dashboard_name}】')

        # 报表的挂载的文件夹id
        catalog_id = root_dashboard.get('catalog_id')
        # 文件夹是否存在
        catalog_is_exists = DmpReportDb.data_is_exists("dashboard",
                                                       {"id": catalog_id, "type": DashboardType.Folder.value})
        if not catalog_is_exists:
            # 全量升级，未匹配到文件夹不升级
            if not self.ids and catalog_id:
                raise UserError(message='报表无法匹配到文件夹，不会升级')
            else:
                if catalog_id:
                    # 指定报表升级，如果没有匹配到文件夹，则放置到根目录下
                    update_upgrade_warning_log(f"指定报表升级，未匹配到文件夹，放置到根目录下")
                    catalog_id = ""
        # 根报表的levelcode
        root_report_level_code = DmpReportDb.generate_dashboard_level_code(catalog_id)

        # 所有报表id
        all_dashboard_id_list = list()
        dashboard_level_code_map = dict()
        pid_map_list = dict()
        # 构建仪表板的子报表levelcode
        self.get_hd_tree_level_code(list_hd_data, '', root_report_level_code, dashboard_level_code_map,
                                    pid_map_list, id_field=id_field)
        # 将自定义生成的level_code数量，更新维护到level_sequence
        ReportUpgrade.update_all_parent_id_sequence('dashboard', pid_map_list, False)

        # 终端类型
        terminal_type = ''
        if self.report_type == HdDashboardType.DASHBOARD.value:
            platform = DashboardPlatforms.PC.value
        else:
            platform = DashboardPlatforms.Mobile.value
            terminal_type = 'mobile_screen'

        for hd_dash_data in list_hd_data:
            # 提示信息
            dashboard_id = hd_dash_data.get(id_field)
            if hd_dash_data.get('parent_id') == '' and dashboard_id == hd_dash_data.get('root_id'):
                dashboard_type = DashboardType.File.value
                # 主报表挂载在哪个文件夹下
                parent_id = catalog_id
            else:
                # 子报表的父层级id
                parent_id = hd_dash_data.get('parent_id')
                dashboard_type = 'CHILD_FILE'

            # 子报表层级调整
            level_code = ''
            if dashboard_id in dashboard_level_code_map:
                level_code = dashboard_level_code_map.get(dashboard_id)

            dmp_data = {
                "level_code": level_code,
                "parent_id": parent_id,
                "dashboard_type": dashboard_type,
                "platform": platform,
                "terminal_type": terminal_type,
            }
            model = self.hd_dash_data2dmp_dash_model(hd_dash_data, dmp_data)
            editor_models.update({hd_dash_data.get(id_field): model})
            all_dashboard_id_list.append(hd_dash_data.get(id_field))

        list_model = editor_models.values()
        fields = ["id", "name", "theme", "type", "level_code", "platform", "terminal_type", "icon", "status", "cover",
                  "background", "rank", "parent_id", "layout", "is_show_mark_img", "new_layout_type", "is_highdata",
                  "erp_app_code", "create_type", "line_height"]
        DmpReportDb.replace_list_model("dashboard", list_model, fields)
        sub_c = len(list_hd_data) - 1
        # 写入报表的接收参数关联关系（筛选和变量关系）
        DashboardUpgrade.init_report_params(list_hd_data)

        update_upgrade_notice_log('报表和子报表升级完成，子报表数量：%s' % str(sub_c))
        return all_dashboard_id_list

    def init_hd_dashboard_application(self):
        """
        门户数据初始化
        :return:
        """
        report_type_name = ReportInfo.get_name(self.report_type)
        name = f'HD{report_type_name}门户'
        if self.report_type == HdDashboardType.DASHBOARD.value:
            platform = DashboardPlatforms.PC.value
        else:
            platform = "mobile_screen"
        # 存在则返回
        if DmpReportDb.data_is_exists("application", {"name": name, "platform": platform}):
            data = DmpReportDb.get_data("application", {"name": name, "platform": platform})
            return ApplicationModel(**data)

        nav_type = 0
        is_show_banner = 0
        banner_url = ""
        if self.report_type == HdDashboardType.M_REPORT.value:
            nav_type = 3
            # 获取HD banner信息
            banner_rs = self.hd_api.get_hd_by_api("get_m_report_banner")
            is_show_banner = banner_rs.get("is_disabled")
            banner_url = banner_rs.get("banner_image_url")

        args = {"name": name, "platform": platform, "nav_type": nav_type, "theme": "浅色",
                "description": "", "is_show_banner": is_show_banner,
                "banner_url": banner_url}
        application_model = ApplicationModel(**args)
        application_service.add_application(application_model)
        return application_model

    def hd_portal2dmp_portal_v2(self):
        """
        升级HD的仪表板首页到DMP
        :return:
        """
        report_type_name = ReportInfo.get_name(self.report_type)
        update_upgrade_notice_log(f'开始升级{report_type_name}门户')
        try:
            application_model = self.init_hd_dashboard_application()
            app_id = application_model.id

            # 1、仪表板目录文件夹升级 2、首页仪表板升级
            all_catalog_id_list = self._hd_catalog2dmp_catalog(app_id)
            self._hd_publish_report2dmp_function(app_id, all_catalog_id_list)

            # 门户启用
            application_service.enable_application(app_id, DashboardTypeAccessReleased.UserRole.value)
        except Exception as e:
            update_upgrade_err_log(f'{report_type_name}门户升级异常，异常信息：%s' % str(e))
            return False, f'{report_type_name}门户升级失败，错误信息：%s ' % str(e)
        update_upgrade_notice_log(f'{report_type_name}门户升级完成！')
        return f'{report_type_name}门户升级完成！'

    def _hd_catalog2dmp_catalog(self, app_id):
        """
        HighData首页文件夹升级逻辑
        :param app_id: dmp应用id
        :return:
        """
        report_type_name = ReportInfo.get_name(self.report_type)
        group_type = ReportInfo.get_group_type(self.report_type)
        update_upgrade_notice_log(f'{report_type_name}首页目录开始升级')

        if self.report_type == HdDashboardType.DASHBOARD.value:
            try:
                data_exists = self.hd_api.get_hd_by_api('publish_catalog_3_level', {"group_type": group_type})
                if data_exists:
                    update_upgrade_warning_log(f'{report_type_name}首页门户存在大于等于3层的目录，目录数：{len(data_exists)}')
            except Exception as e:
                update_upgrade_err_log(f'{report_type_name}首页门户目录大于等于3层检查异常，Err：' + str(e))

        editor_models = {}
        conditions = {"group_type": group_type}
        # 获取指定仪表板id数据，批量写入仪表板数据
        catalog_list = self.hd_api.get_hd_list(table_name="publish_catalog", conditions=conditions, order_by='sort asc')
        # 所有的文件夹id
        all_catalog_id_list = list()
        catalog_level_code_map = dict()
        pid_map_list = dict()
        # 构建仪表板文件夹的levelcode
        self.get_hd_tree_level_code(catalog_list, '', '', catalog_level_code_map, pid_map_list, id_field='catalog_id')
        ReportUpgrade.update_all_parent_id_sequence('function', pid_map_list, True, application_id=app_id)

        c = 0
        for catalog in catalog_list:
            catalog_id = catalog.get('catalog_id')
            level_code = ''
            if catalog_id in catalog_level_code_map:
                level_code = catalog_level_code_map.get(catalog_id)

            model = DashboardUpgrade.hd_dash_publish_catalog2dmp_function_catalog_model(catalog, level_code, app_id)
            editor_models.update({catalog_id: model})
            all_catalog_id_list.append(catalog_id)
            c += 1

        list_model = editor_models.values()
        fields = ["id", "name", "parent_id", "level_code", "icon", "application_id"]
        DmpReportDb.replace_list_model("function", list_model, fields)
        update_upgrade_notice_log(f'{report_type_name}首页目录升级完成，目录数量：%s' % str(c))
        return all_catalog_id_list

    def _hd_publish_report2dmp_function(self, app_id, all_catalog_id_list):
        """
        HighData首页报表升级到DMP
        :param app_id:
        :return list:
        """
        update_upgrade_notice_log('首页报表开始升级')
        editor_models = {}
        # 获取根目录下的报表信息
        all_catalog_id_list.append('')
        conditions = {"catalog_id": all_catalog_id_list}
        # 获取指定仪表板id数据，批量写入仪表板数据
        report_list = self.hd_api.get_hd_list(table_name="publish_catalog_report", conditions=conditions,
                                              order_by='sort asc')
        report_c = 0

        # 获取仪表板的最新名称
        report_name_map = self.get_publish_dashboard_name(report_list)

        for report in report_list:
            report_id = report.get('id')
            model = self.hd_dash_publish_report2dmp_function_report_model(report, app_id, report_name_map)
            # 报表层级levelcode
            model.level_code = ReportUpgrade.generate_function_level_code(model)
            editor_models.update({report_id: model})
            report_c += 1

        list_model = editor_models.values()
        fields = ["id", "name", "parent_id", "level_code", "icon", "icon_url", "application_id", "url"]
        DmpReportDb.replace_list_model("function", list_model, fields)
        update_upgrade_notice_log('首页报表升级完成，报表数量：%s' % str(report_c))
        return all_catalog_id_list

    def get_publish_dashboard_name(self, report_list):
        report_name_map = dict()
        report_id_list = ReportUpgrade.get_publish_report_id(report_list)
        if not report_id_list:
            return report_name_map

        table_name = ReportInfo.get_table_name(self.report_type)
        id_field = ReportInfo.get_id_field(self.report_type)
        name_field = ReportInfo.get_name_field(self.report_type)
        conditions = {id_field: report_id_list}
        report_list = self.hd_api.get_hd_list(table_name=table_name, conditions=conditions,
                                              fields=[id_field, name_field])
        if report_list:
            for report in report_list:
                report_name_map[report.get(id_field)] = report.get(name_field)
        return report_name_map

    def start_flow_calc_dashboard_chart_height(self, dashboard_id):  # 组件高度的异步作业启动
        if not self.token:
            update_upgrade_notice_log('无法计算移动端组件高度，无法找到对应租户')
            return
        hdchart_height_task = repository.get_one('dashboard_hdchart_height_task', {"dashboard_id": dashboard_id})
        if hdchart_height_task:
            status = hdchart_height_task.get('status')
            if status == 3:  # 说明上一次计算出错，更新状态为1
                repository.delete_data('dashboard_hdchart_height_task', {'dashboard_id': dashboard_id})
            else:
                return
        # 新增flow
        open_url = f'{self.yzs_domain}/report/v2/login/sso-login?token={self.token}&redirect_url=&redirect_url=%2Freport%2Fv2%2Fm-report%2Fedit%3FrepId%3D{dashboard_id}'
        flow_id = seq_id()
        data = {
            'flow_id': flow_id,
            'dashboard_id': dashboard_id,
            'status': 1,
            'open_url': open_url
        }
        # 创建flow和instance
        flow = FlowModel(id=flow_id, name='计算HD组件高度', type=FlowType.CalcHDHeight.value)
        node = FlowNodeModel(
            name=flow.name,
            type=FlowNodeType.CalcHDHeight.value,
        )
        flow.nodes = [node]
        flow_service.add_flow(flow)

        repository.add_data('dashboard_hdchart_height_task', data)
        # 执行流程
        flow_service.run_flow(flow_id, queue_name=self.queue_name)

    def wait_flow_calc_dashboard_chart_height(self, all_dashboard_id_list):
        update_upgrade_notice_log('开始等待计算highdata移动端组件高度的异步作业')
        if not self.token:
            return
        num = len(all_dashboard_id_list)
        sum_second = 0
        for dashboard_id in all_dashboard_id_list:
            where = {'dashboard_id': dashboard_id, "status": [2, 3]}
            i = 0
            while i < 36 * num:
                sum_second += 5
                hdchart_height = repository.get_list('dashboard_hdchart_height_task', where)
                if hdchart_height:
                    update_upgrade_notice_log(f'完成{dashboard_id}计算highdata移动端组件高度的异步作业，总耗时{i * 5}s')
                    break
                update_upgrade_notice_log(f'等待组件高度计算：报表id:{dashboard_id},等待次数：{i} 次')
                time.sleep(5)
                i += 1
        update_upgrade_notice_log(f'完成计算highdata移动端组件高度的异步作业，总耗时{sum_second}s')

    def upgrade_single_dashboard_dataset(self, dashboard_id, params):
        # 仪表板数据升级

        conditions = {"root_id": dashboard_id}
        table_name = ReportInfo.get_table_name(self.report_type)
        list_hd_data = self.hd_api.get_hd_list(table_name, conditions)
        id_field = ReportInfo.get_id_field(self.report_type)
        all_dashboard_id_list = [hd_dash_data.get(id_field) for hd_dash_data in list_hd_data]
        chart_table_name = ReportInfo.get_chart_table_name(self.report_type)

        list_hd_chart = self.hd_api.get_hd_list(chart_table_name, conditions={id_field: all_dashboard_id_list},
                                                order_by='parent_id asc, sort asc')
        dataset_id_list = list()
        for hd_chart in list_hd_chart:
            dataset_id = hd_chart.get('dataset_id_used_list').split(',')
            if not dataset_id:
                continue
            for used_id in dataset_id:
                if used_id and used_id not in dataset_id_list:
                    dataset_id_list.append(used_id)
        params['dataset_ids'] = dataset_id_list
        if dataset_id_list:
            dataset_service.dataset_upgrade(**params)


def dashboard_upgrade(dashboard_ids: list = None, params=None):  # NOSONAR
    """
    仪表板或移动报表升级的执行逻辑
    :param dashboard_ids:
    :param params: 升级的请求参数
    :return:
    """
    msg = ''
    try:
        if params is None:
            params = dict()
        dashboard_upgrade_obj = DashboardUpgrade(**params)
        report_type_name = ReportInfo.get_name(dashboard_upgrade_obj.report_type)
        update_upgrade_notice_log(f'{report_type_name}开始升级，传入参数 dashboard_ids ：%s' % str(dashboard_ids))
        # 指定仪表板id
        dashboard_ids_list = list()

        if dashboard_ids:
            dashboard_ids_list = dashboard_ids
            if isinstance(dashboard_ids, str):
                if dashboard_ids.find(',') > -1:
                    dashboard_ids_list = dashboard_ids.split(',')
                else:
                    dashboard_ids_list = [dashboard_ids]
            if len(dashboard_ids_list) == 1:  # 扩展规则，当只升级一个报表的时候就需要升级对应的数据集，因为很多时候很难直接找到报表对应的数据集
                dashboard_upgrade_obj.upgrade_single_dashboard_dataset(dashboard_ids_list[0], params)
        else:
            # 获取所有的仪表板id。按照仪表板id获取仪表板信息
            table_name = ReportInfo.get_table_name(dashboard_upgrade_obj.report_type)
            id_field = ReportInfo.get_id_field(dashboard_upgrade_obj.report_type)
            list_hd_data = dashboard_upgrade_obj.hd_api.get_hd_list(table_name,
                                                                    conditions={"parent_id": ""}, fields=[id_field])
            for item in list_hd_data:
                dashboard_ids_list.append(item.get(id_field))

        # 指定报表升级，不升级文件夹
        if not dashboard_upgrade_obj.ids:
            # 仪表板文件夹升级
            dashboard_upgrade_obj.hd_report_catalog_upgrade()
        # 仪表板升级
        msg += dashboard_upgrade_obj.dashboard_upgrade_exec(dashboard_ids_list)
        # 仪表板门户升级
        dashboard_upgrade_obj.hd_portal2dmp_portal_v2()
    except UserError as ue:
        update_upgrade_warning_log('报表升级异常，错误信息：%s' % ue.message)
        return False, '报表升级异常，异常信息：%s' % str(ue.message)
    except Exception as e:
        logger.exception(e)
        print(traceback.format_exc())
        update_upgrade_err_log('报表升级异常，异常信息：%s' % traceback.format_exc())
        return False, '报表升级异常，异常信息：%s' % str(e)

    return True, f'{report_type_name}升级完成！{msg}'
