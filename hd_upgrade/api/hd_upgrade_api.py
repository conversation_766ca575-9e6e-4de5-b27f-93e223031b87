#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json

import requests
import jwt
from hashlib import md5
from requests import RequestException
from dmplib import config
from dmplib.utils.errors import HttpError, UserError
from datetime import datetime, timedelta
from components import deshelper


class HdUpgradeApi:
    """
    HighData接口调用API
    """
    __slots__ = ['tenant_code', 'hd_host']

    API_PATH = 'report/api/dmp-to-hd'

    HIGH_DATA_SECRET_KEY = "My$oft_"

    def __init__(self, tenant_code, yzs_domain=None):
        super().__init__()
        self.tenant_code = tenant_code
        self.hd_host = config.get('Yzs.domain', 'https://qy-ci.fdccloud.com/')
        if yzs_domain:
            self.hd_host = yzs_domain

    def ping(self):
        """
        HighData 心跳接口检测
        :return:
        """
        try:
            return self.api_request('ping')
        except HttpError as e:
            raise UserError(message=str(e.description))

    def query(self, action_name, biz_params=None):
        """
        HighData数据查询接口
        :param action_name:
        :param biz_params:
        :return:
        """
        try:
            if not biz_params:
                biz_params = dict()
            params = {"action_name": action_name, "biz_params": biz_params}
            return self.api_request('query', params)
        except HttpError as e:
            raise UserError(message=str(e.description))

    def api_request(self, action_path, params: dict = None):
        """
        @param action_path:
        @param params:
        @return:
        """
        if params is None:
            params = dict()
        host = self.hd_host[: len(self.hd_host) - 1] if self.hd_host.endswith('/') else self.hd_host
        url = '%s/%s/%s' % (host, self.API_PATH, action_path)
        params["token"] = self.get_token()
        try:
            # 请求参数加密
            if "biz_params" in params and params.get("biz_params"):
                biz_params = params.get("biz_params")
                encode_data = json.dumps(biz_params)
                encode_data = deshelper.encrypt(encode_data)
                params["biz_params"] = encode_data
            response = requests.post(url, json=params, timeout=60)
        except Exception as be:
            msg = "请检查网络是否正常:{}|url:{}|parameters{}".format(str(be), url, str(params))
            raise HttpError(message=msg, status=response.status_code)
        if response.status_code == 200:
            result = response.json() if response.text else {'isSuccess': 0, 'message': '接口数据返回为空'}
            if not result.get('isSuccess'):
                raise UserError(message=result.get('message'))
            return result.get('result')
        else:
            s = ""
            if response.status_code == 404:
                s = "HighData接口不存在，"
            msg = f"{s}错误反馈信息：" + str(response.status_code) + ' , ' + str(response.reason)
            raise HttpError(message=msg, status=response.status_code)

    def get_token(self):
        """
        获取秘钥
        :return string:
        """
        try:
            payload = {"exp": self.get_exp(), "tenant_code": self.tenant_code}
            token = jwt.encode(payload, self._get_secret_key())
            return token
        except RequestException as e:
            raise UserError(message='获取jwt token失败:' + str(e))

    def _get_secret_key(self):
        """
        获取jwt 的秘钥
        :return string:
        """
        return md5(self.HIGH_DATA_SECRET_KEY.encode("utf-8") + self.tenant_code.encode("utf-8")).hexdigest()

    @staticmethod
    def get_exp():
        """
        获取exp时间
        :return:
        """
        # 获取当前时间
        d1 = datetime.now()
        # 当前时间加上60秒
        d2 = d1 + timedelta(seconds=60)
        return int(d2.timestamp())
