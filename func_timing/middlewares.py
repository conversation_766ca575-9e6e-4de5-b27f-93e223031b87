# -*- coding: utf-8 -*-
"""
    中间件
"""
# pylint: skip-file

import json
import time
from urllib import parse
import uuid

import hug
from loguru import logger

from dmplib.hug import g
from dmplib.redis import conn as conn_redis
from dmplib import config
from .constants import (
    STACK_TIME_MAP, G_STACK_FLAG,
    G_UUID_FLAG, G_SET_FLAG,
    REDIS_TEMPORARY_STORE_KEY,
    REDIS_TEMPORARY_STORE_KEY_TIMEOUT
)
from base.dmp_constant import __STACK_KEYWORD__


class FuncTimingMiddleware(object):
    STACK_FIELD = __STACK_KEYWORD__
    g_set_flag = G_SET_FLAG
    g_uuid_flag = G_UUID_FLAG
    g_stack_flag = G_STACK_FLAG
    total_stack_map = STACK_TIME_MAP

    def condition_is_ok(self, req):  # NOSONAR
        # 要么url带参数，要么cookies中带参数
        return self.rule_check(self.STACK_FIELD, req)

    def rule_check(self, field, req):
        # if 'api/dashboard_chart/chart/data' in req.relative_uri:
        if field in self._stringfy(req.params):
            return True
        if field in self._stringfy(req.cookies):
            return True
        if field in self._stringfy(req.user_agent):
            return True
        query_str = parse.urlparse(req.referer).query
        if field in self._stringfy(query_str):
            return True
        return False

    def _stringfy(self, s):
        if not s:
            return ''
        if not isinstance(s, str):
            return str(s)
        return s

    def init_g_stack_data(self):
        # 1. 生成一个请求的uuid标识
        g_uuid = str(uuid.uuid4())
        setattr(g, self.g_uuid_flag, g_uuid)
        # 2. 生成一个当前请请求的栈信息临时存储字典
        self.total_stack_map[g_uuid] = dict()

    def get_g_uuid(self):
        return getattr(g, self.g_uuid_flag, '')

    def process_request(self, req, resp):
        self.init_g_stack_data()
        if self.condition_is_ok(req=req):
            setattr(g, self.g_stack_flag, 1)
            setattr(req, '_stack', 1)
            setattr(req, '_start_stamp', time.time())

    def process_response(self, req, resp, _, req_succeeded=True):
        total_stack_map = self.total_stack_map
        g_uuid = self.get_g_uuid()
        if hasattr(req, "_stack"):
            try:
                start_stamp = getattr(req, '_start_stamp', 0)
                resp.set_header('Duration-Time', f'{(time.time() - start_stamp) * 1000:.2f}ms')
                resp.set_header('Content-Type', 'application/json')
                timing_info = total_stack_map.pop(g_uuid, {})
                stack_time_info = self.format_stack_time_info(timing_info)
                self.append_stack_info_to_response(resp, stack_time_info, g_uuid)
            except Exception as e:
                # 啥都不干，返回原先的东西
                import traceback
                resp.set_header('stack-message', str(e))
                logger.error(f"""<h1>本次分析失败，请刷新再试！</h1><br/>{traceback.format_exc()}""")
                # tb = traceback.format_exc().replace('\n', '<br/>')
                # resp.body = f"""<h1>本次分析失败，请刷新再试！</h1><br/>{tb}"""
        total_stack_map.pop(g_uuid, None)

    def append_stack_info_to_response(self, response: hug.Response, stack_time_info: dict, g_uuid: str):
        # 1. 如果接口的结果是json, 直接在json中添加stack节点
        # 2. 如果是其他类型， 生成一个临时url查看结果
        if isinstance(response.data, bytes):
            content = response.data.decode()
        else:
            content = response.data
        is_json, data = self.is_json(data=content)

        if is_json and response.status == hug.falcon.HTTP_200:
            data['stack'] = stack_time_info
            response.data = json.dumps(data).encode()
            return
        else:
            # 不是json数据
            timeout = REDIS_TEMPORARY_STORE_KEY_TIMEOUT
            conn = conn_redis()
            key = REDIS_TEMPORARY_STORE_KEY % {'g_uuid': g_uuid}
            conn.set(key, json.dumps(stack_time_info).encode(), timeout)
            domain = config.get('Domain.dmp', '')
            response.set_header('Stack-Info-Url', f'{domain}/api/func_timing/stack?g_uuid={g_uuid}')
            return

    @staticmethod
    def is_json(data):
        try:
            result = json.loads(data)
            is_json = True
        except:
            result = data
            is_json = False
        return is_json, result

    def format_stack_time_info(self, stack_map):
        nodes_map = getattr(config, '__hook_timing_func__', {})
        g_set = stack_map.pop(self.g_set_flag, set())

        # 按不同的g区分不同的取数过程
        timing_map = {}
        for hex_g_id in g_set:
            g_map = self._format_node(nodes_map, stack_map.copy(), hex_g_id)
            g_map = self._clean_empty(g_map)
            timing_map[hex_g_id] = g_map

        request_params = getattr(g, 'request_data', {}).get('params', {})
        __stack__ = str(request_params.get(__STACK_KEYWORD__, ''))
        if __stack__ == '2':
            return {'timing': timing_map}
        else:
            return {'timing': timing_map, 'stack_detail': stack_map}

    def _format_node(self, nodes_map, stack_map, hex_g_id):
        for node, val in nodes_map.items():
            if isinstance(val, list):
                node_key = self._format_list_name(node.func.__qualname__, stack_map, hex_g_id)
                node_val = [self._format_node(node_map, stack_map, hex_g_id) for node_map in val]
                return {node_key: node_val}
            elif val is None:
                # 真正的格式化场景,填充默认的nodes_map结构
                func_name = node.func.__qualname__
                func_record = {
                    full_func_name: record.get('timing', None)
                    for full_func_name, record in stack_map.get(func_name, {}).items()
                    if record.get('hex_g_id') == hex_g_id
                }
                return {func_name: func_record}
            # else:  # 理论上不会走到这里
            #     raise NotImplementedError('意外的格式化场景')

    def _format_list_name(self, func_name, stack_map, hex_g_id):
        func_data = stack_map.get(func_name, {})
        if not func_data:
            return func_name
        # 取所有的timing
        timings = [
            record.get('timing', '')
            for record in func_data.values() if record.get('hex_g_id') == hex_g_id
        ]
        timing_str = ','.join(timings)
        return f'{func_name}:{timing_str}'

    def _clean_empty(self, d):
        # 清除map中的空节点
        if not isinstance(d, (dict, list)):
            return d
        c = lambda v: v not in ([], {})
        if isinstance(d, list):
            return [v for v in (self._clean_empty(v) for v in d) if c(v)]
        return {k: v for k, v in ((k, self._clean_empty(v)) for k, v in d.items()) if c(v)}


def add_func_timing_middleware():
    from dmplib.hug.application import Application
    Application._init_middleware_kept = Application._init_middleware

    def _init_middleware(self):
        self._init_middleware_kept()
        if config.get('App.enable_profiling') in ('True', 'true', '1', 1):
            self.http.add_middleware(FuncTimingMiddleware())

    Application._init_middleware = _init_middleware
