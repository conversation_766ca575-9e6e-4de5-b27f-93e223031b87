#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : data_query.py
# @Author: guq  
# @Date  : 2022/1/26
# @Desc  :

from pymysql.cursors import Cursor as BaseCursor

from sqlalchemy.orm.session import Session
from redis.connection import Connection
from requests.sessions import Session as req_session

from func_timing.base import Node, root


# from dmplib.redis import RedisCache
# from dmplib.db.mysql_wrapper import SimpleMysql


def get_common_func_nodes():
    # 记录一些通用的函数调用方法
    # 为什么不能使用dmplib中的封装的mysql，redis方法
    # 因为dmplib在正式环境中so文件形式存在，里面的方式不是Python的function，无法进行处理，这里是使用三方库的函数
    # <cyfunction RedisCache.get_data at 0x7fb4cb78af60>   <cyfunction SimpleMysql._execute at 0x7f1ce772b6c0>

    # mysql
    # mysql = Node(SimpleMysql._execute, 99997, prev=root, extra={'type': 'mysql'})
    request_session = Node(req_session.request, 99996, prev=root, extra={'type': 'requests', 'record_result': True})
    mysql = Node(BaseCursor.execute, 99997, prev=root, extra={'type': 'mysql'})
    sqlalchemy = Node(Session.execute, 99998, prev=root, extra={'type': 'mysql'})

    # redis
    # redis = [
    #     Node(getattr(RedisCache, method), 99999, prev=root, extra={'type': 'redis'})
    #     for method in [
    #         'get_data', 'del_data', 'keys', 'set', 'get', 'incr', 'delete',
    #         'add', 'exists', 'expire', 'setnx', 'set_nx_ex', 'hget',
    #         'hgetall', 'hset', 'ttl', 'hdel', 'hmset', 'hmget', 'lpush',
    #         'lrange', 'scan', 'hexists'
    #     ]
    # ]
    redis = Node(Connection.send_command, 99999, prev=root, extra={'type': 'redis'})

    return [
        root,
        # mysql, sqlalchemy, *redis
        mysql, sqlalchemy, redis, request_session
        # sqlalchemy, redis, request_session
    ]
