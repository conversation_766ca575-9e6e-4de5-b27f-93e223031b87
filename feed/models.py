from base.enums import FlowType, EmailSendType, ChartFilterOperator, SubscribeCycleType
from base.models import QueryBaseModel, BaseModel
from flow.models import FlowModel

__author__ = 'apple'


class DashboardSubscribeDisplayFormatModel(BaseModel):
    __slots__ = [
        'subscribe_id',
        'dataset_id',
        'field_id',
        'display_format',
        'digit',
        'use_thousands',
        "value_unit",
        "show_value_unit",
    ]

    def __init__(self, **kwargs):
        """
        邮件订阅数值展示格式
        :param kwargs:
        """
        self.subscribe_id = None
        self.dataset_id = None
        self.field_id = None
        self.display_format = None
        self.digit = None
        self.use_thousands = None
        self.value_unit = None
        self.show_value_unit = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('subscribe_id', 'string', {'max': 36}))
        rules.append(('dataset_id', 'string', {'max': 36}))
        rules.append(('field_id', 'string', {'max': 36}))
        rules.append(("display_format", "in_range", {"range": [1, 2]}))
        rules.append(("use_thousands", "in_range", {"range": [0, 1]}))
        rules.append(("value_unit", "in_range", {"range": [0, 1, 2]}))
        rules.append(("show_value_unit", "in_range", {"range": [0, 1]}))


class DashboardFeedsModel(BaseModel):
    __slots__ = [
        'id',
        'dashboard_id',
        'name',
        'type',
        'subject_email',
        'send_frequency',
        "recipients",
        "addresser",
        "message",
        "report_from",
        "frequency_value",
        'flow',
        'release_url',
        'link_url',
        'user_token',
        'config_id',
        'dataset_ids',
    ]

    def __init__(self, **kwargs):
        """
        邮件订阅
        :param kwargs:
        """
        self.id = None
        self.dashboard_id = None
        self.type = None
        self.name = None
        self.subject_email = None
        self.send_frequency = None
        self.recipients = None
        self.addresser = None
        self.message = None
        self.report_from = None
        self.frequency_value = None
        self.release_url = None
        self.user_token = None
        # 简讯是否链接报告
        self.link_url = None
        self.config_id = None
        self.dataset_ids = None
        self.flow = DashboardFeedsFlowModel()
        self.display_format = []
        # 简讯当前发送时间
        self.send_time = None
        self.msg_subscribe_config = None
        self.biz_code = None
        self.app_code = None
        # 报表的原始查看url，不包括第三方应用鉴权信息
        self.report_real_release_url = None
        # 简讯关联应用的渠道ID
        self.party_id = None
        self.is_snap = None
        # 简讯过滤条件列表
        self.filters = []
        self.send_rules = []
        self.roles = []
        # 简讯重发的类型
        self.retry = None
        # 简讯的正文明细章节列表
        self.chapters = []
        # 简讯的拍照id
        self.snap_id = ""
        # 简讯的调度周期类型
        self.cycle_type = ""
        # 简讯调度周期时间段列表
        self.cycle_times = []
        # 是否为预览数据获取
        self.is_preview = False
        # 简讯是否可用
        self.is_available = 1
        # 简讯调度数据集id 列表
        self.sync_dataset_ids = []
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('subject_email', 'string', {'max': 1000}))
        # 不支持保存立即发送（产品需求）
        rules.append(
            (
                'send_frequency',
                'in_range',
                {'range': [e.value for e in EmailSendType.__members__.values() if e.value != EmailSendType.One.value]},
            )
        )
        return rules


class SubscribeConfigModel(BaseModel):
    __slots__ = ['id', 'name', 'corp_id', 'corp_secret', 'agent_id', 'app_code']

    def __init__(self, **kwargs):
        """
        简讯订阅配置
        :param kwargs:
        """
        self.id = None
        self.name = None
        self.corp_id = None
        self.corp_secret = None
        self.agent_id = None
        self.app_code = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        return rules


class FeedSendRulesModel(BaseModel):
    __slots__ = ['email_subscribe_id', 'dataset_id', 'rule_info']

    def __init__(self, **kwargs):
        """
        简讯订阅配置
        :param kwargs:
        """
        self.email_subscribe_id = ''
        self.dataset_id = ''
        self.rule_info = {}
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        # rules.append(('email_subscribe_id', 'string', {'max': 36}))
        rules.append(('dataset_id', 'string', {'max': 36}))

        self.rule_info__rule = self.rule_info.get('rule', '')
        self.rule_info__time_node = self.rule_info.get('time_node', '')
        self.rule_info__day_or_week = self.rule_info.get('day_or_week', '')
        self.rule_info__time_detail = self.rule_info.get('time_detail', '')
        rules.append(('rule_info__rule', 'in_range', {"range": ['早于', '晚于']}))
        rules.append(('rule_info__time_node', 'in_range', {"range": ['当日', '当周', '当月', '前一日', '前一周', '前一月']}))

        if '周' in self.rule_info__time_node:
            rules.append(('rule_info__day_or_week', 'in_range', {"range": range(1, 8)}))
        if '月' in self.rule_info__time_node:
            rules.append(('rule_info__day_or_week', 'in_range', {"range": range(1, 32)}))

        rules.append(('rule_info__time_detail', 'string', {'max': 36}))
        return rules


class DashboardFeedsFlowModel(FlowModel):
    def __init__(self, **kwargs):
        """
        订阅流程
        :param kwargs:
        """
        super().__init__(**kwargs)
        self.type = FlowType.Feeds.value


class FeedsQueryModel(QueryBaseModel):
    __slots__ = ['begin_date', 'end_date', 'type']

    def __init__(self, **kwargs):
        self.begin_date = None
        self.end_date = None
        self.type = None
        super().__init__(**kwargs)


class SubscribeConfigQueryModel(QueryBaseModel):
    __slots__ = ['begin_date', 'end_date', 'type']

    def __init__(self, **kwargs):
        self.begin_date = None
        self.end_date = None
        self.type = None
        super().__init__(**kwargs)


class MsgSubscribeConfigModel(BaseModel):

    def __init__(self, **kwargs):
        """
        简讯订阅的相关设置
        :param kwargs:
        """
        # 简讯订阅类型  1：文本简讯 2：图文简讯
        self.msg_type = 1
        # 图文简讯配图url地址
        self.msg_pic_url = None
        # 简讯内容是否显示发送日期，1：开启 0：关闭，默认为1
        self.is_show_date = 1
        # 时候开启简讯的数据集规则发送
        self.enable_send_rule = 0
        # 简讯发送用户来源，0：个人账号，1：角色。默认为0
        self.user_from = 0
        # 简讯编辑器模式，1：HTML混编模式，2：json模式。默认为1
        self.editor_mode = 1
        # 针对云助手应用，消息关联url选项 0：报告，1：详情页，默认报告
        self.first_entry = 0
        super().__init__(**kwargs)


class MobileSubscribeUserLogModel(BaseModel):
    __slots__ = ['id', 'email_subscribe_id', 'dashboard_id', 'msg_title', 'dashboard_name', 'report_url', 'user_id',
                 'user_name', 'account', 'msg_content', 'send_time', 'app_code', 'is_long']

    def __init__(self, **kwargs):
        """
        简讯订阅配置
        :param kwargs:
        """
        self.id = None
        self.email_subscribe_id = None
        self.dashboard_id = None
        self.msg_title = None
        self.dashboard_name = None
        self.report_url = None
        self.user_id = None
        self.user_name = None
        self.account = None
        self.msg_content = None
        self.send_time = None
        self.app_code = None
        self.is_long = None
        super().__init__(**kwargs)


class MobileSubscribeSendDetailLogModel(BaseModel):
    __slots__ = ['id', 'email_subscribe_id', 'log_id', 'user_id', 'user_name', 'account', 'plan_send_time',
                 'actual_send_time', 'app_code', 'error_reason', 'status', 'retry']

    def __init__(self, **kwargs):
        """
        简讯消息发送用户渠道状态明细表
        :param kwargs:
        """
        self.id = None
        self.email_subscribe_id = None
        self.log_id = None
        self.user_id = None
        self.user_name = None
        self.account = None
        self.plan_send_time = None
        self.actual_send_time = None
        self.app_code = None
        self.error_reason = None
        self.status = None
        self.retry = None
        super().__init__(**kwargs)


class MobileSubscribeSendDetailLogQueryModel(QueryBaseModel):
    __slots__ = ['log_id', 'status']

    def __init__(self, **kwargs):
        self.log_id = None
        self.status = None
        super().__init__(**kwargs)


class MobileSubscribeFilterModel(BaseModel):
    __slots__ = ['email_subscribe_id', 'dataset_id', 'dataset_field_id', 'col_name', 'operator', 'col_value']

    def __init__(self, **kwargs):
        """
        单图过滤模型
        :param kwargs:
        """
        self.email_subscribe_id = None
        self.dataset_id = None
        self.dataset_field_id = None
        self.col_name = None
        self.operator = None
        self.col_value = None

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['email_subscribe_id', 'dataset_id', 'dataset_field_id'], 'string', {'max': 36}))
        rules.append((['col_name', 'operator'], 'string', {'required': True}))
        rules.append(
            (
                'operator',
                'in_range',
                {'range': [e.value for e in ChartFilterOperator.__members__.values()], 'required': False},
            )
        )
        return rules

    @staticmethod
    def get_table_name():
        return 'mobile_subscribe_filter'


class MobileSubscribeRoleModel(BaseModel):
    __slots__ = ['email_subscribe_id', 'role_id']

    def __init__(self, **kwargs):
        """
        简讯角色
        :param kwargs:
        """
        self.email_subscribe_id = None
        self.role_id = None

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['role_id'], 'string', {'max': 36}))
        rules.append((['role_id'], 'string', {'required': True}))
        return rules

    @staticmethod
    def get_table_name():
        return 'mobile_subscribe_role'


class MobileSubscribeChapterModel(BaseModel):
    def __init__(self, **kwargs):
        """
        简讯的明细段落数据
        :param kwargs:
        """
        self.id = None
        self.email_subscribe_id = None
        self.dataset_id = None
        self.chapter_name = None
        self.chapter_message = None

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['chapter_message'], 'string', {'required': True}))
        return rules

    @staticmethod
    def get_table_name():
        return 'mobile_subscribe_chapters'
