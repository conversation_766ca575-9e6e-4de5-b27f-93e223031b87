#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Created by tanzy on 2019/04/10.
"""

from dmplib.saas.project import get_db


def get_feeds_by_dashboard_ids(dashboard_ids):
    """
        根据dashboard_ids获取所有的相关的feeds记录
    :param dashboard_ids:
    :return:
    """
    sql = """
    SELECT * FROM dashboard_email_subscribe WHERE `dashboard_id` in %(dashboard_ids)s
    """
    params = {'dashboard_ids': dashboard_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_subscribe_filter_by_dataset_id(dataset_id):
    """
    根据数据集id去除获取简讯过滤条件
    :param dataset_id:
    :return:
    """
    sql = """
    SELECT
	DISTINCT f.dataset_field_id,f.email_subscribe_id,s.subject_email FROM mobile_subscribe_filter f
	LEFT JOIN dashboard_email_subscribe AS s ON f.email_subscribe_id = s.id 
    WHERE `dataset_id` = %(dataset_id)s
    GROUP BY f.email_subscribe_id,f.dataset_field_id
    """
    params = {'dataset_id': dataset_id}
    with get_db() as db:
        return db.query(sql, params)


def get_subscribe_by_dataset_id(dataset_id):
    """
    根据数据集id获取相关的简讯
    :param dataset_id:
    :return:
    """
    sql = """
    SELECT * FROM dashboard_email_subscribe WHERE dataset_ids like %(dataset_id)s
    """
    params = {'dataset_id': f"%{dataset_id}%"}
    with get_db() as db:
        return db.query(sql, params)


def get_subscribe_chapter_by_id(feed_id):
    """
    根据简讯id获取明细段落
    :param feed_id:
    :return:
    """
    sql = """
    SELECT id,dataset_id,chapter_message FROM mobile_subscribe_chapters 
    WHERE email_subscribe_id = %(email_subscribe_id)s
    """
    params = {'email_subscribe_id': feed_id}
    with get_db() as db:
        return db.query(sql, params)
