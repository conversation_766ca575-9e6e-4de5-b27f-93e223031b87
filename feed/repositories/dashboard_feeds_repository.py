#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2018/3/2.
"""
import json
from typing import List

from loguru import logger
from dmplib.saas.project import get_db
from base import repository
from feed.models import (
    DashboardSubscribeDisplayFormatModel,
    MobileSubscribeFilterModel,
    MobileSubscribeRoleModel,
    MobileSubscribeChapterModel
)
from base.enums import SubscribeSendUserFrom


def get_feeds_or_dashboard(feeds_id):
    """
    获取关联报告的邮件订阅数据
    :param feeds_id:
    :return:
    """
    sql = """
    SELECT a.*, b.name as dashboard_name, b.biz_code, 
    c.id as app_record_id, c.name as app_name, c.app_id, c.app_secret,c.agent_id, c.msg_send_type, c.app_service_type,
    d.id as party_id, d.name as party_name, d.corp_id, d.corp_secret, d.app_code
    FROM dashboard_email_subscribe a
    LEFT JOIN dashboard b ON a.`dashboard_id` = b.`id`
    LEFT JOIN third_party_app c ON a.`config_id` = c.`id`
    LEFT JOIN third_party d ON c.`third_party_id` = d.`id`
    WHERE a.`id`=%(feeds_id)s
    """
    params = {'feeds_id': feeds_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dashboard_feeds(feeds_id):
    """
    获取邮件订阅数据
    :param feeds_id:
    :return:
    """
    sql = """
    SELECT `id`,`dashboard_id`,`subject_email`,`send_frequency`,`recipients`,`addresser`,`message`,`report_from`,
    `release_url`, `frequency_value`, `config_id`, `type`, `msg_subscribe_config`, `cycle_type`, `cycle_times`
     FROM dashboard_email_subscribe
    WHERE id=%(feeds_id)s
    """
    params = {'feeds_id': feeds_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dashboard_feeds_list(query_model):
    sql = """
            select e.id, e.dashboard_id, e.subject_email,e.send_frequency ,e.recipients,e.addresser,
            e.message, e.report_from ,e.frequency_value, e.modified_on, e.created_on, e.created_by,
            e.msg_subscribe_config,e.cycle_type,e.cycle_times,e.is_available,
            d.`name` as dashboard_name ,f.run_status,f.status, f.schedule, e.is_snap,
            tp.app_code
            from dashboard_email_subscribe e
            left join dashboard d on e.dashboard_id = d.id
            left join flow f on f.id = e.id
            left join third_party_app tpa on tpa.id = e.config_id
            left join third_party tp on tp.id = tpa.third_party_id
          """
    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append('e.`subject_email` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.begin_date:
        wheres.append('TO_DAYS(e.`created_on`)>=TO_DAYS(%(begin_date)s)')
        params['begin_date'] = query_model.begin_date
    if query_model.end_date:
        wheres.append('TO_DAYS(e.`created_on`)<=TO_DAYS(%(end_date)s)')
        params['end_date'] = query_model.end_date
    if query_model.type == '简讯订阅':
        wheres.append('e.`type`=%(type)s')
        params['type'] = query_model.type
    # 不为简讯订阅时，type默认查邮件订阅和空
    else:
        wheres.append('(e.`type`="邮件订阅" or e.`type`="" or e.type is null)')

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY ' + (','.join(sorts) if sorts else 'e.`created_on` DESC')
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        item_list = db.query(sql, params)
        feed_format_list(item_list)
        query_model.items = item_list
    return query_model


def feed_format_list(item_list):
    """
    简讯列表的格式化
    1、角色名称显示
    2、简讯状态的显示
    :param item_list:
    :return:
    """
    if not item_list:
        return False
    feed_id_list = []
    for item in item_list:
        feed_id_list.append(item.get('id'))
    if not feed_id_list:
        return False
    # 角色显示
    role_dict = get_feeds_role_list(feed_id_list)
    # 简讯状态显示
    status_dict = get_feeds_status_list(feed_id_list)

    for item in item_list:
        feed_id = item.get('id')
        if role_dict.get(feed_id):
            item['recipients'] = json.dumps(role_dict.get(feed_id), ensure_ascii=False)
        if status_dict.get(feed_id):
            item['run_status'] = status_dict.get(feed_id)


def get_feeds_status_list(feed_id_list):
    """
    获取简讯的最新一条详情记录状态
    :param feed_id_list:
    :return:
    """
    res = get_subscribe_detail_by_email_subscribe_id(feed_id_list)
    status_dict = {}
    for r in res:
        if r.get('status'):
            status_dict[r.get("email_subscribe_id")] = r.get('status')
    return status_dict


def get_feeds_role_list(feed_id_list):
    """
    简讯列表支持显示角色
    :param feed_id_list:
    :return:
    """
    role_dict = {}
    res = get_role_by_feed_ids(feed_id_list)
    if not res:
        return role_dict
    for r in res:
        email_subscribe_id = r.get("email_subscribe_id")
        if r.get("email_subscribe_id") not in role_dict:
            role_dict[email_subscribe_id] = []
        role_dict[email_subscribe_id].append({"name": r.get('name')})
    return role_dict


def get_dashboard_feeds_details(query_model):
    sql = """SELECT i.`id`,i.`name`,i.`flow_id`,i.`type`,i.`startup_time`,i.`end_time`,i.`status`,
         TIMESTAMPDIFF(SECOND,i.startup_time,i.end_time) AS running_time, d.relevancy_url, d.subject_email,
         d.type as subscribe_type, d.`failure_recipients`, d.`send_result`,d.`status` as detail_status,
         d.dashboard_id, d.dashboard_name
         FROM `instance` i
         left join dashboard_email_subscribe_detail d on d.`id` = i.`id` """
    params = {}
    wheres = []
    if query_model.flow_id:
        wheres.append('`flow_id` = %(flow_id)s')
        params['flow_id'] = query_model.flow_id
    if query_model.keyword:
        wheres.append('d.`subject_email` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.type:
        wheres.append('`type` = %(type)s')
        params['type'] = query_model.type
    if query_model.status:
        wheres.append('`status` = %(status)s')
        params['status'] = query_model.status
    if query_model.begin_date:
        wheres.append('TO_DAYS(IFNULL(i.`startup_time`,i.`created_on`))>=TO_DAYS(%(begin_date)s)')
        params['begin_date'] = query_model.begin_date
    if query_model.end_date:
        wheres.append('TO_DAYS(IFNULL(i.`startup_time`,i.`created_on`))<=TO_DAYS(%(end_date)s)')
        params['end_date'] = query_model.end_date
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY IFNULL(i.`startup_time`,i.`created_on`) DESC '
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_dataset_infos(dataset_ids):
    sql = """select id, name from dataset where `id` in %(dataset_ids)s"""
    with get_db() as db:
        return db.query(sql, {"dataset_ids": dataset_ids})


def get_dataset_field_infos(dataset_ids):
    sql = """select id, dataset_id, alias_name, note, col_name, origin_col_name,
            data_type, visible, field_group, `rank`, `type` from dataset_field where `dataset_id` in %(dataset_ids)s
            ORDER BY `rank`"""
    with get_db() as db:
        return db.query(sql, {"dataset_ids": dataset_ids})


def get_config_name_by_ids(config_ids):
    sql = """select `id`, `name` from mobile_subscribe_config WHERE id in %(config_ids)s"""
    with get_db() as db:
        return db.query(sql, {"config_ids": config_ids})


def get_subscribe_config_list(query_model):
    sql = """
            select `id`, `name`, `corp_id`, `corp_secret`, `agent_id`, `app_code`
            from mobile_subscribe_config
          """
    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append('`name` LIKE %(keyword)s')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    if query_model.begin_date:
        wheres.append('TO_DAYS(`created_on`)>=TO_DAYS(%(begin_date)s)')
        params['begin_date'] = query_model.begin_date
    if query_model.end_date:
        wheres.append('TO_DAYS(`created_on`)<=TO_DAYS(%(end_date)s)')
        params['end_date'] = query_model.end_date

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY ' + (','.join(sorts) if sorts else '`created_on` DESC')
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_dataset_by_dashboard_id(dashboard_id):
    """
    根据报告ID获取所有数据集内容
    :param dashboard_id:
    :return:
    """
    sql = """
        select DISTINCT d.id as dataset_id, d.name as dataset_name
        from dataset d
        INNER JOIN dashboard_chart c on c.source = d.id
        INNER JOIN dashboard b on b.id = c.dashboard_id
        where b.id = '{}'
    """.format(
        dashboard_id
    )

    with get_db() as db:
        return db.query(sql)


def get_subscribes_display_format(subscribe_id: str):
    """
    获取所有订阅的格式配置
    :param subscribe_id:
    :return:
    """
    return repository.get_list(
        table_name="dashboard_subscribe_display_format",
        conditions={"subscribe_id": subscribe_id},
        order_by=[("created_on", "DESC")],
    )


def replace_subscribes_display_format(
    subscribe_id: str, display_format_list: List[DashboardSubscribeDisplayFormatModel]
):
    """
    替换邮件订阅
    :param subscribe_id:
    :param display_format_list:
    :return:
    """
    with get_db() as db:
        try:
            db.begin_transaction()
            list_data = [item.get_dict() for item in display_format_list]
            db.delete('dashboard_subscribe_display_format', {"subscribe_id": subscribe_id})
            if list_data:
                db.insert_multi_data(
                    'dashboard_subscribe_display_format',
                    list_data,
                    [
                        'subscribe_id',
                        'dataset_id',
                        'field_id',
                        'display_format',
                        'digit',
                        'use_thousands',
                        "value_unit",
                        "show_value_unit",
                    ],
                )
            db.commit()
            return True, ''
        except Exception as e:
            db.rollback()
            return False, str(e)


def batch_insert_subscribes_display_format(list_data):
    """
    批量插入数据展示格式
    :param list_data:
    :return:
    """
    with get_db() as db:
        fields = [
            'subscribe_id',
            'dataset_id',
            'field_id',
            'display_format',
            'digit',
            'use_thousands',
            'value_unit',
            'show_value_unit',
        ]
        return db.insert_multi_data("dashboard_subscribe_display_format", list_data, fields)


def get_dashboard_feeds_by_email_name(email_name):
    """
    按邮件名称获取邮件订阅
    :param email_name:
    :return:
    """
    sql = """
    SELECT `id`,`recipients` FROM dashboard_email_subscribe
    WHERE recipients like %(email_name)s
    """
    params = {'email_name': '%' + email_name + '%'}
    with get_db() as db:
        return db.query(sql, params)


def update_dashboard_feeds_email(feed_id, recipients):
    """
    更新邮件订阅发送用户的邮件信息
    :param feed_id:
    :param recipients:
    :return:
    """
    return repository.update_data("dashboard_email_subscribe",
                                  data={"recipients": recipients}, condition={"id": feed_id})


def get_feed_app(app_code):
    """
    依据简讯应用code获取应用信息
    :param app_code:
    :return:
    """
    if not app_code:
        return dict()
    return repository.get_one("cloud_apps", conditions={"app_code": app_code},
                              fields=["app_id", "app_code", "app_name", "api_host", "channel_app_id",
                                      "channel_app_secret"],
                              from_config_db=True)


def get_mobile_subscribe_config_by_id(mobile_subscribe_id):
    """
    按应用id获取应用信息
    :param mobile_subscribe_id:
    :return:
    """
    return repository.get_one("mobile_subscribe_config", conditions={"id": mobile_subscribe_id},
                              fields=['id', 'name', 'corp_id', 'corp_secret', 'agent_id', 'app_code'])


def get_mobile_subscribe_user_log(log_id, user_id, account):
    """
    获取某条简讯内容的记录
    :param log_id:
    :param user_id:
    :param account:
    :return:
    """
    sql = """
    SELECT `id`,`dashboard_id`,`email_subscribe_id`,`msg_title`,`dashboard_name`,`report_url`, `user_name`,
    `account`, `msg_content`, `send_time`, `app_code`, `is_long` FROM mobile_subscribe_user_log
    WHERE id=%(log_id)s and user_id=%(user_id)s and account=%(account)s
    """
    params = {'log_id': log_id, 'user_id': user_id, 'account': account}
    with get_db() as db:
        return db.query_one(sql, params)


def batch_insert_subscribes_send_log(list_data):
    """
    批量插入简讯用户渠道消息发送状态明细
    :param list_data:
    :return:
    """
    with get_db() as db:
        fields = [
            'id',
            'email_subscribe_id',
            'log_id',
            'user_id',
            'user_name',
            'account',
            'plan_send_time',
            'app_code',
            'retry',
        ]
        return db.insert_multi_data("mobile_subscribe_send_detail_log", list_data, fields)


def batch_update_subscribes_send_detail_status(list_data, update_fields):
    """
    批量更新用户渠道消息发送状态
    :param list_data:
    :param update_fields:
    :return:
    """
    if not list_data or not update_fields:
        return 0
    params = {}
    values = []
    tmp = 0
    fields = list_data[0].keys()
    for data in list_data:
        if not isinstance(data, dict):
            continue
        tmp_val = []
        for c in fields:
            if not isinstance(c, str):
                continue
            p_name = c + '_' + str(tmp)
            tmp_val.append('%(' + p_name + ')s')
            params[p_name] = data.get(c)
        values.append('(' + ','.join(tmp_val) + ')')
        tmp += 1
    if not values:
        return 0
    sql = 'INSERT INTO {table}({cols}) VALUES {values}'.format(
        table='mobile_subscribe_send_detail_log', cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
    )
    update_field_list = []
    for field in update_fields:
        update_field_list.append(f"{field}=values({field})")
    update_field_sql = ','.join(update_field_list)

    sql = sql + " on duplicate key update " + update_field_sql + ";"
    with get_db() as db:
        return db.exec_sql(sql, params)


def get_mobile_subscribe_send_detail_log(query_model):
    """
    获取简讯发送用户渠道消息状态明细
    :param query_model:
    :return:
    """
    sql = """SELECT `id`,`email_subscribe_id`,`log_id`,`user_name`,`account`,`email`,
    `actual_send_time`,`status`,`error_reason` FROM `mobile_subscribe_send_detail_log`"""
    params = {}
    wheres = []
    if query_model.log_id:
        wheres.append('`log_id` = %(log_id)s')
        params['log_id'] = query_model.log_id
    if query_model.status:
        wheres.append('`status` = %(status)s')
        params['status'] = query_model.status
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY `actual_send_time` DESC '
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def batch_insert_mobile_subscribe_filters(list_data):
    """
    批量插入简讯过滤条件
    :param list_data:
    :return:
    """
    with get_db() as db:
        fields = [
            'email_subscribe_id',
            'dataset_id',
            'dataset_field_id',
            'col_name',
            'operator',
            "col_value",
        ]
        return db.insert_multi_data(MobileSubscribeFilterModel.get_table_name(), list_data, fields)


def batch_insert_send_rules(list_data):
    """
    批量插入简讯过滤条件
    :param list_data:
    :return:
    """
    with get_db() as db:
        fields = [
            'email_subscribe_id',
            'dataset_id',
            'rule_info',
        ]
        return db.insert_multi_data('mobile_subscribe_rules', list_data, fields)


def batch_insert_mobile_subscribe_roles(list_data):
    """
    批量插入简讯角色列表
    :param list_data:
    :return:
    """
    with get_db() as db:
        fields = [
            'email_subscribe_id',
            'role_id',
        ]
        return db.insert_multi_data(MobileSubscribeRoleModel.get_table_name(), list_data, fields)


def batch_insert_mobile_subscribe_chapters(list_data):
    """
    批量插入简讯明细段落列表
    :param list_data:
    :return:
    """
    with get_db() as db:
        fields = [
            'id',
            'email_subscribe_id',
            'dataset_id',
            'chapter_name',
            'chapter_message',
        ]
        return db.insert_multi_data(MobileSubscribeChapterModel.get_table_name(), list_data, fields)


def replace_mobile_subscribe_data(subscribe_id: str, model_list, model_obj=None):
    """
    更新简讯附加数据
    :param subscribe_id:
    :param model_list:
    :param model_obj: 数据的model对象
    :return:
    """
    if model_list:
        model = model_list[0]
    else:
        model = model_obj
    if not model:
        return False
    fields = []
    if isinstance(model, MobileSubscribeFilterModel):
        fields = ['email_subscribe_id',
                  'dataset_id',
                  'dataset_field_id',
                  'col_name',
                  'operator',
                  "col_value"]
    elif isinstance(model, MobileSubscribeRoleModel):
        fields = ['email_subscribe_id', 'role_id']
    elif isinstance(model, MobileSubscribeChapterModel):
        fields = ['id', 'email_subscribe_id', 'dataset_id', 'chapter_name', 'chapter_message']
    if not fields:
        return False
    with get_db() as db:
        try:
            db.begin_transaction()
            list_data = [item.get_dict() for item in model_list]
            db.delete(model.get_table_name(), {"email_subscribe_id": subscribe_id})
            if list_data:
                db.insert_multi_data(
                    model.get_table_name(),
                    list_data,
                    fields,
                )
            db.commit()
            return True, '更新成功'
        except Exception as e:
            db.rollback()
            logger.error(f"数据库更新失败，err：{str(e)}")
            return False, str(e)


def get_mobile_subscribe_filter_list(feed_id):
    """
    获取简讯的过滤条件列表
    :param feed_id:
    :return:
    """
    return repository.get_list(
        table_name=MobileSubscribeFilterModel.get_table_name(),
        fields=['email_subscribe_id', 'dataset_id', 'dataset_field_id', 'col_name', 'operator', 'col_value'],
        conditions={"email_subscribe_id": feed_id},
        order_by=[("created_on", "DESC")],
    )


def get_mobile_subscribe_send_rules(feed_id):
    """
    获取简讯的过滤条件列表
    :param feed_id:
    :return:
    """
    return repository.get_list(
        table_name='mobile_subscribe_rules',
        fields=['email_subscribe_id', 'dataset_id', 'rule_info'],
        conditions={"email_subscribe_id": feed_id},
    )


def get_mobile_subscribe_roles(feed_id, fields='*'):
    """
    获取简讯的角色列表
    :param feed_id:
    :param fields:
    :return:
    """
    return repository.get_list(
        table_name=MobileSubscribeRoleModel.get_table_name(),
        fields=fields,
        conditions={"email_subscribe_id": feed_id},
    )


def get_mobile_subscribe_role_ids(feed_id):
    """
    获取简讯的角色id列表
    :param feed_id:
    :return:
    """
    return repository.get_columns(
        MobileSubscribeRoleModel.get_table_name(),
        {"email_subscribe_id": feed_id},
        'role_id'
    )


def get_role_by_feed_ids(feed_id_list):
    """
    按简讯id获取角色列表
    :param feed_id_list: 简讯id列表
    :return:
    """
    sql = """
    select sr.email_subscribe_id, r.name
    from mobile_subscribe_role sr inner join user_role r on r.id=sr.role_id
    where sr.email_subscribe_id in %(feed_id_list)s
    """
    params = {'feed_id_list': feed_id_list}
    with get_db() as db:
        result = db.query(sql, params)
    return result


def get_mobile_subscribe_chapters(feed_id, fields='*'):
    """
    获取简讯的角色列表
    :param feed_id:
    :param fields:
    :return:
    """
    return repository.get_list(
        table_name=MobileSubscribeChapterModel.get_table_name(),
        fields=fields,
        conditions={"email_subscribe_id": feed_id},
    )


def get_erp_user_by_account(account_list):
    """
    根据渠道名称和数据账号查找erp的用户信息
    :param account_list:
    :return:
    """
    if not account_list:
        return []
    sql = "SELECT u.user_id,u.name,u.account FROM `user_source_user` u " \
          "left join user_source us on u.user_source_id = us.id " \
          "where us.name = 'ERP渠道用户' and u.account in %(account_list)s"
    params = {"account_list": account_list}
    return repository.get_data_by_sql(sql, params)


def get_subscribe_detail_by_email_subscribe_id(email_subscribe_id_list):
    """
    获取多条简讯最新一条的发送详情列表
    :param email_subscribe_id_list:
    :return:
    """
    sql = """select a.id,a.email_subscribe_id,a.status,a.created_on from dashboard_email_subscribe_detail a,
            (
            SELECT email_subscribe_id,MAX(created_on) as last_created_on FROM `dashboard_email_subscribe_detail`
            where email_subscribe_id in %(email_subscribe_id)s
            group by email_subscribe_id
            ) b 
            where a.email_subscribe_id = b.email_subscribe_id 
            and a.created_on = b.last_created_on
            """
    with get_db() as db:
        return db.query(sql, {"email_subscribe_id": email_subscribe_id_list})


def query_sync_dataset(sync_dataset_ids):
    """
    按调度数据集id 查找调度周期信息
    :param sync_dataset_ids:
    :return:
    """
    if not sync_dataset_ids:
        return []
    sql = """select d.id,d.name,f.id as flow_id,f.name as flow_name,f.type as flow_type,f.schedule from dataset as d 
    inner join flow as f on d.id = f.id where d.id in %(sync_dataset_ids)s and f.status = '启用';"""
    params = {"sync_dataset_ids": sync_dataset_ids}
    return repository.get_data_by_sql(sql, params)


def get_subscribe_schedule_dataset(project_code, dataset_ids):
    """
    获取简讯使用的调度数据集
    :param project_code:
    :param dataset_ids:
    :return:
    """
    sql = """
    SELECT id FROM `dataset` where id in %(dataset_ids)s and (connect_type = '' or connect_type is null)
    """
    params = {'dataset_ids': dataset_ids}
    with get_db(project_code) as db:
        return db.query_columns(sql, params)


def get_subscribe_by_dataset_id(dataset_id):
    """
    查找使用了当前数据集的简讯
    :param dataset_id:
    :return:
    """
    sql = "select id from dashboard_email_subscribe where dataset_ids like '%{dataset_id}%'".format(
        dataset_id=dataset_id
    )
    with get_db() as db:
        return db.query(sql)


def get_all_subscribe_dataset_ids(project_code):
    """
    获取租户的所有简讯使用的数据集
    :param project_code:
    :return:
    """
    sql = "select dataset_ids from dashboard_email_subscribe"
    with get_db(project_code) as db:
        return db.query_columns(sql)
