import bleach

from components.fast_logger import FastLogger


def subscribe_fast_log_record(subscribe_type, action, feed_name, feed_model, err_obj, traceback_msg='', extra={}):
    """
    简讯的日志记录
    :param subscribe_type:
    :param action:
    :param feed_name:
    :param feed_model:
    :param err_obj:
    :param traceback_msg:
    :param extra:
    :return:
    """
    biz_type = FastLogger.BizType.EMAIL_SUBSCRIBE if subscribe_type == 'email' \
        else FastLogger.BizType.PERSONAL_SUBSCRIBE

    error_type = '未知错误'
    if action == 'add':
        error_type = FastLogger.ErrorType.SUBSCRIBE_ADD_ERROR
    elif action == 'edit':
        error_type = FastLogger.ErrorType.SUBSCRIBE_EDIT_ERROR
    elif action == 'preview':
        # 仅个人简讯支持预览
        error_type = FastLogger.ErrorType.SUBSCRIBE_PREVIEW_ERROR
        biz_type = FastLogger.BizType.PERSONAL_SUBSCRIBE

    extra["feed_model"] = feed_model.get_dict()
    log_data = {
        "module_type": FastLogger.ModuleType.SUBSCRIBE,
        "biz_type": biz_type,
        "biz_id": feed_model.id,
        "biz_name": feed_name,
        "error_type": f"{biz_type}{error_type}",
        "error_data_id": feed_model.id,
        "error_msg": str(err_obj),
        "error_traceback": traceback_msg,
        "extra_info": extra
    }
    FastLogger.BizErrorFastLogger(**log_data).record()


def xss_clear(html):
    tags = {"html", "head", "title", "style", "body", "br", "header", "img", "div", "span", "section", "a", "p", "h1",
            "h2", "h3", "ul", "li", "i", "button", "input", "textarea", "select", "option", "footer", "meta", "samp"}
    attrs = {
        "header": ['class', 'style'],
        "img": ['src', 'height', 'width'],
        "div": ['class', 'style'],
        "span": ['class', 'style'],
        "section": ['class', 'style'],
        "a": ['class', 'href', 'style', 'target'],
        "p": ['class', 'style'],
        "h1": ['class', 'style'],
        "h2": ['class', 'style'],
        "h3": ['class', 'style'],
        "ul": ['class', 'style'],
        "li": ['class', 'style'],
        "i": ['class', 'style'],
        "button": ['class', 'style'],
        "input": ['class', 'style', 'value'],
        "textarea": ['class', 'style'],
        "select": ['class', 'style'],
        "option": ['class', 'style', 'value'],
        "footer": ['class', 'style'],
        "samp": ['class', 'style', 'value', 'title', 'data-datasetid', 'data-colid']
    }
    return bleach.clean(html ,tags=tags, attributes=attrs)