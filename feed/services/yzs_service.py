#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import json

from base import repository
from dmplib.hug import g
from dmplib.utils.errors import UserError
from feed.repositories import dashboard_feeds_repository
from feed.services import dashboard_feeds_service
from components.yzs_api import YzsApi
from feed.services.wechat_service import WechatService
from feed.services.wechat_service import get_textcard_data


class YzsService(WechatService):
    """
    云助手消息处理service
    Author: lul05
    Time: 2020-11-18 10:22:46
    """

    def __init__(self, project_code, feed_id, feed_detail_id, yzs_api_host, yzs_corp_secret_key):
        self.yzs_api = YzsApi(project_code, yzs_api_host, yzs_corp_secret_key)
        self.project_code = project_code
        self.feed_id = feed_id
        self.feed_detail_id = feed_detail_id

    def validate_feed_model(self, feed_model):
        if not feed_model.recipients:
            msg = "收件人不能为空"
            self.record_log("ERROR", msg)
            raise UserError(message=msg)

    def send_enterprise_yzs(self, feed_model):
        """
        向云助手平台发送消息
        :param feed.models.DashboardFeedsModel feed_model:
        :return:
        """
        self.validate_feed_model(feed_model)

        try:
            feed_model.recipients = json.loads(feed_model.recipients)
        except Exception as be:
            msg = "收件人json格式错误：" + str(be)
            self.record_log("ERROR", msg)
            raise UserError(message=msg)

        failure_recipients = []
        succeed = 0
        failure = 0

        dashboard_name = repository.get_data_scalar("dashboard", {"id": feed_model.dashboard_id}, "name")
        dataset_data = dashboard_feeds_repository.get_dataset_by_dashboard_id(feed_model.dashboard_id)
        for recipient in feed_model.recipients:
            user_name = repository.get_data_scalar("user", {"id": recipient.get("id")}, "name")
            if not user_name:
                failure_recipients.append(recipient)
                failure += 1
                self.record_log("ERROR", "发送简讯给{}，发送失败:{}".format(recipient.get("name"), "该用户不存在DMP中。"))
                continue

            setattr(g, "account", recipient.get("account"))
            no_permission_content = dashboard_feeds_service.check_dataset_or_dashboard_permission(
                recipient.get("id"), feed_model.dashboard_id, dashboard_name, dataset_data
            )

            # 获取用户的功能数据权限
            if no_permission_content and len(no_permission_content) > 0:
                failure_recipients.append(recipient)
                failure += 1
                self._no_permission(recipient, no_permission_content)
            else:
                textcard_data = get_textcard_data(feed_model, recipient)
                try:
                    # 云助手接口调用
                    msg_data = {
                        "msg_url": textcard_data.get("url"),
                        "title": textcard_data.get("title"),
                        "msg": textcard_data.get('description'),
                        "msg_id": feed_model.id
                    }
                    user_id = recipient.get("id")
                    result = self.yzs_api.send_msg(user_id, msg_data)

                    if result.get("errcode") == 0 and result.get('errmsg') == 'ok':
                        succeed += 1
                        self.record_log("INFO", "发送简讯给{}，发送成功。".format(recipient.get("name")))
                    else:
                        failure_recipients.append(recipient)
                        failure += 1
                        self.record_log("ERROR", "用户：{}简讯发送失败，错误原因：{}".format(recipient.get("name"),
                                                                                         result.get('errmsg')))
                except Exception as be:
                    failure_recipients.append(recipient)
                    failure += 1
                    self.record_log("ERROR", "发送简讯给{}，发送失败:{}".format(recipient.get("name"), str(be)))

        succeed_msg = "简讯发送完成，共发送{total}人，成功{succeed}人，失败{failure}人。".format(
            total=len(feed_model.recipients), succeed=succeed, failure=failure
        )
        self.record_log("INFO", succeed_msg)
        return failure_recipients
