#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import datetime
import json
import os
import traceback
from loguru import logger
from concurrent.futures import ThreadPoolExecutor, as_completed
# import multiprocessing
import billiard as multiprocessing
from typing import Callable
import functools

from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dmplib import config
from dmplib.redis import conn as conn_redis, RedisCache
from base import repository
from base.enums import (SubscribeSendDetailLogStatus, ThirdPartyAppCode, ThirdPartyAppServiceType)

from components.weixin_api import WeiXinAPI
from components.yzs_app_api import YzsAppApi, YzsBuiltInAppApi
from components.ding_talk_api import DingTalkAPI

from feed.services import dashboard_feeds_service
from feed.services.app_send_service import AppSendService
from feed.services import feed_external_service
from feed.services import msg_get_data_service
from feed.services.feeds_analysis_service import feed_exec_analysis
from components.erpapi_api import MysoftERPAPI


class FactorySendApp:
    APP_SEND_API_MAP = {
        # 企业微信
        ThirdPartyAppCode.QYWX.value: WeiXinAPI,
        # 云助手
        ThirdPartyAppCode.YZS.value: YzsAppApi,
        # 云助手-新集成
        ThirdPartyAppCode.YZS_NEW.value: YzsAppApi,
        # 云助手-基础应用
        ThirdPartyAppCode.YZS_BUILTIN.value: YzsBuiltInAppApi,
        # 超级工作台
        # 超级工作台应用模式，使用的是数见用户。现在已不推荐这种方式
        ThirdPartyAppCode.SuperWork.value: {
            ThirdPartyAppServiceType.APP.value: YzsAppApi,
        },
        # 产业建管
        ThirdPartyAppCode.MYCYJG.value: {
        },
        # 钉钉
        ThirdPartyAppCode.DD.value: DingTalkAPI,
        # 接口管家集成方案
        ThirdPartyAppCode.JKGJ.value: MysoftERPAPI
    }

    @staticmethod
    def get_instance(feed_data):
        app_code = app_service_type = None
        try:
            if not feed_data:
                raise UserError(message="简讯信息不存在")
            app_code = feed_data.get("app_code")
            app_service_type = feed_data.get("app_service_type")
            party_name = feed_data.get("party_name")
            app_name = feed_data.get("app_name")

            # 应用发送模式判断秘钥信息。云助手-基础应用特别处理
            yzs_app = [ThirdPartyAppCode.YZS_BUILTIN.value]
            if app_service_type == ThirdPartyAppServiceType.APP.value and app_code not in yzs_app and \
                    (not feed_data.get("corp_id") or not feed_data.get("app_secret") or not feed_data.get("app_id")):
                raise UserError(message="简讯关联的应用信息不能为空")

            # 应用全名：渠道名称+应用名称
            app_full_name = f"{party_name}-{app_name}"
            if app_code in FactorySendApp.APP_SEND_API_MAP and FactorySendApp.APP_SEND_API_MAP.get(app_code):
                if app_code == ThirdPartyAppCode.QYWX.value:
                    try:
                        feed_data["app_id"] = int(feed_data["app_id"])
                    except Exception:
                        msg = f"{app_full_name}应用ID格式错误，只支持整型。"
                        raise UserError(message=msg)
                obj = FactorySendApp.APP_SEND_API_MAP.get(app_code)
                # 应用的不同实现，应用模式，服务商模式
                if isinstance(obj, dict):
                    obj = obj.get(app_service_type)
            else:
                raise UserError(message=f'未知的简讯发送渠道')

            return FactorySendApp.get_feed_api_instance(obj, feed_data)
        except Exception as e:
            logger.exception(f"简讯应用发送实例获取失败，err：{str(e)} (app_code:{app_code} app_service_type:{app_service_type})")
            raise e

    @staticmethod
    def get_feed_api_instance(api_obj, feed_data):
        """
        获取简讯渠道对应应用的实例
        :param api_obj:
        :param feed_data:
        :return:
        """
        if not api_obj:
            raise UserError(message=f'简讯应用的实现不存在')
        # 产品编码
        app_code = feed_data.get("app_code")
        # 产品信息
        cloud_app = feed_data.get("cloud_app")
        if app_code == ThirdPartyAppCode.DD.value:
            obj_instance = api_obj(feed_data.get("corp_id"), feed_data.get("app_secret"), feed_data.get("app_id"),
                                   feed_data.get('agent_id'))
        elif app_code in [ThirdPartyAppCode.SuperWork.value, ThirdPartyAppCode.MYCYJG.value]:
            # 应用的类型
            app_service_type = feed_data.get("app_service_type")
            # 超级工作台服务商模式。服务商模式已调整，不会使用数见用户进行简讯发送，会在 saas_app_send_service 非数见用户渠道消息发送中实现
            if app_service_type == ThirdPartyAppServiceType.SERVICE.value:
                # 服务商模式的请求api类
                obj_instance = api_obj(feed_data.get("project_code"), cloud_app)
            else:
                # 超级工作台应用模式
                app_host = cloud_app.get("api_host")
                obj_instance = api_obj(feed_data.get("corp_id"), feed_data.get("app_secret"), feed_data.get("app_id"),
                                       app_host)
        # 云助手基础应用，应用秘钥信息是环境级，需要从 cloud_app 中获取
        elif app_code == ThirdPartyAppCode.YZS_BUILTIN.value:
            app_host = cloud_app.get("api_host")
            channel_app_id = cloud_app.get('channel_app_id')
            channel_app_secret = cloud_app.get('channel_app_secret')
            # 云助手基础应用，企业id就是数见租户code
            obj_instance = api_obj(
                feed_data.get("project_code"),
                channel_app_secret, channel_app_id, app_host
            )
        else:
            obj_instance = api_obj(feed_data.get("corp_id"), feed_data.get("app_secret"), feed_data.get("app_id"))
        return obj_instance


class WechatService(AppSendService):
    """
    使用数见用户的消息发送逻辑处理
    现在支持应用：
    企业微信
    云助手
    超级工作台应用
    超级工作台服务商
    """

    def __init__(self, project_code, feed_id, feed_detail_id, feed_data):
        super().__init__(project_code, feed_id, feed_detail_id, feed_data)
        self.app_name = None
        # 应用的实现类型模式，0：应用模式，1：服务商模式
        self.app_service_type = feed_data.get("app_service_type")
        self.app_api = self.get_app_api()
        self.record_log('INFO', '个人简讯开始发送', self.feed_data)

    def get_app_api(self):
        """
        获取渠道应用的发送实例
        使用数见用户统一执行wechat_service逻辑
        :return:
        """
        try:
            app_api = FactorySendApp.get_instance(self.feed_data)
            # 检查应用实例
            if not app_api:
                raise UserError(message=f'未知的简讯渠道应用类型，获取发送实例失败(app_code:{self.app_code})')
            # 检测发送方法是否存在
            if not hasattr(app_api, 'send_message'):
                raise UserError(message=f'未实现当前应用的send_message(app_code:{self.app_code})')

            party_name = self.feed_data.get("party_name")
            app_name = self.feed_data.get("app_name")
            # 应用全名：渠道名称+应用名称
            self.app_name = f"{party_name}-{app_name}"

            return app_api
        except Exception as e:
            self.record_log("ERROR", "获取简讯应用发送实例异常。err:" + str(e))
            raise e

    def validate_feed_model(self, feed_model):
        if not feed_model.recipients:
            msg = "收件人不能为空"
            self.record_log("ERROR", msg)
            raise UserError(message=msg)
        # 应用模式，则判断秘钥。云助手-基础应用特别处理
        yzs_app = [ThirdPartyAppCode.YZS_BUILTIN.value]
        if self.app_service_type == ThirdPartyAppServiceType.APP.value and self.app_code not in yzs_app:
            if not self.feed_data.get("corp_id"):
                msg = f"{self.app_name}企业ID不能为空"
                self.record_log("ERROR", msg)
                raise UserError(message=msg)
            if not self.feed_data.get("app_secret"):
                msg = f"{self.app_name}的应用秘钥不能为空"
                self.record_log("ERROR", msg)
                raise UserError(message=msg)
            if not self.feed_data.get("app_id"):
                msg = f"{self.app_name}的应用ID不能为空"
                self.record_log("ERROR", msg)
                raise UserError(message=msg)
        try:
            # 字符串才解析
            if isinstance(feed_model.recipients, str):
                feed_model.recipients = json.loads(feed_model.recipients)
        except Exception as be:
            msg = "收件人json格式错误：" + str(be)
            self.record_log('ERROR', msg)
            raise UserError(message=msg)
        if not feed_model.recipients:
            msg = '简讯发送对象为空，不会发送'
            self.record_log('ERROR', msg)
            raise UserError(message=msg)

    def send_app_msg(self, feed_model):
        """
        发送企业微信
        :param feed.models.DashboardFeedsModel feed_model:
        :return:
        """
        self.validate_feed_model(feed_model)

        dashboard_name = ''
        if feed_model.dashboard_id:
            dashboard_name = repository.get_data_scalar("dashboard", {"id": feed_model.dashboard_id}, "name")
        origin_userid = getattr(g, 'userid', None)
        logger.error("发送给%s %s简讯开始发送，发送人数：%s" % (self.app_name,
                                                 msg_get_data_service.clear_title_html(feed_model.subject_email),
                                                 len(feed_model.recipients)))
        # 批量写入消息发送用户渠道状态的明细
        send_detail_list = self.add_subscribe_send_detail_log(feed_model.recipients, feed_model.send_time)
        send_detail_dict = {item.get("account"): item for item in send_detail_list if item}

        # 5 日志写入规则校验
        feed_exec_analysis('create_detail_log')

        # 将数见用户批量转换为当前发送应用所属渠道的用户账号
        third_user_map_list = self.get_send_user_list(feed_model.recipients)

        # 6 用户转换为第三方用户
        feed_exec_analysis('convert_user')

        # 7 开始发送
        feed_exec_analysis('send')
        # 多线程处理方式
        # failure_recipients = []
        # succeed = 0
        # failure = 0
        # succeed, failure = self.multi_thread_send(feed_model, third_user_map_list, dashboard_name, send_detail_dict,
        #                                           failure_recipients)

        # 开发环境使用单线程模式，便于debug调试
        if config.get('App.runtime', 'prod').lower() == 'dev':
            failure_recipients = []
            succeed = failure = 0
            for recipient in feed_model.recipients:
                status, data, recipient = self.iter_send_user(recipient, feed_model, third_user_map_list, dashboard_name, send_detail_dict)
                if status:
                    succeed += 1
                else:
                    failure += 1
                    failure_recipients.append(recipient)
        else:
            process_dict = self.multi_process_send(feed_model, third_user_map_list, dashboard_name, send_detail_dict)
            failure_recipients = process_dict.get('failure_recipients')
            send_detail_dict = process_dict.get('send_detail_dict')
            succeed = process_dict.get('succeed')
            failure = process_dict.get('failure')

        # 7 发送完成
        feed_exec_analysis('send')

        setattr(g, 'userid', origin_userid)

        # 批量更新用户的状态和错误日志
        self.update_subscribe_send_detail_status(send_detail_dict)
        succeed_msg = "简讯发送完成，共发送{total}人，成功{succeed}人，失败{failure}人。".format(
            total=len(feed_model.recipients), succeed=succeed, failure=failure
        )
        # 8 更新简讯详情日志
        feed_exec_analysis('update_detail_log')
        self.record_log("INFO", succeed_msg)
        return failure_recipients

    def get_send_user_list(self, recipients):
        """
        获取需要发送的用户信息
        如果是应用模式，则获取第三方应用的账号
        如果是服务商模式，则需要获取用户id(erp渠道)
        :param recipients:
        :return:
        """
        if self.app_service_type == ThirdPartyAppServiceType.APP.value:
            user_list_map = self.send_user_convert(self.feed_data.get("party_id"), recipients)
        else:
            # 获取服务商模式的用户，模拟 send_user_convert 方法返回格式
            account_list = [item.get("account") for item in recipients]
            erp_user = dashboard_feeds_service.get_erp_user_by_account(account_list)
            user_list_map = {}
            if erp_user:
                user_list_map = {item.get("account"): {
                    "third_user_id": item.get("user_id"),
                    "third_user_account": item.get("account"),
                    "third_user_name": item.get("name")
                } for item in erp_user}
        return user_list_map

    def multi_process_send(self, feed_model, third_user_map_list, dashboard_name, send_detail_dict):
        """
        多线程消息内容获取
        :param feed_model:
        :param third_user_map_list:
        :param dashboard_name:
        :param send_detail_dict:
        :return:
        """
        user_count = len(feed_model.recipients)
        process_num = self.get_process_num()
        # 发送人数少于等于20人，则只需要单进程
        if user_count <= 20:
            process_num = 1
            n = user_count
        else:
            n = user_count//process_num
            n = user_count if n == 0 else n

        recipients_list = feed_model.recipients
        batch_recipients_list = [recipients_list[x:x + n] for x in range(0, user_count, n)]

        allow_key = ['code', 'account', 'userid', 'cookie', 'external_params', 'customize_roles', 'external_user_id',
                     'feed_id', 'feed_name', 'feed_analysis_last_time', 'feed_analysis']
        g_kwargs = {key: getattr(g, key, None) for key in dir(g) if key in allow_key}
        process_dict = {'feed_model': feed_model, 'third_user_map_list': third_user_map_list,
                        'dashboard_name': dashboard_name, 'g': g_kwargs, 'send_detail_dict': send_detail_dict,
                        'feed_detail_id': self.feed_detail_id}

        pool = multiprocessing.Pool(process_num)
        for _, recipients_list in enumerate(batch_recipients_list):
            pool.apply_async(self.process_exec, args=(process_dict, recipients_list),
                             error_callback=self.process_exec_error)
        pool.close()
        pool.join()
        # 从redis中获取数据且进行组合处理
        process_rs_data = self.process_data_convert(feed_model.id, self.feed_detail_id)
        return process_rs_data

    @staticmethod
    def process_exec_error(e):
        """
        进程异常输出处理
        :param e:
        :return:
        """
        logger.error("===消息处理进程发生异常===")
        logger.error(f"-->{str(e)}<--")

    @staticmethod
    def get_process_data_key(feed_id, feed_detail_id, key):
        return 'feed_send:'+feed_id+':'+feed_detail_id+'_'+key

    @staticmethod
    def get_batch_recipients_send_detail(send_detail_dict, recipients_list):
        """
        获取当前批次的用户的发送详情信息
        :param send_detail_dict:
        :param recipients_list:
        :return:
        """
        # 当前批次的用户发送详情信息
        batch_account_list = [r.get('account') for r in recipients_list]
        tmp_send_detail_dict = {}
        for k, v in send_detail_dict.items():
            if k in batch_account_list:
                tmp_send_detail_dict[k] = v
        return tmp_send_detail_dict

    def process_exec(self, process_dict, recipients_list):
        """
        每个进程进行内容获取-消息发送的处理
        进程处理方法
        :param process_dict:
        :param recipients_list:
        :return:
        """
        process_name = multiprocessing.current_process().name
        feed_model = process_dict.get('feed_model')
        third_user_map_list = process_dict.get('third_user_map_list')
        dashboard_name = process_dict.get('dashboard_name')
        send_detail_dict = process_dict.get('send_detail_dict')
        g_kwargs = process_dict.get('g')
        succeed = 0
        failure = 0
        failure_recipients = []
        # 当前批次用户的发送详情记录
        batch_send_detail_dict = self.get_batch_recipients_send_detail(send_detail_dict, recipients_list)

        try:
            all_task = []
            # 进程内开启多线程
            max_workers = self.get_max_workers()
            with ThreadPoolExecutor(max_workers) as pool:
                for recipient in recipients_list:
                    args = (recipient, feed_model, third_user_map_list, dashboard_name, batch_send_detail_dict)
                    kwargs = {"g": g_kwargs}
                    all_task.append(pool.submit(self.handle_g_total(self.iter_send_user), *args, **kwargs))

                for task in as_completed(all_task):
                    status, data, recipient = task.result()
                    if status:
                        succeed += 1
                    else:
                        failure += 1
                        failure_recipients.append(recipient)
            logger.error('消息处理进程：%s 处理完成' % process_name)
        except Exception as e:
            succeed = 0
            failure = len(recipients_list)
            failure_recipients = recipients_list
            for _, item in batch_send_detail_dict.items():
                item['actual_send_time'] = get_now_time()
                item['status'] = SubscribeSendDetailLogStatus.FAIL.value
                item['error_reason'] = '消息发送处理异常，err:'+str(e)
            logger.error('消息处理进程：%s 消息发送处理异常，err:%s trace:%s' % (process_name, str(e), traceback.format_exc()))
        # 每个进程消息发送状态结果写入redis
        self.save_process_data(process_dict, batch_send_detail_dict, succeed, failure, failure_recipients, g_kwargs)

    def save_process_data(self, process_dict, send_detail_dict, succeed, failure, failure_recipients, g_kwargs):
        """
        将每个进程的数据处理结果写入redis
        :param process_dict:
        :param send_detail_dict:
        :param succeed:
        :param failure:
        :param failure_recipients:
        :param g_kwargs:
        :return:
        """
        feed_model = process_dict.get('feed_model')
        feed_detail_id = process_dict.get('feed_detail_id')

        feed_analysis = g_kwargs.get('feed_analysis')
        user_analysis_list = []
        if feed_analysis and 'send' in feed_analysis:
            send_feed_analysis = feed_analysis.get('send')
            user_analysis_list = send_feed_analysis.get('data', [])

        process_data_dict = {
            'send_detail_dict_list': json.dumps(send_detail_dict, ensure_ascii=False),
            'succeed_list': succeed,
            'failure_list': failure,
            'failure_recipients_list': json.dumps(failure_recipients, ensure_ascii=False),
            'g_user_analysis_list': json.dumps(user_analysis_list, ensure_ascii=False)
        }
        redis_conn = RedisCache()
        for k, v in process_data_dict.items():
            tmp_key = self.get_process_data_key(feed_model.id, feed_detail_id, k)
            redis_conn.lpush(tmp_key, v)
            # list 过期时间设置，最后一个进程数据写入后5分钟过期
            redis_conn.expire(tmp_key, 5*60)
            # 临时记录，便于排查
            if k != 'g_user_analysis_list':
                tmp_bak_key = tmp_key + '_bak'
                redis_conn.lpush(tmp_bak_key, v)
                redis_conn.expire(tmp_bak_key, 24 * 60 * 60)

        process_name = multiprocessing.current_process().name
        logger.error(f"消息处理进程：{process_name} 数据写入Redis完成")

    def process_data_convert(self, feed_id, feed_detail_id):
        """
        将redis中的数据读取且进行组合
        :param feed_id:
        :param feed_detail_id:
        :return:
        """
        process_dict = {}
        save_key_list = ['send_detail_dict_list', 'failure_recipients_list', 'succeed_list',
                         'failure_list', 'g_user_analysis_list']
        # 读取list所有数据，然后删除
        for k in save_key_list:
            process_dict[k] = self.get_redis_list_value(feed_id, feed_detail_id, k)
            self.del_redis_list(feed_id, feed_detail_id, k)

        data_dict = {'succeed': sum(process_dict.get('succeed_list')), 'failure': sum(process_dict.get('failure_list')),
                     'failure_recipients': [], 'send_detail_dict': {}}

        # 发送失败的用户列表
        for failure_recipients in process_dict.get('failure_recipients_list'):
            item = json.loads(failure_recipients)
            data_dict['failure_recipients'].extend(item)

        # 所有用户的发送状态日志
        for send_detail_dict in process_dict['send_detail_dict_list']:
            item = json.loads(send_detail_dict)
            data_dict['send_detail_dict'].update(item)

        # 用户发送消息统计信息处理
        g_user_analysis_list = process_dict.get('g_user_analysis_list')
        feed_analysis = getattr(g, 'feed_analysis')
        if g_user_analysis_list and feed_analysis:
            all_user_analysis = {}
            for item_dict in g_user_analysis_list:
                item_dict = json.loads(item_dict)
                all_user_analysis.update(item_dict)
            # 统计信息设置到g全局变量
            feed_analysis['send']['data'] = all_user_analysis
            setattr(g, 'feed_analysis', feed_analysis)

        return data_dict

    def get_redis_list_value(self, feed_id, feed_detail_id, key):
        """
        获取redis list数据
        :param feed_id:
        :param feed_detail_id:
        :param key:
        :return:
        """
        process_data_key = self.get_process_data_key(feed_id, feed_detail_id, key)
        return conn_redis().lrange(process_data_key, 0, -1)

    def del_redis_list(self, feed_id, feed_detail_id, key):
        """
        删除redis list数据
        :param feed_id:
        :param feed_detail_id:
        :param key:
        :return:
        """
        process_data_key = self.get_process_data_key(feed_id, feed_detail_id, key)
        return conn_redis().delete(process_data_key)

    def iter_send_user(self, recipient, feed_model, third_user_map_list, dashboard_name, send_detail_dict):
        """
        线程任务处理方法
        用于处理每个用户的取数-消息发送逻辑
        :param recipient:
        :param feed_model:
        :param third_user_map_list:
        :param dashboard_name:
        :param send_detail_dict:
        :return:
        """
        account = recipient.get("account")
        name = recipient.get("name")
        try:
            # 对每个用户进行简讯数据获取和消息发送
            status = self.send_msg_to_user(feed_model, recipient, third_user_map_list, dashboard_name)
            # 保存发送成功状态
            status_data = {"actual_send_time": get_now_time(), "status": SubscribeSendDetailLogStatus.SUCCESS.value}
            self.record_log("INFO", "发送简讯给{}，发送成功。".format(recipient.get("name")))

        except Exception as e:
            err_msg = f'发送给{name}简讯失败，err:{str(e)}'
            extra = {'trace': traceback.format_exc()}
            self.record_log("ERROR", err_msg, extra)
            # 保存发送失败状态
            status_data = {"actual_send_time": get_now_time(),
                           "status": SubscribeSendDetailLogStatus.FAIL.value, "error_reason": str(e)}
            status = False

        # 7-3 简讯用户消息发送完成
        feed_exec_analysis('send', account, 3)
        self.set_send_detail_status(send_detail_dict, account, status_data)
        return status, status_data, recipient

    @staticmethod
    def copy_g_property(to_g, **g_kwargs):
        # 在不同的协程或者线程中，需要处理g的传递（g只能保证线程程安全）
        for key, val in g_kwargs.items():
            setattr(to_g, key, val)

    def handle_g_total(self, func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            thread_local_g = _AppCtxGlobals()
            g_kwargs = kwargs.pop('g', {})
            self.copy_g_property(thread_local_g, **g_kwargs)  # noqa
            _app_ctx_stack.push(thread_local_g)
            # inject db
            db_ctx = DBContext()
            db_ctx.inject(thread_local_g)
            try:
                return func(*args, **kwargs)
            finally:
                db_ctx.close_all()

        return wrapper

    def multi_thread_send(self, feed_model, third_user_map_list, dashboard_name, send_detail_dict, failure_recipients):
        """
        多线程并发处理用户的消息获取-发送逻辑
        :param feed_model:
        :param third_user_map_list:
        :param dashboard_name:
        :param send_detail_dict:
        :param failure_recipients:
        :return:
        """
        all_task = []
        succeed = 0
        failure = 0
        g_kwargs = {key: getattr(g, key, None) for key in dir(g) if
                    not (key.startswith('__') and key.endswith('__'))}

        max_workers = self.get_max_workers()
        with ThreadPoolExecutor(max_workers) as pool:
            for recipient in feed_model.recipients:
                args = (recipient, feed_model, third_user_map_list, dashboard_name, send_detail_dict)
                kwargs = {"g": g_kwargs}
                all_task.append(pool.submit(self.handle_g_total(self.iter_send_user), *args, **kwargs))

            for task in as_completed(all_task):
                status, data, recipient = task.result()
                if status:
                    succeed += 1
                else:
                    failure += 0
                    failure_recipients.append(recipient)
        return succeed, failure

    @staticmethod
    def get_process_num():
        logger.error("CPU核心数：" + str(os.cpu_count()))
        default_max_process = 1
        try:
            config_max_process = config.get("Subscribe.max_process", None)
            config_max_process = int(config_max_process) if config_max_process else 0
            if config_max_process is None or config_max_process == 0:
                max_process = default_max_process
            else:
                max_process = config_max_process
        except Exception as e:
            max_process = default_max_process
            logger.error("获取进程池数量异常，err:"+str(e))
        logger.error("进程池数量：" + str(max_process))
        return max_process

    @staticmethod
    def get_max_workers():
        default_max_workers = 10
        try:
            config_max_workers = config.get("Subscribe.max_workers", None)
            config_max_workers = int(config_max_workers) if config_max_workers else 0
            if config_max_workers is None or config_max_workers == 0:
                max_workers = default_max_workers
            else:
                max_workers = config_max_workers
        except Exception as e:
            max_workers = default_max_workers
            logger.error("获取线程池数量异常，err:" + str(e))
        logger.error("线程池数量：" + str(max_workers))
        return max_workers

    def send_msg_to_user(self, feed_model, recipient, third_user_map_list, dashboard_name):
        """
        每个用户进行简讯数据获取和消息发送
        :param feed_model: 简讯数据对象
        :param recipient: 简讯用户字典
        :param third_user_map_list: 用户第三方关联账号字典
        :param dashboard_name: 报告名称
        :return:
        """
        account = recipient.get("account")
        feed_exec_analysis('send', account, 0)

        # 检查用户是否存在
        self.send_msg_check_user(recipient)
        setattr(g, "userid", recipient.get("id"))
        setattr(g, "account", account)
        # 检测用户数据权限
        self.send_msg_check_user_permission(feed_model, recipient, dashboard_name)

        # 7-1 用户和权限检查
        feed_exec_analysis('send', account, 1)

        # 获取当前用户的简讯内容
        textcard_data = get_textcard_data(feed_model, recipient)
        # 7-2 简讯内容获取
        feed_exec_analysis('send', account, 2)

        # 记录每个人的消息内容；截取正文字节长度为512和简讯消息关联详情页
        self.add_mobile_subscribe_user_log(feed_model, recipient, textcard_data, dashboard_name, self.feed_data)
        # 获取发送的账号
        send_account = self.get_account(third_user_map_list, recipient)
        result = self.app_api.send_message(send_account, textcard_data)

        # 输出每个人的发送日志
        logger.error("发送简讯给%s(账号:%s)，发送内容：%s | 发送结果：%s" % (recipient.get("name"), account,
                                                           json.dumps(textcard_data, ensure_ascii=False),
                                                           json.dumps(result)))

        if result.get("errcode") == 0 and result.get('errmsg') == 'ok' and not result.get('invaliduser'):
            return True
        else:
            err = "%s未找到用户%s或用户无应用权限" % (self.app_name, recipient.get("name"))
            ue = UserError(message=err) if result.get('invaliduser') else UserError(message=json.dumps(result))
            raise ue

    def get_account(self, third_user_map_list, recipient):
        """
        获取发送的用户对象
        :param third_user_map_list:
        :param recipient:
        :return:
        """
        account = recipient.get("account")
        if self.app_service_type == ThirdPartyAppServiceType.APP.value:
            account = self.get_send_user_account(third_user_map_list, account)
        else:
            # 服务商模式，用户匹配获取
            account = self.get_erp_user_account(third_user_map_list, account)
        # 用户不存在，则抛出异常
        if not account:
            raise UserError(message="用户%s无法匹配到可发送对象" % recipient.get("name"))
        return account

    @staticmethod
    def send_msg_check_user(recipient):
        """
        检查用户是否存在
        :param recipient:
        :return:
        """
        user = repository.get_one("user", {"id": recipient.get("id")}, ["id", "name", "account", "is_disabled"])
        if not user:
            raise UserError(message="该用户不存在DMP中")
        if user.get("is_disabled") == 1:
            raise UserError(message="该用户已被停用，不能发送")
        return user

    def send_msg_check_user_permission(self, feed_model, recipient, dashboard_name):
        """
        检测用户数据权限
        报表可见权限
        :param feed_model:
        :param recipient:
        :param dashboard_name:
        :return:
        """
        # 存在关联报告，进行权限校验
        if feed_model.dashboard_id:
            no_permission_content = dashboard_feeds_service.check_dataset_or_dashboard_permission(
                recipient.get("id"), feed_model.dashboard_id, dashboard_name, []
            )
            # 获取用户的功能数据权限
            if no_permission_content and len(no_permission_content) > 0:
                err_msg = self._no_permission(recipient, no_permission_content)
                # 抛出权限校验异常
                if err_msg:
                    raise UserError(message=err_msg)

    def _no_permission(self, recipient, no_permission_content):
        msg = ""
        no_permission_dataset = []
        for content in no_permission_content:
            if content.get("type") == "dashboard":
                msg = "没有" + content.get("name") + "报告的查看权限。"
            else:
                no_permission_dataset.append(content.get("name"))
        if no_permission_dataset and len(no_permission_dataset) > 0:
            msg += "没有" + ",".join([dataset for dataset in no_permission_dataset]) + "数据集的查看权限。"
        self.record_log("ERROR", "发送简讯给{}，发送失败:{}".format(recipient.get("name"), msg))
        return msg

    @staticmethod
    def send_user_convert(party_id, recipients):
        """
        将数见用户批量转换为当前发送应用所属渠道的用户账号
        :param party_id:
        :param recipients:
        :return:
        """
        third_user_map_list = {}
        if not recipients:
            return third_user_map_list
        try:
            user_list = [{"user_id": item.get("id"), "account": item.get("account")} for item in recipients]
            third_user_list = feed_external_service.get_third_party_user_by_app_code(party_id, user_list)
            return {item.get("account"): item for item in third_user_list}
        except Exception as e:
            logger.error(f"简讯发送用户批量转换为第三方应用用户异常，err：{str(e)}")
            return third_user_map_list

    @staticmethod
    def get_send_user_account(third_user_map_list, account):
        """
        获取简讯发送用户的账号
        :param third_user_map_list:
        :param account:
        :return:
        """
        if not third_user_map_list:
            return account
        third_user = third_user_map_list.get(account) or {}
        if third_user:
            third_user_account = third_user.get("third_user_account")
            msg = "convert user 数见account：%s 第三方account：%s" % (account, third_user_account)
            bind_third_account = third_user.get("bind_third_account")
            if bind_third_account:
                msg += f" 已绑定的第三方账号：{bind_third_account}"
            logger.error(msg)
            if third_user_account:
                account = third_user_account
        return account

    @staticmethod
    def get_erp_user_account(third_user_map_list, account):
        """
        获取服务商模式下，erp的用户id
        :param third_user_map_list:
        :param account:
        :return:
        """
        erp_user_id = None
        if not third_user_map_list:
            return erp_user_id

        erp_user = third_user_map_list.get(account) or {}
        if erp_user:
            third_user_account = erp_user.get("third_user_account")
            third_user_name = erp_user.get("third_user_name")
            erp_user_id = erp_user.get("third_user_id")
            msg = "convert user 数见account：%s ERP account：%s ERP name：%s ERP id：%s" \
                  % (account, third_user_account, third_user_name, erp_user_id)
            logger.error(msg)
        return erp_user_id


def get_textcard_data(feed_model, recipient):
    """
    获取卡片消息内容
    :param feed.models.DashboardFeedsModel feed_model :
    :param recipient: {"account":"cc", "name":"cc","id":"39e1159c-ac5f-4d12-02a2-569ebc353dea","email":"<EMAIL>"}
    :return:
    """
    return msg_get_data_service.get_message_data(feed_model, recipient)


def get_now_time():
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
