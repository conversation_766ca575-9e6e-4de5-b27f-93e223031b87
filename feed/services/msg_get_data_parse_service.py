#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import datetime
import json
import re
import time
import traceback
from loguru import logger
import html
from abc import ABCMeta, abstractmethod
from typing import Union

from dmplib.utils.strings import is_number
from dmplib.utils.errors import UserError

from base.enums import DatasetFieldDataType, FeedType, SubscribeSendEditorMode, ThirdPartyAppCode
from dataset import external_query_service
from feed.models import MsgSubscribeConfigModel, MobileSubscribeChapterModel
from feed.services.msg_get_data_filter_service import (
    get_filter_query_struct_obj,
    get_chapter_filter_query_struct_obj
)
from feed.services.msg_get_data_query_service import (
    parse_combine_field_classify,
    parse_query_combine_number,
    parse_query_combine_not_number,
    query_chapter_detail_message
)
from feed.services.msg_get_data_util_service import format_feed_value


class MsgDataBaseParse(metaclass=ABCMeta):
    """
    简讯内容解析基类
    """

    TYPE_PARAGRAPH = 'paragraph'

    TYPE_CHAPTER = 'chapter'

    CHILDREN = 'children'

    NODE_FIELD = 'field'

    # 支持富文本的应用
    SUPPORT_RICH_APP = [
        ThirdPartyAppCode.YZS.value,
        ThirdPartyAppCode.YZS_NEW.value,
        ThirdPartyAppCode.YZS_BUILTIN.value,
        ThirdPartyAppCode.SuperWork.value
    ]

    def __init__(self, feed_model, recipient):
        """
        :param feed.models.DashboardFeedsModel feed_model :
        :param recipient: {"account":"cc", "name":"cc","id":"39e1159c-ac5f-4d12-02a2-569ebc353dea",
        "email":"<EMAIL>"}
        """
        self.feed_model = feed_model
        self.recipient = recipient
        self.msg_subscribe_config_model = feed_model.msg_subscribe_config  # type: MsgSubscribeConfigModel
        self.editor_mode = self.msg_subscribe_config_model.editor_mode
        self.get_msg_editor_mode()
        # 简讯的发送应用app_code
        self.app_code = feed_model.app_code

    def get_msg_editor_mode(self):
        editor_map = {
            SubscribeSendEditorMode.HTML.value: 'HTML模式',
            SubscribeSendEditorMode.JSON.value: 'JSON模式'
        }
        editor_mode = editor_map.get(self.editor_mode) if editor_map.get(self.editor_mode) else '不支持的解析模式'
        logger.error(f"简讯内容解析模式：{editor_mode}")
        return editor_mode

    @staticmethod
    def get_date():
        year = time.localtime().tm_year
        month = time.localtime().tm_mon
        day = time.localtime().tm_mday
        return str(year) + '年' + str(month) + '月' + str(day) + '日'

    @abstractmethod
    def parse_message(self):
        # 必须继承实现
        raise NotImplementedError("Please Implement this method")

    def query_dataset_by_field(self, replace_dataset_metas, is_dynamic_title=False):
        """
        查询简讯聚合字段的数据
        :param replace_dataset_metas:
        json模式 {"dataset_id": {"dataset_id": dataset_id, "dataset_field_id": col_id},
        "dataset_id": {"dataset_id": dataset_id, "dataset_field_id": col_id}}
        HTML模式 {"dataset_id": {"replace_meta": dataset_meta, "dataset_id": dataset_id,
        "dataset_field_id": dataset_field_id}}
        :param is_dynamic_title:
        :return:
        """
        feed_model = self.feed_model
        recipient = self.recipient
        # 构造数据集的过滤条件query struct where对象
        dataset_filter_map = get_filter_query_struct_obj(replace_dataset_metas, feed_model.filters)
        dataset_field_display_format_map = {item.field_id: item.get_dict() for item in
                                            (feed_model.display_format or [])}

        results = {}
        for dataset_id, replace_dataset_meta in replace_dataset_metas.items():
            try:
                dataset_field_data = external_query_service.get_dataset_fields(dataset_id)
            except Exception as e:
                errs = '数据集字段获取异常，errs:' + str(e)
                raise UserError(message=errs)

            query_dataset_field_map = {field.get("id"): field for field in dataset_field_data if field}
            number_field_list, not_number_field_list = parse_combine_field_classify(dataset_id, replace_dataset_meta,
                                                                                    query_dataset_field_map, results)
            # 数值的查询
            if number_field_list:
                number_rs = parse_query_combine_number(recipient, dataset_id, number_field_list,
                                                       dataset_filter_map, dataset_field_display_format_map)
                if number_rs:
                    results[dataset_id].update(number_rs)

            # 非数值的查询
            if not_number_field_list:
                not_number_rs = parse_query_combine_not_number(recipient, not_number_field_list,
                                                               is_dynamic_title, dataset_filter_map,
                                                               dataset_field_display_format_map, self.feed_model.type)
                if not_number_rs:
                    results[dataset_id].update(not_number_rs)

        return results

    @staticmethod
    def chapter_format_field(dataset_field, dataset_field_display_format_map, value):
        """
        格式化数值字段
        :param dataset_field:
        :param dataset_field_display_format_map:
        :param value:
        :return:
        """
        if not dataset_field_display_format_map or not dataset_field_display_format_map.get(dataset_field.get("id")):
            return value
        if dataset_field.get("data_type") == DatasetFieldDataType.Number.value and is_number(value):
            display_format = dataset_field_display_format_map.get(dataset_field.get("id")) or {}
            value = format_feed_value(value, display_format)
        return value

    @staticmethod
    def get_query_dataset_field_list(chapter_model: MobileSubscribeChapterModel, chapter_field_map):
        """
        获取明细列表的可查询字段列表
        :param chapter_model:
        :param chapter_field_map:
        :return:
        """
        query_dataset_field_list = []
        dataset_id = chapter_model.dataset_id
        if dataset_id:
            replace_field_id_list = chapter_field_map.keys()
            # 明细段落数据集的所有字段
            dataset_field_data = external_query_service.get_dataset_fields(dataset_id)
            if not dataset_field_data:
                raise UserError(message="数据集查找字段列表为空")
            query_dataset_field_list = [field for field in dataset_field_data if
                                        field.get("id") in replace_field_id_list]
        return query_dataset_field_list

    def check_app_support_rich_text(self):
        """
        检查简讯发送应用是否支持富文本渲染
        :return:
        """
        return self.app_code in self.SUPPORT_RICH_APP or self.feed_model.is_preview


class MsgDataJsonParse(MsgDataBaseParse):
    """
    简讯的json模式内容解析
    """

    def parse_message(self):
        feed_model = self.feed_model
        # 简讯正文解析
        message = self.parse_message_data(feed_model.message)
        # “简讯内容是否增加发送日期” 发送日期最后加上，而不是现在加
        message = self.show_date_message(message)
        # 简讯动态标题解析
        subject_email = self.parse_message_data(feed_model.subject_email, True)

        msg_subscribe_config = self.msg_subscribe_config_model.get_dict() if self.msg_subscribe_config_model else dict()
        textcard_data = {
            "title": subject_email,
            "description": message,
            "url": feed_model.release_url,
            "btntxt": "查看详情",
            "msg_subscribe_config": msg_subscribe_config
        }
        return textcard_data

    def parse_message_data(self, message, is_dynamic_title=False):
        """
        简讯的内容解析和替换，支持富文本
        :param message:
        :param is_dynamic_title: 是否为动态标题
        :return:
        """
        # 解析简讯中数据集字段结果
        message_list = self.get_message_list(message)

        # 将简讯的字段、明细列表查找出来
        field_map, chapter_id_list = self.find_dataset_meta(message_list)

        # 解析简讯的聚合字段
        field_rs = self.get_field_data(field_map, is_dynamic_title)

        # 简讯的明细段落结果独立获取
        chapter_rs = self.get_chapter_data(chapter_id_list)

        # 简讯的聚合字段，明细字段解析为文本字符串，同时增加富文本渲染
        return self.parse_replace_combine_data(message_list, field_rs, chapter_rs,
                                               app_support_rich=self.check_app_support_rich_text())

    def get_chapter_data(self, chapter_id_list):
        """
        解析某用户的简讯明细列表的数据集字段内容
        :param chapter_id_list:
        :return:
        """
        feed_model = self.feed_model
        recipient = self.recipient
        chapter_data = {}
        if not chapter_id_list:
            return chapter_data
        # 构造数据集的过滤条件query struct where对象
        dataset_filter_map = get_chapter_filter_query_struct_obj(feed_model.chapters, feed_model.filters)
        # 当前简讯的数值格式
        dataset_field_display_format_map = {item.field_id: item.get_dict()
                                            for item in (feed_model.display_format or [])}
        chapter_model_map = {chapter_model.id: chapter_model for chapter_model in (feed_model.chapters or [])}
        for chapter_id in chapter_id_list:
            # 获取每一个明细段落的解析内容
            chapter_model = chapter_model_map.get(chapter_id) if chapter_model_map.get(chapter_id) else None
            chapter_message = self.get_chapter_parse_message(recipient, chapter_model, dataset_filter_map,
                                                             dataset_field_display_format_map)
            chapter_data[chapter_id] = chapter_message

        return chapter_data

    def get_chapter_parse_message(self, recipient, chapter_model, dataset_filter_map, dataset_field_display_format_map):
        """
        获取明细段落的解析后内容
        :param recipient:
        :param chapter_model:
        :param dataset_filter_map:
        :param dataset_field_display_format_map:
        :return:
        """
        if not chapter_model:
            raise UserError(message="明细段落model对象为空")

        logger.error(f"简讯明细段落数据开始获取，段落ID：{chapter_model.id} 数据集ID：{chapter_model.dataset_id}")
        message_list = self.get_message_list(chapter_model.chapter_message)
        # 获取明细段落中的所有字段id
        chapter_field_map = self.find_chapter_message_field(message_list)

        # 不存在字段，无需请求数据集，直接返回明细段落结果
        if not chapter_field_map:
            logger.error("明细段落是纯文本内容，获取完成")
            return self.parse_replace_combine_data(message_list, app_support_rich=self.check_app_support_rich_text())

        query_dataset_field_list = self.get_query_dataset_field_list(chapter_model, chapter_field_map)
        # 获取明细段落的数据列表
        data_list = self.get_chapter_parse_data(recipient, chapter_model.dataset_id,
                                                query_dataset_field_list, dataset_filter_map)

        # 明细段落的内容解析
        chapter_message = self.chapter_message_replace_format(message_list, data_list, chapter_field_map,
                                                              query_dataset_field_list,
                                                              dataset_field_display_format_map)
        logger.error(f"简讯明细段落数据获取完成")
        return chapter_message

    @staticmethod
    def get_chapter_parse_data(recipient, dataset_id, query_dataset_field_list, dataset_filter_map):
        """
        获取每个明细段落的结果内容
        :param recipient:
        :param dataset_id:
        :param query_dataset_field_list:
        :param dataset_filter_map:
        :return:
        """
        data_list = []
        try:
            kwargs = {
                "recipient": recipient,
                "dataset_id": dataset_id,
                "query_dataset_field_list": query_dataset_field_list,
                "dataset_filter_map": dataset_filter_map,
            }
            data_list = query_chapter_detail_message(**kwargs)
            if not data_list:
                raise UserError(message="数据集查询结果为空")
        except Exception as e:
            # 发生异常，明细段落数据不合并到主段落
            logger.error('简讯明细段落数据获取异常，不会合并到主段落 err:%s trace:%s' % (str(e), traceback.format_exc()))
        return data_list

    def chapter_message_replace_format(self, message_list, data_list, chapter_field_map, query_dataset_field_list,
                                       dataset_field_display_format_map):
        """
        明细段落的数据每一行进行替换和数值格式化
        :param message_list:
        :param data_list:
        :param chapter_field_map:
        :param query_dataset_field_list:
        :param dataset_field_display_format_map:
        :return:
        """
        if not message_list or not data_list:
            return ""

        dataset_field_map = {item.get("id"): item for item in query_dataset_field_list}
        all_chapter = []
        for item in data_list:
            # 处理明细的每一行
            row_data = {}
            for field_id, field_info in chapter_field_map.items():
                # 对数值类型字段进行格式化
                dataset_field = dataset_field_map.get(field_id)
                dataset_id = field_info.get("dataset_id")
                val = ''
                if not dataset_field:
                    logger.error(f"数据集字段不存在，结果为空。field_id：{field_id}")
                else:
                    val = item.get(field_id)
                    val = self.chapter_format_field(dataset_field, dataset_field_display_format_map, val)

                if row_data.get(dataset_id):
                    row_data[dataset_id][field_id] = val
                else:
                    row_data[dataset_id] = {field_id: val}

            # 明细段落每一行json解析替换为文本
            row_message = self.parse_replace_combine_data(message_list, row_data,
                                                          app_support_rich=self.check_app_support_rich_text())
            all_chapter.append(row_message)

        # 明细段落每一行，采用<br>连接
        return "<br>".join(all_chapter)

    @classmethod
    def find_chapter_message_field(cls, message_list):
        """
        获取某个明细段落的字段列表信息
        :param message_list:
        :return:
        """
        chapter_field_map = {}
        for item in message_list:
            if item.get("type") == cls.TYPE_PARAGRAPH and item.get(cls.CHILDREN):
                # 每一行的处理
                for children in item.get(cls.CHILDREN):
                    if children.get("type") == cls.NODE_FIELD:
                        col_id = children.get("col_id")
                        dataset_id = children.get("dataset_id")
                        chapter_field_map[col_id] = {"dataset_id": dataset_id, "name": children.get("name")}
        return chapter_field_map

    @classmethod
    def get_message_list(cls, message):
        is_json = cls.check_json_message(message)
        if not is_json:
            errs = "简讯的内容数据格式异常"
            logger.error(errs)
            raise UserError(message=errs)
        message_list = json.loads(message)
        return message_list

    @staticmethod
    def check_json_message(message):
        try:
            json.loads(message)
            return True
        except ValueError:
            return False

    @classmethod
    def get_subscribe_pre_text(cls, message):
        """
        获取简讯标题的纯文本内容
        :param message:
        :return:
        """
        is_json = cls.check_json_message(message)
        if is_json:
            message_list = json.loads(message)
            message = cls.parse_replace_combine_data(message_list, None, None, True)
        return message

    @classmethod
    def get_subscribe_dataset_id(cls, message):
        """
        获取简讯内容中聚合字段的数据集id列表
        :param message:
        :return:
        """
        is_json = cls.check_json_message(message)
        dataset_ids = []
        if is_json:
            message_list = json.loads(message)
            field_map, _ = cls.find_dataset_meta(message_list)
            if field_map:
                dataset_ids = list(field_map.keys())
        return dataset_ids

    @classmethod
    def parse_replace_combine_data(cls, message_list, field_rs=None, chapter_rs=None, is_pre_text=False,
                                   app_support_rich=False):
        """
        将字段，明细列表，纯文本，富文本内容解析为消息内容
        :param message_list: 原始json 对象
        :param field_rs: 聚合字段数据结果
        :param chapter_rs: 明细列表数据结果
        :param is_pre_text: 是否获取为纯文本，False：需要解析内容，True：获取纯文本（不需解析字段）
        :param app_support_rich: 简讯发送应用是否支持富文本，False：不支持富文本，True：支持
        :return:
        """
        if field_rs is None:
            field_rs = {}
        if chapter_rs is None:
            chapter_rs = {}
        if not isinstance(message_list, list):
            logger.error("简讯json格式错误，返回结果为空")
            return ''
        message_rows = []
        for item in message_list:
            # 每一行的处理
            row = ''
            if item.get("type") == cls.TYPE_PARAGRAPH and item.get(cls.CHILDREN):
                # 聚合字段解析
                for children in item.get(cls.CHILDREN):
                    children_type = children.get("type")
                    if children_type == cls.NODE_FIELD:
                        # 获取聚合字段解析
                        text = cls.parse_children_field_node(children, field_rs, is_pre_text)
                    else:
                        text = children.get("text", '')
                    # 字段或纯文本处理富文本
                    if text and not is_pre_text and app_support_rich:
                        text = cls.rich_text(text, children)
                    text = str(text)
                    text = cls._replace_ufeff(text)
                    row += text

                message_rows.append(row)
            elif item.get("type") == cls.TYPE_CHAPTER:
                # 明细列表解析，存在结果才记录
                text = cls.parse_children_chapter_node(item, chapter_rs)
                if text:
                    text = str(text)
                    text = cls._replace_ufeff(text)
                    row += text
                    message_rows.append(row)

        return cls.parse_combine_message(message_rows)

    @staticmethod
    def _replace_ufeff(text):
        """
        删除前端编辑器产生的特殊字符（占位符） \ufeff
        :param text:
        :return:
        """
        if not text or not isinstance(text, str):
            return text
        return text.replace('\ufeff', '')

    @staticmethod
    def parse_children_field_node(children, field_rs_list, is_pre_text):
        """
        解析聚合字段的数据
        :param children:
        :param field_rs_list:
        :param is_pre_text:
        :return:
        """
        # 获取字段名称模式
        text = ''
        if is_pre_text:
            text = children.get("name")
        else:
            dataset_id = children.get("dataset_id")
            col_id = children.get("col_id")
            # 聚合字段取数
            field_rs = field_rs_list.get(dataset_id)
            if field_rs:
                text = field_rs.get(col_id)
        return text

    @staticmethod
    def parse_children_chapter_node(children, chapter_rs):
        """
        解析明细列表的数据
        :param children:
        :param chapter_rs:
        :return:
        """
        # 解析明细列表的数据
        chapter_id = children.get("chapter_id")
        text = chapter_rs.get(chapter_id, '')
        return text

    @staticmethod
    def rich_text(text, children):
        """
        富文本的处理，加粗颜色
        :param text:
        :param children:
        :return:
        """
        text = str(text)
        # 加粗
        if children.get("bold"):
            text = f"<b>{text}</b>"
        # 颜色
        if children.get("fontColor"):
            text = '<font color="' + children.get("fontColor") + '">'+text+'</font>'
        return text

    @staticmethod
    def parse_combine_message(message_rows):
        """
        将文本中的每一行用br连接
        :param message_rows:
        :return:
        """
        message = ''
        if not message_rows:
            return message
        return '<br>'.join(message_rows)

    def show_date_message(self, message):
        """
        简讯内容是否增加发送日期
        :param message:
        :return:
        """
        if self.msg_subscribe_config_model and self.msg_subscribe_config_model.is_show_date == 1:
            message = self.get_date() + "<br>" + message
        return message

    @classmethod
    def find_dataset_meta(cls, message_list):
        """
        查询message 中的数据集元数据
        :param message_list:
        :return:
        """
        field_map = {}
        chapter_id_list = []
        for item in message_list:
            if item.get("type") == cls.TYPE_PARAGRAPH and item.get(cls.CHILDREN):
                # 每一行的处理
                for children in item.get(cls.CHILDREN):
                    if children.get("type") == cls.NODE_FIELD:
                        dataset_id = children.get("dataset_id")
                        col_id = children.get("col_id")

                        if dataset_id not in field_map:
                            field_map[dataset_id] = []
                        field_map[dataset_id].append({"dataset_id": dataset_id, "dataset_field_id": col_id})
            elif item.get("type") == cls.TYPE_CHAPTER:
                # 明细段落id列表
                chapter_id = item.get("chapter_id")
                chapter_id_list.append(chapter_id)

        return field_map, chapter_id_list

    def get_field_data(self, field_map, is_dynamic_title=False):
        """
        解析聚合字段
        :param field_map:
        :param is_dynamic_title:
        :return:
        """
        results = {}
        if not field_map:
            return results
        return self.query_dataset_by_field(field_map, is_dynamic_title)


class MsgDataHtmlParse(MsgDataBaseParse):
    """
    简讯的html模式内容解析（个人简讯老版本，邮件简讯）
    """

    def __init__(self, feed_model, recipient):
        super().__init__(feed_model, recipient)

    def parse_message(self):
        """
        简讯HTML混编模式的内容解析
        :return:
        """
        # 获取用户的数据集字段汇总数据
        # 1、字符串、日期、枚举多值用逗号分割，超过30个字符显示省略号
        # 2、数值统一求和汇总

        feed_model = self.feed_model
        # 简讯内容是否增加发送日期
        message = self.show_date_message()
        # 解析简讯中数据集字段结果
        message = self.parse_recipient_message(message)
        # 简讯动态标题解析
        subject_email = self.dynamic_title_message()
        # 简讯标题去除html字符
        if feed_model.type == FeedType.Mobile.value:
            subject_email = self.clear_title_html(subject_email)

        # 简讯类型判断，邮件不替换html
        if feed_model.type == FeedType.Mobile.value:
            message = self.clean_message(message)

        # 简讯的明细段落解析处理
        message = self.get_chapter_message(message)

        # 拼接简讯动态标题，增加组织和发送日期【临时方案，以后需去除】
        message, new_title = self.format_title_message(message, feed_model.send_time, subject_email)

        # message, new_title 需要HTML实体decode
        message = self.html_unescape(message)
        new_title = self.html_unescape(new_title)

        msg_subscribe_config = self.msg_subscribe_config_model.get_dict() if self.msg_subscribe_config_model else dict()
        textcard_data = {
            "title": new_title,
            "description": message,
            "url": feed_model.release_url,
            "btntxt": "查看详情",
            "msg_subscribe_config": msg_subscribe_config
        }
        return textcard_data

    def parse_recipient_message(self, message, is_dynamic_title=False):
        """
        解析某用户的简讯数据集字段内容
        :param message:
        :param is_dynamic_title:
        :return:
        """
        replace_dataset_metas = self.find_dataset_meta(message)
        if not replace_dataset_metas:
            return message

        results = self.query_dataset_by_field(replace_dataset_metas, is_dynamic_title)
        # 将message中字段占位符替换
        if results:
            message = self.parse_replace_combine_data(message, replace_dataset_metas, results)
        return message

    @staticmethod
    def parse_replace_combine_data(message, replace_dataset_metas, replace_results):
        """
        将message中的字段占位符替换为字段结果
        :param message:
        :param replace_dataset_metas:
        :param replace_results:
        :return:
        """
        for dataset_id, replace_dataset_meta in replace_dataset_metas.items():
            dataset_result = replace_results.get(dataset_id)
            for replace in replace_dataset_meta:
                replace_val = dataset_result.get(replace.get("dataset_field_id"))
                message = message.replace(replace.get("replace_meta"), str(replace_val))
        return message

    def show_date_message(self):
        """
        简讯内容是否增加发送日期
        :return:
        """
        feed_model = self.feed_model
        message = feed_model.message
        # 如果是邮件则不加上日期
        if feed_model.type == FeedType.Email.value:
            return message
        if self.msg_subscribe_config_model and self.msg_subscribe_config_model.is_show_date == 1:
            message = "<div class=\"gray\">" + self.get_date() + "</div>" + feed_model.message
        return message

    @classmethod
    def clean_message(cls, message):
        """
        去除多余标签和样式以便满足企业微信文本卡片api的字数限制下加长有效文本的字数
        :param message:
        :return:
        """
        message = re.sub('<samp[^-].*?>|</samp>|style=".*?"|class=".*?"', '', message)
        message = cls.clean_tags(message)
        return message

    @staticmethod
    def clean_tags(message):
        """
        清除文本中html标签
        :param message:
        :return:
        """
        message = re.sub('<p.*?>', '<div>', message)
        message = re.sub('</p>', '</div>', message)
        message = re.sub('<div.*?>', '', message)
        message = re.sub('</div>', '<br>', message)
        if message.endswith('<br>'):
            message = re.sub('<br>$', '', message)
        # 空格实体替换为空格字符串
        message = re.sub('&nbsp;', ' ', message)
        return message

    def dynamic_title_message(self):
        """
        简讯支持动态标题
        :return:
        """
        # 简讯标题
        subject_email = self.feed_model.subject_email
        dataset_meta_list = re.findall("<samp[^>]*>[^<>]+</samp>", subject_email, flags=re.I)
        if not dataset_meta_list:
            return subject_email
        return self.parse_recipient_message(subject_email, True)

    @classmethod
    def clear_title_html(cls, subject_email):
        # HTML编辑器模式
        subject_email = cls.clean_message(subject_email)
        subject_email = re.sub('<br>', '', subject_email)
        # json编辑器模式下获取标题的纯文本
        subject_email = MsgDataJsonParse.get_subscribe_pre_text(subject_email)
        return subject_email

    @staticmethod
    def html_unescape(msg):
        """
        html 实体decode
        :param msg:
        :return:
        """
        if not msg:
            return msg
        msg = html.unescape(msg)
        return msg

    @staticmethod
    def format_title_message(message, send_time, subject_email):
        """
        动态简讯标题处理【临时方案】
        1、解析正文的关键字符
        2、拼接发送时间
        :param message:
        :param send_time:
        :param subject_email:
        :return:
        """
        pattern = '{{(.*?)}}'
        search_obj = re.search(pattern, message, re.I)
        new_title = subject_email
        if search_obj:
            # 获取动态标题内容
            title_str = search_obj.group()
            title_str = title_str.replace("{{", '').replace("}}", '').strip()
            # 发送日期
            send_date = datetime.datetime.strptime(send_time, '%Y-%m-%d %H:%M:%S').strftime("%Y-%m-%d")
            new_title = subject_email + title_str + send_date
            # 替换可能存在的换行符
            pattern += r'(<br(\/)?>)*'
            message = re.sub(pattern, "", message, 1)
        return message, new_title

    @staticmethod
    def find_dataset_meta(message):
        """
        查询message 中的数据集元数据
        :param message:
        :return:
        """
        replace_dataset_metas = {}
        dataset_meta_list = re.findall("<samp[^>]*>[^<>]+</samp>", message, flags=re.I)
        for dataset_meta in dataset_meta_list:
            dataset_id = ""
            dataset_field_id = ""
            samp_list = dataset_meta.split(" ")
            for samp in samp_list:
                if samp.find("data-datasetid") > -1:
                    dataset_match = re.findall("data-datasetid=\"(.*)\"", samp.strip(), flags=re.I)
                    dataset_id = dataset_match[0] if dataset_match else ""
                    if dataset_id not in replace_dataset_metas:
                        replace_dataset_metas[dataset_id] = []
                elif samp.find("data-colid") > -1:
                    dataset_field_match = re.findall("data-colid=\"(.*)\"", samp.strip(), flags=re.I)
                    dataset_field_id = dataset_field_match[0] if dataset_field_match else ""

            if dataset_id and dataset_field_id:
                replace_dataset_metas.get(dataset_id).append(
                    {"replace_meta": dataset_meta, "dataset_id": dataset_id, "dataset_field_id": dataset_field_id}
                )

        return replace_dataset_metas

    def get_chapter_message(self, message):
        """
        解析某用户的简讯数据集字段内容
        :param message:
        :return:
        """
        feed_model = self.feed_model
        recipient = self.recipient
        replace_chapter_metas = self.find_chapter_meta(message)
        if not replace_chapter_metas:
            return message
        # 构造数据集的过滤条件query struct where对象
        dataset_filter_map = get_chapter_filter_query_struct_obj(feed_model.chapters, feed_model.filters)
        # 当前简讯的数值格式
        dataset_field_display_format_map = {item.field_id: item.get_dict()
                                            for item in (feed_model.display_format or [])}
        # 处理简讯的每一个明细段落
        chapter_model_map = {chapter_model.id: chapter_model for chapter_model in (feed_model.chapters or [])}
        for chapter_id, item in replace_chapter_metas.items():
            # 获取每一个明细段落的结果
            chapter_model = chapter_model_map.get(chapter_id) if chapter_model_map.get(chapter_id) else None
            chapter_message = self.get_chapter_parse_data(recipient, chapter_model,
                                                          dataset_field_display_format_map, dataset_filter_map)
            item['target_val'] = chapter_message

        # 替换明细段落标签
        for chapter_id, item in replace_chapter_metas.items():
            # 明细段落与主段落的换行判断处理
            target_val = item.get('target_val', '')
            if not message.startswith(item.get('meta')) and message.find('<br>' + item.get('meta')) == -1:
                target_val = '<br>' + target_val
            if message.find(item.get('meta') + '<br>') == -1:
                target_val += '<br>'
            message = message.replace(item.get('meta'), target_val)

        message = self.clean_chapter_message(message)
        return message

    def get_chapter_parse_data(self, recipient, chapter_model, dataset_field_display_format_map, dataset_filter_map):
        """
        获取每个明细段落的内容解析
        :param recipient:
        :param chapter_model:
        :param dataset_field_display_format_map:
        :param dataset_filter_map:
        :return:
        """
        try:
            if not chapter_model:
                raise UserError(message="明细段落model对象为空")
            chapter_message = chapter_model.chapter_message

            logger.error(f"简讯明细段落数据开始获取，段落ID：{chapter_model.id} 数据集ID：{chapter_model.dataset_id}")
            replace_field_data = self.find_chapter_message_field(chapter_message)

            # 不存在字段，无需请求数据集，直接返回明细段落结果
            if not replace_field_data:
                logger.info("明细段落是纯文本内容，获取完成")
                return chapter_message

            query_dataset_field_list = self.get_query_dataset_field_list(chapter_model, replace_field_data)
            kwargs = {
                "recipient": recipient,
                "dataset_id": chapter_model.dataset_id,
                "query_dataset_field_list": query_dataset_field_list,
                "dataset_filter_map": dataset_filter_map,
            }
            data_list = query_chapter_detail_message(**kwargs)
            if not data_list:
                raise UserError(message="数据集查询结果为空")

            # 明细段落的数据每一行进行替换和数值格式化
            if chapter_message and data_list:
                chapter_message = self.chapter_message_replace_format(chapter_message, data_list, replace_field_data,
                                                                      query_dataset_field_list,
                                                                      dataset_field_display_format_map)
        except Exception as e:
            # 发生异常，明细段落数据不合并到主段落
            chapter_message = ''
            logger.error('简讯明细段落数据获取异常，不会合并到主段落 err:%s trace:%s' % (str(e), traceback.format_exc()))

        # 去掉特别标签
        chapter_message = self.clean_chapter_message(chapter_message)
        logger.info("简讯明细段落数据获取完成")
        return chapter_message

    def chapter_message_replace_format(self, chapter_message, data_list, replace_field_data, query_dataset_field_list,
                                       dataset_field_display_format_map):
        """
        明细段落的数据每一行进行替换和数值格式化
        :param chapter_message:
        :param data_list:
        :param replace_field_data:
        :param query_dataset_field_list:
        :param dataset_field_display_format_map:
        :return:
        """
        if not chapter_message or not data_list:
            return chapter_message

        dataset_field_map = {item.get("id"): item for item in query_dataset_field_list}
        all_chapter = []
        for item in data_list:
            # 处理明细的每一行
            tmp_chapter_message = chapter_message
            for field_id, replace_field in replace_field_data.items():
                # 对数值类型字段进行格式化
                dataset_field = dataset_field_map.get(field_id)
                val = ''
                if not dataset_field:
                    logger.error(f"数据集字段不存在，结果为空。field_id：{field_id}")
                else:
                    val = item.get(field_id)
                    val = self.chapter_format_field(dataset_field, dataset_field_display_format_map, val)
                tmp_chapter_message = tmp_chapter_message.replace(replace_field.get("meta"), str(val))
                # 明细段落每一行的数据需要清除标签
                tmp_chapter_message = self.clean_tags(tmp_chapter_message)
            all_chapter.append(tmp_chapter_message)
        # 明细段落每一行，采用<br>连接
        return "<br>".join(all_chapter)

    @classmethod
    def clean_chapter_message(cls, message):
        """
        去除多余标签
        :param message:
        :return:
        """
        if not message:
            return message
        message = re.sub('<samp-chapter.*?>|</samp-chapter>', '', message)
        message = cls.clean_message(message)
        return message

    @staticmethod
    def find_chapter_message_field(chapter_message):
        """
        获取某个明细段落的字段列表信息
        :param chapter_message:
        :return:
        """
        replace_field_data = {}
        field_meta_list = re.findall("<samp[^>]*>[^<>]+</samp>", chapter_message, flags=re.I)
        if not field_meta_list:
            return replace_field_data

        for field_meta in field_meta_list:
            dataset_field_id = ""
            samp_list = field_meta.split(" ")
            for samp in samp_list:
                if samp.find("data-colid") > -1:
                    dataset_field_match = re.findall("data-colid=\"(.*)\"", samp.strip(), flags=re.I)
                    dataset_field_id = dataset_field_match[0] if dataset_field_match else ""

            if dataset_field_id:
                replace_field_data[dataset_field_id] = {"meta": field_meta}
        return replace_field_data

    @staticmethod
    def find_chapter_meta(message):
        """
        查询message中的明细段落的占位符数据
        :param message:
        :return:
        """
        replace_chapter_metas = {}
        chapter_meta_list = re.findall("<samp-chapter[^>]*>[^<>]*</samp-chapter>", message, flags=re.I)
        for meta in chapter_meta_list:
            samp_list = meta.split(" ")
            chapter_id = ''
            for samp in samp_list:
                if samp.find("data-chapter-id") > -1:
                    data_chapter_match = re.findall("data-chapter-id=\"(.*)\"", samp.strip(), flags=re.I)
                    chapter_id = data_chapter_match[0] if data_chapter_match else ""
            if chapter_id:
                replace_chapter_metas[chapter_id] = {"meta": meta}
            else:
                logger.error("简讯明细段落id不存在，meta：" + meta)
        return replace_chapter_metas


class MsgDataParse:
    """
    简讯内容解析对象实例化类
    """
    MSG_PARSE_MAP = {
        # 简讯HTML数据模式
        SubscribeSendEditorMode.HTML.value: MsgDataHtmlParse,
        # 简讯json数据模式
        SubscribeSendEditorMode.JSON.value: MsgDataJsonParse
    }

    @staticmethod
    def get_instance(feed_model, recipient) -> Union[MsgDataJsonParse, MsgDataHtmlParse]:
        """
        获取简讯内容解析的实例化对象
        :param feed_model:
        :param recipient:
        :return:
        """
        msg_subscribe_config_model = feed_model.msg_subscribe_config
        editor_mode = msg_subscribe_config_model.editor_mode

        parse_obj = None
        if editor_mode in MsgDataParse.MSG_PARSE_MAP and MsgDataParse.MSG_PARSE_MAP.get(editor_mode):
            parse_obj = MsgDataParse.MSG_PARSE_MAP.get(editor_mode)
        elif feed_model.type == FeedType.Email.value:
            # 邮件简讯执行HTML解析模式
            parse_obj = MsgDataHtmlParse

        if not parse_obj:
            parse_obj = MsgDataHtmlParse

        return parse_obj(feed_model, recipient)
