#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import re
from loguru import logger


def format_feed_value(value, field_display_format=None):
    try:
        if not field_display_format:
            return value
        if field_display_format.get('display_format') == 2:
            value = value * 100
        if field_display_format.get('value_unit') == 1:
            value = value / 10000
        if field_display_format.get('value_unit') == 2:
            value = value / 100_000_000
        if field_display_format.get('digit') > 0:
            value = round(value, field_display_format.get('digit'))
        else:
            value = int(round(value, 0))
        if field_display_format.get('digit') > 0:
            value = f'%.{field_display_format.get("digit")}f' % value
        if field_display_format.get('use_thousands') == 1:
            value = format_num(value)
        if field_display_format.get('display_format') == 2:
            value = f'{value}%'
        if field_display_format.get('value_unit') == 1 and field_display_format.get('show_value_unit') == 1:
            value = f'{value}万'
        if field_display_format.get('value_unit') == 2 and field_display_format.get('show_value_unit') == 1:
            value = f'{value}亿'
    except Exception as e:
        logger.error(f"数值格式解析异常，errs:{str(e)}")
    return value


def format_num(num):
    num = str(num)
    num_arr = num.split('.')

    pattern = r'(\d+)(\d{3})((,\d{3})*)'
    while True:
        num_arr[0], count = re.subn(pattern, r'\1,\2\3', num_arr[0])
        if count == 0:
            break
    return '.'.join(num_arr)