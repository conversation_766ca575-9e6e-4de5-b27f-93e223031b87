#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import time

from dmplib import config
from base.enums import ThirdPartyAppCode
from components.url import url_add_param
from feed.repositories import dashboard_feeds_repository
from integrate.services import third_party_service
from integrate.models import ThirdPartyUrlModel


def get_redirect_url(project_code, feed_data, report_url, dmp_send_time, snap_id=None):
    """
    获取报表的集成地址
    :param project_code:
    :param feed_data:
    :param report_url:
    :param dmp_send_time:
    :param snap_id:
    :return:
    """
    report_url = get_report_params_url(report_url, dmp_send_time, snap_id)
    return get_report_assistant_url(project_code, feed_data.get("party_id", ""),
                                    feed_data.get("config_id", ""), report_url, feed_data.get("cloud_app"))


def get_report_params_url(report_url, dmp_send_time, snap_id=None):
    """
    获取报表url附加参数后的地址
    :param report_url:
    :param dmp_send_time:
    :param snap_id:
    :return:
    """
    if not report_url:
        return report_url
    report_url = url_add_param(report_url, {"dmp_send_date": time.strftime("%Y-%m-%d", time.localtime())})
    report_url = url_add_param(report_url, {"send_time": dmp_send_time})
    if snap_id:
        report_url = url_add_param(report_url, {"snap_id": snap_id})
    return report_url


def get_report_assistant_url(project_code, third_party_id, app_id, redirect_url="", cloud_app={}):
    """
    获取对应渠道的集成地址
    :param project_code:
    :param third_party_id:
    :param app_id:
    :param redirect_url:
    :param cloud_app:
    :return:
    """
    if not redirect_url:
        return redirect_url
    third_party_app = get_third_party_app_by_id(app_id)
    params = {
        "project_code": project_code,
        "third_party_id": third_party_id,
        "appid": app_id,
        "cloud_app_code": third_party_app.get('app_code') if third_party_app else None,
        "redirect": redirect_url,
    }
    url_model = ThirdPartyUrlModel(**params)
    redirect_url = third_party_service.get_third_party_integrate_url(url_model)
    redirect_url = get_yzs_app_redirect_url(project_code, third_party_app, redirect_url, cloud_app)
    return redirect_url


def get_yzs_app_redirect_url(project_code, third_party_app, redirect_url, cloud_app={}):
    """
    云助手-自建应用
    云助手-基础应用
    集成url地址特别处理
    :param project_code:
    :param third_party_app:
    :param redirect_url:
    :param cloud_app:
    :return:
    """
    yzs_app_code = [ThirdPartyAppCode.YZS_NEW.value, ThirdPartyAppCode.YZS_BUILTIN.value]
    app_code = third_party_app.get('app_code')
    if app_code in yzs_app_code:
        agent_id = ""
        # 自建应用，重置企业id和应用id
        if app_code == ThirdPartyAppCode.YZS_NEW.value:
            project_code = third_party_app.get("corp_id")
            agent_id = third_party_app.get("app_id")
        elif app_code == ThirdPartyAppCode.YZS_BUILTIN.value:
            # 基础应用，获取公共的应用id
            if not cloud_app:
                cloud_app = dashboard_feeds_repository.get_feed_app(app_code)
            agent_id = cloud_app.get("channel_app_id", "")

        redirect_url = third_party_service.get_yzs_app_redirect_url(
            project_code, agent_id, redirect_url
        )
    return redirect_url


def get_third_party_app_by_id(app_record_id):
    return third_party_service.get_third_party_app_by_id(app_record_id)


def get_third_party_user_by_app_code(third_party_id: str, user_list: list):
    return third_party_service.get_third_party_user_by_app_code(third_party_id, user_list)


def get_super_app_redirect_url(report_id, dmp_send_time, snap_id=None):
    """
    获取报表的集成地址
    :param report_id:
    :param dmp_send_time:
    :param snap_id:
    :return:
    """
    if not report_id:
        return ''
    super_app_url = config.get("Domain.dmp") + '/api/user/superapp'
    super_app_url = url_add_param(super_app_url, {"report_id": report_id})
    super_app_url = get_report_params_url(super_app_url, dmp_send_time, snap_id)
    # 超级APP免权限查看报告，简讯消息关联报告url附带参数 user_auth = 'view,download'
    super_app_url = url_add_param(super_app_url, {"user_auth": 'view,download'})
    return super_app_url

