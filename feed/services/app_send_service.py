#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import datetime
from abc import ABCMeta, abstractmethod
from loguru import logger

from dmplib.utils.strings import seq_id
from base.enums import ThirdPartyAppCode, ThirdPartyAppServiceType, ThirdPartyMsgSendType

from feed.models import MobileSubscribeUserLogModel
from feed.services import dashboard_feeds_service
from feed.services import feed_external_service
from feed.repositories import dashboard_feeds_repository
from components.fast_logger import FastLogger


class AppSendService(metaclass=ABCMeta):
    """
    企业微信消息订阅
    """

    MSG_LIMIT_LEN = 512

    def __init__(self, project_code, feed_id, feed_detail_id, feed_data):
        # 当前租户code
        feed_data["project_code"] = project_code
        self.project_code = project_code
        self.feed_id = feed_id
        self.feed_detail_id = feed_detail_id
        self.feed_data = feed_data
        self.app_code = str(feed_data.get("app_code"))
        self.app_name = None

    @abstractmethod
    def send_app_msg(self):
        raise Exception("该方法只能在子类实例上调用！")

    def record_log(self, level_name, message, extra=None):
        """
        简讯发送逻辑错误记录到天眼
        :param level_name: info: 正常日志，error：错误日志
        :param message: 日志字符串
        :param extra: 扩展信息
        :return:
        """
        if not extra:
            extra = {}
        log_data = {
            "module_type": FastLogger.ModuleType.SUBSCRIBE,
            "biz_type": FastLogger.BizType.PERSONAL_SUBSCRIBE_SEND,
            "biz_id": self.feed_id,
            "error_data_id": self.feed_detail_id,
            "biz_name": dashboard_feeds_service.clear_title_html(self.feed_data.get("subject_email")),
            "error_msg": str(message),
            "extra_info": extra,
            "is_success": 1
        }
        if level_name.lower() == 'error':
            log_data["is_success"] = 0
            log_data["error_type"] = '个人简讯发送用户错误'

        FastLogger.BizErrorFastLogger(**log_data).record()
        del log_data["extra_info"]
        logger.error(log_data)

    def add_mobile_subscribe_user_log(self, feed_model, recipient, textcard_data, dashboard_name=None, feed_data={}):
        """
        记录每个用户的发送内容记录且截取正文512字节长度
        发送给企业微信时，正文长度超过512则截取，首先跳转到详情页
        :param feed_model:
        :param recipient:
        :param textcard_data:
        :param dashboard_name:
        :param feed_data:
        :return:
        """
        try:
            message = textcard_data.get("description")
            is_long = self.check_message_len(message)
            # 存储的报表地址。触发简讯详情机制，则保存报表原始url（首页由详情页进行集成鉴权）；否则是包含第三方应用信息的鉴权url（然后重定向到报表原始url）
            report_url = feed_model.report_real_release_url \
                if self.check_is_long(is_long, feed_model) else feed_model.release_url
            logger.error(f"简讯消息入库的report_url:{report_url}")
            data = {
                "email_subscribe_id": self.feed_id,
                "dashboard_id": feed_model.dashboard_id,
                "msg_title": textcard_data.get("title"),
                "dashboard_name": dashboard_name,
                "report_url": report_url,
                "user_id": recipient.get("id") or None,
                "user_name": recipient.get("name") or None,
                "account": recipient.get("account") or None,
                "msg_content": message,
                "send_time": feed_model.send_time,
                "app_code": feed_model.app_code,
                "is_long": int(is_long),
            }
            model = MobileSubscribeUserLogModel(**data)
            log_id = dashboard_feeds_service.add_mobile_subscribe_user_log(model)

            # 非数见用户的第三方渠道简讯发送，集成报告url比较特殊。无需进行简讯详情页处理逻辑
            if feed_data.get("msg_send_type") == ThirdPartyMsgSendType.SHU_JIAN_USER.value:
                message_detail_url = self.get_message_detail_url(feed_model.party_id, feed_model.config_id, log_id)
                # 无关联报告的设置为消息详情地址；服务商模式如果未绑定报告则不设置消息详情地址
                if not feed_model.dashboard_id and not feed_model.report_real_release_url \
                        and feed_data.get("app_service_type") != ThirdPartyAppServiceType.SERVICE.value:
                    dashboard_feeds_service.update_mobile_subscribe_user_log({"report_url": message_detail_url}, log_id)
                    textcard_data["url"] = message_detail_url
                    logger.error(f"关联消息-简讯无报告的详情页url:{message_detail_url}")
                # 字节长度大于512且是企业微信或云助手
                if self.check_is_long(is_long, feed_model):
                    textcard_data["description"] = self.truncate_message(message)
                    textcard_data["url"] = message_detail_url
                    logger.error(f"关联消息-简讯内容超长的详情页url:{message_detail_url}")

        except Exception as e:
            self.record_log("ERROR", "用户：{}，简讯内容记录写入失败".format(recipient.get("name")) + " err:" + str(e))

    def add_subscribe_send_detail_log(self, recipients, send_time):
        """
        批量写入用户渠道发送消息状态明细记录
        :param recipients:
        :param send_time:
        :return: [{"id": 123, "email_subscribe_id": "123123", ...}, {"id": 456, "email_subscribe_id": "456123", ...}]
        """
        list_data = []
        try:
            for recipient in recipients:
                item = {
                    "id": seq_id(),
                    "email_subscribe_id": self.feed_id,
                    "log_id": self.feed_detail_id,
                    "user_id": recipient.get("id"),
                    "user_name": recipient.get("name"),
                    "account": recipient.get("account"),
                    "plan_send_time": send_time,
                    "app_code": self.app_code,
                    "retry": self.feed_data.get("retry"),
                }
                list_data.append(item)
            if list_data:
                dashboard_feeds_repository.batch_insert_subscribes_send_log(list_data)
            logger.error("批量写入用户渠道发送消息状态记录完成")
        except Exception as e:
            logger.error(f"批量写入用户渠道发送消息状态记录异常，err：{str(e)}")
        return list_data

    @staticmethod
    def update_subscribe_send_detail_status(send_detail_dict):
        """
        批量更新用户渠道发送的消息状态
        使用 DUPLICATE KEY UPDATE 存在重复数据则更新进行批量处理
        e.g.: INSERT INTO t(a,b) VALUES (1, 2) as new_update ON DUPLICATE KEY UPDATE a = new_update.a;
        :param send_detail_dict:
        :return:
        """
        try:
            if not send_detail_dict:
                return False
            list_data = []
            for account, item in send_detail_dict.items():
                tmp_item = {
                    "id": item.get("id"),
                    "email_subscribe_id": item.get("email_subscribe_id"),
                    "log_id": item.get("log_id"),
                    "actual_send_time": item.get("actual_send_time"),
                    "error_reason": item.get("error_reason"),
                    "status": item.get("status"),
                }
                list_data.append(tmp_item)
            if list_data:
                update_fields = ['actual_send_time', 'error_reason', 'status']
                dashboard_feeds_repository.batch_update_subscribes_send_detail_status(list_data, update_fields)
            logger.error("批量更新用户渠道发送消息状态完成")
            return list_data
        except Exception as e:
            logger.exception(f"批量更新用户渠道发送消息状态异常，err：{str(e)}")

    @staticmethod
    def set_send_detail_status(send_detail_dict, account, data):
        """
        更新用户渠道发送消息状态信息
        :param send_detail_dict:
        :param account:
        :param data:
        :return:
        """
        if send_detail_dict.get(account):
            send_detail_dict[account]["actual_send_time"] = data.get("actual_send_time")
            send_detail_dict[account]["status"] = data.get("status")
            send_detail_dict[account]["error_reason"] = data.get("error_reason", "")

    @staticmethod
    def get_now_time():
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    @classmethod
    def check_is_long(cls, is_long, feed_model):
        """
        检测是否触发简讯长度校验机制
        :param is_long:
        :param feed_model:
        :return:
        """
        app_code = feed_model.app_code
        # 云助手场景特别处理，通过first_entry 参数来控制，消息关联是报告还是详情页
        if app_code in [ThirdPartyAppCode.YZS.value, ThirdPartyAppCode.YZS_NEW.value,
                        ThirdPartyAppCode.YZS_BUILTIN.value]:
            msg_subscribe_config_model = feed_model.msg_subscribe_config
            first_entry = msg_subscribe_config_model.first_entry if msg_subscribe_config_model else 0
            logger.error(f"云助手应用消息关联url选项 first_entry：{first_entry}")
            return True if first_entry else False
        # 企业微信、钉钉，开启消息长度自动截断功能
        elif is_long and app_code in [ThirdPartyAppCode.QYWX.value, ThirdPartyAppCode.DD.value]:
            return True
        return False

    @classmethod
    def check_message_len(cls, message):
        msg_len = len(message.encode('utf8'))
        return msg_len > cls.MSG_LIMIT_LEN

    @classmethod
    def truncate_message(cls, message):
        """
        对简讯正文截取512字节长度
        :param message:
        :return:
        """
        i = cls.MSG_LIMIT_LEN
        b_msg = message.encode('utf8')
        while True:
            try:
                msg = b_msg[0:i].decode('utf8')
                return msg
            except Exception:
                i -= 1

    def get_message_detail_url(self, party_id, config_id, log_id):
        """
        获取简讯的详情页
        :param party_id:
        :param config_id:
        :param log_id:
        :return:
        """
        # http://dmp-test.mypaas.com.cn/static/msg/index.html?log_id=39ffb306-bbfd-4cd3-1fd9-fa509ea438c4
        # 前端页面路由地址
        detail_url = f"/static/msg/index.html?log_id={log_id}"
        return feed_external_service.get_report_assistant_url(self.project_code, party_id, config_id, detail_url)

