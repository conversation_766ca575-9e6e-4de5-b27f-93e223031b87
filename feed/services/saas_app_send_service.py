#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import json
from loguru import logger
from typing import Union
import time
import random
import jwt

from components.saas_app_api import SaaSApp<PERSON>pi, SaaSYL<PERSON>f<PERSON>pi, SuperWork<PERSON>pi
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib import config

from base import repository
from base.enums import ThirdPartyAppCode, SubscribeSendDetailLogStatus
from feed.repositories import dashboard_feeds_repository
from feed.services.msg_get_data_service import get_message_data
from feed.services.app_send_service import AppSendService
from components.common_service import get_superportal_host


class SaasAppApiService:
    SAAS_APP_MAP = {
        ThirdPartyAppCode.YKJ.value: SaaSAppApi,
        ThirdPartyAppCode.YL_KF.value: SaaSYLKfApi,
        # 智慧客服APP
        ThirdPartyAppCode.KF_APP.value: SaaSYL<PERSON>f<PERSON><PERSON>,
        # 智慧客服企微通
        ThirdPartyAppCode.KF_WEWORK.value: SaaSYLKfApi,
        # 超级工作台，产业建管的简讯发送
        ThirdPartyAppCode.SuperWork.value: SuperWorkApi,
        ThirdPartyAppCode.MYCYJG.value: SuperWorkApi,
    }

    @classmethod
    def get_saas_app_api(cls, app_info) -> Union[SaaSAppApi, SaaSYLKfApi, SuperWorkApi]:
        # 应用code
        app_code = str(app_info.get("app_code"))
        app_api = SaasAppApiService.SAAS_APP_MAP.get(app_code) \
            if app_code in SaasAppApiService.SAAS_APP_MAP.keys() else SaaSAppApi
        # 获取应用信息
        data = cls.get_saas_info(app_code, app_info)
        return app_api(data)

    @staticmethod
    def get_saas_info(app_code, app_info):
        """
        获取应用信息
        :param app_code:
        :param app_info:
        :return:
        """
        cloud_app_info = dashboard_feeds_repository.get_feed_app(app_code)
        if not cloud_app_info:
            raise UserError(message=f"未匹配到内置云应用（app_code:{app_code}）")
        # 合并应用Host信息
        app_info["cloud_app_name"] = cloud_app_info.get("app_name")
        app_info["api_host"] = cloud_app_info.get("api_host")
        # 公共客服组实现的-环境级秘钥的渠道列表
        kf_third_party = SaasAppApiService.get_kf_third_party()
        cyjg_apps = SaasAppApiService.get_cyjg_third_party()
        # 云空间是租户级的秘钥信息
        if app_code == ThirdPartyAppCode.YKJ.value:
            if not app_info["app_id"] or not app_info["app_secret"]:
                raise UserError(message="未匹配到第三方SaaS应用的秘钥信息")
        elif app_code in kf_third_party:
            # 云链智慧客服应用是环境级秘钥信息
            app_info["app_id"] = cloud_app_info.get("channel_app_id")
            app_info["app_secret"] = cloud_app_info.get("channel_app_secret")
            # 租户代码
            app_info["project_code"] = getattr(g, 'code', None)
        elif app_code in cyjg_apps:
            # 超级APP host获取顺序 租户级->环境配置->环境数据库cloud_apps
            app_info["api_host"] = get_superportal_host(cloud_app_info)

            app_info["channel_app_id"] = cloud_app_info.get("channel_app_id")
            app_info["channel_app_secret"] = cloud_app_info.get("channel_app_secret")
            app_info["project_code"] = getattr(g, 'code', None)
        return app_info

    @staticmethod
    def get_kf_third_party():
        """
        获取公共客服组实现的渠道列表
        :return:
        """
        return [
            ThirdPartyAppCode.YL_KF.value, ThirdPartyAppCode.KF_APP.value,
            ThirdPartyAppCode.KF_WEWORK.value
        ]

    @staticmethod
    def get_cyjg_third_party():
        """
        获取超级APP，产业建管的渠道列表
        :return:
        """
        return [
            ThirdPartyAppCode.SuperWork.value, ThirdPartyAppCode.MYCYJG.value,
        ]


class SaasAppUserApiService:

    def __init__(self, app_info):
        try:
            self.app_code = str(app_info.get("app_code"))
            self.saas_app_api = SaasAppApiService.get_saas_app_api(app_info)
        except Exception as e:
            logger.exception(e)
            raise e

    def get_user_group_tree(self, page=1, page_size=10000):
        """
        获取云服务应用的用户组织列表
        :return:
        """
        user_group_list = list()
        try:
            user_group_list = self.saas_app_api.get_user_group_tree(page, page_size)
            tree_list = self.get_group_level_tree(user_group_list)
            return tree_list
        except Exception as e:
            logger.exception(f"请求用户组织接口异常：{str(e)}")
            return user_group_list

    def get_group_level_tree(self, list_data):
        """
        返回树形格式数据
        :param list_data:
        :return:
        """
        result = []
        tmp_dict = {}
        for group in list_data:
            if not isinstance(group, dict):
                continue
            item, parent_group_code, group_code = self._format_tree_by_party(group)
            if parent_group_code not in tmp_dict:
                result.append(item)
                tmp_dict[group_code] = item
            else:
                tmp_dict.get(parent_group_code)['sub'].append(item)
                tmp_dict[group_code] = item
        return result

    def _format_tree_by_party(self, data):
        """
        按第三方应用解析数据格式
        :param data:
        :return:
        """
        item = data
        parent_group_code, group_code = '', ''
        if self.app_code == ThirdPartyAppCode.YKJ.value:
            item = {
                "id": data.get("id"),
                "name": data.get("name"),
                "parent_id": data.get("parent_id"),
                "level": data.get("level"),
                "sub": []
            }
            # code格式: 1000、1000.1001
            group_code = data.get('code')
            parent_group_code = (
                group_code[0: len(group_code) - 5]
            )
        elif self.app_code in SaasAppApiService.get_kf_third_party():
            item = {
                "id": data.get("id"),
                "name": data.get("name"),
                "parent_id": data.get("parent_id"),
                "org_code": data.get("org_code"),
                "level": 0,
                "sub": []
            }
            group_code = data.get('org_code')
            group_code_list = group_code.split('.')
            # 得到父层级编码，例如：group_code=00.A0233.B0020.C0007.D0016.E0010. parent_group_code=00.A0233.B0020.C0007.D0016.
            group_code_list.pop(len(group_code_list) - 2)
            parent_group_code = ".".join(group_code_list)
        elif self.app_code in SaasAppApiService.get_cyjg_third_party():
            item = {
                "id": data.get("BUGUID"),
                "name": data.get("BUName"),
                "parent_id": data.get("ParentGUID"),
                "org_code": data.get("OrderHierarchyCode"),
                "level": 0,
                "sub": []
            }
            # code格式: 1000、1000.1001
            group_code = data.get('OrderHierarchyCode')
            parent_group_code = (
                group_code[0: len(group_code) - 5]
            )
        return item, parent_group_code, group_code

    def _get_user_list_params(self, query_model):
        """
        按第三方应用拼装用户列表的请求参数
        :param query_model:
        :return:
        """
        params = {}
        if self.app_code == ThirdPartyAppCode.YKJ.value:
            params = {
                "page": query_model.page,
                "pageSize": query_model.page_size,
            }
            if query_model.group_id:
                params["groupId"] = query_model.group_id
            if query_model.keyword:
                params["keyword"] = query_model.keyword
        elif self.app_code in SaasAppApiService.get_kf_third_party() \
                or self.app_code in SaasAppApiService.get_cyjg_third_party():
            params = {
                "page": query_model.page,
                "page_size": query_model.page_size,
            }
            if query_model.group_id:
                params["org_id"] = query_model.group_id
            if query_model.keyword:
                params["keyword"] = query_model.keyword

        return params

    def get_user_list(self, query_model):
        """
        查询云服务接口获取组织下的用户列表
        :param query_model:
        :return:
        """
        params = self._get_user_list_params(query_model)
        result = self.saas_app_api.get_user_list(params)
        list_data = result.get("data") if result.get("data") else list()
        new_data = []
        if list_data:
            for item in list_data:
                new_item = self._format_user_list_by_party(item)
                # 过滤用户id为空的
                if not new_item.get("id"):
                    continue
                new_data.append(new_item)
        query_model.items = new_data
        query_model.total = result.get("total")
        return query_model

    def _format_user_list_by_party(self, item):
        """
        按第三方应用解析数据格式
        :param item:
        :return:
        """
        if self.app_code in SaasAppApiService.get_cyjg_third_party():
            # 基础数据平台用户。用于产业建管，超级APP
            new_item = {
                "id": item.get("UserGUID"),
                "account": item.get("UserCode"),
                "name": item.get("UserName"),
                "email": item.get("Email"),
                "roles": [],
                "groups": [],
                "organizations": [],
            }
        else:
            new_item = {
                "id": item.get("id"),
                "account": item.get("account"),
                "name": item.get("name"),
                "email": item.get("email"),
                "roles": [],
                "groups": [],
                "organizations": [],
            }
            # 智慧客服组接入的相关应用，是使用手机号，所以这里用户id改变为手机号
            if self.app_code in SaasAppApiService.get_kf_third_party():
                new_item["id"] = item.get("mobile")
                # 注意：智慧客服组，为了兼容线上已存在的数据（id字段已存储为mobile），新增用户id字段特别处理
                new_item["user_id"] = item.get("id")
        return new_item


class SaasAppSendService(AppSendService):
    """
    云服务SaaS应用消息发送
    """
    def __init__(self, project_code, feed_id, feed_detail_id, feed_data):
        super().__init__(project_code, feed_id, feed_detail_id, feed_data)
        try:
            self.record_log('INFO', '个人简讯开始发送', self.feed_data)
            # 简讯应用信息，云空间秘钥判断
            if self.app_code == ThirdPartyAppCode.YKJ.value and (not feed_data.get("app_id")
                                                                 or not feed_data.get("app_secret")):
                raise UserError(message="简讯发送的应用ID和应用秘钥为空，不能发送")
            self.saas_app_api = SaasAppApiService.get_saas_app_api(feed_data)
        except UserError as ue:
            msg = f"初始化云服务请求实例异常，err:{str(ue.message)}"
            self.record_log("ERROR", msg)
            raise UserError(message=msg)

    def validate_feed_model(self, feed_model):
        if not feed_model.recipients:
            msg = "收件人不能为空"
            self.record_log("ERROR", msg)
            raise UserError(message=msg)
        if not self.app_code:
            msg = "第三方应用code不能为空"
            self.record_log("ERROR", msg)
            raise UserError(message=msg)
        try:
            if isinstance(feed_model.recipients, str):
                feed_model.recipients = json.loads(feed_model.recipients)
        except Exception as be:
            msg = "收件人json格式错误：" + str(be)
            self.record_log("ERROR", msg)
            raise UserError(message=msg)

    def send_app_msg(self, feed_model):
        """
        云服务SaaS应用消息发送
        :param feed.models.DashboardFeedsModel feed_model:
        :return:
        """
        # 数据检查
        self.validate_feed_model(feed_model)
        # 获取报告名称
        dashboard_name = ""
        if feed_model.dashboard_id:
            dashboard_name = repository.get_data_scalar("dashboard", {"id": feed_model.dashboard_id}, "name")

        failure_recipients = []
        succeed = 0
        failure = 0

        # 批量写入消息发送用户渠道状态的明细 add_subscribe_send_detail_log
        send_detail_list = self.add_subscribe_send_detail_log(feed_model.recipients, feed_model.send_time)
        send_detail_dict = {item.get("account"): item for item in send_detail_list if item}

        origin_userid = getattr(g, 'userid', None)
        for recipient in feed_model.recipients:
            account = recipient.get("account")
            # 设置当前用户id，名称
            setattr(g, "userid", recipient.get("id"))
            setattr(g, "account", account)
            logger.error("发送简讯用户：" + json.dumps(recipient, ensure_ascii=False))

            # 云链智慧客服简讯使用的云链api数据集，取数请求需设置用户信息
            self._set_yl_g_to_data(recipient, feed_model)
            textcard_data = get_message_data(feed_model, recipient)

            try:
                content_info = {
                    "title": textcard_data.get("title"), "description": textcard_data.get("description"),
                    "url": textcard_data.get("url"), "msg_subscribe_config": textcard_data.get("msg_subscribe_config")
                }

                # 记录每个人的消息内容；add_mobile_subscribe_user_log
                self.add_mobile_subscribe_user_log(feed_model, recipient, content_info, dashboard_name, self.feed_data)
                # 按应用进行消息发送
                result = self._send_msg_by_app(feed_model, recipient, content_info)
                if result.get("errcode") == 0 and not result.get('invaliduser'):
                    succeed += 1
                    self.record_log("INFO", "发送简讯给{}，发送成功。".format(recipient.get("name")))
                    status_data = {"actual_send_time": self.get_now_time(),
                                   "status": SubscribeSendDetailLogStatus.SUCCESS.value}
                else:
                    if result.get('invaliduser'):
                        failure_recipients.append(recipient)
                        failure += 1
                        raise UserError(message=f"用户{recipient.get('name')}发送失败，失败原因：{result.get('errmsg')}")
                    else:
                        raise UserError(message=json.dumps(result))
            except Exception as be:
                failure_recipients.append(recipient)
                failure += 1
                err_msg = be.message if isinstance(be, UserError) else str(be)
                err_msg = err_msg if err_msg else '未知错误'
                msg = "发送简讯给{}，发送失败:{}".format(recipient.get("name"), err_msg)
                self.record_log("ERROR", msg)
                status_data = {"actual_send_time": self.get_now_time(),
                               "status": SubscribeSendDetailLogStatus.FAIL.value, "error_reason": msg}

            # 每个用户消息发送完成后，设置空值
            self._set_g_none()
            # 设置消息状态
            self.set_send_detail_status(send_detail_dict, account, status_data)

        # 批量更新用户的状态和错误日志
        self.update_subscribe_send_detail_status(send_detail_dict)

        setattr(g, 'userid', origin_userid)
        succeed_msg = "简讯发送完成，共发送{total}人，成功{succeed}人，失败{failure}人。".format(
            total=len(feed_model.recipients), succeed=succeed, failure=failure
        )
        self.record_log("INFO", succeed_msg)
        return failure_recipients

    def _send_msg_by_app(self, feed_model, recipient, content_info):
        """
        按第三方进行消息发送
        :param feed_model:
        :param recipient:
        :param content_info:
        :return:
        """
        if self.app_code == ThirdPartyAppCode.YKJ.value:
            return self._send_msg_by_ykj(feed_model, recipient, content_info)
        elif self.app_code in SaasAppApiService.get_kf_third_party():
            return self._send_msg_by_yl_kf(feed_model, recipient, content_info)
        elif self.app_code in SaasAppApiService.get_cyjg_third_party():
            return self._send_msg_by_cyjg(recipient, content_info)

    def _send_msg_by_ykj(self, feed_model, recipient, content_info):
        """
        云空间应用消息发送
        :param feed_model:
        :param recipient:
        :param content_info:
        :return:
        """
        result = self.saas_app_api.send_msg(recipient.get("id"),
                                            feed_model.biz_code,
                                            feed_model.snap_id,
                                            content_info)
        return result

    def _send_msg_by_yl_kf(self, feed_model, recipient, content_info):
        """
        云链智慧客服应用消息发送
        :param feed_model:
        :param recipient:
        :param content_info:
        :return:
        """
        user_id = recipient.get("user_id", None)
        if not user_id:
            raise UserError(message="缺少用户id，请检查简讯配置的用户")
        result = self.saas_app_api.send_msg(recipient,
                                            feed_model,
                                            content_info)
        return result

    def _send_msg_by_cyjg(self, recipient, content_info):
        """
        产业建管、超级APP
        消息发送
        :param recipient:
        :param content_info:
        :return:
        """
        user_id = recipient.get("id", None)
        if not user_id:
            raise UserError(message="缺少用户id，请检查简讯配置的用户")
        result = self.saas_app_api.send_msg(user_id, content_info)
        return result

    def _set_yl_g_to_data(self, recipient, feed_model):
        """
        云链公共客服组数据集取数时，需要在请求中增加用户信息参数
        :param recipient:
        :param feed_model:
        :return:
        """
        # 这几个应用使用的是云链api数据集，简讯数据获取时需特别处理，构建用户cookie信息
        if self.app_code in SaasAppApiService.get_kf_third_party():
            self._set_yl_g_info(recipient, feed_model)

    @staticmethod
    def _set_g_none():
        setattr(g, "userid", None)
        setattr(g, "account", None)
        setattr(g, "cookie", None)

    def _set_yl_g_info(self, recipient, feed_model):
        """
        云链智慧客服简讯取数设置用户信息
        :param recipient:
        :param feed_model:
        :return:
        """
        # 设置当前用户信息
        setattr(g, "userid", recipient.get("user_id"))
        setattr(g, "account", recipient.get("account"))
        self._get_yl_api_user_cookie(recipient, feed_model)

    def _get_yl_api_user_cookie(self, recipient, feed_model):
        """
        设置用户cookie信息
        :param recipient:
        :param feed_model:
        :return:
        """
        token = self._get_yl_api_user_token(recipient, feed_model)
        cookie = {
            "token": token
        }
        setattr(g, "cookie", cookie)
        logger.error("用户cookie.token：" + token)
        return token

    def _get_yl_api_user_token(self, recipient, feed_model):
        """
        构建cookie的token数据
        :param recipient:
        :param feed_model:
        :return:
        """
        data = self._get_yl_api_user_data(recipient, feed_model)
        secret = config.get('JWT.secret')
        token = jwt.encode(data, secret)
        return token

    def _get_yl_api_user_data(self, recipient, feed_model):
        """
        构建云链api数据集请求的用户信息
        :param recipient:
        :param feed_model:
        :return:
        """
        flag = int(time.time()) + random.randint(1, 1000)
        exp = int(time.time()) + 3600
        yl_user_id = recipient.get("user_id")
        yl_user_data = {
            "passport": "",
            "project_code": self.project_code,
            "user_name": recipient.get("name"),
            "user_id": yl_user_id,
            "yl_user_id": yl_user_id,
            "user_auth": "view,download",
            "biz_code": feed_model.biz_code if feed_model.biz_code else "",
            "exp": exp
        }
        data = {
            '_flag': flag,
            'dashboard_id': feed_model.dashboard_id if feed_model.dashboard_id else "",
            'code': self.project_code,
            'account': None,
            'id': "",
            'group_id': "",
            'external_params': yl_user_data,
            'customize_roles': [],
            'external_user_id': "",
            'extend_yl_params': "",
        }
        return data
