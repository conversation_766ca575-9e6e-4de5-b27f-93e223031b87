#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

import logging

from dmplib.utils.errors import UserError
from base.enums import ThirdPartyAppCode
from feed.services.app_send_service import AppSendService
from feed.services.wechat_service import WechatService
from feed.services.saas_app_send_service import SaasAppSendService

logger = logging.getLogger(__name__)


class FactoryFeed:

    APP_SEND_MAP = {
        # 云空间应用
        ThirdPartyAppCode.YKJ.value: SaasAppSendService,
        # 云链智慧客服
        ThirdPartyAppCode.YL_KF.value: SaasAppSendService,
        # 智慧客服APP
        ThirdPartyAppCode.KF_APP.value: SaasAppSendService,
        # 智慧客服企微通
        ThirdPartyAppCode.KF_WEWORK.value: SaasAppSendService,
        # 超级APP
        ThirdPartyAppCode.SuperWork.value: SaasAppSendService,
        # 产业建管
        ThirdPartyAppCode.MYCYJG.value: SaasAppSendService,
    }

    @staticmethod
    def get_instance(project_code, feed_id, feed_detail_id, feed_data):
        """
        获取云应用消息发送实例对象
        :param project_code:
        :param feed_id:
        :param feed_detail_id:
        :param feed_data:
        :return:
        """
        try:
            if not feed_data:
                raise UserError(message="简讯信息不存在，获取发送实例失败")
            app_code = feed_data.get("app_code")
            obj = None
            if app_code in FactoryFeed.APP_SEND_MAP and FactoryFeed.APP_SEND_MAP.get(app_code):
                # 简讯发送模式，判断是否为云空间发送模式（非数见用户模式）
                msg_send_type = feed_data.get("msg_send_type")
                if msg_send_type == 1:
                    obj = FactoryFeed.APP_SEND_MAP.get(app_code)
            else:
                # 默认使用数见用户的发送实例
                obj = WechatService
            if not issubclass(obj, AppSendService):
                raise UserError(message=f"未知的简讯渠道应用类型，获取发送实例失败(app_code:{app_code})")

            return obj(project_code, feed_id, feed_detail_id, feed_data)
        except Exception as e:
            logger.exception(f"简讯渠道应用发送实例获取失败，err：{str(e)}")
            raise e

