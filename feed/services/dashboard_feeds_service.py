#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2018/3/2.
"""
import math
from datetime import datetime, timedelta
import json
import logging
import re
import os
import traceback
import functools

import jwt
import urllib.parse as urlparse

import app_celery
from base import repository
from base.enums import (
    EmailSendType,
    FlowNodeType,
    FlowInstanceStatus,
    FlowStatus,
    FeedType,
    SubscribeSendUserFrom,
    SubscribeCycleType,
    SubscribeSendEditorMode
)
from base.dmp_constant import SUBSCRIBE_MSG_USED_DATASET_ID_SET_KEY
from components import auth_util
from components.url import url_del_param
from components.excel import Excel
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.redis import conn_custom_prefix as redis_conn_prefix
from feed.models import (
    DashboardFeedsModel,
    DashboardSubscribeDisplayFormatModel,
    MsgSubscribeConfigModel,
    MobileSubscribeFilterModel,
    FeedSendRulesModel,
    MobileSubscribeRoleModel,
    MobileSubscribeChapterModel
)
from feed.repositories import dashboard_feeds_repository
from feed.services import msg_get_data_service
from feed.utils.common import subscribe_fast_log_record, xss_clear
from flow.models import FlowNodeModel, FlowInstanceModel
from flow.services import flow_instance_service
from flow.services import flow_service
from rbac.services import data_permissions
from user.repositories import user_repository
from rbac.repositories import role_repository

queue_name = config.get('RabbitMQ.queue_name_flow_feeds', 'Flow-Feeds')
logger = logging.getLogger(__name__)


def subscribe_log_record(action: str):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            is_mobile = False
            try:
                feed_model = args[0]
                is_mobile = kwargs.get("mobile", False)
                return func(*args, **kwargs)
            except Exception as e:
                traceback_msg = traceback.format_exc()
                subscribe_type = 'mobile' if is_mobile else 'email'
                # 简讯解码后名称
                feed_name = msg_get_data_service.clear_title_html(feed_model.subject_email)
                subscribe_fast_log_record(subscribe_type, action, feed_name, feed_model, e, traceback_msg)
                raise UserError(message=str(e))
        return wrapper
    return decorator


def get_config_name(config_ids):
    result = dashboard_feeds_repository.get_config_name_by_ids(config_ids)
    return {_.get("id"): _.get("name") for _ in result}


def get_dashboard_feeds(feed_id):
    """
    获取邮件订阅
    :param str feed_id:
    :return:
    """
    dashboard_feed = dashboard_feeds_repository.get_dashboard_feeds(feed_id)
    if not dashboard_feed:
        raise UserError(message="简讯不存在，请重试")

    # 个人简讯的相关配置
    msg_subscribe_config_model = MsgSubscribeConfigModel()
    if dashboard_feed["msg_subscribe_config"]:
        msg_subscribe_config = json.loads(dashboard_feed["msg_subscribe_config"])
        msg_subscribe_config_model = MsgSubscribeConfigModel(**msg_subscribe_config) \
            if msg_subscribe_config else MsgSubscribeConfigModel()
    dashboard_feed["msg_subscribe_config"] = msg_subscribe_config_model
    # 简讯周期时间段
    dashboard_feed["cycle_times"] = json.loads(dashboard_feed["cycle_times"]) if dashboard_feed["cycle_times"] else []

    dashboard_feed["link_url"] = 1 if dashboard_feed.get("release_url") else 0

    dashboard_feed['flow'] = flow_service.get_flow(feed_id)

    dashboard_feed['display_format'] = dashboard_feeds_repository.get_subscribes_display_format(feed_id)

    dashboard_feed['filters'] = get_mobile_subscribe_filter_list(feed_id)

    dashboard_feed['send_rules'] = get_mobile_subscribe_send_rules(feed_id)

    dashboard_feed['roles'] = get_mobile_subscribe_roles(feed_id)

    dashboard_feed['chapters'] = get_mobile_subscribe_chapters(feed_id)
    return dashboard_feed


def run_feed(flow_id, mobile=False):
    """
    立即发送订阅（包括邮件和简讯）
    :param flow_id
    :param mobile
    :return:
    """
    # 简讯订阅使用celery，需要传入command
    if mobile:
        run_celery(flow_id)
    else:
        flow_service.run_flow(flow_id, queue_name=queue_name)


def update_feeds_send_rules(feed_id, models: [FeedSendRulesModel]):
    # 要考虑多个数据集的变动，还要与数据库的数据做对比，不如直接删除再添加
    try:
        repository.delete_data('mobile_subscribe_rules', {'email_subscribe_id': feed_id})
        for model in models:
            repository.add_data('mobile_subscribe_rules', {
                'email_subscribe_id': feed_id,
                'dataset_id': model.dataset_id,
                'rule_info': json.dumps(model.rule_info, ensure_ascii=False)
            })
    except:
        logger.error('简讯保存发送条件失败：%s' % traceback.format_exc())
        raise UserError(message='保存发送条件失败，请重新保存再试')


@subscribe_log_record('edit')
def update_dashboard_feeds(model, mobile=False, terminal_type=""):
    """
    更新邮件订阅
    :param dashboard.models.DashboardFeedsModel model:
    :param mobile
    :param terminal_type
    :return:
    """
    model.validate()
    if not get_dashboard_feeds(model.id):
        raise UserError(message='邮件订阅不存在')

    # 报告发布地址
    dashboard_page_url = get_dashboard_url(model.dashboard_id, terminal_type)

    fields = [
        'subject_email',
        'send_frequency',
        'recipients',
        'addresser',
        'message',
        'report_from',
        'frequency_value',
        'release_url',
        'dashboard_id',
        'user_token',
        'msg_subscribe_config',
        'is_available'
    ]
    dataset_ids = []
    # 添加数据集引用
    try:
        # 简讯编辑器模式
        editor_mode = model.msg_subscribe_config.editor_mode \
            if isinstance(model.msg_subscribe_config, MsgSubscribeConfigModel) else SubscribeSendEditorMode.HTML.value
        dataset_ids.extend(_get_dataset_id_by_message(editor_mode, model.message))
        dataset_ids.extend(_get_dataset_id_by_message(editor_mode, model.subject_email))
        # 简讯的明细段落
        if model.chapters:
            chapter_dataset_ids = _get_dataset_id_by_chapter(model.chapters)
            if chapter_dataset_ids:
                dataset_ids.extend(chapter_dataset_ids)
    except:
        logger.error("数据集引用解析异常")
    fields.append("dataset_ids")
    model.dataset_ids = json.dumps(list(set(dataset_ids)))

    if mobile:
        fields.append("config_id")
        fields.extend(['cycle_type', 'cycle_times'])
        model.release_url = ''
        if model.link_url and model.link_url not in [0, '0']:
            model.release_url = dashboard_page_url
        model.msg_subscribe_config = json.dumps(model.msg_subscribe_config.get_dict())
        model.cycle_times = json.dumps(model.cycle_times)
    else:
        # 邮件不需要个人简讯相关配置
        model.msg_subscribe_config = ''
        user_token = jwt.encode(
            {"id": g.userid, "code": g.code, "account": g.account, "group_ids": g.group_ids, "email": 1},
            config.get('JWT.secret'),
        )
        model.user_token = user_token
        model.release_url = dashboard_page_url

    repository.update_model('dashboard_email_subscribe', model, {'id': model.id}, fields)
    # 更新订阅数值显示格式, 使用事务先删除 再新增
    # 更新数值格式
    dashboard_feeds_repository.replace_subscribes_display_format(model.id, model.display_format or [])
    # 更新简讯的发送规则
    update_feeds_send_rules(model.id, model.send_rules)
    # 更新简讯过滤条件
    dashboard_feeds_repository.replace_mobile_subscribe_data(model.id, model.filters or [],
                                                             MobileSubscribeFilterModel())
    # 更新角色
    dashboard_feeds_repository.replace_mobile_subscribe_data(model.id, model.roles or [], MobileSubscribeRoleModel())
    # 更新简讯明细段落
    if model.chapters:
        for i, item_model in enumerate(model.chapters):
            item_model.chapter_name = f"第{i + 1}段落"
    dashboard_feeds_repository.replace_mobile_subscribe_data(model.id, model.chapters or [],
                                                             MobileSubscribeChapterModel())
    # 更新简讯的flow 信息
    update_dashboard_feeds_flow(model, mobile)
    # 记录简讯使用的调度数据集
    add_subscribe_used_schedule_dataset(g.code, dataset_ids)

    fields.append("flow")
    result = model.get_dict(fields)
    result["display_format"] = []
    for item in model.display_format:
        result["display_format"].append(item.get_dict())
    return result


def update_dashboard_feeds_flow(model, mobile):
    """
    更新简讯的flow 信息
    :param model:
    :param mobile:
    :return:
    """
    # 更新flow , node
    model.flow.id = model.id
    # 替换简讯标题中的HTML字符
    model.flow.name = msg_get_data_service.clear_title_html(model.subject_email)
    model.flow.nodes = [
        FlowNodeModel(
            id=repository.get_data_scalar('node', {'flow_id': model.id}, 'id'),
            name=model.flow.name,
            type=FlowNodeType.EmailFeeds.value,
        )
    ]
    # 更新model.flow.schedule
    cron_exp = get_cycle_day_next_time(mobile, model)
    if cron_exp:
        model.flow.schedule = cron_exp

    flow_instance_id = flow_instance_service.running_flow_is_exists(model.id)
    # 简讯不需要停流程，使用celery
    if flow_instance_id and not mobile:
        flow_instance_service.stop_instance(flow_instance_id)

    flow_service.update_flow(model.flow)

    send_frequency = (
        int(model.send_frequency)
        if model.send_frequency and isinstance(model.send_frequency, str)
        else model.send_frequency
    )

    if send_frequency == EmailSendType.One.value:
        # 简讯订阅使用celery，需要传入command
        if mobile:
            command = get_command(model.flow.id)
            flow_service.disable_flow(model.flow.id, command=command)
            run_celery(model.id)
        else:
            flow_service.disable_flow(model.flow.id, queue_name=queue_name)
            flow_service.run_flow(model.id, queue_name=queue_name)

    elif send_frequency in (EmailSendType.Delayed.value, EmailSendType.Cycle.value):
        # 简讯订阅使用celery，需要传入command
        if mobile:
            command = get_command(model.flow.id)
            flow_service.enable_flow(model.flow.id, command=command)
        else:
            flow_service.enable_flow(model.flow.id, queue_name=queue_name)


def update_day_cycle_feeds_flow(model):
    """
    更新简讯周期天的下次执行时间
    :param model:
    :return:
    """
    cron_exp = None
    send_frequency = (
        int(model.send_frequency)
        if model.send_frequency and isinstance(model.send_frequency, str)
        else model.send_frequency
    )
    # 更新model.flow.schedule
    if send_frequency == EmailSendType.Cycle.value \
            and model.cycle_type == SubscribeCycleType.Day.value \
            and model.cycle_times:
        subject_email = msg_get_data_service.clear_title_html(model.subject_email)
        logger.error(f"开始计算周期天的简讯（name:{subject_email} ID:{model.id}）下次执行时间")
        model.flow.id = model.id
        model.flow.name = subject_email
        model.flow.nodes = [
            FlowNodeModel(
                id=repository.get_data_scalar('node', {'flow_id': model.id}, 'id'),
                name=model.flow.name,
                type=FlowNodeType.EmailFeeds.value,
            )
        ]
        # 获取周期天的下一次执行时间点
        cron_exp = get_cycle_day_next_time(True, model)
        if cron_exp:
            model.flow.schedule = cron_exp
            # 更新flow信息
            flow_service.update_flow(model.flow)
            command = get_command(model.flow.id)
            flow_service.enable_flow(model.flow.id, command=command)
    return cron_exp


def run_celery(flow_id, flow_instance_id=None, retry=0, queue_name='feeds'):
    """
    执行celery异步订阅任务
    :param flow_id:
    :param flow_instance_id:
    :return:
    """
    flow = flow_service.get_flow(flow_id)
    if not flow:
        raise UserError(message='该流程不存在')

    if not flow_instance_id:
        flow_instance = FlowInstanceModel(flow_id=flow.id, name=flow.name, type=flow.type)
        flow_instance_id = flow_instance_service.add_instance(flow_instance)

    app_celery.feed_dashboard.apply_async(
        kwargs={'project_code': g.code, 'data_id': flow_id, 'feed_detail_id': flow_instance_id, 'retry': retry},
        queue=queue_name,
    )


def get_command(flow_id, queue_name='feeds'):
    """
    获取rundeck执行celery的command命令
    :param flow_id:
    :return:
    """
    celery_task_name = "app_celery.feed_dashboard"
    cmd_template_feed = config.get(
        "Rundeck.cmd_template_celery", "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
    )
    command = '%s %s %s %s %s' % (cmd_template_feed, g.code, celery_task_name, flow_id, queue_name)
    return command


def delete_dashboard_feeds(feeds_id, mobile=False):
    """
    删除邮件订阅
    :param str feeds_id:
    :return:
    """
    dashboard_feeds_data = get_dashboard_feeds(feeds_id)
    if not dashboard_feeds_data:
        raise UserError(message='邮件订阅不存在')
    repository.delete_data("dashboard_email_subscribe", {"id": feeds_id})
    repository.delete_data("dashboard_email_subscribe_detail", {"email_subscribe_id": feeds_id})
    repository.delete_data("mobile_subscribe_rules", {"email_subscribe_id": feeds_id})
    repository.delete_data("dashboard_subscribe_display_format", {"subscribe_id": feeds_id})
    repository.delete_data("mobile_subscribe_filter", {"email_subscribe_id": feeds_id})
    repository.delete_data("dashboard_email_subscribe_extra", {"id": feeds_id})
    repository.delete_data(MobileSubscribeRoleModel.get_table_name(), {"email_subscribe_id": feeds_id})
    repository.delete_data(MobileSubscribeChapterModel.get_table_name(), {"email_subscribe_id": feeds_id})
    flow_service.delete_flow(feeds_id)
    return dashboard_feeds_data


def update_message_snapshot_status(feed_id, is_snap):
    """
    更新简讯的拍照状态
    :param feed_id:
    :param is_snap:
    :return:
    """
    data = {"is_snap": is_snap}
    # 如果关闭拍照， 则需要去掉release_url中的snap_id参数
    if is_snap == 0:
        subscribe = repository.get_data("dashboard_email_subscribe", conditions={"id": feed_id}, fields=["release_url"])
        if not subscribe:
            raise UserError(message="feed_id不存在")
        release_url = url_del_param(subscribe['release_url'], ["snap_id"])
        data.update({"release_url": release_url})

    repository.update("dashboard_email_subscribe", data, {"id": feed_id})


@subscribe_log_record('add')
def add_dashboard_feeds(model, mobile=False, terminal_type=""):
    """
    新增简讯订阅、邮件订阅
    :param dashboard.models.DashboardFeedsModel model:
    :param mobile:
    :param terminal_type:
    :return:
    """
    model.id = seq_id()
    model.validate()
    for display_format_model in model.display_format:
        display_format_model.validate()

    # 报告发布地址
    dashboard_page_url = get_dashboard_url(model.dashboard_id, terminal_type)

    # 添加数据集引用
    dataset_ids = []
    # 添加数据集引用
    try:
        # 简讯编辑器模式
        editor_mode = model.msg_subscribe_config.editor_mode \
            if isinstance(model.msg_subscribe_config, MsgSubscribeConfigModel) else SubscribeSendEditorMode.HTML.value
        dataset_ids.extend(_get_dataset_id_by_message(editor_mode, model.message))
        dataset_ids.extend(_get_dataset_id_by_message(editor_mode, model.subject_email))
        # 简讯明细段落
        if model.chapters:
            chapter_dataset_ids = _get_dataset_id_by_chapter(model.chapters)
            if chapter_dataset_ids:
                dataset_ids.extend(chapter_dataset_ids)
    except:
        logger.error("数据集引用解析异常")

    if mobile:
        data = model.get_dict(
            [
                'id',
                'name',
                'dashboard_id',
                'subject_email',
                'send_frequency',
                'recipients',
                'addresser',
                'message',
                'report_from',
                'frequency_value',
                'config_id',
                'msg_subscribe_config',
                'cycle_type',
                'cycle_times',
            ]
        )
        data["dataset_ids"] = json.dumps(list(set(dataset_ids)))
        data["type"] = FeedType.Mobile.value
        # 默认给个空，主要是数据库会有warm日志
        data["addresser"] = ""
        # 根据model.link_url计算发布报告地址
        model.release_url = ''
        if model.link_url and model.link_url not in [0, '0']:
            data["release_url"] = dashboard_page_url
        data["msg_subscribe_config"] = json.dumps(model.msg_subscribe_config.get_dict())
        data["cycle_times"] = json.dumps(model.cycle_times)
    else:
        user_token = jwt.encode(
            {"id": g.userid, "code": g.code, "account": g.account, "group_ids": g.group_ids, "email": 1},
            config.get('JWT.secret'),
        )
        model.user_token = user_token

        data = model.get_dict(
            [
                'id',
                'name',
                'dashboard_id',
                'subject_email',
                'send_frequency',
                'recipients',
                'addresser',
                'message',
                'report_from',
                'frequency_value',
                'user_token',
            ]
        )
        data["type"] = FeedType.Email.value
        data["release_url"] = dashboard_page_url
        data["dataset_ids"] = json.dumps(list(set(dataset_ids)))

    repository.add_data('dashboard_email_subscribe', data)

    # 如果有数值显示格式则更新数值展示
    if model.display_format:
        display_format_data = [{**item.get_dict(), "subscribe_id": model.id} for item in model.display_format]
        dashboard_feeds_repository.batch_insert_subscribes_display_format(display_format_data)

    # 添加简讯过滤条件
    if model.filters:
        filter_data_list = [{**item.get_dict(), "email_subscribe_id": model.id} for item in model.filters]
        dashboard_feeds_repository.batch_insert_mobile_subscribe_filters(filter_data_list)

    if model.send_rules:
        filter_data_list = [{
            'email_subscribe_id': model.id,
            'dataset_id': send_rule.dataset_id,
            'rule_info': json.dumps(send_rule.rule_info, ensure_ascii=False)
        } for send_rule in model.send_rules]
        dashboard_feeds_repository.batch_insert_send_rules(filter_data_list)

    # 添加简讯角色
    if model.roles:
        role_list = [{**item.get_dict(), "email_subscribe_id": model.id} for item in model.roles]
        dashboard_feeds_repository.batch_insert_mobile_subscribe_roles(role_list)

    # 添加简讯的明细段落
    if model.chapters:
        chapter_list = [{**item.get_dict(), "email_subscribe_id": model.id, "chapter_name": f"第{i+1}段落"}
                        for i, item in enumerate(model.chapters)]
        dashboard_feeds_repository.batch_insert_mobile_subscribe_chapters(chapter_list)

    # 记录简讯使用的调度数据集
    add_subscribe_used_schedule_dataset(g.code, dataset_ids)

    try:
        add_dashboard_feeds_flow(model, mobile)
    except Exception as ex:
        logger.exception(ex)
        repository.delete_data('dashboard_email_subscribe', {'id': model.id})
        if model.send_rules:
            repository.delete_data('mobile_subscribe_rules', {'email_subscribe_id': model.id})
        flow_service.delete_flow(model.id, is_delete_scheduler=False)
        repository.delete_data('dashboard_subscribe_display_format', {'subscribe_id': model.id})
        raise UserError(message="新增订阅错误，错误内容：" + str(ex))

    return model.get_dict(["id", "subject_email"])


def add_dashboard_feeds_flow(model, mobile):
    """
    添加简讯的flow 信息
    :param model:
    :param mobile:
    :return:
    """
    model.flow.id = model.id
    # 替换简讯标题中的HTML字符
    model.flow.name = msg_get_data_service.clear_title_html(model.subject_email)
    model.flow.nodes = [FlowNodeModel(name=model.subject_email, type=FlowNodeType.EmailFeeds.value)]
    # 更新schedule model.flow.schedule
    cron_exp = get_cycle_day_next_time(mobile, model)
    if cron_exp:
        model.flow.schedule = cron_exp

    flow_service.add_flow(model.flow)

    send_frequency = (
        int(model.send_frequency)
        if model.send_frequency and isinstance(model.send_frequency, str)
        else model.send_frequency
    )
    if send_frequency == EmailSendType.One.value:
        if mobile:
            run_celery(model.id)
        else:
            flow_service.run_flow(model.id, queue_name=queue_name)
    elif send_frequency in (EmailSendType.Delayed.value, EmailSendType.Cycle.value):
        if mobile:
            command = get_command(model.flow.id)
            flow_service.update_flow_schedule(model.id, command=command)
        else:
            flow_service.update_flow_schedule(model.id, queue_name=queue_name)


def get_dashboard_url(dashboard_id, terminal_type):
    dashboard_page_url = ""
    if dashboard_id:
        from dashboard_chart import external_service
        hd_report_redirect = external_service.get_url_for_external_report_redirect(dashboard_id)
        if hd_report_redirect:
            return hd_report_redirect

        dashboard_page_url = config.get('Domain.dmp')
        if terminal_type == "mobile_screen":
            dashboard_page_url += "/dataview-mobile/view"
        else:
            dashboard_page_url += f"{auth_util.get_frontend_prefix()}/dataview/share"
        dashboard_page_url += f"/{dashboard_id}?code={g.code}"
    return dashboard_page_url


def get_dashboard_feeds_list(query_model):
    """
    :获取邮件订阅列表
    :return :
    """
    return dashboard_feeds_repository.get_dashboard_feeds_list(query_model)


def get_dashboard_feeds_details(query_model):
    """
    :获取邮件订阅详情
    :return :
    """
    return dashboard_feeds_repository.get_dashboard_feeds_details(query_model)


def retry(instance_id, flow_id):
    """
    邮件重发
    :param instance_id:
    :param flow_id:
    :return:
    """
    repository.update_data('instance', {'status': FlowInstanceStatus.Created.value}, {"id": instance_id})
    flow_service.run_flow(flow_id, is_continue=1, queue_name=queue_name, instance_id=instance_id)


def mobile_retry(instance_id, flow_id, retry):
    """
    简讯重发
    :param instance_id:
    :param flow_id:
    :return:
    """
    repository.update_data('instance', {'status': FlowInstanceStatus.Created.value}, {"id": instance_id})
    run_celery(flow_id, instance_id, retry)


def enable_flow(flow_id):
    """
    启动流程
    :param str flow_id:
    :return:
    """
    model = flow_service.get_flow(flow_id)
    if not model:
        raise UserError(message='流程不存在')
    feed_type = repository.get_data_scalar('dashboard_email_subscribe', {'id': model.id}, 'type')
    model.status = FlowStatus.Enable.value
    repository.update_data('flow', model.get_dict(['status']), {'id': model.id})
    if feed_type == FeedType.Mobile.value:
        # 启动周期天的简讯，更新model.schedule
        feed_data = get_dashboard_feeds(flow_id)
        dashboard_feed_model = generate_dashboard_feed_model(feed_data)
        cron_exp = get_cycle_day_next_time(True, dashboard_feed_model)
        if cron_exp:
            model.schedule = cron_exp
            flow_service.update_flow(model)
        # 更新flow
        command = get_command(flow_id)
        flow_service.update_flow_schedule(flow_id, command=command)
    else:
        flow_service.update_flow_schedule(flow_id, queue_name=queue_name)
    return True


def disable_flow(flow_id):
    """
    禁用流程
    :param str flow_id:
    :return:
    """
    model = flow_service.get_flow(flow_id)
    if not model:
        raise UserError(message='流程不存在')
    feed_type = repository.get_data_scalar('dashboard_email_subscribe', {'id': model.id}, 'type')
    model.status = FlowStatus.Disable.value
    repository.update_data('flow', model.get_dict(['status']), {'id': model.id})
    if feed_type == FeedType.Mobile.value:
        command = get_command(flow_id)
        flow_service.update_flow_schedule(flow_id, command=command)
    else:
        flow_service.update_flow_schedule(flow_id, queue_name=queue_name)
    return True


def check_user_permission(dashboard_id, biz_ids, user_from=SubscribeSendUserFrom.PERSONAL.value):
    """
    检测用户接收移动报告权限
    :param dashboard_id:
    :param biz_ids:
    :param user_from:
    :return:
    """
    permission = []
    no_permission = []
    if not biz_ids:
        raise UserError(message='简讯发送对象未选择')

    user_account = getattr(g, "account", "")
    dashboard_name = repository.get_data_scalar("dashboard", {"id": dashboard_id}, "name")
    dataset_data = []

    # 简讯关联对象为个人权限校验
    if user_from == SubscribeSendUserFrom.PERSONAL.value:
        user_list = user_repository.get_accounts_by_user_ids(biz_ids)
        # 转成 dict
        accounts = {single.get("id"): single.get("account") for single in user_list}

    for biz_id in biz_ids:
        if user_from == SubscribeSendUserFrom.PERSONAL.value:
            setattr(g, "account", accounts.get(biz_id))
        no_permission_content = {}
        if dashboard_id:
            no_permission_content = check_dataset_or_dashboard_permission(
                biz_id, dashboard_id, dashboard_name, dataset_data, user_from
            )
        if no_permission_content and len(no_permission_content) > 0:
            no_permission.append({"biz_id": biz_id, "data": no_permission_content})
        else:
            permission.append(biz_id)

    setattr(g, "account", user_account)
    return {"permission": permission, "no_permission": no_permission}


def check_dataset_or_dashboard_permission(biz_id, dashboard_id, dashboard_name, dataset_data,
                                          user_from=SubscribeSendUserFrom.PERSONAL.value):
    """
    检查数据集和报告是否有查看权限
    :param biz_id:
    :param dashboard_id:
    :param dashboard_name:
    :param dataset_data:
    :param user_from:
    :return:
    """
    no_permission_content = []
    # 简讯关联用户：个人
    if user_from == SubscribeSendUserFrom.PERSONAL.value:
        check_func = check_data_permission
    else:
        # 角色
        check_func = check_data_permission_role
    if not check_func("dashboard", "view", dashboard_id, biz_id):
        no_permission_content.append({"type": "dashboard", "name": dashboard_name, "id": dashboard_id})
    for dataset in dataset_data:
        if not check_func("dataset", "view", dataset.get("dataset_id"), biz_id):
            no_permission_content.append(
                {"type": "dataset", "name": dataset.get("dataset_name"), "id": dataset.get("dataset_id")}
            )
    return no_permission_content


def check_data_permission(data_type, data_action_code, data_id=None, user_id=None):
    try:
        flag = data_permissions.check_has_data_permission(
            data_type, data_action_code, data_id=data_id, user_id=user_id, check_creator=False
        )
    # check_has_data_permission 函数内，会对没有权限的数据抛UserError错误，这里不做处理
    except UserError:
        flag = False
    return flag


def check_data_permission_role(data_type, data_action_code, data_id=None, role_id=None):
    try:
        flag = data_permissions.check_has_data_permission_role(
            role_id, data_type, data_action_code, data_id
        )
    except UserError:
        flag = False
    return flag


def get_dashboard_dataset_info(dashboard_id):
    """
    根据报告id获取发布报告的数据集信息
    :param dashboard_id:
    :return:
    """
    dataset_ids = repository.get_data(
        "dashboard_released_snapshot_chart", {"dashboard_id": dashboard_id}, ["source"], multi_row=True
    )
    # 去重, 去空
    dataset_ids = [row.get("source") for row in dataset_ids if row.get("source")]
    dataset_ids = list(set(dataset_ids))
    if not dataset_ids:
        return []

    dataset_infos = dashboard_feeds_repository.get_dataset_infos(dataset_ids)
    fields = dashboard_feeds_repository.get_dataset_field_infos(dataset_ids)
    dataset_fields = {}
    # 根据dataset_id做一个分类
    for dataset_info in fields:
        if not dataset_fields.get(dataset_info.get("dataset_id")):
            dataset_fields[dataset_info.get("dataset_id")] = [dataset_info]
        else:
            dataset_fields[dataset_info.get("dataset_id")].append(dataset_info)
    # 把分类好的字段放入dataset_infos中
    for dataset_id, dataset_field in dataset_fields.items():
        for dataset_info in dataset_infos:
            if dataset_info.get("id") == dataset_id:
                dataset_info["fields"] = dataset_field
    return dataset_infos


def list_subscribe_config(query_model):
    return dashboard_feeds_repository.get_subscribe_config_list(query_model)


def add_subscribe_config(model):
    model.id = seq_id()
    model.validate()
    repository.add_model('mobile_subscribe_config', model, ["id", "name", "corp_id", "corp_secret", "agent_id",
                                                            "app_code"])
    return model.id


def update_subscribe_config(model):
    model.validate()
    repository.update_model(
        'mobile_subscribe_config', model, {"id": model.id}, ["name", "corp_id", "corp_secret", "agent_id", "app_code"]
    )
    return model.id


def delete_subscribe_config(subscribe_config_id):
    subscribe_config = repository.data_is_exists("mobile_subscribe_config", {"id": subscribe_config_id})
    if not subscribe_config:
        raise UserError(message='简讯订阅配置不存在')
    repository.delete_data("mobile_subscribe_config", {"id": subscribe_config_id})
    return True


@subscribe_log_record('preview')
def feed_mobile_preview(model):
    if model.link_url:
        # 报告发布地址
        dashboard_page_url = ''
        if model.dashboard_id:
            dashboard_page_url = '%s/dataview/share/%s?code=%s' % (g.url, model.dashboard_id, g.code)
        model.release_url = dashboard_page_url
    else:
        model.release_url = ''
    # 根据前端传的用户进行预览
    # 预览的时候，发件人使用当前用用户(不需要name)
    # 前端不传的时候，默认使用当前用户权限
    if not model.recipients:
        model.recipients = [{"id": g.userid, "name": "name", "account": g.account}]

    # 预览展示富文本结果
    model.is_preview = True
    textcard_data = msg_get_data_service.get_message_data(model, model.recipients[0])
    return textcard_data


def generate_dashboard_feed_model(params: dict):
    model = DashboardFeedsModel(**params)
    if model.message and model.type and model.type == FeedType.Email.value:
        model.message = xss_clear(model.message)
    model.display_format = []
    display_format = params.get("display_format", []) or []
    for item in display_format:
        display_format_model = DashboardSubscribeDisplayFormatModel(**item)
        display_format_model.subscribe_id = model.id
        display_format_model.validate()
        model.display_format.append(display_format_model)
    # 个人简讯的相关配置
    msg_subscribe_config = model.msg_subscribe_config
    if msg_subscribe_config and isinstance(msg_subscribe_config, str) and not isinstance(msg_subscribe_config, dict):
        msg_subscribe_config = json.loads(model.msg_subscribe_config)

    if not isinstance(msg_subscribe_config, MsgSubscribeConfigModel):
        msg_subscribe_config_model = MsgSubscribeConfigModel(**msg_subscribe_config) \
            if model.msg_subscribe_config else MsgSubscribeConfigModel()
        model.msg_subscribe_config = msg_subscribe_config_model  # type: MsgSubscribeConfigModel

    # 简讯调度周期信息
    cycle_times = model.cycle_times
    if cycle_times and isinstance(cycle_times, str) and not isinstance(cycle_times, list):
        model.cycle_times = json.loads(model.cycle_times)

    # 保留报表原始的查看url
    release_url = model.release_url if model.release_url else ""
    model.report_real_release_url = release_url
    # url 去除域名
    model.release_url = del_url_domain(release_url)

    # 简讯过滤条件列表
    model.filters = []
    filters = params.get("filters", []) or []
    for item in filters:
        filter_model = MobileSubscribeFilterModel(**item)
        model.filters.append(filter_model)

    # 简讯的数据集规则发送
    # send_rules不是DashboardFeedsModel的字段，所以在外面添加一个属性
    model.send_rules = get_send_rule_models(params.get('send_rules', []))

    # 简讯发送的角色列表
    roles = params.get("roles", []) or []
    model.roles = get_role_models(roles)

    # 简讯的正文段落列表
    chapters = params.get("chapters", []) or []
    model.chapters = get_chapter_models(chapters)

    return model


def get_send_rule_models(send_rules_list):
    models = [FeedSendRulesModel(**s) for s in send_rules_list if s]
    for send_rule_model in models:
        send_rule_model.validate()
    return models


def get_role_models(role_list):
    models = [MobileSubscribeRoleModel(**s) for s in role_list if s]
    for role_model in models:
        role_model.validate()
    return models


def get_chapter_models(chapters):
    models = [MobileSubscribeChapterModel(**s) for s in chapters if s]
    for chapter_model in models:
        chapter_model.validate()
    return models


def del_url_domain(url):
    if not url:
        return url
    url_parts = list(urlparse.urlparse(url))
    url_parts[0] = url_parts[1] = ''
    return urlparse.urlunparse(url_parts)


def get_dataset_fields(dataset_ids):
    """
    根据报告id获取发布报告的数据集信息
    :param dataset_ids:
    :return:
    """
    # 去重, 去空
    dataset_ids = list(set(dataset_ids))
    if not dataset_ids:
        return []

    dataset_infos = dashboard_feeds_repository.get_dataset_infos(dataset_ids)
    fields = dashboard_feeds_repository.get_dataset_field_infos(dataset_ids)
    dataset_fields = {}
    # 根据dataset_id做一个分类
    for dataset_info in fields:
        if not dataset_fields.get(dataset_info.get("dataset_id")):
            dataset_fields[dataset_info.get("dataset_id")] = [dataset_info]
        else:
            dataset_fields[dataset_info.get("dataset_id")].append(dataset_info)
    # 把分类好的字段放入dataset_infos中
    for dataset_id, dataset_field in dataset_fields.items():
        for dataset_info in dataset_infos:
            if dataset_info.get("id") == dataset_id:
                dataset_info["fields"] = dataset_field
    return dataset_infos


def get_project_yzs_config():
    return repository.get_data('project_yzs_config', {'code': g.code}, from_config_db=True)


def get_message_limit_remind(message):
    if message and isinstance(message, str):
        return "文本不超过512个字节，超过会自动截断，目前字节数: {}".format(len(message.encode('utf8')))
    return ''


def add_mobile_subscribe_user_log(model):
    """
    添加简讯的用户内容记录
    :param model:
    :return:
    """
    model.id = seq_id()
    model.validate()
    repository.add_model('mobile_subscribe_user_log', model)
    return model.id


def update_mobile_subscribe_user_log(data, log_id):
    """
    更新简讯的用户内容记录
    :param data:
    :param log_id:
    :return:
    """
    if not log_id:
        return False
    return repository.update_data('mobile_subscribe_user_log', data, {'id': log_id})


def get_mobile_subscribe_user_log(log_id):
    """
    获取当前登录用户的简讯详情
    :param str log_id:
    :return:
    """
    params = {"log_id": log_id, "user_id": g.userid, "account": g.account}
    logger.error(f"简讯详情查看参数：{json.dumps(params)}")

    content_log = dashboard_feeds_repository.get_mobile_subscribe_user_log(log_id, g.userid, g.account)
    if content_log:
        logger.error(f"简讯详情内容：{json.dumps(content_log, ensure_ascii=False)}")

    if not content_log:
        raise UserError(message='当前简讯内容不存在，请重试')
    content_log["msg_title"] = msg_get_data_service.html_unescape(content_log["msg_title"])
    content_log["msg_content"] = msg_get_data_service.html_unescape(content_log["msg_content"])
    return content_log


def get_mobile_subscribe_send_detail_log(query_model):
    """
    :获取简讯发送用户渠道消息状态明细
    :return :
    """
    return dashboard_feeds_repository.get_mobile_subscribe_send_detail_log(query_model)


def get_dashboard_email_subscribe_by_id(feed_id):
    """
    获取邮件订阅记录
    :param str feed_id:
    :return:
    """
    return dashboard_feeds_repository.get_dashboard_feeds(feed_id)


def get_mobile_subscribe_filter_list(feed_id):
    """
    获取简讯的过滤条件列表
    :param str feed_id:
    :return:
    """
    return dashboard_feeds_repository.get_mobile_subscribe_filter_list(feed_id)


def get_mobile_subscribe_send_rules(feed_id):
    """
    获取简讯的发送条件列表
    :param str feed_id:
    :return:
    """
    rules = dashboard_feeds_repository.get_mobile_subscribe_send_rules(feed_id) or []
    for rule in rules:
        rule['rule_info'] = json.loads(rule['rule_info'])
    return rules


def get_mobile_subscribe_roles(feed_id):
    """
    获取简讯的发送对象角色
    :param str feed_id:
    :return:
    """
    return dashboard_feeds_repository.get_mobile_subscribe_roles(feed_id, ['email_subscribe_id', 'role_id']) or []


def get_mobile_subscribe_chapters(feed_id):
    """
    获取简讯的明细段落列表
    :param str feed_id:
    :return:
    """
    return dashboard_feeds_repository.get_mobile_subscribe_chapters(feed_id,
                                                                    ['id', 'email_subscribe_id', 'dataset_id',
                                                                     'chapter_message']
                                                                    ) or []


def get_mobile_subscribe_role_ids(feed_id):
    """
    获取简讯的角色id列表
    :param feed_id:
    :return:
    """
    return dashboard_feeds_repository.get_mobile_subscribe_role_ids(feed_id) or []


def _get_dataset_id_by_message(editor_mode, message):
    try:
        # 从HTML混编里面获取数据集id
        if editor_mode == SubscribeSendEditorMode.HTML.value:
            dataset_ids = re.findall(r'data-datasetid=\"(.*?)\"', message)
        else:
            # 从json中获取数据集id
            dataset_ids = msg_get_data_service.get_subscribe_dataset_id(message)
    except Exception as e:
        dataset_ids = []
        logger.exception(e)
    return dataset_ids


def _get_dataset_id_by_chapter(chapter_model_list):
    """
    获取明细段落中的数据集id
    :param chapter_model_list:
    :return:
    """
    if not chapter_model_list:
        return []
    dataset_ids = [model.dataset_id for model in chapter_model_list]
    return list(set(dataset_ids))


def export_msg_statistics(response):
    """
    导出简讯已使用数据集的统计数据
    :param response:
    :return:
    """
    title = {'env_code': '环境', 'project_code': '租户', 'subject_email': '简讯名称', 'send_frequency_str': '发送频率',
             'status': '是否启用', 'times': '简讯内容关联数据集个数', 'created_on': '统计时间'}
    fields = list(title.keys())
    data = repository.get_list('msg_used_dataset_statistics', {}, fields, from_config_db=True)
    env_code = os.environ.get("CONFIG_AGENT_CLIENT_CODE", "")
    if data:
        Excel.export_csv(f'{env_code}_简讯已使用数据集的统计数据.csv', data, title, response=response)
    else:
        raise UserError(message=f'{env_code}_暂无统计数据')


def get_email_message_by_user(feed_id, account):
    """
    获取指定邮件，指定用户的简讯正文内容
    :param feed_id:
    :param account:
    :return:
    """
    user = user_repository.get_user_info(account)
    if not user:
        raise UserError(message="用户不存在")
    user_data = {
        "id": user.get("id"), "name": user.get("name"), "account": account
    }
    g.account = account
    g.userid = user.get("id")
    feed_data = get_dashboard_feeds(feed_id)
    if not feed_data:
        raise UserError(message="邮件订阅不存在")
    feed_model = generate_dashboard_feed_model(feed_data)
    content_info = msg_get_data_service.get_message_data(feed_model, user_data)
    if 'btntxt' in content_info:
        del content_info['btntxt']
    if 'msg_subscribe_config' in content_info:
        del content_info['msg_subscribe_config']
    return content_info


def get_subscribe_role_list(role_id_list):
    """
    获取指定角色id列表的角色信息
    :param list role_id_list:
    :return:
    """
    if not role_id_list or not isinstance(role_id_list, list):
        raise UserError(message='角色id不能为空')
    subscribe_role_list = [{"role_id": r} for r in role_id_list]

    role_info = {}
    exists_role_dict = {}
    role_list = role_repository.get_user_role_list_by_ids(role_id_list)

    if role_list:
        exists_role_dict = {item.get("id"): item.get("name") for item in role_list}
        exists_role_id_list = list(exists_role_dict.keys())
        # 获取角色用户
        role_user_list = role_repository.get_all_user_by_role_ids(exists_role_id_list)
        # 获取角色组织
        role_group_list = role_repository.get_all_group_by_role_ids(exists_role_id_list)
        for item in role_user_list:
            if item.get("role_id") not in role_info:
                role_info[item.get("role_id")] = []
            role_info[item.get("role_id")].append({"id": item.get("id"), "name": item.get("name"), "type": "user"})

        for item in role_group_list:
            if item.get("role_id") not in role_info:
                role_info[item.get("role_id")] = []
            role_info[item.get("role_id")].append({"id": item.get("id"), "name": item.get("name"), "type": "group"})

    for role in subscribe_role_list:
        role_id = role.get("role_id")
        role['name'] = exists_role_dict.get(role_id) if role_id in exists_role_dict else '异常角色'
        role['list'] = role_info.get(role_id) if role_id in role_info else []

    return subscribe_role_list


def get_subscribe_role_user_id_list(role_id_list):
    """
    获取角色下的所有user_id
    :param str role_id_list:
    :return:
    """
    user_id_list = []
    role_list = role_repository.get_user_role_list_by_ids(role_id_list)
    if not role_list:
        return user_id_list

    exists_role_id_list = [item.get("id") for item in role_list]
    # 获取角色下的用户
    user_id_list = role_repository.get_user_id_by_role_ids(exists_role_id_list)

    # 获取角色下的组织
    group_id_list = role_repository.get_group_id_by_role_ids(exists_role_id_list)
    # 通过组织id获取用户
    if group_id_list:
        group_user_id_list = role_repository.get_user_id_by_group_ids(group_id_list)
        if group_user_id_list:
            user_id_list.extend(group_user_id_list)
    # 去重
    if user_id_list:
        user_id_list = list(set(user_id_list))
    return user_id_list


def get_user_by_ids(user_id_list):
    if not user_id_list:
        return []
    return user_repository.get_accounts_by_user_ids(user_id_list)


def get_subscribe_role_user_list(role_id_list):
    """
    通过角色id获取角色下所有用户
    :param role_id_list:
    :return:
    """
    user_list = []
    if not role_id_list:
        return user_list
    user_id_list = get_subscribe_role_user_id_list(role_id_list)
    if user_id_list:
        user_list = get_user_by_ids(user_id_list)
    return user_list


def get_cycle_day_next_time(is_mobile, model: DashboardFeedsModel):
    """
    获取简讯周期天的最近一次待执行的crontab表达式
    :param is_mobile:
    :param model:
    :return:
    """
    cron_exp = None
    # 非个人简讯不用处理
    if not is_mobile:
        return cron_exp
    cycle_times = model.cycle_times
    # 如果是json字符串，则转换
    if isinstance(cycle_times, str):
        cycle_times = json.loads(cycle_times)
    if not cycle_times:
        return cron_exp

    send_frequency = (
        int(model.send_frequency)
        if model.send_frequency and isinstance(model.send_frequency, str)
        else model.send_frequency
    )
    # 获取周期天的最近来到的一次时间
    if send_frequency == EmailSendType.Cycle.value and model.cycle_type == SubscribeCycleType.Day.value:
        next_datetime, cron_exp = get_next_cycle_time(cycle_times)
        if not next_datetime:
            raise UserError(message="获取下次执行时间错误，简讯周期获取失败")
        logger.error(f"简讯(name:{model.subject_email} ID:{model.id} )下次执行时间: {next_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
    return cron_exp


def get_next_cycle_time(cycle_crontab_list, now_time=None):
    """
    获取周期的下次执行时间
    :param cycle_crontab_list:
    :param now_time:
    :return datetime 日期类型时间, cron_exp crontab表达式:
    """
    if not now_time:
        now_time = datetime.now()
    cycle_datetime_map = {}
    cycle_datetime_list = []
    next_datetime, cron_exp = None, None
    if not cycle_crontab_list:
        return next_datetime, cron_exp

    for cron_exp_item in cycle_crontab_list:
        cron_nums = cron_exp_item.split(' ')
        if len(cron_nums) != 7:
            break
        minute = int(cron_nums[1])
        hour = int(cron_nums[2])
        datetime_obj = datetime(now_time.year, now_time.month, now_time.day, hour, minute, 0)
        cycle_datetime_list.append(datetime_obj)
        cycle_datetime_map[datetime_obj.strftime("%Y-%m-%d %H:%M:%S")] = cron_exp_item

    if not cycle_datetime_list:
        return next_datetime, cron_exp

    # 时间列表按正序排序
    cycle_datetime_list.sort()
    for tmp_t in cycle_datetime_list:
        if tmp_t >= now_time:
            next_datetime = tmp_t
            # 格式化
            next_datetime_str = next_datetime.strftime("%Y-%m-%d %H:%M:%S")
            cron_exp = cycle_datetime_map[next_datetime_str]
            break
    else:
        # 循环没有中断则进入此逻辑。从第二天的时间开始计算
        today_zero = datetime(now_time.year, now_time.month, now_time.day, 0, 0, 0)
        second_day = today_zero + timedelta(days=1)
        next_datetime, cron_exp = get_next_cycle_time(cycle_crontab_list, second_day)
    return next_datetime, cron_exp


def get_mobile_subscribe_cloud_app(app_code):
    """
    获取简讯关联的产品信息
    :param str app_code:
    :return:
    """
    return dashboard_feeds_repository.get_feed_app(app_code) or {}


def get_erp_user_by_account(account_list):
    """
    获取数见用户在erp中的账号，用户id，名称信息
    :param str account_list:
    :return:
    """
    return dashboard_feeds_repository.get_erp_user_by_account(account_list) or []


def send_time_check(sync_dataset_ids, schedule):
    dataset_list = dashboard_feeds_repository.query_sync_dataset(sync_dataset_ids)
    check_result = []
    if not dataset_list:
        return check_result
    for dataset in dataset_list:
        dataset_id = dataset.get("id")
        dataset_name = dataset.get("name")
        dataset_schedule = dataset.get("schedule")
        if _not_skip_schedule(dataset_schedule, schedule):
            rs = check_schedule_time(dataset_schedule, schedule)
            rs["dataset_id"] = dataset_id
            rs["dataset_name"] = dataset_name
            check_result.append(rs)
        else:
            logger.error(f"数据集 {dataset_id} {dataset_name} 不比对")
    return check_result


def _not_skip_schedule(dataset_schedule, feed_schedule):
    """
    判断数据集周期是否为 小时，分钟。如果是则跳过不进行时间比对
    :param dataset_schedule:
    :param feed_schedule:
    :return:
    """
    schedule_rs = parse_schedule(dataset_schedule)
    dataset_schedule_type = schedule_rs.get("type")
    if dataset_schedule_type in ['hour', 'minute']:
        logger.error("数据集周期为小时或分钟")
        return False
    feed_schedule_rs = parse_schedule(feed_schedule)
    feed_schedule_type = feed_schedule_rs.get("type")
    # 如果简讯与数据集周期相同，且周期是周或月，则跳过对比
    skip_type = ['month', 'week']
    if dataset_schedule_type in skip_type and feed_schedule_type in skip_type:
        logger.error("数据集周期和简讯周期为月或周")
        return False
    return True


def check_schedule_time(dataset_schedule, feed_schedule):
    """
    数据集清洗时间与简讯发送时间比对
    比对思路：
    简讯，数据集周期是 月、周的情况暂不考虑，全部转换为天进行对比。比如简讯按天发送，与数据集天，周，月对比。
    :param dataset_schedule:
    :param feed_schedule:
    :return:
    """
    feed_send_time = _schedule_convert_today_time(feed_schedule)
    if feed_send_time is None:
        raise UserError("简讯调度时间匹配错误")
    # 简讯发送前15分钟作为检查时间点
    send_time_before_minute = 15
    feed_check_time = feed_send_time + timedelta(minutes=-send_time_before_minute)
    # 修正 0 点的特别情况，需要与前一天数据集清洗时间对比
    before_day_time = feed_check_time
    if feed_check_time.hour == 0 and feed_check_time.minute == 0 and feed_check_time.second == 0:
        before_day_time = feed_check_time + timedelta(days=-1)

    dataset_check_time = _schedule_convert_today_time(dataset_schedule, before_day_time.year,
                                                      before_day_time.month, before_day_time.day)
    if dataset_check_time is None:
        raise UserError("数据集调度时间匹配错误")

    delta = feed_check_time - dataset_check_time
    seconds = delta.total_seconds()

    tips = ''
    if dataset_check_time > feed_send_time:
        # 数据集调度时间在简讯发送时间之后，如果按天周期计算，简讯发送时间距离上次数据集清洗时间则需加上1天
        seconds = abs(seconds)
        seconds += 86400
        tips = '数据集调度时间在简讯发送时间之后，校验通过'
    logger.error(f"dataset_schedule: {dataset_schedule} feed_schedule: {feed_schedule} feed_check_time: {feed_check_time} "
                 f"dataset_check_time: {dataset_check_time} total_seconds: {seconds} {tips}")
    if seconds >= 0:
        status = True
        minute = math.ceil(seconds/60)
    else:
        status = False
        minute = math.ceil(seconds/60)

    # 数据集清洗距离简讯发送时间的差
    minute += send_time_before_minute
    data = {
        "status": status,
        "minute": minute,
        "dataset_time": f"{dataset_check_time.hour}:{dataset_check_time.minute}"
    }
    return data


def _schedule_convert_today_time(schedule, year=None, month=None, day=None):
    """
    将调度 schedule 转换为今天的具体日期时间
    :param schedule:
    :param year:
    :param month:
    :param day:
    :return:
    """
    hour, minute = _get_schedule_hour_min(schedule)
    if hour < 0 or minute < 0:
        return None
    year = year if year else datetime.now().year
    month = month if month else datetime.now().month
    day = day if day else datetime.now().day
    return datetime(year, month, day, hour, minute, 0)


def _get_schedule_hour_min(schedule):
    if not schedule:
        return None, None
    parts = schedule.split()
    minute = hour = 0
    try:
        minute = int(parts[1])
        hour = int(parts[2])
    except:
        pass
    return hour, minute


def parse_schedule(cron):
    """
    解析cron表达式，得到周期
    :param cron:
    :return:
    """
    try:
        data = {}
        _type = ''

        datas = cron.split(' ')
        data['minute'] = datas[1] or 0

        if datas[5] == '?':
            if datas[0].startswith('0/'):
                _type = 'second'
                data['second'] = datas[0].split('/')[1]
            elif datas[1].startswith('0/'):
                _type = 'minute'
                data['minute'] = datas[1].split('/')[1]
            elif datas[6] != '*' and isinstance(int(datas[6]), int) and isinstance(int(datas[4]), int) \
                    and isinstance(int(datas[3]), int):
                _type = 'assign_time'
                data['assign_time'] = cron
            else:
                _type = 'month'
                data['hour'] = datas[2]
                data['day'] = datas[3].split(',')
        elif datas[5] != '*':
            _type = 'week'
            data['hour'] = datas[2]
            data['week'] = datas[5].split(',')
        elif datas[5] == '*' and datas[3] == '?':
            if '/' in datas[2] and '-' in datas[2]:
                _type = 'hour'
                a_index = datas[2].index('/')
                b_index = datas[2].index('-')

                data['start_minute'] = datas[1]
                data['start_hour'] = datas[2][:b_index]
                data['end_hour'] = datas[2][b_index + 1:a_index]
                data['step_hour'] = datas[2][a_index + 1:]
            else:
                _type = 'day'
                data['hour'] = datas[2]
        else:
            data = None

        return {
            "data": data,
            "type": _type
        }
    except:
        return {
            "data": '',
            "type": ''
        }


def add_subscribe_used_schedule_dataset(project_code, dataset_ids):
    """
    简讯使用的调度数据集存入redis
    :param project_code:
    :param dataset_ids:
    :return:
    """
    if not dataset_ids:
        return 0
    dataset_ids = list(set(dataset_ids))
    schedule_dataset_ids = dashboard_feeds_repository.get_subscribe_schedule_dataset(project_code, dataset_ids) or []
    if schedule_dataset_ids:
        subscribe_dataset_save_to_redis(project_code, schedule_dataset_ids)
    return len(schedule_dataset_ids)


def get_subscribe_redis():
    return redis_conn_prefix('subscribe:')


def delete_subscribe_dataset_redis():
    key = SUBSCRIBE_MSG_USED_DATASET_ID_SET_KEY
    redis_conn = get_subscribe_redis()
    redis_conn.delete(key)


def subscribe_dataset_save_to_redis(project_code, dataset_ids):
    if not dataset_ids:
        return
    set_ids = {project_code+'_'+dataset_id for dataset_id in dataset_ids}
    key = SUBSCRIBE_MSG_USED_DATASET_ID_SET_KEY
    redis_conn = get_subscribe_redis()
    redis_conn.sadd(key, *set_ids)
    redis_conn.expire(key, 24*3600)


def change_dataset_connect_type_to_subscribe(dataset_id):
    """
    数据集由直连切换为调度，如果有被简讯引用，则要记录
    :param dataset_id:
    :return:
    """
    subscribe_list = dashboard_feeds_repository.get_subscribe_by_dataset_id(dataset_id)
    if subscribe_list:
        subscribe_dataset_save_to_redis(g.code, [dataset_id])


def clear_title_html(subject_email):
    return msg_get_data_service.clear_title_html(subject_email)
