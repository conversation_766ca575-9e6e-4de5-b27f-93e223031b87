#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import datetime
from feed.services.msg_get_data_parse_service import MsgDataParse
from feed.services.msg_get_data_parse_service import MsgDataHtmlParse, MsgDataJsonParse
from feed.models import MsgSubscribeConfigModel


def get_message_data(feed_model, recipient):
    """
    获取个人简讯，邮件简讯内容的解析入口方法
    :param feed.models.DashboardFeedsModel feed_model :
    :param recipient: 用户信息
    {"account":"cc", "name":"cc","id":"39e1159c-ac5f-4d12-02a2-569ebc353dea", "email":"<EMAIL>"}
    :return:
    """
    # 发送时间为空，则取默认时间
    if not feed_model.send_time:
        feed_model.send_time = str(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

    msg_subscribe_config_model = feed_model.msg_subscribe_config \
        if feed_model.msg_subscribe_config else MsgSubscribeConfigModel()
    feed_model.msg_subscribe_config = msg_subscribe_config_model

    msg_parse_obj = MsgDataParse.get_instance(feed_model, recipient)
    return msg_parse_obj.parse_message()


def clear_title_html(subject_email):
    return MsgDataHtmlParse.clear_title_html(subject_email)


def html_unescape(msg):
    return MsgDataHtmlParse.html_unescape(msg)


def get_subscribe_dataset_id(message):
    return MsgDataJsonParse.get_subscribe_dataset_id(message)

