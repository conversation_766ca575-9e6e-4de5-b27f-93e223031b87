#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    API Route
    <NAME_EMAIL> on 2018/3/2.
"""
import json
from urllib.parse import urlparse
from components.global_utils import compare_dict
from dmplib.utils.errors import UserError

from feed.services import dashboard_feeds_service
from feed.models import (
    DashboardFeedsModel,
    FeedsQueryModel,
    SubscribeConfigModel,
    SubscribeConfigQueryModel,
    MobileSubscribeSendDetailLogQueryModel
)
from dmplib import config
from dmplib.hug import APIWrapper
from dmplib.hug import g
from feed.services.dashboard_feeds_service import get_message_limit_remind
from feed.services.saas_app_user_service import SaasAppUserService
from flow.models import FlowInstanceQueryModel
from rbac.validator import PermissionValidator
from user_log.models import UserLogModel
from integrate.services import third_party_service
from integrate.models import ThirdPartyAppQueryModel


api = APIWrapper(__name__)


@api.admin_route.get('/get', validate=PermissionValidator('feeds.view'))
def get_email_feeds(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/get 获取邮件订阅
    @apiGroup  dashboard_feeds
    @apiParam query {string}  id 邮件订阅id
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              {
                "account": "admin",
                "modified_on": "2017-09-01T18:40:47",
                "modified_by": "admin",
                "email": "",
                "id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                "created_by": "",
                "mobile": "",
                "name": "超级管理员",
                "created_on": "1900-01-01T00:00:00"
              }
    }
    */
   """
    return True, '', dashboard_feeds_service.get_dashboard_feeds(kwargs.get('id'))


@api.admin_route.get('/mobile/get', validate=PermissionValidator('feeds.view'))
def get_mobile_feeds(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/get 获取邮件订阅
    @apiGroup  dashboard_feeds
    @apiParam query {string}  id 邮件订阅id
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              {
                "account": "admin",
                "modified_on": "2017-09-01T18:40:47",
                "modified_by": "admin",
                "email": "",
                "id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                "created_by": "",
                "mobile": "",
                "name": "超级管理员",
                "created_on": "1900-01-01T00:00:00"
              }
    }
    */
   """
    return True, '', dashboard_feeds_service.get_dashboard_feeds(kwargs.get('id'))


@api.admin_route.get('/list', validate=PermissionValidator('feeds.view'))
def get_email_feeds_list(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/list 获取订阅列表(移动订阅和简讯订阅)
    @apiGroup  dashboard_feeds
    @apiResponse  200 {
            "result": true,
            "msg": "ok",
            "data": {
            "result": true,
            "msg": "ok",
            "data": {
                 "items": [
                  {
                    "account": "admin",
                    "modified_on": "2017-09-01T18:40:47",
                    "modified_by": "admin",
                    "email": "",
                    "id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                    "created_by": "",
                    "mobile": "",
                    "name": "超级管理员",
                    "created_on": "1900-01-01T00:00:00"
                  }
                ],
                "total": 1
            }

        }
    }
    */
   """
    return True, None, dashboard_feeds_service.get_dashboard_feeds_list(FeedsQueryModel(**kwargs)).get_result_dict()


@api.admin_route.post('/enable', validate=PermissionValidator('feeds.edit'))
def enable_flow(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {post} /api/dashboard_feeds/enable 用邮件订阅
    @apiGroup  dashboard_feeds
     @apiParam query {string}  flow_id 邮件订阅id
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
     }
    */
   """

    return True, '启用成功', dashboard_feeds_service.enable_flow(kwargs.get('flow_id'))


@api.admin_route.post('/disable', validate=PermissionValidator('feeds.edit'))
def disable_flow(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {post} /api/dashboard_feeds/disable 禁用邮件订阅
    @apiGroup  dashboard_feeds
     @apiParam query {string}  flow_id 邮件订阅id
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
     }
    */
   """
    return True, '禁用成功', dashboard_feeds_service.disable_flow(kwargs.get('flow_id'))


@api.admin_route.get('/details', validate=PermissionValidator('feeds.view'))
def get_email_feeds_details(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/details 获取邮件订阅详情
     @apiGroup  dashboard_feeds
     @apiParam query {string} [keyword] 关键字
     @apiParam query {string} [begin_date] 开始时间
     @apiParam query {string} [end_date] 结束时间
     @apiParam query {string} [type] 流程实例类型（订阅）
     @apiParam query {string} [status] 流程实例状态
     @apiParam query {number} page page
     @apiParam query {number} page_size size
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "items": [
              {
                "account": "admin",
                "modified_on": "2017-09-01T18:40:47",
                "modified_by": "admin",
                "email": "",
                "id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                "created_by": "",
                "mobile": "",
                "name": "超级管理员",
                "created_on": "1900-01-01T00:00:00"
              }
            ],
            "total": 1
          }
        }
     */
    """
    return (
        True,
        'ok',
        dashboard_feeds_service.get_dashboard_feeds_details(FlowInstanceQueryModel(**kwargs)).get_result_dict(),
    )


@api.admin_route.get('/retry', validate=PermissionValidator('feeds.view'))
def email_feeds_retry(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/retry 取邮件重发
     @apiGroup  dashboard_feeds
     @apiParam query {string} [id] 流程实例id
     @apiParam query {string} [flow_id] 流程id
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {}
        }
     */
    """
    dashboard_feeds_service.retry(kwargs.get('id'), kwargs.get('flow_id'))
    return True, '操作成功'


@api.admin_route.get('/mobile/retry', validate=PermissionValidator('feeds.view'))
def mobile_feeds_retry(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/retry 取邮件重发
     @apiGroup  dashboard_feeds
     @apiParam query {string} [id] 流程实例id
     @apiParam query {string} [flow_id] 流程id
     @apiParam query {int} [retry] 重发（默认0：不重发，1：重发全部收件人，2：重发失败收件人）
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {}
        }
     */
    """
    dashboard_feeds_service.mobile_retry(kwargs.get('id'), kwargs.get('flow_id'), kwargs.get("retry", 0))
    return True, '操作成功'


@api.admin_route.get('/get_dmp_email', validate=PermissionValidator('feeds.edit'))
def get_dmp_email():
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/get_dmp_email 获取DMP系统邮件对象
     @apiGroup  dashboard_feeds
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "name": "明源云大数据平台",
            "mail": "<EMAIL>"
          }
        }
     */
    """
    data = {'name': config.get('Email.name'), 'mail': config.get('Email.account')}
    return True, 'ok', data


@api.admin_route.post('/add', validate=PermissionValidator('feeds.edit'))
def add_email_feeds(request, **kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {post} /api/dashboard_feeds/add 添加邮件订阅
    @apiGroup  dashboard_feeds
    @apiBodyParam {
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "subject_email{邮件主题}": "cc_test11",
        "send_frequency{发送频率}": 1,
        "recipients{收件人用户id}": [{"name": "chenc04", "email": "253058094qq.com"}],
        "addresser{发件人}": "dmp",
        "message{消息正文}": "test",
        "report_from{报告形式}": [2],
        "flow{流程对象}": {"schedule": "0 0 0 ? * * *", "depend_flow_id": "", "status": "启用"}
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
    */
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    model = dashboard_feeds_service.generate_dashboard_feed_model(kwargs)
    dashboard_feed = dashboard_feeds_service.add_dashboard_feeds(model, terminal_type=kwargs.get("terminal_type"))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'add_feed',
            'id': kwargs.get('id'),
            'content': '创建订阅 [ {name} ] '.format(name=dashboard_feed.get("subject_email")),
        },
    )
    return True, '添加成功', dashboard_feed.get("id")


@api.admin_route.post('/mobile/add', validate=PermissionValidator('feeds.edit'))
def add_email_feeds(request, **kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {post} /api/dashboard_feeds/add 添加简讯订阅
    @apiGroup  dashboard_feeds
    @apiBodyParam {
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "subject_email{简讯主题}": "cc_test11",
        "send_frequency{发送频率}": 1,
        "recipients{收件人用户id}": [{"name": "chenc04", "email": "253058094qq.com", "account": "chenc04"}],
        "message{简讯内容}": "test",
        "report_url{链接报告}": "http://xxx.com",
        "config_id{订阅配置id}": "39e41b58",
        "terminal_type{终端类型}": "mobile_screen",
        "flow{流程对象}": {"schedule": "0 0 0 ? * * *", "depend_flow_id": "", "status": "启用"},
        "display_format{字段显示格式}": [
			{
				"dataset_id": "数据集ID",
				"field_id": "字段ID",
				"display_format": "展示格式",
				"digit": "展示位小数位",
				"use_thousands": "是否使用千分位分隔",
				"value_unit": "数值单位"
			}
	    ]
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
    */
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    model = dashboard_feeds_service.generate_dashboard_feed_model(kwargs)
    dashboard_feed = dashboard_feeds_service.add_dashboard_feeds(
        model, mobile=True, terminal_type=kwargs.get("terminal_type")
    )
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'add_feed',
            'id': kwargs.get('id'),
            'content': '创建订阅 [ {name} ] '.format(name=dashboard_feed.get("subject_email")),
        },
    )
    return True, '添加成功', dashboard_feed.get("id")


@api.admin_route.post('/run', validate=PermissionValidator('feeds.edit'))
def run_email_feed(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dashboard_feeds/run 立即发送邮件订阅
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "id{ID}": "39e41b58-acd8-ab00-de05-37e39b170604"
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709rundd2012"
        }
     */
    """
    flow_id = kwargs.get("id")
    if not flow_id:
        raise UserError(message='id必填')
    return True, '成功触发任务运行', dashboard_feeds_service.run_feed(flow_id)


@api.admin_route.post('/mobile/run', validate=PermissionValidator('feeds.edit'))
def run_mobile_feed(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dashboard_feeds/mobile/run 立即发送简讯订阅
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "id{ID}": "39e41b58-acd8-ab00-de05-37e39b170604"
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
     */
    """
    flow_id = kwargs.get("id")
    if not flow_id:
        raise UserError(message='id必填')
    return True, '成功触发任务运行', dashboard_feeds_service.run_feed(flow_id, mobile=True)


@api.admin_route.post('/update', validate=PermissionValidator('feeds.edit'))
def update_email_feeds(request, **kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {post} /api/dashboard_feeds/update 修改邮件订阅
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "id{ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "subject_email{邮件主题}": "cc_test11",
        "send_frequency{发送频率}": 1,
        "recipients{收件人(用户id)}": [{"name": "chenc04", "email": "253058094qq.com"}],
        "addresser{发件人}": "dmp",
        "message{消息正文}": "test",
        "report_from{报告形式}": [2],
        "flow{流程对象}": {"schedule": "0 0 0 ? * * *", "depend_flow_id": "", "status": "启用"},
        "display_format{字段显示格式}": [
			{
				"dataset_id": "数据集ID",
				"field_id": "字段ID",
				"display_format": "展示格式",
				"digit": "展示位小数位",
				"use_thousands": "是否使用千分位分隔",
				"value_unit": "数值单位"
			}
		]
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
     */
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    model = dashboard_feeds_service.generate_dashboard_feed_model(kwargs)
    old_dashboard_feed = dashboard_feeds_service.get_dashboard_feeds(model.id)
    new_dashboard_feed = dashboard_feeds_service.update_dashboard_feeds(model,
                                                                        terminal_type=kwargs.get("terminal_type"))
    # 收件人有变化，取名称
    if old_dashboard_feed.get("recipients") != new_dashboard_feed.get("recipients"):
        old_dashboard_feed["recipients_name"] = '、'.join(
            [_.get("name") for _ in json.loads(old_dashboard_feed.get("recipients"))]
        )
        new_dashboard_feed["recipients_name"] = '、'.join(
            [_.get("name") for _ in json.loads(new_dashboard_feed.get("recipients"))]
        )

    # 发送频率修改
    if old_dashboard_feed.get("send_frequency") != new_dashboard_feed.get("send_frequency"):
        send_frequency_dict = {1: '立即发送', 2: '定时发送', 3: '周期发送'}
        old_dashboard_feed['send_frequency_detail'] = send_frequency_dict.get(
            int(old_dashboard_feed.get("send_frequency"))
        )
        new_dashboard_feed['send_frequency_detail'] = send_frequency_dict.get(
            int(new_dashboard_feed.get("send_frequency"))
        )
    # 如果是更改周期发送，显示周期发送详情
    if int(new_dashboard_feed.get("send_frequency")) != 1:
        try:
            new_dashboard_feed['schedule'] = getattr(new_dashboard_feed.get('flow'), "schedule", "")
            old_dashboard_feed['schedule'] = getattr(old_dashboard_feed.get('flow'), "schedule", "")
        except:
            new_dashboard_feed['schedule'] = ''
            old_dashboard_feed['schedule'] = ''

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_feed',
            'id': kwargs.get('id'),
            'content': '订阅【{name}】修改.{content}'.format(
                name=old_dashboard_feed.get("subject_email"),
                content=compare_dict(
                    old_dashboard_feed,
                    new_dashboard_feed,
                    field_dict={
                        "subject_email": "订阅名称",
                        "recipients_name": "收件人",
                        "message": "消息正文",
                        "config_name": "发送应用名称",
                        "send_frequency_detail": "发送频率",
                        "schedule": "调度执行",
                    },
                ),
            ),
        },
    )
    return True, '修改成功', model.id


@api.admin_route.post('/mobile/update', validate=PermissionValidator('feeds.edit'))
def update_mobile_feeds(request, **kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {post} /api/dashboard_feeds/update 修改简讯订阅
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "id{ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "subject_email{邮件主题}": "cc_test11",
        "send_frequency{发送频率}": 1,
        "recipients{收件人(用户id)}": [{"name": "chenc04", "email": "253058094qq.com"}],
        "addresser{发件人}": "dmp",
        "message{消息正文}": "test",
        "terminal_type{终端类型}": "mobile_screen",
        "config_id{订阅配置id}": "39e41b58",
        "flow{流程对象}": {"schedule": "0 0 0 ? * * *", "depend_flow_id": "", "status": "启用"},
        "display_format{字段显示格式}": [
			{
				"dataset_id": "数据集ID",
				"field_id": "字段ID",
				"display_format": "展示格式",
				"digit": "展示位小数位",
				"use_thousands": "是否使用千分位分隔",
				"value_unit": "数值单位",
				"show_value_unit": "是否显示数值单位"
			}
		]
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
     */
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    model = dashboard_feeds_service.generate_dashboard_feed_model(kwargs)
    old_dashboard_feed = dashboard_feeds_service.get_dashboard_feeds(model.id)
    new_dashboard_feed = dashboard_feeds_service.update_dashboard_feeds(
        model, mobile=True, terminal_type=kwargs.get("terminal_type")
    )
    # config_id有变化,需要查询对应名称
    if old_dashboard_feed.get("config_id") != new_dashboard_feed.get("config_id"):
        # 获取简讯应用名称
        result = third_party_service.get_third_app_name(
            [old_dashboard_feed.get("config_id"), new_dashboard_feed.get("config_id")]
        )
        old_dashboard_feed["config_name"] = result.get(old_dashboard_feed.get("config_id"))
        new_dashboard_feed["config_name"] = result.get(new_dashboard_feed.get("config_id"))
    # 收件人有变化，取名称
    if old_dashboard_feed.get("recipients") != new_dashboard_feed.get("recipients"):
        old_dashboard_feed["recipients_name"] = '、'.join(
            [_.get("name") for _ in json.loads(old_dashboard_feed.get("recipients"))]
        )
        new_dashboard_feed["recipients_name"] = '、'.join(
            [_.get("name") for _ in json.loads(new_dashboard_feed.get("recipients"))]
        )
    # 发送频率修改
    if old_dashboard_feed.get("send_frequency") != new_dashboard_feed.get("send_frequency"):
        send_frequency_dict = {1: '立即发送', 2: '定时发送', 3: '周期发送'}
        old_dashboard_feed['send_frequency_detail'] = send_frequency_dict.get(
            int(old_dashboard_feed.get("send_frequency"))
        )
        new_dashboard_feed['send_frequency_detail'] = send_frequency_dict.get(
            int(new_dashboard_feed.get("send_frequency"))
        )
    # 如果是更改周期发送，显示周期发送详情
    if int(new_dashboard_feed.get("send_frequency")) != 1:
        try:
            new_dashboard_feed['schedule'] = getattr(new_dashboard_feed.get('flow'), "schedule", "")
            old_dashboard_feed['schedule'] = getattr(old_dashboard_feed.get('flow'), "schedule", "")
        except:
            new_dashboard_feed['schedule'] = ''
            old_dashboard_feed['schedule'] = ''

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_feed',
            'id': kwargs.get('id'),
            'content': '订阅【{name}】修改.{content}'.format(
                name=old_dashboard_feed.get("subject_email"),
                content=compare_dict(
                    old_dashboard_feed,
                    new_dashboard_feed,
                    field_dict={
                        "subject_email": "订阅名称",
                        "recipients_name": "收件人",
                        "message": "消息正文",
                        "config_name": "发送应用名称",
                        "send_frequency_detail": "发送频率",
                        "schedule": "调度执行",
                    },
                ),
            ),
        },
    )
    return True, '修改成功', model.id


@api.admin_route.post('/mobile/snapshot/update', validate=PermissionValidator('feeds.edit'))
def update_message_snapshot_status(request, **kwargs):
    """"
    /*
     @apiVersion 1.1.0
     @api {get} /api/dashboard_feeds/snapshot/update 设置邮件订阅是否开启拍照
     @apiGroup  dashboard_feeds
      @apiParam query {string}  id 邮件订阅id
      @apiResponse  200 {
          "result": true,
          "msg": "ok"
      }
     */
    """
    is_snap = kwargs.get("is_snap", "0")
    feed_id = kwargs.get("feed_id")
    if not feed_id:
        raise UserError(message="feed_id是必传参数")
    if isinstance(is_snap, str) and not is_snap.isdigit():
        raise UserError(message="is_snap参数错误, 只支持0 、 1")
    is_snap = int(is_snap)
    if is_snap not in [0, 1]:
        raise UserError(message="is_snap参数错误, 只支持0 、 1")
    dashboard_feeds_service.update_message_snapshot_status(feed_id, is_snap)
    return True, '更新成功'


@api.admin_route.post('/delete', validate=PermissionValidator('feeds.edit'))
def delete_email_feeds(request, **kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/delete 删除邮件订阅
     @apiGroup  dashboard_feeds
      @apiParam query {string}  id 邮件订阅id
      @apiResponse  200 {
          "result": true,
          "msg": "ok"
      }
     */
    """
    result = dashboard_feeds_service.delete_dashboard_feeds(kwargs.get('id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_feed',
            'id': kwargs.get('id'),
            'content': '删除订阅 [ {name} ] 成功'.format(name=result.get("subject_email")),
        },
    )
    return True, '删除成功', result


@api.admin_route.post('/mobile/delete', validate=PermissionValidator('feeds.edit'))
def delete_email_feeds(request, **kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/delete 删除简讯订阅
     @apiGroup  dashboard_feeds
      @apiParam query {string}  id 邮件订阅id
      @apiResponse  200 {
          "result": true,
          "msg": "ok"
      }
     */
    """
    result = dashboard_feeds_service.delete_dashboard_feeds(kwargs.get('id'), mobile=True)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_feed',
            'id': kwargs.get('id'),
            'content': '删除订阅 [ {name} ] 成功'.format(name=result.get("subject_email")),
        },
    )
    return True, '删除成功'


@api.admin_route.post('/check_user_permission', validate=PermissionValidator('feeds.edit'))
def mobile_report_check_user_permission(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/check_user_permission 检测用户接收移动报告权限
     @apiGroup  dashboard_feeds
      @apiBodyParam query {
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "user_ids{用户ids}": ["ID1": "ID2"]
      }
      @apiResponse  200 {
          "result": {
            "permission": ["ID1"],
            "no_permission": ["ID2"]
          },
          "msg": "ok"
      }
     */
    """
    g.account = "check"
    # 简讯发送消息到内置应用时，不检查用户的数据权限
    mobile_subscribe_id = kwargs.get("app_id")
    if mobile_subscribe_id and SaasAppUserService.check_app_is_api(mobile_subscribe_id):
        return True, '', SaasAppUserService.check_user_permission(kwargs.get("biz_ids"))
    else:
        return (
            True,
            '获取成功',
            dashboard_feeds_service.check_user_permission(kwargs.get('dashboard_id'),
                                                          kwargs.get("biz_ids"),
                                                          kwargs.get("user_from")),
        )


@api.admin_route.get('/get_dataset_info', validate=PermissionValidator('feeds.edit'))
def dashboard_get_dataset_info(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/get_dataset_info 根据报告id获取数据集相关信息
     @apiGroup  dashboard_feeds
      @apiBodyParam query {
        "dashboard_id{报告ID}": "39e41b58-acd8-ab00-de05-37e39b170604"
      }
      @apiResponse  200 {
          "result": {
            [{"id": "xxx", "name": "数据集名称", "fields": [
                {"id": "xxx","name": "字段名称"},
                {"id": "xxx2","name": "字段名称2"}]}
            ]
          },
          "msg": "ok"
      }
     */
    """
    return (True, '获取成功', dashboard_feeds_service.get_dashboard_dataset_info(kwargs.get("dashboard_id")))


@api.admin_route.get('/config/list', validate=PermissionValidator('feeds.edit'))
def list_subscribe_config(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/config/list 获取简讯订阅配置
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "name{名称}": "测试",
        "corp_id{账号}": "cc_test11",
        "corp_secret{密码}": "cc_test1222",
        "agent_id{代理}": "123213"
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {"items": [{"id": "11", "name": "测试"}], total: 2}
        }
     */
    """
    return (
        True,
        '获取成功',
        dashboard_feeds_service.list_subscribe_config(SubscribeConfigQueryModel(**kwargs)).get_result_dict(),
    )


@api.admin_route.post('/config/add', validate=PermissionValidator('feeds.edit'))
def add_subscribe_config(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {post} /api/dashboard_feeds/config/add 新增简讯订阅配置
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "name{名称}": "测试",
        "corp_id{账号}": "cc_test11",
        "corp_secret{密码}": "cc_test1222",
        "agent_id{代理}": "123213"
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
     */
    """
    model = SubscribeConfigModel(**kwargs)
    dashboard_feeds_service.add_subscribe_config(model)
    return True, '修改成功', model.id


@api.admin_route.post('/config/update', validate=PermissionValidator('feeds.edit'))
def update_subscribe_config(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {post} /api/dashboard_feeds/config/update 修改简讯订阅配置
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "id": "123",
        "name{名称}": "测试",
        "corp_id{账号}": "cc_test11",
        "corp_secret{密码}": "cc_test1222",
        "agent_id{代理}": "123213"
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
     */
    """
    model = SubscribeConfigModel(**kwargs)
    dashboard_feeds_service.update_subscribe_config(model)
    return True, '修改成功', model.id


@api.admin_route.post('/config/delete', validate=PermissionValidator('feeds.edit'))
def delete_subscribe_config(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {post} /api/dashboard_feeds/config/delete 删除简讯订阅配置
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "id": "123",
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "True"
        }
     */
    """
    return True, '删除成功', dashboard_feeds_service.delete_subscribe_config(kwargs.get("id"))


@api.admin_route.get('/config/new_list', validate=PermissionValidator('feeds.edit'))
def third_app_list_config(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/config/new_list 获取简讯订阅新的应用信息
     @apiGroup  dashboard_feeds
     @apiParam query {string}  page 页码 page_size 每页数量
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {"items": [
            {
                id: "3a0010bf-24c1-030d-424f-0559c326d8b7",
                third_party_id: "3a0010bf-24bc-e9f9-cbe9-04320dff010c",
                name: "23423",
                app_id: "23424",
                app_type: "1,2"
            }
          ], total: 1}
        }
     */
    """
    from feed.services.feed_service import get_third_app_list_used_user
    query_model = third_party_service.get_third_app_list_by_msg(ThirdPartyAppQueryModel(**kwargs))
    query_model.items = get_third_app_list_used_user(query_model.items)
    return (
        True,
        '获取成功',
        query_model.get_result_dict()
    )


@api.admin_route.post('/mobile/preview', validate=PermissionValidator('feeds.edit'))
def feed_mobile_preview(request, **kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {post} /api/dashboard_feeds/mobile/preview 预览简讯订阅
     @apiGroup  dashboard_feeds
     @apiBodyParam {
        "subject_email{邮件主题}": "cc_test11",
        "send_frequency{发送频率}": 1,
        "recipients{收件人(用户id)}": [{"name": "chenc04", "email": "253058094qq.com"}],
        "addresser{发件人}": "dmp",
        "message{消息正文}": "test"
      }
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "title": feed_model.subject_email,
            "description": message,
            "url": feed_model.release_url,
            "btntxt": "查看详情",
        }
        }
     */
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    model = dashboard_feeds_service.generate_dashboard_feed_model(kwargs)
    textcard_data = dashboard_feeds_service.feed_mobile_preview(model)
    remind = get_message_limit_remind(textcard_data.get("description"))
    return True, remind, textcard_data


@api.admin_route.get('/get_dataset_fields', validate=PermissionValidator('feeds.edit'))
def get_dataset_fields(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/get_dataset_fields 根据报告id获取数据集相关信息
     @apiGroup  dashboard_feeds
      @apiBodyParam query {
        "dataset_ids{数据集id列表ID}": []
      }
      @apiResponse  200 {
          "result": {
            [{"id": "xxx", "name": "数据集名称", "fields": [
                {"id": "xxx","name": "字段名称"},
                {"id": "xxx2","name": "字段名称2"}]}
            ]
          },
          "msg": "ok"
      }
     */
    """
    msg = '获取成功'
    try:
        dataset_ids = json.loads(kwargs.get("dataset_ids", '[]'))
    except Exception:
        dataset_ids = []
        msg = '获取失败'
    return True, msg, dashboard_feeds_service.get_dataset_fields(dataset_ids)


@api.admin_route.get('/get_project_yzs_config')
def get_project_yzs_config(**kwargs):
    return True, "获取成功", dashboard_feeds_service.get_project_yzs_config()


@api.admin_route.get('/msg_view')
def get_feeds_user_content(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/msg_view 获取简讯消息内容记录接口，用于简讯详情页
    http://dmp-test.mypaas.com.cn/static/msg/index.html?log_id=39ffb306-bbfd-4cd3-1fd9-fa509ea438c4
    @apiGroup  dashboard_feeds
        @apiParam query {string}  log_id 消息内容id
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              {
                 "id":"39ffb3fd-afd7-f8f5-994a-a447fd37ba46",
                "dashboard_id":"39ffb306-bbfd-4cd3-1fd9-fa509ea438c4",
                "email_subscribe_id":"39ffb30c-a642-1617-8ca9-1907e4419832",
                "msg_title":"权限测试 上海,深圳 蔡甸区,东西湖区,汉阳区,洪山区,黄陂区2021-10-21",
                "dashboard_name":"权限测试",
                "report_url":"http://dmp-test.mypaas.com.cn/api/user/assistant?project_code=uitest&amp;config_id=39f89df7-b904-6a06-26e3-c18ca52fb7a3&amp;redirect=http%3A//dmp-test.mypaas.com.cn/dataview-mobile/view/39ffb306-bbfd-4cd3-1fd9-fa509ea438c4%3Fcode%3Duitest%26dmp_send_date%3D2021-10-21%26send_time%3D2021-10-21%2B15%253A44%253A00",
                "user_name":"陆磊",
                "account":"lul05",
                "msg_content":"亲爱的客户，您好：&lt;br&gt;测试内容 3 北京,武汉&lt;br&gt;",
                "send_time":"2021-10-21 15:44:00",
                "app_code":"0",
                "is_long":0
              }
    }
    */
   """
    return True, '', dashboard_feeds_service.get_mobile_subscribe_user_log(kwargs.get('log_id'))


@api.admin_route.get('/detail_logs', validate=PermissionValidator('feeds.view'))
def get_mobile_subscribe_send_detail_log(**kwargs):
    """
     /*
     @apiVersion 1.0.9
     @api {get} /api/dashboard_feeds/detail_logs 获取简讯发送用户渠道消息状态明细
     @apiGroup  dashboard_feeds
     @apiParam query {string} [log_id] 简讯详情id
     @apiParam query {string} [status] 消息状态 1：成功，2：失败
     @apiParam query {number} page page
     @apiParam query {number} page_size size
     @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
            "items": [
                  {
                    id: "3a0044ae-13fa-7fb7-9836-b632b2108c7c",
                    email_subscribe_id: "3a003393-69b8-8349-54c3-fe0b42ac9fa4",
                    log_id: "3a0044ac-687a-aea5-b45b-29c706619e29",
                    user_name: "alex",
                    account: "alex",
                    actual_send_time: "2021-11-18 18:04:31",
                    status: 2,
                    error_reason: "发送简讯给alex，发送失败:企业微信未找到用户"
                }
            ],
            "total": 1
          }
        }
     */
    """
    return (
        True,
        'ok',
        dashboard_feeds_service.get_mobile_subscribe_send_detail_log(
            MobileSubscribeSendDetailLogQueryModel(**kwargs)).get_result_dict(),
    )


@api.admin_route.get('/used_dataset_tj', validate=PermissionValidator('feeds.view'))
def get_msg_statistics(response):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/used_dataset_tj 导出简讯已使用数据集的统计数据
    @apiGroup  dashboard_feeds
    @apiResponse  200 csv文件下载
    */
   """
    return True, '', dashboard_feeds_service.export_msg_statistics(response)


@api.admin_route.post('/role_list', validate=PermissionValidator('feeds.view'))
def get_mobile_role_list(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/role_list 获取简讯的角色列表详细信息
    @apiGroup  dashboard_feeds
    @apiParam query {string}  role_ids 角色id列表 ['角色id']
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              {
                {
                    id: "39f914c9-dadf-7ab4-d39a-ad3c8130e4d9",
                    name: "lulalex测试",
                    list: [
                            {
                                id: "39f89a8e-8213-bbc7-17c9-145c2a521642",
                                name: "alex",
                                type: "user"
                            },
                            {
                                id: "39f89a9c-3a4f-61cf-f2a9-edd89cd56dd7",
                                name: "测试Alex分组",
                                type: "group"
                            }
                    ]
                }
             }
    }
    */
   """
    return True, '', dashboard_feeds_service.get_subscribe_role_list(kwargs.get('role_ids'))


@api.admin_route.post('/role_user_list', validate=PermissionValidator('feeds.view'))
def get_mobile_role_user_list(**kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/role_user_list 获取简讯的角色中所有的用户
    @apiGroup  dashboard_feeds
    @apiParam query {string}  role_ids 角色id列表 ['角色id']
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              [
                {
                    "id": "0000EAA2-756F-4CFF-939B-B359A315D04C",
                    "account": "xnYH016573",
                    "name": "虚拟用户016573"
                },
                {
                    "id": "39f89a8e-8213-bbc7-17c9-145c2a521642",
                    "account": "alex",
                    "name": "alex"
                },
                {
                    "id": "39f8f1fb-8778-d998-47fe-c54ecf79d641",
                    "account": "lulei",
                    "name": "陆磊yzs"
                }
              ]
    }
    */
   """
    return True, '', dashboard_feeds_service.get_subscribe_role_user_list(kwargs.get('role_ids'))


@api.admin_route.get('/get_send_cache', validate=PermissionValidator('feeds.view'))
def get_send_cache(**kwargs):
    from feed.services.wechat_service import WechatService
    from dmplib.redis import conn as conn_redis

    feed_id = kwargs.get('feed_id', '')
    feed_detail_id = kwargs.get('feed_detail_id', '')
    save_key_list = ['send_detail_dict_list', 'failure_recipients_list', 'succeed_list',
                     'failure_list', 'g_user_analysis_list']
    data = {}
    for k in save_key_list:
        k += '_bak'
        v = conn_redis().lrange(WechatService.get_process_data_key(feed_id, feed_detail_id, k), 0, -1)
        data[k] = v
    return True, '', data


@api.admin_route.post('/check_feed_dataset_and_field')
def check_feed_dataset_and_field(**kwargs):
    from feed.services.feed_service import check_feed_dataset_and_field
    dataset_ids = kwargs.get('dataset_ids') or []
    field_ids = kwargs.get('field_ids') or []
    return True, '', check_feed_dataset_and_field(dataset_ids, field_ids)


@api.admin_route.post('/mobile/send_time_check', validate=PermissionValidator('feeds.edit'))
def feeds_send_time_check(request, **kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /api/dashboard_feeds/mobile/send_time_check 简讯使用的调度数据集清洗时间与简讯发送时间比对
    简讯发送时间-简讯内容数据集最接近简讯发送时间的清洗时间＜30分钟，则预警可能存在不及时风险；若＜15分钟则强制效验不允许保存，需要调整简讯时间或数据集清洗时间才可以。
    @apiGroup  dashboard_feeds
    @apiParam {
        "sync_dataset_ids": ["3a0a803a-53c3-0f53-ebf7-9eca9a33cf78"],   // 调度数据集id列表
        "schedule": "0 8 10 ? * * *"  // 简讯发送时间 schedule
    }
    @apiResponse  200 {
        "result": true,
        "msg": "",
        "data": [
            {
                "status": false,
                "minute": 13,
                "dataset_time": "23:55",
                "dataset_id": "3a031a1a-ed55-2d8a-c377-db844c325d17",
                "dataset_name": "s1调度"
            },
            {
                "status": true,
                "minute": 1394,
                "dataset_time": "0:54",
                "dataset_id": "3a0ac9c0-46d7-84db-fcbc-a6f8a863c86f",
                "dataset_name": "s1接口管家本地调度数据集"
            },
            {
                "status": false,
                "minute": 29,
                "dataset_time": "23:39",
                "dataset_id": "3a0b5ec2-028a-c007-d8fe-18fe304db728",
                "dataset_name": "s2调度1"
            }
        ]
    }
    */
    """
    sync_dataset_ids = kwargs.get("sync_dataset_ids", [])
    schedule = kwargs.get("schedule", [])
    result = dashboard_feeds_service.send_time_check(sync_dataset_ids, schedule)
    return True, '', result


@api.admin_route.get('/feed_used_dataset_stat')
def feed_used_dataset_stat(**kwargs):
    """
    手动触发简讯调度数据集统计
    http://dmp-test5.mypaas.com.cn/api/dashboard_feeds/feed_used_dataset_stat
    :param kwargs:
    :return:
    """
    import app_celery
    app_celery.feed_used_dataset_stat.apply_async(
        kwargs={},
        queue='celery',
    )
    return True, '当前环境简讯调度数据集统计-成功触发'
