#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
import os
import json
import traceback
import random
import string
import requests
import time
from hashlib import md5
from base64 import b64decode

from components import deshelper
from dmplib.db.mysql_wrapper import get_db
from open_hw.common.enums import InstanceStatus
from loguru import logger
from base import repository
from components.crypt import AESCrypt
from enum import Enum, unique
from dmplib.utils.errors import UserError
from open_hw.models import InstanceModel, ProjectModel, LicenseModel, TenantModel, AppModel, UserSyncModel
from dmplib import config
from dmplib.hug import g
from open_hw.repositories import hw_repository
from dmplib.utils.strings import seq_id

os.environ['prometheus_multiproc_dir'] = '/tmp'

password_special_symbols = '!@#$%^&*'


@unique
class ResponseCode(Enum):
    Success = '000000'  # 成功
    AuthFail = '000001'  # 鉴权失败
    IllegalRequest = '000002'  # 请求参数不合法
    InstanceNotFound = '000003'  # 实例ID不存在（商品续费、过期、资源释放接口可能返回）
    RequestProcessing = '000004'  # 请求处理中
    Error = '000005'  # 其它服务内部错误


RES_MSG = {
    ResponseCode.Success.value: "成功",
    ResponseCode.AuthFail.value: "鉴权失败",
    ResponseCode.IllegalRequest.value: "请求参数不合法",
    ResponseCode.InstanceNotFound.value: "实例ID不存在",
    ResponseCode.RequestProcessing.value: "请求处理中",
    ResponseCode.Error.value: "其他服务内部错误",
}


class InstanceApi(object):

    def __init__(self, **kwargs):
        self.activity = kwargs.get('activity')
        self.params = kwargs
        self.route_path_config = {
            'newInstance': self.new_instance, 'refreshInstance': self.refresh_instance, 'expireInstance': self.expire_instance,
            'releaseInstance': self.release_instance, 'upgrade': self.upgrade, 'instanceStatus': self.instance_status,
            'queryInstance': self.query_instance,
            'getLicense': self.get_license,
            'refreshLicense': self.refresh_license,
            'expireLicense': self.expire_license,
            'releaseLicense': self.release_license
        }

    def run(self):
        if self.activity:
            route_func = self.route_path_config.get(self.activity)
            try:
                return route_func(**self.params)
            except Exception as e:
                return self.error({}, ResponseCode.Error.value, '服务内部错误：{}'.format(str(e)))
        return self.error({}, ResponseCode.Error.value, '没有找到该场景的实现')

    @staticmethod
    def get_db():
        return repository

    @staticmethod
    def save_instance_if_nx(**kwargs):
        instance_model = InstanceModel(**kwargs)
        if instance_model.email:
            instance_model.email = instance_model.decrypt_mobile_phone_or_email(instance_model.email) or ''
        data = repository.get_one('hw_market_instance', {'order_id': instance_model.order_id}, from_config_db=True)
        exists = False
        if not data:
            repository.add_data('hw_market_instance', instance_model.get_dict(), commit=False, from_config_db=True)
        else:
            exists = True
            instance_model = InstanceModel(**data)
        return instance_model, exists

    @staticmethod
    def save_license(**kwargs):
        license_model = LicenseModel(**kwargs)
        data = repository.get_one('hw_market_license', {'instance_id': license_model.instance_id}, from_config_db=True)
        exists = False
        if not data:
            repository.add_data('hw_market_license', license_model.get_dict(), commit=False, from_config_db=True)
        else:
            exists = True
            license_model = LicenseModel(**data)
        return license_model, exists

    @staticmethod
    def new_instance(**kwargs):
        result = {}
        with get_db() as db:
            db.begin_transaction()
            try:
                kwargs['project_code'] = _gen_tenant_code()
                instance_model, exists = InstanceApi.save_instance_if_nx(**kwargs)
                data = {
                    'instanceId': instance_model.instance_id, 'encryptType': '1',
                    'appInfo': {'frontEndUrl': config.get('Domain.dmp'), 'userName': '', 'password': ''}
                }
                result = InstanceApi.success(data)
                if not exists:
                    # 开通租户
                    code = instance_model.project_code
                    admin_pwd = _gen_pwd(code)
                    data['appInfo']['userName'] = instance_model.generate_user_name_or_pwd(code)
                    data['appInfo']['password'] = instance_model.generate_user_name_or_pwd(admin_pwd)
                    customer_name = instance_model.customer_name if instance_model.customer_name else code
                    project = ProjectModel(name=customer_name, title=customer_name,rds_id=hw_repository.get_rds_of_min_using(),
                                         code=code, admin_email=instance_model.email, is_send_email=1, admin_pwd=admin_pwd, type="可视化",
                                         is_rdc_auth="0", storage_type="cloud", allow_dashboard_type="large,dashboard,mobile",
                                         value_added_func=["manual_filling", "active_reports", "ppt"], rds_strategy="average")
                    res = req_open_tenant(project)
                    if res['result']:
                        result = InstanceApi.success(data)
                        db.commit()
                    else:
                        result = InstanceApi.error({}, ResponseCode.Error.value, '服务内部错误：{}'.format(res['msg']))
                        db.rollback()
            except Exception as e:
                logger.error(str(e))
                db.rollback()
                raise e
        return result

    @staticmethod
    def refresh_instance(**kwargs):
        instance_model = InstanceModel(**kwargs)
        data = InstanceApi.get_db().get_one('hw_market_instance', {'instance_id': instance_model.instance_id}, from_config_db=True)
        if not data:
            return InstanceApi.error({}, ResponseCode.InstanceNotFound.value)
        InstanceApi.get_db().update_data('hw_market_instance', {'expire_time': instance_model.expire_time}, {'instance_id': instance_model.instance_id}, from_config_db=True)
        return InstanceApi.success(dict())

    @staticmethod
    def expire_instance(**kwargs):
        expire_time = kwargs.get('timeStamp')
        instance_id = kwargs.get('instanceId')
        data = InstanceApi.get_db().get_one('hw_market_instance', {'instance_id': instance_id}, from_config_db=True)
        if not data:
            return InstanceApi.error({}, ResponseCode.InstanceNotFound.value)
        InstanceApi.get_db().update_data('hw_market_instance', {'instance_status': InstanceStatus.Expired.value, 'expire_time': expire_time}, {'instance_id': instance_id}, from_config_db=True)
        return InstanceApi.success(dict())

    @staticmethod
    def release_instance(**kwargs):
        instance_model = InstanceModel(**kwargs)
        data = InstanceApi.get_db().get_one('hw_market_instance', {'instance_id': instance_model.instance_id}, from_config_db=True)
        if not data:
            return InstanceApi.error({}, ResponseCode.InstanceNotFound.value)
        InstanceApi.get_db().update_data('hw_market_instance', {'instance_status': InstanceStatus.Deleted.value}, {'instance_id': instance_model.instance_id}, from_config_db=True)
        return InstanceApi.success(dict())

    @staticmethod
    def upgrade(**kwargs):
        instance_model = InstanceModel(**kwargs)
        data = InstanceApi.get_db().get_one('hw_market_instance', {'instance_id': instance_model.instance_id}, from_config_db=True)
        if not data:
            return InstanceApi.error({}, ResponseCode.InstanceNotFound.value)
        update = {'sku_code': instance_model.sku_code, 'product_id': instance_model.product_id}
        if instance_model.amount:
            update['amount'] = instance_model.amount
        if instance_model.disk_size:
            update['disk_size']: instance_model.disk_size
        if instance_model.band_width:
            update['band_width']: instance_model.band_width
        InstanceApi.get_db().update_data('hw_market_instance', update, {'instance_id': instance_model.instance_id}, from_config_db=True)
        return InstanceApi.success(dict())

    @staticmethod
    def instance_status(**kwargs):
        status = kwargs.get('status')
        instance_id = kwargs.get('instanceId')
        instance_status = InstanceStatus.Freeze.value if status == 'FREEZE' else InstanceStatus.Normal.value
        data = InstanceApi.get_db().get_one('hw_market_instance', {'instance_id': instance_id}, from_config_db=True)
        if not data:
            return InstanceApi.error({}, ResponseCode.InstanceNotFound.value)
        InstanceApi.get_db().update_data('hw_market_instance', {'instance_status': instance_status}, {'instance_id': instance_id}, from_config_db=True)
        return InstanceApi.success(dict())

    @staticmethod
    def query_instance(**kwargs):
        instance_id = kwargs.get('instanceId')
        data = InstanceApi.get_db().get_list('hw_market_instance', {'instance_id': instance_id}, from_config_db=True)
        if not data:
            return InstanceApi.error({}, ResponseCode.InstanceNotFound.value)
        result = {'info': [], 'encryptType': 1}
        for item in data:
            instance = {
                'instanceId': item.get('instance_id'),
                'appInfo': {'frontEndUrl': config.get('Domain.dmp')},
                'usageInfos': None
            }
            result['info'].append(instance)
        return InstanceApi.success(result)

    @staticmethod
    def get_license(**kwargs):
        with get_db() as db:
            db.begin_transaction()
            try:
                instance_model, exists = InstanceApi.save_instance_if_nx(**kwargs)
                if exists:
                    data = repository.get_one('hw_market_license', {'instance_id': instance_model.instance_id}, from_config_db=True)
                    license = data['license']
                else:
                    id_code = _get_id_code(instance_model.saas_extend_params)
                    license = _generate_license(id_code, instance_model.instance_id)
                    kwargs['license'] = license
                    kwargs['id_code'] = id_code
                    InstanceApi.save_license(**kwargs)
                result = {
                    'license': license,
                    'instanceId': instance_model.instance_id
                }
                db.commit()
            except Exception as e:
                db.rollback()
                raise e
        return InstanceApi.success(result)

    @staticmethod
    def refresh_license(**kwargs):
        with get_db() as db:
            db.begin_transaction()
            try:
                instance_model = InstanceModel(**kwargs)
                expire_time = instance_model.expire_time
                if datetime.now() > expire_time:
                    raise UserError(message='续费订单过期时间小于当前时间')
                InstanceApi.get_db().update_data('hw_market_instance', {'expire_time': instance_model.expire_time},
                                                 {'instance_id': instance_model.instance_id}, from_config_db=True)
                InstanceApi.get_db().update_data('hw_market_license', {'expire_time': instance_model.expire_time, 'ref_order_id': instance_model.order_id, 'status': InstanceStatus.Normal.value},
                                                 {'instance_id': instance_model.instance_id}, from_config_db=True)
                db.commit()
            except Exception as e:
                logger.error(str(e))
                db.rollback()
                raise e
        return InstanceApi.success({})

    @staticmethod
    def expire_license(**kwargs):
        with get_db() as db:
            db.begin_transaction()
            try:
                instance_model = InstanceModel(**kwargs)
                InstanceApi.get_db().update_data('hw_market_instance', {'instance_status': InstanceStatus.Expired.value},
                                                 {'instance_id': instance_model.instance_id}, from_config_db=True)
                InstanceApi.get_db().update_data('hw_market_license', {'ref_order_id': instance_model.order_id, 'status': InstanceStatus.Normal.value},
                                                 {'instance_id': instance_model.instance_id}, from_config_db=True)
                db.commit()
            except Exception as e:
                logger.error(str(e))
                db.rollback()
                raise e
        return InstanceApi.success({})

    @staticmethod
    def release_license(**kwargs):
        with get_db() as db:
            db.begin_transaction()
            try:
                instance_model = InstanceModel(**kwargs)
                InstanceApi.get_db().update_data('hw_market_instance',
                                                 {'instance_status': InstanceStatus.Deleted.value},
                                                 {'instance_id': instance_model.instance_id}, from_config_db=True)
                InstanceApi.get_db().update_data('hw_market_license', {'ref_order_id': instance_model.order_id,
                                                                       'status': InstanceStatus.Deleted.value},
                                                 {'instance_id': instance_model.instance_id}, from_config_db=True)
                db.commit()
            except Exception as e:
                logger.error(str(e))
                db.rollback()
                raise e
        return InstanceApi.success({})

    @staticmethod
    def success(data: dict, msg='success'):
        if not data:
            data = dict()
        msg_code = {'resultCode': ResponseCode.Success.value, 'resultMsg': msg}
        data.update(msg_code)
        return data

    @staticmethod
    def error(data: dict, error_code=ResponseCode.Error.value, msg=None):
        if not data:
            data = dict()
        msg_code = {'resultCode': error_code, 'resultMsg': msg or RES_MSG.get(error_code)}
        data.update(msg_code)
        return data


def _gen_md5_value(value):
    m = md5()
    m.update(value.encode(encoding='UTF-8'))
    md5_value = m.hexdigest()
    return '%s-%s-%s%s-%s-%s' % (
        md5_value[:8],
        md5_value[8:12],
        md5_value[12:17],
        md5_value[17:12],
        md5_value[22:27],
        md5_value[27:32],
    )


def _gen_tenant_code():
    return f"hw_{int(time.time())}"


def _format_res_msg(error_code):
    return {
        'resultCode': error_code,
        'resultMsg': RES_MSG.get(error_code) or ''
    }


def tenant_sync(**kwargs):
    flag = kwargs.get('flag')
    model = TenantModel(**kwargs)
    try:
        # 判断是否已有对应租户信息
        data = repository.get_one('hw_market_tenant', {'instance_id': model.instance_id, 'tenant_id': model.tenant_id}, from_config_db=True)
        if flag:
            # 新增场景
            if not data:
                repository.add_model('hw_market_tenant', model, from_config_db=True)
        else:
            # 删除场景
            if data:
                repository.delete_data('hw_market_tenant', {'instance_id': model.instance_id, 'tenant_id': model.tenant_id}, from_config_db=True)
        return return_suc()
    except Exception as e:
        logger.error(str(e))
        return _format_res_msg(ResponseCode.Error.value)


def application_sync(**kwargs):
    flag = kwargs.get('flag')
    model = AppModel(**kwargs)
    model.get_client_secret()
    try:
        where = {'instance_id': model.instance_id, 'tenant_id': model.tenant_id, 'app_id': model.app_id}
        # 判断是否已有对应应用信息
        data = repository.get_one('hw_market_apps', where, from_config_db=True)
        if flag:
            # 新增场景
            if not data:
                repository.add_model('hw_market_apps', model, from_config_db=True)
        else:
            # 删除场景
            if data:
                repository.delete_data('hw_market_apps', where, from_config_db=True)
        return return_suc()
    except Exception as e:
        logger.error(str(e))
        return _format_res_msg(ResponseCode.Error.value)


def return_suc():
    return _format_res_msg(ResponseCode.Success.value)


def auth_sync(model: UserSyncModel):
    from dmplib.constants import ADMINISTRATORS_GROUP_ID
    if not model.user_list or not model.instance_id:
        return _format_res_msg(ResponseCode.IllegalRequest.value)
    # 获取实例对应的租户code
    tenant_code = repository.get_data_scalar('hw_market_instance', {'instance_id': model.instance_id}, 'project_code', from_config_db=True)
    if not tenant_code:
        logger.error('没有找到对应的租户code')
        return _format_res_msg(ResponseCode.Error.value)
    user_list = model.user_list
    if not user_list:
        return _format_res_msg(ResponseCode.Success.value)
    account_list = [user.get('userName') for user in user_list]
    _set_global_g(tenant_code)
    try:
        if model.flag == 0:
            # 删除用户
            user_ids = repository.get_column('user', {'account': account_list}, 'id') or []
            if user_ids:
                repository.delete_data('user', {'id': user_ids})
                repository.delete_data('user_user_role', {'user_id': user_ids})
                repository.delete_data('user_group_user', {'user_id': user_ids})
                repository.delete_data('user_organization', {'user_id': user_ids})
        elif model.flag == 1:
            # 新增用户
            add_user = []
            has_account = repository.get_column('user', {'account': account_list}, 'account') or []
            for user in user_list:
                if user.get('userName') in has_account or not user.get('userName'):
                    continue
                item = {
                    'id': seq_id(), 'account': user.get('userName'), 'name': user.get('name'),
                    'is_disabled': 0 if user.get('enable') else 1, 'email': user.get('email'),
                    'group_id': ADMINISTRATORS_GROUP_ID
                }
                add_user.append(item)
            if add_user:
                repository.add_list_data('user', add_user, list(add_user[0].keys()))
        elif model.flag == 2:
            # 更新用户
            for user in user_list:
                if not user.get('userName'):
                    continue
                update_data = {
                    'name': user.get('name'), 'is_disabled': 0 if user.get('enable') else 1, 'email': user.get('email')
                }
                repository.update('user', update_data, {'account': user.get('userName')})
    except Exception as e:
        logger.error(str(e))
        return _format_res_msg(ResponseCode.Error.value)
    return return_suc()


def _gen_pwd(code):
    admin_pwd = _generate_password()
    while code in admin_pwd:
        admin_pwd = _generate_password()
    return admin_pwd


def _set_global_g(code):
    g.code = code
    g.account = code


def _decrypt(value: str):
    """
    邮箱解密
    :param value:
    :return:
    """
    try:
        if value:
            value = b64decode(value).decode()
            return AESCrypt(key=config.get('HW.key')).decrypt(value)
        return ''
    except Exception as e:
        logger.error(e)
        return ''


def update_model(model, condition):
    repository.update_data(
        'project_hw_extra', model,
        condition=condition, from_config_db=True
    )


def api_log(request, params):
    data = dict()
    data['params'] = params
    data['route'] = request.path
    data['token'] = request.headers.get("AUTHTOKEN") or data.get('params', {}).get('authToken') or ''
    data['params'] = json.dumps(data['params'], ensure_ascii=False)
    repository.add_data('hw_api_log', data, from_config_db=True)


def req_open_tenant(project):
    from open_data.services.open_data_service import project_is_exists
    try:
        project_code = project.code
        has_project = project_is_exists(project_code)
        if has_project:
            return True, '', ''
        host = config.get("Dmp.admin_host", "http://dmp-admin:8000") or "http://dmp-admin:8000"
        res = requests.post(
            url=f"{host}/openapi/projects",
            json=project.__dict__
        )
        if res.status_code != 200:
            logger.error(f"open tenant error: {res.text}")
        logger.info(f"open tenant res: {res.text}")
        return res.json()
    except Exception as e:
        msg = f"open tenant error: {e} \ntraceback: {traceback.print_exc()}"
        raise UserError(message=msg)


def _generate_password():
    clist = [string.ascii_letters, string.digits, password_special_symbols]
    rest = clist.copy()
    random.shuffle(clist)

    chars = []
    for _ in range(16):
        cset = rest.pop()
        chars.extend(random.sample(cset, 1))
        if not rest:
            rest = clist.copy()
            random.shuffle(rest)
    return ''.join(chars)


def _get_id_code(saas_extend_params):
    id_code = ''
    if saas_extend_params:
        ext_params = json.loads(saas_extend_params)
        for entry in ext_params:
            if 'identificationCode' == entry['name']:
                id_code = entry['value']
    return id_code


def _generate_license(id_code, instance_id):
    s = f'{id_code}&{instance_id}'
    return deshelper.encrypt(s)


if __name__ == "__main__":
    # _decrypt('')
    # s = 'W3sibmFtZSI6ImlkZW50aWZpY2F0aW9uQ29kZSIsInZhbHVlIjoiMDAwMDAwMTZCNUIwNDRGN0I1MUI4QzRDNUE3RTcwMTQzNDFERjAxNkIyNjVERUI4RDJDRERBOEY2QTZEODQ4In1d'
    # s = parse.unquote(s)
    # s = base64.b64decode(s).decode()
    s = _generate_license('00000016B5B044F7B51B8C4C5A7E7014341DF016B265DEB8D2CDDA8F6A6D848','CS1906666666ABCDE')
    print(s)
