#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@author: <EMAIL>
@time: 2021/7/26 14:42
"""
import logging
import os
from datetime import datetime

from base import repository
from components.rundeck import get_rundeck_client

from dmplib.redis import conn_custom_prefix as conn_redis
from components.message_queue import RabbitMQ
from celery_app.celery import celery
from kombu.exceptions import ChannelError
from dmplib.redis import RedisCache
from dmplib.db.mysql_wrapper import get_db
from dmplib import config
from dmplib.hug import g

logger = logging.getLogger(__file__)


def mysql_db_monitor():
    """
    监控mysql的可读可写功能
    :return:
    """
    try:
        with get_db() as db:
            db.query_scalar("select code from project")
            return {"mysql": {"service": {"status": "normal", "msg": "healthy"}}}
    except Exception as e:
        return {"mysql": {"service": {"status": "exception", "msg": str(e)}}}


def redis_db_monitor():
    """
    redis服务监控，是否可用，剩余内存
    :return:
    """
    try:
        redis = RedisCache()
        info = redis._connection.info()
        used_memory = info.get('used_memory', 0)
        used_memory_human = info.get('used_memory_human', "")
        total_system_memory = info.get('total_system_memory', 0)
        total_system_memory_human = info.get('total_system_memory_human', "")
        keyspace_hits = info.get('keyspace_hits', 0)
        keyspace_misses = info.get('keyspace_misses', 0)
        remain_memory = (total_system_memory - used_memory) / 1024 / 1024 if total_system_memory else 0
        remain_memory_str = f"{remain_memory}M" if total_system_memory else ''
        monitor = {
            "redis": {
                "service": {
                    "status": "normal",
                    "msg": "",
                },
                "memory": {
                    "status": "normal",
                    'msg': "",
                    "used_memory_human": used_memory_human,
                    "used_memory": float("%.2f" % (used_memory / 1024 / 1024 / 1024)),
                    "total_memory": total_system_memory_human,
                    "remain_memory": remain_memory_str,
                    "keyspace_hits_rate": "{}%".format(int(keyspace_hits / (keyspace_hits + keyspace_misses) * 100))
                }
            }
        }
        if total_system_memory and remain_memory <= 100:
            monitor['redis']['memory'].update({
                "status": "exception",
                'msg': "可用内存不足",
            }
            )
        return monitor

    except Exception as e:
        return {"redis": {
            "service": {
                "status": "exception",
                "msg": str(e)
            },
            "memory": {
                "status": "exception",
                "msg": str(e)
            }
        }}


def rabbitmq_queue_monitor(mq_queue_name=None):
    """
    rabbitmq服务监控
    数据集调度服务监控
    队列未消费的消息数
    :param mq_queue_name:
    :return:
    """
    mq = RabbitMQ()
    queue_name_list = [config.get("RabbitMQ.queue_name_datax", ""),
                       config.get("RabbitMQ.queue_name_datax_offline", ""),
                       config.get("RabbitMQ.queue_name_download", ""),
                       config.get("RabbitMQ.queue_name_flow_feeds", ""),
                       config.get("RabbitMQ.queue_name_flow", ""),
                       config.get("RabbitMQ.queue_name_flow_offline", ""),
                       config.get("RabbitMQ.queue_name_work_flow", ""),
                       config.get("RabbitMQ.queue_name_flow_priority", ""),
                       config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op') or 'Flow-erp-op']
    if mq_queue_name:
        queue_name_list = [mq_queue_name]

    def _queue_monitor(queue_name):
        monitor_item = {}
        try:
            dataset_queue_count = mq.get_consumer_count(queue_name)
            dataset_message_count = mq.get_message_count(queue_name)
            service = {"status": "normal", "msg": "healthy", "consumer_count": dataset_queue_count}
            queue_status = {"status": "normal", "msg": "", "remain": dataset_message_count}
        except Exception as e:
            service = {"status": "exception", "msg": str(e)}
            queue_status = {"status": "exception", "msg": "数据集队列服务异常", "remain": 0}
        monitor_item['service'] = service
        monitor_item['queue_status'] = queue_status
        return monitor_item

    def _queue_monitor_new(queue_name):
        monitor_item = {}
        try:
            from dmplib.redis import RedisCache
            cache = RedisCache(key_prefix="dmp:consumer")
            dataset_queue_count = cache.hget("consumer_num", queue_name) or 0
    
            dataset_message_count = mq.get_message_count(queue_name)
            service = {
                "status": "normal",
                "msg": "healthy",
                "consumer_count": dataset_queue_count
            }
            queue_status = {"status": "normal", "msg": "", "remain": dataset_message_count}
        except Exception as e:
            service = {"status": "exception", "msg": str(e)}
            queue_status = {"status": "exception", "msg": "数据集队列服务异常", "remain": 0}
    
        monitor_item['service'] = service
        monitor_item['queue_status'] = queue_status
        return monitor_item

    monitor_queue_dict = {}
    for q in queue_name_list:
        if not q:
            continue
        if 'datax' in q or 'feed' in q:
            monitor_queue_dict[q] = _queue_monitor(q)
        else:
            monitor_queue_dict[q] = _queue_monitor_new(q)
    mq_monitor = {
        "rabbitmq": monitor_queue_dict
    }
    return mq_monitor



def celery_queue_monitor(celery_queue_name=None):
    """
    celery服务监控
    队列未消费的消息数
    :param celery_queue_name:
    :return:
    """
    celery_queue_list = ["celery", "app_celery", "new_feeds", "feeds", "download", "parser", "upgrade", "celery-slow"]
    if celery_queue_name:
        celery_queue_list = [celery_queue_name]

    from dmplib.redis import get_redis_conn

    dmp_conn = get_redis_conn(db=int(config.get("Redis.celery_db")))
    admin_conn = get_redis_conn(db=int(config.get('Redis.admin_celery_db') or 6))

    def _queue_monitor(queue_name, conn):
        monitor_item = {
            "status": "normal",
            "msg": "healthy",
            "worker": 0,
            "message_count": 0
        }
        try:
            # queue = celery.connection().channel().queue_declare(queue_name, passive=True)
            # worker_count = queue.consumer_count
            # message_count = queue.message_count
            message_count = conn.llen(queue_name)
            monitor_item["worker"] = 0
            monitor_item["message_count"] = message_count
        except ChannelError as e:
            monitor_item["status"] = "exception"
            monitor_item["msg"] = str(e)
            if e.reply_code == '404':
                monitor_item["status"] = "normal"
                monitor_item["msg"] = "队列不存在(404)"
        except Exception as e:
            monitor_item["status"] = "exception"
            monitor_item["msg"] = str(e)
        return monitor_item

    monitor_queue_dict = {}
    for q in celery_queue_list:
        monitor_queue_dict[q] = _queue_monitor(q, dmp_conn)

    monitor_queue_dict["admin-celery"] = _queue_monitor("celery", admin_conn)
    celery_monitor = {
        "celery": monitor_queue_dict
    }
    return celery_monitor


def rundeck_monitor():
    """
    rundeck服务监控
    :return:
    """
    service = {
        "status": "normal",
        "msg": "healthy"
    }
    # rundeck_system_info = None
    try:
        rundeck_server = config.get('Rundeck.server')
        if rundeck_server is None:
            raise Exception('Rundeck服务配置缺失')

        rundeck = get_rundeck_client()
        _ = rundeck.system_info()
    except Exception as e:
        service["status"] = "exception"
        service["msg"] = str(e)

    # 安全问题, 禁止输出系统消息
    # data = {
    #     "rundeck": {"service": service, "system_info": rundeck_system_info}
    # }
    data = {
        "rundeck": {"service": service}
    }
    return data


def show_log_data(dir_path):
    """
    展示目录下所有的文件夹和文件
    :param dir_path:
    :return:
    """
    def file_stat(file_path):
        file_stat = os.stat(file_path)
        size_mb = round(file_stat.st_size/1024/1024, 2)
        return size_mb, datetime.fromtimestamp(file_stat.st_mtime).strftime('%H:%M:%S')

    def list_files(startpath):
        if not os.path.isdir(startpath):
            raise Exception(f'{startpath} 目录不存在')
        dir_tree = ''
        for root, dirs, files in os.walk(startpath):
            level = root.replace(startpath, '').count(os.sep)
            indent = '&nbsp;' * 4 * (level)
            dir_name = os.path.basename(root)
            dir_tree += '<br>{}{}/'.format(indent, dir_name)
            subindent = '&nbsp;' * 4 * (level + 1)
            files = [os.path.join(root, f) for f in files]
            files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            for f in files:
                size, mtime = file_stat(f)
                file_name = f.replace(root+'/', "")
                view_link = f'<a href="/api/monitor/app/logs/view?dir_name={dir_name}&file_name={file_name}" target="_blank">view</a>'
                dir_tree += '<br>{}{} {}MB {} {}'.format(subindent, f.replace(root+'/', ''), size, mtime, view_link)
        return dir_tree

    return list_files(dir_path)


def view_log(dir_name, file_name, num):
    """
    日志文件在线查看
    :param dir_name:
    :param file_name:
    :param num:
    :return:
    """

    def tail(f, lines=20):
        total_lines_wanted = lines
        BLOCK_SIZE = 1024
        f.seek(0, 2)
        block_end_byte = f.tell()
        lines_to_go = total_lines_wanted
        block_number = -1
        blocks = []
        while lines_to_go > 0 and block_end_byte > 0:
            if (block_end_byte - BLOCK_SIZE > 0):
                f.seek(block_number * BLOCK_SIZE, 2)
                blocks.append(f.read(BLOCK_SIZE))
            else:
                f.seek(0, 0)
                blocks.append(f.read(block_end_byte))
            lines_found = blocks[-1].count(b'\n')
            lines_to_go -= lines_found
            block_end_byte -= BLOCK_SIZE
            block_number -= 1
        all_read_text = b''.join(reversed(blocks))
        return b'\n'.join(all_read_text.splitlines()[-total_lines_wanted:])

    file_path = os.path.join("/app/logs", dir_name, file_name)
    if os.path.isfile(file_path):
        with open(file_path, 'rb') as f:
            return tail(f, num)
    else:
        raise Exception(f'文件不存在,file_path:{file_path}')


def reg_rundeck_task(kwargs):
    import app_celery
    tenant_codes = kwargs.get("tenant_codes")
    if tenant_codes:
        tenant_codes = tenant_codes.split(',') if isinstance(tenant_codes, str) else tenant_codes
    else:
        tenant_codes = [g.code]
    data = []
    domain = config.get('Domain.dmp', '')
    for code in tenant_codes:
        params = {
            "code": code
        }
        app_celery.reg_rundeck_task.apply_async(kwargs=params)
        key = f"reg_rundeck_task_init:{code}"
        data.append({"code": code, "url": f"{domain}/api/monitor/tmp_log2?uuid={key}"})
        # 迁移租户，重新注册任务时，清除项目缓存
        clear_project_cache(code)
    return data


def clear_project_cache(project_code):
    conn = conn_redis(project_code+':')
    conn.delete('Project:Detail:Info:' + project_code)
    conn.delete('dmp:dmp_env_sign:' + project_code)
    conn.delete('dmp:storage:' + project_code)


def move_attach_task(kwargs):
    """
    租户旧环境oss附件数据迁移到新环境的oss
    :param kwargs:
    :return:
    """
    import app_celery
    tenant_codes = kwargs.get("tenant_codes")
    if tenant_codes:
        tenant_codes = tenant_codes.split(',') if isinstance(tenant_codes, str) else tenant_codes
    else:
        tenant_codes = [g.code]
    reg_rundeck = kwargs.get("reg_rundeck")
    reg_rundeck_data = []
    if reg_rundeck:
        reg_rundeck_data = reg_rundeck_task(kwargs)
        logger.error("附件迁移的同时，也注册调度任务")

    origin_endpoint = kwargs.get("origin_endpoint")
    origin_bucket = kwargs.get("origin_bucket")
    origin_oss_service = kwargs.get("origin_oss_service")
    table_name = kwargs.get("table_name")
    enable_rdc = kwargs.get("enable_rdc")
    move_attach_data = []
    domain = config.get('Domain.dmp', '')
    for code in tenant_codes:
        params = {
            "code": code,
            "origin_endpoint": origin_endpoint,
            "origin_bucket": origin_bucket,
            "origin_oss_service": origin_oss_service,
            "table_name": table_name,
        }
        app_celery.move_attach_task.apply_async(kwargs=params, queue='celery-slow')
        key = f"move_attach_task:{code}"
        move_attach_data.append({"code": code, "url": f"{domain}/api/monitor/tmp_log2?uuid={key}"})
        # 是否开启rdc
        if enable_rdc == '1':
            update_project(code, {'is_rdc_auth': 1})
    data = {
        "reg_rundeck": reg_rundeck_data,
        "move_attach": move_attach_data,
    }
    return data


def update_project(tenant_code, columns):
    repository.update_data('project', columns, {'code': tenant_code}, from_config_db=True)
