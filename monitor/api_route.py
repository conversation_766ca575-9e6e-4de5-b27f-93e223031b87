#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@author: <EMAIL>
@time: 2021/7/26 14:19
"""
import json
import hug

from dmplib.hug import APIWrapper
from monitor.services import monitor_service
from dmplib.redis import conn as conn_redis, conn_custom_prefix
from base.dmp_constant import REDIS_TEMPORARY_LOG_STORE_KEY

api = APIWrapper(__name__)


@api.route.get('/health/check')
def health_check(request, response, **kwargs):
    """
    健康检查
    :param kwargs:
    :return:
    """
    msg = 'ok'
    if not kwargs.get('auth'):
        response.status = hug.falcon.HTTP_401
        msg = 'not auth'
    return True, '', msg


@api.route.get("/runtime")
def runtime_monitor(**kwargs):
    """
    站点可访问
    数据库可正常读写
    数据集调度服务正常工作
    数据集消息队列堵塞
    监控数据集清洗报错（暂时做不了）
    管理简讯调度服务正常工作
    管理简讯消息队列堵塞
    监控管理简讯过程报错（暂时做不了）
    :return:
    """
    # celery 指定队列名称
    celery_queue_name = kwargs.get("celery_queue_name")
    # rabbitmq 指定队列名称
    mq_queue_name = kwargs.get("mq_queue_name")

    monitor = {}
    # rabbitmq monitor
    monitor.update(monitor_service.rabbitmq_queue_monitor(mq_queue_name))
    # celery monitor
    monitor.update(monitor_service.celery_queue_monitor(celery_queue_name))
    # mysql monitor
    monitor.update(monitor_service.mysql_db_monitor())
    # redis monitor
    monitor.update(monitor_service.redis_db_monitor())
    # rundeck monitor
    monitor.update(monitor_service.rundeck_monitor())
    return True, '', monitor


@api.admin_route.get('/tmp_log')
def tmp_log(response, uuid, **kwargs):
    """
    查看一些临时日志
    :return:
    """
    raw = str(kwargs.get('raw', '0'))
    conn = conn_redis()
    key = f'{REDIS_TEMPORARY_LOG_STORE_KEY}:{uuid}'
    data = conn.get(key)
    if isinstance(data, bytes):
        data = data.decode()
    else:
        data = data
    try:
        data = json.loads(data)
    except:
        data = str(data)
    if raw == '1':
        response.body = str(data)
        response.set_header('Content-Type', 'text/html; charset=utf-8')
    else:
        return True, 'ok', data


@api.admin_route.get('/tmp_log2')
def tmp_log2(uuid, **kwargs):
    """
    查看一些临时日志
    :return:
    """
    conn = conn_custom_prefix(REDIS_TEMPORARY_LOG_STORE_KEY)
    key = f'{uuid}'
    data = conn.get(key)
    if isinstance(data, bytes):
        data = data.decode()
    else:
        data = data
    try:
        data = json.loads(data)
    except:
        data = str(data)
    return True, 'ok', data


@api.admin_route.get("/app/logs")
def app_logs(response):
    response.set_header('Content-Type', 'text/html; charset=utf-8')
    try:
        dir_path = '/app/logs'
        data = monitor_service.show_log_data(dir_path)
    except Exception as e:
        data = f"执行错误，errs:{str(e)}"
    response.body = data


@api.admin_route.get("/app/logs/view")
def app_logs(response, **kwargs):
    response.set_header('Content-Type', 'text/plain; charset=utf-8')
    try:
        dir_name = kwargs.get("dir_name")
        file_name = kwargs.get("file_name")
        n = int(kwargs.get("n", 10))
        data = monitor_service.view_log(dir_name, file_name, n)
    except Exception as e:
        data = f"查看错误，errs:{str(e)}"
    response.body = data


@api.admin_route.get("/move/sql")
def get_config_sql(response, **kwargs):
    """
    将租户的相关配置库表记录导出为SQL文件
    :param response:
    :param kwargs:
    :return:
    """
    from monitor.services.export_config_sql_service import export_config_insert_sql_file
    try:
        export_config_insert_sql_file(kwargs, response)
    except Exception as e:
        raise Exception(f"SQL获取失败，errs:{str(e)}")


@api.admin_route.get("/move/rundeck")
def reg_rundeck_task(response, **kwargs):
    """
    在新租户中将所有的调度数据集注册到 rundeck
    :param response:
    :param kwargs:
    :return:
    """
    data = {}
    try:
        data = monitor_service.reg_rundeck_task(kwargs)
        msg = 'success'
    except Exception as e:
        msg = f"查看错误，errs:{str(e)}"
    return True, msg, data


@api.admin_route.get("/move/attach")
def move_attach_task(response, **kwargs):
    """
    租户旧环境oss附件数据迁移到新环境的oss
    :param response:
    :param kwargs:
    请求参数：
    :param tenant_codes: 租户列表，多个租户用逗号分隔
    :param origin_endpoint: 租户原环境的oss host
    :param origin_bucket: 租户原环境的oss bucket
    :param origin_oss_service: 租户原环境的oss 服务类型，oss or minio
    :param table_name: 可选。指定附件迁移的表名。不指定就是全部需要迁移的数据表
    :return:
    """
    data = {}
    try:
        data = monitor_service.move_attach_task(kwargs)
        msg = 'success'
    except Exception as e:
        msg = f"查看错误，errs:{str(e)}"
    return True, msg, data
