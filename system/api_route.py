#!/usr/bin/env python
# -*- coding: utf-8 -*-
# pylint: skip-file
# Created by <PERSON><PERSON> on 2018/1/12
import time
import hug
from collections import OrderedDict
from hashlib import sha1
from urllib.parse import urlencode, quote
import logging

import app_celery
from base.dmp_constant import AES_DEFAULT_KEY
from base.event import register_api
from components import dynamics_config
from components.global_utils import compare_dict
from components.publish_center_api import PublishCenterApi

from components.rundeck import RundeckScheduler
from dashboard_chart.services import components_service
from dmplib import config
from dmplib.hug import APIWrapper
from dmplib.hug import g
from dmplib.saas import project
from dmplib.db import mysql_wrapper
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from flow.models import FlowModel
from system.models import SystemSetting
from system.services import env_service, analysis_service, sql_adb_to_st
from system.services import system_setting_service
from app_menu.services.external_service import get_token_url
from user.services import user_service
import os
from user_log.models import UserLogModel
from dashboard_chart.api_route import DataApiWrapper

logger = logging.getLogger(__name__)
api = APIWrapper(__name__)
data_api = DataApiWrapper(__name__)


@api.route.get('/verify_env')
def verify_env(**kwargs):
    env = env_service.EnvService()

    return True, '操作成功', env.verify_env(code=kwargs.get('code'), item=kwargs.get('item'))


@api.admin_route.get('/init')
def system_init(request, response):
    env_code = config.get('App.dmp_env_code', '') or os.environ.get('CONFIG_AGENT_CLIENT_CODE')
    response.set_cookie(
        name='dmp_env_code',
        value=env_code,
        domain=request.host,
        path='/',
        secure=False,
        http_only=False,
    )
    msg = components_service.auto_upgrade_components()
    if msg:
        logging.error('组件初始化或自动升级出现错误：' + msg)

    return True, '操作成功', 1


@api.admin_route.get('/service')
def service(request):
    """
    在线客服登录
    :return:
    """

    query_data = {
        'app_code': config.get('OnlineService.app_code') or '',
        'tenant_id': config.get('OnlineService.tenant_id') or '',
        'key': config.get('OnlineService.key') or '',
    }

    if not g.userid:
        raise UserError(message='未找到用户！')

    online_service_url = config.get('OnlineService.online_service_url')

    user = user_service.get_user(g.userid)
    query_data['user_name'] = user.name + "[" + g.code + '#' + request.host + "]"
    query_data['userguid'] = g.code + '_' + g.userid
    query_data['phone'] = user.mobile or ''

    # 参数连接顺序不可变动
    hash_str = (
        query_data['app_code']
        + query_data['tenant_id']
        + quote(query_data['user_name'])
        + query_data['phone']
        + query_data['key']
    )

    hash = sha1()
    hash.update(hash_str.encode('utf-8'))
    token = hash.hexdigest()

    del query_data['key']

    query_str = online_service_url + "?" + urlencode(OrderedDict(query_data)) + "&token=" + token

    return True, '操作成功', query_str


@api.admin_route.get('/register_clear_rundeck')
def schedule(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api    {get} /api/system/register_clear_rundeck 注册清空rundeck历史数据的定时任务调度
    @apiGroup    system
    @apiBodyParam {
        "id":"123",
        "name":"clear_rundeck_data",
        "status":"启用",
        "schedule{调度时间表达式}": "0 0 22 ? * TUE *",
        "command{执行命令}":"export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/clear_rundeck.py rundeck"
    }
    @apiResponse 200{
        "result": true,
        "msg": "ok"
    }
    **/
    """
    if not kwargs:
        kwargs = {"id": "clear_rundeck", "name": "clear_rundeck", "status": "启用", "schedule": "0 0 22 ? * TUE *"}
    command = "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/clear_rundeck.py rundeck"
    model = FlowModel(**kwargs)
    scheduler = RundeckScheduler(model)
    if scheduler.job_is_exists():
        scheduler.update_job(command)
    else:
        scheduler.add_job(command)
    return True, 'ok'


@api.admin_route.get('/get_system_setting_item')
def get_system_setting_item(**kwargs):
    """
    /**
    @apiVersion 1.0.4
    @api    {get} /api/system/get_system_setting_item 获取系统配置项
    @apiGroup    system
    @apiParam   query  {string}  category  分类：feed
    @apiParam   query  {string}  item  配置项：weixin_feed_switch
    @apiResponse 200{
        "result": true,
        "msg": "ok",
        "data":{
            "id":"39ec6845-5cb2-9444-fa15-584974907890",
            "created_on":"2019-03-07 11:08:32",
            "item":"weixin_feed_switch",
            "modified_on":"",
            "created_by":"admin",
            "category":"feed",
            "name": "简讯订阅开关",
            "description":"简讯订阅开关，1开启 0关闭",
            "modified_by":"admin",
            "value":"1"
        }
    }
    **/
    """
    return True, 'ok', system_setting_service.get_system_setting_item(kwargs.get("category"), kwargs.get("item"))


@api.admin_route.post('/update_system_setting')
def update_system_setting(request, **kwargs):
    """
    /**
    @apiVersion 1.0.4
    @api    {post} /api/system/update_system_setting 修改系统设置项
    @apiGroup    system
    @apiBodyParam  {
        "id":"39ec6845-5cb2-9444-fa15-584974907890",
        "item{系统配置项}":"weixin_feed_switch",
        "value{系统配置值}":"1",
        "name": "简讯订阅开关",
        "description{描述}":"简讯订阅开关，1开启 0关闭",
        "category{分类}":"feed"
    }
    @apiResponse    200 {
        "result": true,
        "msg": "ok",
        "data":"39ec6845-5cb2-9444-fa15-584974907890"
    }
    **/
    """
    model = SystemSetting(**kwargs)
    model.id = model.id or seq_id()
    old_setting = system_setting_service.get_system_setting_item(model.category, model.item)
    if old_setting:
        system_setting_service.update_system_setting(model)
        new_setting = system_setting_service.get_system_setting_item(model.category, model.item)
        # system_setting_service.clear_system_setting_cache()
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_system_setting',
                'id': kwargs.get('id'),
                'content': "系统设置 名称[{name}]，描述[{description}] 变更详情 {content}".format(
                    name=old_setting.get("name"),
                    description=old_setting.get("description"),
                    content=compare_dict(old_setting, new_setting, {"value": "开启关闭"}),
                ),
            },
        )
    else:
        system_setting_service.add_system_setting(model)
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'add_system_setting',
                'id': model.id,
                'content': f"新增system_setting, {kwargs}"
            },
        )

    #如修改集成平台，重新调用接口注册
    if model.category == 'ingrate_platform':
        register_api(g.code)
    return True, '修改成功', model.id


def _get_selected_db(dbname):
    dbname = dbname or 'data'
    allow_dbs = ('data', 'dmp', 'config')
    if dbname not in allow_dbs:
        raise UserError(400, 'db参数只支持:%s' % (','.join(allow_dbs)))

    db_conf = project.get_db_config()
    host = db_conf['host']
    port = int(db_conf['port'])
    user = db_conf['user']
    password = db_conf['password']
    database_name = db_conf['database']

    if dbname == 'data':
        db_conf = project.get_data_db_config()
        database_name = db_conf['database']

    elif dbname == 'config':
        host = config.get('DB.host')
        port = int(config.get('DB.port'))
        user = config.get('DB.user')
        password = config.get('DB.password')
        database_name = config.get('DB.database')

    return mysql_wrapper.get_mysql_source_db(host, port, database_name, user, password)


@api.admin_route.get('/db/trx')
def get_user_role(**kwargs):
    db = _get_selected_db(kwargs.get('db'))
    rows = []
    try:
        rows = db.query('SELECT * FROM information_schema.INNODB_TRX')
    finally:
        db.end()
    return True, 'ok', rows


@api.admin_route.get('/db/profiling')
def get_user_role(**kwargs):
    db = _get_selected_db(kwargs.get('db'))
    rows = []
    try:
        rows = db.query('SELECT id, db, command, time, state, info FROM INFORMATION_SCHEMA.PROCESSLIST')
    finally:
        db.end()
    return (True, 'ok', rows)


@api.admin_route.get('/db/kill')
def get_user_role(**kwargs):
    pids_input = kwargs.get('pids')
    if not pids_input:
        return False, '缺少pid参数', None

    pids = pids_input if isinstance(pids_input, (list, tuple)) else str(pids_input).split(',')
    sql_kill = ';'.join(['KILL %s' % pid for pid in pids])

    db = _get_selected_db(kwargs.get('db'))
    try:
        db.exec_sql(sql_kill)
    except Exception as e:
        return False, e.__str__(), None
    finally:
        db.end()

    return (True, 'ok', sql_kill)

@api.admin_route.post('/cache/analysis')
def handle_cache_analysis(**kwargs):
    """
    @api {post} /api/system/cache/analysis
    @apiGroup  dashboard
    @apiBodyParam {
        "db": "可选, 默认为配置db",
        "async": "可选, 默认为False",
        "target": "可选dataset|pattern|all, 默认为dataset",
        "exact": "可选, 默认False",
        "pattern": {
            "pattern": "string",
            "category": "mix|hash|string|list|set|zset",
            "desc": "",
        }
    }
    """
    db = kwargs.get('db', int(config.get('Redis.db')))
    target = kwargs.get('target', 'dataset')
    pattern = kwargs.get('pattern', None)
    is_async = kwargs.get('async', False)
    exact = kwargs.get('exact', False)

    if is_async:
        app_celery.async_analysis_cache.delay(db=db, target=target, pattern=pattern, exact=exact)
        return ''
    return True, '', analysis_service.analysis_cache(db=db, target=target, pattern=pattern, exact=exact)

@api.admin_route.get('/is_gray')
def handle_is_gray():
    return True, '', int(config.get('Grayscale.gray_env', 0))


@hug.post("/exec/{db}/sql")
def exec_db_sql(**kwargs):
    """"
    execute special db sql
    """
    from components.crypt import AESCrypt
    token = kwargs.get("token", "")

    crypt = AESCrypt(key=AES_DEFAULT_KEY)
    try:
        origin = crypt.decrypt(token)
        code, timestamp = origin.split(";")
        if not code:
            raise UserError(message="签名错误")
        g.code = code
        interval = int(timestamp) - int(time.time())
        if interval <= 0 or interval >= 800:
            raise UserError(message="签名错误")
    except Exception as e:
        raise UserError(message=str(e))

    db = _get_selected_db(kwargs.get('db'))
    sql = kwargs.get("sql")
    if not sql:
        raise UserError(message="缺少sql参数")
    row = 0
    error_rows = []
    try:
        for s in sql.strip("").strip(";").split(";"):
            try:
                row += db.exec_sql(s)
            except Exception as e:
                error_rows.append(str(e))
    except Exception as e:
        row = str(e)
    finally:
        db.end()
    return True, 'ok', {"effect_rows": row, "error_rows": error_rows}


@hug.post('/exec_sql/{db}/batch')
def exec_sql_batch(**kwargs):
    """
    执行sql, 多行sql
    """
    from components.crypt import AESCrypt

    token = kwargs.get("token", "")

    crypt = AESCrypt(key=AES_DEFAULT_KEY)
    try:
        origin = crypt.decrypt(token)
        code, timestamp = origin.split(";")
        if not code:
            raise UserError(message="签名错误")
        g.code = code
        interval = int(timestamp) - int(time.time())
        if interval <= 0 or interval >= 800:
            raise UserError(message="签名错误")
    except Exception as e:
        raise UserError(message=str(e))

    dbname = kwargs.get('db') or 'data'
    allow_dbs = ('data', 'dmp', 'config')
    if dbname not in allow_dbs:
        raise UserError(400, 'db参数只支持:%s' % (','.join(allow_dbs)))

    db_conf = project.get_db_config()
    database_name = db_conf['database']

    if dbname == 'data':
        db_conf = project.get_data_db_config()
        database_name = db_conf['database']

    elif dbname == 'config':
        database_name = config.get('DB.database')
    sql = kwargs.get("sql")
    if not sql:
        raise UserError(message="缺少sql参数")

    MYSQL_CONFIG = {
        "host": config.get('DB.host'),
        "port": int(config.get('DB.port')),
        "user": config.get('DB.user'),
        "password": config.get('DB.password'),
        "database": database_name
    }

    def get_db_cursor_v2():
        import pymysql
        from pymysql.constants import CLIENT

        cnx = pymysql.connect(
            user=MYSQL_CONFIG.get('user'),
            password=MYSQL_CONFIG.get("password"),
            host=MYSQL_CONFIG.get("host"),
            database=MYSQL_CONFIG.get("database"),
            client_flag=CLIENT.MULTI_STATEMENTS   # 批量执行
        )
        return cnx

    conn = get_db_cursor_v2()
    cursor = conn.cursor()
    row = 'suc'
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        row = str(e)
    finally:
        cursor.close()
    return True, 'ok', row


@data_api.data_route.get("/app/config")
def get_app_config(**kwargs):
    """
    获取app.config
    """
    key = kwargs.get("key", "")
    value = config.get(key)
    return True, "ok", value


@api.admin_route.get('/sql/adb_to_st')
def sql_adb_to_st(**kwargs):
    # 将adb语法转st语法
    if g.code != 'yktyadmin':
        return True, "ok", '当前账号没有执行权限'
    redis_cache = RedisCache("adb2st")
    result = redis_cache.get_data("result")
    if not result:
        app_celery.adb_2_st_sql.apply_async(kwargs=kwargs)
        # app_celery.adb_2_st_sql(**kwargs)
        return True, "ok", ''
    elif result and result.get("transform_count") == len(result.get("all_tenant")):
        redis_cache.delete("result")
        app_celery.adb_2_st_sql.apply_async(kwargs=kwargs)
        # app_celery.adb_2_st_sql(**kwargs)
        return True, "ok", ''
    else:
        return True, "ok", '有正在运行中的任务，等任务结束后再试'


@api.admin_route.get('/sql/adb_to_st/progress')
def sql_adb_to_st_progress(response):
    # 将adb语法转st语法
    from system.services import sql_adb_to_st
    return True, "ok", sql_adb_to_st.progress()


@api.admin_route.get('/rundeck_reset')
def rundeck_reset(**kwargs):
    # 将adb语法转st语法
    if g.code != 'yktyadmin':
        return True, "ok", '当前账号没有执行权限'
    from system.services import sql_adb_to_st
    tenant_code_str = kwargs.get("tenant_codes")
    tenant_codes = tenant_code_str
    if not isinstance(tenant_code_str, list):
        tenant_codes = tenant_code_str.split(',')
    return True, "ok", sql_adb_to_st.rundeck_rest(tenant_codes)


@api.admin_route.get('/zip_dataset_sql_transform')
def dataset_sql_transform(response, **kwargs):
    # 将adb语法转st语法
    redis_cache = RedisCache("adb2st")
    result = redis_cache.get_data("zip_result")
    if not result:
        app_celery.adb_2_st_sql_zip.apply_async(kwargs=kwargs)
        # app_celery.adb_2_st_sql(**kwargs)
        return True, "ok", ''
    elif result and result.get("run_state") != 'RUN':
        redis_cache.delete("zip_result")
        app_celery.adb_2_st_sql_zip.apply_async(kwargs=kwargs)
        # app_celery.adb_2_st_sql(**kwargs)
        return True, "ok", ''
    else:
        return True, "ok", '有正在运行中的任务，等任务结束后再试'


@api.admin_route.get('/zip_dataset_sql_transform/progress')
def dataset_sql_transform_progress(**kwargs):
    # 将adb语法转st语法
    from system.services import sql_transform_service
    return True, "ok", sql_transform_service.progress()


@api.admin_route.get('/dynamic_config/get')
def get_dynamic_config(**kwargs):
    config_key = kwargs.get("config_key")
    return dynamics_config.get(config_key)

@api.admin_route.get('/dynamic_config/all')
def get_dynamic_config_all():
    return dynamics_config.get_all()


@api.admin_route.get('/dynamic_config/set')
def set_dynamic_config(**kwargs):
    config_key = kwargs.get("config_key")
    config_value = kwargs.get("config_value")
    return dynamics_config.update_value(config_key, config_value)


@api.admin_route.get('/sync_tenant_status')
def sync_tenant_status():
    from components.common_service import sync_tenant_status
    return True, "ok", sync_tenant_status()


@api.admin_route.get('/get_develop_mode')
def get_develop_mode():
    return True, "ok", PublishCenterApi().get_develop_mode()


@api.admin_route.post('/set_develop_mode')
def set_develop_mode(**kwargs):
    return True, "ok", PublishCenterApi().set_develop_mode(kwargs.get('develop_mode'))


@api.admin_route.get('/fix_data')
def fix_data(**kwargs):
    report_id = kwargs.get('report_id')
    if not report_id:
        return False, '', '报告ID不能为空'
    return True, '已调整行高', system_setting_service.fix_data(report_id)


@api.admin_route.get('/create_token_url')
def create_token_url(**kwargs):
    url = kwargs.get('url')
    if not url:
        return False, '', 'url不能为空'
    return True, 'ok', get_token_url(url)


@api.route.get('/redirect')
def redirect_url(request, response, **kwargs):
    from app_menu.services.external_service import check_token_and_login
    url = check_token_and_login(request, response, **kwargs)
    hug.redirect.to(url)


if __name__ == "__main__":
    from components.crypt import AESCrypt
    key = AES_DEFAULT_KEY
    crypt = AESCrypt(key)
    token = crypt.encrypt(text=f"pmc;{int(time.time()+800)}")
    print("token:", token)




