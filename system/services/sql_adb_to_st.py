import concurrent.futures
import hashlib
import json
import re
import time
from threading import local
from dmplib.redis import RedisCache

import functools
import xlsxwriter
from dmplib import config
from dmplib.hug import g
from dmplib.hug.context import DBContext

from dmplib.saas.project import get_db

from loguru import logger
from base import repository
from base.enums import DatasetEditMode, DatasetType
from components.oss import OSSFileProxy
from components.storage_setting import get_storage_type
from dashboard_chart.utils.common import add_api_dataset_params
from data_source.models import DataSourceModel
from data_source.services.data_source_service import load_data_source_conn_str
from dataset.models import DatasetModel
from dataset.services import dataset_service, dataset_var_service
from dataset.services.dataset_service import get_dataset_tables_collection, get_dataset_service
from flow.services import flow_service
from system.services.sql_adb_to_st_common import redis_cache, request_rdb_to_st, shu_xin_login
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack

from gevent.pool import Pool


def handle_g(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        thread_local_g = _AppCtxGlobals()
        thread_local_g.code = kwargs.pop('code', None)
        thread_local_g.account = kwargs.pop('account', None)  # getattr(parent_thread_g, 'account', None)
        thread_local_g.userid = kwargs.pop('userid', None)  # getattr(parent_thread_g, 'userid', None)
        thread_local_g.cookie = kwargs.pop('cookie', None)  # getattr(parent_thread_g, 'cookie', None)
        thread_local_g.external_params = kwargs.pop('external_params', None)
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()

    return wrapper


def get_poll_num(num_str):
    try:
        return int(num_str)
    except Exception as e:
        return 10

def transform(**kwargs):
    result = redis_cache.get_data("result")
    if not result:
        tenant_code_str = kwargs.get("tenant_codes")
        poll_num = get_poll_num(kwargs.get("poll_num") or '10')
        is_replace = kwargs.get("replace") or False
        tenant_codes = tenant_code_str
        if not isinstance(tenant_code_str, list):
            tenant_codes = tenant_code_str.split(',')
        old_host = config.get("Transform.old_api_host")
        new_host = config.get("Transform.new_api_host")
        Adb2StServer(tenant_codes, old_host, new_host, is_replace, poll_num).transform()
    else:
        print("正在运行。。。。")
        return True


class Adb2StServer:

    def __init__(self, tenant_codes, old_host, new_host, is_replace, poll_num):
        self.pro_obj = {
            "all_tenant": tenant_codes,
            "count": len(tenant_codes),
            "transform_count": 0,
            "error": [],
            "info": {},
            "log_info_url": []
        }
        self.redis = RedisCache("adb2st")
        self.check_sr_time_count = 0
        self.adb2sr_time_count = 0
        self.tenant_codes = tenant_codes
        self.is_replace = is_replace
        self.old_host = old_host
        self.new_host = new_host
        self.all_table_name = []
        self.file = None
        self.worksheet_map = {}
        self.local_data = local()
        self.add_result_cache()
        self.pool = Pool(poll_num)

        self.cookie = getattr(g, 'cookie', None)

    def process_tenant(self, tenant, token):
        try:
            log_path = tenant + 'log.xlsx'
            self.file = xlsxwriter.Workbook(log_path)
            self.worksheet_map = {}
            # self.file = open(log_path, 'w')
            db_ctx = DBContext.instance(g)
            db_ctx.set_conn(None, '', None)
            setattr(g, "code", tenant)
            st_ds = get_api_rds_datasource(self.old_host, self.new_host)
            dataset = _get_dataset()
            self.pro_obj["info"][tenant] = {
                "count": len(dataset)
            }
            self.check_replace_dataset_sql(tenant, dataset, st_ds, token)
            # self.pro_obj["transform_count"] += 1
            # self.file.write('\n')
            # self.file.close()
            # self.add_result_cache()


        except Exception as e:
            logger.exception(str(e))
        finally:
            try:
                self.pro_obj["transform_count"] += 1
                self.pro_obj["error"].append(tenant)
                self.file.close()
                with open(log_path, "rb") as fd:
                    oss_file_url = OSSFileProxy().upload(fd, file_name="sql_adb_to_st/" + log_path, private=False)
                    self.pro_obj.get("log_info_url").append(oss_file_url)
            except Exception as e:
                logger.exception(str(e))
            finally:
                self.add_result_cache()
                # del self.local_data.redis

    def transform(self):
        try:
            token = shu_xin_login()
            self.init_adb_to_sr_cache()
        except Exception as e:
            self.pro_obj["transform_count"] = self.pro_obj["count"]
            self.pro_obj["error_msg"] = str(e)
            self.add_result_cache()
            logger.error(str(e))
            return
        for tenant in self.tenant_codes:
            self.process_tenant(tenant, token)

    def check_replace_dataset_sql(self, tenant, datasets, st_ds, token):
        @handle_g
        def process_dataset(self: Adb2StServer, ds, data_source_model_map, st_ds, tenant, token):
            setattr(g, "code", tenant)
            setattr(g, "cookie", self.cookie)
            content_str = ds.get("content")
            try:
                content = json.loads(content_str, strict=False)
            except Exception as e:
                try:
                    logger.info("json转换异常：" + str(e))
                    self.write_worksheet_tr_error("json_error", tenant, ds.get("id"),
                                                  ds.get("name"), content_str, str(e), '')
                except Exception as e:
                    pass
                return

            if content.get("data_source_id") and st_ds.get(content.get("data_source_id")):
                data_source_model = data_source_model_map.get(content.get("data_source_id"))
                if not data_source_model:
                    data_source = st_ds.get(content.get("data_source_id"))
                    data_source_model = DataSourceModel(**data_source)
                    load_data_source_conn_str(data_source_model)
                    data_source_model_map[content.get("data_source_id")] = data_source_model
                sql = content.get("sql")
                if content.get("transform_sr"):
                    self.write_worksheet_tr_error("is_tr_sr", tenant, ds.get("id"),
                                                  ds.get("name"), sql, '', '')
                    return
                if sql:
                    sql_hash = calculate_sha256(sql)
                    sr_sql, check_state = self.check_cache(sql, sql_hash, ds.get("id"))
                    if sr_sql and check_state in (1, '1'):
                        content["sql"] = sr_sql
                        content["transform_sr"] = True
                        with get_db() as db:
                            db.exec_sql("update dataset set content = %(content)s where id = %(id)s",
                                          {"content": json.dumps(content), 'id': ds.get("id")})
                        self.write_worksheet_tr_error("success", tenant, ds.get("id"),
                                                      ds.get("name"), sql, sr_sql, '')
                        return
                    if check_state in (2, '2'):
                        self.write_worksheet_tr_error("check_error", tenant, ds.get("id"),
                                                      ds.get("name"), sql, sr_sql, '')
                        return
                    if check_state in (3, '3'):
                        self.write_worksheet_tr_error("tr_error", tenant, ds.get("id"),
                                                      ds.get("name"), sql, '', '')
                        return
                    if not sr_sql:
                        start = time.time() * 1000
                        state, new_sql, replace_sql = request_rdb_to_st(sql, token)
                        end = time.time() * 1000
                        adb2sr_time = int(end - start)
                        self.adb2sr_time_count += (end - start)
                    else:
                        state = True
                        new_sql = sr_sql
                    if state:
                        content["sql"] = new_sql
                        content["transform_sr"] = True
                        ds["content"] = json.dumps(content, ensure_ascii=False)
                        start = time.time() * 1000
                        state, msg = check_sql(ds, data_source_model)
                        end = time.time() * 1000
                        check_sr_time = int(end - start)
                        self.check_sr_time_count += (end - start)
                        if state:
                            with get_db() as db:
                                db.exec_sql("update dataset set content = %(content)s where id = %(id)s",
                                              {"content": json.dumps(content), 'id': ds.get("id")})
                            if check_state:
                                self.update_sr_sql(sql_hash, new_sql, 1, ds.get("id"))
                            else:
                                self.save_sr_sql(sql_hash, sql, new_sql, 1, ds.get("id"), check_sr_time, adb2sr_time)
                            self.write_worksheet_tr_error("success", tenant, ds.get("id"),
                                                          ds.get("name"), sql, new_sql, msg or '')
                        else:
                            if check_state:
                                self.update_sr_sql(sql_hash, new_sql, 2, ds.get("id"))
                            else:
                                self.save_sr_sql(sql_hash, sql, new_sql, 2, ds.get("id"), check_sr_time, adb2sr_time or 0)
                            self.write_worksheet_tr_error("check_error", tenant, ds.get("id"),
                                                          ds.get("name"), sql, new_sql, msg or '')
                    else:
                        if not check_state:
                            self.save_sr_sql(sql_hash, sql, sr_sql, 3, ds.get("id"), 0, adb2sr_time)
                        self.write_worksheet_tr_error("tr_error", tenant, ds.get("id"),
                                                      ds.get("name"), sql, replace_sql or '', new_sql or '')
                else:
                    self.write_worksheet_tr_error("sql_empty", tenant, ds.get("id"),
                                                  ds.get("name"), '', '', '')
            else:
                self.write_worksheet_tr_error("other", tenant, ds.get("id"),
                                              ds.get("name"), '', '', '')

        data_source_model_map = {}
        for ds in datasets:
            self.pool.spawn(process_dataset, self, ds, data_source_model_map, st_ds, tenant, token)
        self.pool.join()

    def write_worksheet_tr_error(self, info_type, tenant, data_id, data_name, sql, new_sql, msg):
        count_key = info_type + "_count"
        table_key = info_type + "_tables"
        count = self.pro_obj["info"][tenant].get(count_key)
        if not count:
            count = 0;
            self.pro_obj["info"][tenant][count_key] = count
            self.pro_obj["info"][tenant][table_key] = []
        self.pro_obj["info"][tenant][count_key] = count + 1
        self.pro_obj["info"][tenant][table_key].append(data_name)
        self.add_result_cache()
        if not self.worksheet_map.get(info_type):
            self.worksheet_map[info_type] = self.file.add_worksheet(info_type)
        worksheet = self.worksheet_map.get(info_type)
        worksheet.write(count, 0, tenant)
        worksheet.write(count, 1, data_id)
        worksheet.write(count, 2, data_name)
        worksheet.write(count, 3, sql if len(sql) <= 20000 else sql[0:20000])
        worksheet.write(count, 4, new_sql if len(new_sql) <= 20000 else new_sql[0:20000])
        worksheet.write(count, 5, msg if len(msg) <= 5000 else msg[0:5000])

    def add_result_cache(self):
        self.pro_obj["info"]["check_sr_time_count"] = self.check_sr_time_count
        self.pro_obj["info"]["adb2sr_time_count"] = self.adb2sr_time_count
        self.redis.set("result", json.dumps(self.pro_obj, ensure_ascii=False), 3600 * 24)

    def check_cache(self, sql, sql_hash, dataset_id):
        query_sql = "select sr_sql,adb_sql,check_state from adb_to_sr_cache where sql_hash = %(sql_hash)s and dataset_id = %(dataset_id)s limit 1"
        with get_db("yktyadmin") as config_db:
            row = config_db.query_one(query_sql, params={"sql_hash": sql_hash, "dataset_id": dataset_id})
            if row and row.get("adb_sql") == sql:
                return row.get("sr_sql"), row.get("check_state")
            else:
                return None, None

    def save_sr_sql(self, sql_hash, adb_sql, sr_sql, check_state, dataset_id, check_sr_time, adb2sr_time):
        insert_sql = """
            insert into adb_to_sr_cache(sql_hash,adb_sql,sr_sql,check_state, dataset_id, check_sr_time, adb2sr_time, save_time) 
            values(%(sql_hash)s, %(adb_sql)s, %(sr_sql)s, %(check_state)s, %(dataset_id)s, %(check_sr_time)s, %(adb2sr_time)s, now())
        """
        with get_db("yktyadmin") as config_db:
            config_db.exec_sql(insert_sql,
                            params={"sql_hash": sql_hash, "adb_sql": adb_sql, "sr_sql": sr_sql,
                                    "check_state": check_state, "dataset_id": dataset_id,
                                    "check_sr_time": check_sr_time, "adb2sr_time": adb2sr_time})

    def update_sr_sql(self, sql_hash, sr_sql, check_state, dataset_id):
        update_sql = " update adb_to_sr_cache set check_state = %(check_state)s, save_time = now() "
        params = {"check_state": check_state}
        if sr_sql:
            update_sql += " ,sr_sql = %(sr_sql)s "
            params["sr_sql"] = sr_sql
        update_sql += " where dataset_id = %(dataset_id)s and sql_hash = %(sql_hash)s "
        params["dataset_id"] = dataset_id
        params["sql_hash"] = sql_hash
        with get_db("yktyadmin") as config_db:
            config_db.exec_sql(update_sql, params)

    def init_adb_to_sr_cache(self):
        delete_sql = """
            delete from adb_to_sr_cache where check_state = '3'
        """
        with get_db("yktyadmin") as config_db:
            config_db.exec_sql(delete_sql)



def get_api_rds_datasource(old_host, new_host):
    ds = _get_datasource()
    st_ds = {}
    for d in ds:
        conn_str = d.get("conn_str")
        conn = json.loads(conn_str)
        if conn and conn.get("host"):
            host = conn.get("host")
            if host.startswith(old_host):
                conn["host"] = new_host
                d["conn_str"] = json.dumps(conn, ensure_ascii=False)
                st_ds[d.get("id")] = d
                with get_db() as db:
                    db.exec_sql("update data_source set conn_str = %(conn_str)s where id = %(id)s",
                                  {"conn_str": d.get('conn_str'), 'id': d.get("id")})
    return st_ds


def run_get_data(model, data_source_model):
    """
    运行数据集，产生数据集数据和数据集字段
    :param dataset.models.DatasetModel model:
    :return:
    """
    # if model.type == DatasetType.Sql.value or model.type == DatasetType.Api.value:
    add_api_dataset_params(g, sql_from='testsql', dataset_id=model.id)
    g.datasource_type = data_source_model.type
    return get_dataset_service(model, data_source_model).run_get_data()


def get_rundeck_dataset():
    with get_db() as db:
        sql = """
            select id, `type`,modified_by ,created_by  from flow where status = '启用' and schedule is not null
            order by  modified_on desc
        """
        return db.query(sql, {})


def check_sql(dataset, data_source_model):
    setattr(g, "storage", get_storage_type(g.code))
    dataset_id = dataset.get("id")
    dataset['var_content'] = dataset_var_service.get_dataset_vars(dataset_id)
    if dataset['edit_mode'] == DatasetEditMode.Relation.value:
        dataset['relation_content'] = get_dataset_tables_collection(dataset_id)
        dataset['filter_content'] = (
                repository.get_data('dataset_filter', {"dataset_id": dataset_id}, multi_row=True) or []
        )
    try:
        data_source_model.conn_str.tenant_code = 'empty'
        run_result = run_get_data(DatasetModel(**dataset), data_source_model)
        field = run_result.get("field")
        origin_col_name_map = _field_to_map(field)
        old_filed = repository.get_data('dataset_field', {"dataset_id": dataset_id, "type": ["普通"]},
                                        multi_row=True) or []
        for f in old_filed:
            if f and f.get('origin_col_name') and not origin_col_name_map.get(f.get('origin_col_name').lower()):
                return False, f.get('origin_col_name') + ' 字段不存在'
    except Exception as e:
        logger.exception(e)
        return False, str(e)

    return True, ''


def _field_to_map(field):
    map = {}
    for key in field:
        fs = field.get(key)
        for f in fs:
            if f.get("origin_col_name"):
                map[f.get("origin_col_name").lower()] = f
    return map


def _get_dataset():
    with get_db() as db:
        sql = """
            select id,name,`type`,table_name ,edit_mode ,connect_type ,content  
            from dataset where `type` ='API' and edit_mode != 'relation'
        """
        return db.query(sql, {})


def _get_datasource():
    with get_db() as db:
        sql = """
            select * from data_source ds where `type` ='API'
        """
        return db.query(sql, {})


def replace_variable_field(sql):
    try:
        between_pat = re.compile(r'(between\s+\$\{[^\s]*?\})', re.IGNORECASE)
        eq_pat = re.compile(r'(=\s*\$\{[^\s]*?\})', re.IGNORECASE)
        in_pat = re.compile(r'(in\s+\$\{[^\s]*?\})', re.IGNORECASE)

        between_val_list = set(between_pat.findall(sql))
        in_pat_list = set(in_pat.findall(sql))
        eq_val_list = set(eq_pat.findall(sql))

        replace_value = {}

        for old_value in between_val_list:
            key = "= '" + old_value.replace(" ", "_") + "'"
            sql = sql.replace(old_value, key)
            replace_value[key] = old_value

        for old_value in in_pat_list:
            key = "= '" + old_value.replace(" ", "_") + "'"
            sql = sql.replace(old_value, key)
            replace_value[key] = old_value

        for old_value in eq_val_list:
            key = "= '" + old_value.replace(" ", "_") + "'"
            replace_value[key] = old_value
            sql = sql.replace(old_value, key)

        return sql, replace_value
    except Exception as e:
        print(str(e))
        return sql, {}


def replace_variable_value(sql, replace_value):
    for key in replace_value:
        sql = sql.replace(key, replace_value[key])
    return sql


def progress():
    return redis_cache.get_data("result")


def rundeck_rest(tenant_codes):
    from feed.services.dashboard_feeds_service import get_command
    queue_name_map = get_queue_name_map()
    for tenant in tenant_codes:
        db_ctx = DBContext.instance(g)
        db_ctx.set_conn(None, '', None)
        setattr(g, "code", tenant)
        try:
            rundeck_dataset = get_rundeck_dataset()
            print(len(rundeck_dataset))
            for rd in rundeck_dataset:
                try:
                    rd_type = rd.get("type")
                    queue_name = queue_name_map.get(rd.get("type"))
                    command = None
                    if rd_type and queue_name_map.get(rd.get("type")):
                        if rd_type == "订阅":
                            feed_type = get_feed_type(rd.get("id"))
                            if feed_type and feed_type == '简讯订阅':
                                command = get_command(rd.get("id"))
                                queue_name = None
                        setattr(g, "account", rd.get("modified_by") or rd.get("created_by"))
                        flow_service.enable_flow(rd.get("id"), command=command, queue_name=queue_name)
                        setattr(g, "account", "")
                except Exception as e:
                    setattr(g, "account", "")
                    logger.error("流程启动失败" + str(e))
        except Exception as e:
            setattr(g, "account", "")
            logger.error("rundeck_rest error：" + str(e))


def get_queue_name_map():
    queue_name_flow = config.get('RabbitMQ.queue_name_flow', 'Flow')
    # queue_name_flow_offline = config.get('RabbitMQ.queue_name_flow_offline', 'Flow-offline')
    queue_name_flow_feeds = config.get('RabbitMQ.queue_name_flow_feeds', 'Flow-Feeds')
    return {
        "数据清洗": queue_name_flow,
        "数据集": queue_name_flow,
        "用户同步": queue_name_flow,
        "数据集缓存": queue_name_flow,
        "用户引入": queue_name_flow,
        "订阅": queue_name_flow_feeds
    }


def get_feed_type(flow_id):
    with get_db() as db:
        sql = """
            select `type` from dashboard_email_subscribe des where id = %(id)s
        """
        return db.query_one(sql, {"id": flow_id})


def calculate_sha256(text):
    sha256_hash = hashlib.sha256()
    sha256_hash.update(text.encode('utf-8'))
    return sha256_hash.hexdigest()
