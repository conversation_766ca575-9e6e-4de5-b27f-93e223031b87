#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/3/2.
"""
# pylint:disable=R0201,W0123
import smtplib
import time

from rundeck.client import Rundeck

from app_celery import test_celery
from components.message_queue import RabbitMQ
from components.oss import OSSFileProxy
from components.oss_sts import OssStsFileProxy
from components.rundeck import get_rundeck_client
from data_source.models import ODPSConnStrModel
from data_source.services.data_source_client import ODPSDataSource
from dmplib import config
from dmplib.db.mysql_wrapper import get_db
from dmplib.redis import RedisCache  # noqa: E402
from dmplib.utils.errors import UserError


class EnvService:
    code = None

    def verify_env(self, code=None, item=None):
        """
        :param item: 检查特定项名称
        :param code:
        :验证私有环境是否正确
        :return tuple:
        """

        if code:
            self.code = code

        check_list = {
            'DB': 'get_db()',
            'OSS': 'self.check_oss()',
            # 这样写的原因是因为显示调用OssStsFile，防止没有引用被人（或工具）误以为没有引用被删除，下面rabbitmq同理。
            'OSS_STS': f'{OssStsFileProxy.__name__}().get_security_token()',
            'Email': 'self.check_email()',
            'Redis': "RedisCache().add('_test_connect','test',1)",
            'RabbitMQ': f"{RabbitMQ.__name__}().get_connection()",
            'Rundeck': "self.check_rundeck()"
        }

        check_result = []
        if item:
            if not check_list.get(item):
                raise UserError(404, '无效的检查项%s' % item)

            check_result.append(self.common_check_function(item, check_list.get(item)))
            return check_result

        for k, v in check_list.items():
            check_result.append(self.common_check_function(k, v))

        return check_result

    def common_check_function(self, module, test_function):

        '''
         通用检测方法
        :param dataset.models.OperateRecordQueryModel query_model:
        :return tuple:
        '''
        try:

            eval(test_function)
            return {module: 'ok'}

        except Exception as e:
            # print(module + str(e))
            return {module: ' Fail ! Exception msg:' + str(e)}

    def check_email(self):
        '''
         验证私有环境email 配置是否正确
        :return obj:
        '''

        server = config.get('Email.smtp_server')
        port = int(config.get('Email.smtp_port'))
        use_ssl = int(config.get('Email.smtp_enable_ssl')) == 1

        server = smtplib.SMTP_SSL(server, port, timeout=5) if use_ssl else smtplib.SMTP(server, port, timeout=5)

        server.login(config.get('Email.account'), config.get('Email.password'))

        return server

    def check_rundeck(self):
        '''
        验证私有环境 rundeck 配置是否正确
        :return tuple:
        '''
        rundeck = get_rundeck_client()

        return rundeck.system_info()

    def check_oss(self):
        '''
        验证私有环境 rundeck 配置是否正确
        :return tuple:
        '''
        oss = OSSFileProxy()
        return oss.object_list(root='/')

    def check_odps(self):
        '''
        验证私有环境 rundeck 配置是否正确
        :return tuple:
        '''

        where = ''
        err_msg = '未找到odps项目配置信息，'
        if self.code:
            err_msg = 'code= %s 在数据库里未找到odps项目配置信息，' % self.code
            where = " where code='" + self.code + "' "

        sql = (
            "select odps_proj AS project_name,odps_access_id AS access_id,odps_access_secret AS access_key ,code"
            " from project %s limit 1" % where
        )

        odps_config = get_db().query_one(sql)

        if not odps_config:
            raise UserError(message=err_msg)

        model = ODPSConnStrModel(**odps_config)
        return ODPSDataSource.test_connection(model)

    def check_celery(self):
        '''
        验证私有环境 celery 配置是否正确
        '''

        key = 'test_celery_key'

        test_celery.delay(key)

        time.sleep(1)

        redis_cache = RedisCache()

        if redis_cache.get(key):
            msg = redis_cache.get(key).decode()
            redis_cache.delete(key)
            return msg

        raise UserError(message='Celery server not running')

    def clean_rundeck(self):

        '''
        清理rundeck 历史数据
        '''

        print(config.get('Rundeck.server'))

        rundeck = get_rundeck_client()

        rundeck.get_project_history('dmp')

        return rundeck.system_info()
