import json
import os
import tempfile
import traceback
import urllib
import zipfile
from json import JSONDecodeError
from urllib.parse import unquote, urlparse

import requests
import xlsxwriter
from dmplib import config

from loguru import logger
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError

from components.oss import OSSFileProxy
from system.services.sql_adb_to_st_common import redis_cache, shu_xin_login, request_rdb_to_st

ADB_2_ST_SQL_PATH = '/api/sqlparser/adb2sr'
SHU_XIN_TOKEN_PATH = '/api/priv/user/login'



class SqlTransformService:

    def __init__(self, file_url):
        self.pro_obj = {
            "count": 0,
            "run_state": "RUN",
            "info": {},
            "log_info_url": []
        }
        self.tmp_upload_path = None
        self.file_name = None
        self.file_url = file_url
        self.file = None
        self.cache = RedisCache(key_prefix='parse_zip_file')
        self.transform_st = False
        self.old_host = config.get("Transform.old_api_host")
        self.new_host = config.get("Transform.new_api_host")
        self.tmp_upload_folder = "tmp_sql_transform_download"
        self.token = ""
        self.worksheet_map = {}
        self.is_replace = False
        if not os.path.exists(self.tmp_upload_folder):
            os.makedirs(self.tmp_upload_folder)

    def transform(self):
        result = redis_cache.get_data("zip_result")
        if result:
            print("正在运行。。。。")
            return True
        try:
            self.token = shu_xin_login()
            _url = urlparse(unquote(self.file_url))
            self.file_name = os.path.basename(_url.path)
            self.tmp_upload_path = os.path.join(self.tmp_upload_folder, self.file_name)
            export_data = self._download_zip()
            log_path = self.file_name + '_log.xlsx'
            self.file = xlsxwriter.Workbook(log_path)
            if export_data.get("datasets"):
                datasets = export_data.get("datasets")
                if not datasets:
                    self.pro_obj["info"] = '无数据集，执行完成'
                    return
                self.pro_obj["count"] = len(datasets)
                self.add_result_cache()
                for dataset in datasets:
                    self._transform_dataset(dataset)
            self._update_zip(export_data)
            self.file.close()
            with open(self.tmp_upload_path, "rb") as fd:
                new_zip = OSSFileProxy().upload(fd, file_name="tmp_sr/" + self.file_name, private=True)
                self.pro_obj["new_zip"] = new_zip
            with open(log_path, "rb") as fd:
                oss_file_url = OSSFileProxy().upload(fd, file_name="zip_sql_adb_to_st/" + log_path, private=True)
                self.pro_obj["log_info_url"] = oss_file_url
        except Exception as e:
            self.pro_obj["info"] = str(e)
            traceback.print_exc()
        finally:
            self.pro_obj["run_state"] = 'finished'
            self.add_result_cache()

    def _transform_dataset(self, dataset_all_obj):
        if self._transform_datasource(dataset_all_obj):
            dataset = dataset_all_obj.get("dataset")
            if not dataset:
                return
            content_str = dataset.get("content")
            content = {}
            try:
                content = json.loads(content_str, encoding='utf-8', strict=False)
            except Exception as e:
                logger.error("json转换失败" + str(e))
            sql = content.get("sql")
            if content.get("transform_sr"):
                self.write_worksheet_tr_error("is_tr_sr", dataset.get("id"),
                                              dataset.get("name"), sql, '', '')
                return
            if sql:
                state, new_sql, replace_sql = request_rdb_to_st(sql, self.token)
                if state:
                    content["sql"] = new_sql
                    content["transform_sr"] = True
                    dataset["content"] = json.dumps(content, ensure_ascii=False)
                    self.write_worksheet_tr_error("tr_success", dataset.get("id"),
                                                  dataset.get("name"), sql, new_sql, '')
                else:
                    content["transform_sr"] = False
                    self.write_worksheet_tr_error("tr_error", dataset.get("id"),
                                                  dataset.get("name"), sql, new_sql, '')
                    return False
            else:
                self.write_worksheet_tr_error("sql_empty", dataset.get("id"),
                                              dataset.get("name"), str(content_str), '', '')
        else:
            dataset = dataset_all_obj.get("dataset")
            if dataset:
                self.write_worksheet_tr_error("other", dataset.get("id"), dataset.get("name"), "", "", "")

    def _transform_datasource(self, dataset):
        try:
            if dataset.get("data_source"):
                data_source = dataset.get("data_source")
                conn_str = data_source.get("conn_str")
                if conn_str:
                    conn = json.loads(conn_str) or {}
                    if conn.get("host") and conn.get("host").startswith(self.old_host):
                        conn["host"] = self.new_host
                        data_source["conn_str"] = json.dumps(conn, ensure_ascii=False)
                        return True
        except Exception as e:
            print(str(e))
        return False

    def _update_zip(self, export_data):
        td, new_zip = tempfile.mkstemp(suffix=".zip", dir=self.tmp_upload_folder)
        with zipfile.ZipFile(self.tmp_upload_path, 'r') as zin, zipfile.ZipFile(new_zip, "w") as zou:
            for name in zin.infolist():
                if self.tmp_update_file_name and self.tmp_update_file_name == name.filename:
                    zou.writestr(name, json.dumps(export_data, ensure_ascii=False))
                else:
                    zou.writestr(name.filename, zin.read(name.filename))
        os.remove(self.tmp_upload_path)

        os.rename(new_zip, self.tmp_upload_path)

    def _download_zip(self):

        res = requests.get(self.file_url)
        with open(self.tmp_upload_path, 'wb') as f:
            f.write(res.content)

        with zipfile.ZipFile(self.tmp_upload_path, "r") as zip_file:
            for name in zip_file.namelist():
                with zip_file.open(name) as zfile:
                    r_data = zfile.read()
                    if r_data:
                        try:
                            self.tmp_update_file_name = name
                            export_data = json.loads(r_data.decode('utf-8', 'ignore'))
                        except JSONDecodeError:
                            raise UserError(400, "文件格式异常，json解析失败")
                        break

        if not export_data:
            raise UserError(400, '无效的数据文件格式')

        return export_data

    def write_worksheet_tr_error(self, info_type, data_id, data_name, sql, new_sql, msg):
        count_key = info_type + "_count"
        table_key = info_type + "_tables"
        count = self.pro_obj.get(count_key)
        if not count:
            count = 0;
            self.pro_obj[count_key] = count
            self.pro_obj[table_key] = []
        self.pro_obj[count_key] = count + 1
        self.pro_obj[table_key].append(data_name)
        self.add_result_cache()
        if not self.worksheet_map.get(info_type):
            self.worksheet_map[info_type] = self.file.add_worksheet(info_type)
        worksheet = self.worksheet_map.get(info_type)
        worksheet.write(count, 0, data_id)
        worksheet.write(count, 1, data_name)
        worksheet.write(count, 2, sql if len(sql) <= 20000 else sql[0:20000])
        worksheet.write(count, 3, new_sql if len(new_sql) <= 20000 else new_sql[0:20000])
        worksheet.write(count, 4, msg if len(msg) <= 5000 else msg[0:5000])

    def add_result_cache(self):
        redis_cache.set("zip_result", json.dumps(self.pro_obj, ensure_ascii=False), 3600 * 24)


def progress():
    return redis_cache.get_data("zip_result")