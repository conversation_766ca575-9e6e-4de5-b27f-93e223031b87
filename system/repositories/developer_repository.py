#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/8/1.
"""
from dmplib.db.mysql_wrapper import get_db as get_master_db
from dmplib.saas.project import get_db
from dmplib.constants import ADMIN_ROLE_ID


def get_project_list(query_model):
    """
    获取项目列表
    :param project.models.ProjectQueryModel query_model:
    :return: project.models.ProjectQueryModel
    """
    sql = 'SELECT `name`,`code`,`project_enabled`,`project_disable_reason` FROM `project` '
    params = {}
    wheres = ['is_rdc_auth = 1']
    if query_model.keyword:
        wheres.append('( `name` LIKE %(keyword)s OR `code` LIKE %(keyword)s)')
        params['keyword'] = '%' + query_model.keyword_escape + '%'
    sql += (' WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY created_on DESC'
    with get_master_db() as db:
        query_model.total = db.query_scalar('select count(*) as total from ({}) a'.format(sql), params)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_developer_list(tenant_code):
    with get_db(tenant_code) as db:
        sql = 'select a.`name` as developer_name, a.account as developer_code, a.email from `user` a' \
              ' left join user_user_role b on a.id = b.user_id' \
              ' where a.is_developer = 1 and a.email != "" and role_id = "{role_id}"'.format(role_id=ADMIN_ROLE_ID)
        result = db.query(sql)
        return result


def get_auth_developer_project(account):
    with get_master_db() as db:
        params = {'account': account}
        sql = """
        select b.name, a.code from developer_auth_user a 
        INNER JOIN project b on a.code = b.code and a.account = %(account)s and b.is_rdc_auth = 1;
        """
        return db.query(sql, params)


def get_developer_login_tenant(developer_account):
    with get_master_db() as db:
        params = {'account': developer_account}
        sql = """select a.developer_account,b.tenant_code,b.created_on from developer_auth_user a INNER JOIN developer_login_log b INNER JOIN project c 
        on a.developer_account = b.developer_account and a.`code` = c.`code` and a.`code` = b.tenant_code and a.account = %(account)s 
        and c.is_rdc_auth = 1 order by b.created_on desc limit 1"""
        return db.query_one(sql, params)
