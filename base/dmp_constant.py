#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
    created by yang<PERSON>@gmail.com 2018/1/4
"""

SELF_SERVICE_VIRTUAL_USER_ID = '********-0001-0001-0001-************'
SELF_SERVICE_VIRTUAL_USER_INFO = {
    "id": SELF_SERVICE_VIRTUAL_USER_ID,
    "name": "自助分析虚拟用户",
    "account": "",
    "mobile": "",
    "email": "",
    "group_id": [],
}

EXTERNAL_USER_ACCOUNT = 'external_user'

SYSTEM_TEMPLATE_DATASET_FOLDER_ID = '********-0000-0000-2222-********0000'

DEFAULT_USER_GROUP = '********-0000-0000-0000-********0000'
DEFAULT_USER_ROLE_GROUP = '********-0000-0000-0000-********0000'
DEFAULT_USER_ROLE_GROUP_PARENT_ID = '********-0000-0000-0000-************'

# 数据源列表中的数据集guid
DATA_SOURCE_DATASET_GUID = '5ac24c73-4710-11ee-a8bb-0c42a1e84220'


def get_config():
    import os
    # from loguru import logger
    from dmplib.config import Config

    # 要先获取config的正确路径
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'app.config')
    config = Config(config_file_path=config_path)
    return config


# 图表查询默认限制数量
def get_chart_query_default_limit():
    # https://www.tapd.cn/********/prong/stories/view/11********001337374?url_cache_key=from_url_iteration_list_7920e14ccdb579fc2d8232dd91bdacbe&action_entry_type=stories
    try:
        config = get_config()
        chart_limit = int(config.get('Function.chart_query_default_limit'))
        if chart_limit <= 10:
            chart_limit = 10
    except:
        # logger.error(f'加载chart_query_default_limit错误： {config.get("chart_query_default_limit")}, {str(e)}')
        chart_limit = 1500
    return chart_limit


# 图表返回的列限制数量
def get_return_chart_column_limit_num():
    # https://www.tapd.cn/********/prong/stories/view/11********001445417?url_cache_key=from_url_iteration_list_8b5a622eaf803e0409f2e8666c6ada7a&action_entry_type=stories
    try:
        config = get_config()
        limit_num = int(config.get('Function.chart_column_limit_num'))
        if limit_num <= 0:
            limit_num = 100
    except:
        limit_num = 100
    return limit_num


CHART_QUERY_DEFAULT_LIMIT = get_chart_query_default_limit()
CHART_COLUMN_LIMIT_NUM = get_return_chart_column_limit_num()
CHART_QUERY_UPPER_LIMIT = 20000
CHART_DEFAULT_PAGE_SIZE = 100
DATASET_MAX_SIZE = 100000
# 单图后N值设置标记特殊值
CHART_LAST_N_SPECIAL_FLAG = -9999
# 透视表查询限制最大值
EXCEL_TABLE_QUERY_LIMIT = 20000
EXCEL_TABLE_CELL_MAX_SIZE = 50000

# 默认数据权限
DEFAULT_DATA_PERMISSIONS = [
    {'data_type': data_type, 'data_action_code': ['view']}
    for data_type in ['data_source', 'dataset', 'dashboard', 'multiple_screen', 'application']
]

USER_ROLE_CACHE_KEY = 'dmp:user:role:cache_1:%s'
USER_OWN_CACHE_KEY = 'dmp:user:own:cache:%s|%s'
DATA_IS_FLODER_CACHE_KEY = 'dmp:data:is_floder:cache:%s'
DATA_IS_MULTIPLE_SCREEN_CACHE_KEY = 'dmp:data:multiple_screen:cache:%s'

PERMISSION_ACTIONS = {'view': '查看', 'edit': '编辑', 'copy': '复制', 'export': '下载'}

# 所有权限
ALL_DATA_ACTIONS = [{'action_code': 'view', 'action_name': '查看'}, {'action_code': 'edit', 'action_name': '编辑'}]

# 报告所有权限
ALL_REPORT_DATA_ACTIONS = ALL_DATA_ACTIONS + [
    {'action_code': 'copy', 'action_name': '复制'},
    {'action_code': 'export', 'action_name': '下载'},
]

ALL_REPORT_DATA_ACTIONS_DICT = {single.get('action_code'): single for single in ALL_REPORT_DATA_ACTIONS}

# SQL数据集直连模式缓存前缀
DIRECT_PREFIX = 'DIRECT_PREFIX'

# 内置ERP同步组织机构流程ID
ERP_SYNC_FLOW_ID = "********-1111-2222-5555-********0000"
# ERP组织机构表
ERP_USER_GROUP = "external_user_group"
# ERP岗位表
ERP_USER_ROLE = "external_user_role"
# ERP组织机构和岗位关联表
ERP_USER_GROUP_ROLE = "external_group_role"
# ERP用户表
ERP_USER = "external_user"
# ERP用户岗位关联表
ERP_USER_USER_ROLE = "external_user_user_role"

# 判断数据集是否有行列权限前缀
CHECK_HAS_ROLE_FILTER = "check_has_role_filter:"

DATASET_ROW_PERMISSION = "dataset_row_permission:"

DATASET_ROW_PERMISSION_KEYS = 'dataset_row_permission_keys:{dataset_id}'

# 元数据jsonschema校验器版本
SCHEMA_VERSION = "http://json-schema.org/draft-07/schema#"

# 数据集元数据缓存key前缀
DATASET_META_VERSION = "version"
DATASET_META = "dataset_meta"
DATASET_FIELD_META = "dataset_field_meta"
DATASET_LINK_DATA_META = "dataset_link_data_meta"
# 数据集多字段元数据缓存key前缀
DATASET_MULTI_FIELD = "multi_field_meta"

# 数据集结果数据缓存key前缀
DATASET_SQL_RESULT = "sql_result_"
DATASET_SQL_KEYS = "sql_keys_"
DATASET_SQL_KEYS_VISIT = "sql_keys_visit_"
DATASET_SQL_EXPIRE = "sql_keys_expire_"

# 报告编辑器缓存前缀
DASHBOARD_EDITOR_CACHE_PREFIX = 'dashboard_editor_'

# 报告编辑最后提交时间缓存
DASHBOARD_LAST_UPDATE_TIME_CACHE_KEY = 'dashboard_last_update_time'

# 数据源元数据缓存key前缀
DATA_SOURCE_META = "data_source:"

MOBILE = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad"]

# 数据集行列权限key
DATASET_ROW_PERMISSION_USER_ID = "dataset_row_permission_userid:"

# 报告锁定缓存过期秒数
DASHBOARD_LOCK_EXPIRE_SECONDS = 30 * 60

# 单图跨报告复制超时时间
CHART_COPY_TRANSACTION_TIME_OUT = 60 * 60

# 用户最后活跃时间
USER_LAST_ACTIVE = "user_last_active"

# 变量
DATASET_VARS_NAME = "变量"

# Rundeck发送通用消息的执行脚本Command_template
RUNDECK_PRODUCE_COMMON_MQ_COMMAND_TEMPLATE = (
    'export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/produce_common_mq.py --queue_name=%s --message=%s'
)

LOG_DASHBOARD_HEALTH = 'dmp_dashboard_health'
LOG_SUBJECT_INSPECT = 'dmp_subject_inspect'

# 主题数据集和API数据源全链路巡检的发送巡检结果MQ队列名
DATASET_SUBJECT_INSPECTION_RESULT_QUEUE_NAME = 'dmp_subject_inspection_result_{project_code}_{subject_id}'
DATASET_API_INSPECTION_RESULT_QUEUE_NAME = 'dmp_api_inspection_result_{project_code}_{subject_id}'
DATASET_SUBJECT_INSPECTIONS_QUEUE_NAME = 'proc_common_task'

# 外部主题数据集ID
EXTERNAL_SNOW_SUBJECT_DATASET_ID = '********-0000-0000-0000-************'
EXTERNAL_SNOW_SUBJECT_DATASET_NAME = '自助分析汇总主题'
EXTERNAL_MULTI_DIM_SUBJECT_DATASET_ID = '********-0000-0000-0000-********0002'
EXTERNAL_MULTI_DIM_SUBJECT_DATASET_NAME = '自助分析多维主题'

# 多维主题用来区分取数模式
EXTERNAL_CUBE_SUBJECT_COUNT_ALIAS = '6pfr3dgolkibgfd2eed1dfrfdede2'
EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX = '_rkc9hqmdw6zsp3vn5ilaytj4082bg'
EXTERNAL_CUBE_SUBJECT_DETAIL_ALIAS = 'nw7yvq51hrprrit2c5j7u2xw0ccfc'
EXTERNAL_SUBJECT_COMMON_CONDITION_ALIAS = 'idrtz4s6ewkaggqs2ychnufleaj7z'

# 外部主题数据集基础维度表临时表名称
EXTERNAL_SUBJECT_BASE_DIM_TEMP_TABLE_NAME = 'external_subject_dim_2pfr3dgolkibgfd2eed1dfrfdede2'

# 外部主题数据集事实临时表表前缀
EXTERNAL_SUBJECT_DETAIL_TEMP_TABLE_NAME_PREFIX = 'external_subject_detail_owe6re5dfee3efecs4aikg23hjyh74'

# 外部主题取数查询临时表前缀
EXTERNAL_SUBJECT_COUNT_TEMP_TABLE_NAME = 'external_count_iosu19s0ax841mf0x1931'
EXTERNAL_SUBJECT_SUBTOTAL_TEMP_TABLE_NAME = 'external_subtotal_5ktwix8w1boc3l0wbqo4j'

# 可作为过滤器数据集表关联个数限制
DATASET_FILTERABLE_TABLE_LIMIT = 5

# 云助手相关配置
YZS_SUITE_ID = '1001'
YZS_CHANNEL_ID = '10'
YZS_SYNC_DATA_PAGE_SIZE = 500
YZS_REPORT_THIRD_APP_CODE = 'shujianreport'
YZS_MSG_THIRD_APP_CODE = 'shujianmsg'
YZS_REPORT_APP_CODE = '8013'
YZS_MSG_APP_CODE = '8014'
YZS_API_ACCESS_TOKEN_CACHE_KEY = 'yzs_api_access_token'

# 当前环境所有简讯使用的调度数据集集合列表（环境级）
SUBSCRIBE_MSG_USED_DATASET_ID_SET_KEY = 'subscribe_msg_used_dataset_id_list'

# 配置库组件表灰度映射
component_table_map = {
    "component": "component_gray",
    "component_data_logic_type": "component_data_logic_type_gray",
    "component_init_log": "component_init_log_gray",
    "component_menu": "component_menu_gray"
}

function_table_map = {
    "function": "function_gray",
    "func_action": "func_action_gray",
    "`function`": "function_gray",
    "`func_action`": "func_action_gray"
}

# 接口管家的des 加密
MYSOFT_DES_KEY = 'My$oft_9'

# 删除报告时需要删除的表, 以报告id为条件
TABLES_FOR_DELETE_DASHBOARD = [
    # 报告信息
    "dashboard",
    # 报告扩展信息
    "dashboard_extra",
    # 多屏
    "screen_dashboard",
    # 单图及相关配置表
    "dashboard_chart",
    "dashboard_chart_dim",
    "dashboard_chart_num",
    "dashboard_chart_field_sort",
    "dashboard_chart_markline",
    "dashboard_chart_comparison",
    "dashboard_chart_desire",
    "dashboard_chart_filter",
    "dashboard_chart_filter_relation",
    # 报告级筛选
    "dashboard_filter",
    "dashboard_filter_relation",
    "dashboard_dataset_field_relation",
    # 穿透关联
    "dashboard_chart_penetrate_relation",
    # 跳转配置
    "dashboard_jump_config",
    "dashboard_jump_relation",
    # 变量跳转
    "dashboard_vars_jump_relation",
    # 固定值跳转
    "dashboard_fixed_var_jump_relation",
    # 筛选器跳转
    "dashboard_filter_chart_jump_relation",
    # 全局参数跳转
    "dashboard_global_params_jump_relation",
    # 全局参数
    "dashboard_jump_global_params",
    # 全局参数字段关系
    "dashboard_global_params_2_dataset_field_relation",
    # 全局参数变量关系
    "dashboard_global_params_2_dataset_vars_relation",
    # 全局参数筛选器关系
    "dashboard_global_params_2_filter_chart_relation",
    # 参数配置及参数跳转
    "dashboard_chart_params",
    "dashboard_chart_params_jump",
    # 新组件筛选
    "dashboard_filter_chart",
    "dashboard_filter_chart_relation",
    # 新联动
    "dashboard_linkage",
    "dashboard_linkage_relation",
    # 筛选默认值
    "dashboard_filter_chart_default_values",
    # 变量绑定
    "dashboard_dataset_vars_relation",
    # 报告巡检相关的三张表
    "healthy_dashboard",
    "healthy_dashboard_instance",
    "healthy_dashboard_result_node",
    # 元数据编辑历史
    "dashboard_metadata_history",
    # 变量及取值来源的绑定关系
    "dashboard_value_source",
    "dashboard_vars_value_source_relation",
    # 组件事件表
    "dashboard_chart_visible_triggers",
]

KEEP_LOGIN_DEFAULT_EXPIRE = 120 * 30 * 24 * 3600

# 组件data_logic_type_code缓存key
COMPONENT_LOGIC_CODE_KEY = "component_logic_code_cache"

# 配置库组件表灰度映射
component_table_map = {
    "component": "component_gray",
    "component_data_logic_type": "component_data_logic_type_gray",
    "component_init_log": "component_init_log_gray",
    "component_menu": "component_menu_gray"
}

function_table_map = {
    "function": "function_gray",
    "func_action": "func_action_gray"
}

# 新和旧跳转关系的所有表名
ALL_REDIRECT_TABLES = [
    'dashboard_global_params_jump_relation',
    'dashboard_filter_chart_jump_relation',
    'dashboard_fixed_var_jump_relation',
    'dashboard_vars_jump_relation',
    'dashboard_jump_relation',
    'dashboard_chart_params_jump',
]

# 旧跳转关系的所有表名
OLD_REDIRECT_TABLES = [
    'dashboard_fixed_var_jump_relation',
    'dashboard_vars_jump_relation',
    'dashboard_jump_relation',
    'dashboard_chart_params_jump',
]

# 关键字的标记
KEYWORD_FLAG = '"@{keyword_value}"'
FILTER_KEYWORD_PREFIX = '@keyword_value:'

# 一些接口排查用到的关键字
# 1. 运行时返回性能医生关键字的标记
__ANALYSIS_DOCTOR_KEYWORD__ = '__analysis__'
# 2. 性能医生返回SQL的标记
__ANALYSIS_DOCTOR_DISPLAY_SQL_KEYWORD__ = '__sql__'
# 3.调用栈分析的标记
__STACK_KEYWORD__ = '__stack__'

# 数芯数据集同步
PULSAR_DATASET_KEY = "{code}:pulsar_dataset"
# 额外的取数字段后缀
EXTRA_NUMS_SUFFIX = '_EXTRA_NUMS'

# 租户集成平台配置缓存key
# INGRATE_PLATFORM_CACHE_KEY = 'system_setting_tenant_ingrate_platform_config'
# 储存临时日志的key
REDIS_TEMPORARY_LOG_STORE_KEY = 'tmp_log_store_key'

# AES_DEFAULT_KEY
AES_DEFAULT_KEY = 'ac24awoz01qxc74o'

# 当前环境erp图片是否支持返回url
SUPPORT_ERP_IMAGE_URL = 'support_erp_image_url'