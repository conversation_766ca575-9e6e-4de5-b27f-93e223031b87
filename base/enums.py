#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    DMP所有枚举类型
    <NAME_EMAIL> on 2017/3/16.
"""
from enum import Enum, unique

@unique
class LoginAuthMode(Enum):
    Normal = 'normal'
    Email = 'email'

@unique
class UserChannel(Enum):
    """
    用户渠道
    """

    Erp = 1  # 主渠道
    Others = 2  # 非主渠道


@unique
class NodeCheckerCategory(Enum):
    DataSource = 'data_source'
    Dataset = 'dataset'


@unique
@unique
class MockField(Enum):
    """
    模拟dim 用于透视表没有维度的情况
    """

    Dim = 'xxxxx_mock_dim'


@unique
class DashboardTypeChartSelector(Enum):
    """
    单图关联行为
    """

    Link = 0


@unique
class DashboardTypeSelector(Enum):
    """
    单图关联行为
    """

    Global = 0
    Custom = 1


@unique
class FlowType(Enum):
    """
    流程类型
    """

    Collector = '数据采集'
    OperationCollector = '运营数据采集'
    Cleaning = '数据清洗'
    Dashboard = '数据看板'
    Label = '标签定义'
    Download = '数据下载'
    Dataset = '数据集'
    Feeds = '订阅'
    AsyncTask = '异步任务'
    UserSync = '用户同步'
    UserImport = '用户引入'
    RoleImport = '角色引入'
    Datawork = '数据开发'
    PdfExport = 'PDF导出'
    DatasetCache = '数据集缓存'
    DashboardSnap = '看板拍照'
    ManualFilling = '手工填报'
    CalcHDHeight = '计算HD组件高度'
    PulsarDataset = '数芯数据集'
    CheckDashboard = '检测报表是否正常'


@unique
class FlowStatus(Enum):
    """
    流程状态
    """

    Enable = '启用'
    Disable = '禁用'


@unique
class FlowNodeType(Enum):
    """
    流程节点类型
    """

    Collector = '采集'
    ODPS_SQL = 'ODPS_SQL'
    Sync = '同步'
    Label = '标签'
    Image = '360'
    Mapping = '映射'
    Chart = '单图'
    GeoCoder = '地理编码'
    MapGrid = '城市栅格'
    Organization = '组织架构'
    Download = '下载'
    Dataset = '数据集'
    EmailFeeds = '邮件订阅'
    MobileFeeds = '简讯订阅'
    REPLACEMENT = '数据替换'
    APPEND = '数据追加'
    ERPORG = 'ERP组织机构同步'
    UserSync = '用户同步'
    UserImport = '用户引入'
    UserRoleImport = '角色引入'
    UserOrgImport = '用户组织引入'
    PdfExport = 'PDF导出'
    DatasetCache = '数据集缓存'
    DatasetPreCache = '数据集预缓存'
    OperationCollector = '运营数据采集'
    DashboardSnap = '看板拍照'
    ManualFilling = '手工填报'
    CalcHDHeight = '计算HD组件高度'
    CheckDashboard = '检测报表是否正常'


@unique
class FlowInstanceStatus(Enum):
    """
    流程实例状态
    """

    NoBegin = '未开始'
    Created = '已创建'
    Running = '运行中'
    Successful = '已成功'
    Failed = '已失败'
    Aborted = '已中止'
    Ignored = '已忽略'


@unique
class DataSourceType(Enum):
    """
    数据源类型
    """

    ODPS = 'ODPS'
    Mysql = 'MySQL'
    MSSQL = 'MSSQL'
    DM = 'DM'
    SaaS = 'SaaS'
    MysoftERP = 'MysoftERP'
    API = 'API'
    DataHub = 'DataHub'
    PostgreSQL = 'PostgreSQL'
    HighData = 'HighData'
    ADS = 'ADS'
    Presto = 'Presto'
    MysoftNewERP = 'MysoftNewERP'
    MysoftShuXin = "MysoftShuXin"
    MysoftShuXin15 = "MysoftShuXin15"
    StarRocks = "StarRocks"


@unique
class MysoftNewERPConfigType(Enum):
    """
    MysoftNewERP类型数据源
    配置类型 1：手工录入，2：配置中心，3：三云api
    """

    MANUAL_INPUT = 1
    CONFIG_CENTER = 2
    CLOUD_MYSQL = 3


@unique
class MysoftNewERPDataBaseType(Enum):
    """
    MysoftNewERP 数据源的数据库类型
    """

    Oracle = 'oracle'
    DM = 'dmsql'
    Mysql = 'mysql'
    SQL_Server = 'sqlserver'
    Cloud_Mysql = 'cloudmysql'


@unique
class MysoftNewERPDataFromType(Enum):
    """
    MysoftNewERP数据源的数据来源类别
    """

    MYSOFT_ERP = 'MysoftErp'
    MYSOFT_SAAS = 'MysoftSaaS'
    NO_MYSOFT = 'NoMysoft'


@unique
class MysoftNewERPCloudType(Enum):
    """
    MysoftNewERP 三云数据源的链接方式
    三云链接方式，1：api，2：数据库，默认为1
    """

    CLOUD_API = 1
    CLOUD_DB = 2


@unique
class MysoftNewERPDataFromType(Enum):
    """
    MysoftNewERP数据源的数据来源类别
    """

    MYSOFT_ERP = 'MysoftErp'
    MYSOFT_SAAS = 'MysoftSaaS'
    NO_MYSOFT = 'NoMysoft'


@unique
class DataBaseType(Enum):
    """
    数据源类型
    """

    Oracle = 'Oracle'
    Mysql = 'MySQL'
    SQL_Server = 'SQL_Server'


@unique
class ErpDataType(Enum):
    """
    数据源类型
    """

    Oracle = 'oracle'
    Mysql = 'mysql'
    SQLServer = 'sqlserver'


@unique
class DataCollectMode(Enum):
    """
    数据采集类型
    """

    Incremental = '增量'
    CycleFull = '周期全量'
    OneFull = '一次全量'


@unique
class ChartSourceType(Enum):
    """
    单图数据源类型
    """

    Dataset = '数据集'
    Label = '标签'
    SQL = 'SQL'


@unique
class ColTypes(Enum):
    Dim = 'dim'
    Num = 'num'
    Comparison = 'comparison'
    Desire = 'desire'


@unique
class ChartNumFormulaMode(Enum):
    """
    单图数值计算方式
    """

    Count = 'count'
    Sum = 'sum'
    Avg = 'avg'
    Max = 'max'
    Min = 'min'
    Distinct = 'distinct'
    Ratio = 'ratio'
    GroupConcat = 'group_concat'


@unique
class ChartOffsetType(Enum):
    """
    单图偏移取数类型
    """

    Head = 'head'
    Tail = 'tail'


@unique
class DownloadHeaderDataSource(Enum):
    """
    下载头数据来源
    """

    # 行小计
    SubtotalRow = 'subtotal_row'
    # 其他(原始数据)
    Other = 'other'
    # 列序号
    SerialNumber = 'serial_number'


@unique
class SubtotalRowAliasPrefix(Enum):
    """
    行小计字段别名前缀
    """

    Row = 'subtotal_row'
    Summary = 'subtotal_row_summary'


@unique
class SubtotalRowTargetDataType(Enum):
    """
    行小计目标数据类型
    """

    Source = 'source'  # 原始数据
    SubtotalCol = 'subtotal_col'  # 列小计
    SubtotalColSummary = 'subtotal_col_summary'  # 列总计


@unique
class FieldSortFieldSource(Enum):
    """
    排序值来源类型
    """

    Dims = 'dims'
    Nums = 'nums'
    Comparisons = 'comparisons'
    Desires = 'desires'
    Zaxis = 'zaxis'


@unique
class SubtotalColAliasPrefix(Enum):
    """
    列小计字段别名前缀
    """

    Row = 'subtotal_col'
    Summary = 'subtotal_col_summary'


@unique
class ChartNumSubtotalFormulaMode(Enum):
    """
    单图数值小计计算方式
    """

    Count = 'count'
    Sum = 'sum'
    Avg = 'avg'
    Max = 'max'
    Min = 'min'
    Distinct = 'distinct'
    Auto = 'auto'
    No = ''
    GroupConcat = 'group_concat'


@unique
class DatasetVarValueSource(Enum):
    """
    变量值来源
    """

    Userdefined = 'userdefined'
    Cookie = 'cookie'
    Url = 'url'
    Context = 'context'


@unique
class ChartNumFormulaStringMode(Enum):
    """
    单图数值显示方式
    """

    Count = '计数'
    Sum = '求和'
    Avg = '平均值'
    Max = '最大值'
    Min = '最小值'
    Distinct = '去重计数'
    Year = '按年'
    Month = '按月'
    Day = '按天'
    Hour = '按时'
    Minute = '按分'
    Second = '按秒'


@unique
class ChartDimFormulaMode(Enum):
    """
    单图维度计算方式
    """

    Year = 'year'
    Month = 'month'
    Week = 'week'
    Quarter = 'quarter'
    Day = 'day'
    Hour = 'hour'
    Minute = 'minute'
    Second = 'second'


@unique
class ChartFilterOperator(Enum):
    """
    单图过滤器操作符
    """

    Gt = '>'
    Lt = '<'
    Eq = '='
    Gte = '>='
    Lte = '<='
    Neq = '!='
    Between = 'between'
    In = 'in'
    Notin = 'not in'
    Like = 'like'

    # 日期
    Far = 'far'
    FromYesterday = 'far_yesterday'
    FromWeek = 'from_week'
    FromMonth = 'from_month'
    FromQuarter = 'from_quarter'
    FromYear = 'from_year'


@unique
class SqlWhereOperator(Enum):
    """
    单图where条件所有操作符
    """

    Gt = '>'
    Lt = '<'
    Eq = '='
    Gte = '>='
    Lte = '<='
    Neq = '!='
    Like = 'LIKE'
    Between = 'BETWEEN'
    Notbetween = 'NOT BETWEEN'
    In = 'IN'
    Notin = 'NOT IN'
    IsNull = 'IS NULL'
    IsNotNull = 'IS NOT NULL'
    # 距今日
    FromDay = 'FAR'
    FromYesterday = 'FAR_YESTERDAY'
    FromWeek = 'FROM_WEEK'
    FromMonth = 'FROM_MONTH'
    FromQuarter = 'FROM_QUARTER'
    FromYear = 'FROM_YEAR'
    Nlike = 'NOT LIKE'


@unique
class SqlWhereDateType(Enum):
    Today = 'today'
    Yesterday = 'yesterday'
    Lastweek = 'lastweek'
    Lastmonth = 'lastmonth'
    Lastquarter = 'lastquarter'
    Lastyear = 'lastyear'


@unique
class SqlWhereDateTypePrefix(Enum):
    Today = 'today_'
    Week = 'week_'
    Month = 'month_'
    Quarter = 'quarter_'
    Year = 'year_'
    MonthDay = 'monthday_'
    WeekDay = 'weekday_'
    QuarterDay = 'quarterday_'
    YearDay = 'yearday_'


@unique
class SqlWhereDateTypeSymbol(Enum):
    Positive = 'positive'
    Nagative = 'nagative'
    Custom = 'custom'


@unique
class ChartMarklineMode(Enum):
    """
    单图辅助线模式mode值
    """

    FixedValue = '固定值'
    CalculatorValue = '计算值'


@unique
class ChartDesireMode(Enum):
    """
    单图目标值模式mode值
    """

    # 固定值
    FixedValue = 1
    # 计算值
    CalculatorValue = 0


@unique
class ChartMarklineFormulaMode(Enum):
    """
    单图辅助线计算方法值
    """

    Null = ''
    Max = 'max'
    Min = 'min'
    Avg = 'avg'
    Percentile = 'percentile'


@unique
class ChartCode(Enum):
    """
    单图类型
    """

    Pie = 'pie'
    NumericalValue = 'numerical_value'
    ClusterColumn = 'cluster_column'
    Scatter = 'scatter'
    Table = 'table'
    Treegrid = 'treegrid'
    Area = 'area'
    Radar = 'radar'
    Line = 'line'
    AreaMap = 'area_map'
    LabelMap = 'label_map'
    ScatterMap = 'scatter_map'
    HeatMap = 'heat_map'
    DoubleAxis = 'double_axis'
    CirclePie = 'circle_pie'
    CircleRosePie = 'circle_rose_pie'
    SimpleTab = 'simple_tab'

    Gauge = 'gauge'
    SplitGauge = 'split_gauge'
    Funnel = 'funnel'
    HorizonBar = 'horizon_bar'
    RosePie = 'rose_pie'
    StackBar = 'stack_bar'
    HorizonStackBar = 'horizon_stack_bar'
    StackArea = 'stack_area'
    StackLine = 'stack_line'
    FlowBar = 'flow_bar'
    TreeMap = 'treemap'
    LiquidFill = 'liquid_fill'
    Candlestick = 'candlestick'
    Roadline = 'roadline'
    HorizonBarRoll = 'horizon_bar_roll'
    ClusterColumnRoll = 'cluster_column_roll'
    StackBarRoll = 'stack_bar_roll'
    PaginationTable = 'pagination_table'
    PaginationTableYk = 'pagination_table_yk'
    ExcelTable = 'excel_table'


@unique
class ChartComponentCode(Enum):
    # 单图筛选控件
    CheckboxFilter = "checkbox_filter"
    TimeIntervalFilter = "time_interval_filter"
    NumberFilter = "number_filter"
    SelectFilter = "select_filter"
    Timeline = 'timeline'
    Tablist = "tablist"


@unique
class ChartSimpleCode(Enum):
    # 简单单图控件
    SimpleText = "simple_text"
    SimpleImage = "simple_image"


@unique
class ChartConditionType(Enum):
    """
    单图各种条件类型
    """

    # 单图内筛选
    InnerFilter = 1
    # 单图组件筛选
    ComponentFilter = 2
    # 单图联动筛选
    LinkageFilter = 3
    # 报告跳转筛选
    DashboardJumpFilter = 4
    # 报告级筛选
    DashboardFilter = 5
    # 单图穿透
    Penetrate = 6
    # 单图穿透筛选
    PenetrateFilter = 7
    # 标签筛选
    LabelFilter = 8
    # 钻取筛选
    DrillFilter = 9


@unique
class ChartPenetrateRelationType(Enum):
    """
    单图穿透关系类型
    """

    # 穿透
    PenetrateType = 0
    # 筛选、联动关联类型
    PenetrateFilterType = 1
    # 变量穿透关联类型
    VarPenetrateFilterType = 2


@unique
class MapChartGeog(Enum):
    """
    地址类单图的地理信息
    """

    Longitude = 'longitude'
    Latitude = 'latitude'
    Country = 'country'
    Province = 'province'
    City = 'city'
    District = 'district'
    Street = 'street'
    Level = 'level'


@unique
class IndicatorType(Enum):
    """
    指标类型
    """

    Description = '描述'
    Dimension = '维度'
    Number = '数值'
    Datetime = '日期'
    Address = '地址'


@unique
class LabelMode(Enum):
    Basic = '基础'
    Advanced = '高级'


@unique
class TemplateKeyTableName(Enum):
    """
    指标模板关键表
    """

    Master = 'fact_master'
    MasterToOrgan = 'fact_master_organization'
    Master360 = 'fact_360'
    LabelToMaster = 'label_360'


@unique
class DatasetType(Enum):
    """
    :todo 数据集类型配置
    :date 2017/6/14
    """

    Union = 'UNION'
    Sql = 'SQL'
    Excel = 'EXCEL'
    Label = 'LABEL'
    Folder = 'FOLDER'
    Template = 'TEMPLATE'
    Api = 'API'
    ExternalSubject = 'EXTERNAL_SUBJECT'
    Indicator = 'INDICATOR'

@unique
class UpgradeResult(Enum):
    """
    :升级结果
    """

    Jump = 0
    Success = 1
    Fail = -1

@unique
class DatasetConnectType(Enum):
    """
    : 数据集连接类型
    """

    Directly = '直连'
    Push = '推送'


@unique
class DatasetEditMode(Enum):
    """
    : 数据集编辑模式类型
    """

    Sql = 'sql'
    Relation = 'relation'


@unique
class DatasetFieldGroup(Enum):
    """
    数据集字段分组
    """

    Dimension = '维度'
    Measure = '度量'


@unique
class DatasetFieldDataType(Enum):
    """
    数据集字段数据类型
    """

    Description = '字符串'
    Datetime = '日期'
    Enumeration = '枚举'
    Number = '数值'
    Address = '地址'


@unique
class DatasetFieldType(Enum):
    """
    数据集字段类型
    """

    Normal = '普通'
    Customer = '普通高级'
    Calculate = '计算高级'
    Group = '分组字段'
    Indicator = '普通指标'
    CalculateIndicator = '计算指标'


@unique
class AnalysisMode(Enum):
    Mix = 'mix'
    OnlyDim = 'only_dim'
    OnlyNum = 'only_num'

@unique
class DatasetFieldOperator(Enum):
    """
    数据集字段操作符
    """

    Count = 'COUNT'
    Sum = 'SUM'
    Avg = 'AVG'
    Min = 'MIN'
    MinBy = "MIN_BY"
    Max = 'MAX'
    MaxBy = "MAX_BY"
    Plus = '+'
    Minus = '-'
    Multiply = '*'
    Divide = '/'
    LeftBracket = '('
    RightBracket = ')'
    Input = 'INPUT'
    TODAY = 'TODAY'
    DAYDIFF = 'DAY_DIFF'
    HOURDIFF = 'HOUR_DIFF'
    MINUTEDIFF = 'MINUTE_DIFF'
    Substr = 'SUBSTR'
    Replace = 'REPLACE'
    If = 'IF'
    Comma = ','
    Equal = '='
    Gt = '<'
    Lt = '>'
    LEQ = '<='
    GEQ = '>='
    NEQ = '<>'


@unique
class DatasetFileType(Enum):
    """
    数据集上传文件类型
    """

    Csv = 'csv'
    Txt = 'txt'
    Xls = 'xls'
    Xlsx = 'xlsx'


@unique
class DataReportType(Enum):
    """
    数据报告类型
    """

    File = 'FILE'
    Folder = 'FOLDER'
    CHILD_FILE = 'CHILD_FILE'


@unique
class DashboardType(Enum):
    """
    看板类型
    """

    File = 'FILE'
    Folder = 'FOLDER'
    CHILD_FILE = 'CHILD_FILE'


# @unique
# class DashboardGlobalParamsType(Enum):
#     """
#     看板类型
#     """
#
#     Text = '文本'
#     Date = '日期'


@unique
class OrderType(Enum):
    """
    排序类型
    """

    Asc = 'ASC'
    Desc = 'DESC'


@unique
class DashboardTypeStatus(Enum):
    """
    多屏类型
    """

    Draft = 0
    Release = 1


@unique
class DashboardStatus(Enum):
    """
    报告状态
    """

    Drafted = 0
    Releasing = -1
    Released = 1


@unique
class DashboardTypeAccessReleased(Enum):
    """
    查看报告的权限控制
    """

    NoLimited = 0
    Passwd = 1
    UserGroups = 2
    # 第三方可见
    ThirdParty = 3
    # 角色控制
    UserRole = 4


@unique
class ObjectAuthMode(Enum):
    """
    报告、门户的发布鉴权模式
    0: 租户的授权模式，默认模式（第三方和数见发布方式都可选）
    1: 租户的授权模式，基础数据的权限体系（只能选择第三方发布鉴权）
    """
    Default = 0
    ThirdMip = 1


@unique
class ApplicationTypeAccessReleased(Enum):
    """
    查看门户的权限控制
    """

    NoLimited = 0
    Passwd = 1
    UserGroups = 2
    # 第三方可见
    ThirdParty = 3
    # 角色控制
    UserRole = 4


@unique
class ApplicationTypeAccessReleasedSourceStr(Enum):
    """
    门户第三方的授权来源
    """

    ThirdParty = '3cloud'  # 第三方发布（三云）
    UserRole = 'shujian' # 数见方式发布
    MipAuth = 'mip'  # 基础数据平台发布方式
    DashboardMipAuth = 'dashboard_mip'   # 报表基础平台权限校验
    DashboardUserAuth = 'dashboard_user_auth'


@unique
class EmailTemplateType(Enum):
    """
    邮件模板类型
    """

    # 新增用户模板
    AddUser = 1
    # 重置密码模板
    ResetPassword = 2
    # 注册组件开发者用户模板
    RegisterDeveloper = 4
    # 新增域账号用户模板(没有修改密码链接)
    AddDomainUser = 5
    # 新用户初始化密码模板
    InitPassword = 8
    # 开发者申请模板
    InitDeveloper = 9
    # 开发者管理员授权码模板邮件
    AuthCodeDeveloper = 10
    # 开发者申请通过邮件模板
    SuccessDeveloper = 11


@unique
class OperateMode(Enum):
    """
    操作方式
    """

    Add = '新增'
    Update = '修改'
    Delete = '删除'
    Download = '下载'
    Append = '追加'
    Replace = '替换'
    Save_version = '保存版本'
    Delete_version = '删除版本'
    Restore_version = '还原版本'
    Replace_version = '应用版本'
    Sync_data = '数据同步'
    Update_flow = '修改调度'


@unique
class LogType(Enum):
    """
    日志对象类型 数据源、数据集、报告、应用、用户、系统
    """

    data_source = '数据源'
    dataset = '数据集'
    dashboard = '报告'
    application = '应用'
    user = '用户'
    system = '系统'

    @staticmethod
    def get_action_by_type(type_name):

        if type_name == 'data_source':
            return ['data_source']
        if type_name == 'dataset':
            return ['dataset']
        if type_name == 'dashboard':
            return ['view_dashboard']
        if type_name == 'user':
            return ['login', 'logout']
        if type_name == 'system':
            return ['system']
        if type_name == 'application':
            return ['application']

    @staticmethod
    def get_type_by_action(action):

        if action in ['data_source']:
            return LogType.data_source.value
        if action in ['dataset']:
            return LogType.dataset.value
        if action in ['view_dashboard', 'delete_dashboard']:
            return LogType.dashboard.value
        if action in ['login', 'logout']:
            return LogType.user.value
        if action in ['system']:
            return LogType.system.value
        if action in ['application']:
            return LogType.application.value


@unique
class LogAction(Enum):
    """
    日志对象类型 行为
    """

    login = '登录'
    logout = '退出登录'
    view_dashboard = '查看报告'


@unique
class AuthMethod(Enum):
    """
    认证方式
    """

    Apikey = 'apikey'
    Jwt = 'jwt'


@unique
class SyncMethod(Enum):
    """
    同步方式
    """

    Sync = '同步'
    NoSync = '不同步'


@unique
class ApiParamType(Enum):
    """
    api参数类型
    """

    Sys = 'sys'
    Query = 'query'


@unique
class ApiParamSysValue(Enum):
    """
    api参数系统key值
    """

    ProjectCode = 'project_code'
    BizCode = 'biz_code'
    UserAccount = 'user_account'
    PortalId = 'portal_id'
    SessionId = 'session_id'
    ExternalUserId = 'external_user_id'
    UserName = 'user_name'
    ScreenId = 'screen_id'
    UserId = 'user_id'
    ExtendYlParams = 'extend_yl_params'
    SuperportalUserId = "ug"
    SuperportalAccount = "uc"
    SuperportalCode = "oc"
    SnapId = 'snap_id'
    UserAuth = 'user_auth'
    DisableAuthCenter = 'disable_authcenter'
    ReportId = 'ri'


@unique
class DashboardPlatforms(Enum):
    """
    报告平台类型
    """

    PC = 'pc'
    TV = 'tv'
    Mobile = 'mobile'
    Mobile_New = 'mobile_screen'


@unique
class DashboardJumpType(Enum):
    """
    报告跳转类型
    """

    Dashboard = 'dashboard'
    Url = 'url'
    Child_file = 'childfile'
    SelfService = 'self_service'


class EmailSendType(Enum):
    """
    邮件订阅发送类型
    """

    # 发送一次
    One = 1
    # 定时发送
    Delayed = 2
    # 周期发送
    Cycle = 3


@unique
class SubscribeCycleType(Enum):
    """
    邮件订阅发送类型
    """
    # 月
    Month = 'month'
    # 周
    Week = 'week'
    # 天
    Day = 'day'


@unique
class DashboardComponentOperation(Enum):
    Add = 'add'
    Upgrade = 'upgrade'
    Rollback = 'rollback'


class MessageStatus(Enum):
    """
    消息状态类型
    """

    # 已删除
    Delete = 0
    # 正常
    Nornal = 1


@unique
class DataSourceOrigin(Enum):
    """
    前端字段类型
    """

    DataSet = 'dataSet'
    Manual = 'manual'
    No = 'none'


@unique
class AccountMode(Enum):
    """
    账户登录模式
    """

    DMP = 'DMP'
    DOMAIN = 'DOMAIN'
    ERP = 'ERP'
    IMPORT = 'IMPORT'
    MIX = 'MIX'
    SYNC = 'SYNC'
    IDM = 'IDM'


@unique
class AddMode(Enum):
    """
    账户添加模式
    """

    MANUAL = '手工'
    AUTO = '自动'


@unique
class DashboardTheme(Enum):
    """
    报告主题
    """

    TechBlue = 'tech_blue'
    ColorfulBlue = 'colorful_blue'
    ColorfulWhite = 'colorful_white'
    GradualBlue = 'gradual_blue'


@unique
class DatasetFieldClusterType(Enum):
    """
    数据集字段簇类值
    """

    Sale = 1
    Value = 2
    Land = 3


@unique
class DashboardDataMsgCode(Enum):
    """
    单图数据结果返回码
    """

    Successful = 1001  # 正常
    NullDashboardData = 4001  # 报告数据为空(没有符合条件的数据)
    NoPermissionData = 4003  # 当前用户或角色没有数据访问权限
    NullDatasetData = 5001  # 找不到数据集
    ErrorDatasetData = 5002  # 数据集异常
    OverDataLimit = 5003  # 超过限制数
    QueryException = 5004  # 取数查询异常
    OtherException = 5010  # 后端透传错误
    GetTotalException = 5020  # 获取总数异常

@unique
class DashboardSQLExecuteStatus(Enum):
    """
    单图数据sql执行结果返回码
    """

    NOExectue = 100  # 未执行
    InternalError = 500  # 服务器内部错误
    JSONFormatError = 501  # Json格式错误
    JSONToSQLError = 502  # Json解析Sql错误
    SQLExecuteError = 503  # Sql执行错误
    RequestTimeoutError = 504  # 请求接口超时
    ExecuteTimeoutError = 505  # sql执行超时
    DBConnectError = 506  # db连接错误


@unique
class DashboardGuideRecordStatus(Enum):
    """
    报告指引状态
    """

    Undone = 0
    Finish = 1


@unique
class DashboardFilterSelectType(Enum):
    """
    报告筛选选择类型
    """

    Normal = 0  # 正常
    SelectAll = 1  # 全选


@unique
class DashboardJumpTargetType(Enum):
    """ "
    报告跳转目标类型
    """

    Dashboard = 'dashboard'
    Url = 'url'


@unique
class DashboardDatasetFieldType(Enum):
    """ "
    报告字段类型  度量或维度
    """

    Dim = 0
    Num = 1


@unique
class JumpConfigSourceType(Enum):
    """ "
    报告跳转来源类型
    """

    Dim = 0  # 维度
    Num = 1  # 度量
    Title = 2  # 标题


@unique
class DashboardJumpConfigStatus(Enum):
    """ "
    跳转关系是否启用
    """

    Invalid = 0
    Valid = 1


@unique
class DataLogicTypeCode(Enum):
    """
    单图逻辑类型
    """

    Assist = 'assist'
    Default = 'default'
    Interval = 'interval'
    Nondataset = 'nondataset'
    Excel = 'excel'


@unique
class FieldValueType(Enum):
    """
    字段值类型
    """

    Column = 'column'
    String = 'string'
    Number = 'number'
    Null = 'null'
    Datetime = 'datetime'
    List = 'list'


@unique
class MetaDataVersionCheckFlag(Enum):
    """ "
    元数据缓存版本
    """

    Invalid = 0  # 校验不一致
    Valid = 1  # 校验一致


@unique
class FeedType(Enum):
    """
    订阅类型
    """

    Email = "邮件订阅"
    Mobile = "简讯订阅"


@unique
class GroupNumberType(Enum):
    """
    数值分组类型
    """

    Fixed = "fixed"
    Custom = "custom"


@unique
class GroupDatetimeType(Enum):
    """
    日期分组类型
    """

    Custom = "date_custom"
    Group_year = "group_year"


class DBEngine(Enum):
    """
    数据库 引擎
    """

    RDS = "rds"
    Mysql = "mysql"
    ADS = "ads"
    PG = "pg"
    MSSQL = 'mssql'
    DM = 'dmsql'
    ORACLE = 'oracle'
    Dremio = 'dremio'
    SqlServer = 'sqlserver'
    PRESTO = 'presto'


@unique
class DashboardLayoutType(Enum):
    """
    订阅类型
    """

    Stand = "标准布局"
    Auto = "自由布局"


@unique
class DashboardCreateType(Enum):
    """
    报告创建类型
    """

    Standard = 0
    Brandnew = 1


@unique
class HealthyCheckResultStatus(Enum):
    """
    报告巡检健康状态
    """

    Run = -2
    ToCheck = -1
    Healthy = 0
    Warning = 1
    UnHealthy = 2


@unique
class DashboardNewLayoutType(Enum):
    """
    报告布局类型
    """

    Standard = 0
    Constant = 1


@unique
class DatasetVarVarType(Enum):
    """
    : 数据集变量 变量类型
    :date 2019/03/29
    """

    Text = 1  # 文本
    Datetime = 2  # 日期
    Number = 3  # 数值


@unique
class DatasetVarValueType(Enum):
    """
    : 数据集变量 值类型
    :date 2019/03/29
    """

    List = 1  # 序列
    Value = 2  # 任意值
    Section = 3  # 区间


@unique
class DatasetFieldCompareType(Enum):
    """
    数据集字段校验类型
    """

    NormalField = '普通字段'
    AdvancedField = '高级引用字段'
    DashboardReference = '报告引用'
    DashboardFeed = '移动简讯'
    OpenAPIData = '数据开放API'
    USER_SYNC = '用户同步'
    DatasetVar = '变量'
    IndicatorField = '普通指标'
    CalculateIndicatorField = '计算指标'
    DatasetPermission = '权限数据集'
    ManualFilling = '数据填报'


class DatasetFieldCompareSubType(Enum):
    """
    数据集字段校验子类型
    """

    NormalField = '普通字段'
    AdvancedField = '高级字段'
    GroupField = '分组字段'
    IndicatorField = '普通指标'
    CalculateIndicatorField = '计算指标'

    DashboardChartDimension = '单图维度'
    DashboardChartNumber = '单图数值'
    DashBoardChartDesire = '单图目标值'
    DashboardChartMarkLine = '单图辅助线'
    DashboardChartParams = '单图参数'
    DashboardChartFilter = '单图筛选'
    DashBoardChartComparison = '透视表'

    DashboardChartPenetrate = '单图间穿透'
    DashboardChartSelector = '单图间联动'

    DashboardFilter = '报告级筛选'
    DashboardComponentFilter = '组件筛选'
    DashboardChartJumpRelation = '报告跳转'

    DashboardFeed = '报告移动简讯'
    MobileSubscribeFilter = '移动简讯过滤器'
    OpenData = '数据开放API'

    USER_SYNC = '用户同步'
    DatasetVar = '变量'
    DatasetPermission = '权限数据集'

    ManualFilling = '数据填报'


@unique
class DatasetVersionActionType(Enum):
    """
    数据集版本动作类型
    """

    Auto = '自动'
    Manual = '手动'
    Snap = '拍照'



@unique
class DatasetVersionType(Enum):
    """
    数据集版本类型
    """

    RC = '预发布版本'
    RELEASE = '正式版本'
    HISTORY = '历史版本'
    SNAP = '拍照版本'


@unique
class DatasetVersionNote(Enum):
    """
    数据集版本备注
    """

    Latest = '最新版本'
    Schedule = '调度执行'
    Update = '编辑保存'


@unique
class SubjectDataSourceType(Enum):
    """
    数据集版本备注
    """

    Subject = 'subject'
    Api = 'api'


@unique
class DatasetVersionStatus(Enum):
    """
    数据集版本巡检状态
    """

    Normal = '正常'
    Failure = '失败'
    CheckFailure = '巡检失败'


@unique
class DatasetBusinessType(Enum):
    """
    数据集业务类型
    """

    UserSync = '用户同步'


@unique
class FunctionIconType(Enum):
    """
    门户图标类型
    """

    System = 0  # 系统图标
    Custom = 1  # 自定义图标


@unique
class DatasetVarAccurateType(Enum):
    """
    : 数据集变量 日期精确类型
    :date 2019/03/29
    """

    NotAccurate = 0  # 不精确到秒
    Accurate = 1  # 精确到秒


@unique
class ScheduleParamType(Enum):
    """
    : 获取调度任务参数
    """

    Datawork = 0  # 大数据调度任务类型


@unique
class FlowStage(Enum):
    """
    流程阶段
    """

    # 草稿
    Draft = 0
    # 发布
    Release = 1
    # 历史
    History = 2


@unique
class DashboardTerminalType(Enum):
    """
    报告终端类型
    """

    Default = ''
    PC = 'pc_screen'
    Mobile = 'mobile_screen'
    Large = 'large_screen'


@unique
class InstallCheckDatasetStatus(Enum):
    """
    数据集安装前校验状态
    """

    # 无已存在数据集
    NotHave = 0
    # 有已存在数据集
    Have = 1


@unique
class DistributeType(Enum):
    """
    报告分发类型
    """

    Default = 0
    Distribute = 1


@unique
class DatasetInstallType(Enum):
    """
    数据集安装方式类型
    """

    Default = 0
    Replace = 1
    Negative = 2
    Copy = 3


@unique
class SubjectDatasetInspectionCheckItemResult(Enum):
    Normally = '正常'
    Failed = '失败'
    Warning = '警告'


@unique
class SubjectDatasetInspectionNodeType(Enum):
    DMPDataStorage = 'DMP数据存储'
    DMPDataDashboard = 'DMP数据报告'


@unique
class SubjectDatasetInspectionStatus(Enum):
    Normally = '正常'
    Error = '错误'
    Warning = '警告'
    Inspecting = '巡检中'


@unique
class TableDownloadFrom(Enum):
    """
    通用表格下载来源
    """

    Preview = 'preview'
    Released = 'released'


@unique
class AdvancedComputeMothed(Enum):
    """
    高级计算方法
    """

    Ratio = 'ratio'
    SameRatio = 'same_ratio'
    RingRatio = 'ring_ratio'

    @staticmethod
    def get_mothed_original_formula_mode(compute_mothed):
        formula_mode_map = {
            AdvancedComputeMothed.Ratio.value: ChartNumFormulaMode.Sum.value,
            AdvancedComputeMothed.SameRatio.value: ChartNumFormulaMode.Sum.value,
            AdvancedComputeMothed.RingRatio.value: ChartNumFormulaMode.Sum.value,
        }
        if compute_mothed in formula_mode_map:
            return formula_mode_map[compute_mothed]
        return compute_mothed


@unique
class InspectNodeNames(Enum):
    """
    巡检节点名称
    """

    DmpSendDataRequest = "DMP发送取数请求"
    BusinessDataPreparationNode = "业务数据准备"
    ApiDataDataCleaningNode = "API数据清洗"
    HighDataDataCleaningNode = "HighData数据清洗"
    HighDataDataPushNode = "HighData数据推送"
    DMPDataStorageNode = "DMP数据存储"
    DMPDataReportNode = "DMP数据报告"


@unique
class InspectStatus(Enum):
    """
    巡检状态
    """

    InspectNormally = "正常"
    Inspecting = "巡检中"
    InspectFailed = "错误"
    InspectWarning = "警告"


@unique
class InspectNodeStatus(Enum):
    """
    巡检状态
    """

    InspectNodeNormally = "正常"
    InspectNodeFailed = "失败"
    InspectNodeWarning = "警告"
    InspectNodeInspecting = "巡检中"


@unique
class InspectTeam(Enum):
    """
    巡检负责团队
    """

    DmpTeam = "云创DMP团队"
    YkTeam = "云客鹰眼实验室"
    HdTeam = "ERP HighData 团队"
    YxTeam = "一线"


@unique
class FixedValueType(Enum):
    """
    固定值筛选值类型
    """

    Datetime = "datetime"
    String = "string"
    Number = "number"


@unique
class ChartFilterInitiatorSouce(Enum):
    """
    筛选器组件筛选发起方值类型fixed_value
    """

    Datasetfield = "dataset_field"
    Fixedvalue = "fixed_value"


@unique
class ChartCopyTransactionStatus(Enum):
    """
    跨报告复制事务状态
    """

    Running = 0
    Succeed = 1


@unique
class ChartCopyTaskStatus(Enum):
    """
    跨报告复制任务状态
    """

    Waiting = -1
    Running = 0
    Succeed = 1
    Failed = 2
    Timeout = 3


@unique
class IndicatorWarningCompareType(Enum):
    """
    指标预警对比类型
    """

    Direct = 1
    Relative = 2


@unique
class IndicatorWarningCompareMethod(Enum):
    """
    指标预警对比方法
    """

    Dif = 1
    DifRatio = 2


@unique
class IndicatorWarningResultStatus(Enum):
    """
    指标预警结果状态
    """

    Waiting = 0
    Checking = 1
    Warning = 3
    Success = 2
    Error = 4
    Timeout = 5


@unique
class IndicatorWarningItemRelation(Enum):
    """
    指标预警结果状态
    """

    And = 1
    Or = 0


@unique
class IndicatorWarningTargetVersionType(Enum):
    """
    指标预警结果目标版本类型
    """

    Relative = 1
    Fixed = 2
    RelativeDay = 3


@unique
class LoginCode(Enum):
    """
    指标预警结果目标版本类型
    """

    InitPassword = 1


@unique
class SelfServiceReportTableCate(Enum):
    """
    自助报表表类型
    """

    Dim = "dim"
    Dwd = "dwd"
    Dws = "dws"
    Ads = "ads"


@unique
class DatasetPermissionType(Enum):
    """
    DMP数据集权限类型
    """

    Dataset = 'relate_permission_dataset'
    UserOrg = 'relate_user_organization'
    Api = 'relate_permission_api'


@unique
class DataCenterAction(Enum):
    """
    数据服务中心获取data set接口action 类型
    """

    DatasetGetData = 'DMPDataset/GetData'
    DMPDatasetExecuteSql = 'DMPDataset/ExecuteSql'


@unique
class DatasetStorageType(Enum):
    """
    拍照数据集存储方式
    """

    DatasetStorageOfLocal = 'local'
    DatasetStorageOfCloud = 'cloud'


@unique
class KeywordDataType(Enum):
    """
    : 关键字数据类型
    """

    Text = '文本'  # 文本
    Datetime = '日期'  # 日期
    Number = '数值'  # 数值


@unique
class KeywordType(Enum):
    """
    : 关键字数据类型
    """

    Program = 0  # 程序关键字
    Sql = 1  # SQL关键字


@unique
class HdUpgradeModuleList(Enum):
    """
    HighData数据升级模块标识
    """

    ALL = 'all'
    DATASOURCE_KEYWORD = 'datasource_keyword'
    DATASET = 'dataset'
    DASHBOARD = 'dashboard'
    M_REPORT = 'm_report'
    ROLE = 'role'
    MESSAGE = 'message'


@unique
class HdUpgradeModuleNameList(Enum):
    """
    HighData数据升级模块名称
    """

    ALL = '全量升级'
    DATASOURCE_KEYWORD = '数据源、关键字'
    DATASET = '数据集'
    DASHBOARD = '仪表板'
    M_REPORT = '移动报表'
    ROLE = '角色权限'
    MESSAGE = '简讯'


@unique
class SqlComplexOrNot(Enum):
    """
    是否是复杂sql
    """

    Unknown = 0
    Sample = 1
    Complex = 2


@unique
class SqlNeedProcedure(Enum):
    """
    是否是复杂sql
    """

    Unknown = 0
    NeedProcedure = 1
    NotProcedure = 2


@unique
class ChartDataMode(Enum):
    """
    指标预警结果目标版本类型
    """

    ManualInput = "manual_input"
    Dataset = "dataset"


@unique
class TenantEnvConfig(Enum):
    """
    租户级环境配置
    """

    HD = 'hd'
    SHUJIAN = 'shujian'
    CLOUD = 'cloud'
    DMP = 'dmp'


@unique
class ApplicationType(Enum):
    """
    报告类型
    """

    Dashboard = 0
    SelfService = 1
    HD_Dashboard = 3
    External_Dashboard = 4
    ActiveReport = 5
    SimpleReport = 6
    FineReport = 7   # 帆软报表
    LargeScreen = 8


@unique
class PulsarModelCatagory(Enum):
    """
    模型类别
    """
    DIM = "dim"
    DWD = "dwd"
    DWS = "dws"
    ADS = "ads"
    MultiDim = "multi_dim"


@unique
class ExternalSubjectType(Enum):
    Summary = "EXTERNAL_SUMMARY_SUBJECT"
    MultiDim = "EXTERNAL_MULTI_DIM_SUBJECT"

@unique
class DashboardAnalysisType(Enum):
    """
    模型类别
    """
    Summary = "EXTERNAL_SUMMARY_SUBJECT"
    MultiDim = "EXTERNAL_MULTI_DIM_SUBJECT"
    Dataset = "DATASET"
    SummaryDetail = "EXTERNAL_SUMMARY_SUBJECT_DETAIL"
    MultiDimDetail = "EXTERNAL_MULTI_DIM_SUBJECT_DETAIL"


@unique
class OperationFlowType(Enum):
    """
    运营数据采集
    """

    START = 1
    STOP = 2


@unique
class SnapshotStatus(Enum):
    """
    拍照状态
    """
    INIT = 0
    SUCCESS = 1
    FAIL = 2
    DELETED = 3
    RUNNING = 4


@unique
class DashboardSnapshot(Enum):
    """
    报告拍照
    """
    ON = 1
    OFF = 0


@unique
class DashboardSnapshotSchedule(Enum):
    """
    报告拍照调度周期类型
    """
    YEAR = 'year'
    QUARTER = 'quarter'
    MONTH = 'month'
    WEEK = 'week'


@unique
class SnapshotType(Enum):
    """
    拍照类型
    """
    FEED = '简讯'
    Dashboard = '报表'
    Application = '门户'


@unique
class SubscribeSendDetailLogStatus(Enum):
    """
    简讯用户渠道消息发送状态
    """
    RUNNING = 0
    SUCCESS = 1
    FAIL = 2


@unique
class SubscribeDetailStatus(Enum):
    """
    简讯发送详情的状态
    """
    Created = '已创建'
    Running = '运行中'
    Successful = '已成功'
    Failed = '已失败'
    FailedPart = '部分失败'


@unique
class UserSourceType(Enum):
    API = 'api'
    AD = 'ad'
    DATASET = 'dataset'


@unique
class PullDataType(Enum):
    USER = 'user'
    DEPARTMENT = 'department'


@unique
class ThirdPartyAppCode(Enum):
    YK = '6000'  # 云客
    YKJ = '4000'  # 云空间
    YL = '5000'   # 云链
    YL_KF = '5100'  # 云链智慧客服公共组（消息发送应用）
    QYWX = '0'    # 企业微信
    YZS = '1'     # 云助手_旧
    YZS_NEW = '11'  # 云助手 新集成
    YZS_BUILTIN = '111'  # 云助手 基础应用（云助手系统内置）
    DD = '2'       # 钉钉
    System = '10'  # 站内消息
    SuperWork = '3'  # 超级工作台
    MYCYJG = '4'  # 明源产业建管
    JKGJ = '5'  # 明源接口管家
    KF_APP = '1000'  # 智慧客服APP
    KF_WEWORK = '1001'  # 智慧客服企微通
    ZHYQ_WEWORK = '1002'  # 智慧园区企业微信集成

@unique
class DatasetPermissionModel(Enum):
    Alone = "alone"
    Role = "role"


@unique
class SubscribeSendUserFrom(Enum):
    """
    简讯关联用户的来源类型
    """
    PERSONAL = 0
    ROLE = 1


@unique
class SubscribeSendEditorMode(Enum):
    """
    简讯用户渠道消息发送状态
    """
    HTML = 1
    JSON = 2


@unique
class AddFuncType(Enum):
    """
    增值模块类型
    """
    Ppt = 'ppt'
    ActiveReport = 'active_report'
    Dashboard = 'dashboard'
    ERPReport = 'erp_report'


@unique
class ProjectValueAddedFunc(Enum):
    """
    租户的增值功能，在线报告，明细报告等
    """
    PPT = 'ppt'
    ACTIVE_REPORT = 'active_reports'
    SELF_SERVICE = 'self-service'
    REPORT_CENTER = 'report_center'


@unique
class ImportFileType(Enum):
    """
    导入包类型，zip或报表1.0
    """
    ZIP = 'zip'
    RPTX = 'rptx'


@unique
class IndicatorModelOpt(Enum):
    """
    指标回调操作类型
    """
    IndicatorRelease = "indicator_release"
    IndicatorOffline = "indicator_offline"
    ClassUpdate = "class_update"
    ClassDel = 'class_del'
    All = "all"
    ModelRelease = "model_release"
    ModelOffline = "model_offline"


@unique
class LoginFrom(Enum):
    """
    登录来源
    """
    DashboardSso = "dashboard_sso"
    SuperPortal = "super_portal"
    SuppApp = "super_app"
    ZHYQ = "zhyq_app"
    ERPPortal = "erp_portal"  # 新的erp集成们户标识
    ERPDashboard = "erp_dashboard"  # 新的erp集成报告标识，还未实现
    ERPOldDashboard = "erpdashbordapi"  # 老的菜单集成门户标识，场景： 菜单栏直接点击打开数见报表
    ERPOldPortalList = "erpportallist"  # 老的集成门户标识, 场景：erp右上角点击跳转数见管理方式登录点击跳转有数见权限的第一个门户
    ERPOldRedirectWay = "erpredirect"  # 老的erp跳转


@unique
class IngrateAction(Enum):
    """
    集成平台接口调用类型
    """
    Register = "register"   # 接口注册到集成平台
    Call = "call"       # 通过集成平台调用注册到集成平台的接口


@unique
class EntryAction(Enum):
    """
    超级APP进入数见操作类型
    """
    Link = "link"  # 连接
    Scan = "scan"  # 扫码


@unique
class TokenAuthFrom(Enum):
    """
    权限来源
    """
    ThirdCloudAuth = "third_cloud_auth"  # 来源于三云
    MipAuth = "mip_auth"  # 来源于基础数据平台授权


@unique
class ThirdFuncFilterType(Enum):
    """
    第三方门户菜单过滤类型
    """
    MipAuthWay = "mip_auth_way" # 来源于基础数据平台门户授权形式
    MultiCloudWay = "multi_cloud_way" # 来源于多云门户集成形式


@unique
class ExportImportBizType(Enum):
    """
    导出导入、分发支持的功能
    """
    DASHBOARD = 'dashboard'
    DATASET = 'dataset'
    APPLICATION = 'application'
    FILLING = 'filling'
    SELF_SERVICE = 'self-service'
    PPT = 'ppt'
    ACTIVE_REPORT = 'active_reports'


@unique
class ExternalDatasetType(Enum):
    """
    外部数据集类型
    """
    PulsarIndicator = "pulsar_indicator"
    PulsarDim = "dim"
    PulsarDWD = "dwd"


@unique
class PulsarCommonCacheType(Enum):
    """
    数芯数据集缓存类型
    """
    Indicator = "indicator"
    Detail = "detail"


@unique
class PulsarFieldType(Enum):
    """
    数芯数据集字段类型
    """
    Common = "common"
    Dynamic = "dynamic"


@unique
class PulsarQueryMode(Enum):
    """
    数芯取数模型
    """
    AGG = 'Agg'
    NOQUERYMODE = 'NoQueryMode'
    COUNT = 'Count'
    DETAIL = 'Detail'
    DETAILCOUNT = 'DetailCount'
    SQLCOUNT = 'Sql'


class DataQueryMode(Enum):
    """
    取数的模式
    将明细和列总计拆开
    """
    DETAIL = 'detail'  # 明细+列小计+行小计
    SUMMARY = 'summary' # 列总计
    ORIGIN = 'origin'  # 原来的逻辑，明细+列小计+行小计+列总计


@unique
class ThirdPartyAppServiceType(Enum):
    """
    简讯发送的实现模式 0=>应用模式;1=>服务商模式
    """
    APP = 0  # 应用模式
    SERVICE = 1  # 服务商模式


@unique
class ThirdPartyMsgSendType(Enum):
    """
    简讯发送模式，判断是否为云空间发送模式（非数见用户模式）
    0=> 数见用户；1=> 非数见用户
    """
    SHU_JIAN_USER = 0  # 数见用户
    NOT_SHU_JIAN_USER = 1  # 非数见用户


@unique
class ErpApiId(Enum):
    OP = 'e2695c3e-3b0a-11ed-a6d0-02420a0c0002'
    SAAS = '565c75fe-3b0c-11ed-a6d0-02420a0c0002'


@unique
class ErpApiType(Enum):
    OP = 'op'
    SAAS = 'saas'


@unique
class PulsarSyncMode(Enum):
    # 指标和明细
    ALL = "all"
    # 明细
    DIM = 'dim'
    DWD = 'dwd'
    DWS = 'dws'
    # 指标
    INDICATOR = 'indicator'


@unique
class DatasetQueryType(Enum):

    Api = "api数据集"
    Indicator = "数芯数据集"
    DataCenter = "数据服务中心数据集"


@unique
class QueryDataError(Enum):

    AnyError = 10000
    ExternalError = 10001
    OtherError = 10002
    FusingError = 10003
    TimeoutError = 10004
    SqlError = 10005


@unique
class Notify(Enum):
    All = 1
    Developer = 2
    BusinessPersonnel = 3


@unique
class TemplateDataPublishWay(Enum):
    THIRD = 'THIRD_PARTY'
    DMP = 'DMP'


@unique
class MoveDashboardAsLargeScreenStatus(Enum):
    Init = 0
    Progressing = 1
    Success = 2
    Failed = 3


class FunctionReportType(Enum):
    #0：数见，1：HighData，2：老移动报表, 3: 统计报表，4: 报表中心
    #5: erp报表
    #6: 酷炫大屏
    DASHBOARD = 0
    HIGHDATA = 1
    ACTIVE_REPORT = 3
    COMPLICATE_REPORT = 4
    ERP_REPORT = 5
    LargeScreen = 6


class ApplicationReportType(Enum):
    """单门户挂接的报表类型"""
    DASHBOARD = 0
    EXTERNAL_URL = 1
    LargeScreen = 2


@unique
class CopyDashboardType(Enum):
    """
    复制仪表的类型
    """
    CHILD_TO_CHILD = 'child_to_child'
    CHILD_TO_FILE = 'child_to_file'


@unique
class ProjectAction(Enum):
    """
    租户操作
    租户操作场景。init：开租户，enable：租户启用，disable：租户禁用
    """
    Init = 'init'
    Enable = 'enable'
    Disable = 'disable'

@unique
class MysoftShuXinTableType(Enum):
    """
    数芯元数据表类型
    """
    DIM = '维度表'
    DWD = '明细事实表'
    DWS = '汇总事实表'
    DWS_VIEW = '汇总视图事实表'
    ODS = '数据采集表'

@unique
class MysoftShuXin15QueryType(Enum):
    """
    数芯1.5查询接口类型 0: 填充query_structure参数, 1: 填充query_sql参数, 2:填充query_indicator参数
    """
    query_structure = 0
    query_sql = 1
    query_indicator = 2


@unique
class MysoftShuXin15UpgradeModuleNameList(Enum):
    """
    MysoftShuXin15数据升级模块名称
    """

    ALL = '全量升级'
    report_check_chart_before = '升级前报表取数检测'
    check_chart_before = '升级前组件取数检测'
    datasource = '创建数据源'
    import_dashboard = '导入覆盖看板'
    portal_upgrade = '门户替换'
    dataset_upgrade = '数据集升级'
    keyword = '处理关键字'
    check_chart_after = '升级后组件取数检测'
    report_check_chart_after = '升级后报表取数检测'
    role_folder = '目录权限补充'
    restart_check_chart = '重跑超时的取数检测'


@unique
class SkylineApps(Enum):

    DMP = 'dmp'
    #集成平台
    IPAAS = 'ipaas'
    #超级工作台门户站点
    WORKBENCH_PORTAL = 'workbench.protal'
    #超级工作台后台
    WORKBENCH = 'workbench'
    #通行证服务
    WORKBENCH_AUTH = 'workbench.auth'
    #运营平台开户
    OMP_TENANT = 'tenantProxy'
    #数芯
    DAP = 'dap'
    #套打
    PUBSERVICE_PRINT = 'pubservice.print'
    #公共服务(电签)
    PUBSERVICE_ESIGN = 'pubservice.esign'
    #建模平台
    APAAS = 'apaas'

    def mks_key(self):
        if self == SkylineApps.IPAAS:
            return 'IngratePlatform.host'
        elif self == SkylineApps.WORKBENCH_PORTAL:
            return 'Superportal.host'
        elif self == SkylineApps.DAP:
            return 'ThirdDatasource.bigdata_api_host'
        elif self == SkylineApps.DMP:
            return 'Domain.dmp'
        elif self == SkylineApps.PUBSERVICE_PRINT:
            return 'ReportCenter.open_print_url'
        elif self == SkylineApps.WORKBENCH:
            return 'Superportal.host'
        else:
            return None

    @staticmethod
    def custom_key(key):
        class CustomKey(str):
            @staticmethod
            def mks_key():
                return '不支持自定义站点的服务内配置获取'
            @property
            def value(self):
                return self.__str__()

        return CustomKey(key)

@unique
class MysoftShuXinDimType(Enum):
    """
    MysoftShuXin 指标的dimtype
    """

    指标 = 'indicator'
    维度 = 'dimension'


@unique
class TenantFrom(Enum):

   ERPOP = 'erpop'
   ERPSAAS = 'erpsaas'


@unique
class TaskDataType(Enum):
    #门户发布重新同步基础数据
    APPLICATION_PUBLISH_SYNC = 'APP_SYNC_FAIL'
