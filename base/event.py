from collections import defaultdict
from loguru import logger
from dmplib.redis import RedisCache
from base.enums import IngrateAction

_event = None
AddDataSourceEvent = "AddDataSource"
UpdateDataSourceEvent = "UpdateDataSource"
DelDataSourceEvent = "DelDataSourceEvent"


class Event:
    def __init__(self):
        self.fns = defaultdict(list)

    def register(self, event_name: str, fn):
        self.fns[event_name].append(fn)

    def trigger(self, event_name: str, data):
        for fn in self.fns[event_name]:
            fn(event_name, data)


def get_instance():
    global _event
    if not _event:
        _event = Event()
    return _event


def register(event_name: str, fn):
    get_instance().register(event_name, fn)


def trigger(event_name: str, data):
    get_instance().trigger(event_name, data)


def _register_api_2_ingrate_platform(code=None):
    from components.ingrate_platform import IngratePlatformApi
    from open_data.openapi_schema import superportal_api_info

    if RedisCache(key_prefix="register_api").set_nx_ex('_register_api_2_ingrate_platform', 1, ex=3600, nx=True):
        IngratePlatformApi(code=code, timeout=3, action=IngrateAction.Register.value).register_interface(
            name='数见',
            connector_code='Dmp',
            api_info=superportal_api_info
        )


def register_api(code=None):
    try:
        # 注册菜单api到集成平台
        _register_api_2_ingrate_platform(code)
    except Exception as e:
        logger.error(f"register_api error: {e}")


