#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=W1401

"""
    数据库基本操作
    <NAME_EMAIL> on 2017/3/14.
"""
import re

from base.models import BaseModel
from base.enums import MysoftNewERPDataBaseType
from dmplib.saas.project import get_db, get_data_db
from dmplib.db import mysql_wrapper
from dmplib.utils.errors import UserError
from dmplib import config

from dmplib.db.mysql_wrapper import SimpleMysql
from components.storage_setting import compatible_local_type_no_self
from components.data_center_api import (
    get_new_erp_datasource_model,
    get_data_by_sql as get_erp_data_by_sql
)
from typing import Dict, List, Optional, Tuple, Union, Any


class Pagination:
    def __init__(self, page, pagesize):
        self.page = int(page)
        self.pagesize = int(pagesize)
        self.page = 1 if self.page <= 1 else self.page
        self.pagesize = 1 if self.pagesize <= 1 else self.pagesize


def _get_db(from_config_db: bool) -> SimpleMysql:
    if from_config_db:
        return mysql_wrapper.get_db()
    else:
        return get_db()


def get_data_by_sql(sql, params, from_config_db=False, pagination=None):
    """
    根据sql语句获取数据
    :param str sql:
    :param dict params:
    :param bool from_config_db:
    :param Pagination pagination:
    :return:
    """
    if not sql:
        raise ValueError('sql not allow empty')

    db = _get_db(from_config_db)
    if pagination is not None:
        sql += 'LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
    return db.query(sql, params)


def get_data_order_by(table_name, conditions, fields=None, multi_row=None, order_by=None, pagination=None):
    """
    获取表数据
    :param Pagination pagination:
    :param str table_name: 表名
    :param dict conditions: 条件
    :param list fields: 列名
    :param bool multi_row: 是否返回多行，默认返回单行
    :param list order_by: 排序字段
    :return:
    """
    sql = 'SELECT {col} FROM `{table_name}` ' ''.format(
        col='`' + '`,`'.join(fields) + '`' if fields else '*', table_name=table_name.strip('`')
    )
    if conditions and isinstance(conditions, dict):
        sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    if order_by and isinstance(order_by, list):
        sql += ' ORDER BY ' + ','.join([ob[0] + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)])
    with get_db() as db:
        if multi_row:
            if pagination is not None:
                sql += 'LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)


def get_data(
    table_name: str,
    conditions: Dict[str, str],
    fields: Optional[List[str]] = None,
    multi_row: Optional[bool] = None,
    order_by: Optional[List[Tuple[str, str]]] = None,
    from_config_db: bool = False,
    pagination: None = None,
) -> Union[Dict, None, List[Any]]:
    """
    获取表数据
    :param Pagination pagination:
    :param from_config_db: 是否从配置库查询
    :param str table_name: 表名
    :param dict conditions: 条件
    :param list fields: 列名
    :param bool multi_row: 是否返回多行，默认返回单行
    :param list order_by: 排序字段
    :return:
    """
    sql = 'SELECT {col} FROM `{table_name}` ' ''.format(
        col='`' + '`,`'.join(fields) + '`' if fields else '*', table_name=table_name.strip("`")
    )
    if conditions and isinstance(conditions, dict):
        sql += _parse_where(conditions)
    if order_by and isinstance(order_by, list):
        sql += _parse_order_by(order_by)
    db = _get_db(from_config_db)
    if multi_row:
        if pagination is not None:
            sql += 'LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
        return db.query(sql, conditions)
    else:
        sql += ' LIMIT 1 '
        return db.query_one(sql, conditions)


def get_list(
    table_name: str,
    conditions: Dict[str, Any],
    fields: Optional[List[str]] = None,
    order_by: Any = None,
    from_config_db: bool = False,
    limit: Optional[int] = None,
    **kwargs,
) -> List[Dict[str, Union[int, str]]]:
    """
    查询数据列表
    :param str table_name: 表名
    :param dict conditions: 查询条件
    :param str|list fields: 查询的字段
    :param str|list order_by: 排序
    :param from_config_db: 是否从配置库查询
    :param str limit: limit 限制记录数
    :param list page: 分页
    :param str join: 连表
    :param str|list group: 分组字段
    :param str having: 分组条件
    :return: list
    """
    join = kwargs.get('join')
    group = kwargs.get('group')
    having = kwargs.get('having')
    page = kwargs.get('page')
    db = _get_db(from_config_db)
    sql = _parse_select(
        table_name, conditions, fields, order_by, limit=limit, page=page, join=join, group=group, having=having
    )
    return db.query(sql, conditions)


def get_one(
    table_name: str,
    conditions: Dict[str, str],
    fields: Optional[List[str]] = None,
    order_by: None = None,
    from_config_db: bool = False,
    **kwargs,
) -> Dict[str, Union[int, str]]:
    """取一条数据"""
    join = kwargs.get('join')
    group = kwargs.get('group')
    having = kwargs.get('having')
    result = get_list(
        table_name, conditions, fields, order_by, from_config_db, limit=1, join=join, group=group, having=having
    )
    if result:
        return result[0]
    return None


def get_value(table_name, conditions, field, order_by=None, from_config_db=False, **kwargs):
    join = kwargs.get('join')
    group = kwargs.get('group')
    having = kwargs.get('having')
    result = get_one(table_name, conditions, field, order_by, from_config_db, join=join, group=group, having=having)
    if result:
        return list(result.values())[0]
    return None


def get_column(table_name, conditions, fields, order_by=None, from_config_db=False, limit=None, **kwargs):
    join = kwargs.get('join')
    group = kwargs.get('group')
    having = kwargs.get('having')
    page = kwargs.get('page')
    result = get_list(
        table_name,
        conditions,
        fields,
        order_by=order_by,
        from_config_db=from_config_db,
        limit=limit,
        page=page,
        join=join,
        group=group,
        having=having,
    )
    if result:
        return [list(i.values())[0] for i in result]
    return None


def get_dict(table_name, conditions, fields, order_by=None, from_config_db=False, limit=None, **kwargs):
    join = kwargs.get('join')
    group = kwargs.get('group')
    having = kwargs.get('having')
    page = kwargs.get('page')
    result = get_list(
        table_name,
        conditions,
        fields,
        order_by=order_by,
        from_config_db=from_config_db,
        limit=limit,
        page=page,
        join=join,
        group=group,
        having=having,
    )
    if result:
        ret = {}
        if isinstance(fields, str):
            fields = fields.split(',')
        fields_len = None
        for v in result:
            if not fields_len:
                fields_len = len(v)
                index_key = _find_index_key(v.keys(), fields[0])
            if fields_len == 2:
                # 两个字段，字面量组成字典
                key = v.pop(index_key)
                ret[key] = list(v.values())[0]
            else:
                key = v[index_key]
                ret[key] = v
        return ret
    return None


def _find_index_key(keys, index_str: str):
    index_str = index_str.strip()
    index_str_no_s = index_str.replace('`', '')
    # 完全匹配
    for key in keys:
        if key in (index_str, index_str_no_s):
            return key
    # 所有key中，只有一个key是index_str的子串
    ret = None
    for key in keys:
        if key in index_str:
            if ret:
                ret = None
                break
            else:
                ret = key
    if ret:
        return ret
    # 匹配 as 的情况
    ma = re.match('.* (as )?(\w+)$', index_str_no_s, re.IGNORECASE)
    if ma and ma.group(2) in keys:
        return ma.group(2)
    # 匹配 table.field的情况
    ma = re.match('\w+\.(\w+)$', index_str_no_s.replace(' ', ''))
    if ma and ma.group(1) in keys:
        return ma.group(1)
    raise UserError(message='没有找到索引的key, index_str=%s in %s' % (index_str, list(keys)))


def _parse_select(
    table_name: str,
    conditions: Dict[str, str],
    fields: Optional[List[str]] = None,
    order_by: None = None,
    limit: Optional[int] = None,
    page: None = None,
    **kwargs,
) -> str:
    join = kwargs.get('join')
    group = kwargs.get('group')
    having = kwargs.get('having')
    sql = 'SELECT {} FROM {}'.format(_parse_fields(fields), _parse_field(table_name))
    if join:
        sql += ' '
        if not re.match('(left|right|natural|inner)? ?join ', join, re.IGNORECASE):
            sql += 'JOIN '
        sql += join
    sql += _parse_where(conditions)
    if group:
        sql += ' GROUP BY ' + _parse_fields(group)
        if having:
            sql += ' HAVING ' + having
    sql += _parse_order_by(order_by)
    if limit:
        sql += ' LIMIT ' + str(limit)
    elif page:
        page_index, page_size = page
        sql += ' LIMIT %s,%s' % ((page_index - 1) * page_size, page_size)
    return sql


def _parse_field(field: str) -> str:
    """解析 select 的一个字段"""
    if not field or field == '*':
        return '*'
    field = field.strip()
    org_str = (' ', '(', '`')
    for v in org_str:
        if v in field:
            return field
    arr = ['`%s`' % v for v in field.split('.')]
    return '.'.join(arr)


def _parse_fields(fields: List[str]) -> str:
    """
    解析 select 的字段，如果表达示中有英文逗号，必须使用 list 方式传参
    :param str|list fields:
    :return:
    """
    if not fields:
        return '*'
    if isinstance(fields, str):
        fields = fields.strip().split(',')
    arr = [_parse_field(v) for v in fields]
    return ','.join(arr)


def _parse_where(condition: Dict[str, str]) -> str:
    if not condition:
        return ''
    and_items = []
    for k, v in condition.items():
        field = k
        operator = '='
        if isinstance(v, list):
            operator = ' IN '
        elif v is None:
            operator = ' IS '
        else:
            ma = re.match('(.*?)(<>|!=|<=|>=|=|>|<| like| regexp)$', k, re.IGNORECASE)
            if ma:
                field, operator = ma.groups()
                operator_lower = operator.strip().lower()
                operator_map = {'like': ' LIKE ', 'regexp': ' REGEXP '}
                operator = operator_map.get(operator_lower, operator)
        ma = re.match('(.*?) (is not|not)$', field.strip(), re.IGNORECASE)
        if ma:
            field, _ = ma.groups()
            if operator == ' IS ':
                operator += 'NOT '
            else:
                operator = ' NOT' + operator
        and_items.append('{}{}%({})s'.format(_parse_field(field), operator, k))
    return ' WHERE ' + ' AND '.join(and_items)


def _parse_order_by(order_by: None) -> str:
    ret_arr = []
    if order_by:
        if isinstance(order_by, str):
            items = order_by.split(',')
            for item in items:
                *fields, asc_or_desc = item.strip().split(' ')
                ret_arr.append(_parse_field(' '.join(fields)) + ' ' + asc_or_desc.upper())
        if isinstance(order_by, tuple):
            order_by = [order_by]
        if isinstance(order_by, list):
            ret_arr = [_parse_field(ob[0]) + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)]
    return ' ORDER BY ' + ','.join(ret_arr) if ret_arr else ''


def get_data_scalar_by_sql(sql, params, from_config_db=False):
    if not sql:
        raise ValueError('sql not allow empty')
    db = _get_db(from_config_db)
    return db.query_scalar(sql, params)


def get_data_scalar(table_name: str, conditions: Dict[str, str], col_name: str, from_config_db: bool = False) -> str:
    """
    获取第一行第一列数据
    :param from_config_db:
    :param str table_name:
    :param str table_name:
    :param dict conditions:
    :param str col_name:
    :return:
    """
    if not col_name:
        return None
    sql = 'SELECT `{col}` FROM `{table_name}` '.format(col=col_name, table_name=table_name.strip('`'))
    if conditions and isinstance(conditions, dict):
        sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    sql += ' LIMIT 1 '
    db = _get_db(from_config_db)
    return db.query_scalar(sql, conditions)


def get_columns(table_name, conditions, col_name, from_config_db=False):
    """
    获取表中一列数据
    :param from_config_db:
    :param str table_name:
    :param dict conditions:
    :param str col_name:
    :return list:
    """
    if not col_name:
        return None
    sql = 'SELECT `{col}` FROM `{table_name}` '.format(col=col_name, table_name=table_name.strip('`'))
    if conditions and isinstance(conditions, dict):
        sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    db = _get_db(from_config_db)
    return db.query_columns(sql, conditions)


def add_data(table_name, data, commit=True, from_config_db=False):
    """
    添加数据
    :param str table_name:
    :param dict data:
    :param bool commit:
    :param bool from_config_db:
    :return bool:
    """
    if not data:
        return False
    db = _get_db(from_config_db)
    return db.insert(table_name, data, commit=commit) == 1


def add_list_data(table_name, list_data, fields, commit=True):
    """
    添加多行数据
    :param table_name:
    :param list_data:
    :param fields:
    :param commit:
    :return:
    """
    if not list_data or not isinstance(list_data, list):
        return False
    with get_db() as db:
        return db.insert_multi_data(table_name, list_data, fields, commit) > 0


def replace_list_data(table_name, list_data, fields, from_config_db=False, commit=True):
    """
    替换多行数据
    :param table_name:
    :param list_data:
    :param fields:
    :param commit:
    :return:
    """
    if not list_data or not isinstance(list_data, list):
        return False

    db = _get_db(from_config_db)
    return db.replace_multi_data(table_name, list_data, fields, commit=commit) > 0


def add_model(table_name, model, fields=None, from_config_db=False, commit=True):
    """
    添加数据
    :param str table_name:
    :param base.models.BaseModel model:
    :param list fields:
    :param from_config_db
    :param commit
    :return bool:
    """
    data = model.get_dict(fields)
    return add_data(table_name, data, from_config_db=from_config_db, commit=commit)


def add_list_model(table_name, list_model, fields, commit=True):
    """
    添加数据
    :param table_name:
    :param list_model:
    :param fields:
    :param commit:
    :return:
    """
    list_data = []
    for model in list_model:
        if not isinstance(model, BaseModel):
            continue
        list_data.append(model.get_dict(fields))
    if not list_data:
        return False
    return add_list_data(table_name, list_data, fields, commit=commit)


def update_data(table_name, data, condition, commit=True, from_config_db=False):
    """
    更新数据
    :param str table_name:
    :param dict data:
    :param dict condition:
    :param bool commit:
    :param bool from_config_db:
    :return int:
    """
    if not data:
        return 0
    with _get_db(from_config_db) as db:
        return db.update(table_name, data, condition, commit=commit)


def update(table_name, data, conditions):
    sql, params = _parse_update_set(data)
    sql = 'UPDATE {} SET {}'.format(_parse_field(table_name), sql)
    sql += _parse_where(conditions)
    params.update(conditions)
    return get_db().exec_sql(sql, params)


def _parse_update_set(data):
    sql_item = []
    new_data = dict()
    for k, v in data.items():
        new_key = '_' + k + '_'
        sql = '{}=%({})s'.format(_parse_field(k), new_key)
        sql_item.append(sql)
        new_data[new_key] = v
    return ','.join(sql_item), new_data


def update_model(table_name, model, condition, fields=None):
    """
    更新数据
    :param str table_name:
    :param base.models.BaseModel model:
    :param dict condition:
    :param list fields:
    :return int:
    """
    data = model.get_dict(fields)
    return update_data(table_name, data, condition)


def delete_data(table_name, condition, commit=True, from_config_db=False):
    """
    删除数据
    :param str table_name:
    :param dict condition:
    :param bool commit:
    :param bool from_config_db:
    :return int:
    """
    if not condition:
        return 0
    sql = "DELETE FROM `%s`" % table_name.strip('`')
    sql += _parse_where(condition)
    return _get_db(from_config_db).exec_sql(sql, condition, commit=commit)


def data_is_exists(
    table_name: str, condition: Optional[Dict[str, Union[int, str]]] = None, exclude_condition: None = None
):
    """
    数据是否存在
    :param str table_name:
    :param dict condition:
    :param dict exclude_condition: 不包括的条件
    :return:
    """
    sql = 'SELECT 1 FROM `%s` ' % (table_name.strip("`"),)
    where = []
    params = {}
    if condition:
        for k, v in condition.items():
            where.append('`{c}` = %({c})s'.format(c=k))
            params[k] = v
    if exclude_condition:
        for k, v in exclude_condition.items():
            where.append('`{c}`<> %({c})s '.format(c=k))
            params[k] = v
    sql += (' WHERE ' + ' AND '.join(where)) if where else ''
    sql += ' LIMIT 1 '
    with get_db() as db:
        return db.query_scalar(sql, params)


def delete_data(table_name, condition, commit=True, from_config_db=False):
    """
    删除数据
    :param str table_name:
    :param dict condition:
    :param bool commit:
    :param bool from_config_db:
    :return int:
    """
    if not condition:
        return 0
    sql = "DELETE FROM `%s`" % table_name
    sql += _parse_where(condition)
    return _get_db(from_config_db).exec_sql(sql, condition, commit=commit)


def get_data_max_rank(table_name, rank_col_name=None, condition=None):
    """
    获取最大的数据
    :param str table_name:
    :param str rank_col_name: 默认为 rank
    :param dict condition:
    :return int:
    """
    if not rank_col_name:
        rank_col_name = 'rank'
    sql = 'SELECT IFNULL(MAX(`%s`),0)+1 AS `rank` FROM `%s`' % (rank_col_name, table_name.strip('`'))
    params = {}
    if condition:
        sql += ' WHERE 1=1 '
        for k in condition.keys():
            sql += ' AND `%s` = %%(%s)s' % (k, k)
            params[k] = condition[k]
    with get_db() as db:
        return db.query_scalar(sql, params)


def update_data_rank(table_name, source_id, old_rank, cur_rank, id_col_name=None, rank_col_name=None, condition=None):
    """
     更新数据排序
    :param str table_name:
    :param str source_id:
    :param int old_rank:
    :param int cur_rank:
    :param str id_col_name:
    :param str rank_col_name:
    :param dict condition:
    :return:
    """
    if old_rank < cur_rank:
        sql = (
            'UPDATE `{table_name}` '
            'SET `{rank_col_name}`=`{rank_col_name}`-1 '
            'WHERE `{rank_col_name}` > %(old_rank)s '
            'AND `{rank_col_name}` <= %(cur_rank)s '
            'AND `{id_col_name}`<>%(id)s '.format(
                table_name=table_name.strip('`'),
                rank_col_name=rank_col_name if rank_col_name else 'rank',
                id_col_name=id_col_name if id_col_name else 'id',
            )
        )
    else:
        sql = (
            'UPDATE `{table_name}` '
            'SET `{rank_col_name}`=`{rank_col_name}`+1 '
            'WHERE `{rank_col_name}` >= %(cur_rank)s '
            'AND `{rank_col_name}` <= %(old_rank)s '
            'AND `{id_col_name}`<>%(id)s '.format(
                table_name=table_name.strip('`'),
                rank_col_name=rank_col_name if rank_col_name else 'rank',
                id_col_name=id_col_name if id_col_name else 'id',
            )
        )
    params = {'id': source_id, 'cur_rank': cur_rank, 'old_rank': old_rank}
    if condition:
        for k in condition.keys():
            sql += ' AND `%s` = %%(%s)s' % (k, k)
            params[k] = condition[k]
    with get_db() as db:
        return db.exec_sql(sql, params)


def get_data_db_table_columns(table_name):
    """
    获取数据表字段
    :param table_name:
    :return:
    """
    sql = (
        'SELECT column_name FROM information_schema.COLUMNS '
        'WHERE table_schema= DATABASE() AND `table_name`=%(table_name)s '
    )
    with get_data_db() as db:
        return db.query_columns(sql, {'table_name': table_name})


def delete_data_db_table(table_name):
    """
    删除数据表
    :param table_name:
    :return:
    """
    sql = 'DROP TABLE IF EXISTS `%s`' % table_name.strip('`')
    with get_data_db() as db:
        return db.exec_sql(sql)


def check_data_db_table_is_exist_local(table_name, dataset):
    content = dataset.get('content')
    dataset_id = dataset.get('id')
    datasource_model = get_new_erp_datasource_model(dataset_content=content)
    if datasource_model.db_type.lower() == MysoftNewERPDataBaseType.Mysql.value.lower():
        sql = "show tables like '%s';" % table_name
    elif datasource_model.db_type == MysoftNewERPDataBaseType.DM.value.lower():
        sql = """select count(*) from dba_tables 
        where owner = sys_context('userenv','current_schema') and TABLE_NAME='{}'""".format(table_name)
    else:
        sql = f"select top 1 * from sysObjects where Id=OBJECT_ID(N'{table_name}') and xtype='U'"
    return get_erp_data_by_sql(sql, datasource_model, dataset_id=dataset_id).get("Data")


@compatible_local_type_no_self
def check_data_db_table_is_exist(table_name, dataset):
    """
    判断数据表是否存在
    :param table_name:
    :param dataset:
    :return:
    """
    sql = 'SHOW TABLES LIKE "%s"' % table_name
    with get_data_db() as db:
        return db.query_one(sql)


def check_data_db_table_exist(table_name):
    """
    判断数据表是否存在
    :param table_name:
    :return:
    """
    sql = 'SHOW TABLES LIKE "%s"' % table_name
    with get_data_db() as db:
        return db.query_one(sql)


def check_db_table_is_exist(table_name):
    """
    判断数据表是否存在
    :param table_name:
    :return:
    """
    sql = 'SHOW TABLES LIKE "%s"' % table_name
    with get_db() as db:
        return db.query_one(sql)


def exec_sql(sql):
    """
    执行sql语句
    :param sql:
    :return:
    """
    with get_db() as db:
        return db.exec_sql(sql)


def build_in_query(list_data, placeholder_prefix):
    """
    构建in查询的占位符和参数
    :param list_data:
    :param placeholder_prefix:
    :return:
    """
    params = {}
    in_str_arr = []
    for index, element in enumerate(list_data):
        placeholder = placeholder_prefix + "_" + str(index)
        in_str_arr.append("%(" + placeholder + ")s")
        params[placeholder] = element
    return ",".join(in_str_arr), params


def check_field_exists(table_name, field_name, condition):
    """
    检查字段是否存在于表中
    :param condition:
    :param field_name:
    :param table_name:
    :return:
    """
    sql = f"select count(1) as c from `{table_name.strip('`')}` where {field_name} = %(condition)s"
    count = {'c': 0}
    params = {"condition": condition}
    with get_db() as db:
        count = db.query_one(sql, params)
    return count['c']


def get_data_with_pass_db(db, table_name, conditions, fields, multi_row=None, order_by=None):
    """
    获取表数据
    """
    sql = 'SELECT {col} FROM `{table_name}` ' \
          ''.format(col='`' + '`,`'.join(fields) + '`' if fields else '*',
                    table_name=table_name.strip('`'))
    if conditions and isinstance(conditions, dict):
        sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    if order_by and isinstance(order_by, list):
        sql += ' ORDER BY ' + ','.join([ob[0] + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)])
    if multi_row:
        return db.query(sql, conditions) or []
    else:
        sql += ' LIMIT 1 '
        return db.query_one(sql, conditions) or {}


def execute_multi_sql(db_name='', sql=''):
    from dmplib.saas import project
    if not sql:
        return False

    if db_name == 'data':
        db_conf = project.get_data_db_config()
    elif db_name == 'config':
        db_conf = {
            'host': config.get('DB.host'), 'prot': int(config.get('DB.port')), 'user': config.get('DB.user'),
            'password': config.get('DB.password'), 'database_name': config.get('DB.database')
        }
    else:
        db_conf = project.get_db_config()
    if not db_conf:
        raise UserError(message='没有获取到数据库连接配置')

    conn = _get_db_cursor_v2(db_conf)
    cursor = conn.cursor()
    try:
        cursor.execute(sql)
        conn.commit()
    except Exception as e:
        from loguru import logger
        logger.info('执行多SQL报错：{}'.format(str(e)))
        raise e
    finally:
        cursor.close()


def _get_db_cursor_v2(db_conf: dict):
    import pymysql
    from pymysql.constants import CLIENT

    port = db_conf.get('port') or 3306
    cnx = pymysql.connect(
        user=db_conf.get('user') or config.get('DB.user'),
        password=db_conf.get('password') or config.get('DB.password'),
        host=db_conf.get('host') or config.get('DB.host'),
        database=db_conf.get('database'),
        port=int(port),
        client_flag=CLIENT.MULTI_STATEMENTS   # 批量执行
    )
    return cnx


def get_total(sql, params=None, db=None, from_config_db=False):
    if not db:
        db = _get_db(from_config_db)
    return db.query_scalar(f"select count(*) as total from ({sql}) as t", params=params)

