#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    通用Service操作
    <NAME_EMAIL> on 2017/3/14.
"""
import copy
import functools
from base import repository
from base.enums import DatasetType, DashboardType
from dmplib.utils.errors import UserError
from dmplib.utils.strings import is_ascii
from pypinyin import lazy_pinyin
from pypinyin.constants import PINYIN_DICT

from datetime import datetime
from typing import Any, Dict, List, Optional, Union


def list_dict_group_by(
        data_list: List[Dict[str, Union[str, None]]], key: str
) -> Dict[str, Union[List[Dict[str, str]], List[Union[Dict[str, str], Dict[str, Union[str, None]]]]]]:
    """
    将列表字典按照关键字进行分组
    :param data_list:
    :param key:
    :return:
    """
    if not data_list or not isinstance(data_list, (list, tuple)):
        return {}
    result = {}
    for item in data_list:
        if not isinstance(item, dict):
            continue
        if isinstance(key, list):
            result.setdefault('.'.join([item.get(k) for k in key]), []).append(item)
        else:
            result.setdefault(item.get(key), []).append(item)
    return result


def update_data_rank(model, condition=None):
    """
    更新数据排序
    :param base.models.RankModel model:
    :param dict condition:
    :return:
    """
    source = repository.get_data(model.table_name, {model.id_col_name: model.source_id}, [model.rank_col_name])
    if not source:
        raise UserError(message='数据不存在')
    target = None
    if model.target_id:
        target = repository.get_data(model.table_name, {model.id_col_name: model.target_id}, [model.rank_col_name])
    source_rank = source.get(model.rank_col_name) or 0
    cur_rank = (
        target.get(model.rank_col_name) or 0
        if target
        else repository.get_data_max_rank(model.table_name, model.rank_col_name, condition)
    )
    if source_rank < cur_rank:
        cur_rank -= 1
    if cur_rank < 0:
        cur_rank = 0
    recode = 0
    recode += repository.update_data(
        model.table_name, {model.rank_col_name: cur_rank}, {model.id_col_name: model.source_id}
    )
    recode += repository.update_data_rank(
        model.table_name,
        model.source_id,
        source.get(model.rank_col_name) or 0,
        cur_rank,
        model.id_col_name,
        model.rank_col_name,
        condition,
    )
    return recode


def sync_rank_to_release():
    app_rank = repository.get_list('application', {'enable': [1, 2]}, ['id', 'rank'])
    update_sql = []
    for rank in app_rank:
        update_sql.append(
            "UPDATE release_application SET `rank` = {} WHERE `id` = '{}';".format(rank.get('rank'), rank.get('id'))
        )
    if update_sql:
        exec_sql = ''.join(update_sql)
        repository.execute_multi_sql(sql=exec_sql)


def cmp(cx: Union[datetime, int, List[str], str], cy: Union[datetime, int, List[str], str]) -> int:
    if cx and cy:
        if cx > cy:
            return 1
        elif cx < cy:
            return -1
        else:
            return 0
    if cx and not cy:
        return 1
    if not cx and cy:
        return -1
    return 0


def cmp_name_func(x: Dict[str, Any], y: Dict[str, Any], field: str = 'name') -> int:
    """
    逐字比较
    """
    x, y = x[field], y[field]
    xlen, ylen = len(x), len(y)
    i, j = 0, 0
    while i < xlen and j < ylen:
        if is_ascii(x[i]) and is_ascii(y[j]):  # 如果都是ascii码 直接比较
            eq = cmp(x[i], y[j])
            if eq != 0:
                return eq

        elif ord(x[i]) in PINYIN_DICT and ord(y[j]) in PINYIN_DICT:  # 如果都是汉字，转换为拼音后比较
            x_py, y_py = lazy_pinyin(x[i]), lazy_pinyin(y[j])
            eq = cmp(x_py, y_py)
            if eq != 0:
                return eq

        else:  # 如果一个是汉字，一个是拼音 直接比较
            return cmp(x[i], y[j])

        i += 1
        j += 1

    return cmp(xlen, ylen)  # 比较字符长度


def sort_tree_data(list_data: Any, order_by: Optional[str] = None, reverse: bool = True) -> Any:
    """
    :对数据集和报告这种树形结构排序,文件夹优先排在前面，然后依次根据创建时间最新优先
    :date 2018/6/09
    :param list list_data 单维度数据集:
    :return list:
    """
    folder_list = []
    not_folder_list = []

    for v in list_data:
        if v.get('sub'):
            v['sub'] = sort_tree_data(v.get('sub'), order_by)

        if v.get('type') in [DatasetType.Folder.value, DashboardType.Folder.value]:
            folder_list.append(v)
        else:
            not_folder_list.append(v)

    if order_by == "sort":
        dashboard_list = sorted(list_data, key=lambda x: x.get("sort"), reverse=False)
        return dashboard_list
    else:
        if order_by:

            def cmp_time_func(x, y):
                mtime_x, mtime_y = x.get("modified_on", x["created_on"]), y.get("modified_on", x["created_on"])
                return cmp(mtime_x, mtime_y)

            def cmp_int_func(x, y, field):
                mtime_x, mtime_y = x.get(field), y.get(field)
                return cmp(mtime_x, mtime_y)

            def final_cmp(x, y):
                if order_by in ("name", "created_by"):  # 此处需要研究能否去掉这个条件，默认就是这个排序。
                    return cmp_name_func(x, y, order_by) or cmp_time_func(x, y)
                elif order_by == "mtime":
                    return cmp_time_func(x, y) or cmp_name_func(x, y)
                elif order_by in ["application_type", "status"]:
                    return cmp_int_func(x, y, order_by)
                return None

            sort_k_f = functools.cmp_to_key(final_cmp)
        else:
            sort_k_f = lambda x: (x['created_on'], x['name'])

        if order_by == 'rank':
            sort_k_f = lambda x: (x['rank'], -x['created_on'].timestamp())
            folder_list = sorted(folder_list, key=sort_k_f)
            not_folder_list = sorted(not_folder_list, key=sort_k_f)
        else:
            folder_list = sorted(folder_list, key=sort_k_f, reverse=reverse)
            not_folder_list = sorted(not_folder_list, key=sort_k_f, reverse=reverse)

        folder_list.extend(not_folder_list)
        return folder_list


def sort_report_tree_data(list_data: Any, order_by: Optional[str] = None, reverse: bool = True) -> Any:
    """
    :针对报表中心的数据进行排序，根据名称进行排序
    :param list list_data 单维度数据集:
    :return list:
    """
    folder_list = []
    not_folder_list = []

    for v in list_data:
        if len(v.get('sub')) > 1:
            v['sub'] = sort_report_tree_data(v.get('sub'), order_by, reverse)

        if v.get('type') in [DatasetType.Folder.value, DashboardType.Folder.value]:
            folder_list.append(v)
        else:
            not_folder_list.append(v)

    def cmp_time_func(x, y):
        mtime_x, mtime_y = x.get("modified_on", x["created_on"]), y.get("modified_on", x["created_on"])
        return cmp(mtime_x, mtime_y)

    def final_cmp(x, y):
        if order_by == "name":
            return cmp_name_func(x, y, order_by) or cmp_time_func(x, y)
        elif order_by == "mtime":
            return cmp_time_func(x, y) or cmp_name_func(x, y)
        return None

    sort_k_f = functools.cmp_to_key(final_cmp)

    folder_list = sorted(folder_list, key=sort_k_f, reverse=reverse)
    not_folder_list = sorted(not_folder_list, key=sort_k_f, reverse=reverse)

    folder_list.extend(not_folder_list)
    return folder_list



def filter_permission_tree(list_data, action):
    new_list_data = []
    length = copy.deepcopy(len(list_data))
    for i in range(length)[::-1]:
        row = list_data[i]
        if row.get('sub'):
            row['sub'] = filter_permission_tree(row.get('sub'), action)
        # 文件夹不做处理
        if action not in row.get('actions') and row.get('type') == 'FILE':
            list_data.remove(row)
        else:
            # 空目录不需要加入
            if row.get('type') == 'FOLDER' and not row.get('sub'):
                continue
            new_list_data.insert(0, row)
    return new_list_data
