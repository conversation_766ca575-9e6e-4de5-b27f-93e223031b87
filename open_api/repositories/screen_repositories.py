#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from base import repository
from dmplib.saas.project import get_db
from base.enums import DashboardType
from dashboard_chart.models import DashboardOpenAPIEmbeddedQueryModel

supported_charts = [
    'simple_text', 'simple_text_mobile', 'title_link_pc', 'simple_image',
    'cluster_column', 'multiple_bar_mobile', 'stack_bar',
    'multiple_horizon_bar_mobile', 'horizon_bar', 'flow_bar_mobile', 'horizon_stack_bar',
    'double_axis_mobile', 'flow_bar', 'double_axis', 'compare_bar', 'comparison_line',
    'multiple_line_mobile', 'stack_line', 'multiple_area_mobile', 'area', 'stack_area',
    'pie', 'pie_mobile', 'circle_pie', 'rose_pie_mobile', 'rose_pie', 'circle_rose_pie', 'numerical_value',
    'numerical_value_mobile', 'liquid_fill', 'numerical_panel_mobile', 'gauge', 'gauge_mobile',
    'split_gauge', 'multiple_split_gauge_mobile', 'form', 'form', 'progress', 'progress', 'scatter',
    'radar_mobile', 'radar', 'funnel_mobile', 'candlestick', 'track', 'funnel_plus',
    'room_diagram_mobile', 'treemap', 'trajectory_diagram', 'simple_clock', 'numerical_value_next'
]
supported_charts = list(set(supported_charts))


def get_dashboard_columns():
    columns = repository.get_data_by_sql(sql='show columns from `dashboard`', params={})
    columns = [i.get('Field', '') for i in columns]
    return columns


# def has_support_chart(dashboard_id, status):
#     params = {}
#     if str(status) == '1':
#         sql = """select id from `dashboard_released_snapshot_chart` as dc
#         where dc.dashboard_id = %(dashboard_id)s and `chart_code` in %(dc_chart_code)s limit 1"""
#     else:
#         sql = """select id from `dashboard_chart` as dc
#         where dc.dashboard_id = %(dashboard_id)s and `chart_code` in %(dc_chart_code)s limit 1"""
#     params['dc_chart_code'] = supported_charts
#     params['dashboard_id'] = dashboard_id
#     with get_db() as db:
#         return db.query(sql, params)


def get_dashboard_list_by_query_model(query_model: DashboardOpenAPIEmbeddedQueryModel, fields):
    """
    获取看板数据
    """
    if not isinstance(query_model, DashboardOpenAPIEmbeddedQueryModel):
        return []

    sql = 'select %s from `dashboard`' % ', '.join(fields)
    params = {}
    wheres = []
    query_model.validate()
    if query_model.dashboard_ids:
        wheres.append('id in %(dashboard_ids)s')
        params['dashboard_ids'] = query_model.dashboard_ids
    if query_model.status is not None:
        wheres.append('status = %(status)s')
        params['status'] = int(query_model.status)
        # 是否需要过滤不符合的组件
        if str(query_model.with_filter) == '1':
            if params['status'] == 1:
                # 已发布的的报告，没有相关的支持组件，不展示
                wheres.append(
                    '(select id from `dashboard_released_snapshot_chart` as dc '
                    'where dc.dashboard_id = `dashboard`.id and `chart_code` in %(dc_chart_code)s limit 1) is not null'
                )
                params['dc_chart_code'] = supported_charts
            else:
                # 设计时的报告，没有相关的支持组件，不展示
                wheres.append(
                    '(select id from `dashboard_chart` as dc '
                    'where dc.dashboard_id = `dashboard`.id and `chart_code` in %(dc_chart_code)s limit 1) is not null'
                )
                params['dc_chart_code'] = supported_charts
    if query_model.type:
        wheres.append('type = %(type)s')
        params['type'] = query_model.type
    else:
        wheres.append('type != %(type)s')
        params['type'] = DashboardType.CHILD_FILE.value
    if query_model.platform:
        wheres.append('platform = %(platform)s')
        params['platform'] = query_model.platform
    if query_model.created_by:
        wheres.append('created_by = %(created_by)s')
        params['created_by'] = query_model.created_by
    if query_model.build_in is not None:
        wheres.append('build_in=%(build_in)s')
        params['build_in'] = query_model.build_in
    if query_model.is_multiple_screen is not None:
        wheres.append('is_multiple_screen=%(is_multiple_screen)s')
        params['is_multiple_screen'] = query_model.is_multiple_screen
    if query_model.dashboard_type:
        # 仪表板
        dashboard_type_sql = generate_dashboard_type_sql(query_model.dashboard_type)
        if dashboard_type_sql:
            wheres.append(dashboard_type_sql)

    if query_model.parent_id:
        # 只获取根节点下所有数据集对象列表
        if query_model.parent_id == 'root':
            query_model.parent_id = ''
        level_code = repository.get_value("dashboard", {"id": query_model.parent_id}, ["level_code"])
        # 系统分发文件夹可能存在level_code错乱情况，仍按parent_id查询
        params["parent_id"] = query_model.parent_id
        if level_code and not level_code.startswith("9000-"):
            params["level_code"] = level_code + "%"
            wheres.append('id != %(parent_id)s and dashboard.level_code like %(level_code)s ')
        else:
            wheres.append('`parent_id`=%(parent_id)s ')
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' order by `rank`'
    with get_db() as db:
        return db.query(sql, params)


def generate_dashboard_type_sql(dashboard_type):
    # params['platform'] = ['pc', 'mobile']  # 现在mobile+new_layout_type=0 入口已经没有了

    sql_map = {
        'pc_screen': '(platform = "pc" and new_layout_type= 1)',
        'large_screen': '(platform = "pc" and new_layout_type= 0)',
        'mobile_screen': '(terminal_type = "mobile_screen")',
    }
    sql_list = [sql_map.get(_type, '') for _type in dashboard_type]
    sql = ' or '.join(sql_list)
    if sql:
        return f'({sql})'
    return ''


def get_released_chart_data_by_dashboard_id(dashboard_id):
    return repository.get_data(
        'dashboard_released_snapshot_chart',
        conditions={'dashboard_id': dashboard_id, 'chart_code': supported_charts},
        multi_row=True
    )


def get_dashboard_by_id(dashboard_id):
    return repository.get_one('dashboard', conditions={'id': dashboard_id})
