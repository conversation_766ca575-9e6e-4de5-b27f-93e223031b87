
from base.models import BaseModel


class TenantAddParams(BaseModel):
    __slots__ = []

    def __init__(self, **kwargs):

        self.traceId = None
        self.taskId = None
        self.taskCallbackUrl = None
        self.deliveryNo = None
        self.deliveryBatchNo = None
        self.isFirstOpen = None
        self.enterpriseCode = None
        self.enterpriseName = None
        self.envCode = None
        self.tenantCode = None
        self.tenantName = None
        self.tenantSourceType = None
        self.initUserName = None
        self.initPassword = None
        self.creatorUsername = None
        self.creatorName = None
        self.customerId = None
        self.customerName = None
        self.customerShortName = None
        self.topCustomerId = None
        self.topCustomerName = None
        self.customerCorpId = None
        self.customerCorpName = None
        self.masterId = None
        self.masterUserName = None
        self.masterName = None
        self.masterPhone = None
        self.masterEmail = None
        self.bizMode = None
        self.customVars = {}
        self.license = None
        self.erpLanguage = None
        self.appAuths = []
        self.appList = []


        super().__init__(**kwargs)


class TenantCancellationParams(BaseModel):
    __slots__ = []

    def __init__(self, **kwargs):
        self.taskId = None
        self.taskCallbackUrl = None
        self.enterpriseCode = None
        self.enterpriseName = None
        self.tenantCode = None
        self.tenantName = None
        self.customerId = None

        super().__init__(**kwargs)