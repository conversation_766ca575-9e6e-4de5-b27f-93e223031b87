import json
import logging
from urllib.parse import urljoin

import hug
from hug.authentication import authenticator

from base.enums import SkylineApps
from components import auth_util
from components.app_hosts import AppHosts
from dmplib import config
from dmplib.hug import APIWrapper, g
from dmplib.nacos_client import NacosClient
from dmplib.utils.errors import UserError
from open_api.models import TenantAddParams, TenantCancellationParams
from open_api.services import tenant_add_service

@authenticator
def _verify_token_handle(request, response, verify_user, **kwargs):
    """

    :param request:
    :param response:
    :return:
    """
    try:
        data = _verify_token(request)
        if not data:
            return False
    except:
        return False
    g.account = 'openapi'
    return True


def _verify_token(request):
    token = auth_util.get_auth_header(request)
    if not token:
        return True
    if auth_util.is_enable_skyline_auth():
        payload = auth_util.verify_token(token)
        return payload
    return True


class OmpAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_omp_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._omp_route = None
        self.api.http.base_url = '/api/omp'

    @property
    def omp_route(self):
        if not self._omp_route:
            self._omp_route = hug.http(api=self.api, requires=_verify_token_handle(None))
        return self._omp_route


api = OmpAPIWrapper(__name__)



@api.omp_route.post('/tenant/add')
def tenant_add(request, **kwargs):
    token = request.get_header('Authorization')
    if not token:
        raise UserError(message="token为空")
    logging.error(f'开户参数:{json.dumps(kwargs)}')
    res = tenant_add_service.tenant_add(token, TenantAddParams(**kwargs))
    return res


@api.omp_route.post('/tenant/remove')
def tenant_add(request, **kwargs):
    token = request.get_header('Authorization')
    if not token:
        raise UserError(message="token为空")
    res = tenant_add_service.cancellation(token, TenantCancellationParams(**kwargs))
    return res


@api.omp_route.get('/tenant/status')
def tenant_add(request, **kwargs):
    task_id = kwargs.get('taskId')
    if not task_id:
        raise UserError(message="taskId为空")
    res = tenant_add_service.status(task_id)
    return res


@api.omp_route.get('/nacos/get')
def nacos_get(request, **kwargs):
    key = kwargs.get('key')
    res = NacosClient.get(key)
    return res



@api.omp_route.get('/test')
def test(request, **kwargs):
    url = urljoin(config.get('Domain.dmp'), '/api/omp/test')
    return url