#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401

"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
import json
import logging
import traceback

import hug
from hug.authentication import authenticator
from urllib.parse import urlparse

from base import repository
from base.dmp_constant import DEFAULT_USER_ROLE_GROUP
from base.enums import OperationFlowType, SkylineApps
from components import auth_util
from components.app_hosts import AppHosts
from dashboard_chart.services.download import download_service
from data_source.models import DataSourceModel
from data_source.services import data_source_service, external_data_source_service
from dmplib import config
from base.errors import UserError
from components.message_queue import RabbitMQ
from dmplib.hug import APIWrapper, g
from dmplib.utils.strings import seq_id

from flow.services import flow_service, common_rundeck_flow_service
from indicator.models import TemplateQueryModel
from indicator.services import template_service
from indicator.services import type_service
from label.models import LabelFlowModel, LabelFlowQueryModel
from label.services import label_service
from open_api.services import screen_openapi_service, screen_auth_service
from rbac.models import Role
from rbac.services import grant_service, func_service, role_service
from self_service.models import SyncModel
from user import external_user_service
from user.models import UserModel
from user.services import user_service
from user_group.services import user_group_service
from dashboard_chart.services import dashboard_openapi_service
from dataset import external_query_service
from dataset.services import dataset_subject_service, dataset_version_service, dataset_service
from message.services import message_service
from message.models import MessageModel
import app_celery
from app_menu.services import application_openapi_service, application_auth_service, application_service
from healthy.services import subject_inspection_openapi_service
from open_api.services import utils_service
from user_log.models import UserLogModel

secret_key = "FDGRCVBSFGDSF"


@authenticator
# pylint: disable=W0613
def _verify_auth_key_handle(request, response, verify_user, **kwargs):
    """

    :param request:
    :param response:
    :return:
    """
    project_code = request.headers.get('X-TENANT')
    if not project_code:
        project_code = request.headers.get('X-CONSUMER-CUSTOM-ID')

    # 粗略校验code格式
    if not project_code or len(project_code) > 255:
        #统一应用认证
        skyline_token = auth_util.get_auth_header(request)
        if skyline_token:
            try:
                data = auth_util.verify_token(skyline_token)
                if not data:
                    logging.error('my-api-authorization验证失败')
                    return False
                project_code = request.params.get('tenant_code', '')
                logging.error(f'指定租户:{project_code}')
            except Exception as e:
                logging.error(f"统一应用认证解析token异常: {str(e)} skyline_token: {skyline_token}")
                return False
        else:
            return False

    g.code = project_code
    g.account = 'openapi'
    return True


class OpenAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_open_route', '_yzs_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._open_route = None
        self._yzs_route = None
        self.api.http.base_url = '/openapi'

    @property
    def open_route(self):
        if not self._open_route:
            # pylint: disable=E1120
            self._open_route = hug.http(api=self.api, requires=_verify_auth_key_handle(None))
        return self._open_route

    @property
    def yzs_route(self):
        if not self._yzs_route:
            self._yzs_route = hug.http(api=self.api, requires=None)
        return self._yzs_route


api = OpenAPIWrapper(__name__)


@api.open_route.get('/self_service/refresh_all')
def refresh_all(**kwargs):
    """更新指定租户的所有模型"""
    # 获取所有配置的dmp_project_codes
    from rbac import external_service
    dmp_project_codes = external_service.get_value_added_func_project_codes("self-service")
    dmp_project_code = kwargs.get('project_code', None)
    if dmp_project_code:
        if dmp_project_code not in dmp_project_codes:
            raise UserError(message=f'项目<{dmp_project_code}>不存在')
        dmp_project_codes = [dmp_project_code]

    is_async = kwargs.get('async', False)
    if is_async:
        app_celery.refresh_all_self_report.apply_async(
            kwargs={'dmp_project_codes': dmp_project_codes},
            queue="self-service"
        )
    else:
        app_celery.refresh_all_self_report(dmp_project_codes=dmp_project_codes)

    return True, 'ok', {}


@api.open_route.get_post(urls='/self_service/refresh')
def refresh(**kwargs):
    """更新指定模型"""
    pulsar_project_code = kwargs.get('project_code') or ''
    model = SyncModel(
        pulsar_project_code=pulsar_project_code,  # 数芯业务板块
        external_subject_id=kwargs.get('subject_code'),
        external_space_id=kwargs.get('space_id'),
        action=kwargs.get('action'),
        model_category=kwargs.get('model_category'),
    )
    model.validate()

    # 异步执行同步
    app_celery.sync_self_report.apply_async(
        kwargs={
            'pulsar_project_code': model.pulsar_project_code,
            'subject_code': model.external_subject_id,
            'space_id': model.external_space_id,
            'action': model.action,
            'model_category': model.model_category,
        },
        queue="self-service"
    )
    return True, 'ok', {}


@api.open_route.post('/self_service/check')
def check():
    """检查模型是否被引用(废弃)"""
    return True, 'ok', {}


@api.open_route.get('/check_flow')
def check_flow_exists(code, flow_id):
    """
    /**
    @apiVersion 1.0.1
    @api    {get} /check_flow 检查流程是否存在
    @apiGroup    openapi
    @apiParam   formData  {string}    code 租户code
    @apiParam   formData  {string}    flow_id 流程ID
    @apiResponse 200{
        "result": true,
        "msg": "ok",
        "data": {
            "code": "project code",
            "flow_id": "flow id"
        }
    }
    **/
    """
    dmp_flow_id = flow_service.get_flow_id(code, flow_id)
    if dmp_flow_id and flow_id == dmp_flow_id:
        return True, 'ok', {"code": code, "flow_id": dmp_flow_id}
    else:
        return False, 'DMP系统中不存在该流程ID:' + flow_id, {}


@api.open_route.get('/get_oss_config')
def get_oss_config():
    """
    /**
    @apiVersion 1.0.1
    @api    {get} /get_oss_config 获取oss配置信息
    @apiGroup    openapi
    @apiResponse 200{
        "result": true,
        "msg": "ok",
        "data": {
            "access_key_id": "oss ID",
            "access_key_secret{oss密钥}": "oss access_key",
            "bucket": "oss bucket",
            "endpoint": "oss endpoint"
        }
    }
    **/
    """
    result = {
        'access_key_id': config.get('OSS.access_key_id'),
        'access_key_secret': config.get('OSS.access_key_secret'),
        'bucket': config.get('OSS.bucket'),
        'endpoint': config.get('OSS.endpoint'),
    }
    return True, '获取成功成功', result


@api.open_route.post('/send_message')
def send_message(
        queue_name: str, queue_message_body: str, durable: int = 0, auto_delete: int = 1, message_ttl: int = 0
):
    """
    /**
    @apiVersion 1.0.1
    @api    {post} /send_message 发送消息到mq
    @apiGroup    openapi
    @apiParam   formData  {string}    queue_name 队列名
    @apiParam   formData  {string}    queue_message_body 队列内容
    @apiParam   formData  {int}    durable 持久化
    @apiParam   formData  {int}    auto_delete 自动删除队列
    @apiParam   formData  {int}    message_ttl 消息存活时长
    @apiResponse    200 {
        "result": true,
        "msg": "发送mq消息成功",
        "data": {
            "status": "SUCCESS"
        }
    }
    **/
    """

    if not queue_name:
        return False, '队列名称不能为空', {"status": "FAILED"}

    rabbit_mq = RabbitMQ()
    res = rabbit_mq.send_message(
        queue_name,
        queue_message_body,
        durable=(durable == 1),
        auto_delete=(auto_delete == 1),
        headers={"_dmp_message_uuid": seq_id()},
        arguments={'x-message-ttl': message_ttl} if message_ttl and message_ttl > 0 else None,
    )
    if not res:
        result = {"status": "SUCCESS"}
    else:
        result = {"status": "FAILED"}

    return True, '发送mq消息成功', result


@api.open_route.get('/get_organ')
def get_organ(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api    {get} /get_organ 分层级获取用户组下机构
    @apiGroup    openapi
    @apiParam   query  {string}    id 用户组id
    @apiParam   query  {string}    parent_id 父级id
    @apiResponse    200 {
        "result": true,
        "msg": "ok",
        "data": {
            "name": "金融街控股股份有限公司",
            "is_leaf": 0,
            "parent_id": "",
            "id": "11b11db4-e907-4f1f-8835-b9daab6e1f23",
            "code": "00001-"
        }
    }
    **/
    """

    group_ids = kwargs.get('id') or user_service.get_cur_user_group_ids()
    group_id = group_ids[0] if group_ids and len(group_ids) > 0 else ''
    parent_id = kwargs.get('parent_id')
    return True, '', user_group_service.get_user_group_organ(group_id, parent_id)


@api.open_route.get('/template/list')
def get_indicator_template_list(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api    {get} /template/list 获取指标模板列表
    @apiGroup    openapi
    @apiParam   query  {number}  page_size 分页数 默认 100000
    @apiResponse    200{
        "result": true,
        "msg": "ok",
        "data":{
            "total":2,
            "items":[
                {
                    "rank":25,
                    "name":"客户模型",
                    "description":"123",
                    "indicator_configured":0,
                    "id":"39e047a4-6e40-e7e7-2a4c-6f1e9857b8cb",
                    "indicator_total":0
                },
                {
                    "rank":7,
                    "name":"客户指标模板",
                    "description":"！！！请勿调整，所有测试数据依赖此模板，如需操作请自行增加模板",
                    "indicator_configured":84,
                    "id":"cfbaca8c-861e-11e6-8ffc-0242c0a80004",
                    "indicator_total":93
                }
            ]
        }

    }
    **/
    """

    if not kwargs.get('page_size'):
        kwargs = {'page_size': 100_000}

    return True, None, template_service.get_template_list(TemplateQueryModel(**kwargs)).get_result_dict()


@api.open_route.get('/label/list')
def get_label_list(**kwargs):
    """
    /**
    @apiVersion 1.0.4
    @api    {get} /label/list 获取标签列表
    @apiGroup    openapi
    @apiParam   query  {number}  page 页码 默认 1
    @apiParam   query  {number}  page_size 分页数 默认 40
    @apiParam   query  {string}  org_id 组织id
    @apiParam   query  {string}  tmpl_id 模板id
    @apiParam   query  {string}  [keyword] 关键词
    @apiParam   query  {string}  sorts 根据字段排序 [{"id":"name","method":"DESC"}]
    @apiResponse    200 {
        "result": true,
        "msg": "ok",
        "data":{
                "total":48,
                "items":[
                    {
                        "build_in":0,
                        "type":"标签定义",
                        "depend_flow_name":"",
                        "mode":"高级",
                        "cover_count":0,
                        "id":"39e2cacf-2a37-4f40-0420-fa214f1611c7",
                        "depend_flow_id":"",
                        "status":"禁用",
                        "run_status":"",
                        "name":"12342351345",
                        "description":"",
                        "schedule":"0 0 0 ? * * *"
                    },
                    {
                        "build_in":0,
                        "type":"标签定义",
                        "depend_flow_name":"",
                        "mode":"基础",
                        "cover_count":0,
                        "id":"39e282c5-e1b1-be6f-63de-bb64bc1f16a6",
                        "depend_flow_id":"",
                        "status":"禁用",
                        "run_status":"",
                        "name":"zcTest",
                        "description":"11",
                        "schedule":"0 0 0 ? * * *"
                    }
                ]
            }

    }
    **/
    """

    return True, None, label_service.get_label_list(LabelFlowQueryModel(**kwargs)).get_result_dict()


@api.open_route.get('/indicator/type/list')
def get_indicator_type_list(**kwargs):
    """
    /**
       @apiVersion 1.0.0
       @api    {get} /indicator/type/list 获取指标列表
       @apiGroup    openapi
       @apiParam   query  {string}  tmpl_id  模板id
       @apiParam   query  {number}  include_indicator  是否包含指标（0,1）
       @apiParam   query  {number}  include_dimension  是否包含指标维度（0,1）
       @apiResponse    200 {
           "result": true,
           "msg": "ok",
           "data":[
                    {
                        "name":"会员信息",
                        "id":"17db46b6-861f-11e6-a635-0242c0a80004",
                        "indicator":[
                            {
                                "rank":1,
                                "name":"会员状态",
                                "type":"描述",
                                "type_id":"17db46b6-861f-11e6-a635-0242c0a80004",
                                "odps_field":"hy_zt",
                                "odps_table":"fact_hy",
                                "id":"a3a49fa4-86b9-11e6-b231-0242c0a80004"
                            }
                        ]
                    },
                    {
                        "name":"交易信息",
                        "id":"1bb8fff8-861f-11e6-a635-0242c0a80004",
                        "indicator":[
                            {
                                "rank":5,
                                "name":"项目名称",
                                "type":"维度",
                                "type_id":"1bb8fff8-861f-11e6-a635-0242c0a80004",
                                "odps_field":"xsjh_gfyxw",
                                "odps_table":"fact_xsjh",
                                "dimension":[
                                    {
                                        "rank":1,
                                        "name":"大公司",
                                        "id":"a1",
                                        "indicator_id":"c28c75e0-86b9-11e6-a866-0242c0a80004"
                                    },
                                    {
                                        "rank":2,
                                        "name":"小公司",
                                        "id":"a2",
                                        "indicator_id":"c28c75e0-86b9-11e6-a866-0242c0a80004"
                                    }
                                ],
                                "id":"c28c75e0-86b9-11e6-a866-0242c0a80004"
                            },
                            {
                                "rank":10,
                                "name":"交易状态",
                                "type":"维度",
                                "type_id":"1bb8fff8-861f-11e6-a635-0242c0a80004",
                                "odps_field":"jy_ztw",
                                "odps_table":"fact_jy",
                                "dimension":null,
                                "id":"cae56f9e-86b9-11e6-890d-0242c0a80004"
                            }
                        ]
                    }
                ]

       }
    **/
    """

    return (
        True,
        '',
        type_service.get_type_list_by_tmpl_id(
            kwargs.get('tmpl_id'), kwargs.get('include_indicator'), kwargs.get('include_dimension')
        ),
    )


@api.open_route.get('/label/check_logical_expression')
def check_logical_expression(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api    {get} /label/check_logical_expression 校验高级标签拼接条件是否符合sql语法
    @apiGroup    openapi
    @apiParam   query  {string}  logical_expression 高级字段表达式：{"logical_expression":"{g:0}"}
    @apiResponse    200 {
        "result": true,
        "msg": "ok",
        "data": {"data": "", "msg": "\u6821\u9a8c\u6210\u529f", "result": true}

    }
    **/
    """

    label_service.check_logical_expression(kwargs.get('logical_expression'))
    return True, '校验成功'


@api.open_route.post('/label/add')
def add_label(**kwargs):
    # pylint: disable= C0301
    """
    /**
    @apiVersion 1.0.2
    @api    {post} /label/add 添加标签
    @apiGroup    openapi
    @apiBodyParam  {
        "org_name{组织名}":"金融街控股股份有限公司",
        "name{标签名}":"12342351345",
        "description{标签描述}":"",
        "depend_flow_id{依赖流程id}":"",
        "depend_flow_type{flow类型}":"数据清洗",
        "schedule{调度时间}":"0 0 0 ? * * *",
        "id":"",
        "label{lable结构}":{
            "expression_groups{表达式结构}":[
                {
                    "id":0,
                    "type":"描述",
                    "expression{表达式}":{"indicator_id":"a3a49fa4-86b9-11e6-b231-0242c0a80004","operator":"like","value":["123"]}
                }
            ],
            "logical_expression{逻辑表达式}":"{g:0}",
            "label_id":"",
            "sync_detail{同步标签明细}":1,
            "mode{标签模式：高级|基础}":"高级",
            "org_id{组织id}":"11b11db4-e907-4f1f-8835-b9daab6e1f23",
            "tmpl_id{模板id}":"cfbaca8c-861e-11e6-8ffc-0242c0a80004",
            "indicators{指标列表}":[

            ],
            "list_cols{指标列}":[
                {
                    "indicator_id":"a3a49fa4-86b9-11e6-b231-0242c0a80004",
                    "rank":0
                }
            ]
        }
    }

    @apiResponse    200 {
        "result": true,
        "msg": "ok",
        "data":{"label_id":"39e2cacf-2a37-4f40-0420-fa214f1611c7"}}
    **/
    """
    model = LabelFlowModel(**kwargs)
    label_service.add_label(model)
    return True, '添加成功', model.id


@api.open_route.post('/data_source/add')
def add_data_source(**kwargs):
    """
    /*
    @apiVersion 1.0.2
    @api {post} /data_source/add 添加数据源
    @apiGroup    openapi
    @apiBodyParam {
         "name{名称}": "",
         "code{数据源唯一编码，抽取数据到ODPS会作为前缀部分}": "",
         "conn_str{连接字符串}": {
             "port":"3306",
             "password":"aaaa",
             "ssh_password":"",
             "use_ssh":0,
             "host":"127.0.0.1",
             "database":"aaa",
             "user":"sa"
         },
        "type{枚举ODPS,MySQL,MSSQL,SaaS,MysoftERP}": "MySQL",
        "is_buildin": 0
    }
    @apiResponse    200 {
        "result": true,
        "msg": "ok",
        "data":{"label_id":"39e2cacf-2a37-4f40-0420-fa214f1611c7"}}
    */
    """
    model = DataSourceModel(**kwargs)
    data_source_service.add_data_source(model)
    return True, '添加成功', model.id


# pylint: disable=W0613
@api.open_route.post('/user/add')
def add_user(request, **kwargs):
    """
    /*
    @apiVersion 1.0.7
    @api {post} /user/add 添加用户(支持密码,发送邮件)
    @apiGroup    openapi
    @apiBodyParam {
        "name{用户名字}": "张三",
        "account{用户登录帐号}": "mysoft01",
        "password{密码,明文}": "123",
        "mobile{手机号码}": "***********",
        "account_mode{帐号登录模式,支持DMP,ERP,DOMAIN}": "DMP",
        "group_id{用户所属组织id}": "39e3c8c9-ac60-58d3-7e1d-bd533eb863e8",
        "role_name{用户角色名字,可选}": "管理员",
        "email": "mysoft01mail.com",
        "org_id": "组织id",
        "org_name": "组织名称",
        "org_level": "组织层级"
    },
    @apiResponse  200 {
        "result": true,
        "msg": "添加成功",
        "data{用户id}": "39e3c8c9-ac60-58d3-7e1d-bd533eb863e8"
    }
    */
    """
    model = UserModel(**kwargs)
    ok = user_service.add_user(model, group_id=model.group_id, send_mail=True)
    if not ok:
        return False, '添加失败,请稍候重试', None

    role_name = kwargs.get('role_name')
    if role_name:
        role = role_service.get_role(role_name, ['id'])
        if not role:
            return False, '角色:%s不存在' % role_name, None
        role_service.set_roles_for_user(model.id, [role['id']])
    if kwargs.get("org_id"):
        user_service.upset_user_organization(
            model.id, kwargs.get("org_id"), kwargs.get("org_name", ""), kwargs.get("org_level", "")
        )
    return True, '添加成功', model.id


@api.open_route.post('/user/upset')
def upset_user(request, **kwargs):
    """
    /*
    @apiVersion 1.0.4
    @api {post} /user/upset 添加或更新用户,支持重置用户的角色
    @apiGroup openapi
    @apiBodyParam {
        "name{用户名字}": "张三",
        "account{用户帐号}": "mysoft01",
        "group_id{用户所属组织id,可选}": "39e3c8c9-ac60-58d3-7e1d-bd533eb863e8",
        "email": "mysoft01",
        "role_name{用户角色名字,可选}": "管理员"
        "org_id": "组织id",
        "org_name": "组织名称",
        "org_level": "组织层级"
    },
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data{用户id}": "39e3c8c9-ac60-58d3-7e1d-bd533eb863e8"
    }
    */
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc + '/'
    model = UserModel(**kwargs)
    role_name = kwargs.get('role_name')
    user_service.upset_user(model, group_id=model.group_id)
    if role_name:
        role = role_service.get_role(role_name, ['id'])
        if not role:
            return False, '角色:%s不存在' % role_name, None
        role_service.set_roles_for_user(model.id, [role['id']])

    if kwargs.get("org_id"):
        user_service.upset_user_organization(
            model.id, kwargs.get("org_id"), kwargs.get("org_name", ""), kwargs.get("org_level", "")
        )

    return True, '添加成功', model.id


# pylint: disable=W0613
@api.open_route.post('/user/role/upset')
def upset_role(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /user/role/upset 更新或新增角色,并分配功能权限
    @apiGroup openapi
    @apiBodyParam {
      "role_name{用户角色名称}": "管理员",
      "funcs{功能权限}": [{"func_code{功能代码}": "flow-ops", "func_action_codes{功能权限代码}":["write", "read"]}]
    }
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": {
          "role_id{角色id}": "39e47d2d-3f20-422b-94c9-e286d7190967"
        }
    }
    */
    """
    funcs = kwargs.get('funcs')
    role_name = kwargs.get('role_name')
    # 角色的父层级角色id
    parent_id = kwargs.get('parent_id', '')
    role = role_service.get_role(role_name, ['id', 'name'])
    if not role:
        # 新增角色
        m = Role()
        m.name = role_name
        m.role_group_id = DEFAULT_USER_ROLE_GROUP
        m.parent_id = parent_id
        role = role_service.upset_role(m)
    # 分配功能权限
    if funcs:
        grant_service.grant_funcs_for_role(role['id'], funcs)
    return True, 'ok', {'role_id': role['id']}


# pylint: disable=W0613
@api.open_route.get('/rbac/funcs/list')
def list_funcs(request, **kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {get} /rbac/funcs/list 列出所有的功能权限元数据
    @apiGroup openapi
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": [{
                "func_code{功能权限}": "system-control",
                "children": [
                  {
                    "actions{功能代码}": [
                      {
                        "action_name{名称}": "查看",
                        "action_code{代码}": "view"
                      }
                    ],
                    "func_name{名称}": "用户管理",
                    "func_code{代码}": "user",
                    "id": "********-0000-0000-0008-********0001",
                    "parent_id{父级id}": "********-0000-0000-0008-********0000",
                    "func_url{导航地址}": "http://"
                  }
                ],
                "func_name{功能权限名称}": "访问控制",
                "actions": [
                  {
                    "action_name": "查看",
                    "action_code": "view"
                  }
                ],
                "id": "********-0000-0000-0008-********0000",
                "func_url": "http://"
              }
          ]
    }
    */
    """
    return True, 'ok', func_service.list_funcs()


# pylint: disable=W0613
@api.open_route.get('/dashboard/list')
def list_dashboard(request, **kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /openapi/dashboard/list 获取看板(报告)列表数据
    @apiGroup openapi
    @apiParam query {string} [parent_id]  父级报告ID
    @apiParam query {string} [user_account] 用户账户，根据该用户的权限删选报告列表
    @apiParam query {string="FILE","FOLDER"} [type] 报告类型,FILE:报告;FOLDER:文件夹
    @apiParam query {number} [status]  报告状态,0:未发布，1:已发布
    @apiParam query {string="pc","mobile"} [platform] 报告平台类型：pc或者mobile
    @apiParam query {string} [created_by] 创建者(用户账户),可筛选由该用户创建的报告
    @apiParam query {number} [is_multiple_screen] 数据报告是否多屏 0：非多屏 1：多屏
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "cover{封面}": "",
                "id{看板的标识}": "39ddc507-2cb6-150b-47f9-8ba1f5dc6c4c",
                "biz_code{看板的业务代码}": "sale",
                "icon{图标}": "",
                "type{是目录或报告FOLDER/FILE}": "FILE",
                "name{名字}": "京西（新）-6号楼"
            }
        ]
    }
    */
    """
    data = dashboard_openapi_service.get_report_list(**kwargs)
    return True, 'ok', data


# pylint: disable=W0613
@api.open_route.get('/embedded/dashboard/list')
def list_embedded_dashboard(request, **kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /openapi/dashboard/list 获取看板(报告)列表数据
    @apiGroup openapi
    @apiParam query {string} [parent_id]  父级报告ID
    @apiParam query {string} [user_account] 用户账户，根据该用户的权限删选报告列表
    @apiParam query {string="FILE","FOLDER"} [type] 报告类型,FILE:报告;FOLDER:文件夹
    @apiParam query {number} [status]  报告状态,0:未发布，1:已发布
    @apiParam query {string="pc","mobile"} [platform] 报告平台类型：pc或者mobile
    @apiParam query {string} [created_by] 创建者(用户账户),可筛选由该用户创建的报告
    @apiParam query {number} [is_multiple_screen] 数据报告是否多屏 0：非多屏 1：多屏
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "cover{封面}": "",
                "id{看板的标识}": "39ddc507-2cb6-150b-47f9-8ba1f5dc6c4c",
                "biz_code{看板的业务代码}": "sale",
                "icon{图标}": "",
                "type{是目录或报告FOLDER/FILE}": "FILE",
                "name{名字}": "京西（新）-6号楼"
            }
        ]
    }
    */
    """
    data = screen_openapi_service.get_dashboard_list_for_embedded_openapi(**kwargs)
    return True, 'ok', data


# pylint: disable=W0613
@api.open_route.get('/dashboard/released_chart')
def list_released_chart(request, **kwargs):
    """
    /*
    @apiVersion 1.0.9
    @api {get} /openapi/dashboard/released_chart 获取已发布的报告中的组件数据
    @apiGroup openapi
    @apiParam query {string} [dashboard_id]  报告ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "id{组件的标识}": "39ddc507-2cb6-150b-47f9-8ba1f5dc6c4c",
                "nums{度量}": "",
                "dims{维度}": "",
                "chart_type{表格类型}": "chart",
                "name{名字}": "通用表格"
            }
        ]
    }
    */
    """
    dashboard_id = kwargs.get('dashboard_id', '')
    data = screen_openapi_service.get_dashboard_released_chart_data_for_openapi(dashboard_id)
    return True, 'ok', data


@api.open_route.get('/portal/list')
def list_portal(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /openapi/portal/list 获取门户列表数据
    @apiGroup openapi
    @apiParam query {string} [user_account] 用户账户，根据该用户的权限删选门户列表
    @apiParam query {string="pc","mobile", "mobile_screen", "tv"} [platform] 平台类型：pc或者mobile
    @apiParam query {number} [enable]  门户状态,0: 未启用，1:已启用
    @apiParam query {string} [created_by] 创建者(用户账户),可筛选由该用户创建的门户
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "cover{封面}": "",
                "id{看板的标识}": "39ddc507-2cb6-150b-47f9-8ba1f5dc6c4c",
                "biz_code{看板的业务代码}": "sale",
                "icon{图标}": "",
                "type{是目录或报告FOLDER/FILE}": "FILE",
                "name{名字}": "京西（新）-6号楼"
            }
        ]
    }
    */
    """
    data = application_openapi_service.get_portal_list_for_openapi(**kwargs)
    return True, 'ok', data


@api.open_route.get('/portal/dashboards')
def portal_included_dashboards(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /openapi/portal/dashboards 获取门户内报告列表数据
    @apiGroup openapi
    @apiParam query {string} [portal_id] DMP平台的应用门户ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "biz_code{看板业务代码}": '913d30b9d7bc406d94cd538eb85f0857',
                'id{看板标识id}': '39f41e03-eedc-cad9-2b36-a167069120ae',
                'name{看板名称}': '看板名称'
            }
        ]
    }
    */
    """
    portal_id = kwargs.get('portal_id')
    if not portal_id:
        raise UserError(400, '缺少`portal_id`参数')
    return True, 'ok', application_openapi_service.get_relational_dashboards([portal_id])


@api.open_route.post('/portal/upload_auth')
def portal_upload_auth(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /openapi/portal/upload_auth 提交门户第三方用户报表授权信息
    @apiGroup  openapi
    @apiBodyParam {
       "auth_id": "",
       "portal_id": "",
       "dashboard_auths{报表权限数据}": [
           {
               "biz_code": "123",
               "auth": "view,download"
           }
       ]
    }
    @apiResponse  200 {
        "result": true,
        "msg": "ok"
        "session_id": "52261efdc51df057"
    }
    */
    """
    auth_id = kwargs.get('auth_id')
    dashboard_auths = kwargs.get('dashboard_auths')
    portal_id = kwargs.get('portal_id')
    if not auth_id:
        return False, '缺少`auth_id`参数', ''
    if not dashboard_auths:
        return False, '缺少`dashboard_auths`参数', ''
    if not portal_id:
        return False, '缺少`portal_id`参数', ''
    try:
        session_id = application_auth_service.upload_auth(auth_id, portal_id, dashboard_auths)
    except UserError as ue:
        return False, str(ue.message), ''
    return True, 'ok', {'session_id': session_id}


@api.open_route.get('/user/info')
def get_user_info(**kwargs):
    """
    /*
    @apiVersion 1.0.3
    @api {get} /openapi/user/info 获取用户基础信息
    @apiGroup openapi
    @apiParam query {string} user_account 用户账户
    @apiParam query {string} user_id 用户ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": {
            "id{用户ID}": "39ee0f68-7122-b8a2-c3a6-b0131c5f5af8",
            "name{用户名称}": "tanzy_test_no_group",
            "account{账户名}": "tanzy_test_no_group",
            "mobile{手机号}": "",
            "email{邮箱}": "<EMAIL>",
            "last_login_time{最后登录时间}": **********,
            "groups{用户组信息}": {
                "groups{所属用户组}": [
                    {
                        "id{用户组ID}": "39ee0fd9-86be-5fb5-f78e-01ce24462743",
                        "name{用户组名称}": "DMP\u9879\u76ee\u7ec4",
                        "parent_id{父级用户组ID}": "39ee0fd8-817f-73bb-ac88-a499eb25a7df",
                        "code{用户组层级代码}": "0001-0050-0003-0001-"
                    }

                ],
                "parent_groups_data{父级用户组数据，用于构建用户组的父级拼接展示}": [
                    {
                        "id{用户组ID}": "********-0000-0000-1111-********0000",
                        "name{用户组名称}": "test",
                        "parent_id{父级用户组ID}": "********-0000-0000-0000-********0000",
                        "code{用户组层级代码}": "0001-"
                    }
                ]
            }
            "roles{角色信息}": [
                {
                    "id{角色ID}": "aaaa-bbb-cccc-ddd"，
                    "name{角色名称}": "编辑者",
                    "description{描述}": "描述",
                    "account_mode{账号模式}": "DMP|ERP|DOMAIN"
                }
            ]
        }
    }
    */
    """
    account = kwargs.get('user_account')
    userid = kwargs.get('user_id')
    if not account and not userid:
        raise UserError(400, '请至少指定user_account或user_id一个参数')
    return True, 'success', user_service.get_user_info(user_id=userid, account=account)


# pylint: disable=W0613
@api.open_route.post('/celery/sync-dashboard')
def sync_dashboard(request, **kwargs):
    """
    /*
    @apiVersion 1.0.2
    @api {post} /openapi/celery/sync-dashboard 同步看板到特定的租户
    @apiGroup  openapi
    @apiBodyParam {
       "remark{备注}": "备注",
       "action{支持create/update}": "create",
       "project_code{源项目代码}": "dev",
       "dashboard_id{源看板的id}": "39e4108b-74a6-7964-f4e7-0350b1bae43d",
       "target_project_codes{目标项目代码(数组)}": ["39e4108b-74a6-7964-f4e7-0350b1bae43d"],
       "apply_all{是否同步到所有的租户}": true
    }
    @apiResponse  200 {
        "result": true,
        "msg": "ok"
    }
    */
    """

    app_celery.sync_dashboard.apply_async(kwargs=kwargs)
    return True, 'ok'


@api.open_route.post('/message/add')
def message_add(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /openapi/message/add 添加消息
    @apiGroup  openapi
    @apiBodyParam {
       "project_code{源项目代码}": "dev",
       "source_id{消息来源ID,同一个来源ID只存一条记录}":"39e51f8a-dcd3-e2fe-ce68-d6d671818f5c",
       "source{支持数据源/数据集/单图/报告/应用门户/订阅/组件中心/模板中心/版本更新/通知}":"数据源",
       "type{支持系统消息/运营消息/第三方业务消息}":"系统消息",
       "title{消息标题}": "标题",
       "url{消息处理URL}":"flow/ops-instance/39e5f2bd-8fab-5c6f-8861-9da10d7774e6/%E4%B8%AA%E4%BA%BA%E6%95%B0%E6%8D%AE"
    }
    @apiResponse  200 {
        "result": true,
        "msg": "",
        "data": "message_id"
    }
    */
    """
    return True, '', message_service.message_add(MessageModel(**kwargs))


@api.open_route.post('/flow/enable')
def flow_enable(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api    {get} /openapi/flow/enable 启用流程定时调度
    @apiGroup    openapi
    @apiBodyParam {
        "id{流程ID}": "39e51f8a-dcd3-e2fe-ce68-d6d671818f5c",
        "queue_name{消息队列名称}":"Flow-offline"
    }
    @apiResponse 200{
        "result": true,
        "msg": "启用成功",
        "data": true
    }
    **/
    """
    queue_name = (
        kwargs.get("queue_name") if kwargs.get("queue_name") else config.get('RabbitMQ.queue_name_flow_offline')
    )
    return True, '启用成功', flow_service.enable_flow(kwargs.get('flow_id'), queue_name=queue_name,
                                                      command=kwargs.get("command"))


@api.open_route.post('/flow/run')
def flow_run(**kwargs):
    """
    @api    {get} /openapi/flow/run 运行flow
    @apiBodyParam {
        "id{流程ID}": "39e51f8a-dcd3-e2fe-ce68-d6d671818f5c"
    }
    @apiResponse 200{
        "result": true,
        "msg": "运行成功",
        "data": true
    }
    """
    queue_name = (
        kwargs.get("queue_name") if kwargs.get("queue_name") else config.get('RabbitMQ.queue_name_flow_offline')
    )
    return True, '运行成功', flow_service.run_flow(kwargs.get('flow_id'), queue_name=queue_name)


@api.open_route.post('/rundeck/job/run')
def rundeck_job_run(**kwargs):
    """
    rundeck任务立即运行
    :param kwargs:
    :return:
    """
    from components.rundeck import CommonRunDeckScheduler

    job_id = kwargs.get("job_id")
    if not job_id:
        return False, "未执行", "缺少job_id参数"

    return True, "运行成功", CommonRunDeckScheduler().run_job(job_id=job_id)


@api.open_route.post('/flow/delete/schedule')
def flow_delete_schedule(flow_id):
    """
    /**
    @apiVersion 1.0.1
    @api    {get} /openapi/flow/flow/delete/schedule 删除流程调度
    @apiGroup    openapi
    @apiBodyParam {
        "flow_id{流程ID}": "39e51f8a-dcd3-e2fe-ce68-d6d671818f5c"
    }
    @apiResponse 200{
        "result": true,
        "msg": "删除成功",
        "data": true
    }
    **/
    """
    return True, '删除成功', flow_service.delete_flow_schedule(flow_id)


@api.open_route.get('/dashboard/all')
def get_all_dashboard(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api    {get} /openapi/dashboard/all 报告列表
    @apiGroup    openapi
    @apiResponse 200{
        "result": true,
        "msg": "删除成功",
        "data": [
            {
                "id":"报告ID",
                "name":"报告名称",
                "cover":"报告截图",
                "description":"报告描述",
                "platform":"平台",
                "icon":"图标",
                "is_multiple_screen":"是否多屏"
            }
        ]
    }
    **/
    """
    # return True, '', dashboard_repository.get_all_dashboard()
    return True, '', dashboard_openapi_service.get_report_list(**kwargs)


@api.open_route.get('/dashboard/page_chart/download/list')
def get_dashboard_page_chart_download_list(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api    {get} /openapi/dashboard/page_chart/download/list 报告列表
    @apiGroup    openapi
    @apiBodyParam {
        "user_id{用户id}": "39e51f8a-dcd3-e2fe-ce68-d6d671818f5c",
        "is_admin{是否管理员}": "0",
        "per_page": "100",
        "page": "1"
    }
    @apiResponse 200{
        "result": true,
        "msg": "操作成功",
        "data": {
            "data": [
                {
                    "id":"记录ID",
                    "dashboard_name":"报告名称",
                    "user_id":"用户id",
                    "download_time":"导出时间",
                    "status":"导出状态",
                    "download_url":"导出文件url地址"
                }
            ],
            "total": 1000
        }
    }
    **/
    """
    return True, '', download_service.get_dashboard_page_chart_download_list(**kwargs)


@api.open_route.post('/message/flow/notify')
def message_flow_notify(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /openapi/message/flow/notify ERP数据集消息通知
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
        "subject_id{主题标识,36个字符的UUID，类似:39e3e5eb-878a-386f-c9d3-623575a27efb}": "",
        "subject{主题名称}": "",
        "version{主题的版本号}": "",
        "created_on{创建时间(Unix timestamp)}": "",
        "file": {
            "file_url{主题包文件地址}": "",
            "file_type{文件类型，默认zip}": "zip"
        }
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data{请求ID}": "request_id"
    }
    **/
    """
    dataset_subject_service.receive_message_upset_dataset_subject(**kwargs)
    return True, 'success', kwargs.get('subject_id')


@api.open_route.post('/dataset/subject/add')
def add_subject_dataset(body):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /openapi/dataset/subject/add OpenAPI添加数据数据集
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
        "subject_table_id{主题数据表dataset_subject_table标识,36个字符的UUID}": ""
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data{数据集ID}": ""
    }
    **/
    """
    return True, 'success', external_query_service.multi_add_subject_dataset(body)


@api.open_route.get('/dataset/get_cited_fields')
def get_cited_fields_by_dataset(dataset_id: str):
    """
    /**
    @apiVersion 1.0.1
    @api {get} /openapi/dataset/get_cited_fields 获取数据集引用字段
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiParam query {string} dataset_id {数据集id}
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data{数据集ID}": ["id1", "id2"]
    }
    **/
    """
    return True, 'success', external_query_service.get_cited_fields_by_dataset(dataset_id)


@api.open_route.post('/dataset/update_subject_fields')
def update_subject_fields(dataset_id: str):
    """
    /**
    @apiVersion 1.0.1
    @api {get} /openapi/dataset/update_subject_fields 更新主题包数据集字段
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
        "dataset_id{36个字符的UUID}": ""
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": true
    }
    **/
    """
    return True, 'success', dataset_version_service.compare_struct_update(dataset_id)


@api.open_route.get('/dataset/replace_sql')
def get_union_replace_sql(dataset_id: str):
    """
    /**
    @apiVersion 1.0.1
    @api {get} /openapi/dataset/replace_sql 获取组合数据集replace_sql
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
        "dataset_id{36个字符的UUID}": ""
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": "select id from aa"
    }
    **/
    """
    return True, 'success', dataset_service.get_union_replace_sql(dataset_id)


@api.open_route.post('/rundeck_job/upset')
def register_rundeck_job(**kwargs):
    """
    /**
    @apiVersion 1.0.3
    @api {post} /openapi/rundeck_job/upset 注册rundeck job定时任务
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
        "jobs{流程配置}": [{
            "id{流程ID，唯一标识}": "",
            "name{流程名称}": "",
            "description{流程描述，可选参数，不填写则取值name}": "",
            "schedule{流程调度信息，crontab定时调度规则}": "",
            "schedule_enabled{是否开启调度，可选参数，不传则默认开启}": "",
            "group{job分组，可选参数，不传则默认取值name}": ""
        }],
        "queue_name{消息队列名称}": ""
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": true
    }
    **/
    """
    return True, 'success', common_rundeck_flow_service.upset_sysevent_rundeck_job(kwargs)


@api.open_route.post('/data_source/update')
def data_source_update(body):
    """
    /**
    @apiVersion 1.0.2
    @api {post} /openapi/data_source/update 更新MYSQL数据源的连接信息
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
            "tenant_code{企业代码}": {
            "code{数据源编码}": {
                "name{数据源名称(必填)}": "",
                "description{数据源描述}": "",

                "database{数据库名称(必填)}":"",
                "host{主机地址(必填)}":"",
                "user{用户名(必填)}":"test",
                "password{密码(必填)}":"",
                "port{端口(必填)}":3306,

                "use_ssh{是否使用ssh, 0或者1}":0,
                "ssh_host{ssh地址}":"",
                "ssh_user{ssh用户名}":"",
                "ssh_password{ssh用户密码}":"",
                "ssh_port{ssh端口}":"",

                "table_name_prefix{表名过滤（前缀）}":""
            }
        }
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": true
    }
    **/
    """
    return True, '', external_data_source_service.update_mysql_data_source_conn_str(body)


@api.open_route.post('/data_source/replace_into')
def data_source_replace_into(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /openapi/data_source/replace_into 替换或新增MYSQL数据源的连接信息
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
        "tenant_code{企业代码(必填)}": "",
        "data_source{数据源}": {
            "id{数据源ID(必填-长度36)}": "",
            "code{数据源唯一编码(必填)}": "",
            "name{数据源名称(必填)}": "",
            "description{数据源描述}": "",
            "database{数据库名称(必填)}":"",
            "host{主机地址(必填)}":"",
            "user{用户名(必填)}":"test",
            "password{密码(必填)}":"",
            "port{端口(必填)}":3306,
            "use_ssh{是否使用ssh, 0或者1}":0,
            "ssh_host{ssh地址}":"",
            "ssh_user{ssh用户名}":"",
            "ssh_password{ssh用户密码}":"",
            "ssh_port{ssh端口}":"",
            "table_name_prefix{表名过滤（前缀）}":""
        }
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": 1
    }
    **/
    """
    return True, 'success', external_data_source_service.replace_into_mysql_data_source(kwargs)


@api.open_route.get('/get_version')
def get_version():
    return True, '', {"version": "v3.0.3.190408", "time": "2019-04-12"}


@api.open_route.post('/user_sync/setting')
def user_sync_setting(**kwargs):
    """
    /**
    @apiVersion 1.0.3
    @api {post} /openapi/user_sync/setting 用户组织同步配置
    @apiGroup openapi
    @apiHeader {string} X-TENANT 租户代码(企业代码)
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取
    @apiBodyParam {
        "tenant_code{企业代码(必填)}": "",
        "encrypt{加密方式默认md5(必填)}":"md5",
        "user{用户属性配置(必填)}":{
            "source_dataset_id{用户数据集配置(必填)}":"39eeaf37-c682-c140-6d99-3661f7c76686",
            "field_relation":{
                "id{用户ID(必填)}":"ID_3400796682",
                "name{用户名(必填)}":"NAME_3759278814",
                "account{账号(必填)}":"ACCOUNT_4345433130",
                "pwd{密码-可选}":"PWD_3579644552",
                "group_id{所属组织ID-可选}":"GROUPID_4567075990",
                "email{邮箱-可选}":"EMAIL_3947039557",
                "mobile{手机-可选}":"MOBILE_4146793397",
                "is_disabled{账号有效状态-int类型0=启用1=禁用-可选}":""
            }
        },
        "user_group{组织属性配置(必填)}":{
            "source_dataset_id{组织数据集配置(必填)}":"39efaba7-730d-afdd-8e95-dcba7bb765cd",
            "field_relation":{
                "id{组织ID(必填)}":"ID_6269242875",
                "name{组织名称(必填)}":"NAME_6788877975",
                "parent_id{上级组织ID(必填)}":"ID_4298706019",
                "hierarchy{层级(必填)}":"HIERARCHY_8154779678"
            }
        }
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": true
    }
    **/
    """
    return True, 'success', external_user_service.update_sync_setting(kwargs)


@api.open_route.post('/dataset_subject/inspect_result')
def receive_dataset_subject_inspect_result(request, body):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /openapi/dataset_subject/inspect_result 主题数据集全链路监控接收节点巡检结果
    @apiGroup openapi
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取,或向对接相关人员获取
    @apiBodyParam {
        "subject_id{主题包ID，必填}":"主题包ID，唯一标识",
        "project_code{租户代码, 必填}": ""
        "version{版本号, 必填}": ""
        "inspection_result{巡检结果,list类型，必填}":[
            {
                "node_type{节点类型，可选值：业务数据准备、数据源取数、HighData数据清洗、HighData数据推送、DMP数据存储、DMP数据报告，必填}":"数据源取数",
                "node_name{节点业务对象名称}": "",
                "check_items{节点包含的检查项，必填}":[
                    {
                        "name{检查项名称，必填}":"营销屏清洗检查",
                        "result{检查结果, 可选值：成功、失败、警告，必填}":"成功",
                        "msg{检查项结果内容，必填}":"清洗成功",
                        "url{检查想结果详情页面连接url，建议巡检失败提供，非必填}":""
                    }
                ],
                "check_time{节点巡检时间，日期格式: yyyy-mm-dd hh:mm:ss，必填}":"2019-11-28 16:21:00"
            }
        ]
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": true
    }
    **/
    """
    if not body.get('subject_id'):
        raise UserError(message='缺少主题包subject_id')
    return (True, 'success', external_query_service.receive_dataset_subject_inspect_result(body))


@api.open_route.post('/dataset_subject/inspect_result_api')
def receive_dataset_subject_inspect_result_api(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /openapi/dataset_subject/inspect_result 主题数据集全链路监控接收节点巡检结果
    @apiGroup openapi
    @apiParam query {string} apikey OpenAPI调用的apikey,从dmp-admin获取,或向对接相关人员获取
    @apiParam query {string} code 租户代码, 必填
    @apiParam query {string} subject_id 主题包ID，必填
    @apiBodyParam {
        "inspection_result{巡检结果,list类型，必填}":[
            {
                "node_type{节点类型，可选值：业务数据准备、数据源取数、HighData数据清洗、HighData数据推送、DMP数据存储、DMP数据报告，必填}":"数据源取数",
                "node_name{节点业务对象名称}": "",
                "check_items{节点包含的检查项，必填}":[
                    {
                        "name{检查项名称，必填}":"营销屏清洗检查",
                        "result{检查结果, 可选值：成功、失败、警告，必填}":"成功",
                        "msg{检查项结果内容，必填}":"清洗成功",
                        "url{检查想结果详情页面连接url，建议巡检失败提供，非必填}":""
                    }
                ],
                "check_time{节点巡检时间，日期格式: yyyy-mm-dd hh:mm:ss，必填}":"2019-11-28 16:21:00"
            }
        ]
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": true
    }
    **/
    """
    kwargs["project_code"] = kwargs.get("code")
    if not kwargs.get('subject_id'):
        raise UserError(message='缺少数据源subject_id')
    return (True, 'success', external_query_service.receive_dataset_api_inspect_result(kwargs))


@api.open_route.post('/dataset_subject/inspection/run')
def run_dataset_subject_inspection(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /dataset_subject/inspection/run
    _subject/inspection/run 向DMP下发主题数据集巡检任务接口
    @apiGroup openapi
    @apiBodyParam {
        "subject_id{主题包ID，必填}":"主题包ID，唯一标识",
        "project_code{租户代码, 必填}":"",
        "data_source_type{数据源类型, 必填}":"",
    """
    return True, 'success', dashboard_openapi_service.run_dataset_subject_inspection(kwargs)


@api.open_route.post('/dataset_subject/inspection/send_notify')
def send_dataset_subject_inspection_notify(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /openapi/dataset_subject/inspection/send_notify 主题包巡检异常告警通知
    @apiGroup openapi
    @apiBodyParam {
        "subject_id{主题包ID，必填}":"主题包ID，唯一标识",
    """
    subject_id = kwargs.get('subject_id')
    return True, 'success', subject_inspection_openapi_service.send_notify(subject_id)


@api.open_route.get('/user/groups')
def get_groups(**kwargs):
    """
    /*
    @apiVersion 1.0.3
    @api {get} /openapi/user/groups 获取所有组
    @apiGroup openapi
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "id": "aaaa-bbbb-cccc-dddd-eeee", //string 用户组ID
                "name": "组名称", //string 组名称
                "parent_id": "dddd-eeee-ffff-hhhh", //string 父级组ID
                "account_mode": "DMP/ERP/DOMAIN" //账户模式
            }
        ]
    }
    */
    """
    return True, 'success', user_group_service.get_user_group_list()


@api.open_route.get('/user/roles')
def get_roles(**kwargs):
    """
    /*
    @apiVersion 1.0.3
    @api {get} /openapi/user/roles 获取所有角色列表
    @apiGroup openapi
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "id": "aaaa-bbbb-cccc-dddd-eeee", //string 角色ID
                "name": "组名称", //string 角色名称
                "description": "描述", //string 角色描述
                "account_mode": "DMP/ERP/DOMAIN" //账户模式
            }
        ]
    }
    */
    """
    return True, 'success', user_service.get_role_list()


@api.open_route.get('/screen/dashboards')
def screen_included_dashboards(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /openapi/screen/dashboards 获取多屏内报告列表数据
    @apiGroup openapi
    @apiParam query {string} screen_id 多屏Id
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "biz_code{看板业务代码}": '913d30b9d7bc406d94cd538eb85f0857',
                'id{看板标识id}': '39f41e03-eedc-cad9-2b36-a167069120ae',
                'name{看板名称}': '看板名称'
            }
        ]
    }
    */
    """
    screen_id = kwargs.get('screen_id')
    if not screen_id:
        raise UserError(400, '缺少`screen_id`参数')
    return True, 'ok', screen_openapi_service.get_relational_dashboards_info(screen_id)


@api.open_route.post('/screen/upload_auth')
def screen_upload_auth(request, **kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /openapi/screen/upload_auth 提交多屏第三方用户报表授权信息
     @apiGroup  openapi
     @apiBodyParam {
        "auth_id": "",
        "dashboard_auths{报表权限数据}": [
            {
                "biz_code": "123",
                "auth": "view,download"
            }
        ]
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
         "data": {
            "session_id": "52261efdc51df057"
         }
     }
     */
    """
    auth_id = kwargs.get('auth_id')
    dashboard_auths = kwargs.get('dashboard_auths')
    if not auth_id:
        return False, '缺少`auth_id`参数', ''
    if not dashboard_auths:
        return False, '缺少`dashboard_auths`参数', ''
    try:
        session_id = screen_auth_service.upload_auth(auth_id, dashboard_auths)
    except UserError as ue:
        return False, str(ue.message), {}
    return True, 'ok', {'session_id': session_id}


@api.open_route.post('/dataset/operation/flow')
def dataset_operation_flow(request, **kwargs):
    """
    /*
     @apiVersion 1.0.0
     @api {post} /openapi/screen/upload_auth 提交多屏第三方用户报表授权信息
     @apiGroup  openapi
     @apiBodyParam {
        "project_code": "project_code",
        "status": 1
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
         "data": {
         }
     }
     */
    :param request:
    :param kwargs:
    :return:
    """
    from dataset.services import dataset_define_service
    # status: 1 开启   2 关闭
    status = int(kwargs.get("status", 0))
    g.code = kwargs.get("project_code")
    g.account = "admin"
    if status not in [OperationFlowType.START.value, OperationFlowType.STOP.value]:
        raise UserError(message="不支持的参数类型，status=1 开启， status=2关闭")
    dataset_define_service.update_operation_flow(int(status))
    return True, '', "ok"


@api.open_route.post('/api_dataset/get_data')
def get_data_of_api_dataset(request, **kwargs):
    """
    /*
     @apiVersion 1.0.0
     @api {post} /api_dataset/get_data 提交多屏第三方用户报表授权信息
     @apiGroup  openapi
     @apiBodyParam {
        "project_code": "project_code",
        "status": 1
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
         "data": {
         }
     }
     */
    :param request:
    :param kwargs:
    :return:
    """
    # from loguru import logger
    # logger.error(f"api数据集调度，数据集id: {kwargs.get('dataset_id')}")
    # from dataset.services.dataset_service import get_api_dataset_result_data
    # # 取数业务放在协程里面异步处理
    # from gevent import monkey;
    # monkey.patch_socket()
    # import gevent
    # task = gevent.spawn(get_api_dataset_result_data, kwargs)
    # task.start()
    app_celery.run_api_dataset_schedule.apply_async(kwargs=kwargs, queue='parser')
    return True, '已经提交异步任务', "ok"


@api.open_route.post('/rabbitmq/purge')
def purge_queue(request, **kwargs):
    """
    清理队列
    """
    name = kwargs.get("queue_name")
    if not name:
        raise UserError(message="参数queue_name不能为空")
    res = utils_service.rabbitmq_purge(name)
    return True, '', res


@api.open_route.post('/dashboard_chart/delete_dashboard')
def delete_dashboard(request, **kwargs):
    """
    删除报告,回收报告
    """

    dashboard_ids = kwargs.get("dashboard_ids")
    if not dashboard_ids:
        raise UserError(message="参数dashboard_ids不能为空")
    dashboard_ids = str.split(dashboard_ids, ",")
    from dashboard_chart.services import dashboard_service
    res = dashboard_service.delete_dashboards(dashboard_ids)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_dashboard',
            'content': f'{",".join(dashboard_ids)}] 撤回成功',
        }
    )
    return True, '', res


@api.open_route.post('/msg/content')
def get_subscribe_msg_by_user(**kwargs):
    """
    获取简讯的正文内容接口
    :param kwargs:
    :return:
    """
    feed_id = kwargs.get("feed_id")
    account = kwargs.get("account")
    from feed.services.dashboard_feeds_service import get_email_message_by_user
    data = get_email_message_by_user(feed_id, account)
    return True, 'ok', data


@api.open_route.post('/publish_report')
def publish_report(**kwargs):
    """
    后台发布报表
    {
        "tenant_code": "企业代码",
        "dashboard_id": "需要发布的报表Id",
        "status": "0=>取消发布，1=>发布"
    }
    """
    from user.services import user_service

    status = kwargs.get("status")
    dashboard_id = kwargs.get("dashboard_id")
    code = g.account = g.code
    g.userid = user_service.get_user_id_by_account(g.account)

    if not all([dashboard_id, code]):
        raise UserError(message="缺少参数")
    if status not in [0, 1, '0', '1']:
        raise UserError(message="参数错误")

    from app_celery import async_release_message_push
    from dashboard_chart.services import released_dashboard_service, dashboard_service
    from dashboard_chart.models import ReleaseModel

    model = ReleaseModel(
        id=dashboard_id,
        status=int(status),
        type_access_released=3
    )
    re = released_dashboard_service.release_with_process(model)
    if re:
        dashboard = dashboard_service.get_dashboard_info(model.id)
        if config.get("ThirdParty.message_push_api"):
            async_release_message_push.delay(model.status, dashboard, g.code)
    return True, 'ok', re


@api.open_route.get('/get_dashboard')
def get_dashboard(**kwargs):
    """
        获取单个报告的元数据
    """
    dashboard_id = kwargs.get("dashboard_id")
    if not dashboard_id:
        return False, '报告ID不能为空', ''
    from dashboard_chart.services import dashboard_service
    fields = ['id', 'name', 'type', 'parent_id', 'level_code', 'icon', 'platform', 'layout_type', 'is_multiple_screen',
              'status', 'cover', 'biz_code']
    dashboard = dashboard_service.get_dashboard_info(dashboard_id, fields)
    return True, 'ok', dashboard


@api.open_route.post('/hd/local/init')
def hd_local_init(**kwargs):
    """
    初始化hd本地模式的相关表
    """
    from hd_upgrade.services.hd_local_init_service import HDLocalInit
    from_init = kwargs.get("from_init")
    if from_init not in ['erpsaas', 'erpop']:
        raise UserError(message="from_init参数错误")
    app_code_list = kwargs.get("app_code_list", [])
    others = kwargs.get("others", {})
    init_1_5_app = kwargs.get('init_1_5_app', 0)
    hd_init = HDLocalInit(from_init=from_init, app_code_list=app_code_list,init_1_5_app=init_1_5_app, others=others)
    return True, 'ok', hd_init.init()


@api.open_route.get('/init/dataset/run')
def init_dataset_flow():
    from hd_upgrade.services.hd_local_init_service import HDLocalInit
    dataset_init = HDLocalInit()
    return True, 'ok', dataset_init.init_dataset_run()


@api.open_route.get('/fresh/dataset/is_complex')
def fresh_dataset_is_complex(**kwargs):
    """
    初始化升级过来的数据集是否是复杂sql
    """
    from base import repository
    from dataset.services.dataset_service import DatasetMysoftNewErp
    from components.data_center_api import get_new_erp_datasource_model
    from dataset.models import DatasetModel
    from dataset.cache import dataset_meta_cache

    datasource_model = get_new_erp_datasource_model()

    datasets = repository.get_list("dataset", {"is_complex": 0, "type": "SQL"}) or []

    for item in datasets:
        try:
            dataset_model = DatasetModel(**item)
            dataset_service = DatasetMysoftNewErp(dataset_model, datasource_model)
            is_complex = dataset_service.get_sql_is_complex()
            repository.update("dataset", {"is_complex": is_complex}, {"id": item.get('id')})
            dataset_meta_cache.del_dataset_cache(item.get('id'))
        except Exception as e:
            repository.update("dataset", {"is_complex": 0}, {"id": item.get('id')})
            logging.error("dataset_id: {}, error: {}".format(item.get('id'), str(e)))
            logging.error(traceback.print_exc())


@api.open_route.get('/indicator_model/callback')
def indicator_model_callback(**kwargs):
    """
    指标模型同步
    :param kwargs:
    :return:
    """
    from dataset.services import indicator_service

    return indicator_service.indicator_model_callback(**kwargs)


@api.open_route.get('/pulsar_dataset/sync')
def pulsar_dataset_sync(**kwargs):
    """
    数芯数据集同步
    :param kwargs:
    :return:
    """
    import app_celery

    code = kwargs.get('tenant_code')
    # 创建实例
    from dataset.services import indicator_service

    instance_id = indicator_service.create_flow_instance()
    app_celery.sync_indicator_model.apply_async(kwargs={'code': code, 'instance_id': instance_id}, queue='celery-slow')

    return True, 'ok', ""


@api.open_route.get('/self_service/report_list/sort')
def sort_self_service_list(**kwargs):
    """
    排序
    :param kwargs:
    :return:
    """
    from dashboard_chart.services import dashboard_service

    g.account = g.code

    # data = dashboard_service.get_self_report_list(**kwargs)
    data = dashboard_service.get_all_self_report_list(**kwargs)

    def _tree_sort(data_list):
        start = 0
        for item in data_list:
            if item.get('sub'):
                _tree_sort(item['sub'])
            start += 1
            repository.update_data('dashboard', {'sort': start}, {"id": item.get('id')})

    _tree_sort(data['tree'])

    return True, 'ok', ""


@api.open_route.get('/get_release_dashboard_data')
def fresh_dataset_is_complex(**kwargs):
    """
    获取报表实时的运行时数据
    """
    from dashboard_chart.services.released_dashboard_service import (
        ReleaseModel,
        _handle_release_dashboard,
        _get_all_release_dashboard,
        get_db
    )

    dashboard_id = kwargs.get('dashboard_id', '')
    if not dashboard_id:
        return False, '没有dashboard_id', {}

    def release_dashboard(current_model: ReleaseModel):
        """
        dashboard_chart/services/released_dashboard_service.py:1015
        """
        all_release_dashboard = _get_all_release_dashboard(current_model)
        with get_db() as conn:
            try:
                result = {}
                for release_dashboard in all_release_dashboard:
                    model = release_dashboard.get('model')
                    dashboard = release_dashboard.get('dashboard')
                    _handle_release_dashboard(conn, model, dashboard)

                    sql1 = """select * from dashboard_released_snapshot_dashboard where snapshot_id = %(dashboard_id)s"""
                    sql2 = """select * from dashboard_released_snapshot_chart where snapshot_id = %(dashboard_id)s"""
                    dashboard_released_snapshot_dashboard = conn.query(sql1, params={'dashboard_id': dashboard_id})
                    dashboard_released_snapshot_chart = conn.query(sql2, params={'dashboard_id': dashboard_id})
                    result.update({
                        dashboard_id: {
                            'dashboard': dashboard_released_snapshot_dashboard,
                            'chart': dashboard_released_snapshot_chart,
                        }
                    })
                conn.rollback()
                return result
            except Exception as e:
                conn.rollback()
                logging.error(f'数据报告发布函数异常: {traceback.format_exc()}')
                raise UserError(message="数据报告发布函数异常：{}".format(str(e))) from e
            finally:
                conn.rollback()

    model = ReleaseModel(id=dashboard_id, status=1, type_access_released=0)
    return True, '获取成功', release_dashboard(model)


@api.open_route.post('/move/dataset')
def move_dataset(**kwargs):
    from dataset.services.dataset_define_service import move_dataset
    from dmplib.constants import ADMIN_ROLE_ID

    g.customize_roles = [ADMIN_ROLE_ID]
    move_dataset(kwargs.get('dataset_id'), kwargs.get('target_dataset_id'))

    return True, 'suc', ''


@api.open_route.post('/move/dashboard')
def move_dataset(**kwargs):
    from dashboard_chart.services import dashboard_service
    from dmplib.constants import ADMIN_ROLE_ID

    g.customize_roles = [ADMIN_ROLE_ID]
    if kwargs.get('application_type') in [1, '1']:
        dashboard_service.move_dashboard.set_permissions('self_service-edit')
    re = dashboard_service.move_dashboard(kwargs.get('dash_id'), kwargs.get('target_dash_id'), is_open_api=True)

    return True, '移动成功', re


@api.open_route.get('/clean/data_table')
def clean_data_table(**kwargs):
    """
    清理数据集垃圾表
    """
    import app_celery
    project_code = g.code
    if not project_code:
        return False, '租户code不能为空', {}
    kwargs['project_code'] = g.code
    kwargs['openapi'] = True
    app_celery.clean_dataset_history_table.apply_async(kwargs=kwargs)
    return True, "ok", '已注册celery任务,具体信息请查看dmp_celery日志'


@api.open_route.post('/reg/key_dashboard_task')
def key_dashboard_task():
    """
    使用场景：admin开启租户重点大屏管理功能时，调用此接口
    作用：注册当前环境所有租户重点大屏数量统计的任务
    :return:
    """
    from dashboard_chart.services import key_dashboard_service
    key_dashboard_service.init_key_dashboard_stat_task()
    return True, '注册成功', []


@api.open_route.post('/add/tenant_mip_config')
def add_tenant_mip_config(host, key, secret):
    """
    一键开户时候的添加租户级别集成平台配置
    :return:
    """
    from system.services import system_setting_service

    env_mip_host = AppHosts.get(SkylineApps.IPAAS) or ''
    env_mip_host = env_mip_host.strip().strip('/')
    host = host.strip().strip('/')
    key = key.strip()
    secret = secret.strip()
    mip_kwargs = {
        "id": "",
        "item": "basic_data_platform",
        "value": json.dumps({
            'client_id': key,
            'client_secret': secret,
            'host': host,
            'report_authorize_url': '/api/basicdata/CheckUserReportAuthorize',
        }),
        "name": "",
        "description": "",
        "category": "ingrate_platform"
    }
    logging.error(f'接收到配置，开始租户的集成平台配置, env_mip_host: {env_mip_host}, host: {host}')

    if env_mip_host:
        if env_mip_host == host:
            logging.error('租户的host配置与环境配置的host相同，跳过配置租户的集成平台配置！')
        else:
            logging.error(f'开始配置租户的集成平台配置<与环境配置host不一致>')
            system_setting_service.add_or_update_system_setting(mip_kwargs)
    else:
        logging.error(f'开始配置租户的集成平台配置<环境配置host配置为空>')
        system_setting_service.add_or_update_system_setting(mip_kwargs)

    logging.error(f'完成配置租户的集成平台配置')
    return True, 'ok', []


@api.open_route.post('/add_superportal_info')
def add_superportal_info(**kwargs):
    """
    一键开户时记录租户级的超级APP参数
    :return:
    """
    from system.services import system_setting_service
    from components.common_service import check_superportal_host

    host = kwargs.get("host", "").strip().strip('/')
    if not host:
        return False, '超级APP host参数不能为空'

    category = "integrate"
    item = "superportal_info"
    superportal_info = {
        "host": host
    }
    mip_kwargs = {
        "id": "",
        "item": item,
        "value": json.dumps(superportal_info, ensure_ascii=False),
        "name": "超级APP集成",
        "description": "超级APP集成的host",
        "category": category
    }
    logging.error(f'接收到配置，开始租户的超级APP参数配置, superportal_info: {json.dumps(kwargs, ensure_ascii=False)}')

    check_rs = check_superportal_host(host)
    if not check_rs:
        system_setting_service.add_or_update_system_setting(mip_kwargs)
    else:
        # 删除租户级可能已存在的配置
        system_setting_service.delete_system_setting_item(category, item)
        logging.error(f'租户的超级APP host参数与环境级一致，无需写入租户库')

    logging.error('完成配置租户的超级APP参数配置')
    return True, 'ok', []


@api.open_route.post('/old_erp_report_list')
def key_dashboard_task():
    """
    使用场景：admin升级erp历史租户时候用来获取老erp报告的列表
    :return:
    """
    from ppt.external_service import get_menu_erp_report_list
    g.account = g.code
    user = repository.get_data("user", conditions={"account": g.account}) or {}
    g.userid = user.get('id', '')

    data, err = get_menu_erp_report_list()
    if err:
        return False, err, []
    else:
        return True, 'ok', data


@api.open_route.post('/flow/enable_by_project')
def flow_enable_by_project(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api    {get} /openapi/flow/enable_by_project 租户所有启用的数据集, 看板拍照, 手工填报 调度任务注册
    @apiGroup    openapi
    **/
    """
    kwargs["code"] = code = g.code
    app_celery.reg_rundeck_task.apply_async(kwargs=kwargs)
    # 按租户操作类型进行 flow 任务处理
    action = kwargs.get("action")
    domain = config.get('Domain.dmp', '')
    key = f"reg_rundeck_task_{action}:{code}"
    data = {"url": f"{domain}/api/monitor/tmp_log2?uuid={key}"}
    return True, 'ok', data


@api.open_route.post('/portal/sync_application')
def sync_application(request, **kwargs):
    """
    批量发布门户
    :param request:
    :param kwargs:
    :return:
    """
    application_ids = kwargs.get("application_ids")
    if not application_ids:
        raise UserError(message="参数application_ids不能为空")
    res, data = application_service.enable_application_by_api(application_ids)
    log = '成功' if res else f'失败:{data}'
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'enable_app',
            'content': f'{",".join(application_ids)}] 门户发布{log}',
        }
    )
    return res, '', data
