#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from urllib.parse import urlencode

from open_api.repositories import screen_repositories
from dashboard_chart.models import DashboardOpenAPIEmbeddedQueryModel
from dashboard_chart.services import dashboard_openapi_service
from rbac.repositories import data_permission_repository
from dmplib.constants import ADMIN_ROLE_ID
from app_menu.services.external_service import recursive_get_target_dashboard_ids
from dashboard_chart.repositories import external_dashboard_repository
from base import repository
from dmplib.utils.errors import UserError
from dmplib import config
from base.enums import ApplicationType


def get_relational_dashboard_ids(screen_id):
    dashboard_ids = get_releation_dashboard(screen_id)
    all_target_dashboard_ids = set()
    recursive_get_target_dashboard_ids(dashboard_ids, all_target_dashboard_ids)
    if all_target_dashboard_ids:
        dashboard_ids.extend(list(all_target_dashboard_ids))
    return dashboard_ids


def get_relational_dashboards_info(screen_id):
    """
    获取多屏包含的所有报告(包括跳转目标报告)
    :return:
    """
    relation_dashboard_ids = get_relational_dashboard_ids(screen_id)
    if not relation_dashboard_ids:
        return []
    return external_dashboard_repository.get_info_by_dashboard_ids(relation_dashboard_ids)


def get_releation_dashboard(screen_id):
    conditions = {'dashboard_id': screen_id, 'type': 1}
    col_name = 'screen_id'
    dashboard_ids = repository.get_columns('screen_dashboard', conditions, col_name)
    return dashboard_ids if dashboard_ids else []


def extract_list_args(kwargs, arg_name):
    fields = kwargs.get(arg_name, '')
    if isinstance(fields, str):
        fields = kwargs.get(arg_name, '').split(',')
    return [f.strip() for f in fields]


def get_dashboard_list_for_embedded_openapi(**kwargs):
    user_account = kwargs.get('user_account', None) or kwargs.get('userCode', None)
    fields = extract_list_args(kwargs, 'fields')
    dashboard_type = extract_list_args(kwargs, 'dashboard_type')
    kwargs['dashboard_type'] = dashboard_type

    dashboard_fields = screen_repositories.get_dashboard_columns()
    fields = list(set(fields) & set(dashboard_fields))
    if not fields:
        # 没有传过来字段
        return []

    # 需要根据这3个来生成报告类型
    builtin_fields = ['platform', 'new_layout_type', 'terminal_type', 'type', 'application_type', 'level_code', 'id']
    fields_has_not_pass = [i for i in builtin_fields if i not in fields]
    for field in builtin_fields:
        if field not in fields:
            fields.append(field)

    if user_account is not None:
        from dmplib.hug import g

        g.account = user_account
        has_role, role_ids = dashboard_openapi_service.get_user_role_ids(user_account)
        if not has_role:
            return []
        else:
            if ADMIN_ROLE_ID not in role_ids and not data_permission_repository.get_permission_by_role_ids(
                    role_ids, 'dashboard', 'view'
            ):
                allow_ids = data_permission_repository.get_allow_ids(role_ids, 'dashboard', 'view') or []
                own_ids = external_dashboard_repository.get_user_create_dashboard_ids(user_account)
                allow_ids.extend(own_ids or [])
                if not allow_ids:
                    return []
                kwargs['dashboard_ids'] = list(set(allow_ids))

    query_model = DashboardOpenAPIEmbeddedQueryModel(**kwargs)
    result = screen_repositories.get_dashboard_list_by_query_model(query_model, fields)

    has_ac_report = False
    for record in result:
        application_type = record.get('application_type')
        if application_type == ApplicationType.ActiveReport.value:
            has_ac_report = True
        id = record.get('id')
        record['dashboard_type'] = _format_dashboard_type(record)
        record['name'] = dashboard_openapi_service._format_folder_name(record)
        record['preview_url'] = f"{config.get('Domain.dmp')}/api/user/superapp?{urlencode({'__from': 'superapp', 'report_id': record.get('id') or '', 'report_type': 'dashboard'})}"
        # 剔除掉多余的
        for field in fields_has_not_pass:
            record.pop(field, None)
        record['id'] = id

    deal_ac_report_url(has_ac_report, result)
    return result


def deal_ac_report_url(has_ac_report, result):
    """
    这里将返回结果中的复杂报表连接进行处理
    1.这里的场景有点复杂
        原本embed接口只是超级工作台获取数见单组件渲染的报表
        但是后来却被用来可以获取整个数见的报表（我不知道为什么发展成这样）
    2. 为什么这里返回了复杂报表
        因为这里原始的实现获取全部报表的时候返回看了复杂报表的数据
    3. embed的preview_url和openapi中super_portal_integrate_url有什么不一样
        embed的preview_url返回的是超级app中预览报告的集成地址
        openapi中super_portal_integrate_url返回的是超级工作台中预览报告的集成地址
        （这俩的权限校验不同！！！！）
    4. 为什么不像openapi中直接根据d_type获取单独的报表数据
        embed的参数和openapi完全不一样。如果改成openapi的获取形式，工作台的代码需要改动
    5. 为什么这里将复杂报表preview_url替换成openapi中super_portal_integrate_url
        embed接口只返回了一个预览地址，对于复杂报表来说，目前的实现集成地址就是super_portal_integrate_url
        场景上本身也没有问题，既然是复杂报表，那么使用正确的能渲染的复杂报表集成地址也没问题
    """
    if not has_ac_report:
        return

    from ppt import external_service
    data = external_service.get_active_report_list(**{})
    if not data:
        return
    ref = {d.get('id'): d for d in data}
    for record in result:
        id = record.get('id')
        if id and id in ref:
            super_portal_integrate_url = ref[id].get('super_portal_integrate_url')
            if super_portal_integrate_url:
                record['preview_url'] = super_portal_integrate_url
    return


def _format_dashboard_type(record):
    """
    格式化报表类型
    """
    dashboard_type = ''
    if record.get('application_type') == ApplicationType.LargeScreen.value:
        dashboard_type = 'large_screen'
    elif record['platform'] == 'pc' and record['new_layout_type'] == 1:
        dashboard_type = 'pc_screen'
    elif record['platform'] == 'pc' and record['new_layout_type'] == 0:
        dashboard_type = 'large_screen'
    elif record['terminal_type'] == 'mobile_screen':
        dashboard_type = 'mobile_screen'
    elif record['platform'] == 'mobile' and record['new_layout_type'] == 0:
        dashboard_type = 'mobile_screen'
    return dashboard_type


def get_release_url(report_id: str, terminal_type: str):
    from feed.services.dashboard_feeds_service import get_dashboard_url

    return get_dashboard_url(report_id, terminal_type)


def get_dashboard_released_chart_data_for_openapi(dashboard_id):
    result = screen_repositories.get_dashboard_by_id(dashboard_id) or {}
    if not result:
        raise UserError(message='报表不存在！')

    result = screen_repositories.get_released_chart_data_by_dashboard_id(dashboard_id) or []
    for chart in result:
        chart.pop('config', None)
    return result
