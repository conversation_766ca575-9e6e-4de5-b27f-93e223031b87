#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import json
import random
import string

from loguru import logger

from base import repository
from base.enums import SkylineApps
from components import common_service, auth_util
from components.app_hosts import AppHosts
from components.fast_logger import FastLogger
from components.password_encode_util import PasswordV3Utils, PasswordV2Utils
from dmplib import redis, config
from open_api.models import TenantAddParams, TenantCancellationParams
from open_data.services import open_data_service

AUTH_JWK_URI = '/.well-known/openid-configuration/jwks'
JWK_CACHE_KEY = 'tenant_add_jwk'


def tenant_add(token, params: TenantAddParams):
    # 缺少：DeployMode，mIPInfo，superportal，ERP_Language，
    app_list = params.appList or params.appAuths
    is_open_super_app = False
    # 安全管理，智能硬件，质量管理
    super_app_app_codes = ['7100', '7200', '7050']
    apps = []
    for app in app_list:
        app_code = app.get('mainAppCode') or app.get('appCode')
        apps.append({
            'app_code': app_code,
            'app_name': app.get('appName'),
        })
        if app_code and str(app_code) in super_app_app_codes:
            is_open_super_app = True

    data = repository.get_data_scalar('project', {'code': params.tenantCode}, 'code', True)
    action = 'update' if data else 'create'
    dmp_params = {
        'task_id': params.taskId,
        'code': params.tenantCode,
        'name': params.tenantName,
        'account': params.initUserName,
        'admin_pwd': params.initPassword,
        'title': '数见平台',
        'admin_email': params.masterEmail,
        'bizmode': params.bizMode,
        'type': '可视化',
        'rds_strategy': 'average',
        'allow_dashboard_type': 'large,dashboard,mobile',
        'customer_id': params.customerId,
        'app_code_list': apps,
        'is_send_email': 0,
        'password_is_encode': 1,
        'action': action,
        'ERP_Language': params.erpLanguage,
        'from_init': 'erpsaas',
        'from': 'erpsaas',
        'init_1_5_app': 1,
        'env_code': params.envCode
    }
    # 日志
    omp_tenants_log = {
        'app_code': 'dmp',
        'traceId': params.traceId,
        'taskId': params.taskId,
        'msg': '正在创建任务...',
        'log_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        'extend_json': {
            'action': 'create',
            'env_code': auth_util.get_env_code()
        }
    }
    FastLogger.OMPTenantsFastLogger(**omp_tenants_log).record()
    dmp_params['omp_tenants_log'] = omp_tenants_log

    custom_vars = params.customVars
    if custom_vars:
        # 集成平台
        mip_info = {'MIPCloudUrl': custom_vars.get('mip_cloudurl'),
                    'Serviceurl': custom_vars.get('serviceUrl'),
                    'AppKey': custom_vars.get('appKey'),
                    'AppSecret': custom_vars.get('appSecret'),
                    }
        dmp_params['mip_info'] = mip_info
        # 超级工作台
        val = custom_vars.get('portal.domainEnterprise')
        if val:
            dmp_params['superportal'] = {'host': val}

    # 如果omp传了密码，用多重方式解密
    if params.initPassword:
        s = decrypt_pwd(params.initPassword)
        if s:
            dmp_params['admin_pwd'] = s
            dmp_params['password_is_encode'] = 0

    # 老方式
    # 邮件自己发送
    # 密码自己生成
    if not params.initPassword or not params.initUserName:
        dmp_params['is_send_email'] = 1
        dmp_params['password_is_encode'] = 0
        dmp_params['account'] = None
        dmp_params['admin_pwd'] = ''.join(random.sample(string.ascii_letters + string.digits, 10)) + '!'

    # if not is_cy:
    #     dmp_params['dmp_env_sign'] = 'hd'
    #     dmp_params['storage_type'] = 'local'
    # if is_cy:
    #     dmp_params['value_added_func'] = 'self-service,active_reports'
    # elif is_fk:
    #     dmp_params['value_added_func'] = 'report_center'
    #TODO 需确认这几个参数不根据环境类型判断是否正确
    dmp_params['value_added_func'] = 'self-service,report_center'
    if is_open_super_app:
        dmp_params['value_added_func'] += ',superapp'

    res = open_data_service.to_open_tenant(**dmp_params)
    conn = redis.conn()
    if isinstance(res, dict):
        result = res.get('result')
        msg = res.get('msg')
    else:
        result = res[0]
        msg = res[1]

    if not result:
        set_task_status(params.taskId, 2, msg)
        return {"code": 40000, "message": msg}

    set_task_status(params.taskId, 0, '已创建任务..')

    task_id = params.taskId
    task_callback_key = task_id + ':callback'
    callback_url = params.taskCallbackUrl if params.taskCallbackUrl else 'https://omp.mypaas.com/enterprise-service/api/v1/task/execute-result-callback'
    if auth_util.is_env_enable_skyline_auth():
        callback_url = AppHosts.get(SkylineApps.OMP_TENANT, True)
        callback_url += '/api/v1/task/execute-result-callback'
    logger.error(f'设置开户回调地址:{task_callback_key} -> {callback_url}')
    conn.set(task_callback_key, {'token': token, 'url': callback_url}, 90 * 60)
    return {"code": 0, "message": "开户中..."}


def cancellation(token, params: TenantCancellationParams):
    logger.error(f"OMP调用销户:{params}")
    open_data_service.tenant_cancellation_task(**(params.__dict__))
    task_id = params.taskId
    task_callback_key = task_id + ':callback'
    callback_url = params.taskCallbackUrl if params.taskCallbackUrl else 'https://omp.mypaas.com/enterprise-service/api/v1/task/execute-result-callback'
    if auth_util.is_enable_skyline_auth():
        callback_url = AppHosts.get(SkylineApps.OMP_TENANT)
        callback_url += '/api/v1/task/execute-result-callback'
    conn = redis.conn()
    logger.error(f'设置销户回调地址:{task_callback_key} -> {callback_url}')
    conn.set(task_callback_key, {'token': token, 'url': callback_url}, 90 * 60)
    return {"code": 0, "message": "销户中..."}


def status(task_id):
    conn = redis.conn()
    res = conn.get(task_id + ':status')
    if res:
        return json.loads(res)
    return {
        "code": 40400,
        "message": "当前taskId不存在"
    }

def set_task_status(task_id, status, msg):
    conn = redis.conn()
    result = {
        "code": 0,
        "data": {
            "taskStatus": status,
            "taskStatusDescription": msg
        }
    }
    conn.set(task_id + ':status', result, 60 * 60)


def decrypt_pwd(pwd):
    s = None
    try:
        s = PasswordV3Utils.decryptGCM(pwd, config.get('OMP.pwd_v3_key', PasswordV3Utils.V3_AES_KEY).encode())
        if s:
            return s
    except Exception as e:
        logger.error(f'V3密码解密失败:{str(e)}')

    try:
        s = PasswordV2Utils.decrypt(pwd, config.get('OMP.pwd_v2_key', PasswordV2Utils.V2_AES_KEY))
        if s:
            return s
    except Exception as e:
        logger.error(f'V2密码解密失败:{str(e)}')

    try:
        s = PasswordV2Utils.decrypt(pwd, config.get('OMP.pwd_v1_key', PasswordV2Utils.V1_AES_KEY))
        if s:
            return s
    except Exception as e:
        logger.error(f'V1密码解密失败:{str(e)}')

    return s
