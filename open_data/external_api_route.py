import json
import traceback

import jwt
import hug
import requests
import io
from falcon import request_helpers
from loguru import logger

from components import auth_util
from dmplib.hug import APIWrapper, g
from dmplib import config
from dmplib.utils.errors import UserError
from hug.authentication import authenticator

from open_data.services.suppapp_service import domain_info, menu_info, domain_systempage_info, menu_systempage_info
from dashboard_chart.services import dashboard_openapi_service
from app_menu.services import application_openapi_service, application_service
from open_api.services import screen_openapi_service


@authenticator
def _verify_token_handle(request, response, verify_user, **kwargs):
    """

    :param request:
    :param response:
    :return:
    """

    code = request.params.get('tenantCode') or request.params.get('tenant_code')
    account = request.params.get('userCode') or request.params.get('user_code')
    try:
        data = _verify_token(request, code)
        if not data:
            return False
        if not data.get('code') and not code:
            return False
        g.code = code or data.get('code')
    except Exception as e:
        logger.error(e)
        return False
    # account
    g.account = account or 'openapi'
    return True


def _verify_token(request, code):
    """
    多重秘钥解析
    :param request:
    :return:
    """
    if code and auth_util.is_enable_skyline_auth(code):
        skyline_token = auth_util.get_auth_header(request)
        if skyline_token:
            try:
                data = auth_util.verify_token(skyline_token)
                return data
            except Exception as e:
                logger.error(f"统一应用认证解析token异常: {str(e)} skyline_token: {skyline_token}")

    try:
        token = request.params.get('token') or request.headers.get('TOKEN')
        if not token:
            return None
        secret = "YC2UFKz7"
        data = jwt.decode(token, secret, algorithms=["HS256"])
        return data
    except:
        try:
            common_secret = auth_util.get_common_secret()
            data = jwt.decode(token, common_secret, algorithms=["HS256"])
            return data
        except Exception as e2:
            logger.error(f"公共秘钥解析错误 common_secret: {common_secret}")
            raise e2


class JwtAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_jwt_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._jwt_route = None
        self.api.http.base_url = '/api'

    @property
    def jwt_route(self):
        if not self._jwt_route:
            self._jwt_route = hug.http(api=self.api, requires=_verify_token_handle(None))
        return self._jwt_route


jwt_api =  JwtAPIWrapper(__name__)


@jwt_api.jwt_route.post('/open_tenant/projects')
def open_tenant(**kwargs):
    """
    开租户
    :param kwargs:
    :return:
    """
    from open_data.services.open_data_service import to_open_tenant
    res = to_open_tenant(**kwargs)
    return res


@jwt_api.jwt_route.post('/uninstall_data_center')
def uninstall(**kwargs):
    """
    卸载数据服务中心租户
    :param kwargs:
    :return:
    """
    from shuxin15_upgrade.services.mks_service import uninstall_data_center
    uninstall_data_center(**kwargs)
    return True, '成功', None


@jwt_api.jwt_route.get('/shuxin15_upgrade_service')
@jwt_api.jwt_route.post('/shuxin15_upgrade_service')
def shuxin15_upgrade_service(**kwargs):
    """
    升级1.5数见
    :param kwargs:
    :return:
    """
    from shuxin15_upgrade.services import shuxin15_upgrade_service
    status, upgrade_task_id = shuxin15_upgrade_service.trigger_upgrade(
        kwargs.get('module', '全量升级')
    )
    msg = '执行失败, 请稍候重试' if not status else '执行成功'
    return status, msg, upgrade_task_id

@jwt_api.jwt_route.get('/shuxin15_upgrade_service/log_view')
def shuxin15_upgrade_service_log_view(**kwargs):
    """
    获取1.5数见异步信息
    :param kwargs:
    :return:
    """
    upgrade_task_id = kwargs.get('upgrade_task_id')
    from shuxin15_upgrade.services import shuxin15_upgrade_service
    return True, 'success', shuxin15_upgrade_service.log_view(upgrade_task_id)


@jwt_api.jwt_route.get('/delete_tenant')
def delete_tenant(**kwargs):
    """
    删除租户
    """
    try:
        host = config.get("Dmp.admin_host", "http://dmp-admin:8000") or "http://dmp-admin:8000"
        code = kwargs.get('code')
        if not code:
            return False, '租户code不能为空', {}
        get_url = "{}/openapi/delete_tenant?code={}".format(host.rstrip('/'), code)
        res = requests.get(get_url)
        if res.status_code != 200:
            logger.error(f"删除租户错误: {res.text}")
        logger.info(f"删除租户返回值: {res.text}")
        return res.json()
    except Exception as e:
        msg = f"open tenant error: {e} \ntraceback: {traceback.print_exc()}"
        raise UserError(message=msg)


@jwt_api.jwt_route.get('/open_tenant_status')
def open_tenant_status(**kwargs):
    """
    查询开户状态
    """
    try:
        task_id = kwargs.get('task_id')
        if not task_id:
            return False, '任务ID不能为空', {}
        from open_data.services.open_data_service import get_open_tenant_status
        return True, '', get_open_tenant_status(task_id)
    except Exception as e:
        raise UserError(message=str(e))


@jwt_api.jwt_route.post('/set-erp-api-info')
def set_erp_api_info(**kwargs):
    """
    设置接口管家地址
    """
    from components.fast_logger import FastLogger
    tenant_code = kwargs.get('tenant_code') or kwargs.get('tenantCode')
    log_data = {
        'api_param': kwargs, 'org_code': tenant_code, 'action': 'response_erp',
        'token': kwargs.get('token'), 'is_success': 1
    }
    try:
        host = config.get("Dmp.admin_host", "http://dmp-admin:8000") or "http://dmp-admin:8000"
        if not tenant_code:
            return False, '租户code不能为空', {}
        get_url = "{}/openapi/set-erp-api-info".format(host.rstrip('/'))
        res = requests.post(url=get_url, data=kwargs)
        if res.status_code != 200:
            log_data['is_success'] = 0
            logger.error(f"设置接口管家错误: {res.text}")
        logger.info(f"设置接口管家返回: {res.text}")
        log_data['api_result'] = res.text
        # 日志记录天眼
        FastLogger.ApiFastLogger(**log_data).record()
        return res.json()
    except Exception as e:
        msg = f"open tenant error: {e} \ntraceback: {traceback.print_exc()}"
        log_data['is_success'] = 0
        log_data['api_result'] = msg
        FastLogger.ApiFastLogger(**log_data).record()
        raise UserError(message=msg)


@jwt_api.jwt_route.post('/deliver_callback')
def deliver_callback(**kwargs):
    """
    在线、统计报告的分发后回调接口
    转发到dmp-admin中处理
    :param kwargs:
    :return:
    """
    try:
        host = config.get("Dmp.admin_host", "http://dmp-admin:8000") or "http://dmp-admin:8000"
        res = requests.post(
            url=f"{host}/openapi/deliver_callback",
            json=kwargs
        )
        if res.status_code != 200:
            logger.error(f"分发回调接口错误: {res.text}")
        logger.error(f"分发回调接口res: {res.text}")
        return res.json()
    except Exception as e:
        msg = f"分发回调接口错误: {e} \ntraceback: {traceback.print_exc()}"
        raise UserError(message=msg)


@jwt_api.jwt_route.post('/deliver_simple_callback')
def deliver_callback(**kwargs):
    """
    在线、统计报告的分发后回调接口
    转发到dmp-admin中处理
    :param kwargs:
    :return:
    """
    try:
        host = config.get("Dmp.admin_host", "http://dmp-admin:8000") or "http://dmp-admin:8000"
        res = requests.post(
            url=f"{host}/openapi/deliver_simple_callback",
            json=kwargs
        )
        if res.status_code != 200:
            logger.error(f"分发回调接口错误: {res.text}")
        logger.error(f"分发回调接口res: {res.text}")
        return res.json()
    except Exception as e:
        msg = f"分发回调接口错误: {e} \ntraceback: {traceback.print_exc()}"
        raise UserError(message=msg)


@jwt_api.jwt_route.get('/domain_info', output=hug.output_format.json)
def query_domain_info(**kwargs):
    """
    超级工作台调用数见的接口获取站点信息（数见的接口需要发布的集成平台）
    :param kwargs:
    :return:
    """
    return domain_info()

@jwt_api.jwt_route.get('/domain_systempage_info', output=hug.output_format.json)
def query_domain_systempage_info(**kwargs):
    """
    超级工作台调用数见的接口获取站点信息（数见的接口需要发布的集成平台）
    :param kwargs:
    :return:
    """
    return domain_systempage_info()


@jwt_api.jwt_route.get('/menu_info', output=hug.output_format.json)
def query_menu_info(**kwargs):
    """
    超级工作台调用数见的接口获取菜单信息（数见的接口需要发布的集成平台）
    :param kwargs:
    :return:
    """
    return menu_info()

@jwt_api.jwt_route.get('/menu_systempage_info', output=hug.output_format.json)
def query_menu_systempage_info(**kwargs):
    """
    超级工作台调用数见的接口获取菜单信息（数见的接口需要发布的集成平台）
    :param kwargs:
    :return:
    """
    return menu_systempage_info()



@jwt_api.jwt_route.get('/dashboard/list')
def list_dashboard(request, **kwargs):
    """
    dmp外部客户获取看板(报告)列表数据
    示例：
    http://dmp-test5.mypaas.com.cn/api/external/dashboard/list?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhIjoxLCJjb2RlIjoidWl0ZXN0In0.TqT2OKLXG3JHAsnuVbGK4ff71LJET4goL2bhJujHTYY&platform=pc
    与 open_api 请求接口一样逻辑，http://localhost:8000/openapi/dashboard/list
    """
    code = kwargs.get('tenant_code')
    if code :
        g.code = code
    data = dashboard_openapi_service.get_report_list(**kwargs)
    return True, 'ok', data


@jwt_api.jwt_route.get('/portal/list')
def list_portal(**kwargs):
    """
    门户列表接口
    示例：
    http://dmp-test5.mypaas.com.cn/api/external/portal/list?token=
    eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhIjoxLCJjb2RlIjoidWl0ZXN0In0.TqT2OKLXG3JHAsnuVbGK4ff71LJET4goL2bhJujHTYY&platform=pc
    与 open_api 请求接口一样逻辑，http://localhost:8000/openapi/portal/list
    """
    data = application_openapi_service.get_portal_list_for_openapi(**kwargs)
    return True, 'ok', data


@jwt_api.jwt_route.get('/embedded/dashboard/list')
def list_embedded_dashboard(**kwargs):
    """
    内嵌报告列表接口
    示例：
    http://dmp-test5.mypaas.com.cn/api/external/embedded/dashboard/list?
    token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhIjoxLCJjb2RlIjoidWl0ZXN0In0.TqT2OKLXG3JHAsnuVbGK4ff71LJET4goL2bhJujHTYY
    &dashboard_type=pc_screen&fields=id,name
    与 open_api 请求接口一样逻辑，http://localhost:8000/openapi/embedded/dashboard/list
    """
    data = screen_openapi_service.get_dashboard_list_for_embedded_openapi(**kwargs)
    return True, 'ok', data



@jwt_api.jwt_route.post('/pkey/save')
def save_pkey(**kwargs):
    from dashboard_chart.services.external_dashboard_service import save_params
    # 校验token并解析
    g.code = kwargs.get('project_code') or kwargs.get('tenantCode')
    g.account = kwargs.get('userCode') or kwargs.get('project_code')
    if kwargs.get('token'):
        del kwargs['token']
    return True, 'success', save_params(kwargs)



@jwt_api.jwt_route.post('/app/menu_list')
def app_menu_list(request, **kwargs):
    # 工作台查询当前用户已授权门户菜单列表
    kwargs['application_id'] = kwargs.get('chartsTypesGUID', '')
    kwargs['dashboard_name'] = kwargs.get('name', '')
    kwargs['page_size'] = kwargs.get('pageSize', 10)
    res = application_service.get_permission_granted_pc_menu_list(True, kwargs)
    return {'code':1, 'errorCode':'', 'message':'', 'result': res}


@jwt_api.jwt_route.post('/app/list')
def app_list(request, **kwargs):
    # 工作台查询当前用户已授权门户
    kwargs['template_id'] = kwargs.get('templateGuid', '')
    kwargs['application_name'] = kwargs.get('typeName', '')
    data = application_service.get_permission_granted_pc_applications(True, kwargs.get('application_id'))
    return {'code':1, 'errorCode':'', 'message':'', 'result': data}


@jwt_api.jwt_route.post('/template/save')
def template_save(request, **kwargs):
    # 工作台保存模板配置数据
    kwargs['template_id'] = kwargs.get('templateGuid', '')
    orig_list = kwargs.get('chartsDetails', [])
    menu_list = []
    for menu in orig_list:
        menu_list.append({
            'data_id':menu.get('chartsDetailGUID', ''),
            'data_type':menu.get('dmpReportType', ''),
            'data_name':menu.get('chartsName', ''),
            'workbench_used_id':menu.get('superWorkbenchUsedChartsGUID', ''),
            'user_id': g.account,
        })
    kwargs['data'] = menu_list
    application_service.save_template_data(kwargs)
    return {'code':1, 'errorCode':'', 'message':'', 'result':True}


@jwt_api.jwt_route.post('/template/list')
def template_list(request, **kwargs):
    # 工作台查询模板配置数据
    kwargs['template_id'] = kwargs.get('templateGuid', '')
    kwargs['user_id'] = kwargs.get('userGuid') or g.account
    kwargs['page_size'] = kwargs.get('pageSize', 10)
    data = application_service.list_template_data(True, kwargs)
    return {'code':1, 'errorCode':'', 'message':'', 'result': data}


@jwt_api.jwt_route.post('/menu/url')
def get_menu_access_url(**kwargs):
    """
    获取菜单的访问地址
    :param menu_id:
    :return:
    """
    kwargs['menu_id'] = kwargs.get('chartsDetailGUID')
    res = application_service.get_menu_access_url(kwargs)
    return {'code':1, 'errorCode':'', 'message':'', 'result': {'chartsSuperUrl':res,'sourceType':1}}
