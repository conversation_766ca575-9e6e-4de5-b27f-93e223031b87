# 提供给超级工作的接口：数见菜单列表、数见站点地址
superportal_api_info = {
    "path":
        {
            "/api/external/domain_info": {
                "get": {
                    "tags": [
                        "dmp"
                    ],
                    "description": "dmp站点接口",
                    "summary": "dmp站点接口",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/menu_info": {
                "get": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp菜单接口",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
			 "/api/external/domain_systempage_info": {
                "get": {
                    "tags": [
                        "dmp"
                    ],
                    "description": "dmp系统页面站点接口",
                    "summary": "dmp系统页面站点接口",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/menu_systempage_info": {
                "get": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp系统页面菜单接口",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/open_tenant/projects": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp开户接口",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/dashboard/list": {
                "get": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp外部客户获取看板(报告)列表数据",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/portal/list": {
                "get": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp门户列表",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/embedded/dashboard/list": {
                "get": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp内嵌报告列表",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/set-erp-api-info": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp设置接口管家地址",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/pkey/save": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp设置报表参数",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/app/menu_list": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp当前用户已授权的门户菜单列表",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/app/list": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp当前用户已授权的门户列表",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/template/save": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp模板列表配置",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/template/list": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp模板列表查询",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/external/menu/url": {
                "post": {
                    "tags": [
                        "dmp"
                    ],
                    "summary": "dmp菜单访问地址获取",
                    "operationId": "updatePetWithForm",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "example": None,
                                "schema": {

                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
}
