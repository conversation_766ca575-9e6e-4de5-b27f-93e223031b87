#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import logging
import traceback

import requests

from base import repository
from celery_app.celery import CeleryTask, get_task_id
from components.crypt import MysqlAESCrypt
from components.remove_comment import remove_comment
from dataset.common import sql_helper
from dataset.repositories import dataset_repository
from dmplib import config
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id, is_ascii
from open_data.models import OpenDataQueryModel, OpenDataModel
from open_data.repositories import open_data_repository
from open_data.services import dataset_data_service
from user.services import user_service

logger = logging.getLogger(__name__)


def list_open_data(query_model: OpenDataQueryModel):
    results = open_data_repository.get_opendata_list(query_model).get_result_dict()
    for open_data in results.get("items"):
        if open_data.get("content"):
            open_data["content"] = json.loads(open_data.get("content"))
    return results


def get_open_data(data_id):
    result = repository.get_data(
        'open_data', {"id": data_id}, fields=["id", "name", "note", "content", "created_on", "modified_on"]
    )
    if result and result.get("content"):
        result["content"] = json.loads(result.get("content"))
    return result


def add_open_data(model: OpenDataModel):
    model.id = seq_id() if not model.id else model.id
    model.validate()
    if repository.data_is_exists("open_data", {"name": model.name, "status": 1}):
        raise UserError(message=u'数据集API名称： %s 已存在，请修改。' % model.name)
    if not is_ascii(model.name):
        raise UserError(message=u'不支持中文或特殊字符！')
    if model.content:
        model.dataset_id = get_dataset_id_by_content(model.content.get("sql"))
        model.content = json.dumps(model.content)
    repository.add_model('open_data', model, fields=["id", "name", "note", "content", "dataset_id"])
    return model.id


def get_dataset_id_by_content(sql):
    dataset_id = ''
    try:
        # 解析content，获取表名，获取数据集id,用于删除时关联查询
        sql, semicolon_num = remove_comment(sql)
        table_names = sql_helper.extract_tables(sql, is_alias=True)
        table_alias_names = sql_helper.parse_table_alias_names(table_names)
        if table_alias_names:
            query_dataset_names = [table.get("sql_table_name") for table in table_alias_names]
            query_dataset_metadata = dataset_repository.get_dataset_by_name(query_dataset_names)
            if not query_dataset_metadata or len(query_dataset_metadata) != len(table_alias_names):
                raise UserError(
                    message='{sql} 中存在无效表名 {table_names}'.format(sql=sql, table_names=','.join(query_dataset_names))
                )
            dataset_ids = [dataset.get("id") for dataset in query_dataset_metadata]
            dataset_id = json.dumps(dataset_ids) if dataset_ids else ''
    except Exception as e:
        msg = "解析content，获取表名失败：{}".format(str(e))
        logger.error(msg, exc_info=True)
    return dataset_id


def update_open_data(model: OpenDataModel):
    model.validate()
    open_data = repository.get_data('open_data', {'name': model.name, "status": 1}, fields=["id"])
    if open_data and open_data.get('id') != model.id:
        raise UserError(message=u'数据集API名称： %s 已存在，请修改。')
    if not is_ascii(model.name):
        raise UserError(message=u'不支持中文或特殊字符！')
    if model.content:
        model.dataset_id = get_dataset_id_by_content(model.content.get("sql"))
        model.content = json.dumps(model.content)
    repository.update_model('open_data', model, {"id": model.id}, fields=["name", "note", "content", "dataset_id"])
    return model.id


def delete_open_data(data_id):
    return repository.update_data('open_data', {'status': 0}, {'id': data_id})


def get_open_dataset_data(api, biz_params, is_count=False):
    """
    获取数据集数据
    :param api:
    :param biz_params:
    :param is_count:
    :return:
    """
    user_id = user_service.get_cur_user_id()
    if not user_id:
        raise UserError(message="没有用户信息，请登录。")
    open_data = repository.get_data("open_data", {"name": api}, fields=["id", "name", "note", "content"])
    if not open_data:
        raise UserError(message="api：{}，不存在。".format(api))
    try:
        content = json.loads(open_data.get('content'))
        sql = content.get("sql")
    except Exception as e:
        raise UserError(message="开放数据content内容序列化错误：" + str(e))

    limit = config.get('OpenData.data_limit', 1000)
    return dataset_data_service.get_open_dataset(user_id, sql, biz_params, limit=limit, is_count=is_count)


def run_open_dataset(sql, account, biz_params, is_count=False):
    """
    测试运行
    :param sql:
    :param account:
    :param biz_params:
    :param is_count:
    :return:
    """
    user_id = user_service.get_user_id_by_account(account)
    if not user_id:
        raise UserError(message="没有用户信息，请登录。")
    return dataset_data_service.get_open_dataset(user_id, sql, biz_params, limit=100, is_count=is_count)


def get_open_tenant_status(task_id):
    """
    获取开户状态
    """
    default = {'status': -1, 'err_msg': ''}
    data = repository.get_one('tenant_open_log', {'task_id': task_id}, ['status', 'err_msg'], from_config_db=True) or default
    return data


def project_is_exists(project_code):
    """
    判断租户是否存在
    """
    project_info = repository.get_one('project', {'code': project_code}, ['id'], from_config_db=True)
    return True if project_info else False

def add_project_create_record(**kwargs):
    try:
        params = json.dumps(kwargs, ensure_ascii=False)
        app_codes = ''
        id = seq_id()
        app_code_list = kwargs.get('app_code_list', [])
        from_init = kwargs.get('from_init', '')
        if not from_init:
            from_init = kwargs.get('from', '')
        if app_code_list:
            app_codes = ','.join([x['app_code'] for x in app_code_list])
        updated = repository.add_data('project_create_record',
                                      {'id': id, 'code': kwargs['code'], 'from_init': from_init, 'params': params,
                                       'app_codes': app_codes, 'status': 'INITED', 'source': 'ERP'},from_config_db=True)
        if updated:
            return id
    except Exception as e:
        logging.error(f'保存开户记录异常:{str(e)}')
    return ''


def update_project_record_failure(record_id, err):
    try:
        if record_id:
            repository.update_data('project_create_record', {'status': 'FAILURE', 'msg': err}, {'id': record_id}, from_config_db=True)
    except Exception as e:
        logging.error(f'更新开户记录[{record_id}]失败:{str(e)}')


def to_open_tenant(**kwargs):
    record_id = add_project_create_record(**kwargs)
    kwargs['create_record_id'] = record_id
    try:
        project_code = kwargs.get('code')
        action = kwargs.get('action')
        has_project = project_is_exists(project_code)
        # 如果租户已存在且action为update则做增购动作
        if has_project and action == 'create':
            return True, '租户已存在', {}
        host = config.get("Dmp.admin_host", "http://dmp-admin:8000") or "http://dmp-admin:8000"
        if has_project:
            # admin-celery任务
            task_id = additional_purchase_task(**kwargs)
            return True, '已创建增购系统数据同步任务', {'task_id': task_id}
        else:
            password_is_encode = kwargs.get('password_is_encode')
            if password_is_encode:
                aes = MysqlAESCrypt('Mysoft95938@2022')
                kwargs['admin_pwd'] = aes.decrypt(kwargs.get('admin_pwd'))
            res = requests.post(
                url=f"{host}/openapi/projects",
                json=kwargs
            )
            logger.info(f"open tenant res: {res.text}")
            if res.status_code != 200:
                logger.error(f"open tenant error: {res.text}")
                return False, res.text, {}
            return res.json()
    except Exception as e:
        msg = f"open tenant error: {e} \ntraceback: {traceback.print_exc()}"
        update_project_record_failure(record_id, str(e))
        raise UserError(message=msg)



def additional_purchase_task(**kwargs):
    """
    异步将报告运行时数据还原到设计时（启动admin的celery执行）
    :param dashboard_id: 报告id
    :param task_id: 任务id
    :param is_new_jump: 报告跳转模式
    :return:
    """
    from celery_app.celery import get_backend

    backend = get_backend(config.get('Redis.admin_celery_db') or 6)

    CELERY_APP_NAME = "dmp_admin_celery"
    CELERY_TASK_NAME = "app_celery.additional_purchase"
    ADMIN_CELERY_QUEUE_NAME = "admin-celery"

    celery_app = CeleryTask(name=CELERY_APP_NAME, backend=backend, queue_name=ADMIN_CELERY_QUEUE_NAME)
    celery_app.celery.send_task(
        CELERY_TASK_NAME,
        kwargs=kwargs
    )
    task_id = kwargs.get('create_record_id', '')
    project_code = kwargs.get('code')
    if task_id:
        return get_task_id('project', "additional", project_code, task_id).replace('dmp:', 'dmp-admin:')
    else:
        return ''


def tenant_cancellation_task(**kwargs):
    from celery_app.celery import get_backend

    backend = get_backend(config.get('Redis.admin_celery_db') or 6)

    CELERY_APP_NAME = "dmp_admin_celery"
    CELERY_TASK_NAME = "app_celery.tenant_cancellation"
    ADMIN_CELERY_QUEUE_NAME = "admin-celery"

    celery_app = CeleryTask(name=CELERY_APP_NAME, backend=backend, queue_name=ADMIN_CELERY_QUEUE_NAME)
    celery_app.celery.send_task(
        CELERY_TASK_NAME,
        kwargs=kwargs
    )