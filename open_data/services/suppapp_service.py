from urllib.parse import urlencode
from urllib.parse import urlparse
from loguru import logger

from dmplib import config
from base import repository
from base.enums import LoginFrom


def domain_info():
    return {
        "code": 0,
        "message": "success",
        "data": [
            {
                "appCode": "dmp",
                "appName": "数见",
                "siteUrl": config.get('Domain.dmp'),
                "isSsoSite": True
            },
            {
                "appCode": "ppt",
                "appName": "在线报告",
                "siteUrl": config.get('PPT.domain'),
                "isSsoSite": True
            }
        ]
    }


def domain_systempage_info():
    return {
        "code": 0,
        "message": "success",
        "data": [
            {
                "appCode": "intelligent",
                "appName": "自助报表",
                "siteUrl": config.get('Domain.dmp'),
                "isSsoSite": True
            },
            {
                "appCode": "ppt",
                "appName": "在线报告",
                "siteUrl": config.get('Domain.dmp'),
                "isSsoSite": True
            }
        ]
    }


def __get_pc_portal_url(report_id='', report_type='portal'):
    params = {'__from': LoginFrom.SuperPortal.value, 'report_id': report_id or '', 'report_type': report_type}
    return f"/api/user/superportal/dashboard?{urlencode(params)}"


def __get_mobile_screen_portal_url(report_id='', report_type='portal'):
    params = {'__from': LoginFrom.SuppApp.value, 'report_id': report_id or '', 'report_type': report_type}
    return f"/api/user/superapp?{urlencode(params)}"


def get_app_list(platform='mobile'):
    from app_menu.services.application_openapi_service import _portal_index_pc, _portal_index_mobile_screen

    # 获取门户首页
    if platform == 'mobile':
        first_page = _portal_index_mobile_screen()
        is_mobile = 1
        platform = ['mobile', 'mobile_screen']
    else:
        first_page = _portal_index_pc()
        platform = ['tv', 'pc']
        is_mobile = 0

    app_list = repository.get_list("application", {'platform': platform, 'enable': 1}, fields=['id', 'name']) or []
    app_list_return = []

    url_obj = urlparse(first_page.get('preview_url'))

    app_list_return.append({
        'menuCode': first_page.get('id'),
        'menuName': first_page.get('name'),
        'isMobile': is_mobile,
        'menuUrl': f"{url_obj.path}?{url_obj.query}",
    })

    # 获取门户列表
    for app in app_list:
        item = dict()
        if platform == "mobile":
            preview_url = __get_mobile_screen_portal_url(app.get('id'))
        else:
            preview_url = __get_pc_portal_url(app.get('id'))
        item['menuCode'] = app.get('id')
        item['menuName'] = app.get('name')
        item['isMobile'] = is_mobile
        item['menuUrl'] = preview_url
        app_list_return.append(item)

    return app_list_return


def menu_info():
    menu_list = []
    data = [{
        "appCode": "dmp",
        "appName": "数见",
        "menuList": menu_list
    }]
    try:
        # 添加移动门户列表
        menu_list.append({
            "menuCode": "basicData",
            "menuName": "移动门户",
            "children": get_app_list('mobile')
        })
        msg = 'success'
        code = 0
    except Exception as e:
        logger.error(f'获取菜单异常: {e}')
        msg = str(e)
        code = -1
    return {
        "code": code,
        "message": msg,
        "data": data
    }


def menu_systempage_info():
    return {
        "code": 0,
        "message": 'success',
        "data": [{
            "appCode": "intelligent",
            "appName": "自助报表",
            "menuList": [
                {
                    "menuCode": "intelligentpage",
                    "menuName": "自助报表",
                    "children": [
                        {
                            "menuCode": "intelligentpage2",
                            "menuName": "自助报表",
                            "menuUrl": '/api/user/superportal/intelligent-report',
                            "isMobile": "0"
                        }
                    ]
                }
            ]
        },
            {
                "appCode": "ppt",
                "appName": "在线报告",
                "menuList": [
                    {
                        "menuCode": "pptpage",
                        "menuName": "在线报告",
                        "children": [
                            {
                                "menuCode": "pptpage2",
                                "menuName": "在线报告",
                                "menuUrl": '/api/user/superportal/slide',
                                "isMobile": "0"
                            }
                        ]
                    }
                ]
            }
        ]
    }
