#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test template service
"""

import logging
import unittest
from tests.base import BaseTest
from dashboard_template.services import template_service

logger = logging.getLogger(__name__)


class TestTemplateService(BaseTest):
    def __init__(self, method_name="runTest"):
        self.oss_url = (
            "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/tmp/%E5%85%A8%E5%9C%BA%E6%99%AF-api_20200102145047.zip"
        )
        super().__init__(method_name, code='test', account='admin')

    def test_check_install_template(self):
        result = template_service.check_install(self.oss_url)
        print(result)

    def test_install_template(self):
        kwargs = {"oss_url": self.oss_url, "dataset_install_type": 3}
        result = template_service.install_template(**kwargs)
        print(result)

    def test_generate_template_redirect_url(self):

        result = template_service.generate_template_redirect_url()
        print(result)

    def test_get_client_name(self):
        result = template_service.get_client_name("test")
        print(result)


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestTemplateService("test_get_client_name"))
    runner = unittest.TextTestRunner()
    runner.run(s)
