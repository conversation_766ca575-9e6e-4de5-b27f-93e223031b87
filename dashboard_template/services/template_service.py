#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401

# ---------------- 标准模块 ----------------
import os
import json
import zipfile
import uuid
from urllib.parse import urlparse, urlencode, urlunparse, urljoin
import jwt
from hashlib import md5
from urllib.request import urlretrieve
import logging

# ---------------- 业务模块 ----------------
from dmplib.utils.errors import UserError
from dataset.services import dataset_service
from base.enums import InstallCheckDatasetStatus, DistributeType, DashboardType, DatasetType, DatasetInstallType
from dmplib import config
from celery_app.celery import CeleryTask
from base import repository
from dmplib.utils.strings import seq_id
from dmplib.hug import g, debugger
from dashboard_chart.models import DashboardModel
from dataset.models import DatasetModel
from components import ops_api

logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)

EXPORT_FOLDER_LEVEL_CODE = '9001'
TOP_LEVEL_CODE = EXPORT_FOLDER_LEVEL_CODE + '-'
DEFAULT_DASHBOARD_FOLDER_NAME = '模板报告文件夹'
DEFAULT_DATASET_FOLDER_NAME = '模板数据集文件夹'
CELERY_APP_NAME = "dmp_admin_celery"
CELERY_TASK_NAME = "app_celery.install_dashboard_dataset"
ADMIN_CELERY_QUEUE_NAME = "admin-celery"


def download_and_parse_zipfile(oss_url):
    """
    下载并解压zip包
    :param oss_url:
    :return:
    """
    temp_file_name = seq_id()
    filename_with_path = f"./{temp_file_name}.zip"
    urlretrieve(oss_url, filename_with_path)
    export_data = None
    with zipfile.ZipFile(filename_with_path, "r") as zip_file:
        for name in zip_file.namelist():
            with zip_file.open(name) as zfile:
                r_data = zfile.read()
                if r_data:
                    try:
                        export_data = json.loads(r_data.decode('utf-8'))
                    except:
                        raise UserError(400, 'zip压缩文件内容格式有误')
                    break
    if not export_data:
        raise UserError(400, '无效的数据文件格式')

    if os.path.exists(filename_with_path):
        os.remove(filename_with_path)

    return export_data


def check_install(oss_url):
    """
    安装前检查安装包中的数据集
    :param oss_url:
    :return:
    """
    if not oss_url:
        raise UserError(501, "缺少oss文件链接参数")

    package_data = download_and_parse_zipfile(oss_url)
    if not package_data:
        raise UserError(501, "oss文件解析失败")

    query_dataset_ids = []
    datasets = package_data.get("datasets", [])
    for operate_dataset in datasets:
        dataset_data = operate_dataset.get("dataset", {})
        if dataset_data and dataset_data.get("id"):
            query_dataset_ids.append(dataset_data.get("id"))

    db_datasets = dataset_service.get_dataset_with_folder_by_ids(query_dataset_ids)
    db_dataset_ids = [i.get("id") for i in db_datasets] if db_datasets else []
    exist_dataset_ids = set(query_dataset_ids) & set(db_dataset_ids)
    check_status = (
        InstallCheckDatasetStatus.Have.value if exist_dataset_ids else InstallCheckDatasetStatus.NotHave.value
    )
    result = {"exist_status": check_status, "exist_datasets": db_datasets, "exist_dataset_ids": exist_dataset_ids}
    return result


def get_default_install_dashboard_folder():
    """
    获取报告安装文件夹
    :return:
    """
    query_data = repository.get_data(
        "dashboard",
        {
            "name": DEFAULT_DASHBOARD_FOLDER_NAME,
            "type": DashboardType.Folder.value,
            "distribute_type": DistributeType.Distribute.value,
            "level_code": TOP_LEVEL_CODE,
        },
        multi_row=False,
    )
    return query_data.get("id") if query_data else ""


def get_default_install_dataset_folder():
    """
    获取数据集安装文件夹
    :return:
    """
    query_data = repository.get_data(
        "dataset",
        {"name": DEFAULT_DATASET_FOLDER_NAME, "type": DatasetType.Folder.value, "level_code": TOP_LEVEL_CODE},
        multi_row=False,
    )
    return query_data.get("id") if query_data else ""


def create_default_install_dashboard_folder():
    """
    创建默认文件夹
    :return:
    """
    new_dashboard_id = seq_id()
    kwargs = {
        'id': new_dashboard_id,
        'name': DEFAULT_DASHBOARD_FOLDER_NAME,
        'type': DashboardType.Folder.value,
        'parent_id': '',
        'created_by': g.code or "",
        'modified_by': g.code or "",
    }
    model = DashboardModel(**kwargs)
    data = model.get_dict(
        [
            'id',
            'theme',
            'name',
            'platform',
            'user_group_id',
            'description',
            'type',
            'parent_id',
            'level_code',
            'is_multiple_screen',
            'cover',
            'layout',
            'background',
            'type_selector',
            'created_by',
            'modified_by',
            'create_type',
            'new_layout_type',
        ]
    )
    data['level_code'] = TOP_LEVEL_CODE
    data['user_group_id'] = '00000000-0000-0000-1111-000000000000'
    data['biz_code'] = uuid.uuid4().__str__().replace('-', '')
    data['distribute_type'] = DistributeType.Distribute.value
    repository.add_data('dashboard', data)
    return model.id


def create_default_install_dataset_folder():
    """
    创建数据集文件夹
    :return:
    """
    model = DatasetModel()
    model.id = seq_id()
    model.name = DEFAULT_DATASET_FOLDER_NAME
    model.type = DatasetType.Folder.value
    model.level_code = TOP_LEVEL_CODE
    repository.add_model("dataset", model, ["id", "name", "type", "level_code"])
    return model.id


def handle_dataset_install_type(dataset_install_type, oss_url):
    """
    判断dataset_install_type
    :param dataset_install_type:
    :param oss_url:
    :return:
    """
    exclude_dataset_ids, copy_dataset_ids = [], []
    str_exclude_dataset_ids, str_copy_dataset_ids = '', ''
    try:
        dataset_install_type = int(dataset_install_type)
    except:
        dataset_install_type = None
    if dataset_install_type not in [e.value for e in DatasetInstallType.__members__.values()]:
        raise UserError(503, "DATASET_INSTALL_TYPE参数校验非法")

    check_result = check_install(oss_url)
    exist_dataset_ids = check_result.get("exist_dataset_ids", [])
    if dataset_install_type == DatasetInstallType.Negative.value:
        str_exclude_dataset_ids = ",".join(exist_dataset_ids) if exist_dataset_ids else ""
    elif dataset_install_type == DatasetInstallType.Copy.value:
        str_copy_dataset_ids = ",".join(exist_dataset_ids) if exist_dataset_ids else ""

    return str_exclude_dataset_ids, str_copy_dataset_ids


def install_template(**kwargs):
    """
    安装模板入口
    :param kwargs:
    :return:
    """
    oss_url = kwargs.get("oss_url")
    dataset_install_type = kwargs.get("dataset_install_type")
    if not oss_url:
        raise UserError(501, "缺少oss文件链接参数")

    target_dashboard_folder_id = get_default_install_dashboard_folder()
    if not target_dashboard_folder_id:
        target_dashboard_folder_id = create_default_install_dashboard_folder()

    target_dataset_folder_id = get_default_install_dataset_folder()
    if not target_dataset_folder_id:
        target_dataset_folder_id = create_default_install_dataset_folder()

    if not target_dashboard_folder_id or not target_dataset_folder_id:
        raise UserError(502, "获取模板安装目录异常")

    str_exclude_dataset_ids, str_copy_dataset_ids = handle_dataset_install_type(dataset_install_type, oss_url)

    sync_import_data(
        oss_url, target_dashboard_folder_id, target_dataset_folder_id, str_exclude_dataset_ids, str_copy_dataset_ids
    )
    return generate_folder_redirect_url()


def sync_import_data(
    file_url: str,
    target_dashboard_folder_id: str,
    target_dataset_folder_id: str,
    str_exclude_dataset_ids: str,
    str_copy_dataset_ids: str,
):
    """
    异步安装数据集和报告数据。（启动admin的celery执行）
    :param file_url: 文件地址
    :param target_dashboard_folder_id: 指定目标 报告文件夹ID
    :param target_dataset_folder_id: 指定目标数据集文件夹ID
    :param str_exclude_dataset_ids: 需要排除的数据集id
    :param str_copy_dataset_ids: 需要复制的数据集id
    :return:
    """
    from celery_app.celery import get_backend

    backend = get_backend(config.get('Redis.admin_celery_db') or 6)

    celery_app = CeleryTask(name=CELERY_APP_NAME, backend=backend, queue_name=ADMIN_CELERY_QUEUE_NAME)
    celery_app.celery.send_task(
        CELERY_TASK_NAME,
        kwargs={
            'project_code': g.code,
            'file_url': file_url,
            'target_dashboard_folder_id': target_dashboard_folder_id,
            'target_dataset_folder_id': target_dataset_folder_id,
            'exclude_dataset_ids': str_exclude_dataset_ids,
            'copy_dataset_ids': str_copy_dataset_ids,
        },
    )


def encrypt_in_md5(data):
    data = data or ""
    obj = md5(data.encode("utf-8"))
    obj.update("ycdmp".encode("utf-8"))
    return obj.hexdigest()


def generate_token():
    """
    生成token
    :return:
    """
    token_data = {
        "tenant_code": getattr(g, "code", ""),
        "account": getattr(g, "account", ""),
        "userid": getattr(g, "userid", ""),
        "client": os.environ.get("CONFIG_AGENT_CLIENT_CODE", ""),
    }
    secret = config.get("JWT.dashboard_template_secret", "")
    token = jwt.encode(token_data, secret)
    return token


def generate_params():
    """
    生成跳转用的参数
    :return:
    """
    tenant_code = getattr(g, "code", "")
    account = getattr(g, "account", "")
    userid = getattr(g, "userid", "")
    client = os.environ.get("CONFIG_AGENT_CLIENT_CODE", "")
    client_name = get_client_name(client)
    dmp_domain = config.get("Domain.dmp", "")
    return {
        "tenant_code": tenant_code,
        "account": account,
        "userid": userid,
        "client": client,
        "client_name": client_name,
        "token": generate_token(),
        "m_tenant_code": encrypt_in_md5(tenant_code),
        "m_account": encrypt_in_md5(account),
        "m_userid": encrypt_in_md5(userid),
        "m_client": config.get('App.client_id', encrypt_in_md5(client)),
        "dmp_domain": dmp_domain,
    }


def append_url_parmas(base_url, params):
    """
    url追加请求参数
    :param base_url:
    :param params:
    :return:
    """
    if not base_url:
        raise UserError(504, "缺少必要的链接参数")
    parsed_url = urlparse(base_url)
    extend_url_params = urlencode(params) if params else ""
    new_query_params = parsed_url.query + "&" + extend_url_params if parsed_url.query else extend_url_params

    # 重新组装url
    new_redirect_url = urlunparse(
        [
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query_params,
            parsed_url.fragment,
        ]
    )
    return new_redirect_url


def generate_folder_redirect_url():
    """
    生成模板中心跳转到dmp目录的跳转链接
    :return:
    """
    domain = config.get("Domain.dmp", "")
    if not domain:
        raise UserError(503, "获取当前环境Domain异常")

    install_folder_id = get_default_install_dashboard_folder()
    if not install_folder_id:
        install_folder_id = create_default_install_dashboard_folder()
    folder_url = urljoin(base=domain, url=f"/dataview/{install_folder_id}")
    return append_url_parmas(folder_url, params={"token": generate_token()})


def generate_template_redirect_url():
    """
    生成到模板中心的跳转链接
    :return:
    """
    dashboard_template_base_url = config.get("Domain.dmp_dashboard_template", "")
    return append_url_parmas(dashboard_template_base_url, params=generate_params())


def get_client_name(cur_client_code):
    """
    获取当前环境名称
    :return:
    """
    client_name = ""
    try:
        clients = ops_api.get_clients()
    except Exception as e:
        logger.error(msg="获取OPS平台环境列表异常，异常现象: {}".format(str(e)))
        clients = []
    if not cur_client_code or not clients or not isinstance(clients, list):
        return client_name
    for item in clients:
        if item.get("code") == cur_client_code:
            client_name = item.get("name", "")
            break
    return client_name
