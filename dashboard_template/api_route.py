#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401,E1120

"""
route
"""

# ---------------- 标准模块 ----------------
import hug

# ---------------- 业务模块 ----------------
from dmplib.hug import APIWrapper
from dashboard_template.services import authority_service, template_service


class ProxyAPIWrapper(APIWrapper):
    __slots__ = ["_template_route"]

    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._route = hug.http(api=self.api)
        self._template_route = None
        self.api.http.base_url = "/dashboard_template"

    @property
    def dashboard_template_route(self):
        if not self._template_route:
            self._template_route = hug.http(
                api=self.api, requires=authority_service.verify_dashboard_template_handle(None)
            )
        return self._template_route


template_api = ProxyAPIWrapper(__name__)
api = APIWrapper(__name__)


@template_api.dashboard_template_route.post("/check_install")
def check_install(**kwargs):
    """
    /**
    @apiVersion 1.0.3
    @api {post} /api/dashboard_template/check_install 模板安装前数据集校验
    @apiParam formData {string}  oss_url 安装包链接
    @apiParam formData {string}  token DMP用户信息加密串
    @apiGroup  dashboard_template
    @apiResponse  200 {
        "msg": "ok",
        "result": true,
        "data": {"exist_status{存在相同数据集 0不存在 1已存在}": 1,
                "exsit_datasets": [
                    {
                        "id": "39e7702f-6ce1-d0b3-4007-cf7a51c7f22d",
                        "name": "全局数据1",
                        "folder_id": "39e75911-71d0-5aef-6bd1-50d100630d9b",
                        "folder_name": "佩奇"
                    }
                ]
        }
    }
    **/
    """
    result = template_service.check_install(kwargs.get("oss_url"))
    return True, "", result


@template_api.dashboard_template_route.post("/install")
def install_template(**kwargs):
    """
    /**
    @apiVersion 1.0.2
    @api {post} /api/dashboard_template/install 安装模板
    @apiParam formData {string}  oss_url 安装包链接
    @apiParam formData {number}  dataset_install_type 数据集安装类型 0 默认 1 覆盖安装 2 不安装
    @apiParam formData {string}  token DMP用户信息加密串
    @apiGroup  dashboard_template
    @apiResponse  200 {
        "msg": "ok",
        "result": true,
        "data": {
            "redirect_url{dmp模板安装目录跳转url}": "https://dmp-test.mypaas.com.cn/dataview/
            39f17a0d-3f62-5739-774f-6aac1b0272a8"
        }
    }
    **/
    """
    return True, "", {"redirect_url": template_service.install_template(**kwargs)}


@api.admin_route.get("/get_redirect_url")
def get_redirect_url():
    """
    /*
    @apiVersion 1.0.2
    @api {get} /api/dashboard_template/get_redirect_url 获取模板中心跳转链接
    @apiGroup  dashboard_template
    @apiResponse  200 {
        "msg": "ok",
        "result": true,
        "data": {"redirect_url": "http://dmp-tpl-test.mypaas.com?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"
        }
    }
    */
    """
    return True, "", {"redirect_url": template_service.generate_template_redirect_url()}
