import hug

from dmplib.hug import APIWrapper
from base.enums import ExportImportBizType, ProjectValueAddedFunc
from report_center.services.report_service import ReportService
from .services import export_service

api = APIWrapper(__name__)


@api.admin_route.get('/exports')
def list_exports(**kwargs):
    """
    /**
    @apiVersion 1.0.5
    @api  {get} /api/export/exports 报告导出列表
    @apiGroup  报告导出
    @apiParam  query  {string}  keywords  查询关键字
    @apiParam  query {number}  page  page
    @apiParam  query {number}  pagesize  pagesize
    @apiParam  query {string}  start_time  起始时间
    @apiParam  query {string}  end_time  结束时间
    @apiResponse 200{
        "result": true,
        "msg": "ok",
        "data": {
            "total": 44,
            "items": [{
            "description": "年度签约总金额",
            "dataset_ids": "0221b02b-35a8-4097-953f-25271614c822,07223658-a6e8-4a2c-8b95-857627a5c2aa",
            "title": "年度签约",
            "id": "39e7fcc5-cb50-2141-eae1-15fe3768b50f",
            "url": "",
            "created_on": "2018-07-30T17:24:38",
            "completed_on": "",
            "created_by": "huwl",
            "modified_by": "huwl",
            "modified_on": "2018-07-30T17:24:38",
            "status": "已创建",
            "dashboard_ids": "39e6040d-e4f2-113a-f5e9-ecedecf3101e,39e60440-3e15-089a-8f02-12608c22bf14",
            "is_deleted": 0
            }]
        }
    }
    **/
    """
    total, items = export_service.list_exports(
        kwargs.get('keywords', ''),
        kwargs.get('start_time', None),
        kwargs.get('end_time', None),
        kwargs.get('page', 1),
        kwargs.get('pagesize', 20),
    )

    return True, '', {'total': total, 'items': items}


@api.admin_route.delete('/export/{export_id}')
def delete_export(export_id: hug.types.text):
    """
    /**
    @apiVersion 1.0.0
    @api  {delete} /api/export/export/{export_id} 逻辑删除报告导出
    @apiGroup  报告导出
    @apiParam  path {string}  export_id  导出记录id
    @apiResponse 200{
        "result": true,
        "msg": "ok"
    }
    **/
    """
    ok = export_service.logic_delete_export(export_id)
    return ok, ''


@api.admin_route.get('/export/{export_id}')
def detail_export(export_id: hug.types.text):
    """
    /**
    @apiVersion 1.0.4
    @api  {get} /api/export/export/{export_id} 报告导出详情
    @apiGroup  报告导出
    @apiParam  path {string} export_id 导出记录id
    @apiResponse 200{
        "result": true,
        "msg": "ok",
        "data": {
            "id": "04d9dfce-c512-3e01-828a-cb2b83fcc7c2",
            "dashboards": [{"name": "年度销售大民屏", "id": "04d9dfce-c512-3e01-828a-cb2b83fcc7c1"}],
            "datasets":[{"name": "sql数据集", "id": "04d9dfce-c512-3e01-828a-cb2b83fcc7c2"}],
            "title": "年度销售大屏",
            "description": "年度销售大屏++",
            "url": "http://url"
        }
    }
    **/
    """

    export_data = export_service.get_export_detail(export_id)
    if export_data:
        del export_data['dataset_ids']
        del export_data['dashboard_ids']
        export_data['feed_ids'] = export_data['feeds']
        del export_data['feeds']
    return True, 'ok', export_data


@api.admin_route.post('/exports')
def export(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api  {post} /api/export/exports 报告导出
    @apiGroup  报告导出
    @apiBodyParam {
        "title": "年度销售报告",
        "description": "",
        "dashboard_ids": ["04d9dfce-c512-3e01-828a-cb2b83fcc7c3"],
        "dataset_ids": ["04d9dfce-c512-3e01-828a-cb2b83fcc7c3"],
        "applications": ["04d9dfce-c512-3e01-828a-cb2b83fcc7c3"]
    }
    @apiResponse 200 {
        "result": true,
        "msg": "ok"
    }
    **/
    """
    biz_ids = {
        'ppt_ids': kwargs.get('ppt_ids', []),
        'active_report_ids': kwargs.get('active_report_ids', []),
        'report_center_ids': kwargs.get('report_center_ids', []),
        'filling_ids': kwargs.get('filling_ids', []),
        'feed_ids': kwargs.get('feed_ids', []),
        'large_screen_ids': kwargs.get('large_screen_ids', []),
    }
    success = export_service.trigger_export(
        kwargs.get('title', ''),
        kwargs.get('description', ''),
        kwargs.get('dashboard_ids', []),
        kwargs.get('dataset_ids', []),
        kwargs.get('application_ids', []),
        biz_ids,
        kwargs.get('is_export_excel_data', 0),  # 0: 只导出Excel结构，1：导出Excel里面的数据
        kwargs.get('is_export_all_mode', 0)  #导出是否使用all包模式，0，默认不使用，1使用
    )

    return success, 'ok' if success else '导出失败, 请稍候重试'


@api.admin_route.post('/get_relation_dataset')
def get_relation_dataset_ids(**kwargs):
    """
    /**
    @apiVersion 1.0.2
    @api  {post} /api/export/get_relation_dataset  获取报告关联数据集ID集合
    @apiGroup  报告导出
    @apiBodyParam {
        "dashboard_ids": ["04d9dfce-c512-3e01-828a-cb2b83fcc7c3"]
    }
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data":["39ed7f76-ed05-b85c-9711-647e8f37b1aa"]
    }
    **/
    """
    biz_type = kwargs.get('biz_type', ExportImportBizType.DASHBOARD.value)
    if biz_type == ExportImportBizType.FILLING.value:
        dataset_ids = export_service.get_filling_relation_dataset(kwargs.get('filling_ids', []))
    else:
        dataset_ids = export_service.get_dashboard_relation_dataset(kwargs.get('dashboard_ids', []))
    return True, 'ok', dataset_ids


@api.admin_route.post('/get_relation_dashboard_and_dataset')
def get_relation_dashboard_and_dataset(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api  {post} /api/export/get_relation_dashboard_and_dataset  获取门户相关联报告以及数据集ID集合
    @apiGroup  报告导出
    @apiBodyParam {
        "application_ids": ["04d9dfce-c512-3e01-828a-cb2b83fcc7c3"]
    }
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data": {
            "dashboard_ids": ["39f3bc14-69a1-defa-4f70-817a2f9ef4a5"],
            "dataset_ids": ["39e7702f-cfd1-8f82-51ce-d23f5326a124"]
        }
    }
    **/
    """
    results = export_service.get_relation_dashboard_and_dataset(kwargs.get('application_ids', []))
    return True, 'ok', results


@api.admin_route.get('/get_export_items')
def get_export_items():
    """
    /**
    @apiVersion 1.0.0
    @api  {post} /api/export/get_export_items  获取当前租户是否开启了在线报告、明细报告，如开启了则可导出
    @apiGroup
    @apiBodyParam
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "key": "ppt",
                "name": "在线报告",
                "is_open": 1
            },
            {
                "key": "active_reports",
                "name": "明细报告",
                "is_open": 0
            }
        ]
    }
    **/
    """
    results = export_service.get_project_export_items()
    return True, 'ok', results


@api.admin_route.get('/export_report_tree')
def get_export_report_tree(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api  {post} /api/export/export_report_tree  获取当前租户的在线报告、明细报告树形列表
    @apiGroup
    @apiBodyParam
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data": [
            {
                id: "12312",
                name: "盛波",
                type: "FOLDER",
                parent_id: "",
                "sub": [
                    {
                        id: "3a03aa80-649e-b03a-18c9-32c22d5a7597",
                        name: "4.7.1",
                        type: "FOLDER",
                        parent_id: "39fd3e8a-27cc-38f4-0a69-a6564d85fab6",
                        "sub": [
                            {
                                id: "3a03ba6d-06c0-c454-4723-50244657266f",
                                name: "数值卡2 - 大屏",
                                type: "FILE",
                                parent_id: "3a03aa80-649e-b03a-18c9-32c22d5a7597"
                            },
                            {
                                id: "3a03ae57-d699-7715-4dea-e81c27d568da",
                                name: "数值卡2",
                                type: "FILE",
                                parent_id: "3a03aa80-649e-b03a-18c9-32c22d5a7597"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    **/
    """
    biz_type = kwargs.get("biz_type")
    if not biz_type:
        return False, '业务标识不能为空'
    if ProjectValueAddedFunc.REPORT_CENTER.value == biz_type:
        report_service = ReportService(request, **kwargs)
        rs = report_service.get_dashboard_list().get("tree")
        return True, '', rs

    begin_time = kwargs.get("begin_time")
    end_time = kwargs.get("end_time")
    include_file = kwargs.get("include_file")
    params = {
        "begin_time": begin_time,
        "end_time": end_time,
        "include_file": include_file,
    }
    results = export_service.get_biz_system_report_list(biz_type, params)
    return True, 'ok', results


@api.admin_route.post('/get_biz_relation_dataset')
def get_biz_relation_dataset(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api  {post} /api/export/get_biz_relation_dataset  获取在线报告、明细报告关联的数据集id列表
    @apiGroup
    @apiBodyParam
    @apiResponse 200 {
        "result": true,
        "msg": "ok",
        "data": [
            "1212321", "zzzzzzz"
        ]
    }
    **/
    """
    biz_type = kwargs.get("biz_type")
    if not biz_type:
        return False, '业务标识不能为空'
    ids = kwargs.get("ids")
    if not ids:
        return False, '没有选择任何报告'
    results = export_service.get_biz_relation_dataset(biz_type, ids)
    return True, 'ok', results


@api.admin_route.get('/init_export_dashboard_task')
def init_export_dashboard_task(**kwargs):
    if export_service.init_auto_export_task(kwargs.get('version', None), kwargs.get('apps', None)):
        return True, '自动导出任务注册成功'
    else:
        return False, '定时导出任务注册失败'

@api.admin_route.get('/create_sync_publish_center_task')
def create_sync_publish_center_task(**kwargs):
    if export_service.create_sync_publish_center_task(kwargs.get('autosync', 0)):
        return True, '自动同步发布中心任务注册成功'
    else:
        return False, '同步发布中心任务注册失败'


@api.admin_route.post('/delete_export_dashboard_task')
def delete_export_dashboard_task():
    record = export_service.delete_auto_export_task()
    return True, '当前租户自动导出任务已删除', record


@api.admin_route.post('/get_relation_feed_data')
def get_relation_feed_data(**kwargs):
    feed_ids = kwargs.get('feed_ids') or []
    if not feed_ids:
        return False, '请选择需要导出的简讯'
    return True, '', export_service.get_relation_feed_data(feed_ids)
