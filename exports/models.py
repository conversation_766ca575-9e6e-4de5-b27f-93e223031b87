#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/7/30.
"""
from base.models import BaseModel


class ExportDatasetModel(BaseModel):
    __slots__ = [
        'dataset_id',
        'dataset',
        'dataset_fields',
        'dataset_folders',
        'flow',
        'nodes',
        'data_source',
        'dataset_depend',
        'dataset_index',
        'dataset_tables_collection',
        'dataset_field_delete',
        'dataset_filter',
        'dataset_vars',
        'keyword',
        'keyword_details',
        'dataset_field_include_vars',
        'dataset_design',
        'dataset_of_indicator_class_map',
        'indicator_field_map'
    ]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.dataset = None
        self.dataset_fields = None
        self.dataset_folders = None
        self.flow = None
        self.nodes = None
        self.data_source = None
        self.dataset_depend = None
        self.dataset_index = None
        self.dataset_tables_collection = None
        self.dataset_field_delete = None
        self.dataset_filter = None
        self.dataset_vars = None
        self.keyword = None
        self.keyword_details = None
        self.dataset_field_include_vars = None
        self.cache_flow = None
        self.cache_nodes = None
        self.dataset_design = None
        self.dataset_of_indicator_class_map = None
        self.indicator_field_map = None
        super().__init__(**kwargs)


class ExportAppcalitonModel(BaseModel):
    __slots__ = ['application', 'application_guide_img', 'function', 'function_icon']

    def __init__(self, **kwargs):
        self.application = []
        self.application_guide_img = []
        self.function = []
        self.function_icon = []
        super().__init__(**kwargs)
