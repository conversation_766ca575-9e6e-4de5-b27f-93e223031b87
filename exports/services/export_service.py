#!/usr/bin/env python
# -*- coding: utf-8 -*-
# pylint: disable=W0703,W1202,W1201,W1401

import json
import logging
from itertools import groupby

from loguru import logger
import traceback
import os
import tempfile
import zipfile
from urllib import parse

import time
import app_celery
from dashboard_chart.services import dashboard_service
from dataset.services import dataset_service
from dmplib import redis

from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from components.oss import OSSFileProxy
from exports import EXPORT_STATUS_CREATED, EXPORT_STATUS_RUNNING, EXPORT_STATUS_FAILURE, EXPORT_STATUS_SUCCESS

from exports.repositories import export_repository
from exports.repositories.export_repository import update_export_msg
from base import repository
from base.enums import ProjectValueAddedFunc, ApplicationType
from exports.services import (
    export_dataset_service, export_dashboard_service, export_application_service, export_filling_service, export_feed_service
)
import re
from app_menu.services import external_service
from app_menu.repositories import external_application_repository
from dashboard_chart import external_service as dashboard_external_service
from exports.services.export_simple_file import check_export_simple, create_simple_zip_file
from flow.services.flow_service import delete_flow

from rbac.external_service import get_project_value_added_func
from exports.services.biz_link_service import BizLinkService
from exports.services import external_export_service
from exports.services import export_helper

from components.storage_setting import get_setting, get_project_setting


def insert_export(exports_row):
    return repository.add_data(
        'exports',
        {
            'id': seq_id(),
            'dashboard_ids': ','.join(exports_row['dashboard_ids']),
            'dataset_ids': '.'.join(exports_row['dataset_ids']),
            'url': exports_row['url'],
            'title': exports_row['filename'],
            'description': exports_row['description'],
        },
    )


def list_exports(keywords, start_time, end_time, page=1, pagesize=20):
    return export_repository.list_exports(keywords, start_time, end_time, page, pagesize)


def get_export_detail(export_id):
    if not export_id:
        return None

    export_data = repository.get_data('exports', {'id': export_id}, multi_row=False)
    if export_data:
        dashboard_ids = export_data.get('dashboard_ids')
        large_screen_ids = export_data.get('large_screen_ids')
        dataset_ids = export_data.get('dataset_ids')
        application_ids = export_data.get('application_ids')
        ppt_ids = export_data.get('ppt_ids')
        active_report_ids = export_data.get('active_report_ids')
        report_center_ids = export_data.get('report_center_ids')
        filling_ids = export_data.get('filling_ids')
        feed_ids = export_data.get('feed_ids')
        dashboard_ids = dashboard_ids.split(',') if dashboard_ids else []
        large_screen_ids = large_screen_ids.split(',') if large_screen_ids else []
        dataset_ids = dataset_ids.split(',') if dataset_ids else []
        application_ids = application_ids.split(',') if application_ids else []
        ppt_ids = ppt_ids.split(',') if ppt_ids else []
        active_report_ids = active_report_ids.split(',') if active_report_ids else []
        report_center_ids = report_center_ids.split(',') if report_center_ids else []
        filling_ids = filling_ids.split(',') if filling_ids else []
        feed_ids = feed_ids.split(',') if feed_ids else []

        export_data['dashboards'] = dashboard_service.get_dashboards_by_ids(dashboard_ids)
        export_data['large_screens'] = dashboard_service.get_dashboards_by_ids(large_screen_ids)
        export_data['datasets'] = dataset_service.get_dataset_by_ids(dataset_ids)
        export_data['applications'] = external_application_repository.batch_get_application_info(application_ids, is_release=True)
        export_data['dataset_ids'] = dataset_ids
        export_data['dashboard_ids'] = dashboard_ids
        export_data['large_screen_ids'] = large_screen_ids
        export_data['application_ids'] = application_ids
        export_data['ppt_ids'] = ppt_ids
        export_data['active_report_ids'] = active_report_ids

        export_data['filling_ids'] = filling_ids
        export_data['feed_ids'] = feed_ids
        if feed_ids:
            export_data['feeds'] = repository.get_list('dashboard_email_subscribe', {'id': feed_ids}, ['id', 'subject_email', 'msg_subscribe_config', 'created_on']) or []
        else:
            export_data['feeds'] = []
        if report_center_ids:
            export_data['report_centers'] = get_report_center_by_ids(report_center_ids)
        if active_report_ids:
            if getattr(g, 'userid', None):
                biz_link_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, g.code, getattr(g, 'userid', None))
                exists_report_list = biz_link_service.get_report_by_id(active_report_ids)
                export_data['active_reports'] = exists_report_list
    return export_data


def logic_delete_export(export_ids):
    """
    逻辑删除
    :param export_ids:
    :return:
    """
    if not export_ids:
        return False
    return export_repository.delete_exports(export_ids) > 0


def trigger_export(title, description, dashboard_ids, dataset_ids, application_ids, biz_ids, is_export_excel_data=0, is_export_all_mode=0):
    """
    is_export_excel_data:  0: 只导出Excel结构，1：导出Excel里面的数据
    """
    # 在线报告
    ppt_ids = biz_ids.get("ppt_ids")
    # 统计报告
    active_report_ids = biz_ids.get("active_report_ids")
    # 报表中心
    report_center_ids = biz_ids.get("report_center_ids")
    # 手工填报
    filling_ids = biz_ids.get("filling_ids")
    # 简讯
    feed_ids = biz_ids.get('feed_ids')
    # 酷炫大屏
    large_screen_ids = biz_ids.get('large_screen_ids')

    if not any([
        application_ids, dashboard_ids, dataset_ids, ppt_ids,
        active_report_ids, filling_ids, report_center_ids, feed_ids,
        large_screen_ids
    ]) or not title:
        raise UserError(400, message='无效的参数')

    # 校验非法字符
    if title:
        pattern = re.compile("[\\\!/<>\"\':\?\*\|]+")
        search_result = re.search(pattern, title)
        if search_result and search_result.group():
            raise UserError(message="文件名存在不支持的非法字符")

    # 存在非法的数据集
    db_dataset_ids = []
    if dataset_ids:
        db_datasets = dataset_service.get_dataset_by_ids(dataset_ids)
        db_dataset_ids = [i.get('id') for i in db_datasets if i.get('id')]
    if len(dataset_ids) != len(db_dataset_ids):
        raise UserError(message="选择的数据集中存在非法数据集")

    export_id = seq_id()
    # 需要按照根报告，带出子报告对应的数据集
    dashboard_ids = get_all_dashboard_ids(dashboard_ids)
    # 大屏也需要按照根报告，带出子报告对应的数据集
    large_screen_ids = get_all_dashboard_ids(large_screen_ids)

    # 如果是在线报告，明细报告需要判断功能是否开启
    if ppt_ids and not external_export_service.check_export_biz_is_open(ProjectValueAddedFunc.PPT.value):
        raise UserError(message="当前租户不支持在线报告的导出")
    if active_report_ids and not external_export_service.check_export_biz_is_open(ProjectValueAddedFunc.ACTIVE_REPORT.value):
        raise UserError(message="当前租户不支持明细报告的导出")
    if report_center_ids and not external_export_service.check_export_biz_is_open(ProjectValueAddedFunc.REPORT_CENTER.value):
        raise UserError(message="当前租户不支持明细报告的导出")

    ok = export_repository.insert_export(
        {
            'id': export_id,
            'dashboard_ids': ','.join(dashboard_ids),
            'large_screen_ids': ','.join(large_screen_ids),
            'dataset_ids': ','.join(dataset_ids),
            'application_ids': ','.join(application_ids),
            'ppt_ids': ','.join(ppt_ids),
            'active_report_ids': ','.join(active_report_ids),
            'report_center_ids': ','.join(report_center_ids),
            'filling_ids': ','.join(filling_ids),
            'feed_ids': ','.join(feed_ids),
            'title': title,
            'description': description,
            'url': '',
            'source_user_id': g.userid,
            'is_export_excel_data': is_export_excel_data,
            'is_export_all_mode': is_export_all_mode
        }
    )
    if report_center_ids:
        active_report_ids = report_center_ids
    # 请求在线、明细报告系统的导出接口
    export_biz_system_report(export_id, ppt_ids, active_report_ids, is_export_all_mode)

    # 导出在线报告或明细报告时，不立即添加导出任务。待对方处理成功后异步回调数见接口，再添加任务。
    if ok and (not ppt_ids and not active_report_ids and not report_center_ids):
        add_export_dashboard_task(export_id)
    return ok

CACHE_EXPORT_COUNT_KEY = "export:%s:count"
CACHE_EXPORT_RESULT_LIST_KEY = "export:%s:list"
CACHE_EXPORT_KEY_TIME_OUT = 3600

def export_biz_system_report(export_id, ppt_ids, active_report_ids, is_export_all_mode=0):
    """
    在线、明细报告系统的导出接口请求
    :param export_id:
    :param ppt_ids:
    :param active_report_ids:
    :return:
    """
    if not ppt_ids and not active_report_ids:
        return False
    try:
        count = 0
        count += 1 if ppt_ids else 0
        count += 1 if active_report_ids else 0
        redis.conn().set(CACHE_EXPORT_COUNT_KEY % export_id, count, CACHE_EXPORT_KEY_TIME_OUT)
        if ppt_ids:
            ppt_biz_service = BizLinkService(ProjectValueAddedFunc.PPT.value, g.code, g.userid)
            ppt_biz_service.export_report(export_id, ppt_ids)
        if active_report_ids:
            active_biz_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, g.code, g.userid)
            active_biz_service.export_report(export_id, active_report_ids, is_export_all_mode)
    except Exception as e:
        # 更新导出任务为失败
        msg = f'业务系统报告导出失败，errs:{str(e)}'
        export_repository.update_exports(
            export_id, {'status': EXPORT_STATUS_FAILURE, 'message': msg,
                        'completed_on': time.strftime('%Y-%m-%d %H:%M:%S')}
        )
        logger.exception(msg)


def add_export_dashboard_task(export_id):
    """
    增加celery 导出报告任务
    :param export_id:
    :return:
    """
    try:
        args = {'project_code': g.code, 'export_id': export_id, 'account': g.account, 'userid': g.userid}
        app_celery.export_dashboard.apply_async(kwargs=args, queue='parser')
        # app_celery.export_dashboard(g.code, export_id, g.account, g.userid)
    except Exception as e:
        logger.exception(e)
        export_repository.delete_exports(export_id, False)


def exec_export_task(project_code, export_id):
    """
    导出报告task, 提供给celery
    :param project_code:
    :param export_id:
    :return:
    """
    if not export_id:
        logger.error('无效的export_id')
        return

    export_data = get_export_detail(export_id)

    if not export_data:
        export_repository.update_exports(
            export_id, {'status': EXPORT_STATUS_FAILURE, 'completed_on': time.strftime('%Y-%m-%d %H:%M:%S')}
        )
        logger.error("[导出报告] export_id数据不存在")
        update_export_msg(export_id, "[导出报告] export_id数据不存在")
        return

    if not export_repository.update_exports(export_id, {'status': EXPORT_STATUS_RUNNING}):
        export_repository.update_exports(
            export_id, {'status': EXPORT_STATUS_FAILURE, 'completed_on': time.strftime('%Y-%m-%d %H:%M:%S')}
        )
        logger.error("[导出报告] 更新导出状态失败")
        update_export_msg(export_id, "[导出报告] 更新导出状态失败")
        return

    dashboard_ids = export_data.get('dashboard_ids')
    large_screen_ids = export_data.get('large_screen_ids')
    dataset_ids = export_data.get('dataset_ids')
    application_ids = export_data.get('application_ids')
    ppt_ids = export_data.get('ppt_ids')
    active_report_ids = export_data.get('active_report_ids')
    report_center_ids = export_data.get('report_center_ids')
    title = export_data.get('title')
    description = export_data.get('description')
    source_user_id = export_data.get('source_user_id')
    filling_ids = export_data.get('filling_ids')
    feed_ids = export_data.get('feed_ids') or []
    is_export_excel_data = export_data.get('is_export_excel_data', 0)
    specific_file_name = export_data.get('specific_file_name')
    is_export_all_mode = export_data.get('is_export_all_mode', 0)

    if not any([
        application_ids, dashboard_ids, dataset_ids, ppt_ids,
        active_report_ids, filling_ids, report_center_ids, feed_ids,
        large_screen_ids
    ]):
        logger.error('导出缺少关键数据，退出导出流程！')
        return

    (
        dashboard_data, application_data, dataset_data,
        filling_data, feed_data, excel_paths, large_screen_data
    ) = {}, {}, [], {}, {}, [], []

    if dataset_ids:
        # 导出数据集
        try:
            dataset_data = export_dataset_service.query_multi_dataset_data(export_id, dataset_ids)
        except Exception as e:
            update_exports_error(export_id, '数据集', e)
            return

    if is_export_excel_data:
        # 导出Excel数据集的数据
        try:
            oss_status, oss_err_msg, excel_paths = export_dataset_service.export_excel_data(export_id, dataset_ids)
            if not oss_status:
                update_exports_error(export_id, 'Excel数据集导出数据', oss_err_msg)
                return
        except Exception as e:
            update_exports_error(export_id, 'Excel数据集导出数据', e)
            return

    if dashboard_ids:
        # 导出报告
        try:
            dash_status, dash_msg, dashboard_data = export_dashboard_service.get_dashboard_issue_data(
                export_id, dashboard_ids
            )
            if not dash_status:
                update_exports_error(export_id, '报告', dash_msg)
                return
        except Exception as e:
            update_exports_error(export_id, '报告', e)
            return

    if large_screen_ids:
        # 导出酷炫大屏
        try:
            large_screen_status, large_screen_msg, large_screen_data = export_dashboard_service.get_dashboard_issue_data(
                export_id, large_screen_ids
            )
            if not large_screen_status:
                update_exports_error(export_id, '酷炫大屏', large_screen_msg)
                return
        except Exception as e:
            update_exports_error(export_id, '酷炫大屏', e)
            return

    if application_ids:
        # 导出门户
        try:
            application_data = export_application_service.get_applications_export_data(application_ids)
        except Exception as e:
            update_exports_error(export_id, '门户', e)
            return

    # 导出手工填报
    if filling_ids:
        try:
            # 获取填报数据
            filling_data = export_filling_service.get_filling_export_data(filling_ids)
        except Exception as e:
            update_exports_error(export_id, '手工填报', e)
            return

    # 导出简讯
    if feed_ids:
        try:
            feed_data = export_feed_service.get_feed_export_data(feed_ids)
        except Exception as e:
            update_exports_error(export_id, '消息订阅', e)
            return

    now = time.strftime('%Y-%m-%d %H:%M:%S')
    data = {
        'export_id': export_id,
        'title': title,
        'description': description,
        'dashboards': dashboard_data,  # 执行的数据
        'large_screens': large_screen_data,  # 酷炫大屏
        'datasets': dataset_data,  # 数据集的数据
        'applications': application_data,  # 门户数据
        'fillings': filling_data,  # 手工填报
        'feeds': feed_data,  # 简讯数据
        'export_excel_data': export_dataset_service.format_export_excel_data_output(excel_paths),  # 导出的Excel数据信息
        'source_project': project_code,
        'source_project_id': repository.get_data_scalar('project', {'code': project_code}, 'id', True),
        'is_new_jump': get_chart_redirect_is_new_jump(),
        'source_user_id': source_user_id,
        'created_on': now,
        'version': 'v1.5.0.0'  # 验证dmp未构建
    }
    # 未升级租户导出版本还是v1.0.0.0
    dataset_is_combine_entrance = True if get_project_setting("is_data_cloud_1_5_enabled", "0") in (1, '1') else False
    if not dataset_is_combine_entrance:
        data['version'] = 'v1.0.0.0'

    json_file = ''
    zipfile_file = ''
    exported_file_url = ''
    try:

        # 获取在线报告、明细的报告元数据oss zip 文件
        try:
            biz_report_local_file_map = parse_biz_system_report_zip(export_data, data) or {}
        except Exception as e:
            # 在线、明细报告zip包下载失败
            msg = f'导出失败-业务系统报告文件下载失败，errs：{str(e)}'
            export_repository.update_exports(
                export_id,
                {'url': exported_file_url, 'status': EXPORT_STATUS_FAILURE, 'message': msg,
                 'completed_on': time.strftime('%Y-%m-%d %H:%M:%S')}
            )
            logger.exception(msg+' trace:' + traceback.format_exc())
            return False

        # 导出excel数据的文件路径
        excel_data_files = [f.get('local_file_path', '') for f in excel_paths if f.get('local_file_path', '')]

        json_file = tempfile.NamedTemporaryFile(suffix='.json').name

        with open(json_file, 'w+b') as temp:
            temp.write(bytes(json.dumps(data, ensure_ascii=False), encoding='utf-8'))

        tmp_folder = os.path.dirname(json_file)
        # 支持指定下载文件名称
        if specific_file_name:
            zipfile_name = specific_file_name + '.zip'
            oss_zipfile_name = parse.quote(specific_file_name) + '.zip'
        else:
            zipfile_name = title + '_' + time.strftime('%Y%m%d%H%M%S') + '.zip'
            # oss 文件名由于oss会自动对文件名urlencode 因此实际下载文件名会变化
            oss_zipfile_name = parse.quote(title) + '_' + time.strftime('%Y%m%d%H%M%S') + '.zip'
        zipfile_file = os.path.join(tmp_folder, zipfile_name)
        oss_zipfile_file = os.path.join(tmp_folder, oss_zipfile_name)
        arc_filename = os.path.splitext(zipfile_name)[0] + '.json'

        logger.info("[导出报告或门户] 压缩文件%s" % zipfile_file)

        # with zipfile.ZipFile(zipfile_file, 'w', compression=zipfile.ZIP_DEFLATED) as zf:
        #     zf.write(json_file, arcname=arc_filename)
        #     if biz_report_local_file:
        #         zf.write(biz_report_local_file, arcname=os.path.basename(biz_report_local_file))
        #     if excel_data_files:
        #         for excel_data_file in excel_data_files:
        #             zf.write(excel_data_file, arcname=os.path.basename(excel_data_file))
        if check_export_simple(export_data) and is_export_all_mode in ('0', 0):
            create_simple_zip_file(export_data, data, zipfile_file, biz_report_local_file_map, *excel_data_files)
        else:
            biz_report_local_file = []
            if biz_report_local_file_map:
                biz_report_local_file = list(biz_report_local_file_map.values())
            zip_export_files(
                zipfile_file,
                arc_filename,
                json_file,  # json文件
                *biz_report_local_file, # 复杂报表
                *excel_data_files  # 导出的Excel文件
            )

        success = True
        if not os.path.isfile(zipfile_file):
            success = False
            logger.error('[导出报告或门户] 压缩文件失败')
            update_export_msg(export_id, '[导出报告或门户] 压缩文件失败')
        else:
            logger.info("[导出报告或门户] 上传至oss")
            exported_file_url = OSSFileProxy().upload(
                open(zipfile_file, 'rb'), file_name=os.path.join('exports', zipfile_file)
            )
            # 地址要换成实际地址
            exported_file_url = exported_file_url.replace(zipfile_file, oss_zipfile_file)

        status = EXPORT_STATUS_FAILURE if not success else EXPORT_STATUS_SUCCESS
        export_repository.update_exports(
            export_id, {'url': exported_file_url, 'status': status, 'completed_on': time.strftime('%Y-%m-%d %H:%M:%S')}
        )
    finally:
        try:
            os.unlink(json_file)
            os.unlink(zipfile_file)
        except:
            pass
        # 删除多余的本地文件
        try:
            excel_data_files = [f.get('local_file_path', '') for f in excel_paths if f.get('local_file_path', '')]
            remove_useless_files(*excel_data_files)
        except:
            pass


def zip_export_files(zipfile_file, main_json_arcname, *file_paths):
    max_size_limit = 50 * 1024 * 1024  # 50MB
    with zipfile.ZipFile(zipfile_file, 'w', compression=zipfile.ZIP_DEFLATED) as zf:
        for file_path in file_paths:
            if not file_path:
                continue
            size = os.path.getsize(zipfile_file)
            if size > max_size_limit:
                raise UserError(message=f'导出包的体积已经超过导出限制{int(max_size_limit/1024/1024)}MB')
            if file_path.endswith('.json'):
                 arc_filename = main_json_arcname
            else:
                arc_filename = os.path.basename(file_path)
            zf.write(file_path, arcname=arc_filename)


def remove_useless_files(*files):
    # 移除多余文件
    if files:
        for file in files:
            try:
                os.remove(file)
            except:
                pass


def get_chart_redirect_is_new_jump():
    from dashboard_chart.services.chart_service import METADATA_CONFIG
    return METADATA_CONFIG.get_is_new_jump()


def update_exports_error(export_id, export_type_name, err):
    err_msg = f'[导出{export_type_name}] {export_type_name}导出异常,错误信息:{str(err)}'
    export_repository.update_exports(
        export_id, {'status': EXPORT_STATUS_FAILURE, 'completed_on': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'message': err_msg}
    )
    logger.exception(err_msg)


def get_filling_relation_dataset(filling_ids: list):
    """
    根据填报ID集合 查询所依赖的数据集ID集合
    :param filling_ids:
    :return:
    """
    dataset_ids = []
    if not filling_ids:
        return dataset_ids
    dataset_ids = export_filling_service.get_filling_template_dataset_id_list(filling_ids)
    return dataset_ids


def get_dashboard_relation_dataset(dashboard_ids: list):
    """
    根据报告ID集合 查询所依赖的数据集ID集合
    :param dashboard_ids:
    :return:
    """
    if not dashboard_ids:
        return []
    dataset_ids = []
    # 需要按照根报告，带出子报告对应的数据集
    dashboard_ids = get_all_dashboard_ids(dashboard_ids)
    datas = export_repository.query_dashboard_relation_dataset(dashboard_ids)
    if datas:
        for data in datas:
            if data.get("source"):
                dataset_ids.append(data.get("source"))
    return dataset_ids


def get_relation_dashboard_and_dataset(application_ids: list):
    """
    根据门户ID集合查询所依赖的报告ID以及数据集ID集合
    :param application_ids:
    :return:
    """
    relation_data = {"dashboard_ids": [], "dataset_ids": [], "large_screen_ids": []}
    if not application_ids:
        return relation_data
    relation_dashboard_ids = external_service.get_relation_dashboard_ids(application_ids)
    # 需要按照根报告，带出子报告对应的数据集
    relation_dashboard_data = get_all_dashboard_data(relation_dashboard_ids, fields='id,application_type,name')
    relation_dashboard_ids = [d.get('id') for d in relation_dashboard_data]
    if not relation_dashboard_ids:
        return relation_data
    query_dataset_ids = export_repository.query_released_dashboard_relation_dataset(relation_dashboard_ids)
    relation_dataset_ids = [i.get("source") for i in query_dataset_ids] if query_dataset_ids else []
    relation_data["dashboard_ids"] = [d.get('id') for d in relation_dashboard_data if d.get('application_type') == ApplicationType.Dashboard.value]
    relation_data["large_screen_ids"] = [d.get('id') for d in relation_dashboard_data if d.get('application_type') == ApplicationType.LargeScreen.value]
    if relation_dataset_ids:
        relation_data["dataset_ids"] = relation_dataset_ids
    return relation_data


def get_all_dashboard_ids(dashboard_ids):
    if not dashboard_ids:
        return []
    # 需要按照根报告，带出子报告对应的数据集
    all_dashboard = dashboard_external_service.get_all_dashboard_by_ids(dashboard_ids)
    return [dashboard.get('id') for dashboard in all_dashboard]


def get_all_dashboard_data(dashboard_ids, fields='id,name'):
    fields = fields.split(',')
    if not dashboard_ids:
        return []
    # 需要按照根报告，带出子报告对应的数据集
    all_dashboard = dashboard_external_service.get_all_dashboard_by_ids(dashboard_ids)
    return [{key: dashboard.get(key) for key in fields} for dashboard in all_dashboard]


def get_project_export_items():
    """
    导出功能获取是否在线报告等功能是否开启
    :return:
    """
    project_value_aded_func = get_project_value_added_func()
    export_items = []
    export_item_map = {ProjectValueAddedFunc.PPT.value: '在线报告', ProjectValueAddedFunc.ACTIVE_REPORT.value: '统计报表'
                       , ProjectValueAddedFunc.REPORT_CENTER.value: '报表中心'}
    for item, name in export_item_map.items():
        is_open = 1 if item in project_value_aded_func else 0
        tmp_item = {"key": item, "name": name, "is_open": is_open}
        export_items.append(tmp_item)
    return export_items


def get_biz_system_report_list(biz_type, params):
    """
    在线、明细报告系统的报告树形列表
    :param biz_type:
    :param params:
    :return:
    """
    ppt_biz_service = BizLinkService(biz_type, g.code, g.userid)
    return ppt_biz_service.get_report_tree(params)


def get_active_report_file_ids():
    params = {"include_file": 1}
    ppt_biz_service = BizLinkService('active_reports', g.code, g.userid)
    result = ppt_biz_service.get_report_tree(params)
    ids = prepare_report_tree_to_list(result)
    return ids


def prepare_report_tree_to_list(tree: list):
    ids = []
    if not tree:
        return []
    for item in tree:
        if item.get('type') == 'FILE':
            ids.append(item.get('id'))
        if item.get('sub'):
            ids = ids + prepare_report_tree_to_list(item.get('sub'))
    return ids


def get_biz_relation_dataset(biz_type, ids):
    """
    获取在线、明细报告的相关数据集id列表
    :param biz_type:
    :param ids:
    :return:
    """
    ppt_biz_service = BizLinkService(biz_type, g.code, g.userid)
    return ppt_biz_service.get_biz_relation_dataset(ids)


def get_export_by_id(export_id):
    """
    获取导出任务
    :param export_id:
    :return:
    """
    return repository.get_data('exports', {'id': export_id}, multi_row=False)


def export_callback(**kwargs):
    """
    在线、明细报告导出任务的回调处理逻辑
    :param kwargs:
    :return:
    """
    export_row = {}
    try:
        logger.info("报告导出回调请求参数：" + json.dumps(kwargs, ensure_ascii=False))
        export_id = kwargs.get("export_id")
        export_data = get_export_by_id(export_id)
        if not export_data:
            raise UserError(message="导出任务不存在")

        if export_data.get("callback_result") and export_data.get("status") != EXPORT_STATUS_CREATED:
            raise UserError(message="导出任务已处理，不能再次执行")

        export_result = kwargs.get("export_result")
        if not export_result:
            raise UserError(message="导出回调数据不能为空")

        kwargs['biz_type'] = get_biz_type(export_data, **kwargs)

        result_status = export_result.get("result")
        # 业务系统报告导出失败
        if not result_status:
            msg = f"报告元数据导出失败，errs：{export_result.get('msg')}"
            raise UserError(message=msg)

        # 业务系统报告导出成功
        export_row['callback_result'] = json.dumps(kwargs, ensure_ascii=False)

        if cache_export_result(export_id, export_row, export_data) == 0:

            export_repository.update_exports(export_id, export_row)
            # 触发celery导出任务
            add_export_dashboard_task(export_id)
            logger.error(f"导出任务回调完成！export_id:{export_id}")
        return True
    except Exception as e:
        export_row['callback_result'] = json.dumps(kwargs, ensure_ascii=False)
        export_row['status'] = EXPORT_STATUS_FAILURE
        export_row['message'] = "报告导出回调接口异常，"+str(e)
        export_repository.update_exports(export_id, export_row)
        cache_export_result(export_id, export_row, export_data)
        raise UserError(message=f"报告导出回调接口异常，{str(e)}")


def cache_export_result(export_id, export_row, export_data):
    if check_export_one(export_data):
        redis.conn().sadd(CACHE_EXPORT_RESULT_LIST_KEY % export_id, export_row['callback_result'])
        redis.conn().expire(CACHE_EXPORT_RESULT_LIST_KEY % export_id, CACHE_EXPORT_KEY_TIME_OUT)
        count = redis.conn().decr(CACHE_EXPORT_COUNT_KEY % export_id)
        if count == 0:
            status = export_repository.get_export_status_by_id(export_id)
            if EXPORT_STATUS_FAILURE == status:
                return 1
            all_callback_result = list(redis.conn().smembers(CACHE_EXPORT_RESULT_LIST_KEY % export_id))
            new_all_callback_result = [json.loads(r) for r in all_callback_result]
            export_row['callback_result'] = json.dumps(new_all_callback_result, ensure_ascii=False)
        return count
    else:
        return 0


def check_export_one(export_data):
    # 插件是否只有一个导出，如果只有一个，则位置原来的导出逻辑
    count = 0
    count = count + (1 if export_data.get('ppt_ids') else 0)
    count = count + (1 if export_data.get('active_report_ids') or export_data.get('report_center_ids') else 0)
    return count > 1


def get_biz_type(export_data, **kwargs):
    # 识别业务系统
    biz_type = kwargs.get("biz_type") or ''
    if biz_type:
        if biz_type == ProjectValueAddedFunc.PPT.value:
            return biz_type
        if biz_type == ProjectValueAddedFunc.ACTIVE_REPORT.value:
            if export_data.get('active_report_ids'):
                biz_type = ProjectValueAddedFunc.ACTIVE_REPORT.value
            else:
                biz_type = ProjectValueAddedFunc.REPORT_CENTER.value
            return biz_type
    if export_data.get('ppt_ids'):
        biz_type = ProjectValueAddedFunc.PPT.value
    elif export_data.get('active_report_ids'):
        biz_type = ProjectValueAddedFunc.ACTIVE_REPORT.value
    elif export_data.get('report_center_ids'):
        biz_type = ProjectValueAddedFunc.REPORT_CENTER.value
    if not biz_type:
        raise UserError(message="业务系统报告数据不存在")
    return biz_type

def parse_biz_system_report_zip(export_data, data):
    """
    下载在线、明细报告的报告元数据zip文件
    :param export_data:
    :param data:
    :return:
    """
    ppt_ids = export_data.get('ppt_ids')
    active_report_ids = export_data.get('active_report_ids')
    report_center_ids = export_data.get('report_center_ids')
    if not ppt_ids and not active_report_ids and not report_center_ids:
        return None
    callback_result_json = export_data.get("callback_result")
    if not callback_result_json:
        raise UserError(message="业务系统的回调数据不存在")
    callback_result_obj = json.loads(callback_result_json)
    callback_result_list = []
    if isinstance(callback_result_obj, list):
        callback_result_list = callback_result_obj
    else:
        callback_result_list.append(callback_result_obj)

    local_file_path_map = {}
    for callback_result in callback_result_list:
        biz_type = callback_result.get('biz_type')
        if not biz_type:
            raise UserError(message="不支持的业务系统")
        biz_type = 'active_reports' if biz_type == 'report_center' else biz_type

        export_result = callback_result.get("export_result")
        export_result_data = export_result.get("data")
        oss_url = export_result_data.get("oss_url")
        if not oss_url:
            raise UserError(message="业务系统的报告元数据文件oss url不存在")
        # 指定名称获取
        local_file_name = f'{biz_type}.zip'
        # 模板库导出任务并不一定是oss，直接从url下载
        if str(export_data.get('title', '')).startswith('模板库数据导出任务'):
            local_file_path = export_helper.download_from_url(local_file_name, oss_url)
            download_from = 'url'
        else:
            local_file_path = export_helper.download_oss_file(local_file_name, oss_url)
            download_from = 'oss'
        if not os.path.isfile(local_file_path):
            raise UserError(message="报告元数据的文件不存在")

        logger.info(f"业务系统报告元数据从{download_from}下载成功，file:{local_file_path}")

        # 业务系统的报告导出数据与主导出文件合并
        biz_system_report_list = callback_result.get("dashboard")
        ids = ppt_ids if biz_type == ProjectValueAddedFunc.PPT.value else active_report_ids
        data[biz_type] = {
            "data": biz_system_report_list,
            "ids": ids,
        }
        if report_center_ids:
            data["report_center"] = {
                "data": [],
                "ids": report_center_ids
            }
        local_file_path_map[callback_result.get('biz_type')] = local_file_path
    return local_file_path_map


def init_auto_export_task(version=None, apps=None):
    #确认是否覆盖版本文件
    confirm_overwrite_version_file(version, apps)
    from flow.services.flow_service import update_flow_schedule
    from flow.models import FlowModel, FlowNodeModel
    from base.enums import FlowType, FlowStatus, FlowNodeType
    task_id = '00000000-0000-0000-0000-0000autotask'
    task_name = '模板库自动导出数据'
    cron = '0 0 2 ? * * *'
    data = {'id': task_id, 'name': task_name, 'schedule': cron}
    flow = FlowModel(**data)
    flow.status = FlowStatus.Enable.value
    flow.type = FlowType.Download.value
    flow.nodes = [FlowNodeModel(name=task_name, type=FlowNodeType.Download.value)]
    update_export_flow(flow)
    # 注册Rundeck任务
    update_flow_schedule(task_id, command=get_command(task_id, 'auto_export_template_data'))
    return app_celery.auto_export_template_data.apply_async(kwargs={'project_code': g.code, 'version': version, 'apps': apps})

def create_sync_publish_center_task(autosync):
    from flow.services.flow_service import update_flow_schedule
    from flow.models import FlowModel, FlowNodeModel
    from base.enums import FlowType, FlowStatus, FlowNodeType
    task_id = '00000000-0000-0000-0000-1111autotask'
    if autosync == 1:
        task_name = '模板库同步发布中心'
        cron = '0 0 5 ? * * *'
        data = {'id': task_id, 'name': task_name, 'schedule': cron}
        flow = FlowModel(**data)
        flow.status = FlowStatus.Enable.value
        flow.type = FlowType.Download.value
        flow.nodes = [FlowNodeModel(name=task_name, type=FlowNodeType.Download.value)]
        update_export_flow(flow)
        # 注册Rundeck任务
        update_flow_schedule(task_id, command=get_command(task_id, 'sync_to_publish_center'))
    else:
        flow = delete_flow(task_id)
    return app_celery.sync_to_publish_center.apply_async(kwargs={'project_code': g.code})


def delete_auto_export_task():
    task_id = '00000000-0000-0000-0000-0000autotask'
    flow = delete_flow(task_id)
    return flow


def confirm_overwrite_version_file(version, apps):
    if not version:
        return
    if apps:
        cond = ''
        or_list = []
        for app in apps:
            or_list.append(f" title like '%-{version}_{app}' ")
        cond = 'or'.join(or_list)
        sql = """
            select title from exports where {cond} group by title
        """
        sql = sql.format(cond=cond)
    else:
        sql = """
            select title from exports where title like '%-{version}_%' group by title
        """
        sql = sql.format(version=version)
    data = repository.get_data_by_sql(sql, params=None)
    exp = 8
    max = 3
    if data:
        conn = redis.conn()
        key = f'template_file_version_confirm:{version}'
        exists_apps = [item['title'] for item in data]
        if conn.exists(key):
            count = conn.incr(key)
            if count < max:
                raise UserError(message=f"{exists_apps}版本{version}已存在，确认覆盖文件请在{exp}内调用{max}次，当前第{count}次")
            else:
                conn.delete(key)
                return
        else:
            conn.set(key, 1, exp)
            raise UserError(message=f"{exists_apps}版本{version}已存在，确认覆盖文件请在{exp}秒内调用{max}次，当前第1次")


def get_command(flow_id, func, queue_name='celery'):
    """
    获取rundeck执行celery的command命令
    :param flow_id:
    :param queue_name:
    :return:
    """
    from dmplib import config
    celery_task_name = "app_celery." + func
    cmd_template_snap = config.get(
        "Rundeck.cmd_template_celery",
        "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
    )
    command = '%s %s %s %s %s' % (cmd_template_snap, g.code, celery_task_name, flow_id, queue_name)
    return command


def update_export_flow(flow_model):
    from flow.services.flow_service import add_flow, update_flow
    flow = repository.get_data("flow", {"id": flow_model.id})
    if flow:
        update_flow(flow_model, False)
    else:
        add_flow(flow_model)
    return flow


def sync_to_publish_center():
    logging.info('开始同步模板库到发布中心..')
    from components.publish_center_api import PublishCenterApi
    apps = repository.get_data('product_application_relation', conditions=None, fields=['app_name'],
                              multi_row=True, from_config_db=True)
    or_list = []
    for app in apps:
        or_list.append(f" title like '模板库数据导出任务-{app.get('app_name')}%' ")
    cond = 'or'.join(or_list)
    sql = """
        select url,title as name from (
				select *
				from exports where created_on > DATE_SUB(NOW(), INTERVAL 1 MONTH)
				and {cond} 
				order by created_on desc limit 9999
        ) t group by title
    """
    sql = sql.format(cond=cond)
    data = repository.get_data_by_sql(sql, params=None)
    try:
        api = PublishCenterApi()
        api.sync_template_data(data)
    except Exception as e:
        logging.error('同步模板库到发布中心异常', e)


def auto_export_template_data(version=None, apps=None):
    logging.info('开始模板库导出任务..')
    # 按应用、目录(子系统)分别创建导出任务
    params = None
    if apps:
        params = {'app_name':apps}
    app_list = repository.get_list('product_application_relation', params, ['app_name', 'app_code'], from_config_db=True)
    if not app_list:
        return True
    app_name_list = [x['app_name'] for x in app_list]
    task_map = {}
    create_application_template_data_export_task(app_name_list, task_map)
    create_dashboard_template_data_export_task(app_name_list, task_map)
    create_feeds_template_data_export_task(app_list, task_map)
    create_report_center_template_data_export_task(app_name_list, task_map)
    # 统计报表不同步
    # create_active_report_template_data_export_task(app_name_list, task_map)
    for app_name,data in task_map.items():
        try:
            export_id = seq_id()
            dashboard_ids = check_dashboard_exists(remove_none_elements(data['dashboard_ids']))
            large_screen_ids = check_dashboard_exists(remove_none_elements(data['large_screen_ids']))
            dataset_ids = check_dataset_exists(remove_none_elements(data['dataset_ids']))
            report_ids = remove_none_elements(data['report_ids'])
            report_center_ids = remove_none_elements(data['report_center_ids'])
            specific_file_name = ''
            if version:
                file_name = version + '_' + app_name
                specific_file_name = file_name
            else:
                file_name = app_name
            export_repository.insert_export(
                {
                    'id': export_id,
                    'feed_ids': ','.join(remove_none_elements(data['feed_ids'])),
                    'application_ids': ','.join(remove_none_elements(data['app_ids'])),
                    'active_report_ids': ','.join(report_ids),
                    'report_center_ids': ','.join(report_center_ids),
                    'dashboard_ids': ','.join(dashboard_ids),
                    'large_screen_ids': ','.join(large_screen_ids),
                    'dataset_ids':  ','.join(dataset_ids),
                    'title': f'模板库数据导出任务-{file_name}',
                    'description': f'模板库数据导出任务-{file_name}',
                    'url': '',
                    'source_user_id': g.userid,
                    'specific_file_name': specific_file_name,
                }
            )
            # 统计报表、复杂报表二选一，优先复杂报表
            report_ids = report_center_ids if report_center_ids else report_ids
            if not report_ids:
                exec_export_task(g.code, export_id)
            else:
                try:
                    export_biz_system_report(export_id, [], list(set(report_ids)))
                except Exception as e:
                    logger.error(f'模板库导出统计报表服务调用失败:{str(e)}')
                    # 如果统计报表服务调用失败
                    exec_export_task(g.code, export_id)
            logging.info(f'模板库子系统[{app_name}]导出完成')
        except Exception as e:
            logger.error(f'模板库子系统[{app_name}]导出任务异常:{str(e)}')
    return True


def get_all_template_active_reports(app_list):
    ppt_biz_service = BizLinkService('active_reports', g.code, g.userid)
    params = {"include_file": 1}
    result = ppt_biz_service.get_report_tree(params)
    if not result:
        return []
    if len(result) == 1 and not result[0]['parent_id']:
        result = result[0]['sub']
    report_dict = {}
    ext_reports(result, app_list, report_dict, None)
    return report_dict


def ext_reports(tree, app_list, report_dict, belong_app):
    if not tree:
        return []
    ids = []
    for item in tree:
        if item['type'] == 'FOLDER':
            name = item['name']
            if not name in app_list:
                if belong_app or item['sub']:
                    arr = ext_reports(item['sub'], app_list, report_dict, belong_app)
                    if arr and belong_app:
                        arr += report_dict.get(belong_app, [])
                        report_dict[belong_app] = arr
                else:
                    continue
            else:
                arr = ext_reports(item['sub'], app_list, report_dict, name)
                if arr:
                    arr += report_dict.get(name, [])
                    report_dict[name] = arr
        else :
            ids.append(item['id'])
            sub = item.get('sub', [])
            if sub:
                for child in sub:
                    ids.append(child['id'])
    return ids


def create_application_template_data_export_task(app_name_list, task_map):
    sql = '''
        select * from application where {cond}
    '''
    sql = sql.format(cond = splicing_level_code_cond(app_name_list))
    template_apps = repository.get_data_by_sql(sql, params=None)
    if not template_apps:
        return
    for app in template_apps:
        app_id = app['id']
        app_name = precison_app_name(app_name_list, app['name'])
        try:
            rel = get_relation_dashboard_and_dataset([app_id])
            dashboard_ids = rel['dashboard_ids'] or []
            dataset_ids = rel['dataset_ids'] or []
            compute_new_value(app_name, task_map, app_ids = [app_id],
                               dashboard_ids = dashboard_ids, dataset_ids = dataset_ids)
        except Exception as e:
            logger.error(f'创建模板库门户导出任务[{app_name}]异常:{str(e)}')

def precison_app_name(app_name_list, name:str):
    for app_name in app_name_list:
        if name.startswith(app_name):
            return app_name
    return name

def remove_none_elements(ids):
    if not ids:
        return []
    ids = list(set(ids))
    return [x for x in ids if x]

def check_dataset_exists(ids):
    if not ids:
        return []
    return list(set(repository.get_column('dataset', {'id': ids}, 'id') or []))

def check_dashboard_exists(ids):
    if not ids:
        return []
    return list(set(repository.get_column('dashboard', {'id': ids}, 'id') or []))

def compute_new_value(app_name, task_map, app_ids = None, dashboard_ids = None, dataset_ids = None, feed_ids = None, report_ids = None, report_center_ids = None, large_screen_ids = None):
    app_ids = app_ids or []
    dashboard_ids = dashboard_ids or []
    dataset_ids = dataset_ids or []
    feed_ids = feed_ids or []
    report_ids = report_ids or []
    report_center_ids = report_center_ids or []
    large_screen_ids = large_screen_ids or []
    if app_name not in task_map:
        task_map[app_name] = {'app_ids': app_ids, 'dashboard_ids': dashboard_ids, 'dataset_ids': dataset_ids,
                              'feed_ids': feed_ids, 'report_ids': report_ids, 'report_center_ids': report_center_ids,
                              'large_screen_ids': large_screen_ids}
    else:
        data = task_map[app_name]
        data['app_ids'] += app_ids
        data['dashboard_ids'] += dashboard_ids
        data['dataset_ids'] += dataset_ids
        data['feed_ids'] += feed_ids
        data['report_ids'] += report_ids
        data['report_center_ids'] += report_center_ids
        data['large_screen_ids'] += large_screen_ids


def create_feeds_template_data_export_task(app_list, task_map):
    groups = []
    app_map = {}
    for item in app_list:
        app_map[item['app_code']] = item['app_name']
    try:
        template_feeds = repository.get_list('dashboard_email_subscribe_extra', {'app_level_code': list(app_map.keys())},
                                             ['id', 'app_level_code']) or []
        groups = groupby(template_feeds, key=lambda x: (x['app_level_code']))
    except Exception as e:
        logger.error(f'创建模板库简讯导出任务异常:{str(e)}')

    for key, val in groups:
        app_name = app_map[key]
        try:
            ids = [x['id'] for x in list(val)]
            rel = get_relation_feed_data(ids)
            dataset_ids = []
            dashboard_ids = []
            if rel:
                for id in ids:
                    feeds_rel = rel.get(id)
                    if not feeds_rel:
                        continue
                    dataset_ids += feeds_rel['dataset_ids']
                    dashboard_ids.append(feeds_rel['dashboard_ids'])
            compute_new_value(app_name, task_map, dashboard_ids = dashboard_ids, dataset_ids = dataset_ids, feed_ids= ids)
        except Exception as e:
            logger.error(f'创建模板库简讯导出任务[{app_name}]异常:{str(e)}')


def create_active_report_template_data_export_task(app_list, task_map):
    try:
        report_map = get_all_template_active_reports(app_list)
        for key, val in report_map.items():
            if val:
                dataset_ids = get_biz_relation_dataset('active_reports', val) or []
            compute_new_value(key, task_map, dataset_ids = dataset_ids, report_ids = val)
    except Exception as e:
        logger.error(f'创建模板库统计报表导出任务异常:{str(e)}')
        logger.error(traceback.format_exc())



def create_report_center_template_data_export_task(app_name_list, task_map):
    try:
        params = {"include_file": 1}
        result = dashboard_service.get_report_center_list(**params).get("tree")
        if not result:
            return
        report_dict = {}
        ext_reports(result, app_name_list, report_dict, None)
        for key, val in report_dict.items():
            if val:
                dataset_ids = get_biz_relation_dataset('report_center', val) or []
            compute_new_value(key, task_map, dataset_ids = dataset_ids, report_center_ids = val)
    except Exception as e:
        logger.error(f'创建模板库统计报表导出任务异常:{str(e)}')
        logger.error(traceback.format_exc())


def create_dashboard_template_data_export_task(app_name_list, task_map):
    sql = '''
        select name,level_code from dashboard where type = 'FOLDER' and ({cond})
    '''
    sql = sql.format(cond=splicing_level_code_cond(app_name_list))
    app_folder = repository.get_data_by_sql(sql, params=None)
    if not app_folder:
        return
    app_groups = group_app_folder(app_folder)
    for key, val in app_groups.items():
        try:
            conds = [" level_code like '" + s + "%' " for s in val]
            or_cond = 'or'.join(conds)
            # 需要支持导出第三方报表
            sql = f"select id from dashboard where type in ('FILE', 'CHILD_FILE') and application_type in ({ApplicationType.Dashboard.value}, {ApplicationType.HD_Dashboard.value}, {ApplicationType.External_Dashboard.value}, {ApplicationType.FineReport.value}) and ({or_cond})"
            data = repository.get_data_by_sql(sql, params=None)
            dashboard_ids = [x['id'] for x in data]
            dataset_ids = get_dashboard_relation_dataset(dashboard_ids)
            app_name = precison_app_name(app_name_list, key)
            compute_new_value(app_name, task_map, dashboard_ids = dashboard_ids, dataset_ids = dataset_ids)
            # 大屏
            sql = f"select id from dashboard where type in ('FILE', 'CHILD_FILE') and application_type in ({ApplicationType.LargeScreen.value}) and ({or_cond})"
            data = repository.get_data_by_sql(sql, params=None)
            large_screen_ids = [x['id'] for x in data]
            large_screen_dataset_ids = get_dashboard_relation_dataset(large_screen_ids)
            compute_new_value(app_name, task_map, large_screen_ids=large_screen_ids,
                              dataset_ids=large_screen_dataset_ids)
        except Exception as e:
            logger.error(f'创建模板库仪表板导出任务[{key}]异常:{str(e)}')

def group_app_folder(app_folder):
    folder_map = {}
    for folder in app_folder:
        codes = folder_map.get(folder['name'])
        if codes:
            codes.append(folder['level_code'])
        else:
            folder_map[folder['name']] = [folder['level_code']]
    return folder_map


def splicing_level_code_cond(app_list):
    conds = [" name like '" + x + "%' " for x in app_list]
    cond = 'or'.join(conds)
    return cond

def auto_export_dashboard():
    # 获取所有需要导出的报告ID和数据集ID
    dashboard = repository.get_column('dashboard', {'type': ['FILE', 'CHILD_FILE']}, 'id') or []
    dashboard_ids = list(set(dashboard))
    dataset_ids = repository.get_column('dataset', {'content !=': ''}, 'id') or []
    dataset_ids = list(set(dataset_ids))
    # 获取统计报告相关数据
    active_report_ids = get_active_report_file_ids()
    export_id = seq_id()
    export_repository.insert_export(
        {
            'id': export_id,
            'dashboard_ids': ','.join(dashboard_ids),
            'dataset_ids': ','.join(dataset_ids),
            'active_report_ids': ','.join(active_report_ids),
            'title': '产业建管自动导出报告任务',
            'description': '产业建管自动导出报告任务',
            'url': '',
            'source_user_id': g.userid,
        }
    )
    if not active_report_ids:
        return exec_export_task(g.code, export_id)
    # 请求统计报告导出接口
    export_biz_system_report(export_id, [], active_report_ids)
    return True


def get_relation_feed_data(feed_ids: list):
    """
    获取简讯需要导出的相关报告和数据集信息
    """
    feed_data = {}
    # 获取简讯使用的报告和数据集
    feed_list = repository.get_list('dashboard_email_subscribe', {'id': feed_ids}, ['id', 'dashboard_id', 'dataset_ids']) or []
    for feed in feed_list:
        feed_id = feed.get('id') or ''
        if not feed_id:
            continue
        try:
            dataset_id = json.loads(feed.get('dataset_ids'))
        except Exception as e:
            logger.error(str(e))
            dataset_id = []
        dashboard_id = feed.get('dashboard_id') or ''
        dataset_ids = dataset_id or []
        # 获取报告对应使用的所有数据集
        if dashboard_id:
            # 获取对应报告的所有子报告包含自己
            all_dashboard_ids = get_all_dashboard_ids(dashboard_id)
            # 获取所有报告使用的数据集
            if all_dashboard_ids:
                all_dataset_ids = get_dashboard_relation_dataset(all_dashboard_ids) or []
                dataset_ids = dataset_ids + all_dataset_ids
        data = {feed_id: {'dataset_ids': list(set(dataset_ids)), 'dashboard_ids': dashboard_id}}
        feed_data.update(data)
    return feed_data


def get_report_center_by_ids(report_center_ids):
    if not report_center_ids:
        return []

    return repository.get_data_by_sql(
        """select  id, name, modified_on, created_on ,level_code  
        from dashboard where id in %(ids)s and application_type in (5,6) order by level_code  asc""",
        {'ids': report_center_ids},
    )