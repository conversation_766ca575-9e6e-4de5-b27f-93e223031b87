#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
门户分发导出
"""
# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from exports.models import ExportAppcalitonModel
from app_menu.repositories import external_application_repository
from collections import defaultdict


def get_applications_export_data(application_ids: list):
    """
    获取门户分发导出数据
    :param application_ids:
    :return:
    """
    application_export_data = {}
    if not application_ids:
        return application_export_data

    applications = external_application_repository.batch_get_all_applications(application_ids, is_release=True)
    guide_imgs = external_application_repository.batch_get_all_application_guide_imgs(application_ids)
    functions = external_application_repository.batch_get_all_functions(application_ids, is_release=True)
    function_icons = external_application_repository.batch_get_all_function_icons()
    applications_in_dict = format_to_dict(applications, "id")
    guide_imgs_in_dict = format_to_dict(guide_imgs, "application_id")
    functions_in_dict = format_to_dict(functions, "application_id")
    have_function_icons = False

    for application_id in application_ids:
        export_application_model = ExportAppcalitonModel()
        export_application_model.application = applications_in_dict.get(application_id, [])
        export_application_model.application_guide_img = guide_imgs_in_dict.get(application_id, [])
        export_application_model.function = functions_in_dict.get(application_id, [])
        if not have_function_icons:
            export_application_model.function_icon = function_icons
            have_function_icons = True
        application_export_data[application_id] = export_application_model.get_dict()
    return application_export_data


def format_to_dict(data: list, key: str):
    """
    格式化为dict数据
    :param application_ids:
    :return:
    """
    dict_data = defaultdict(list)
    if not data:
        return dict_data
    for item in data:
        dict_data[item.get(key)].append(item)
    return dict_data
