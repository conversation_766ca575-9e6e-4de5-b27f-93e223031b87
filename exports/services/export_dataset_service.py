#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by chenchao 2018/7/30.
"""
import copy
import json
import os
import re
import traceback
from typing import List, Dict

from loguru import logger

from base import repository
from base.enums import DatasetType
from dataset.repositories import dataset_repository
from dmplib.utils.errors import UserError
from exports.models import ExportDatasetModel
from exports.repositories.export_repository import update_export_msg
from dmplib import config
from exports.services import export_helper


def query_dataset_data(export_id, dataset_id):
    """
    根据数据集ID查询数据集相关的表数据
    # dataset
    # dataset_field
    # flow
    # node
    # data_source
    :param export_id:
    :param dataset_id:
    :param group_type:
    :param dataset:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    dataset = repository.get_data("dataset", {"id": dataset_id})
    if not dataset:
        message = "数据集不存在：" + dataset_id
        update_export_msg(export_id, message)
        raise UserError(message=message)
    # 导出多sql
    dataset_design = repository.get_list('dataset_design', {'dataset_id': dataset_id}) or []
    for item in dataset_design:
        if item.get('is_enable') in ['1', 1]:
            dataset['content'] = item.get('content')
    try:
        dataset_content = json.loads(dataset.get("content"))
    except Exception as e:
        message = "解析数据集content字段json错误：" + str(e)
        update_export_msg(export_id, message)
        raise UserError(message=message) from e

    dataset_fields = repository.get_data("dataset_field", {"dataset_id": dataset_id}, multi_row=True)
    # 数据集所属父级文件夹
    dataset_folders = dataset_repository.get_dataset_folders(dataset.get("level_code")[0:5])
    flow = repository.get_data("flow", {"id": dataset_id})
    nodes = repository.get_data("node", {"flow_id": dataset_id}, multi_row=True)
    cache_flow = None
    cache_nodes = None
    if dataset.get('cache_flow_id'):
        cache_flow_id = dataset.get('cache_flow_id')
        cache_flow = repository.get_data("flow", {"id": cache_flow_id})
        cache_nodes = repository.get_data("node", {"flow_id": cache_flow_id}, multi_row=True)

    # 导出数据集如果数据源为数见数据源不需要导出
    data_source = repository.get_data("data_source", {"id": dataset_content.get("data_source_id")})
    if data_source and data_source.get('is_buildin') == 1:
        data_source = None

    dataset_depend = repository.get_data("dataset_depend", {"depend_id": dataset_id}, multi_row=True)
    dataset_index = repository.get_data("dataset_index", {"dataset_id": dataset_id}, multi_row=True)
    dataset_tables_collection = repository.get_data(
        "dataset_tables_collection", {"dataset_id": dataset_id}, multi_row=True
    )
    dataset_filter = repository.get_data("dataset_filter", {"dataset_id": dataset_id}, multi_row=True)
    dataset_field_delete = repository.get_data("dataset_field_delete", {"dataset_id": dataset_id}, multi_row=True)
    dataset_vars = repository.get_data("dataset_vars", {"dataset_id": dataset_id}, multi_row=True)
    dataset_field_include_vars = repository.get_data(
        "dataset_field_include_vars", {"dataset_id": dataset_id}, multi_row=True
    )
    dataset_used_table = repository.get_data(
        "dataset_used_table", {"dataset_id": dataset_id}, multi_row=True
    )

    keyword, keyword_details = collect_keyword(dataset_id)

    # 指标模型数据集和字段映射
    try:
        dataset_of_indicator_class_map = repository.get_list(
            'dataset_of_indicator_class_map', {'dataset_id': dataset_id}
        )
        indicator_field_map = repository.get_list(
            'indicator_field_map',
            {
                'dataset_field_id': [i.get('id') for i in dataset_fields]
            }
        )
    except Exception as e:
        logger.error(f"get indicator map error: {e}")
        dataset_of_indicator_class_map = []
        indicator_field_map = []

    export_dataset_model = ExportDatasetModel()
    export_dataset_model.dataset_design = dataset_design
    export_dataset_model.dataset_id = dataset_id
    dataset['replacement_id'] = ''
    export_dataset_model.dataset = dataset
    export_dataset_model.dataset_fields = dataset_fields
    export_dataset_model.dataset_folders = get_related_folders(dataset_folders, dataset.get("level_code"))
    export_dataset_model.flow = flow
    export_dataset_model.cache_flow = cache_flow
    export_dataset_model.nodes = nodes
    export_dataset_model.cache_nodes = cache_nodes
    export_dataset_model.data_source = data_source
    export_dataset_model.keyword = keyword
    export_dataset_model.keyword_details = keyword_details
    export_dataset_model.dataset_depend = dataset_depend
    export_dataset_model.dataset_index = dataset_index
    export_dataset_model.dataset_tables_collection = dataset_tables_collection
    export_dataset_model.dataset_field_delete = dataset_field_delete
    export_dataset_model.dataset_filter = dataset_filter
    export_dataset_model.dataset_vars = dataset_vars
    export_dataset_model.dataset_field_include_vars = dataset_field_include_vars
    export_dataset_model.dataset_of_indicator_class_map = dataset_of_indicator_class_map
    export_dataset_model.indicator_field_map = indicator_field_map
    export_dataset_model.dataset_used_table = dataset_used_table
    export_dataset_data = export_dataset_model.get_dict()

    return export_dataset_data


def query_dataset_data_for_pulsar(export_id, dataset_id, group_type=None, dataset=None):
    """
    根据数据集ID查询数据集相关的表数据
    # dataset
    # dataset_field
    # flow
    # node
    # data_source
    :param export_id:
    :param dataset_id:
    :param group_type:
    :param dataset:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    if not dataset:
        message = "数据集不存在：" + dataset_id
        update_export_msg(export_id, message)
        raise UserError(message=message)
    dataset_content = {}

    dataset_fields = repository.get_data(
        "dataset_field", {"dataset_id": dataset_id, "group_type": group_type}, multi_row=True
    )
    # 数据集所属父级文件夹
    dataset_folders = dataset_repository.get_dataset_folders(dataset.get("level_code")[0:5])
    data_source = repository.get_data("data_source", {"id": dataset_content.get("data_source_id")})
    dataset_filter = repository.get_data("dataset_filter", {"dataset_id": dataset_id}, multi_row=True)
    dataset_vars = repository.get_data("dataset_vars", {"dataset_id": dataset_id}, multi_row=True)
    dataset_field_include_vars = repository.get_data(
        "dataset_field_include_vars", {"dataset_id": dataset_id}, multi_row=True
    )

    export_dataset_model = ExportDatasetModel()
    export_dataset_model.dataset_id = dataset_id
    dataset['replacement_id'] = ''
    export_dataset_model.dataset = dataset
    export_dataset_model.dataset_fields = dataset_fields
    export_dataset_model.dataset_folders = get_related_folders(dataset_folders, dataset.get("level_code"))
    export_dataset_model.data_source = data_source
    export_dataset_model.dataset_filter = dataset_filter
    export_dataset_model.dataset_vars = dataset_vars
    export_dataset_model.dataset_field_include_vars = dataset_field_include_vars
    export_dataset_data = export_dataset_model.get_dict()

    return export_dataset_data


def collect_keyword(dataset_id):
    # 收集导出的所有关键字，包含嵌套关键字
    keyword_result = []
    _collect_keyword_(dataset_id, keyword_result)
    keyword_details_result = repository.get_data("keyword_details", {"dataset_id": dataset_id}, multi_row=True) or []
    return keyword_result, keyword_details_result


def _collect_keyword_(dataset_id, keyword_result, with_names=[], datasource_id=''):
    if not with_names:
        # 1. 根据数据集id查找关键字
        keywords = repository.get_data_by_sql(
            """
            select distinct keyword.* from  keyword 
            join keyword_details on keyword_details.keyword_id = keyword.id 
            WHERE dataset_id = %(dataset_id)s 
            """,
            {"dataset_id": dataset_id}
        ) or []
    else:
        # 2. 根据关键字名字和数据源id查找关键字
        keywords = repository.get_data_by_sql(
            """
            select distinct * from keyword 
            WHERE keyword_name in %(names)s and datasource_id = %(datasource_id)s
            """,
            {"names": with_names, "datasource_id": datasource_id}
        ) or []

    # 解析嵌套关键字
    match = r'(?<=\[key:)[^key:\]]+(?=\])'
    for one_keyword in keywords:
        keyword_sql_text = one_keyword.get('sql_text') or ''
        keyword_datasource_id = one_keyword.get('datasource_id') or ''
        if not keyword_sql_text:
            continue

        match_list = list(set(re.findall(match, keyword_sql_text)))
        if not match_list:
            continue

        _collect_keyword_(dataset_id, keyword_result, with_names=match_list, datasource_id=keyword_datasource_id)

    for keyword in keywords:
        if keyword not in keyword_result:
            keyword_result.append(keyword)


def get_related_folders(folders, level_code):
    """
    获取相关联的文件夹
    :param folders:
    :param level_code:
    :return:
    """
    levels = level_code.split("-")
    current_level = None
    new_folders = []
    for i, level in enumerate(levels):
        if i < len(levels) - 2:
            current_level = current_level + level + "-" if current_level else level + "-"
            for folder in folders:
                if current_level == folder.get("level_code"):
                    new_folders.append(folder)
                    break
    return new_folders


def query_multi_dataset_data(export_id, dataset_ids, group_type=None):
    """
    根据多个数据集ID查询数据集相关的表数据
    :param export_id:
    :param dataset_ids:
    :param group_type:
    :return:
    """
    dataset_data_list = []
    for dataset_id in dataset_ids:
        if group_type:
            dataset = repository.get_data("dataset", {"id": dataset_id}) or {}
            if group_type and dataset.get("type") == "FOLDER":
                continue
            export_dataset_data = query_dataset_data_for_pulsar(export_id, dataset_id, group_type=group_type,
                                                                dataset=dataset)
        else:
            export_dataset_data = query_dataset_data(export_id, dataset_id)

        # 获取组合数据集数据
        if export_dataset_data.get("dataset").get("type") == DatasetType.Union.value:
            union_dataset_ids = []
            get_union_ids(dataset_id, union_dataset_ids)
            for union_dataset_id in set(union_dataset_ids):
                union_dataset_data = query_dataset_data(export_id, union_dataset_id)
                dataset_data_list.append(union_dataset_data)

        dataset_data_list.append(export_dataset_data)
    return dataset_data_list


def get_union_ids(dataset_id, dataset_ids):
    """
    获取组合数据集的依赖数据集ids
    :return:
    """
    source_dataset_ids = repository.get_columns('dataset_depend', {'depend_id': dataset_id}, 'source_dataset_id')
    if source_dataset_ids:
        for source_dataset_id in source_dataset_ids:
            dataset_ids.append(source_dataset_id)
            dataset = repository.get_data('dataset', {'id': source_dataset_id}, ['id', 'type'])
            if dataset.get("type") == DatasetType.Union.value:
                get_union_ids(source_dataset_id, dataset_ids)


def export_excel_data(export_id, dataset_ids):
    """'
    导出Excel数据集的数据到文件
    """
    from dataset.services.dataset_async_service import download_dataset_to_excel

    datasets = repository.get_data('dataset', conditions={'id': dataset_ids, 'type': 'EXCEL'}, multi_row=True) or []
    logger.info(f'要导出excel数据集数据个数： {len(datasets)}')
    if not datasets:
        return True, '', {}

    result = []
    dataset_id = ''
    dataset_name = ''
    limit_num = int(config.get('Function.excel_export_rows', 1000))
    for dataset in datasets:
        try:
            dataset_id = dataset.get('id', '')
            dataset_name = dataset.get('name', '')
            local_file_name = generate_excel_file_name(dataset)
            logger.info(f'开始导出Excel数据集数据: {dataset_name}, id: {dataset_id}')

            file_path = download_dataset_to_excel(
                version_id=None, dataset_id=dataset_id, dataset=dataset,
                limit_num=limit_num, upload_oss=False
            )
            if not os.path.exists(file_path):
                raise UserError(message=f"导出的Excel数据<{dataset_id}><{dataset_name}>，文件不存在")

            # 改成Excel指定的名字
            local_file_path = os.path.join(os.path.dirname(file_path), local_file_name)
            os.rename(file_path, local_file_path)
            logger.info(f'已经导出Excel数据集数据<{dataset_id}><{dataset_name}>到临时目录: {local_file_path}')
            result.append({
                'id': dataset_id,
                'name': dataset.get('name', ''),
                'table_name': dataset.get('table_name', ''),
                'type': dataset.get('type', ''),
                'zip_path': local_file_name,
                'local_file_path': local_file_path,
            })
        except Exception as e:
            logger.exception(
                f'导出Excel数据集数据失败，数据集id<{dataset_id}>name<{dataset_name}>: {traceback.format_exc()}')
            msg = f'数据集id<{dataset_id}>name<{dataset_name}>：{str(e)}'
            update_export_msg(export_id, msg)
            return False, msg, []

    logger.info('完成导出Excel数据集数据')
    return True, '', result


def generate_excel_file_name(data):
    """
    生成导出的Excel在压缩包内的路径名
    """
    id = data.get("id", "")
    return f'excel_data_{id}.csv'


def format_export_excel_data_output(datas):
    """
    datas : {
                'id': dataset_id,
                'name': dataset.get('name', ''),
                'table_name': dataset.get('table_name', ''),
                'type': dataset.get('type', ''),
                'zip_path': local_file_name,
                'local_file_path': local_file_path,

    """
    result = []
    datas = copy.deepcopy(datas)
    for data in datas:
        data.pop('local_file_path', None)
        result.append(data)
    return result
