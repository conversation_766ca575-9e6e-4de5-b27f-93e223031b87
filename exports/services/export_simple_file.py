import json
import os
import shutil
import traceback
import zipfile
from collections import deque
from dmplib.utils.errors import User<PERSON>rror


def check_export_simple(export_data):
    if export_data.get('filling_ids'):
        return False
    return True


def create_simple_zip_file(export_data, data, zipfile_file, report_file_map, *data_file_paths):
    try:
        export_id = data.get("export_id")
        dataset_data = data.get("datasets")
        dashboard_data = data.get("dashboards")
        large_screen_data = data.get("large_screens")
        application_data = data.get("applications")
        feed_data = data.get("feeds")
        report_type = "report_center" if export_data.get('report_center_ids') else "active_reports"
        root_path = "dmp_export_tmp/" + export_id
        if not os.path.exists(root_path):
            # 使用os.makedirs()函数创建目录
            os.makedirs(root_path)

        all_file = [create_base_info_file(root_path, get_base_info(data))]
        all_file.extend(create_list_file(root_path, dataset_data, "datasets"))
        all_file.extend(create_map_file(root_path, dashboard_data, "dashboards"))
        all_file.extend(create_map_file(root_path, large_screen_data, "large_screens"))
        all_file.extend(create_application_file(root_path, application_data, "applications"))
        all_file.extend(create_feed_file(root_path, feed_data))
        for report_type in report_file_map:
            all_file.extend(create_report_file(root_path, report_file_map.get(report_type), report_type, data))

        create_zip_file(all_file, zipfile_file, *data_file_paths)
    except Exception as e:
        # 更新导出任务为失败
        traceback.print_exc()
        msg = f'业务系统报告导出失败，errs:{str(e)}'
        raise UserError(message=msg)
    finally:
        # 删除多余的本地文件
        try:
            if os.path.exists(root_path):
                shutil.rmtree(root_path)
        except:
            pass


def get_base_info(data):
    export_excel_data = data.get("export_excel_data")
    if export_excel_data:
        for export_dataset_info in export_excel_data:
            export_dataset_info["zip_path"] = 'excel/' + export_dataset_info["zip_path"]

    return {
        'export_id': data.get('export_id'),
        'title': data.get('title'),
        'description': data.get('description'),
        'source_project': data.get('source_project'),
        'source_project_id': data.get('source_project_id'),
        'is_new_jump': data.get('is_new_jump'),
        'source_user_id': data.get('source_user_id'),
        'created_on': data.get('created_on'),
        'version': data.get('version'),
        "export_excel_data": export_excel_data
    }


def create_zip_file(all_file, zipfile_file, *data_file_paths):
    zip_file = zipfile.ZipFile(zipfile_file, 'w', zipfile.ZIP_DEFLATED)
    for path in all_file:
        relative_path = os.path.join(*path.split('/')[2:])
        zip_file.write(path, arcname=relative_path)
    for path in data_file_paths:
        relative_path = os.path.join("excel", os.path.basename(path))
        zip_file.write(path, arcname=relative_path)
    zip_file.close()


def create_base_info_file(root_path, base_info):
    directory = root_path + "/base"
    os.makedirs(directory)
    file_path = directory + "/info.json"
    with open(file_path, 'w+b') as temp:
        temp.write(bytes(json.dumps(base_info, ensure_ascii=False), encoding='utf-8'))
    return file_path


def create_list_file(root_path, data, table):
    filter_data(data)
    directory = root_path + "/" + table
    os.makedirs(directory)
    file_list = []
    for row in data:
        file_path = directory + "/" + row.get('dataset_id') + ".json"
        with open(file_path, 'w+b') as temp:
            temp.write(bytes(json.dumps(row, ensure_ascii=False), encoding='utf-8'))
        file_list.append(file_path)
    return file_list


def create_map_file(root_path, data, table):
    filter_data(data)
    directory = root_path+"/" + table
    os.makedirs(directory)
    file_list = []
    if not data:
        return file_list
    parent_child_ids = find_child_ids(data)

    for key, child_ids in parent_child_ids.items():
        data_map = {key: data.get(key)}
        file_path = directory+"/"+key+".json"
        for child_id in child_ids:
            data_map[child_id] = data.get(child_id)
        with open(file_path, 'w+b') as temp:
            temp.write(bytes(json.dumps(data_map, ensure_ascii=False), encoding='utf-8'))
        file_list.append(file_path)
    return file_list


def create_application_file(root_path, data, table):
    filter_data(data)
    directory = root_path+"/" + table
    os.makedirs(directory)
    file_list = []
    if not data:
        return file_list
    for key, value in data.items():
        data_map = {key: value}
        file_path = directory+"/"+key+".json"
        with open(file_path, 'w+b') as temp:
            temp.write(bytes(json.dumps(data_map, ensure_ascii=False), encoding='utf-8'))
        file_list.append(file_path)
    return file_list


def create_feed_file(root_path, data):
    filter_data(data)
    directory = root_path+"/feeds"
    os.makedirs(directory)
    file_list = []
    if not data:
        return file_list
    export_table = {
        'dashboard_email_subscribe': 'id', 'dashboard_subscribe_display_format': 'subscribe_id',
        'mobile_subscribe_filter': 'email_subscribe_id',
        'mobile_subscribe_rules': 'email_subscribe_id', 'mobile_subscribe_chapters': 'email_subscribe_id', 'flow': 'id'
    }
    feed_ids = set([])
    _map = {}
    for table, rows in data.items():
        _map[table] = {}
        id_filed = export_table.get(table)
        for row in rows:
            _id = row.get(id_filed)
            if not _map.get(table).get(_id):
                _map.get(table)[_id] = []
            _map.get(table).get(_id).append(row)
            feed_ids.add(_id)

    for _id in feed_ids:
        file_path = directory + "/" + _id + ".json"
        data_map = {}
        for table, id_filed in export_table.items():
            if _map.get(table) and _map.get(table).get(_id):
                data_map[table] = _map.get(table).get(_id)
            else:
                data_map[table] = []

        with open(file_path, 'w+b') as temp:
            temp.write(bytes(json.dumps(data_map, ensure_ascii=False), encoding='utf-8'))
        file_list.append(file_path)
    return file_list


def create_report_file(root_path, report_file, table, data):
    file_list = []
    if not report_file:
        return file_list
    directory = root_path + "/" + table
    os.makedirs(directory)
    zip_file = zipfile.ZipFile(report_file, 'r')
    for file_name in zip_file.namelist():
        file_info = zip_file.getinfo(file_name)
        if not file_info.is_dir():
            extracted_file_path = os.path.join(directory, file_name)
            directory_path = os.path.dirname(extracted_file_path)
            if not os.path.exists(directory_path):
                os.makedirs(directory_path)
            with open(extracted_file_path, 'wb') as extracted_file:
                extracted_file.write(zip_file.read(file_name))
            file_list.append(extracted_file_path)
    zip_file.close()

    if table in ['active_reports', 'report_center']:
        active_reports_json = data.get("active_reports")
        if active_reports_json:
            if table == "report_center":
                active_reports_json['ids'] = data.get("report_center").get("ids")
            extracted_file_path = os.path.join(directory, "base.json")
            with open(extracted_file_path, 'w+b') as temp:
                temp.write(bytes(json.dumps(active_reports_json, ensure_ascii=False), encoding='utf-8'))
            file_list.append(extracted_file_path)
    else:
        table_json = data.get(table)
        if table_json:
            extracted_file_path = os.path.join(directory, "base.json")
            with open(extracted_file_path, 'w+b') as temp:
                temp.write(bytes(json.dumps(table_json, ensure_ascii=False), encoding='utf-8'))
            file_list.append(extracted_file_path)
    return file_list


def find_child_ids(data):
    # 构建父 ID 到子 ID 的映射
    parent_to_child = {}
    for key, value in data.items():
        parent_id = value.get('dashboard')[0].get('parent_id') or ""
        if parent_id not in parent_to_child:
            parent_to_child[parent_id] = []
        parent_to_child[parent_id].append(key)

    # 广度优先搜索
    def bfs(root_id):
        queue = deque()
        queue.append(root_id)
        children = []
        visited = set()  # 记录已访问的节点

        while queue:
            current_id = queue.popleft()
            if current_id in parent_to_child:
                for child_id in parent_to_child[current_id]:
                    if child_id not in visited:  # 避免重复访问
                        children.append(child_id)
                        queue.append(child_id)
                        visited.add(child_id)

        return children

    # 找到所有根 ID 下的子 ID
    root_ids = [key for key, value in data.items() if value.get('dashboard')[0].get('parent_id') not in data]
    result = {root_id: bfs(root_id) for root_id in root_ids}

    return result


def filter_data(row, filter_field=[]):
    default_filter_field = ['created_on', 'created_by', 'modified_on', 'modified_by']
    if filter_field and len(filter_field) > 0:
        default_filter_field += filter_field
    deep_delete(row, default_filter_field)


def deep_delete(obj, attrs):
    if isinstance(obj, dict):
        for key in list(obj.keys()):
            if key in attrs:
                obj[key] = ''
            else:
                deep_delete(obj[key], attrs)
    elif isinstance(obj, list):
        for item in obj:
            deep_delete(item, attrs)
    elif isinstance(obj, tuple):
        for item in obj:
            deep_delete(item, attrs)
