#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/07/17
# pylint: disable=W1202,E1305

"""
报告分发导出
"""
# ---------------- 标准模块 ----------------
import json
import logging

from base import repository
from base.enums import DashboardTypeStatus, ApplicationType
from dashboard_chart.repositories import chart_repository, dashboard_repository
from exports.repositories.export_repository import update_export_msg


logger = logging.getLogger(__name__)
EXPORT_FOLDER_LEVEL_CODE = '9000'


def add_export_folder_level_code(orig_level_code, export_code=EXPORT_FOLDER_LEVEL_CODE):
    """
    添加导出文件夹level_code
    :param orig_level_code:
    :param export_code:
    :return:
    """
    return export_code + '-' + orig_level_code


def get_dashboard_table_data(dashboard_id):
    """
    获取dashboard表数据
    :param dashboard_id:
    :return:
    """
    dashboard_data = repository.get_data('dashboard', {'id': dashboard_id}, multi_row=True)
    return dashboard_data


def get_dashboard_chart_table_data(dashboard_id):
    """
    获取chart表数据
    :param dashboard_id:
    :return:
    """
    dashboard_chart_data = chart_repository.get_chart_list_by_dashboard_id(dashboard_id)
    return dashboard_chart_data


def pop_item(data, not_need_key=None):
    """
    去除不需要字段
    :param data:
    :param not_need_key:
    :return:
    """
    if not not_need_key:
        return data
    for item in data:
        for key in not_need_key:
            try:
                item.pop(key)
            except KeyError:
                pass
    return data


def get_chart_table_data_by_ids(chart_id_list, suffix=''):
    """
    获取数据表数据
    :param chart_id_list:
    :param suffix:
    :return:
    """
    chart_id_list_str = '("{}")'.format('","'.join(chart_id_list)) if isinstance(chart_id_list, list) else ''
    table_name = 'dashboard_chart_' + suffix
    data = chart_repository.get_chart_by_table(table_name, chart_id_list_str)
    data = pop_item(data, not_need_key=['created_on', 'modified_on'])
    return data


def get_component_table_data_by_ids(chart_id_list, suffix):
    """
    获取组件筛选表数据
    :param chart_id_list:
    :param suffix:
    :return:
    """
    data = list()
    if suffix == 'component_filter':
        data = chart_repository.get_component_filter_by_chart_ids(chart_id_list)
    elif suffix == 'component_filter_field_relation':
        data = chart_repository.get_component_filter_field_relation_by_chart_ids(chart_id_list)
    data = pop_item(data, not_need_key=['created_on', 'modified_on'])
    return data


def get_other_table_data_by_dashboard_id(table_name, conditions=None):
    """
    获取数据表数据
    :param table_name:
    :param conditions:
    :return:
    """
    if not table_name or not conditions:
        return None
    data = repository.get_data(table_name, conditions, multi_row=True)
    data = pop_item(data, not_need_key=['created_on', 'modified_on'])


    # 暂时不需要处理level_code
    # if table_name in ['dashboard_released_snapshot_chart', 'dashboard_released_snapshot_dashboard']:
    #     for item in data:
    #         level_code = item.get('level_code', '')
    #         item['level_code'] = add_export_folder_level_code(level_code) if level_code else level_code
    return data


def get_one_issue_data(dashboard_id, snapshot_id):
    """
    获取单个dashboard相关表数据
    :param dashboard_id:
    :param snapshot_id:
    :return:
    """
    one_issue_data = dict()
    if not dashboard_id:
        return one_issue_data
    chart_suffix_list = [
        'dim',
        'num',
        'filter',
        'filter_relation',
        'layers',
        'markline',
        'desire',
        'params',
        'params_jump',
        'comparison',
        'penetrate_relation',
        'field_sort',
        'visible_triggers',
    ]
    other_suffix_list = [
        'chart_selector',
        'chart_selector_field',
        'filter',
        'filter_vars_relation_field',
        'filter_relation',
        'dataset_field_relation',
        'released_snapshot_chart',
        'released_snapshot_dashboard',
        'screen_dashboard',
        'filter_chart',
        'filter_chart_fixed_value',
        'filter_chart_relation',
        'linkage',
        'linkage_relation',
        'filter_chart_default_values',
        'extra',
    ]

    # 跳转关系表
    jum_suffix_list = [
        'jump_config', 'jump_relation',
        'vars_jump_relation', 'fixed_var_jump_relation',
        'filter_chart_jump_relation',
        'global_params_jump_relation',
    ]
    other_suffix_list.extend(jum_suffix_list)

    # 全局参数
    other_suffix_list.extend([
        'jump_global_params',
        'global_params_2_dataset_field_relation',
        'global_params_2_dataset_vars_relation',
        'global_params_2_filter_chart_relation',
    ])

    # 变量关系
    other_suffix_list.extend(['dataset_vars_relation'])

    # 组件筛选表
    component_suffix_list = ['component_filter', 'component_filter_field_relation']

    # 变量及取值来源绑定关系
    other_suffix_list.extend(['value_source', 'vars_value_source_relation'])

    dashboard_chart_list = get_dashboard_chart_table_data(dashboard_id)
    chart_id_list = [n.get('id', '') for n in dashboard_chart_list]

    # dashboard and dashboard_chart表数据
    dashboard = get_dashboard_table_data(dashboard_id)
    one_issue_data['dashboard'] = dashboard
    one_issue_data['dashboard_chart'] = dashboard_chart_list
    # 报告所属父级文件夹
    level_code, current_level_code = '', ''
    if dashboard and len(dashboard) > 0:
        current_level_code = dashboard[0].get("level_code")
        level_code = dashboard[0].get("level_code")[0:5]
    dashboard_folders = dashboard_repository.get_dashboard_folders(level_code)
    one_issue_data['dashboard_folders'] = get_related_folders(dashboard_folders, current_level_code)

    # 根据单图id列表拿取chart相关表数据
    for suffix in chart_suffix_list:
        chart_table_name = 'dashboard_chart_{}'.format(suffix)
        if chart_table_name not in one_issue_data:
            one_issue_data.update({chart_table_name: []})
        one_issue_data[chart_table_name] = get_chart_table_data_by_ids(chart_id_list, suffix)

    # 根据看板id拿取表数据
    # third_party_config_ids = []
    for other_suffix in other_suffix_list:
        chart_table_name = 'dashboard_{}'.format(other_suffix)
        chart_table_name = other_suffix if other_suffix in ['screen_dashboard'] else chart_table_name
        if chart_table_name not in one_issue_data:
            one_issue_data.update({chart_table_name: []})
        conditions = {'dashboard_id': dashboard_id}
        if other_suffix in ['released_snapshot_dashboard']:
            conditions = {'id': dashboard_id, "snapshot_id": snapshot_id}
        elif other_suffix in ['released_snapshot_chart']:
            conditions = {'dashboard_id': dashboard_id, "snapshot_id": snapshot_id}
        one_issue_data[chart_table_name] = get_other_table_data_by_dashboard_id(
            table_name=chart_table_name, conditions=conditions
        )
        #删除第三方跳转配置信息
        # if other_suffix == 'jump_config':
        #     one_issue_data[chart_table_name] = delete_third_party_jump_config(
        #         one_issue_data[chart_table_name], third_party_config_ids)
        # if other_suffix == 'released_snapshot_chart':
        #     delete_released_third_party_jump_other(one_issue_data[chart_table_name])


    # 删除第三方跳转关联配置信息
    # delete_third_party_jump_other_config(one_issue_data, jum_suffix_list, third_party_config_ids)

    # 获取组件筛选表数据
    for component_suffix in component_suffix_list:
        component_table_name = 'dashboard_{}'.format(component_suffix)
        if component_table_name not in one_issue_data:
            one_issue_data.update({component_table_name: list()})
        one_issue_data[component_table_name] = get_component_table_data_by_ids(chart_id_list, component_suffix)

    return one_issue_data


def delete_third_party_jump_config(one_issue_data, config_ids):
    new_datas = []
    for data in one_issue_data:
        if data.get("target_type") and data.get("target_type") == "thirdParty":
            config_ids.append(data.get("id"))
        else:
            new_datas.append(data)
    return new_datas


def delete_third_party_jump_other_config(one_issue_data_all, jum_suffix_list, config_ids):
    if config_ids and len(config_ids) == 0:
        return
    for other_suffix in jum_suffix_list:
        chart_table_name = 'dashboard_{}'.format(other_suffix)
        one_issue_data = one_issue_data_all.get(chart_table_name)
        if one_issue_data:
            new_datas = []
            for data in one_issue_data:
                if not (data.get("jump_config_id") and data.get("jump_config_id") in config_ids):
                    new_datas.append(data)
            one_issue_data_all[chart_table_name] = new_datas

def delete_released_third_party_jump_other(one_issue_data):
    for data in one_issue_data:
        if data.get("jump"):
            try:
                jump_list = json.loads(data.get("jump"))
                jump_list_new = []
                for jump in jump_list:
                    if not (jump.get("target_type") and jump.get("target_type") == "thirdParty"):
                        jump_list_new.append(jump)
                data["jump"] = json.dumps(jump_list_new)
            except Exception as e:
                logger.error(f"数据处理失败，err：{str(e)}")
                pass


def get_related_folders(folders, level_code):
    """
    获取相关联的文件夹
    :param folders:
    :param level_code:
    :return:
    """
    levels = level_code.split("-")
    current_level = None
    new_folders = []
    for i, level in enumerate(levels):
        if i < len(levels) - 2:
            current_level = current_level + level + "-" if current_level else level + "-"
            for folder in folders:
                if current_level == folder.get("level_code"):
                    new_folders.append(folder)
                    break
    return new_folders


def get_dashboard_issue_data(export_id, dashboard_id_list):
    """
    获取报告分发导出数据
    :param export_id:
    :param dashboard_id_list: 面板id列表
    :return:
    """
    logger.info('执行报告分发导出,导出报告编号列表:{}'.format(','.join(dashboard_id_list)))
    dashboard_issue_data = dict()
    if not len(dashboard_id_list):
        return False, '报告编号选择不能为空', dashboard_issue_data

    dashboard_data = dashboard_repository.get_dashboards(dashboard_id_list)
    for dashboard in dashboard_data:
        dashboard_id = dashboard.get('id', '')
        status = dashboard.get('status', None)
        if not dashboard_id:
            msg = '存在编号为空的报告'
            update_export_msg(export_id, msg)
            return False, msg, {}

        # 第三方报表支持导出， 第三方报表没有 screen_dashboard 表记录
        if dashboard.get("application_type") in [
                ApplicationType.HD_Dashboard.value, ApplicationType.External_Dashboard.value,
                ApplicationType.FineReport.value
        ]:
            screen_list = [{
                "screen_id": dashboard_id
            }]
        else:
            screen_list = (
                repository.get_data(
                    table_name='screen_dashboard',
                    conditions={'dashboard_id': dashboard_id, 'type': DashboardTypeStatus.Release.value},
                    multi_row=True,
                )
                or []
            )

        for screen_data in screen_list:
            screen_id = screen_data.get('screen_id', '')
            if not screen_id:
                continue
            one_issue_data = get_one_issue_data(screen_id, dashboard_id)
            if one_issue_data and screen_id not in dashboard_issue_data.keys():
                dashboard_issue_data.update({screen_id: one_issue_data})

        # 报告未发布取预览表数据
        if status not in ["1", 1]:
            one_issue_data = get_one_issue_data_v2(dashboard_id)
            if one_issue_data and dashboard_id not in dashboard_issue_data.keys():
                dashboard_issue_data.update({dashboard_id: one_issue_data})

    return True, '', dashboard_issue_data


def get_one_issue_data_v2(dashboard_id, include_folder=True):
    """
    获取单个dashboard相关表数据
    :param dashboard_id:
    :param include_folder: 是否获取报告的所属文件夹信息，默认为True
    :return:
    """
    one_issue_data = dict()
    if not dashboard_id:
        return one_issue_data
    chart_suffix_list = [
        'dim',
        'num',
        'filter',
        'filter_relation',
        'layers',
        'markline',
        'desire',
        'params',
        'params_jump',
        'comparison',
        'penetrate_relation',
        'field_sort',
        'visible_triggers',
    ]
    other_suffix_list = [
        'chart_selector',
        'chart_selector_field',
        'filter',
        'filter_relation',
        'dataset_field_relation',
        'chart',
        'screen_dashboard',
        'filter_chart',
        'filter_chart_fixed_value',
        'filter_chart_relation',
        'linkage',
        'linkage_relation',
        'filter_chart_default_values',
        'extra',
    ]

    # 跳转关系表
    other_suffix_list.extend([
        'jump_config', 'jump_relation', 'vars_jump_relation', 'fixed_var_jump_relation',
        'filter_chart_jump_relation', 'global_params_jump_relation',

    ])

    # 全局参数
    other_suffix_list.extend([
        'jump_global_params',
        'global_params_2_dataset_field_relation',
        'global_params_2_dataset_vars_relation',
        'global_params_2_filter_chart_relation',
    ])

    # 变量关系
    other_suffix_list.extend(['dataset_vars_relation'])

    # 组件筛选表
    component_suffix_list = ['component_filter', 'component_filter_field_relation']

    # 变量及取值来源绑定关系
    other_suffix_list.extend(['value_source', 'vars_value_source_relation'])

    dashboard_chart_list = get_dashboard_chart_table_data(dashboard_id)
    chart_id_list = [n.get('id', '') for n in dashboard_chart_list]

    # dashboard and dashboard_chart表数据
    dashboard = get_dashboard_table_data(dashboard_id)
    one_issue_data['dashboard'] = dashboard
    one_issue_data['dashboard_chart'] = dashboard_chart_list
    # 报告所属父级文件夹
    if include_folder:
        level_code, current_level_code = '', ''
        if dashboard and len(dashboard) > 0:
            current_level_code = dashboard[0].get("level_code")
            level_code = dashboard[0].get("level_code")[0:5]
        dashboard_folders = dashboard_repository.get_dashboard_folders(level_code)
        one_issue_data['dashboard_folders'] = get_related_folders(dashboard_folders, current_level_code)

    # 根据单图id列表拿取chart相关表数据
    for suffix in chart_suffix_list:
        chart_table_name = 'dashboard_chart_{}'.format(suffix)
        if chart_table_name not in one_issue_data:
            one_issue_data.update({chart_table_name: []})
        one_issue_data[chart_table_name] = get_chart_table_data_by_ids(chart_id_list, suffix)

    # 根据看板id拿取表数据
    for other_suffix in other_suffix_list:
        chart_table_name = 'dashboard_{}'.format(other_suffix)
        chart_table_name = other_suffix if other_suffix in ['screen_dashboard'] else chart_table_name
        if chart_table_name not in one_issue_data:
            one_issue_data.update({chart_table_name: []})
        conditions = {'dashboard_id': dashboard_id}
        if other_suffix in ['released_snapshot_dashboard']:
            conditions = {'id': dashboard_id}
        elif other_suffix in ['released_snapshot_chart']:
            conditions = {'dashboard_id': dashboard_id}
        one_issue_data[chart_table_name] = get_other_table_data_by_dashboard_id(
            table_name=chart_table_name, conditions=conditions
        )

    # 获取组件筛选表数据
    for component_suffix in component_suffix_list:
        component_table_name = 'dashboard_{}'.format(component_suffix)
        if component_table_name not in one_issue_data:
            one_issue_data.update({component_table_name: list()})
        one_issue_data[component_table_name] = get_component_table_data_by_ids(chart_id_list, component_suffix)

    return one_issue_data
