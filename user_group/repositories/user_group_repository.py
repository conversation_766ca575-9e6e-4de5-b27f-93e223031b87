#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
import hashlib

from base.dmp_constant import ERP_USER_GROUP, ERP_USER, DEFAULT_USER_GROUP
from base.enums import UserChannel
from dmplib.constants import DATA_CLEAN_ORGANIZATION_CODE_TABLE_NAME
from dmplib.saas.project import get_db
import logging

from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id


def get_user_group(group_id):
    """
    获取用户组
    :param group_id:
    :return:
    """
    sql = 'SELECT `id`,`name`,`parent_id`,`code` FROM user_group WHERE id=%(id)s LIMIT 1'
    with get_db() as db:
        return db.query_one(sql, {'id': group_id})


def check_group_exists(groups):
    """
    检查用户组是否都存在
    :param groups:
    :return:
    """
    sql = """
    select count(1) as c from user_group where id in ('%s')
    """ % "', '".join(
        groups
    )
    ret = {'c': 0}
    with get_db() as db:
        ret = db.query_one(sql, None)
        logging.info(ret["c"])
    return len(groups) == ret['c'] and len(groups) != 0


def get_user_group_code(group_id):
    """
    获取用户组编码
    :param group_id:
    :return:
    """
    sql = 'SELECT `code` FROM user_group WHERE id=%(id)s LIMIT 1'
    with get_db() as db:
        return db.query_scalar(sql, {'id': group_id})


def get_user_group_organ_codes(group_ids):
    """
    获取用户组下设置的机构code
    :param group_ids:
    :return:
    """
    sql = (
        'SELECT b.`code` FROM user_group_organ AS a '
        'INNER JOIN {to} AS b ON a.org_id=b.id '
        'WHERE a.group_id in %(group_ids)s '
        'ORDER BY b.`code` '.format(to=DATA_CLEAN_ORGANIZATION_CODE_TABLE_NAME)
    )
    with get_db() as db:
        return db.query_columns(sql, {'group_ids': group_ids})


def get_user_group_func(group_ids):
    """
    获取用户组下已设置的功能模块
    :param list group_ids:
    :return:
    """
    sql = (
        'SELECT  gf.`func_id`,f.application_id,f.`level_code` FROM user_group_func AS gf '
        'INNER JOIN `function` AS f on gf.func_id=f.id '
        'WHERE gf.group_id in %(group_ids)s '
    )
    with get_db() as db:
        return db.query(sql, {'group_ids': group_ids})


def get_user_group_func_codes(group_id):
    """
    获取用户组下设置的功能模块code
    :param str group_id:
    :return:
    """
    sql = (
        'SELECT  CONCAT(f.application_id,\'+\',f.`level_code`) as `code` FROM user_group_func AS gf '
        'INNER JOIN `function` AS f on gf.func_id=f.id '
        'WHERE gf.group_id=%(group_id)s '
        'ORDER BY f.`level_code`'
    )
    with get_db() as db:
        return db.query_columns(sql, {'group_id': group_id})


def get_user_group_list_by_group_code(group_code):
    """
    根据用户组编码获取所有用户组
    :param str group_code:
    :return:
    """
    sql = (
        'SELECT id,`name`,parent_id,`code`,account_mode, user_source_id FROM user_group '
        'WHERE `code` LIKE %(group_code)s ORDER BY `code`'
    )
    with get_db() as db:
        return db.query(sql, {'group_code': group_code + '%'})


def get_user_group_list():
    sql = 'SELECT id,`name`,parent_id,`code`, account_mode, user_source_id FROM user_group ORDER BY `code`'
    with get_db() as db:
        return db.query(sql)


def get_erp_user_group_list():
    sql = 'SELECT `id`,`name`,parent_id,`code`,account_mode FROM {table_name} ORDER BY `code`'.format(
        table_name=ERP_USER_GROUP
    )
    with get_db() as db:
        return db.query(sql)


def get_external_user_by_group_id(query_model):
    """
    :param user.models.ExternalUserQueryModel query_model:
    :return:
    """
    sql = "SELECT DISTINCT id,name,account,pwd,mobile,email,account_mode FROM {table_name} ".format(table_name=ERP_USER)

    conditions = []
    params = {}

    if query_model.user_group_id:
        if isinstance(query_model.user_group_id, str):
            conditions.append(" group_id in (%(user_group_id)s) ")
            params['user_group_id'] = query_model.user_group_id
        elif isinstance(query_model.user_group_id, list):
            conditions.append(" group_id in (%(user_group_id)s) ")
            params['user_group_id'] = ",".join(query_model.user_group_id)
        else:
            raise UserError(message="user_group_id字段类型只支持字符串和数组类型")

    if query_model.keyword:
        conditions.append(' ( `name` LIKE %(keyword)s or `account` LIKE %(keyword)s) ')
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sql_total = "SELECT count(DISTINCT id) FROM {table_name}  ".format(table_name=ERP_USER)

    if len(conditions) > 0:
        sql += ('WHERE ' + ' AND '.join(conditions)) if conditions else ''
        sql_total += ('WHERE ' + ' AND '.join(conditions)) if conditions else ''
    else:
        params = None

    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
    with get_db() as db:
        query_model.items = db.query(sql, params)
        query_model.total = db.query_scalar(sql_total, params)
    return query_model


def get_user_role(user_group_id):
    sql = (
        " select user_group_role.group_id,user_role.* from user_role "
        "inner join user_group_role on user_group_role.role_id = user_role.id "
        "WHERE user_group_role.group_id = '{user_group_id}' ".format(user_group_id=user_group_id)
    )
    with get_db() as db:
        return db.query(sql)


def get_user_by_role_id(role_id):
    sql = (
        " select user_user_role.role_id,user.* from user "
        "inner join user_user_role on user_user_role.user_id = user.id "
        "WHERE user_user_role.role_id = '{role_id}' ".format(role_id=role_id)
    )
    with get_db() as db:
        return db.query(sql)


def count_user(group_code, include_sub=None):
    """
    用户下是否存在用户
    :param str group_code:
    :param bool include_sub:
    :return:
    """
    sql = 'SELECT g.`name`, count(1) as count FROM `user` AS u ' 'INNER JOIN user_group AS g ' 'ON u.group_id=g.id '
    params = {}
    if include_sub:
        sql += 'WHERE g.`code` LIKE %(code)s '
        params['code'] = group_code + '%'
    else:
        sql += 'WHERE g.`code` = %(code)s '
        params['code'] = group_code
    sql += 'GROUP BY g.`name` HAVING COUNT(1)>0'
    with get_db() as db:
        return db.query(sql, params)


def delete_user_group_func(group_code, include_sub=None):
    """
    删除用户组设置的功能菜单
    :param str group_code:
    :param  bool include_sub:
    :return:
    """
    sql = (
        'DELETE FROM user_group_func '
        'WHERE group_id IN ( '
        'SELECT id FROM user_group '
        'WHERE `code` {o} %(code)s '
        ')'.format(o='LIKE' if include_sub else '=')
    )
    with get_db() as db:
        return db.exec_sql(sql, {'code': group_code + ('%' if include_sub else '')})


def delete_user_group_organ(group_code, include_sub=None):
    """
    删除用户组设置的组织机构
    :param str group_code:
    :param  bool include_sub:
    :return:
    """
    sql = (
        'DELETE FROM user_group_organ '
        'WHERE group_id IN ( '
        'SELECT id FROM user_group '
        'WHERE `code` {o} %(code)s '
        ')'.format(o='LIKE' if include_sub else '=')
    )
    with get_db() as db:
        return db.exec_sql(sql, {'code': group_code + ('%' if include_sub else '')})


def delete_user_group(group_code, include_sub=None):
    """
    删除用户组
    :param str group_code:
    :param  bool include_sub:
    :return:
    """
    sql = 'DELETE FROM user_group ' 'WHERE `code` {o} %(code)s '.format(o='LIKE' if include_sub else '=')
    with get_db() as db:
        return db.exec_sql(sql, {'code': group_code + ('%' if include_sub else '')})


def delete_user_group_relation(group_id):
    return get_db().delete('user_group_user', {'group_id': group_id})


def cleaning_user_group_organ(group_id):
    """
    清理用户组子级授权机构（子级授权机构必须在父级授权机构范围内）
    :param str group_id:
    :return:
    """
    sql = """
    DELETE a from user_group_organ as a
    INNER JOIN dim_organization_code as b on a.org_id =b.id
    INNER JOIN (
    select id from user_group
    where `code` LIKE CONCAT((select `code` from user_group where id =%(group_id)s),'%%')
    and id <> %(group_id)s) c on a.group_id=c.id
    INNER JOIN (select CONCAT(',',IFNULL(GROUP_CONCAT(b.`code`),'')) codes from user_group_organ as a
    INNER JOIN dim_organization_code as b on a.org_id =b.id
    where a.group_id=%(group_id)s
    ) d on LOCATE(CONCAT(',',b.`code`),d.codes)=0
    """
    with get_db() as db:
        db.exec_sql('SET SESSION group_concat_max_len=102400;')
        return db.exec_sql(sql, {'group_id': group_id})


def cleaning_user_group_func(group_id):
    """
    清理用户组子级授权功能（子级授权功能必须在父级授权功能范围内）
    :param str group_id:
    :return:
    """
    sql = """
    DELETE a from user_group_func as a
    INNER JOIN `function` as b on a.func_id =b.id
    INNER JOIN (
    select id from user_group
    where `code` LIKE CONCAT((select `code` from user_group where id =%(group_id)s),'%%')
    and id <> %(group_id)s) c on a.group_id=c.id
    INNER JOIN (
    select CONCAT(',', IFNULL(GROUP_CONCAT(cd.`code`),'')) as codes from (
        select CONCAT(b.application_id,'+',b.`level_code`) as `code`
        from user_group_func as a
        INNER JOIN `function` as b on a.func_id =b.id
        where a.group_id= %(group_id)s
        UNION
        select CONCAT(b.id,'+',c.`level_code`) as `code` from user_group_app  as a
        INNER JOIN application as b on a.app_id =b.id
        INNER JOIN `function` as c on b.id=c.application_id
        where a.group_id= %(group_id)s
    ) cd
    ) d on LOCATE(CONCAT(',',b.application_id,'+',b.`level_code`),d.codes)=0
    """
    with get_db() as db:
        db.exec_sql('SET SESSION group_concat_max_len=102400;')
        return db.exec_sql(sql, {'group_id': group_id})


def cleaning_user_group_app(group_id):
    """
    清理用户组自己授权应用
    :param group_id:
    :return:
    """
    sql = """
    DELETE a FROM user_group_app as a
    INNER JOIN (
        SELECT id FROM user_group
        WHERE `code` LIKE CONCAT((SELECT `code` FROM user_group WHERE id =%(group_id)s),'%%')
        AND id <> %(group_id)s
    )
    AS b
    ON a.group_id=b.id
    INNER JOIN (
        SELECT IFNULL(CONCAT(',', GROUP_CONCAT(app_id)),'') AS app_ids FROM user_group_app
        WHERE group_id=%(group_id)s
    ) AS c
    ON LOCATE(CONCAT(',',a.app_id),c.app_ids)=0
    """
    with get_db() as db:
        db.exec_sql('SET SESSION group_concat_max_len=102400;')
        return db.exec_sql(sql, {'group_id': group_id})


def update_user_group(user_id, group_ids):
    """
    设置用户的用户组
    :param user_id:
    :param group_ids:
    :return:
    """
    if not user_id:
        return False

    group_ids = [] if not group_ids else list(set(group_ids))
    db = get_db()
    db.delete('user_group_user', {'user_id': user_id}, commit=False)
    for group_id in group_ids:
        db.insert('user_group_user', {'user_id': user_id, 'group_id': group_id, 'id': seq_id()}, commit=False)
    db.commit()
    return True


def add_multi_external_user_groups(user_source, user_groups):
    """
    批量添加外部用户组
    :param user_groups:
    :return:
    """
    affect_row = 0
    is_erp_user_source = True if user_source.get('type') == UserChannel.Erp.value else False
    if is_erp_user_source:
        db = get_db()
        for user_group in user_groups:
            query_sql = "select `id` from {table_name} where `id` = %(id)s ".format(table_name=ERP_USER_GROUP)
            user_group_id = db.query_scalar(query_sql, {"id": user_group.get("id")})
            if user_group_id:
                user_group.pop("id")
                affect_row += db.update(ERP_USER_GROUP, user_group, condition={"id": user_group_id}, commit=False)
            else:
                affect_row += db.insert(ERP_USER_GROUP, user_group, commit=False)
        db.commit()
    else:
        table_name = 'user_source_group'
        db = get_db()
        query_exist_user_group = 'select id,user_source_id,user_source_group_id FROM user_source_group where user_source_id=%(user_source_id)s'
        exist_user_group_list = db.query(query_exist_user_group, {'user_source_id': user_source.get('id')})
        exist_user_group_dict = {}
        for exist_user_group in exist_user_group_list:
            exist_user_group_dict[exist_user_group.get('user_source_group_id')] = exist_user_group.get('id')
        for user_group in user_groups:
            user_group['user_source_group_id'] = user_group['id']
            user_group['id'] = seq_id()
            user_group['user_source_id'] = user_source.get('id')
            id = exist_user_group_dict.get(user_group.get('user_source_group_id'))
            if id:
                user_group.pop("id")
                affect_row += db.update(table_name, user_group, condition={"id": id}, commit=False)
            else:
                affect_row += db.insert(table_name, user_group, commit=False)
        db.commit()
    return affect_row


def update_user_organization():
    """
    更新用户组织关系表
    :return:
    """
    sql = """
    replace into user_organization(user_id, group_id, org_name, org_level)
    select external_user.id as user_id , external_user_group.id as group_id , external_user_group.name as org_name,
    external_user_group.hierarchy as org_level
    from external_user INNER JOIN external_user_group on external_user_group.id = external_user.group_id
    """
    with get_db() as db:
        db.exec_sql(sql)


def tmp_table_rename(table_name, tmp_table_name):
    """
    临时表重命名
    :return:
    """
    sql = 'drop table if exists `%s`' % (table_name,)
    with get_db() as db:
        db.exec_sql(sql)
    sql = 'rename table `%s` to `%s` ' % (tmp_table_name, table_name)
    with get_db() as db:
        db.exec_sql(sql)


def organ_code(user_source):
    """
    机构编码
    :return:
    """
    is_erp_user_source = True if user_source.get('type') == UserChannel.Erp.value else False
    tmp_table_name = 'external_user_group' if is_erp_user_source else 'user_source_group'
    new_tmp_table_name = 'tmp_' + 'usergroup' + '_' + hashlib.md5(seq_id().encode('utf-8')).hexdigest()[8:-8]
    sql = 'drop table if exists `%s`' % (new_tmp_table_name,)
    with get_db() as db:
        db.exec_sql(sql)
    sql = 'CREATE TABLE `%s` LIKE `%s`  ' % (new_tmp_table_name, tmp_table_name)
    with get_db() as db:
        db.exec_sql(sql)
    sql = 'INSERT INTO `%s` SELECT * FROM `%s` WHERE 1=2 ' % (new_tmp_table_name, tmp_table_name)
    with get_db() as db:
        db.exec_sql(sql)
    sql = ' ALTER TABLE  `%s`  MODIFY COLUMN `%s` VARCHAR (255) ' % (new_tmp_table_name, "code")
    with get_db() as db:
        db.exec_sql(sql)

    fields = ['id', 'parent_id', 'name', 'hierarchy', 'account_mode', 'code']
    if not is_erp_user_source:
        fields.append('user_source_id')
        fields.append('user_source_group_id')
    sql_where = ' 1=1 ' if is_erp_user_source else "  user_source_id='{}' ".format(user_source.get('id'))
    sql = 'SELECT {fields} FROM {tmp_table_name} where {sql_where}  order by parent_id'.format(
        fields=','.join(fields),
        tmp_table_name=tmp_table_name,
        sql_where=sql_where
    )

    with get_db() as db:
        organ_list = db.query(sql)
    if organ_list:
        i = 1
        for organ in organ_list:
            if not organ.get("parent_id"):
                code = '0000{num}-'.format(num=str(i))
                organ['code'] = code
                pareint_id = organ.get('id') if is_erp_user_source else organ.get('user_source_group_id')
                _generate_organ_code(organ_list, pareint_id, code, 1)
                i += 1
        sql = 'INSERT INTO {t} ( {fields} ) VALUES '.format(
            t=new_tmp_table_name,
            fields=','.join(fields)
        )
        data = []
        for organ in organ_list:
            values = []
            for field in fields:
                if 'parent_id' == field:
                    values.append("'{}'".format(organ.get('parent_id') or DEFAULT_USER_GROUP))
                elif 'id' == field:
                    values.append("'{}'".format(organ.get('id').replace("\"", "")))
                else:
                    values.append("'{}'".format(organ.get(field)))
            data.append("({})".format(",".join(values)))
        sql += ','.join(data)

        with get_db() as db:
            db.exec_sql(sql)
    return new_tmp_table_name


def _generate_organ_code(organ_list, parent_id, parent_code, num):
    """
    生成机构Code
    :param organ_list:
    :param parent_id:
    :param parent_code:
    :param num:
    :return:
    """
    for organ in organ_list:
        if organ.get('parent_id') != parent_id:
            continue
        # pylint: disable=W0621
        organ_code = parent_code + str(num).zfill(5) + '-'
        num += 1
        organ['code'] = organ_code
        organ_id = organ.get('user_source_group_id') if organ.get('user_source_group_id') else organ.get('id')
        _generate_organ_code(organ_list, organ_id, organ_code, 1)


def get_role_ids_by_group_ids(group_ids: list):
    sql = 'SELECT role_id FROM user_group_role WHERE group_id in %(group_ids)s'
    params = {'group_ids': group_ids}
    with get_db() as db:
        return db.query_columns(sql, params)


def delete_user_user_source(user_source_id):
    # 删除用户渠道
    affect_row = 0
    db = get_db()
    affect_row += db.delete('user_source', {'id': user_source_id}, commit=False)  # 删除用户渠道
    affect_row += db.delete('user_source_user', {'user_source_id': user_source_id}, commit=False)  # 删除渠道用户
    affect_row += db.delete('user_source_group', {'user_source_id': user_source_id}, commit=False)  # 删除渠道用户组

    # 删除相关用户
    sql_delete_user = '''delete from `user` where user_source_id =%(user_source_id)s'''
    sql_delete_user_role = '''delete from user_user_role where user_id in (select id from `user` where user_source_id =%(user_source_id)s)'''
    sql_delete_group_user = '''delete from user_group_user where user_id in (select id from `user` where user_source_id =%(user_source_id)s)'''
    sql_delete_user_org = '''delete from user_organization where user_id in (select id from `user` where user_source_id =%(user_source_id)s)'''
    affect_row += db.exec_sql(sql_delete_user_role, {"user_source_id": user_source_id}, commit=False)
    affect_row += db.exec_sql(sql_delete_group_user, {"user_source_id": user_source_id}, commit=False)
    affect_row += db.exec_sql(sql_delete_user_org, {"user_source_id": user_source_id}, commit=False)
    affect_row += db.exec_sql(sql_delete_user, {"user_source_id": user_source_id}, commit=False)
    delete_user_source_user_group(db, user_source_id)
    # 删除api渠道拉取信息
    db.delete('third_party_seq', {'user_source_id': user_source_id}, commit=False)
    db.commit()
    return affect_row

def delete_user_source_user_group(db, group_id):
    code = get_user_group_code(group_id)
    affect_row = 0
    if not code:
        return affect_row
    sqls = [
        'DELETE FROM user_group_func WHERE group_id IN ( SELECT id FROM user_group WHERE `code` like %(code)s )',
        'DELETE FROM user_group_organ WHERE group_id IN ( SELECT id FROM user_group WHERE `code` like %(code)s )',
        'DELETE FROM user_group_user WHERE group_id IN ( SELECT id FROM user_group WHERE `code` like %(code)s ) ',
        'DELETE FROM user_group_role WHERE group_id IN ( SELECT id FROM user_group WHERE `code` like %(code)s ) ',
        'DELETE FROM user_group_dashboard WHERE group_id IN ( SELECT id FROM user_group WHERE `code` like %(code)s ) ',
        'DELETE FROM user_group_app WHERE group_id IN ( SELECT id FROM user_group WHERE `code` like %(code)s ) ',
        'DELETE FROM user_group WHERE `code` like %(code)s',
    ]
    for sql in sqls:
        affect_row += db.exec_sql(sql, {'code': code+'%'}, commit=False)
    return affect_row

def get_user_source_group_list(user_source_id):
    sql = 'SELECT `id`,`user_source_group_id`,`user_source_id`,`name`,parent_id,`code`,account_mode FROM {table_name} ' \
          'where user_source_id=%(user_source_id)s  ORDER BY `code`'.format(table_name='user_source_group')
    with get_db() as db:
        return db.query(sql, {"user_source_id": user_source_id})


def get_user_soruce_by_id(user_source_id):
    sql = 'select `id`, `name`, `data`, `remark`, `type` from user_source where id =%(user_source_id)s'
    with get_db() as db:
        return db.query_one(sql, {"user_source_id": user_source_id})


def sync_user_source_group(table_name, tmp_table_name, fields, delete_conditions: dict):
    """
    临时表数据同步到正式
    :return:
    """
    sql_where = ' 1=1 '
    if delete_conditions:
        sql_where += 'and ' + ' and '.join(
            [" {} = '{}' ".format(key, delete_conditions[key]) for key in delete_conditions.keys()])

    sql_delete = 'delete from {} where {}'.format(table_name, sql_where)
    sql_insert = 'insert into {} ({}) select {} from {}'.format(table_name, ','.join(fields), ','.join(fields),
                                                                tmp_table_name)
    sql_drop_table = 'drop table if exists `{}`'.format(tmp_table_name)
    with get_db() as db:
        db.exec_sql(sql_delete, commit=False)
        db.exec_sql(sql_insert, commit=False)
        db.exec_sql(sql_drop_table, commit=False)
        db.commit()


def get_group_code_by_user_id(user_id):
    sql = "select code from user_group where id in (select group_id  from user_group_user l where user_id = %(user_id)s) order by code asc"
    with get_db() as db:
        return db.query(sql, {"user_id": user_id})


def get_group_name_by_code(code_list):
    sql = "select code, name from user_group where code in %(code_list)s"
    with get_db() as db:
        return db.query(sql, {"code_list": code_list})