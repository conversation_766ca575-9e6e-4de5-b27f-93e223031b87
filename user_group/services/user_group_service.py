#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
from app_menu.services import function_service
from base import repository, service
from base.dmp_constant import ERP_USER_GROUP, ERP_USER, SELF_SERVICE_VIRTUAL_USER_ID
from base.enums import AccountMode, AddMode, UserChannel, ApplicationTypeAccessReleasedSourceStr, ObjectAuthMode
from dmplib import config
from dmplib.constants import ADMINISTRATORS_GROUP_ID, ADMINISTRATORS_GROUP_CODE
from dmplib.hug import g
from dmplib.saas import project
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from flow.services import flow_service
from flow.services.user_sync_flow_service import sync_flow_id, import_flow_id
from level_sequence.models import UserGroupLevelSequenceModel
from level_sequence.services.level_sequence_service import generate_level_code
from rbac.repositories import func_auth_repository, func_repository, role_repository
from rbac.services import func_service, func_auth_service
from user.repositories import user_repository
from user_group.repositories import user_group_repository, organ_repository
from user_group.repositories.user_group_repository import get_group_code_by_user_id, get_group_name_by_code
from user_group.services import organ_service
import datetime
from dmplib.hug import debugger
from components.mip_auth_application import ApplicationsAvailable
from base.enums import ThirdFuncFilterType
from app_menu.models import ApplicationModel

_debugger = debugger.Debug(__name__)

# 出厂内置的DMP应用id
from typing import Any, Dict, List, Optional, Union

buildin_application_id = '39e0ff72-ec9f-49ac-605e-f7e70201cd5a'


def get_user_group(group_id):
    if not group_id:
        raise UserError(message='缺少用户组id')
    fields = ['id', 'name', 'parent_id', 'code', 'account_mode']
    user_group = repository.get_data('user_group', {'id': group_id}, fields)
    if not user_group:
        raise UserError(message='用户组不存在')
    return user_group


def add_user_group(model):
    """
    添加用户组
    :param user_group.models.UserGroupModel model:
    :return:
    """
    model.id = seq_id()
    model.code = generate_group_code(model.parent_id)
    model.validate()
    fields = ['id', 'name', 'parent_id', 'code']
    return repository.add_model('user_group', model, fields)


def update_user_group(model):
    """
    修改用户组
    :param user_group.models.UserGroupModel model:
    :return:
    """
    model.validate()
    validate_admin_group(model.id)
    return repository.update_data('user_group', {'name': model.name}, {'id': model.id})


def delete_user_group(group_id):
    """
    删除用户组
    :param group_id:
    :return:
    """
    validate_admin_group(group_id)
    group_code = validate_group_auth(group_id, True)
    affect_row = 0
    affect_row += user_group_repository.delete_user_group_func(group_code, True)
    affect_row += user_group_repository.delete_user_group_organ(group_code, True)
    affect_row += user_group_repository.delete_user_group(group_code, True)
    affect_row += user_group_repository.delete_user_group_relation(group_id)

    return affect_row


def get_user_by_id(user_id):
    return repository.get_data("user", {"id": user_id}, ["name"])


def get_group_by_id(group_id):
    return repository.get_data("user_group", {"id": group_id}, ["name"])


def get_user_group_list(group_id=None):
    """
    获取用户组列表
    :param group_id:
    :return:
    """
    if not group_id:
        return user_group_repository.get_user_group_list()
    else:
        group_code = validate_group_auth(group_id)
        return user_group_repository.get_user_group_list_by_group_code(group_code)


def user_group_is_exists(group_id):
    """
    用户组是否存在
    :param group_id:
    :return:
    """
    exist = repository.data_is_exists('user_group_user', {'group_id': group_id})
    if not exist:
        exist = repository.data_is_exists('user_group', {'id': group_id})
    return exist


def generate_group_code(parent_group_id):
    """
    根据父级组生成层级编码
    :param parent_group_id:
    :return:
    """
    group = repository.get_data('user_group', {'id': parent_group_id}, ['code'])
    if not group:
        raise UserError(message='父级用户组不存在')
    return generate_level_code(UserGroupLevelSequenceModel(level_id=parent_group_id))


def check_group_name_exists(parent_group_id, name):
    """
    校验组名是否重复
    :param parent_group_id:
    :param name:
    :return:
    """
    name = repository.get_data('user_group', {'parent_id': parent_group_id, 'name': name}, ['name'])
    if name:
        raise UserError(message='名称重复')


def validate_admin_group(group_id):
    """
    校验用户组
    :param group_id:
    :return:
    """
    if not group_id or not user_group_is_exists(group_id):
        raise UserError(message='用户组不存在')
    else:
        return True


# pylint: disable=W0613
def validate_group_auth(group_id, only_sub=None):
    """
    验证用户组权限
    :param str group_id:
    :param bool only_sub:
    :return:
    """
    if group_id is None or group_id == '':
        group_code = ADMINISTRATORS_GROUP_CODE
    else:
        group_code = user_group_repository.get_user_group_code(group_id)
    if not group_code:
        raise UserError(message='用户组编码缺失')

    return group_code


def set_user_group_organ(model):
    """
    设置用户组可访问组织
    :param user_group.models.UserGroupOrganModel model:
    :return:
    """
    validate_admin_group(model.group_id)
    repository.delete_data('user_group_organ', {'group_id': model.group_id})
    list_data = [{'group_id': model.group_id, 'org_id': organ_id} for organ_id in model.organ_list]
    repository.add_list_data('user_group_organ', list_data, ['group_id', 'org_id'])
    user_group_repository.cleaning_user_group_organ(model.group_id)
    return True


def get_user_group_organ_tree(group_ids):
    """
    获取用户组机构树
    :param group_ids:
    :return:
    """
    if not group_ids or len(group_ids) < 1:
        raise UserError(message='缺少用户组id')

    is_admin = False
    for group_id in group_ids:
        if group_id.lower() == ADMINISTRATORS_GROUP_ID.lower():
            is_admin = True

    if is_admin:
        organ_data = organ_service.get_all_organ()
    else:
        organ_codes = user_group_repository.get_user_group_organ_codes(group_ids)
        organ_data = organ_service.get_full_organ_by_codes(organ_codes)
    return organ_service.get_organ_tree(organ_data)


def get_user_group_organ(group_id, parent_id=''):
    """
    获取用户组机构
    :param group_id:
    :param parent_id:
    :return:
    """

    if not group_id:
        raise UserError(message='缺少用户组id')

    organ_data = organ_repository.get_organ_by_pid(parent_id)

    return organ_data


def get_user_group_organ_editable_tree(group_id):
    """
    获取用户组下功能菜单树（编辑使用）
    :param group_id:
    :return:
    """
    if group_id.lower() == ADMINISTRATORS_GROUP_ID.lower():
        return None
    user_group = get_user_group(group_id)
    parent_id = user_group.get('parent_id')
    if not parent_id or parent_id.lower() == ADMINISTRATORS_GROUP_ID.lower():
        organ_list = organ_service.get_all_organ()
    else:
        organ_codes = user_group_repository.get_user_group_organ_codes([parent_id])
        organ_list = organ_service.get_full_organ_by_codes(organ_codes)
    if not organ_list:
        return None
    cur_organ_codes = user_group_repository.get_user_group_organ_codes([group_id])
    if cur_organ_codes:
        for organ in organ_list:
            code = organ.get('code')
            if not code:
                continue
            for cur_code in cur_organ_codes:
                if code != cur_code:
                    continue
                organ['selected'] = True
                break
    return organ_service.get_organ_tree(organ_list)


def set_user_group_func(model):
    """
    设置用户可访问功能菜单
    :param user_group.models.UserGroupFuncModel model:
    :return:
    """
    validate_admin_group(model.group_id)
    repository.delete_data('user_group_app', {'group_id': model.group_id})
    if model.app_list:
        list_data = [{'group_id': model.group_id, 'app_id': app_id} for app_id in model.app_list]
        repository.add_list_data('user_group_app', list_data, ['group_id', 'app_id'])
    user_group_repository.cleaning_user_group_app(model.group_id)

    repository.delete_data('user_group_func', {'group_id': model.group_id})
    if model.func_list:
        list_data = [{'group_id': model.group_id, 'func_id': func_id} for func_id in model.func_list]
        repository.add_list_data('user_group_func', list_data, ['group_id', 'func_id'])
    user_group_repository.cleaning_user_group_func(model.group_id)
    return True


def get_user_group_func_tree(group_ids, filter_app_enable=None, project_type=None):
    """
    获取用户功能菜单树
    :param list group_ids:
    :param bool filter_app_enable:
     :param str project_type:
    :return:
    """
    if not group_ids:
        raise UserError(message='缺少用户组id')
    if not project_type:
        project_data = user_repository.get_user_project_profile()
        project_type = project_data.get('type')
    return generate_func_tree(group_ids, filter_app_enable, project_type=project_type)


def get_user_func_tree(
    user_context: Dict[str, Union[str, List[str]]],
    funcs_set: Dict[str, List[str]],
    filter_app_enable: Optional[bool] = None,
    project_type: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """
    :param user_context: {'user_id': '', 'group_id': ''}
    :param funcs_set: {'user': ['edit', 'view']}
    :param filter_app_enable:
    :param project_type:
    :return:
    """
    if not project_type:
        project_data = user_repository.get_user_project_profile()
        project_type = project_data.get('type')
    if not user_context['group_ids']:
        user_context['group_ids'] = []
    return generate_func_tree_of_user(user_context, funcs_set, filter_app_enable, project_type=project_type)


def deal_admin_auth_func_data(project_type: str, func_data: list, new_func_data: list) -> list:
    for i in range(len(func_data)):
        func_id = func_data[i].get('id')
        if not func_service.is_display_func(project_type, func_id):
            continue
        new_func_data.append(func_data[i])
    auth_func_data = service.list_dict_group_by(new_func_data, 'application_id')
    return auth_func_data


def filter_menu_permission_by_func(
    func_data: dict, app_ids: list, auth_func_data: dict, group_func_data_authorized: dict
):
    # 根据功能权限过滤菜单
    for app_id in func_data.keys():
        funcs = []
        # 只控制DMP以外的应用功能权限
        if app_id != buildin_application_id:
            funcs = func_data[app_id]
        elif app_id in app_ids:
            funcs = group_func_data_authorized[app_id]
            menu_group_map = {}
            display_menu_ids = set()
            for item in func_data[app_id]:
                if (item['url'] is None or item['url'].strip() == '') and item['id']:
                    menu_group_map[item['id']] = item

            for item in funcs:
                if item['parent_id'] and item['parent_id'].strip() != '':
                    display_menu_ids.add(item['parent_id'])

            menu_group_items = [func for func_id, func in menu_group_map.items() if func_id in display_menu_ids]
            funcs = funcs + menu_group_items
        level_codes = [func['level_code'] for func in funcs]
        auth_func_data[app_id] = _filter_func(funcs, level_codes)
    return auth_func_data


def deal_single_app(
    filter_app_enable: bool,
    user_context: Dict[str, Union[str, List[str]]],
    auth_func_data: Dict[str, Union[List[Dict[str, str]], List[Union[Dict[str, str], Dict[str, Union[str, None]]]]]],
    selected_func_codes: List[Any],
    selected_app_ids: List[Any],
    func_data: List[Dict[str, Union[str, None]]],
) -> List[Dict[str, Any]]:
    auth_app_data = []
    # 解决相互引用问题
    from app_menu.services import application_service

    platform = user_context.get("platform") or 'pc'
    platform = platform.lower()
    app_data = application_service.get_all_application(platform=platform, is_release=filter_app_enable)
    # 验证是否显示应用. 只要有应用的查看权限就显示
    has_app_view_auth = True
    has_check_app_view_auth = False
    for app in app_data:
        app_model = ApplicationModel(**app)
        application_service.set_app_url_info(app_model)
        app = app_model.get_dict()

        # -------------- 过滤app --------------------------- #
        if filter_app_enable and not app.get('enable'):
            continue

        app_id = app.get('id')
        if app_id != buildin_application_id and not has_check_app_view_auth:
            # 获取用户的应用的查看权限
            user = {'userid': user_context['user_id'], 'group_ids': user_context["group_ids"]}
            has_app_view_auth = func_auth_service.verify_funcs_permission(user, ['app-site.view'])
            has_check_app_view_auth = True

        if not has_app_view_auth:
            continue

        # --------------------------------------------- #

        if selected_func_codes:
            for func in func_data:
                func['selected'] = func.get('func_code') in selected_func_codes

        app['function'] = function_service.get_function_tree(app.get('id'), is_release=True)
        if selected_app_ids and app_id in selected_app_ids:
            app['selected'] = True
        auth_app_data.append(app)
    return auth_app_data


def generate_func_tree_of_user(
    user_context: Dict[str, Union[str, List[str]]],
    funcs_set: Dict[str, List[str]],
    filter_app_enable: Optional[bool] = None,
    selected_group_id: None = None,
    project_type: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """
    生成用户功能菜单树
    :param user_context:
    :param funcs_set: {'user': ['edit', 'view']}
    :param bool filter_app_enable:
    :param str selected_group_id:
    :param str project_type:
    :return:
    """
    from dmplib.saas.project import get_project_info
    group_ids = user_context['group_ids']
    is_admin = ADMINISTRATORS_GROUP_ID in group_ids

    # 当项目类型没有平台，删除离线大数据菜单，后期要做成功能配置
    # 删除首页菜单
    # 菜单添加在线报告
    new_func_data = []
    auth_func_data = {}
    if is_admin:

        func_data = function_service.get_all_function(is_release=filter_app_enable)
        from rbac import external_service
        func_data = external_service.filter_value_added_func(func_data)
        auth_func_data = deal_admin_auth_func_data(project_type, func_data, new_func_data)
    else:
        # 过滤，只有view 权限才能查看该菜单
        func_codes_authorized = []
        for menu, actions in funcs_set.items():
            if 'view' in actions:
                func_codes_authorized.append(menu)
        func_rows_authorized = func_repository.get_funcs_rows(func_codes_authorized, is_release=filter_app_enable)

        # 过滤项目类型/平台或可视化
        for i in range(len(func_rows_authorized)):
            func_id = func_rows_authorized[i].get('id')
            if func_service.is_display_func(project_type, func_id):
                new_func_data.append(func_rows_authorized[i])

        func_rows_authorized = new_func_data
        func_data = service.list_dict_group_by(new_func_data, 'application_id')

        group_func_data_authorized = (
            service.list_dict_group_by(func_rows_authorized, 'application_id') if func_rows_authorized else {}
        )
        app_ids = list(group_func_data_authorized.keys())
        # 根据功能权限过滤菜单
        auth_func_data = filter_menu_permission_by_func(func_data, app_ids, auth_func_data, group_func_data_authorized)

    selected_app_ids = []
    selected_func_codes = []
    if selected_group_id:
        selected_app_ids = repository.get_columns('user_group_app', {'group_id': selected_group_id}, 'app_id')
        if ADMINISTRATORS_GROUP_ID in group_ids:
            role_funcs = func_service.get_all_func_with_codes()
        else:
            role_funcs = func_auth_repository.get_funcs_by_group_id(group_ids)
        selected_func_codes = [item['func_code'] for item in role_funcs]

    auth_app_data = deal_single_app(
        filter_app_enable, user_context, auth_func_data, selected_func_codes, selected_app_ids, func_data
    )
    """
    现状问题：
    user/profile接口历史代码复杂纷乱，用户门户和菜单门户没有区分，且存在性能问题。
    工作台页面刷新就会调用user/profile接口，登录进入工作台会在此接口等待时间过长。
    原因是该接口不仅仅获取后台的菜单数据，还有用户门户列表的获取。而用户门户列表的获取随着数量增多会变得很慢。
    导致接口响应非常缓慢，等待时间很长。
    解决办法：
    避免大的影响，现在用户门户列表获取的逻辑拆分，使用其他专用门户接口 /api/user/app/index 来获取。
    user/profile接口只查询后台菜单，不查询展示门户数据。其他接口调用当前逻辑还是获取所有的门户（菜单门户+用户门户），不影响其他接口
    门户列表获取逻辑增加 is_application_all 参数控制
    is_application_all 参数：获取门户的范围【1：获取所有门户（后台菜单+用户门户列表），0：只获取后台菜单门户】默认是获取所有门户
    """
    is_application_all = 1 if user_context.get("is_application_all") is None else user_context.get("is_application_all")
    _debugger.log({f'门户列表查询逻辑参数is_application_all：': is_application_all})

    # 获取当前租户的发布鉴权模式
    project_info = get_project_info(g.code)

    # 之前逻辑过于复杂
    # 查看该用户有哪些权限
    if not is_admin:
        if auth_app_data and isinstance(auth_app_data, list):
            app_data = auth_app_data[0]
            if app_data.get('id') == buildin_application_id:
                auth_app_data = auth_app_data[:1]
            else:
                auth_app_data = []
        # 获取数见授权的门户
        get_sj_application(auth_app_data, project_info, group_ids, user_context, is_application_all)

    # 基础数据平台授权的门户数据
    get_mip_application(auth_app_data, user_context, is_application_all)

    # 开发者模式下，非开发者账号登录删除后台菜单门户
    del_rdc_auth_application(auth_app_data, project_info)

    return auth_app_data


def get_sj_application(auth_app_data, project_info, group_ids, user_context, is_application_all=1):
    """
    获取数见授权门户数见

    :param auth_app_data:
    :param project_info:
    :param group_ids:
    :param user_context:
    :param is_application_all:
    :return:
    """

    if is_application_all not in [1, '1']:
        return
    platform = user_context.get("platform") or 'pc'
    platform = platform.lower()

    auth_mode = project_info.get("auth_mode", ObjectAuthMode.Default.value)
    # 0：数见权限门户列表合并基础数据权限门户列表；1：从基础数据获取门户列表权限
    _debugger.log({f'租户{g.code}报告、门户的发布鉴权模式': auth_mode})

    # 租户发布鉴权模式为0，合并数见权限的门户列表
    if auth_mode == ObjectAuthMode.Default.value:
        from rbac.services.user_service import get_all_roles_of_user
        if group_ids:
            all_group_ids = get_all_parent_group_by_group_ids(group_ids)
            group_ids = list(set(group_ids + all_group_ids))
        role_ids = get_all_roles_of_user(user_context.get('user_id'), group_ids)
        from rbac.services import data_permissions
        # 兼容自助报表第三方登录使用虚拟用户
        if user_context.get('user_id') == SELF_SERVICE_VIRTUAL_USER_ID:
            role_ids = g.external_params.get('customize_roles') if hasattr(g, 'external_params') and g.external_params else []
        app_function = data_permissions.get_all_applications_by_role_ids(role_ids, platform)
        _debugger.log({'数见平台查到的授权门户数据': app_function})
        auth_app_data += app_function
    else:
        _debugger.log({'只从基础数据获取门户列表权限，不查询数见授权门户': auth_mode})


def check_app_allow_exists(user_id, group_ids):
    """
    检查当前用户是否存在有权限门户数据
    :param user_id:
    :param group_ids:
    :return:
    """
    from dmplib.redis import conn as conn_redis
    cache = conn_redis()

    key = f'check_app_allow_exists:{user_id}'
    val = cache.get(key)
    if val:
        if isinstance(val, bytes):
            val = val.decode()
    if val is not None:
        return bool(int(val))
    # 设置5分钟缓存
    is_exists = get_app_allow_exists_status(user_id, group_ids)
    cache.set(key, int(is_exists), 300)
    return is_exists


def get_app_allow_exists_status(user_id, group_ids):
    """
    获取当前用户是否存在有权限门户
    :param user_id:
    :param group_ids:
    :return:
    """
    try:
        is_admin = ADMINISTRATORS_GROUP_ID in group_ids
        if is_admin:
            return True
        from rbac.services.user_service import get_all_roles_of_user
        if group_ids:
            all_group_ids = get_all_parent_group_by_group_ids(group_ids)
            group_ids = list(set(group_ids + all_group_ids))
        role_ids = get_all_roles_of_user(user_id, group_ids)
        from rbac.services import data_permissions
        # 兼容自助报表第三方登录使用虚拟用户
        if user_id == SELF_SERVICE_VIRTUAL_USER_ID:
            role_ids = g.external_params.get('customize_roles') if hasattr(g, 'external_params') else []
        can_view_all, app_allow_ids = data_permissions.check_app_allow_ids(role_ids)
        _debugger.log({'检查当前用户是否存在数见权限门户': {"can_view_all": can_view_all, "app_allow_ids": app_allow_ids}})
        if can_view_all or app_allow_ids:
            return True
        mip_data = check_mip_app_allow_exists()
        _debugger.log({'检查当前用户是否存在mip权限门户': {"mip_data": mip_data}})
        if mip_data:
            return True
    except Exception as e:
        _debugger.log(f'检查当前用户的权限门户错误，errs：{str(e)}')
        return False
    return False


def get_mip_application(auth_app_data, user_context, is_application_all=1):
    if is_application_all not in [1, '1']:
        return
    platform = user_context.get("platform") or 'pc'
    platform = platform.lower()
    additional_get_mip_application_data(auth_app_data, platform)


def del_rdc_auth_application(auth_app_data, project_info):
    is_rdc_auth = project_info.get('is_rdc_auth')
    if auth_app_data and isinstance(auth_app_data, list):
        app_data = auth_app_data[0]
        if app_data.get('id') == buildin_application_id and is_rdc_auth and getattr(g, 'is_developer', 0) != 1:
            del auth_app_data[0]


def additional_get_mip_application_data(auth_app_data, platform):
    """
    额外的获取获取基础数据平台授权的门户数据
    """

    mip_app_cls = ApplicationsAvailable()
    if not mip_app_cls.mip_config_is_exist():
        _debugger.log('环境没有配置集成环境地址，不会到基础数据查询数据！')
        return

    # 真正去查询门户数据
    mip_data = mip_app_cls.get_mip_available_portals() or []
    # 获取基础平台授权的门户信息
    mip_function = get_mip_auth_application_data(mip_data, platform)
    _debugger.log({'基础数据平台查到的授权门户数据': mip_function})
    merge_mip_app_data(auth_app_data, mip_function)


def check_mip_app_allow_exists():
    """
    检查是否存在基础数据授权的门户
    :return:
    """
    mip_data = []
    mip_app_cls = ApplicationsAvailable()
    if not mip_app_cls.mip_config_is_exist():
        _debugger.log('环境没有配置集成环境地址，不会到基础数据查询数据！')
        return mip_data

    # 查询授权门户数据
    mip_data = mip_app_cls.get_mip_available_portals() or []
    return mip_data


def merge_mip_app_data(auth_app_data, mip_function):
    # 合并基础数据平台授权和数见自己授权门户数据门户
    # 规则为：
    # 1. 门户数据取并集
    # 2. 门户下的权限如果基础数据平台有授权，以基础数据平台为准
    user_inner_app_auth = {af.get('id'): af for af in auth_app_data if af.get('id')}
    for mip_app in mip_function:
        mip_app_id = mip_app.get('id', '')
        if not mip_app_id:
            continue
        if mip_app_id in user_inner_app_auth:
            # 基础数据平台和数见同时授权了同一门户，使用基础数据的菜单授权信息
            user_inner_app_auth[mip_app_id]['function'] = mip_app.get('function') or []
        else:
            # 只有基础数据平台授权信息
            auth_app_data.append(mip_app)


def get_mip_auth_application_data(mip_function, platform='pc'):
    """
    app_function: 用户数见授权的门户
    mip_function: 用户基础平台授权的门户
    """
    from app_menu.services.application_service import get_application_without_permission
    result = []
    for mip_app in mip_function:
        mip_app_id = mip_app.get('id', '')
        app_model = get_application_without_permission(mip_app_id)
        if not app_model or app_model.platform != platform:
            continue
        app_model.function = function_service.get_function_tree(
            mip_app_id, auth_filter_flag=True, auth_way=ThirdFuncFilterType.MipAuthWay.value
        )
        result.append(app_model.get_dict())
    return result


def generate_func_tree(group_ids, filter_app_enable=None, selected_group_id=None, project_type=None):
    """
    生成用户功能菜单树
    :param list group_ids:
    :param bool filter_app_enable:
    :param str selected_group_id:
    :param str project_type:
    :return:
    """
    is_admin = ADMINISTRATORS_GROUP_ID in group_ids
    # 解决相互引用问题
    from app_menu.services import application_service

    app_data = application_service.get_all_application()
    func_data = function_service.get_all_function()

    # 当项目类型没有平台，删除离线大数据菜单，后期要做成功能配置
    # 删除首页菜单
    new_func_data = []
    for i in range(len(func_data)):
        if (
            func_data[i].get('id')
            in [
                '00000000-0000-0000-0003-000000000000',
                '00000000-0000-0000-0003-000000000001',
                '00000000-0000-0000-0003-000000000002',
                '00000000-0000-0000-0003-000000000003',
                '00000000-0000-0000-0003-000000000004',
            ]
            and project_type
            and '可视化' == project_type
        ):
            continue
        new_func_data.append(func_data[i])
    func_data = service.list_dict_group_by(new_func_data, 'application_id')
    auth_func_data = {}
    if is_admin:
        auth_func_data = func_data
        group_app_ids = []
        group_func_app_ids = []
    else:
        group_app_data = repository.get_data('user_group_app', {'group_id': group_ids[0]}, ['app_id'], True)
        group_func_data = user_group_repository.get_user_group_func(group_ids)
        group_func_data = service.list_dict_group_by(group_func_data, 'application_id') if group_func_data else {}
        group_func_app_ids = list(group_func_data.keys())
        group_app_ids = [func['app_id'] for func in group_app_data] if group_app_data else []
        for app_id, funcs in func_data.items():
            if app_id in group_app_ids:
                auth_func_data[app_id] = funcs
                continue
            if app_id in group_func_app_ids:
                auth_func_data[app_id] = _filter_func(funcs, [func['level_code'] for func in group_func_data[app_id]])

    return deal_app_data(
        is_admin, app_data, selected_group_id, filter_app_enable, group_app_ids, group_func_app_ids, auth_func_data
    )


def deal_app_data(
    is_admin, app_data, selected_group_id, filter_app_enable, group_app_ids, group_func_app_ids, auth_func_data
) -> list:
    auth_app_data = []
    selected_app_ids = []
    selected_func_ids = []
    if selected_group_id:
        selected_app_ids = repository.get_columns('user_group_app', {'group_id': selected_group_id}, 'app_id')
        selected_func_ids = repository.get_columns('user_group_func', {'group_id': selected_group_id}, 'func_id')
    for app in app_data:
        if filter_app_enable and not app.get('enable'):
            continue
        app_id = app.get('id')
        if is_admin or app_id in group_app_ids or app_id in group_func_app_ids:
            functions = auth_func_data.get(app_id)
            selected_func_ids and _selected_func(functions, selected_func_ids)
            app['function'] = function_service.generate_func_tree(functions)
            if selected_app_ids and app_id in selected_app_ids:
                app['selected'] = True
            app['disable'] = False if is_admin else app_id not in group_app_ids
            auth_app_data.append(app)
    return auth_app_data


def _selected_func(func_data, selected_ids):
    if not func_data or not selected_ids:
        return
    for func in func_data:
        func['selected'] = func.get('id') in selected_ids


def _filter_func(func_data, func_codes):
    parent_codes = []
    for code in func_codes:
        tmp_code = code.split('-')
        code_len = len(tmp_code)
        for i in range(code_len - 2):
            parent_code = '-'.join(tmp_code[0 : i + 1]) + '-'
            if parent_code not in func_codes:
                parent_codes.append('-'.join(tmp_code[0 : i + 1]) + '-')
    result = []
    for func in func_data:
        level_code = func.get('level_code')
        if level_code in parent_codes:
            func['disable'] = True
            result.append(func)
            continue
        for code in func_codes:
            if level_code.startswith(code):
                result.append(func)
                break
    return result


def get_user_group_func_editable_tree(group_id):
    """
    获取用户功能菜单树（编辑使用）
    :param group_id:
    :return:
    """
    if group_id.lower() == ADMINISTRATORS_GROUP_ID.lower():
        return None
    user_group = get_user_group(group_id)
    parent_id = user_group.get('parent_id')
    if not parent_id:
        return []
    project_data = user_repository.get_user_project_profile()
    return generate_func_tree([parent_id], selected_group_id=group_id, project_type=project_data.get('type'))


def get_user_group_tree():
    """
    获取用户组树
    :return list:
    """
    list_data = get_user_group_list()
    result = []
    tmp_dict = {}
    for group in list_data:
        if not isinstance(group, dict):
            continue
        group['sub'] = []
        # code格式: 00001-、00001-00001-
        group_code = group.get('code')
        parent_group_code = (
            group_code[0 : len(group_code) - 6]
            if group.get('account_mode') in [AccountMode.ERP.value]
            else group_code[0 : len(group_code) - 5]
        )
        if parent_group_code not in tmp_dict:
            result.append(group)
            tmp_dict[group_code] = group
        else:
            tmp_dict.get(parent_group_code)['sub'].append(group)
            tmp_dict[group_code] = group
        # 查询当前用户组所有权限
        group["roles"] = role_repository.list_roles_of_group(group.get("id"))
    return result


def get_role_or_user(user_group_id):
    """
    根据用户组ID获取角色or用户列表
    :param user_group_id:
    :return:
    """
    role_data = user_group_repository.get_user_role(user_group_id)
    for role in role_data:
        role["user"] = user_group_repository.get_user_by_role_id(role.get("id"))
    return role_data


def get_account_mode():
    """
    获取系统账户登录模式
    :return:
    """
    project_config = project.get_db_config(code=getattr(g, 'code'))
    return project_config.get('account_mode')


def get_external_tree():
    """
    获取外部组织机构树
    :return list:
    """
    if not repository.check_db_table_is_exist(ERP_USER_GROUP):
        raise UserError(message='external_user_group数据表数据未生成')

    list_data = user_group_repository.get_erp_user_group_list()
    result = []
    tmp_dict = {}
    for group in list_data:
        if not isinstance(group, dict):
            continue
        group['sub'] = []
        # code格式: 00001-、00001-00001-
        group_code = group.get('code')
        parent_group_code = group_code[0 : len(group_code) - 6]
        if parent_group_code not in tmp_dict:
            result.append(group)
            tmp_dict[group_code] = group
        else:
            tmp_dict.get(parent_group_code)['sub'].append(group)
            tmp_dict[group_code] = group
    return result


def get_external_group_list_data():
    """
    获取外部组织机构列表
    :return list:
    """
    if not repository.check_db_table_is_exist(ERP_USER_GROUP):
        raise UserError(message='external_user_group数据表数据未生成')

    list_data = user_group_repository.get_erp_user_group_list()
    return list_data


def get_external_user(query_model):
    """
    根据外部组织ID获取外部用户列表
    :return list:
    """
    return user_group_repository.get_external_user_by_group_id(query_model)


def generate_erp_user_group(group_code):
    if group_code:
        dmp_user_group = repository.get_data("user_group", {"code": group_code})
        if not dmp_user_group:
            erp_user_group = repository.get_data("external_user_group", {"code": group_code})
            repository.add_data("user_group", erp_user_group)
            parent_group_code = group_code[0 : len(group_code) - 6]
            generate_erp_user_group(parent_group_code)


def generate_group_path(group_code, group_path):
    if not group_code:
        return group_path
    dmp_user_group = repository.get_data("user_group", {"code": group_code})
    if not dmp_user_group:
        return group_path
    group_path = dmp_user_group.get("name") + "-" + group_path
    parent_group_code = group_code[0 : len(group_code) - 6]
    return generate_group_path(parent_group_code, group_path)


def add_single_user(user_data: dict) -> dict:
    # 添加用户
    dmp_user = repository.get_data("user", {"id": user_data.get("id")})
    if "is_select" in user_data:
        user_data.pop("is_select")
    user_data['add_mode'] = AddMode.AUTO.value
    if dmp_user:
        user_data['modified_on'] = datetime.datetime.now()
        user_repository.update_data(user_data, {"id": dmp_user.get("id")})
    else:
        user_repository.add_data(user_data)
    return user_data

def _add_or_update_user(exist_user, user_data):
    user_source = repository.get_one('user_source', {'id': user_data.get('user_source_id')})
    is_erp_user_source = False if user_source and user_source.get('type') == UserChannel.Others.value else True
    user_source_table_name = ERP_USER if is_erp_user_source else 'user_source_user'
    # 检查external表的id是否和user表id相同, 不同则使用user表的id并更新external_user表, 解决添加用户失败的问题
    real_user_id = exist_user.get('id') if exist_user else None
    if real_user_id is not None and user_data.get("id") != real_user_id:
        repository.update(user_source_table_name, {'id': real_user_id}, {'id': user_data.get('id')})
        user_data['id'] = real_user_id

    #  同步模式/导入模式也需要自动添加
    if user_data.get("account_mode") in [
        AccountMode.ERP.value,
        AccountMode.SYNC.value,
        AccountMode.IMPORT.value,
        AccountMode.DOMAIN.value,
    ]:
        # 添加用户
        user_data['group_id'] = ""
        user_data = add_single_user(user_data)
    if is_erp_user_source:
        # 添加用户和ERP组织关联表，用于数据集关联组织筛选过滤。
        erp_user_group_ids = repository.get_columns(ERP_USER, {"id": user_data.get("id")}, "group_id")
        if erp_user_group_ids:
            for group_id in erp_user_group_ids:
                add_user_organization(user_data.get("id"), group_id)
        elif user_data.get("group_id"):
            add_user_organization(user_data.get("id"), user_data.get("group_id"))

def _add_user(dmp_group_id, user_data):
    from user.services import user_service
    exist_user = user_service.get_user_by_account(user_data.get('account'), ['id', 'user_source_id'])
    if not exist_user or exist_user.get('user_source_id') == user_data.get('user_source_id'):
        _add_or_update_user(exist_user, user_data)

    # 添加用户和用户组关系
    user_id = exist_user.get('id') if exist_user else user_data.get("id")
    dmp_user_group_user = repository.get_data("user_group_user", {"group_id": dmp_group_id, "user_id": user_id})
    if not dmp_user_group_user:
        repository.add_data("user_group_user", {"id": seq_id(), "group_id": dmp_group_id, "user_id": user_id})

    # 将用户加入用户组下的角色中
    # 这里不在将用户加入用户组下的角色中，因为会自动继承用户组的角色
    # 清除缓存
    user_service.refresh_cur_rold_id(user_id=user_data.get("id"))

def add_user(users):
    """
    添加用户到用戶組
    :return list:
    """

    user_ids = []

    for data in users:
        dmp_group_id = data.get("group_id")
        if not dmp_group_id:
            raise UserError(message='请先选择用户组')
        dmp_user_group = repository.get_data("user_group", {"id": dmp_group_id})
        if not dmp_user_group:
            raise UserError(message='用户组不存在：' + dmp_group_id)
        for user_data in data.get("user"):
            if user_data.get("id") is None:
                continue
            _add_user(dmp_group_id, user_data)
            user_ids.append(user_data.get("id"))

    return user_ids


def add_user_user_role(user_id, group_id):
    """
    添加用户和ERP组织关联表， 用于数据集关联组织筛选过滤。
    :param user_id:
    :param group_id:
    :return:
    """
    user_group_roles = repository.get_columns("user_group_role", {"group_id": group_id}, "role_id")
    if user_group_roles:
        for role_id in user_group_roles:
            user_group_roles = repository.get_data("user_user_role", {"user_id": user_id, "role_id": role_id})
            if not user_group_roles:
                repository.add_data("user_user_role", {"user_id": user_id, "role_id": role_id})


def add_user_organization(user_id, group_id):
    """
    添加用户和ERP组织关联表，用于数据集关联组织筛选过滤。
    :param user_id:
    :param group_id:
    :return:
    """
    erp_user_groups = repository.get_data("external_user_group", {"id": group_id}, multi_row=True)
    for erp_user_group in erp_user_groups:
        hierarchy = erp_user_group.get("hierarchy")
        erp_group_name = erp_user_group.get("name")
        condition = {"user_id": user_id, "group_id": group_id}
        user_organization = repository.get_data("user_organization", condition)
        new_user_organization = {
            "user_id": user_id,
            "group_id": group_id,
            "org_name": erp_group_name,
            "org_level": hierarchy,
        }
        if user_organization:
            repository.update_data("user_organization", new_user_organization, condition)
        else:
            repository.add_data("user_organization", new_user_organization)


def get_inorganization_users(query_model):
    """
    获取无组织用户列表
    :param user.models.QueryBaseModel query_model:
    :return user.models.QueryBaseModel:
    """
    return user_repository.get_user_list_exclusion_org(query_model)


def get_user_list(query_model):
    """
    获取用户列表
    :param user.models.UserQueryModel query_model:
    :return user.models.UserQueryModel:
    """
    return user_repository.get_user_list(query_model)


def update_user_user_group(user_id, group_ids):
    """
    用户添加多用户组(先删除用户所有关联的用户组，再逐个添加)
    """
    return user_group_repository.update_user_group(user_id, group_ids)


def get_user_permission_apps(user_id, group_ids, platforms):
    """
    获取用户有权限的应用
    """
    group_ids = group_ids if group_ids else []
    is_admin = ADMINISTRATORS_GROUP_ID in group_ids
    auth_app_data = []
    if is_admin:  # 系统管理员
        # 解决相互引用问题
        from app_menu.services import application_service

        app_data = []
        for platform in platforms:
            app_data += application_service.get_all_application(platform=platform, is_release=True)
        # 获取用户的应用的查看权限
        user = {'userid': user_id, 'group_ids': group_ids}
        has_app_view_auth = func_auth_service.verify_funcs_permission(user, ['app-site.view'])
        for app in app_data:
            if app.get('enable') == 1 and has_app_view_auth and app.get('id') != buildin_application_id:
                auth_app_data.append(app)
    else:  # 非管理员
        from rbac.services.user_service import get_all_roles_of_user

        role_ids = get_all_roles_of_user(user_id, group_ids)
        from rbac.services import data_permissions

        for platform in platforms:
            auth_app_data += data_permissions.get_all_applications_by_role_ids(role_ids, platform, False)
    return auth_app_data


def add_user_source(user_source):
    # 新增，修改用户渠道
    user_source.id = user_source.id if user_source.id else seq_id()
    exist_user_source = repository.get_one("user_source", {'id': user_source.id})
    same_name_user_source = repository.get_one("user_source", {'name': user_source.name})
    if exist_user_source:  # 修改
        if same_name_user_source and same_name_user_source.get('id') != user_source.id:
            raise UserError(message='用户渠道已存在，不允许修改')
        user_source.type = exist_user_source.get('type')
        repository.update_model("user_source", user_source, {'id': user_source.id})
    else:  # 新增
        if same_name_user_source:
            raise UserError(message='用户渠道已存在，不允许添加')
        user_source.type = 2
        repository.add_model("user_source", user_source)
    if repository.data_is_exists('flow', {'id': sync_flow_id(user_source_id=user_source.id)}):
        repository.update('flow', {'name': user_source.name}, {'id': sync_flow_id(user_source_id=user_source.id)})
    if repository.data_is_exists('flow', {'id': import_flow_id(user_source_id=user_source.id)}):
        repository.update('flow', {'name': f'{user_source.name}-用户引入'}, {'id': import_flow_id(user_source_id=user_source.id)})

    return user_source.id


def delete_user_source(user_source_id):
    """
    删除用户组
    :param group_id:
    :return:
    """
    validate_delete_user_source(user_source_id)
    affect_row = user_group_repository.delete_user_user_source(user_source_id)
    flow_service.delete_flow(sync_flow_id(user_source_id=user_source_id))
    flow_service.delete_flow(import_flow_id(user_source_id=user_source_id))
    return affect_row


def validate_delete_user_source(user_source_id):
    if not user_source_id:
        raise UserError(message='请求参数user_source_id不能为空')
    all_user_source = get_user_source_list()
    if len(all_user_source) == 1:
        raise UserError(message='只有一个用户渠道，不允许删除')
    if not repository.data_is_exists('user_source', {'id': user_source_id}):
        raise UserError(message='渠道不存在，不允许删除')


def get_user_source_list():
    # 获取用户渠道列表
    return repository.get_list('user_source', None, order_by='created_on asc')


def get_source_user(query_model, user_source_id, user_source_group_id=None):
    # 获取渠道用户
    query_model.validate()
    user_source = repository.get_one('user_source', {'id': user_source_id})
    if not user_source:
        raise UserError(message='渠道不存在，请稍后重试')
    return user_repository.get_source_user_list(query_model, user_source, user_source_id, user_source_group_id)


def get_user_soruce_by_id(user_source_id):
    return repository.get_data("user_source", {"id": user_source_id}, ["id", "name", "data", "type", "remark"])


def get_user_source_group_tree(user_source_id):
    # 获取渠道组织权限树
    """
    获取渠道组织权限树
    :return list:
    """
    user_source = get_user_soruce_by_id(user_source_id)
    if not user_source:
        raise UserError(message='用户渠道不存在')
    if user_source.get('type') == 1:
        result = get_external_tree()
        for group in result:
            group['user_source_id'] = user_source_id
            group['user_source_group_id'] = group.get('id')
    else:
        if not repository.check_db_table_is_exist("user_source_group"):
            raise UserError(message='user_source_group数据表数据未生成')

        list_data = user_group_repository.get_user_source_group_list(user_source_id)
        result = []
        tmp_dict = {}
        for group in list_data:
            group['id'] = group.get('user_source_group_id')  # 前端id，不好改，这里id返回组织ID
            if not isinstance(group, dict):
                continue
            group['sub'] = []
            tmp_dict[group.get('user_source_group_id')] = group
        for group in list_data:
            parent_id = group.get('parent_id')
            if parent_id in tmp_dict:
                tmp_dict.get(parent_id)['sub'].append(group)
            else:
                result.append(group)
    return result


def get_erp_auth_apps():
    """
    获取erp用户有权限的门户级门户下报告
    :return:
    """
    # 获取当前用户有权限的门户
    auth_apps = []
    pc_apps = get_user_permission_apps(g.userid, g.group_ids, ['pc'])
    for app in pc_apps:
        auth_app = {
            'id': app.get('id'),
            'parent_id': '',
            'name': app.get('name'),
            'icon_url': '',
            'url': '',
            'created_on': app.get('created_on'),
            'children': []
        }
        if app.get('url'):
            dashboard_info = get_dashboard_info(app.get('url'), app.get('id'))
            if dashboard_info:
                auth_app.get('children').append(dashboard_info)
        get_sub_menu(auth_app)
        auth_apps.append(auth_app)
    return filter_dashboard_sub(auth_apps)


def get_sub_menu(auth_app):
    from rbac.services.data_permissions import filter_application_sub
    sub_menus = filter_application_sub(function_service.get_function_tree(auth_app.get('id')))
    if sub_menus:
        for menu in sub_menus:
            _recursive_get_sub_menu(auth_app, menu)


def _recursive_get_sub_menu(auth_app, menu):
    if menu.get('is_dashboard_exist') == 1:
        dashboard_info = get_dashboard_info(menu.get('url'), auth_app.get('id'))
        if dashboard_info:
            auth_app.get('children').append(dashboard_info)
    sub_menus = menu.get('sub')
    if sub_menus:
        for sub_menu in sub_menus:
            _recursive_get_sub_menu(auth_app, sub_menu)


def get_dashboard_info(dashboard_id, parent_id):
    dashboard = repository.get_data('dashboard', {'id': dashboard_id})
    if dashboard:
        icon_url = dashboard.get('cover') if dashboard.get('cover') \
            else f"{config.get('Domain.dmp')}/static/images/layout-free-white-2.3ba3976.png"
        dashboard_info = {
            'id': dashboard.get('id'),
            'parent_id': parent_id,
            'name': dashboard.get('name'),
            'icon_url': icon_url,
            'created_on': dashboard.get('created_on'),
            'url': f"{config.get('Domain.dmp')}/dataview/share/{dashboard_id}?code={g.code}"
        }
        return dashboard_info
    return None


def filter_dashboard_sub(auth_apps):
    """
    检查报告是否有相关权限
    :param auth_apps:
    :return:
    """
    from dashboard_chart import external_service
    all_dashboard_id = []
    for app in auth_apps:
        for dashboard in app.get('children'):
            all_dashboard_id.append(dashboard.get('id'))
    if not all_dashboard_id:
        return []
    auth_dashboard = external_service.filter_permission_dashboard(all_dashboard_id)
    auth_dashboard_id = [dashboard.get('id') for dashboard in auth_dashboard]
    result = []
    for app in auth_apps:
        children = app.get('children')
        app['children'] = []
        for dashboard in children:
            if dashboard.get('id') in auth_dashboard_id:
                app.get('children').append(dashboard)
        if len(app.get('children')) > 0:
            result.append(app)
    return result


def get_all_parent_group_by_group_ids(group_ids: list):
    all_code = []
    codes = repository.get_column('user_group', {'id': group_ids}, 'code') or []
    if not codes:
        return all_code
    for code in codes:
        if not code:
            continue
        all_code = all_code + get_parent_code_by_code(code)
    if not all_code:
        return []
    all_code = list(set(all_code))
    group_ids = repository.get_column('user_group', {'code': all_code}, 'id') or []
    have_child_group_ids = repository.get_column('user_group_role', {'group_id': group_ids, 'is_include_child': 1}, 'group_id') or []
    return have_child_group_ids


def get_parent_code_by_code(code):
    if len(code) > 5:
        code = code[:-5]
        if len(code) > 5:
            return [code] + get_parent_code_by_code(code)
    return [code]


def get_user_group_all_path(user_id):
    """
    根据用户id，查询改用户的组织全路径
    """
    result = get_group_code_by_user_id(user_id) or []
    if not result:
        return []
    code_list = []
    all_code_path = []
    for row in result:
        simple_code_path = []
        code = row.get('code') or ''
        code_split = code.split('-')
        p = ''
        for s in code_split:
            if s:
                p += s + '-'
                simple_code_path.append(p)
        if simple_code_path:
            all_code_path.append(simple_code_path)
            code_list.extend(simple_code_path)
    code_list = list(set(code_list))
    code_name_list = get_group_name_by_code(code_list) or []
    code_name_map = {}
    for row in code_name_list:
        code_name_map[row.get('code')] = row.get('name') or ''
    result = []
    for simple_path in all_code_path:
        name_list = []
        for code in simple_path:
            name = code_name_map.get(code) or ''
            name_list.append(name)
        result.append('/'.join(name_list))
    return result

