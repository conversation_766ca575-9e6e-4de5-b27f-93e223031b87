import json
from base.enums import FlowNodeType, FlowStatus
from base import repository
from base.enums import FlowType
from dmplib.utils.errors import UserError
from flow.services import flow_service, flow_instance_service
from flow.models import FlowNodeModel, FlowModel


def save_api_type_user_source(user_source: dict, old_user_source: dict):
    check_api_user_source(user_source)
    save_user_source(user_source, old_user_source)
    source_name = old_user_source.get('name', None)
    user_source_id = user_source.get('user_source_id', None)
    schedule = user_source.get('api_info', {}).get('schedule')
    # 更新流程调度信息
    save_flow(user_source_id, schedule, source_name)


def save_ad_type_user_source(user_source: dict, old_user_source: dict):
    check_ad_user_source(user_source)
    save_user_source(user_source, old_user_source)
    source_name = old_user_source.get('name', None)
    user_source_id = user_source.get('user_source_id', None)
    schedule = user_source.get('ad_info', {}).get('schedule')
    # 更新流程调度信息
    save_flow(user_source_id, schedule, source_name)


def save_user_source(user_source: dict, old_user_source: dict):
    # 判断是否之前配置过数据集用户同步
    user_source_id = user_source.get('user_source_id', None)
    clear_dataset_depend(old_user_source)
    # 更新用户来源信息
    repository.update_data('user_source', {"data": json.dumps(user_source)}, {"id": user_source_id})


def clear_dataset_depend(data: dict):
    """
    清理数据集同步的依赖
    """
    data = json.loads(data.get("data")) if data.get("data") else {}
    old_user_dataset_id = data.get("user", {}).get("source_dataset_id", None)
    old_user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id", None)
    if old_user_dataset_id and old_user_group_dataset_id:
        repository.delete_data(
            "dataset_depend", {"source_dataset_id": old_user_dataset_id, "depend_id": old_user_group_dataset_id}
        )
    user_source_id = data.get('user_source_id', None)
    if user_source_id:
        repository.delete_data("dataset_depend", {"depend_id": user_source_id})


def check_api_user_source(user_source: dict):
    """
    保存api用户同步流程
    :param user_source:
    :return:
    """
    user_source_id = user_source.get('user_source_id', None)
    api_info = user_source.get('api_info', {})
    third_party_id = api_info.get('third_party_id', '')
    if not third_party_id:
        raise UserError(message='没有绑定第三方')
    if not api_info.get('schedule'):
        raise UserError(message='调度信息不能为空')
    if not user_source_id:
        raise UserError(message='用户来源ID不存在')


def check_ad_user_source(user_source: dict):
    """
    检查域用户配置
    :param user_source:
    :return:
    """
    user_source_id = user_source.get('user_source_id', None)
    ad_info = user_source.get('ad_info', {})
    schedule = ad_info.get('schedule')
    if not schedule:
        raise UserError(message='调度信息不能为空')
    if not user_source_id:
        raise UserError(message='用户来源ID不存在')


def save_flow(user_source_id, schedule, source_name: str):
    # 获取流程
    flow_model = flow_service.get_flow(user_source_id, include_nodes=True)
    if not flow_model:
        flow_model = FlowModel()
        flow_model.id = user_source_id
        flow_model.name = source_name
        flow_model.status = FlowStatus.Enable.value
        flow_model.schedule = schedule
        flow_model.type = FlowType.UserSync.value
        flow_model.nodes = [FlowNodeModel(name=source_name, type=FlowNodeType.UserSync.value,
                                          content=json.dumps({"item": user_source_id}))]
        flow_service.add_flow(flow_model)
        flow_service.update_flow_schedule(flow_model.id)
    else:
        flow_model.status = FlowStatus.Enable.value
        flow_model.schedule = schedule
        flow_service.update_flow(flow_model)

    flow_instance_id = flow_instance_service.running_flow_is_exists(flow_model.id)
    if flow_instance_id:
        flow_instance_service.stop_instance(flow_instance_id)

    # 立即运行流程
    flow_service.run_flow(user_source_id)


def clear_user_source_data(user_source_id: str, third_party_id: str):
    if user_source_id:
        repository.delete_data('user_source_user', {'user_source_id': user_source_id})
        repository.delete_data('user_source_group', {'user_source_id': user_source_id})
    if third_party_id:
        repository.delete_data('third_party_seq', {'third_party_id': third_party_id})
    return True
