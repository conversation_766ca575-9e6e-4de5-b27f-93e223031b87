#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from dmplib.hug import APIWrapper

from report_center.services.report_service import ReportService

api = APIWrapper(__name__)
logger = logging.getLogger(__name__)


@api.admin_route.post('/add')
def add(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs = report_service.add()
    return True, '创建成功', rs


@api.admin_route.post('/rename')
def rename(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs = report_service.rename()
    return True, '重命名成功', rs


@api.admin_route.post('/delete')
def delete(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs = report_service.delete()
    return True, '删除成功', rs


@api.admin_route.get('/get_dashboard_list')
def get_dashboard_list(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs = report_service.get_dashboard_list()
    return True, '', rs


@api.admin_route.get('/search')
def search(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs = report_service.search()
    return True, '', rs


@api.admin_route.post('/move')
def move(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs = report_service.move()
    return True, '移动成功', rs


@api.admin_route.post('/copy')
def copy(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs, errors = report_service.copy()
    if not errors:
        data = {"id": rs.get("id"), "parent_id": rs.get("parent_id"), "name": rs.get("name")}
        return True, '复制成功', data
    else:
        return False, '复制失败', errors[0]


@api.admin_route.post('/release')
def release(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    rs = report_service.release()
    return True, '发布成功', rs


@api.admin_route.get('/go_app')
def release(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    return report_service.go_app()


@api.admin_route.get('/print/login')
def print_login(request, **kwargs):
    report_service = ReportService(request, **kwargs)
    report_service.print_login()


