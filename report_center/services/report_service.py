#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
import uuid
from typing import Union

import hug
import jwt

from components import auth_util
from components.app_hosts import AppHosts
from dmplib.hug import g
from dmplib import config

from base.enums import ApplicationType, SkylineApps
from rbac.validator import PermissionValidator

from report_center.services.biz_service import (
    ActiveReport, SimpleReport, BaseService
)
from user.repositories import user_repository
from user.services import user_service


class BizAppReportService:
    """
    业务应用的操作实现类
    """
    BIZ_MAP = {
        # 自助报表
        ApplicationType.SimpleReport.value: SimpleReport,
        # 统计报表
        ApplicationType.ActiveReport.value: ActiveReport
    }

    @staticmethod
    def get_instance(request, **kwargs) -> Union[SimpleReport, ActiveReport]:
        """
        业务应用的操作的实例化对象
        :param request:
        :param kwargs:
        :return:
        """
        application_type = kwargs.get("application_type")
        application_type = int(application_type) if application_type else ApplicationType.SimpleReport.value
        biz_obj = None
        if application_type in BizAppReportService.BIZ_MAP and BizAppReportService.BIZ_MAP.get(application_type):
            biz_obj = BizAppReportService.BIZ_MAP.get(application_type)
        if not biz_obj:
            biz_obj = SimpleReport

        return biz_obj(application_type, request, **kwargs)

    @classmethod
    def get_active_report_instance(cls, request, **kwargs):
        kwargs["application_type"] = ApplicationType.ActiveReport.value
        return cls.get_instance(request, **kwargs)

    @staticmethod
    def get_report_permissions(kwargs):
        report_id = kwargs.get('id') or ''
        return BaseService.get_report_permissions(report_id)


def get_login_jwt_payload(host, page_type):
    user_id = user_service.get_cur_user_id();
    fields = ['name', 'account']
    user_data = {}
    if user_id:
        user_data = user_repository.get_data({'id': user_id}, fields) or {}
    aid = config.get("ReportCenter.open_print_aid")
    time_out = int(config.get("ReportCenter.open_print_time_out", 3600))
    auth_type = config.get("ReportCenter.open_print_auth_type")
    iat = int(time.mktime(time.localtime(time.time())))
    return {
        "iss": host,
        "exp": iat + time_out,
        "sub": user_data.get("account"),
        "acu": "1",
        "un": user_data.get("name"),
        "ops": "print",
        "aud": g.code,
        "iat": iat,
        "jti": uuid.uuid4(),
        "aid": aid,
        "authType": auth_type,
        "pageType": page_type
    }


class ReportService:

    def __init__(self, request, **kwargs):
        self.request = request
        self.report_biz = BizAppReportService.get_instance(request, **kwargs)

    def add(self):
        """
        添加报表中心报告，子报告，文件夹
        :return:
        """
        validator = PermissionValidator('report_center.view', 'report_center.edit')
        validator()
        return self.report_biz.add()

    def rename(self):
        """
        报告，子报告，文件夹重命名
        :return:
        """
        return self.report_biz.rename()

    def delete(self):
        """
        报告，子报告，文件夹删除
        :return:
        """
        return self.report_biz.delete()

    def get_dashboard_list(self):
        """
        报告列表
        :return:
        """
        validator = PermissionValidator('report_center.view')
        validator()
        return self.report_biz.get_dashboard_list()

    def search(self):
        """
        报告搜索
        :return:
        """
        validator = PermissionValidator('report_center.view')
        validator()
        return self.report_biz.search()

    def move(self):
        """
        报告、文件夹移动
        :return:
        """
        return self.report_biz.move()

    def copy(self):
        """
        报告复制
        :return:
        """
        return self.report_biz.copy()

    def release(self):
        """
        报告发布
        :return:
        """
        return self.report_biz.release()

    def go_app(self):
        """
        跳转到对应子系统应用的url
        :return:
        """
        return self.report_biz.go_app()

    def print_login(self):
        from dashboard_chart.services.third_party_service import ThirdPartyService
        third_party = ThirdPartyService(self.request, **{'type':'print'})
        return third_party.template_list()
