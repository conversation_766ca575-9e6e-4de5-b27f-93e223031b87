#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
from abc import ABCMeta, abstractmethod
import uuid
import logging
import falcon

import hug
from dmplib.hug import g

from base import repository
from base.errors import UserError
from base.enums import (
    DataReportType,
    ApplicationType,
    ProjectValueAddedFunc,
    DashboardType,
    DashboardStatus
)
from rbac.services.data_permissions import execute_function_with_new_permission, data_permission_edit_filter
from user_log.models import UserLogModel
from ppt.services.ppt_service import build_ppt_redirect_url

from dashboard_chart.models import DashboardModel, ReleaseModel
from dashboard_chart.services import (
    dashboard_service,
    released_dashboard_service,
    dashboard_extra_service
)
from dashboard_chart.repositories import dashboard_repository, chart_repository, dashboard_released_design_repository

from exports.services.biz_link_service import BizLinkService

logger = logging.getLogger(__name__)


class BaseService(metaclass=ABCMeta):

    def __init__(self, cur_application_type, request, **kwargs):
        self.cur_application_type = cur_application_type
        self.request = request
        self.kwargs = kwargs

    action_map = {
        "add": "添加",
        "copy": "复制",
        "update": "编辑",
        "rename": "重命名",
        "move": "移动",
        "release": "发布",
        "delete": "删除"
    }

    @abstractmethod
    def add(self):
        # 必须继承实现
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def rename(self):
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def delete(self):
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def copy(self):
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def go_app(self):
        raise NotImplementedError("Please Implement this method")

    @abstractmethod
    def release(self):
        raise NotImplementedError("Please Implement this method")

    def _set_common_params(self, kwargs):
        kwargs['application_type'] = self.cur_application_type

    @staticmethod
    def log_set(func):
        def warp(self, *args, **kwargs):
            rs = func(self, *args, **kwargs)
            func_name = func.__name__
            kw = self.kwargs
            dashboard_id = kw.get("id") or kw.get("dashboard_id") or kw.get("dash_id") or rs.get("id")
            action = f'{func_name}_dashboard'
            action_name = self.action_map.get(func_name) if self.action_map.get(func_name) else func_name
            # 日志记录
            UserLogModel.log_setting(
                request=self.request,
                log_data={
                    'action': action,
                    'id': dashboard_id,
                    'content': '{dashboard_type} [ {name} ] {action}成功'.format(
                        name=kw.get("name") or kw.get("dashboard_name"),
                        dashboard_type=dashboard_service.get_dashboard_type_word(kw.get("type")),
                        action=action_name
                    ),
                },
            )
            return rs
        return warp

    @execute_function_with_new_permission(dashboard_service.get_report_center_list, 'report_center-view')
    def get_dashboard_list(self):
        """
        报表中心列表
        :return:
        """
        kwargs = self.kwargs
        rs = dashboard_service.get_report_center_list(**kwargs)
        return rs

    @execute_function_with_new_permission(dashboard_service.search_dashboard, 'report_center-view')
    def search(self):
        """
        报表中心的搜索
        :return:
        """
        kwargs = self.kwargs
        kwargs["application_type"] = [ApplicationType.SimpleReport.value, ApplicationType.ActiveReport.value]
        kwargs["file_type"] = "dashboard"
        rs = dashboard_service.search_dashboard(**kwargs)
        return rs

    @execute_function_with_new_permission(dashboard_service.move_dashboard, 'report_center-edit')
    def move(self):
        """
        报表中心的报告，文件夹移动
        :return:
        """
        kwargs = self.kwargs
        rs = dashboard_service.move_dashboard(kwargs.get('dash_id'), kwargs.get('target_dash_id'))
        return rs

    @staticmethod
    @execute_function_with_new_permission(
        dashboard_service.get_report_permissions_from_report_center, 'report_center-view'
    )
    def get_report_permissions(report_id):
        # 获取报表中心报告的权限
        if g.code and g.account and not getattr(g, 'userid', None):
            g.userid = repository.get_data_scalar('user', conditions={'account': g.account}, col_name='id')
        application_type = repository.get_data_scalar(
            'dashboard', conditions={'id': report_id}, col_name='application_type'
        )
        if application_type not in [ApplicationType.SelfService.value, ApplicationType.ActiveReport.value]:
            raise UserError(message=f'暂时不支持当前类型【{application_type}】的报告！')
        data = dashboard_service.get_report_permissions_from_report_center(report_id=report_id)
        dashboard = data.get('tree') or []
        if not dashboard:
            return []
        else:
            permissions = dashboard[0].get('actions') or []
            permissions.append({'action_code': 'view', 'action_name': '查看'})
            return permissions


class SimpleReport(BaseService):

    def _set_self_service_params(self, kwargs):
        self._set_common_params(kwargs)
        kwargs['analysis_type'] = 'DATASET'
        kwargs['platform'] = 'pc'
        kwargs['theme'] = 'colorful_white'
        kwargs['is_show_mark_img'] = 1
        kwargs['layout'] = "{\"ratio\": \"16:9\",\"width\":1920,\"height\":1080,\"lattice\":10, \"mode\": \"grid\"}"
        kwargs['background'] = "{\"show\": true,\"color\":\"#EBEDF2\",\"image\":\"\",\"size\":\"stretch\"}"
        kwargs['create_type'] = 1
        kwargs['new_layout_type'] = 1

    @BaseService.log_set
    def add(self):
        """
        添加简单报表
        :return:
        """
        dashboard_service.license_control()
        # todo 权限
        # if kwargs.get('application_type') in [1, '1']:
        #     validator = PermissionValidator('self-service.edit')
        # else:
        #     validator = PermissionValidator('data-report.edit')
        # validator()
        kwargs = self.kwargs
        file_type = kwargs.get("type")
        # 如果是文件，则初始化默认数据。自助报表没有子报告
        if file_type == DataReportType.File.value:
            self._set_self_service_params(kwargs)
        kwargs["external_subject_ids"] = ','.join(kwargs.get("external_subject_ids", []))
        model = DashboardModel(**kwargs)
        dashboard_data = dashboard_service.add_dashboard(model, return_dict=True)
        return {'id': dashboard_data.get("id"), 'name': dashboard_data.get("name")}

    @BaseService.log_set
    @execute_function_with_new_permission(dashboard_service.update_dashboard_name, 'report_center-edit')
    def rename(self):
        kwargs = self.kwargs
        model = DashboardModel(**kwargs)
        dashboard_service.update_dashboard_name(model)
        return {"id": model.id}

    @BaseService.log_set
    @execute_function_with_new_permission(dashboard_service.delete_dashboard_by_dashboard_id, 'report_center-edit')
    def delete(self):
        kwargs = self.kwargs
        rs = dashboard_service.delete_dashboard_by_dashboard_id(kwargs.get("id"), kwargs.get('application_type'))
        return rs

    @BaseService.log_set
    @execute_function_with_new_permission(dashboard_service.copy_dashboard, 'report_center-copy')
    def copy(self):
        """
        报表中心的报告，文件夹移动
        :return:
        """
        kwargs = self.kwargs
        # 指定数据类型，用于日志记录
        self.kwargs["type"] = DataReportType.File.value
        parent_id = kwargs.get('target_id');
        if kwargs.get('target_id') == 'root':
            parent_id = ''
        rs, errors = dashboard_service.copy_dashboard(
            kwargs.get('dashboard_id'), parent_id, kwargs.get('dashboard_name'),
            "", 0
        )
        return rs, errors

    @BaseService.log_set
    @execute_function_with_new_permission(released_dashboard_service.release_with_process, 'report_center-edit')
    def release(self):
        """
        报表中心的报告发布
        :return:
        """
        kwargs = self.kwargs
        model = ReleaseModel(**kwargs)
        self.kwargs["type"] = DataReportType.File.value
        rs = released_dashboard_service.release_with_process(model)
        if rs:
            dashboard = dashboard_service.get_dashboard_info(model.id)
            self.kwargs["name"] = dashboard.get("name")
        return rs

    def go_app(self):
        kwargs = self.kwargs
        redirect_uri = kwargs.get("redirect_uri")
        return hug.redirect.to(redirect_uri, falcon.HTTP_302)


class ActiveReport(BaseService):
    """
    统计报告的系列操作的实现类
    """

    def __init__(self, cur_application_type, request, **kwargs):
        super().__init__(cur_application_type, request, **kwargs)
        self.active_report_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, g.code, g.userid)

    @staticmethod
    def _set_active_report_params(kwargs):
        kwargs['platform'] = 'pc'
        kwargs['theme'] = 'colorful_white'
        kwargs['is_show_mark_img'] = 1
        kwargs['create_type'] = 1
        kwargs['new_layout_type'] = 1

    @staticmethod
    def check_name_exists(model: DashboardModel):
        """
        检查是否存在重复的报告名称，文件夹名称
        :param model:
        :return:
        """
        if model.type == DashboardType.Folder.value:
            application_type = ApplicationType.SimpleReport.value
        else:
            application_type = ApplicationType.ActiveReport.value
        repeat_flag = dashboard_service.validate_repeat_name(model.name, model.parent_id, model.type,
                                                             application_type=application_type)
        if repeat_flag:
            raise UserError(message='存在重名文件或文件夹')
        return repeat_flag

    @staticmethod
    def check_rename_exists(model: DashboardModel):
        """
        文件，文件夹重命名的检查判断
        :param model:
        :return:
        """
        if model.type == DashboardType.Folder.value:
            application_type = ApplicationType.SimpleReport.value
        else:
            application_type = ApplicationType.ActiveReport.value
        is_exists = dashboard_service.validate_repeat_name(
            model.name,
            model.parent_id,
            model.type,
            ignore_flag=True,
            dashboard_id=model.id,
            application_type=application_type,
        )
        return is_exists

    @BaseService.log_set
    def add(self):
        kwargs = self.kwargs
        dashboard_type = kwargs.get("type")
        # 如果是文件，则初始化默认数据
        if dashboard_type in [DataReportType.File.value, DataReportType.CHILD_FILE.value]:
            self._set_active_report_params(kwargs)

        model = DashboardModel(**kwargs)
        self.check_folder_level(model)
        is_exists = self.check_name_exists(model)
        if not is_exists:
            parent_id = kwargs.get("parent_id")
            parent_type = DashboardType.Folder.value
            # 获取父层级的文件类型
            if parent_id:
                parent_dashboard = dashboard_service.get_dashboard(parent_id)
                if parent_dashboard:
                    parent_type = parent_dashboard.get("type")
            params = {"type": dashboard_type, "name": kwargs.get("name"), "parent_id": parent_id,
                      "parent_type": parent_type}
            rs = self.active_report_service.add_active_report(params)
            if rs:
                dashboard_id = rs.get("id")
                model.id = dashboard_id
                self.add_report_center(model)
                return {"id": dashboard_id, "name": model.name}

    @staticmethod
    def add_report_center(model):
        model.validate()
        data = model.get_dict(DashboardModel.fields)
        if model.parent_id is None:
            model.parent_id = ''
            data['parent_id'] = ''

        # 报告类型
        if model.type in [DataReportType.File.value, DataReportType.CHILD_FILE.value]:
            data['application_type'] = ApplicationType.ActiveReport.value
        else:
            data['application_type'] = ApplicationType.SimpleReport.value

        data['level_code'] = dashboard_service.generate_level_code(model.parent_id)
        data['biz_code'] = uuid.uuid4().__str__().replace('-', '')

        repository.add_data('dashboard', data)
        if model.type in [DataReportType.File.value, DataReportType.CHILD_FILE.value]:
            chart_repository.update_dashboard_modified_time(model.id)

    @staticmethod
    def check_folder_level(model: DashboardModel):
        """
        检查文件夹的层级
        :param model:
        :return:
        """
        # 判断第几级别，最多创建10级文件夹
        if model.parent_id:
            parent_dashboard = dashboard_repository.get_dashboard(model.parent_id)
            if (
                    parent_dashboard
                    and len(parent_dashboard['level_code'].split('-')) > 10
                    and model.type == DashboardType.Folder.value
            ):
                raise UserError(message='最多创建10级文件夹')

    @BaseService.log_set
    @data_permission_edit_filter('report_center-edit')
    def rename(self):
        kwargs = self.kwargs
        model = DashboardModel(**kwargs)
        model.validate()
        # 检查是否重命名
        is_exists = self.check_rename_exists(model)
        if is_exists:
            raise UserError(message='存在重名文件或文件夹')
        # 请求统计报表接口
        params = {"id": kwargs.get("id"), "type": kwargs.get("type"), "name": kwargs.get("name"),
                  "parent_id": kwargs.get("parent_id")}
        rs = self.active_report_service.rename_active_report(params)
        if rs:
            self.rename_report_center(model)
            return {"id": model.id}

    @staticmethod
    def rename_report_center(model):
        """
        重命名报表中心报告名称
        :param model:
        :return:
        """
        fields = ['name']
        # 登记当前报告的编辑时间
        dashboard_extra_service.update_dashboard_edit_on(model.id)
        return repository.update_model('dashboard', model, {'id': model.id}, fields)

    @BaseService.log_set
    @execute_function_with_new_permission(dashboard_service.delete_dashboard_by_dashboard_id, 'report_center-edit')
    @data_permission_edit_filter('report_center-edit')
    def delete(self):
        kwargs = self.kwargs
        dashboard_id = kwargs.get("id")
        application_type = [ApplicationType.SimpleReport.value, ApplicationType.ActiveReport.value]
        dashboard = dashboard_service.get_dashboard(dashboard_id)
        if not dashboard:
            raise UserError(message="报告不存在，删除错误")
        self.check_delete(dashboard, application_type)

        # 删除
        params = {"id": dashboard_id, "type": dashboard.get("type")}
        rs = self.active_report_service.delete_active_report(params)
        if rs:
            delete_rs = dashboard_service.delete_dashboard_by_dashboard_id(kwargs.get("id"), application_type)
            return delete_rs

    @staticmethod
    def check_delete(dashboard_data, application_type):
        if dashboard_data.get("status") == DashboardStatus.Released.value:
            raise UserError(message="报告已发布，请先取消报告发布再执行删除")
        # 删除文件夹的情况
        if dashboard_data.get("type") == DashboardType.Folder.value:
            list_data = dashboard_repository.get_dash_list_by_group_id(**{"parent_id": dashboard_data.get("id"),
                                                                          "application_type": application_type})
            if list_data:
                raise UserError(message="此文件夹下还有未删除的文件夹或报告，请先删除后再删除此文件夹")

    @BaseService.log_set
    @data_permission_edit_filter('report_center-copy')
    def copy(self):
        # 移动
        errs = []
        data = {}
        try:
            kwargs = self.kwargs
            dashboard_id = kwargs.get("dashboard_id")
            dashboard = dashboard_service.get_dashboard(dashboard_id)
            if not dashboard:
                raise UserError(message="报告不存在，不能复制")

            parent_id = kwargs.get("target_id")

            # 检查名称
            dashboard_name = self.check_copy_name(dashboard, kwargs.get("dashboard_name"), parent_id)
            if dashboard.get("type") == DataReportType.CHILD_FILE.value:
                params = {"dashboard_id": dashboard_id, "target_id": '', "dashboard_name": dashboard_name}
            else:
                params = {"dashboard_id": dashboard_id, "target_id": parent_id, "dashboard_name": dashboard_name}
            report_list = self.active_report_service.copy_active_report(params)
            if report_list:
                new_dashboard_id = self.add_copy_dashboard(report_list, dashboard, parent_id)
                chart_repository.update_dashboard_modified_time(dashboard_id)
                # 返回新的报告信息
                data = {"id": new_dashboard_id, "parent_id": parent_id, "name": dashboard_name}
        except Exception as e:
            errs.append(str(e))
        return data, errs

    def add_copy_dashboard(self, report_list, dashboard, parent_id):
        """
        报表中心-统计报表复制
        :param report_list:
        :param dashboard:
        :param parent_id:
        :return:
        """
        dashboard_type = dashboard.get("type")
        application_type = ApplicationType.ActiveReport.value

        # 指定数据类型，用于日志记录
        self.kwargs["type"] = DataReportType.File.value
        new_dashboard_id = ""
        for item in report_list:
            model_data = {
                "id": item.get("id"),
                "name": item.get("name"),
                "parent_id": item.get("parent_id"),
                "created_on": item.get("created_time")
            }
            # 复制的报告数据初始化
            self._set_active_report_params(model_data)
            model = DashboardModel(**model_data)
            # 报告复制或子报告复制
            if model.parent_id == parent_id:
                if dashboard_type == DataReportType.File.value:
                    model.type = DataReportType.File.value
                else:
                    model.type = DataReportType.CHILD_FILE.value
                # 复制后新的报告id（主报告id或子报告根id）
                new_dashboard_id = model.id
                # 报告复制来源关系维护（这个功能没上线）
                # model.copy_from_id = dashboard_id
                # model.copy_source_id = dashboard.get("copy_source_id")
            else:
                model.type = DataReportType.CHILD_FILE.value
            model.application_type = application_type
            self.add_dashboard_by_copy(model)
        return new_dashboard_id

    @staticmethod
    def add_dashboard_by_copy(model):
        """
        添加报告以及子报告
        :param model:
        :return:
        """
        field_list = DashboardModel.fields
        # field_list.extend(['copy_from_id', 'copy_source_id'])
        data = model.get_dict(field_list)
        if model.parent_id is None:
            model.parent_id = ''
            data['parent_id'] = ''
        data['level_code'] = dashboard_service.generate_level_code(model.parent_id)
        data['biz_code'] = uuid.uuid4().__str__().replace('-', '')
        return repository.add_data('dashboard', data)

    @staticmethod
    def check_copy_name(dashboard, dashboard_name, parent_id):
        count = 0
        while True:
            repeat_flag = dashboard_service.validate_repeat_name(
                dashboard_name, parent_id, dashboard.get('type'), application_type=dashboard.get('application_type')
            )
            if repeat_flag and count >= 10:
                raise UserError(message='存在重名的报告：' + dashboard_name)
            if not repeat_flag:
                break
            count += 1
            dashboard_name += '_副本'
        return dashboard_name

    @BaseService.log_set
    @execute_function_with_new_permission(dashboard_service.move_dashboard, 'report_center-edit')
    @data_permission_edit_filter('report_center-edit')
    def move(self):
        """
        统计报告移动
        :return:
        """
        kwargs = self.kwargs
        dashboard_id = kwargs.get("dash_id")
        dashboard = dashboard_service.get_dashboard(dashboard_id)
        if not dashboard:
            raise UserError(message="报告或文件夹不存在，不能移动")

        # 移动目标id
        target_dash_id = kwargs.get("target_dash_id")
        # 根目录节点特别处理
        if target_dash_id == '':
            target_dash_id = 'root'
        params = {"dash_id": dashboard_id, "type": dashboard.get("type"),
                  "target_dash_id": kwargs.get("target_dash_id")}
        rs = self.active_report_service.move_active_report(params)
        if rs:
            move_rs = dashboard_service.move_dashboard(kwargs.get('dash_id'), target_dash_id)
            return move_rs

    @BaseService.log_set
    @data_permission_edit_filter('report_center-edit')
    def release(self):
        """
        报告发布
        :return:
        """
        kwargs = self.kwargs
        dashboard_id = kwargs.get("id")
        dashboard = dashboard_service.get_dashboard(dashboard_id)
        if not dashboard:
            raise UserError(message="报告不存在，不能发布")
        # 发布
        model = ReleaseModel(**kwargs)
        params = {"id": dashboard_id, "type_access_released": model.type_access_released}
        rs = self.active_report_service.release_active_report(params)
        if rs:
            self.kwargs["type"] = DataReportType.File.value
            self.kwargs["name"] = dashboard.get("name")
            # 获取所有的子报告，更新报告状态为发布，不需要真实发布
            all_dashboard = dashboard_service.get_child_dashboard(dashboard_id, with_deleted=True)
            for dashboard in all_dashboard:
                dashboard_status = {
                    "status": model.status,
                    "type_access_released": model.type_access_released,
                }
                repository.update("dashboard", dashboard_status, {"id": dashboard.get("id")})
            # 取消发布的情况，需要清理dashboard_extra的登记时间
            if int(model.status) == DashboardStatus.Drafted.value:
                dashboard_extra_service.reset_edit_and_released_on(model.id)
            # 报告发布的情况，需要登记当前报告的发布时间
            elif int(model.status) == DashboardStatus.Released.value:
                dashboard_extra_service.reset_edit_or_released_on(model.id, "edit_on")
                dashboard_extra_service.update_dashboard_released_on(model.id)
            return True

    def go_app(self):
        """
        跳转到统计报告后台
        :return:
        """
        kwargs = self.kwargs
        redirect_uri = kwargs.get('redirect_uri')
        if redirect_uri is None:
            redirect_uri = ''
        url = build_ppt_redirect_url("/login_by_jwt", from_type='active_report', params={'redirect_uri': redirect_uri},
                                     backend=True)
        return hug.redirect.to(url, falcon.HTTP_302)

    def callback_log(func):
        def warp(self, *args, **kwargs):
            rs = func(self, *args, **kwargs)
            func_name = func.__name__
            logger.error(f"统计报表请求接口：{func_name}")
            logger.error(f"统计报表请求参数：{json.dumps(self.kwargs, ensure_ascii=False)}")
            logger.error(f"统计报表请求结果：{json.dumps(rs, ensure_ascii=False)}")
            return rs
        return warp

    @callback_log
    def check_name(self):
        """
        检查报告，子报告的名称是否重复
        :return:
        """
        kwargs = self.kwargs
        data = {
            "id": kwargs.get("id"),
            "parent_id": kwargs.get("parent_id"),
            "type": kwargs.get("type"),
            "name": kwargs.get("name")
        }
        model = DashboardModel(**data)
        is_exists = self.check_rename_exists(model)
        data = {"is_exists": is_exists}
        return data

    @callback_log
    def callback_update(self):
        """
        更新报告的名称，状态，更新时间
        :return:
        """
        kwargs = self.kwargs
        dashboard_id = kwargs.get("id")
        dashboard = dashboard_service.get_dashboard(dashboard_id)
        if not dashboard:
            raise UserError(message=f"报告不存在，请重试（id:{dashboard_id}）")
        data = {
            "id": dashboard_id,
            "name": kwargs.get("name"),
            "status": kwargs.get("status"),
            "modified_on": kwargs.get("modified_on"),
        }
        data["type"] = dashboard_type = dashboard.get("type")
        model = DashboardModel(**data)
        model.validate()
        if not dashboard:
            raise UserError(message='数据看板不存在')

        fields = ['name', 'status', 'modified_on']
        repository.update_model('dashboard', model, {'id': model.id}, fields)
        root_id = None
        if dashboard_type == DataReportType.File.value:
            root_id = model.id
        elif dashboard_type == DashboardType.CHILD_FILE.value:
            root_dashboard = dashboard_released_design_repository. \
                get_root_dashboard_by_level_code(dashboard.get("level_code"))
            root_id = root_dashboard.get("id")
        if root_id:
            modified_on = kwargs.get("modified_on")
            dashboard_extra_service.update_dashboard_edit_on(root_id, modified_on)
        return data

    @callback_log
    def move_dashboard(self):
        """
        报表移动到其他报告
        :return:
        """
        kwargs = self.kwargs
        move_rs = dashboard_service.move_dashboard(kwargs.get('dash_id'), kwargs.get('target_dash_id'))
        return move_rs
