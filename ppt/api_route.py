#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@author: <EMAIL>
@time: 2021/7/26 14:19
"""
import falcon
import hug
from hug import redirect
from hug.routing import U<PERSON><PERSON><PERSON><PERSON>
import functools

from dmplib.hug import APIWrapper
from dmplib.hug import g
from dmplib.utils.errors import UserError
from base.enums import AddFuncType
from components.storage_setting import get_storage_type
from dashboard_chart.services import dashboard_service
from ppt.services import ppt_service
from ppt import external_service
from rbac.services import func_auth_service
from user.services.ingrate_service import auto_login
# from dashboard_chart_embedded.services.token_services import embedded_token_authenticator as ppt_authenticator
# from dashboard_chart_embedded.api_route import EmbeddedAPIWrapper as PPTAPIWrapper

from exports.services import export_service
from imports.services import dashboard_import_data_services
from report_center.services.report_service import BizAppReportService
from user.services.user_service import filter_func, get_cur_user_id
from user_log.models import ReportVisitLogModel
from user_log.services import report_visit_log_service

api = APIWrapper(__name__)

class PPTAPIWrapper(APIWrapper):
    __slots__ = ["_route", "_ppt_route"]

    def __init__(self, name: str) -> None:
        super().__init__(name)
        self._route = hug.http(api=self.api)
        self._ppt_route = None

    @property
    def embedded_route(self) -> URLRouter:
        if not self._ppt_route:
            # pylint: disable=E1120
            self._ppt_route = hug.http(api=self.api, requires=None)
        return self._ppt_route


def ppt_authenticator(func):

    @functools.wraps(func)
    def wrapper(request, response, *args, **kwargs):
        # jwt验证
        token = request.get_param('token')
        data = ppt_service.token_decode(token)

        # 获取报告发布方式
        release_type = kwargs.get("release_type", 2)
        report_id = kwargs.get("report_id", '')
        report_type = kwargs.get("report_type", '')
        # 0:不限制,1:通过密码,2:限制用户 3:三方控制 4:角色控制
        if release_type not in [2, 3, 4, '2', '3', '4']:
            raise UserError(message="release_type参数错误")

        # 免登
        auto_login(
            request,
            response,
            report_id,
            data.get("code"),
            data.get("account"),
            data.get("user_id"),
            int(release_type),
            report_type=report_type,
            is_developer=data.get("is_developer"),
            extra=data.get("extra")
        )
        result = func(request, response, *args, **kwargs)
        return result

    return wrapper


@api.admin_route.get("/page")
def ppt_page(**kwargs):
    """
    会议报告
    :param kwargs:
    :return:
    """
    route = kwargs.get("route")
    from_type = kwargs.get('from_type', 'ppt')
    if not route:
        raise UserError(message="缺少参数route")
    url = ppt_service.build_ppt_redirect_url(route, from_type)
    return redirect.to(url, falcon.HTTP_302)


ppt_api = PPTAPIWrapper(__name__)


@ppt_api.embedded_route.get("/report/third_app/list")
@ppt_authenticator
def get_report_third_app_list(request, response, **kwargs):
    """
    获取第三方渠道列表
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    report_type = kwargs.get("report_type")
    report_id = kwargs.get("report_id")

    if report_type not in [AddFuncType.Ppt.value, AddFuncType.ActiveReport.value]:
        raise UserError(code=5007, message='report_type参数错误')
    if not report_id:
        raise UserError(code=5008, message='缺少report_id参数')

    results = ppt_service.get_third_app_list(report_id, report_type)

    return True, "", results


@ppt_api.embedded_route.get("/app/get_redirect_url")
@ppt_authenticator
def get_redirect_url(request, response, **kwargs):
    """
    获取默认url
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    report_type = kwargs.get("report_type")
    report_id = kwargs.get("report_id")

    if report_type not in [AddFuncType.Ppt.value, AddFuncType.ActiveReport.value]:
        raise UserError(code=5007, message='report_type参数错误')
    if not report_id:
        raise UserError(code=5008, message='缺少report_id参数')

    results = ppt_service.get_custom_redirect_url(report_id, report_type)

    return True, "", results


@ppt_api.embedded_route.post("/dashboard_chart/chart/data")
@ppt_authenticator
def chart_data(request, response, **kwargs):
    """
    取数接口
    :param request:
    :param response:
    :param kwargs: {
       token: "",
       chart_params: [{}]
    }
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    setattr(g, "storage", get_storage_type(g.code))

    results = ppt_service.batch_get_chart_data(kwargs.get("chart_params"))

    return True, "", results


@ppt_api.embedded_route.post("/dashboard_chart/chart/get_total")
@ppt_authenticator
def get_total(request, response, **kwargs):
    """
    get total
    """
    cookie = request.cookies
    g.cookie = cookie
    setattr(g, "storage", get_storage_type(g.code))

    results = ppt_service.get_total(kwargs.get("chart_params"))

    return True, "", results


@ppt_api.embedded_route.get("/dataset/tree_with_filterable")
@ppt_authenticator
def tree_with_filterable(request, response, **kwargs):
    """
    获取带是否可过滤字段的数据集树
    :return:
    """
    parent_id = kwargs.get('parent_id')
    exclude_types = kwargs.get('exclude_types')
    start_time = kwargs.get('begin_time', None)
    end_time = kwargs.get('end_time', None)
    return True, '获取成功', external_service.get_dataset_tree(parent_id, exclude_types, start_time=start_time, end_time=end_time, with_filterable=True)


@ppt_api.embedded_route.get("/datasets_of_dashboard")
@ppt_authenticator
def tree_with_filterable(request, response, **kwargs):
    """
    根据dashboard_id获取当前报告及其子报告 所使用的数据集
    :return:
    """
    from dashboard_chart.repositories import external_dashboard_repository

    dashboard_id = kwargs.get("dashboard_id")
    if not dashboard_id:
        raise UserError(message="缺少dashboard_id参数")
    return True, 'ok', external_dashboard_repository.get_dataset_of_dashboard_id(dashboard_id)


@ppt_api.embedded_route.get("/dataset/field_group")
@ppt_authenticator
def get_field_group(request, response, **kwargs):
    res = external_service.get_field_group(kwargs.get('dataset_id'))
    return True, '获取成功', res


@ppt_api.embedded_route.get("/dataset/dataset_field/get")
@ppt_authenticator
def get_dataset_field(request, response, **kwargs):
    """
     @apiVersion 1.0.0
     @api {get} /api/dataset/dataset_field/get 获取数据集字段信息及变量信息
     @apiParam query {string}  dataset_id 数据集ID
     @apiParam query {number}  is_not_category  是否需要分类
     @apiParam query {number}  include_dataset_vars  是否包含数据集变量
     @apiGroup  dataset
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": {
            "group_type{度量|维度|变量}": [
                {
                    "id": "变量ID",
                    "name": "变量名称",
                    "description": "变量描述",
                    "var_type": "数据类型, 1:文本 2:日期 3:数值",
                    "value_type": "值类型 1: 列表 2: 任意值 3: 区间",
                    "default_value": "默认值 a,b",
                    "dataset_id": "所属数据集ID",
                    "include_vars": ["变量1", "变量2"]
                }
            ]
         }
    }
    """
    # 高级字段保存时，在带有单引号的输入框值里面强行加入了双斜杠，返回给前端时需要去除双斜杠
    dataset_fields = external_service.get_dataset_field(kwargs.get('dataset_id'), kwargs.get('is_not_category'), kwargs.get("include_dataset_vars"))
    if not dataset_fields:
        dataset_fields = []
    if not kwargs.get('is_not_category'):
        for k, v in dataset_fields.items():
            dataset_fields[k] = external_service.del_diagonal(v)
    else:
        dataset_fields = external_service.del_diagonal(dataset_fields)

    return True, '获取成功', dataset_fields


@ppt_api.embedded_route.get("/dataset")
@ppt_authenticator
def get_dataset(request, response, **kwargs):
    """
    获取数据集
    """
    from dataset.services import dataset_service

    return True, '获取成功', dataset_service.get_dataset(kwargs.get('id'))


@ppt_api.embedded_route.post("/user_log/save")
@ppt_authenticator
def save_log(request, response, **kwargs):
    """
    在线报告操作日志保存
    :return:
    """
    return True, '保存日志成功', ppt_service.save_log(kwargs)


@ppt_api.embedded_route.get("/user_group/user_list")
@ppt_authenticator
def user_list_of_group(request, response, **kwargs):
    """
    根据组织ID和用户名、邮箱查询用户列表
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    from user_group.services import user_group_service
    from user.models import UserQueryModel

    return True, '', user_group_service.get_user_list(UserQueryModel(**kwargs)).get_result_dict()


@ppt_api.embedded_route.get("/user_group/user_source_list")
@ppt_authenticator
def user_source_list(request, response, **kwargs):
    """
    查询组织树
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    from user_group.services import user_group_service
    list_data = user_group_service.get_user_group_tree()
    return True, '', list_data


@ppt_api.embedded_route.get("/rbac/role_group")
@ppt_authenticator
def rbac_role_group(request, response, **kwargs):
    """
    角色组
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    from user_role_group.services import user_role_group_service
    return True, '', user_role_group_service.get_user_role_group_tree(**kwargs)


@ppt_api.embedded_route.get("/user/profile")
@ppt_authenticator
def user_profile(request, response, **kwargs):
    """
    用户信息
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    from user.services import user_service
    return True, '', user_service.get_user_profile(request, response=response)


@ppt_api.embedded_route.post("/export/callback")
@ppt_authenticator
def export_callback(request, response, **kwargs):
    """
    在线报告，明细报告导出后的回调接口
    :param request:
    :param response:
    :param kwargs: {
       token: "",
       chart_params: [{}]
    }
    :return:
    """
    if 'token' in kwargs:
        del kwargs['token']
    results = export_service.export_callback(**kwargs)
    return True, "导出回调成功", results


@ppt_api.embedded_route.post("/import/callback")
@ppt_authenticator
def export_callback(request, response, **kwargs):
    """
    在线报告，明细报告导入后的回调接口
    :param request:
    :param response:
    :param kwargs: {
       token: "",
       chart_params: [{}]
    }
    :return:
    """
    if 'token' in kwargs:
        del kwargs['token']
    results = dashboard_import_data_services.import_callback(**kwargs)
    return True, "导入回调成功", results


@ppt_api.embedded_route.post("/report_center/check_name")
@ppt_authenticator
def report_center_check_name(request, response, **kwargs):
    """
    统计报告的报告重命名检查
    :param request:
    :param response:
    :param kwargs: {
        "id": "3a070bea-786b-2b5a-2c87-c51152fc4192",
        "name": "新建报告",
        "type": 'FILE',
        "parent_id": "123123123"
    }
    :return:
    """
    active_report = BizAppReportService.get_active_report_instance(request, **kwargs)
    results = active_report.check_name()
    return True, "名称检查回调成功", results


@ppt_api.embedded_route.post("/report_center/update")
@ppt_authenticator
def report_center_update(request, response, **kwargs):
    """
    统计报告的报告更新回调接口
    :param request:
    :param response:
    :param kwargs: {
        "id": "3a070bea-786b-2b5a-2c87-c51152fc4192",
        "name": "新建报告",
        "status": 1,
        "modified_on": "2022-10-25 10:40:11"
    }
    :return:
    """
    active_report = BizAppReportService.get_active_report_instance(request, **kwargs)
    results = active_report.callback_update()
    return True, "更新回调成功", results


@ppt_api.embedded_route.post("/report_center/move")
@ppt_authenticator
def report_center_move(request, response, **kwargs):
    """
    统计报告的子报告移动接口
    :param request:
    :param response:
    :param kwargs: {
        "dash_id": "3a070bea-786b-2b5a-2c87-c51152fc4192",
        "target_dash_id": "3a070bea-786b-2b5a-2c87-c51152fc4111"
    }
    :return:
    """
    active_report = BizAppReportService.get_active_report_instance(request, **kwargs)
    results = active_report.move_dashboard()
    return True, "移动成功", results


@ppt_api.embedded_route.post("/report_center/dashboard/permissions")
@ppt_authenticator
def report_center_permissions(request, response, **kwargs):
    """
    统计报告的子报告移动接口
    :param request:
    :param response:
    :param kwargs: {
        "id": "3a070bea-786b-2b5a-2c87-c51152fc4192"
    }
    :return:
    """
    permissions = BizAppReportService.get_report_permissions(kwargs)
    return True, "ok", permissions


@ppt_api.embedded_route.get("/func_map")
@ppt_authenticator
def all_func_map(request, response, **kwargs):
    """
    获取功能权限
    params:{"func_code":"功能code"}  func_code如果不存在，则返回所有功能的权限
    """
    user_id = get_cur_user_id()
    user_group_ids = g.group_ids
    funcs_set = func_auth_service.get_user_funcs_set(user_id, user_group_ids) or {}
    funcs_set = filter_func(funcs_set)
    func_code = kwargs.get("func_code") or ""
    if func_code:
        return True, "ok", {func_code: funcs_set.get(func_code) or {}}
    return True, "ok", funcs_set


@ppt_api.embedded_route.get("/try_get_erp_image_url")
@ppt_authenticator
def try_get_erp_image_url(request, response, **kwargs):
    print(g.code)
    image_id = kwargs.get('image_id', '')
    dataset_id = kwargs.get('dataset_id', '')
    if not image_id:
        raise UserError(message='图片的guid不能为空')
    url = dashboard_service.try_get_erp_image_url(image_id, dataset_id=dataset_id)
    return True, '', url


@ppt_api.embedded_route.post("/set_visit_log")
@ppt_authenticator
def set_visit_log(request, response, **kwargs):
    if not g.code:
        return False, '租户code不能为空', ''
    model = ReportVisitLogModel(**kwargs)
    model.account = model.account or request.cookies.get('account', '')
    return True, '', report_visit_log_service.write_visit_log(model)
