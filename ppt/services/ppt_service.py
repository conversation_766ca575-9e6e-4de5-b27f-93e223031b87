import time

import jwt
from gevent.pool import Pool
from typing import List, Dict, Any, Iterable
import json
from urllib.parse import urlparse, urlunparse, urlencode

from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from base import repository
from base.enums import AddFuncType, ProjectValueAddedFunc
from components.url import url_add_param
from dashboard_chart.utils.common import add_api_dataset_params
from ppt.repositories import ppt_repository

pool = Pool(500)


def get_jwt_token(jwt_secret=None, extra=None):
    user = repository.get_data("user", conditions={"id": getattr(g, 'userid', '')}) or {}
    user_name = user.get("name", "")
    g_name = getattr(g, 'name', '')
    if g_name:
        name = g_name
    else:
        name = user_name
    if not name:
        name = 'admin'
    data = {
        "user_id": getattr(g, 'userid', ''),
        "code": g.code,
        "account": getattr(g, 'account', ''),
        "name": name,
        "is_developer": getattr(g, 'is_developer', 0),
        "extra": extra or {}
    }
    jwt_secret = jwt_secret or config.get('PPT.jwt_secret')
    return jwt.encode(data, jwt_secret)


def token_decode(token):
    if not token:
        raise UserError(message="'缺少签名'")
    try:
        jwt_secret = config.get('PPT.sso_secret')
        _alg = 'HS256'
        data = jwt.decode(token, jwt_secret, algorithms=_alg)
    except Exception as e:
        raise UserError(message="无效的签名:{}".format(str(e)))
    return data


def build_ppt_redirect_url(route: str, from_type='ppt', params: dict = None, extra=None, backend=True):
    """
    构造ppt重定向url
    :param route:
    :param from_type:
    :param params:
    :param extra:
    :param backend:
    :return:
    """
    domain = ''
    if from_type == AddFuncType.Ppt.value:
        domain = config.get('PPT.domain')
    elif from_type == AddFuncType.ActiveReport.value:
        if backend:
            domain = config.get('PPT.active_report_domain')
        else:
            domain = config.get('PPT.active_report_domain_front')
    elif from_type == AddFuncType.ERPReport.value:
        from ppt.external_service import get_erp_report_site_url
        front_url, backend_url = get_erp_report_site_url()
        if backend:
            domain = backend_url
        else:
            domain = front_url
    if not domain:
        raise UserError(message="from_type参数错误 or domain未配置")
    if not params:
        params = dict()
    params.update({"token": get_jwt_token(extra=extra)})

    if "id" in params:
        if from_type == AddFuncType.Ppt.value:
            params['returnUrl'] = f'/share/{g.code}/{params["id"]}'
        else:
            params['redirect_uri'] = f'/report/{params["id"]}/preview'
        del params['id']

    return url_add_param(f"{domain}{route}", params)


def get_total(chart_params):
    """
    get total
    """
    result = batch_get_chart_data(chart_params)
    total_result = {}
    for k, v in result.items():
        total_result[k] = len(v.get("data", []))
    return total_result


def batch_get_chart_data(chart_params: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    批量获取数据
    :param chart_params:
    :return:
    """
    from dashboard_chart.services.chart_service import handle_g
    from dataset.external_query_service import get_dataset_data

    results = {}
    if not chart_params or not isinstance(chart_params, Iterable):
        return results

    def single_chart_result(params, result):
        chart_id = params.get("id")
        dataset_id = params.get("dataset_id")
        query_json = params.get("query_json")
        if any([chart_id, dataset_id, query_json]) is False:
            raise UserError(message="【缺少必要参数】：id, dataset_id, query_json")

        params.update(dict(
            user_id=g.userid,
            chart_id=chart_id,
            query_structure_json=json.dumps(query_json),
            is_order_master_id=params.get("is_order_master_id", False),
            dataset_version=params.get("dataset_version"),
            external_subject_ids=params.get("external_subject_ids", []),
            table_name=params.get("table_name")
        ))
        setattr(g, 'query_chart_data_source_ppt', '1')
        add_api_dataset_params(g, dataset_id=dataset_id)
        result[chart_id] = get_dataset_data(**params)
        return param.get("id")

    for param in chart_params:
        get_chart_data = handle_g(single_chart_result)
        external_params = getattr(g, 'external_params', None)
        cookie = getattr(g, 'cookie', None)
        customize_roles = getattr(g, 'customize_roles', [])
        external_user_id = getattr(g, 'external_user_id', None)
        is_developer = getattr(g, 'is_developer', 0)
        pool.spawn(
            get_chart_data,
            param,
            results,
            code=g.code,
            account=g.account,
            userid=g.userid,
            cookie=cookie,
            is_developer=is_developer,
            external_params=external_params,
            customize_roles=customize_roles,
            external_user_id=external_user_id,
        )
    pool.join()
    return results


def save_log(log_data):
    from user_log.models import UserOperationLogModel
    if log_data:
        user_log_model = UserOperationLogModel(**log_data)
        repository.add_model('user_operation_log', user_log_model)


def get_third_app_list(report_id, report_type):
    """
    获取渠道列表
    :param report_id:
    :param report_type:
    :return:
    """
    from integrate.services.third_party_service import get_third_party_and_app_list, set_third_party_app_redirect_url

    # get third app
    dict_list = get_third_party_and_app_list()

    # get dmp url
    normal_share_url = build_ppt_redirect_url("/login_by_jwt", from_type=report_type, params={'id': report_id},
                                              backend=False)

    # get custom url
    new_redirect_url = get_custom_redirect_url(report_id, report_type)

    return_data = set_third_party_app_redirect_url(dict_list, new_redirect_url, normal_share_url)
    return return_data


def get_custom_redirect_url(report_id, report_type):
    """
    get url
    :param report_id:
    :param report_type:
    :return:
    """
    # get dmp url
    normal_share_url = build_ppt_redirect_url("/login_by_jwt", from_type=report_type, params={'id': report_id},
                                              backend=False)

    # get custom url
    base_redirect_url = config.get("ThirdParty.base_redirect_url", "")

    if not base_redirect_url:
        return normal_share_url

    # build biz_code
    if report_type == AddFuncType.Ppt.value:
        biz_code = f"ppt_{report_id}"
    else:
        biz_code = f"ar_{report_id}"

    # 拼接后的url请求参数
    parsed_url = urlparse(base_redirect_url)

    extend_url_params = urlencode({"biz_code": biz_code, "code": g.code})

    new_query_params = parsed_url.query + "&" + extend_url_params if parsed_url.query else extend_url_params

    # 重新组装url
    new_redirect_url = urlunparse(
        [
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query_params,
            parsed_url.fragment,
        ]
    )
    return new_redirect_url


def get_related_ppt_by_dataset_id(dataset_id):
    reports = ppt_repository.get_open_data_relate_dataset(dataset_id)
    for report in reports:
        report['type'] = ProjectValueAddedFunc.PPT.value
    return reports
