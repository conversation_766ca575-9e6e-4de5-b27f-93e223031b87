from dmplib.saas.project import get_db
import dmplib.db.errors


def get_open_data_relate_dataset(dataset_id):
    sql = """select t.id,t.name,t.update_time as modified_on,t_report_ppt_dir.name as folder 
    from (select id,name,dir_id,update_time from t_report_ppt 
    where id in (select report_ppt_id from t_report_ppt_shape where properties like %(dataset_id)s)) as t 
    left join t_report_ppt_dir on t.dir_id=t_report_ppt_dir.id
    """
    params = {
        "dataset_id": '%' + dataset_id + '%'
    }
    with get_db() as db:
        try:
            return db.query(sql, params)
        except dmplib.db.errors.TableNotFoundError:
            return []
