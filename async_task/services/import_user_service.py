#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2018/7/20.
"""
import os
import random
import re
import string
from _sha1 import sha1

import bcrypt

from async_task.services.read_excel import ReadExcel
from base import repository
from base.enums import AccountMode, EmailTemplateType
from components import mail
from components.oss import OSSFileProxy
from dataset.models import DatasetExcelResultModel
from dmplib import redis
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from user.models import UserExcelModel
from user.repositories import user_repository
from user.services import user_service
from user_group.repositories import user_group_repository


class UserExcelService(ReadExcel):
    """
    用户Excel业务类
    """

    def __init__(self, project_code=None, account=None, oss_url=None):
        super().__init__(project_code, account, oss_url)
        # 用户组列出限制：6列（姓名、账号、手机号、邮箱地址、所属组织ID、所属组织名称）
        self.columns_limit = 6

    def async_process_data(self, task_id, user_source_id, is_preview=1):
        result = DatasetExcelResultModel()
        try:
            result.task_id = task_id
            infos, count, invalid_count = self.start_import(user_source_id, is_preview)
            if isinstance(infos, str):
                result.info = infos
            else:
                # redis存储需要将model转换为dict
                new_infos = []
                for info in infos:
                    new_infos.append(info.get_dict())
                result.info = new_infos
            result.status = 1
            result.count = count
            result.invalid_count = invalid_count
            redis.conn().set_data(task_id, result.get_dict())
        except Exception as ex:
            error_msg = "处理用戶excel失败，错误内容：" + str(ex)
            result.error_msg = error_msg
            result.status = 1
            redis.conn().set_data(task_id, result.get_dict())
        finally:
            if self.file_path and os.path.exists(self.file_path):
                os.remove(self.file_path)
            if not is_preview:
                OSSFileProxy().delete(self.oss_url, is_url=True)
        return task_id

    def start_import(self, user_source_id, is_preview=1):
        user_source = user_group_repository.get_user_soruce_by_id(user_source_id)
        if not user_source:
            raise UserError(message='用户渠道不存在，请刷新页面再试')
        # 处理excel数据
        self.file_path = self.download_oss_file()
        sheet = self.read_data()
        count = sheet.nrows - 2
        user_excel_models, all_user_excel_models, error_models = self.process_data(sheet)
        # 预览数据直接返回前100条
        if is_preview == 1:
            if len(error_models) > 0:
                return error_models, count, len(error_models)
            return all_user_excel_models[:100], count, len(error_models)
        # 写入user表
        insert_user_models = self.write_data(user_source, user_excel_models, all_user_excel_models)
        # 发送邮件
        names = []
        for user_model in insert_user_models:
            self.send_email(user_model)
            names.append(user_model.name)
        # 避免写日志出错影响主流程
        try:
            from user_log.models import UserLogModel

            UserLogModel.log_setting(
                request=object(),
                log_data={'action': 'add_user', 'id': "", 'content': '添加用户 [ {name} ] '.format(name='、'.join(names))},
            )
        except:
            pass
        return "成功导入{}条数据。".format(len(user_excel_models)), count, len(error_models)

    def validation_head_data(self, head_datas):
        """
        校验excel头部合法性
        :return:
        """
        if len(head_datas) < self.columns_limit:
            raise UserError(message='文件内容必须包含姓名、账号、手机号、邮箱地址、所属组织ID、所属组织名称。')

        if "姓名" not in head_datas[0]:
            raise UserError(message='第二行表头缺少姓名')

        if "帐号" not in head_datas[1]:
            raise UserError(message='第二行表头缺少帐号')

        if "邮箱" not in head_datas[2]:
            raise UserError(message='第二行表头缺少邮箱')

        if "手机号" not in head_datas[3]:
            raise UserError(message='第二行表头缺少手机号')

        if "所属组织id" not in head_datas[4]:
            raise UserError(message='第二行表头缺少所属组织id')

    def validation_data(self, datas):
        """
        校验email合法性
        :return:
        """
        error_msg = ""
        if not datas[1]:
            error_msg += "账户不能为空 \n"
        if not datas[2]:
            error_msg += "邮箱不能为空 \n"
        pattern = r'^[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+){0,4}@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+){0,4}$'
        if not re.match(pattern, datas[2]):
            error_msg = "邮箱不合法"
        return error_msg

    def process_data(self, sheet):
        account_group_duplicate = {}
        account_duplicate = {}
        all_user_excel_models = []
        error_models = []
        for row in range(sheet.nrows):
            # 忽略第一行注意事项
            if row == 0:
                continue
            # 校验表头
            if row == 1:
                self.validation_head_data(sheet.row_values(row))
                continue

            user_id = repository.get_data_scalar('user', {'account': sheet.row_values(row)[1]}, 'id')

            # 读取内容
            user_excel_model = UserExcelModel()
            user_excel_model.id = user_id or seq_id()
            user_excel_model.name = sheet.row_values(row)[0]
            user_excel_model.account = sheet.row_values(row)[1]
            user_excel_model.email = sheet.row_values(row)[2]
            user_excel_model.mobile = sheet.row_values(row)[3]
            user_excel_model.group_id = sheet.row_values(row)[4]
            user_excel_model.group_ids.append(sheet.row_values(row)[4])
            user_excel_model.group_name = sheet.row_values(row)[5]
            user_excel_model.account_mode = AccountMode.IMPORT.value
            account_group_key = user_excel_model.account + user_excel_model.group_id
            account_key = user_excel_model.account

            if account_key not in account_duplicate:
                # 生成随机8位密码
                pwd = ''.join(random.sample(string.ascii_letters + string.digits, 8))
                user_excel_model.password = pwd
                user_excel_model.pwd = bcrypt.hashpw(
                    sha1(pwd.encode()).hexdigest().encode('utf-8'), bcrypt.gensalt()
                ).decode('utf-8')
            else:
                original_user_excel_model = account_duplicate[account_key]
                user_excel_model.password = original_user_excel_model.password
                user_excel_model.pwd = original_user_excel_model.pwd

            error_msg = self.validation_data(sheet.row_values(row))
            if error_msg:
                user_excel_model.error_msg = error_msg
                error_models.append(user_excel_model)
            if account_group_key in account_group_duplicate:
                user_excel_model.error_msg = error_msg + "同一组织下帐号重复  \n"
                error_models.append(user_excel_model)

            if not error_msg:
                account_group_duplicate[account_group_key] = 1
                if account_key not in account_duplicate:
                    account_duplicate[account_key] = user_excel_model
                    all_user_excel_models.append(user_excel_model)
                else:
                    new_model = account_duplicate[account_key]
                    new_model.group_ids.append(user_excel_model.group_id)
                    account_duplicate[account_key] = new_model

                    user_excel_model.id = new_model.id
                    all_user_excel_models.append(user_excel_model)

        user_excel_models = list(account_duplicate.values()) if account_duplicate else []

        return user_excel_models, all_user_excel_models, error_models

    def write_data(self, user_source, user_excel_models, all_user_excel_models):
        # 批量添加用户
        insert_user_models = user_repository.add_multi_external_user(user_source, user_excel_models,
                                                                     all_user_excel_models)
        # 更新用户组织关系表
        if user_source.get('type') == 1:
            user_group_repository.update_user_organization()
        return insert_user_models

    def send_email(self, model):
        email_template_type = EmailTemplateType.AddUser.value
        email_template_data, content = user_service.get_user_email_content(
            model.name, model.account_mode, model.account, model.password, email_template_type
        )
        mail.send(
            mail.Mail(
                subject=email_template_data.get('subject'),
                body=content,
                receiver=[mail.MailContact(name=model.name, mail=model.email)],
            ),
            subtype=email_template_data.get('send_mode') if email_template_data.get('send_mode') else 'html',
        )
