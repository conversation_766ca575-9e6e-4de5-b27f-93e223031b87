#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""

from base.models import BaseModel
from base.enums import (
    DashboardSnapshot,
    DashboardSnapshotSchedule,
)
from dmplib.utils.errors import UserError


class SnapshotEditModel(BaseModel):
    __slots__ = [
        "auto_snap",
        "schedule_config",
        "dashboard_id"
    ]

    def __init__(self, **kwargs) -> None:
        self.auto_snap = 0
        self.schedule_config = ''
        self.dashboard_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("auto_snap", "in_range", {"required": True, "range": [0, 1]}))
        rules.append(("dashboard_id", "string", {"required": True}))

        if self.auto_snap not in [DashboardSnapshot.ON.value, DashboardSnapshot.OFF.value]:
            raise UserError(message="未知的auto_snap值类型")

        if self.schedule_config and not isinstance(self.schedule_config, dict):
            raise UserError(message="schedule_config参数格式错误")

        if self.schedule_config.get("schedule_type") not in [
            DashboardSnapshotSchedule.QUARTER.value, DashboardSnapshotSchedule.MONTH.value,
            DashboardSnapshotSchedule.WEEK.value
        ]:
            raise UserError(message="未知的调度周期类型")

        return rules


class DashboardSnapshotRecordModel(BaseModel):

    __slots__ = ["id", "snap_id", "dashboard_id", "title", "created_on", "created_by", "modified_on", "modified_by"]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.snap_id = None
        self.dashboard_id = None
        self.title = ''
        self.created_on = None
        self.created_by = None
        self.modified_on = None
        self.modified_by = None
        super().__init__(**kwargs)
