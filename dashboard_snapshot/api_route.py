#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import json

import hug

from dmplib.hug import APIWrapper
from dmplib.utils.errors import UserError

from dashboard_snapshot.models import SnapshotEditModel
from dashboard_snapshot.services import dashboard_snapshot_service
from dashboard_chart.services import dashboard_service
from user_log.models import UserLogModel

api = APIWrapper(__name__)


@api.admin_route.post('/edit')
def edit_dashboard_snapshot(request, **kwargs):
    """
    报告拍照设置
    :param dict kwargs: {
        "auto_snap": 1,
        "schedule": {
            "type": "month",
            "month": 1,
            "day": 1
        }
    }
    :return tuple:
    """
    # 参数校验
    model = SnapshotEditModel(**kwargs)
    model.validate()
    # 编辑
    pre_dashboard = dashboard_service.get_dashbaord_info(model.dashboard_id)
    application_type = pre_dashboard.get('application_type')

    dashboard_snapshot_service.edit_dashboard_snapshot(model)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'application_type': application_type,
            'action': 'edit_dashboard_snapshot',
            'id': kwargs.get('id'),
            'content': '{dashboard_type} [ {name} ] 编辑报表拍照设置，状态：{status}，时间：{config}'.format(
                status='开' if model.auto_snap == 1 else '关',
                name=pre_dashboard.get('name'),
                config=dashboard_snapshot_service.format_schedule_config(model.schedule_config),
                dashboard_type=dashboard_service.get_dashboard_type_word(pre_dashboard.get('type'), application_type),
            ),
            'extra': json.dumps({
                'old': pre_dashboard.get('schedule_config'),
                'new': kwargs,
            }, ensure_ascii=False)
        },
    )

    return True, '', ""


@api.admin_route.post('/execute')
def just_execute_snapshot(**kwargs):
    """
    立即拍照一次
    """
    dashboard_id = kwargs.get("dashboard_id")
    if not dashboard_id:
        raise UserError(message="缺少参数 dashboard_id")
    dashboard_snapshot_service.just_execute_dashboard_snap(dashboard_id)
    return True, '', ""


@api.admin_route.post('/detail/edit')
def edit_snapshot_detail(**kwargs):
    """
    快照标题编辑
    """
    record_id = kwargs.get("id")
    title = kwargs.get("title")
    dashboard_snapshot_service.edit_dashboard_snapshot_record(record_id, title)
    return True, '', ""


@api.admin_route.post('/detail/del')
def edit_snapshot(**kwargs):
    """
    删除快照
    """
    record_id = kwargs.get("id")
    dashboard_snapshot_service.del_dashboard_snapshot_record(record_id)
    return True, '', ""


@api.admin_route.get('/list')
def get_snapshot_list(**kwargs):
    """
    快照列表
    """
    dashboard_id = kwargs.get("dashboard_id")
    return True, '', dashboard_snapshot_service.get_dashboard_snapshot_record_list(dashboard_id)
