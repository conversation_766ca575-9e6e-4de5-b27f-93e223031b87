# !/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> on 2017/6/14.
"""
from base import repository
from dmplib.saas.project import get_db
from dmplib.saas.project import get_data_db


def get_flow(conditions, fields):
    return repository.get_data("flow", conditions=conditions, fields=fields)


def get_dashboard(dashboard_id):
    return repository.get_data("dashboard", {"id": dashboard_id})


def get_dashboard_extra_data(dashboard_id):
    """
    获取单个报告的数据
    :param dashboard_id:
    :return:
    """
    if not dashboard_id:
        return []
    sql = """select released_on
          from dashboard_extra where dashboard_id =%(dashboard_id)s"""
    with get_db() as db:
        return db.query_one(sql, {"dashboard_id": dashboard_id})
