#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/7/6 14:13
# <AUTHOR> caoxl
# @File     : test_chart_copy.py
# pylint: skip-file
from dmplib.hug import g
from tests.base import BaseTest
import os

import unittest
import logging

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)


logger = logging.getLogger(__name__)


class TestDashboardSnapshot(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='zk', account='zk')

    def test_calculate(self):
        from dashboard_snapshot.services.dashboard_snapshot_service import _calculate_schedule
        cron = _calculate_schedule(schedule_config={'type': 1, 'schedule_type': 'week', 'month': 1, 'date': 6})
        print(cron)


if __name__ == '__main__':
    unittest.main()
