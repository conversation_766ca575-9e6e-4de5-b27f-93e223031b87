from datetime import datetime
from loguru import logger
import traceback
import time
import os
from copy import deepcopy

from base.enums import SnapshotType, FlowInstanceStatus
from dmplib.utils.errors import UserError
from dmplib.hug import g
from dmplib.constants import LOG_LOGSTORE
from dmplib import config
from base import repository
from dmplib.utils.strings import seq_id
from dashboard_snapshot.models import DashboardSnapshotRecordModel
from components.snapshot_service import Snapshot, DropSnapShotTables
from flow.services import flow_instance_service
from flow.models import FlowInstanceModel
from dashboard_chart.services.released_dashboard_service import get_all_dashboard_and_dataset_of_need_snapshot
from dashboard_snapshot.repositories import dashboard_snapshot_repository


def _get_log_contents(project_code, instance_id, msg):
    return [
        ("env_code", str(os.environ.get("CONFIG_AGENT_CLIENT_CODE", ""))),
        ("project_code", str(project_code)),
        ("flow_instance_id", str(instance_id)),
        ("node_id", ""),
        ("level_name", ""),
        ("level_no", "0"),
        ("created", str(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))),
        ("sort", str(datetime.now().strftime("%Y-%m-%d %H:%M:%S:%f"))),
        ("file_name", "dmp/dashboard_snapshot/service/external_service.py"),
        ("module", "dashboard_snapshot"),
        ("func_name", "execute_dashboard_snap"),
        ("path_name", "dmp/dashboard_snapshot/service/external_service.py"),
        ("exc_text", ""),
        ("message", msg),
        ("type", ""),
        ("id", "")
    ]


def logger_of_flow(project_code, instance_id, msg):
    from app_celery import log_to_logstore
    log_to_logstore.apply_async(
        kwargs={
            "logstore": LOG_LOGSTORE,
            "topic": "",
            "contents": _get_log_contents(project_code, instance_id, msg)
        }
    )


def execute_dashboard_snap(dashboard_id):
    """
    执行拍照
    """
    if not dashboard_id:
        raise UserError(message="参数错误")

    # 是否发布校验
    extra = dashboard_snapshot_repository.get_dashboard_extra_data(dashboard_id)
    if not extra or not extra.get("released_on"):
        logger.error(f"报告未发布：{dashboard_id}")
        return

    # 创建flow instance流程实例记录
    flow = repository.get_data('flow', {"id": dashboard_id})
    if not flow:
        msg = f"流程不存在flow_id: {dashboard_id}"
        logger.error(msg)
        raise UserError(message=msg)
    startup_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    flow_instance = FlowInstanceModel(
        flow_id=flow.get('id'), name=flow.get('name'), type=flow.get('type'), status=FlowInstanceStatus.Running.value,
        startup_time=startup_time
    )
    instance_id = flow_instance_service.add_instance_with_startup_time(flow_instance)
    repository.update_data('flow', {"startup_time": startup_time}, {"id": dashboard_id})
    logger_of_flow(g.code, instance_id, "开始报表拍照流程")

    try:
        # 获取所有关联的报告和数据集id
        dashboard_ids, dataset_ids = get_all_dashboard_and_dataset_of_need_snapshot(
            [dashboard_id], [], [dashboard_id]
        )

        # 过滤掉未发布的报告，跳转
        copy_dashboard_ids = deepcopy(dashboard_ids)
        for _id in copy_dashboard_ids:
            extra = dashboard_snapshot_repository.get_dashboard_extra_data(_id)
            if not extra or not extra.get("released_on"):
                logger.error(f"报告未发布：{dashboard_id}")
                dashboard_ids.remove(_id)

        logger_of_flow(g.code, instance_id, f"当前报表关联的报告: {dashboard_ids}, 数据集: {dataset_ids}")

        # 生成拍照记录
        params = {
            "dashboard_ids": dashboard_ids,
            "dataset_ids": dataset_ids,
        }
        snap_id = Snapshot.generate_snap_record(g.code, SnapshotType.Dashboard.value, params)

        params.update({"snap_id": snap_id})

        # 执行拍照
        logger_of_flow(g.code, instance_id, f"开始拍照, snap_id: {snap_id}")
        snapshot = Snapshot(
            code=g.code,
            snap_type=SnapshotType.Dashboard.value,
            params=params
        )
        snapshot.do()
        status = FlowInstanceStatus.Successful.value
        logger_of_flow(g.code, instance_id, "拍照结束")

        # 生成报告拍照记录
        now = datetime.now()
        dashboard_snapshot_record_model = DashboardSnapshotRecordModel(
            id=seq_id(),
            snap_id=snap_id,
            dashboard_id=dashboard_id,
            title=flow.get('name') + now.strftime("%Y%m%d%H%M%S"),
            created_on=now,
            created_by=g.account,
            modified_on=now,
            modified_by=g.account
        )
        repository.add_model("dashboard_snapshot_record", model=dashboard_snapshot_record_model)

        # 版本数量限制
        logger_of_flow(g.code, instance_id, "拍照版本数量控制")
        deal_snapshot_version(dashboard_id, dashboard_ids, dataset_ids)

    except Exception as e:
        logger.error("报表拍照失败：{}".format(str(traceback.format_exc())))
        status = FlowInstanceStatus.Failed.value
        logger_of_flow(g.code, instance_id, f"拍照失败：{str(e)}")

    # 更新流程状态
    end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    repository.update_data('instance', {"status": status, "end_time": end_time}, {"id": instance_id})
    repository.update_data('flow', {"run_status": status, "end_time": end_time}, {"id": dashboard_id})
    logger_of_flow(g.code, instance_id, "拍照成功!")


def deal_snapshot_version(dashboard_id, dashboard_ids=None, dataset_ids=None):
    """
    拍照版本数量控制
    """
    records = repository.get_list(
        "dashboard_snapshot_record",
        {"dashboard_id": dashboard_id},
        fields=["snap_id"],
        order_by="modified_on desc"
    )
    max_version_num = int(config.get("Function.dashboard_max_version_num", 24))
    if len(records) > max_version_num:
        snap_ids = [item.get("snap_id") for item in records[max_version_num:]]
        # 获取拍照的dashboard_ids, dataset_ids
        if not dashboard_ids and not dataset_ids:
            dashboard_ids, dataset_ids = get_all_dashboard_and_dataset_of_need_snapshot(
                [dashboard_id], [], [dashboard_id]
            )

        # 删除拍照数据
        DropSnapShotTables(dashboard_ids, dataset_ids, snap_ids).do()


