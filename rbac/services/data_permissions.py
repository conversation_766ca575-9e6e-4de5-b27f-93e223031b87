# -*- coding: utf-8 -*-
"""
    class
    Created by guozh on 2018/05/19.
"""
import json
import functools
from app_menu.services.function_service import get_function_tree
from base import repository
from base.dmp_constant import (
    ALL_REPORT_DATA_ACTIONS,
    ALL_DATA_ACTIONS,
    ALL_REPORT_DATA_ACTIONS_DICT,
    DATA_IS_FLODER_CACHE_KEY,
    PERMISSION_ACTIONS,
    DATA_IS_MULTIPLE_SCREEN_CACHE_KEY,
)
from dmplib.redis import conn as conn_redis
from dmplib.constants import ADMIN_ROLE_ID
from dmplib.saas.project import get_db
from dmplib.hug import debugger
from dmplib.utils.errors import UserError
from rbac.repositories import data_permission_repository
from rbac.repositories.data_permission_repository import cache_data_is_one
from rbac.services import func_service

# 表名 对应 功能权限中func_code
from rbac.services.grant_service import _check_buildin_roles
from user.services.user_service import get_cur_role_id
from user_group.services.user_group_service import buildin_application_id
from app_menu.services.external_service import application_highdata_auth_report_filter
from base.enums import ApplicationType
from dmplib.hug import g
from app_menu.models import ApplicationModel

from typing import Any, Callable, Dict, List, Union

_debugger = debugger.Debug(__name__)

func_code_dict = {
    'data_source': 'add-datasource',
    'dataset': 'add-dataset',
    'dashboard': 'dashboard',
    'multiple_screen': 'large-screen',
    'application': 'app-site',
    'self_service': 'self-service',
    'external_subject': 'external-subject',
}


# TableQueryModel
# OperateRecordQueryModel


def get_check_all_data_permissions(role_id, data_type):
    """
    根据data_type查找所有操作权限
    :param role_id:
    :param data_type:
    :return:
    """
    # 暂时不做处理，后期可能会检测，保留代码
    if not check_has_func_permissions(role_id, data_type):
        return []
    return data_permission_repository.get_data_permissions_actions(role_id, data_type)


def check_has_func_permissions(role_id, data_type):
    """
    检测是否有对应的功能权限
    :param role_id:
    :param data_type:
    :return:
    """
    # 目前不做检测，后期可能会加上
    if role_id:
        return True
    funcs = func_service.list_funcs_of_role(role_id)
    for k in funcs:
        if k.get('func_code') == func_code_dict.get(data_type):
            return True
    # 没有对应功能权限
    return False


def set_action_for_tree(results: list) -> list:
    for item in results:
        if item.get("sub"):
            item["sub"] = set_action_for_tree(item.get("sub"))
        item.setdefault('actions', [])
    return results


def recursive_set_all_action_for_tree(results: list, actions: list) -> list:
    for item in results:
        if item.get("sub"):
            item["sub"] = recursive_set_all_action_for_tree(item.get("sub"), actions)
        item.setdefault('actions', actions)
    return results


def recursive_set_folder_action_for_tree(results: list, actions: dict, parent_actions: list = None) -> list:
    # actions 结构 {"data_id": [{'action_code': 'view', 'action_name': '查看'}]}
    if not parent_actions:
        parent_actions = []
    for item in results:
        # 只查看目录
        object_id = item.get("id")
        action = actions.get(object_id, [])
        if item.get("sub"):
            # 有一种情况，大的目录选择了报告读权限，子目录选择了编辑权限，这时候权限应该是两个权限加起来
            item["sub"] = recursive_set_folder_action_for_tree(
                item.get("sub"), actions, parent_actions=parent_actions + action
            )
        # 目录本身权限 + 继承父级权限
        for row_action in parent_actions + action:
            if row_action and row_action not in item['actions']:
                item['actions'] = item['actions'] + [row_action]
    return results


def set_folder_action_for_tree(results: list, actions: dict) -> list:
    # actions 结构 {"data_id": [{'action_code': 'view', 'action_name': '查看'}]}
    for item in results:
        # 只查看目录
        object_id = item.get("id")
        action = actions.get(object_id, [])
        # 只有目录才需要设置目录权限
        if item.get("sub"):
            item["sub"] = set_folder_action_for_tree(item.get("sub"), actions)
        # 目录本身权限 + 继承父级权限
        for row_action in action:
            if row_action and row_action not in item['actions']:
                item['actions'] = item['actions'] + [row_action]
    return results


def set_single_action_for_tree(results: list, actions: dict) -> list:
    for item in results:
        action = actions.get(item.get("id"), [])
        if item.get("sub"):
            item["sub"] = set_single_action_for_tree(item.get("sub"), actions)
        for row_action in action:
            if row_action and row_action not in item['actions']:
                item['actions'] = item['actions'] + [row_action]
    return results


def get_data_permissions_tree(role_id, data_type):
    """
    获取角色的数据权限(返回树形结构)
    :return:
    """
    # 先查功能权限，只有允许访问的功能权限才有数据权限
    # 先查总的，默认跟功能权限一致
    # 查对应数据权限表，修改数据权限list，返回前端
    if not check_has_func_permissions(role_id, data_type):
        return []
    if _data_type_in_dashboard(data_type):
        # pylint: disable=C0415
        from dashboard_chart.services import dashboard_service

        # application_type = [0, 3, 4] if data_type == 'dashboard' else 1
        # if data_type == 'dashboard':
        #     application_type = [0, 3, 4]
        # elif data_type == 'report_center':
        #     application_type = [6, 5]
        # else:
        #     application_type = 1

        application_type = get_application_type_by_data_type(data_type)

        # 默认是修改时间倒序
        data = dashboard_service.get_dashboard_list_not_permission(application_type=application_type,
                                                                   data_type=data_type)
    elif _data_type_in_dataset(data_type):
        from dataset.services import dataset_service
        result = dataset_service.get_model_dataset_tree(
            exclude_types='EXTERNAL_SUBJECT,TEMPLATE'
        )
        data = {'route': []}
        data['tree'] = dataset_service.sort_tree_data(result)
    else:
        raise UserError(message='目前仅支持报告目录权限')
    # data结构 {"route": [], "tree": []}
    results = data.get("tree")
    # 没有数据之间返回，不再过滤
    if not results:
        return results

    # user_role_all_data_permission 表查看
    # 查看操作权限
    # all_data_permission 结构 [{'action_code': 'view', 'action_name': '查看'}]
    all_data_permission = data_permission_repository.get_data_permissions_actions(role_id, data_type)

    # 是否拥有所有数据权限
    has_all_actions = check_all_action_permissions(data_type, all_data_permission)
    if has_all_actions:
        return set_action_for_tree(results)

    # 有部分全选权限
    results = set_action_for_tree(results)

    # user_role_folder_data_permission 表查看
    folder_data_permission = data_permission_repository.get_folder_data_permissions_actions(role_id, data_type)

    results = set_folder_action_for_tree(results, folder_data_permission)

    # user_role_data_permission 表查看
    single_data_permission = data_permission_repository.get_single_data_permissions_actions(role_id, data_type)
    # 处理单个权限
    results = set_single_action_for_tree(results, single_data_permission)

    return results


def get_data_permissions_external_subject(role_id, data_type, model):
    from self_service import external_service  # pylint: disable=C0415

    data = get_data_permissions_no_deal_top(role_id, data_type, model)
    # 加spaces，处理为两层结构
    spaces = external_service.get_spaces_no_permission()
    subject_dict = {}
    for space in spaces:
        for subject in space.get('sub', []):
            subject_dict[subject.get('id')] = space
        space['sub'] = []

    for item in data.get("items"):
        subject_id = item.get('id')
        space = subject_dict.get(subject_id)
        sub = space.get('sub')
        sub.append(item)
    return spaces


def get_data_permissions_no_deal_top(role_id, data_type, model):
    """
    获取角色的数据权限(不处理顶层权限)
    :return:
    """
    # 先查功能权限，只有允许访问的功能权限才有数据权限
    # 先查总的，默认跟功能权限一致
    # 查对应数据权限表，修改数据权限list，返回前端
    if not check_has_func_permissions(role_id, data_type):
        return []
    results = data_permission_repository.get_data_list(model, data_type).get_result_dict()
    # 没有数据之间返回，不再过滤
    if not results:
        return results

    # 查看操作权限
    has_actions = data_permission_repository.get_data_permissions_actions(role_id, data_type)
    # pylint: disable=W0106
    [item.setdefault('actions', []) for item in results.get('items')]

    # 是否拥有所有数据权限
    has_all_actions = check_all_action_permissions(data_type, has_actions)
    if has_all_actions:
        return results

    if data_type == 'application':
        return deal_application_sub_menu(results, role_id=role_id)

    # 处理单个权限
    results = deal_single_data_permissions(role_id, data_type, results)

    return results


def get_data_permissions(role_id, data_type, model):
    """
    获取角色的数据权限
    :return:
    """
    # 先查功能权限，只有允许访问的功能权限才有数据权限
    # 先查总的，默认跟功能权限一致
    # 查对应数据权限表，修改数据权限list，返回前端
    if not check_has_func_permissions(role_id, data_type):
        return []
    results = data_permission_repository.get_data_list(model, data_type).get_result_dict()
    # 没有数据之间返回，不再过滤
    if not results:
        return results

    # 查看操作权限
    has_actions = data_permission_repository.get_data_permissions_actions(role_id, data_type)

    # 是否拥有所有数据权限
    has_all_actions = check_all_action_permissions(data_type, has_actions)
    if has_all_actions:
        if data_type == 'application':
            return deal_application_sub_menu(results, has_all_actions=has_all_actions)
        all_actions = ALL_REPORT_DATA_ACTIONS if _data_type_in_dashboard(data_type) else ALL_DATA_ACTIONS
        # pylint: disable=W0106
        [item.setdefault('actions', all_actions) for item in results.get('items')]
        return results

    if data_type == 'application':
        return deal_application_sub_menu(results, role_id=role_id, has_actions=has_actions)

    # 默认actions中的的数据权限
    # pylint: disable=W0106
    [item.setdefault('actions', has_actions) for item in results.get('items')]

    # 处理单个权限
    results = deal_single_data_permissions(role_id, data_type, results)

    return results


def deal_application_sub_menu(results, has_all_actions=False, role_id=None, has_actions=None):
    allow_view_ids = []
    edit_all_action = view_all_action = False
    if has_actions:
        if has_actions[0].get('action_code') == 'edit':
            edit_all_action = True
        else:
            view_all_action = True
    if not has_all_actions:
        funcs_ids = repository.get_data('user_role_app_sub_menu', {'role_id': role_id}, ['func_id'], multi_row=True)
        view_ids = repository.get_data(
            'user_role_data_permission',
            {'role_id': role_id, 'data_type': 'application', 'data_action_code': 'view'},
            ['data_id'],
            multi_row=True,
        )
        edit_ids = repository.get_data(
            'user_role_data_permission',
            {'role_id': role_id, 'data_type': 'application', 'data_action_code': 'edit'},
            ['data_id'],
            multi_row=True,
        )
        data_ids = [k.get('data_id') for k in view_ids]
        funcs_ids = [k.get('func_id') for k in funcs_ids]
        allow_view_ids = data_ids + funcs_ids
        allow_edit_ids = [k.get('data_id') for k in edit_ids]

    for item in results.get('items'):
        # 查看该门户下的子菜单权限
        sub_menu = get_function_tree(item.get('id'))
        if not has_all_actions:
            item['actions'] = []
            if item.get('id') in allow_view_ids or view_all_action:
                item['actions'] += [{'action_code': 'view', 'action_name': '查看'}]
            if item.get('id') in allow_edit_ids or edit_all_action:
                item['actions'] += [{'action_code': 'edit', 'action_name': '编辑'}]
        else:
            item['actions'] = [
                {'action_code': 'view', 'action_name': '查看'},
                {'action_code': 'edit', 'action_name': '编辑'},
            ]
        if sub_menu:
            # 目前只有查看权限控制子菜单
            if has_all_actions or view_all_action:
                item['sub'] = set_all_application_action(sub_menu)
            else:
                item['sub'] = set_application_by_allow_ids(sub_menu, allow_view_ids)

    return results


def set_all_application_action(data):
    for k in data:
        k['actions'] = [{'action_code': 'view', 'action_name': '查看'}, {'action_code': 'edit', 'action_name': '编辑'}]
        if k.get('sub'):
            k['sub'] = set_all_application_action(k.get('sub'))
    return data


def set_application_by_allow_ids(data, allow_view_ids):
    for item in data:
        if item.get('id') in allow_view_ids:
            item['actions'] = [{'action_code': 'view', 'action_name': '查看'}]
        else:
            item['actions'] = []
        if item.get('sub'):
            item['sub'] = set_application_by_allow_ids(item.get('sub'), allow_view_ids)
    return data


def get_all_applications_by_role_ids(role_ids, platform='pc', has_func=True):
    from app_menu.services import application_service
    # 先查出所有有权限门户
    app_datas = repository.get_data(
        'application',
        {'enable': [1, 2], 'platform': platform},
        fields=[
            'id',
            'name',
            'platform',
            'description',
            'icon',
            'url',
            'target',
            'is_buildin',
            'rank',
            'enable',
            'collapse',
            'nav_type',
            'use_guide',
            'theme',
            'menu_display_type',
            'type_access_released',
            'distribute_type',
            'user_defined_style',
            'is_show_banner',
            'banner_url',
            'bottom_color',
            'relation_id',
            'is_cache',
            'report_type',
            'common_config',
            'enable_snapshot',
            'enable_filter',
            'filter_config',
            'created_by',
            'modified_on'
        ],
        multi_row=True,
        order_by=[('rank', 'asc')]
    )

    can_view_all, app_allow_ids = data_permission_repository.get_app_allow_ids_by_role(role_ids)
    has_permission_datas = []
    for app in app_datas:
        app_model = ApplicationModel(**app)
        application_service.set_app_url_info(app_model)
        app = app_model.get_dict()
        if app.get('id') == buildin_application_id:
            continue
        # 查看该门户下的子菜单权限
        if can_view_all:
            if has_func:
                sub_menus = get_function_tree(app.get('id'), is_release=True)
                app['function'] = sub_menus
            has_permission_datas.append(app)
        else:
            # 有这个应用的权限
            if app.get('id') in app_allow_ids or app.get('created_by') == g.account:
                if has_func:
                    sub_menus = get_function_tree(app.get('id'), is_release=True)
                    sub_menus = get_has_permission_sub_menu(sub_menus, app_allow_ids)
                    app['function'] = sub_menus
                has_permission_datas.append(app)

    return has_permission_datas


def check_app_allow_ids(role_ids):
    can_view_all, app_allow_ids = data_permission_repository.get_app_allow_ids_by_role(role_ids)
    return can_view_all, app_allow_ids


def get_has_permission_sub_menu(data, app_allow_ids):
    if not data:
        return []
    new_data = []
    for single in data:
        if single.get('sub'):
            single['sub'] = get_has_permission_sub_menu(single.get('sub'), app_allow_ids)
        if single.get('id') in app_allow_ids or single.get('created_by') == g.account:
            new_data.append(single)
    return new_data


def check_all_action_permissions(data_type, actions):
    """
    验证是否有所有权限
    :param data_type:
    :param actions:
    :return:
    """
    if len(actions) >= 4:
        return True
    if not _data_type_in_dashboard(data_type) and len(actions) == 2:
        return True
    return False


def deal_single_data_permissions(role_id, data_type, results):
    """
    处理过滤每条返回的权限
    :param role_id:
    :param data_type:
    :param results:
    :return:
    """
    ids = [item.get('id') for item in results.get('items')]
    details = data_permission_repository.get_data_permissions_actions_detail(role_id, data_type, ids)
    for data in results.get('items'):
        res_actions = details.get(data.get('id'))
        if res_actions:
            data['actions'] = data.get('actions') + res_actions
    return results


def update_all_data_permissions(role_id, data_type, datas):
    """
    更新角色全部权限
    :param role_id:
    :param data_type:
    :param datas:
    :return:
    """
    for data in datas:
        table_data = {'role_id': role_id, 'data_type': data_type, 'data_action_code': data.get("data_action_code")}
        if data.get("selected"):
            repository.add_data('user_role_all_data_permission', table_data)
        else:
            repository.delete_data('user_role_all_data_permission', table_data)
            # 这里不再删除user_role_data_permission表记录
        # pylint: disable=C0415
        from dataset.services.dataset_service import refresh_cache_by_dataset_id

        refresh_cache_by_dataset_id(data.get("data_id"))


def get_action_code_by_data_id(role_id, data):
    if data and len(data) == 1:
        return repository.get_data(
            'user_role_data_permission',
            {"role_id": role_id, "data_id": data[0].get("data_id")},
            fields=["data_action_code"],
            multi_row=True,
        )
    # pylint: disable=R1710
    return []


def get_folder_action_code_by_data_id(role_id, data):
    if data and len(data) == 1:
        return repository.get_data(
            'user_role_folder_data_permission',
            {"role_id": role_id, "data_id": data[0].get("data_id")},
            fields=["data_action_code"],
            multi_row=True,
        )
    # pylint: disable=R1710
    return []


def update_folder_data_permissions(role_id, data_type, datas):
    """
    为角色分配功能权限
    :param role_id:
    :param data_type:
    :param datas: [{"data_id": "id", "data_action_code":"read", "selected": True/False}]
    :return:
    """
    # 内置角色不能更改权限
    _check_buildin_roles(role_id)

    for data in datas:
        table_data = {'role_id': role_id, 'data_type': data_type, "data_id": data.get("data_id")}
        # 建议前端更改为单个action, 不传整个数组
        repository.delete_data('user_role_folder_data_permission', table_data)
        for row_action in data.get("data_action_code"):
            table_data["data_action_code"] = row_action
            repository.add_data('user_role_folder_data_permission', table_data)

    return True


# pylint: disable=R1710
def update_data_permissions(role_id, data_type, datas, check_all=False, top_permissions=False):
    """
    为角色分配功能权限
    :param role_id:
    :param data_type:
    :param datas: [{"data_id": "id", "data_type": "data_source", "data_action_code":["write", "read"]}]
    :return:
    """
    # 内置角色不能更改权限
    _check_buildin_roles(role_id)
    # 检测是否开启功能权限
    if check_has_edit_data_permissions(datas) and not repository.data_is_exists(
            'user_role_func',
            {'role_id': role_id, 'func_code': func_code_dict.get(data_type), 'func_action_code': 'edit'}
    ):
        raise UserError(message=u'请先开启编辑的功能权限！')
    # todo 数据源，数据集需先判断是否有对应的功能权限, 设置全部操作后，不允许对单个去除勾选
    # 如果是类型 dataset，且是设置所有权限，则需要检测所有数据源是否有对应权限
    if check_all and data_type == 'dataset':
        check_all_data_source(role_id, datas[0].get('data_action_code'))

    if top_permissions:
        update_all_data_permissions(role_id, data_type, datas)
    else:
        return data_permission_repository.grant_permissions_for_role(role_id, data_type, datas)


def get_rbac_resource_detail(rbac_data, top_permissions=False, old_action_code=None):
    """
    获取rbac资源详情，用于日志审计
    :param rbac_data: 资源data
    {
      "role_id": "9189fd82-8e28-45f4-bfaf-81cf2db89e20",
      "data_type": "dataset",
      "data": [
        {
          "data_id": "39ea6198-96c4-d24b-782f-4f681cb93a7c",
          "data_action_code": [
            "edit",
            "view"
          ]
        }
      ]
    }
    :return: list
    """

    db = get_db()
    role = db.query_one('select * from user_role where id=%(role_id)s', {'role_id': rbac_data.get('role_id')})

    data_type = rbac_data.get('data_type')
    data_type_table = {
        'dataset': '数据集',
        'dashboard': '报告',
        'data_source': '数据源',
        'application': '应用',
        'self_service': '自助报表',
    }
    data_table = {
        'dataset': 'dataset',
        'dashboard': 'dashboard',
        'data_source': 'data_source',
        'application': 'application',
        'self_service': 'dashboard',
    }

    if len(rbac_data.get('data')) == 1:
        request_data = rbac_data.get('data')[0]
    else:
        return False

    # 资源名称
    resource_name = ''
    action = '变更'

    if data_type in data_table and not top_permissions:
        table = data_table.get(data_type)
        resource = db.query_one('select name from ' + table + ' where id=%(id)s', {'id': request_data.get('data_id')})
        resource_name = resource.get('name')

    if top_permissions:
        resource_name = "所有"
        if request_data.get('selected'):
            action = '全部设置'
        else:
            action = '全部取消'

    # 单个设置
    func_code = ''
    if isinstance(request_data.get('data_action_code'), list):
        old_func_str = ''
        if old_action_code:
            old_func_str = ','.join(PERMISSION_ACTIONS.get(_.get("data_action_code")) for _ in old_action_code)
        if len(request_data.get('data_action_code')) > 0:
            func_code = ','.join([PERMISSION_ACTIONS.get(_) for _ in request_data.get('data_action_code')])
            return (
                "给角色“{role}” {action} “{data_type}-{resource}” 权限 \n "
                "变更前：“{old_func_str}“ \n 变更后：“{func_code}” ".format(
                    role=role.get('name'),
                    action=action,
                    data_type=data_type_table.get(data_type),
                    resource=resource_name,
                    old_func_str=old_func_str,
                    func_code=func_code,
                )
            )

        else:
            # 取消单个权限
            return "角色“{role}” {action} “{data_type}-{resource}”  “{old_func_str}” 权限".format(
                role=role.get('name'),
                action="取消",
                data_type=data_type_table.get(data_type),
                resource=resource_name,
                old_func_str=old_func_str,
            )

    # 全部设置
    if isinstance(request_data.get('data_action_code'), str):
        func_code = PERMISSION_ACTIONS.get(request_data.get('data_action_code'))

    return "给角色“{role}” {action} “{data_type}-{resource}” 为 “{func_code}” 权限".format(
        role=role.get('name'),
        action=action,
        data_type=data_type_table.get(data_type),
        resource=resource_name,
        func_code=func_code,
    )


def check_has_edit_data_permissions(datas):
    # 目前不需要检测是否有编辑权限，后期也许会加上，暂时保留代码
    # pylint: disable=W0125
    if 2:
        return False
    for data in datas:
        data_action_codes = data.get('data_action_code')
        if data_action_codes:
            for data_action_code in data_action_codes:
                if data_action_code == 'edit':
                    return True
    return False


def check_all_data_source(role_id, data_action_codes):
    """
    检测是否有所有数据源对应权限
    :param role_id:
    :return:
    """
    has_actions = data_permission_repository.get_data_permissions_actions(role_id, 'data_source')
    all_actions = [k.get('action_code') for k in has_actions]
    for action in data_action_codes:
        if action not in all_actions:
            raise UserError(message=u'部分数据源没有相关权限')


def filter_data_edit(permissions, role_ids, data_id):
    """
    查看该角色是否有编辑该资源的权限
    :param permissions:
    :param role_ids:
    :param data_id:
    :return:
    """
    permissions = permissions.split('-')
    if len(permissions) != 2:
        raise UserError(message=u'permissions参数错误')
    data_type, data_action_code = permissions[0], permissions[1]
    # TODO(Judy): 暂时不检查self_service权限
    if data_type == 'self_service':
        return True
    if not role_ids:
        return False
    flag = data_permission_repository.get_permission_by_own(data_id, g.account, data_type)
    if flag:
        return True

    # 报告的目录权限根据是否有编辑报告的功能权限判断
    if _data_type_in_dashboard(data_type) and data_action_code == 'edit':
        # 缓存是否为目录
        cache_key = DATA_IS_FLODER_CACHE_KEY % data_id
        cache = conn_redis()
        flag = cache_data_is_one(cache, cache_key)
        # flag有值，有两种情况,是目录和不是目录。
        if flag is not None:
            if flag:
                return flag
        else:
            is_floder = repository.data_is_exists('dashboard', {'id': data_id, 'type': 'FOLDER'})
            # 是否为目录不太可能改动，缓存7天
            cache.set(cache_key, is_floder, 7 * 24 * 60 * 60)
            if is_floder:
                return True

    # 仪表板如果是子报告，需要转换为根报告id
    data_id = _child_dashboard_convert_dashboard(data_type, data_id)

    flag = filter_data_permission(data_type, data_id, role_ids, data_action_code)
    if flag:
        return True
    # 判断单个权限
    allow_ids = data_permission_repository.get_allow_ids(role_ids, data_type, data_action_code)

    # 查询自己创建的资源
    if data_id not in allow_ids:
        # 查询目录权限(不影响之前的逻辑判断)
        # 目前只有报告有目录权限
        data_tree, folder_allow_ids = get_folder_allow_ids(role_ids, data_type, data_action_code)
        if data_id in folder_allow_ids:
            return True
        return False

    return True


def filter_data_permission(data_type, data_id, role_ids, data_action_code):
    # data_type 前面步骤应该做好区分
    # 解决多屏报告data_type == 'dashboard' 的问题， 应改为：data_type == 'multiple_screen'
    if data_type == 'dashboard':
        # 缓存是否为多屏
        cache_key = DATA_IS_MULTIPLE_SCREEN_CACHE_KEY % data_id
        cache = conn_redis()
        flag = cache_data_is_one(cache, cache_key)
        if flag is not None:
            if flag:
                data_type = 'multiple_screen'
        else:
            is_multiple_screen = (
                    repository.data_is_exists('dashboard', {'id': data_id, 'is_multiple_screen': 1}) or False
            )
            cache.set(cache_key, is_multiple_screen, 7 * 24 * 60 * 60)
            if is_multiple_screen:
                data_type = 'multiple_screen'

    flag = data_permission_repository.get_permission_by_role_ids(role_ids, data_type, data_action_code)
    if flag:
        return True
    return False


def get_application_type_by_data_type(data_type):
    # 数见报表
    if data_type == 'dashboard':
        application_type = [0, 3, 4]
    # 报表中心
    elif data_type == 'report_center':
        application_type = [6, 5]
    # 酷炫大屏
    elif data_type == 'large_screen':
        application_type = ApplicationType.LargeScreen.value
    # 自助报表
    else:
        application_type = 1
    return application_type


def get_folder_allow_ids(role_ids, data_type, data_action_code, data_tree: Union[list, None] = None):
    # 考虑data_tree重复使用
    if _data_type_in_dataset(data_type) is False and not data_tree:
        # pylint: disable=C0415
        from dashboard_chart.services import dashboard_service

        # application_type = [0, 3, 4] if data_type == 'dashboard' else 1
        application_type = get_application_type_by_data_type(data_type)
        data = dashboard_service.get_dashboard_list_not_permission(application_type=application_type)
        data_tree = data.get("tree")
        if not data_tree:
            return None, []
    # 目前只有报告有目录权限
    folder_allow_ids = []
    if _data_type_in_dashboard(data_type):
        folder_allow_ids = data_permission_repository.get_folder_allow_ids(role_ids, data_type, data_action_code)
        folder_allow_ids = list(set(recursive_get_sub_allow_ids(data_tree, folder_allow_ids)))

    # 数据集支持目录权限
    if _data_type_in_dataset(data_type):

        folder_allow_ids = data_permission_repository.get_folder_allow_ids(role_ids, data_type, data_action_code)
        if folder_allow_ids:
            from dataset.services import dataset_service
            datasets = dataset_service.get_datasets_by_parentid(folder_allow_ids)
            if datasets:
                folder_allow_ids = []
                for dataset in datasets:
                    folder_allow_ids.append(dataset['id'])
        else:
            folder_allow_ids = []

    return data_tree, folder_allow_ids


def recursive_get_sub_allow_ids(data, folder_allow_ids, append=False):
    if not folder_allow_ids:
        return []
    for item in data:
        object_id = item.get("id")
        if item.get("sub"):
            recursive_get_sub_allow_ids(
                item.get("sub"), folder_allow_ids, append=(append or object_id in folder_allow_ids)
            )
        # 当前的id也应该加在 folder_allow_ids 中
        if append:
            folder_allow_ids.append(object_id)
    return folder_allow_ids


def filter_data(permissions: str, role_ids: List[str], data, ignore_error: bool = False):
    """
    根据数据权限过滤数据
    :param permissions:
    :param data:
    :param role_ids:
    :return:
    """
    if not data:
        return data

    # 自己创建的资源不做权限限制
    if isinstance(data, dict) and data.get('created_by') == g.account:
        return data

    data_type, data_action_code = permissions.split('-')

    # 管理员不需要过滤权限
    if role_ids and ADMIN_ROLE_ID in role_ids:
        flag = True
    else:
        # self_service暂时不做权限检查
        if data_type == 'self_service':
            flag = False  # 自助报表按当前登录用户过滤。不查找任何非本人创建的自助报表
        else:
            flag = data_permission_repository.get_permission_by_role_ids(role_ids, data_type, data_action_code)

    if flag:
        # 拥有所有查看权限，查看剩下权限
        # 报告多加一个复制权限的
        if _data_type_in_dashboard(data_type):
            data = add_actions_code(role_ids, data, data_type=data_type, data_action_code='copy')
        if data_type in ('external_subject',):
            return data
        return add_actions_code(role_ids, data, data_type=data_type, data_action_code='edit')

    # 目前只有报告有目录权限
    data_tree, folder_allow_ids = get_folder_allow_ids(role_ids, data_type, data_action_code)

    # 判断单个权限
    allow_ids = data_permission_repository.get_allow_ids(role_ids, data_type, data_action_code)

    # 查询自己创建的资源

    own_ids = _deal_own_data(data)

    allow_ids = allow_ids + own_ids + folder_allow_ids

    # 报告返回树形结构，需查找对应的目录id
    if _data_type_in_dashboard(data_type):
        floder_ids = data_permission_repository.get_dashboard_parent_id(allow_ids)
        allow_ids += floder_ids
    # 门户需要查找对应的子菜单id
    elif data_type == 'application':
        sub_ids = data_permission_repository.get_application_allow_sub_ids(role_ids)
        allow_ids += sub_ids

    if not allow_ids:
        return []
    # 传入单个数据，一般为打开时，直接抛出没有对应权限
    if isinstance(data, dict):
        if data.get('id') in allow_ids:
            return data
        # 报告的目录有可能传route中
        if data.get('route'):
            return []
        if not ignore_error:
            raise UserError(415, '没有权限访问该资源，请联系管理员开通权限！')
        return False
    data = deal_data(data, set(allow_ids))
    if data_type == 'application':
        # 请求HD报告权限接口，校验门户挂载HD报告的菜单是否显示
        data = application_highdata_auth_report_filter(g.account, data, allow_ids)

    if _data_type_in_dashboard(data_type):
        data = add_actions_code(role_ids, data, data_type=data_type, data_action_code='copy')
    if data_type in ('external_subject',):
        return data
    # 拥有所有查看权限，查看剩下权限
    return add_actions_code(role_ids, data, data_type=data_type, data_action_code='edit')


def _deal_own_data(data):
    """
    递归查找自己创建的资源
    :param data:
    :return:
    """
    own_ids = []

    for k in data:

        if not isinstance(k, dict):
            continue
        if k.get('sub'):
            own_ids += _deal_own_data(k.get('sub'))

        # 自己创建的资源不做权限限制
        if k.get('created_by') == g.account:
            own_ids.append(k.get('id'))
    return own_ids


def deal_data(data, allow_ids):
    """
    递归处理树形的目录结构，过滤allow_ids (注意目录是不需要设置权限的)
    :param data:
    :param allow_ids:
    :return:
    """
    new_data = []
    for k in data:
        if k.get('sub'):
            sub_data = deal_data(k.get('sub'), allow_ids)
            if sub_data and k not in new_data:
                k['sub'] = sub_data
                new_data.append(k)

        # 自己创建的资源不做权限限制
        if k.get('id') in allow_ids and k not in new_data:
            new_data.append(k)
    return new_data


def add_actions_code(
        role_ids: List[str],
        data,
        data_type: str = 'dataset',
        data_action_code: str = 'edit',
        admin: bool = False,
        data_tree: Union[list, None] = None,
):
    # 暂时先为数据集加上编辑权限
    # 查看是否勾选全选
    flag = data_permission_repository.get_permission_by_role_ids(role_ids, data_type, data_action_code)
    if flag or admin:
        return recursive_add_actions_code(data, data_action_code, check_all=flag)

    # 目录权限
    # 目前只有报告有目录权限
    data_tree, folder_allow_ids = get_folder_allow_ids(role_ids, data_type, data_action_code, data_tree=data_tree)

    # 获取 allow_ids
    allow_ids = data_permission_repository.get_allow_ids(role_ids, data_type, data_action_code)
    own_ids = _deal_own_data(data)

    allow_ids = allow_ids + own_ids + folder_allow_ids
    return recursive_add_actions_code(data, data_action_code, allow_ids=allow_ids)


def recursive_add_actions_code(
        data: Any, data_action_code: str, allow_ids: None = None, check_all: List[Dict[str, int]] = False
) -> Any:
    # 处理空数据
    if isinstance(data, dict):
        return data
    ACTIONS = ALL_REPORT_DATA_ACTIONS_DICT.get(data_action_code)
    for single in data:
        # 初始化
        if not single.get('actions'):
            single['actions'] = []
        if check_all or single.get('id') in allow_ids:
            single['actions'].append(ACTIONS)
        # 判断是否有子目录
        if single.get('sub'):
            single['sub'] = recursive_add_actions_code(
                single.get('sub'), data_action_code, allow_ids=allow_ids, check_all=check_all
            )
    return data


# def rollback_origin_permissions(wrapper_func, origin_permissions):
#     def decorator(func):
#         def wrapper(*args, **kwargs):
#             """"""
#             try:
#                 return func(*args, **kwargs)
#             finally:
#                 func.set_permissions(origin_permissions)
#         wrapper.set_permissions = wrapper_func.set_permissions
#         wrapper.get_permissions = wrapper_func.get_permissions
#         return wrapper
#     return decorator


def data_permission_filter(permissions: str) -> Callable:
    origin_permissions = permissions

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            _debugger.log(f'data_permission_filter, func: {func.__qualname__}, permissions: {permissions}')
            data = func(*args, **kwargs)
            if not data:
                return data
            # 查询用户所属于角色
            role_ids = get_cur_role_id()
            # model查出来的结构是 {'items': [{...}, {...}], total: 20}, 每个list的结构不统一很烦
            if isinstance(data, dict):
                # itmes 和 tree为空的情况下
                if data.get('tree') == [] or data.get('items') == []:
                    return data
                if data.get('items'):
                    # 目前 数据源 和 应用是这种结构
                    data['items'] = filter_data(permissions, role_ids, data.get('items'))
                    # 修改total
                    if isinstance(data.get('items'), list) and len(data.get('items')) != data.get('total'):
                        data['total'] = len(data.get('items'))
                    return data
                if data.get('tree'):
                    data['tree'] = filter_data(permissions, role_ids, data.get('tree'))
                    return data
            return filter_data(permissions, role_ids, data)

        # 设置permissions参数值
        def set_permissions(value):
            nonlocal permissions
            permissions = value

        def get_permissions():
            nonlocal permissions
            return permissions

        @functools.wraps(func)
        def roll_permissions_wrapper(*args, **kwargs):
            """
            当外部使用set_permissions给函数设置了新的执行权限，会改变权限装饰器内部的权限值（历史bug）。
            会导致内部的值一直是新的权限值，当函数下次调用的时候也会是新的权限值。
            这里是执行了一次之后，无论如何都会将权限还原至原有的函数权限。
            """
            try:
                return wrapper(*args, **kwargs)
            finally:
                set_permissions(origin_permissions)

        roll_permissions_wrapper.set_permissions = set_permissions
        roll_permissions_wrapper.get_permissions = get_permissions

        return roll_permissions_wrapper

    return decorator


def filter_application_sub(data):
    role_ids = get_cur_role_id()
    # 如果是管理员,不做处理
    if role_ids and ADMIN_ROLE_ID in role_ids:
        return data
    return filter_data('application-view', role_ids, data)


def data_permission_copy_filter(permissions):
    origin_permissions = permissions

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            _debugger.log(f'data_permission_copy_filter, func: {func.__qualname__}, permissions: {permissions}')
            # 查询用户所属于角色
            role_ids = get_cur_role_id()
            # 如果是管理员,不做处理
            if ADMIN_ROLE_ID in role_ids:
                return func(*args, **kwargs)
            # 数据集版本的相关操作为dataset_id
            data_id = args[0]
            flag = filter_data_edit(permissions, role_ids, data_id)
            if flag:
                return func(*args, **kwargs)
            raise UserError(message=u'没有权限操作该资源，请联系管理员开通权限')

        # 设置permissions参数值
        def set_permissions(value):
            nonlocal permissions
            permissions = value

        # 获取permissions参数值
        def get_permissions():
            nonlocal permissions
            return permissions

        @functools.wraps(func)
        def roll_permissions_wrapper(*args, **kwargs):
            """
            当外部使用set_permissions给函数设置了新的执行权限，会改变权限装饰器内部的权限值（历史bug）。
            会导致内部的值一直是新的权限值，当函数下次调用的时候也会是新的权限值。
            这里是执行了一次之后，无论如何都会将权限还原至原有的函数权限。
            """
            try:
                return wrapper(*args, **kwargs)
            finally:
                set_permissions(origin_permissions)

        roll_permissions_wrapper.set_permissions = set_permissions
        roll_permissions_wrapper.get_permissions = get_permissions

        return roll_permissions_wrapper

    return decorator


# 使用指定某个新权限来执行之前已经被装饰过的权限函数
def execute_function_with_new_permission(permission_func=None, new_permission=''):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal permission_func
            if permission_func is None:
                permission_func = func
            origin_permission = permission_func.get_permissions()
            try:
                permission_func.set_permissions(new_permission)
                result = func(*args, **kwargs)
                return result
            finally:
                permission_func.set_permissions(origin_permission)

        return wrapper

    return decorator


def data_permission_edit_filter(permissions: str) -> Callable:
    origin_permissions = permissions

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            _debugger.log(f'data_permission_edit_filter, func: {func.__qualname__}, permissions: {permissions}')
            # 检查租户是否过期
            # check_tenant_expire(permissions)
            # 查询用户所属于角色
            role_ids = get_cur_role_id()
            # 如果是管理员,不做处理
            if role_ids and ADMIN_ROLE_ID in role_ids:
                return func(*args, **kwargs)
            # 报告复制
            if isinstance(args[0], str):
                data_id = args[0]
            # update dataset 的时候传参
            elif isinstance(args[0], dict):
                if args[0].get('dataset_id'):
                    data_id = args[0].get('dataset_id')
                else:
                    data_id = args[0].get('id')
            else:
                # 数据集版本的相关操作为dataset_id
                # run_get_data 接口传的是 content
                if hasattr(args[0], 'dashboard_id') and args[0].dashboard_id:
                    data_id = args[0].dashboard_id
                elif hasattr(args[0], 'dataset_id') and args[0].dataset_id:
                    data_id = args[0].dataset_id
                elif hasattr(args[0], 'content') and args[0].content and isinstance(args[0].content, str):
                    content = json.loads(args[0].content)
                    data_id = content.get('dataset_id')
                # 控制应用的菜单，使用application_id
                elif hasattr(args[0], 'application_id') and args[0].application_id:
                    data_id = args[0].application_id
                elif hasattr(args[0], 'id'):
                    data_id = args[0].id
                # 来自报表中心的报表
                elif hasattr(args[0], 'kwargs') and args[0].kwargs:
                    # 寻找报表id
                    data_id = args[0].kwargs.get('id') or args[0].kwargs.get('dashboard_id') or args[0].kwargs.get(
                        'dash_id')
                else:
                    raise UserError(message=u'没有权限操作该资源，请联系管理员开通权限')
            flag = filter_data_edit(permissions, role_ids, data_id)
            if flag:
                return func(*args, **kwargs)
            raise UserError(message=u'没有权限操作该资源，请联系管理员开通权限')

        # 设置permissions参数值
        def set_permissions(value):
            nonlocal permissions
            permissions = value

        # 获取permissions参数值
        def get_permissions():
            nonlocal permissions
            return permissions

        @functools.wraps(func)
        def roll_permissions_wrapper(*args, **kwargs):
            """
            当外部使用set_permissions给函数设置了新的执行权限，会改变权限装饰器内部的权限值（历史bug）。
            会导致内部的值一直是新的权限值，当函数下次调用的时候也会是新的权限值。
            这里是执行了一次之后，无论如何都会将权限还原至原有的函数权限。
            """
            try:
                return wrapper(*args, **kwargs)
            finally:
                set_permissions(origin_permissions)

        roll_permissions_wrapper.set_permissions = set_permissions
        roll_permissions_wrapper.get_permissions = get_permissions

        return roll_permissions_wrapper

    return decorator


def query_data_by_model():
    pass


def check_has_data_permission_no_error(data_type, data_action_code, data_id=None):
    try:
        return check_has_data_permission(data_type, data_action_code, data_id)
    except UserError:
        return False


def check_has_data_permission(data_type, data_action_code, data_id=None, user_id=None, check_creator=True):
    """
    检测是否有权限
    :param data_type:
    :param data_action_code:
    :param data_id:
    :param user_id:
    :param check_creator: 是否校验创建者（默认：True）
    :return:
    """
    role_ids = get_cur_role_id(user_id)
    if not role_ids:
        raise UserError(code=407, message=u'没有权限操作该资源，请联系管理员开通权限')
    # 如果是管理员,不做处理
    if ADMIN_ROLE_ID in role_ids:
        return True
    flag = check_data_permission_by_role_ids(
        role_ids, data_type, data_action_code, data_id, check_creator=check_creator
    )
    if flag:
        return flag
    raise UserError(code=407, message=u'没有权限操作该资源，请联系管理员开通权限')


def check_has_data_permission_role(role_id, data_type, data_action_code, data_id=None):
    # 先检查 user_role_all_data_permission 数据
    has_all = repository.data_is_exists(
        'user_role_all_data_permission',
        {'role_id': role_id, 'data_type': data_type, 'data_action_code': data_action_code},
    )
    if has_all:
        return True

    if data_id:
        has_permission = repository.data_is_exists(
            'user_role_data_permission',
            {'role_id': role_id, 'data_type': data_type, 'data_action_code': data_action_code, 'data_id': data_id},
        )
        # 角色校验，需要加上文件夹权限
        if _data_type_in_dashboard(data_type):
            data_tree, folder_allow_ids = get_folder_allow_ids([role_id], data_type, data_action_code)
            if data_id in folder_allow_ids:
                has_permission = True
        # 查看是否为创建者(创建者也有权限)
        if not has_permission:
            table_name = 'dashboard' if data_type in ('multiple_screen', 'self_service') else data_type
            has_permission = repository.data_is_exists(table_name, {'created_by': g.account, 'id': data_id})
        return bool(has_permission)
    return False


def check_data_permission_by_role_ids(role_ids, data_type, data_action_code, data_id=None, check_creator=True):
    # 如果有data_id，先判断是不是所有者
    if data_id and check_creator:
        flag = data_permission_repository.get_permission_by_own(data_id, g.account, data_type)
        if flag:
            return True

    # 先检查 user_role_all_data_permission 数据
    has_all = data_permission_repository.get_permission_by_role_ids(role_ids, data_type, data_action_code)
    if has_all:
        return True

    if data_id:
        has_permission = data_permission_repository.get_permission_by_role_ids_and_data_id(
            role_ids, data_type, data_action_code, data_id
        )
        if has_permission:
            return bool(has_permission)
        # 之前的没有权限才判断目录权限，做到影响最小
        # 目前只有报告有目录权限
        if _data_type_in_dashboard(data_type):
            data_tree, folder_allow_ids = get_folder_allow_ids(role_ids, data_type, data_action_code)
            if data_id in folder_allow_ids:
                return True
    return False


def _data_type_in_dashboard(data_type):
    if data_type:
        return data_type.lower() in ('dashboard', 'self_service', 'report_center', 'large_screen')
    return False


def _data_type_in_dataset(data_type):
    if data_type:
        return data_type.lower() == 'dataset'
    return False


def _child_dashboard_convert_dashboard(data_type, data_id):
    """
    报告如果是子报告，则需要转换为根报告后进行权限校验
    :param data_type:
    :param data_id:
    :return:
    """
    if data_type == 'dashboard':
        # pylint: disable=C0415
        from dashboard_chart.services import dashboard_service
        root_dashboard = dashboard_service.get_root_dashboard_by_dashboard_id(data_id)
        return root_dashboard.get("id")
    return data_id


def check_tenant_expire(permissions):
    from components.common_service import expire_func_auth
    permissions = permissions.split('-')
    if len(permissions) != 2:
        raise UserError(message=u'permissions参数错误')
    func_code, action = permissions[0], permissions[1]
    expire_func_auth(func_code, action)
