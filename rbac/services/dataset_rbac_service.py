"""
数据集的数据权限
1. 登录用户.
列: 登录用户的角色
过滤条件: 取登录用户的角色s的条件设置

2. 非登录用户

列: 取数据集创建者的角色s的列
过滤条件: 取数据集创建者的角色s的条件设置
"""
# pylint: disable=C0415
# pylint: skip-file
import datetime
import json
from decimal import Decimal

from base import repository
from base.dmp_constant import DATASET_ROW_PERMISSION_USER_ID
from base.enums import SqlWhereOperator, DatasetPermissionType, UserChannel, DashboardDataMsgCode
from dataset.repositories import dataset_repository
from dataset.services import dataset_rbac_result_service, dataset_field_service
from dmplib import config
from dmplib.hug import g
from dmplib.redis import conn as conn_redis
from dmplib.constants import ADMIN_ROLE_ID, ADMINISTRATORS_GROUP_ID
from dmplib.utils.errors import UserError
from rbac.services import user_org_service
from rbac.services.data_permissions import filter_data

from self_service.repositories.external_subject_repository import get_subject_detail, is_external_subject
from self_service.services.external_api_service import get_permission_filter_values
from user.services import user_service
from ..repositories import data_permission_repository
from typing import Any, Dict, List
import logging
from rbac.models import DatasetPermission

logger = logging.getLogger(__name__)


def get_permission_data(
    dataset_id: str, role_ids: List[str], user_id: str = None, group_ids: None = None, external_subject_id: str = None
) -> Dict[str, List[Any]]:
    """
    获取角色的数据集限制
    返回空表示没有限制
    :param group_ids:
    :param role_ids:
    :param dataset_id:
    :param user_id:
    :param external_subject_id:
    :return:
    """
    if not isinstance(role_ids, list):
        raise ValueError('role_ids should be list')

    if not group_ids:
        group_ids = []

    # 管理员有全部权限
    if ADMIN_ROLE_ID in role_ids or ADMINISTRATORS_GROUP_ID in group_ids:
        return {'filters': [], 'fields': []}
    permission_role_ids = (
        data_permission_repository.get_permission_role_ids(external_subject_id, role_ids, 'external_subject')
        if external_subject_id
        else data_permission_repository.get_permission_role_ids(dataset_id, role_ids, 'dataset')
    )
    filter_json_strs = (
        data_permission_repository.get_dataset_filter_by_role(
            external_subject_id, permission_role_ids, "external_subject"
        )
        if external_subject_id
        else data_permission_repository.get_dataset_filter_by_role(dataset_id, permission_role_ids, "dataset")
    )
    # 处理下数据库已经有的垃圾数据
    filter_json_strs = [row for row in filter_json_strs if row.get('dataset_filter') or row.get('hide_field_ids')]
    if not filter_json_strs:
        return {'filters': [], 'fields': []}
    # 有一种情况，一个用户属于2个角色，一个角色设置了行列权限，另一个没有设置
    if len(filter_json_strs) != len(permission_role_ids):
        # 查询拥有数据集权限的角色
        # 查出没有设置行列权限的角色是否有数据集权限
        has_filter_role_ids = [single_row.get('role_id') for single_row in filter_json_strs]
        no_filter_role_ids = list(set(permission_role_ids).difference(set(has_filter_role_ids)))
        # 如果一个角色没有设置行列权限，且有该数据集权限，则可以读取所有数据
        dataset = dataset_repository.get_dataset(dataset_id)
        action_code = "external_subject-view" if external_subject_id else "dataset-view"
        if filter_data(action_code, no_filter_role_ids, dataset, ignore_error=True):
            return {'filters': [], 'fields': []}

    dataset_field_dict = get_dataset_field_dict(dataset_id, external_subject_id)
    filters = []
    hide_field_ids = []
    # fix bug[ID1074237] 当用户找不到匹配的权限过滤值时应该抛出异常, 使用flag数组标记用户是否有权限值
    api_filter_flags = []
    for row in filter_json_strs:
        new_filters_row = []
        filters_row = json.loads(row['dataset_filter']) if row['dataset_filter'] else []
        # 用户关联数据集权限解析， 对于数据表user_organization。
        # 例如：数据集字段：城市，操作符：relate_user_organization，值：层级(公司)
        # 解析为：数据集字段：城市，操作符：in，值：北京公司，天津公司
        for _filter in filters_row:
            new_filters_row = deal_single_filter(
                new_filters_row, user_id, _filter, dataset_field_dict, api_filter_flags
            )
        filters.append(new_filters_row)
        _ids = row['hide_field_ids'].split(',') if row['hide_field_ids'] else []
        _new_ids = []
        for filter_id in _ids:
            # 数据集字段被删除，对应的权限字段也需要删除
            if dataset_field_dict.get(filter_id):
                _new_ids.append(filter_id)
        hide_field_ids.append(list(set(_new_ids)))
    # 若flag都为False则抛出异常, 当前用户没有数据权限
    if len(api_filter_flags) > 0 and not any(api_filter_flags):
        raise UserError(code=DashboardDataMsgCode.NoPermissionData.value, message="当前用户或角色没有数据访问权限！")
    return {'filters': filters, 'fields': hide_field_ids}


def generate_permission_filter(user_id, dataset_id, filter_json):
    dataset_field_dict = get_dataset_field_dict(dataset_id)
    filters = []
    api_filter_flags = []
    new_filters_row = []
    for _filter in filter_json:
        new_filters_row = deal_single_filter(
            new_filters_row, user_id, _filter, dataset_field_dict, api_filter_flags
        )
    filters.append(new_filters_row)

    return {'filters': filters}


def _format_filter_value(value):
    if isinstance(value, datetime.datetime):
        return str(value)
    if isinstance(value, Decimal):
        return float(value)
    return value


def _judge_user_id(user_id):
    if not user_id:
        raise UserError(message="请指定用户！")


def deal_single_filter(new_filters_row, user_id, _filter, dataset_field_dict, api_filter_flags: list = None):
    filter_value = None
    is_used_dataset_filter = False  # 是否应用到权限数据集
    if _filter.get("operator") == DatasetPermissionType.UserOrg.value and _filter.get("col_value"):
        # 关联用户组织
        _judge_user_id(user_id)
        is_used_dataset_filter = True
        filter_value = user_org_service.get_user_org_name(user_id, _filter.get("col_value"))
    elif _filter.get("operator") == DatasetPermissionType.Dataset.value and _filter.get("dataset_permission_id"):
        # 关联数据集
        _judge_user_id(user_id)
        is_used_dataset_filter = True
        from dataset import external_query_service

        filter_value = external_query_service.get_user_dataset_permission_field_value(
            user_id, _filter.get("dataset_permission_id")
        )
    elif _filter.get("operator") == DatasetPermissionType.Api.value and _filter.get("col_value"):
        # 添加api权限是否使用缓存标识, 默认不使用缓存
        _filter["use_cache"] = False
        logger.exception("权限字段配置：{}".format(_filter))
        dataset_id = _filter.get('dataset_id') or ""
        is_external_subject_flag = bool(is_external_subject(dataset_id))
        external_subject_name = ""
        if is_external_subject_flag:
            external_subject_info = get_subject_detail(dataset_id)
            external_subject_name = external_subject_info.get('name')
        external_user_id = g.external_user_id if hasattr(g, 'external_user_id') else None
        if not external_user_id and config.get("ThirdParty.user_source_id"):
            third_user_info = get_third_user_info(config.get("ThirdParty.user_source_id"))
            logger.exception("第三方用户信息：{}".format(third_user_info))
            external_user_id = third_user_info.get("id") or getattr(g, "userid", "")
        filter_values = get_permission_filter_values(
            g.code,
            g.account,
            external_user_id=external_user_id,
            external_subject_id=dataset_id,
            external_subject_name=external_subject_name,
            is_external_subject=is_external_subject_flag,
            identifier=_filter.get("col_value")
        )
        # filter_values = [{"name": "分期", "identifier": "api.fq", "col_value": "*", "operator": "="}]
        if filter_values:
            identifier = _filter.get("col_value")
            filter_value = _get_target_filter_value(filter_values, identifier)
            if filter_value.get("col_value"):
                _filter["operator"] = filter_value.get("operator")
                _filter["col_value"] = _format_filter_value(filter_value.get("col_value"))
                api_filter_flags.append(True)
            else:
                # 外部api不返回权限过滤值则不查询数据
                _filter["operator"] = SqlWhereOperator.Eq.value
                _filter["col_value"] = None
                api_filter_flags.append(False)
        else:
            # 外部api不返回权限过滤值则不查询数据
            _filter["operator"] = SqlWhereOperator.Eq.value
            _filter["col_value"] = None
            api_filter_flags.append(False)
        is_used_dataset_filter = False

    if is_used_dataset_filter:
        # 有组织名称采用in查询
        if filter_value:
            _filter["operator"] = SqlWhereOperator.In.value
            _filter["col_value"] = [_format_filter_value(org_name.get('org_name')) for org_name in filter_value]
        # 无组织名称取反，不查询数据
        else:
            _filter["operator"] = SqlWhereOperator.Eq.value
            _filter["col_value"] = None

    # 数据集字段被删除，对应的权限字段也需要删除
    if dataset_field_dict.get(_filter.get("dataset_field_id")):
        new_filters_row.append(_filter)
    return new_filters_row


def get_dataset_field_dict(dataset_id: str, external_subject_id: str = None):
    dataset_field_dict = {}
    if external_subject_id:
        fields = dataset_field_service.get_original_external_subject_fields(external_subject_id)
    else:
        fields = repository.get_data(
            "dataset_field", {"dataset_id": dataset_id}, fields=["id", "col_name"], multi_row=True
        )
    for dataset_field in fields:
        dataset_field_dict[dataset_field.get("id")] = dataset_field
    return dataset_field_dict


def _generate_user_cache_key(dataset_id: str, user_id: str) -> str:
    return 'dmp:dataset:permission:%s:%s' % (dataset_id, user_id or '')


def _use_cahe(filters):
    if isinstance(filters, list):
        for filter_rows in filters:
            for filter_row in filter_rows:
                if "use_cache" in filter_row:
                    return filter_row.get("use_cache")
    return True


def get_permission_of_user(dataset_id: str, user_id: str, external_subject_id: str = None) -> Dict[str, List[Any]]:
    """
    获取数据集数据权限
    :param dataset_id:
    :param user_id:
    :param external_subject_id:
    :return:
    """
    # todo 此处需要考虑权限
    has_external_role_flag = has_external_role()
    cache = conn_redis()
    real_id = external_subject_id or dataset_id
    if not has_external_role_flag:
        _cache_key = _generate_user_cache_key(real_id, user_id)
        data_cache = cache.hget(DATASET_ROW_PERMISSION_USER_ID, _cache_key)
        if data_cache is not None:
            # 角色权限开关控制是否使用缓存
            close_role_privilege_cache = config.get("Cache.close_role_privilege_cache", 0)
            # 0 开启  1 关闭
            if close_role_privilege_cache is None or int(close_role_privilege_cache) == 0:
                return json.loads(data_cache)
    role_ids = user_service.get_cur_role_id(user_id) or []
    # 支持外部传入角色
    if has_external_role_flag:
        role_ids = g.customize_roles
    auth_data = get_permission_data(dataset_id, role_ids, user_id, external_subject_id=external_subject_id)

    if auth_data and not has_external_role_flag and _use_cahe(auth_data.get("filters")):
        cache.hset(DATASET_ROW_PERMISSION_USER_ID, _cache_key, json.dumps(auth_data))

    return auth_data


def get_permission_of_dataset(dataset_id: str, user_id: str, external_subject_id: str = None):
    """
    数据集权限独立：获取数据集数据权限
    :param dataset_id:
    :param user_id:
    :param external_subject_id:
    :return:
    """
    filter_json_str = data_permission_repository.get_dataset_permission_filter(dataset_id)
    # 处理下数据库已经有的垃圾数据
    filter_json_str = [row for row in filter_json_str if row.get('dataset_filter') or row.get('hide_field_ids')]
    if not filter_json_str:
        return {'filters': [], 'fields': []}

    dataset_field_dict = get_dataset_field_dict(dataset_id, external_subject_id)
    filters = []
    hide_field_ids = []
    for row in filter_json_str:
        new_filters_row = []
        filters_row = json.loads(row['dataset_filter']) if row['dataset_filter'] else []
        for _filter in filters_row:
            new_filters_row = deal_single_filter(
                new_filters_row, user_id, _filter, dataset_field_dict, []
            )
        filters.append(new_filters_row)
        _ids = row['hide_field_ids'].split(',') if row['hide_field_ids'] else []
        _new_ids = []
        for filter_id in _ids:
            # 数据集字段被删除，对应的权限字段也需要删除
            if dataset_field_dict.get(filter_id):
                _new_ids.append(filter_id)
        hide_field_ids.append(list(set(_new_ids)))
    return {'filters': filters, 'fields': hide_field_ids}


def get_permission_of_role(dataset_id, role_ids, user_id):
    """
    获取数据集数据权限
    :param dataset_id:
    :param role_ids:
    :param user_id:
    :return:
    """
    dataset = dataset_rbac_result_service.get_dataset(dataset_id)
    if not dataset:
        raise UserError('数据集%s不存在' % dataset_id)
    auth_data = get_permission_data(dataset_id, role_ids, user_id)
    return auth_data


def _get_target_filter_value(filter_values, identifier):
    for filter_value in filter_values:
        if filter_value.get('identifier') == identifier:
            return filter_value
    raise UserError("未找到对应的权限过滤字段值")


def has_external_role():
    if hasattr(g, 'customize_roles') and g.customize_roles:
        return True
    return False


def get_third_user_info(user_source_id: str) -> dict:
    rv = {
        'id': '',
        'account': '',
    }
    user_id = g.userid if hasattr(g, 'userid') else ''
    if not user_id:
        return rv
    if not user_source_id:
        return rv
    user = repository.get_data('user', {'id': user_id}, ['account', 'user_source_id'])
    if not user:
        return rv
    # 如果用户本身来源渠道就是该渠道，不需要去查映射关系表, 直接获取渠道中用户的第三方id
    if user.get('user_source_id') == user_source_id:
        user_source_type = repository.get_data_scalar('user_source', {'id': user_source_id}, 'type')
        is_erp_user_source = user_source_type == UserChannel.Erp.value
        if is_erp_user_source:
            third_user_id = repository.get_data_scalar('external_user', {'account': user.get('account')}, 'id')
        else:
            third_user_id = repository.get_data_scalar('user_source_user', {'user_source_id': user_source_id, 'account': user.get('account')}, 'user_id')
        return {
            'id': third_user_id,
            'account': user.get('account'),
        }

    # 否则查看用户是否有该渠道的映射关系
    third_user_id = repository.get_data_scalar('user_source_user_map',
                                               {'dmp_user_id': user_id, 'user_source_id': user_source_id},
                                               'third_user_id')
    if not third_user_id:
        return rv
    return {
        'id': third_user_id,
        'account': user.get('account')
    }


def grant_dataset_permission(model: DatasetPermission):
    """"
    数据集授权
    """
    # 参数校验
    model.validate()
    if model.dataset_filter:
        model.dataset_filter = json.dumps(model.dataset_filter)
    # 判断新增/编辑
    fields = ["dataset_id", "dataset_filter", "data_type", "type"]
    if repository.data_is_exists("dataset_permission_filter", {"dataset_id": model.dataset_id}):
        repository.update_data(
            "dataset_permission_filter", model.get_dict(fields), condition={"dataset_id": model.dataset_id}
        )
    else:
        repository.add_data("dataset_permission_filter", model.get_dict(fields))


def query_dataset_permission_info(dataset_id: str):
    """
    获取数据集权限信息
    """
    permission_filter = repository.get_data("dataset_permission_filter", {"dataset_id": dataset_id})
    if not permission_filter:
        return {}
    permission_filter['dataset_filter'] = json.loads(permission_filter.get("dataset_filter")) if permission_filter.get("dataset_filter") else ""
    return permission_filter


