"""
用户相关操作
"""
from collections import defaultdict
from dmplib.saas.project import get_db
from base import repository
from rbac.models import RBACUserListQuery
import datetime


def delete_group_for_user(user_id, group_id):
    """
    delete用户的用户组
    :param group_id:
    :param user_id:
    :return:
    """
    return repository.delete_data("user_group_user", {"user_id": user_id, "group_id": group_id})


def get_all_roles_of_user(user_id, group_ids):
    """
    获取用户所属组和用户的角色id
    :param user_id:
    :param group_ids:
    :return:
    """
    db = get_db()
    if not group_ids:
        role_of_group = []
    else:
        role_of_group = db.query(
            'select role_id from user_group_role where group_id in %(group_ids)s', {'group_ids': group_ids}
        )

    role_of_user = db.query('select role_id from user_user_role where user_id=%(user_id)s', {'user_id': user_id})
    role_ids = [item['role_id'] for item in role_of_group] + [item['role_id'] for item in role_of_user]
    return list(set(role_ids))


def get_role_of_user(user_id):
    db = get_db()
    role_of_user = db.query('select role_id from user_user_role where user_id=%(user_id)s', {'user_id': user_id})
    return role_of_user


def get_user_by_id(userid):
    if not userid:
        return None

    db = get_db()
    return db.query_one('select id, group_id from user where id = %(userid)s', {'userid': userid})


def get_user_by_account(account):
    if not account:
        return None
    db = get_db()
    return db.query_one('select id, group_id from user where account=%(account)s', {'account': account})


def delete_user_group_role(user_id, group_id):
    """
    删除用户对应的用户组和角色
    :param user_id:
    :param group_id:
    :return:
    """
    with get_db() as db:
        # 刪除用戶和用戶組關係
        db.delete("user_group_user", {"user_id": user_id, "group_id": group_id}, commit=False)
        db.update('user', {'modified_on': datetime.datetime.now()}, {'id': user_id},
                  commit=False)  # 刷新用户修改时间，云助手增量同步依赖用户表的修改时间
        # 将用户移除用户组，不再删除所拥有的角色，因为将用户添加到用户组的时候，不会再将用户组的角色授权给用户，使用继承的方式
        # 刪除用戶組下角色和用戶關係

        db.commit()


def get_user_ids_by_role_ids(role_ids: list):
    if not role_ids:
        return []

    sql = 'SELECT user_id, role_id FROM user_user_role WHERE role_id in %(role_ids)s'
    params = {'role_ids': role_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_user_roles_by_user_ids(user_ids: list):
    # query roles
    sql_role = (
        'select a.role_id, b.name as role_name, a.user_id from user_user_role a '
        ' inner join user_role b on a.role_id=b.id where user_id in %(user_ids)s'
    )
    with get_db() as db:
        roles = db.query(sql_role, {'user_ids': user_ids})
    user_roles_map = defaultdict(list)
    user_role_ids_set = defaultdict(set)
    for role_item in roles:
        usr_id = role_item['user_id']
        if usr_id in user_role_ids_set[usr_id]:
            continue
        user_role_ids_set[usr_id].add(usr_id)
        user_roles_map[usr_id].append({'role_id': role_item['role_id'], 'role_name': role_item['role_name']})

    # query_group_roles
    sql_group_role = """
    select ur.id as role_id, ur.name as role_name, ugu.user_id from user_group_user ugu
    inner join user_group ug on ugu.group_id=ug.id
    INNER JOIN user_group_role ugr on ugr.group_id = ug.id
    INNER JOIN user_role ur on ur.id = ugr.role_id
    where ugu.user_id in %(user_ids)s
    """
    with get_db() as db:
        group_roles = db.query(sql_group_role, {'user_ids': user_ids})
    for group_role in group_roles:
        usr_id = group_role.get('user_id')
        if usr_id in user_role_ids_set[usr_id]:
            continue
        user_role_ids_set[usr_id].add(usr_id)
        user_roles_map[usr_id].append({'role_id': group_role['role_id'], 'role_name': group_role['role_name']})

    return user_roles_map


def get_user_groups_by_user_ids(user_ids: list):
    sql_group = (
        'select ugu.group_id, ug.name as group_name, ugu.user_id from user_group_user ugu '
        ' inner join user_group ug on ugu.group_id=ug.id where ugu.user_id in %(user_ids)s'
    )
    with get_db() as db:
        groups = db.query(sql_group, {'user_ids': user_ids})
    user_groups_map = {}
    for group_item in groups:
        usr_id = group_item['user_id']
        if usr_id not in user_groups_map:
            user_groups_map[usr_id] = []
        user_groups_map[usr_id].append({'group_id': group_item['group_id'], 'group_name': group_item['group_name']})
    return user_groups_map


def get_user_organization_by_user_ids(user_ids: list):
    sql_organization = 'select user_id, org_name, org_level from user_organization  ' 'where user_id in %(user_ids)s'
    with get_db() as db:
        organizations = db.query(sql_organization, {'user_ids': user_ids})
    user_organizations_map = {}
    for organization_item in organizations:
        usr_id = organization_item['user_id']
        if usr_id not in user_organizations_map:
            user_organizations_map[usr_id] = []
        user_organizations_map[usr_id].append(
            {'org_name': organization_item['org_name'], 'org_level': organization_item['org_level']}
        )
    return user_organizations_map


def get_user_ids_by_group_ids(group_ids: list):
    if not group_ids:
        return []

    sql = 'SELECT user_id FROM user_group_user WHERE group_id in %(group_ids)s'
    params = {'group_ids': group_ids}
    with get_db() as db:
        return db.query_columns(sql, params) or []


def get_user_ids_by_external_group_ids(external_group_ids: list):
    if not external_group_ids:
        return []

    sql = 'SELECT user_id FROM user_organization WHERE group_id in %(external_group_ids)s'
    params = {'external_group_ids': external_group_ids}
    with get_db() as db:
        return db.query_columns(sql, params) or []


def get_groups_ids_by_role_ids(role_ids: list):
    if not role_ids:
        return []

    sql = 'SELECT group_id FROM user_group_role WHERE role_id in %(role_ids)s'
    params = {'role_ids': role_ids}
    with get_db() as db:
        return db.query_columns(sql, params) or []


def get_all_users_by_query_model(query_model: RBACUserListQuery):
    user_ids = query_model.user_ids
    if user_ids is not None and not user_ids:
        return {'items': [], 'total': 0}

    sql = 'SELECT u.id AS user_id, u.`name`, u.account FROM user AS u'
    wheres = []
    params = {}

    if user_ids:
        wheres.append('u.id in %(user_ids)s')
        params['user_ids'] = user_ids

    if query_model.keyword:
        wheres.append('(u.`name` LIKE %(keyword)s or u.account LIKE %(keyword)s)')
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            sorts.append(sort.id + ' ' + sort.method)

    if wheres:
        sql += ' WHERE ' + ' AND '.join(wheres)

    sql += ' ORDER BY ' + (','.join(sorts) if sorts else 'u.`created_on` DESC')

    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model.get_result_dict()
