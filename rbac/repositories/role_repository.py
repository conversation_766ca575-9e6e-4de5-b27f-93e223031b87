from base import repository
from dmplib.saas.project import get_db
from collections import defaultdict
from dmplib import config

MYSQL_AES_KEY = config.get('Security.mysql_aes_key', 'qwertyuiasdfghjk')

def list_roles(page, page_size, keywords=None):
    """
    分页角色
    Args:
        page: int
        page_size: int
        keywords: string

    Returns: total, items

    """
    db = get_db()
    offset = (page - 1) * page_size
    condition = ''
    params = None
    if keywords is not None and keywords != '':
        condition = ' where name like %(keywords)s '
        params = {'keywords': '%{}%'.format(keywords)}
    total = db.query_scalar('select count(*) from user_role ' + condition, params)
    items = []
    if total > 0:
        if params is None:
            params = {}
        params['offset'] = offset
        params['page_size'] = page_size
        items = db.query(
            'select * from user_role ' + condition + ' order by id desc limit %(offset)s, %(page_size)s ', params
        )
    return total, items


def check_user_role_group_exists(group_id):
    """
    检查用户角色组是否存在
    :param group_id:
    :return:
    """
    ret = {'c': 0}
    sql = "select count(1) as c from user_role_group where id =%(id)s"
    with get_db() as db:
        ret = db.query_one(sql, {'id': group_id})
    return ret['c']


def check_user_role_exists_by_name(role_name, parent_id=''):
    """
    检查角色名是否已存在
    :param role_name:
    :param parent_id:
    :return:
    """
    where = {'name': role_name, 'parent_id': parent_id}
    return repository.get_data_scalar('user_role', where, 'id')


# pylint: disable=R1718
def get_user_name_user_ids(user_ids):
    sql = """ select name from user where id in %(user_ids)s"""
    with get_db() as db:
        return db.query(sql, {"user_ids": user_ids})


def get_role_name_role_ids(role_ids):
    if not role_ids:
        return []
    sql = """ select name from user_role where id in %(role_ids)s"""
    with get_db() as db:
        return db.query(sql, {"role_ids": role_ids})


def add_role_for_user(model):
    db = get_db()
    count = db.query_scalar(
        'select count(*) from user_user_role where user_id=%(user_id)s and role_id=%(role_id)s',
        {'user_id': model['user_id'], 'role_id': model['role_id']},
    )
    if count == 0:
        db.insert('user_user_role', model)
    return True


def add_role_for_group(model):
    db = get_db()
    count = db.query_scalar(
        'select count(*) from user_group_role where group_id=%(group_id)s and role_id=%(role_id)s',
        {'group_id': model['group_id'], 'role_id': model['role_id']},
    )
    if count == 0:
        db.insert('user_group_role', model)
    return True


def delete_roles_for_group(group_id):
    if not group_id:
        raise ValueError('group_id 不能为空')
    db = get_db()
    params = {'group_id': group_id}
    db.exec_sql('delete from user_group_role where group_id=%(group_id)s', params)
    return True


def delete_role_for_user(userid, role_ids, all_roles=True):
    db = get_db()
    if all_roles:
        db.exec_sql('delete from user_user_role where user_id=%(user_id)s ', {'user_id': userid})
        return True

    if not userid or not role_ids:
        return False

    db.exec_sql(
        'delete from user_user_role where user_id=%(user_id)s and role_id in %(role_ids)s',
        {'user_id': userid, 'role_ids': role_ids},
    )
    return True


def query_user_group_roles(user_id):
    """
    根据用户ID查询该用户所属用户组的角色
    :param user_id:
    :return:
    """
    sql = """
        select user_role.id ,user_role.name, user_group.name as group_name from user_role
        INNER JOIN user_group_role on user_group_role.role_id = user_role.id
        INNER JOIN user_group_user on user_group_user.group_id = user_group_role.group_id
        INNER JOIN user_group on user_group.id = user_group_user.group_id
        INNER JOIN `user` on `user`.id = user_group_user.user_id
        where `user`.id  = %(user_id)s
    """
    with get_db() as db:
        return db.query(sql, {"user_id": user_id})


def delete_role_relation(role_id_list):
    db = get_db()
    params = {'role_id': role_id_list}
    db.exec_sql('delete from user_user_role where role_id in %(role_id)s', params)
    db.exec_sql('delete from user_group_role where role_id in %(role_id)s', params)
    db.exec_sql('delete from user_role_func where role_id in %(role_id)s', params)
    # 删除dataset_role_filter表数据
    db.exec_sql('delete from dataset_role_filter where role_id in %(role_id)s', params)
    return True


def delete_role_for_group(group_id, role_id):
    if not group_id or not role_id:
        return False

    db = get_db()
    db.exec_sql(
        'delete from user_group_role where group_id=%(group_id)s and role_id=%(role_id)s',
        {'group_id': group_id, 'role_id': role_id},
    )
    return True


def list_roles_of_user(userid):
    db = get_db()
    return db.query(
        'select b.id, b.name, a.created_on, a.modified_on, a.created_by, a.modified_by '
        'from user_user_role a inner join user_role b on a.role_id=b.id where a.user_id=%(user_id)s'
        ' order by a.created_on desc',
        {'user_id': userid},
    )


def list_roles_of_group(group_id):
    db = get_db()
    return db.query(
        'select b.id, b.name, a.created_on, a.modified_on, a.created_by, a.modified_by '
        'from user_group_role a inner join user_role b on a.role_id=b.id where a.group_id=%(group_id)s'
        ' order by a.created_on desc',
        {'group_id': group_id},
    )


def list_users_of_role(role_id, has_or_not, keywords, page, page_size):
    """
    查询拥有角色或没拥有角色的用户列表
    :param role_id: 拥有该角色的id
    :param has_or_not: 是查询拥有或不拥有该角色
    :param keywords:
    :param page:
    :param page_size:
    :return:
    """
    db = get_db()
    offset = (page - 1) * page_size
    if not has_or_not:
        condition = 'where (a.role_id is null or a.role_id <> %(role_id)s)'
    else:
        condition = 'where a.role_id=%(role_id)s'

    params = {'role_id': role_id}
    if keywords:
        condition = ' '.join([condition, ' and (b.name like %(keywords)s or b.account like %(keywords)s)'])
        params['keywords'] = '%{}%'.format(keywords)

    sql_total = ' '.join(
        ['select count(DISTINCT b.id) from user b  left join user_user_role a  on a.user_id=b.id ', condition]
    )
    total = db.query_scalar(sql_total, params)

    sql_rows = ' '.join(
        [
            f"select b.id, max(b.account) as `account`, max(b.email) as `email`, max(b.name) as `name`, max(b.mobile) as mobile, max(b.created_on) as `created_on`, "
            'max(b.modified_on), max(b.created_by), max(b.modified_by), d.name as group_name  '
            'from user b left join user_user_role a on a.user_id=b.id',
            'left join user_group_user c on b.id = c.user_id '
            'left join user_group d on c.group_id = d.id ',
            condition,
            'group by b.id order by b.created_on desc, b.account limit %(offset)s, %(page_size)s',
        ]
    )
    params['offset'] = offset
    params['page_size'] = page_size
    items = db.query(sql_rows, params)

    from user.repositories.user_repository import decrypt_field

    for item in items:
        item["mobile"] = decrypt_field(item.get("mobile"))
    return total, items


def get_role_data_by_role_ids(role_ids: list):
    if not role_ids:
        return []

    sql = 'SELECT `id`, `name` as role_name FROM user_role WHERE id in %(role_ids)s'
    params = {'role_ids': role_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_user_ids_by_role_ids(role_ids: list):
    if not role_ids:
        return []

    sql = 'SELECT user_id FROM user_user_role WHERE role_id in %(role_ids)s'
    params = {'role_ids': role_ids}
    with get_db() as db:
        return db.query_columns(sql, params) or []


def get_all_role_data_by_user_ids(user_ids: list):
    sql = 'SELECT user_id, role_id FROM user_user_role WHERE user_id in %(user_ids)s'
    params = {'user_ids': user_ids}
    with get_db() as db:
        user_role_mapping = db.query(sql, params)

    sql = 'SELECT user_id, group_id FROM user_group_user WHERE user_id in %(user_ids)s'
    with get_db() as db:
        user_group_mapping = db.query(sql, params)
    group_ids = list({r['group_id'] for r in user_group_mapping})
    user_group_ids = defaultdict(list)
    for r in user_group_mapping:
        user_group_ids[r['user_id']].append(r['group_id'])

    group_role_ids = defaultdict(list)
    if group_ids:
        sql = 'SELECT group_id, role_id FROM user_group_role WHERE group_id in %(group_ids)s'
        params = {'group_ids': group_ids}
        with get_db() as db:
            group_role_mapping = db.query(sql, params)
        for r in group_role_mapping:
            group_role_ids[r['group_id']].append(r['role_id'])

    user_role_id_map = defaultdict(list)
    all_role_ids = []
    for r in user_role_mapping:
        all_role_ids.append(r['role_id'])
        user_role_id_map[r['user_id']].append(r['role_id'])

    for user_id, group_ids in user_group_ids.items():
        for group_id in group_ids:
            if group_id not in group_role_ids:
                continue
            role_ids = group_role_ids[group_id]
            all_role_ids.extend(role_ids)
            user_role_id_map[user_id].extend(role_ids)

    for user_id, role_ids in user_role_id_map.items():
        user_role_id_map[user_id] = list(set(role_ids))

    return user_role_id_map, list(set(all_role_ids))


def list_groups_of_role(role_id, keywords, page, page_size):
    """
    查询拥有角色或没拥有角色的用户列表
    :param role_id: 拥有该角色的id
    :param keywords:
    :param page:
    :param page_size:
    :return:
    """
    db = get_db()
    offset = (page - 1) * page_size
    condition = 'where a.role_id=%(role_id)s'

    params = {'role_id': role_id}
    if keywords:
        condition = ' '.join([condition, ' and b.name like %(keywords)s'])
        params['keywords'] = '%{}%'.format(keywords)

    sql_total = ' '.join(
        ['select count(DISTINCT b.id) from user_group b  left join user_group_role a  on a.group_id=b.id ', condition]
    )
    total = db.query_scalar(sql_total, params)

    sql_rows = ' '.join(
        [
            'select b.id, b.name, b.parent_id, b.code, b.created_on, '
            'b.modified_on, b.created_by, b.modified_by, a.is_include_child '
            'from user_group b left join user_group_role a on a.group_id=b.id',
            condition,
            'group by b.id order by a.created_on desc limit %(offset)s, %(page_size)s',
        ]
    )
    params['offset'] = offset
    params['page_size'] = page_size
    items = db.query(sql_rows, params)
    result = [dict(t) for t in set([tuple(d.items()) for d in items])]

    # 查看组内有多少个用户
    group_ids = [d.get("id") for d in items]
    group_total = {}
    if group_ids:
        sql_group_total = (
            "select count(user_id) as total, user_group_user.group_id from user_group_user "
            "inner join user on user_group_user.user_id = user.id where user_group_user.group_id in %(group_ids)s "
            "group by user_group_user.group_id"
        )
        group_total_result = db.query(sql_group_total, {"group_ids": group_ids})
        # 变成dict结构，方便插入result中
        group_total = {row.get("group_id"): row.get("total") for row in group_total_result}
    for row in result:
        row["total"] = group_total.get(row.get("id"), 0)

    return total, result


def get_user_count_by_group_ids(group_ids: list):
    db = get_db()
    group_total_result = 0
    if group_ids:
        sql_group_total = (
            "select count(distinct user_id) as total from user_group_user "
            "inner join user on user_group_user.user_id = user.id where user_group_user.group_id in %(group_ids)s "
        )
        data = db.query(sql_group_total, {"group_ids": group_ids})
        group_total_result = data[0].get('total') if data else 0
    return group_total_result


def add_role_for_group_user(list_data: list):
    return repository.replace_list_data("user_user_role", list_data, fields=["user_id", "role_id"])


def delete_role_for_group_user(role_id, user_ids):
    if not user_ids :
        return
    sql = "DELETE FROM user_user_role where role_id=%(role_id)s AND user_id in %(user_ids)s"
    params = {"role_id": role_id, "user_ids": user_ids}
    with get_db() as db:
        db.exec_sql(sql, params)


def get_user_role_by_id(role_id: str):
    return repository.get_one('user_role', {'id': role_id})


def get_id_by_level_code(role_group_id: str, level_code: str):
    return repository.get_column('user_role',
                                 {'role_group_id': role_group_id, "level_code like": level_code + '%'}, 'id')


def get_user_role_by_level_code(role_group_id: str, level_code: str):
    return repository.get_data('user_role', {'role_group_id': role_group_id, "level_code like": level_code + '%'},
                               multi_row=True)


def get_user_role_list_by_ids(role_id_list):
    """
    获取角色列表
    :param role_id_list:
    :return:
    """
    if not role_id_list:
        return False
    return repository.get_list('user_role', {'id': role_id_list}, fields=['id', 'name'])


def get_all_user_by_role_ids(role_id_list):
    """
    按角色id获取所有的用户（用户不会去重）
    :param role_id_list: 角色的id
    :return:
    """
    sql = """
    select b.id, b.account, b.name, a.role_id
    from user b left join user_user_role a on a.user_id=b.id
    where a.role_id in %(role_ids)s
    order by a.created_on desc;
    """
    params = {'role_ids': role_id_list}
    with get_db() as db:
        result = db.query(sql, params)
    return result


def get_all_group_by_role_ids(role_id_list):
    """
    按角色id获取所有的组织（组织不会去重）
    :param role_id_list: 角色的id
    :return:
    """
    sql = """
    select b.id, b.name, a.role_id 
    from user_group b left join user_group_role a on a.group_id=b.id
    where a.role_id in %(role_ids)s
    order by a.created_on desc;
    """
    params = {'role_ids': role_id_list}
    with get_db() as db:
        result = db.query(sql, params)
    return result


def get_user_id_by_role_ids(role_id_list):
    """
    按角色id获取所有的user_id（用户去重）
    :param role_id_list: 角色的id
    :return:
    """
    sql = """
    select distinct b.id
    from user b left join user_user_role a on a.user_id=b.id
    where a.role_id in %(role_ids)s
    """
    params = {'role_ids': role_id_list}
    with get_db() as db:
        result = db.query_columns(sql, params)
    return result


def get_group_id_by_role_ids(role_id_list):
    """
    按角色id获取所有的组织id（组织去重）
    :param role_id_list: 角色的id
    :return:
    """
    sql = """
    select distinct b.id
    from user_group b left join user_group_role a on a.group_id=b.id
    where a.role_id in %(role_ids)s
    """
    params = {'role_ids': role_id_list}
    with get_db() as db:
        result = db.query_columns(sql, params)
    return result


def get_user_id_by_group_ids(group_id_list):
    """
    按组织id获取所有的user_id（用户去重）
    :param group_id_list: 组织的id
    :return:
    """
    sql = """
    select distinct user_id from user_group_user 
    inner join user on user_group_user.user_id = user.id 
    where user_group_user.group_id in %(group_ids)s
    """
    params = {'group_ids': group_id_list}
    with get_db() as db:
        result = db.query_columns(sql, params)
    return result
