"""
数据权限
"""
import re
from dmplib.redis import conn as conn_redis
from base import repository
from base.dmp_constant import DEFAULT_DATA_PERMISSIONS, ALL_REPORT_DATA_ACTIONS, USER_OWN_CACHE_KEY
from dmplib.constants import ADMIN_ROLE_ID

from components.global_utils import verify_sql_params
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError

from typing import List


def get_dataset_filter(dataset_id, data_type='dataset'):
    if not dataset_id:
        return []
    db = get_db()
    return db.query(
        'select role_id, dataset_filter, hide_field_ids '
        ' from dataset_role_filter where dataset_id=%(dataset_id)s and data_type=%(data_type)s'
        'order by created_on desc',
        {'dataset_id': dataset_id, 'data_type': data_type},
    )


def get_dataset_filter_by_dataset_ids(dataset_ids: list):
    if not dataset_ids:
        return []
    sql = """SELECT role_id, dataset_id, dataset_filter, hide_field_ids FROM dataset_role_filter
    WHERE dataset_id in %(dataset_ids)s"""
    params = {'dataset_ids': dataset_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_filter_by_role_ids(role_ids: list):
    if not role_ids:
        return []
    sql = """SELECT role_id, dataset_id, dataset_filter, hide_field_ids FROM dataset_role_filter
    WHERE role_id in %(role_ids)s"""
    params = {'role_ids': role_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_filter_by_role(data_id: str, role_ids: list, data_type: str = "dataset"):
    if not data_id:
        raise UserError(400, '参数不能为空')
    if not role_ids or len(role_ids) == 0:
        return []

    db = get_db()
    return db.query(
        'select role_id, dataset_filter, hide_field_ids from dataset_role_filter where dataset_id=%(dataset_id)s '
        'and data_type =%(data_type)s'
        'and role_id in %(role_ids)s',
        {'dataset_id': data_id, 'data_type': data_type, 'role_ids': role_ids},
    )


def get_dataset_permission_filter(data_id: str):
    if not data_id:
        raise UserError(400, '参数不能为空')

    db = get_db()
    return db.query(
        'select dataset_id, dataset_filter, hide_field_ids from dataset_permission_filter where dataset_id=%(dataset_id)s ',
        {'dataset_id': data_id},
    )


def data_permission_add(model):
    data = model.get_dict(['role_id', 'data_id', 'data_type', 'data_action_code'])
    repository.add_data('user_role_data_permission', data)

    return model.role_id


def get_data_permissions_of_ids(soure_type, ids):
    sql = '''select id, data_action_code from user_role_data_permission WHERE datasource_type=%(datasource_type)s AND
            id in %(ids)s)'''
    with get_db() as db:
        return db.query(sql, {'datasource_type': soure_type, 'ids': ids})


def get_id_by_name(name, table_name):
    with get_db() as db:
        get_id_sql = 'select sql_calc_found_rows id FROM {} WHERE name=%(name)s'.format(table_name)
        result = db.query_one(get_id_sql, {'name': name})
        return result.get('id')


def get_data_list(query_model, table_name):
    """
    获取数据的列表
    :param query_model:
    :return:
    """
    # 多屏的表为dashboard
    multiple_screen = False
    application_type = 0
    if table_name == 'multiple_screen':
        table_name = 'dashboard'
        multiple_screen = True
    if table_name == 'self_service':
        table_name = 'dashboard'
        application_type = 1
    if table_name == 'report_center':
        table_name = 'dashboard'
        application_type = [6, 5]
    if table_name == 'external_subject':
        table_name = 'dataset_external_subject'

    # table_name无法使用params预防sql注入，需要自己判断
    verify_sql_params(table_name)

    sql = """select id, name from {} """.format(table_name)

    params = {}
    wheres = []
    order_by = ''
    if query_model.parent_id:
        wheres.append('`parent_id` = %(parent_id)s')
        params['parent_id'] = query_model.parent_id
    if query_model.level_code:
        wheres.append('`level_code` like %(level_code)s')
        params['level_code'] = query_model.level_code + '%'
    # 数据集和报告中有目录，需要排除目录
    if table_name in ['dataset', 'dashboard']:
        wheres.append('`type` != %(type)s')
        params['type'] = 'FOLDER'
    if table_name == 'dataset':
        wheres.append("`type` != 'EXTERNAL_SUBJECT'")
    if table_name == 'dashboard':
        wheres.append('`is_multiple_screen` = %(is_multiple_screen)s')
        # wheres.append('`application_type` = %(application_type)s')
        if isinstance(application_type, (tuple, list)):
            wheres.append('`application_type` in %(application_type)s')
        else:
            wheres.append('`application_type` = %(application_type)s')
        params['is_multiple_screen'] = '1' if multiple_screen else '0'
        params['application_type'] = application_type
        # 过滤模板内置数据
        wheres.append('`build_in` != %(build_in)s')
        params['build_in'] = '1'
    elif table_name == 'application':
        order_by = ' ORDER BY `rank`,`created_on` '
        wheres.append('`is_buildin` = %(is_buildin)s')
        params['is_buildin'] = '0'
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += order_by or ' ORDER BY `modified_on` DESC, `id` DESC'
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql_with_limit = sql + ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql_with_limit, params)
    return query_model


def get_data_permissions_actions(role_id, data_type):
    actions_name = {k.get('action_code'): k.get('action_name') for k in ALL_REPORT_DATA_ACTIONS}
    sql = """select data_action_code from user_role_all_data_permission WHERE role_id=%(role_id)s
                  and data_type=%(data_type)s;"""
    with get_db() as db:
        actions = db.query(sql, {'role_id': role_id, 'data_type': data_type})
        # 添加名称
        return [
            {'action_code': k.get('data_action_code'), 'action_name': actions_name.get(k.get('data_action_code'))}
            for k in actions
        ]


def get_folder_data_permissions_actions(role_id, data_type):
    actions_name = {k.get('action_code'): k.get('action_name') for k in ALL_REPORT_DATA_ACTIONS}
    sql = """select data_id, data_action_code from user_role_folder_data_permission WHERE role_id=%(role_id)s
                  and data_type=%(data_type)s;"""
    with get_db() as db:
        datas = db.query(sql, {'role_id': role_id, 'data_type': data_type})
        # 将当前结构[{'data_id': '39e581e3-63f7-c8e1-7de2-3bdde2e80848', 'data_action_code': 'view'}]
        # 修改为 {‘39e581e3-63f7-c8e1-7de2-3bdde2e80848’: [{'action_code': 'view','action_name': '查看'}]}
        result = {}
        for data in datas:
            _data = [
                {
                    'action_code': data.get('data_action_code'),
                    'action_name': actions_name.get(data.get('data_action_code')),
                }
            ]
            if result.get(data.get('data_id')):
                result[data.get('data_id')] = result[data.get('data_id')] + _data
            else:
                result[data.get('data_id')] = _data
        return result


def get_single_data_permissions_actions(role_id, data_type):
    actions_name = {k.get('action_code'): k.get('action_name') for k in ALL_REPORT_DATA_ACTIONS}
    sql = """select data_id, data_action_code from user_role_data_permission WHERE
                data_type=%(data_type)s and role_id=%(role_id)s;"""
    with get_db() as db:
        datas = db.query(sql, {'data_type': data_type, 'role_id': role_id})
        # 将当前结构[{'data_id': '39e581e3-63f7-c8e1-7de2-3bdde2e80848', 'data_action_code': 'view'}]
        # 修改为 {‘39e581e3-63f7-c8e1-7de2-3bdde2e80848’: {'action_code': 'view','action_name': '查看'}}
        result = {}
        for data in datas:
            _data = [
                {
                    'action_code': data.get('data_action_code'),
                    'action_name': actions_name.get(data.get('data_action_code')),
                }
            ]
            if result.get(data.get('data_id')):
                result[data.get('data_id')] = result[data.get('data_id')] + _data
            else:
                result[data.get('data_id')] = _data
        return result


def get_data_permissions_actions_detail(role_id, data_type, ids):
    if not ids:
        return ids
    actions_name = {k.get('action_code'): k.get('action_name') for k in ALL_REPORT_DATA_ACTIONS}
    if len(ids) == 1:
        sql = """select data_id, data_action_code from user_role_data_permission WHERE data_id=%(data_id)s
                  and data_type=%(data_type)s and role_id=%(role_id)s;"""
    else:
        sql = """select data_id, data_action_code from user_role_data_permission WHERE
                  data_type=%(data_type)s and role_id=%(role_id)s and data_id in {ids} ;""".format(
            ids=tuple(ids)
        )
    with get_db() as db:
        datas = db.query(sql, {'data_id': ids[0], 'data_type': data_type, 'role_id': role_id})
        # 将当前结构[{'data_id': '39e581e3-63f7-c8e1-7de2-3bdde2e80848', 'data_action_code': 'view'}]
        # 修改为 {‘39e581e3-63f7-c8e1-7de2-3bdde2e80848’: {'action_code': 'view','action_name': '查看'}}
        result = {}
        for data in datas:
            _data = [
                {
                    'action_code': data.get('data_action_code'),
                    'action_name': actions_name.get(data.get('data_action_code')),
                }
            ]
            if result.get(data.get('data_id')):
                result[data.get('data_id')] = result[data.get('data_id')] + _data
            else:
                result[data.get('data_id')] = _data
        return result


def get_allow_ids(role_ids, data_type, data_action_code):
    """
    根据 role_id,data_type,data_action_code 返回所有ids
    :param role_id:
    :param data_type:
    :param data_action_code:
    :return:
    """
    if not role_ids:
        return []

    # 特殊处理, data_type 为 ['dashboard', 'multiple_screen'] 的情况，考虑到接口都是一样的，区分这两个成本太高
    new_data_type = ''
    if data_type in ['dashboard', 'multiple_screen']:
        new_data_type = ('dashboard', 'multiple_screen')
    if len(role_ids) == 1:
        sql = """select data_id from user_role_data_permission WHERE role_id=%(role_id)s and data_type=%(data_type)s and
             data_action_code=%(data_action_code)s;"""
    else:
        sql = """select data_id from user_role_data_permission WHERE data_type=%(data_type)s and
             data_action_code=%(data_action_code)s and role_id in {role_ids};""".format(
            role_ids=tuple(role_ids)
        )

    if new_data_type:
        sql = re.sub("data_type=", "data_type in ", sql)
    with get_db() as db:
        data = db.query(
            sql,
            {
                'role_id': role_ids[0],
                'data_type': new_data_type if new_data_type else data_type,
                'data_action_code': data_action_code,
            },
        )
        return list({k.get('data_id') for k in data})


def get_folder_allow_ids(role_ids, data_type, data_action_code):
    """
    根据 role_id,data_type,data_action_code 返回所有ids
    :param role_ids:
    :param data_type:
    :param data_action_code:
    :return:
    """
    if not role_ids:
        return []

    sql = """select data_id from user_role_folder_data_permission WHERE data_type=%(data_type)s and
             data_action_code=%(data_action_code)s and role_id in %(role_ids)s"""

    with get_db() as db:
        data = db.query_columns(
            sql,
            {
                'role_ids': role_ids,
                'data_type': data_type,
                'data_action_code': data_action_code,
            },
        )
        return data


def get_application_allow_sub_ids(role_ids):
    if not role_ids:
        return []

    if len(role_ids) == 1:
        sql = """select func_id from user_role_app_sub_menu WHERE role_id=%(role_id)s;"""
    else:
        sql = """select func_id from user_role_app_sub_menu WHERE role_id in {role_ids};""".format(
            role_ids=tuple(role_ids)
        )
    with get_db() as db:
        data = db.query(sql, {'role_id': role_ids[0]})
        return list({k.get('func_id') for k in data})


def get_permission_by_own(data_id, account, data_type):
    """
    根据 data_id查询是否是创建者
    :param account:
    :return:
    """
    # 缓存是否为创建者
    cache_key = USER_OWN_CACHE_KEY % (data_id, account)
    cache = conn_redis()
    flag = cache_data_is_true(cache, cache_key)
    if flag is not None:
        return flag
    table_name = data_type
    if data_type in ['dashboard', 'multiple_screen', 'report_center', 'large_screen']:
        table_name = "dashboard"

    sql = "select `id`,`created_by` from {table_name} where id=%(data_id)s and created_by=%(created_by)s".format(
        table_name=table_name
    )

    with get_db() as db:
        data = db.query(sql, {'data_id': data_id, 'created_by': account})
        flag = bool(data)
    # 创建者应该不可能改动，缓存7天
    cache.set(cache_key, flag, 7 * 24 * 60 * 60)
    return flag


def cache_data_is_true(cache, cache_key):
    flag = cache.get(cache_key)
    if flag is not None:
        if flag.decode("utf-8") == 'True':
            return True
        return False
    return flag


def cache_data_is_one(cache, cache_key):
    flag = cache.get(cache_key)
    if flag is not None:
        if flag.decode("utf-8") == '1':
            return True
        return False
    return flag


def get_permission_by_role_ids(role_ids: List[str], data_type: str, data_action_code: str):
    # 判断格式
    if not role_ids:
        return []

    if isinstance(role_ids, str):
        role_ids = [role_ids]
    # 判断长度，因为数组长度为1时，转成的tuple的时候多一个逗号",", 执行sql会报语法错误
    if len(role_ids) == 1:
        sql = """select 1 from user_role_all_data_permission WHERE data_type=%(data_type)s and
             data_action_code=%(data_action_code)s and role_id=%(role_id)s limit 1"""
    else:
        sql = """select 1 from user_role_all_data_permission WHERE data_type=%(data_type)s and
             data_action_code=%(data_action_code)s and role_id in {role_ids} limit 1""".format(
            role_ids=tuple(role_ids)
        )
    with get_db() as db:
        return db.query(sql, {'role_id': role_ids[0], 'data_type': data_type, 'data_action_code': data_action_code})


def get_permission_by_role_ids_and_data_id(role_ids, data_type, data_action_code, data_id):
    # 判断格式
    if not role_ids or not data_id:
        return []

    sql = """select 1 from user_role_data_permission WHERE data_id=%(data_id)s and data_type=%(data_type)s and
             data_action_code=%(data_action_code)s and role_id in %(role_ids)s limit 1"""
    with get_db() as db:
        return db.query(
            sql,
            {'role_ids': role_ids, 'data_type': data_type, 'data_action_code': data_action_code, 'data_id': data_id},
        )


def get_dashboard_parent_id(allow_ids):
    if not allow_ids:
        return []
    # 判断长度，因为数组长度为1时，转成的tuple的时候多一个逗号",", 执行sql会报语法错误
    if len(allow_ids) == 1:
        sql = """select parent_id from dashboard WHERE id=%(allow_id)s and
             type=%(type)s """
    else:
        sql = """select parent_id from dashboard WHERE type=%(type)s and id in {allow_ids} """.format(
            allow_ids=tuple(allow_ids)
        )
    with get_db() as db:
        results = db.query(sql, {'allow_id': allow_ids[0], 'type': 'FILE'})
        return [k.get('parent_id') for k in results]


def get_app_allow_ids_by_role(role_ids):
    """
    获取门户及其子菜单权限
    :param role_ids:
    :return:
    """
    # 最高权限，不需要走验证
    if ADMIN_ROLE_ID in role_ids:
        return True, []
    # 角色为空，直接返回
    if not role_ids or len(role_ids) < 1:
        return False, []
    # 查看操作权限
    if len(role_ids) == 1:
        sql = """select data_action_code from user_role_all_data_permission WHERE data_type=%(data_type)s
                  and data_action_code=%(data_action_code)s and role_id=%(role_id)s limit 1""".format(
            role_id=role_ids[0]
        )
    else:
        sql = """select data_action_code from user_role_all_data_permission WHERE data_type=%(data_type)s
                  and data_action_code=%(data_action_code)s and role_id in {role_ids} limit 1""".format(
            role_ids=tuple(role_ids)
        )
    with get_db() as db:
        # 只需判断是否有门户的所有查看权限
        data_action_codes = db.query(
            sql, {'role_id': role_ids[0], 'data_type': 'application', 'data_action_code': 'view'}
        )
        if data_action_codes:
            return True, []
        else:
            # 查询单个菜单权限
            allow_ids = []
            # 后期改为in查询，时间不够了，这里可能有性能问题
            for role_id in role_ids:
                funcs_ids = repository.get_data(
                    'user_role_app_sub_menu', {'role_id': role_id}, ['func_id'], multi_row=True
                )
                data_ids = repository.get_data(
                    'user_role_data_permission',
                    {'role_id': role_id, 'data_type': 'application', 'data_action_code': 'view'},
                    ['data_id'],
                    multi_row=True,
                )
                data_ids = [k.get('data_id') for k in data_ids]
                funcs_ids = [k.get('func_id') for k in funcs_ids]
                allow_ids += data_ids
                allow_ids += funcs_ids
            return False, allow_ids


def grant_default_permissions(role_id):
    """
    分配默认数据权限
    :param role_id:
    :return:
    """
    db = get_db()
    for permission in DEFAULT_DATA_PERMISSIONS:
        db.delete('user_role_all_data_permission', {'role_id': role_id, 'data_type': permission.get('data_type')})
        for action_code in permission.get('data_action_code'):
            db.insert(
                'user_role_all_data_permission',
                {'role_id': role_id, 'data_type': permission.get('data_type'), 'data_action_code': action_code},
            )
    return True


def grant_permissions_for_role(role_id, data_type, datas):
    """
    为用户角色分配数据权限
    :param role_id:
    :param datas: [{"data_id": "id", "data_type": "data_source",
                    "data_action_code":["write", "read"]}]
    :return:
    """

    if not isinstance(datas, list):
        raise ValueError('datas should be list!')

    db = get_db()

    for data in datas:
        data_id = data.get('data_id')
        data_action_codes = data.get('data_action_code')
        if not data_id:
            raise ValueError('data_id is required')
        if not data_type:
            raise ValueError('data_type is required')
        if not isinstance(data_action_codes, list):
            raise ValueError('data_action_code should be list')

        # todo 为数据集添加权限，先判断是否有数据源权限, 全选的操作在上一步已经验证过了(有些数据集并没有跟数据源挂钩，如excel)
        db.delete('user_role_data_permission', {'role_id': role_id, 'data_id': data_id, 'data_type': data_type})
        for data_action_code in data_action_codes:
            db.insert(
                'user_role_data_permission',
                {'role_id': role_id, 'data_id': data_id, 'data_type': data_type, 'data_action_code': data_action_code},
            )
            # todo 应用还需添加子菜单权限
        if data_type == 'application' and data.get('sub'):
            save_application_sub_menu(role_id, data)

        from dataset.services.dataset_service import refresh_cache_by_dataset_id

        refresh_cache_by_dataset_id(data.get("data_id"))

    return True


def save_application_sub_menu(role_id, data):
    sub_menus = data.get('sub')
    if not sub_menus:
        save_application(role_id, data)
    else:
        for sub_menu in sub_menus:
            if sub_menu.get('data_id') and sub_menu.get('data_action_code'):
                save_application(role_id, sub_menu)
            if sub_menu.get('sub'):
                subs = sub_menu.get('sub')
                for sub in subs:
                    save_application_sub_menu(role_id, sub)
            save_application(role_id, sub_menu)


def save_application(role_id, data):
    db = get_db()
    db.delete('user_role_app_sub_menu', {'role_id': role_id, 'func_id': data.get('data_id')})
    # 先删除权限,再添加, 目前没有action,不排除未来有可能添加，暂时不对data_action_code控制
    for data_action_code in data.get('data_action_code'):
        try:
            if data_action_code == 'view':
                db.insert('user_role_app_sub_menu', {'role_id': role_id, 'func_id': data.get('data_id')})
        except:
            pass


def get_permission_role_ids(data_id, role_ids, data_type):
    """
    获取数据操作权限的role_id
    :param data_id:
    :param role_ids:
    :param data_type:
    :return:
    """
    if not role_ids:
        return role_ids
    sql = """
        select role_id from user_role_data_permission
        where data_id =%(data_id)s and data_action_code='view' and data_type=%(data_type)s and role_id in %(role_ids)s
        union
        select role_id from user_role_all_data_permission
        where data_type=%(data_type)s and data_action_code='view' and  role_id in %(role_ids)s
        """
    with get_db() as db:
        # 只需判断是否有门户的所有查看权限
        return db.query_columns(sql, {'data_id': data_id, 'data_type': data_type, 'role_ids': role_ids})


def get_role_data_by_all_data_permission(data_types: list, data_action_codes: list, role_ids: list):
    if not role_ids:
        return []

    sql = 'SELECT role_id, data_action_code FROM user_role_all_data_permission'
    wheres = []
    params = {}
    wheres.append('role_id in %(role_ids)s')
    params['role_ids'] = role_ids

    if len(data_types) == 1:
        wheres.append('data_type = %(data_type)s')
        params['data_type'] = data_types[0]
    elif len(data_types) > 1:
        wheres.append('data_type in %(data_types)s')
        params['data_types'] = data_types
    if len(data_action_codes) == 1:
        wheres.append('data_action_code = %(data_action_code)s')
        params['data_action_code'] = data_action_codes[0]
    elif len(data_action_codes) > 1:
        wheres.append('data_action_code in %(data_action_codes)s')
        params['data_action_codes'] = data_action_codes

    sql += ' WHERE ' + ' AND '.join(wheres)
    with get_db() as db:
        return db.query(sql, params)


def get_role_data_by_data_permission(data_types: list, data_action_codes: list, role_ids: list):
    if not role_ids:
        return []

    sql = 'SELECT role_id, data_id, data_action_code, data_type FROM user_role_data_permission'
    wheres = []
    params = {}
    wheres.append('role_id in %(role_ids)s')
    params['role_ids'] = role_ids

    if len(data_types) == 1:
        wheres.append('data_type = %(data_type)s')
        params['data_type'] = data_types[0]
    elif len(data_types) > 1:
        wheres.append('data_type in %(data_types)s')
        params['data_types'] = data_types
    if len(data_action_codes) == 1:
        wheres.append('data_action_code = %(data_action_code)s')
        params['data_action_code'] = data_action_codes[0]
    elif len(data_action_codes) > 1:
        wheres.append('data_action_code in %(data_action_codes)s')
        params['data_action_codes'] = data_action_codes

    sql += ' WHERE ' + ' AND '.join(wheres)
    with get_db() as db:
        return db.query(sql, params)


def get_role_folder_data_by_permission(data_types: list, data_action_codes: list, role_ids: list):
    if not role_ids:
        return []

    sql = 'SELECT role_id, data_id, data_type, data_action_code FROM user_role_folder_data_permission'
    wheres = []
    params = {}
    wheres.append('role_id in %(role_ids)s')
    params['role_ids'] = role_ids

    if len(data_types) == 1:
        wheres.append('data_type = %(data_type)s')
        params['data_type'] = data_types[0]
    elif len(data_types) > 1:
        wheres.append('data_type in %(data_types)s')
        params['data_types'] = data_types
    if len(data_action_codes) == 1:
        wheres.append('data_action_code = %(data_action_code)s')
        params['data_action_code'] = data_action_codes[0]
    elif len(data_action_codes) > 1:
        wheres.append('data_action_code in %(data_action_codes)s')
        params['data_action_codes'] = data_action_codes

    sql += ' WHERE ' + ' AND '.join(wheres)
    with get_db() as db:
        return db.query(sql, params)
