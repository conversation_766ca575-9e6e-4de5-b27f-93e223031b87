from operator import itemgetter
from base import repository
from base.dmp_constant import function_table_map
from dmplib import config
from dmplib.db import mysql_wrapper
from dmplib.saas import project
from dmplib.constants import ADMIN_ROLE_ID

from typing import Dict, List, Optional


def list_funcs(func_codes=None):
    function_table = get_func_table("`function`")
    func_action_table = get_func_table("func_action")
    db = mysql_wrapper.get_db()
    condition_action = ''
    params_action = None
    if func_codes is not None:
        condition_action = 'where func_code in %(func_codes)s'
        params_action = {'func_codes': func_codes}

    sql = f'select id, name as func_name, func_code, func_url, parent_id,level_code from {function_table} order by level_code'
    funcs = db.query(sql)
    sql_action = ' '.join([f'select action_name, action_code, func_code from {func_action_table}', condition_action])
    func_actions = db.query(sql_action, params_action)
    top_funcs_map = deal_top_funcs_map(funcs)

    for action in func_actions:
        for top_id, top_func in top_funcs_map.items():
            __append_action_children(top_func, action)
    return [v for k, v in top_funcs_map.items()]


def __append_action_children(current_func, action):
    func_code = action['func_code']
    action_item = {'action_name': action['action_name'], 'action_code': action['action_code']}
    if current_func['func_code'] == func_code:
        # 查看在后面
        if action.get('action_code') == 'view':
            current_func['actions'].insert(0, action_item)
        else:
            current_func['actions'].append(action_item)

    for child_func in current_func['children']:
        __append_action_children(child_func, action)


def deal_top_funcs_map(funcs):
    top_funcs_map = {}
    for row in funcs:
        # is top level
        if not row['parent_id']:
            top_funcs_map[row['id']] = {
                'children': [],
                'id': row['id'],
                'func_code': row['func_code'],
                'func_name': row['func_name'],
                'func_url': row['func_url'],
                'level_code': row['level_code'],
                'actions': [],
            }
    for parent_func in top_funcs_map.values():
        __append_func_children(parent_func, funcs)
    return top_funcs_map


def __append_func_children(parent_func, funcs):
    for func in funcs:
        if func.get('parent_id') == parent_func.get('id'):
            func['children'] = []
            func['actions'] = []
            parent_func['children'].append(func)
            __append_func_children(func, funcs)


def list_funcs_of_role(role_id):
    # TODO 酷炫大屏默认开启
    # 查询角色的功能权限code
    if role_id == ADMIN_ROLE_ID:
        funcs_of_role = get_all_func_with_codes()
    else:
        saas_db = project.get_db()
        funcs_of_role = saas_db.query(
            'select func_code, func_action_code from user_role_func ' 'where role_id=%(role_id)s', {'role_id': role_id}
        )
    if len(funcs_of_role) == 0:
        return None

    func_codes = set()
    funcs_auth_map = {}
    for row in funcs_of_role:
        func_codes.add(row['func_code'])
        funcs_auth_map['{}#{}'.format(row['func_code'], row['func_action_code']).lower()] = 1

    funcs = list_funcs(list(func_codes))
    retvl = []

    def _filter_auth_actions(func_item):
        _actions_retvl = []
        for action_item in func_item['actions']:
            k = '{}#{}'.format(func_item['func_code'], action_item['action_code']).lower()
            if k in funcs_auth_map:
                _actions_retvl.append(action_item)
        return _actions_retvl

    for level1_func in funcs:
        level1_func['actions'] = _filter_auth_actions(level1_func)
        level2_funcs = level1_func['children']
        level1_func['children'] = []
        for level2_func in level2_funcs:
            level2_func['actions'] = _filter_auth_actions(level2_func)
            level3_funcs = level2_func['children']
            level2_func['children'] = []
            for level3_func in level3_funcs:
                level3_func['actions'] = _filter_auth_actions(level3_func)
                if len(level3_func['actions']) > 0:
                    level2_func['children'].append(level3_func)
            if len(level2_func['actions']) > 0 or len(level2_func['children']) > 0:
                level1_func['children'].append(level2_func)
        if len(level1_func['actions']) > 0 or len(level1_func['children']) > 0:
            retvl.append(level1_func)
    return retvl


def get_funcs_rows(func_codes, is_release: bool = False):
    func_table = 'release_function' if is_release else '`function`'
    function_table = get_func_table("`function`")
    if len(func_codes) == 0:
        return []
    db = mysql_wrapper.get_db()
    func_from_config = db.query(
        f'select * from {function_table} where func_code in %(func_codes)s', {'func_codes': func_codes}
    )
    if 'app-site' in func_codes:
        fields = ['id', 'name', 'parent_id', 'level_code', 'icon', 'url', 'target', 'application_id']
        order_by = [('application_id', 'ASC'), ('level_code', 'ASC')]
        func_from_tenant = repository.get_data(func_table, {}, fields, True, order_by) or []
    else:
        func_from_tenant = []
    funcs = func_from_tenant + func_from_config
    funcs = sorted(funcs, key=itemgetter('level_code'), reverse=False)
    return funcs


def get_all_func_with_codes(filter_func_codes: Optional[List[str]] = None) -> List[Dict[str, str]]:
    func_action_table = get_func_table("func_action")
    db = mysql_wrapper.get_db()
    sql = f'select action_code as func_action_code, func_code from {func_action_table}'
    params = None
    if filter_func_codes is not None:
        sql = ' '.join([sql, ' where func_code in %(func_codes)s '])
        params = {'func_codes': filter_func_codes}
    return db.query(sql, params)


def get_func_table(table_name):
    if int(config.get("Grayscale.gray_env")):
        return function_table_map.get(table_name)
    return table_name
