from dmplib.saas import project
from dmplib.constants import ADMIN_ROLE_ID, ADMINISTRATORS_GROUP_ID
from . import func_repository
from dmplib.db import mysql_wrapper

from typing import Dict, List, Optional


def get_funcs_by_user(
        userid: str,
        user_group_ids: List[str],
        customize_roles: List[str] = None,
        filter_func_codes: Optional[List[str]] = None,
) -> List[Dict[str, str]]:
    """
    获取用户的权限数据
    :param filter_func_codes: list
    :param user_group_ids:
    :param customize_roles:
    :param userid:
    :return:
    """
    db = project.get_db()
    if customize_roles:
        role_ids = customize_roles
    else:
        # 非指定角色，角色为用户角色加所属组角色
        if not user_group_ids:
            user_group_ids = []

        if ADMINISTRATORS_GROUP_ID in user_group_ids:
            return func_repository.get_all_func_with_codes(filter_func_codes)

        # 1. get funcs_actions of userid
        sql = 'select func_code, func_action_code from user_user_func where user_id = %(userid)s'
        params = {'userid': userid}
        if filter_func_codes is not None:
            sql = ' '.join([sql, ' and func_code in %(func_codes)s '])
            params['func_codes'] = filter_func_codes

        funcs_data = db.query(sql, params)
        if funcs_data and len(funcs_data) > 0:
            return funcs_data

        # 2. get roles of userid
        sql = 'select role_id from user_user_role where user_id = %(userid)s'
        rows = db.query(sql, {'userid': userid})
        role_ids_of_user = [row['role_id'] for row in rows]

        # 管理员角色,取所有权限
        if ADMIN_ROLE_ID in role_ids_of_user:
            return func_repository.get_all_func_with_codes()

        role_ids_of_group = []
        # 3. get roles of group_ids
        if user_group_ids:
            from user_group.services.user_group_service import get_all_parent_group_by_group_ids
            group_ids = get_all_parent_group_by_group_ids(user_group_ids)
            user_group_ids = list(set(group_ids + user_group_ids))
            sql4 = 'select role_id from user_group_role where group_id in %(group_ids)s'
            rows = db.query(sql4, {'group_ids': user_group_ids})
            role_ids_of_group = [row['role_id'] for row in rows]
            # 管理员角色,取所有权限
            if ADMIN_ROLE_ID in role_ids_of_group:
                return func_repository.get_all_func_with_codes()

        role_ids = set(role_ids_of_user + role_ids_of_group)
    # 4. get funcs_actions of roles
    items = _get_funcs_actions_of_roles(db, filter_func_codes, role_ids)

    db = mysql_wrapper.get_db()
    all_func = db.query(
        'select func_code,level_code,parent_id,id from `function`'
    )
    all_func_dict = {func['func_code']: func for func in all_func}
    parent_dict = {}
    for item in items:
        current_func = all_func_dict.get(item.get('func_code'))
        if current_func:
            for func in all_func:
                if current_func.get('level_code') != func.get('level_code') and \
                        str(current_func.get('level_code')).find(func.get('level_code')) == 0:
                    parent_func = [{'func_code': func.get('func_code'), 'func_action_code': 'edit'},
                                   {'func_code': func.get('func_code'), 'func_action_code': 'view'}]
                    parent_dict[func.get('func_code')] = parent_func
    for parent in parent_dict.values():
        items = items + parent
    return items
    # for item in items:
    #     # 如果有子菜单，则添加主菜单权限
    #     if item.get('func_code') in ['data-clean', 'indicator-definition', 'indicator-configuration',
    #                                  'label-definition']:
    #         items.append({'func_code': 'offline-bigdata', 'func_action_code': 'edit'})
    #         items.append({'func_code': 'offline-bigdata', 'func_action_code': 'view'})
    #     elif item.get('func_code') in ['flow-dashboard', 'flow-ops', 'healthy', 'indicator-new-inspection',
    #                                    'export_list', 'log']:
    #         items.append({'func_code': 'flow-monitor', 'func_action_code': 'edit'})
    #         items.append({'func_code': 'flow-monitor', 'func_action_code': 'view'})
    #     elif item.get('func_code') in ['user', 'user-group', 'user-role', 'data-permission', 'permission']:
    #         items.append({'func_code': 'user-permission', 'func_action_code': 'edit'})
    #         items.append({'func_code': 'user-permission', 'func_action_code': 'view'})
    #     elif item.get('func_code') in ['open-data-api', 'custom-report', 'datawork']:
    #         items.append({'func_code': 'open-data', 'func_action_code': 'edit'})
    #         items.append({'func_code': 'open-data', 'func_action_code': 'view'})
    # return items


def _get_funcs_actions_of_roles(db, filter_func_codes, role_ids):
    items = []
    if len(role_ids) > 0:
        sql = 'select func_code, func_action_code from user_role_func where role_id in %(role_ids)s'
        params = {'role_ids': list(role_ids)}
        if filter_func_codes is not None:
            sql = ' '.join([sql, ' and func_code in %(func_codes)s '])
            params['func_codes'] = filter_func_codes
        items = db.query(sql, params)
    return items


def get_funcs_by_group_id(group_ids):
    if not group_ids:
        return []

    if ADMINISTRATORS_GROUP_ID in group_ids:
        return func_repository.get_all_func_with_codes()

    # 3. get roles of group_ids
    db = project.get_db()
    sql1 = 'select role_id from user_group_role where group_id in %(group_ids)s'
    rows = db.query(sql1, {'group_ids': group_ids})
    role_ids_of_group = [row['role_id'] for row in rows]

    # 4. get funcs_actions of roles
    role_ids = set(role_ids_of_group)
    items = []
    # 管理员角色拥有所有权限
    if ADMIN_ROLE_ID in role_ids:
        return func_repository.get_all_func_with_codes()
    elif len(role_ids) > 0:
        sql = 'select func_code, func_action_code from user_role_func where role_id in %(role_ids)s'
        items = db.query(sql, {'role_ids': list(role_ids)})
    return items
