#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class UserLog
    <NAME_EMAIL> on 2018/2/5.
"""

import time
from components.log_client import LogClient
from dmplib import config
from dmplib.hug import g

from base.models import BaseModel
from base import repository
from base.enums import ApplicationType

from celery.result import AsyncResult
from falcon.request import Request
from typing import Dict


class ActionLogModel(BaseModel):
    __slots__ = ["account", "action", "brower", "domain", "do_object", "log_time", "ip", "org_code", "path", "url"]

    def __init__(self, **kwargs):
        # pylint: disable= W0235
        super().__init__(**kwargs)


class UserLogModel(object):
    # pylint: disable= R0205
    __slots__ = ["user", "data_source", "dataset", "dashboard", "application", "system", "feed", "dashboard_component",
                 "export"]

    def __init__(self) -> None:
        self.user = UserAction
        self.data_source = DataSourceAction
        self.dataset = DatasetAction
        self.dashboard = DashboardAction
        self.application = AppAction
        self.system = SystemAction
        self.feed = FeedAction
        self.dashboard_component = ComponentAction
        self.export = ExportAction

    def get_action(self, log_type):

        return getattr(self, log_type)

    def get_info_by_action(self, action):
        """
        根据action查询对应的module信息
        :param action:
        :return:
        """
        if not action:
            return None, None

        for mod in self.__slots__:
            mod_obj = getattr(self, mod)
            if mod_obj and action in mod_obj.actions.keys():
                return mod_obj.module_title, mod_obj.actions.get(action)
        return None, None

    def get_all_action(self, log_type):
        """
        根据action查询对应的module的所有action
        :param action:
        :return:
        """
        if not log_type:
            return None

        mod_obj = getattr(self, log_type)
        if mod_obj:
            return mod_obj.actions.keys()
        return None

    def get_mod_name_by_action(self, action: str) -> str:
        """
        根据action查询对应的module的英文名称
        :param action:
        :return:
        """
        for mod in self.__slots__:
            mod_obj = getattr(self, mod)
            if mod_obj and action in mod_obj.actions.keys():
                return mod
        return None

    @staticmethod
    def log_setting(request: Request, log_data: Dict[str, str]) -> AsyncResult:

        # 开启行为日志开关
        if config.get('Log.user_action') == 0:
            return False

        data = {}

        # 基本属性
        if hasattr(g, "account"):
            data["account"] = g.account
        else:
            data["account"] = log_data.get("account") or getattr(request, "remote_addr", "")
            g.account = getattr(request, "remote_addr", "")

        data['org_code'] = log_data.get('org_code') or g.code
        data['log_time'] = time.strftime('%Y-%m-%d %H:%M:%S')
        data['ip'] = getattr(request, "remote_addr", "")
        data['domain'] = getattr(request, "host", "")
        data['path'] = getattr(request, "path", "")
        data['brower'] = getattr(request, "user_agent", "")
        data['url'] = getattr(request, "url", "")
        data['do_object'] = log_data.get('id') or ''
        data['action'] = log_data.get('action') or ''
        data['content'] = log_data.get('content') or ''
        data['extra'] = log_data.get('extra') or ''

        # 输出日志
        log_model = UserLogModel()
        log_type = log_model.get_mod_name_by_action(action=data.get("action"))
        if log_type is None:
            return None

        log_type = log_model.adapt_dashboard_log_type(log_type, log_data)
        data['moudel'] = log_type

        # 解决报告公开分享、密码访问时日志记录没有用户account的问题
        if not getattr(g, 'account'):
            data['created_by'] = 'public'
            data['modified_by'] = 'public'

        # 将数据记录到数据库
        user_log_model = UserOperationLogModel(**data)
        return repository.add_model('user_operation_log', user_log_model)

    @staticmethod
    def adapt_dashboard_log_type(log_type, log_data):
        """
        根据不用报表适配报表的明细操作名字，拆分出报表和大屏
        """
        application_type = log_data.get('application_type')
        if log_type == 'dashboard':
            if application_type == ApplicationType.LargeScreen.value:
                return 'large_screen'
            else:
                return log_type
        else:
            return log_type


# pylint: disable= R0205
class LogAction(object):
    module_title = ""
    actions = {}


class FeedAction(LogAction):
    module_title = "订阅"

    actions = {"add_feed": "添加订阅", "update_feed": "修改订阅", "delete_feed": "添加订阅"}


class ExportAction(LogAction):
    module_title = "数据导出"

    actions = {"dashboard_chart": "组件数据导出"}


class UserAction(LogAction):
    module_title = "用户"

    actions = {
        "login": "登录",
        "logout": "退出登录",
        "set_data_permissions": "设置模块权限",
        "set_permissions": "用户权限",
        "set_group": "设置组",
        "add_user": "添加用户",
        "update_user": "修改用户",
        "delete_user": "删除用户",
        "add_group": "添加用户组",
        "update_group": "修改用户组",
        "delete_group": "删除用户组",
        "set_dataset_filter": "设置数据集行列权限",
    }


class DataSourceAction(LogAction):
    module_title = "数据源"
    actions = {"add_data_source": "添加数据源", "update_data_source": "修改数据源", "delete_data_source": "删除数据源"}


class DashboardAction(LogAction):
    module_title = "报告"
    actions = {
        "view_dashboard": "查看报告",
        "add_dashboard": "添加报告",
        "copy_dashboard": "复制报告",
        "update_dashboard": "编辑报告",
        "rename_dashboard": "重命名报告",
        "move_dashboard": "移动报告",
        "release_dashboard": "发布报告",
        "async_release": "异步发布报告单屏/多屏",
        "delete_dashboard": "删除报告",
        "preview_dashboard": "预览报告",
        "add_dashbaord_filter": "添加报告级筛选",
        "update_dashbaord_filter": "更新报告级筛选",
        "delete_dashbaord_filter": "删除报告级筛选",
        "save_chart_config": "修改单图样式",
        "delete_chart": "删除单图",
        "copy_chart": "复制单图",
        "move_chart": "移动单图位置",
        "save_multi_dashboard": "保存多屏",
        "add_chart": "添加单图",
        "rename_chart": "重命名单图",
        "update_chart": "修改单图数据配置",
        "async_restore": "报告还原",
        "edit_dashboard_snapshot": "报告拍照设置",
    }


class DatasetAction(LogAction):
    module_title = "数据集"
    actions = {
        "add_dataset": "添加数据集",
        "update_dataset": "修改数据集",
        "delete_dataset": "删除数据集",
        "add_user_dataset_field": "添加高级字段",
        "update_user_dataset_field": "修改高级字段",
        "delete_user_dataset_field": "删除高级字段",
    }


class AppAction(LogAction):
    module_title = "应用"
    actions = {
        "update_app": "修改门户",
        "update_app_rank": "修改门户排序",
        "add_app": "添加门户",
        "view_app": "查看门户",
        "list_app": "查看门户列表",
        "delete_app": "删除门户",
        "disable_app": "禁用门户",
        "enable_app": "启用门户",
        "add_app_menu": "添加门户菜单",
        "delete_app_menu": "删除门户菜单",
        "update_app_menu": "更新门户菜单",
        "update_app_menu_rank": "修改门户菜单",
    }


class SystemAction(UserLogModel):
    module_title = "系统"
    actions = {"system": "查看报告"}


class ComponentAction(UserLogModel):
    module_title = "组件"
    actions = {"install_component": "安装组件", "upgrade_component": "升级组件"}


class UserOperationLogModel(BaseModel):
    __slots__ = [
        "account", "org_code", "moudel", "action", "content", "ip",
        "domain", "path", "url", "do_object", "brower", "extra",
        "created_by", "modified_by",
    ]

    def __init__(self, **kwargs):
        self.account = None
        self.org_code = None
        self.moudel = None
        self.action = None
        self.content = None
        self.ip = None
        self.domain = None
        self.path = None
        self.url = None
        self.do_object = None
        self.brower = None
        self.extra = None
        self.created_by = ''
        self.modified_by = ''
        super().__init__(**kwargs)


class ReportVisitLogModel(BaseModel):
    __slots__ = [
        "account", "user_name", "report_id", "report_name", "report_type"
    ]

    __table__ = 'report_visit_log'

    def __init__(self, **kwargs):
        self.account = None
        self.user_name = None
        self.report_id = None
        self.report_name = None
        self.report_type = None
        super().__init__(**kwargs)


class QueryVisitLogModel(BaseModel):
    __slots__ = [
        "start_time", "end_time", "report_id", "report_name", "report_type", "account", "user_name", "page", "page_size", "order_by"
    ]

    def __init__(self, **kwargs):
        self.start_time = None
        self.end_time = None
        self.account = None
        self.user_name = None
        self.report_id = None
        self.report_name = None
        self.report_type = None
        self.page = 1
        self.page_size = 20
        self.order_by = None
        super().__init__(**kwargs)
        if isinstance(self.user_name, str) and self.user_name:
            self.user_name = self.user_name.split(',')
        if isinstance(self.account, str) and self.account:
            self.account = self.account.split(',')
        if isinstance(self.report_id, str) and self.report_id:
            self.report_id = self.report_id.split(',')
        if isinstance(self.report_name, str) and self.report_name:
            self.report_name = self.report_name.split(',')
        if isinstance(self.report_type, str) and self.report_type:
            self.report_type = self.report_type.split(',')

