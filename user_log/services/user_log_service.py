#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/1/15
"""

from dmplib.hug import g
import datetime
import dateutil.relativedelta
import time
from components.log_client import LogClient
from user_log import models
from base import repository
from dmplib.utils.errors import UserError
from components.excel import Excel
import json


def view_log_for_db(**kwargs):
    where = {}
    now = int(time.time())
    # 起始时间
    from_time = now - 3600 * 24 * 30
    from_time = time.strftime("%Y-%m-%d", time.localtime(from_time))
    if kwargs.get('from_time'):
        from_time = kwargs.get('from_time')
    where['created_on>='] = from_time
    # 终点时间
    to_time = time.strftime("%Y-%m-%d 23:59", time.localtime(now))
    if kwargs.get('to_time'):
        to_time = kwargs.get('to_time')
        to_time_day = datetime.datetime.strptime(to_time, "%Y-%m-%d %H:%M")
        to_time = to_time_day.strftime("%Y-%m-%d 23:59")

    where['created_on<='] = to_time
    # 用户名查询
    if kwargs.get('user_name'):
        where['account like'] = '%{}%'.format(kwargs.get('user_name'))
    # 模块查询
    log_type = kwargs.get('log_type')
    if log_type:
        where['moudel'] = log_type

    page_size = 10
    page = kwargs.get('skip', 1)
    if kwargs.get('page_size'):
        page_size = int(kwargs.get('page_size'))
    page_info = [page, page_size]
    order_by = 'created_on desc'

    fields = ['account', 'moudel', 'action', 'content', 'created_on']

    data = repository.get_list('user_operation_log', where, fields, order_by, page=page_info)
    count = repository.get_value('user_operation_log', where, 'count(*)')
    result = {'items': [], 'page_size': page_size, 'skip': page, 'total': count if count else 0}
    if data:
        log_model = models.UserLogModel()
        for item in data:
            module_title, log_info = log_model.get_info_by_action(item.get('action'))
            log_info = item.get('content') if item.get('content') else log_info
            module_title = _format_module_title(item, module_title)
            result.get('items').append(
                {
                    'account': item.get('account'), 'action': item.get('action'), 'log_info': log_info,
                    'log_time': item.get('created_on'), 'log_type': module_title
                }
            )
    return result


def _format_module_title(item, module_title):
    if item.get('moudel') == 'large_screen':
        return '大屏'
    else:
        return module_title


def _format_log(content):
    '''
     :格式化日志格式
     :param dataset.models.OperateRecordQueryModel query_model:
     :return tuple:
     '''

    log_model = models.UserLogModel()

    log_list = []
    for item in content:
        log_dict = {}

        log_detail = item.get('log')

        detail = json.loads(log_detail)

        if detail.get('account'):
            log_dict['account'] = detail.get('account')
        else:
            log_dict['account'] = item.get('__source__')

        log_dict['log_time'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(int(item.get('__time__'))))
        log_dict['action'] = detail.get('action')

        # 通过action查找对应的logType
        module_title, log_info = log_model.get_info_by_action(detail.get('action'))

        log_dict['log_type'] = module_title

        # 描述使用content，若没描述，则使用定义里的说明
        if detail.get('content'):
            log_dict['log_info'] = detail.get('content')
        else:
            log_dict['log_info'] = log_info

        log_list.append(log_dict)
    return log_list


def export_log(response):
    # 获取最近30天的操作日志
    now = datetime.datetime.now()
    month_ago = now + dateutil.relativedelta.relativedelta(months=-1)
    where = {'created_on>=': month_ago.strftime('%Y-%m-%d')}
    fields = ['account', 'moudel', 'action', 'content', 'created_on', 'extra']
    title = {
        'account': '账号', 'moudel': '操作模块', 'action': '操作功能',
        'content': '具体行为', 'created_on': '操作时间', 'extra': '额外信息'
    }
    data = repository.get_list('user_operation_log', where, fields)
    if data:
        log_model = models.UserLogModel()
        for row in data:
            row['moudel'], row['action'] = log_model.get_info_by_action(row.get('action'))
        Excel.export_csv('操作日志.csv', data, title, response=response)
    else:
        raise UserError(message='当前没有操作日志需要导出')
