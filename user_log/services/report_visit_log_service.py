#!/usr/bin/env python3
# -*- coding: utf-8 -*-


import datetime
import dateutil.relativedelta

from user_log import models
from base import repository
from dmplib.utils.errors import UserError
from components.excel import Excel
from dashboard_chart.repositories.dashboard_released_design_repository import get_root_dashboard_by_level_code


def write_visit_log(model: models.ReportVisitLogModel):
    if not model.report_id or not model.account:
        return '报告或者用户信息为空，跳过记录'
    try:
        # 查询报告是否为主报告
        dashboard_info = repository.get_one('dashboard', {'id': model.report_id}, ['type', 'level_code']) or {}
        if dashboard_info.get('type') == 'CHILD_FILE':
            root_dashboard = get_root_dashboard_by_level_code(dashboard_info.get('level_code')) or {}
            if root_dashboard:
                model.report_id = root_dashboard.get('id')
                model.report_name = root_dashboard.get('name')
        # 删除两年以前的数据
        two_year_age = datetime.datetime.now() + dateutil.relativedelta.relativedelta(years=-2)
        repository.delete_data(model.__table__, {'created_on <': two_year_age.strftime('%Y-%m-%d')})
        return repository.add_model(model.__table__, model)
    except Exception as e:
        raise UserError(message=str(e))


def get_query_log_where(model: models.QueryVisitLogModel):
    where = {}
    filter_where = {}
    if not model.start_time:
        now = datetime.datetime.now()
        month_ago = now + dateutil.relativedelta.relativedelta(months=-1)
        model.start_time = month_ago.strftime('%Y-%m-%d')
    where.update({'created_on>=': model.start_time})
    filter_where.update({'created_on>=': model.start_time})
    if model.end_time:
        where.update({'created_on<=': model.end_time})
        filter_where.update({'created_on<=': model.end_time})
    if model.report_name:
        where.update({'report_name': model.report_name})
    if model.user_name:
        where.update({'user_name': model.user_name})
    if model.account:
        where.update({'account': model.account})
    if model.report_id:
        where.update({'report_id': model.report_id})
    if model.report_type:
        where.update({'report_type': model.report_type})
    return where, filter_where


def get_visit_list(model: models.QueryVisitLogModel):
    where, filter_where = get_query_log_where(model)
    page = [model.page, model.page_size]
    fields = ['account', 'user_name', 'report_id', 'report_name', 'report_type', 'created_on']
    order_by = model.order_by or 'created_on desc'
    data = repository.get_list('report_visit_log', where, fields, order_by, page=page)
    count = repository.get_value('report_visit_log', where, ['count(*) as total'])
    user_name = repository.get_column('report_visit_log', filter_where, ['`user_name`'], 'user_name asc', group=['user_name'])
    account = repository.get_column('report_visit_log', filter_where, ['account'], 'account asc', group=['account'])
    report_name = repository.get_column('report_visit_log', filter_where, ['MAX(`report_name`) as `report_name`'], group=['report_id']) or []
    report_name = list(set(report_name))
    report_name.sort()
    report_id = repository.get_column('report_visit_log', filter_where, ['`report_id`'], 'report_id asc', group=['report_id'])
    report_type = repository.get_column('report_visit_log', filter_where, ['`report_type`'], 'report_type asc', group=['report_type'])
    filter_data = {'user_name': user_name, 'account': account, 'report_name': report_name, 'report_id': report_id, 'report_type': report_type}
    return {'data': data, 'page_size': model.page_size, 'page': model.page, 'total': count if count else 0, 'filter_data': filter_data}


def export_visit_list(model: models.QueryVisitLogModel, response):
    where, _ = get_query_log_where(model)
    page = [1, 20000]
    fields = ['account', 'user_name', 'report_id', 'report_name', 'report_type', 'created_on']
    order_by = model.order_by or 'created_on desc'
    data = repository.get_list('report_visit_log', where, fields, order_by, page=page)
    title = {
        'created_on': '访问时间', 'report_name': '报表名称', 'user_name': '用户名',
        'account': '账号', 'report_type': '报表类型', 'report_id': '报表ID'
    }
    Excel.export_xls('报表访问明细.xls', data, title, response=response)


def get_report_visit_list(model: models.QueryVisitLogModel):
    where, filter_where = get_query_log_where(model)
    page = [model.page, model.page_size]
    fields = ['MAX(`report_name`) as `report_name`', 'report_id', 'MAX(`report_type`) as `report_type`', 'count(*) as pv', 'count(DISTINCT `account`) as uv']
    order_by = model.order_by or 'pv desc'
    group = ['`report_id`']
    data = repository.get_list('report_visit_log', where, fields, order_by, group=group, page=page)
    count = repository.get_value('report_visit_log', where, ['count(DISTINCT `report_id`)'])
    report_name = repository.get_column('report_visit_log', filter_where, ['MAX(`report_name`) as `report_name`'], group=group) or []
    report_name = list(set(report_name))
    report_name.sort()
    report_type = repository.get_column('report_visit_log', filter_where, ['MAX(`report_type`) as `report_type`'], 'report_type asc', group=['report_type'])
    filter_data = {'report_name': report_name, 'report_type': report_type}
    return {'data': data, 'page_size': model.page_size, 'page': model.page, 'total': count if count else 0, 'filter_data': filter_data}


def export_report_visit(model: models.QueryVisitLogModel, response):
    where, _ = get_query_log_where(model)
    page = [1, 20000]
    fields = ['MAX(`report_name`) as `report_name`', 'MAX(`report_type`) as `report_type`', 'count(*) as pv', 'count(DISTINCT `account`) as uv']
    order_by = model.order_by or 'pv desc'
    group = ['`report_id`']
    data = repository.get_list('report_visit_log', where, fields, order_by, group=group, page=page)
    title = {
        'report_name': '报表名称', 'report_type': '报表类型',
        'pv': '访问次数', 'uv': '访问用户数'
    }
    Excel.export_xls('访问报表统计.xls', data, title, response=response)


def get_user_visit_list(model: models.QueryVisitLogModel):
    where, filter_where = get_query_log_where(model)
    page = [model.page, model.page_size]
    fields = ['MAX(`user_name`) as `user_name`', '`account`', 'count(*) as pv', 'count(DISTINCT report_id) as report_count']
    order_by = model.order_by or 'pv desc'
    group = ['`account`']
    data = repository.get_list('report_visit_log', where, fields, order_by, group=group, page=page)
    count = repository.get_value('report_visit_log', where, ['count(DISTINCT `account`)'])
    user_name = repository.get_column('report_visit_log', filter_where, ['`user_name`'], 'user_name asc', group=['user_name'])
    account = repository.get_column('report_visit_log', filter_where, ['account'], 'account asc', group=group)
    filter_data = {'user_name': user_name, 'account': account}
    return {'data': data, 'page_size': model.page_size, 'page': model.page, 'total': count if count else 0, 'filter_data': filter_data}


def export_user_visit(model: models.QueryVisitLogModel, response):
    where, _ = get_query_log_where(model)
    page = [1, 20000]
    fields = ['MAX(`user_name`) as `user_name`', '`account`', 'count(*) as pv', 'count(DISTINCT report_id) as report_count']
    order_by = model.order_by or '`account` asc, pv desc'
    group = ['`account`']
    data = repository.get_list('report_visit_log', where, fields, order_by, group=group, page=page)
    title = {
        'user_name': '用户名', 'account': '账号', 'pv': '访问次数', 'report_count': '访问报告数'
    }
    Excel.export_xls('访问用户统计.xls', data, title, response=response)


def get_report_user_detail_log(model: models.QueryVisitLogModel):
    where, _ = get_query_log_where(model)
    page = [model.page, model.page_size]
    order_by = model.order_by or 'pv desc'
    if model.report_id:
        fields = [
            'MAX(`user_name`) as `user_name`', '`account`', 'MAX(`report_name`) as `report_name`',
            'MAX(`report_type`) as `report_type`', 'MAX(`report_id`) as `report_id`', 'count(*) as pv'
        ]
        group = ['`account`']
        count_filed = ['count(DISTINCT `account`)']
    else:
        fields = [
            'MAX(`user_name`) as `user_name`', 'MAX(`account`) as `account`', '`report_id`',
            'MAX(`report_name`) as `report_name`', 'MAX(`report_type`) as `report_type`', 'count(*) as pv'
        ]
        group = ['`report_id`']
        count_filed = ['count(DISTINCT `report_id`)']
    data = repository.get_list('report_visit_log', where, fields, order_by, group=group, page=page)
    count = repository.get_value('report_visit_log', where, count_filed)
    return {'data': data, 'page_size': model.page_size, 'page': model.page, 'total': count if count else 0}


def export_report_user_detail_log(model: models.QueryVisitLogModel, response):
    where, _ = get_query_log_where(model)
    page = [1, 20000]
    order_by = model.order_by or 'pv desc'
    if model.report_id:
        fields = [
            'MAX(`user_name`) as `user_name`', '`account`', 'MAX(`report_name`) as `report_name`',
            'MAX(`report_type`) as `report_type`', 'MAX(`report_id`) as `report_id`', 'count(*) as pv'
        ]
        group = ['`account`']
        title = {'user_name': '用户名', 'account': '账号', 'report_name': '报表名称', 'pv': '访问次数'}
    else:
        fields = [
            'MAX(`user_name`) as `user_name`', 'MAX(`account`) as `account`', '`report_id`',
            'MAX(`report_name`) as `report_name`', 'MAX(`report_type`) as `report_type`', 'count(*) as pv'
        ]
        group = ['`report_id`']
        title = {'report_name': '报表名称', 'report_type': '报表分类', 'user_name': '用户名', 'account': '账号', 'pv': '访问次数'}
    data = repository.get_list('report_visit_log', where, fields, order_by, group=group, page=page)
    Excel.export_xls('访问用户统计.xls', data, title, response=response)


def get_visit_time():
    time = repository.get_value('report_visit_log', {}, ['created_on'], order_by='created_on asc')
    if not time:
        return ''
    return time.strftime("%Y-%m-%d")
