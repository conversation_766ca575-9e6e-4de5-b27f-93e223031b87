#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    流程服务
    Created by
"""

import base64
import hmac
from hashlib import sha1
import time
from urllib.parse import urlparse
import os

from typing import Dict, Union

from dmplib.utils.errors import UserError

try:
    import urllib.request as urllib
except ImportError:
    import urllib2 as urllib

from components.oss import OSSFileProxy
from components.oss_sts import OssStsFileProxy
from components.global_utils import aes_encode
from dmplib import config
import logging
from dmplib.utils.strings import uid

logger = logging.getLogger(__name__)


def upload_image(image_file):
    """
    上传图片文件
    :param falcon.multipart.parser.Parser image_file:
    :return:
    """

    oss_url = OSSFileProxy().upload(
        image_file, max_size=2097152, allow_extensions=['.bmp', '.png', '.gif', '.jpg', '.jpeg', '.svg']
    )

    # 图片外网访问地址替换成config配置的域名
    if config.get('OSS.img_domain_url'):
        oss_domain = config.get('OSS.endpoint').replace('//', '//' + config.get('OSS.bucket') + '.')
        img_path = oss_url.split(oss_domain).pop()
        return config.get('OSS.img_domain_url') + img_path
    else:
        return oss_url


def get_sts_token() -> Dict[str, Union[int, str]]:
    """
    :获取oss sts临时token
    :date 2017/11/9
    :param :
    :return :
    """
    token_data = OssStsFileProxy().get_security_token()
    return aes_encode(token_data)


def get_oss_signature(file_name, method='GET', content_type=None):
    """
    获取签名的oss地址
    :param file_name:
    :param method:
    :param content_type:
    :return:
    """
    access_key_id = config.get('OSS.access_key_id')
    access_key_secret = config.get('OSS.access_key_secret')
    bucket = config.get('OSS.bucket')
    endpoint = config.get('OSS.endpoint')
    if endpoint.find('http') > -1:
        _url = urlparse(endpoint)
        url = '{scheme}://{bucket}.{netloc}{path}{query}'.format(
            scheme=_url.scheme,
            bucket=bucket,
            netloc=_url.netloc,
            path=_url.path,
            query=('?' + _url.query if _url.query else ''),
        )
    else:
        url = 'http://{bucket}.{endpoint}'.format(bucket=bucket, endpoint=endpoint)
    now = int(time.time())
    expire = now - (now % 1800) + 3600
    tosign = "%s\n\n\n%d\n/%s/%s" % (method, expire, bucket, file_name)
    if method in ('PUT', 'POST'):
        tosign = "%s\n\n%s\n%d\n/%s/%s" % (method, content_type, expire, bucket, file_name)
    h = hmac.new(access_key_secret.encode(), tosign.encode(), sha1)
    sign = urllib.quote(base64.b64encode(h.digest()).strip())
    oss_url = '%s/%s?OSSAccessKeyId=%s&Expires=%d&Signature=%s' % (url, file_name, access_key_id, expire, sign)

    return {
        'oss_signature_url': oss_url,
        'url': url,
        'file_name': file_name,
        'endpoint': endpoint,
        'access_key_id': access_key_id,
        'access_key_secret': access_key_secret,
        'bucket': bucket,
        'expire': expire,
        'signature': sign,
    }


def upload_file(**kwargs):
    """
    上传文件
    max_size: 104,857,600 bytes
    :param file:
    :return:
    """
    try:
        data = {}
        file = kwargs.get('file')
        upload_location = kwargs.get('upload_location', '')
        file_name = kwargs.get('file_name')
        new_file_name = ''
        if upload_location:
            # 检查上传的文件后缀名
            check_file_extension(upload_location)
            upload_location, new_file_name = get_random_file_name(upload_location)

        # 检查上传的文件后缀名
        check_file_extension(file_name)
        if not new_file_name:
            # 文件名改为随机guid
            _, new_file_name = get_random_file_name(file_name)

        root = upload_location.split('/')[0] if '/' in upload_location else ''
        oss_url = OSSFileProxy().upload(file, file_name=new_file_name, root=root, key=upload_location, max_size=104857600)
    except Exception as e:
        logger.debug(str(e))
        raise UserError(code=400, message=str(e))

    data['url'] = oss_url
    data['file_name'] = new_file_name
    return data


def check_file_extension(file_name):
    extension = get_file_extension(file_name)
    allow_extensions = [
        '.xlsx', '.xls', '.csv',
        '.zip', '.rptx',
        '.mkv', '.mp4', '.avi', '.mov', '.wmv',
        '.bmp', '.png', '.gif', '.jpg', '.jpeg', '.svg'
    ]
    if extension not in allow_extensions:
        raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))


def get_file_extension(file_name):
    """
    获取文件后缀名
    :param file_name:
    :return:
    """
    extension = os.path.splitext(os.path.split(file_name)[-1])[-1] or ''
    extension = extension.lower()
    return extension


def get_random_file_name(file_name):
    """
    不采用用户自定义文件名，文件名称随机生成
    :param file_name:
    :return:
    """
    path, _ = os.path.split(file_name)
    extension = get_file_extension(file_name)
    new_file_name = uid() + extension
    return os.path.join(path, new_file_name), new_file_name
