#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/19 10:47
# <AUTHOR> wangf10
# @File     : third_party.py


from base import repository
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib import config
from dmplib.hug import g
from urllib.parse import urlencode
from base.enums import ThirdPartyAppCode
from integrate.models import (
    ThirdPartyModel,
    ThirdPartyAppModel,
    UserToThirdPartyModel,
    ThirdPartyUrlModel, ThirdPartyParamModel,
)
from components.common_service import get_cloud_apps
from integrate.repositories import third_party_user_repositories, third_party_app_repositories


def init_system_third_party(func):
    def wrapper(*arg, **kwargs):
        cloud_name = config.get('Product.mysoft_cloudapp_name', None)
        app_id = g.code
        app_secret = config.get('Product.mysoft_cloudapp_default_secret', '')
        user_secret = config.get('Product.mysoft_cloudapp_default_user_secret', '')
        if cloud_name:
            init_third_data = {}
            init_app_data = {}
            cloud_app_list = get_cloud_apps()
            for app in cloud_app_list:
                if app.get('app_name') == cloud_name:
                    app_code = app.get('app_code')
                    # 获取当前第三方中是否已有当前app_code的数据
                    result = repository.get_one(ThirdPartyModel.__TABLE__, {'app_code': app_code})
                    # 如果没有则组装数据
                    if not result:
                        init_third_data = {
                            'id': seq_id(), 'name': cloud_name, 'app_code': app.get('app_code'),
                            'corp_id': app_id, 'corp_secret': app_secret, 'user_secret': user_secret, 'is_system': 1
                        }
                        init_app_data = {
                            'id': seq_id(), 'name': cloud_name, 'third_party_id': init_third_data.get('id'),
                            'app_id': app_id, 'app_secret': app_secret, 'app_type': '1,2'
                        }
                    break
            if init_third_data:
                # 初始化默认第三方
                repository.add_data(ThirdPartyModel.__TABLE__, init_third_data)
                repository.add_data(ThirdPartyAppModel.__TABLE__, init_app_data)
        return func(*arg, **kwargs)

    return wrapper


@init_system_third_party
def get_third_party_list():
    fields = ['id', 'name', 'app_code', 'is_system']
    order_by = 'created_on ASC'
    result = repository.get_list(ThirdPartyModel.__TABLE__, {}, fields, order_by)
    # 获取渠道信息
    channel = get_cloud_apps()
    app_code_and_icon = {item.get('app_code', ''): item.get('icon', '') for item in channel}
    for row in result:
        row['icon'] = app_code_and_icon.get(row['app_code'], '')
    return result


@init_system_third_party
def get_used_third_party_list():
    """
    返回配置了域名和用户接口的第三方应用
    """
    fields = ['id', 'name', 'app_code']
    order_by = 'created_on ASC'
    third_party = repository.get_list(ThirdPartyModel.__TABLE__, {}, fields, order_by)
    # 获取渠道信息
    channel = get_cloud_apps()
    app_code = [
        item.get('app_code') for item in channel if item.get('get_user_api')
    ]
    data = []
    for key, value in enumerate(third_party):
        if value.get('app_code') in app_code:
            data.append(value)
    return data


def save_third_party_and_app(third_party: dict, third_party_app_list: list, party_info_param: list):
    """
    @param third_party 第三方信息
    @param third_party_app_list 第三方应用信息
    """
    party_info = {}
    third_party_model = ThirdPartyModel(**third_party)
    third_party_app_list = third_party_app_list if third_party_app_list else []
    third_party_app_model_list = []
    for app in third_party_app_list:
        if third_party.get('app_code', None) == ThirdPartyAppCode.YKJ.value:
            app['msg_send_type'] = 1
        third_party_app_model_list.append(ThirdPartyAppModel(**app))
    # 保存第三方信息
    party_info['party_info'] = save_third_party(third_party_model)
    # 保存第三方应用信息
    third_party_id = party_info['party_info'].get('id', None)
    party_info['party_app_list'] = save_third_party_app(third_party_app_model_list, third_party_id)
    party_info['party_info_param'] = save_party_info_param(party_info_param, third_party_id)
    cache = RedisCache('Config:')
    cache_key = 'CloudApps'
    cache.del_data(cache_key)

    return party_info


def save_third_party(third_party_model: ThirdPartyModel):
    """
    保存第三方集成信息
    @param third_party_model:
    """
    third_party_model.validate()
    party_id = third_party_model.id
    if not party_id:
        third_party_model.id = seq_id()
        repository.add_model(third_party_model.__TABLE__, third_party_model)
    else:
        repository.update_model(third_party_model.__TABLE__, third_party_model, {'id': party_id})
    data = third_party_model.get_dict()
    return data


def save_third_party_app(third_party_app_model: list, third_party_id: str = None):
    """
    保存第三方应用集成信息
    @param third_party_app_model:
    @param third_party_id
    """
    add_model_list = []
    update_model_list = []
    app_name_list = []
    data = []
    app_ids = []
    # 获取当前渠道下的应用列表
    where = {'third_party_id': third_party_id}
    ids = repository.get_column(ThirdPartyAppModel.__TABLE__, where, ['id'])
    for app_model in third_party_app_model:
        app_id = app_model.id
        if not app_id:
            app_model.id = seq_id()
            app_model.third_party_id = third_party_id
            add_model_list.append(app_model)
            app_name_list.append(app_model.name)
            app_model.validate()
            data.append(app_model.get_dict())
        else:
            app_model.third_party_id = third_party_id
            update_model_list.append(app_model)
            app_name_list.append(app_model.name)
            app_model.validate()
            app_ids.append(app_id)
            data.append(app_model.get_dict())
    # 判断名称列表中是否有重复的
    if len(app_name_list) != len(list(set(app_name_list))):
        raise UserError(message='应用名称有重复')
    if add_model_list:
        fields = list(add_model_list[0].get_dict().keys())
        repository.add_list_model(ThirdPartyAppModel.__TABLE__, add_model_list, fields)
    if update_model_list:
        for update_model in update_model_list:
            fields = list(update_model.get_dict().keys())
            where = {'id': update_model.id}
            repository.update_model(ThirdPartyAppModel.__TABLE__, update_model, where, fields)
    del_list = list(set(ids).difference(set(app_ids))) if app_ids else ids
    if del_list:
        repository.delete_data(ThirdPartyAppModel.__TABLE__, {'id': del_list})
    return data


def save_party_info_param(third_party_param_list: list, third_party_id: str):
    """
    保存第三方应用集成参数信息
    @param third_party_param_list:
    @third_party_id： 集成id
    """
    data = []
    for param in third_party_param_list:
        param['third_party_id'] = third_party_id
        app_id = param['id']
        if not app_id:
            param['id'] = seq_id()
            param_model = ThirdPartyParamModel(**param)
            repository.add_list_data(ThirdPartyParamModel.__TABLE__, [param_model.get_dict()],
                                     ['id', 'third_party_id', 'key', 'name', 'value', 'desc', 'can_empty'])
        else:
            where = {'id': app_id}
            param_model = ThirdPartyParamModel(**param)
            repository.update_data(ThirdPartyParamModel.__TABLE__, {'value': param_model.value}, where)
        data.append(param_model.get_dict())
    return data


def get_third_party_and_app(party_id: str):
    """
    根据第三方ID获取第三方和第三方应用信息
    @param party_id
    """
    if not party_id:
        return False
    data = {}
    fields = ['id', 'name', 'app_code', 'corp_id', 'corp_secret', 'user_secret']
    party_where = {"id": party_id}
    data['party_info'] = repository.get_one(ThirdPartyModel.__TABLE__, party_where, fields)
    app_where = {"third_party_id": party_id}
    fields = ['id', 'third_party_id', 'name', 'app_id', 'app_secret', 'app_type', 'agent_id']
    order_by = 'created_on ASC'
    data['party_app_list'] = repository.get_list(ThirdPartyAppModel.__TABLE__, app_where, fields, order_by)
    data['party_info_param'] = repository.get_list(ThirdPartyParamModel.__TABLE__, app_where)
    return data


def delete_party_by_id(party_id: str):
    """
    删除第三方
    @param party_id
    """
    if not party_id:
        return False
    # 判断第三方是否使用
    data = repository.get_one('user_2_third_party', {'third_party_id': party_id}, ['id'])
    if data:
        raise UserError(message='第三方已被用户绑定，请解除后在删除')
    data = third_party_user_repositories.is_exit_user_source_by_party_id(party_id)
    if data:
        raise UserError(message='第三方已被用户渠道引用，请更换后在删除')
    data = repository.get_one('data_source', {'third_party_id': party_id}, ['id'])
    if data:
        raise UserError(message='第三方已被数据源引用，请更换后在删除')
    party = {"id": party_id}
    repository.delete_data(ThirdPartyModel.__TABLE__, party)
    party_id_where = {'third_party_id': party_id}
    repository.delete_data('third_party_seq', party_id_where)
    repository.delete_data(ThirdPartyParamModel.__TABLE__, party_id_where)
    return repository.delete_data(ThirdPartyAppModel.__TABLE__, party_id_where)


def delete_party_app_by_app_ids(app_id: list):
    if not app_id:
        return False
    app = {'id': app_id}
    return repository.delete_data(ThirdPartyAppModel.__TABLE__, app)


def save_bind_third_party_user(user_id, bing_third_user_model: list):
    data = []
    third_party_ids = []
    for third_user_model in bing_third_user_model:
        third_user_model.id = seq_id() if not third_user_model.id else third_user_model.id
        third_party_ids.append(third_user_model.third_party_id)
        third_user_model.validate()
        data.append(third_user_model.get_dict())
    if len(third_party_ids) != len(list(set(third_party_ids))):
        raise (UserError(message='一个数见账号只能绑定同一个第三方的一个账号'))
    # 删除对应用户绑定的第三方账号
    where = {"user_id": user_id}
    repository.delete_data(UserToThirdPartyModel.__TABLE__, where)
    if bing_third_user_model:
        fields = list(bing_third_user_model[0].get_dict().keys())
        repository.add_list_model(UserToThirdPartyModel.__TABLE__, bing_third_user_model, fields)
    return data


def delete_third_party_user(bind_id: str):
    return repository.delete_data(ThirdPartyModel.__TABLE__, {'id': bind_id})


def get_bind_party_user(user_id: str):
    return third_party_user_repositories.get_bind_party_user(user_id)


def get_account_by_third_account(account: str, third_party_id: str):
    if not account or not third_party_id:
        raise UserError(message='绑定信息不完整')
    return third_party_user_repositories.get_account_by_third_account(account, third_party_id)


def get_third_party_by_app_code(app_code: str):
    return repository.get_one('third_party', {'app_code': app_code})


def get_one_app_dashboard_info_by_party_id(party_id: str):
    return repository.get_one('third_party_app', {'third_party_id': party_id, 'app_type like': '%{}%'.format('1')})


def get_third_party_and_app_by_app_id(app_id: str):
    return third_party_user_repositories.get_third_party_and_app_by_app_id(app_id)


def get_cloud_app_code_by_app_id(app_id: str):
    third_party_info = third_party_user_repositories.get_third_party_and_app_by_app_id(app_id)
    return third_party_info.get('app_code') if third_party_info else None


def get_third_party_and_app_list():
    data = third_party_user_repositories.get_dashboard_third_app_list()
    dict_list = {}
    for row in data:
        if row.get('third_party_id') in dict_list:
            dict_list[row.get('third_party_id')].append(row)
        else:
            dict_list[row.get('third_party_id')] = [row]
    return dict_list


def set_third_party_app_redirect_url(app_info: dict, redirect_url: str, normal_share_url: str):
    return_data = []
    mysoft_app_code = [ThirdPartyAppCode.YK.value, ThirdPartyAppCode.YKJ.value, ThirdPartyAppCode.YL.value]
    yzs_app_code = [ThirdPartyAppCode.YZS_NEW.value, ThirdPartyAppCode.YZS_BUILTIN.value]
    if app_info:
        for item_list in app_info.values():
            for item in item_list:
                arr = dict()
                arr['is_system'] = item.get('is_system', 0)
                arr['name'] = item.get('party_name') + '-' + item.get('app_name') \
                    if len(item_list) > 1 else item.get('party_name')
                app_code = str(item.get('app_code', ''))
                if app_code not in mysoft_app_code:
                    params = {'project_code': g.code, 'appid': item.get('app_id'),
                              'cloud_app_code': item.get('app_code'),
                              'third_party_id': item.get('third_party_id'), 'redirect': normal_share_url}
                    url_model = ThirdPartyUrlModel(**params)
                    wx_url = get_third_party_integrate_url(url_model)
                    arr['redirect_url'] = wx_url
                    # 云助手应用，集成url特别处理
                    if app_code in yzs_app_code:
                        app_id = item.get("third_party_app_app_id")
                        project_code = item.get("corp_id") if app_code == ThirdPartyAppCode.YZS_NEW.value else g.code
                        arr['redirect_url'] = get_yzs_app_redirect_url(project_code, app_id, wx_url)
                else:
                    arr['redirect_url'] = redirect_url
                return_data.append(arr)
    return return_data


def get_yzs_app_redirect_url(project_code, app_id, sj_redirect_url):
    """
    云助手集成url特别处理
    :param project_code: 数见租户code
    :param app_id: 云助手应用id
    :param sj_redirect_url:
    :return:
    """
    yzs_domain = config.get('Yzs.domain', 'https://www.fdccloud.com')
    yzs_domain = yzs_domain[: len(yzs_domain) - 1] if yzs_domain.endswith('/') else yzs_domain
    params = {
        "app_code": app_id,
        "tenant_id": project_code,
        "channel_id": "-2",
        "redirect_uri": sj_redirect_url,
    }
    redirect_url = f"{yzs_domain}/api/tenant-open/sso?" + urlencode(params)
    return redirect_url


def get_third_app_list_by_msg(query_model):
    """
    获取可供简讯使用的应用列表
    :param query_model:
    :return:
    """
    query_model.app_type = '2'
    return third_party_app_repositories.get_third_app_list(query_model)


def get_third_app_list_by_id(id_list):
    """
    按应用id获取应用信息
    :param id_list:
    :return:
    """
    return third_party_app_repositories.get_third_app_list_by_id(id_list)


def get_third_app_name(id_list):
    result = get_third_app_list_by_id(id_list)
    return {_.get("id"): _.get("name") for _ in result}


def get_third_party_app_by_id(app_record_id):
    """
    按应用记录id获取企业和应用信息
    :param app_record_id:
    :return:
    """
    return third_party_user_repositories.get_third_party_app_by_id(app_record_id)


def get_third_party_integrate_url(url_model: ThirdPartyUrlModel):
    third_url = config.get("Domain.dmp") + '/api/user/assistant?'
    params = url_model.get_dict()
    return third_url + urlencode(params)


@init_system_third_party
def get_dashboard_third_app_list(dashboard_id: str):
    from dashboard_chart.external_service import get_custom_redirect_url, get_custom_normal_redirect_url
    dict_list = get_third_party_and_app_list()
    redirect_url = get_custom_redirect_url(dashboard_id)
    normal_url = get_custom_normal_redirect_url(dashboard_id)
    return_data = set_third_party_app_redirect_url(dict_list, redirect_url, normal_url)
    return return_data


@init_system_third_party
def get_application_third_app_list(application_id: str):
    from app_menu.services.application_service import get_custom_redirect_url, get_custom_normal_redirect_url
    redirect_url = get_custom_redirect_url(application_id)
    normal_url = get_custom_normal_redirect_url(application_id)
    dict_list = get_third_party_and_app_list()
    return_data = set_third_party_app_redirect_url(dict_list, redirect_url, normal_url)
    return return_data


def get_mysoft_third_party():
    mysoft_app = get_cloud_apps()
    mysoft_app_code = [mysoft.get('app_code') for mysoft in mysoft_app if mysoft.get('is_mysoft_app', 0) == 1]
    where = {'app_code': mysoft_app_code}
    data = repository.get_list(ThirdPartyModel.__TABLE__, where, ['id', 'name'])
    return data


def get_user_by_third_party_id(third_party_id: str, user_id: str = None, account: str = None):
    if not third_party_id:
        raise UserError(message='third_party_id不能为空')
    if not user_id:
        user_id = g.userid
    if not account:
        account = g.account
    # 获取用户是否绑定了第三方的账号
    where = {'user_id': user_id, 'third_party_id': third_party_id}
    bind_user = repository.get_one('user_2_third_party', where, ['user_id', 'third_party_account'])
    if bind_user:
        account = bind_user.get('third_party_account')
    # 根据渠道ID获取用户渠道ID
    user_source_id = third_party_user_repositories.get_user_source_id_by_party_id(third_party_id)
    if not user_source_id:
        return False
    # 根据user_source_id和用户账号获取用户池中对应的用户信息
    fields = ['user_id', 'name', 'account']
    return repository.get_one('user_source_user', {'user_source_id': user_source_id, 'account': account}, fields)


def get_third_party_user_by_app_code(third_party_id: str, user_list: list):
    user_ids = [user.get('user_id') for user in user_list]
    # 获取对应第三方绑定的用户信息
    where = {'user_id': user_ids, 'third_party_id': third_party_id}
    bind_users = repository.get_list('user_2_third_party', where, ['user_id', 'third_party_account'])
    bind_users_dict = {
        bind.get('user_id'): bind.get('third_party_account') for bind in bind_users
    } if bind_users else dict()
    for user in user_list:
        bind_account = bind_users_dict.get(user.get('user_id'), None)
        user['bind_third_account'] = bind_account if bind_account else ''
    # 根据渠道ID获取用户渠道ID
    user_source_id = third_party_user_repositories.get_user_source_id_by_party_id(third_party_id)
    # 没有渠道信息则直接返回绑定用户
    if not user_source_id:
        for user in user_list:
            user['third_user_id'] = None
            user['third_user_account'] = user.get('bind_third_account', None)
            user['third_user_name'] = None
        return user_list
    user_codes = [
        user.get('bind_third_account') if user.get('bind_third_account') else user.get('account')
        for user in user_list
    ]
    # 根据user_source_id和用户账号获取用户池中对应的用户信息
    fields = ['user_id', 'name', 'account']
    third_party_users = repository.get_list(
        'user_source_user', {'user_source_id': user_source_id, 'account': user_codes}, fields
    )
    third_party_user_dict = {
        third_user.get('account'): third_user for third_user in third_party_users
    } if third_party_users else {}
    for user in user_list:
        account = user.get('bind_third_account') if user.get('bind_third_account') else user.get('account')
        third_user_info = third_party_user_dict.get(account, {})
        user['third_user_id'] = third_user_info.get('user_id', None)
        user['third_user_account'] = third_user_info.get('account', None)
        user['third_user_name'] = third_user_info.get('name', None)
    return user_list


def get_third_party_param_model(app_code):
    return repository.get_list('third_party_param_model', {'app_code': app_code})
