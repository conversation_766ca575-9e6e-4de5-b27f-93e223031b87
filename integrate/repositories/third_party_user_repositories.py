#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2021/3/19 15:30
# <AUTHOR> wangf10
# @File     : keyword_repositories.py
from dmplib.saas.project import get_db


def get_bind_party_user(user_id: str):
    sql = """select id,user_id,third_party_id,third_party_account from user_2_third_party
     where user_id = %(user_id)s"""
    params = {'user_id': user_id}
    with get_db() as db:
        return db.query(sql, params)


def get_account_by_third_account(account: str, third_party_id: str):
    sql = """select account from user where id = (select user_id from user_2_third_party 
    where third_party_id = %(third_party_id)s and third_party_account = %(account)s limit 1)
    """
    params = {'third_party_id': third_party_id, 'account': account}
    with get_db() as db:
        return db.query_scalar(sql, params)


def get_dashboard_third_app_list():
    sql = """select a.id as app_id, a.app_id as third_party_app_app_id, a.name as app_name,a.third_party_id, 
    b.name as party_name, b.is_system, b.app_code, b.corp_id 
    from third_party_app a left join third_party b on a.third_party_id = b.id where a.app_type like '%1%'
    order by a.created_on, a.id
    """
    with get_db() as db:
        return db.query(sql)


def get_third_party_and_app_by_app_id(app_id: str):
    sql = """select a.id, a.third_party_id,a.app_id, a.app_secret,a.agent_id, b.corp_id,b.app_code from 
    third_party_app a left join third_party b on a.third_party_id = b.id where a.id = %(app_id)s
    """
    params = {'app_id': app_id}
    with get_db() as db:
        return db.query_one(sql, params)


def is_exit_user_source_by_party_id(third_party_id: str):
    sql = """select id from user_source where data like %(third_party_id)s"""
    params = {'third_party_id': '%'+third_party_id+'%'}
    with get_db() as db:
        if db.query_scalar(sql, params):
            return True
        else:
            return False


def get_third_party_app_by_id(app_record_id):
    """
    按应用记录id获取企业和应用信息
    :param app_record_id:
    :return:
    """
    sql = """select a.id, a.name,a.third_party_id,a.app_id,a.app_secret,a.msg_send_type,
    b.id as party_id, b.name as party_name, b.is_system, b.app_code , b.corp_id , b.corp_secret 
    from third_party_app a left join third_party b on a.third_party_id = b.id 
    where a.id = %(app_record_id)s
    """
    params = {"app_record_id": app_record_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_user_source_id_by_party_id(third_party_id: str):
    sql = """select id from user_source where data like %(third_party_id)s"""
    params = {'third_party_id': '%' + third_party_id + '%'}
    with get_db() as db:
        return db.query_scalar(sql, params)


def get_third_party_app_corpid(app_code):
    sql = """
           select tp.corp_id, tpa.app_id, tpa.app_secret, tp.id as third_party_id from third_party_app as tpa inner join third_party as tp 
           on tp.id = tpa.third_party_id where tp.app_code = %(app_code)s
           """
    params = {"app_code": app_code}
    with get_db() as db:
        return db.query_one(sql, params)


