#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/5/18 10:40
# <AUTHOR> caoxl
# @File     : models.py
from base.models import BaseModel


class IconModel(BaseModel):
    __slots__ = ["id", "name", "description", "icon_url", "icon_type", "status"]

    def __init__(self, **kwargs):
        self.id = ""
        self.name = ""
        self.description = ""
        self.icon_url = ""
        self.icon_type = 0
        self.status = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append((["name", "icon_url"], "string", {"max": 1024, "required": True}))
        return rules


class ColorHsvModel(BaseModel):
    def __init__(self, **kwargs):
        self.h = 0
        self.s = 0
        self.v = 0
        self.a = 1
        super().__init__(**kwargs)


class ColorRgbModel(BaseModel):
    def __init__(self, **kwargs):
        self.r = 0
        self.g = 0
        self.b = 0
        self.a = 1
        super().__init__(**kwargs)


class CustomImageModel(BaseModel):
    __tablename__ = 'custom_image_upload'
    __slots__ = ["id", "oss_url", "type"]

    def __init__(self, **kwargs):
        self.id = ''
        self.oss_url = ''
        self.type = ''
        super().__init__(**kwargs)