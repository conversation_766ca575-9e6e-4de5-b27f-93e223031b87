#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/3/16.
"""
import copy
import hashlib
import os
import re
import string

from components.oss import OSSFileProxy
from dmplib import config
from urllib import parse, request
from io import BytesIO
from dmplib.utils.errors import UserError
from base import repository
from dmplib.utils.strings import seq_id
from image.models import IconModel, ColorHsvModel
import colorsys
import PIL.Image as Image
from image.repositories import custom_image_repositories


def upload_image(image_file):
    """
    上传图片文件
    :param falcon.multipart.parser.Parser image_file:
    :return:
    """

    oss_url = OSSFileProxy().upload(
        image_file, max_size=1024 * 1024 * 50, allow_extensions=['.bmp', '.png', '.gif', '.jpg', '.jpeg', '.svg']
    )

    # 图片外网访问地址替换成config配置的域名
    if config.get('OSS.img_domain_url'):
        oss_domain = config.get('OSS.endpoint').replace('//', '//' + config.get('OSS.bucket') + '.')
        img_path = oss_url.split(oss_domain).pop()
        return config.get('OSS.img_domain_url') + img_path
    else:
        return oss_url


def get_agent_image_content(image_url):
    """
    获取图片url的代理地址
    :param image_url: 图片url地址
    :return:
    """
    image_url = parse.unquote(image_url)
    parse_result = parse.urlparse(image_url)
    if not parse_result.path:
        raise UserError(message="非法的图片地址!")
    path = os.path.basename(parse_result.path)
    ext = path[path.rindex('.') + 1:]
    ext_allow_set = {'jpg', 'jpeg', 'png', 'gif', 'svg'}
    if ext not in ext_allow_set:
        raise UserError(message="只能上传jpg, jpeg, png, gif, svg格式的图片!")
    content = BytesIO()
    image_url = request.quote(image_url, safe=string.printable)
    content.write(request.urlopen(image_url).read())
    return ext, content


def get_icons_by_type(icon_type: int):
    """
    根据icon类型获取icon列表
    :param icon_type:
    :return:
    """
    return repository.get_list("function_icon", {'icon_type': icon_type, 'status': 0})


def add_icon(icon_model: IconModel):
    """
    添加自定义图标
    :param icon_model:
    :return:
    """
    icon_model.id = seq_id()
    # 校验参数
    icon_model.validate()
    repository.add_model("function_icon", icon_model, ["id", "name", "description", "icon_url", "icon_type"])
    return icon_model.id


def hsv2rgb(h, s, v):
    h /= 360
    s /= 100
    v /= 100
    r, g, b = colorsys.hsv_to_rgb(h, s, v)
    return int(round(r * 255)), int(round(g * 255)), int(round(b * 255))


def rgb2hsv(r, g, b):
    r /= 255
    g /= 255
    b /= 255
    h, s, v = colorsys.rgb_to_hsv(r, g, b)
    return h * 360, s * 100, v * 100


def get_hsv_scheme(h: int, dark_theme: bool = True):
    if h <= 54 or h >= 216:
        v = 100
        s = 80
    elif 54 < h < 108:
        v = 100 - ((10 / 54) * (h - 54)) if dark_theme else 100 - ((20 / 54) * (h - 54))
        s = 80 + ((10 / 54) * (h - 54))
    elif 108 <= h <= 180:
        v = 90 if dark_theme else 80
        s = 90
    else:
        v = 90 + ((10 / 36) * (h - 180)) if dark_theme else 80 + ((20 / 36) * (h - 180))
        s = 90 - ((10 / 36) * (h - 180))
    return {"h": h, "s": s, "v": v, "a": 1}


def get_component_colors(h1, h2, dark_theme):
    component_colors = []
    if dark_theme:
        component_colors.append(get_hsv_scheme((h2 + 360 - 10) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 + 10) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 50) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 30) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 90) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 70) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 130) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 110) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 170) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h2 + 360 - 150) % 360, dark_theme))
    else:
        component_colors.append(get_hsv_scheme(h1, dark_theme))
        component_colors.append(get_hsv_scheme((h1 + 360 - 60) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h1 + 360 - 120) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h1 + 360 - 180) % 360, dark_theme))
        component_colors.append(get_hsv_scheme((h1 + 360 - 240) % 360, dark_theme))
    return component_colors


def get_theme_colors(main_color, secondary_color):
    dark_theme = main_color.v / 100 < 0.8
    component_colors = get_component_colors(main_color.h, secondary_color.h, dark_theme)
    img_colors = {
        "dominant": {
            "rgb": to_rgba_string({'h': main_color.h, 's': main_color.s, 'v': main_color.v}),
            "hsv": [main_color.h / 360, main_color.s / 100, main_color.v / 100],
        },
        "secondary": {
            "rgb": to_rgba_string({'h': secondary_color.h, 's': secondary_color.s, 'v': secondary_color.v}),
            "hsv": [secondary_color.h / 360, secondary_color.s / 100, secondary_color.v / 100],
        },
    }
    dark_table_colors = {
        "head": {
            "h": secondary_color.h,
            "s": secondary_color.s - 40 if (secondary_color.s - 40) > 5 else 5,
            "v": secondary_color.v - 70 if (secondary_color.v - 70) > 5 else 5,
        },
        "column": [
            # 奇行
            {"h": main_color.h, "s": 25, "v": 20},
            # 偶行
            {
                "h": secondary_color.h,
                "s": secondary_color.s - 50 if (secondary_color.s - 50) > 5 else 5,
                "v": secondary_color.v - 80 if (secondary_color.v - 80) > 5 else 5,
            },
        ],
    }
    light_table_colors = {
        "head": {"h": main_color.h, "s": main_color.s - 60 if (main_color.s - 60) > 5 else 5, "v": main_color.v},
        "column": [
            {"h": main_color.h, "s": 6, "v": 98},
            {"h": main_color.h, "s": main_color.s - 75 if (main_color.s - 75) > 5 else 5, "v": main_color.v},
        ],
    }
    if dark_theme:
        theme_colors = {
            "background_colors": {
                "dashboard": {"h": main_color.h, "s": 25, "v": 20, "a": 1},
                "card": [
                    {"h": main_color.h, "s": 20, "v": 100, "a": 0.06},
                    {"h": main_color.h, "s": 20, "v": 100, "a": 0.03},
                ],
                "shadow": {"h": main_color.h, "s": 80, "v": 20, "a": 1},
            },
            "table_colors": dark_table_colors,
            "text_colors": [
                {"h": main_color.h, "s": 10, "v": 90, "a": 1},
                {"h": main_color.h, "s": 15, "v": 70, "a": 1},
                {"h": main_color.h, "s": 20, "v": 50, "a": 1},
                {"h": main_color.h, "s": 20, "v": 40, "a": 1},
            ],
            "theme_colors": component_colors,
            "img_colors": img_colors,
        }
    else:
        theme_colors = {
            "background_colors": {
                "dashboard": {"h": main_color.h, "s": 6, "v": 98, "a": 1},
                "card": [{"h": 0, "s": 0, "v": 100, "a": 0.6}],
                "shadow": {"h": main_color.h, "s": 20, "v": 80, "a": 1},
            },
            "table_colors": light_table_colors,
            "text_colors": [
                {"h": main_color.h, "s": 30, "v": 30, "a": 1},
                {"h": main_color.h, "s": 20, "v": 50, "a": 1},
                {"h": main_color.h, "s": 10, "v": 70, "a": 1},
                {"h": main_color.h, "s": 5, "v": 80, "a": 1},
            ],
            "theme_colors": component_colors,
            "img_colors": img_colors,
        }
    return theme_colors


def to_rgba_string(hsv: dict, a=None):
    r, g, b = hsv2rgb(hsv.get('h'), hsv.get("s"), hsv.get("v"))
    return f"rgba({int(r)},{int(g)},{int(b)},{hsv.get('a', 1)})" if not a else f"rgba({int(r)},{int(g)},{int(b)},{a})"


def to_rgb(color: str):
    rgb = re.findall(r'\d+\.?\d*', color)
    return int(rgb[0]), int(rgb[1]), int(rgb[2])


def smart_beauty_by_rgb(rgb):
    if len(rgb) > 1:
        main_color, secondary_color = rgb[0], rgb[1]
    else:
        main_color, secondary_color = rgb[0], rgb[0]
    rgbs_main = to_rgb(main_color)
    rgbs_sec = to_rgb(secondary_color)
    rgb_default = to_rgb('rgb(24,109,235)')
    h1, s1, v1 = rgb2hsv(rgbs_main[0], rgbs_main[1], rgbs_main[2])
    h2, s2, v2 = rgb2hsv(rgbs_sec[0], rgbs_sec[1], rgbs_sec[2])
    main_color_model = ColorHsvModel(**{'h': h1, 's': s1, 'v': v1})
    # default hsv
    h, s, v = rgb2hsv(rgb_default[0], rgb_default[1], rgb_default[2])
    secondary_color_model = (
        ColorHsvModel(**{'h': h2, 's': s2, 'v': v2}) if h2 > 20 else ColorHsvModel(**{"h": h, "s": s, "v": v})
    )
    dark_theme = main_color_model.v / 100 < 0.75

    theme_colors = get_theme_colors(main_color_model, secondary_color_model)
    format_theme_colors(theme_colors, dark_theme)
    return theme_colors


def smart_beauty_by_file(file):
    secondary_color_model = ColorHsvModel(**{})

    img = Image.open(file)
    colors = img.getcolors()
    if not colors:
        raise UserError(500, "读取图片颜色失败")
    sorted_colors = sorted(colors, key=lambda x: x[0], reverse=True)
    main_color_rgba = sorted_colors[0][1]
    h1, s1, v1 = rgb2hsv(*main_color_rgba)
    # default
    dh, ds, dv = rgb2hsv(24, 109, 235)
    main_color_model = ColorHsvModel(**{'h': h1, 's': s1, 'v': v1})
    dark_theme = main_color_model.v / 100 < 0.75

    for color in sorted_colors[1:]:
        h, s, v = rgb2hsv(*color[1])
        if dark_theme and v / 100 > 0.7 and h > 20:
            secondary_color_model = ColorHsvModel(**{'h': h, 's': s, 'v': v})
        elif not dark_theme and v / 100 < 0.7 and h > 20:
            secondary_color_model = ColorHsvModel(**{'h': h, 's': s, 'v': v})
        else:
            secondary_color_model = ColorHsvModel(**{'h': dh, 's': ds, 'v': dv})

    theme_colors = get_theme_colors(main_color_model, secondary_color_model)
    format_theme_colors(theme_colors, dark_theme)
    return theme_colors


def proc_color(color: dict):
    if color['s'] != 0:
        color['s'] /= 100
    if color['v'] != 0:
        color['v'] /= 100
    return color


def format_theme_colors(theme_colors, dark_theme):
    raw_theme_colors = copy.deepcopy(theme_colors)
    deal_theme_color(theme_colors)
    if dark_theme:
        components = []
        for i in range(0, 10, 2):
            components.append(
                [
                    to_rgba_string(raw_theme_colors['theme_colors'][i + 1], 1),
                    to_rgba_string(raw_theme_colors['theme_colors'][i], 1),
                ]
            )
        theme_colors['theme_colors'] = components
    theme_colors['darkTheme'] = dark_theme
    return theme_colors


def deal_theme_color(theme_colors):
    for item, colors in theme_colors.items():
        if item == "img_colors":
            continue
        if item == "background_colors":
            deal_background_colors(colors)
        elif item == "table_colors":
            deal_table_colors(colors)
        elif isinstance(colors, list):
            for index, color in enumerate(colors):
                theme_colors[item][index] = to_rgba_string(color)


def deal_table_colors(colors):
    for label, color in colors.items():
        if label == "head":
            colors[label] = to_rgba_string(color)
        else:
            for index, column_color in enumerate(colors[label]):
                colors[label][index] = to_rgba_string(column_color)


def deal_background_colors(colors):
    for label, color in colors.items():
        if label == "card":
            for index, card_color in enumerate(colors[label]):
                colors[label][index] = to_rgba_string(card_color)
        else:
            colors[label] = to_rgba_string(color)


def smart_beauty(**kwargs):
    file = kwargs.get("image")
    rgb = kwargs.get("color")
    if rgb and not isinstance(rgb, list):
        raise UserError(400, "参数格式不正确")
    if not file and not rgb:
        raise UserError(400, "参数不允许为空")
    if rgb:
        return smart_beauty_by_rgb(rgb)
    return smart_beauty_by_file(file)


def upload_custom_image_util(image_file, suffix):
    file_name = hashlib.md5(image_file.file.read()).hexdigest()
    image_file.file.seek(0)  # read后content会为空，重新将文件seek到0
    file_name = f'{file_name}.{suffix}'
    oss_url = OSSFileProxy().upload(
        image_file, max_size=1024 * 1024 * 50, file_name=file_name,
        allow_extensions=['.bmp', '.png', '.gif', '.jpg', '.jpeg', '.svg']
    )
    # 图片外网访问地址替换成config配置的域名
    if config.get('OSS.img_domain_url'):
        oss_domain = config.get('OSS.endpoint').replace('//', '//' + config.get('OSS.bucket') + '.')
        img_path = oss_url.split(oss_domain).pop()
        return config.get('OSS.img_domain_url') + img_path
    else:
        return oss_url


def upload_custom_image(image_file, kwargs):
    # 图片文件属性中的后缀
    image_file_type_tmp = (image_file.filename or '').split('.')
    image_file_type = image_file_type_tmp[-1] if image_file_type_tmp else ''

    # 前端传过来的后缀
    front_image_type = kwargs.get('type', '')

    # 前端没传的话取文件属性中的后缀
    image_type = front_image_type or image_file_type
    if not image_type:
        raise UserError(message='缺少必要的文件类型')
    oss_url = upload_custom_image_util(image_file, suffix=image_type)

    is_existed = custom_image_repositories.get_custom_image_by_oss_url(oss_url, image_type)
    if not is_existed:
        custom_image_repositories.add_custom_image_record(oss_url, image_type)
    return True


def get_custom_image_list(kwargs):
    front_image_type = kwargs.get('type', '')
    # page = kwargs.get('page') or 1
    # page_size = kwargs.get('page_size') or 20
    # page_arg = int(page), int(page_size)
    return custom_image_repositories.get_custom_image_list(front_image_type)


def delete_custom_image(kwargs):
    id = kwargs.get('id', '')
    return custom_image_repositories.delete_custom_image_by_id(id)
