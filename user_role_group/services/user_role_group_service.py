#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2020/7/31.
"""
from base import repository
from base.dmp_constant import DEFAULT_USER_ROLE_GROUP
from base.dmp_constant import DEFAULT_USER_ROLE_GROUP_PARENT_ID
from dmplib.constants import ADMINISTRATORS_GROUP_CODE
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from level_sequence.models import UserRoleGroupLevelSequenceModel
from level_sequence.services.level_sequence_service import generate_level_code
from user_group.repositories import user_group_repository

from user_role_group.repositories import user_role_group_repository
from rbac.models import Role
from rbac.services.role_service import update_user_role_to_group, generate_user_role_level_code

buildin_application_id = '39e0ff72-ec9f-49ac-605e-f7e70201cd5a'


def get_user_role_group(role_group_id):
    if not role_group_id:
        raise UserError(message='缺少用户角色组id')
    fields = ['id', 'name', 'parent_id', 'level_code']
    user_role_group = repository.get_data('user_role_group', {'id': role_group_id}, fields)
    if not user_role_group:
        raise UserError(message='用户角色组不存在')
    return user_role_group


def get_user_role_group_tree(**kwargs):
    """
    :param kwargs:
    :return:
        add search func:
        groups = []
        if keyword match groups:
            return groups and group(roles)
        if keyword match roles:
            roles = get_roles()
            temp_group_roles_map = {}
            for role in roles:
                if role.role_group_id not in temp_group_roles_map:
                    temp_group_roles_map[role_group_id] = []
                    temp_group_roles_map[role_group_id].append(role)
                else:
                    temp_group_roles_map[role_group_id].append(role)

            for role_group_id, roles in temp_group_roles_map.items():
                group = get_group_by_role_group_id(role_group_id)
                group["roles"] = roles
                groups.append(group)
    """
    from user.services.developer_service import Developer
    keyword = kwargs.get("keyword", '')
    data = {'groups': []}
    fields = ['id', 'name', 'parent_id', 'level_code', 'created_by', 'created_on', 'modified_by', 'modified_on']
    user_role_fields = [
        'id',
        'name',
        'description',
        'created_on',
        'modified_on',
        'created_by',
        'modified_by',
        'account_mode',
        'role_group_id',
        'parent_id',
        'level_code',
        'user_source_id',
    ]
    if not keyword:
        user_role_group = repository.get_data(
            'user_role_group', {}, fields=fields, multi_row=True, order_by=[('created_on', 'asc')]
        )
        for group in user_role_group:
            group['roles'] = []
            group['roles'] = repository.get_data(
                'user_role',
                {'role_group_id': group['id'], 'id !=': Developer.ADMIN_DEVELOPER_ROLE_ID},
                fields=user_role_fields,
                multi_row=True,
                order_by=[('level_code', 'asc')],
            )
            data['groups'].append(group)
    else:
        user_role_group = user_role_group_repository.get_user_role_group_list_by_name(keyword)
        for group in user_role_group:
            group['roles'] = []
            group['roles'] = repository.get_data(
                'user_role',
                {'role_group_id': group['id'], 'id !=': Developer.ADMIN_DEVELOPER_ROLE_ID},
                fields=user_role_fields,
                multi_row=True,
                order_by=[('level_code', 'asc')],
            )
            data['groups'].append(group)
        roles = user_role_group_repository.get_user_role_list_by_name(keyword)
        temp_group_roles_map = {}
        for role in roles:
            if role["role_group_id"] not in temp_group_roles_map:
                temp_group_roles_map[role["role_group_id"]] = []
                temp_group_roles_map[role["role_group_id"]].append(role)
            else:
                temp_group_roles_map[role["role_group_id"]].append(role)

        for role_group_id, roles in temp_group_roles_map.items():
            group = get_user_role_group(role_group_id)
            group["roles"] = roles
            data["groups"].append(group)

    return data


def add_user_role_group(model):
    """
    添加用户角色组
    :param user_role_group.models.UserRoleGroupModel model:
    :return:
    """
    if check_user_role_group_exists(model.name):
        raise UserError(code=403, message="角色組名已存在")
    model.id = seq_id()
    model.level_code = generate_group_level_code(model.parent_id)
    model.validate()
    fields = ['id', 'name', 'parent_id', 'level_code']
    return repository.add_model('user_role_group', model, fields)


def check_user_role_group_exists(group_name):
    """

    :param group_name:
    :return:
    """
    return user_role_group_repository.check_group_name_exists(group_name)


def user_role_group_exists(group_id):
    """
    :param group_id:
    :return:
    """
    return user_role_group_repository.check_group_exists(group_id)


def move_role_to_group(role_id, target_group_id):
    """
    :param role_id:
    :param target_group_id:
    :return:
    """
    if not user_role_group_exists(target_group_id):
        raise UserError(code=404, message="目标用户角色组不存在！")
    return update_user_role_to_group(target_group_id, role_id)


def update_user_role_group(model):
    """
    修改用户角色组
    :param user_role_group.models.UserRoleGroupModel model:
    :return:
    """
    model.validate()
    if check_user_role_group_exists(model.name):
        raise UserError(code=403, message="角色組名已存在")
    return repository.update_data('user_role_group', {'name': model.name}, {'id': model.id})


def delete_user_role_group(role_group_id):
    """
    删除用户角色组
    :param role_group_id:
    :return:
    """
    affect_row = 0
    # 移动原分组下角色到默认分组
    batch_move_user_role(role_group_id)
    # 删除角色分组
    affect_row += user_role_group_repository.delete_user_role_group(role_group_id)
    return affect_row


def batch_move_user_role(source_group_id):
    """
    批量移动原分组角色到默认分组下
    :param source_group_id:
    :return:
    """
    if user_role_group_repository.has_role_in_group(source_group_id):
        try:
            # 原分组下所有角色全部转移到默认分组下
            role_list = user_role_group_repository.get_user_role_by_group_id(source_group_id)
            for role in role_list:
                role_model = Role(**role)
                # 改为目标分组
                role_model.role_group_id = DEFAULT_USER_ROLE_GROUP
                # 更新level_code层级
                role_model.level_code = generate_user_role_level_code(role_model)
                fields = ['role_group_id', 'level_code']
                repository.update_model('user_role', role_model, {"id": role.get("id")}, fields)
        except Exception:
            raise UserError(code=500, message="转移角色至默认分组失败")


def get_user_group_list(group_id=None):
    """
    获取用户组列表
    :param group_id:
    :return:
    """
    if not group_id:
        return user_group_repository.get_user_group_list()
    else:
        group_code = validate_group_auth(group_id)
        return user_group_repository.get_user_group_list_by_group_code(group_code)


def get_user_role_group_list():
    """
    获取用户组列表
    :return:
    """
    return user_role_group_repository.get_user_role_group_list()


def get_group_by_id(group_id):
    """
    :param group_id:
    :return:
    """
    return repository.get_data("user_role_group", {"id": group_id}, ["name"])


def user_role_group_is_exists(group_name):
    """
    用户角色组是否存在
    :param group_name:
    :return:
    """
    exist = repository.data_is_exists('user_role_group', {'name': group_name})
    return exist


def generate_group_level_code(parent_id):
    """
    根据父级组生成层级编码
    :param parent_id:
    :return:
    """
    if not parent_id:
        parent_id = DEFAULT_USER_ROLE_GROUP_PARENT_ID
    return generate_level_code(UserRoleGroupLevelSequenceModel(level_id=parent_id))


# pylint: disable=W0613
def validate_group_auth(group_id):
    """
    验证用户组权限
    :param str group_id:
    :param bool only_sub:
    :return:
    """
    if group_id is None or group_id == '':
        group_code = ADMINISTRATORS_GROUP_CODE
    else:
        group_code = user_group_repository.get_user_group_code(group_id)
    if not group_code:
        raise UserError(message='用户组编码缺失')

    return group_code
