#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Class
    Author:<EMAIL>
    Created: 2020/8/06
"""

from tests.base import BaseTest

import builtins
import unittest

import logging

from user_role_group.services.user_role_group_service import (
    get_user_role_group_tree,
    add_user_role_group,
    update_user_role_group,
    delete_user_role_group,
    move_role_to_group,
)

from user_role_group.models import UserRoleGroupModel

logger = logging.getLogger(__name__)


class TestUserGroupService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code="test", account="test")
        builtins.code = 'test'
        builtins.account = 'test'

    def test_get_user_role_group(self):
        kwargs = {"keyword": "管理员"}
        tree = get_user_role_group_tree(**kwargs)
        print(tree)

    def test_add_user_role_group(self):
        kwargs = {"name": "test", "parent_id": '', "level_code": ''}
        model = UserRoleGroupModel(**kwargs)
        ret = add_user_role_group(model)
        print(ret)

    def test_update_user_role_group(self):
        kwargs = {'id': "39f6c45f-cdbc-6e7e-dd78-afee6c319fcc", "name": "test", "parent_id": '', "level_code": ''}
        model = UserRoleGroupModel(**kwargs)
        ret = update_user_role_group(model)
        print(ret)

    def test_delete_user_role_group(self):
        kwargs = {'id': "39f6c45f-cdbc-6e7e-dd78-afee6c319fcc"}
        model = UserRoleGroupModel(**kwargs)
        ret = delete_user_role_group(model.id)
        print(ret)

    def test_move_role_to_group(self):
        kwargs = {
            'id': "39e8b0ca-a9a1-c4f6-bb37-eb2885a2ae62",
            'curr_group_id': '00000001-0000-0000-0000-000000000000',
            'target_group_id': '39f6c469-4258-8be8-1ad6-a6ea39cd84b7',
        }
        ret = move_role_to_group(kwargs.get('id'), kwargs.get('target_group_id'))
        print(ret)


if __name__ == '__main__':
    unittest.main()
