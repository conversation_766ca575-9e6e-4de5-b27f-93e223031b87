#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
from base.models import BaseModel


class UserRoleGroupModel(BaseModel):
    __slots__ = ['id', 'name', 'parent_id', 'level_code']

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.parent_id = None
        self.level_code = None
        super().__init__(**kwargs)
