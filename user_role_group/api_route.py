#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2020/07/31.
"""
from rbac.validator import PermissionValidator
from user_role_group.models import UserRoleGroupModel
from user_role_group.services import user_role_group_service

from dmplib.hug import APIWrapper
from dmplib.utils.errors import UserError
from user_log.models import UserLogModel


api = APIWrapper(__name__)


@api.admin_route.post('/add', validate=PermissionValidator('user-role.edit'))
def add_user_role_group(request, **kwargs):
    """
    添加用户角色组
    :param request:
    :param dict kwargs:
    :return tuple:
    """
    model = UserRoleGroupModel(**kwargs)
    model.name = model.name.strip()
    if not model.name:
        raise UserError(message='分组名称不能为空')

    user_role_group_service.add_user_role_group(model)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'add_user_role_group',
            'id': kwargs.get('id'),
            'content': '创建用户角色分组 [ {name} ] '.format(name=model.name),
        },
    )

    return True, '添加成功', model.id


@api.admin_route.post('/move', validate=PermissionValidator('user-role.edit'))
def move_user_role(request, **kwargs):
    """
    :param request:
    :param kwargs:
    :return:
    """
    role_id = kwargs.get('id', '')
    curr_group_id = kwargs.get('curr_group_id', '')
    target_group_id = kwargs.get('target_group_id', '')
    affect_row = user_role_group_service.move_role_to_group(role_id, target_group_id)
    if affect_row:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'move_role_group',
                'id': kwargs.get('id'),
                'content': '变更用户角色所在角色组,变更前[{before}],变更后[{after}]'.format(before=curr_group_id, after=target_group_id),
            },
        )
    return True, '移动成功', target_group_id


@api.admin_route.post('/update', validate=PermissionValidator('user-role.edit'))
def update_user_role_group(request, **kwargs):
    """
    修改用户角色组
    :param request:
    :param dict kwargs:
    :return tuple:
    """
    model = UserRoleGroupModel(**kwargs)
    model.name = model.name.strip()
    if not model.name:
        raise UserError(message='分组名称不能为空')
    old_group = user_role_group_service.get_group_by_id(model.id)
    user_role_group_service.update_user_role_group(model)

    if old_group.get("name") != model.name:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'update_role_group',
                'id': kwargs.get('id'),
                'content': '用户角色组修改,变更前[{before}],变更后[{after}]'.format(before=old_group.get("name"), after=model.name),
            },
        )
    return True, '修改成功', model.id


@api.admin_route.post('/delete', validate=PermissionValidator('user-role.edit'))
def delete_user_role_group(request, **kwargs):
    """
    删除用户角色组，并将角色组下的角色移动至默认角色组
    :param request:
    :param kwargs:
    :return:
    @apiParam query {string}  user_role_group_id 用户角色组ID
    """
    group = user_role_group_service.get_group_by_id(kwargs.get("id"))
    result = user_role_group_service.delete_user_role_group(kwargs.get('id'))
    if result:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'delete_group',
                'id': kwargs.get('id'),
                'content': '删除用户角色组 [ {name} ] '.format(name=group.get("name")),
            },
        )

    return (True, '删除成功', result) if result else (False, '删除失败', result)


@api.admin_route.get('/tree', validate=PermissionValidator('user-role.view'))
def get_user_role_group(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /api/user_role_group/tree
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              groups[{
                "id": "********-0000-0000-0000-************",
                "name": "默认分组",
                "level_code": "1000-",
                "parent_id": "",
                "roles":[{
                    "id": "3a76c84a-beee-4f4e-b219-009bc474643d",
                    "name": "部门领导",
                    "description": "",
                    "account_mode": "DMP",
                    "role_group_id":"********-0000-0000-0000-************",
                    parent_id: "",
                    level_code: "0001-"
                    "created_on":,
                    "created_by":,
                    "modified_on":,
                    "modified_by":,
                }],
                "created_on":,
                "created_by":,
                "modified_on":,
                "modified_by":,
              }]
    }
    */

    add search func:
        groups = []
        if keyword match groups:
            return groups and group(roles)
        if keyword match roles:
            roles = get_roles()
            temp_group_roles_map = {}
            for role in roles:
                if role.role_group_id not in temp_group_roles_map:
                    temp_group_roles_map[role_group_id] = []
                    temp_group_roles_map[role_group_id].append(role)
                else:
                    temp_group_roles_map[role_group_id].append(role)

            for role_group_id, roles in temp_group_roles_map.items():
                group = get_group_by_role_group_id(role_group_id)
                group["roles"] = roles
                groups.append(group)
   """
    return True, '', user_role_group_service.get_user_role_group_tree(**kwargs)


@api.admin_route.get('/list', validate=PermissionValidator('user-role.view'))
def get_user_role_group_list():
    """
    获取用户角色组列表
    :return:
    """
    return True, '', user_role_group_service.get_user_role_group_list()
