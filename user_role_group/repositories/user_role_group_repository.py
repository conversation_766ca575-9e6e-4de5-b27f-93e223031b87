#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2020/08/01.
"""

from base.dmp_constant import DEFAULT_USER_ROLE_GROUP
from dmplib.saas.project import get_db
import logging

from dmplib.utils.errors import UserError


def get_user_role_group_list():
    """
    获取用户角色组列表
    :return:
    """
    sql = 'SELECT * FROM user_role_group ORDER BY `created_on` ASC'
    with get_db() as db:
        return db.query(sql)


def get_user_role_group_list_by_name(name):
    """
    关键字匹配获取用户角色组列表
    :return:
    """
    name_tmp = ''
    for s in name:
        name_tmp += '\\' + str(s)
    sql = 'SELECT * FROM user_role_group WHERE `name` LIKE %(name)s ORDER BY `created_on` ASC'
    with get_db() as db:
        return db.query(sql, {'name': '%' + name_tmp + '%'})


def get_user_role_list_by_name(name):
    """
    关键字匹配获取用户角色组列表
    :return:
    """
    name_tmp = ''
    for s in name:
        name_tmp += '\\' + str(s)
    sql = 'SELECT * FROM user_role WHERE `name` LIKE %(name)s ORDER BY `level_code` ASC'
    with get_db() as db:
        return db.query(sql, {'name': '%' + name_tmp + '%'})


def check_group_exists(group_id):
    """
    检查用户角色组是否存在
    :param groups:
    :return:
    """
    ret = {'c': 0}
    sql = "select count(1) as c from user_role_group where id =%(id)s"
    with get_db() as db:
        ret = db.query_one(sql, {'id': group_id})
        logging.info(ret['c'])
    return ret['c']


def check_group_name_exists(group_name):
    """
    检查用户角色组是否存在
    :param group_name:
    :return:
    """
    ret = {'c': 0}
    sql = "select count(1) as c from user_role_group where name =%(name)s"
    with get_db() as db:
        ret = db.query_one(sql, {'name': group_name})
        logging.info(ret['c'])
    return ret['c']


def check_role_exists(role_id):
    ret = {'c': 0}
    sql = "select count(1) as c from user_role where id =%(role_id)s"
    with get_db() as db:
        ret = db.query_one(sql, {'id': role_id})
        logging.info(ret['c'])
    return ret['c']


def get_level_code(group_id):
    """
    获取用户角色组编码
    :param group_id:
    :return:
    """
    sql = 'SELECT `level_code` FROM user_role_group WHERE id=%(id)s LIMIT 1'
    with get_db() as db:
        return db.query_scalar(sql, {'id': group_id})


def get_user_role(user_group_id):
    sql = (
        " select user_group_role.group_id,user_role.* from user_role "
        "inner join user_group_role on user_group_role.role_id = user_role.id "
        "WHERE user_group_role.group_id = '{user_group_id}' ".format(user_group_id=user_group_id)
    )
    with get_db() as db:
        return db.query(sql)


def has_role_in_group(group_id):
    sql = "SELECT count(*) as c FROM user_role WHERE role_group_id = %(group_id)s"
    with get_db() as db:
        return db.query_scalar(sql, {"group_id": group_id})


def delete_group_by_id(sql, group_id):
    with get_db() as db:
        return db.exec_sql(sql, {'id': group_id})


def delete_user_role_group(group_id: str):
    """
    删除用户角色组
    :param group_id:
    :return:
    """
    sql = 'DELETE FROM user_role_group WHERE `id` = %(id)s'
    if group_id == DEFAULT_USER_ROLE_GROUP:
        raise UserError(code=403, message="没有权限删除默认分组")
    return delete_group_by_id(sql, group_id)


def move_user_role_to_group(src_id, dst_id=DEFAULT_USER_ROLE_GROUP):
    """
    移动角色到指定分组，默认为默认分组
    :param src_id: 源组id
    :param dst_id: 目标组id
    :return:
    """
    sql = 'UPDATE user_role SET role_group_id=%(dst_id)s WHERE `role_group_id`=%(src_id)s'
    with get_db() as db:
        return db.exec_sql(sql, {'dst_id': dst_id, 'src_id': src_id})


def get_role_ids_by_group_ids(group_ids: list):
    sql = 'SELECT role_id FROM user_group_role WHERE group_id in %(group_ids)s'
    params = {'group_ids': group_ids}
    with get_db() as db:
        return db.query_columns(sql, params)


def get_user_role_by_group_id(group_id):
    sql = "SELECT * FROM user_role WHERE role_group_id = %(group_id)s order by level_code asc"
    with get_db() as db:
        return db.query(sql, {"group_id": group_id})
