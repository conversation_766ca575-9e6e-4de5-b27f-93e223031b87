from falcon.api import API
from falcon.routing.compiled import CompiledRouter
from typing import Callable
import falcon

origin_falcon_compile = falcon.routing.compiled.CompiledRouter._compile
origin_set_error_serializer = falcon.api.API.set_error_serializer


def __empty_action(self: CompiledRouter) -> None:  # pylint:disable=unused-argument
    # print('defer compile')
    pass


def __reset_falcon_find(self: CompiledRouter) -> None:
    # print('start compiling')
    self._find = self._compile()


def wrapper_set_error_serializer(self: API, serializer: Callable) -> None:
    falcon.routing.compiled.CompiledRouter._compile = origin_falcon_compile
    self._router.__reset_falcon_find()
    return origin_set_error_serializer(self, serializer)


def patch():

    # disable _compile to save time
    falcon.routing.compiled.CompiledRouter._compile = __empty_action
    falcon.routing.compiled.CompiledRouter.__reset_falcon_find = __reset_falcon_find

    falcon.api.API.set_error_serializer = wrapper_set_error_serializer
