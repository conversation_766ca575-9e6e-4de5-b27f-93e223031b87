#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : app_hook.py
# @Author: guq
# @Date  : 2021/8/26
# @Desc  :
import traceback

from components.versioned_query import VersionedQuery
from components.analysis_time import AnalysisTimeUtils
from dmplib.db.mysql_wrapper import SimpleMysql
from dmplib.utils.logs import logger
from dmplib.utils.logs import init_logging
from dmplib import config


def correct_logging():
    # 以前的logging有个bug，就是在项目启动至app.py中的init_logging执行之间的日志等级，不受控制
    # 比如debug信息在info级别也会出现，需要将以前的init_logging提前，提到项目启动最前面
    import os
    os.environ['DMP_ROOT_PATH'] = os.path.dirname(os.path.abspath(__file__))
    init_logging()


# 修改dmp的_execute方法，支持多版本查询
def versioned_execute(self, sql: str, params=None):
    backup_sql, backup_params = sql, params
    try:
        versioned_query = VersionedQuery(sql, params, db=self)
        if versioned_query.need_deal:
            sql, params = versioned_query.versioned_format()
    except:
        sql, params = backup_sql, backup_params
        logger.error(f'版本sql查询错误：{traceback.format_exc(limit=1)}')

    AnalysisTimeUtils.recode_time_node('开始查询SQL')
    data = self._execute_kept(sql, params)
    AnalysisTimeUtils.recode_time_node('结束查询SQL', extra={'sql': sql, 'params': params})
    return data


def hook_dmplib_execute():
    SimpleMysql._execute_kept = SimpleMysql._execute
    SimpleMysql._execute = versioned_execute


# def app_mysql_connect_timing():
#     from components.wait_lock import WaitLocker
#
#     def connect(self):
#         with WaitLocker('ffgvbhufguasgfhjasfgshjagfhja', 10) as locker:
#             locker.lock(1)
#             AnalysisTimeUtils.recode_time_node('开始连接MySQL')
#             self.connect_kept()
#             AnalysisTimeUtils.recode_time_node('结束连接MySQL')
#
#     SimpleMysql.connect_kept = SimpleMysql.connect
#     SimpleMysql.connect = connect


def app_mysql_connect_timing():
    def connect(self):
        AnalysisTimeUtils.recode_time_node('开始连接MySQL')
        self.connect_kept()
        AnalysisTimeUtils.recode_time_node('结束连接MySQL')

    SimpleMysql.connect_kept = SimpleMysql.connect
    SimpleMysql.connect = connect


def app_redis_connect_timing():
    from redis.connection import Connection

    def send_command(self, *args):
        AnalysisTimeUtils.recode_time_node('开始执行Redis')
        self.send_packed_command(self.pack_command(*args))
        AnalysisTimeUtils.recode_time_node('结束执行Redis', extra={'command': args})

    Connection.send_command_kept = Connection.send_command
    Connection.send_command = send_command


def include_snapshot_models():
    from components.snapshot_service import SNAPSHOT_SERVICES

    # 只有在导包时候装饰器才会被执行
    __import__('dashboard_chart.snapshot_models')
    __import__('dataset.snapshot_models')
    __import__('app_menu.snapshot_models')

    logger.debug('all registered snapshot services: %s' % str(SNAPSHOT_SERVICES.registered_names))


def func_timing_hook():
    """
    指定函数添加计时公共功能，代码完全由配置文件开启或者关闭。
    第一步（给函数添加计时）：               app_hook.py->func_timing/data_query.py
    第二步（计时装饰器，添加时间以及栈信息）：  func_timing/timing.py.py
    第三步（中间件返回数据添加计时信息）：     func_timing/middlewares.py
    :return:
    """

    if not config.get('App.enable_profiling') in ('True', 'true', '1', 1):
        return

    from func_timing.middlewares import add_func_timing_middleware
    add_func_timing_middleware()

    # hook掉_gevent_waiter
    if config.get('App.hook_waiter') in ('True', 'true', '1', 1):
        from dmplib.db import mysql_wrapper
        mysql_wrapper._gevent_waiter = None

    import inspect
    import os
    from func_timing.timing import time_interval
    from func_timing.nodes import NODES, NODES_MAP

    for module_or_class, func_name, stack_level, extra in [n.hook_func for n in NODES if not n.is_root]:
        # 处理思路： 将要添加计时的函数使用time_interval包一层进行替换，等价于装饰器
        # 1. 模块中的函数：  直接处理后进行替换
        # 2. 类中的函数：   将自己本身以及其集成的子类一起，将函数处理进行替换
        func = getattr(module_or_class, func_name)
        if not inspect.isfunction(func):
            raise RuntimeError('timing hook func error: %r' % func)
        if inspect.isclass(module_or_class):
            # 找到所有的继承子类, 并且处理所有子类同名方法
            subclasses = module_or_class.__subclasses__() or []
            subclasses.append(module_or_class)
            module_or_class = subclasses
        else:
            module_or_class = [module_or_class]

        for _module_or_class_ in module_or_class:
            real_func = getattr(_module_or_class_, func_name)  # 如果是继承的子类，必须是自己的重写的真实方法
            setattr(real_func, '__stack_level__', stack_level)
            setattr(real_func, '__node_extra__', extra)
            if (
                    inspect.isclass(_module_or_class_)
                    and isinstance(inspect.getattr_static(_module_or_class_, func_name), staticmethod)
            ):
                # 如果被处理的函数是类里面的staticmethod，这个方法需要重新被staticmethod装饰
                setattr(_module_or_class_, func_name, staticmethod(time_interval(real_func)))
            else:
                setattr(_module_or_class_, func_name, time_interval(real_func))

    setattr(config, '__hook_timing_func__', NODES_MAP)
    setattr(config, '__root_path__', os.path.dirname(os.path.abspath(__file__)))


def update_ssl_certifi():
    import ssl
    import certifi
    from urllib import request

    # 创建一个包含根证书的SSL上下文
    ssl_context = ssl.create_default_context(cafile=certifi.where())

    # 创建一个opener并将SSL上下文设置为全局默认
    opener = request.build_opener(request.HTTPCookieProcessor(), request.HTTPSHandler(context=ssl_context))
    request.install_opener(opener)


def hook_effect():
    # 更正logging
    correct_logging()

    # 取数接口函数计时
    # func_timing_hook()

    # hook dmplib的_execute 方法
    hook_dmplib_execute()

    # app_mysql_connect_timing()

    # app_redis_connect_timing()

    # 载入拍照的models
    include_snapshot_models()

    # 更新根证书
    update_ssl_certifi()
