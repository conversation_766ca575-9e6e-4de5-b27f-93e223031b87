

import hug

from dmplib.hug import APIWrapper, g
from dmplib import config
from hug.authentication import authenticator

from publish_center.services import publish_center_service
from publish_center.services.publish_center_service import push_deliver


@authenticator
# pylint: disable=W0613
def _verify_auth_key_handle(request, response, verify_user, **kwargs):
    """

    :param request:
    :param response:
    :return:
    """
    token = request.auth if request.auth else request.get_header('Authorization')
    if not token:
        print("获取Authorization失败")
        return False

    jwt_secret = config.get('PublishCenter.secret')
    if jwt_secret == token:
        return True
    else:
        print(token, jwt_secret)
        return False


@authenticator
def _verify_auth_key_rdc_handle(request, response, verify_user, **kwargs):
    """

    :param request:
    :param response:
    :return:
    """
    token = request.auth if request.auth else request.get_header('Authorization')
    if not token:
        print("获取Authorization失败")
        return False

    jwt_secret = 'aijqgdgNQII2h7ZyHemQmzQ7fldsNXbVb2bMzkjT8otxRMhrI4R2BUamDn1XzM6n'
    if jwt_secret == token:
        return True
    else:
        print(token, jwt_secret)
        return False


class PublishCenterAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_publish_center_route', '_publish_center_rdc_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._publish_center_route = None
        self._publish_center_rdc_route = None
        self.api.http.base_url = '/publish_center'

    @property
    def publish_center_route(self):
        if not self._publish_center_route:
            self._publish_center_route = hug.http(api=self.api, requires=_verify_auth_key_handle(None))
        return self._publish_center_route

    @property
    def publish_center_rdc_route(self):
        if not self._publish_center_rdc_route:
            self._publish_center_rdc_route = hug.http(api=self.api, requires=_verify_auth_key_rdc_handle(None))
        return self._publish_center_rdc_route


api = PublishCenterAPIWrapper(__name__)


@api.publish_center_route.post('/push_version')
def push_version(**kwargs):
    """
    发布中心推送的制品在应用端更新

    """
    return push_deliver(**kwargs)


@api.publish_center_route.post('/find_project_list')
def find_project_list(**kwargs):
    """
    发布中心查询所有的租户
    """
    return publish_center_service.find_project_list(kwargs.get("page"), kwargs.get("size"), kwargs.get("key"))


@api.publish_center_rdc_route.post('/get_admin_url')
def get_admin_url():
    return True, "", config.get("Domain.dmp_admin")
