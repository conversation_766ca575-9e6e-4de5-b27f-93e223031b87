import os
from threading import Thread

import jwt
import requests
from loguru import logger
from dmplib.hug import g
from dmplib import config
from dmplib.utils.errors import UserError
from dmplib.db.mysql_wrapper import get_db as get_master_db


def register_publish_center():
    try:
        # 注册到发布中心
        if not config.get('PublishCenter.host'):
            logger.error(f"未配置发布中心地址")
            return
        env_code = os.environ.get('CONFIG_AGENT_CLIENT_CODE') or config.get("PublishCenter.env", "local_test")
        dmp_domain = config.get('Domain.dmp')
        dmp_version = config.get('PublishCenter.dmp_version', "")
        t = Thread(target=request_report_center, args=("/openapi/env/report",
                              {"app": "DMP", "env": env_code, "domain": dmp_domain, "appVersion": dmp_version}))
        t.start()
    except Exception as e:
        logger.error(f"register_publish_center error: {e}")


def get_admin_token():
    secret = config.get('PublishCenter.secret')
    payload = {
        "id": 'report_center',
        "account": "report_center",
        "code": "report_center",
    }
    return jwt.encode(payload, secret)


def push_deliver(**kwargs):
    """
        "artifact":""  制品ID
        "pushId":""    推送记录ID
        "artifactName":""  制品名称
        "projects":[{
            "code":"",
            "name":"",
            "customerId":"",
        }]    分发租户列表
        "fileList":[{
            "filePath":"" 分发包路径
            "versionName":""  产品名称
        }]   分发内容
        "allProjects":0 是否分发所有租户
        "replaceSource":0 是否覆盖数据源
        "lockDataset":1 是否锁定数据集
        "distributeType":1 是否锁定报表
    """
    token = get_admin_token()
    host = config.get("Domain.dmp_admin")
    url = host + "/api/open/publish_center/deliver"
    # 制品ID
    artifact = kwargs.get("artifact")
    # 推送ID
    pushId = kwargs.get("pushId")
    artifact_name = kwargs.get("artifactName")
    projects = kwargs.get("projects")
    projects = [project.get('code') for project in projects] if projects else []
    file_list = kwargs.get("fileList")
    parameters = {
        "projects": projects,
        "file_list": file_list,
        "artifact_name": artifact_name,
        "is_all_projects": kwargs.get("allProjects"),
        "distribute_type": kwargs.get("distributeType"),
        "replace_data_source": kwargs.get("replaceSource"),
        "is_lock_dataset": kwargs.get("lockDataset")
    }
    response = requests.post(url,params={"token": token}, json=parameters, timeout=60)
    if response.status_code != 200:
        raise UserError(
            message=' 状态：' + str(response.status_code) + ' , ' + response.reason + '。' + response.text
        )
    result = response.json()
    if not result.get('result'):
        raise UserError(
            message=result.get('msg')
        )
    return True, "", None


def find_project_list(page, page_size, keyword):
    page_size = int(page_size) if page_size else 9999
    page = (int(page) if page else 1) - 1
    skip = page_size * page
    sql = 'SELECT SQL_CALC_FOUND_ROWS `id`,`name`,`code`,customer_id FROM `project` '
    params = {}
    wheres = []
    keyword = keyword.replace('_', '\\_') if keyword else None
    if keyword:
        wheres.append('( `name` LIKE %(keyword)s OR `title` LIKE %(keyword)s OR `code` LIKE %(keyword)s)')
        params['keyword'] = '%' + keyword + '%'
    sql += (' WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' ORDER BY created_on DESC'
    sql += ' LIMIT ' + str(skip) + ',' + str(page_size)
    with get_master_db() as db:
        items = db.query(sql, params)
        total = db.query_scalar('SELECT FOUND_ROWS() AS total;')
        return True, "", {"data": items, "total": total}


def get_publish_center_headers():
    secret = config.get('PublishCenter.secret')
    payload = {
        "code": "",
        "account": "",
        "env": os.environ.get('CONFIG_AGENT_CLIENT_CODE') or config.get("PublishCenter.env", "local_test")
    }
    jwt_token = jwt.encode(payload, secret, 'HS256')

    return {"Authorization": "Bearer " + jwt_token}


def request_report_center(url, params):
    host = config.get('PublishCenter.host')
    response = requests.post(host + url, json=params, headers=get_publish_center_headers(), timeout=6)
    if response.status_code != 200:
        raise UserError(
            message=' 状态：' + str(response.status_code) + ' , ' + response.reason + '。' + response.text
        )
    result = response.json()
    if result.get("code") and result.get("code") in [200, "200"]:
        return result.get("data")
    else:
        raise UserError(
            message=' 状态：' + str(result.get("code")) + ' , ' + str(result.get("message"))
        )