#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from base.repository import _get_db
from base import repository

from dmplib.hug import g

FIELDS = (
    '`id`,`name`,`icon`,`logo`,`background`,`statement`, '
    '`account_memory`,`enable`,`icp`,`tenant_code`'
)


def get_home_template_list(query_model):
    # pylint:disable=line-too-long
    with _get_db(from_config_db=True) as db:
        sql = f'SELECT {FIELDS} FROM `home_template` '
        sql += ' ORDER BY created_on DESC'
        query_model.total = repository.get_total(sql, db=db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql)
    return query_model


def get_all():
    return _get_db(from_config_db=True).query(
        f'SELECT {FIELDS} FROM `home_template`'
    )


def get_home_yysc_template_all():
    return _get_db(from_config_db=True).query(
        'SELECT `id`,`name`,`logo`,`redirect_url`,`template_type` FROM `home_yysc_template`'
    )


def get_user_recent_edit_dashboards_data():
    sql = """
    SELECT  dd.id, dd.name, dd.parent_id, dd.level_code, dr.edit_time,
            dd.platform, dd.new_layout_type, dd.terminal_type
     FROM dashboard_recent_edit_record as dr 
    JOIN dashboard as dd ON dr.dashboard_id = dd.id
    WHERE account = %(account)s and analysis_type = ''
    ORDER BY dr.edit_time DESC LIMIT 10
    """
    account = getattr(g, 'account', '')
    params = {'account': account}

    data = repository.get_data_by_sql(sql, params) or []
    return data


def get_dashboards_by_ids(level_codes):
    if not level_codes:
        return []
    sql = """
    SELECT  dd.id, dd.name, dd.level_code FROM dashboard as dd 
    WHERE level_code in %(level_codes)s and type = "FOLDER"
    """
    params = {'level_codes': level_codes}
    data = repository.get_data_by_sql(sql, params) or []
    return data
