# -*- coding: utf-8 -*-
# pylint: skip-file
import io
import json
import zipfile
from copy import deepcopy

import xmltodict

from base.enums import ImportFileType
from components.oss import OSSFileProxy
from dashboard_chart.repositories import dashboard_repository
from dataset.repositories import dataset_repository
from dmplib.utils.errors import UserError
from app_menu.repositories import external_application_repository
from urllib import parse

from imports.services import import_simple_file


def read_file_from_oss(file_url):
    if not file_url:
        return None

    oss_file = OSSFileProxy()
    obj_result = oss_file.get_object(parse.unquote(file_url), True)
    if not obj_result:
        raise UserError(400, '读取文件失败')

    bytes_data = obj_result.read()
    if len(bytes_data) == 0:
        raise UserError(400, '文件为空，读取失败')
    return bytes_data


def parse_file(file_url, file_type=ImportFileType.ZIP.value):
    if file_type == ImportFileType.RPTX.value:
        return parse_rptx_file(file_url)
    else:
        return parse_zip_file(file_url)


def parse_rptx_file(file_url):
    def postprocessor(path, key, value):
        if isinstance(value, dict) and '@p4:nil' in value and value['@p4:nil'] == 'true':
            return key, None
        return key, value

    data = read_file_from_oss(file_url)
    package_dict = xmltodict.parse(data, postprocessor=postprocessor, encoding='utf-8')
    return package_dict


# def parse_zip_file(file_url):
#     bytes_data = read_file_from_oss(file_url)
#     data = io.BytesIO(bytes_data)
#     export_data = None
#     with zipfile.ZipFile(data, "r") as zip_file:
#         for name in zip_file.namelist():
#             with zip_file.open(name) as zfile:
#                 r_data = zfile.read()
#                 if r_data:
#                     try:
#                         export_data = json.loads(r_data.decode('utf-8'))
#                     except:
#                         raise UserError(400, 'zip压缩文件内容格式有误')
#                     break
#
#     if not export_data:
#         raise UserError(400, '无效的数据文件格式')
#
#     title = export_data.get('title', '')
#     if not title:
#         raise UserError(400, '缺少title字段')
#
#     return export_data

def parse_zip_file(file_url):
    return import_simple_file.parse_zip_file(file_url)

def get_preview_dashboards(dashboards: dict):
    preview_dashboards = []

    if not dashboards:
        return preview_dashboards

    dashboard_ids = [dashboard_id for dashboard_id in dashboards.keys()]
    if not dashboard_ids:
        return preview_dashboards
    exist_dashboards = dashboard_repository.get_dashboard_by_ids(dashboard_ids)
    for dashboard_id, dashboard_data in dashboards.items():
        if not dashboard_data.get("dashboard"):
            raise UserError(400, '数据格式错误，dashboards中的dashboard不能为空')
        is_exist = False

        for exist_dashboard in exist_dashboards:

            if dashboard_data.get("dashboard")[0].get("id") == exist_dashboard.get("id"):
                is_exist = True
                break

        preview_dashboard = dashboard_data.get("dashboard")[0]
        preview_dashboard["check_status"] = bool(is_exist)
        preview_dashboards.append(preview_dashboard)
        preview_dashboards.extend(dashboard_data.get("dashboard_folders", []))

    return preview_dashboards


def _get_dashboard_new_id(id):
    s_ids = _get_split_char_36(id)
    new_id = merge_split_ids(
        [s_ids[6], s_ids[7], s_ids[2], s_ids[3], s_ids[4], s_ids[5], s_ids[0], s_ids[1]]) if s_ids else ''
    return new_id


def _get_mobile_new_id(id):
    s_ids = _get_split_char_36(id)
    new_id = merge_split_ids(
        [s_ids[5], s_ids[6], s_ids[7], s_ids[3], s_ids[4], s_ids[0], s_ids[1], s_ids[2]]) if s_ids else ''
    return new_id


def _get_split_char_36(id):
    if id and len(id) == 36:
        return [id[0:4], id[4:8], id[9:13], id[14:18], id[19:23], id[24:28], id[28: 32], id[32:36]]
    return None


def merge_split_ids(split_ids):
    if split_ids and len(split_ids) == 8:
        return split_ids[0] + split_ids[1] + '-' + split_ids[2] + '-' + split_ids[3] + '-' \
            + split_ids[4] + '-' + split_ids[5] + split_ids[6] + split_ids[7]
    return None


def get_preview_applications(applications: dict):
    """
    门户没有文件夹概念，需要兼容输出树形结构数据
    :param application_ids:
    :return:
    """
    preview_applications = []
    if not applications or not applications.keys():
        return preview_applications
    db_applications = external_application_repository.batch_get_application_info(list(applications.keys()))
    db_application_ids = [item.get("id") for item in db_applications]
    import_applications = [item.get("application")[0] for _, item in applications.items() if item.get("application")]
    for application in import_applications:
        is_exist = True if application.get("id") in db_application_ids else False
        preview_application = deepcopy(application)
        preview_application["check_status"] = is_exist
        preview_application["type"] = ""
        preview_application["parent_id"] = ""
        preview_application["level_code"] = ""
        preview_applications.append(preview_application)
    return preview_applications


def get_preview_datasets(datasets: list):
    preview_datasets = []

    if not datasets:
        return preview_datasets

    dataset_ids = [dataset_data.get("dataset_id") for dataset_data in datasets]
    exist_datasets = dataset_repository.get_dataset_by_ids(dataset_ids)
    dataset_names = [dataset_data.get("dataset", {}).get("name") for dataset_data in datasets]
    exist_dataset_names = dataset_repository.get_dataset_by_names(dataset_names)

    for dataset in datasets:
        is_exist = False
        for exist_dataset in exist_datasets:
            if dataset.get("dataset_id") == exist_dataset.get("id"):
                is_exist = True
                break
        if not is_exist and dataset.get("dataset", {}).get("name") in exist_dataset_names:
            is_exist = True
        preview_dataset = dataset.get("dataset")
        preview_dataset["check_status"] = bool(is_exist)
        preview_datasets.append(preview_dataset)
        preview_datasets.extend(dataset.get('dataset_folders', []))
    return preview_datasets


def get_preview_datasource(datasets: list):
    preview_datasource = []
    if not datasets:
        return preview_datasource
    datasource_ids = list(set([
        dataset_data.get("data_source", {}).get("id", None)
        for dataset_data in datasets if dataset_data.get("data_source")
    ]))
    for datasource_id in datasource_ids:
        if not datasource_id:
            continue
        data = {'source_id': datasource_id, 'name': '', 'dataset_list': []}
        for dataset in datasets:
            if not dataset.get("data_source"):
                continue
            source_id = dataset.get("data_source", {}).get("id", None)
            name = dataset.get("data_source", {}).get("name", None)
            if source_id and datasource_id == source_id:
                data['name'] = name
                data['dataset_list'].append(
                    {
                        'dataset_name': dataset.get("dataset", {}).get("name", ''),
                        'dataset_id': dataset.get('dataset_id', '')
                    }
                )
        preview_datasource.append(data)
    return preview_datasource


def build_record_tree(records):
    records = remove_duplicate_records(records)
    data_mapping = {r.get('id'): r for r in records}
    result = []
    for r in records:
        parent_id = r.get('parent_id')
        if r.get('level_code', '').count('-') == 1 or not parent_id or r.get('id') == parent_id:
            result.append(r)
        else:
            parent_r = data_mapping.get(parent_id)
            if not parent_r:
                continue
            if not parent_r.get('sub'):
                parent_r['sub'] = []
            parent_r['sub'].append(r)
    return result


def build_record_tree_v1(records):
    records = remove_duplicate_records(records)
    data_mapping = {r.get('id'): r for r in records}
    result = []
    for r in records:
        parent_id = r.get('parent_id')
        if len(r.get('level_code', '')) == 5 or not parent_id or r.get('id') == parent_id:
            result.append(r)
        else:
            parent_r = data_mapping.get(parent_id)
            if not parent_r:
                result.append(r)
                continue
            if not parent_r.get('sub'):
                parent_r['sub'] = []
            parent_r['sub'].append(r)
    return result


def remove_duplicate_records(records):
    if not isinstance(records, list):
        return []
    ids = set()
    new_result = []
    for r in records:
        if r.get('id') in ids:
            continue
        ids.add(r.get('id'))
        new_result.append(r)
    new_result = sorted(new_result, key=lambda x: x.get('level_code'))
    return new_result


def filter_records_fields(records, allowed_fields=None):
    allowed_fields = allowed_fields or {'id', 'name', 'created_on', 'type', 'level_code', 'parent_id', 'check_status'}
    filtered_records = []
    for r in records:
        data = {}
        for field, value in r.items():
            if field in allowed_fields:
                data[field] = value
        filtered_records.append(data)
    return filtered_records


def get_preview_feed(feed_data: dict):
    from base import repository
    preview_feed = []
    feed_list = feed_data.get('dashboard_email_subscribe') or []
    if not feed_list:
        return []
    all_feed_ids = [feed.get('id') for feed in feed_list]
    has_feed_ids = repository.get_column('dashboard_email_subscribe', {'id': all_feed_ids}, ['id']) or []
    for feed in feed_list:
        copy_feed = {}
        is_exist = True if feed.get('id') in has_feed_ids else False
        copy_feed['id'] = feed.get('id')
        copy_feed['subject_email'] = feed.get('subject_email')
        copy_feed['msg_subscribe_config'] = feed.get('msg_subscribe_config')
        copy_feed['check_status'] = is_exist
        preview_feed.append(copy_feed)
    return preview_feed

