#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
    <NAME_EMAIL> on 2019/05/22.
"""
import json

from base.enums import DashboardType, ProjectValueAddedFunc
from base.errors import UserError
from imports.models import DashboardImportsQueryModel
from imports.repositories import dashboard_imports_repositories
from dashboard_chart.repositories import external_dashboard_repository
from imports.services.import_helper import build_record_tree


def get_dashboard_imports_list(query_model: DashboardImportsQueryModel):
    """
    获取报告导出列表
    :param query_model:
    :return:
    """
    data = dashboard_imports_repositories.get_dashboard_import_list(query_model)
    if not data.get('items'):
        return data

    for r in data.get('items'):
        try:
            content = json.loads(r['content']) if r['content'] else {}
        except:
            content = {}
        r.pop('content')

        # 显示在线报告，明细报告的报告名称
        report_list = content.get('dashboards', [])
        if content.get(ProjectValueAddedFunc.PPT.value):
            report_list.extend(content.get(ProjectValueAddedFunc.PPT.value))
        if content.get(ProjectValueAddedFunc.ACTIVE_REPORT.value):
            report_list.extend(content.get(ProjectValueAddedFunc.ACTIVE_REPORT.value))
        if content.get('large_screens'):
            report_list.extend(content.get('large_screens') or [])
        r['source_dashboard_names'] = '、'.join(
            [r.get('name') for r in report_list
             if r.get('type', '') != DashboardType.CHILD_FILE.value])
        r['source_dataset_names'] = '、'.join([r.get('name') for r in content.get('datasets', [])])
    return data


def get_dashboard_import_detail(dashboard_import_id: str):
    """
    获取报告导出记录详情
    :param dashboard_import_id:
    :return:
    """
    data = dashboard_imports_repositories.get_dashboard_import_by_id(dashboard_import_id)
    if not data:
        raise UserError(400, '报告导入记录不存在')

    try:
        result = json.loads(data['content']) if data['content'] else {'dashboards': [], 'datasets': []}
    except:
        result = {'dashboards': [], 'datasets': []}
    result.update({'filename': data.get('filename')})
    if result.get('dashboards'):
        result['dashboards'] = [r for r in result.get('dashboards') if r.get('type') != DashboardType.CHILD_FILE.value]
    return result


def get_level_code_tree(data, table_name):
    if not data:
        return []

    level_codes = set()
    for r in data:
        level_code = r.get('level_code')
        level_codes.add(level_code)
        while len(level_code) >= 10:
            level_code = level_code[:-5]
            level_codes.add(level_code)

    level_codes = list(level_codes)
    if table_name == 'dashboard':
        data = external_dashboard_repository.get_dashboards_records_by_level_codes(level_codes)
    if table_name == 'dataset':
        data = external_dashboard_repository.get_dataset_records_by_level_codes(level_codes)

    return build_record_tree(data)
