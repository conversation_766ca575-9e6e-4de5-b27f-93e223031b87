import io
import json
import zipfile

from dmplib.utils.errors import UserError

from imports.services import import_helper


def parse_zip_file(file_url):
    # 指定 base_info.json 文件路径
    base_info_file_path = 'base/info.json'

    bytes_data = import_helper.read_file_from_oss(file_url)
    data = io.BytesIO(bytes_data)
    export_data = None
    with zipfile.ZipFile(data, "r") as zip_file:
        # 判断 ZIP 文件中是否存在 base_info.json 文件
        if base_info_file_path in zip_file.namelist():
            # 如果存在 base_info.json 文件，读取该文件的内容
            with zip_file.open(base_info_file_path) as base_info_file:
                try:
                    export_data = json.loads(base_info_file.read().decode('utf-8'))
                except:
                    raise UserError(400, 'zip压缩文件内容格式有误')

                read_map(export_data, 'dashboards', zip_file)
                read_list(export_data, 'datasets', zip_file)
                read_map(export_data, 'large_screens', zip_file)
                read_map(export_data, 'applications', zip_file)
                read_feeds(export_data, 'feeds', zip_file)
                read_active_reports(export_data, zip_file)
                read_other(export_data, 'ppt', zip_file)

        else:
            # 如果不存在 base_info.json 文件，读取根目录下的 .json 文件
            json_files = [name for name in zip_file.namelist() if name.endswith('.json')]
            for file_name in json_files:
                with zip_file.open(file_name) as file:
                    r_data = file.read()
                    if r_data:
                        try:
                            export_data = json.loads(r_data.decode('utf-8'))
                        except:
                            raise UserError(400, 'zip压缩文件内容格式有误')
                        break
    return export_data


def read_map(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = {}
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _map = json.loads(content.decode('utf-8'))
            for key,value in _map.items():
                export_data.get(directory)[key] = value


def read_list(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = []
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _list = json.loads(content.decode('utf-8'))
            export_data[directory].append(_list)


def read_feeds(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = {}
    export_table = {
        'dashboard_email_subscribe': 'id', 'dashboard_subscribe_display_format': 'subscribe_id',
        'mobile_subscribe_filter': 'email_subscribe_id',
        'mobile_subscribe_rules': 'email_subscribe_id', 'mobile_subscribe_chapters': 'email_subscribe_id', 'flow': 'id'
    }
    export_data[directory] = {}
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _map = json.loads(content.decode('utf-8'))
            for key, value in export_table.items():
                if not export_data.get(directory).get(key):
                    export_data.get(directory)[key] = []
                _val = _map.get(key) or []
                export_data.get(directory).get(key).extend(_val)


def read_active_reports(export_data, zip_file):
    is_active_reports = True
    files_in_directory = [name for name in zip_file.namelist() if name == "active_reports/base.json"]
    if not files_in_directory or len(files_in_directory) <= 0:
        is_active_reports = False
        files_in_directory = [name for name in zip_file.namelist() if name == 'report_center/base.json']
    # 遍历二级目录下的文件，并读取其内容
    if files_in_directory and len(files_in_directory) > 0:
        file_name = files_in_directory[0]
        with zip_file.open(file_name) as file:
            content = file.read()
            export_data["active_reports"] = json.loads(content.decode('utf-8'))
        if not is_active_reports:
            ids = export_data["active_reports"]["ids"]
            export_data["active_reports"]["ids"] = []
            export_data["report_center"] = {"data": {}, "ids": ids}


def read_other(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if name == (directory+"/base.json")]
    if files_in_directory and len(files_in_directory) > 0:
        file_name = files_in_directory[0]
        with zip_file.open(file_name) as file:
            content = file.read()
            export_data[directory] = json.loads(content.decode('utf-8'))
