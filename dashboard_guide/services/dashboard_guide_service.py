#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/10

"""
service
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from base import repository
from base.enums import DashboardGuideRecordStatus
from dashboard_guide.models import DashboardGuideRecordModel, DashboardGuideRecordQueryModel
from dmplib.utils.errors import UserError

from typing import Dict, Union

logger = logging.getLogger(__name__)


def get_guide_status(query_model: DashboardGuideRecordQueryModel) -> Dict[str, Union[int, str]]:
    """
    获取指引状态
    :param query_model:
    :return:
    """
    query_model.validate()
    try:
        record_dict = dict()
        record_dict.update({'user_id': query_model.user_id, 'status': DashboardGuideRecordStatus.Undone.value})
        if not query_model.user_id:
            return record_dict
        record_data = repository.get_data(
            'dashboard_read_guide_record', {'user_id': query_model.user_id}, ['user_id', 'status']
        )
        if not record_data:
            return record_dict
        return record_data
    except Exception as e:
        logger.exception(e)
        msg = '获取当前指引状态异常'
        raise UserError(message=msg)


def add_guide_record(model: DashboardGuideRecordModel):
    """
    添加记录
    :param model:
    :return:
    """
    try:
        model.status = DashboardGuideRecordStatus.Finish.value
        model.validate()
        attributes = model.get_dict()
        repository.replace_list_data('dashboard_read_guide_record', [model.get_dict()], list(attributes.keys()))
        return {'user_id': model.user_id, 'status': model.status}
    except Exception as e:
        logger.exception(e)
        msg = '添加指引记录异常'
        raise UserError(message=msg)
