#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/10

"""
model
"""

# ---------------- 业务模块 ----------------
from base.models import BaseModel, QueryBaseModel
from base.enums import DashboardGuideRecordStatus
from user.services import user_service
from dmplib.utils.errors import UserError


from typing import Dict, List, Tuple


class DashboardGuideRecordModel(BaseModel):
    """
    指引记录类
    """

    __slots__ = ['user_id', 'account', 'user_name', 'status']

    def __init__(self, **kwargs):
        self.user_id = None
        self.account = None
        self.user_name = None
        self.status = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('user_id', 'validate_user_id', {'required': False}))
        rules.append(
            ('status', 'in_range', {'range': [e.value for e in DashboardGuideRecordStatus.__members__.values()]})
        )
        return rules

    @staticmethod
    def validate_user_id(config, attr_name, value):  # pylint: disable=unused-argument
        """
        校验user_id参数
        :param config:
        :param attr_name:
        :param value:
        :return:
        """
        if not value:
            raise UserError(message='无登录用户信息')
        cur_user_id = user_service.get_cur_user_id()
        if cur_user_id != value:
            raise UserError(message='当前用户登录信息错误')


class DashboardGuideRecordQueryModel(QueryBaseModel):
    """
    指引查询类
    """

    __slots__ = ['user_id']

    def __init__(self, **kwargs) -> None:
        self.user_id = None
        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[str, str, Dict[str, bool]]]:
        rules = super().rules()
        rules.append(('user_id', 'validate_user_id', {'required': False}))
        return rules

    @staticmethod
    def validate_user_id(
        config: Dict[str, bool], attr_name: str, value: str  # pylint: disable=unused-argument
    ) -> None:  # pylint: disable=unused-argument
        """
        校验user_id参数
        :param config:
        :param attr_name:
        :param value:
        :return:
        """
        if not value:
            raise UserError(message='无登录用户信息')
        cur_user_id = user_service.get_cur_user_id()
        if cur_user_id != value:
            raise UserError(message='当前用户登录信息错误')
