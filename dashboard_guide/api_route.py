#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/08/10

"""
route
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from dmplib.hug import APIWrapper
from dashboard_guide.services import dashboard_guide_service
from dashboard_guide.models import DashboardGuideRecordModel, DashboardGuideRecordQueryModel


api = APIWrapper(__name__)


@api.admin_route.get("/get")
def get_record(**kwargs):
    """
    获取状态
    :param kwargs:
    :return:
    """
    query_model = DashboardGuideRecordQueryModel(**kwargs)
    return True, '', dashboard_guide_service.get_guide_status(query_model)


@api.admin_route.post("/add")
def add_record(**kwargs):
    """
    新增记录
    :param kwargs:
    :return:
    """
    model = DashboardGuideRecordModel(**kwargs)
    return True, '', dashboard_guide_service.add_guide_record(model)


@api.admin_route.post("/update")
def update_record(**kwargs):
    """
    更改记录
    :param kwargs:
    :return:
    """
    return True, '', ''
