# -*- coding: utf-8 -*-
import re
import os
import sys

os.environ['prometheus_multiproc_dir'] = '/tmp'
site_packages_path = os.path.join(os.path.dirname(os.path.abspath(os.__file__)), 'site-packages')
sys.path.insert(0, site_packages_path)

from gunicorn.app.wsgiapp import run  # noqa

if __name__ == '__main__':
    command =  'app:__hug_wsgi__ --timeout=120 --bind=0.0.00:8000 -w 1 --max-requests 500 --log-level INFO -k gevent --pythonpath ./'
    sys.argv[0] = re.sub(r'(-script\.pyw?|\.exe)?$', '', sys.argv[0])
    sys.argv = [sys.argv[0]] + [s.strip() for s in command.split(' ')]
    print(f'---> command: gunicorn {command}')
    sys.exit(run())
