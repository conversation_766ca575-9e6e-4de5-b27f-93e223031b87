#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/4/13.
"""
from dmplib import config
from dmplib.saas.project import get_db
from dmplib.redis import conn as conn_redis
from message.models import MessageQueryModel

from dmplib.hug import g
from base import repository

from datetime import datetime
from typing import Dict, List, Union, Optional, Any


def message_key_redis() -> str:
    from user.services import user_service

    return '{}:READ_LIST'.format(user_service.get_cur_user_id())


def get_message_list(query_model: MessageQueryModel) -> MessageQueryModel:
    """
    获取消息的列表
    :param query_model:
    :return:
    """
    sql = """select id, source, type, title, url, level, modified_on from message """
    params = {}
    wheres = []
    # 类型过滤
    if query_model.type:
        wheres.append('`type` = %(type)s')
        params['type'] = query_model.type
        # 个人消息还需过滤user_id
        if query_model.type == '个人消息':
            wheres.append('`user_id` = %(user_id)s')
            params['user_id'] = g.userid
        else:
            wheres.append('user_id is null')
    # 时间过滤
    if query_model.modified_on:
        wheres.append('`modified_on` > %(modified_on)s')
        params['modified_on'] = query_model.modified_on
    # 默认只显示未删除的消息
    wheres.append('`status` = %(status)s')
    params['status'] = '1'
    order_by = ''
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += order_by or ' ORDER BY `modified_on` DESC'
    with get_db() as db:
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_message_id_list(query_model: MessageQueryModel) -> MessageQueryModel:
    """
    获取消息的列表
    :param query_model:
    :return:
    """
    sql = """select id, type from message """
    params = {}
    wheres = []
    # 类型过滤
    if query_model.type:
        wheres.append('`type` = %(type)s')
        params['type'] = query_model.type
    # 时间过滤
    if query_model.modified_on:
        wheres.append('`modified_on` > %(modified_on)s')
        params['modified_on'] = query_model.modified_on
    # 默认只显示未删除的消息
    wheres.append('`status` = %(status)s')
    wheres.append('user_id is null')
    params['status'] = '1'
    order_by = ''
    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += order_by or ' ORDER BY `modified_on` DESC'
    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
    with get_db() as db:
        query_model.items = db.query(sql, params)
        query_model.total = 0
    return query_model


def get_all_message_ids(message_type=None):
    """
    获取所有消息的ID（目前用于标记所有已读）
    :param message_type:
    :return:
    """
    sql = """select id from message WHERE status=1 """
    if message_type:
        sql += ' and `type` = %(message_type)s'
    sql += "ORDER BY modified_on DESC limit 1000"
    with get_db() as db:
        items = db.query(sql, {'message_type': message_type})
    return [item.get('id') for item in items] if items else []


def message_read(message_id: str, message_type: Optional[str] = None) -> bool:
    """
    标记已读（单个标记&id=message_id, 全部标记&id=all, message_type=系统消息 or 个人消息)
    :param dict kwargs:
    :return tuple:
    """
    cache_key = message_key_redis()
    if message_id in ['all', 'ALL']:
        message_ids = get_all_message_ids(message_type)
        if not message_ids:
            return True
        result = conn_redis()._connection.lpush(cache_key, *message_ids)
    else:
        result = conn_redis()._connection.lpush(cache_key, message_id)
    conn_redis()._connection.ltrim(cache_key, 0, -1)
    return bool(result)


def get_read_message_ids() -> List[Any]:
    cache_key = message_key_redis()
    _messages = conn_redis()._connection.lrange(cache_key, 0, -1)
    # 编码转换
    read_messages = [message_id.decode('utf-8') for message_id in _messages]
    return read_messages


def deal_message(
    results: Dict[str, Union[List[Dict[str, Union[str, datetime]]], int]]
) -> Dict[str, Union[List[Dict[str, Union[str, int, datetime]]], int]]:
    """
    # 标记已读消息和处理OSS url
    :param messages:
    [
    {id:id, source:source, title:title, ...},
    ...
    ]
    :return:
    """
    read_messages = get_read_message_ids()
    current_domain = config.get('Domain.dmp')
    for message in results.get('items'):
        if read_messages and message.get('id') in read_messages:
            # 1 表示已读
            message['read'] = 1
        else:
            # 0 表示未读
            message['read'] = 0
        if message.get("url") and message.get("url").find("oss-cn") != -1:
            message['url'] = "{domain}/api/download/oss?url={url}".format(domain=current_domain, url=message.get("url"))
    return results


def get_unread_status() -> Dict[str, int]:
    """
    获取是否有消息未读(区分系统消息和个人消息)
    :return:
    """
    # 200条消息之前如果还有未读消息不显示了
    kwargs = {'page_size': 200}
    results = get_message_id_list(MessageQueryModel(**kwargs)).get_result_dict()
    read_message_ids = get_read_message_ids()
    unread_system_message = 0
    unread_personal_message = 0
    # while system_message and operate_message:
    for message in results.get('items'):
        if message.get("id") not in read_message_ids and message.get('type') == '系统消息':
            # 目前前端没有显示未读总数，这里也不再计算总数，有一条未读就直接返回
            unread_system_message = 1
            break
    # 处理个人消息未读消息标记
    message_ids = get_user_message_list()
    for message in message_ids:
        if message.get("id") not in read_message_ids and message.get('type') == '个人消息':
            # 目前前端没有显示未读总数，这里也不再计算总数，有一条未读就直接返回
            unread_personal_message = 1
            break

    return {'unread_system_message': unread_system_message, 'unread_personal_message': unread_personal_message}


def get_user_message_list() -> List[Dict[str, str]]:
    """
    获取个人信息列表
    :return:
    """
    sql = """select id, `type` from message WHERE user_id=%(userid)s
        and status=1 and `type`='个人消息' ORDER BY modified_on DESC limit 200"""
    with get_db() as db:
        message_ids = db.query(sql, {'userid': g.userid})
    return message_ids


def add_message(message_model):
    """
    添加消息
    :return:
    """
    with get_db() as db:
        results = db.insert('message', message_model)
        return results
