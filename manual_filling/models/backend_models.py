from dmplib.hug import g
from base.models import BaseModel
from base import repository
from manual_filling.common.enums import TemplateType


class FillingBaseModel(BaseModel):

    def __init__(self, **kwargs):
        self.created_name = getattr(g, "name", "")
        self.modified_name = getattr(g, "name", "")
        super().__init__(**kwargs)


class FillingTemplateParamsModel(BaseModel):

    __slots__ = [
        "id",
        "display_name",
        "table_name",
        "remark",
        "sort",
        "status",
        "interval_type",
        "interval_date",
        "save_type",
        "unique_columns",
        "is_addinput_batch",
        "is_needaudit",
        "version",
        "type",
        "parent_id",
        "dataset_id",
        "columns",
        "filling_user",
        "filling_reviewer",
        "filling_dataset_filter",
        "filling_dataset_permission_filter",
        "is_extend_last_data"
    ]

    def __init__(self, **kwargs):
        # 光辉要求将display_name =>text
        kwargs["display_name"] = kwargs.get("text")
        self.id = ""
        self.display_name = ""
        self.table_name = ""
        self.remark = ""
        self.sort = 0
        self.status = 0
        self.interval_type = ""
        self.interval_date = ""
        self.save_type = ""
        self.unique_columns = ""
        self.is_addinput_batch = 0
        self.is_needaudit = 1
        self.version = 0
        self.type = TemplateType.FILE.value
        self.parent_id = None
        self.dataset_id = ""
        self.columns = []
        self.filling_user = []
        self.filling_reviewer = []
        self.filling_dataset_filter = []
        self.filling_dataset_permission_filter = []
        self.is_extend_last_data = 0
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append((["display_name", "table_name"], "string", {"required": True}))
        rules.append(
            ("interval_type", "in_range", {"required": True, "range": ["year", "quarter", "month", "day", "week", "once"]})
        )
        rules.append(("interval_date", "string", {"required": True}))
        rules.append((["save_type", "is_addinput_batch", "is_needaudit"], "in_range", {"range": [0, 1], "required": True}))
        rules.append(("type", "in_range", {"range": [TemplateType.FILE.value, TemplateType.FOLDER.value], "required": True}))
        rules.append(("is_extend_last_data", "in_range", {"range": [0, 1], "required": True}))
        return rules


class FillingTemplateModel(FillingBaseModel):

    __slots__ = [
        "id",
        "display_name",
        "table_name",
        "remark",
        # "sort",
        "status",
        "interval_type",
        "interval_date",
        "save_type",
        "is_needaudit",
        "unique_columns",
        "is_addinput_batch",
        "version",
        "type",
        "parent_id",
        "dataset_id",
        "is_extend_last_data",
        "data_source_type",
        "data_source_id",
    ]

    _TABLE_NAME = "filling_template"

    def __init__(self, **kwargs):
        self.id = ""
        self.display_name = ""
        self.table_name = ""
        self.remark = ""
        # self.sort = 0
        self.status = 0
        self.interval_type = ""
        self.interval_date = ""
        self.save_type = 0
        self.unique_columns = ""
        self.is_needaudit = 1
        self.is_addinput_batch = 0
        self.version = 0
        self.type = TemplateType.FILE.value
        self.parent_id = None
        self.dataset_id = ""
        self.is_extend_last_data = 0
        self.data_source_type = 0
        self.data_source_id = ''
        super().__init__(**kwargs)
        self.version = self.version or 0

    def rules(self) -> list:
        rules = super().rules()
        rules.append((["display_name"], "string", {"required": True}))
        return rules


class FillingColumns(FillingBaseModel):
    __slots__ = [
        "id",
        "template_id",
        "column_name",
        "design_data",
        "sort",
        "data_type",
        "remark",
        "display_name",
        "is_dataset_field",
        "dataset_field_id",
        "dataset_id",
        "is_display",
        "version"
    ]

    _TABLE_NAME = "filling_template_column"

    def __init__(self, **kwargs):
        self.id = ""
        self.template_id = ""
        self.column_name = ""
        self.design_data = ""
        self.sort = 0
        self.data_type = ""
        self.remark = ""
        self.display_name = ""
        self.is_dataset_field = 0
        self.dataset_field_id = ""
        self.dataset_id = ""
        self.is_display = 1
        self.version = 0
        super().__init__(**kwargs)
        self.version = self.version or 0

    def rules(self) -> list:
        rules = super().rules()
        rules.append(
            (["template_id", "column_name", "display_name", "data_type"], "string", {"required": True})
        )
        rules.append((["is_display", "is_dataset_field"], "in_range", {"range": [0, 1], "required": True}))
        return rules


class FillingDatasetFilter(FillingBaseModel):
    __slots__ = [
        "id",
        "template_id",
        "dataset_id",
        "dataset_field_id",
        "operator",
        "col_value"
    ]

    _TABLE_NAME = "filling_dataset_filter"

    def __init__(self, **kwargs):
        self.id = ""
        self.template_id = ""
        self.dataset_id = ""
        self.dataset_field_id = ""
        self.operator = ""
        self.col_value = ""
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(
            (["template_id", "dataset_id", "dataset_field_id", "operator"], "string", {"required": True})
        )
        return rules


class FillingDatasetPermissionFilter(FillingBaseModel):
    __slots__ = [
        "id",
        "template_id",
        "dataset_id",
        "dataset_field_id",
        "dataset_permission_id"
    ]

    _TABLE_NAME = "filling_dataset_permission_filter"

    def __init__(self, **kwargs):
        self.id = ""
        self.template_id = ""
        self.dataset_id = ""
        self.dataset_field_id = ""
        self.dataset_permission_id = ""
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(
            (["template_id", "dataset_id", "dataset_field_id", "dataset_permission_id"], "string", {"required": True})
        )
        return rules


class FillingReviewer(FillingBaseModel):
    __slots__ = [
        "id",
        "template_id",
        "user_id",
        "roles",
        "user_name"
    ]

    _TABLE_NAME = "filling_reviewer"

    def __init__(self, **kwargs):
        self.id = ""
        self.template_id = ""
        self.user_id = ""
        self.roles = ""
        self.user_name = ""
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(
            (["template_id", "user_id"], "string", {"required": True})
        )
        return rules


class FillingUser(FillingBaseModel):
    __slots__ = [
        "id",
        "template_id",
        "role_id"
    ]

    _TABLE_NAME = "filling_user"

    def __init__(self, **kwargs):
        self.id = ""
        self.template_id = ""
        self.role_id = ""
        super().__init__(**kwargs)

    def rules(self) -> list:
        rules = super().rules()
        rules.append(
            (["template_id", "role_id"], "string", {"required": True})
        )
        return rules




