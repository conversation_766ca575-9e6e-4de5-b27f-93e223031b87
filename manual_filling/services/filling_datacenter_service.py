# -*- coding: utf-8 -*-
import random
import string
from decimal import Decimal
from datetime import date, datetime
from loguru import logger
from base import repository
from base.enums import MysoftNewERPDataBaseType, DataCenterAction
from dmplib.utils.errors import UserError
from dmplib.saas.project import get_data_db


def fill_data_to_datacenter(table_name):
    template_info = repository.get_one('filling_template', {'table_name': table_name})
    if not template_info:
        logger.error('没有找到对应的填报模板')
        return False
    data_source_type = template_info.get('data_source_type', 0)
    if data_source_type == 0:
        logger.error('填报绑定数见数据源不需要落地')
        return False
    data_source_id = template_info.get('data_source_id')
    if not data_source_id:
        logger.error('没有配置落地数据源')
        return False
    db_type = repository.get_data_scalar('data_source', {'id': data_source_id}, 'db_type')
    if not db_type:
        logger.error('落地数据源没有配置数据库类型')
        return False
    template_column = repository.get_list('filling_template_column', {'template_id': template_info.get('id')},
                                          ['column_name', 'data_type'])
    if not template_column:
        logger.error('没有获取到填报的字段信息')
        return False
    add_default_column(template_column)
    unique_field = template_info.get('unique_columns', '')
    unique_field = unique_field.split(",")
    # 生成对应mssql的字段类型
    prepare_column_type(template_column)
    # 生成临时表名
    random_string = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(8))
    temp_table_name = '{}_{}_temp'.format(table_name, random_string)
    try:
        # 执行创建临时表
        create_table_sql(db_type, temp_table_name, template_column, unique_field)
        # 将数据分页插入临时表中
        exec_insert_sql(db_type, table_name, temp_table_name)
        # 将临时表重命名为正式表
        rename_table(table_name, temp_table_name, db_type)
    except Exception as e:
        logger.error('填报数据同步错误：{}'.format(str(e)))
        drop_table(temp_table_name, db_type)


def create_table_sql(db_type, table_name, template_column, unique_field):
    create_sql = ''
    if db_type == MysoftNewERPDataBaseType.Mysql.value:
        create_sql = create_table_by_mysql(table_name, template_column, unique_field)
    elif db_type == MysoftNewERPDataBaseType.SQL_Server.value:
        create_sql = create_table_by_mssql(table_name, template_column)
    if create_sql:
        local_execute_sql(create_sql)


def exec_datacenter_sql(table_sql, insert_sql):
    exec_all_sql = '{} {}'.format(table_sql, insert_sql)
    local_execute_sql(exec_all_sql)


def rename_table(table_name, temp_table_name, db_type):
    if db_type == MysoftNewERPDataBaseType.Mysql.value:
        delete_sql = 'drop table IF EXISTS {table};'.format(table=table_name)
        rename_sql = 'rename table {temp_table} to {table_name};'.format(temp_table=temp_table_name, table_name=table_name)
    else:
        delete_sql = "if object_id(N'{table}', N'U') is not null drop table [{table}];".format(table=table_name)
        rename_sql = "exec sp_rename '{temp_table}', '{table_name}';".format(temp_table=temp_table_name, table_name=table_name)
    sql = delete_sql + rename_sql
    local_execute_sql(sql)


def drop_table(table_name, db_type):
    if db_type == MysoftNewERPDataBaseType.Mysql.value:
        delete_sql = 'drop table IF EXISTS {table};'.format(table=table_name)
    else:
        delete_sql = "if object_id(N'{table}', N'U') is not null drop table [{table}];".format(table=table_name)
    local_execute_sql(delete_sql)


def create_table_by_mysql(table_name, template_column, unique_field):
    unique_field = ",".join([f"`{i}`" for i in unique_field])
    unique_key = f"UNIQUE KEY `unique_verify` (`batch_id`,{unique_field})"
    field_sql = ",".join(
        [f"`{column.get('column_name')}`" + " " + column.get('data_type') + " null" for column in template_column])
    create_sql = """
    drop table IF EXISTS {table};
    create table {table}(
    {field_sql},
    {unique_key}
    ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
    """.format(table=table_name, field_sql=field_sql, unique_key=unique_key)
    return create_sql


def create_table_by_mssql(table_name, template_column):
    field_sql = ",".join(
        [f"[{column.get('column_name')}]" + " " + column.get('mssql_data_type') for column in template_column])
    create_sql = """
    if object_id(N'{table}', N'U') is not null drop table [{table}];
    create table [{table}](
    {field_sql}
    );
    """.format(table=table_name, field_sql=field_sql)
    return create_sql


def add_default_column(template_columns: list):
    template_columns.append({'column_name': 'batch_id', 'data_type': 'char(36)'})
    template_columns.append({'column_name': 'batch_name', 'data_type': 'varchar(64)'})
    template_columns.append({'column_name': 'batch_update_time', 'data_type': 'datetime'})
    template_columns.append({'column_name': 'cur_fill_user_id', 'data_type': 'char(36)'})
    template_columns.append({'column_name': 'cur_fill_user_name', 'data_type': 'varchar(128)'})
    template_columns.append({'column_name': 'status', 'data_type': 'char(36)'})
    template_columns.append({'column_name': 'remark', 'data_type': 'varchar(256)'})
    template_columns.append({'column_name': 'reviewer', 'data_type': 'varchar(256)'})
    template_columns.append({'column_name': 'review_time', 'data_type': 'datetime'})


def prepare_column_type(template_columns):
    for column in template_columns:
        col_type = column.get('data_type') or 'text'
        col_type = col_type.replace('varchar', 'nvarchar')
        key_index = col_type.find('(')
        data_type = col_type if key_index == -1 else col_type[:key_index]
        if data_type.lower().find('int') != -1:
            column['mssql_data_type'] = data_type
        else:
            column['mssql_data_type'] = col_type


def exec_insert_sql(db_type, table_name, temp_table_name):
    with get_data_db() as db:
        # 获取表字段
        sql = """select COLUMN_NAME from information_schema.COLUMNS 
        where table_name = '{}' AND column_key != 'PRI' and TABLE_SCHEMA = database();""".format(table_name)
        fields = db.query_columns(sql)
        if not fields:
            logger.error('查不到表{}对应的字段信息'.format(table_name))
            return ''
        fields_str = "`,`".join(fields)
        select_field = '''`{}`'''.format(fields_str)
        select_sql = 'select {} from {}'.format(select_field, table_name)
        exec_page_insert_sql(temp_table_name, db_type, select_sql)


def exec_page_insert_sql(table_name, db_type, select_sql):
    insert_sql = """insert into {table}({select_field}) values {values};"""
    page = 1
    page_size = 500
    with get_data_db() as db:
        while True:
            start = (page - 1) * page_size
            page_sql = '{} limit {},{}'.format(select_sql, start, page_size)
            data_page = db.query(page_sql)
            if not data_page:
                break
            values = []
            select = ''
            for row in data_page:
                if db_type == MysoftNewERPDataBaseType.SQL_Server.value:
                    select_keys = ['[{}]'.format(select_key) for select_key in row.keys()]
                    select = ','.join(select_keys)
                    format_table = '[{}]'.format(table_name)
                    value_list = [trans_values_mssql(item) if item is not None else 'null' for item in list(row.values())]
                else:
                    select_keys = ['`{}`'.format(select_key) for select_key in row.keys()]
                    select = ','.join(select_keys)
                    format_table = '`{}`'.format(table_name)
                    value_list = ['%r' % trans_values(item) if item is not None else 'null' for item in list(row.values())]
                values.append("({})".format(','.join(value_list)))
            values = ','.join(values)
            sql = insert_sql.format(table=format_table, select_field=select, values=values)
            local_execute_sql(sql)
            page = page + 1


def delete_local_table(table_name):
    template_info = repository.get_one('filling_template', {'table_name': table_name})
    if not template_info:
        return
    data_source_type = template_info.get('data_source_type', 0)
    if data_source_type == 0:
        return
    data_source_id = template_info.get('data_source_id')
    if not data_source_id:
        return
    db_type = repository.get_data_scalar('data_source', {'id': data_source_id}, 'db_type')
    if not db_type:
        return
    if db_type == MysoftNewERPDataBaseType.Mysql.value:
        drop_sql = "drop table IF EXISTS {table}".format(table=table_name)
    else:
        drop_sql = "if object_id(N'{table}', N'U') is not null drop table [{table}];".format(table=table_name)
    local_execute_sql(drop_sql)


def local_execute_sql(sql):
    from components.data_center_api import request_data_center, get_new_erp_datasource_model, get_data_source_info
    data_source = get_new_erp_datasource_model()
    if data_source is None:
        raise UserError(message='数据源对象异常，获取结果失败')
    return request_data_center(
        DataCenterAction.DMPDatasetExecuteSql.value,
        params={
            "DataInfo": {
                "DataSourceModel": get_data_source_info(data_source.conn_str),
                "ExecuteSql": sql,
            }
        })


def trans_values(o):
    if isinstance(o, Decimal):
        return float(o)
    elif isinstance(o, datetime):
        return o.strftime('%Y-%m-%d %H:%M:%S')
    elif isinstance(o, date):
        return o.strftime('%Y-%m-%d')
    else:
        return o


def trans_values_mssql(o):
    if isinstance(o, Decimal):
        return '%r' % float(o)
    elif isinstance(o, datetime):
        return '%r' % o.strftime('%Y-%m-%d %H:%M:%S')
    elif isinstance(o, date):
        return '%r' % o.strftime('%Y-%m-%d')
    elif isinstance(o, str):
        return "'{}'".format(o.replace("'", "''"))
    return '%r' % o
