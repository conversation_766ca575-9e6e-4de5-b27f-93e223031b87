# -*- coding: utf-8 -*-
# @Time : 2021/12/23 16:11
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : manual_filling_service.py
# @Project : dmp
import time
from datetime import datetime
import json
from loguru import logger

from components.data_center_api import get_master_local_db_new_erp_data_source
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from dmplib import config
from dmplib.saas.project import get_db, get_data_db
from base import repository
from dmplib.hug import g

from manual_filling.models.backend_models import (
    FillingTemplateParamsModel,
    FillingTemplateModel,
    FillingColumns,
    FillingReviewer,
    FillingDatasetFilter,
    FillingDatasetPermissionFilter,
    FillingUser
)
from manual_filling.repositories import filling_template_repositories
from manual_filling.common.enums import (
    TemplateType,
    IntervalType,
    IsAddInputBatch,
    TemplateStatusType,
    <PERSON><PERSON><PERSON>set<PERSON>ield,
    SaveType,
    IsExpendLastData,
    ReviewStatus
)
from manual_filling.common.constant import SHARE_TABLE, TMP_TABLE


class ManualFillingService(object):
    __slots__ = ["params", "template_id", "version", "template"]

    def __init__(self, **kwargs):
        self.params = kwargs
        self.template_id = kwargs.get("id")
        self.template = None
        self.version = 0

    def _validate_and_get_template_models(self, is_edit=False):
        """
        get template models
        :return:
        """
        params_model = FillingTemplateParamsModel(**self.params)
        params_model.validate()
        if is_edit is False:
            self.params['id'] = self.template_id
        # 模板名称重复校验
        if filling_template_repositories.check_template_name_exists(params_model.parent_id, params_model.display_name, self.template_id, is_edit):
            raise UserError(message="模板名重复")
        # dataset只支持调度模式
        if filling_template_repositories.check_dataset_connect_type(params_model.dataset_id) is False:
            raise UserError(message="不支持直连数据集")
        # 校验表名是否已存在
        if is_edit is False and filling_template_repositories.check_table_name_exists_of_data(params_model.table_name):
            raise UserError(message=f"表{params_model.table_name}已存在")
        # 不允许编辑表名、主键
        if is_edit:
            old_template = filling_template_repositories.get_filling_template(self.template_id)
            if old_template.get("status") == TemplateStatusType.OnLine.value:
                raise UserError(message="下线后才能编辑模板")
            if (
                    filling_template_repositories.check_had_batch(self.template_id)
            ) and ((
                    params_model.table_name != old_template.get("table_name")
            ) or (
                    params_model.unique_columns != old_template.get("unique_columns")
            )):
                raise UserError(message="编辑模板不允许修改表名和唯一字段")
        if not is_edit:
            self.params["sort"] = self.get_max_sort_by_parent_id(params_model.parent_id) + 1
        return params_model, FillingTemplateModel(**self.params)

    def _validate_and_get_columns_models(self, columns, is_edit=False):  # NOSONAR
        """
        校验columns
        :param columns:
        :return:
        """
        models = []
        models_ids = []
        column_name_list = []
        display_name_list = []
        if is_edit and not self.template:
            self.template = filling_template_repositories.get_filling_template(self.template_id)
        had_batch = filling_template_repositories.check_had_batch(self.template_id)
        for i in columns:
            model = FillingColumns(**i)
            model.id = model.id or seq_id()
            models_ids.append(model.id)
            model.is_dataset_field == IsDatasetField.Not.value and filling_template_repositories.check_data_type(model.data_type)
            # 字段存储名重复校验
            if model.column_name in column_name_list:
                raise UserError(message="字段存储名重复")
            column_name_list.append(model.column_name)
            if model.display_name in display_name_list:
                raise UserError(message="字段显示名重复")
            display_name_list.append(model.display_name)
            if is_edit:
                # 编辑模板不允许删字段、不允许修改存储字段名
                col = filling_template_repositories.get_column_by_id(model.id)
                if not col:
                    # 新增的字段需要加版本
                    model.version = filling_template_repositories.get_version(self.template_id)
                else:
                    if had_batch:
                        if model.data_type != col.get("data_type"):
                            raise UserError(message="有批次不允许修改字段类型")
                        if model.column_name != col.get("column_name"):
                            raise UserError(message="有批次不允许修改存储字段名")

            model.template_id = model.template_id or self.template_id
            model.design_data = json.dumps(model.design_data)
            model.validate()
            models.append(model)
        if is_edit:
            old_ids = filling_template_repositories.get_filling_column_ids(self.template_id)
            for i in old_ids:
                if i not in models_ids and had_batch:
                    raise UserError(message="有批次不允许删字段")
        return models

    def _validate_and_get_dataset_filter_models(self, filling_dataset_filter):
        """
        校验columns
        :param filling_dataset_filter:
        :return:
        """
        models = []
        for i in filling_dataset_filter:
            model = FillingDatasetFilter(**i)
            model.id = model.id or seq_id()
            model.template_id = model.template_id or self.template_id
            model.validate()
            models.append(model)
        return models

    def _validate_and_get_dataset_permission_filter_models(self, filling_dataset_permission_filter):
        """
        校验columns
        :param filling_dataset_permission_filter:
        :return:
        """
        models = []
        for i in filling_dataset_permission_filter:
            model = FillingDatasetPermissionFilter(**i)
            model.id = model.id or seq_id()
            model.template_id = model.template_id or self.template_id
            model.validate()
            models.append(model)
        return models

    def _validate_and_get_filling_user_models(self, filling_users, is_edit=False):
        """
        检验填报人员
        :param filling_users:
        :return:
        """
        if not filling_users:
            raise UserError(message="缺少填报人员参数")
        models = []
        for i in filling_users:
            if is_edit and isinstance(i, dict):
                model = FillingUser(**i)
            else:
                model = FillingUser(
                    id=seq_id(),
                    template_id=self.template_id,
                    role_id=i
                )
            if filling_template_repositories.check_role_exists_by_id(model.role_id) is False:
                raise UserError(message="填报角色不存在")
            models.append(model)
        return models

    def _validate_and_get_filling_reviewer_models(self, is_needaudit, filling_reviewer, is_edit=False):
        """
        检验审核人员
        :param is_needaudit:
        :param filling_reviewer:
        :return:
        """
        if is_needaudit and not filling_reviewer:
            raise UserError(message="缺少审核人员参数")
        models = []
        for i in filling_reviewer:
            model = FillingReviewer(**i)
            model.id = model.id or seq_id()
            model.template_id = model.template_id or self.template_id
            if filling_template_repositories.check_user_exists_by_id(model.user_id) is False:
                raise UserError(message="审核人员不存在")
            models.append(model)
        return models

    def validate_and_get_models(self, is_edit=False):
        """
        参数校验
        :return:
        """
        # 自动绑定数据服务中心数据源
        try:
            self.params['data_source_id'] = get_master_local_db_new_erp_data_source().get('id')
        except:
            logger.info("不存在数据服务中心数据源")

        # 模板参数校验
        template_params_model, template_model = self._validate_and_get_template_models(is_edit)
        # 字段校验
        columns_models = self._validate_and_get_columns_models(template_params_model.columns, is_edit)
        # 数据过滤
        dataset_filter_models = self._validate_and_get_dataset_filter_models(
            template_params_model.filling_dataset_filter)
        # 填报人员
        filling_users = self._validate_and_get_filling_user_models(
            template_params_model.filling_user, is_edit
        )
        # 审核人员
        filling_reviewer = self._validate_and_get_filling_reviewer_models(
            template_params_model.is_needaudit,
            template_params_model.filling_reviewer,
            is_edit
        )
        # # 权限数据集
        # filling_permissions = self._validate_and_get_dataset_permission_filter_models(
        #     template_params_model.filling_dataset_permission_filter
        # )
        if is_edit:
            return {
                "filling_template": template_model,
                "filling_template_column": columns_models,
                "filling_dataset_filter": dataset_filter_models,
                "filling_user": filling_users,
                "filling_reviewer": filling_reviewer,
                # "filling_dataset_permission_filter": filling_permissions
            }
        return [template_model] + columns_models + dataset_filter_models + filling_users + filling_reviewer

    def save_manual_filling(self):
        """
        新增手工填报
        :return:
        """
        if not self.template_id:
            self.template_id = seq_id()
        # 参数校验
        insert_models = self.validate_and_get_models()
        # 保存
        filling_template_repositories.save_manual_filling(insert_models)

        return self.template_id

    def edit_manual_filling(self):
        """
        编辑
        :return:
        """
        if not self.template_id:
            raise UserError(message="缺少参数模板id")

        self.template = filling_template_repositories.get_filling_template(self.template_id)
        if not self.template:
            raise UserError(message="模板不存在")

        # 参数校验
        update_models = self.validate_and_get_models(is_edit=True)
        # 编辑
        filling_template_repositories.edit_manual_filling(update_models, self.template_id)

        return "ok"

    def get_manual_filling(self):
        if not self.template_id:
            raise UserError(message="缺少参数模板id")
        template = filling_template_repositories.get_filling_template(self.template_id)
        template.update({
            "columns": filling_template_repositories.get_filling_template_column(self.template_id),
            "filling_user": filling_template_repositories.get_filling_user(self.template_id),
            "filling_reviewer": filling_template_repositories.get_filling_reviewer(self.template_id),
            "filling_dataset_filter": filling_template_repositories.get_filling_template_dataset_filter(
                self.template_id),
            # "filling_dataset_permission_filter": filling_template_repositories.get_filling_dataset_permission_filter(
            #     self.template_id)
            "data_center_list": repository.get_list('data_source', {'app_level_code': '1000.1401'}, ['id', 'name']),
        })
        return template

    @staticmethod
    def get_template_list():
        fields = ['id', 'parent_id', 'display_name', 'type', 'sort', 'status']
        order_by = 'parent_id ASC, sort ASC'
        template_list = filling_template_repositories.get_filling_template_list(fields, order_by)
        return template_list

    @staticmethod
    def get_max_sort_by_parent_id(parent_id=''):
        max_sort = filling_template_repositories.get_max_sort_by_parent_id(parent_id)
        return max_sort

    @staticmethod
    def is_exists_folder_name_by_parent_id(parent_id='', name='', template_id=''):
        where = {'parent_id': parent_id, 'type': TemplateType.FOLDER.value, 'display_name': name}
        if template_id:
            where['id!='] = template_id
        data = repository.get_one('filling_template', where, ['display_name'])
        return True if data else False

    @staticmethod
    def is_exists_template_name(name='', template_id=''):
        where = {'type': TemplateType.FILE.value, 'display_name': name}
        if template_id:
            where['id!='] = template_id
        data = repository.get_one('filling_template', where, ['display_name'])
        return True if data else False

    @staticmethod
    def add_folder(data: dict):
        model = FillingTemplateModel(**data)
        is_exists = ManualFillingService.is_exists_folder_name_by_parent_id(model.parent_id, model.display_name)
        if is_exists:
            raise UserError(message='当前层级下已存在相同的名称，请更换后重试')
        model.id = seq_id()
        model.type = TemplateType.FOLDER.value
        model.sort = ManualFillingService.get_max_sort_by_parent_id(model.parent_id) + 1
        repository.add_model('filling_template', model)
        return {
            'id': model.id, 'display_name': model.display_name, 'type': model.type,
            'sort': model.sort, 'parent_id': model.parent_id
        }

    @staticmethod
    def rename(template_id, display_name):
        data = repository.get_one('filling_template', {'id': template_id})
        if not data:
            raise UserError(message='当前操作的对象已经被删除，请刷新后在试')
        if data.get('display_name') == display_name:
            return True
        parent_id = data.get('parent_id', '')
        if data.get('type') == TemplateType.FOLDER.value:
            is_exists = ManualFillingService.is_exists_folder_name_by_parent_id(parent_id, display_name, template_id)
            if is_exists:
                raise UserError(message='当前层级下已存在相同的名称，请更换后重试')
        else:
            is_exists = ManualFillingService.is_exists_template_name(display_name, template_id)
            if is_exists:
                raise UserError(message='已存在相同模板的名称，请更换后重试')
        repository.update_data('filling_template', {'display_name': display_name}, {'id': template_id})

    def on_line(self):
        """
        模板发布
        :return:
        """
        # 发布前校验
        filling_template = self._validate_before_on_line()
        self.version = filling_template.get('version')

        # 维护共享表，结果表，暂存表结构（新增字段）
        self._generate_table(filling_template)

        # 修改模板状态，version
        repository.update_data('filling_template', {'status': TemplateStatusType.OnLine.value},
                               {'id': self.template_id})

        # 调用rundeck生成调度
        self.update_scheduler(filling_template)

    def _generate_table(self, filling_template):
        template_columns = filling_template_repositories.get_filling_template_column(self.template_id)
        if not template_columns:
            raise UserError("列为空，请先编辑模板列后再发布")

        result_table = filling_template.get('table_name')
        share_table = SHARE_TABLE.format(result_table)
        tmp_table = TMP_TABLE.format(result_table)
        unique = filling_template.get("unique_columns", "")
        if not unique:
            raise UserError(message="缺少唯一性校验字段")
        unique = ",".join([f"`{i}`" for i in unique.split(",")])

        had_batch = filling_template_repositories.check_had_batch(self.template_id)
        recreate = False
        if self.version != 0 and not had_batch:
            with get_data_db() as db:
                table_sql = """drop table IF EXISTS {};""".format(result_table)
                share_sql = """drop table IF EXISTS {};""".format(share_table)
                tmp_sql = """drop table IF EXISTS {};""".format(tmp_table)
                db.exec_sql(table_sql)
                db.exec_sql(share_sql)
                db.exec_sql(tmp_sql)
            recreate = True

        if self.version == 0 or recreate:
            # 新增模板
            template_columns.insert(1, {'column_name': 'batch_id', 'data_type': 'char(36)'})
            template_columns.insert(2, {'column_name': 'batch_name', 'data_type': 'varchar(64)'})
            template_columns.insert(3, {'column_name': 'batch_update_time', 'data_type': 'datetime'})
            template_columns.insert(4, {'column_name': 'cur_fill_user_id', 'data_type': 'char(36)'})
            template_columns.insert(5, {'column_name': 'cur_fill_user_name', 'data_type': 'varchar(128)'})
            template_columns.insert(6, {'column_name': 'status', 'data_type': 'char(36)'})
            template_columns.insert(7, {'column_name': 'remark', 'data_type': 'varchar(256)'})
            template_columns.insert(8, {'column_name': 'reviewer', 'data_type': 'varchar(256)'})
            template_columns.insert(9, {'column_name': 'review_time', 'data_type': 'datetime'})
            primary_key = f"id_{int(time.time())} INT UNSIGNED NOT NULL PRIMARY KEY AUTO_INCREMENT"
            unique_key = f"UNIQUE KEY `unique_verify` (`batch_id`,{unique})"
            field_sql = ",".join(
                [f"`{column.get('column_name')}`" + " " + column.get('data_type') + " null" for column in template_columns])
            create_share_table_sql = f"""
            create table {share_table}(
            {primary_key},
            {field_sql},
            {unique_key}
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
            """
            create_result_table_sql = f"""
            create table {result_table}(
            {primary_key},
            {field_sql},
            {unique_key}
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
            """
            create_temp_table_sql = f"""
            create table {tmp_table}(
            {primary_key},
            {field_sql},
            {unique_key}
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
            """
            with get_data_db() as db:
                db.exec_sql(create_temp_table_sql, commit=False)
                db.exec_sql(create_share_table_sql, commit=False)
                db.exec_sql(create_result_table_sql, commit=False)
                db.commit()
        else:
            # 修改模板，添加新字段
            add_columns = [column for column in template_columns if column.get('version') == self.version]
            if add_columns:
                with get_data_db() as db:
                    for column in add_columns:
                        add_share_table_column_sql = f"alter table {share_table} add column `{column.get('column_name')}` {column.get('data_type')} null;"
                        add_result_table_column_sql = f"alter table {result_table} add column `{column.get('column_name')}` {column.get('data_type')} null;"
                        add_tmp_table_column_sql = f"alter table {tmp_table} add column `{column.get('column_name')}` {column.get('data_type')} null;"
                        db.exec_sql(add_share_table_column_sql, commit=False)
                        db.exec_sql(add_result_table_column_sql, commit=False)
                        db.exec_sql(add_tmp_table_column_sql, commit=False)
                    db.commit()
        if filling_template.get('data_source_type') == 1 and filling_template.get('data_source_id'):
            from app_celery import sync_filling_data
            result = {'project_code': g.code, 'table_name': result_table}
            sync_filling_data.apply_async(kwargs=result)

    def _validate_before_on_line(self):
        """
        模板发布前校验
        :return:
        """
        filling_template = filling_template_repositories.get_filling_template(self.template_id)
        if not filling_template:
            raise UserError(message="模板不存在")

        if filling_template.get('status') == TemplateStatusType.OnLine.value:
            raise UserError(message="模板状态处于上线状态，请下线编辑后再发布")

        return filling_template

    def off_line(self):
        """
        模板下线
        :return:
        """
        # 修改模板状态，version
        filling_template = self._validate_before_off_line()
        # 修改模板状态，version
        new_version = filling_template.get('version') + 1
        repository.update_data('filling_template', {'status': TemplateStatusType.OffLine.value, 'version': new_version},
                               {'id': self.template_id})
        # 删除调度
        self.delete_schedule(self.template_id)

    def _validate_before_off_line(self):
        """
        模板下线前校验
        :return:
        """
        filling_template = filling_template_repositories.get_filling_template(self.template_id)
        if not filling_template:
            raise UserError(message="模板不存在")

        if filling_template.get('status') == TemplateStatusType.OffLine.value:
            raise UserError(message="模板状态处于下线状态，无法下线")

        return filling_template

    @staticmethod
    def generate_cron_str(interval_type, interval_date):  # NOSONAR
        """
        生成cron str
        :param interval_type:
        :param interval_date:
        :return:
        """
        try:
            hour = int(config.get("External.fill_h", 0))
            minute = int(config.get("External.fill_m", 30))
        except:
            hour = 0
            minute = 30
        m_d = interval_date.replace(";", ",").split('-')
        if len(m_d) != 2:
            raise UserError(message="interval_date 参数错误")

        def year():   # NOSONAR
            return f"0 {minute} {hour} {m_d[-1]} {m_d[0]} ? *"

        def quarter():  # NOSONAR
            m = ','.join(list(map(lambda x: str(x + int(m_d[0]) - 1), [1, 4, 7, 10])))
            return f"0 {minute} {hour} {m_d[-1]} {m} ? *"

        def month():  # NOSONAR
            d = m_d[-1].replace("31", "L")
            return f"0 {minute} {hour} {d} * ? *"

        def week():  # NOSONAR
            date = m_d[-1]
            if int(date) not in [1, 2, 3, 4, 5, 6, 7]:
                raise UserError(message="date参数错误")
            w = int(date) + 1 if int(date) < 7 else 1
            return f"0 {minute} {hour} ? * {w} *"

        def day():  # NOSONAR
            return f"0 {minute} {hour} * * ? *"

        if interval_type not in locals():
            raise UserError(message="未知的interval_type")

        return locals().get(interval_type)()

    @staticmethod
    def generate_input_batch(template_id):
        """
        生成批次
        :param template_id:
        :return:
        """
        template = filling_template_repositories.get_filling_template(template_id)
        if not template:
            raise UserError(message="模板不存在")
        batch_name = f"{template.get('display_name')}-{datetime.now(): %Y-%m-%d}"
        auto_num = filling_template_repositories.get_max_bitch_num(template_id)
        batch_id = filling_template_repositories.create_input_batch(template_id, batch_name, auto_num, template.get("version"))
        # 如果选择了“是”，则在生成新批次时，按照主键关联，带出上一批次的数据
        ManualFillingService.expend_data_of_last_batch(template, batch_id)

    @staticmethod
    def expend_data_of_last_batch(template, cur_batch_id):
        """
        按照主键关联，带出上一批次的数据
        :param template:
        :param cur_batch_id:
        :return:
        """
        if template.get("is_extend_last_data") == IsExpendLastData.Not.value:
            return
        # 获取最新审核通过的批次
        last_batch_id = filling_template_repositories.get_last_batch_of_review(template.get("table_name"))
        if not last_batch_id:
            return

        # 获取最新一次审核通过的数据, 数据写入暂存表
        from manual_filling.services.filling_data_service import external_extend_last_data

        external_extend_last_data(cur_batch_id, template)

    @staticmethod
    def update_filling_flow(flow_id, name, schedule):
        from base.enums import FlowType, FlowStatus, FlowNodeType
        from flow.models import FlowModel, FlowNodeModel
        from flow.services.flow_service import add_flow

        flow = repository.get_data("flow", {"id": flow_id})
        if flow:
            repository.update("flow", {"name": name, "schedule": schedule, "status": FlowStatus.Enable.value}, {"id": flow_id})
        else:
            flow = FlowModel()
            flow.id = flow_id
            flow.name = name
            flow.status = FlowStatus.Enable.value
            flow.type = FlowType.ManualFilling.value
            flow.schedule = schedule
            flow.nodes = [FlowNodeModel(name=name, type=FlowNodeType.ManualFilling.value)]
            add_flow(flow)
        return flow

    @staticmethod
    def get_command(flow_id, queue_name='celery'):
        """
        获取rundeck执行celery的command命令
        :param flow_id:
        :param queue_name:
        :return:
        """
        celery_task_name = "app_celery.generate_filling_batch"
        cmd_template_snap = config.get(
            "Rundeck.cmd_template_celery",
            "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
        )
        command = '%s %s %s %s %s' % (cmd_template_snap, g.code, celery_task_name, flow_id, queue_name)
        return command

    def update_scheduler(self, template):
        """
        更新、新增调度
        :param template: 模板
        :return:
        """
        from flow.services.flow_service import update_flow_schedule, delete_flow_schedule

        if template.get("interval_type") == IntervalType.Once.value:
            self.generate_input_batch(template.get("id"))
            try:
                delete_flow_schedule(template.get("id"))
            except:
                print("删除调度")
            return
        if template.get("is_addinput_batch") == IsAddInputBatch.Yes.value:
            self.generate_input_batch(template.get("id"))

        # 生成cron str
        cron_str = self.generate_cron_str(template.get("interval_type"), template.get("interval_date"))
        # 创建flow
        self.update_filling_flow(template.get("id"), template.get("display_name"), cron_str)
        # 更新rundeck
        update_flow_schedule(template.get("id"), command=self.get_command(template.get("id")))

    @staticmethod
    def delete_schedule(flow_id):
        """
        删除调度任务
        :param flow_id:
        :return:
        """
        from flow.services.flow_service import delete_flow_schedule
        from base.enums import FlowStatus

        # 禁用flow调度
        effect = repository.update("flow", {"status": FlowStatus.Disable.value}, {"id": flow_id})

        if effect:
            # 删除调度任务
            delete_flow_schedule(flow_id)

    @staticmethod
    def delete_data(template_id: str):
        data = repository.get_one('filling_template', {'id': template_id})
        if not data:
            raise UserError(message='当前操作的对象已经被删除，请刷新后在试')
        if data.get('type') == TemplateType.FOLDER.value:
            children = repository.get_one('filling_template', {'parent_id': template_id})
            if children:
                raise UserError(message='目录下有其他目录或模板，请删除后在试')
            repository.delete_data('filling_template', {'id': template_id})
        else:
            if data.get('status') == 1:
                raise UserError(message='请将模板下线后在删除')
            from manual_filling.services.filling_datacenter_service import delete_local_table
            delete_local_table(data.get('table_name'))
            # todo:如果填报生成的表被数据集引用也不能删除
            ManualFillingService.delete_template(template_id, data.get('table_name'))

    @staticmethod
    def delete_template(template_id, table_name):
        with get_db() as db:
            try:
                db.begin_transaction()
                db.delete('filling_template', {'id': template_id})
                db.delete('filling_dataset_filter', {'template_id': template_id})
                db.delete('filling_inputbatch', {'template_id': template_id})
                db.delete('filling_reviewer', {'template_id': template_id})
                db.delete('filling_template_column', {'template_id': template_id})
                db.delete('filling_user', {'template_id': template_id})
                db.commit()
            except Exception:
                db.rollback()
                raise UserError(message='删除失败，请重试')
        with get_data_db() as db:
            try:
                table_share_name = SHARE_TABLE.format(table_name)
                table_tmp_name = TMP_TABLE.format(table_name)
                table_sql = """drop table IF EXISTS {};""".format(table_name)
                share_sql = """drop table IF EXISTS {};""".format(table_share_name)
                tmp_sql = """drop table IF EXISTS {};""".format(table_tmp_name)
                db.exec_sql(table_sql)
                db.exec_sql(share_sql)
                db.exec_sql(tmp_sql)
            except Exception as e:
                logger.error(str(e))
        return True

    @staticmethod
    def get_template_by_dataset_id(dataset_id):
        fields = ['id', 'display_name', 'modified_on']
        data = repository.get_list('filling_template', {'dataset_id': dataset_id}, fields)
        if data:
            for row in data:
                row['type'] = 'manual_filling'
                row['name'] = row.pop('display_name')
        return data

    @staticmethod
    def del_batch(batch_id):
        """
        删除指定批次
        """
        template = filling_template_repositories.get_template_by_batch_id(batch_id)

        if template.get("save_type") != SaveType.Append.value:
            raise UserError(message="非追加模式不可删除")

        # 删除共享表、 结果表数据 、删除批次记录
        filling_template_repositories.del_filling_data_of_batch(
            batch_id, template.get("table_name"), template.get("is_needaudit")
        )

    @staticmethod
    def sync_fill_data(template_id):
        from app_celery import sync_filling_data
        template = filling_template_repositories.get_filling_template(template_id)
        if not template:
            raise UserError(message='模板不存在')
        result = {'project_code': g.code, 'table_name': template.get('table_name')}
        sync_filling_data.apply_async(kwargs=result)

