# -*- coding: utf-8 -*-
# @Time : 2021/12/29 9:45
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : filling_data_service.py
# @Project : dmp
import json
from copy import deepcopy
from datetime import datetime, date
from decimal import Decimal
import gzip
import base64
from urllib.parse import unquote

import app_celery
from base.enums import ThirdPartyAppCode
from base import repository
from dmplib.saas.project import get_data_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from manual_filling.models.filling_models import FillingModel, QueryDataModel, ReviewFillingData
from manual_filling.repositories import filling_template_repositories, filling_data_repositories
from manual_filling.common import constant
from manual_filling.common.enums import TemplateAudit, TemplateStatusType, SaveType
from components.query_models import QueryStructure, Select, Where, Prop, ModelEncoder, Object
from components.query_sql_encoder import encode_query
from dataset.external_query_service import get_dataset_data
from manual_filling.common.enums import ViewAction, ReviewStatus, DataStatus
from dmplib.hug import g
from dmplib.constants import ADMIN_ROLE_ID


def check_required(column, value, design_data, column_map):
    if value is None or value == '':
        raise UserError(message=f"【{column_map.get(column)}】是必填字段")


def check_range(column, value, design_data, column_map):
    if value == '':
        return
    try:
        min_v = design_data.get("min")
        max_v = design_data.get("max")

        value = float(value)

        if min_v not in ["", None] and value < float(min_v):
            raise UserError(message=f"【{column_map.get(column)}】数值范围是{min_v}~{max_v}")
        if max_v not in ["", None] and value > float(max_v):
            raise UserError(message=f"【{column_map.get(column)}】数值范围是{min_v}~{max_v}")
    except UserError as e:
        raise e
    except Exception as e:
        print(e)
        raise UserError(message=f"【{column_map.get(column)}】字段是数值类型")


def _get_column_map(template_id: str):
    """
    获取字段map
    :param template_id:
    :return:
    """
    columns = filling_template_repositories.get_filling_template_column(template_id)
    column_map = {}
    column_check = {}
    for item in columns:
        column_map[item.get("display_name")] = item.get("column_name")
        design_data = item.get("design_data")
        check = []
        if design_data.get("required") is True:
            check.append(check_required)
        if design_data.get("range") == "on":
            check.append(check_range)
        if check:
            column_check[item.get("column_name")] = {"check": check, "design_data": design_data}
    return column_map, column_check


def filling_data_service(**kwargs):  # NOSONAR
    """
    数据填报接口
    """
    # 参数校验
    model = FillingModel(**kwargs)
    model.validate()
    # 获取批次信息
    batch = filling_template_repositories.get_filling_batch(model.batch_id)
    # 获取模板信息
    template = filling_template_repositories.get_filling_template(model.template_id)
    is_needaudit = template.get("is_needaudit")
    if model.action in [
        ViewAction.Submit.value, ViewAction.Staging.value, ViewAction.ReviewReject.value,
        ViewAction.ReviewStaging.value
    ] and batch.get("status") == ReviewStatus.Pass.value:
        raise UserError(message="当前批次已审核通过，不允许再填报")
    # 数据解压缩
    model.data = filling_data_decompress_v2(model.data)
    # 填报数据
    if model.action == ViewAction.Submit.value:
        staging_model = deepcopy(model)
        # 数据入暂存表
        staging_model.action = ViewAction.Staging.value
        filling_data(staging_model, batch, template)
        # 数据入中间表
        filling_data(model, batch, template)
        # 不需要审核的场景，数据入结果表
        if is_needaudit == TemplateAudit.NotNeed.value:
            share_table_2_result_table(batch, model.action)
    elif model.action == ViewAction.Review.value or model.action == ViewAction.Amend.value:
        # 添加数据校验
        _check_data_pass(model.data)
        # 数据到共享表
        filling_data(model, batch, template)
        # 数据入结果表
        share_table_2_result_table(batch, model.action)
    else:
        filling_data(model, batch, template)
    # 处理审核状态
    status = batch.get("status")
    if model.action == ViewAction.Submit.value:
        # status = ReviewStatus.Filling.value
        # status = status
        pass
    elif model.action == ViewAction.Review.value:
        status = ReviewStatus.Pass.value
    elif model.action == ViewAction.ReviewReject.value:
        _check_data_reject(model.data)
        status = ReviewStatus.Back.value
        # 发送打回提醒
        users = filling_template_repositories.get_filling_users_of_batch(model.batch_id, constant.SHARE_TABLE.format(template.get("table_name")))
        textcard = f"【{template.get('display_name')}】填报被打回，请及时修订。"
        msg_id = seq_id()
        app_celery.send_message.apply_async(
            kwargs={
                'code': g.code,
                'to_user': users,
                'channel': ThirdPartyAppCode.QYWX.value,
                'body': textcard,
                'msg_id': msg_id
            }
        )
        # app_celery.send_message(
        #     **{
        #         'code': g.code,
        #         'to_user': users,
        #         'channel': ThirdPartyAppCode.QYWX.value,
        #         'body': textcard,
        #         'msg_id': msg_id
        #     }
        # )
        repository.add_data(
            "message_record",
            {
                "id": msg_id,
                "type": "填报打回",
                "external_id": model.batch_id,
                "to_user": users,
                "app_code": ThirdPartyAppCode.QYWX.value,
                "textcard": textcard,
                "status": '已创建'
            }
        )
    filling_data_repositories.update_bitch_status(batch.get("id"), status)


def add_filling_task(batch_id):
    """
    添加任务
    """
    return filling_data_repositories.add_filling_data_task(batch_id)


def get_filling_task(task_id):
    """
    获取任务
    """
    return filling_data_repositories.get_filling_data_task(task_id)


def _check_data_pass(data):
    """
    校验数据是否通过
    """
    for item in data:
        if item.get("status") == DataStatus.Reject.value:
            raise UserError(message="还存在审核结果为打回的记录，请修改审核结果为通过后再通过归档")


def _check_data_reject(data):
    """
    校验数据打回
    """
    for item in data:
        if item.get("status") == DataStatus.Reject.value:
            return
    raise UserError(message="不存在审核结果为打回的记录")


def filling_data_decompress(origin_data):
    if not origin_data:
        return []
    # base64解密
    b64_data = base64.b64decode(origin_data)
    # gzip解压
    g_data = gzip.decompress(b64_data)
    # url decode
    return json.loads(unquote(g_data.decode("utf-8")))


def filling_data_decompress_v2(origin_data):
    if not origin_data:
        return []
    # base64解密
    b64_data = base64.b64decode(origin_data)
    # gzip解压
    g_data = gzip.decompress(b64_data)
    # url decode
    return json.loads(g_data.decode("utf-8"))


def filling_data(model, batch, template):
    """
    数据填报接口
    :param model:
    :param batch:
    :param template:
    :return:
    """
    # 数据唯一性校验
    filling_data_repositories.check_unique_column(model.data, template.get("unique_columns", ""))
    # 需要审核，则数据入共享表，否则，入结果表
    if model.action == ViewAction.Staging.value:
        table_name = constant.TMP_TABLE.format(template.get("table_name"))
    else:
        # if batch.get("status") == ReviewStatus.Pass.value or template.get("is_needaudit") == TemplateAudit.NotNeed.value:
        #     table_name = template.get("table_name")
        # else:
        #     table_name = constant.SHARE_TABLE.format(template.get("table_name"))
        table_name = constant.SHARE_TABLE.format(template.get("table_name"))
    # 校验模板存储表是否存在
    if not filling_template_repositories.check_table_name_exists_of_data(table_name):
        raise UserError(message="存储表未生成")
    # 数据写入
    column_map, column_check = _get_column_map(model.template_id)
    filling_data_repositories.filling_data_2_table(model.data, table_name, batch, column_map, model.action, column_check)


def _query_data_dataset_data(fields, action, template):
    """
    获取数据集数据
    :param fields:
    :param action:
    :param template:
    :return:
    """
    # from dataset.external_query_service import generate_permission_filter

    # 审核通过的场景不合并数据集数据
    if action == ViewAction.Review.value:
        return []

    business_fields = fields.get('bz')
    system_fields = fields.get('sys') + fields.get('build_in')
    template_id = template.get("id")
    if not business_fields:
        return []
    # 构建query_structure_json
    query = QueryStructure()
    # 构建select字段
    for col in business_fields:
        query.select.append(
            Select(
                alias=col.get("column_name"),
                prop_name=filling_data_repositories.get_dataset_col_name_by_id(col.get("dataset_field_id"))
            )
        )
    for col in system_fields:
        query.select.append(Select(prop_name="", alias=col.get("column_name")))
    # 数据过滤
    dataset_filters = filling_template_repositories.get_filling_template_dataset_filter(template_id)
    if dataset_filters:
        where = Where(logical_relation="")
        count = 0
        for _filter in dataset_filters:
            count += 1
            logical_relation = ""
            if count > 1:
                logical_relation = "AND"
            where.conditions.append(Where(
                left=Prop(
                    prop_name=filling_data_repositories.get_dataset_col_name_by_id(_filter.get("dataset_field_id"))
                ),
                operator=_filter.get("operator"),
                right=Prop(value=_filter.get("col_value")),
                logical_relation=logical_relation
            ))
        query.where.append(where)
    # # 权限数据集过滤, 审核模式不过滤数据集权限
    # if action in [ViewAction.Review.value, ViewAction.Sync.value]:
    #     permission_filters = filling_template_repositories.get_filling_dataset_permission_filter(template_id)
    #     if permission_filters:
    #         generate_permission_filter(g.userid, template.get("dataset_id"), permission_filters, query)

    params = dict(
        user_id=g.userid,
        chart_id=template_id,
        query_structure_json=json.dumps(query, cls=ModelEncoder),
        dataset_id=template.get("dataset_id")
    )
    # 查询
    result = get_dataset_data(**params)

    return result and result.get("data", [])


def _query_filling_data(table_name, template_id, save_type, batch_id, auto_num=0, fields=None, user_id=None):
    """
    查询填报数据
    :param table_name:
    :param template_id:
    :param save_type:
    :param batch_id:
    :param fields:
    :param user_id: 处理暂存取数
    :return:
    """
    query = QueryStructure()
    query.object.append(Object(name=table_name))
    # fields.extend(["batch_update_time", "cur_fill_user_name", "status", "remark"])
    for field in fields:
        query.select.append(Select(prop_name=field))
    # if save_type == SaveType.Append.value:
    #     batch_ids = filling_data_repositories.get_batch_ids(template_id, auto_num)
    #     query.where.append(Where(
    #         left=Prop(prop_name="batch_id"),
    #         right=Prop(value=batch_ids),
    #         operator="in"
    #     ))
    # else:
    if batch_id:
        query.where.append(Where(
            left=Prop(prop_name="batch_id"),
            right=Prop(value=batch_id),
            operator="="
        ))
        # # 暂存取数才传该参数
        # if user_id:
        #     query.where.append(Where(
        #         logical_relation="AND",
        #         left=Prop(prop_name="cur_fill_user_id"),
        #         right=Prop(value=user_id),
        #         operator="="
        #     ))
    sql = encode_query(query)
    return filling_data_repositories.query_data(sql)


def _get_all_fields_names(fields):
    """
    获取字段名列表
    :param fields:
    :return:
    """
    col_list = []
    for _, value in fields.items():
        if value:
            for item in value:
                col_list.append(item.get("column_name"))
    return col_list


def _build_key_map(data, unique_columns):
    """
    构造key-value
    """
    def __get_unique(m):
        return ",".join([str(m.get(i)) for i in unique_columns.split(",")])

    def __format(m):
        for k, v in m.items():
            if isinstance(v, (datetime, date)):
                if k in ['batch_update_time', 'review_time']:
                    m[k] = v.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    m[k] = v.strftime("%Y-%m-%d")
            if isinstance(v, Decimal):
                m[k] = float(v)
        return m

    key_map = {}
    for item in data:
        key_map[__get_unique(item)] = __format(item)
    return key_map


def _merge_data(data_filling, dataset_data, unique_columns, fields, merge_type=False):
    """
    merge
    :param data_filling: 填报数据
    :param dataset_data: 数据集数据
    :param dataset_data: unique_columns
    :param dataset_data: fields
    :param merge_type: True
    :return:
    """
    bz_map = {}
    fill_map = {}
    if dataset_data:
        bz_map = _build_key_map(dataset_data, unique_columns)

    if data_filling:
        fill_map = _build_key_map(data_filling, unique_columns)

    if not dataset_data:
        return list(fill_map.values()) if fill_map else []

    if not data_filling:
        return list(bz_map.values()) if bz_map else []

    bz_list = [item.get("column_name") for item in fields.get("bz")]

    def __del_col(item):
        for col in bz_list:
            del item[col]

    # bz_map = _build_key_map(dataset_data, unique_columns)
    # fill_map = _build_key_map(data_filling, unique_columns)

    res_list = []
    fill_in_data = []
    has_bz = bool(fields.get("bz"))

    for key, value in bz_map.items():
        f_value = fill_map.get(key)
        if f_value:
            c_value = deepcopy(f_value)
            __del_col(c_value)
            f_value.update(c_value)
            res_list.append(f_value)
            if merge_type and not has_bz:
                fill_in_data.append(key)
        else:
            res_list.append(value)

    if fill_in_data:
        for key in (set(fill_map.keys()) - set(fill_in_data)):
            res_list.append(fill_map.get(key))

    return res_list


def _get_columns(fields):
    """
    获取字段信息
    :param fields:
    :return:
    """
    columns = fields.get("bz", []) + fields.get("sys", []) + fields.get("build_in", [])
    columns.sort(key=lambda x: x.get("sort"))
    for item in columns:
        item["design_data"] = json.loads(item.get("design_data", "{}"))
    return columns


def get_filling_data(**kwargs):
    """
    取数
    :param kwargs:
    :return:
    """
    # 参数校验
    model = QueryDataModel(**kwargs)
    model.validate()
    # 获取模板信息
    template = filling_template_repositories.get_filling_template(model.template_id)
    if template.get("status") == TemplateStatusType.OffLine.value:
        raise UserError(message="模板已下线")
    # 获取批次信息
    batch = filling_template_repositories.get_filling_batch(model.batch_id)
    # 获取字段
    fields = filling_template_repositories.get_filling_fields(model.template_id)
    # 有业务字段，则需要先从数据集获取数据，并数据过滤
    dataset_data = _query_data_dataset_data(fields, model.action, template)
    # 获取存储表数据
    if (
            batch.get("status") != ReviewStatus.Pass.value and template.get("is_needaudit") == TemplateAudit.Need.value
    ) or (template.get("save_type") == SaveType.Cover.value):
        table_name = constant.SHARE_TABLE.format(template.get("table_name"))
    else:
        table_name = template.get("table_name")
    fields_names = _get_all_fields_names(fields)
    data_filling = _query_filling_data(
        table_name, template.get("id"), template.get("save_type"), batch.get("id"), batch.get("auto_num"), fields_names
    )
    # 合并数据：data_filling -> dataset_data
    data = _merge_data(data_filling, dataset_data, template.get("unique_columns"), fields)

    # 暂存取数需要和暂存表数据合并
    if model.action == ViewAction.Staging.value:
        staging_data = _query_filling_data(
            constant.TMP_TABLE.format(template.get("table_name")),
            template.get("id"), template.get("save_type"),
            batch.get("id"),
            batch.get("auto_num"),
            fields_names,
            g.userid
        )
        data = _merge_data(staging_data, data, template.get("unique_columns"), fields, merge_type=True)
    # 组装字段信息
    columns = _get_columns(fields)

    # 获取批次最新更新日期
    batch_update_name = filling_data_repositories.get_batch_update_time(table_name, model.batch_id)

    # 获取总数
    total = len(data)
    # 预览场景只返回前1000条数据
    if model.action == ViewAction.Preview.value:
        data = data[:1000]

    return {"data": data, "columns": columns, "total": total, "batch_update_time": batch_update_name}


def external_extend_last_data(cur_batch_id, template):
    """
    外部使用：获取审核通过的数据
    :param cur_batch_id:
    :param template:
    :return:
    """
    # 获取字段
    fields = filling_template_repositories.get_filling_fields(template.get("id"))

    # 有业务字段，则需要先从数据集获取数据，并数据过滤
    dataset_data = _query_data_dataset_data(fields, ViewAction.Review.value, template)
    table_name = template.get("table_name")
    del fields["build_in"]
    fields_names = _get_all_fields_names(fields)
    # 根据主键Id从所有历史批次找到最近一次的填报的记录
    data_filling = _query_filling_data_of_latest(table_name, template.get("unique_columns"), fields_names)

    # 合并数据：data_filling -> dataset_data
    result = _merge_data(data_filling, dataset_data, template.get("unique_columns"), fields)

    if not result:
        return

    # 获取当前批次信息
    cur_batch = filling_template_repositories.get_filling_batch(cur_batch_id)

    # 数据入暂存表
    copy_result = deepcopy(result)
    column_map, _ = _get_column_map(template.get("id"))
    filling_data_repositories.filling_data_2_table(
        result, constant.TMP_TABLE.format(table_name), cur_batch, column_map, fill_type=ViewAction.Staging.value, is_to_mdc=False
    )

    # 数据入结果表
    filling_data_repositories.filling_data_2_table(
        copy_result, constant.SHARE_TABLE.format(table_name), cur_batch, column_map, fill_type=ViewAction.Submit.value, is_to_mdc=False
    )


def _query_filling_data_of_latest(table_name, unique_keys: str, fields_names: list):
    """
    根据主键Id从所有历史批次找到最近一次的填报的记录
    """
    unique_keys = unique_keys.split(',')
    sql = """
    SELECT {fields_names}
    FROM {table_name}
    WHERE ({unique_keys}, batch_update_time) IN (
        SELECT {unique_keys}, MAX(batch_update_time) AS latest_time
        FROM {table_name}
        GROUP BY {unique_keys}
    )
    """.format(
        table_name=table_name,
        fields_names=', '.join([f'`{f}`' for f in fields_names]),
        unique_keys=', '.join([f'`{k}`' for k in unique_keys])
    )
    return filling_data_repositories.query_data(sql)


def review_filling_data(**kwargs):
    """
    填报审核
    :param kwargs:
    :return:
    """
    # 参数校验
    model = ReviewFillingData(**kwargs)
    model.validate()

    # 获取当前批次
    batch = filling_template_repositories.get_filling_batch(model.batch_id)
    if not batch:
        raise UserError(message="批次不存在")
    if batch.get("status") == ReviewStatus.Pass.value:
        raise UserError(message="当前批次已审核")

    if model.status == ReviewStatus.Pass.value:
        # 数据入结果表
        share_table_2_result_table(batch)

    # 修改审核状态
    filling_data_repositories.update_review_status(model.status, model.remark, model.batch_id)


def share_table_2_result_table(batch, action=None):
    """
    数据入结果表
    :param batch:
    :param action:
    :return:
    """
    # 获取存储表名
    template = filling_data_repositories.get_template_by_batch_id(batch.get("id"))
    result_table = template.get("table_name")
    share_table = constant.SHARE_TABLE.format(result_table)
    # 获取字段
    fields = filling_template_repositories.get_filling_fields(template.get("id"))
    fields_names = _get_all_fields_names(fields)
    # 获取分享表数据
    share_data = filling_data_repositories.get_data_of_share_table(
        batch.get("id"), share_table, template.get("save_type"), fields_names
    )
    # 有业务字段，则需要先从数据集获取数据，并数据过滤
    dataset_data = _query_data_dataset_data(fields, ViewAction.Review.value, template)
    # 合并数据：share_data -> dataset_data
    data = _merge_data(share_data, dataset_data, template.get("unique_columns"), fields)
    # 覆盖场景，结果表需要删除历史批次数据
    if template.get("save_type") == SaveType.Cover.value:
        with get_data_db() as db:
            db.exec_sql(f'truncate table {result_table};')
    # 数据入结果表
    column_map, _ = _get_column_map(template.get("id"))
    filling_data_repositories.filling_data_2_table(data, result_table, batch, column_map, fill_type=action)


# 添加模板填报权限点
def get_user_filling_template():
    """
    获取当前用户有权限的填报模板
    :param  :
    :return:
    """
    curr_user_role_ids = filling_data_repositories.get_current_user_role_id(g.userid)
    if not curr_user_role_ids:
        return None

    if ADMIN_ROLE_ID in curr_user_role_ids:
        # 系统管理员，返回所有填报模板
        return filling_template_repositories.get_filling_template_all(TemplateStatusType.OnLine.value)
    else:
        filling_user = filling_template_repositories.get_filling_user_by_role_ids(curr_user_role_ids)
        template_ids = [item.get('template_id') for item in filling_user]
        return filling_template_repositories.get_filling_template_by_ids(template_ids, TemplateStatusType.OnLine.value)


# @添加模板填报权限点
def get_filling_input_batch(template_id, status, page, page_size):
    """
    获取当前模板填报批次信息
    :param  :
    :return:
    """
    # 获取模板信息
    template = filling_template_repositories.get_filling_template(template_id)
    # 获取批次信息
    batch = filling_data_repositories.get_filling_input_batch(template_id, status, page, page_size)
    # 获取填报人和填报时间
    table_name = template.get("table_name")
    if template.get("is_needaudit") == TemplateAudit.Need.value:
        table_name = constant.SHARE_TABLE.format(table_name)
    # 获取审核人信息
    reviewer = filling_data_repositories.get_reviewer(template_id) or []
    is_reviewer = g.userid in reviewer
    for b in batch:
        fill = filling_data_repositories.get_fill_user_and_time(b.get("id"), table_name)
        b["archived_on"] = fill.get("review_time") if fill else ""
        if not is_reviewer:
            user_ids = filling_template_repositories.get_filling_user_of_batch(b.get("id"), table_name)
            b['status'] = ReviewStatus.Back.value if g.userid in user_ids else ReviewStatus.Filling.value
    return batch


def get_template_of_reviewer(user_id=None):
    """
    获取审核填报模板列表
    :param user_id:
    :return:
    """
    return filling_data_repositories.get_template_of_reviewer(user_id)


if __name__ == "__main__":
    # data = "H4sIAAAAAAAAA+2WS26DMBCGTzPLSMYvxkvbuNdANKVqVBJVBO5fj9UmUJIqIe3OElij8fifh7+FQTkoHXAOwYAtwBkIJRgHNmwgKLAVGCQDOVikOGHjSj9wn87FPQTUEDQYD+bp13ObZTwECc4C+olm3FLgWApm4HxdaMkFSsU0YnGSK2ZlxKqjDiUPYGySjYkcqcUVDRmxRdRJTRZMGl4Ic1Kb5y/BqkmNyeM0jYkMedZgjJeXNGLpFbURMGX1SSOWUn6p2u+utOIKJbtSx3IOlBW5uHzixqxJQzMpHtUQaC5M8Dg0w3hc+vt23/TvS/927OvXXdfV47Ht60Ozb5cxz82wfavHj5dmaOth9yOkrChqBcpuJcrub1Gezz+jnFG+H2W2EmXyOE9OMpC+83VpmmDMOxW/kWmRmc5MP8j0fyJ9GLvufqplpjpT/SDVft74zVj7a48OpKu6G2WVUc4on1BW1ScIwODXDg4AAA=="
    # print(filling_data_decompress(data))
    dd = "H4sIAAAAAAAAA4uuVno2dcPThj1KVoY6SsUliSWlxUpWSko6SkWpuYlF2RB2cmlRfFpmTk58aXFqUXxeYm4qULw0syS1uAQom5RYkpwRX1qQkliSGl+SCZY0MjAy0jUw0TWwVDA0szKwsDK2BJtZlplanloEswHEg+lQqtVBuMVoMLnFaMBdEwsAX132eacBAAA="
    d = "H4sIAAAAAAAAA8WSzUrDQBSF32XWGZjJZJJJd4IKQsG/pUi4M3MHg62U/CgighuRgi7ryoXuXfgAxbdJ6GM4WNRAzbbdnXvOXZzvck9uSG7JgIgUmVBK0UgxGkKkKTcI1HFnjfbKSU4CUl1P0C9vHez5oZ19NHdzMuABaZ5mi7fH9vmhfXn3eTN/be6ni8+p3yorqOrSm14XOIbifKlNXWQuH42yusQiu4AxLn0NlTnL6omFCrMq/7ELvMzxCovu9BvfBh2KKE2QSkYTy2MaOtDUuQSlFLFB3YE4Phx2IMJViI0ROAdAMdKOJsYZqlKLVAC3kUitjqX5Y9jdH27vHHUwxArGBhhkyCUwTEUoFVgJfTePeh5n3X1TkXz35QYsZ6z3R+R/fddV9vQLXFRu66oDAAA="
    print(filling_data_decompress_v2(dd))

