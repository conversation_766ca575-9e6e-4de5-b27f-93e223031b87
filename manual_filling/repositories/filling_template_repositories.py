# -*- coding: utf-8 -*-
# @Time : 2021/12/24 9:52
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : manual_filling_repositories.py
# @Project : dmp
import json

from loguru import logger

from base import repository
from dmplib.saas.project import get_db, get_data_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from manual_filling.common.enums import TemplateAudit
from manual_filling.common.constant import SHARE_TABLE
from manual_filling.models.backend_models import FillingTemplateModel, FillingColumns


def check_role_exists_by_id(role_id: str) -> bool:
    """
    校验
    :param role_id:
    :return:
    """
    return bool(repository.data_is_exists("user_role", condition={"id": role_id}))


def check_user_exists_by_id(user_id: str) -> bool:
    """
    校验
    :param user_id:
    :return:
    """
    return bool(repository.data_is_exists("user", condition={"id": user_id}))


def check_column_exists_by_id(column_id: str) -> bool:
    """
    校验
    :param column_id:
    :return:
    """
    return bool(repository.data_is_exists("filling_template_column", condition={"id": column_id}))


def get_column_by_id(column_id: str):
    """
    校验
    :param column_id:
    :return:
    """
    return repository.get_data("filling_template_column", conditions={"id": column_id})


def get_version(template_id: str):
    """
    校验
    :param template_id:
    :return:
    """
    return repository.get_data_scalar("filling_template", conditions={"id": template_id}, col_name="version")


def get_filling_template(template_id: str):
    """
    获取模板信息
    :param template_id:
    :return:
    """
    return repository.get_data("filling_template", conditions={"id": template_id})


def get_max_bitch_num(template_id: str):
    """
    获取批次号
    :param template_id:
    :return:
    """
    sql = """select max(auto_num) as auto_num from filling_inputbatch where template_id = %(template_id)s"""
    params = {'template_id': template_id}
    with get_db() as db:
        sort = db.query_scalar(sql, params)
        return sort if sort else 0


def get_filling_template_list(fields=None, order_by=None):
    """
    获取模板列表
    :return:
    """
    return repository.get_list('filling_template', {}, fields, order_by)


def get_filling_batch(batch_id: str):
    """
    获取批次信息
    :param batch_id:
    :return:
    """
    return repository.get_data("filling_inputbatch", conditions={"id": batch_id})


def check_had_batch(template_id: str):
    """
    校验是否有批次
    """
    one = repository.get_one("filling_inputbatch", conditions={"template_id": template_id})
    return bool(one)


def get_filling_fields(template_id: str):
    """
    获取字段信息
    :param template_id:
    :param is_dataset_field:
    :return:
    """
    return {
        "bz": repository.get_data("filling_template_column",
                                  conditions={"template_id": template_id, "is_dataset_field": 1}, multi_row=True),
        "sys": repository.get_data("filling_template_column",
                                   conditions={"template_id": template_id, "is_dataset_field": 0}, multi_row=True),
        "build_in": [FillingColumns(
            **item
        ).get_dict() for item in [
            {"data_type": "int(11)", "column_name": "status", "display_name": "审核结果", "design_data": "{}", "sort": 101},
            {"data_type": "varchar(128)", "column_name": "remark", "display_name": "审核说明", "design_data": "{}", "sort": 102},
            {"data_type": "varchar(128)", "column_name": "cur_fill_user_name", "display_name": "填报人", "design_data": "{}", "sort": 103},
            {"data_type": "varchar(128)", "column_name": "batch_update_time", "display_name": "填报日期", "design_data": "{}", "sort": 104},
            {"data_type": "varchar(128)", "column_name": "reviewer", "display_name": "审核人", "design_data": "{}", "sort": 105},
            {"data_type": "varchar(128)", "column_name": "review_time", "display_name": "审核日期", "design_data": "{}", "sort": 106}
        ]]
    }


def get_filling_template_column(template_id: str):
    """
    获取模板信息
    :param template_id:
    :return:
    """
    columns = repository.get_list("filling_template_column", conditions={"template_id": template_id})
    for item in columns:
        item["design_data"] = json.loads(item.get("design_data", "{}"))
    return columns


def get_filling_template_dataset_filter(template_id: str):
    """
    获取模板信息
    :param template_id:
    :return:
    """
    return repository.get_list("filling_dataset_filter", conditions={"template_id": template_id})


def get_filling_reviewer(template_id: str):
    """
    获取模板信息
    :param template_id:
    :return:
    """
    return repository.get_list("filling_reviewer", conditions={"template_id": template_id})


def get_filling_user(template_id: str):
    """
    获取模板信息
    :param template_id:
    :return:
    """
    filling_users = repository.get_list("filling_user", conditions={"template_id": template_id})
    for fu in filling_users:
        fu["role_name"] = repository.get_data_scalar("user_role", {"id": fu.get("role_id")}, col_name="name")
    return filling_users


def get_filling_dataset_permission_filter(template_id: str):
    """
    获取模板信息
    :param template_id:
    :return:
    """
    return repository.get_list("filling_dataset_permission_filter", conditions={"template_id": template_id})


def check_column_data_type(column_id: str, data_type: str) -> bool:
    """
    校验字段类型
    :param column_id:
    :param data_type:
    :return:
    """
    return repository.get_data_scalar(
        "filling_template_column", conditions={"id": column_id}, col_name="data_type") == data_type


def check_table_name_exists_of_data(table_name: str):
    """
    校验表名是否存在
    :param table_name:
    :return:
    """
    return repository.check_data_db_table_exist(table_name)


def check_data_type(data_type):
    """
    校验字段类型
    :param data_type:
    :return:
    """
    if (
            data_type
            and data_type.lower().split("(")[0] not in [
        # 数值
        "int",  "decimal", "tinyint", "smallint", "mediumint", "bigint", "float",
        # 字符串
        "varchar", "text", "char", "tinyblob", "tinytext", "blob", "mediumblob", "mediumtext", "longblob", "longtext",
        # 日期
        "double", "date", "datetime", "time", "year", "timestamp"
    ]
    ):
        raise UserError(message="不支持的字段类型")


def get_filling_column_ids(template_id: str):
    """
    获取column_ids
    :param template_id:
    :return:
    """
    return repository.get_columns("filling_template_column", conditions={"template_id": template_id}, col_name="id")


def get_filling_template_column_version(column_id: str):
    """
    获取模板信息
    :param column_id:
    :return:
    """
    return repository.get_data_scalar("filling_template_column", conditions={"id": column_id}, col_name="version")


def check_dataset_connect_type(dataset_id: str):
    """
    校验数据集类型
    :param dataset_id:
    :return:
    """
    return repository.get_data_scalar("dataset", {"id": dataset_id}, col_name="connect_type") != "直连"


def check_template_name_exists(parent_id, name, template_id="", is_edit=False):
    """
    校验模板重名
    :param parent_id:
    :param template_id:
    :param name:
    :param is_edit:
    :return:
    """
    sql = "select display_name from filling_template where display_name=%(name)s and parent_id=%(parent_id)s"
    params = {"name": name, "parent_id": parent_id}
    if is_edit:
        sql += " and id!=%(template_id)s"
        params.update({"template_id": template_id})
    with get_db() as db:
        return bool(db.query_scalar(sql, params))


def _insert(_db, _model):
    data = _model.get_dict()
    _db.insert(_model._TABLE_NAME, data, commit=False)


def save_manual_filling(models: list):
    """
    保存填报信息
    :param models:
    :return:
    """
    with get_db() as db:
        try:
            db.begin_transaction()
            for model in models:
                _insert(db, model)
            db.commit()
        except Exception as e:
            logger.error("保存失败: {}".format(e))
            db.rollback()
            raise UserError(message="保存失败：{}".format(e))


def edit_manual_filling(models: dict, template_id: str):
    """
    编辑模板
    :param models:
    :param template_id:
    :return:
    """
    with get_db() as db:
        try:
            db.begin_transaction()
            for key, model in models.items():
                if isinstance(model, FillingTemplateModel):
                    db.update(key, model.get_dict(), {'id': model.id}, commit=False)
                else:
                    db.delete(table=key, condition={"template_id": template_id}, commit=False)
                    for m in model:
                        _insert(db, m)
            db.commit()
        except Exception as e:
            logger.error("编辑失败: {}".format(e))
            db.rollback()
            raise UserError(message="编辑失败：{}".format(e))


def get_filling_template_by_ids(template_ids, template_status):
    if not template_ids:
        return None
    params = {'status': template_status, 'id': template_ids}
    return repository.get_list('filling_template', params,
                               ['id', 'parent_id', 'display_name', 'table_name', 'sort', 'type'], order_by='sort asc')


def get_filling_template_all(template_status):
    params = {'status': template_status}
    return repository.get_list('filling_template', params,
                               ['id', 'parent_id', 'display_name', 'table_name', 'sort', 'type'], order_by='sort asc')


def get_filling_user_by_role_ids(role_ids):
    return repository.get_list('filling_user', {'role_id': role_ids})


def get_max_sort_by_parent_id(parent_id=''):
    sql = """select max(sort) as sort from filling_template where parent_id = %(parent_id)s"""
    params = {'parent_id': parent_id}
    with get_db() as db:
        sort = db.query_scalar(sql, params)
        return sort if sort else 0


def create_input_batch(template_id, batch_name, auto_num, version):
    """创建批次"""
    with get_db() as db:
        batch_id = seq_id()
        db.insert("filling_inputbatch", {
            "id": batch_id,
            "template_id": template_id,
            "batch_name": batch_name,
            "auto_num": auto_num,
            "version": version
        })
        return batch_id


def get_template_by_field_ids(field_ids):
    sql = """select a.display_name,a.id,b.dataset_field_id from filling_template a left join filling_template_column b on a.id = b.template_id
     where b.dataset_field_id in %(field_ids)s group by a.id, a.display_name, b.dataset_field_id"""
    params = {'field_ids': field_ids}
    with get_db() as db:
        return db.query(sql, params)


def get_template_by_batch_id(batch_id):
    """
    获取模板
    """
    sql = """
    select t.* from filling_template as t inner join filling_inputbatch as b on b.template_id=t.id 
    where b.id = %(batch_id)s
    """
    params = {'batch_id': batch_id}
    with get_db() as db:
        return db.query_one(sql, params)


def del_filling_data_of_batch(batch_id, table_name, is_needaudit):
    """
    删除batch数据
    """
    with get_data_db() as db:
        if is_needaudit == TemplateAudit.Need.value:
            db.delete(SHARE_TABLE.format(table_name), {"batch_id": batch_id})
        db.delete(table_name, {"batch_id": batch_id})

    repository.delete_data("filling_inputbatch", {"id": batch_id})


def get_last_batch_of_review(table_name):
    """
    获取最新的审核通过的批次
    :param table_name:
    :return:
    """
    with get_data_db() as db:
        sql = f"""
        select batch_id from {table_name} order by batch_update_time desc
        """
        return db.query_scalar(sql)


def get_filling_user_of_batch(batch_id, table_name):
    """
    获取当前批次被打回的数据
    :param batch_id:
    :param table_name:
    :return:
    """
    with get_data_db() as db:
        sql = f"""
        select distinct cur_fill_user_id from {table_name} where 
        batch_id=%(batch_id)s and status="打回" group by cur_fill_user_id
        """
        ids = db.query_columns(sql, params={"batch_id": batch_id}) or []
        ids = [i for i in ids if i]
        return ids


def get_filling_users_of_batch(batch_id, table_name):
    """
    获取当前批次被打回的数据
    :param batch_id:
    :param table_name:
    :return:
    """
    with get_data_db() as db:
        sql = f"""
        select distinct cur_fill_user_id from {table_name} where 
        batch_id=%(batch_id)s and status="打回" group by cur_fill_user_id
        """
        ids = db.query_columns(sql, params={"batch_id": batch_id}) or []
        ids = [i for i in ids if i]
        if ids:
            ids = repository.get_data("user", conditions={"id": ids}, fields=['id', 'account'], multi_row=True)
        return ids

