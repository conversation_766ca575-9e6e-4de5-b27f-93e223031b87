# -*- coding: utf-8 -*-
# @Time : 2021/12/29 11:06
# <AUTHOR> songh02
# @Email : <EMAIL>
# @File : filling_data_repositories.py
# @Project : dmp
from datetime import datetime
import logging
from copy import deepcopy


from base import repository
from dmplib.saas.project import get_data_db, get_db
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from manual_filling.common.enums import ViewAction, FillDataTaskStatus
from manual_filling.services.filling_datacenter_service import fill_data_to_datacenter


logger = logging.getLogger(__name__)


def trans_value(item):
    if item in ["", None]:
        return "null"
    return "%r" % item


def get_user_name(user_id=None):
    """
    获取用户名称
    """
    user_id = user_id or getattr(g, "userid", None)
    name = repository.get_data_scalar("user", {"id": user_id}, col_name="name")
    return name


def check_column_value(item, column_check, column_map):
    """
    字段值校验
    """
    if not column_check:
        return
    for k, v in column_check.items():
        for func in v.get("check", []):
            func(k, item.get(k), v.get("design_data"), column_map)


def filling_data_2_table(data, table_name, batch, column_map: dict = None, fill_type=None, column_check: dict = None, is_to_mdc=True):
    """
    物化数据
    :param data:
    :param table_name:
    :param batch:
    :param column_map:
    :param fill_type:
    :param column_check:
    :return:
    """
    if not data:
        return
    col_name_list = list(data[0].keys())
    col_name = list(column_map.values()) + ["batch_update_time", "cur_fill_user_name", "status", "remark", "reviewer", "review_time"]
    for col in col_name_list:
        if col not in col_name:
            raise UserError(message=f"字段【{col}】不存在")
    col_name_list = ["batch_id", "batch_name"] + col_name_list
    values = []
    reversed_map = {v: k for k, v in column_map.items()}
    now = f"{datetime.now():%Y-%m-%d %H:%M:%S}"
    user_name = get_user_name()
    not_review = fill_type not in [ViewAction.Review.value, ViewAction.ReviewReject.value, ViewAction.ReviewStaging.value]
    if not_review:
        col_name_list.append("cur_fill_user_id")
    update_values = ", ".join([f"`{i}`=values(`{i}`)" for i in col_name_list])

    for item in data:
        if fill_type != ViewAction.Staging.value:
            check_column_value(item, column_check, reversed_map)
        if fill_type == ViewAction.Review.value:
            item["status"] = "通过"
        item["batch_id"] = batch.get("id")
        item["batch_name"] = batch.get("batch_name")
        if not_review:
            item["batch_update_time"] = now
            item["cur_fill_user_id"] = g.userid
            item["cur_fill_user_name"] = user_name
        else:
            item["review_time"] = now
            item["reviewer"] = user_name
        values.append(f"({','.join([trans_value(item.get(i)) for i in col_name_list])})")
        if len(values) >= 5000:
            _update_insert_to_table(table_name, col_name_list, values, update_values)
            values = []
    if values:
        _update_insert_to_table(table_name, col_name_list, values, update_values)
    # 同步填报数据到本地
    is_to_mdc and fill_data_to_datacenter(table_name)


def add_filling_data_task(batch_id):
    task_id = seq_id()
    repository.add_data("filling_data_task", {
        "id": task_id,
        "batch_id": batch_id,
        "status": FillDataTaskStatus.Init.value,
        "message": ""
    })
    return task_id


def update_filling_data_task(task_id, status, message=""):
    repository.update(
        "filling_data_task",
        {"status": status, "message": message},
        {"id": task_id}
    )


def get_filling_data_task(task_id):
    task = repository.get_data("filling_data_task", {"id": task_id})
    if not task:
        raise UserError(message="任务未生成")
    return task


def _update_insert_to_table(table_name, col_name_list, values, update_values):
    """
    插入数据
    """
    if not values:
        return
    with get_data_db() as db:
        insert_sql = "INSERT INTO {table_name} ({col_name_list}) VALUES {values} ON DUPLICATE KEY UPDATE {update_values}".format(
            table_name=table_name,
            col_name_list=', '.join([f"`{i}`" for i in col_name_list]),
            values=",".join(values),
            update_values=update_values
        )
        try:
            db.exec_sql(insert_sql)
        except Exception as e:
            raise UserError(message=f"数据写入错误：{e}")


def get_batch_update_time(table_name, batch_id):
    """
    获取批次填报更新时间
    :param table_name:
    :param batch_id:
    :return:
    """
    with get_data_db() as db:
        sql = "select batch_update_time from " + table_name + " where batch_id=%(batch_id)s"
        return db.query_scalar(sql, params={"batch_id": batch_id, "table_name": table_name})


def get_dataset_col_name_by_id(dataset_field_id):
    """
    获取数据集字段信息
    :param dataset_field_id:
    :return:
    """
    return repository.get_data_scalar("dataset_field", {"id": dataset_field_id}, col_name="col_name")


def get_dataset_table_name(dataset_id):
    """
    获取数据集字段信息
    :param dataset_id:
    :return:
    """
    return repository.get_data_scalar("dataset", {"id": dataset_id}, col_name="table_name")


def get_current_user_role_id(user_id):
    """
    获取当前用户拥有的角色
    :param  :
    :return:
    """
    from user_group.services.user_group_service import get_all_parent_group_by_group_ids

    # 获取用户角色
    user_user_role = repository.get_list("user_user_role", {"user_id": user_id}, ['role_id']) or []
    roles = [role.get('role_id') for role in user_user_role]
    # 获取用户组织关联的角色
    group_ids = repository.get_columns("user_group_user", {'user_id': user_id}, col_name='group_id')
    if group_ids:
        group_ids = get_all_parent_group_by_group_ids(deepcopy(group_ids)) + group_ids
        group_role_ids = repository.get_column("user_group_role", {"group_id": group_ids}, fields=['role_id']) or []
        roles.extend(group_role_ids)
    return roles


def get_filling_input_batch_by_template_id(template_id):
    """
    获取当前模板填报批次信息
    :param  :
    :return:
    """
    return repository.get_list("filling_inputbatch", {"template_id": template_id})


def query_data(sql):
    """
    数据查询
    :param sql:
    :return:
    """
    with get_data_db() as db:
        return db.query(sql)


def get_batch_ids(template_id: str, auto_num: int = 0):
    """
    获取批次
    :param template_id:
    :param auto_num:
    :return:
    """
    with get_db() as db:
        sql = "select id from filling_inputbatch where template_id=%(template_id)s and auto_num <= %(auto_num)s"
        data = db.query(sql, params={"template_id": template_id, "auto_num": auto_num})
        return [role.get('id') for role in data]


def get_template_by_batch_id(batch_id: str):
    """
    获取表名
    :param batch_id:
    :return:
    """
    with get_db() as db:
        sql = "select ft.* from filling_template as ft inner join filling_inputbatch as fi on fi.template_id=ft.id where fi.id=%(batch_id)s"
        return db.query_one(sql=sql, params={"batch_id": batch_id})


def update_review_status(status, remark, batch_id):
    """
    更新审核状态
    :param status:
    :param remark:
    :param batch_id:
    :return:
    """
    return repository.update_data("filling_inputbatch", {"status": status, "remark": remark}, {"id": batch_id})


def get_data_of_share_table(batch_id: str, table_name: str, save_type: str, fields_names: list):
    """
    获取分享表数据
    :param batch_id:
    :param table_name:
    :param save_type:
    :param fields_names:
    :return:
    """
    # 追加模式：获取共享表所有小于等于当前批次序号的数据
    # 覆盖模式：获取共享表当前批次数据
    fields = ",".join([f"`{i}`" for i in fields_names])
    with get_data_db() as db:
        query = f"select {fields} from {table_name}" + " where batch_id = %(batch_id)s"
        params = {"batch_id": batch_id}
        return db.query(query, params)


def get_template_of_reviewer(user_id=None):
    """
    获取审核填报列表
    :param user_id:
    :return:
    """
    with get_db() as db:
        sql = """
        select fi.* from filling_template as fi inner join filling_reviewer as fr on fr.template_id = fi.id
        where fr.user_id=%(user_id)s
        """
        return db.query(sql=sql, params={"user_id": user_id or g.userid})


def get_filling_input_batch(template_id, status, page, page_size):
    """
    获取当前模板填报批次信息
    :param  :
    :return:
    """
    params = {"template_id": template_id}
    if status:
        params['status'] = status if isinstance(status, list) else [int(i) for i in str(status).split(',')]
    page_params = (int(page), int(page_size)) if (page and page_size) else None
    return repository.get_list("filling_inputbatch", params, order_by='created_on desc', **{'page': page_params})


def update_bitch_status(batch_id, status):
    """
    修改审核状态
    :param batch_id:
    :param status:
    :return:
    """
    return repository.update("filling_inputbatch", {"status": status}, {"id": batch_id})


def get_reviewer(template_id):
    """
    修改审核状态
    :param template_id:
    :return:
    """
    return repository.get_columns("filling_reviewer", {"template_id": template_id}, col_name="user_id")


def check_unique_column(data, unique_columns):
    """
    数据唯一性校验
    """
    if not data:
        return
    columns = [i for i in unique_columns.split(",")]
    unique = []
    for item in data:
        u = ",".join(["%r"%item.get(i, "") for i in columns])
        if u in unique:
            raise UserError(message=f"字段值【{u}】不唯一")
        else:
            unique.append(u)


def get_fill_user_and_time(batch_id, table_name):
    """
    获取填报人员及日期
    """
    with get_data_db() as db:
        sql = f"""
        select batch_update_time, cur_fill_user_id, review_time from {table_name} where batch_id=%(batch_id)s
        """
        return db.query_one(sql, params={"batch_id": batch_id})
