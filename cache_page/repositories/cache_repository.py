#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from base import repository
from cache_page.models import GraphParamsModel


def get_available_datasource():
    sql = """select distinct ds.type  from 
    data_source as ds right join cache_hit_data as ch 
    on ds.id = ch.data_source_id  WHERE ch.source = 'released'"""
    return repository.get_data_by_sql(sql, params={}) or []


# def get_display_datasource(env_sign_code, storage_type):
#     sql = """select * from data_source_show_config where env_sign_code=%(env_sign_code)s and storage_type=%(storage_type)s"""
#     params = {
#         'env_sign_code': env_sign_code,
#         'storage_type': storage_type,
#     }
#     return repository.get_data_by_sql(sql, params=params, from_config_db=True)
#

def get_clac_data(model: GraphParamsModel):
    bar_sql = """
SELECT
	sum(not_hit)+sum(has_hit) AS hit_count,
	sum(has_hit)/(sum(not_hit)+ sum(has_hit)) AS hit_rate,
	hit_date 
FROM
	cache_hit_data as ch 
	join data_source as ds on ds.id = ch.data_source_id
	join dataset as dst on dst.id = ch.dataset_id
 {WHRER_SQL}
GROUP BY
	hit_date
	"""

    table_sql = """
SELECT
	dd.id as dashboard_id,
	dd.name as dashboard_name,
	(SELECT count(distinct source) from dashboard_chart as dc WHERE dc.dashboard_id=dd.id and dc.source in (SELECT DISTINCT dataset_id FROM `cache_hit_data` WHERE dashboard_id = ch.dashboard_id)) as related_chart_count,
	(SELECT count(distinct source) from dashboard_chart as dc WHERE dc.dashboard_id=dd.id) as chart_count,
	sum(has_hit)/(sum(not_hit)+ sum(has_hit)) AS hit_rate
FROM
	cache_hit_data as ch 
	join dashboard as dd on ch.dashboard_id = dd.id
	join data_source as ds on ds.id = ch.data_source_id
    join dataset as dst on dst.id = ch.dataset_id
{WHRER_SQL}
GROUP BY
	dashboard_id
"""

    params = {}
    wheres = []
    # 现在仅仅展示运行时的数据
    wheres.append('ch.source = %(source)s')
    params['source'] = 'released'

    if model.start:
        wheres.append('ch.hit_date >= %(start)s')
        params['start'] = model.start
    if model.end:
        wheres.append('ch.hit_date <= %(end)s')
        params['end'] = model.end
    if model.dataset_type:
        if model.dataset_type == 'direct':
            wheres.append('dst.connect_type = %(connect_type)s')
            params['connect_type'] = '直连'
        else:
            wheres.append("(dst.connect_type is null or dst.connect_type = '')")
            # 下面这么写会有问题
            # wheres.append('dst.connect_type != %(connect_type)s')
            # params['connect_type'] = '直连'
    if model.data_source_type:
        model.data_source_type = list(set(model.data_source_type))
        wheres.append('ds.type in %(data_source_type)s')
        params['data_source_type'] = model.data_source_type
    if wheres:
        pha = 'WHERE ' + ' AND '.join(wheres)
        bar_sql = bar_sql.format(WHRER_SQL=pha)
        table_sql = table_sql.format(WHRER_SQL=pha)
    bar_data = repository.get_data_by_sql(bar_sql, params=params) or []
    table_data = repository.get_data_by_sql(table_sql, params=params) or []
    return bar_data, table_data
