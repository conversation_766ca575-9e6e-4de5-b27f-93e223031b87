#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by chenc04 2017/5/12.
"""

from base import repository
from base.models import QuerySortModel
from dmplib.saas.project import get_db


def get_label_table_name(label_id):
    label_name_list = repository.get_columns('label', {'label_id': label_id}, 'label_tablename')
    if label_name_list:
        return label_name_list[0]
    else:
        return ''


def get_table_data(query_model, table_name):
    """
    获取表数据
    :param query_model:
    :param str table_name:
    :return download.models.DownloadJobQueryModel:
    """
    with get_db() as db:
        sql = 'SELECT {cols} ' 'FROM {table_name} WHERE download_job_id=%(download_job_id)s '.format(
            cols='`' + '`,`'.join(query_model.list_cols) + '`', table_name='`' + table_name + '`'
        )
        params = {'download_job_id': query_model.download_job_id}
        if query_model.keyword:
            sql += ' AND `name` LIKE %(keyword)s '
            params['keyword'] = '%' + query_model.keyword_escape + '%'
        order_by = ''
        if query_model.sorts:
            for sort in query_model.sorts:
                if not isinstance(sort, QuerySortModel):
                    continue
                if sort.id == 'name':
                    order_by = ' ORDER BY convert(f.`name` USING gbk) COLLATE gbk_chinese_ci ' + sort.method
                    break
        sql += order_by or ' ORDER BY `created_on` DESC'
        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model


def get_download_job_data(query_model):
    """
    获取下载任务表数据
    :param query_model:
    :return download.models.DownloadJobQueryModel:
    """
    with get_db() as db:
        sql = 'SELECT f.id,f.`name`,d.description,f.`run_status`,' 'd.format,d.`type`,d.created_on ' 'FROM flow as f ' 'INNER JOIN download_job as d on d.id=f.id '

        params = {}
        if query_model.keyword:
            params['keyword'] = '%' + query_model.keyword_escape + '%'
            sql += ' WHERE (' + ' OR '.join(['`' + col + '` LIKE %(keyword)s' for col in query_model.query_col]) + ')'
        order_by = ''
        if query_model.sorts:
            for sort in query_model.sorts:
                if not isinstance(sort, QuerySortModel):
                    continue
                if sort.id == 'name':
                    order_by = ' ORDER BY convert(f.`name` USING gbk) COLLATE gbk_chinese_ci ' + sort.method
                    break
        sql += order_by or ' ORDER BY f.`created_on` DESC'

        query_model.total = repository.get_total(sql, params, db)
        sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
        query_model.items = db.query(sql, params)
    return query_model
