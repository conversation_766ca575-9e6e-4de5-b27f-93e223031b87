#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
报告信息
"""

# ---------------- 标准模块 ----------------
import logging

# ---------------- 业务模块 ----------------
from healthy.services.dashboard_charts_map.map_generator import MapGenerator
from healthy.services.dashboard_charts_map.charts_map_models import ChartsMapDashboardModel, ChartsMapChartModel
from base.enums import HealthyCheckResultStatus


logger = logging.getLogger(__name__)


class DashboardGenerator(MapGenerator):
    """
    报告级筛选
    ps: 特殊的generator，用于组装报告自己的数据和报告级筛选的巡检错误
    """

    def generate(self, chart_map_chart_model: ChartsMapChartModel):
        """

        :param chart_map_chart_model:
        :return:
        """
        dashboard = self._metadata_storage.get_dashboard()
        dashboard_id = dashboard.get("id")
        dashboard_healthy_info = self._metadata_storage.get_dashboard_healthy_result()
        info = dashboard_healthy_info.get("info", {})

        errors = {
            "errcnt": info.get("error_cnt", 0),
            "warncnt": info.get("warning_cnt", 0),
            "errors": self._metadata_storage.get_chart_healthy_msgs(
                dashboard_id, HealthyCheckResultStatus.UnHealthy.value
            ),
            "warnings": self._metadata_storage.get_chart_healthy_msgs(
                dashboard_id, HealthyCheckResultStatus.Warning.value
            ),
        }
        model = ChartsMapDashboardModel()
        model.dashboard_id = dashboard_id
        model.dashboard_name = dashboard.get("name")
        model.cover = dashboard.get("cover")
        model.create_type = dashboard.get("create_type")
        model.status = info.get("status")
        model.start_time = info.get("start_time", "")
        model.errors = errors

        return model.get_dict()
