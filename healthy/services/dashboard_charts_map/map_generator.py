#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/1/14 10:12
# <AUTHOR> caoxl
# @File     : map_generator.py
from abc import ABC, abstractmethod
from healthy.services.dashboard_metadata_storage import DashboardMetadataStorage
from .charts_map_models import ChartsMapChartModel, ChartsMapChartDatasetModel
from healthy.services import healthy_common_service


class MapGenerator(ABC):
    def __init__(self, metadata_storage: DashboardMetadataStorage):
        self._metadata_storage = metadata_storage

    @abstractmethod
    def generate(self, chart_map_chart_model: ChartsMapChartModel) -> ChartsMapChartModel:
        """
        生成模块的model
        :param chart_map_chart_model: 传入的chart model对象
        :return:
        """

    # pylint: disable=R0201
    def append_addition_msg(self, errors, _):
        """
        追加巡检结果
        :param errors:
        :param check_data_id:
        :return:
        """
        return errors

    def get_dataset_model(self, dataset_id, p_dataset_id):
        dataset = self._metadata_storage.get_dataset(dataset_id)
        if not dataset:
            return dataset

        is_same_dataset = 1 if dataset_id == p_dataset_id else 0
        return ChartsMapChartDatasetModel(
            **{
                "dataset_id": dataset.get("id"),
                "dataset_name": dataset.get("name"),
                "type": dataset.get("type"),
                "is_same_dataset": is_same_dataset,
                "childs": healthy_common_service.get_child_datasets(dataset),
            }
        )

    def get_chart_healthy_msgs(self, chart_id, status):
        dataset_msgs = self._metadata_storage.get_chart_healthy_msgs(chart_id, status)
        relation_msgs = self._metadata_storage.get_chart_healthy_msgs(chart_id, status)
        dataset_msgs.extend(relation_msgs)
        return dataset_msgs

    @staticmethod
    def get_single_field_relation(initiator_field_info, responder_field_info):
        """

        :param initiator_field_info:
        :param responder_field_info:
        :return:
        """
        return [
            {
                "field_id": initiator_field_info.get("id", "") if initiator_field_info else "",
                "field_name": initiator_field_info.get("alias_name")
                or initiator_field_info.get("col_name")
                or initiator_field_info.get("origin_col_name")
                if initiator_field_info
                else "",
                "field_type": initiator_field_info.get("data_type") if initiator_field_info else "",
            },
            {
                "field_id": responder_field_info.get("id", "") if responder_field_info else "",
                "field_name": responder_field_info.get("alias_name")
                or responder_field_info.get("col_name")
                or responder_field_info.get("origin_col_name")
                if responder_field_info
                else "",
                "field_type": responder_field_info.get("data_type") if responder_field_info else "",
            },
        ]
