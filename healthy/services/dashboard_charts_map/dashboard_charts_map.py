#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/1/14 10:10
# <AUTHOR> caoxl
# @File     : dashboard_charts_map_service.py.py
import traceback
import logging
from ..dashboard_metadata_storage import DashboardMetadataStorage
from .charts_map_models import (
    BaseChartsMapModel,
    ChartsMapModel,
    ChartsMapDashboardModel,
    ChartsMapChartModel,
    ChartsMapChartDatasetModel,
)
from base.enums import HealthyCheckResultStatus
from . import dashboard_generator
from healthy.services import healthy_common_service
from dmplib.utils.errors import UserError


logger = logging.getLogger(__name__)


class DashboardChartsMap:
    def __init__(self, dashboard_metadata):
        self._charts_map_model = ChartsMapModel()
        self._metadata_storage = DashboardMetadataStorage(dashboard_metadata=dashboard_metadata)
        self._chart_node_generators = dict()

    def attach_chart_node_generators(self, generators: list):
        """
        批量添加chart节点生成器
        :param generators:
        :return:
        """
        for generator in generators:
            self._chart_node_generators[str(generator)] = self._create_chart_node_generator(generator)
        return self._chart_node_generators

    def _create_chart_node_generator(self, name):
        """
        根据名称生成chart节点生成器类实例
        :param name: 生成器名称
        :return:MapGenerator
        """
        class_file_name = name
        class_name = ""
        name_arr = name.split("_")
        for item in name_arr:
            class_name += item.capitalize()
        module_obj = __import__("healthy.services.dashboard_charts_map." + class_file_name, fromlist=[class_name])
        editor_class = getattr(module_obj, class_name)
        return editor_class(metadata_storage=self._metadata_storage)

    def get_dashboard_model(self):
        """
        生成dashboard model 信息
        :return:
        """
        dashboard = self._metadata_storage.get_dashboard()
        dashboard_healthy_info = self._metadata_storage.get_dashboard_healthy_result()
        info = dashboard_healthy_info.get("info", {})
        return ChartsMapDashboardModel(
            **{
                "dashboard_id": dashboard.get("id"),
                "dashboard_name": dashboard.get("name"),
                "cover": dashboard.get("cover"),
                "status": info.get("status"),
                "errors": {"errcnt": info.get("error_cnt", 0), "warncnt": info.get("warning_cnt", 0)},
                "start_time": info.get("start_time", ""),
            }
        )

    def get_dataset_model(self, dataset_id):
        dataset = self._metadata_storage.get_dataset(dataset_id)
        if not dataset:
            return dataset
        return ChartsMapChartDatasetModel(
            **{
                "dataset_id": dataset.get("id"),
                "dataset_name": dataset.get("name"),
                "type": dataset.get("type"),
                "childs": healthy_common_service.get_child_datasets(dataset),
            }
        )

    def get_chart_model(self, chart_info):
        """
        生成 chart model信息
        :param chart_info: dict chart信息
        :return:
        """
        dataset = None
        if chart_info.get("data").get("datasource") and chart_info.get("data").get("datasource").get("id"):
            dataset = self.get_dataset_model(chart_info.get("data").get("datasource").get("id"))

        return ChartsMapChartModel(
            **{
                "chart_id": chart_info.get("id"),
                "chart_name": chart_info.get("name"),
                "chart_code": chart_info.get("chart_component_code"),
                "parent_id": "",
                "status": 0,
                "errors": {
                    "errors": self._metadata_storage.get_chart_healthy_msgs(
                        chart_info["id"], HealthyCheckResultStatus.UnHealthy.value
                    ),
                    "warnings": self._metadata_storage.get_chart_healthy_msgs(
                        chart_info["id"], HealthyCheckResultStatus.Warning.value
                    ),
                },
                "dataset": dataset,
                "penetrates": [],
                "linkages": [],
                "filters": [],
                "redirects": [],
                "new_filters": [],
                "new_linkages": [],
            }
        )

    def _filter_charts(self, charts):
        sub_penetrates_charts = self._metadata_storage.get_sub_penetrates_charts()
        if sub_penetrates_charts:
            sub_penetrates_chart_ids = [item.get("chart_id") for item in sub_penetrates_charts]
            charts = list(filter(lambda item: item.get("id") not in sub_penetrates_chart_ids, charts))
        charts.sort(
            key=lambda elem: 1
            if elem.get("data").get("datasource") and elem.get("data").get("datasource").get("id")
            else 2
        )
        return charts

    def generate_charts_map(self):
        """
        生成字典对象
        :return:
        """
        charts = self._filter_charts(self._metadata_storage.get_charts())
        for chart in charts:
            chart_map_chart_model = self.get_chart_model(chart)
            for name, generator in self._chart_node_generators.items():
                try:
                    chart_map_chart_model = generator.generate(chart_map_chart_model)
                except UserError as e:
                    raise UserError(message=str(e))
                except Exception as e:
                    logger.error("generator:%s exception:%s,traceback:%s", name, e, traceback.format_exc())

            self._charts_map_model.charts.append(chart_map_chart_model)

        # 对结果按优先级排序，1引用数据集的单图优先，2有巡检错误的单图优先
        self.rank_for_healthy_data()

        return self._data2dict(self._charts_map_model)

    def _data2dict(self, data):
        """
        混合数据转换为字典
        :return:
        """
        if isinstance(data, BaseChartsMapModel):
            data = data.get_dict()
            for key, item in data.items():
                data[key] = self._data2dict(item)
        elif isinstance(data, dict):
            for key, item in data.items():
                data[key] = self._data2dict(item)
        elif isinstance(data, (list,)):
            for index, item in enumerate(data):
                data[index] = self._data2dict(item)
        return data

    def generate_dashboard_map(self):
        """

        :return:
        """
        return dashboard_generator.DashboardGenerator(self._metadata_storage).generate(ChartsMapChartModel())

    def rank_for_healthy_data(self):
        """
        对结果按优先级排序，1有引用数据集的单图优先，2有巡检错误的单图优先
        :return:
        """
        rank_charts_list = list()
        rank_charts_dict = {"1_have_error": [], "2_have_dataset": [], "3_no_dataset": []}
        if not self._charts_map_model.charts:
            return
        for single_chart in self._charts_map_model.charts:
            if not isinstance(single_chart, ChartsMapChartModel):
                rank_charts_dict["3_no_dataset"].append(single_chart)
                continue
            if single_chart.errors and (single_chart.errors.get("errors") or single_chart.errors.get("warnings")):
                rank_charts_dict["1_have_error"].append(single_chart)
                continue
            if not single_chart.dataset:
                rank_charts_dict["3_no_dataset"].append(single_chart)
                continue
            rank_charts_dict["2_have_dataset"].append(single_chart)
        for key in sorted(list(rank_charts_dict.keys())):
            rank_charts_list.extend(rank_charts_dict[key])
        self._charts_map_model.charts = rank_charts_list
