#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=R0201,E0401

"""
报告间关系图
"""

# ---------------- 标准模块 ----------------
import ujson
import logging
from collections import defaultdict

# ---------------- 业务模块 ----------------
from .checker import Checker
from base.enums import DashboardJumpType, DashboardJumpConfigStatus, JumpConfigSourceType
from healthy.models import DashboardRelationItemModel, InspectJumpRelationModel
from healthy.repositories import healthy_repository
from dataset import external_query_service


logger = logging.getLogger(__name__)


class DashboardRelationChecker(Checker):
    def __init__(self, metadata_storage, dashboard_id, instance_id):
        self.dashboard_id = dashboard_id
        self.instance_id = instance_id
        self.dataset_field_id_list = list()
        self.target_dashboard_info_dict = dict()
        self._dataset_field_info_dict = defaultdict(dict)
        self._check_result_dict = defaultdict(dict)
        super().__init__(metadata_storage)

    def get_single_relation(self, dataset_field_info):
        """

        :param dataset_field_info:
        :return:
        """
        if not dataset_field_info:
            return None
        return {
            "field_id": dataset_field_info.get("id", ""),
            "field_name": dataset_field_info.get("alias_name")
            or dataset_field_info.get("col_name")
            or dataset_field_info.get("origin_col_name")
            if dataset_field_info
            else "",
            "field_type": dataset_field_info.get("data_type") if dataset_field_info else "",
        }

    def _collect_dataset_field_v1(self, redirects_data):
        """
        收集字段
        :param redirects_data:
        :return:
        """
        for single_redirect in redirects_data:
            chart_redirect = single_redirect.get("chart_redirect")
            if not chart_redirect:
                continue
            self._collect_field_id(chart_redirect)

    def _collect_field_id(self, chart_redirect):
        """
        收集字段id
        :param chart_redirect:
        :return:
        """
        for single_chart_redirect in chart_redirect:
            dataset_field_id = single_chart_redirect.get("dataset_field_id")
            if dataset_field_id:
                self.dataset_field_id_list.append(dataset_field_id)
            relations = single_chart_redirect.get("relations")
            if not relations:
                continue
            for item in relations:
                if item.get("field_initiator_id"):
                    self.dataset_field_id_list.append(item.get("field_initiator_id"))

    def _collect_dataset_field_v2(self, data):
        """
        收集字段
        :return:
        """
        for item in data:
            if item.get("source_type") == JumpConfigSourceType.Title.value:
                continue
            if item.get("dataset_field_id"):
                self.dataset_field_id_list.append(item.get("dataset_field_id"))

    def assign_field_info(self):
        """

        :return:
        """
        if self.dataset_field_id_list:
            external_dataset_field_infos = external_query_service.get_multi_dataset_fields(
                list(set(self.dataset_field_id_list))
            )
            for single_dataset_field_info in external_dataset_field_infos:
                dataset_field_id = single_dataset_field_info.get("id")
                if dataset_field_id:
                    self._dataset_field_info_dict[dataset_field_id] = single_dataset_field_info

    def _collect_target_dashboard(self, redirects_data):
        """
        收集被跳报告
        :param redirects_data:
        :return:
        """
        target_dashboard_id_list = list()
        for redirect in redirects_data:
            chart_redirect = redirect.get("chart_redirect")
            if not chart_redirect:
                continue
            for single_chart_redirect in chart_redirect:
                target = single_chart_redirect.get("target")
                target_type = single_chart_redirect.get("target_type")
                if target_type == DashboardJumpType.Dashboard.value and target:
                    target_dashboard_id_list.append(target)
        query_dashboard_data = None
        if len(target_dashboard_id_list):
            query_dashboard_data = healthy_repository.batch_get_dashboards(target_dashboard_id_list)
        if query_dashboard_data:
            for single_data in query_dashboard_data:
                self.target_dashboard_info_dict[single_data.get("id")] = single_data

    def generate_jump_data(self, jump_dashboards):
        """
        组装跳转目标为当前报告的数据
        :param jump_dashboards:
        :return:
        """
        jump_dashboard_data_list = list()
        # 收集所有以当前报告为跳转target的报告
        if not jump_dashboards:
            return jump_dashboard_data_list

        for single_dashboard in jump_dashboards:
            relations = list()
            if single_dashboard.get('source_type') == JumpConfigSourceType.Title.value:
                continue
            model = InspectJumpRelationModel()
            dataset_field_id = single_dashboard.get("source_id")
            dashboard_id = single_dashboard.get('dashboard_id', '')
            dataset_field_info = self._dataset_field_info_dict.get(dataset_field_id)
            model.id = dataset_field_id
            model.dashboard_id = dashboard_id
            model.name = single_dashboard.get("name")
            model.cover = single_dashboard.get("cover")
            model.create_type = single_dashboard.get("create_type")
            model.parent_id = ""
            model.relation_type = 1  # 0默认 1跳转 2其他
            model.target_type = DashboardJumpType.Dashboard.value
            single_relation = self.get_single_relation(dataset_field_info)
            if single_relation:
                relations.append(single_relation)
            model.relations = relations
            jump_dashboard_data_list.append(model.get_dict())
        return jump_dashboard_data_list

    def get_target_url_relation(self, main_dashboard_id, single_chart_redirect):
        """

        :param main_dashboard_id:
        :param single_chart_redirect:
        :return:
        """
        target = single_chart_redirect.get("target")
        target_type = single_chart_redirect.get("target_type")
        dataset_field_id = single_chart_redirect.get("dataset_field_id")
        dataset_field_info = self._dataset_field_info_dict.get(dataset_field_id)
        model = DashboardRelationItemModel()
        model.parent_id = main_dashboard_id
        model.relation_type = 1  # 0默认 1跳转 2其他
        model.target_type = target_type
        model.id = ""
        model.name = ""
        model.cover = ""
        model.target_url = target
        field_relations = list()
        single_relation = self.get_single_relation(dataset_field_info)
        if single_relation:
            field_relations.append(single_relation)
        model.relations = field_relations
        return model.get_dict()

    def get_target_dashboard_relation(self, main_dashboard_id, single_relation, target, target_type):
        """

        :param main_dashboard_id:
        :param single_relation:
        :param target:
        :param target_type:
        :return:
        """
        dataset_field_id = single_relation.get("field_initiator_id")
        dataset_field_info = self._dataset_field_info_dict.get(dataset_field_id)
        model = DashboardRelationItemModel()
        model.parent_id = main_dashboard_id
        model.relation_type = 1  # 0默认 1跳转 2其他
        model.target_type = target_type
        model.id = target
        dashboard_info = self.target_dashboard_info_dict.get(target)
        if dashboard_info:
            model.name = dashboard_info.get("name")
            model.cover = dashboard_info.get("cover")
            model.create_type = dashboard_info.get("create_type")
        field_relations = list()
        append_single_relation = self.get_single_relation(dataset_field_info)
        if append_single_relation:
            field_relations.append(append_single_relation)
        model.relations = field_relations
        return model.get_dict()

    def generate_target_data(self, main_dashboard_id, redirects_data):
        """
        组装起跳为当前报告的数据
        :param main_dashboard_id:
        :param redirects_data:
        :return:
        """
        target_data_list = list()
        if not redirects_data:
            return target_data_list

        for redirect in redirects_data:
            chart_redirect = redirect.get("chart_redirect")
            if not chart_redirect:
                continue
            self._deal_with_single_chart_redirect(chart_redirect, target_data_list, main_dashboard_id)
        return target_data_list

    def _deal_with_single_chart_redirect(self, chart_redirect, target_data_list, main_dashboard_id):
        """
        处理chart_redirect
        :param chart_redirect:
        :param target_data_list:
        :param main_dashboard_id:
        :return:
        """
        for single_chart_redirect in chart_redirect:
            target = single_chart_redirect.get("target")
            target_type = single_chart_redirect.get("target_type")
            relations = single_chart_redirect.get("relations")
            status = single_chart_redirect.get("status")
            if status in [DashboardJumpConfigStatus.Invalid.value]:
                continue
            # 被跳方是url
            if target_type == DashboardJumpType.Url.value:
                target_data_list.append(self.get_target_url_relation(main_dashboard_id, single_chart_redirect))
            # 被跳方是报告
            elif target_type == DashboardJumpType.Dashboard.value and relations:
                for single_relation in relations:
                    target_data_list.append(
                        self.get_target_dashboard_relation(main_dashboard_id, single_relation, target, target_type)
                    )

    def generate_relation_data(self):
        """
        生成报告间关系数据
        :return:
        """
        dashboard_relation_data = {"dashboard": {}, "left": [], "right": []}
        dashboard_data = self._metadata_storage.get_dashboard()
        redirects_data = self._metadata_storage.get_redirect_data()
        first_report = self._metadata_storage.get_first_report()
        jump_dashboards_data = first_report.get("jump_dashboards_data")

        # 先收集元数据中redirects中的数据集字段
        self._collect_dataset_field_v1(redirects_data)

        # 收集被跳报告或url
        self._collect_target_dashboard(redirects_data)

        if not dashboard_data:
            return dashboard_relation_data

        # 主报告信息
        main_dashboard_id = dashboard_data.get("id")
        main_model = DashboardRelationItemModel()
        main_model.id = main_dashboard_id
        main_model.name = dashboard_data.get("name")
        main_model.cover = dashboard_data.get("cover")
        main_model.create_type = dashboard_data.get("create_type")
        main_model.parent_id = ""
        main_model.relation_type = 0
        main_model.target_type = ""
        main_model.relations = []
        dashboard_relation_data["dashboard"] = main_model.get_dict()

        # 其他报告通过普通跳转到当前报告
        if jump_dashboards_data:
            self._collect_dataset_field_v2(jump_dashboards_data)

        # # 其他报告通过参数跳转到当前报告
        # param_jump_dashboards = healthy_repository.get_param_jump_dashboards(main_dashboard_id)
        # if param_jump_dashboards:
        #     self._collect_dataset_field_v2(param_jump_dashboards)

        # 获取数据集字段数据
        self.assign_field_info()

        # 主跳报告
        dashboard_relation_data["left"] = self.generate_jump_data(jump_dashboards_data)
        # 被跳报告
        dashboard_relation_data["right"] = self.generate_target_data(main_dashboard_id, redirects_data)
        return dashboard_relation_data

    def check(self):

        # 生成报告间关系数据
        dashboard_relation_data = self.generate_relation_data()

        # 组装后的数据以json字符串格式保存到instance表
        healthy_repository.save_dashboard_relation_data(
            self.instance_id, self.dashboard_id, ujson.dumps(dashboard_relation_data)
        )

        # 这个checker不需要校验错误，兼容外层的调用，返回空数据
        return self._check_result_dict
