import traceback
import logging
from abc import ABC, abstractmethod

from base import repository
from healthy.services.dashboard_healthy_service import get_dashboard_metadata
from healthy.services.dashboard_metadata_storage import DashboardMetadataStorage


logger = logging.getLogger(__name__)


class CheckNode(ABC):
    check_mag = {'status': True, 'error_info': []}

    def __init__(self, metadata_storage):
        self._metadata_storage = metadata_storage
        self.metadata = DashboardMetadataStorage(self._metadata_storage)

    def get_dataset_ids(self):
        charts = self.metadata.get_charts()
        dataset_ids = set()
        for chart in charts:
            if not chart.get("data").get("datasource"):
                continue
            dataset_id = chart.get("data").get("datasource").get("id")
            if not dataset_id:
                continue
            dataset_ids.add(dataset_id)
        return list(dataset_ids)

    def get_chart_info_by_id(self, chart_id):
        charts = self.metadata.get_charts()
        for chart in charts:
            if chart.get('id') == chart_id:
                return chart
        return dict()

    def get_var_relations(self):
        chart_filter_relation = self.metadata.get_chart_relations()
        return chart_filter_relation.get('var_relations')

    @abstractmethod
    def check(self):
        pass

    @abstractmethod
    def node_name(self):
        pass


class DashboardConfigChecker(object):
    def __init__(self, dashboard_id):
        self.dashboard_id = dashboard_id
        self.dashboard_metadata = None
        self.checker_node = []
        self.result = dict()
        self.load_config()

    def load_config(self):
        dashboard_metadata = get_dashboard_metadata(self.dashboard_id, 0)
        instance_node_list = ['DatasetNode', 'ChartFilterNode', 'ChartVarRelationsNode']
        for node in instance_node_list:
            class_name = 'Check{}'.format(node)
            module = __import__(__name__)
            check_module = module.services.dashboard_healthy_check.dashboard_config_check
            self.checker_node.append(getattr(check_module, class_name)(dashboard_metadata))

    def add_node_checker(self, node_checker: CheckNode):
        self.checker_node.append(node_checker)

    def check(self):
        """
        串行调用checker
        :return:
        """
        for node in self.checker_node:
            if isinstance(node, CheckNode):
                try:
                    self.result[node.node_name()] = node.check()
                except Exception as e:
                    logger.error("%s检查出错,%s", node.node_name(), traceback.format_exc())
                    self.result[node.node_name()] = str(e)
        return self.result


class CheckDatasetNode(CheckNode):

    def check(self):
        # 获取元数据中所有的使用的dataset_ids
        dataset_ids = self.get_dataset_ids()
        if dataset_ids:
            # 真实存在的数据集ID
            has_dataset_ids = repository.get_column('dataset', {'id': dataset_ids}, ['id']) or []
            # 组件已使用，但是不存在的数据集ID
            bug_dataset_ids = list(set(dataset_ids).difference(set(has_dataset_ids)))
            if bug_dataset_ids:
                self._check_dataset_ids(bug_dataset_ids)
        return self.check_mag

    def _check_dataset_ids(self, bug_dataset_ids):
        charts = self.metadata.get_charts()
        for chart in charts:
            chart_dataset_id = chart.get("data").get("datasource", {}).get('id') or ''
            if not chart_dataset_id:
                continue
            if chart_dataset_id in bug_dataset_ids:
                self.check_mag['status'] = False
                self.check_mag['error_info'].append(
                    {'chart_id': chart.get('id'), 'chart_name': chart.get('name'), 'dataset_id': chart_dataset_id}
                )

    def node_name(self):
        return 'dataset_node'


class CheckChartFilterNode(CheckNode):
    def check(self):
        # 获取被筛选的相关组件
        chart_filters = self.metadata.get_new_filter_chart() or []
        filtered_list = self.get_filtered_chart_id(chart_filters)
        # 查询真是存在的组件
        has_chart_id = repository.get_column('dashboard_chart', {'id': filtered_list}, ['id'])
        # 找出异常的组件ID
        bug_chart_ids = list(set(filtered_list).difference(set(has_chart_id)))
        if bug_chart_ids:
            self.check_mag['status'] = False
            self.check_mag['error_info'].append(
                {'bug_chart_ids': bug_chart_ids, 'bug_filter_chart_info': self.get_bug_filter_chart(bug_chart_ids)}
            )
        return self.check_mag

    @staticmethod
    def get_filtered_chart_id(chart_filters):
        filtered_list = set()
        for chart_filter in chart_filters:
            related_list = chart_filter.get('related_list')
            for related in related_list:
                chart_responder_id = related.get('chart_responder_id')
                filtered_list.add(chart_responder_id)
        return list(filtered_list)

    def get_bug_filter_chart(self, bug_chart_ids):
        bug_filter_chart_ids = set()
        bug_filter_chart_info = []
        # 获取被筛选的相关组件
        chart_filters = self.metadata.get_new_filter_chart() or []
        for chart_filter in chart_filters:
            related_list = chart_filter.get('related_list')
            for related in related_list:
                if related.get('chart_responder_id') in bug_chart_ids:
                    bug_filter_chart_ids.add(chart_filter.get('chart_initiator_id'))
        if bug_filter_chart_ids:
            for filter_id in bug_filter_chart_ids:
                chart_info = self.get_chart_info_by_id(filter_id)
                if chart_info:
                    bug_filter_chart_info.append({'id': chart_info.get('id'), 'chart_name': chart_info.get('name')})
        return bug_filter_chart_info

    def node_name(self):
        return 'chart_filters_relation_node'


class CheckChartVarRelationsNode(CheckNode):

    def check(self):
        # 获取变量筛选元数据
        var_relation_info = self.get_var_relations() or []
        # 获取关联的变量ID
        var_ids = [var_relation.get('var_id') for var_relation in var_relation_info]
        # 查询对应的变量ID是否异常
        real_var_ids = repository.get_column('dataset_vars', {'id': var_ids}, ['id'])
        # 找出有问题的变量ID
        bug_var_ids = list(set(var_ids).difference(set(real_var_ids)))
        # 找出使用这个变量的筛选组件
        if bug_var_ids:
            bug_chart_filter_id = [var_relation.get('chart_initiator_id') for var_relation in var_relation_info if var_relation.get('var_id') in bug_var_ids]
            if bug_chart_filter_id:
                self.check_mag['error_info'].append(
                    {'bug_var_ids': bug_var_ids, 'bug_filter_chart_info': self.get_bug_filter_chart(bug_chart_filter_id)}
                )
                self.get_bug_filter_chart(bug_chart_filter_id)
        return self.check_mag

    def get_bug_filter_chart(self, bug_filter_chart_ids):
        bug_filter_chart_info = []
        if bug_filter_chart_ids:
            for filter_id in bug_filter_chart_ids:
                chart_info = self.get_chart_info_by_id(filter_id)
                if chart_info:
                    bug_filter_chart_info.append({'id': chart_info.get('id'), 'chart_name': chart_info.get('name')})
        return bug_filter_chart_info

    def node_name(self):
        return 'chart_var_relation_node'
