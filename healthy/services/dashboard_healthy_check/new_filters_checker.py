#!/usr/local/bin python3
# -*- coding: utf-8 -*-

from .checker import Checker
from base.enums import HealthyCheckResultStatus


class NewFiltersChecker(Checker):
    def check(self) -> dict:
        result = {}
        dataset_fields = self.get_in_realtion_fields("new_filter")
        filters = self._metadata_storage.get_new_filter_chart()
        for fc in filters:
            # 固定值不需要巡检
            if fc.get("initiator_source") == 'fixed_value':
                continue
            initiator = fc.get("chart_initiator_id")
            initiator_field = fc.get("field_initiator_id")
            initiator_name = self._metadata_storage.get_chart_name_from_metadata(initiator)
            if initiator_field not in dataset_fields:
                self.add_check_msg(
                    result,
                    initiator_field,
                    initiator_name,
                    1007,
                    HealthyCheckResultStatus.UnHealthy.value,
                    *[initiator_name, "筛选", self._metadata_storage.get_field_name_from_metadata(initiator_field)],
                )

            for related in fc["related_list"]:
                responder = related.get("chart_responder_id")
                responder_field = related.get("field_responder_id")
                if not self.is_chart_exist(responder):
                    self.add_check_msg(
                        result, initiator, initiator_name, 1004, HealthyCheckResultStatus.UnHealthy.value
                    )
                    continue

                # 检查当前关系中的数据集和charts中的datasource是否一致
                if related.get("related_dataset_id") != self._metadata_storage.get_chart_source_by_chart_id(responder):
                    self.add_check_msg(
                        result,
                        initiator,
                        initiator_name,
                        1016,
                        HealthyCheckResultStatus.UnHealthy.value,
                        *[initiator_name, "筛选", self._metadata_storage.get_chart_name_from_metadata(responder)],
                    )
                    continue

                if responder_field not in dataset_fields:
                    self.add_check_msg(
                        result,
                        initiator,
                        initiator_name,
                        1008,
                        HealthyCheckResultStatus.UnHealthy.value,
                        *[
                            initiator_name,
                            "筛选",
                            self._metadata_storage.get_chart_name_from_metadata(responder),
                            self._metadata_storage.get_field_name_from_metadata(responder_field),
                        ],
                    )

        return result
