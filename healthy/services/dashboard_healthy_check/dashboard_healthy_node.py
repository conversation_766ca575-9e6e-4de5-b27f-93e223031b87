#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=R0201

from ..healthy_node import <PERSON><PERSON><PERSON>ode<PERSON>he<PERSON>
from healthy.services.healthy_node import CheckingResult
from base.enums import HealthyCheckResultStatus as ResultStatus
import traceback
import logging

logger = logging.getLogger(__name__)


class DashboardHealthyNode(AbsNodeChecker):
    def __init__(self, project: str, dashboard_id: str, instance_id: str, metadata_storage):
        self.code = "dashboard"
        self.checkers = {}
        super().__init__(self.code, project, dashboard_id, instance_id, metadata_storage)

    def attach_checkers(self, **kwargs):
        """
        注册单图相关的checker
        :param kwargs:
        :return:
        """
        checkers = kwargs.get("checkers")
        if not checkers:
            return
        for checker_name in checkers:
            self.checkers[checker_name] = self._create_checker(
                checker_name, kwargs.get("dashboard_id"), kwargs.get("instance_id")
            )

    def _create_checker(self, name, dashboard_id, instance_id):
        """
        根据具体巡检名称生成具体巡检对象
        :param name:
        :param dashboard_id:
        :param instance_id:
        :return:
        """
        class_file_name = name
        class_name = ""
        name_arr = name.split("_")
        for item in name_arr:
            class_name += item.capitalize()
        module_obj = __import__("healthy.services.dashboard_healthy_check." + class_file_name, fromlist=[class_name])
        checker_class = getattr(module_obj, class_name)

        if name == "dashboard_relation_checker":
            return checker_class(self.metadata_storage, dashboard_id, instance_id)
        return checker_class(self.metadata_storage)

    def _execute(self) -> CheckingResult:
        check_result = CheckingResult()
        check_result.rules = {}
        check_result.status = ResultStatus.ToCheck.value

        for name, checker in self.checkers.items():
            try:
                logger.debug("begin check:%s", name)
                result = checker.check()
            except Exception as e:
                result = {}
                logger.error("checker:%s exception:%s, traceback:%s", name, e, traceback.format_exc())

            check_result.rules[name] = result
            for _, check_items in result.items():
                check_items_result = check_items.get("result")
                if not result:
                    continue
                for check_item in check_items_result:
                    status = check_item.get("status")
                    check_result.check_status.append(status)
                    check_result.status = status if status > check_result.status else check_result.status

        error_count = check_result.check_status.count(ResultStatus.UnHealthy.value)
        warn_count = check_result.check_status.count(ResultStatus.Warning.value)
        check_result.summary = self.generate_summary(error_count, warn_count)

        check_result.rules["info"] = {
            "dashboard_id": self.dashboard_id,
            "name": self.metadata_storage.get_dashboard_name(),
            "error_cnt": error_count,
            "warning_cnt": warn_count,
            "node_cnt": 0,
            "status": check_result.status,
        }

        return check_result
