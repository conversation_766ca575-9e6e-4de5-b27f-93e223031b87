#!/usr/local/bin python3
# -*- coding: utf-8 -*-

import copy
from abc import ABC, abstractmethod
from healthy.services.dashboard_metadata_storage import DashboardMetadataStorage
from base.enums import HealthyCheckResultStatus
from functools import lru_cache


ret_info_dict = {
    "1000": {"code": 1000, "msg": "正常"},
    "1001": {"code": 1001, "msg": "引用的数据集字段【{}】不存在"},
    "1002": {"code": 1002, "msg": "引用的数据集【{}】不存在"},
    "1003": {"code": 1003, "msg": "穿透单图不存在"},
    "1004": {"code": 1004, "msg": "响应单图不存在"},
    "1005": {"code": 1005, "msg": "没有配置响应单图"},
    "1006": {"code": 1006, "msg": "单图【{}】无法查询到数据,请检查组件配置: {}"},
    "1007": {"code": 1007, "msg": "【{}】单图发起的【{}关系】中,发起字段【{}】不存在"},  # 【xx】单图发起的【筛选关系】中,发起字段【】不存在
    "1008": {"code": 1008, "msg": "【{}】单图发起的【{}关系】中,【{}】响应单图的字段【{}】不存在"},  # 【xx】单图发起的【筛选关系】中，【bb】响应单图的【cc】字段不存在
    "1009": {"code": 1009, "msg": "【{}】单图发起的【跳转关系】中,响应报告的主字段【{}】不存在"},
    "1010": {"code": 1010, "msg": "【{}】单图发起的【跳转关系】中,响应报告未配置报告级筛选"},
    "1011": {"code": 1011, "msg": "【{}】单图发起的穿透关系中,没有配置字段关联"},
    "1012": {"code": 1012, "msg": "报告级筛选引用的主数据集不存在"},
    "1013": {"code": 1013, "msg": "报告级筛选引用的主数据集字段不存在"},
    "1014": {"code": 1014, "msg": "报告级筛选配置的筛选条件中字段值【{}】不存在"},
    "1015": {"code": 1015, "msg": "【{}】单图的【{}】字段【{}】不存在"},
    "1016": {"code": 1016, "msg": "【{}】单图的【{}】关系中,响应单图【{}】数据集已被修改"},
    "1017": {"code": 1017, "msg": "单图【{}】无法查询到数据,请检查组件配置"},
    "1018": {"code": 1018, "msg": "单图【{}】引用的数据集不存在"},
}


class Checker(ABC):
    """
    报告分业务巡检基类
    """

    def __init__(self, metadata_storage: DashboardMetadataStorage):
        self._metadata_storage = metadata_storage

    def get_in_realtion_fields(self, relation_name) -> dict:
        """
        获取单图关系中某一类关系下的所有字段
        """
        field_ids = set()
        filed_dict = {}
        if relation_name in ("filter", "linkage"):
            self._append_field_ids_for_old(relation_name, field_ids)

        elif relation_name in ("new_filter", "new_linkage"):
            self._append_field_ids_for_new(relation_name, field_ids)

        if not field_ids:
            return filed_dict

        queried = self._metadata_storage.batch_get_field_data_by_ids(list(field_ids))
        if not queried:
            return filed_dict
        for dataset_field in queried:
            filed_dict[dataset_field.get("id")] = dataset_field

        return filed_dict

    def _append_field_ids_for_old(self, relation_name, field_ids):
        """
        for old relations
        :param relation_name:
        :param field_ids:
        :return:
        """
        relation_data = (
            self._metadata_storage.get_filter_chart()
            if relation_name == "filter"
            else self._metadata_storage.get_linkage_chart()
        )
        for rd in relation_data:
            for related in rd.get("related_list", []):
                for relation in related["relations"]:
                    field_ids.add(relation["field_initiator_id"])
                    field_ids.add(relation["field_responder_id"])

    def _append_field_ids_for_new(self, relation_name, field_ids):
        """
        for new relations
        :param relation_name:
        :param field_ids:
        :return:
        """
        relation_data = (
            self._metadata_storage.get_new_filter_chart()
            if relation_name == "new_filter"
            else self._metadata_storage.get_new_linkage_chart()
        )
        for rd in relation_data:
            field_ids.add(rd.get("field_initiator_id"))
            for rl in rd.get("related_list", []):
                field_ids.add(rl.get("field_responder_id"))

    @staticmethod
    def get_formatted_charts_data(charts_data):
        """
        获取以单图id为key的字段类型单图数据
        :param charts_data:
        :return:
        """
        formatted_charts_data = dict()
        for single_chart in charts_data:
            if not single_chart:
                continue
            chart_id = single_chart.get("id")
            if chart_id and chart_id not in formatted_charts_data.keys():
                formatted_charts_data[chart_id] = single_chart
        return formatted_charts_data

    @staticmethod
    def get_check_result_info(msg_code=1000, status=HealthyCheckResultStatus.UnHealthy.value):
        """
        统一返回的错误信息
        :param msg_code: 错误码
        :param status: 检查结果. 2:错误, 1:警告, 0:正常
        :return:
        """
        ret_info = ret_info_dict.get(str(msg_code))
        if not ret_info:
            return {}

        ret_info = copy.deepcopy(ret_info)
        if msg_code != 1000 and status:
            ret_info["status"] = status
        elif (msg_code != 1000 and not status) or ret_info["status"] not in [
            i.value for i in HealthyCheckResultStatus.__members__.values()
        ]:
            ret_info["status"] = HealthyCheckResultStatus.UnHealthy.value
        return ret_info

    def add_check_msg(self, result, check_data_id, chart_name, code, healthy, *msgargs):
        """
        追加错误信息
        :param result:
        :param check_data_id:
        :param code:
        :param healthy:
        :param msgargs:
        :return:
        """
        if check_data_id not in result:
            result[check_data_id] = {"result": [], "name": chart_name}

        retinfo = self.get_check_result_info(code, healthy)
        if msgargs:
            retinfo["msg"] = retinfo["msg"].format(*msgargs)
        result[check_data_id]["result"].append(retinfo)

    @lru_cache()
    def _charts_map(self) -> dict:
        chart_dict = {}
        charts = self._metadata_storage.get_charts()
        for chart in charts:
            chart_dict[chart["id"]] = chart
        return chart_dict

    def is_chart_exist(self, chart_id):
        charts = self._charts_map()
        return chart_id in charts

    def is_dataset_field_exists(self, field_id):
        try:
            info = self._metadata_storage.batch_get_field_data_by_ids([field_id])
            if not info:
                return False
        except Exception:
            return False

        return True

    @abstractmethod
    def check(self):
        """
        检查指定业务(子类强制实现此方法)
        return: https://www.tapd.cn/38229611/documents/show/1138229611001001366?file_type=word
        """
