#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=R0201

"""
检查所有单图中引用的数据集、字段是否存在
所有单图能否正常取数
返回:

"""
import json
import logging
from .checker import Checker
from base.enums import HealthyCheckResultStatus as ResultStatus, DatasetVarValueType, ChartDataMode
from dashboard_chart.services.chart_service import batch_get_chart_data
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)


class ChartsChecker(Checker):
    @staticmethod
    def _query_data(dashboard_id, chart, query_vars):
        charts_params = [
            {
                "id": chart["id"],
                "conditions": [],
                "dashboard_id": dashboard_id,
                "data_logic_type_code": chart["data"]["data_type"]["logic_type"],
                "filter_conditions": [],
                "chart_code": chart["chart_component_code"],
                "penetrate_conditions": [],
                "query_vars": query_vars,
            }
        ]
        result = batch_get_chart_data(charts_params)
        return result

    @staticmethod
    def get_chart_logic_type(chart) -> str:
        data = chart.get("data", {})
        data_type = data.get("data_type", {})
        return data_type.get("logic_type", "")

    def get_all_dataset_fields_dict(self) -> dict:
        fields_dict = {}
        fields = self._metadata_storage.get_all_charts_dataset_fields()
        queried = self._metadata_storage.batch_get_field_data_by_ids(fields)
        if queried:
            for q in queried:
                fields_dict[q.get("id")] = q
        return fields_dict

    def _pre_get_var_dict(self):
        var_dict = {}
        # 获取变量对应的取值来源
        var_value_sources = self._metadata_storage.get_var_value_sources_data() or []
        for op_v in var_value_sources:
            relations = op_v.get("relations", [])
            op_v.pop("relations")
            for single_var_id in relations:
                var_dict[single_var_id] = op_v
        return var_dict

    def _assign_value_source(self, chart_vars, var_dict):
        # 获取模拟取数用的query_vars，使用变量的默认值default_value取数
        for single_var in chart_vars:
            default_value = single_var.get("default_value", "")
            try:
                single_var["value"] = (
                    json.loads(default_value)
                    if single_var.get("value_type")
                    in [DatasetVarValueType.List.value, DatasetVarValueType.Section.value]
                    else default_value
                )
            except:
                single_var["value"] = ""
            var_external_info = var_dict.get(single_var.get("var_id"), {})
            single_var["value_source"] = var_external_info.get("value_source", "userdefined")
            single_var["value_identifier"] = var_external_info.get("value_identifier", "")

    def check(self) -> dict:
        result = {}
        charts = self._metadata_storage.get_charts()
        field_dict = self.get_all_dataset_fields_dict()
        name_map = {
            "filters": "筛选",
            "chart_params": "参数",
            "nums": "度量",
            "dims": "维度",
            "marklines": "辅助线",
            "desires": "目标值",
            "comparisons": "对比",
            "zaxis": "z轴",
        }
        # 获取变量对应的取值来源
        var_dict = self._pre_get_var_dict()

        for chart in charts:
            chart_id = chart.get("id")
            chart_name = chart.get("name")
            logic_type = self.get_chart_logic_type(chart)
            data_mode = chart.get("fixed_data_mode")
            if data_mode == ChartDataMode.ManualInput.value:
                continue
            if logic_type == "nondataset" or not logic_type:
                continue
            if not self._check_chart_source(result, chart_id, chart_name):
                continue
            indicators = chart.get("data", {}).get("indicator", [])
            self._check_indicators(result, indicators, chart, field_dict, name_map)
            chart_vars = indicators.get("chart_vars", [])
            self._assign_value_source(chart_vars, var_dict)
            data = self._query_data(self._metadata_storage.get_dashboard_id(), chart, chart_vars)
            health = ResultStatus.UnHealthy.value if data.get("execute_status") != 200 else ResultStatus.Warning.value
            check_flag, check_msg = self._check_chart_data(data, chart)
            if not check_flag and check_msg:
                self.add_check_msg(result, chart_id, chart_name, 1006, health, *[chart_name, check_msg])
            elif not check_flag and not check_msg:
                self.add_check_msg(result, chart_id, chart_name, 1017, health, *[chart_name])

        return result

    def _check_chart_data(self, data, chart):
        """
        巡检chart_data
        :param data:
        :param chart:
        :return:
        """
        if not data:
            return False, ""
        chart_data = data.get(chart.get("id"))
        if not chart_data:
            return False, ""
        if isinstance(chart_data, dict) and not chart_data.get("data") and chart_data.get("msg"):
            return False, f"错误信息[ {chart_data.get('msg', '')} ] , 查询sql[ {chart_data.get('sql', '')} ]"
        # 兼容标签筛选组件的data返回数据是list类型
        elif isinstance(chart_data, list):
            for data_item in chart_data:
                if not data_item:
                    return False, ""
                if not data_item.get("data") and data_item.get("msg"):
                    return False, f"错误信息[ {data_item.get('msg', '')} ] , 查询sql[ {data_item.get('sql', '')} ]"
        return True, ""

    def _check_chart_source(self, result, chart_id, chart_name):
        """
        巡检单图的数据集来源
        :param result:
        :param chart_id:
        :param chart_name:
        :return:
        """
        check_result = True
        datasource = self._metadata_storage.get_chart_source_by_chart_id(chart_id)
        try:
            dataset_data = self._metadata_storage.get_single_dataset_data(datasource)
        except UserError:
            dataset_data = None
        if not dataset_data:
            self.add_check_msg(result, chart_id, chart_name, 1018, ResultStatus.UnHealthy.value, chart_name)
            check_result = False
        return check_result

    def _check_indicators(self, result, indicators, chart, field_dict, name_map):
        """
        巡检indicators数据
        :param result:
        :param indicators:
        :param chart:
        :param field_dict:
        :param name_map:
        :return:
        """
        chart_id = chart.get("id")
        chart_name = chart.get("name")
        for name, indicator in indicators.items():
            for single_item in indicator:
                if not single_item:
                    continue
                datset_field = single_item.get("dim") or single_item.get("num") or single_item.get("dataset_field_id")
                logger.debug("chart:%s relation:%s field:%s", chart_name, name, datset_field)
                if datset_field and datset_field not in field_dict:
                    self.add_check_msg(
                        result,
                        chart_id,
                        chart_name,
                        1015,
                        ResultStatus.UnHealthy.value,
                        *[
                            chart_name,
                            name_map[name],
                            self._metadata_storage.get_field_name_from_metadata(datset_field) or "",
                        ],
                    )
