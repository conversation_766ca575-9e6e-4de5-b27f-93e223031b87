#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401
import re

from pandas import DataFrame
from pandas import merge as df_merge
import datetime
import json

from base import repository
from dmplib.utils.strings import seq_id
from base.enums import DatasetType, FlowInstanceStatus, DatasetConnectType, NodeCheckerCategory
from dataset.services import dataset_service
from healthy.repositories import healthy_node_repository, healthy_repository
from healthy.services.healthy_node import CheckingResult
from healthy.services.healthy_node_recorder import NodeCheckerRecorder
from .healthy_node import AbsNode<PERSON>hecker
from base.enums import HealthyCheckResultStatus as ResultStatus

# sql 执行超过5秒，提示。超过30秒，错误。
SQL_EXECUTE_WARM = 5
SQL_EXECUTE_ERROR = 30

DATASET_CHECK_STRUCTURE = 'check_structure'


class DatasetNodeChecker(AbsNodeChecker):
    """
    数据集检查节点
    """

    def __init__(
        self, project: str, dashboard_id: str, instance_id: str, metadata_storage, recorder: NodeCheckerRecorder = None
    ):
        self.code = 'dataset'
        self.recorder = recorder
        super().__init__(self.code, project, dashboard_id, instance_id, metadata_storage)

    def _execute(self) -> CheckingResult:
        # test dataset
        datasets = healthy_node_repository.get_dataset_ids_by_dashboard_id(self.dashboard_id)
        # 如果有记录器, 并且记录了数据源已经被巡检过, 跳过巡检
        if self.recorder:
            datasets = [
                ds for ds in datasets if not self.recorder.is_checked(NodeCheckerCategory.Dataset.value, ds.get('id'))
            ]
        result = CheckingResult()
        result.status = ResultStatus.Healthy.value
        error_count = warn_count = 0
        for dataset in datasets:
            if self.recorder:
                self.recorder.mark_checked(NodeCheckerCategory.Dataset.value, dataset.get('id'))
            _dict, check_status = self._check_single_dataset(dataset)
            # 2:错误, 1:警告, 0:正常, -1:待检查
            status = max(check_status)
            result.check_status.extend(check_status)
            error_count += check_status.count(ResultStatus.UnHealthy.value)
            warn_count += check_status.count(ResultStatus.Warning.value)
            result.status = status if status > result.status else result.status
            result.rules.append(_dict)
        result.summary = self.generate_summary(error_count, warn_count)
        return result

    def _check_single_dataset(self, dataset, deep=0, check_status=None) -> (dict, int):
        _dict = dict()
        _dict["id"] = dataset.get("id")
        _dict["name"] = dataset.get("name")
        _dict["type"] = dataset.get("type")
        _dict["check_items"] = []
        if not check_status:
            check_status = []
        # 组合数据集
        # 避免组合数据集无限嵌套
        if dataset.get("type") == DatasetType.Union.value and deep == 0:
            union_datasets = self._get_union_dataset_ids(dataset)
            result = []
            for union_dataset in union_datasets:
                union_dict, result_status = self._check_single_dataset(union_dataset, deep=1, check_status=check_status)
                result.append(union_dict)
                check_status.extend(result_status)
            _dict["result"] = result

        # sql语句执行时间过长(limit 100 跟测试运行同一个接口)
        check_execute_time = self._check_execute_time(dataset.get("id"))
        _dict["check_items"].append(check_execute_time)
        check_status.append(check_execute_time.get("status"))

        # api/直连数据集/外部主题数据集不检查flow
        if (
            dataset.get('type') not in [DatasetType.ExternalSubject.value]
            and dataset.get('connect_type') != DatasetConnectType.Directly.value
        ):
            check_instance_status = self._check_instance_status(dataset.get("id"))
            _dict["check_items"].append(check_instance_status)
            check_status.append(check_instance_status.get("status"))

            check_instance_duration = self._check_instance_duration(dataset.get("id"))
            _dict["check_items"].append(check_instance_duration)
            check_status.append(check_instance_duration.get("status"))

        # 数据集字段结构变化巡检
        check_structure = self._check_structure(dataset.get("id"))
        _dict["check_items"].append(check_structure)
        check_status.append(check_structure.get("status"))

        # todo 添加其他校验
        return _dict, check_status

    @staticmethod
    def _get_union_dataset_ids(dataset: dict) -> list:
        """
        获取组合数据集关联
        :param dataset_id:
        :return:
        """
        content = (
            json.loads(dataset.get("content")) if isinstance(dataset.get("content"), str) else dataset.get("content")
        )
        source_dataset_ids = content.get("source_dataset_ids", [])
        if source_dataset_ids:
            return healthy_node_repository.get_dataset_by_ids(source_dataset_ids)
        return []

    @staticmethod
    def _check_execute_time(dataset_id: str) -> dict:
        begin_time = datetime.datetime.now()
        try:
            dataset_service.get_dataset_result_data(dataset_id)
        except Exception as e:
            msg = "sql语句执行错误，错误信息{}".format(str(e))
            status = ResultStatus.UnHealthy.value
            return {"status": status, "msg": msg}
        end_time = datetime.datetime.now()
        execute_time = (end_time - begin_time).seconds
        if execute_time > 30:
            msg = "sql语句执行时间过长，执行时间{}秒".format(execute_time)
            status = ResultStatus.Warning.value
        else:
            msg = "sql语句执行时间正常，执行时间{}秒".format(execute_time)
            status = ResultStatus.Healthy.value
        return {"status": status, "msg": msg, "func_name": "execute_time"}

    @staticmethod
    def is_subject_table(dataset_id: str):
        if repository.data_is_exists("dataset_subject_table", {"dataset_id": dataset_id}):
            result = healthy_repository.get_dataset_subject(dataset_id)
            return result
        return {}

    def _check_instance_status(self, dataset_id: str) -> dict:
        """
        检查最新一次流程状态
        :param dataset_id:
        :return:
        """
        # 检测是否为主题包类型数据库
        subject = self.is_subject_table(dataset_id)
        if subject and subject.get("id"):
            instance = healthy_node_repository.get_last_instance(subject.get("id"))
        else:
            instance = healthy_node_repository.get_last_instance(dataset_id)
        if not instance:
            status = ResultStatus.Warning.value
            message = "数据集流程未执行。"
        elif instance.get("status") == FlowInstanceStatus.Failed.value:
            status = ResultStatus.UnHealthy.value
            message = instance.get("message")
        elif instance.get("status") == FlowInstanceStatus.Successful.value:
            status = ResultStatus.Healthy.value
            message = (
                instance.get("message")
                if instance.get("message")
                else "数据集同步成功 同步时间：{}".format(str(instance.get("end_time")))
            )
        else:
            status = ResultStatus.Warning.value
            message = instance.get("message")
        return {
            "status": status,
            "msg": message,
            "flow_instance_id": instance.get("id") if instance else "",
            "subject_id": subject.get("id") if subject else '',
            "subject_name": subject.get("name") if subject else '',
            "func_name": "instance_status",
        }

    def _check_instance_duration(self, dataset_id: str) -> dict:
        """
        检查最新一次流程执行时长
        :param dataset_id:
        :return:
        """
        # 检测是否为主题包类型数据库
        subject = self.is_subject_table(dataset_id)
        if subject and subject.get("id"):
            instance = healthy_node_repository.get_last_instance(subject.get("id"))
        else:
            instance = healthy_node_repository.get_last_instance(dataset_id)
        if not instance:
            status = ResultStatus.Warning.value
            message = "数据集流程未执行。"
        elif instance.get("end_time") and instance.get("startup_time"):
            execute_time = (instance.get("end_time") - instance.get("startup_time")).seconds
            if execute_time > 60:
                message = "调度流程执行时间过长，执行时间{}秒".format(execute_time)
                status = ResultStatus.Warning.value
            else:
                message = "调度流程执行时间正常，执行时间{}秒".format(execute_time)
                status = ResultStatus.Healthy.value
        else:
            status = ResultStatus.Warning.value
            message = "流程执行没有开始时间或结束时间，无法计算执行时长。"
        return {
            "status": status,
            "msg": message,
            "flow_instance_id": instance.get("id") if instance else "",
            "subject_id": subject.get("id") if subject else '',
            "subject_name": subject.get("name") if subject else '',
            "func_name": "instance_duration",
        }

    def _check_structure(self, dataset_id: str) -> dict:
        """
        检查数据集结构变化
        :param dataset_id: 数据集ID
        :return:
        """
        # 1. 获取指定dataset_id的所有当前待检查的dataset_field
        fields_dataset = (
            'id',
            'alias_name',
            'col_name',
            'data_type',
            'visible',
            'field_group',
            'type',
            'group_type',
            'expression',
            'expression_advance',
        )

        dataset_fields_curr = healthy_node_repository.get_dataset_field_by_dataset_id(dataset_id, fields_dataset)
        # 2. 获取上一次数据集结构检查结果
        fields_diff = ('id', 'status', 'latest_struct', 'latest_diff')
        dataset_struct_diff = healthy_node_repository.get_dataset_struct_diff(dataset_id, fields_diff)
        # 3. 对比，记录差异项及差异值
        if not dataset_struct_diff:
            # 此数据集第一次做报告巡检，保存数据status, latest_struct，latest_diff，返回正常
            dataset_struct_diff = {
                'id': seq_id(),
                'dataset_id': dataset_id,
                'status': ResultStatus.Healthy.value,
                'latest_struct': json.dumps(dataset_fields_curr),
                'latest_diff': '',
            }
            healthy_node_repository.add_dataset_struct_diff(dataset_struct_diff)
            return {"status": ResultStatus.Healthy.value, "msg": '数据集字段无变化', "func_name": DATASET_CHECK_STRUCTURE}
        # 对比
        dataset_fields_latest = json.loads(dataset_struct_diff['latest_struct'])
        df_dataset_fields_latest = DataFrame(dataset_fields_latest)
        df_dataset_fields_curr = DataFrame(dataset_fields_curr)
        df_dataset_fields_latest_curr = df_merge(
            df_dataset_fields_latest,
            df_dataset_fields_curr,
            how='outer',
            left_on='id',
            right_on='id',
            suffixes=('_latest', '_curr'),
        )
        # 空字符串填充NA值
        df_dataset_fields_latest_curr.fillna('', inplace=True)

        # id字段不做对比
        for fld in fields_dataset[1:]:
            df_dataset_fields_latest_curr[fld + '_diff'] = df_dataset_fields_latest_curr[
                [fld + '_latest', fld + '_curr']
            ].apply(self.diff, axis=1, fld=fld)

        diffs = []
        for fld in fields_dataset[1:]:
            d = df_dataset_fields_latest_curr[df_dataset_fields_latest_curr[fld + '_diff'] != ''][
                ['id', 'alias_name_curr', 'alias_name_latest', fld + '_diff']
            ]
            if d.empty:
                continue
            diffs_fld = list(d.T.to_dict().values())
            for diff_fid in diffs_fld:
                diff_fid['field'] = fld
                diffs.append(diff_fid)

        if len(diffs) == 0:
            # 没有新的改变
            return {"status": ResultStatus.Healthy.value, "msg": '数据集字段无变化', "func_name": DATASET_CHECK_STRUCTURE}

        dataset_struct_diff_id = dataset_struct_diff.pop('id')
        dataset_struct_diff['latest_struct'] = json.dumps(dataset_fields_curr)
        dataset_struct_diff['latest_diff'] = json.dumps(diffs)
        healthy_node_repository.update_dataset_struct_diff(dataset_struct_diff, {'id': dataset_struct_diff_id})

        msgs = []
        all_senior_field = {}
        try:
            col_name_fields = {field.get("col_name"): field for field in dataset_fields_latest}
            senior_fields = [_ for _ in dataset_fields_curr if _.get("expression_advance")]
            self._get_senior_field_list(senior_fields, col_name_fields, all_senior_field)

        # 检测高级字段变更，个别老数据可能出错，忽略。
        except:
            pass
        for diff in diffs:
            field = diff['field']
            alias_name = diff.get('alias_name_curr') or diff.get('alias_name_latest')
            msg = '字段：%s, %s' % (alias_name, diff.get(field + '_diff'))
            msgs.append(msg)
            # 判断高级字段的引用是否删除
            if diff.get(field + '_diff').find("删除字段:") != -1 and diff.get("id") in all_senior_field.keys():
                msg = '字段：%s 中所引用字段 %s 被删除' % (
                    all_senior_field.get(diff.get("id")).get("senior_alias_name"),
                    alias_name,
                )
                msgs.append(msg)
        msg = '\n'.join(msgs)

        return {
            "status": ResultStatus.Warning.value,
            "msg": '数据集字段结构有改变: \n' + msg,
            "func_name": DATASET_CHECK_STRUCTURE,
        }

    def diff(self, row, fld):
        """
        判断：新增、修改、删除
        :param row:
        :param fld:
        :return:
        """
        fields_dataset_commons = {
            'alias_name': '字段名',
            'col_name': '内部字段名',
            'data_type': '字段类型',
            'visible': '可见性',
            'field_group': '字段分组',
            'type': '类型',
            'group_type': '分组类型',
            'expression': '表达式（旧）',
            'expression_advance': '表达式',
        }
        latest, curr = row[fld + '_latest'], row[fld + '_curr']
        if fld == 'visible':
            # 对visible字段做转义
            latest = '可见' if latest else '不可见'
            curr = '可见' if curr else '不可见'
        return self._check_and_get_result(latest, curr, fld, fields_dataset_commons)

    @staticmethod
    def _check_and_get_result(latest, curr, fld, fields_dataset_commons):
        if curr and not latest:
            # 当前存在，上次不存在：新增
            if fld == 'alias_name':
                return '新增字段: %s' % curr
            else:
                return ''
        elif not curr and latest:
            # 当前不存在，上次存在：删除
            if fld == 'alias_name':
                return '删除字段: %s' % latest
            else:
                return ''
        elif curr and latest:
            # 当前存在，上次存在，判断是否修改
            if latest == curr:
                # 没有修改
                return ''
            return '改变项: %s, 原值: %s, 当前值: %s' % (fields_dataset_commons[fld], latest, curr)
        else:
            # 当前与上次都不存在（不可能发生）
            return ''

    @staticmethod
    def _get_senior_field_list(senior_fields, col_name_fields, all_senior_field):
        for senior_field in senior_fields:
            all_col_name = re.findall(r'[\[](.*?)[\]]', senior_field.get('expression_advance'), re.S)
            for col_name in all_col_name:
                field = col_name_fields.get(col_name)
                field['senior_alias_name'] = senior_field.get("alias_name")
                all_senior_field[field.get("id")] = field
