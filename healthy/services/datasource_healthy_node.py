import json
import time
import logging

from data_source.models import DataSourceModel
from data_source.services import data_source_service
from data_source.services.data_source_service import load_data_source_conn_str
from healthy.repositories import healthy_node_repository
from healthy.services.healthy_node import CheckingResult
from healthy.services.healthy_node_recorder import NodeCheckerRecorder
from .healthy_node import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from base.enums import HealthyCheckResultStatus as ResultStatus, NodeCheckerCategory

logger = logging.getLogger(__name__)


class DatasourceNodeChecker(AbsNodeChecker):
    """
    数据源检查节点
    """

    def __init__(
        self, project: str, dashboard_id: str, instance_id: str, metadata_storage, recorder: NodeCheckerRecorder = None
    ):
        self.code = 'datasource'
        self.recorder = recorder
        super().__init__(self.code, project, dashboard_id, instance_id, metadata_storage)

    def _execute(self) -> CheckingResult:
        # test data_source
        # 获取报告关联数据集
        dataset_contents = healthy_node_repository.get_dataset_contents_by_dashboard_id(self.dashboard_id)
        # 获取数据源信息
        data_sources = []
        for dataset_content in dataset_contents:
            content = (
                json.loads(dataset_content.get("content"))
                if isinstance(dataset_content.get("content"), str)
                else dataset_content.get("content")
            )
            if content.get("data_source_id"):
                data_sources.append(content.get("data_source_id"))
        # 去重
        data_sources = list(set(data_sources))

        # 如果有记录器, 并且记录了数据源已经被巡检过, 跳过巡检
        if self.recorder:
            data_sources = [
                ds for ds in data_sources if not self.recorder.is_checked(NodeCheckerCategory.DataSource.value, ds)
            ]
        if not data_sources:
            result = CheckingResult()
            result.status = ResultStatus.Healthy.value
            return result
        # 获取配置
        connection_configs = healthy_node_repository.get_datasource_config(data_sources)

        result = CheckingResult()
        result.status = ResultStatus.ToCheck.value
        for data_source in connection_configs:
            if self.recorder:
                self.recorder.mark_checked(NodeCheckerCategory.DataSource.value, data_source.get('id'))
            _dict = dict()
            _dict["id"] = data_source.get("id")
            _dict["name"] = data_source.get("name")
            _dict["type"] = data_source.get("type")
            model = DataSourceModel(**data_source)
            load_data_source_conn_str(model)
            count = 3
            status = ResultStatus.Healthy.value
            msg = "数据源连接成功。"
            while count > 0:
                try:
                    data_source_service.test_connection(model)
                    status = ResultStatus.Healthy.value
                    msg = "数据源连接成功。"
                    count = 0
                except Exception as e:
                    # pylint: disable=E1101
                    msg = "{name}数据源连接失败。失败原因：{message}。".format(name=model.name, message=str(e))
                    status = ResultStatus.Warning.value
                    count -= 1
                    logging.exception("连接失败，开始重试！")
                    time.sleep(60)
            _dict["check_items"] = [{"status": status, "msg": msg, "func_name": "test_connect"}]
            result.check_status.append(status)
            result.status = status if status > result.status else result.status
            result.rules.append(_dict)
        error_count = result.check_status.count(ResultStatus.UnHealthy.value)
        warn_count = result.check_status.count(ResultStatus.Warning.value)
        result.summary = self.generate_summary(error_count, warn_count)
        return result
