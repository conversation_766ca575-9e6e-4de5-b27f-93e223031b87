#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/1/14 10:11
# <AUTHOR> caoxl
# @File     : dashboard_metadata_storage.py
import functools
from dataset import external_query_service as dataset_service
from dmplib.utils.errors import UserError
from base import repository
import json

# pylint: disable-msg=R0904
class DashboardMetadataStorage:
    def __init__(self, dashboard_metadata):
        if not isinstance(dashboard_metadata, dict):
            dashboard_metadata = json.loads(dashboard_metadata)
        self._dashboard_metadata = dashboard_metadata
        if isinstance(dashboard_metadata, str):
            self._dashboard_metadata = json.loads(dashboard_metadata)
        self._first_report = self.get_first_report()
        self._chart_relations = self.get_chart_relations()
        self._dashboard_healthy_result = self._get_dashboard_healthy_result(self.get_dashboard_id())

    def get_first_report(self):
        """
        从元数据获取第一个报告
        :return:
        """
        return self._dashboard_metadata.get("first_report", {})

    def get_dashboard_name(self):
        first_report = self.get_first_report()
        if first_report:
            return first_report.get("name", "")
        return None

    def get_chart_relations(self):
        if not self._first_report:
            return {}
        return self._first_report.get("chart_relations", {})

    def get_charts(self):
        """
        从元数据中获取chart列表
        :return:
        """
        return self._first_report.get("charts", [])

    @staticmethod
    def get_dataset(dataset_id):
        """
        获取指定数据集
        :param dataset_id:
        :return:
        """
        try:
            if not dataset_id:
                return None
            return dataset_service.get_dataset(dataset_id)
        except UserError:
            return None

    @functools.lru_cache()
    def get_sub_penetrates_charts(self):
        """
        获取所有的穿透子图
        :return:
        """
        if not self._chart_relations:
            return []
        penetrates = self._chart_relations.get("penetrates")
        if not penetrates:
            return []
        return filter(lambda item: item.get("parent_id"), penetrates)

    @functools.lru_cache()
    def get_datasets(self):
        """
        从元数据中获取报告使用的所有数据集
        :return:
        """
        charts = self.get_charts()
        dataset_ids = set()
        datasets = []
        for chart in charts:
            if not chart.get("data").get("datasource"):
                continue
            dataset_id = chart.get("data").get("datasource").get("id")
            if not dataset_id:
                continue
            dataset_ids.add(dataset_id)
        for dataset_id in dataset_ids:
            dataset = self.get_dataset(dataset_id)
            if not dataset:
                continue
            # datasets.append({"id": dataset.get("id"), "name": dataset.get("name"), "type": dataset.get("type")})
            datasets.append(dataset)
        return datasets

    def get_dashboard_id(self):
        return self._dashboard_metadata.get("first_report", {}).get("id", "")

    def get_new_filter_chart(self):
        """
        获取新版的联动数据
        """
        if not self._chart_relations:
            return []
        return self._chart_relations.get("chart_filters", [])

    def get_new_linkage_chart(self):
        """
        获取新版的联动数据
        """
        if not self._chart_relations:
            return []
        return self._chart_relations.get("chart_linkages", [])

    def get_filter_chart(self):
        if not self._chart_relations:
            return []
        return self._chart_relations.get("filters", [])

    def get_linkage_chart(self):
        if not self._chart_relations:
            return []
        return self._chart_relations.get("linkages", [])

    def get_dashboard(self):
        """
        从元数据获取dashboard
        :return:
        """
        return self._dashboard_metadata.get("dashboard", {})

    def get_penetrate_data(self):
        """
        获取报告元数据中的单图穿透
        :return:
        """
        return self._chart_relations.get("penetrates", [])

    def get_redirect_data(self):
        """
        获取报告元数据中的跳转
        :return:
        """
        if not self._chart_relations:
            return []
        return self._chart_relations.get("redirects", [])

    def get_dashboard_filter_data(self):
        """
        获取报告元数据中的跳转
        :return:
        """
        return self._first_report.get("dashboard_filters", [])

    def get_var_value_sources_data(self):
        """
        获取报告元数据中的取值来源数据
        :return:
        """
        return self._first_report.get("var_value_sources", [])

    def get_chart_data_by_chart_id(self, chart_id):
        """
        获取元数据中单个单图数据
        :param chart_id:
        :return:
        """
        charts = self.get_charts()
        for single_chart in charts:
            if single_chart.get("id") == chart_id:
                return single_chart
        return {}

    def get_chart_source_by_chart_id(self, chart_id):
        """
        获取source
        :param chart_id:
        :return:
        """
        chart_data = self.get_chart_data_by_chart_id(chart_id)
        if not chart_data:
            return ""
        data = chart_data.get("data")
        if not data:
            return ""
        datasource = data.get("datasource")
        return datasource.get("id") if datasource else ""

    @staticmethod
    def get_single_dataset_data(dataset_id):
        """
        批量获取数据集数据
        :param dataset_id:
        :return:
        """
        if not dataset_id:
            return dict()
        return dataset_service.get_dataset(dataset_id)

    @functools.lru_cache()
    def batch_get_chart_data(self):
        """
        批量获取单图数据，包含对应数据集的信息
        :return:
        """
        chart_data_dict = dict()
        charts = self.get_charts()
        for single_chart in charts:
            # 获取单图对应数据集的信息
            datasource_data = single_chart["data"]["datasource"]
            dataset_id = datasource_data.get("id")
            single_chart["dataset_info"] = dataset_service.get_dataset(dataset_id) if dataset_id else {}
            if single_chart.get("id"):
                chart_data_dict[single_chart.get("id")] = single_chart
        return chart_data_dict

    @staticmethod
    def batch_get_field_data_by_ids(field_id_list):
        """
        批量获取数据集字段数据
        :param field_id_list:
        :return:
        """
        # 过滤为空的字段id
        if not isinstance(field_id_list, list) or not field_id_list:
            return list()
        return dataset_service.get_multi_dataset_fields([i for i in field_id_list if i])

    @staticmethod
    def _get_dashboard_healthy_result(dashboard_id):
        instance = repository.get_data(
            "healthy_dashboard_instance",
            {"dashboard_id": dashboard_id},
            fields=["id", "state_details", "start_time"],
            order_by=[("start_time", "desc")],
        )
        if not instance:
            return {}

        instance_id = instance.get("id", "")
        result_node = repository.get_data(
            "healthy_dashboard_result_node",
            {"instance_id": instance_id, "node_code": "dashboard"},
            fields=[
                "id",
                "instance_id",
                "dashboard_id",
                "node_code",
                "status",
                "summary",
                "result",
                "start_time",
                "end_time",
            ],
            order_by=[("start_time", "desc")],
        )
        if not result_node:
            return {}
        result = result_node.get("result")
        return json.loads(result) if result else {}

    def get_dashboard_healthy_result(self):
        return self._dashboard_healthy_result

    def get_chart_healthy_msgs(self, chart_id, status):
        msgs = []
        for name, checker_info in self._dashboard_healthy_result.items():
            if not name.endswith("_checker"):
                continue
            for result_chart_id, result in checker_info.items():
                if result_chart_id != chart_id:
                    continue
                for r in result.get("result", []):
                    if r["status"] == status:
                        msgs.append(r["msg"])
        return msgs

    @functools.lru_cache()
    def get_field_name_from_metadata(self, field_id):
        charts = self.get_charts()
        for chart in charts:
            indicators = chart.get("data", {}).get("indicator", [])
            for indicator in indicators.values():
                field_name = self.match_field_name(indicator, field_id)
                if field_name:
                    return field_name
        return None

    @staticmethod
    def match_field_name(indicator, field_id):
        """
        匹配字段名称
        :param indicator:
        :param field_id:
        :return:
        """
        field_name = None
        for single_item in indicator:
            if not single_item:
                continue
            field_name = single_item.get("alias_name") or single_item.get("alias") or single_item.get("col_name")
            dataset_field = single_item.get("dataset_field_id") or single_item.get("dim") or single_item.get("num")
            if dataset_field and dataset_field == field_id:
                return field_name
        return field_name

    def get_all_charts_dataset_fields(self) -> list:
        charts = self.get_charts()
        fields = set()
        for chart in charts:
            indicators = chart.get("data", {}).get("indicator", [])
            for indicator in indicators.values():
                for single_item in indicator:
                    if not single_item:
                        continue
                    dataset_field_id = (
                        single_item.get("dataset_field_id") or single_item.get("dim") or single_item.get("num")
                    )
                    if dataset_field_id:
                        fields.add(dataset_field_id)
        return list(fields)

    @functools.lru_cache()
    def get_chart_name_from_metadata(self, chart_id):
        charts = self.get_charts()
        for chart in charts:
            if chart.get("id") == chart_id:
                return chart.get("name")

        return None
