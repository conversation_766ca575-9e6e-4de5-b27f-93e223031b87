# pylint: disable=E0401
"""
报告健康检查业务逻辑
"""
import functools
import asyncio
import os

import ujson as json
import datetime
import concurrent.futures as cf
import traceback
import logging


import app_celery
from base import repository
from base.enums import FlowStatus
from components.rundeck import RundeckScheduler
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from healthy.models import HealthyQueryModel
from healthy.repositories import healthy_repository, healthy_node_repository
from healthy.services.dataset_healthy_node import Dataset<PERSON>ode<PERSON>hecker
from healthy.services.datasource_healthy_node import <PERSON>sour<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .healthy_node import Abs<PERSON><PERSON><PERSON><PERSON><PERSON>, CheckingResult
from .dashboard_charts_map.dashboard_charts_map import DashboardChartsMap
from .dashboard_datasets_map.dashboard_datasets_map import DashboardDatasetsMap
from dmplib.hug.context import DBContext
from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
from dashboard_chart import external_service as dashboard_chart_service
from .dashboard_metadata_storage import DashboardMetadataStorage
from base.enums import HealthyCheckResultStatus as ResultStatus
from healthy.services.dashboard_healthy_check.dashboard_healthy_node import DashboardHealthyNode
from dashboard_chart.services import metadata_extend_service

logger = logging.getLogger(__name__)


def handle_g(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        parent_thread_g = args[-1]
        thread_local_g = _AppCtxGlobals()
        thread_local_g.code = parent_thread_g.code
        thread_local_g.account = getattr(parent_thread_g, "account", None)
        thread_local_g.userid = getattr(parent_thread_g, "userid", None)
        thread_local_g.cookie = getattr(parent_thread_g, "cookie", None)
        _app_ctx_stack.push(thread_local_g)
        # inject db
        db_ctx = DBContext()
        db_ctx.inject(thread_local_g)
        try:
            return func(*args, **kwargs)
        finally:
            db_ctx.close_all()
            _app_ctx_stack.pop()

    return wrapper


class DashboardChecker:
    def __init__(self, dashboard_id, is_released=1, dashboard_metadata=None):
        self.dashboard_id = dashboard_id
        self.is_released = is_released
        self.dashboard_metadata = dashboard_metadata
        self.checkers = []
        self.instance_id = seq_id()

    def add_node_checker(self, node_checker: AbsNodeChecker):
        self.checkers.append(node_checker)

    def before_check(self):
        # 1. insert healthy_dashboard_instance start_time
        repository.update_data(
            "healthy_dashboard",
            {"status": ResultStatus.Run.value, "start_time": datetime.datetime.now(), "end_time": None},
            {"dashboard_id": self.dashboard_id},
        )

        repository.add_data(
            "healthy_dashboard_instance",
            {
                "id": self.instance_id,
                "dashboard_id": self.dashboard_id,
                "status": ResultStatus.Run.value,
                "is_released": self.is_released,
                "dashbaord_metadata": json.dumps(self.dashboard_metadata),
                "start_time": datetime.datetime.now(),
            },
        )

    def after_check(self, result: CheckingResult, dashboard_instance_id: str):
        # update healthy_dashboard_instance  end_time
        # 还需更新主表状态
        repository.update_data(
            "healthy_dashboard",
            {
                "end_time": datetime.datetime.now(),
                "status": result.status,
                "summary": result.summary,
                "state_details": json.dumps(result.check_status),
            },
            {"dashboard_id": self.dashboard_id},
        )

        repository.update_data(
            "healthy_dashboard_instance",
            {
                "end_time": datetime.datetime.now(),
                "status": result.status,
                "summary": result.summary,
                "state_details": json.dumps(result.check_status),
            },
            {"id": dashboard_instance_id, "dashboard_id": self.dashboard_id},
        )
        # 清除最近14天的历史记录
        healthy_repository.clear_check_logs()

    def check_sync(self):
        """
        串行调用checker
        :return:
        """
        self.before_check()
        result = CheckingResult()

        node_status = []
        node_summary = []

        for node in self.checkers:
            try:
                node_result = node.check()
            except Exception as e:
                logger.error("%s检查出错,%s", node.code, traceback.format_exc())
                node_result = CheckingResult()
                node_result.status = ResultStatus.UnHealthy.value
                node_result.check_status = [ResultStatus.UnHealthy.value]
                node_result.summary = str(e)
            if node_result:
                node_status.extend(node_result.check_status)
                node_summary.append(node_result.summary)

        result.status = max(node_status) if len(node_status) else ResultStatus.Healthy.value
        result.check_status = node_status
        result.summary = ",".join(node_summary)

        self.after_check(result, self.instance_id)

    def check(self):
        """
        异步执行节点巡检过程
        :return:
        """
        self.before_check()
        node_status = []
        node_summary = []
        # total = len(self.checkers)
        total = 2

        @handle_g
        def do_check(node, _=None):
            try:
                node_result = node.check()
            except Exception as e:
                node_result = CheckingResult()
                node_result.status = ResultStatus.UnHealthy.value
                node_result.check_status = [ResultStatus.UnHealthy.value]
                node_result.summary = str(e)

            return {"status": node_result.check_status, "summary": node_result.summary}

        async def execute():
            with cf.ThreadPoolExecutor(max_workers=total) as executor:
                loop = asyncio.get_event_loop()
                futures = []
                for i in range(total):
                    future = loop.run_in_executor(executor, do_check, self.checkers[i], _app_ctx_stack.top)
                    futures.append(future)

            for result in await asyncio.gather(*futures):
                node_status.extend(result["status"])
                node_summary.append(result["summary"])

        loop = asyncio.get_event_loop()
        loop.run_until_complete(execute())

        result = CheckingResult()
        # 默认是健康
        result.status = max(node_status) if node_status else ResultStatus.Healthy.value
        result.check_status = node_status
        result.summary = ",".join(node_summary)
        self.after_check(result, self.instance_id)


def dashboard_healthy_checker(project_code: str, dashboard_id: str, instance_id, metadata_storage):
    """
    注册单图相关的checker
    :param project_code:
    :param dashboard_id:
    :param instance_id:
    :param metadata_storage:
    :return:
    """
    checker_nodes = [
        "dashboard_filters_checker",
        "dashboard_relation_checker",
        "new_filters_checker",
        "new_linkages_checker",
        "penetrates_checker",
        "redirects_checker",
        "charts_checker",
    ]
    dashboard_healthy_node = DashboardHealthyNode(project_code, dashboard_id, instance_id, metadata_storage)
    dashboard_healthy_node.attach_checkers(checkers=checker_nodes, dashboard_id=dashboard_id, instance_id=instance_id)
    return dashboard_healthy_node


def start_dashboard_healthy(project_code: str, dashboard_id: str, is_released=1, recorder=None):
    """
    启动报告巡检
    :param project_code: 租户code
    :param dashboard_id: 报告id
    :param is_released: 报告是否发布. 0:未发布, 1:已发布
    :return:
    """
    # TODO 后期单图添加：Node Checker
    dashboard_metadata = get_dashboard_metadata(dashboard_id, is_released)
    metadata_storage = DashboardMetadataStorage(dashboard_metadata)
    dashboard_check = DashboardChecker(dashboard_id, is_released=is_released, dashboard_metadata=dashboard_metadata)
    datasource_node_check = DatasourceNodeChecker(
        project_code, dashboard_id, dashboard_check.instance_id, metadata_storage, recorder
    )
    dashboard_check.add_node_checker(datasource_node_check)
    dataset_node_check = DatasetNodeChecker(
        project_code, dashboard_id, dashboard_check.instance_id, metadata_storage, recorder
    )
    dashboard_check.add_node_checker(dataset_node_check)
    dashboard_check.add_node_checker(
        dashboard_healthy_checker(project_code, dashboard_id, dashboard_check.instance_id, metadata_storage)
    )
    dashboard_check.check_sync()
    # 查看报告巡检结果,如有错误,上传logstore
    try:
        check_health_result(dashboard_id)
    except:
        # 只记录日志，不影响流程
        logger.exception("上传报告巡检结果到logstore失败")


def check_health_result(dashboard_id: str):
    result = repository.get_one("healthy_dashboard", {"dashboard_id": dashboard_id}, fields=["state_details"])
    if not result:
        return
    # 查询报告名称
    dashboard_name = repository.get_value("dashboard", {"id": dashboard_id}, field=["name"])
    if not dashboard_name:
        return
    state_details = result.get("state_details")
    if isinstance(state_details, str):
        state_details = json.loads(state_details)
    error_count = state_details.count(2)
    if error_count:
        current_domain = config.get('Domain.dmp')
        # 报告巡检结果查看地址
        dashboard_health_url = f'{current_domain}/healthy/report/{dashboard_id}'

        contents = list()
        contents.append(("env_code", str(os.environ.get('CONFIG_AGENT_CLIENT_CODE', '未知'))))
        contents.append(('project_code', getattr(g, 'code', '')))
        contents.append(('dashboard_id', dashboard_id))
        contents.append(('dashboard_name', dashboard_name))
        contents.append(('count', str(error_count)))
        contents.append(('url', dashboard_health_url))
        contents.append(('msg', f'【{dashboard_name}】存在{error_count}条失败项,详情{dashboard_health_url}。'))


def run_healthy(dashboard_id: str, is_released=1):
    """
    异步执行报告巡检
    :param dashboard_id: 报告id
    :param is_released: 报告是否发布. 0:未发布, 1:已发布
    :return:
    """
    # TODO 后期单图补充报告是否发布、报告元数据快照，目前is_released=1 ， dashboard_metadata=""

    app_celery.healthy_dashboard.apply_async(
        kwargs={"project_code": g.code, "data_id": dashboard_id, "is_released": is_released}
    )

    return dashboard_id


def update_healthy_config(model, rundeck=False):
    # 判断 healthy_dashboard 表是否有值
    if not repository.data_is_exists("healthy_dashboard", {"dashboard_id": model.dashboard_id}):
        repository.add_data("healthy_dashboard", {"dashboard_id": model.dashboard_id})
    if model.enabled is not None and model.schedule is not None:
        # enbale 0：关闭 1：开启
        # 保存 dev 能保存配置，先不注册rundeck
        if rundeck:
            # 赋值id，方便后面Rundeck更新任务
            model.id = model.dashboard_id
            model.name = model.dashboard_id
            model.description = ""
            if model.enabled:
                model.status = FlowStatus.Enable.value
                # 注册定时任务
                scheduler = RundeckScheduler(model)
                if scheduler.job_is_exists():
                    scheduler.delete_job()
                scheduler.add_job(command=get_command(model.dashboard_id))
            else:
                model.status = FlowStatus.Disable.value
                # 删除定时任务
                RundeckScheduler(model).delete_job()
            del model.id
            del model.name
            del model.description
            model.status = None
        repository.update_model("healthy_dashboard", model, {"dashboard_id": model.dashboard_id})
        return list_dashboard_healthy(HealthyQueryModel(**{"dashboard_id": model.dashboard_id}))
    else:
        return {}


def get_command(dashboard_id, queue_name='celery'):
    """
    获取rundeck执行celery的command命令
    :param dashboard_id:
    :return:
    """
    celery_task_name = "app_celery.healthy_dashboard"
    cmd_template_feed = config.get(
        "Rundeck.cmd_template_celery", "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
    )
    command = "%s %s %s %s %s" % (cmd_template_feed, g.code, celery_task_name, dashboard_id, queue_name)
    return command


def get_dashboard_healthy_status(dashboard_id):
    dashboard_healthy = repository.get_data("healthy_dashboard", {"dashboard_id": dashboard_id}, fields=["status"])
    return dashboard_healthy.get("status") if dashboard_healthy else "2"


def list_dashboard_healthy(query_model: HealthyQueryModel):
    results = healthy_repository.get_healthy_list(query_model).get_result_dict()
    # 处理state_details
    items = results.get("items")
    for item in items:
        state_details_json_str = item.get("state_details")
        if not state_details_json_str:
            item["state_details"] = []
            continue

        try:
            item["state_details"] = json.loads(state_details_json_str)
        except Exception as e:
            item["state_details"] = []
            logger.exception(e)

    return results


def get_dashboard_charts_healthy(result_node):
    """
    获取报告单图间错误流程结构
    """
    result = {}
    dashboard_healthy = result_node.get("result")
    if not dashboard_healthy:
        return []

    d_healthy_dict = json.loads(dashboard_healthy)
    for key, values in d_healthy_dict.items():
        if key == "info":
            continue
        assign_result(values, result)

    logger.info("get_dashboard_charts_healthy return:%s", result)
    return list(filter(lambda x: x["check_items"], result.values()))


def assign_result(values, result):
    for chart_id, chart_result in values.items():
        if chart_id in result:
            single_chart = result[chart_id]
        else:
            single_chart = {"id": chart_id, "check_items": []}
            result[chart_id] = single_chart
        for r in chart_result.get("result", []):
            if r.get("status") in (ResultStatus.UnHealthy.value, ResultStatus.Warning.value):
                single_chart["name"] = chart_result.get("name")
                single_chart["check_items"].append(
                    {"func_name": "chart_check", "status": r.get("status"), "msg": r.get("msg", "")}
                )


def get_dashboard_healthy(dashboard_id: str, instance_id=None) -> dict:
    healthy_dashborad_list = healthy_repository.get_healthy_list(
        HealthyQueryModel(**{"dashboard_id": dashboard_id})
    ).get_result_dict()
    if not healthy_dashborad_list.get("items"):
        raise UserError(message=u"暂无巡检报告")
    healthy_dashborad = healthy_dashborad_list.get("items")[0]
    instance = {}
    # 默认查询最新实例
    if not instance_id:
        instance = repository.get_data(
            "healthy_dashboard_instance",
            {"dashboard_id": dashboard_id},
            fields=["id", "state_details"],
            order_by=[("start_time", "desc")],
        )
        if not instance:
            raise UserError(message=u"暂无巡检报告")
        instance_id = instance.get("id")
    result_nodes = repository.get_data(
        "healthy_dashboard_result_node",
        {"instance_id": instance_id},
        fields=[
            "id",
            "instance_id",
            "dashboard_id",
            "node_code",
            "status",
            "summary",
            "result",
            "start_time",
            "end_time",
        ],
        multi_row=True,
    )

    state_details = []
    for result_node in result_nodes:
        if result_node.get("node_code") != "dashboard":
            result_node["result"] = (
                json.loads(result_node.get("result"))
                if result_node.get("result") and isinstance(result_node.get("result"), str)
                else result_node.get("result")
            )
            state_details.extend(deal_result_nodes(result_node))
        elif result_node.get("node_code") == "dashboard":
            result_node["result"] = get_dashboard_charts_healthy(result_node)
            state_details.extend(deal_result_nodes(result_node))

    healthy_dashborad["result_nodes"] = result_nodes
    healthy_dashborad["state_details"] = state_details
    return healthy_dashborad


# 临时方案处理计算流程巡检中错误总数和警告数
def deal_result_nodes(result_node):
    state_details = []
    if isinstance(result_node.get("result"), list):
        for node in result_node.get("result"):
            state_details.extend([item.get("status") for item in node.get("check_items")])
            # 组合数据集
            if isinstance(node.get("result"), list):
                for _node in node.get("result"):
                    state_details.extend([item.get("status") for item in _node.get("check_items")])
    return state_details


def get_dashboard_metadata(dashboard_id, is_released):
    """
    获取报告元数据
    :param dashboard_id: 报告ID
    :param is_released: 报告是否发布
    :return:
    """
    metadata = dashboard_chart_service.get_dashboard_metadata_without_comment(dashboard_id, is_released)
    # 临时方案：补充巡检需要用而元数据缺少的数据
    metadata = metadata_extend_service.assign_metadata(metadata, is_released)
    return metadata


def assign_map(charts_map, instance):
    """
    补充map数据
    :param charts_map:
    :param instance:
    :return:
    """
    if not charts_map:
        return charts_map
    dashboard = charts_map.get("dashboard")
    if not dashboard:
        return charts_map
    charts_map["dashboard"]["start_time"] = instance.get("start_time")
    return charts_map


def get_dashboard_charts_map(instance_data):
    """
    根据报告元数据生成单图关系
    :param instance_data:
    :return:
    """
    dashboard_metadata = instance_data.get("dashbaord_metadata")
    # 注册报告相关的校验器
    chart_node_generators = [
        "filters_generator",
        "linkages_generator",
        "new_filters_generator",
        "new_linkages_generator",
        "penetrates_generator",
        "redirects_generator",
    ]
    if not dashboard_metadata:
        raise UserError(message="获取当前报告信息失败")

    map_obj = DashboardChartsMap(dashboard_metadata=dashboard_metadata)
    map_obj.attach_chart_node_generators(chart_node_generators)

    charts_map = map_obj.generate_charts_map()
    dashboard_map = map_obj.generate_dashboard_map()
    charts_map["dashboard"] = dashboard_map

    # 补充instance表数据
    charts_map = assign_map(charts_map, instance_data)

    return charts_map


def get_dashboard_charts_map_from_instance(instance_id):
    """
    通过巡检实例获取单图关系
    :param instance_id:
    :return:
    """
    instance = healthy_node_repository.get_instance_by_id(instance_id)
    if not instance:
        raise UserError(message="实例不存在！")
    return get_dashboard_charts_map(instance)


def get_dashboard_datasets_map(instance_data):
    """
    根据报告元数据生成数据集关系
    :param instance_data:
    :return:
    """
    dashboard_metadata = instance_data.get("dashbaord_metadata")
    chart_node_generators = [
        "linkages_generator",
        "filters_generator",
        "new_filters_generator",
        "new_linkages_generator",
        "penetrates_generator",
        "redirects_generator",
    ]
    if not dashboard_metadata:
        raise UserError(message="获取当前报告信息失败")

    map_obj = DashboardDatasetsMap(dashboard_metadata=dashboard_metadata)
    map_obj.attach_dataset_node_generators(chart_node_generators)

    datasets_map = map_obj.generate_datasets_map()
    dashboard_map = map_obj.generate_dashboard_map()
    dashboard_map["errors"]["errcnt"] = 0  # 目前数据集没有存储错误信息
    datasets_map["dashboard"] = dashboard_map

    # 补充instance表数据
    datasets_map = assign_map(datasets_map, instance_data)

    return datasets_map


def get_dashboard_datasets_map_from_instance(instance_id):
    """
    通过巡检实例获取数据集关系
    :param instance_id:
    :return:
    """
    instance = healthy_node_repository.get_instance_by_id(instance_id)
    if not instance:
        raise UserError(message="实例不存在！")
    return get_dashboard_datasets_map(instance)


def get_dashboard_latest_instance(dashboard_id):
    """
    通过报告ID获取报告的最新的巡检实例
    :param dashboard_id:
    :return:
    """
    latest_instance = healthy_repository.get_dashboard_latest_instanct(dashboard_id)
    if not latest_instance:
        raise UserError(message="该报告未完成任何巡检，无法获取巡检结果！")
    return latest_instance


def get_dashboard_relation(instance_id):
    """
    获取报告间关系
    :param instance_id:
    :return:
    """
    data = repository.get_data("healthy_dashboard_instance", {"id": instance_id}, ["dashboard_relation"])
    if not data:
        return {}
    dashboard_relation = data.get("dashboard_relation")
    if not dashboard_relation:
        return {}
    return json.loads(dashboard_relation)


def check_dashboard_config(dashboard_id):
    from healthy.services.dashboard_healthy_check.dashboard_config_check import DashboardConfigChecker
    dashboard_config_check = DashboardConfigChecker(dashboard_id)
    return dashboard_config_check.check()
