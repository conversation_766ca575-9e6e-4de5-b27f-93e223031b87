#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.saas.project import get_db


def insert_dashboard_healthy_node():
    pass


def get_dataset_contents_by_dashboard_id(dashboard_id: str):
    sql = """SELECT DISTINCT(dataset.content)
      FROM dashboard_chart  INNER JOIN dataset ON dashboard_chart.source = dataset.id
        WHERE dashboard_chart.dashboard_id = %(dashboard_id)s and
        dataset.type not in ("EXCEL", "UNION");"""

    with get_db() as db:
        return db.query(sql, {"dashboard_id": dashboard_id})


def get_dataset_ids_by_dashboard_id(dashboard_id: str):
    sql = """SELECT DISTINCT(dataset.id), dataset.name, dataset.type, dataset.content, dataset.connect_type
        FROM dashboard_chart
        INNER JOIN dataset ON dashboard_chart.source = dataset.id
        WHERE dashboard_chart.dashboard_id = %(dashboard_id)s;"""

    with get_db() as db:
        return db.query(sql, {"dashboard_id": dashboard_id})


def get_dataset_by_ids(dataset_ids: list):
    sql = """select id, name, type, content from dataset where id in %(dataset_ids)s;"""
    with get_db() as db:
        return db.query(sql, {"dataset_ids": dataset_ids})


def get_datasource_config(data_source_ids: list):
    sql = """select id, name, type, conn_str, is_buildin from data_source where id in %(data_source_ids)s;"""
    with get_db() as db:
        return db.query(sql, {"data_source_ids": data_source_ids})


def get_instance_by_id(instance_id):
    sql = """SELECT `id`,`dashboard_id`,`is_released`,`dashbaord_metadata`,`status`,`summary`,`start_time`, `end_time`
    FROM `healthy_dashboard_instance`
    WHERE `id`=%(instance_id)s"""
    params = {"instance_id": instance_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_last_instance(flow_id):
    """
    获取最后一次流程实例
    :return:
    """
    sql = """
        select id, status, message, startup_time, end_time from instance
        where flow_id = %(flow_id)s
        ORDER BY  created_on desc limit 1;
        """
    with get_db() as db:
        return db.query_one(sql, {'flow_id': flow_id})


def get_dataset_field_by_dataset_id(dataset_id: str, fields: tuple):
    sql_select = 'SELECT `%s`' % '`,`'.join(fields)
    sql_from = 'FROM dataset_field'
    sql_where = 'WHERE dataset_id = %(dataset_id)s'
    sql = ' '.join([sql_select, sql_from, sql_where])
    with get_db() as db:
        return db.query(sql, {'dataset_id': dataset_id})


def get_dataset_struct_diff(dataset_id: str, fields: tuple):
    sql_select = 'SELECT `%s`' % '`,`'.join(fields)
    sql_from = 'FROM healthy_dataset_struct_diff'
    sql_where = 'WHERE dataset_id = %(dataset_id)s'
    sql = ' '.join([sql_select, sql_from, sql_where])
    with get_db() as db:
        return db.query_one(sql, {'dataset_id': dataset_id})


def add_dataset_struct_diff(data: dict):
    with get_db() as db:
        return db.insert('healthy_dataset_struct_diff', data)


def update_dataset_struct_diff(data: dict, condition: dict):
    with get_db() as db:
        return db.update('healthy_dataset_struct_diff', data, condition)
