#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from dmplib.saas.project import get_db
from base.enums import DashboardJumpType, DashboardJumpConfigStatus


def get_healthy_list(query_model):
    """
    :param flow.models.HealthyQueryModel query_model:
    :return tuple:
    """
    sql = """select DISTINCT(hd.dashboard_id),dr.name, dr.modified_on as 'release_time', user.name,
             hd.schedule,hd.status,hd.state_details,
             hd.options,hd.summary,hd.start_time,
             hd.end_time,hd.enabled from healthy_dashboard hd
  INNER JOIN dashboard_released_snapshot_dashboard dr on hd.dashboard_id = dr.snapshot_id
  INNER JOIN user on dr.modified_by = user.account """

    sql_toal = """select COUNT(DISTINCT(hd.dashboard_id)) from healthy_dashboard hd
  INNER JOIN dashboard_released_snapshot_dashboard dr on hd.dashboard_id = dr.snapshot_id
  INNER JOIN user on dr.modified_by = user.account """

    params = {}
    wheres = []
    if query_model.keyword:
        wheres.append("( hd.`summary` LIKE %(keyword)s ")
        params["keyword"] = "%" + query_model.keyword_escape + "%"
    if query_model.status:
        wheres.append("( hd.`status` = %(status)s )")
        params["status"] = query_model.status
    if query_model.name:
        wheres.append("( dr.`name` LIKE %(name)s )")
        params["name"] = "%" + query_model.name.replace("_", "\\_") + "%"
    if query_model.begin_date:
        wheres.append("TO_DAYS(hd.`start_time`)>=TO_DAYS(%(begin_date)s)")
        params["begin_date"] = query_model.begin_date
    if query_model.end_date:
        wheres.append("TO_DAYS(hd.`start_time`)<=TO_DAYS(%(end_date)s)")
        params["end_date"] = query_model.end_date
    if query_model.dashboard_id:
        wheres.append("( hd.`dashboard_id` = %(dashboard_id)s )")
        params["dashboard_id"] = query_model.dashboard_id

    # 默认查看查看已发布报告
    wheres.append("( dr.`data_type` = %(dr_data_type)s )")
    params["dr_data_type"] = "1"
    wheres.append("( dr.`is_multiple_screen` = %(dr_is_multiple_screen)s )")
    params["dr_is_multiple_screen"] = "0"
    wheres.append("( dr.`status` = %(dr_status)s )")
    params["dr_status"] = "1"
    wheres.append("( dr.`application_type` = %(application_type)s )")
    params["application_type"] = "0"
    sql += ("WHERE " + " AND ".join(wheres)) if wheres else ""
    sql_toal += ("WHERE " + " AND ".join(wheres)) if wheres else ""
    order_by = ""
    sql += order_by or " ORDER BY hd.`start_time` DESC"
    sql += " LIMIT " + str(query_model.skip) + "," + str(query_model.page_size)
    with get_db() as db:
        query_model.items = db.query(sql, params)
        query_model.total = db.query_scalar(sql_toal, params)
    return query_model


def get_dashboard_charts_name(dashboard_id):
    """
    获取报告下所有单图的名字
    """
    sql = "select id, name from dashboard_chart where dashboard_id = %s"
    with get_db() as db:
        return db.query(sql, [dashboard_id])


def get_dataset_name_by_dashboardid(dashboard_id):
    """
    批量获取数据集名称
    """
    sql = (
        "select ds.id, ds.name from dataset ds inner join "
        "dashboard_chart dc on dc.source = ds.id where dc.dashboard_id = %s"
    )
    with get_db() as db:
        return db.query(sql, [dashboard_id])


def get_dashboard_latest_instanct(dashboard_id):
    """
    获取报告的最近一次巡检的实例
    :param dashboard_id:
    :return:
    """
    sql = (
        "SELECT "
        "`id`,`dashboard_id`,`is_released`,`dashbaord_metadata`,`status`,`state_details`,`summary`,`start_time`,"
        "`end_time`,`dashboard_relation` "
        "FROM `healthy_dashboard_instance` "
        "WHERE `dashboard_id`=%(dashboard_id)s "
        "ORDER BY end_time DESC limit 1"
    )
    params = {"dashboard_id": dashboard_id}
    with get_db() as db:
        return db.query_one(sql, params)


def batch_get_dashboard_info(dashboard_ids: list):
    """
    批量获取报告信息
    :param dashboard_ids:
    :return:
    """
    sql = """select id,name,cover from dashboard where id in %(dashboard_ids)s"""
    with get_db() as db:
        return db.query(sql, {"dashboard_ids": dashboard_ids})


def get_jump_dashboards(target_dashboard_id):
    """

    :param target_dashboard_id:
    :return:
    """
    sql = """select distinct(d.id) as id,d.name,d.cover,d.create_type,
          djc.source_id,djc.dashboard_id,djc.source_type
          from dashboard_jump_config djc
          inner join dashboard d on djc.dashboard_id=d.id
          where djc.target = %(dashboard_id)s and djc.target_type=%(target_type)s and djc.status=%(status)s"""
    with get_db() as db:
        return db.query(
            sql,
            {
                "dashboard_id": target_dashboard_id,
                "target_type": DashboardJumpType.Dashboard.value,
                "status": DashboardJumpConfigStatus.Valid.value,
            },
        )


def get_param_jump_dashboards(target_dashboard_id):
    """

    :param target_dashboard_id:
    :return:
    """
    sql = """select distinct(d.id) as id,d.name,d.cover,d.create_type,
          dcpj.param_dataset_field_id as dataset_field_id
          from dashboard_filter df
          inner join dashboard_chart_params_jump dcpj on df.id=dcpj.dashboard_filter_id
          inner join dashboard d on dcpj.dashboard_id=d.id
          where df.dashboard_id=%(dashboard_id)s"""
    with get_db() as db:
        return db.query(sql, {"dashboard_id": target_dashboard_id, "target_type": DashboardJumpType.Dashboard.value})


def batch_get_dashboards(target_dashboard_ids):
    """

    :param target_dashboard_ids:
    :return:
    """
    sql = """select id,name,cover,create_type from dashboard where id in %(target_dashboard_ids)s"""
    with get_db() as db:
        return db.query(sql, {"target_dashboard_ids": target_dashboard_ids})


def save_dashboard_relation_data(instance_id, dashboard_id, data):
    """
    保存生成后的报告间数据
    :param instance_id:
    :param dashboard_id:
    :param data:
    :return:
    """
    with get_db() as db:
        return db.update(
            "healthy_dashboard_instance",
            {"dashboard_relation": data},
            {"id": instance_id, "dashboard_id": dashboard_id},
        )


def get_dataset_subject(dataset_id):
    sql = """ select dataset_subject.dataset_folder_id as id, dataset_subject.name from dataset_subject
        LEFT JOIN dataset_subject_table on dataset_subject.id = dataset_subject_table.dataset_subject_id
    where dataset_subject_table.dataset_id= %(dataset_id)s;
    """
    with get_db() as db:
        return db.query_one(sql, {"dataset_id": dataset_id})


def clear_check_logs():
    sqls = (
        """
    DELETE FROM healthy_dashboard_instance WHERE DATE(start_time) < DATE_SUB( CURRENT_DATE(), INTERVAL 14 DAY );
    """,
        """
    DELETE FROM healthy_dashboard_result_node WHERE DATE(start_time) < DATE_SUB( CURRENT_DATE(), INTERVAL 14 DAY );
    """,
    )
    with get_db() as db:
        for sql in sqls:
            db.exec_sql(sql)
