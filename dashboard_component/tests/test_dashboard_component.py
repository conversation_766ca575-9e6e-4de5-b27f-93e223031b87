# -*- coding: utf-8 -*-
# pylint: skip-file
"""
Created on 2017年6月16日

@author: chenc04
"""
# pylint: skip-file

import os
import unittest
import logging
from dashboard_component.models import DashboardComponentModel
from dashboard_component.services import component_service
from tests.base import BaseTest

logger = logging.getLogger(__name__)


class TestDashboardComponentService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='test')

    def test_install_dashboard_component(self):
        os.environ['CONFIG_AGENT_CLIENT_CODE'] = 'test'
        kwargs = {"package": "adjustable_image_demo", "version": "0.0.0"}
        model = DashboardComponentModel(**kwargs)
        result = component_service.install_component(model)
        print(result)

    def test_get_components(self):
        from dashboard_chart.services import components_service

        components = components_service.get_editor_components("")
        print(components)

    def test_auto_upgrade(self):
        from dashboard_chart.services import components_service

        msg = components_service.auto_upgrade_components()
        print(msg)

    def test_component_init(self):
        is_init, msg = component_service.init_components()
        print(is_init, msg)


if __name__ == '__main__':
    unittest.main()
