#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401

"""
    @desc component service
    @date 2018年4月4日
    <AUTHOR>
"""
import json
import os

import copy

import logging
import traceback
import uuid
import curlify

import requests
import time
import jwt

from base.dmp_constant import COMPONENT_LOGIC_CODE_KEY, component_table_map
from base.enums import DashboardComponentOperation
from components import auth_util
from dashboard_component.models import DashboardComponentModel
from dmplib.utils.errors import UserError
from dmplib import config
from dmplib.db.mysql_wrapper import get_db
from dmplib.hug import g, debugger
from base import repository
from dashboard_component.repositories import dashboard_component_repository
from dmplib.redis import conn_custom_prefix as conn_redis
from dashboard_chart.utils import dashboard_cache
from datetime import datetime

from typing import Tuple

logger = logging.getLogger(__name__)
logger.setLevel(config.get("Log.level", "INFO"))
_debugger = debugger.Debug(__name__)

# MOP平台接口地址列表
MOP_URL_PATH_DICT = {
    'lists': '/openapi/v3/distribute/lists',
    'check': '/openapi/v3/distribute/check',
    'update_status': '/openapi/v3/distribute/update_status',
    'init': '/openapi/v3/distribute/init',
}


class ComponentJWTToken:
    """
    the jwt_token class of dashbaord_component
    """

    __instance = None

    def __init__(self):
        self.secret = config.get('JWT.component_secret')

    def __new__(cls):
        if ComponentJWTToken.__instance is None:
            ComponentJWTToken.__instance = object.__new__(cls)
        return ComponentJWTToken.__instance

    def create_token(self, data):
        """
        create component token by jwt
        :param data:
        :return:
        """
        if 'tenant' not in data or 'client' not in data:
            raise UserError(400, '缺少必要参数')
        # 临时兼容client_code不一致问题
        _get_real_client_for_test_env(data)
        data['time'] = int(time.time())
        token = jwt.encode(data, self.secret)
        return token

    def verify_token(self, token):
        """
        验证jwt的token是否正确
        :param token:
        :return:
        """
        if not token:
            return False
        try:
            data = jwt.decode(token, self.secret, algorithms="HS256")
            if not data:
                return False
            if 'time' not in data or 'tenant' not in data:
                return False
            now = int(time.time())
            timestamp = data.get('time')
            # 超过30分钟被视为失效
            if now - timestamp > 1800:
                return False
            return data
        except Exception:
            return False


class ComponentRequest:
    """
    request请求封装类
    """

    _headers = {}

    def __init__(self):
        """
        初始化必要（通用）参数
        """
        self.domain = config.get('MOP.domain')
        # self.is_gray = config.get("Grayscale.gray_env") or 0
        self.global_params = {
            'client': os.environ.get('CONFIG_AGENT_CLIENT_CODE') or 'dmp_test',
            'tenant': g.code,
            'time': int(time.time()),
            # 'is_gray': self.is_gray
        }
        self._headers['X-API-TOKEN'] = ComponentJWTToken().create_token(self.global_params)
        self.deployment = config.get('App.deployment', 'cloud')

    def _get_complate_headers(self, headers: dict):
        if not headers:
            return self._headers
        return headers.update(self._headers)

    def post(self, url, data: dict, headers: dict = None):
        """
        封装调用MOP的post请求
        :param str url: 去除掉域名后的url地址参数
        :param dict data: 需要传过去的参数
        :return: json的字符串格式数据
        """
        if self.deployment == 'local':
            return {'code': 500, 'msg': '本地环境不能访问mop'}
        params = self.global_params.copy()
        params.update(data)
        url = self.domain + url + f"?apikey={auth_util.get_common_secret()}"
        try:
            data = self.parse_response(
                requests.post(url, json=params, headers=self._get_complate_headers(headers), timeout=10)
            )
            return data
        except Exception as e:
            if not isinstance(e, UserError):
                logger.exception("使用POST调用MOP异常，异常信息: %s", str(e))
            return {'code': 500, 'msg': str(e)}

    def get(self, url, params: dict, headers: dict = None):
        """
        封装调用MOP的get请求
        :param str url: 去除掉域名后的url地址参数
        :param dict params: 需要传过去的参数
        :return: json的字符串格式数据
        """
        if self.deployment == 'local':
            return {'code': 500, 'msg': '本地环境不能访问mop'}
        url = self.domain + url + f"?apikey={auth_util.get_common_secret()}"
        args = self.global_params.copy()
        args.update(params)
        try:
            data = self.parse_response(
                requests.get(url, params=args, headers=self._get_complate_headers(headers), timeout=10)
            )
            return data
        except Exception as e:
            if not isinstance(e, UserError):
                logger.exception("使用GET调用MOP异常，异常信息: %s", str(e))
            return {'code': 500, 'msg': str(e)}

    @staticmethod
    def parse_response(r):
        """
        解析返回数据
        :param Response r: 返回的response对象
        :return:
        """
        try:
            data = json.loads(r.text)
            ComponentRequest.profiling_mop_request(response=r)
            return data
        except ValueError:
            raise UserError(message='解析http响应体失败，响应体内容:{content}'.format(content=r.text))

    @staticmethod
    def profiling_mop_request(response):
        try:
            profiling_data = curlify.to_curl(response.request, compressed=True)
            logger.info(f'MOP组件请求：{profiling_data}')
            _debugger.log({'MOP组件请求': profiling_data})
        except Exception as e:
            logger.error(f'MOP解析profiling失败： {str(e)}')


def get_distributions():
    """
    从组件中心获取用户需要新增、更新的组件列表
    :return:
    """
    url = MOP_URL_PATH_DICT.get('lists')
    data = ComponentRequest().get(url, {})
    if data.get('code') or data.get("message"):
        return False, data.get('msg') or data.get("message")
    component_list = data.get('data') or []
    return True, component_list


def clear_installed_components_cache():
    """
    删除已安装组件缓存
    :return:
    """
    cache_key = dashboard_cache.get_installed_components_cache_key()
    redis_cache = conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE'))
    return redis_cache.delete(cache_key)


def clear_component_logic_code_cache():
    conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).del_data(COMPONENT_LOGIC_CODE_KEY)


def install_component(model):
    """
    安装组件
    :param DashboardComponentModel model:
    :return:
    """
    url = MOP_URL_PATH_DICT.get('check')
    params = {
        'client': os.environ.get('CONFIG_AGENT_CLIENT_CODE'),
        'tenant': g.code,
        'package': model.package,
        'version': model.version,
        'operation': DashboardComponentOperation.Add.value,
    }
    # 临时兼容client_code不一致问题
    _get_real_client_for_test_env(params)
    try:
        data = ComponentRequest().post(url, params)
    except Exception as e:
        raise UserError(message=str(e))
    if data.get('code'):
        raise UserError(message='安装校验失败:' + data.get('msg'))
    #  1、安装信息以check返回结果为准
    #  2、判断本地是否有安装 若有安装 则更新mop中的状态即可
    # db_component = repository.get_data('component', {'package': model.package})
    check_result_data = data.get('data')
    check_result_data['status'] = 1  # 强制将状态设置为可用
    model_new = DashboardComponentModel(**check_result_data)
    if not lock_component_sync(model):
        return model_new
    result = 1
    dashboard_component_repository.replace_list_data(
        "component", [model_new.get_dict()], list(model_new.get_dict().keys())
    )
    if not result:
        raise UserError(message='抱歉，组件安装失败，请重试！')
    #  删除已安装组件缓存
    clear_installed_components_cache()
    # 删除data_logic_type_code 缓存
    clear_component_logic_code_cache()

    # 添加成功后，通知MOP更新数据状态
    update_data = {
        'client': os.environ.get('CONFIG_AGENT_CLIENT_CODE'),
        'tenant': g.code,
        'package': model_new.package,
        'version': model_new.version,
        'operation': DashboardComponentOperation.Add.value,
    }
    # 临时兼容client_code不一致问题
    _get_real_client_for_test_env(update_data)
    try:
        result, msg = update_component_status([update_data], DashboardComponentOperation.Add.value)
        if not result:
            raise UserError(message='抱歉，更新组件中心安装状态失败,错误信息:[{errorMsg}]，请重试！'.format(errorMsg=msg))
    except Exception as e:
        raise UserError(message=str(e))
    #  清理无用版本
    # dashboard_component_repository.clear_component_version(model_new.package, model_new.version)

    # 刷新组件最后更新时间，用于已发布报告元数据缓存
    refresh_component_update_on()

    return model_new


def update_component_status(lists, operation):
    """
    安装或者更新完组件后通知MOP更新组件状态
    :param DashboardComponentModel model:
    :return:
    """
    url = MOP_URL_PATH_DICT.get('update_status')
    for each in lists:
        each['operation'] = operation
    result = ComponentRequest().post(url, {'data': lists})
    return result.get('result'), result.get('msg')


def upgrade_component(model):
    """
    更新组件
    :param DashboardComponentModel model:
    :return:
    """
    url = MOP_URL_PATH_DICT.get('check')
    params = {
        'client': os.environ.get('CONFIG_AGENT_CLIENT_CODE'),
        'tenant': g.code,
        'package': model.package,
        'version': model.version,
        'operation': DashboardComponentOperation.Upgrade.value,
    }
    # 临时兼容client_code不一致问题
    _get_real_client_for_test_env(params)
    try:
        data = ComponentRequest().post(url, params)
    except Exception as e:
        raise UserError(message=str(e))
    if data.get('code'):
        raise UserError(message='更新校验失败:' + data.get('msg'))
    #  1、安装信息以check返回结果为准
    #  2、判断本地是否已经更新 如果没有则更新  最后通知mop平台
    check_result_data = data.get('data')
    check_result_data['status'] = 1  # 强制将状态设置为可用
    model_new = DashboardComponentModel(**check_result_data)
    if not lock_component_sync(model):
        return model_new
    dashboard_component_repository.replace_list_data(
        "component", [model_new.get_dict()], list(model_new.get_dict().keys())
    )
    #  删除已安装组件缓存
    clear_installed_components_cache()
    # 删除组件logic code 缓存
    clear_component_logic_code_cache()
    # 添加成功后，通知MOP更新数据状态
    update_data = {
        'client': os.environ.get('CONFIG_AGENT_CLIENT_CODE'),
        'tenant': g.code,
        'package': model_new.package,
        'version': model_new.version,
        'operation': DashboardComponentOperation.Add.value,
    }
    # 临时兼容client_code不一致问题
    _get_real_client_for_test_env(update_data)
    try:
        result, msg = update_component_status([update_data], DashboardComponentOperation.Upgrade.value)
        if not result:
            raise UserError(message='抱歉，更新组件中心安装状态失败,错误信息:[{errorMsg}]，请重试！'.format(errorMsg=msg))
    except Exception as e:
        raise UserError(message=str(e))

    # 刷新组件最后更新时间，用于已发布报告元数据缓存
    refresh_component_update_on()

    return model_new


def is_inited() -> bool:
    """
    判断当前环境的租户是否已经进行过初始化
    :return:
    """
    client = os.environ.get('CONFIG_AGENT_CLIENT_CODE')
    table_name = (
        component_table_map.get("component_init_log")
        if int(config.get("Grayscale.gray_env", 0))
        else "component_init_log"
    )
    data = repository.get_data(table_name, {'client': client}, from_config_db=True)
    if data:
        return True
    return False


def _mark_as_inited(packages: list):
    """
    标记已经初始化
    :param packages:
    :return:
    """
    client = os.environ.get('CONFIG_AGENT_CLIENT_CODE') or "dmp_test"
    data = {'client': client, 'packages': '' if len(packages) == 0 else ','.join(packages)}
    dashboard_component_repository.replace_list_data("component_init_log", [data], list(data.keys()))
    return True


def get_operation_word(operation):
    return {DashboardComponentOperation.Add.value: '安装', DashboardComponentOperation.Upgrade.value: '升级'}.get(
        operation, '回滚'
    )


def _auto_install_component(component, errors):
    try:
        install_component(DashboardComponentModel(**component))
    except Exception as e:
        if not isinstance(e, UserError):
            logger.exception(e)
        errors.append(
            '{component} {op_word} 失败:{msg}'.format(
                component=component.get('name'), op_word=get_operation_word(component.get('operation')), msg=str(e)
            )
        )
    return errors


def _auto_upgrade_component(component, errors):
    try:
        upgrade_component(DashboardComponentModel(**component))
    except Exception as e:
        if not isinstance(e, UserError):
            logger.exception(e)
        errors.append(
            '{component} {op_word} 失败:{msg}'.format(
                component=component.get('name'), op_word=get_operation_word(component.get('operation')), msg=str(e)
            )
        )
    return errors


def auto_upgrade_components():
    """
    自动升级组件
    :return:
    """
    # 错误列表
    errors = []
    if not conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).set_nx_ex(
        "auto_upgrade_components:lock", 1, ex=30, nx=True
    ):
        # 限制连接mop的频率
        return errors
    result, distributions = get_distributions()
    # 发生错误
    if not result:
        errors.append('获取分发记录失败:{msg}'.format(msg=str(distributions)))
        return errors
    # 需要新增的组件
    add_components = []
    # 需要安装的组件
    update_components = []
    for distribution in list(distributions):
        if distribution.get('operation') == DashboardComponentOperation.Add.value:
            add_components.append(distribution)
        elif distribution.get('operation') == DashboardComponentOperation.Upgrade.value:
            update_components.append(distribution)
    for component in add_components:
        errors = _auto_install_component(component, errors)
    for component in update_components:
        errors = _auto_upgrade_component(component, errors)

    return errors


def _build_init_data(component_data, installed_components, add_packages):
    update_data = []
    add_data = []
    for each in list(component_data):
        if each.get('package') not in installed_components:
            _temp = copy.deepcopy(each)
            # 安装的组件状态要为可用
            _temp['status'] = 1
            add_data.append(_temp)
            add_packages.append(_temp.get('package'))
        # 更新未安装状态的的分发 已安装的不用更新状态
        if each.get('status') == 0:
            update_data.append(
                {
                    'client': os.environ.get('CONFIG_AGENT_CLIENT_CODE'),
                    'tenant': g.code,
                    'package': each.get('package'),
                    'version': each.get('version'),
                    'operation': DashboardComponentOperation.Add.value,
                }
            )
    return add_data, update_data, add_packages


def init_components() -> Tuple[bool, str]:
    """
    初始化组件
    :return:
    """
    if is_inited():  # 已经初始化后跳过
        return True, ''
    url = MOP_URL_PATH_DICT.get('init')
    try:
        data = ComponentRequest().post(url, {})
    except Exception as e:
        logger.exception(e)
        return False, str(e)
    if data.get('code'):
        return False, data.get('msg')
    component_data = data.get('data')
    add_packages = []
    if not component_data:
        #  无系统内置和环境内置组件 标记为已经初始化 直接返回
        _mark_as_inited(add_packages)
        return True, ''
    # 添加数据到本地租户组件表
    # 兼容dmp中有组件 mop中没有的异常情况
    db_components = dashboard_component_repository.get_installed_components()
    installed_components = []
    for db_component in db_components:
        installed_components.append(db_component.get('package'))
    # 更新MOP中组件 及 添加组件到DMP
    add_data, update_data, add_packages = _build_init_data(component_data, installed_components, add_packages)
    if add_data:
        #  删除已安装组件缓存
        clear_installed_components_cache()
        if not dashboard_component_repository.replace_list_data(
            'component', add_data, list(DashboardComponentModel().get_dict().keys())
        ):
            return False, '初始化时安装组件失败，刷新页面后系统将再次执行初始化！'
    if update_data:
        #  删除已安装组件缓存
        clear_installed_components_cache()
        result, err_msg = update_component_status(update_data, DashboardComponentOperation.Add.value)
        if not result:
            return False, '更新组件中心状态发生错误，错误信息:[{errorMsg}], 请执行手动安装！'.format(errorMsg=err_msg)
    # 标记已经初始化
    _mark_as_inited(add_packages)

    # 刷新组件最后更新时间，用于已发布报告元数据缓存
    refresh_component_update_on()

    return True, ''


def lock_component_sync(model):
    """同时只允许一个进程做组件的同步(升级或安装)，所以这里要加锁"""
    lock_key = "dmp:component:lock_component_sync:%s:%s" % (model.package, model.version)
    return conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).set_nx_ex(lock_key, 1, ex=30, nx=True)


def refresh_component_update_on():
    """
    刷新组件更新时间
    :return:
    """
    now_time = datetime.now().strftime("%Y%m%d%H%M%S")
    update_on_key = dashboard_cache.get_component_update_on_cache_key()
    conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).set_data(update_on_key, now_time)
    return now_time


def get_component_update_on():
    """
    获取组件最后刷新时间
    :return:
    """
    # 尝试获取当前环境组件最后更新时间
    update_on_key = dashboard_cache.get_component_update_on_cache_key()
    component_update_on = conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).get_data(update_on_key)

    # 兼容初始场景，获取不到组件更新时间，则设置当前时间点为组件最后更新时间
    if not component_update_on:
        component_update_on = refresh_component_update_on()

    return component_update_on


def _get_real_client_for_test_env(data: dict):
    """
    兼容test环境环境代码不一致问题
    :param data:
    :return:
    """
    if data and data.get("client") and os.environ.get('CONFIG_AGENT_CLIENT_CODE') == "test":
        data["client"] = "test"


def acquire_component_lock(lock_name, time_out=30):
    identifier = str(uuid.uuid4())
    lock_key = "dmp:component:lock_component_sync_v2:%s" % lock_name
    if conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).set_nx_ex(lock_key, identifier, ex=time_out, nx=True):
        return identifier
    return False


def release_component_lock(lock_name, identifier):
    lock_key = "dmp:component:lock_component_sync_v2:%s" % lock_name
    lock_value = conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).get(lock_key)
    if not lock_value:
        return True
    if lock_value.decode() == identifier:
        conn_redis(os.environ.get('CONFIG_AGENT_CLIENT_CODE')).delete(lock_key)
        return True
    return False


def install_components(components):
    if not components:
        return False, '没有组件可供安装或更新', {}
    if not isinstance(components, list):
        return False, "可供安装的组件必须是列表形式", {}
    for component in components:
        # 强制设为可用
        component['status'] = 1
    try:
        with get_db() as db:
            fields = [
                'menu_id',
                'name',
                'icon',
                'navbar_icon',
                'layout',
                'package',
                'preview_image',
                'layout_preview_image',
                'description',
                'version',
                'data_logic_type_code',
                'is_build_in',
                'md5version',
                'md5RunTimeversion',
                'chart_type',
                'data_source_origin',
                'indicator_description',
                'indicator_rules',
                'sortable',
                'penetrable',
                'linkage',
                'can_linked',
                'has_zaxis',
                'has_desiredvalue',
                'dims_report_redirect',
                'nums_report_redirect',
                'contain_css',
                'contain_mapgallery',
                'runTerminal',
                'base_chart_lib',
                'extension',
                'status',
            ]
            component_table = get_component_table('component')
            res = db.replace_multi_data(component_table, components, fields=fields)
            msg = f"成功安装推送组件， 推送结果：{res}, 推送组件：{components}"
            logger.info(msg)
    except Exception as e:
        return False, f"安装组件异常，错误信息：{str(e)}", {}
    clear_installed_components_cache()
    return True, 'success', {}


def get_component_table(table_name):
    if int(config.get("Grayscale.gray_env", 0)):
        return component_table_map.get(table_name)
    return table_name
