#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    @desc component api route
    @date 2018年4月3 日
    <AUTHOR>
"""

from dashboard_component.models import DashboardComponentModel
from dashboard_component.services import component_service
from dmplib import config
from dmplib.hug import APIWrapper, g
from user_log.models import UserLogModel


api = APIWrapper(__name__)


@api.admin_route.get('/get_distributions')
def get_distributions():
    """
    从运营平台获取组件新增、更新列表
    :return:
    """
    distributions = component_service.get_distributions()
    return True, '', distributions


@api.admin_route.post('/install')
def install_components(request, **kwargs):
    """
    安装新增并分发下来的组件
    :param request:
    :param kwargs:
    :return:
    """
    model = DashboardComponentModel(**kwargs)
    result = component_service.install_component(model)
    UserLogModel.log_setting(
        request=request,
        log_data={'action': 'install_component', 'content': '组件 [ {component} ] 安装成功'.format(component=model.name)},
    )
    return True, '', result


@api.admin_route.post('/upgrade')
def upgrade_components(request, **kwargs):
    """
    更新分发下来的组件新版本
    :param request:
    :param kwargs:
    :return:
    """
    model = DashboardComponentModel(**kwargs)
    result = component_service.upgrade_component(model)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'upgrade_component',
            'content': '组件 [ {component} ] 版本升级至 [ {version} ] 成功。'.format(component=model.name, version=model.version),
        },
    )
    return True, '', result


@api.admin_route.post('/init')
def init_components():
    """
    初始化组件
    :return:
    """
    if int(config.get('Component.init_components')) == 1:
        result, msg = component_service.init_components()
        return result, msg
    else:
        return False, '未开启初始化功能，不能进行初始化'


@api.admin_route.get('/auto_upgrade')
def auto_upgrade_components():
    """
    自动升级组件
    :return:
    """
    errors = component_service.auto_upgrade_components()
    if errors:
        return False, '', '\r\n'.join(errors)
    return True, '', ''


@api.route.post('/components/install')
def components_install(**kwargs):
    """
    安装mop推送的组件
    :param kwargs:
    :return:
    """
    setattr(g, 'account', kwargs.get('account', 'mop_celery'))
    components = kwargs.get('components', [])
    return component_service.install_components(components)
