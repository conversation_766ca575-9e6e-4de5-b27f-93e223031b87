#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    @desc component service
    @date 2018年4月8日
    <AUTHOR>
"""
from base.models import BaseModel


class DashboardComponentModel(BaseModel):
    __slots__ = [
        'menu_id',
        'name',
        'icon',
        'navbar_icon',
        'layout',
        'package',
        'preview_image',
        'layout_preview_image',
        'description',
        'version',
        'data_logic_type_code',
        'is_build_in',
        'md5version',
        'md5RunTimeversion',
        'chart_type',
        'data_source_origin',
        'indicator_description',
        'indicator_rules',
        'sortable',
        'penetrable',
        'linkage',
        'can_linked',
        'has_zaxis',
        'has_desiredvalue',
        'dims_report_redirect',
        'nums_report_redirect',
        'contain_css',
        'contain_mapgallery',
        'runTerminal',
        'base_chart_lib',
        'extension',
    ]

    def __init__(self, **kwargs):
        self.menu_id = None
        self.name = None
        self.icon = None
        self.navbar_icon = None
        self.package = None
        self.layout = None
        self.preview_image = None
        self.layout_preview_image = None
        self.description = None
        self.version = None
        self.status = 0
        self.data_logic_type_code = None
        self.is_build_in = None
        self.chart_type = ''
        self.data_source_origin = 'none'
        self.indicator_description = ''
        self.indicator_rules = ''
        self.sortable = 0
        self.penetrable = 0
        self.linkage = 0
        self.can_linked = 0
        self.has_zaxis = 0
        self.has_desiredvalue = 0
        self.dims_report_redirect = 0
        self.nums_report_redirect = 0
        self.md5version = None
        self.md5RunTimeversion = None
        self.contain_css = 1
        self.contain_mapgallery = 0
        self.runTerminal = ""
        self.base_chart_lib = ""
        self.extension = ""
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('package', 'string'))
        rules.append(('version', "string", {'max': 128}))
        rules.append(('menu_id', 'string', {'max': 36, 'required': False}))
        rules.append(('is_build_in', 'int', {'required': False}))
        return rules
