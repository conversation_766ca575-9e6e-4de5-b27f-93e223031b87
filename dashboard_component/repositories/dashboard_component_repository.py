#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    dmp
    <NAME_EMAIL> on 2018/04/27.
"""
from base.dmp_constant import component_table_map
from dmplib import config
from dmplib.db.mysql_wrapper import get_db as get_master_db


def clear_component_version(package, version):
    sql = """
    DELETE FROM `{component}` WHERE package=%(package)s AND `version`!=%(version)s
    """
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component': component_table_map.get("component") if is_gray else 'component'})
    params = {'package': package, 'version': version}
    with get_master_db() as db:
        return db.exec_sql(sql, params)


def get_component_count():
    """
    获取组件表的组件个数
    :param user.models.UserLoginModel model:
    :return:
    """
    sql = 'SELECT count(*) as total from `{component}`'
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component': component_table_map.get("component") if is_gray else 'component'})
    with get_master_db() as db:
        return db.query_one(sql)


def get_installed_components():
    """
    获取已经安装的组件
    :return:
    """
    sql = """
    SELECT `menu_id`,`name`,`icon`,`package`,`chart_type`,`preview_image`,`description`,`version`,
    `data_logic_type_code`,`status`,`is_build_in`,`data_source_origin`,`indicator_description`,' '`indicator_rules`,
    `sortable`,`penetrable`,`linkage`,`can_linked`,`has_zaxis`,`has_desiredvalue`,' '`dims_report_redirect`,
    `nums_report_redirect`,`md5version`, md5RunTimeversion,`created_on`,`contain_css`,`contain_mapgallery`,`layout`,`layout_preview_image`,
    `navbar_icon`
    FROM `{component}` WHERE `status`=1
    """
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component': component_table_map.get("component") if is_gray else 'component'})
    with get_master_db() as db:
        return db.query(sql)


def batch_delete_components(packages):
    if not packages:
        return 0
    sql = "DELETE FROM `{component}` WHERE `package` in %(package)s "
    is_gray = int(config.get("Grayscale.gray_env", 0))
    sql = sql.format(**{'component': component_table_map.get("component") if is_gray else 'component'})
    return get_master_db().exec_sql(sql, {'package': packages})


def replace_list_data(table_name, list_data, fields, commit=True):
    """
    替换多行数据
    :param table_name:
    :param list_data:
    :param fields:
    :param commit:
    :return:
    """
    if not list_data or not isinstance(list_data, list):
        return False
    if int(config.get("Grayscale.gray_env", 0)):
        table_name = component_table_map.get(table_name)
    with get_master_db() as db:
        return db.replace_multi_data(table_name, list_data, fields, commit=commit) > 0
