#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import json
import logging
import traceback
from collections import defaultdict
from typing import List, Dict

from dmplib.saas.project import get_db, get_db_config
from dmplib import config
from dmplib.hug import g
from components.fast_logger import FastLogger
from components.utils import space_saving_json
from sync_data.services.sync_fast_rules import QueryDataRules
from sync_data.models import RuleModel
from dmplib.utils.errors import UserError
from base import repository
from components.utils import handle_g_auto, get_g_property
from components.date_utils import DateUtil
from dmplib.db.mysql_wrapper import SimpleMysql

FLAG_UUID = 'c1dbcd3c-fa61-4f61-a0d8-9af7784c1e78'


class QueryData:

    def __init__(self, code, batch_num, from_config_db=False):
        self.code = code
        self.batch_num = batch_num
        self.from_config_db = from_config_db
        self.db = self.get_db()

    def get_db(self):
        # if self.from_config_db:
        #     db = repository._get_db(from_config_db=True)
        # else:
        #     db = get_db(self.code)

        if self.from_config_db:
            db = SimpleMysql(
                host=config.get('DB.host'),
                port=int(config.get('DB.port')),
                db=config.get('DB.database'),
                user=config.get('DB.user'),
                passwd=config.get('DB.password'),
            )
        else:
            d_config = get_db_config(self.code)
            if not config:
                raise UserError(code=404, message='账号名或登录密码错误')
            db = SimpleMysql(
                host=d_config.get('host'),
                port=d_config['port'],
                db=d_config['database'],
                user=d_config['user'],
                passwd=d_config['password'],
            )
        return db

    def query_total(self, sql):
        total_sql = f"""SELECT count(1) FROM ({sql}) AS tmp"""
        return self.db.query_scalar(total_sql)

    def generate_sync_data(self, sql):
        """
        根据总数分页下载数据
        """
        total = self.query_total(sql)
        for idx, curr in enumerate(range(0, total, self.batch_num)):
            curr = idx * self.batch_num
            limit_sql = f"{sql} LIMIT {curr}, {self.batch_num}"
            datas = self.db.query(limit_sql)
            for data in datas:
                yield data

    def __del__(self):
        self.db.end()
        del self.db


class OLdDataAdapter:
    def adapt(self):
        exec_data = repository.get_one('upgrade_log', conditions={'id': FLAG_UUID}, from_config_db=True) or {}
        try:
            data = json.loads(exec_data.get('execute_msg'))
        except:
            data = {}
        has_exec = data.get('exec') or []
        created_on = exec_data.get('created_on') or ''
        for exec_type in has_exec:
            is_existed = repository.get_one('sync_data_out_record', conditions={'type': exec_type}, from_config_db=True)
            if not is_existed:
                # 将旧的数据格式迁移到新的记录表中
                if isinstance(created_on, datetime.datetime):
                    created_on = created_on.strftime('%Y-%m-%d')
                record = {'type': exec_type, 'sync_time': created_on}
                repository.add_data('sync_data_out_record', data=record, from_config_db=True)


class SyncDataToFast:
    """同步到天眼文件"""

    def __init__(self, assign_rules: List[Dict] = [], assign_code=''):
        self.assign_rules = assign_rules
        self.assign_code = assign_code
        self.batch_num = 1000
        self.threads = 4
        self.rules = QueryDataRules().load_rules()  # type: List[RuleModel]
        self.__check_has_init()
        self.__check()
        self.errors = defaultdict(list)
        OLdDataAdapter().adapt()

    def __check(self):
        for rule in self.rules:
            rule.validate()
        try:
            self.batch_num = int(config.get('Function.fast_sync_batch_num'))
        except:
            self.batch_num = 1000
        try:
            self.threads = int(config.get('Function.fast_sync_threads'))
        except:
            self.threads = 4

    def __check_has_init(self):
        """
        给rule添加一个全量还是增量的标记
        """
        if self.assign_rules:
            # 接口调用调试
            self.rules = [RuleModel(**rule) for rule in self.assign_rules]
        else:
            # 线上模式
            records = self.__load_exec_data()
            for rule in self.rules:
                if rule.type in records:
                    sync_time = records[rule.type].get('sync_time') or ''
                    if not sync_time:
                        # 可能得异常场景，但是概率很低
                        last_day = DateUtil().get_day(days=1)
                        setattr(rule, 'curr_mode', 'incremental')
                        setattr(rule, 'sync_time', last_day)
                        logging.error(f'异常场景，将会更新截止<{last_day}>的数据')
                    else:
                        setattr(rule, 'curr_mode', 'incremental')
                        setattr(rule, 'sync_time', sync_time)
                else:
                    setattr(rule, 'curr_mode', 'full')

    def __load_exec_data(self):
        records = repository.get_list(
            'sync_data_out_record', from_config_db=True, conditions={},
            fields=['id', 'type', 'sync_time', 'sync_type']
        ) or []
        return {record.get('type'): record for record in records}

    def check_project_db_is_exist(self, org_code):
        conf = get_db_config(code=org_code)
        if not conf.get('host') or not conf.get('port') or not conf.get('password'):
            return False
        else:
            return True

    def __format_logging_rule_data(self, rules):
        return str({rule.type: f"{getattr(rule, 'curr_mode', 'full')}{rule.sync_time}" for rule in rules})

    def sync_one_org_code(self, org_code, rules: List[RuleModel], from_config_db):
        """
        同步一个租户库/配置库的数据
        """
        g.code = org_code

        # 配置库的同步不需要检查租户数据
        if not from_config_db and not self.check_project_db_is_exist(org_code):
            logging.error(f'租户<{org_code}>的数据库配置为空，跳过...')
            return org_code, 0, 0

        f_data = self.__format_logging_rule_data(rules)
        if from_config_db:
            logging.error(f'开始写出配置库的同步文件，rule: {f_data}')
        else:
            logging.error(f'开始写出<{org_code}>同步文件，rule: {f_data}')

        batch_num = self.batch_num
        success, fail, errors = 0, 0, defaultdict(list)
        for rule in rules:
            curr_mode = getattr(rule, 'curr_mode', 'full')
            if curr_mode == 'incremental':
                sync_time = rule.sync_time
                if isinstance(sync_time, datetime.date):
                    sync_time = sync_time.strftime('%Y-%m-%d')
                sql = rule.incremental_sql % {'sync_time': sync_time}
            else:
                sql = rule.full_sql
            try:
                if from_config_db:
                    # 同步配置库的数据
                    query = QueryData(code='', batch_num=batch_num, from_config_db=True)
                    datas = query.generate_sync_data(sql)
                else:
                    # 同步租户库的数据
                    query = QueryData(code=org_code, batch_num=batch_num, from_config_db=False)
                    datas = query.generate_sync_data(sql)
                for data in datas:
                    self.sync_to_fast(data, type=rule.type, org_code=org_code)
                    success += 1

                del query
                del datas
            except Exception as e:
                errr_msg = self.__format_error(e, org_code, batch_num, rule, sql)
                logging.error(errr_msg)
                fail += 1
                self.errors[rule.type].append(errr_msg)

        return org_code, success, fail

    def __format_error(self, e, org_code, batch_num, rule, sql):
        """格式化租户的错误原因"""
        if '1054' in str(e):
            return f'{org_code}字段不存在, type: {rule.type}：{str(e)}'
        elif '1049' in str(e):
            return f'{org_code}数据库/租户不存在：{str(e)}'
        else:
            return f'同步数据失败, code: {org_code}, batch_num: {batch_num}, type: {rule.type}, sql: {sql}, 错误原因： {traceback.format_exc(limit=1)}'

    def generate_task(self, codes, config_rules, org_code_rules):
        # 先来租户库的
        if config_rules:
            yield '', config_rules, True
        # 再来租户库的
        if org_code_rules:
            for code in codes:
                yield code, org_code_rules, False

    def sync_all_org_code(self):
        """
        同步所有租户的数据
        """
        config_rules = [rule for rule in self.rules if rule.sync_type == 'config_type']
        org_code_rules = [rule for rule in self.rules if rule.sync_type == 'tenant_type']
        logging.error(
            f'开始处理数据写出到天眼文件，批量数：{self.batch_num}，线程数：{self.threads}')

        if not self.assign_code:
            org_codes = repository.get_columns('project', col_name='code', from_config_db=True, conditions={}) or []
            logging.error(f'总共找到 {len(org_codes)} 个租户')
        else:
            org_codes = [self.assign_code]
            logging.error(f'指定导出租户 {str(org_codes)} ')

        # # 1. 同步写出配置库的数据
        # self.sync_one_org_code('', config_rules, from_config_db=True)
        #
        # # 2. 同步写出租户库的数据
        # for org_code in org_codes:
        #     self.sync_one_org_code(org_code, org_code_rules, from_config_db=False)
        #     break

        g_kw = get_g_property()
        tasks = self.generate_task(org_codes, config_rules, org_code_rules)

        from concurrent.futures import ThreadPoolExecutor
        with ThreadPoolExecutor(self.threads) as executor:
            sync_one_org_code = handle_g_auto(self.sync_one_org_code)
            future_tasks = [executor.submit(sync_one_org_code, *task, **{'g': g_kw}) for task in tasks]
            success_result, fail_result = [], []
            for future in future_tasks:
                org_code, success, fail = future.result()
                success_result.append(success)
                fail_result.append(fail)

        logging.error(
            f'完成处理数据写出到天眼文件，处理成功数量: {sum(success_result)}, 失败数量: {sum(fail_result)}'
        )

        self.update_task_status()

    def update_task_status(self):
        """标记已经执行过的表数据"""
        for rule in self.rules:
            record = {
                'type': rule.type,
                'sync_type': rule.sync_type, 'sync_time': DateUtil().get_day(0),
            }
            curr_mode = getattr(rule, 'curr_mode', 'full')
            if curr_mode == 'incremental':
                sql = rule.incremental_sql.format(rule.sync_time)
            else:
                sql = rule.full_sql
            if rule.type not in self.errors:
                record['status'] = 3
                record['message'] = f'同步成功，模式：{curr_mode}，表名： {rule.type}, sql: {sql}， 日期： {rule.sync_time}'
            else:
                record['status'] = 2
                record['message'] = (
                    f'同步失败，模式：{curr_mode}，表名： {rule.type}, sql: {sql}，  日期： {rule.sync_time},'
                    f'详细原因：{space_saving_json(self.errors[rule.type])}'
                )

            is_existed = repository.get_one('sync_data_out_record', conditions={'type': rule.type}, from_config_db=True)
            if is_existed:
                repository.update_data('sync_data_out_record', condition={'type': rule.type}, data=record,
                                       from_config_db=True)
            else:
                repository.add_data('sync_data_out_record', data=record, from_config_db=True)

    def sync_to_fast(self, data, type, org_code):
        """
        写出天眼日志文件
        """
        if isinstance(data, (dict, list)):
            data = space_saving_json(data)
        elif not isinstance(data, (str,)):
            data = str(data)
        log_data = {
            "data": data,
            "type": type,
            "org_code": org_code
        }
        FastLogger.SyncDataFastLogger(**log_data).record()
