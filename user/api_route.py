#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401,W0613,E1120
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
from urllib.parse import urlparse, unquote, quote
import hug
import json

import jwt
from jwt import DecodeError
import app_celery
from async_task.services import async_task_service
from base.enums import ApiParamSysValue
from base.models import QueryBaseModel
from components import auth_util
from components.global_utils import compare_dict
from components.weixin_api import userid_to_openuserid
from dmplib.hug import g
import time
from hashlib import sha1
from dmplib import config
from dmplib.hug import APIWrapper
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from rbac.validator import PermissionValidator
from user.models import (
    <PERSON>rModel,
    ChangePasswordModel,
    <PERSON>r<PERSON>ueryModel,
    <PERSON>rLoginModel,
    OaUserLoginModel,
    YzsUserLoginModel,
    IngrateModel
)
from user.services import user_service, user_sync_service
from user.services import reporting_sso_service
from rbac.services import role_service
from user.services import assistant_service
from user.services.assistant_service import OAuthFailedGetPlatformUser, OAuthInvalidPlatform
from user.services.wx_service import WXWebAuth
from user_log.models import UserLogModel
from components.common_admin_route import CommonProxyAPIWrapper
from components.utils import redirect_to_error_page
from base.dmp_constant import YZS_REPORT_THIRD_APP_CODE, YZS_MSG_THIRD_APP_CODE
from user.services.yzs_sdk_service import User
from base.dmp_constant import YZS_REPORT_APP_CODE, YZS_MSG_APP_CODE
from feed.services.saas_app_user_service import SaasAppUserService
from user.services import ingrate_service
from dashboard_chart.api_route import DataApiWrapper
from user.services import gtcloud_service

api = APIWrapper(__name__)
common_admin_api = CommonProxyAPIWrapper(__name__)
data_api = DataApiWrapper(__name__)


@api.admin_route.get('/profiling')
def get_user_role(**kwargs):
    return (
        True,
        '',
        user_service.get_user_role(
            account=kwargs.get("account"), name=kwargs.get("name"), dataset_id=kwargs.get("dataset_id")
        ),
    )


@api.admin_route.get('/profile')
def get_cur_user_profile(request, response):
    """
    /*
        @apiVersion 1.0.3
        @api {get} /api/user/profile 用户资料
        @apiGroup  user
        @apiResponse  200 {
            "result": true,
            "msg": "",
            "data": {
                "account": "unittest",
                "account_mode": "DMP",
                "add_mode": "",
                "created_on": "2016-09-01 11:25:05",
                "email": "<EMAIL>",
                "group_id": "********-0000-0000-1111-********0000",
                "group_ids": "",
                "id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                "mobile": "",
                "name": "功能测试",
                "password": "",
                "role_ids": "",
                "funcs_map": {
                    "add-dataset": [
                        "edit",
                        "view"
                    ]
                },
                "app": [
                    {
                        "id": "39e0ff72-ec9f-49ac-605e-f7e70201cd5a",
                        "name": "DMP",
                        "description": "数据管理平台",
                        "icon": "",
                        "url": "",
                        "target": "",
                        "is_buildin": 1,
                        "rank": 1,
                        "enable": 1,
                        "nav_type": 0,
                        "function": [
                            {
                                "id": "********-0000-0000-0003-********0000",
                                "name": "离线大数据管理",
                                "parent_id": "",
                                "level_code": "0003-",
                                "icon": "dmpicon-datamanage",
                                "url": "",
                                "target": "",
                                "application_id": "39e0ff72-ec9f-49ac-605e-f7e70201cd5a",
                                "sub": [
                                    {
                                        "id": "********-0000-0000-0003-********0001",
                                        "name": "数据清洗",
                                        "parent_id": "********-0000-0000-0003-********0000",
                                        "level_code": "0003-0001-",
                                        "icon": "",
                                        "url": "/dataclean",
                                        "target": "",
                                        "application_id": "39e0ff72-ec9f-49ac-605e-f7e70201cd5a"
                                    }
                                ]
                            }
                        ]
                    }
                ],
                "theme": "",
                "project": {
                    "id": "39e1f1e8-fa37-be9a-ede5-51bbc1a4db8c",
                    "logo_uri": "",
                    "code": "unittest",
                    "title": "DMP单元测试环境",
                    "description": "测试项目",
                    "type": "平台",
                    "is_domain_account": 0,
                    "account_mode": "DMP",
                    "erp_data_source_code": "aa",
                    "saas_mode": 0,
                    "allow_dashboard_type": "allow_dashboard_type",
                    "dmp_env_sign": "hd", // DMP环境版本标识，可选值 hd：for HighData，shujian:for数见，cloud：for 三云，dmp:招商、华宇、荣盛
                },
                "domain": "dmp-test9.mypaas.com.cn",
                "disable_skin_button": 0,
                "log": {
                    "env_code": "qjq",
                    "user_name": "功能测试",
                    "user_org": ""
                },
                "dashboard_lock_seconds": 1800,
                "setting{用户系统设置，可能是一个空对象}": {
                    "app_fit_page{门户展示方式 0 适应宽度 1适应页面}": 0,
                    "pin_components{用户设置的常用组件，多个组件使用英文逗号分隔。如果用户从来没有设置过返回NULL}":"default"
                    "smart_beauty_status": 1 首次使用 | 2 非首次使用
                }
                "is_support_multi_external_subjects": 0,
                "smart_beauty_on": 0 | 1 美颜开启状态
            }
        }
    */
    """
    return True, '', user_service.get_user_profile(request, response=response)


@api.admin_route.post('/theme/set')
def set_user_theme(request, **kwargs):
    """
    设置用户主题
    :param request:
    :param kwargs:
    :return:
    """
    user_service.set_user_theme(request.user_agent, kwargs.get('theme'))
    return True, '设置成功'


@api.admin_route.get('/theme/get')
def get_user_theme(request):
    """
    获取用户主题
    :param request:
    :return:
    """
    return True, '', user_service.get_user_theme(request.user_agent)


@api.admin_route.get('/get')
def get_user(**kwargs):
    return True, '', user_service.get_user(kwargs.get('id'))


@api.admin_route.post('/add', validate=PermissionValidator('user-group.edit'))
def add_user(request, **kwargs):
    """
    添加用户
    :param falcon.Request request:
    :param kwargs:
    :return:
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc + '/'
    model = UserModel(**kwargs)
    user_service.add_user(model)

    if model.role_ids and len(model.role_ids) > 0:
        role_service.set_roles_for_user(model.id, model.role_ids)

    UserLogModel.log_setting(
        request=request,
        log_data={'action': 'add_user', 'id': kwargs.get('id'), 'content': '创建用户 [ {name} ] '.format(name=model.name)},
    )
    return True, '添加成功', model.id


@api.admin_route.post('/update', validate=PermissionValidator('user-group.edit'))
def update_user(request, **kwargs):
    """
    修改用户
    :param kwargs:
    :return:
    """
    model = UserModel(**kwargs)
    old_user = user_service.get_user_by_id(model.id)
    user_service.update_user(model)
    if model.role_ids is not None:
        role_service.set_roles_for_user(model.id, model.role_ids)
    new_user = user_service.get_user_by_id(model.id)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_user',
            'id': kwargs.get('id'),
            'content': '用户修改,变更详情为：\n {name}'.format(
                name=compare_dict(
                    old_user, new_user, field_dict={"name": "姓名", "mobile": "手机号码", "email": "邮箱", "role": "角色"}
                )
            ),
        },
    )

    return True, '修改成功'


@api.admin_route.post('/users/update-group', validate=PermissionValidator('user-group.edit'))
def update_users_group(**kwargs):
    """
    添加多个用户到用户组
    :param kwargs:
    :return:
    """
    user_ids = kwargs.get('user_ids')
    group_id = kwargs.get('group_id')
    user_service.update_users_group(user_ids, group_id)
    return True, '修改成功'


@api.admin_route.post('/disable', validate=PermissionValidator('user-group.edit'))
def disable_user(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api    {post} /api/user/disable 禁用用户
    @apiGroup    user
    @apiParam   formData  {string}    user_id 用户id
    @apiParam   formData  {string}    is_disabled 禁用1启用0
    @apiResponse 200{
        "result": true,
        "msg": "修改成功",
        "data": true
    }
    **/
    """
    return True, '修改成功', user_service.disable_user(kwargs.get('user_id'), kwargs.get("is_disabled"))


@api.admin_route.post('/delete', validate=PermissionValidator('user-group.edit'))
def delete_user(request, **kwargs):
    """
    删除用户
    :param kwargs:
    :return:
    """
    if not kwargs.get("id"):
        raise UserError('缺少用户id')
    user = user_service.delete_user(kwargs.get('id'))

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_user',
            'id': kwargs.get('id'),
            'content': '删除用户 [ {name} ] '.format(name=user.name),
        },
    )

    return True, '删除成功', user


@api.admin_route.post('/reset_password', validate=PermissionValidator('user-group.edit'))
def reset_user_password(request, **kwargs):
    """
    重置用户密码
    :param falcon.Request request:
    :param kwargs:
    :return:
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc + '/'
    return True, '重置成功', user_service.reset_user_password(kwargs.get('id'), kwargs.get('password'))


@api.admin_route.post('/change_password')
def change_password(**kwargs):
    """
    修改当前用户密码
    :param kwargs:
    :return:
    """
    model = ChangePasswordModel(**kwargs)
    if not model.id:
        model.id = user_service.get_cur_user_id()
    return True, '修改成功', user_service.change_password(model)


@api.route.post('/init_password')
def init_password(**kwargs):
    return True, '修改成功', user_service.init_password(**kwargs)


@api.admin_route.get('/list', validate=PermissionValidator('user-group.view'))
def get_user_list(**kwargs):
    """
    获取用户列表
    @api {get} /api/user/list 获取用户列表
    :param kwargs:
    :return:
    """
    # 简讯发送消息到内置应用时，获取用户组下的用户列表
    mobile_subscribe_id = kwargs.get("app_id")
    if mobile_subscribe_id and SaasAppUserService.check_app_is_api(mobile_subscribe_id):
        return True, '', SaasAppUserService. \
            get_user_list(mobile_subscribe_id, UserQueryModel(**kwargs)).get_result_dict()
    else:
        return True, '', user_service.get_user_list(UserQueryModel(**kwargs))


@api.admin_route.get('/get_erp_users', validate=PermissionValidator('user-group.view'))
def get_erp_user_list(**kwargs):
    """
    获取ERP用户列表
    :param kwargs:
    :return:
    """
    return True, '', user_service.get_erp_user_list(QueryBaseModel(**kwargs)).get_result_dict()


@api.route.post('/login_mode')
def login_mode(request, response, **kwargs):
    model = UserLoginModel(**kwargs)
    model.request = request
    model.response = response
    return True, '', user_service.login_mode(model)


@api.route.post('/login')
def login(request, response, **kwargs):
    """
    用户登录
    :param falcon.Request request:
    :param falcon.Response response:
    :param kwargs:
    :return:
    """
    model = UserLoginModel(**kwargs)
    model.request = request
    model.response = response
    result, msg, data = user_service.login(model)
    return result, msg, data


@api.route.post('/generate_email_code')
def generate_email_code(request, response, **kwargs):
    model = UserLoginModel(**kwargs)
    model.request = request
    model.response = response
    user_service.generate_email_code(model)
    return True, '', True


@api.route.get('/refresh')
def refresh(request, **kwargs):
    """
    用户刷新token
    :param falcon.Request request:
    :param kwargs:
    :return:
    """
    token = request.cookies.get('token')
    if token:
        try:
            data = jwt.decode(token, config.get('JWT.secret'), algorithms='HS256')
        except DecodeError:
            return True, '无效token', True
        userid = data.get("id")
        code = data.get("code")
        if userid and code:
            keep_login = kwargs.get("keep_login") == '1'
            user_service.refresh(token, keep_login, userid=userid, code=code, auto_logout=True)
    return True, '刷新成功', True


@api.admin_route.get('/forced_offline')
def forced_offline(request, **kwargs):
    """
    强制用户下线
    """
    token = None
    if kwargs.get("only_self"):
        # 只退出当前登录用户
        token = request.cookies.get('token')
    workbench_token = request.cookies.get('workbench_token')
    return True, '成功下线', user_service.forced_offline(kwargs.get("user_id"), kwargs.get("code"), token, workbench_token)


@api.admin_route.post('/logout')
def logout(response):
    """
    用户登出
    :param falcon.Response response:
    :return:
    """
    token_name = config.get('App.custom_cookie_token_name') or 'token'
    response.unset_cookie('code')
    response.unset_cookie('account')
    response.unset_cookie(token_name)
    response.unset_cookie('cookie_domain')
    response.unset_cookie('grayscale_user')
    response.unset_cookie('user_name')
    response.unset_cookie('user_org')
    return True, '登出成功'


@api.admin_route.post('/check_login')
def check_login():
    """
    校验是否登录
    :return:
    """
    return True, '', getattr(g, 'code'), getattr(g, 'account')


@api.route.post('/captcha')
def generate_captcha(**kwargs):
    """
    获取验证码
    :param kwargs: account用户名 tenant_code企业代码
    :return:str base64
    """
    model = UserLoginModel(**kwargs)
    return True, '获取验证码成功', user_service.generate_captcha(model)


@api.route.post('/check_need_captcha')
def check_need_captcha(**kwargs):
    """
    校验是否需要验证码
    :param kwargs: account用户名 tenant_code企业代码
    :return:str  1 0
    """
    model = UserLoginModel(**kwargs)
    return True, '', user_service.check_need_captcha(model)


@api.admin_route.get('/sso/reporting')
def login_mobile_reporting(redirect_url=None):
    """
    /*
    @api {get} /api/user/sso/reporting 登录移动报表平台
    @apiVersion 1.0.0
    @apiParam query {string} redirect_url 要跳转的地址
    @apiResponse 200 {
      "result": true,
      "msg": "ok",
      "data{跳转的地址}": "http://xxx.url"
    }
    */
    """

    # 1. generate token
    # 2. call remote URL + token + from
    # 3. redirect to
    sso = reporting_sso_service.ReportingSSOService()

    if redirect_url is None:
        redirect_url = config.get('Open.mreporting_redirect_url')
    r_url = sso.generate_login_url(redirect_url)
    # pylint: disable=I1101
    hug.redirect.to(r_url)


@api.route.get('/sso/login')
def login_sso(access_token: hug.types.text, _from: hug.types.text, request, response):  # pylint: disable=I1101
    """
    /**
    @apiVersion 1.0.2
    @api {get} /api/user/sso/login DMP标准单点登录
    @apiGroup user
    @apiParam query {string} access_token 权限校验token,生成token：http://doc.mypaas.com.cn/dmp/dev/ThirdParty/SignIn.html
    @apiParam query {string} _from 登录来源
    @apiResponse 302{
    }
    **/
    """
    sso = reporting_sso_service.ReportingSSOService()
    success, data, redirect_url = sso.login(access_token, request, response)
    if success:
        # pylint: disable=I1101
        hug.redirect.to(redirect_url)
    else:
        if str(request.params.get('show_error_page')) == '1':
            redirect_to_error_page(msg_str=data)
        else:
            redirect_url = reporting_sso_service.get_redirect_url(redirect_url)
            response.set_header('sso-message', json.dumps(data))
            # pylint: disable=I1101
            hug.redirect.to(redirect_url)


@api.route.get('/v2/sso/login')
def login_sso_v2(access_token: hug.types.text, _from: hug.types.text, request, response):  # pylint: disable=I1101
    """
    /**
    @apiVersion 1.0.2
    @api {get} /api/user/sso/login DMP标准单点登录
    @apiGroup user
    @apiParam query {string} access_token 权限校验token,生成token：http://doc.mypaas.com.cn/dmp/dev/ThirdParty/SignIn.html
    @apiParam query {string} _from 登录来源
    @apiResponse 302{
    }
    **/
    """
    sso = reporting_sso_service.ReportingSSOService()
    success, data, origin_redirect_url = sso.login(access_token, request, response)
    if success:
        # pylint: disable=I1101
        reporting_sso_service.render_temporary_redirect_page(response, origin_redirect_url)
    else:
        if str(request.params.get('show_error_page')) == '1':
            redirect_to_error_page(msg_str=data)
        else:
            redirect_url = reporting_sso_service.get_redirect_url(origin_redirect_url)
            response.set_header('sso-message', json.dumps(data))
            # pylint: disable=I1101
            hug.redirect.to(redirect_url)


@api.route.get('/sso/login_external')
def login_sso_by_external_secret(
        access_token: hug.types.text,
        _from: hug.types.text,
        is_external_secret: hug.types.boolean,
        code: hug.types.text,
        request,
        response,
):  # pylint: disable=I1101
    """
    /**
    @apiVersion 1.0.2
    @api {get} /api/user/sso/login DMP标准单点登录
    @apiGroup user
    @apiParam query {string} access_token 权限校验token,生成token：http://doc.mypaas.com.cn/dmp/dev/ThirdParty/SignIn.html
    @apiParam query {string} _from 登录来源
    @apiParam query {boolean} is_external_secret 是否使用外部秘钥
    @apiResponse 302{
    }
    **/
    """
    sso = reporting_sso_service.ReportingSSOService(is_external_secret=is_external_secret, code=code)
    success, data, redirect_url = sso.login(access_token, request, response)
    if success:
        # pylint: disable=I1101
        hug.redirect.to(redirect_url)

    redirect_url = reporting_sso_service.get_redirect_url(redirect_url)
    response.set_header('sso-message', json.dumps(data))
    # pylint: disable=I1101
    hug.redirect.to(redirect_url)


@api.route.get('/sso/ecology/login')
def login_sso_ecology(request, response):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/user/sso/ecology/login 单点登录（泛微、荣盛）
    @apiGroup user
    @apiParam query {string} UserCode v2用户账户标识
    @apiParam query {string} PageUrl v2重定向地址
    @apiParam query {string} TenantCode v2企业(租户)代码
    @apiParam query {string} tenant 企业(租户)代码
    @apiParam query {string} loginid  用户账户标识,如果为MOBILE，值为dmp的用户名;如果为PC，则为对dmp用户名的加密字符串，加密方式联系开发
    @apiParam query {string} divtype  登录设备类型，支持(PC、MOBILE)，不区分大小写
    @apiParam query {string} redirect 重定向的目标页面地址url
    @apiParam query {string} [signtype] 签名算法,支持 'sha1', 'sha256', 'md5' (divtype=MOBILE时必填)
    @apiParam query {string} [stamp] 随机数,时间 (divtype=MOBILE时必填)
    @apiParam query {string} [token] 签名,签名算法signtype对(secret + loginid + stamp)的签名字符串 (divtype=MOBILE时必填)
    @apiParam query {string} [FailedRedirect] 登录失败时的重定向地址
    @apiResponse 302{
    }
    **/
    """
    sso = reporting_sso_service.EcologySSOService()
    success, data = sso.login(request, response)

    if success:
        # pylint: disable=I1101
        hug.redirect.to(data['redirect'])

    redirect = request.params.get('redirect', '')
    failed_redirect = request.params.get('FailedRedirect', '')
    redirect_url = reporting_sso_service.get_redirect_url(redirect, failed_redirect)
    response.set_header('sso-message', json.dumps(data))
    # pylint: disable=I1101
    hug.redirect.to(redirect_url)


@api.route.get('/assistant')
def login_assistant(request, response):
    # 登录云助手/企业微信/钉钉
    try:
        ass_service = assistant_service.instance_oauth_service(request, response)
        ass_service.jump()
    except OAuthInvalidPlatform as e:
        raise UserError(code=400, message='未知的平台，请确认是在正确的客户端中浏览') from e
    except OAuthFailedGetPlatformUser as e:
        raise UserError(code=41, message='Failed to get user') from e


@api.route.get('/superapp')
def login_super_work(request, response):
    # 登录超级工作台
    try:
        ass_service = assistant_service.instance_oauth_by_app_code(request, response)
        ass_service.jump()
    except OAuthInvalidPlatform as e:
        raise UserError(code=400, message='未知的平台') from e
    except OAuthFailedGetPlatformUser as e:
        raise UserError(code=41, message='Failed to get user') from e


@api.route.get('/superportal/dashboard')
def login_superportal(request, response, **kwargs):
    """
    超级工作台集成数据报表
    """
    from loguru import logger

    logger.error(f"开始校验参数：{kwargs}")
    model = IngrateModel(**kwargs)
    model.validate()
    logger.error(f"参数校验成功")
    code = kwargs.get('code')
    skyline_auth_code = ''
    if code:
        from user.services.user_service import is_tenant_exists
        # 如果有对应租户则为租户编码，否则是工作台身份令牌
        if is_tenant_exists(code):
            model.tenant_code = code if not model.tenant_code else model.tenant_code
        else:
            skyline_auth_code = code
    # 免登跳转
    return ingrate_service.login_and_jump(request, response, model, skyline_auth_code)

@api.route.get('/superportal/{report_type}')
def login_superportal_by_role(request, response, **kwargs):
    """
    角色登录
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    # 免登跳转
    from loguru import logger
    logger.error(f"开始校验参数：{kwargs}")
    model = IngrateModel(**kwargs)
    code = kwargs.get('code')
    skyline_auth_code = ''
    if code:
        from user.services.user_service import is_tenant_exists
        # 如果有对应租户则为租户编码，否则是工作台身份令牌
        if is_tenant_exists(code):
            model.tenant_code = code if not model.tenant_code else model.tenant_code
        else:
            skyline_auth_code = code
    redirect_url = ingrate_service.login_with_customer_role(request, response, model, skyline_auth_code)
    hug.redirect.to(redirect_url)


@api.route.get('/yzs_provide')
def login_yzs_provide(request, response):
    """
    云助手基础应用免登到数见
    :param request:
    :param response:
    :return:
    """
    try:
        assistant_service.login_by_yzs_provide(request, response)
    except UserError as e:
        raise e


@api.admin_route.post('/async_import', validate=PermissionValidator('user-group.edit'))
def async_import_user(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /api/user/async_import 异步excel导入用户
    @apiGroup  user
    @apiBodyParam {
        "is_preview{是否预览(1 代表预览)}": 1,
        "oss_url{oss路径}": "http://dmp-test.oss-cn-shenzhen.aliyuncs.com/cc用户导入.xlsx"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {"task_id":"dmp:user:EXCEL:mysoft:39e7c8d9-095f-ae82-12c3-8e93b6cd72f2"}
        }
    */
    """
    task_id = async_task_service.get_task_id(
        module_name="user", task_name="EXCEL", project_code=g.code, flow_id=seq_id()
    )
    data = {
        "task_id": task_id,
        "project_code": g.code,
        "account": g.account,
        "oss_url": kwargs.get("oss_url"),
        "is_preview": kwargs.get("is_preview"),
        "user_source_id": kwargs.get("user_source_id"),
    }
    app_celery.generate_user.apply_async(kwargs=data)
    return True, 'ok', {'task_id': task_id}


@common_admin_api.admin_with_third_party_route.put('/user_setting')
def update_user_setting(**kwargs):
    """
    /*
        @apiVersion 1.0.1
        @api {put} /api/user/user_setting 更新用户系统设置
        @apiGroup  user
        @apiBodyParam {
            "app_fit_page{门户展示方式 0 适应宽度 1适应页面}": 1,
            "pin_components{用户设置的常用组件}": "default",
            "dashboard_display_style{报告展示方式 0 图标展示 1 列表展示}"：0,
            "smart_beauty_status": 0, 1, 2
        }
        @apiResponse  200 {
            "result": true,
            "msg": ""
        }
    */
    """
    return user_service.set_user_setting(kwargs), 'ok'


@api.admin_route.get('/info')
def get_user_info():
    """
    /*
    @apiVersion 1.0.3
    @api {get} /api/user/info 获取用户基础信息
    @apiGroup user
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": {
            "id{用户ID}": "39ee0f68-7122-b8a2-c3a6-b0131c5f5af8",
            "name{用户名称}": "tanzy_test_no_group",
            "account{账户名}": "tanzy_test_no_group",
            "mobile{手机号}": "",
            "email{邮箱}": "<EMAIL>",
            "last_login_time{最后登录时间}": **********,
            "groups{用户组信息}": {
                "groups{所属用户组}": [
                    {
                        "id{用户组ID}": "39ee0fd9-86be-5fb5-f78e-01ce24462743",
                        "name{用户组名称}": "DMP\u9879\u76ee\u7ec4",
                        "parent_id{父级用户组ID}": "39ee0fd8-817f-73bb-ac88-a499eb25a7df",
                        "code{用户组层级代码}": "0001-0050-0003-0001-"
                    }

                ],
                "parent_groups_data{父级用户组数据，用于构建用户组的父级拼接展示}": [
                    {
                        "id{用户组ID}": "********-0000-0000-1111-********0000",
                        "name{用户组名称}": "test",
                        "parent_id{父级用户组ID}": "********-0000-0000-0000-********0000",
                        "code{用户组层级代码}": "0001-"
                    }

                ]
            }
        }
    }
    */
    """
    return True, 'success', user_service.get_user_info()


@api.admin_route.get('/user_sync_setting')
def get_user_sync_setting():
    """
    /*
        @apiVersion 1.0.0
        @api {get} /api/user/user_sync_setting 获取用户同步设置
        @apiGroup  user
        @apiBodyParam {
        }
        @apiResponse  200 {
            "result": true,
            "msg": "",
            "data": {
                "user": {"source_dataset_id": "xxx1",
                    "field_relation": {"id": "user_id", "name": "user_name", "account": "col1", "pwd": "col2",
                                       "group_id": "col5", "email": "col7", "mobile": "",
                                       "is_disabled": ""}},
                "user_group": {"source_dataset_id": "xxx2",
                    "field_relation": {"id": "group_id", "name": "group_name", "parent_id": "col1", "hierarchy": "col2"
                    }},
                "encrypt": "col4"
            }
        }
    */
    """
    return True, "", user_service.get_sync_setting()


@api.admin_route.post('/user_sync_setting')
def update_user_sync_setting(**kwargs):
    """
    /*
        @apiVersion 1.0.0
        @api {post} /api/user/user_sync_setting 用户同步设置
        @apiGroup  user
        @apiBodyParam {
                "user": {"source_dataset_id": "xxx1",
                    "field_relation": {"id": "user_id", "name": "user_name", "account": "col1", "pwd": "col2",
                                       "encrypt": "col4", "group_id": "col5", "email": "col7", "mobile": "",
                                       "is_disabled": ""}},
                "user_group": {"source_dataset_id": "xxx2",
                    "field_relation": {"id": "group_id", "name": "group_name", "parent_id": "col1", "hierarchy": "col2"
                    }}
        }
        @apiResponse  200 {
            "result": true,
            "msg": "",
            "data": ""
        }
    */
    """
    return True, "", user_sync_service.update_sync_setting(kwargs)


@api.route.post('/oa/login')
def oa_login(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/user/oa/login oa登陆
    @apiGroup user
    @apiBodyParam {
        "loginid": "登陆ID",
        "stamp": "时间戳",
        "token": "token",
        "tenant_code": "租户代码"
    }
    @apiResponse 200{
        "result": true,
        "msg": "success",
        "data": true
    }
    **
    """
    model = OaUserLoginModel(**kwargs)
    model.request = request
    model.response = response
    model.validate()
    user_id = user_service.oa_login(model)
    return True, '登陆成功!', user_id


@api.admin_route.get('/redirect')
def redirect(request, response, **kwargs):
    """
    /**
    @apiVersion 1.0.2
    @api {get} /api/user/redirect 用户页面重定向
    @apiGroup user
    @apiResponse 302{
    }
    **/
    """
    user_id = g.userid
    group_ids = g.group_ids
    code = g.code

    hug.redirect.to(user_service.get_redirect_url(user_id, group_ids, code))  # pylint: disable=I1101


@api.route.get('/yzs_report_login')
def yzs_report_login(request, response, **kwargs):
    kwargs['app_secret'] = config.get('Yzs.report_app_key')
    kwargs['third_app_code'] = YZS_REPORT_THIRD_APP_CODE
    kwargs['app_code'] = YZS_REPORT_APP_CODE
    kwargs['func_name'] = 'yzs_report_login'
    result, msg = third_app_login(request, response, **kwargs)
    if result == False:
        return False, msg, []


@api.route.get('/yzs_msg_login')
def yzs_msg_login(request, response, **kwargs):
    kwargs['app_secret'] = config.get('Yzs.msg_app_key')
    kwargs['third_app_code'] = YZS_MSG_THIRD_APP_CODE
    kwargs['app_code'] = YZS_MSG_APP_CODE
    kwargs['func_name'] = 'yzs_msg_login'
    result, msg = third_app_login(request, response, **kwargs)
    if result == False:
        return False, msg, []


@api.route.get('/yzs_report_login_debug')
def yzs_report_login_debug(request, response, **kwargs):  # pylint: disable=I1101
    try:
        redirect_url = kwargs.get("redirect_url")
        account = kwargs.get("account")
        tenant_code = kwargs.get("tenant_code")
        if account is None:
            raise Exception('缺少account')
        if tenant_code is None:
            raise Exception('缺少tenant_code')
        user_id, tenant_code, group_ids = user_service.yzs_login_debug(request, response, account, tenant_code)
    except Exception as e:
        debug_msg = '{0}'.format(e.__str__())
        return (False, debug_msg, [])
    if redirect_url is None:
        hug.redirect.to(user_service.get_redirect_url(user_id, group_ids, tenant_code))
    else:
        hug.redirect.to(redirect_url)


def third_app_login(request, response, **kwargs):
    try:
        user_service.yzs_login(request, response, kwargs)
        redirect_url = get_third_app_default_url(kwargs.get("redirect_url"))
    except Exception as e:
        debug_msg = f"params: {kwargs} Error:{e.__str__()}"
        return False, debug_msg
    # 登录成功后跳转
    hug.redirect.to(redirect_url)


def get_third_app_default_url(redirect_url):
    if not redirect_url or redirect_url.lower() == 'none':
        from user_group.services import user_group_service

        mobile_apps = user_group_service.get_user_permission_apps(g.userid, g.group_ids, ['mobile', 'mobile_screen'])
        if len(mobile_apps) == 1:  # 用户有权限的主页只有有一个，直接跳转
            redirect_url = f'/app/index/{mobile_apps[0].get("id")}/mobile'
        else:  # 多个主页时，跳转到列表
            redirect_url = f'/app/index/mobile_home'
    return redirect_url


@api.admin_route.get('/get_user_permission_apps')
def get_user_permission_apps():
    from user_group.services import user_group_service

    return True, "获取成功", user_group_service.get_user_permission_apps(g.userid, g.group_ids, ['mobile', 'mobile_screen'])


# 云助手授权验证
def _verify_yzs_handle(kwargs):
    """
    :param request:
    :param response:
    :return:
    """
    signature = kwargs.get('signature')
    timestamp = kwargs.get('timestamp')
    channel_id = kwargs.get('channel_id')
    project_code = kwargs.get('channel_code')
    if not signature or not timestamp or not channel_id or not project_code or len(project_code) > 255:
        return False

    salt = 'party'
    str_tmp = '{0}{1}{2}{3}'.format(timestamp, config.get('Yzs.api_token'), salt, channel_id)
    hsa1_signature = get_hsa1_signature(str_tmp)
    if str(signature).lower() == hsa1_signature.lower():
        g.code = project_code
        g.account = 'openapi'
        return True
    else:
        return False


def _verify_yzs_handle_for_suite(kwargs):
    """
    :param request:
    :param response:
    :return:
    """
    signature = kwargs.get('signature')
    timestamp = kwargs.get('timestamp')
    if not signature or not timestamp:
        return False

    salt = 'suite-api-auth'
    str_tmp = '{0}{1}{2}'.format(timestamp, config.get('Yzs.api_token'), salt)
    hsa1_signature = get_hsa1_signature(str_tmp)
    if str(signature).lower() == hsa1_signature.lower():
        return True
    else:
        return False


def get_hsa1_signature(_str):
    s1 = sha1()
    s1.update(_str.encode("utf-8"))
    return s1.hexdigest()


# @api.yzs_route.post('/user/sync_data')
@api.route.post('/sync_data')
def sync_data(**kwargs):
    if _verify_yzs_handle(kwargs):
        func = kwargs.get('data2pull')
        params_seq = kwargs.get('seq')
        if func == 'department':
            return user_service.get_sync_dept(params_seq)
        elif func == 'user':
            return user_service.get_sync_user(params_seq)
        else:
            return False, 'failure', "请求的方法不正确"
    else:
        return False, 'failure', "接口请求授权失败"


@api.route.post('/get_yzs_auth_apps')
def get_yzs_auth_apps(**kwargs):
    """
    获取指定租户在云助手中已经集成的应用
    :param template_type:
    :return:
    """
    if not _verify_yzs_handle_for_suite(kwargs):
        return {'success': 0, 'errmsg': '接口请求授权失败', 'errcode': 1001}

    project_code = kwargs.get('tenant_code')
    data_list = user_service.get_yzs_auth_apps(project_code)
    return {'success': 1, 'errmsg': '', 'errcode': 0, 'data': data_list}


# @api.admin_route.get('/get_erp_auth_apps')
@reporting_sso_service.erp_sso_authenticator()
def get_erp_auth_apps():
    """
    获取erp用户有权限的门户及门户下报告
    :return:
    """
    result = {}
    from user_group.services import user_group_service

    try:
        result['success'] = 1
        result['data'] = user_group_service.get_erp_auth_apps()
    except Exception as e:
        result['success'] = 0
        result['data'] = str(e)
    return result


@api.route.get('/get_third_party_url')
def get_third_party_url(**kwargs):
    """
    用户登录
    :param kwargs:
    :return:
    """
    project_code = kwargs.get('project_code')
    if not project_code:
        raise UserError(message='缺少租户code')
    url = user_service.get_third_party_url(project_code)
    return True, "", url


@api.route.post('/third_party_login')
def third_party_login(request, response, **kwargs):
    """
    第三发授权登录处理
    :param kwargs:
    :return:
    """
    access_code = kwargs.get('code')
    if not access_code:
        raise UserError("入参code为空")
    project_code = kwargs.get('project_code')
    if not project_code:
        raise UserError("入参project_code为空")
    # 数见单点登录
    result, user_id = user_service.third_party_login(request, response, access_code, project_code)
    return result, "", user_id


@api.route.get('/wx/index')
def wx_home(request, response, **kwargs):
    """
    应用主页--跳转到授权页面
    """
    auth_url = WXWebAuth("web_app").wx_auth_url()
    hug.redirect.to(auth_url)


@api.route.get('/wx/login')
def wx_login(request, response, **kwargs):
    """
    登录页
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    code = kwargs.get("code")
    if not code:
        raise UserError(message='授权重定向失败，未获取到授权code')
    WXWebAuth("web_app").wx_login(request, response, code)
    url = f"{config.get('Domain.dmp')}/dataview-mobile/portal/{config.get('Wwx.app_id', '39e97950-ce2f-6f2c-bc25-1145946c62b5')}"
    hug.redirect.to(url)


@api.route.get('/wx/callback', output=hug.output_format.text)
def wx_callback_get(request, response, **kwargs):
    """
    指令回调
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    from dmplib.utils.logs import logger
    logger.error("Get请求")
    message = WXWebAuth("web_app").get_message(**kwargs)
    logger.error("Get请求结束" + message)
    return message


@api.route.post('/wx/callback', output=hug.output_format.text)
def wx_callback_post(request, response, *args, **kwargs):
    """
    指令回调
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    from dmplib.utils.logs import logger
    logger.error("Post请求")
    WXWebAuth("web_app").callback(request, **kwargs)
    logger.error("Post结束")
    return "success"


@api.route.get('/wx/agent_app_callback', output=hug.output_format.text)
def wx_agent_app_callback_get(request, response, **kwargs):
    from dmplib.utils.logs import logger
    logger.error("Get请求")
    message = WXWebAuth("agent_app").get_message(**kwargs)
    logger.error("Get请求结束" + message)
    return message


@api.route.post('/wx/agent_app_callback', output=hug.output_format.text)
def wx_agent_app_callback_post(request, response, **kwargs):
    from dmplib.utils.logs import logger
    logger.error("Post请求")
    WXWebAuth("agent_app").callback(request, **kwargs)
    logger.error("Post结束")
    return "success"


@api.admin_route.get('/wx/userid_to_openuserid')
def wx_userid_to_openuserid(**kwargs):
    corp_id = kwargs.get("corp_id")
    if not corp_id:
        raise UserError("corp_id不能为空")
    app_secret = kwargs.get("app_secret")
    if not app_secret:
        raise UserError("app_secret不能为空")
    userid = kwargs.get("userid_list",'')
    if not userid:
        raise UserError("userid_list不能为空")
    if not isinstance(userid, list):
        userid = userid.split(',')
    return userid_to_openuserid(corp_id, app_secret, userid)


@api.route.post('/idm/login')
def idm_login(request, response, **kwargs):
    """
    IDM与数见集成登录接口
    接口文档：https://note.youdao.com/s/ApGVVBRP
    /**
    @apiVersion 1.0.2
    @api {get} /api/user/idm/login
    @apiGroup user
    @apiResponse 302{
    }
    **/
    """
    ticket_value = kwargs.get('ticket_value')
    ticket_name = kwargs.get('ticket_name')
    ticket_type = kwargs.get('ticket_type')
    project_code = kwargs.get('project_code')
    if not project_code:
        raise UserError("租户code不能为空")
    # 数见单点登录
    result, user_id = user_service.idm_validate(request, response, project_code, ticket_type, ticket_name, ticket_value)
    msg = '登录成功' if result else '登录失败'
    data = {
        "user_id": user_id,
    }
    return result, msg, data


@api.admin_route.get('/get_developer_tenant_list')
def get_developer_tenant_list(**kwargs):
    account = kwargs.get('account') or g.account
    from user.services.developer_service import Developer
    return True, '', Developer.get_developer_tenant_list(account)


@api.admin_route.get('/change_developer_login_tenant')
def change_developer_login_tenant(request, response, **kwargs):
    from user.services.developer_service import Developer
    tenant_code = kwargs.get('tenant_code')
    account = g.account
    if not tenant_code:
        return False, '租户CODE不能为空', ''
    return True, '', Developer().change_developer_login_tenant(request, response, account, tenant_code)


@api.route.get('/gtcloud/sso')
def gt_cloud_sso(request, response, **kwargs):
    try:
        ticket = kwargs.get("ticket")
        tenant = kwargs.get("tenant")
        redirect_url = kwargs.get("redirect") or '/home'

        if not ticket:
            raise UserError(message="缺少认证信息ticket")

        # 默认指定为sdp_prd租户
        # https://www.tapd.cn/********/prong/stories/view/11********001505172?action_entry_type=stories
        g.code = tenant or config.get("External.gtcloud_code", 'sdp_prd') or 'sdp_prd'

        # 登录
        gtcloud_service.GtCloudSSo(request, response).sso_login(ticket)

        redirect_url = unquote(redirect_url)
    except UserError as e:
        redirect_url = f'/static/errorTip.html?msg={quote(e.message)}'
    except Exception as e:
        redirect_url = f'/static/errorTip.html?msg={quote(str(e))}'

    return hug.redirect.to(redirect_url)


@api.route.get('/auth_hw_login')
def auth_hw_login(request, response, **kwargs):
    from open_hw.services.auth_service import HwAuth
    code = kwargs.get('code')
    tenant_id = kwargs.get('tenant')
    if not code or not tenant_id:
        return False, '缺少必要参数', ''
    auth = HwAuth(**kwargs)
    auth.hw_login(request, response)
    hug.redirect.to("/")


@data_api.data_route.get('/app/index')
def application_index(request, **kwargs):
    """
    获取用户有权限的门户列表
    兼容第三方用户，数见用户场景
    /api/user/app/index?platform=&code=uitest
    :param request:
    :param kwargs:
    code 租户code
    platform 门户类型，mobile_screen:移动门户，pc：pc门户
    :return:
    """
    from dmplib.saas.project import set_correct_project_code
    from dmplib.saas.errors import EmptyProjectCodeError
    code = kwargs.get("code")
    if not code:
        raise UserError(message="缺少code参数")
    try:
        g.code = set_correct_project_code(code, request)
    except EmptyProjectCodeError:
        raise UserError(message='{}在数见系统中不存在'.format(code))
    return True, '', user_service.get_user_application_index(request, kwargs)