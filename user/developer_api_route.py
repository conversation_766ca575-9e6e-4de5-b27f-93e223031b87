#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    API Route
    <NAME_EMAIL> on 2017/3/16.
"""

from dmplib.hug import APIWrapper
from base.models import QueryBaseModel
from user.services.developer_service import Developer, DeveloperAuth
from user.models import DeveloperSecretModel
from dmplib.hug import g

api = APIWrapper(__name__)


@api.route.post('/login')
def developer_login(request, response, **kwargs):
    """
    开发者登录验证接口
    """
    token = kwargs.get('token')
    if not token:
        return False, 'token不存在'
    developer = Developer(token)
    return True, '', developer.developer_login(request, response)


@api.route.get('/get_config')
def get_rdc_config():
    """
    获取当前环境的开发者配置
    """
    return True, '', DeveloperAuth().get_developer_config()


@api.route.get('/get_project_list')
def get_project_list(**kwargs):
    """
    获取当前环境中开启了开发者认证的租户列表
    """
    model = QueryBaseModel(**kwargs)
    return True, '', Developer.get_project_list(model)


@api.route.post('/get_developer_list')
def get_developer_list(**kwargs):
    """
    根据租户获取开发者账号信息
    """
    tenant_code = kwargs.get('tenant_code')
    if not tenant_code:
        return False, '租户code不能为空'
    return True, '', Developer.get_developer_user(tenant_code)


@api.route.post('/check_secret')
def check_secret(request, response, **kwargs):
    """
    验证秘钥激活
    """
    model = DeveloperSecretModel(**kwargs)
    model.validate()
    return True, '', Developer(model.token).check_admin_secret(model, request, response)


@api.route.post('/send_email')
def send_amail(**kwargs):
    """
    向开发者发送申请邮件
    """
    token = kwargs.get('token')
    tenant_code = kwargs.get('tenant_code')
    tenant_name = kwargs.get('tenant_name')
    selected_developer_code = kwargs.get('selected_developer_code')
    if not token or not tenant_code or not selected_developer_code:
        return False, '参数错误', ''
    return True, '', Developer.send_email_for_developer(tenant_code, tenant_name, token, selected_developer_code)


@api.route.post('/check_email_token')
def check_email_token(**kwargs):
    """
    邮件申请验证
    """
    token = kwargs.get('token')
    role_ids = kwargs.get('role_ids')
    if not token:
        return False, 'token不能为空', ''
    developer = Developer()
    return True, '验证通过', developer.check_email_token(token, role_ids)


@api.route.get('/get_role_list')
def get_role_list(**kwargs):
    """
    获取租户的角色列表
    """
    from rbac.services.role_service import list_roles
    tenant_code = kwargs.get('tenant_code')
    if not tenant_code:
        return False, '租户不能为空', ''
    g.code = tenant_code
    keyword = kwargs.get('keyword')
    page = kwargs.get('page', 1)
    page_size = kwargs.get('page_size', 100)
    total, items = list_roles(page, page_size, keyword)
    data = []
    for item in items:
        if item.get('id') == Developer.ADMIN_DEVELOPER_ROLE_ID:
            continue
        data.append(item)
    return True, 'ok', {'total': total, 'items': data}


@api.route.post('/is_admin_user')
def is_admin_user(**kwargs):
    token = kwargs.get('token')
    tenant_code = kwargs.get('tenant_code')
    if not tenant_code:
        return False, '租户不能为空', ''
    if not token:
        return False, 'token不能为空', ''
    developer = Developer(token)
    return True, '', developer.is_admin_user(tenant_code)


@api.route.post('/auth_admin_user')
def auth_admin_user(request, response, **kwargs):
    tenant_code = kwargs.get('tenant_code')
    token = kwargs.get('token')
    if not tenant_code:
        return False, '租户code不能为空', ''
    if not token:
        return False, 'token不能为空', ''
    result = Developer(token).auth_admin_user(tenant_code, request, response)
    return True, '', result
