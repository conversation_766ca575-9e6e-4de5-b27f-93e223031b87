#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    用户登录天际系统
"""

from dmplib.hug import APIWrapper
from user.services.workbench_auth_service import Workbench

api = APIWrapper(__name__)


@api.route.post('/login')
def login(request, response, **kwargs):
    """
    根据授权码获取token
    """
    auth_code = kwargs.get('code')
    if not auth_code:
        return False, '参数无效'
    return True, '', Workbench().login(auth_code, request, response)
