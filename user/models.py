#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
from base.dmp_constant import AES_DEFAULT_KEY
from base.models import BaseModel, QueryBaseModel
from components.crypt import AESCrypt

from typing import Dict, List, Tuple


class UserModel(BaseModel):
    __slots__ = [
        'id',
        'name',
        'account',
        'password',
        'mobile',
        'email',
        'group_id',
        'group_ids',
        'role_ids',
        'account_mode',
        'add_mode',
        'created_on',
        'user_source_id',
    ]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.name = None
        self.account = None
        self.password = None
        self.mobile = None
        self.email = None
        self.group_id = None
        self.group_ids = None
        self.role_ids = None
        self.account_mode = None
        self.add_mode = None
        self.created_on = None
        self.user_source_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['id'], 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 254}))
        rules.append(('account', 'string', {'max': 254}))
        rules.append(('mobile', 'string', {'len': 11, 'required': False}))
        return rules


class UserQueryModel(QueryBaseModel):
    __slots__ = ['group_id', 'nor_group_id', 'user_id', 'is_developer']

    def __init__(self, **kwargs):
        self.group_id = None
        # 不在该用户组
        self.nor_group_id = None
        self.user_id = None
        self.is_developer = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('group_id', 'string', {'max': 36, 'required': False}))
        rules.append(('nor_group_id', 'string', {'max': 36, 'required': False}))
        rules.append(('user_id', 'string', {'max': 36, 'required': False}))
        return rules


class ExternalUserQueryModel(QueryBaseModel):
    __slots__ = ['user_group_id']

    def __init__(self, **kwargs):
        self.user_group_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('user_group_id', 'string', {'max': 36, 'required': False}))
        return rules


class ChangePasswordModel(BaseModel):
    __slots__ = ['id', 'old_password', 'new_password']

    def __init__(self, **kwargs):
        self.id = None
        self.old_password = None
        self.new_password = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append((['old_password', 'new_password'], 'string', {'max': 254}))
        rules.append(
            ('new_password', 'match', {'pattern': r'(?=.*[0-9])(?=.*[a-zA-Z]).{8,}', 'msg': '密码中必须包含字母、数字，至少8个字符'})
        )
        return rules


class UserLoginModel(BaseModel):
    __slots__ = ['tenant_code', 'account', 'password', 'request', 'response', "captcha", 'keep_alive', 'return_token']

    def __init__(self, **kwargs) -> None:
        self.tenant_code = None
        self.account = None
        self.password = None
        self.request = None
        self.response = None
        self.captcha = None
        self.keep_alive = None
        self.return_token = False
        self.mail_code = None
        self.rdc_token = None
        super().__init__(**kwargs)
        if self.tenant_code:
            self.tenant_code = self._params_decrypt(self.tenant_code)
        if self.account:
            self.account = self._params_decrypt(self.account)
        if self.password:
            self.password = self._params_decrypt(self.password)

    def _params_decrypt(self, value):
        try:
            key = AES_DEFAULT_KEY
            crypt = AESCrypt(key)
            return crypt.decrypt(text=value).strip()
        except:
            return value

    def rules(self) -> List[Tuple[List[str], str, Dict[str, int]]]:
        rules = super().rules()
        rules.append((['tenant_code', 'account', 'password'], 'string', {'max': 254}))
        return rules


class OaUserLoginModel(BaseModel):
    __slots__ = ['tenant_code', 'loginid', 'stamp', 'token', 'response', "request", "token", "return_token"]

    def __init__(self, **kwargs) -> None:
        self.tenant_code = None
        self.loginid = None
        self.stamp = None
        self.request = None
        self.response = None
        self.token = None
        self.return_token = False
        super().__init__(**kwargs)
        if self.tenant_code:
            self.tenant_code = self.tenant_code.strip()
        if self.loginid:
            self.loginid = self.loginid.strip()

    def rules(self) -> List[Tuple[List[str], str, Dict[str, int]]]:
        rules = super().rules()
        rules.append((['tenant_code', 'loginid', 'token'], 'string', {'max': 254}))
        rules.append((['stamp']))
        return rules


class YzsUserLoginModel(BaseModel):
    __slots__ = ['tenant_code', 'account', 'response', "request", "return_token"]

    def __init__(self, **kwargs) -> None:
        self.tenant_code = None
        self.account = None
        self.request = None
        self.response = None
        self.return_token = False
        super().__init__(**kwargs)
        if self.tenant_code:
            self.tenant_code = self.tenant_code.strip()
        if self.account:
            self.account = self.account.strip()

    def rules(self) -> List[Tuple[List[str], str, Dict[str, int]]]:
        rules = super().rules()
        rules.append((['tenant_code', 'account'], 'string', {'max': 254}))
        return rules

class SingleUserLoginModel(BaseModel):
    __slots__ = ['tenant_code', 'account', 'response', "request"]

    def __init__(self, **kwargs) -> None:
        self.tenant_code = None
        self.account = None
        self.request = None
        self.response = None
        super().__init__(**kwargs)
        if self.tenant_code:
            self.tenant_code = self.tenant_code.strip()
        if self.account:
            self.account = self.account.strip()

    def rules(self) -> List[Tuple[List[str], str, Dict[str, int]]]:
        rules = super().rules()
        rules.append((['tenant_code', 'account'], 'string', {'max': 254}))
        return rules


class UserOrganizationModel(BaseModel):
    __slots__ = ['id', 'user_id', 'group_id', 'org_name', 'org_level']

    def __init__(self, **kwargs):
        self.id = None
        self.user_id = None
        self.group_id = None
        self.org_name = None
        self.org_level = None
        super().__init__(**kwargs)


class UserExcelModel(BaseModel):
    __slots__ = [
        'id',
        'name',
        'account',
        'password',
        'pwd',
        'mobile',
        'email',
        'group_id',
        'group_name',
        'account_mode',
        'error_msg',
        'group_ids',
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.account = None
        self.password = None
        self.pwd = None
        self.mobile = None
        self.email = None
        self.group_id = None
        self.group_name = None
        self.group_ids = []
        self.account_mode = None
        self.error_msg = None
        super().__init__(**kwargs)


class IngrateModel(BaseModel):
    __slots__ = ['report_id', 'token', 'type', 'release_type', 'report_type', 'tenant_code', 'disable_authcenter', 'user_auth', 'user_id', 'account']

    def __init__(self, **kwargs):
        self.report_id = None
        self.token = None
        self.type = 'dashboard'
        self.report_type = ''
        self.release_type = None
        self.tenant_code = None
        self.disable_authcenter = 0
        self.user_auth = None
        self.user_id = None
        self.account = None
        self.customize_roles = None

        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[List[str], str, Dict[str, int]]]:
        rules = super().rules()
        rules.append(('token', 'string', {'required': True}))
        return rules


class WorkbenchTokenData(BaseModel):
    __slots__ = ['access_token', 'token_type', 'expires_in', 'refresh_token', 'access_payload', 'refresh_payload']

    def __init__(self, **kwargs):
        self.access_token = ''
        self.access_payload: WorkbenchJwt = None
        self.refresh_token = ''
        self.refresh_payload: WorkbenchJwt = None
        self.token_type = ''
        self.expires_in = 0
        super().__init__(**kwargs)


class WorkbenchTokenCheck(BaseModel):
    __slots__ = ['active', 'scope', 'username', 'token_type', 'exp', 'iat', 'is_logged_out_by_other']

    def __init__(self, **kwargs):
        self.active = False
        self.scope = ''
        self.username = ''
        self.token_type = 'Bearer'
        self.exp = 0
        self.iat = 0
        self.is_logged_out_by_other = False
        super().__init__(**kwargs)

class WorkbenchJwt(BaseModel):
    __slots__ = ['nbf', 'user_code', 'user_name', 'user_guid', 'source', 'exp', 'iat', 'token_type', 'tenant_code']

    def __init__(self, **kwargs):
        self.nbf = None
        self.user_code = ''
        self.user_name = ''
        self.user_guid = ''
        self.source = ''
        self.exp = 0
        self.iat = 0
        self.token_type = ''
        self.tenant_code = ''
        super().__init__(**kwargs)


class DeveloperSecretModel(BaseModel):
    __slots__ = ['secret', 'tenant_code', 'is_active', 'token']

    def __init__(self, **kwargs):
        self.secret = None
        self.tenant_code = None
        self.is_active = 0
        self.token = None
        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[List[str], str, Dict[str, int]]]:
        rules = super().rules()
        rules.append(('secret', 'string', {'required': True}))
        rules.append(('token', 'string', {'required': True}))
        rules.append(('tenant_code', 'string', {'required': True}))
        return rules


class DeveloperModel(BaseModel):
    __slots__ = ['tenant_code', 'developer_account', 'account', 'name', 'email', 'phone']

    def __init__(self, **kwargs):
        self.tenant_code = None
        self.developer_account = None
        self.account = None
        self.name = None
        self.email = None
        self.phone = None
        super().__init__(**kwargs)

    def rules(self) -> List[Tuple[List[str], str, Dict[str, int]]]:
        rules = super().rules()
        rules.append(('developer_account', 'string', {'required': True}))
        rules.append(('account', 'string', {'required': True}))
        rules.append(('tenant_code', 'string', {'required': True}))
        return rules


if __name__ == "__main__":
    value = 'awsew'
    key = AES_DEFAULT_KEY
    crypt = AESCrypt(key)
    print(crypt.decrypt(text=value).strip())
