#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401

import base64
import time
import html

import hashlib
import binascii
import traceback
from urllib.parse import quote

from http.cookies import Morsel

from components import auth_util
from components.app_hosts import AppHosts
from dmplib.nacos_client import NacosClient
from .workbench_auth_service import Workbench, replace_code_parameter
from ..models import WorkbenchJwt
from ..repositories.ingrate_repository import get_dashboard_terminal_type

Morsel._reserved['samesite'] = 'SameSite'  # noqa 补丁
import json
import jwt
import falcon
import logging
from jwt import DecodeError, ExpiredSignatureError

from base.dmp_constant import SELF_SERVICE_VIRTUAL_USER_INFO, KEEP_LOGIN_DEFAULT_EXPIRE
from components.crypt import PBECrypt
from components.grayscale import set_grayscale_project
from dmplib import config
from dmplib.hug import g, debugger
from . import user_service
from dmplib.saas import project
from user.commons.sso_security_helper import EcologySSOE<PERSON>rity<PERSON>el<PERSON>, B64DecodeError
from base import repository
from base.enums import DashboardTerminalType, LoginFrom, SkylineApps
from user.repositories.user_repository import get_external_secret
from base import repository
from dmplib.utils.errors import UserError
from loguru import logger

_debugger = debugger.Debug(__name__)

default_aes_key = 'mysoft5a2fc34a9e340'


# 给cookies添加samesite属性，支持跨域
# 不使用response.set_cookie是因为cookies使用的SimpleCookies对象不支持这个属性
def set_cookies_samesite_property(response):
    if config.get('Domain.dmp').startswith("https"):
        try:
            resp_cookies = response._cookies
            if resp_cookies:
                for k, values in resp_cookies.items():
                    values['SameSite'] = 'None'
                    values['secure'] = True
            response._cookies = resp_cookies
        except Exception as e:
            response.set_header('sso-message', 'samesite cookies error: %s' % str(e))


class ReportingSSOService:
    """
    移动报表单点登录
    """

    def __init__(self, app_name='dmp', is_external_secret=False, code=None):
        super().__init__()
        self._alg = 'HS256'
        self.app_name = app_name
        self.secret = (
            self._get_external_secret(code)
            if is_external_secret and code
            else config.get('JWT.sso_dashboard_secret', default_aes_key)
        )
        # 移动报表api
        m_reporting_api_domain = config.get('Open.mreporting_api')
        self.sso_redirect_url = '%s/api/sso-auth/login2' % m_reporting_api_domain

    def _encode_data(self, payload, secret=None):
        return base64.b64encode(jwt.encode(payload, secret or self.secret, self._alg).encode()).decode('utf-8')

    def _get_token(self, redirect_url):
        tenant = getattr(g, 'code')
        payload = {
            "user_code": tenant,  # 管理员账号（admin）
            "org_code": tenant,  # 企业代码 (mysoft)
            "redirect": redirect_url,  # 跳转地址(http://xxx.com)
            "exp": int(time.time()) + 120
            # "app_mark": ""  #要获取免登的应用标识，eg 要跳转到dmp 则传 dmp
        }
        return self._encode_data(payload)

    @staticmethod
    def _get_external_secret(code):
        project_data = get_external_secret(code)
        return project_data.get('external_secret_key')

    def generate_login_url(self, redirect_url):
        # api/sso-auth/login2?access_token=xxxx&_from=dmp
        token = self._get_token(redirect_url)
        return '%s?access_token=%s&_from=%s' % (self.sso_redirect_url, token, self.app_name)

    def _decode_data(self, signature, code=None):
        if not signature:
            return False, '缺少签名'
        try:
            jwt_token = base64.b64decode(signature)
            if auth_util.is_enable_skyline_auth(code):
                try:
                    k = NacosClient.get('appSecret')
                    md5_hash = hashlib.md5()
                    md5_hash.update(k.encode('utf-8'))
                    k = md5_hash.hexdigest()
                    data = jwt.decode(jwt_token, k, algorithms="HS256")
                except Exception as e:
                    logger.error(f'统一认证jwt解析异常,尝试用老认证方式解析:{str(e)}')
                    data = jwt.decode(jwt_token, self.secret, algorithms=self._alg)
            else:
                data = jwt.decode(jwt_token, self.secret, algorithms=self._alg)
        except DecodeError:
            return False, '无效的签名'
        except ExpiredSignatureError:
            return False, '签名过期'
        except binascii.Error:
            return False, '非法签名'
        return True, data

    @staticmethod
    def _handle_mobile_dashboard_url(redirect_url: str, biz_code: str):
        """
        临时方案，移动端的访问链接与pc端报告的有区别
        """
        if not redirect_url or not biz_code:
            return redirect_url

        dashboard_info = repository.get_one(
            table_name="dashboard", conditions={"biz_code": biz_code}, fields=["terminal_type"]
        )
        terminal_type = dashboard_info.get("terminal_type") if dashboard_info else ""

        normal_preffix = "/dataview/share"
        mobile_preffix = "/dataview-mobile/view"
        if terminal_type == DashboardTerminalType.Mobile.value and normal_preffix in redirect_url:
            redirect_url = redirect_url.replace(normal_preffix, mobile_preffix)
        return redirect_url

    def _set_same_site_info(self, response):
        return set_cookies_samesite_property(response)

    def _set_user_id(self, user):
        pass

    def get_report_id(self, redirect_url):
        try:
            from user.services.assistant_service import SuperWorkOAuthService

            if not redirect_url:
                return "", ""
            return SuperWorkOAuthService._get_report_id(redirect_url)
        except Exception as e:
            logging.error(f"get report id error: {str(e)}")
            return "", ""

    def _get_release_type(self, redirect_url, biz_code):
        from user.services.ingrate_service import _get_login_type

        report_id, report_type = "", ""
        try:
            redirect_url = self._handle_mobile_dashboard_url(redirect_url, biz_code)
            report_id, report_type = self.get_report_id(redirect_url)
        except BaseException:
            pass
        if not report_id or not report_type:
            return "", "", ""
        return report_id, _get_login_type(report_id, report_type), report_type

    def login(self, signature, request, response):
        success, result = self._decode_data(signature)
        if not success:
            return False, result, ''

        redirect_url = result.get('redirect', '')
        code = result.get('tenant_code')
        account = result.get('account')
        biz_code = result.get('biz_code', '')
        extend_yl_params = result.get('extend_yl_params', '')  # 云链在单点登陆的时候会多加一个参数,这个参数会原封不动传给数芯
        customize_roles = result.get('customize_roles', [])
        external_user_id = result.get('external_user_id')
        dataset_ids = result.get('dataset_ids', []) or []
        if not account or not code:
            return False, '缺少帐号数据', redirect_url
        # login
        g.code = code
        g.account = account
        set_grayscale_project(request, response, g.code)

        # 兼容自助报表第三方登录无用户id, 使用虚拟用户
        if customize_roles:
            # 若指定了角色，此时可以无需指定用户
            user = SELF_SERVICE_VIRTUAL_USER_INFO
            user['account'] = account
            user['name'] = account
            group_ids = []
        else:
            user = user_service.get_user_by_account(account, ['pwd', 'id', 'group_id'])
            if not user:
                return False, f'用户{account}在租户{code}下不存在', redirect_url
            group_ids = user_service.get_cur_user_group_ids()
        g.group_ids = group_ids
        # 赋值角色
        g.customize_roles = customize_roles
        # if hasattr(g, 'userid') and not g.userid:
        #     g.userid = user['id']
        self._set_user_id(user)
        # acc
        domain = request.host
        # 直接设置登录状态
        user_service.set_login_status(
            response,
            domain,
            code,
            user['id'],
            account,
            group_ids,
            **{"customize_roles": customize_roles, "external_user_id": external_user_id,
               "extend_yl_params": extend_yl_params, "dataset_ids": dataset_ids}
        )

        self._set_same_site_info(response)

        if customize_roles:
            return True, result, redirect_url

        # 临时处理方案，兼容移动端报告的访问链接，后续需要另外出解决方案
        try:
            redirect_url = self._handle_mobile_dashboard_url(redirect_url, biz_code)
        except BaseException:
            pass

        return True, result, redirect_url


class EcologySSOService:
    """泛微AO单点登录服务"""

    def __init__(self, secret_pc='ecology', secret_mobile='YFehjP', app_name='dmp'):
        super().__init__()
        self.app_name = app_name
        self.secret_pc = config.get('SSO_SECURITY.ecology_secret_pc', secret_pc)
        self.secret_mobile = config.get('SSO_SECURITY.ecology_secret_mobile', secret_mobile)
        self.secret_v2 = config.get('SSO_SECURITY.ecology_secret_v2', 'ecology')

    @staticmethod
    def encrypt(src, alg):
        alg = alg.lower()
        if alg not in ['sha1', 'sha256', 'md5']:
            raise Exception('未支持的加密算法: %s' % alg)
        encryptor = getattr(hashlib, alg)()
        encryptor.update(src if isinstance(src, bytes) else bytes(src, encoding='utf-8'))
        return str(encryptor.hexdigest())

    def validate(self, src, target, alg):
        encrypted_src = self.encrypt(src, alg)
        return encrypted_src == target

    def _loginv1(self, req, resp):
        account = req.params.get('loginid', None)
        stamp = req.params.get('stamp', '')
        divtype = req.params.get('divtype', None)
        signtype = req.params.get('signtype', None)
        code = project.set_correct_project_code(req.params.get('tenant', None))

        if not account or not code:
            return False, '缺少帐号数据'

        set_grayscale_project(req, resp, code)

        if isinstance(divtype, str) and divtype.upper() == 'MOBILE':
            # 来自移动端的授权请求
            # 验证token：token=SHA1(秘钥+usercode+stamp)
            token = req.params.get('token', None)
            src = self.secret_mobile + account + stamp
            is_validated = self.validate(src, token, signtype)
            if not is_validated:
                return False, '签名错误: %s' % token
        elif isinstance(divtype, str) and divtype.lower() == 'pc':
            # 来自PC端的授权请求
            # 数据库验证usercode==db.account和password==db.pwd
            try:
                account = EcologySSOEcurityHelper.decrypt(account, self.secret_pc)
            except (B64DecodeError, ValueError):
                return False, '无效的base64字符'
            if not account:
                return False, '无效的base64字符'
            # 预留
        else:
            return False, '未知的设备divtype=%s' % divtype

        succ, msg = self._set_login_status(req, resp, code, account)
        if not succ:
            return succ, msg

        return True, {'redirect': req.params.get('redirect', '/home')}

    def _loginv2(self, req, resp):
        account = req.params.get('UserCode', None)
        code = req.params.get('TenantCode', None)
        if not account or not code:
            return False, '缺少帐号数据'

        set_grayscale_project(req, resp, code)

        c = PBECrypt(self.secret_v2)
        try:
            account = c.decrypt(account)
        except Exception as e:
            return False, f'数据解密失败, {str(e)}'

        succ, msg = self._set_login_status(req, resp, code, account)
        if not succ:
            return succ, msg

        return True, {'redirect': req.params.get('PageUrl', '/home')}

    def _set_login_status(self, req, resp, code, account):
        # 设置code
        g.code = code

        # 设置g.account
        user = user_service.get_user_by_account(account, ['pwd', 'id', 'group_id'])
        if not user:
            return False, '用户:%s不存在' % account
        g.account = account
        group_ids = user_service.get_cur_user_group_ids()

        # 直接设置登录状态
        expires = int(config.get('JWT.expires')) + KEEP_LOGIN_DEFAULT_EXPIRE
        user_service.set_login_status(resp, req.host, code, user['id'], account, group_ids, **{"expires": expires})
        return True, ""

    def login(self, req, resp):
        divtype = req.params.get('divtype', None)
        if divtype:
            return self._loginv1(req, resp)
        else:
            return self._loginv2(req, resp)


class ERPSSOService(ReportingSSOService):
    DEFAULT_SECRET = '7Kh9uhMtcrfK6nk+l9JLf60zjA2UkbXH+xAI56aZBVY='

    def __init__(self, secret):
        super().__init__(app_name='erp')
        self.secret = secret
        self.sso_redirect_url = ''

    def _decode_data(self, signature, code=None):
        if not signature:
            return False, '缺少签名'
        code_info = get_yzs_config_by_erp_token(signature)
        code = code or code_info['code']
        try:
            # 根据租户秘钥解密
            secret = base64.b64decode(self.secret)
            data = jwt.decode(signature, secret, algorithms=self._alg, options={'verify_aud': False})
        except DecodeError:
            # 如果开启了统一认证，尝试用md5(app_secret)解密，否则认证失败
            if auth_util.is_enable_skyline_auth(code):
                try:
                    data = jwt.decode(signature, auth_util.get_jwt_secret(), algorithms=self._alg, options={'verify_aud': False})
                except Exception as ee:
                    return False, '无效的签名'
            else:
                return False, '无效的签名'
        except ExpiredSignatureError:
            return False, '签名过期'
        except binascii.Error:
            return False, '非法签名'
        except Exception as e:
            return False, '未知的jwt异常：%s' % str(e)

        data['account'] = data.get('userCode')
        if not data.get('tenantCode'):
            data['tenant_code'] = code
        else:
            data['tenant_code'] = data.get('tenantCode')
        return True, data


    # def _set_same_site_info(self, response):
    #     try:
    #         _cookies = dict(response._cookies)
    #         for k, values in _cookies.items():
    #             values['SameSite'] = 'None'
    #             values['secure'] = True
    #         response._cookies = _cookies
    #     except Exception as e:
    #         response.set_header('sso-message', 'samesite cookies error: %s' % str(e))

    def _set_user_id(self, user):
        g.userid = user['id']

    def login(self, signature, request, response):
        from user.repositories import ingrate_repository
        from user.services import ingrate_service
        success, result = self._decode_data(signature)
        if not success:
            return False, result, ''
        code = result.get('tenant_code')
        account = result.get('account')
        redirect_url = result.get('redirect', '')
        report_id = request.get_param('report_id')
        report_type = request.get_param('report_type')
        user_id = result.get('user_id')
        g.code = code
        g.account = account
        g.userid = user_id

        if report_id:
            try:
                login_type = ingrate_repository.get_dashboard_share_type(report_id)
                if login_type in [3, "3"]:
                    user_auth = 'view,download'
                    ingrate_service.auto_login(
                        request, response, report_id, code, account,
                        user_id, release_type=login_type, report_type=report_type or 'dashboard', extra=None, user_auth=user_auth
                    )
                    return True, result, redirect_url
            except Exception as e:
                logger.error(f"{e}")

        # erp右上角打开门户列表
        if getattr(g, 'site_from', '') == LoginFrom.ERPOldPortalList.value:
            dmp_user = user_service.get_user_by_account(account, ['id'])
            if dmp_user:
                # 以数见方式登录，走父类的数见登录
                _debugger.log(f'数见存在对应的用户<{code}:{account}>，将以数见方式登录')
            else:
                _debugger.log(f'数见不存在对应的用户<{code}:{account}>，将使用第三方用户登录')
                # 以第三方登录
                report_id = None
                report_type = 'portal_list'
                # 门户列表，以第三方进行用户登录
                release_type = 3
                extra = {
                    "login_from": LoginFrom.ERPOldPortalList.value,
                }
                ingrate_service.auto_login(
                    request, response, report_id, code, account, user_id,
                    release_type=release_type,
                    report_type=report_type,
                    extra=extra,
                )
                _debugger.log('使用第三方用户登录成功')
                return True, result, redirect_url

        return super().login(signature, request, response)

class ERPSkylineSSOService(ERPSSOService):
    def __init__(self, tenant_code, workbench_callback_code=None):
        super().__init__(secret=base64.b64encode(ERPSSOService.DEFAULT_SECRET.encode('UTF-8')))
        self.workbench_callback_code = workbench_callback_code
        self.tenant_code = tenant_code

    def login(self, signature, request, response):
        enable_skyline_auth = auth_util.is_enable_skyline_auth(self.tenant_code)
        if not enable_skyline_auth:
            return super().login(signature, request, response)

        if self.workbench_callback_code:
            access_payload = Workbench().get_token(self.workbench_callback_code)
            signature = self._encode_data({'userCode': access_payload.user_code, 'tenantCode': self.tenant_code,
                                         'user_id': access_payload.user_guid}, ERPSSOService.DEFAULT_SECRET)
            return super().login(signature, request, response)

        # 统一身份认证 如果开启天际统一认证跳转到登录服务
        return Workbench().to_login_callback_current(request, self.tenant_code)


    def _encode_data(self, payload, secret=None):
        return jwt.encode(payload, secret, algorithm='HS256')


class HDSSOService(ReportingSSOService):
    def __init__(self, secret):
        super().__init__(app_name='hd')
        self.secret = secret
        self.sso_redirect_url = ''

    def _decode_data(self, signature, code=None):
        if not signature:
            return False, '缺少签名'
        try:
            secret = base64.b64decode(self.secret)
            data = jwt.decode(signature, secret, algorithms=self._alg)
            data['account'] = data.get('user_code')
            code_info = get_yzs_config_by_hd_token(signature)
            data['tenant_code'] = code_info.get('code')
            return True, data
        except DecodeError:
            return False, '无效的签名'
        except ExpiredSignatureError:
            return False, '签名过期'
        except binascii.Error:
            return False, '非法签名'
        except Exception as e:
            return False, '未知的jwt异常：%s' % str(e)

    def _set_user_id(self, user):
        g.userid = user['id']

    def _set_same_site_info(self, response):
        if config.get('Domain.dmp').startswith("https"):
            set_cookies_samesite_property(response)


class PropertyJWTService(ERPSSOService):
    """业财接口使用， 只提供加解密token"""

    def _encode_data(self, payload):
        return jwt.encode(payload, base64.b64decode(self.secret.encode()), self._alg)

    def generate_token(self, app_id, expires: int = 7200):
        payload = {
            "app_id": app_id,
            "exp": int(time.time()) + expires
        }
        return self._encode_data(payload)

    def verify_token(self, token):
        return self._decode_data(token)


def get_redirect_url(target_redirect_url: str, failed_redirect_url: str = ''):
    """
    单点登录失败时，获取失败之后重定向地址
    :param target_redirect_url: 重定向地址
    :return:
    """
    if failed_redirect_url:
        return failed_redirect_url
    sso_default_redirect = config.get('SSO_SECURITY.sso_login_default_redirect_url')
    if sso_default_redirect:
        redirect_url = sso_default_redirect
    else:
        redirect_url = '/login?returnUrl=%s' % (target_redirect_url or '/home')
    return redirect_url


def get_tenant_by_jwt_token(token: str) -> dict:
    if token:
        try:
            payload = jwt.decode(token, '', algorithm=['HS256'], options={'verify_signature': False})
        except:
            raise UserError(message='token中payload不正确')
        customer_id = payload.get('customerGuid', '')
        if customer_id:
            p_config = get_customer_info(customer_id)
            if p_config:
                return p_config
            else:
                raise UserError(message='没有找到对应的customer_id')
        else:
            raise UserError(message='token中没有customer_id')
    else:
        raise UserError(message='token格式不正确')


def get_customer_info(customer_id):
    # https://www.tapd.cn/38229611/prong/stories/view/1138229611001070381  
    # 优先从project_yzh_config中获取，取不到再从project表获取，如果从project中获取时，secret为7Kh9uhMtcrfK6nk+l9JLf60zjA2UkbXH+xAI56aZBVY
    def from_yzs_config():
        return repository.get_data(
            "project_yzs_config", {"customer_id": customer_id},
            fields=["code", "tenant_id", "secret", "customer_id"],
            from_config_db=True
        ) or {}

    def from_project():
        data = repository.get_data(
            "project", {"customer_id": customer_id},
            fields=["code", "customer_id"], from_config_db=True
        )
        if data:
            data.setdefault('secret', '7Kh9uhMtcrfK6nk+l9JLf60zjA2UkbXH+xAI56aZBVY=')
            return data
        return {}

    return from_yzs_config() or from_project()


def render_sso_error_page(response, error_msg):
    error_msg = html.escape(error_msg)
    error_page = """<img src="https://mic-open.oss-cn-hangzhou.aliyuncs.com/dmp-error-svg/no-data.svg" style="width: 50%;margin: 100px auto 0;display: block; padding:0;">
    <h3 style="width: 100%;text-align: center;margin:0">{msg}</h3>"""
    response.set_header('Content-Type', 'text/html; charset=utf-8')
    response.body = error_page.format(msg=error_msg)


def erp_sso_authenticator(need_redirect=False):
    def wrapper1(func):
        def wrapper2(request, response, *args, **kwargs):
            kwargs['app_name'] = 'erp'
            mark_request_erp(request)
            auth_wrapper(request, response, func, *args, **kwargs)

        return wrapper2

    return wrapper1


def additional_header_token(func):
    def wrapper(request, response, *args, **kwargs):
        token_key = '__header_token'
        try:
            return func(request, response, *args, **kwargs)
        except falcon.http_status.HTTPStatus as fe:
            header_token_flag = str(request.params.get(token_key))
            # 设置了返回cookies，而且请求参数里面有这个参数
            if response._cookies and header_token_flag == '1':
                render_temporary_redirect_page(response, fe.headers.get('location') or '')
            # 不满足，走原始的重定向逻辑
            else:
                raise fe
    return wrapper


def mark_request_erp(request):
    # erp旧的集成方式
    # erp菜单栏直接跳转报告，放开所有报告权限
    if request.path == '/api/dashboard':
        g.site_from = LoginFrom.ERPOldDashboard.value
    elif request.path == '/api/sso':
        g.site_from = LoginFrom.ERPOldPortalList.value
    elif request.path == '/api/sso-auth':
        g.site_from = LoginFrom.ERPOldRedirectWay.value


def auth_wrapper(request, response, func, *args, **kwargs):
    token = request.get_param('token', '')
    report_id = request.get_param('report_id', '')
    workbench_callback_code = request.get_param('code', '')
    tenant_code = request.get_param('tenantCode', '') or request.get_param('tenant_code', '') or try_get_tenant_code(token)
    try:
        app_name = kwargs.get('app_name')
        if app_name == "erp":
            if auth_util.is_enable_skyline_auth(tenant_code) and is_not_mobile_view(report_id, tenant_code):
                sso_service = ERPSkylineSSOService(tenant_code, workbench_callback_code)
            else:
                code_info = get_yzs_config_by_erp_token(token, tenant_code)
                sso_service = ERPSSOService(secret=code_info['secret'])
        elif app_name == "hd":
            code_info = get_yzs_config_by_hd_token(token)
            sso_service = HDSSOService(secret=code_info['hd_secret'])
        else:
            sso_service = ReportingSSOService()

        success, data, redirect_url = sso_service.login(token, request, response)
        if success:
            return func(request, response, *args, **kwargs)
        else:
            logging.info('jwt verify failed: %s', data)
            return render_sso_error_page(response, data)
    except UserError as ue:
        return render_sso_error_page(response, ue.message)
    except falcon.http_status.HTTPStatus as fe:
        raise fe
    except Exception as e:
        logger.error(f'sso error: {traceback.format_exc()}')
        response.set_header('sso-message', str(e))
        return render_sso_error_page(response, '未知错误')

def is_not_mobile_view(report_id, tenant_code):
    if tenant_code:
        g.code = tenant_code
        return not report_id or get_dashboard_terminal_type(report_id) != "mobile_screen"
    return True

def try_get_tenant_code(token):
    payload = jwt.decode(token, '', algorithm=['HS256'], options={'verify_signature': False})
    return payload.get('tenantCode', '')  # NOSONAR


def get_yzs_config_by_erp_token(token: str, code=None) -> dict:  # NOSONAR
    if not token:
        raise UserError(message='token格式不正确')
    try:
        payload = jwt.decode(token, '', algorithm=['HS256'], options={'verify_signature': False})
        code = code or payload.get('tenantCode', '')  # NOSONAR
    except Exception:
        raise UserError(message='token中payload不正确')
    default = {'code': code, 'secret': ERPSSOService.DEFAULT_SECRET}
    p_config = None
    field_name = ''
    if code:
        field_name, field_value = "code", code
        p_config = get_yzs_config_by_field(field_name, field_value) or default
    if not p_config:
        customer_id = payload.get('customerGuid', '')
        if not customer_id:
            p_config = default
        else:
            field_name, field_value = "customer_id", customer_id
            p_config = get_yzs_config_by_field(field_name, field_value)
    if not p_config:
        raise UserError(message=f'没有找到对应的{field_name}')
    return p_config


def get_erp_sso_redirect_url(request, result):
    if request.path == '/api/sso':
        if result:
            # return get_my_first_portal_url()
            return f'/app_preview/pc_portal?code={g.code}'
        else:
            return get_redirect_url(target_redirect_url='')
    elif request.path == '/api/sso-auth':
        redirect_url_from_params = request.get_param('redirect_url', '')
        redirect_url = redirect_url_from_params if redirect_url_from_params else get_my_first_portal_url()
        if result:
            return redirect_url
        else:
            return get_redirect_url(target_redirect_url='')
    else:
        return get_redirect_url(target_redirect_url='')


def get_yzs_config_by_hd_token(token: str) -> dict:
    if not token:
        raise UserError(message='token格式不正确')
    try:
        payload = jwt.decode(token, '', algorithm=['HS256'], options={'verify_signature': False})
    except Exception:
        raise UserError(message='token中payload不正确')
    tenant_id = payload.get('tenant_id', '')
    if not tenant_id:
        raise UserError(message='token中没有tenant_id')
    p_config = get_yzs_config_by_field("tenant_id", tenant_id)
    if not p_config:
        raise UserError(message=f'没有找到对应的tenant_id')
    return p_config


def hd_sso_authenticator():
    def wrapper1(func):
        def wrapper2(request, response, *args, **kwargs):
            kwargs['app_name'] = 'hd'
            auth_wrapper(request, response, func, *args, **kwargs)

        return wrapper2

    return wrapper1


def get_yzs_config_by_field(field_name, field_value):
    p_config = repository.get_data(
        "project_yzs_config", {field_name: field_value},
        fields=["code", "tenant_id", "secret", "hd_secret", "customer_id"],
        from_config_db=True
    )
    return p_config


def get_my_first_portal_url():
    portals = user_service.get_my_portals()
    portals = [i for i in portals if i['name'] != "DMP"]
    if portals:
        first_portal_id = portals[0]['id']
        return '/app_preview/index/%s' % first_portal_id
    else:
        return f'/static/errorTip.html?msg={quote("当前用户没有任何报表的查询权限")}'


def get_dmp_auth_token_from_response(response):
    err_flag = ''
    try:
        dmp_auth_token = {item.key: item.value for item in response._cookies.values()}  # noqa
        if dmp_auth_token:
            dmp_auth_token = base64.b64encode(json.dumps(dmp_auth_token, ensure_ascii=False).encode()).decode()
            return dmp_auth_token
        else:
            err_flag = 'empty'
    except:
        err_flag = 'error'
        logger.error(f'登录提取token发生错误：{traceback.format_exc()}')

    if err_flag:
        err_map = {
            'empty': '登录时取到了空token，请联系管理员',
            'error': '登录提取token发生错误，请联系管理员',
        }
        render_sso_error_page(response, error_msg=err_map.get(err_flag, '错误的空提示'))

    return ''


# 渲染跳转中转页面
def render_temporary_redirect_page(response, origin_redirect_url):
    dmp_auth_token = get_dmp_auth_token_from_response(response)
    if not dmp_auth_token:
        return

    temporary_page = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title></title>
    </head>
    <body>
        
    </body>
    <script>
        const dmp_auth_token = '{dmp_auth_token}'
        const redirect_url = '{redirect_url}'
        localStorage.setItem('dmp_auth_token', dmp_auth_token)
        location.href = redirect_url
    </script>
    </html>
    """
    response.set_header('Content-Type', 'text/html; charset=utf-8')
    response.body = temporary_page.format(redirect_url=origin_redirect_url, dmp_auth_token=dmp_auth_token)
