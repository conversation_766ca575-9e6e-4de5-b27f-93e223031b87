import requests

from base import repository
from dmplib import config
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError

""" 对企业微信发送给企业后台的消息加解密示例代码.
@copyright: Copyright (c) 1998-2014 Tencent Inc.
"""
# ------------------------------------------------------------------------
import logging
import base64
import random
import hashlib
from urllib.parse import quote
import time
import struct
from Crypto.Cipher import AES
import xml.etree.cElementTree as ET
import socket
import json
from dmplib.db.mysql_wrapper import get_db as get_master_db
from dmplib.hug import g

WXBizMsgCrypt_OK = 0
WXBizMsgCrypt_ValidateSignature_Error = -40001
WXBizMsgCrypt_ParseXml_Error = -40002
WXBizMsgCrypt_ComputeSignature_Error = -40003
WXBizMsgCrypt_IllegalAesKey = -40004
WXBizMsgCrypt_ValidateCorpid_Error = -40005
WXBizMsgCrypt_EncryptAES_Error = -40006
WXBizMsgCrypt_DecryptAES_Error = -40007
WXBizMsgCrypt_IllegalBuffer = -40008
WXBizMsgCrypt_EncodeBase64_Error = -40009
WXBizMsgCrypt_DecodeBase64_Error = -40010
WXBizMsgCrypt_GenReturnXml_Error = -40011

"""
关于Crypto.Cipher模块，ImportError: No module named 'Crypto'解决方案
请到官方网站 https://www.dlitz.net/software/pycrypto/ 下载pycrypto。
下载后，按照README中的“Installation”小节的提示进行pycrypto安装。
"""


class FormatException(Exception):
    pass


def throw_exception(message, exception_class=FormatException):
    """my define raise exception function"""
    raise exception_class(message)


class SHA1:
    """计算企业微信的消息签名接口"""

    def get_sha1(self, token, timestamp, nonce, encrypt):
        """用SHA1算法生成安全签名
        @param token:  票据
        @param timestamp: 时间戳
        @param encrypt: 密文
        @param nonce: 随机字符串
        @return: 安全签名
        """
        try:
            sortlist = [token, timestamp, nonce, encrypt]
            sortlist.sort()
            sha = hashlib.sha1()
            sha.update("".join(sortlist).encode())
            return WXBizMsgCrypt_OK, sha.hexdigest()
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e)
            return WXBizMsgCrypt_ComputeSignature_Error, None


class XMLParse:
    """提供提取消息格式中的密文及生成回复消息格式的接口"""

    # xml消息模板
    AES_TEXT_RESPONSE_TEMPLATE = """<xml>
<Encrypt><![CDATA[%(msg_encrypt)s]]></Encrypt>
<MsgSignature><![CDATA[%(msg_signaturet)s]]></MsgSignature>
<TimeStamp>%(timestamp)s</TimeStamp>
<Nonce><![CDATA[%(nonce)s]]></Nonce>
</xml>"""

    def extract(self, xmltext):
        """提取出xml数据包中的加密消息
        @param xmltext: 待提取的xml字符串
        @return: 提取出的加密消息字符串
        """
        try:
            xml_tree = ET.fromstring(xmltext)
            encrypt = xml_tree.find("Encrypt")
            return WXBizMsgCrypt_OK, encrypt.text
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e)
            return WXBizMsgCrypt_ParseXml_Error, None

    def generate(self, encrypt, signature, timestamp, nonce):
        """生成xml消息
        @param encrypt: 加密后的消息密文
        @param signature: 安全签名
        @param timestamp: 时间戳
        @param nonce: 随机字符串
        @return: 生成的xml字符串
        """
        resp_dict = {
            'msg_encrypt': encrypt,
            'msg_signaturet': signature,
            'timestamp': timestamp,
            'nonce': nonce,
        }
        resp_xml = self.AES_TEXT_RESPONSE_TEMPLATE % resp_dict
        return resp_xml


class PKCS7Encoder():
    """提供基于PKCS7算法的加解密接口"""

    block_size = 32

    def encode(self, text):
        """ 对需要加密的明文进行填充补位
        @param text: 需要进行填充补位操作的明文
        @return: 补齐明文字符串
        """
        text_length = len(text)
        # 计算需要填充的位数
        amount_to_pad = self.block_size - (text_length % self.block_size)
        if amount_to_pad == 0:
            amount_to_pad = self.block_size
        # 获得补位所用的字符
        pad = chr(amount_to_pad)
        return text + (pad * amount_to_pad).encode()

    def decode(self, decrypted):
        """删除解密后明文的补位字符
        @param decrypted: 解密后的明文
        @return: 删除补位字符后的明文
        """
        pad = ord(decrypted[-1])
        if pad < 1 or pad > 32:
            pad = 0
        return decrypted[:-pad]


class Prpcrypt(object):
    """提供接收和推送给企业微信消息的加解密接口"""

    def __init__(self, key):

        # self.key = base64.b64decode(key+"=")
        self.key = key
        # 设置加解密模式为AES的CBC模式
        self.mode = AES.MODE_CBC

    def encrypt(self, text, receiveid):
        """对明文进行加密
        @param text: 需要加密的明文
        @return: 加密得到的字符串
        """
        # 16位随机字符串添加到明文开头
        text = text.encode()
        text = self.get_random_str() + struct.pack("I", socket.htonl(len(text))) + text + receiveid.encode()

        # 使用自定义的填充方式对明文进行补位填充
        pkcs7 = PKCS7Encoder()
        text = pkcs7.encode(text)
        # 加密
        cryptor = AES.new(self.key, self.mode, self.key[:16])
        try:
            ciphertext = cryptor.encrypt(text)
            # 使用BASE64对加密后的字符串进行编码
            return WXBizMsgCrypt_OK, base64.b64encode(ciphertext)
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e)
            return WXBizMsgCrypt_EncryptAES_Error, None

    def decrypt(self, text, receiveid, is_vertify_receiveid=True):
        """对解密后的明文进行补位删除
        @param text: 密文
        @receiveid:接受者
        @check_receiveid:
        @return: 删除填充补位后的明文
        """
        try:
            cryptor = AES.new(self.key, self.mode, self.key[:16])
            # 使用BASE64对密文进行解码，然后AES-CBC解密
            plain_text = cryptor.decrypt(base64.b64decode(text))
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e)
            return WXBizMsgCrypt_DecryptAES_Error, None
        try:
            pad = plain_text[-1]
            # 去掉补位字符串
            # pkcs7 = PKCS7Encoder()
            # plain_text = pkcs7.encode(plain_text)
            # 去除16位随机字符串
            content = plain_text[16:-pad]
            xml_len = socket.ntohl(struct.unpack("I", content[: 4])[0])
            xml_content = content[4: xml_len + 4]
            from_receiveid = content[xml_len + 4:]
        except Exception as e:
            logger = logging.getLogger()
            logger.error(e)
            return WXBizMsgCrypt_IllegalBuffer, None

        if is_vertify_receiveid and from_receiveid.decode('utf8') != receiveid:
            return WXBizMsgCrypt_ValidateCorpid_Error, None
        return 0, xml_content

    def get_random_str(self):
        """ 随机生成16位字符串
        @return: 16位字符串
        """
        return str(random.randint(1000000000000000, 9999999999999999)).encode()


class WXBizMsgCrypt(object):
    # 构造函数
    def __init__(self, token, encoding_aes_key, receive_id, is_vertify_receiveid):
        try:
            self.key = base64.b64decode(encoding_aes_key + "=")
            self.is_vertify_receiveid = is_vertify_receiveid
            assert len(self.key) == 32
        except:
            throw_exception("[error]: EncodingAESKey unvalid !", FormatException)
            # return ierror.WXBizMsgCrypt_IllegalAesKey,None
        self.token = token
        self.receive_id = receive_id

        # 验证URL
        # @param sMsgSignature: 签名串，对应URL参数的msg_signature
        # @param sTimeStamp: 时间戳，对应URL参数的timestamp
        # @param sNonce: 随机串，对应URL参数的nonce
        # @param sEchoStr: 随机串，对应URL参数的echostr
        # @param sReplyEchoStr: 解密之后的echostr，当return返回0时有效
        # @return：成功0，失败返回对应的错误码

    def verify_url(self, msg_signature, timestamp, nonce, echo_str):
        sha1 = SHA1()
        ret, signature = sha1.get_sha1(self.token, timestamp, nonce, echo_str)
        if ret != 0:
            return ret, None
        if not signature == msg_signature:
            return WXBizMsgCrypt_ValidateSignature_Error, None
        pc = Prpcrypt(self.key)
        ret, reply_echo_str = pc.decrypt(echo_str, self.receive_id, self.is_vertify_receiveid)
        return ret, reply_echo_str

    def encrypt_msg(self, reply_msg, nonce, timestamp=None):
        # 将企业回复用户的消息加密打包
        # @param sReplyMsg: 企业号待回复用户的消息，xml格式的字符串
        # @param sTimeStamp: 时间戳，可以自己生成，也可以用URL参数的timestamp,如为None则自动用当前时间
        # @param sNonce: 随机串，可以自己生成，也可以用URL参数的nonce
        # sEncryptMsg: 加密后的可以直接回复用户的密文，包括msg_signature, timestamp, nonce, encrypt的xml格式的字符串,
        # return：成功0，sEncryptMsg,失败返回对应的错误码None
        pc = Prpcrypt(self.key)
        ret, encrypt = pc.encrypt(reply_msg, self.receive_id)
        encrypt = encrypt.decode('utf8')
        if ret != 0:
            return ret, None
        if timestamp is None:
            timestamp = str(int(time.time()))
        # 生成安全签名
        sha1 = SHA1()
        ret, signature = sha1.get_sha1(self.token, timestamp, nonce, encrypt)
        if ret != 0:
            return ret, None
        xml_parse = XMLParse()
        return ret, xml_parse.generate(encrypt, signature, timestamp, nonce)

    def decrypt_msg(self, post_data, msg_signature, time_stamp, nonce):
        # 检验消息的真实性，并且获取解密后的明文
        # @param sMsgSignature: 签名串，对应URL参数的msg_signature
        # @param sTimeStamp: 时间戳，对应URL参数的timestamp
        # @param sNonce: 随机串，对应URL参数的nonce
        # @param sPostData: 密文，对应POST请求的数据
        #  xml_content: 解密后的原文，当return返回0时有效
        # @return: 成功0，失败返回对应的错误码
        # 验证安全签名
        xml_parse = XMLParse()
        ret, encrypt = xml_parse.extract(post_data)
        if ret != 0:
            return ret, None
        sha1 = SHA1()
        ret, signature = sha1.get_sha1(self.token, time_stamp, nonce, encrypt)
        if ret != 0:
            return ret, None
        if not signature == msg_signature:
            return WXBizMsgCrypt_ValidateSignature_Error, None
        pc = Prpcrypt(self.key)
        ret, xml_content = pc.decrypt(encrypt, self.receive_id, self.is_vertify_receiveid)
        return ret, xml_content


WXPROVIDERACCESSTOKEN = "dmp:wx:provider_access_token"


class WXWebAuth:
    def __init__(self, suite_type=None):
        self.suite_type = suite_type
        self.corp_id = config.get('Wwx.corp_id')
        self.provider_secret = config.get('Wwx.provider_secret')
        self.suite_id = None
        self.suite_secret = None
        self.token = None
        self.encoding_ses_key = None
        self.load_config()
        self.redis_suite_access_token_key = "dmp:wx:suite_access_token:" + suite_type

    def load_config(self):
        if self.suite_type == "web_app":
            # self.suite_id = "wwfd1b7f5aafbb13d7"  #config.get('Wwx.suite_id')
            self.suite_id = config.get('Wwx.suite_id')
            self.suite_secret = config.get('Wwx.suite_secret')
            self.token = config.get('Wwx.token')
            self.encoding_ses_key = config.get('Wwx.encoding_ses_key')
            self.is_vertify_receiveid = True
        elif self.suite_type == "agent_app":
            self.suite_id = config.get('Wwx.template_id')
            self.suite_secret = config.get('Wwx.template_secret')
            self.token = config.get('Wwx.template_token')
            self.encoding_ses_key = config.get('Wwx.template_encoding_ses_key')
            self.is_vertify_receiveid = False

    def wx_login(self, request, response, code):
        """
        企业微信入口
        :param request:
        :param response:
        :param code:
        :return:
        """
        from user.models import SingleUserLoginModel
        from user.services import user_service

        # TODO 获取企业微信用户信息, 后面可能需要保存用户信息
        self.get_qy_wx_user_info(code)

        login_model = SingleUserLoginModel()
        login_model.request = request
        login_model.response = response
        login_model.account = config.get("Wwx.project_code", "test")
        login_model.tenant_code = config.get("Wwx.project_code", "test")
        login_model.validate()
        user_service.single_user_login(login_model)

    def wx_auth_url(self):
        """
        构造auth2.0授权链接
        :return:
        """
        redirect_uri = quote(f"{config.get('Domain.dmp')}/api/user/wx/login")
        auth_url = f"https://open.weixin.qq.com/connect/oauth2/authorize?appid={self.suite_id}&redirect_uri={redirect_uri}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect"
        return auth_url

    def get_qy_wx_user_info(self, code):
        """
        获取企业微信用户信息
        :param code:
        :return:
        """
        try:
            res = requests.get(
                url="https://qyapi.weixin.qq.com/cgi-bin/service/getuserinfo3rd",
                params={
                    'code': code,
                    'suite_access_token': self._get_suite_access_token()
                }
            )
            logging.info(f"获取企业微信用户信息：{res.text}")
            user_data = res.json()
            return user_data
        except Exception as e:
            logging.error(f"获取企业微信用户信息失败: {str(e)}")

    def callback(self, request, **kwargs):
        """
        指令回调
        :param request:
        :param response:
        :param kwargs:
        :return:
        """
        try:
            info_type = "" #防止ret不等于0时，info_type没有声明，catch代码报错
            g.account = "admin"
            msg_signature = kwargs.get('msg_signature', "")
            timestamp = kwargs.get('timestamp', "")
            nonce = kwargs.get('nonce', "")
            data = request.bounded_stream.read().decode('utf8')
            logging.error(f"callback-data: {data}")
            wx_cpt = WXBizMsgCrypt(self.token, self.encoding_ses_key, self.suite_id, self.is_vertify_receiveid)
            ret, xml_content = wx_cpt.decrypt_msg(data, msg_signature, timestamp, nonce)
            if ret == 0:
                xml_tree = ET.fromstring(xml_content)
                info_type = xml_tree.find("InfoType").text.strip()
                #保存suit_ticket
                if info_type == "suite_ticket":
                    suite_ticket = xml_tree.find("SuiteTicket").text.strip()
                    self._set_suite_access_token(suite_ticket)
                elif info_type in ["create_auth", "change_auth", "reset_permanent_code"]:  #授权
                    auth_code = xml_tree.find("AuthCode").text.strip()
                    self._save_permanent_code(auth_code=auth_code)
                elif info_type == "cancel_auth":  #取消授权
                    auth_corp_id = xml_tree.find("AuthCorpId").text.strip()
                    self._delete_permanent_code(auth_corp_id)
            else:
                logging.error(f"ret: {ret}")
                logging.error(f"xml_content: {xml_content}")
                raise UserError(message="callback error")
        except Exception as e:
            log_data = {"msg_signature": msg_signature, "timestamp": timestamp, "nonce": nonce, "data": data, "xml_content": xml_content,
                        "info_type": info_type}
            logging.error("企业微信post失败,log_data:" + json.dumps(log_data) + "err:" + str(e))

    def get_message(self, **kwargs):
        try:
            msg_signature = kwargs.get('msg_signature', "")
            timestamp = kwargs.get('timestamp', "")
            nonce = kwargs.get('nonce', "")
            echo_str = kwargs.get('echostr', "")
            wx_cpt = WXBizMsgCrypt(self.token, self.encoding_ses_key, self.corp_id, self.is_vertify_receiveid)
            ret, xml_content = wx_cpt.verify_url(msg_signature, timestamp, nonce, echo_str)
            if ret != 0:
                raise UserError(message="ERR: VerifyURL ret: " + str(ret))
            return xml_content.decode('utf8')
        except Exception as e:
            log_data = {"msg_signature": msg_signature, "timestamp": timestamp, "nonce": nonce, "echo_str": echo_str, "token": self.token,
                        "encoding_ses_key": self.encoding_ses_key, "corp_id": self.corp_id}
            logging.error("企业微信get_message失败,log_data:" + json.dumps(log_data) + "err:" + str(e))

    def _save_permanent_code(self, auth_code):
        suite_access_token = self._get_suite_access_token()
        if suite_access_token:
            url = f"https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token={suite_access_token}"
            params = {"auth_code": auth_code}
            data = self._http_post(url, params)
            permanent_code = data.get('permanent_code')
            if data.get('auth_corp_info'):
                auth_corp_id = data.get('auth_corp_info').get('corpid')
                corp_name = data.get('auth_corp_info').get('corp_name')
                agent_id = ""
                if data.get('auth_info').get('agent'):
                    agent_id = data.get('auth_info').get('agent')[0].get('agentid')
                if permanent_code and auth_corp_id:
                    data = {"project_code": "", "auth_corp_id": auth_corp_id, "suite_type": self.suite_type, "permanent_code": permanent_code,
                            "corp_name": corp_name, "is_delete": 0, "agent_id": agent_id}
                    with get_master_db() as m_db:
                        m_db.delete('wx_project_auth', {"auth_corp_id": auth_corp_id, "suite_type": self.suite_type})
                        m_db.insert('wx_project_auth', data, auto_audit=False)

    def _delete_permanent_code(self, auth_corp_id):
        with get_master_db() as m_db:
            m_db.update("wx_project_auth", {"is_delete": 1}, {"auth_corp_id": auth_corp_id, "suite_type": self.suite_type})

    def _set_suite_access_token(self, suite_ticket):
        """
        刷新第三方应用凭证
        :return:
        """
        cache = RedisCache()
        params = {"suite_id": self.suite_id, "suite_secret": self.suite_secret, "suite_ticket": suite_ticket}
        usl = "https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token"
        data = self._http_post(usl, params)
        suite_access_token = data.get('suite_access_token')
        expires_in = data.get("expires_in")
        if suite_access_token and expires_in:
            cache.delete(key=self.redis_suite_access_token_key)
            cache.set(key=self.redis_suite_access_token_key, value=suite_access_token, time=expires_in - 300)

    def _get_suite_access_token(self):
        """
        获取第三方应用凭证
        :return:
        """
        cache = RedisCache()
        provider_access_token = cache.get(key=self.redis_suite_access_token_key)
        return provider_access_token.decode()

    def _get_provider_token(self):
        """
        获取服务商凭证
        :return:
        """
        # 先从缓存中获取
        cache = RedisCache()
        provider_access_token = cache.get(key=WXPROVIDERACCESSTOKEN)
        # 缓存中不存在，则从数据库中获取
        if not provider_access_token:
            url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_provider_token"
            corp_id = self.corp_id
            provider_secret = self.provider_secret
            if corp_id and provider_secret:
                params = {"corpid": corp_id, "provider_secret": provider_secret}
                requests.post(url, data=params, timeout=60)
                data = self._http_post(url, params)
                expires_in = data.get('expires_in')
                provider_access_token = data.get('provider_access_token')
                cache.set(key=WXPROVIDERACCESSTOKEN, value=provider_access_token, time=expires_in - 300)
        if isinstance(provider_access_token, bytes):
            provider_access_token = provider_access_token.decode()
        return provider_access_token

    def _http_get(self, url):
        try:
            response = requests.get(url, timeout=10)
            if response.status_code != 200:
                return None
            if response.text:
                data = response.json()
            return data
        except Exception as e:
            raise UserError(message='连接失败:' + str(e))

    def _http_post(self, url, params):
        try:
            response = requests.post(url, json=params, timeout=60)
            if response.status_code != 200:
                raise UserError(message='状态：' + str(response.status_code) + ' , ' + response.reason + '。' + response.text)
            if response.text:
                data = response.json()
            if not data or (data.get('errmsg') and data.get('errmsg') != "ok"):
                raise UserError(message='企业微信获取数据失败:' + str(data))
            return data
        except Exception as e:
            raise UserError(message='连接失败:' + str(e))


if __name__ == '__main__':
    wx_cpt = WXBizMsgCrypt(
        token='u6ZvsKo5TLaD7TFnnRHefn8',
        encoding_aes_key='CUDqynVyptqfMovjFhltyhLOA6bq7wEKI5o726usCgz',
        # receive_id='wx058a6f2349b0c4b7',
        receive_id='wwfd1b7f5aafbb13d7',  # daima
        is_vertify_receiveid=True
    )
    data = '<xml><ToUserName><![CDATA[wx058a6f2349b0c4b7]]></ToUserName><Encrypt><![CDATA[Gt+mZQ3nKopFvP3YdiMKLSsVKAFHd0/ljy5MV4AlG3MQlEXJBilPys2yhDFITPkAhccGfr5/TQm0JiB8U3EKCPRylWC5qt1kBUAQ7llAyFMc+nwwH2/vpJNwrVc0ymR5DdIiH6h9638zUMzQjeu/8RfRn2t0pK9MlDCGs7TzxlQJMC+DcVGKbaomzf1BEDPORVqTGScQC11pJ4X3Zx6yuBOt6RMQYGfhEsUtY8fbWtseAVvRRcCCPutJ4IJXEKVmpZXVpAs++OXDh+B9PY9elT3rBTobfojqSSicsOm32gy24HuYpYS77CGKO3mZpiJPuBXgV1bBrDgPy5W5TdpTBqtJVWbKox1H8F4fMqF0AmOTKGO3ccJl1wu8bT67zJW6]]></Encrypt><AgentID><![CDATA[]]></AgentID></xml>'
    msg_signature = 'b317faa8074d76f57265371c118e18a2ef37af0c'
    timestamp = '1658990817'
    nonce = '1659477498'
    ret, xml_content = wx_cpt.decrypt_msg(data, msg_signature, timestamp, nonce)
    print(ret, xml_content)