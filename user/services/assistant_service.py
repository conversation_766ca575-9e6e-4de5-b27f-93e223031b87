#!/usr/bin/env python3
# pylint: skip-file
"""
集成到第三方平台

## 集成企业微信

1. 第三方平台添加应用, 拿到secret(对应一个应用), corpid(对应一个企业微信).

2. 在dmp-admin中为租户添加应用,保存应用名称,corpid,secret,首页地址等信息.
   点击查看按钮可以显示应用的外链地址.如:https://dmp.mypaas.com.cn/api/user/assistant?appid=39e60421-8648-9d36-bad8-27426d8aa1ea&redirect=/

3. 在企业微信配置应用
   3.1 配置应用的地址
       企业应用->进入目标应用->工作台应用主页->设置url: 填入第2步的外链地址
   3.2 添加可信任回调域名
       企业应用->进入目标应用->网页授权及JS-SDK, 填入域名.如:dmp.mypaas.com.cn

## 集成云助手


"""
import json
import urllib.parse
from urllib.parse import urlparse, quote, urljoin
import hug
import requests
import logging
import regex as re

from base import repository
from base.enums import ThirdPartyAppCode, LoginFrom, EntryAction
from components import auth_util
from components.grayscale import set_grayscale_project
from components.weixin_api import WeiXinAPI
from dmplib.hug import g
from dmplib.saas import project
from dmplib.saas.errors import EmptyProjectCodeError
from dmplib.utils.errors import UserError
from dmplib.utils.jwt_login import LoginToken, conn_redis
from user.services import user_service
from integrate.services.third_party_service import (
    get_account_by_third_account, get_third_party_and_app_by_app_id,
    get_third_party_by_app_code, get_one_app_dashboard_info_by_party_id
)
from . import oauth_service
from dmplib import config
from components.ding_talk_api import DingTalkAPI
from components.url import url_add_param
from components.erpapi_api import MysoftERPAPI
from components.common_service import get_superportal_host

logger = logging.getLogger(__name__)
PATTERN = re.compile(r'.*([0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12})')


class OAuthRequestsException(Exception):
    pass


class OAuthInvalidPlatform(Exception):
    pass


class OAuthFailedGetPlatformUser(Exception):
    pass


class OAuthNotFoundApp(Exception):
    pass


def instance_oauth_service(request, response):
    agent = request.headers.get("USER-AGENT")
    if request.params.get('cloud_app_code') == ThirdPartyAppCode.JKGJ.value:
        return JKGJOAuthService(request, response)
    elif request.params.get('cloud_app_code') == ThirdPartyAppCode.YZS_NEW.value \
            and request.params.get('code') and request.params.get('tenant_id'):
        return YzsNewOAuthService(request, response)
    elif request.params.get('cloud_app_code') == ThirdPartyAppCode.YZS_BUILTIN.value \
            and request.params.get('code') and request.params.get('tenant_id'):
        return YzsBuiltInOAuthService(request, response)
    elif request.params.get('cloud_app_code') in [ThirdPartyAppCode.SuperWork.value, ThirdPartyAppCode.MYCYJG.value]:
        return SuperWorkOAuthService(request, response)
    elif request.headers.get("CODE") and request.headers.get("CORPID"):
        return AssistantOAuthService(request, response)
    elif request.params.get('__sso_code') and request.params.get('tenant_id'):
        return AssistantOAuthService(request, response)
    elif request.params.get('cloud_app_code') == ThirdPartyAppCode.DD.value:
        return DingTalkOAuthService(request, response)
    elif request.params.get('cloud_app_code') == ThirdPartyAppCode.ZHYQ_WEWORK.value:
        return ZHYQWXService(request, response)
    elif agent.find("MicroMessenger") > 0:
        return QywxOAuthService(request, response)
    else:
        # 通过查库获取平台类型
        appid = request.params.get("appid")
        app = oauth_service.get_oauth_config_by_app_id(appid)
        if app and app.get('platform') == LanlingOAuthService.platform:
            return LanlingOAuthService(request, response)
        raise OAuthInvalidPlatform("未知的平台")


def instance_oauth_by_app_code(request, response):
    if request.params.get("__CLIENT_BIZ_TYPE") or request.params.get("__from"):
        return SuperAppServiceOAuthService(request, response)
    else:
        app_code = request.params.get("cloud_app_code")
        if not app_code:
            raise UserError(message='cloud_app_code不能为空')
        if app_code in [ThirdPartyAppCode.SuperWork.value, ThirdPartyAppCode.MYCYJG.value]:
            return SuperWorkOAuthService(request, response)
    raise OAuthInvalidPlatform("未知的平台")


def login_by_yzs_provide(request, response):
    """
    云助手基础应用免登到数见
    :param request:
    :param response:
    :return:
    """
    try:
        if request.params.get('code') and request.params.get('tenant_id') and request.params.get("__from"):
            yzs_built_in_oauth = YzsBuiltInOAuthService(request, response)
            yzs_built_in_oauth.jump()
        raise UserError(message="数见云助手登录失败，未知的平台")
    except OAuthFailedGetPlatformUser:
        raise UserError(message="数见云助手登录失败，第三方用户未找到")
    except EmptyProjectCodeError:
        raise UserError(message=f'数见云助手登录失败，租户{yzs_built_in_oauth.project_code}不存在')


class OAuthService(object):
    """
    oauth base class

    1. oa = OAuthService()
       oa.load_config()

    """

    def __init__(self, request, response):
        self.request = request
        self.response = response
        self.appid = request.params.get("appid")
        self.project_code = request.params.get("project_code")
        self.redirect_url = request.params.get("redirect")
        self.config_id = request.params.get("config_id")
        self.third_party_id = request.params.get("third_party_id", '')
        self.platform = ""
        self.options = None
        self.corpid = ""
        self.agent_id = ''
        self.client_id = ''
        self.client_secret = ''
        self.url_get_token = ""
        self.url_get_login_info = ""
        set_grayscale_project(self.request, self.response, self.project_code)

    def get_user_account(self):
        raise NotImplementedError()

    def jump(self):
        # 如果已经登录,直接跳转到相应的页面. 登录失败, 返回错误信息(业务)
        self.load_config()
        token = self.request.cookies.get("token")
        logger.error(f'jump request.cookies.token: {token}')
        verified_token = None

        success, result = self.get_user_account()
        if not success:
            raise UserError(400, result)

        account = result
        logger.error(f'第三方应用用户账号{account}')
        if not account:
            raise OAuthFailedGetPlatformUser()
        # set_login_status
        self.project_code = project.set_correct_project_code(self.project_code)
        g.code = self.project_code
        # 是否换取第三方绑定用户
        if self.third_party_id:
            user_account = get_account_by_third_account(account, self.third_party_id)
            account = user_account if user_account else account
            logger.error('DMP中映射的用户账号：' + account)
        # 验证之前登录的用户账号是否和现在一致
        if token:
            verified_token = LoginToken().verify_token(token)
            if verified_token and verified_token.get('account', '') == account:
                logger.error('cookie中的用户账号：' + verified_token.get('account', ''))
                hug.redirect.to(self.redirect_url)
                return

        if verified_token:
            logger.error(f'jump verified_token: {json.dumps(verified_token, ensure_ascii=False)}')
            user_id = verified_token.get('id', '')
            code = verified_token.get('code', '')
            logger.error('cookie中的用户id:' + user_id + ';cookie中的租户code:' + code)
            if user_id and code:
                prefix = LoginToken._LoginToken__login_cache_prefix  # noqa
                cache = conn_redis(prefix)
                login_caching_id = LoginToken()._caching_id(user_id, token)
                logger.error('用户token缓存id：' + login_caching_id)
                cache.delete(login_caching_id)

        g.account = account
        user_row = user_service.get_user_by_account(account, ["id", "group_id"])
        group_ids = user_service.get_cur_user_group_ids()
        if user_row:
            user_service.set_login_status(
                self.response,
                self.request.host,
                self.project_code,
                user_row["id"],
                account,
                group_ids,
                **{"expires": 3600 * 24 * 100000},
            )

        hug.redirect.to(self.redirect_url)

    def load_config(self):
        # 简讯订阅的跳转使用corip，取密码
        if self.config_id:
            g.code = self.project_code
            options = oauth_service.get_third_party_app_config(self.config_id)
            if options:
                self.corpid = options["corp_id"]
                self.client_secret = options["app_secret"]
        elif self.third_party_id:
            g.code = self.project_code
            third_party_info = get_third_party_and_app_by_app_id(self.appid)
            if third_party_info:
                self.agent_id = third_party_info.get('agent_id')
                self.client_id = third_party_info.get('app_id')
                self.client_secret = third_party_info.get('app_secret')
                self.corpid = third_party_info.get('corp_id')
        else:
            app = oauth_service.get_app(self.appid)
            if not app:
                raise OAuthNotFoundApp("appid:%s" % self.appid)
            self.project_code = app["code"]
            options = oauth_service.get_oauth_config(self.platform, self.appid)
            if options:
                self.corpid = options["corpid"]
                self.client_secret = options["app_secret"]

            # 优先使用指定的url
            if not self.redirect_url:
                self.redirect_url = app["default_page_url"].strip() or "/"

    def _post(self, url, payload):
        r = None
        try:
            headers = {}
            if auth_util.is_enable_skyline_auth():
                headers[auth_util.TENANT_KEY] = self.project_code
            r = requests.post(url, json=payload, timeout=10, headers=headers)
            if r.status_code != 200:
                raise OAuthRequestsException("Failed to requests.post(%s). status_code: %d" % (url, r.status_code))
            return r.json()
        except Exception as e:
            raise OAuthRequestsException("调用%s接口失败. url:(%s). error: %s" % (self.platform, url, str(e)))
        finally:
            logger.info(f'assistant POST: url: {url}, payload: {payload}, result: {r.text if r else None}')

    def _get(self, url):
        r = None
        try:
            r = requests.get(url, timeout=10)
            if r.status_code != 200:
                raise OAuthRequestsException("Failed to requests.get(%s). status_code: %d" % (url, r.status_code))
            return r.json()
        except Exception as e:
            raise OAuthRequestsException("调用%s接口失败. url:(%s). error: %s" % (self.platform, url, str(e)))
        finally:
            logger.info(f'assistant GET: url: {url}, result: {r.text if r else None}')


class AssistantOAuthService(OAuthService):
    """
    云助手登录服务
    """
    assistant_domain = config.get('Yzs.domain', 'https://www.fdccloud.com')

    def __init__(self, request, response):
        super().__init__(request, response)
        """
        参数：get_user_mode 获取用户账号的模式
        可选值 1：保持原有的应用实现用户接口 2：采用老集成用户获取接口 /api/tenant-open/get-wzs-user-info
        业务背景：
        在云助手扫码打开数见报告时，采用新集成的应用免登时，云助手没有传递“新集成用户获取接口”所需的code参数
        这样就无法请求新集成用户获取接口得到用户，这种情况下改调用老集成用户获取接口。
        老集成用户获取接口
        /api/tenant-open/get-wzs-user-info
        新集成用户获取接口
        /api/tenant-open/sso-user-info
        """
        self.get_user_mode = 1
        self.platform = "云助手"
        host = self.assistant_domain
        assistant_domain = host[: len(host) - 1] if host.endswith('/') else host
        self.url_get_token = assistant_domain + "/api/third-app-open/get-access-token"
        self.url_get_login_info = assistant_domain + "/api/tenant-open/get-wzs-user-info"

    def get_access_token(self):
        payload = {"corpid": self.corpid, "corpsecret": self.client_secret}
        result = self._post(self.url_get_token, payload)
        logger.error(f'yzs {self.platform} url_get_token: {self.url_get_token}, payload: {payload}, result: {result}')
        if result["errcode"] != 0:
            logger.error(
                "从%s获取token返回错误. errcode: %s, errmsg: %s" % (self.platform, result["errcode"], result["errmsg"])
            )
            return ""
        return result["data"]["access_token"]

    def get_auth_code(self):
        return self.request.headers.get("CODE") if self.request.headers.get("CODE", None) \
            else self.request.params.get('__sso_code')

    def get_user_account(self):
        token = self.get_access_token()
        if not token:
            return False, None
        payload = {"code": self.get_auth_code()}

        result = self._post(self.url_get_login_info + "?access_token=" + token, payload)
        logger.info(
            f'url_get_login_info: {self.url_get_login_info}, token: {token}, payload: {payload}, result: {result}')
        if result["errcode"] != 0:
            logger.error(
                "从{}获取用户返回错误. errcode: {}, errmsg: {}".format(self.platform, result["errcode"], result["errmsg"]))
            return False, result["errmsg"]

        return True, result["data"]["user_code"]


class YzsNewOAuthService(AssistantOAuthService):
    """
    云助手新集成
    登录服务
    """
    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "云助手-新集成"
        host = self.assistant_domain
        assistant_domain = host[: len(host) - 1] if host.endswith('/') else host
        self.assistant_domain = assistant_domain
        self.url_get_login_info = assistant_domain + "/api/tenant-open/sso-user-info"

    def get_auth_code(self):
        if self.get_user_mode == 2:
            return super().get_auth_code()

        return self.request.params.get('code')

    def get_user_account(self):
        # 云助手扫码场景使用老集成用户获取接口
        if self.get_user_mode == 2:
            self.url_get_login_info = self.assistant_domain + "/api/tenant-open/get-wzs-user-info"
            return super().get_user_account()

        token = self.get_access_token()
        if not token:
            return False, None
        payload = {"code": self.get_auth_code()}
        get_user_url = self.url_get_login_info + "?access_token=" + token
        result = self._post(get_user_url, payload)
        logger.error(f'yzs {self.platform} url_get_login_info, url: {get_user_url} '
                     f'payload: {json.dumps(payload, ensure_ascii=False)}, '
                     f'result: {json.dumps(result, ensure_ascii=False)}')
        if result["errcode"] != 0:
            logger.error(
                "yzs 从{}获取用户返回错误. errcode: {}, errmsg: {}".format(self.platform, result["errcode"], result["errmsg"]))
            return False, result["errmsg"]
        # 从云助手基础应用数见应用点击跳转到移动门户场景，没有数见租户编码，需要设置
        if not self.project_code:
            self.project_code = result["data"]["tenant_code"]
        return True, result["data"]["user_code"]

    def load_config(self):
        super().load_config()
        # 增加拍照参数
        self.redirect_url = self._add_snap_params()

    def _add_snap_params(self):
        """
        从云助手跳转过来会请求中会有两个问号参数，request 获取 redirect值时不会获取完整的回调url
        例如：
        https://dmp-test.mypaas.com.cn/api/user/assistant?appid=3a0a86ec-9ca5-4780-b42b-2b0e36ba0e66&cloud_app_code=11&project_code=test
        &redirect=/dataview-mobile/view/497877cfa3e848838677e3a24dd8a181?code=test&dmp_send_date=2023-04-27&send_time=2023-04-27+15%3A16%3A00&snap_id=3a0ad3be-7fd3-f791-6ee2-81a046fb
        现在手动加上拍照相关参数
        :return:
        """
        snap_params = {}
        dmp_send_date = self.request.params.get("dmp_send_date", "")
        send_time = self.request.params.get("send_time", "")
        snap_id = self.request.params.get("snap_id", "")
        if dmp_send_date:
            snap_params["dmp_send_date"] = dmp_send_date
        if send_time:
            snap_params["send_time"] = send_time
        if snap_id:
            snap_params["snap_id"] = snap_id
        if snap_params:
            return url_add_param(self.redirect_url, snap_params)
        return self.redirect_url


class YzsBuiltInOAuthService(YzsNewOAuthService):
    """
    云助手基础应用免登数见
    """
    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "云助手-基础应用"
        self.corpid = request.params.get('tenant_id')

    def load_config(self):
        app_code = ThirdPartyAppCode.YZS_BUILTIN.value
        # 获取cloud_app配置
        cloud_app_info = repository.get_one('cloud_apps', {'app_code': app_code}, from_config_db=True)
        if not cloud_app_info:
            raise UserError(message='没有找到云助手基础应用渠道信息')

        self.client_id = cloud_app_info.get('channel_app_id')
        self.client_secret = cloud_app_info.get('channel_app_secret')
        api_host = remove_host_slash(cloud_app_info.get('api_host'))
        self.url_get_token = f"{api_host}/api/yzs-app-open/get-access-token"
        # 没有重定向url，则默认跳转到默认移动门户
        if not self.redirect_url:
            from app_menu.services.application_service import generate_path_by_platform
            # 固定门户id
            app_id = "3a0a8579-3aac-e348-b3e7-2414d3d46077"
            suffix_path = generate_path_by_platform(app_id, "mobile_screen")
            self.redirect_url = f"{config.get('Domain.dmp')}{suffix_path}"
        else:
            self.redirect_url = self._add_snap_params()


class LanlingOAuthService(OAuthService):
    domain = config.get('LoginConfig.lanling_domain', '')
    platform = '蓝凌'

    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = '蓝凌'

    def get_code(self):
        # 已经获取到code
        code = self.request.params.get('accessCode')
        if code:
            return code

        params = {
            'response_type': 'code',
            'client_id': self.corpid,
            'redirect_uri': self.request.url
        }
        host = f'{self.domain}/sso/oauth/authorize'
        authorize_url = f'{host}?{urllib.parse.urlencode(params)}'
        hug.redirect.to(authorize_url)

    def get_access_token(self, code):
        url = f'{self.domain}/sso/oauth/accessToken'
        try:
            rsp = requests.get(url, params={
                'client_id': self.corpid,
                'client_secret': self.client_secret,
                'grant_type': 'authorization_code',
                'redirect_uri': self.request.url,
                'code': code
            }, timeout=10)
            rv = rsp.json()
            if rv.get('error'):
                raise UserError(400, message=rv.get('error_description'))
            return rv.get('access_token')
        except UserError as e:
            raise e from e
        except Exception as e:
            raise OAuthRequestsException("调用%s接口失败. url:(%s). error: %s" % (self.platform, url, str(e)))

    def get_uid(self, token):
        url = f'{self.domain}/sso/oauth/userInfo'
        try:
            rsp = requests.get(url, params={
                'access_token': token
            })
            rv = rsp.json()
            if rv.get('ret') != 0:
                raise UserError(400, message=rv.get('msg'))
            return rv.get('uid')
        except UserError as e:
            raise e from e
        except Exception as e:
            raise OAuthRequestsException("调用%s接口失败. url:(%s). error: %s" % (self.platform, url, str(e)))

    def get_user_account(self):
        code = self.get_code()
        token = self.get_access_token(code)
        uid = self.get_uid(token)
        return True, uid


class QywxOAuthService(OAuthService):
    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "企业微信"

    def get_access_token(self):
        return WeiXinAPI(self.corpid, self.client_secret, agentid="").get_access_token()

    def get_user_account(self):
        wx_auth_code = self.request.params.get("code")
        if wx_auth_code and self.request.params.get("state"):
            # 从微信端跳转回来
            token = self.get_access_token()
            url = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + token + "&code=" + wx_auth_code
            logger.error(f"get_user_account: {url}")
            result = self._get(
                "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + token + "&code=" + wx_auth_code
            )
            logger.error(f"get_user_account res: {result}")
            if result["errcode"] == 0:
                return True, result.get("UserId")
            else:
                return False, result["errmsg"]

        dmp_host = config.get('Domain.dmp')
        if not dmp_host:
            raise UserError(message="请配置Domain.dmp")
        redirect_uri = urljoin(dmp_host.strip(), self.request.relative_uri)

        api_url = (
                "https://open.weixin.qq.com/connect/oauth2/authorize?appid="
                + self.corpid
                + "&redirect_uri="
                + urllib.parse.quote(redirect_uri)
                + "&response_type=code&scope=snsapi_base&agentid=&state=1#wechat_redirect"
        )
        logger.error(f"api_url: {api_url}")
        hug.redirect.to(api_url)


class DingTalkOAuthService(OAuthService):
    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "钉钉"

    def get_user_account(self):
        auth_code = self.request.params.get("authCode")
        if auth_code:
            ding_talk_api = DingTalkAPI(self.corpid, self.client_secret, self.client_id, self.agent_id, auth_code)
            success, user_id = ding_talk_api.get_user_info()
            return success, user_id
        redirect_uri = urllib.parse.quote(
            self.request.uri.replace(self.request.prefix, config.get("Domain.dmp").strip("/")))
        api_url = f'{config.get("Domain.dmp").strip("/")}/static/ding_talk_auth.html?corp_id={self.corpid}&redirect_uri={redirect_uri}'
        hug.redirect.to(api_url)


class JKGJOAuthService(OAuthService):
    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "接口管家"

    def get_user_account(self):
        request_param = self.request.params
        mysoft_erp_api = MysoftERPAPI(third_party_id=self.third_party_id)
        ret = mysoft_erp_api.get_erp_api_auth(request_param=request_param)
        if ret.get('success', 0) == "1":
            return True, ret.get('usercode')
        return False, ret


class SuperWorkOAuthService(AssistantOAuthService):

    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "超级工作台"
        self.cloud_app_code = request.params.get("cloud_app_code", '3')
        # 超级APP、超级工作台的host
        self.callback_host = request.params.get("__callback")

    def load_config(self):
        if not self.cloud_app_code:
            raise UserError(message='cloud_app_code不能为空')
        g.code = self.project_code
        # 获取cloud_app配置
        cloud_app_info = repository.get_one('cloud_apps', {'app_code': self.cloud_app_code}, from_config_db=True)
        if self.appid and self.third_party_id:
            third_party_info = get_third_party_and_app_by_app_id(self.appid)
            if third_party_info:
                self.agent_id = third_party_info.get('agent_id')
                self.client_id = third_party_info.get('app_id')
                self.client_secret = third_party_info.get('app_secret')
                self.corpid = third_party_info.get('corp_id')
        else:
            # 获取渠道ID
            third_party_info = get_third_party_by_app_code(self.cloud_app_code)
            if not third_party_info:
                raise UserError(message='没有找到对应的渠道信息')
            # 获取渠道应用
            third_party_app_info = get_one_app_dashboard_info_by_party_id(third_party_info.get('id'))
            if not third_party_app_info:
                raise UserError(message='没有找到对应的应用信息')
            self.agent_id = third_party_app_info.get('agent_id')
            self.client_id = third_party_app_info.get('app_id')
            self.client_secret = third_party_app_info.get('app_secret')
            self.corpid = third_party_info.get('corp_id')
        api_host = cloud_app_info.get('api_host')
        # 集成场景优先获取url中__callback参数host，其次获取版本中配置的超级APP，产业建管host
        if self.cloud_app_code in [ThirdPartyAppCode.SuperWork.value, ThirdPartyAppCode.MYCYJG.value]:
            api_host = self.callback_host if self.callback_host else get_superportal_host(cloud_app_info, False)

        app_host = api_host.rstrip('/')
        self.url_get_token = "{}/api/third-app-open/get-access-token".format(app_host)
        self.url_get_login_info = "{}/api/tenant-open/get-wzs-user-info".format(app_host)
        logger.info(f'url_get_token: {self.url_get_token}, url_get_login_info: {self.url_get_login_info}')

    @staticmethod
    def _get_report_id(redirect_url):
        if "%2F" in redirect_url:
            redirect_url = urllib.parse.unquote(redirect_url)
        report_type = "dashboard"
        if "portal" in redirect_url or ("app/index" in redirect_url):
            report_type = 'portal'
        parsed_url = urlparse(redirect_url)

        match = re.match(PATTERN, parsed_url.path)
        if match.group():
            report_id = match.group(1)
            return report_id, report_type
        raise UserError(code=5001, message="未获取到 report_id, url:{}".format(redirect_url))

    def jump(self):
        from user.services import ingrate_service

        self.load_config()

        success, result = self.get_user_account()
        if not success:
            raise UserError(400, result)

        account = result

        # 获取report_id
        report_id, report_type = self._get_report_id(self.redirect_url)
        # 免登
        ingrate_service.auto_login(self.request, self.response, report_id, g.code, account, '', report_type=report_type)

        # jump
        return hug.redirect.to(self.redirect_url)


def remove_host_slash(host: str):
    return str(host).strip().rstrip('/')


class SuperAppServiceOAuthService(AssistantOAuthService):

    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "超级app"
        self.cloud_app_code = self._get_app_code(
            request.params.get("__CLIENT_BIZ_TYPE") or request.params.get("__from"))
        self._cache = None
        self.request = request
        self.token = request.params.get("access_token")
        self.report_id = request.params.get("report_id")
        self.report_type = request.params.get("type") or request.params.get("report_type", "dashboard")
        self.user_auth = request.params.get("user_auth")
        # 超级APP进入数见操作类型，可选值，scan：扫码，默认：连接
        self.action = request.params.get("__action__", EntryAction.Link.value)
        # 超级APP、超级工作台的host
        self.callback_host = request.params.get("__callback")

    @staticmethod
    def _get_app_code(biz_type):  # NOSONAR
        # 超级app
        if biz_type in ["gzt", "erpapp"]:  # NOSONAR
            app_code = 3
        # 产业建设
        elif biz_type in ['cyjs', 'cyjsapp']:  # NOSONAR
            app_code = 4
        else:
            app_code = 3
        return app_code

    def load_config(self):
        if not self.cloud_app_code:
            raise UserError(message='cloud_app_code不能为空')
        g.code = self.project_code
        # 获取cloud_app配置
        cloud_app_info = repository.get_one('cloud_apps', {'app_code': self.cloud_app_code}, from_config_db=True)
        if not cloud_app_info:
            raise UserError(message='没有找到对应的渠道信息')
        if not self.token:
            raise UserError(message='缺少参数token')
        self.client_id = cloud_app_info.get('channel_app_id')
        self.client_secret = cloud_app_info.get('channel_app_secret')
        # 集成场景优先获取url中__callback参数host，其次获取版本中配置的超级APP，产业建管host
        api_host = self.callback_host if self.callback_host else get_superportal_host(cloud_app_info, False)
        api_host = remove_host_slash(api_host)
        self.url_get_token = f"{api_host}/openapi/v1/via-provider/get-token"
        self.url_get_login_info = f"{api_host}/openapi/v1/via-provider/get-user-info-by-token"
        logger.info(f'url_get_token: {self.url_get_token},  url_get_login_info: {self.url_get_login_info}')

    @property
    def redis_cache(self):
        if not self._cache:
            self._cache = conn_redis(f"superapp")
        return self._cache

    def get_access_token(self):
        key = f"{self.cloud_app_code}:{self.client_id}"
        access_token = self.redis_cache.get(key)
        if access_token:
            return access_token.decode()
        result = self._post(self.url_get_token, {"channel_id": self.client_id, "api_token": self.client_secret})
        logger.info(f'result: {result}')
        if result["errcode"] != 0:
            logger.error(
                "从%s获取token返回错误. errcode: %s, errmsg: %s" % (self.platform, result["errcode"], result["errmsg"])
            )
            return ""
        data = result.get("data")
        if not data:
            raise UserError(message=f"get access_token error: {result}")
        access_token = data.get("access_token")
        expires_in = data.get("expires_in")
        self.redis_cache.set(key, access_token, int(expires_in / 2))
        return access_token

    def get_user_account(self):
        access_token = self.get_access_token()
        if not access_token:
            return False, None

        result = self._post(self.url_get_login_info, {"access_token": access_token, "token": self.token})
        if result["errcode"] != 0:
            logger.error(
                "从{}获取用户返回错误. errcode: {}, errmsg: {}".format(self.platform, result["errcode"], result["errmsg"]))
            return False, f"从{self.platform}获取用户失败：" + result["errmsg"] or ''

        return True, result["data"]

    def _get_report_id(self):
        if not self.report_id:
            raise UserError(code=5001, message="缺少参数 report_id")
        return self.report_id, self.report_type

    def _get_redirect_url(self, report_id, report_type="dashboard"):
        """
        获取跳转地址
        """
        if report_type == 'portal':
            from app_menu.services.application_service import generate_path_by_platform
            # redirect_url = f"{config.get('Domain.dmp')}/app/index/{report_id}"

            application_info = repository.get_one("application", conditions={"id": report_id}, fields=["platform"])
            app_platform = application_info.get("platform") if application_info else ""
            suffix_path = generate_path_by_platform(report_id, app_platform)
            redirect_url = f"{config.get('Domain.dmp')}{suffix_path}"
        elif report_type == 'portal_list':
            redirect_url = f"{config.get('Domain.dmp')}/mobile_portal?code={g.code}"
        else:
            from user.services import ingrate_service

            redirect_url = ingrate_service.get_redirect_url(report_id)

        return redirect_url

    def jump(self):
        from user.services import ingrate_service

        try:

            self.load_config()

            success, result = self.get_user_account()
            if not success:
                raise UserError(400, result)
            g.code = result.get("channel_code")
            account = result.get("user_code")
            user_id = result.get("user_guid")

            # 免登进入移动门户列表
            release_type = None
            if self.report_type == 'portal_list':
                report_id = None
                report_type = self.report_type
                # 门户列表，以第三方进行用户登录
                release_type = 3
            else:
                # 获取report_id
                report_id, report_type = self._get_report_id()

            extra = {
                "login_from": LoginFrom.SuppApp.value
            }
            # 超级APP通过扫码打开的报告，不需要进行报告鉴权
            if self.action == EntryAction.Scan.value:
                self.user_auth = 'view,download'
            # 免登
            ingrate_service.auto_login(
                self.request, self.response, report_id, g.code, account,
                user_id, release_type=release_type, report_type=report_type, extra=extra, user_auth=self.user_auth
            )

            # 透传用户传过来的参数
            redirect_url = self._get_redirect_url(report_id, report_type)

            kwargs = self.request.params
            if 'access_token' in kwargs:
                del kwargs['access_token']

            redirect_url = url_add_param(redirect_url, kwargs)
        except UserError as e:
            return hug.redirect.to(f'/static/errorTip.html?msg={quote(e.message)}')
        except EmptyProjectCodeError:
            return hug.redirect.to(f'/static/errorTip.html?msg={quote(f"租户{g.code}不存在")}')

        # jump
        if str(self.request.params.get('support_cors')) == '1':
            from user.services import reporting_sso_service
            return reporting_sso_service.render_temporary_redirect_page(self.response, origin_redirect_url=redirect_url)
        else:
            return hug.redirect.to(redirect_url)


class ZHYQWXService(SuperWorkOAuthService):
    # 集成文档：https://docs.mingyuanyun.com/pages/viewpage.action?pageId=133592588

    def __init__(self, request, response):
        super().__init__(request, response)
        self.platform = "智慧园区企业微信集成"
        self.cloud_app_code = ThirdPartyAppCode.ZHYQ_WEWORK.value
        self.project_code = request.params.get('tenantCode')
        self.token = request.params.get('token')
        self.redirect_url = request.params.get('redirect')
        self.url_get_user = ''
        self.report_type = 'portal'
        self.report_id = ''

    def load_config(self):
        if not self.token:
            raise UserError(message='缺少参数token')
        if not self.project_code:
            raise UserError(message='缺少参数tenantCode')
        if not self.redirect_url:
            raise UserError(message='缺少参数redirect参数')
        g.code = self.project_code
        # 获取cloud_app配置
        cloud_app_info = repository.get_one('cloud_apps', {'app_code': self.cloud_app_code}, from_config_db=True)
        if not cloud_app_info:
            raise UserError(message='没有找到对应的渠道信息')

        api_host = remove_host_slash(cloud_app_info.get('api_host') or '')
        get_user_api = cloud_app_info.get('get_user_api') or ''
        self.url_get_user = urljoin(api_host, get_user_api)
        logger.info(f'url_get_user: {self.url_get_user}')

    def get_user_account(self):
        result = ''
        try:
            res = requests.post(
                url=self.url_get_user,
                json={'token': self.token},
                headers={
                    "Content-Type": "application/json",
                    "tenantCode": self.project_code
                }
            )
            if res.status_code != 200:
                return False, f"获取用户信息失败: {res.text}"

            result = res.json()

            if result.get('code') not in [0, '0']:
                return False, f"获取用户信息失败: {res.text}"

            return True, result.get('data')
        except Exception as e:
            return False, f"获取用户信息失败: {e}"
        finally:
            msg = f"""
            {self.platform} \n 
            request: url: {self.url_get_user}, token: {self.token}, tenantCode: {self.project_code} \n
            response: {result}
            """
            logger.info(msg)

    def jump(self):
        from user.services import ingrate_service

        try:

            self.load_config()

            success, result = self.get_user_account()
            if not success:
                raise UserError(400, result)
            g.code = self.project_code
            account = result.get("staffCode")
            user_id = result.get("staffId")

            # 获取report_id
            report_id, report_type = self._get_report_id(self.redirect_url)

            extra = {
                "login_from": LoginFrom.ZHYQ.value
            }

            # 免登
            ingrate_service.auto_login(
                self.request, self.response, report_id, g.code, account,
                user_id, report_type=report_type, extra=extra
            )

            # 透传用户传过来的参数
            redirect_url = self.redirect_url

            kwargs = self.request.params
            if 'access_token' in kwargs:
                del kwargs['access_token']

            redirect_url = url_add_param(redirect_url, kwargs)
        except UserError as e:
            return hug.redirect.to(f'/static/errorTip.html?msg={quote(e.message)}')
        except EmptyProjectCodeError:
            return hug.redirect.to(f'/static/errorTip.html?msg={quote(f"租户{g.code}不存在")}')

        return hug.redirect.to(redirect_url)


if __name__ == "__main__":
    redirect_url1 = "https%3A//dmp-dbeta.mypaas.com.cn/dataview-mobile/portal/3a040636-dfad-acba-2fed-cdfccd6dcf10"
    parsed_url = urlparse(redirect_url1)
    report_id = parsed_url.path.split("/")[-1]
    print(report_id)
