#!/usr/bin/env python
# -*- coding: utf-8 -*-

import datetime
import jwt
import requests
import json
import logging
import hashlib
import urllib.parse

from base import repository
from base.enums import EmailTemplateType
from dmplib import config
from dmplib.hug import g
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError, Error
from dmplib.utils.strings import seq_id
from dmplib.constants import ADMIN_ROLE_ID
from components import mail
from user.models import DeveloperSecretModel, DeveloperModel
from user.services.user_service import set_login_status


class DeveloperError(Error):
    def __init__(self, code=None, message='', name=''):
        """
        业务异常基类, 返回message给用户
        :param int code:
        :param str message:
        """
        self.code = code or 500
        self.message = message
        self.name = name
        super().__init__()


class DeveloperAuth(object):

    def __init__(self):
        self.authority = config.get('Rdc.authority')
        self.client_id = config.get('Rdc.client_id')
        self.enable_auth = config.get('Rdc.enable_auth')
        self.rdc_url = config.get('Rdc.rdc_url')
        self.certification_types = config.get('Rdc.certification_types')
        self.redirect_uri = config.get('Rdc.redirect_uri') or 'https://bi.mypaas.com/static/rdc-callback.html'
        self.enable_developer_login_mode = config.get('Rdc.enable_developer_login_mode', 0)

    def get_developer_config(self):
        return {
            'authority': self.authority, 'client_id': self.client_id, 'enable_auth': self.enable_auth,
            'redirect_uri': self.redirect_uri, 'enable_developer_login_mode': self.enable_developer_login_mode
        }

    @staticmethod
    def get_jwt_payload(token):
        payload = jwt.decode(token, '', algorithms=['RS256'], options={'verify_signature': False})
        if payload.get('user'):
            user_code = payload.get('name') or ''
            default_user_info = {'Name': user_code, 'UserName': user_code, 'Email': payload.get('email')}
            try:
                payload['user'] = json.loads(payload.get('user'))
                if not payload.get('user'):
                    payload['user'] = default_user_info
            except Exception as e:
                logging.error(f'json序列化失败：{str(e)}')
                payload['user'] = default_user_info
        return payload

    @staticmethod
    def validate_rdc_token(token):
        payload = {}
        if token:
            logging.info(f'开发者登录回调获取到的token:{token}')
            try:
                payload = DeveloperAuth.get_jwt_payload(token)
            except Exception as e:
                raise UserError(message=f'开发者认证失败，token无效，请联系系统管理员,e: {str(e)}')

            exp_date = datetime.datetime.fromtimestamp(payload.get('exp'))
            if exp_date <= datetime.datetime.now() or payload.get('iss') != config.get('Rdc.authority'):
                raise UserError(message='开发者已过期或认证失败，请联系系统管理员')
            id_dev = 'true' if config.get('App.runtime') in ('test', 'dev', 'unitest') else 'false'
            response = requests.get(
                f"{config.get('Rdc.rdc_url')}/rdc-service/api/v2/developer-cert?customerId=00000000-0000-0000-0000-000000000000&isDev={id_dev}",
                {},
                headers={"Authorization": f'Bearer {token}'},
            )
            logging.info(f'获取开发者证书接口返回：{response.text}')
            data = json.loads(response.text).get('result')
            certification_types = config.get('Rdc.certification_types')
            is_auth = False
            if certification_types:
                dmp_certification_types = certification_types.split(',')
                user_certification_types = data.get('certificationTypes')
                for user_cer in user_certification_types:
                    if str(user_cer) in dmp_certification_types:
                        is_auth = True
                        break
            if not is_auth:
                raise DeveloperError(code=1, message=f'开发者认证失败，当前用户无数见开发者证书', name=payload.get('user').get('Name'))
        return payload


class Developer(object):

    ADMIN_DEVELOPER_ROLE_ID = '3a0c2cc8-60a2-7850-555b-be5d9358c43b'

    def __init__(self, access_token=None):
        self.auth = DeveloperAuth()
        self.access_token = access_token
        self.developer_model = None
        self.expires = 3*24*60*60
        g.account = ''

    def developer_login(self, request, response):
        try:
            # 验证token是否正确,并判断是否有数见证书
            self.check_token()
            # 验证开发者是否绑定过租户
            self._check_developer_tenant()
            # 登录开发者账号到数见
            self._dmp_login(request, response)
            return {'error_code': 3}
        except DeveloperError as de:
            return {'developer_name': de.name, 'error_code': de.code}

    def _dmp_login(self, request, response):
        g.code = self.developer_model.tenant_code
        # 查询用户是否在对应租户中，如果不存在则插入数据
        self._init_dmp_user()
        # 登录用户到数见
        self._set_login(request, response)
        return True

    def _set_login(self, request, response):
        domain = request.host or urllib.parse.urlsplit(config.get('Domain.dmp')).netloc
        user_id = self._create_user_id()
        set_login_status(
            response, domain, self.developer_model.tenant_code, user_id,
            self.developer_model.account, [], **{"expires": self.expires, "is_developer": 1}
        )
        # 写入登录日志
        self._set_login_log()
        return True

    def _set_login_log(self):
        import datetime
        # 记录登录日志
        log_data = {'tenant_code': self.developer_model.tenant_code, 'developer_account': self.developer_model.developer_account}
        data = repository.get_data_scalar('developer_login_log', log_data, 'tenant_code', from_config_db=True)
        if not data:
            repository.add_data('developer_login_log', log_data, from_config_db=True)
        else:
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            repository.update_data('developer_login_log', {'created_on': now}, log_data, from_config_db=True)

    def check_token(self):
        if not self.access_token:
            raise UserError(message='access_token不能为空')
        auth_info = self.auth.validate_rdc_token(self.access_token)
        self.developer_model = Developer.prepare_user_info(auth_info.get('user'))

    @staticmethod
    def prepare_user_info(user_info):
        info = {
            'tenant_code': '', 'developer_account': user_info.get('UserName'),
            'name': user_info.get('Name'), 'email': user_info.get('Email'),
            'phone': user_info.get('Phone'), 'account': f'developer_{user_info.get("UserName")}',
        }
        return DeveloperModel(**info)

    def _check_developer_tenant(self):
        auth_code_list = []
        # 查询开发者绑定的租户列表
        tenant_list = repository.get_column('developer_auth_user', {'developer_account': self.developer_model.developer_account}, ['code'], from_config_db=True) or []
        if tenant_list:
            # 获取开启了开发者认证的租户
            auth_code_list = repository.get_column('project', {'code': tenant_list, 'is_rdc_auth': 1}, ['code'], from_config_db=True)
        if not auth_code_list:
            raise DeveloperError(code=2, message='开发者没有绑定租户记录，请先绑定租户', name=self.developer_model.name)
        # 获取最近登录的日志
        history = repository.get_one(
            'developer_login_log', {'developer_account': self.developer_model.developer_account, 'tenant_code': auth_code_list}, ['tenant_code'],
            order_by=('created_on', 'desc'), from_config_db=True
        ) or {}
        self.developer_model.tenant_code = history.get('tenant_code') or auth_code_list[0]

    @staticmethod
    def get_project_list(model):
        from system.repositories.developer_repository import get_project_list
        return get_project_list(model)

    @staticmethod
    def get_developer_user(tenant_code):
        from system.repositories.developer_repository import get_developer_list
        if not tenant_code:
            raise UserError(message='需要指定租户')
        developer_list = get_developer_list(tenant_code) or []
        all_email = [developer.get('email') for developer in developer_list]
        # 获取租户的审核人信息
        reviewer_list = repository.get_list('developer_reviewer', {'tenant_code': tenant_code},  ['email', 'name', 'account'], 'is_default asc', from_config_db=True) or []
        for reviewer in reviewer_list:
            if reviewer.get('email') not in all_email:
                developer_list.insert(
                    0,
                    {'email': reviewer.get('email'), 'developer_name': reviewer.get('name'), 'developer_code': reviewer.get('account')}
                )
        return developer_list

    def check_admin_secret(self, model: DeveloperSecretModel, request, response):
        # 查询秘钥是否合法
        data = repository.get_one('admin_developer_secret', {'secret': model.secret, 'is_active': model.is_active, 'tenant_code': model.tenant_code}, from_config_db=True)
        if not data:
            raise UserError(message='当前秘钥不合法或已激活，请重试')
        # 激活秘钥，并将用户初始化到当前租户的开发者
        repository.update_data('admin_developer_secret', {'is_active': 1}, {'secret': model.secret, 'is_active': 0}, from_config_db=True)
        # 获取用户信息
        data = DeveloperAuth.validate_rdc_token(model.token).get('user') or {}
        self.developer_model = Developer.prepare_user_info(data)
        self.developer_model.tenant_code = model.tenant_code
        # 绑定开发者和租户数见的关系
        self._bind_developer_for_tenant()
        # 初始化数见账号
        self._init_dmp_user(is_admin=True)
        # 登录用户到数见
        self._set_login(request, response)
        return True

    @staticmethod
    def send_email_for_developer(tenant_code, tenant_name, token, select_developer_code):
        g.code = tenant_code
        data = DeveloperAuth.validate_rdc_token(token).get('user') or {}
        # 查询开发者的邮箱信息
        developer = repository.get_one('user', {'account': select_developer_code, 'is_developer': 1}, ['email', 'name'])
        if not developer:
            developer = repository.get_one('developer_reviewer', {'account': select_developer_code, 'tenant_code': tenant_code}, ['email', 'name'], from_config_db=True)
            if not developer:
                raise UserError(message='没有找到开发者的邮箱信息或该用户已不是开发者，请重新选择')
        user_info = {
            'name': data.get('Name'), 'developer_account': data.get('UserName'), 'account': f'developer_{data.get("UserName")}',
            'email': data.get('Email'), 'phone': data.get('Phone'), 'tenant_code': tenant_code
        }
        user_secret = jwt.encode(user_info, config.get('JWT.secret', '0UZR4h#@'))
        params = {
            "token": user_secret,
            "page_type": "authorize",
            "email": user_info.get('email'),
            "tenant_code": tenant_code,
            "tenant_name": tenant_name
        }
        params = urllib.parse.urlencode(params)
        check_url = f"{config.get('Domain.dmp')}/static/rdc-callback.html?{params}"
        replace_data = {
            '{开发者}': user_info.get('name') or '',
            '{邮箱}': user_info.get('email') or '',
            '{租户名称}': tenant_name or '',
            '{授权URL}': check_url,
        }
        Developer.send_email(EmailTemplateType.InitDeveloper.value, replace_data, developer.get('name'), developer.get('email'))
        return True

    def check_email_token(self, token, role_ids):
        try:
            user_info = jwt.decode(token, config.get('JWT.secret', 'YC2UFKz7'), algorithms="HS256")
            self.developer_model = DeveloperModel(**user_info)
            self.developer_model.validate()
        except Exception as e:
            raise UserError(message=f'token验证失败:{str(e)}')
        # 绑定开发者和租户数见的关系
        self._bind_developer_for_tenant()
        # 判断该用户是否已经是开发者
        if self._have_account_for_tenant():
            raise UserError(message='该用户已存在，请不要重复激活')
        # 初始化数见账号
        self._init_dmp_user(role_ids=role_ids)

    def _have_account_for_tenant(self):
        g.code = self.developer_model.tenant_code
        data = repository.get_one('user', {'account': self.developer_model.account}, ['account'])
        return True if data else False

    def _bind_developer_for_tenant(self):
        # 判断是否已经绑定过了
        result = repository.get_one('developer_auth_user', {'code': self.developer_model.tenant_code, 'developer_account': self.developer_model.developer_account}, from_config_db=True)
        if not result:
            data = {'id': seq_id(), 'code': self.developer_model.tenant_code, 'account': self.developer_model.account, 'developer_account': self.developer_model.developer_account}
            # 记录开发者与租户的绑定关系
            repository.add_data('developer_auth_user', data, from_config_db=True)

    def _init_dmp_user(self, role_ids=None, is_admin=False):
        from user.repositories.user_repository import encrypt_field
        # 将开发者账号初始化到数见用户
        g.code = self.developer_model.tenant_code
        user_id = self._create_user_id()
        dmp_user = {
            'id': user_id, 'name': self.developer_model.name, 'account': self.developer_model.account,
            'mobile': encrypt_field(self.developer_model.phone), 'email': self.developer_model.email, 'is_developer': 1
        }
        # 判断账号是否已经存在
        data = repository.get_one('user', {'account': self.developer_model.account}, ['account', 'is_disabled'])
        if not data:
            repository.add_data('user', dmp_user)
            if is_admin:
                # 给默认角色
                Developer.init_admin_role(user_id)
            else:
                Developer.init_developer_role(role_ids, user_id)
                # 获取申请成功邮件模板
                replace_data = {
                    '{企业域地址}': config.get('Domain.dmp') or '',
                    '{企业代码}': self.developer_model.tenant_code or '',
                }
                if role_ids != [self.ADMIN_DEVELOPER_ROLE_ID]:
                    self.send_email(
                        EmailTemplateType.SuccessDeveloper.value, replace_data, dmp_user.get('name'), dmp_user.get('email')
                    )
            return
        if data.get('is_disabled') == 1:
            raise DeveloperError(code=4, message='该用户已被禁用，请联系管理员', name=self.developer_model.name)

    @staticmethod
    def init_admin_role(user_id):
        # 给默认角色
        default_data = [{'role_id': ADMIN_ROLE_ID, 'user_id': user_id}]
        repository.replace_list_data('user_user_role', default_data, ['role_id', 'user_id'])

    @staticmethod
    def init_developer_role(role_ids, user_id):
        if not role_ids:
            return
        data = []
        for role_id in role_ids:
            data.append({'role_id': role_id, 'user_id': user_id})
        if data:
            repository.replace_list_data('user_user_role', data, ['role_id', 'user_id'])

    def _create_user_id(self):
        ticks = hashlib.md5(f'{self.developer_model.tenant_code}-{self.developer_model.account}'.encode("utf-8")).hexdigest()
        return '''{}-{}-{}-{}-{}'''.format(ticks[:8], ticks[8:12], ticks[12:16], ticks[16:20], ticks[20:])

    @staticmethod
    def get_developer_tenant_list(dmp_user_account):
        from system.repositories.developer_repository import get_auth_developer_project
        return get_auth_developer_project(dmp_user_account) or []

    def change_developer_login_tenant(self, request, response, account, tenant_code):
        g.code = tenant_code
        if hasattr(g, 'cache'):
            g.cache = RedisCache()
        where = {'account': account, 'code': tenant_code}
        # 查询对应租户是否有用户的认证信息
        data = repository.get_one('developer_auth_user', where, ['code', 'developer_account', 'account'], from_config_db=True)
        if not data:
            raise UserError(message='当前开发者没有对应租户的权限，请申请后重试')
        self.developer_model = DeveloperModel(**data)
        self.developer_model.tenant_code = tenant_code
        domain = request.host or urllib.parse.urlsplit(config.get('Domain.dmp')).netloc
        user_id = repository.get_data_scalar('user', {'account': account}, 'id') or ''
        if not user_id:
            raise UserError(message='对应租户没有生成开发者对应的数见用户，请重新使用开发者登录该租户后重试')
        set_login_status(
            response, domain, tenant_code, user_id, account, [], **{"expires": self.expires, "is_developer": 1}
        )
        # 写入登录日志
        self._set_login_log()

    @staticmethod
    def is_developer_by_account(account=None):
        if not account:
            account = getattr(g, 'account', None)
        if not account:
            return False
        is_developer = getattr(g, 'is_developer', None)
        if is_developer is None:
            is_developer = repository.get_data_scalar('user', {'account': account}, 'is_developer')
        return True if is_developer else False

    @staticmethod
    def send_email(email_type, replace_data, send_name, email):
        email_info = repository.get_one('email_template', {'type': email_type}, ['subject', 'content'], from_config_db=True)
        if not email_info:
            raise UserError(message='没有获取到对应的邮件模板')
        content = mail.replace_content(email_info.get('content'), replace_data)
        # 向开发者发送邮件
        mail.send(
            mail.Mail(
                subject=email_info.get('subject') or '开发者通知', body=content,
                receiver=[mail.MailContact(name=send_name, mail=email)],
            ),
            subtype='html',
        )

    def is_admin_user(self, tenant_code):
        try:
            self.check_token()
            data = repository.get_one('users', {'account': self.developer_model.developer_account}, from_config_db=True)
            if data:
                return 'admin'
            # 判断是否为默认的审核人员
            data = repository.get_one('project', {'code': tenant_code}, ['admin_email'], from_config_db=True) or {}
            if data.get('admin_email') == self.developer_model.email and self.developer_model.email:
                return 'reviewer'
            return 'normal'
        except DeveloperError as de:
            raise UserError(message=de.message)

    def auth_admin_user(self, tenant_code, request, response):
        try:
            role = self.is_admin_user(tenant_code)
            if role == 'normal':
                raise UserError(message='当前用户不是系统管理员，不能直接开通对应租户的权限')
            # 默认审核人为管理员角色，其余为运营管理员角色
            role_id = ADMIN_ROLE_ID if role == 'reviewer' else self.ADMIN_DEVELOPER_ROLE_ID
            self.developer_model.tenant_code = tenant_code
            # 绑定开发者和租户数见的关系
            self._bind_developer_for_tenant()
            # 初始化数见账号
            self._init_dmp_user(role_ids=[role_id])
            # 登录用户到数见
            self._set_login(request, response)
            return True
        except DeveloperError as de:
            raise UserError(message=de.message)
