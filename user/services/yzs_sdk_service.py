import time
import hashlib
from Crypto.Cipher import AES
import base64
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import algorithms
from dmplib.utils.errors import UserError
from dmplib import config
from base.dmp_constant import YZS_CHANNEL_ID
import hug


class User:
    _host = ""

    def __init__(self):
        self._host = config.get('Yzs.domain')
        self.channel_id = '10'

    def get_user(self, tenant_id: str, app_code: str, app_secret: str, _from: str, redirect_url: str):
        current_time = str(time.time())
        ticket = self.__get_ticket(tenant_id, app_code, app_secret, self.channel_id, _from, current_time)
        dmp_domain = config.get('Domain.dmp')
        url = f'{self._host}/api/yzs-sdk/get-user?tenant_id={tenant_id}&app_code={app_code}&channel_id={YZS_CHANNEL_ID}&from={_from}' \
              f'&time={current_time}&ticket={ticket}&redirect_url={dmp_domain}{redirect_url}'
        return url

    # 解密
    def decrypt(self, key, cipher_text):
        new_key = self.__get_mysoft_key(key)
        aes = AesHelper(new_key, "ECB")
        return aes.decrypt(cipher_text)

    # 获取ticket
    def __get_ticket(self, tenant_id: str, app_code: str, app_secret: str, channel_id: str, _from: str, current_time: str):
        ticket = tenant_id + app_code + channel_id + _from + current_time
        new_key = self.__get_mysoft_key(app_secret)
        aes = AesHelper(new_key, "ECB")
        aes_encrypt = aes.encrypt(ticket)
        return hashlib.md5(aes_encrypt).hexdigest()

    # 秘钥加密算法
    def __get_mysoft_key(self, key):
        new_key = bytearray(32)
        md = hashlib.md5()
        md.update(key.encode('UTF-8'))
        hash_ = md.digest()
        self.__copy_array(hash_, 0, new_key, 0, 16)
        self.__copy_array(hash_, 0, new_key, 15, 16)
        return bytes(new_key)

    # 秘钥填充算法
    def __copy_array(self, src_array, src_index, dest_array, dest_index, length):
        for i in range(src_index, src_index + length, 1):
            dest_array[dest_index + i] = src_array[i]


# AES封装
class AesHelper:
    BLANK_STRING = ""

    # 初始化
    def __init__(self, key, model, iv="", encode_='UTF-8'):
        self.encode_ = encode_
        self.model = {'ECB': AES.MODE_ECB, 'CBC': AES.MODE_CBC}[model]
        self.key = key
        self.PADDING = lambda s: s + (self.bs - len(s) % self.bs) * chr(self.bs - len(s) % self.bs)
        if model == 'ECB':
            self.aes = AES.new(self.key, self.model)
        elif model == 'CBC':
            self.aes = AES.new(self.key, self.model, iv)

    # 填充算法
    def __pkcs7_padding(self, data):
        if not isinstance(data, bytes):
            data = data.encode()
        padder = padding.PKCS7(algorithms.AES.block_size).padder()
        padded_data = padder.update(data) + padder.finalize()
        return padded_data

    # 加密
    def encrypt(self, text):
        if text is None or len(text) < 1:
            raise UserError(message='加密字符串不能为空')
        encrypt_text = self.aes.encrypt(self.__pkcs7_padding(text))
        return base64.b64encode(encrypt_text)

    # 解密
    def decrypt(self, text):
        if text is None or len(text) < 1 or self.key is None or len(self.key) < 1:
            return self.BLANK_STRING
        text = base64.b64decode(text)
        decrypt_text = self.aes.decrypt(text).decode(self.encode_)
        return decrypt_text[0:decrypt_text.rfind('}') + 1]


def test_get_user():
    user = User()
    tenant_id = "my56a9c966df069"
    app_code = "8013"
    app_secret = "Mysoft5fa8e9eeddf05"
    _from = "multi_wx1"
    redirect_url = "/api/user/yzs_report_login"
    url = user.get_user(tenant_id, app_code, app_secret, _from, redirect_url)
    hug.redirect(url)


def test_decrypt():
    user = User()
    text = 'u1c9mENBA3ovzNrCBKptFVW5O5Y9Sd1Wbs+ubAIgHvM+HTT98adfhxeNqqFr6jml6tAUzGLQ+D4w3fYIo4D+wTAwebkn3NZZORaN8F1c5rDUUAMcSgZSnizVXjgVyU0RVEOfZYSa9Du2A6DFI7QpSWtlA7gaHpDhIAtqHT/dlgzKJB4MHEVYSm6ouidl13MEWxoHeqo51rc/PEtaiPsQ7A=='
    key = 'Mysoft5fa8e9eeddf05'
    user.decrypt(key, text)


# 测试
if __name__ == '__main__':
    test_get_user()
    test_decrypt()
