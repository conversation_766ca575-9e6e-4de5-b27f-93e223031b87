#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :ingrate_service.py
# @Time      :2022/5/16 14:20
# <AUTHOR>
import hashlib
import json
from urllib.parse import quote

import jwt
import hug
import time
from loguru import logger
import random
from urllib import parse

from components import auth_util
from components.app_hosts import AppHosts
from dmplib.nacos_client import NacosClient
from dmplib.redis import conn
from dmplib import config
from dmplib.utils.errors import UserError
from dmplib.saas.errors import EmptyProjectCodeError
from dmplib.hug import g
from user.repositories import ingrate_repository
from user.models import IngrateModel, WorkbenchJwt
from components.grayscale import set_grayscale_project
from dmplib.saas.project import set_correct_project_code
from base.enums import ApiParamSysValue, AddFuncType, LoginFrom, TokenAuthFrom, ApplicationTypeAccessReleasedSourceStr, \
    SkylineApps
from base.dmp_constant import SELF_SERVICE_VIRTUAL_USER_INFO
from components.ingrate_platform import IngratePlatformApi
from components.url import url_add_param


def superportal_verify(jwt_token):
    use_old_auth_jwt = False
    try:
        try:
            k = auth_util.get_jwt_secret()
            data = jwt.decode(jwt_token, k, algorithms="HS256")
        except Exception as e:
            logger.error(f'以统一认证模式解密jwt失败,尝试使用老模式解密:{jwt_token}, {str(e)}')
            app_secret = config.get("Superportal.app_secret")
            if not app_secret:
                raise UserError(message="未配置超级工作台秘钥")
            data = jwt.decode(jwt_token, app_secret, algorithms="HS256")
            use_old_auth_jwt = True
        return data, use_old_auth_jwt
    except Exception as e:
        raise UserError(message="token校验失败：{}".format(str(e)))


def _get_login_type(report_id, report_type='dashboard'):
    """
    获取报告发布类型
    0:不限制,1:通过密码,2:限制用户 3:三方控制 4:角色控制
    """
    if report_type == 'portal':
        return ingrate_repository.get_portal_share_type(report_id)
    else:
        return ingrate_repository.get_dashboard_share_type(report_id)


def _oa_way(request, response, code, account, user_id, report_id, is_developer=None, extra=None, user_auth=None,
            report_type=''):
    from user.services import user_service

    if not account or not code:
        raise UserError(message="token中没有account、tenant信息")
    g.code = code
    g.account = account
    set_grayscale_project(request, response, g.code)
    user = user_service.get_user_by_account(account, ['pwd', 'id', 'group_id'])
    if not user:
        raise UserError(message='%s账号在数见中不存在' % account)
    g.userid = user.get("id")
    group_ids = user_service.get_cur_user_group_ids()
    g.group_ids = group_ids
    if is_developer:
        g.is_developer = is_developer
    domain = request.host
    # 直接设置登录状态
    token = user_service.set_login_status(
        response,
        domain,
        code,
        user['id'],
        account,
        group_ids,
        is_developer=is_developer,
        is_auto_login=True,  # 是否是免登场景
    )
    return token


def _third_way(request, response, code, account, user_id, report_id, is_developer=None, extra=None, user_auth='',
               report_type=''):
    from dashboard_chart.services import dashboard_login_service
    from user.services import user_service

    logger.error(f"进入第三方登录接口")

    # 设置 code 和 account
    g.code = code
    g.account = account
    g.userid = user_id
    if is_developer:
        g.is_developer = is_developer
    set_grayscale_project(request, response, g.code)
    set_correct_project_code(code)

    # 设置用户信息
    user_info = {
        'id': user_id,
        'user_id': user_id,
        'account': account,
        'group_id': '',
        'user_name': '',
        'customize_roles': [],
        'external_user_id': user_id,
        'external_user_account': account,
        '_account': account,
        'project_code': g.code,
        'biz_code': report_id,
        'extend_yl_params': ''
    }

    # 获取免登来源, 超级工作台和超级app需要调用mit获取报表权限信息
    extra = extra or {
        "login_from": LoginFrom.DashboardSso.value,
        "data": user_info
    }
    login_from = extra.get("login_from", LoginFrom.DashboardSso.value)

    # 第三方token的原始data
    sso_data = extra.get("data") or {}

    # 现在入口，当前只允许两种场景从url传user_auth: 超级app, 组件嵌入
    # if not user_auth:
    #     user_auth = request.params.get('user_auth', '') or ''

    # Product.env_type 参数已不存在，如启用下面代码，需要重新评估
    # if config.get("Product.env_type") in ['cyjg_saas', 'cyjg_private']:
    #     user_auth = 'view,download'

    logger.error("开始获取关联报表id")

    # 获取子报表和跳转报表
    report_ids = [report_id]
    if report_type == "dashboard":
        try:
            report_ids = get_relation_dashboard(report_id) or []
            report_ids = list(set(report_ids))
        except:
            report_ids = [report_id]

    logger.error(f"关联报表id: {report_ids}")

    mit_user_auth = None

    sso_data['report_type'] = report_type

    # source_func 和 source_from 参数是用于第三方报表鉴权， 在报表元数据接口鉴权接口中(def _check_third_party)使用
    request.params['source_func'] = ApplicationTypeAccessReleasedSourceStr.DashboardMipAuth.value

    logger.error(f"report_type：{report_type}, login_from: {login_from}, user_auth: {user_auth}")

    if report_type == "dashboard" and login_from in [LoginFrom.SuperPortal.value, LoginFrom.SuppApp.value] \
            and (not user_auth or 'view' not in user_auth):
        logger.error(f"调用基础平台鉴权")
        request.params['source_from'] = ApplicationTypeAccessReleasedSourceStr.DashboardMipAuth.value

        # 调用基础数据平台的接口校验用户是否有该报表的可见权限
        mit_user_auth = IngratePlatformApi(g.code).check_report_is_grant(g.account, report_ids)
        if 'view' not in mit_user_auth.get(report_id, ''):
            raise UserError(message="用户没有该报告的可见权限")
    elif report_type == "portal" and login_from in [LoginFrom.ERPPortal.value, LoginFrom.ZHYQ.value,
                                                    LoginFrom.SuperPortal.value, LoginFrom.SuppApp.value]:
        # 调用基础平台获取门户可见权限（后面如果有门户权限场景，需要再次添加校验接口）
        # 这里现在默认认为门户存在就是可见，默认就是通过了基础数据平台门户的授权
        # 授权通过之后，在cookies中标记改登录方式通过了基础平台授权
        g.site_from = login_from
        g.auth_from = TokenAuthFrom.MipAuth.value
        is_view = True
        export_permission = True
        # （区分门户集成登录的运行时与设计时）
        # /app/index/ （前端逻辑）页面打开的时候，如果external_params中没有portal_id=xxxx
        # 会出现/user/profile接口，但是集成登录的不能通过admin_route装饰器的校验
        # 如果存在portal_id=xxxx，就会不请求/user/profile接口，这是之前已有的前端逻辑(区分运行时与设计时)
        sso_data['portal_id'] = report_id
    else:
        if isinstance(user_auth, list):
            user_auth = ','.join(user_auth)
        sso_data['user_auth'] = user_auth
        mit_user_auth = {i: user_auth for i in report_ids}
        request.params['source_from'] = ApplicationTypeAccessReleasedSourceStr.DashboardUserAuth.value

    logger.error(f"mit_user_auth: {mit_user_auth}")
    logger.error(f"request params: {request.params}")

    if 'project_code' not in sso_data:
        sso_data['project_code'] = g.code

    # 设置完整token信息
    new_data = {
        '_flag': int(time.time()) + random.randint(1, 1000),
        'dashboard_id': report_id,
        'code': code,
        'account': user_info['account'],
        'id': user_info['id'] or user_id,
        'group_id': user_info['group_id'] or sso_data.get("group_id"),
        'external_params': sso_data,
        'customize_roles': user_info['customize_roles'] or sso_data.get('customize_roles'),
        'external_user_id': user_info['external_user_id'] or sso_data.get('external_user_id'),
        'extend_yl_params': sso_data.get('extend_yl_params', '') or ''
    }
    if mit_user_auth:
        # new_data['mit_user_auth'] = mit_user_auth
        cache_report_auth(mit_user_auth)
    setattr(g, "external_params", new_data.get('external_params'))
    setattr(g, "customize_roles", new_data.get('customize_roles'))
    setattr(g, "external_user_id", new_data.get('external_user_id'))
    # 门户登录将token里面的dashboard_id换成portal_id
    if report_type == "portal":
        new_data['portal_id'] = new_data.pop('dashboard_id', '')
    # 添加一些信息到cookies中
    add_source_info_to_token(token_dict=new_data, **{'__from': 'site_from', 'auth_from': 'auth_from'})
    # 免登
    login_service = dashboard_login_service.DashboardLoginService()
    token = login_service.set_cookies(request, response, new_data)
    # 添加天眼需要的cookie信息
    user_service.write_ty_cookie(request.host, response, user_info.get('id'), user_info)

    logger.error(f"设置cookies成功")

    return token


def cache_report_auth(mit_user_auth: dict):
    if not mit_user_auth:
        return
    cache = conn()
    for report_id, user_auth in mit_user_auth.items():
        cache.set(f'mit_user_auth:{g.account}:{report_id}', user_auth, 43200)  # 缓存12小时


def get_cache_report_auth(report_id):
    cache = conn()
    return cache.get(f'mit_user_auth:{g.account}:{report_id}')


# 添加登录来源以及集成登录认证来源信息
def add_source_info_to_token(token_dict, **kwargs):
    # 在cookies的token中添加一些数据
    # 比如: kwargs: {'k1': 'v1', 'k2': 'v2'}  ->  cookie['k1']=g.v1   cookie['k2']=g.v2
    for cookie_flag, g_flag in kwargs.items():
        g_flag_value = getattr(g, g_flag, '')
        if g_flag_value:
            token_dict[cookie_flag] = g_flag_value


def _public_way(request, response, code, account, user_id, report_id, is_developer=None, extra=None, user_auth=None,
                report_type=''):
    set_correct_project_code(code)
    return ''


def get_relation_dashboard(dashboard_id):
    from app_menu.services import external_service
    from dashboard_chart.services.dashboard_service import get_child_dashboard

    jump_dashboard = {dashboard_id: ''}
    logger.error(f"开始获取跳转报表")
    external_service.recursive_get_jump_dashboard_auths([dashboard_id], jump_dashboard) or {}
    logger.error(f"开始获取子报表")
    child_dashboard = get_child_dashboard(dashboard_id) or []
    dashboard_ids = list(jump_dashboard.keys()) + [i.get('id') for i in child_dashboard]
    logger.error(f"获取到的关联报表：{dashboard_ids}")
    return dashboard_ids


def auto_login(
        request, response,
        report_id, code, account, user_id,
        release_type=None, report_type='dashboard',
        is_developer=None,
        extra=None, user_auth=None
):  # NOSONAR
    """
    0:不限制,1:通过密码,2:限制用户 3:三方控制 4:角色控制
    """
    from user.services.reporting_sso_service import set_cookies_samesite_property

    dashboard_login_type = {
        0: _public_way,
        1: _public_way,
        2: _oa_way,
        3: _third_way,
        4: _oa_way
    }

    portal_login_type = {
        0: _public_way,
        1: _public_way,
        2: _oa_way,
        3: _third_way,
        4: _oa_way
    }

    if report_type in ["portal", "portal_list"]:
        login_type = portal_login_type
    else:
        login_type = dashboard_login_type

    if report_type in [AddFuncType.Ppt.value] and release_type is None:
        raise UserError(code=5006, message="缺少release_type参数")

    if report_type == AddFuncType.ActiveReport.value:
        ar_release_type = ingrate_repository.get_release_type_of_ar(report_id)
        if ar_release_type is not None:
            release_type = ar_release_type

        # 获取报告分享类型
    if release_type is None:
        share_type = int(_get_login_type(report_id, report_type))
    else:
        share_type = int(release_type)

    logger.error(f"报表发布类型：{share_type}")

    # 根据分享类型获取对应免登方式
    func = login_type.get(share_type)
    if not func:
        raise UserError(code=5004, message=f"不支持的登录类型: {share_type}")

    # 免登逻辑
    token = func(request, response, code, account, user_id, report_id, is_developer, extra, user_auth=user_auth,
                 report_type=report_type)

    # 设置跨域
    set_cookies_samesite_property(response=response)

    # 将 response cookies 复制给 request, 取数场景有使用
    set_req_cookies(request, response)

    logger.error("免登成功")

    return token


def set_req_cookies(request, response):
    try:
        cookies = {val.key: parse.unquote(val.value) for _, val in dict(response._cookies).items()}
        # setattr(response, '_cookies', {})
        g.cookie = cookies
        for key, val in cookies.items():
            request.cookies[key] = val
    except Exception as e:
        logger.error("set request cookies error:{}".format(e))

def get_page_url(report_id, report_type, extra):
    if report_type in [AddFuncType.Ppt.value, AddFuncType.ActiveReport.value]:
        from ppt.services.ppt_service import build_ppt_redirect_url

        page_url = build_ppt_redirect_url(
            "/login_by_jwt",
            from_type=report_type,
            params={"id": report_id},
            extra=extra,
            backend=False
        )
    else:
        if report_type == 'portal':
            # 门户跳转
            from app_menu.services.application_service import get_custom_normal_redirect_url
            page_url = get_custom_normal_redirect_url(report_id)
        elif report_type == 'portal_list':
            # pc门户列表首页
            from app_menu.services.application_service import get_pc_portal_index_redirect_url
            project_code = g.code
            page_url = get_pc_portal_index_redirect_url(project_code)
        else:
            # 报告跳转
            logger.error("获取跳转地址")
            page_url = get_redirect_url(report_id)
    logger.error(f"获取到跳转地址： {page_url}")
    return page_url


def jump(report_id, report_type=AddFuncType.Dashboard.value, extra=None, request=None):
    logger.error("进入jump")
    page_url = get_page_url(report_id, report_type, extra)
    # 透传参数
    if request:
        kwargs = request.params
        if 'token' in kwargs:
            del kwargs['token']
        if 'code' in kwargs:
            del kwargs['code']

        page_url = url_add_param(page_url, kwargs)

    logger.error(f"添加透传参数后的url: {page_url}")

    return hug.redirect.to(page_url)


def get_redirect_url(report_id: str):
    from feed.services.dashboard_feeds_service import get_dashboard_url

    terminal_type = ingrate_repository.get_dashboard_terminal_type(report_id)
    return get_dashboard_url(report_id, terminal_type)


def verify_workbench_access(access_payload: WorkbenchJwt, model: IngrateModel):
    logger.error(f'验证token信息与身份认证登录用户信息是否一致: 身份认证={access_payload}, TOKEN={model}')
    if model.tenant_code and access_payload.tenant_code != model.tenant_code:
        raise UserError(message='租户信息不一致')
    if model.account and access_payload.user_code != model.account:
        raise UserError(message='用户账号不一致')
    # if model.user_id and access_payload.user_guid != model.user_id:
    #     raise UserError(message='用户ID不一致')

def set_token_data(token_data, model: IngrateModel):
    model.tenant_code = token_data.get(ApiParamSysValue.SuperportalCode.value) or model.tenant_code
    model.account = token_data.get(ApiParamSysValue.SuperportalAccount.value)
    model.user_id = token_data.get(ApiParamSysValue.SuperportalUserId.value)
    model.user_auth = token_data.get(ApiParamSysValue.UserAuth.value)
    model.report_id = token_data.get(ApiParamSysValue.ReportId.value) or model.report_id
    return model


def login_and_jump(request, response, model: IngrateModel, workbench_callback_code = None):
    try:
        from user.services.workbench_auth_service import Workbench
        if not model.tenant_code and model.token:
            model.tenant_code = parse_jwt_token(model.token).get('oc')
        token_data, use_old_auth_jwt = superportal_verify(model.token)
        set_token_data(token_data, model)
        model.tenant_code = set_correct_project_code(model.tenant_code)

        # 统一身份认证
        enable_skyline_auth = auth_util.is_enable_skyline_auth(model.tenant_code)
        if workbench_callback_code:
            # 如果有code表示工作台已登录，拿token验证用户信息
            access_payload: WorkbenchJwt = Workbench().get_token(workbench_callback_code)
            verify_workbench_access(access_payload, model)
        elif enable_skyline_auth and not use_old_auth_jwt:
            if model.disable_authcenter != 1:
                # 回调地址为当前接口
                logger.error('跳转到工作台登录')
                Workbench().to_login_callback_current(request, model.tenant_code)
                return

        # 登录源头
        extra = {"login_from": LoginFrom.SuperPortal.value}

        report_type = model.report_type or model.type
        # PC门户列表以第三方用户登录
        if report_type == 'portal_list':
            model.release_type = 3

        logger.error("开始进入auto_login接口")

        # 免登
        auto_login(
            request, response, model.report_id, model.tenant_code, model.account, model.user_id,
            release_type=model.release_type,
            report_type=report_type,
            extra=extra,
            user_auth=model.user_auth
        )
    except UserError as e:
        return hug.redirect.to(f'/static/errorTip.html?msg={parse.quote(e.message)}')
    except EmptyProjectCodeError:
        return hug.redirect.to(f'/static/errorTip.html?msg={parse.quote(f"租户{model.tenant_code}不存在")}')

    logger.error("开始jump")
    # jump
    if workbench_callback_code:
        del request.params['code']
    return jump(model.report_id, report_type, extra, request)

def parse_jwt_token(token):
    try:
        payload = jwt.decode(token, '', algorithms='HS256', options={'verify_signature': False})
        return payload
    except Exception as e:
        return {}

def login_erp_and_jump(request, response, model: IngrateModel, workbench_callback_code=None):
    # 默认支持报告以及门户登录，但是现在该接口只开放了门户登录
    from user.services.reporting_sso_service import ERPSSOService, get_yzs_config_by_erp_token
    from user.services.workbench_auth_service import Workbench

    try:
        # model.validate()
        code = None
        account = None
        menu_id = None
        user_id = None
        if model.token:
            code_info = get_yzs_config_by_erp_token(model.token, code=model.tenant_code)
            erp_service = ERPSSOService(secret=code_info['secret'])
            success, result = erp_service._decode_data(model.token, code_info['code'])
            if not success:
                return hug.redirect.to(f'/static/errorTip.html?msg={parse.quote(f"{result}")}')

            code = result.get('tenant_code')
            account = result.get('account')
            menu_id = result.get('menu_id')
            user_id = result.get('user_id')

        if auth_util.is_enable_skyline_auth(model.tenant_code):
            if workbench_callback_code:
                # 如果有code表示工作台已登录，拿token验证用户信息
                access_payload: WorkbenchJwt = Workbench().get_token(workbench_callback_code)
                verify_workbench_access(access_payload, model)
                code = access_payload.tenant_code
                account = access_payload.user_code
                user_id = access_payload.user_guid
            elif model.disable_authcenter != 1:
                # 回调地址为当前接口
                logger.error('跳转到工作台登录')
                Workbench().to_login_callback_current(request, code)
                return

        if model.type == 'portal':
            extra = {"login_from": LoginFrom.ERPPortal.value}
        else:
            # 暂时未正式启用
            extra = {"login_from": LoginFrom.ERPDashboard.value}

        g.code = code
        g.account = account
        g.userid = user_id

        # 免登
        auto_login(
            request, response, model.report_id, code, account, user_id,
            release_type=None,
            report_type=model.type,
            extra=extra,
            user_auth=None
        )
        # 如果是通过身份认证不传code参数:
        if workbench_callback_code:
            del request.params['code']
        return portal_or_dashboard_jump(model.report_id, model.type, extra, request, menu_id)
    except UserError as e:
        return hug.redirect.to(f'/static/errorTip.html?msg={parse.quote(e.message)}')
    except EmptyProjectCodeError:
        code = getattr(g, 'code', '')
        return hug.redirect.to(f'/static/errorTip.html?msg={parse.quote(f"租户{code}不存在")}')


def portal_or_dashboard_jump(report_id, report_type=AddFuncType.Dashboard.value, extra=None, request=None, menu_id=''):
    if report_type == 'portal':
        # 门户跳转
        from app_menu.services.application_service import get_custom_normal_redirect_url
        page_url = get_custom_normal_redirect_url(report_id)
        menu_id = menu_id or (request and request.params.get('menu_id')) or ''
        if menu_id:
            page_url = f'{page_url}/{menu_id}'
    else:
        # 报告跳转
        page_url = get_redirect_url(report_id)

    # 透传参数
    if request:
        kwargs = request.params
        if 'token' in kwargs:
            del kwargs['token']

        page_url = url_add_param(page_url, kwargs)

    return hug.redirect.to(page_url)


def login_with_customer_role(request, response, model: IngrateModel, workbench_callback_code = None):
    """
    角色登录
    :param request:
    :param response:
    :param kwargs:
    :return:
    """
    from user.services import user_service
    from user.services.reporting_sso_service import set_cookies_samesite_property
    from user.services.workbench_auth_service import Workbench

    if not model.tenant_code and model.token:
        model.tenant_code = parse_jwt_token(model.token).get('oc')
    enable_skyline_auth = auth_util.is_enable_skyline_auth(model.tenant_code)
    token_data,use_old_auth_jwt = superportal_verify(model.token)
    set_token_data(token_data, model)
    model.tenant_code = set_correct_project_code(model.tenant_code)

    # 统一身份认证
    if workbench_callback_code:
        # 如果有code表示工作台已登录，拿token验证用户信息
        access_payload: WorkbenchJwt = Workbench().get_token(workbench_callback_code)
        verify_workbench_access(access_payload, model)
    elif enable_skyline_auth and not use_old_auth_jwt:
        if model.disable_authcenter != 1:
            # 回调地址为当前接口
            logger.error('跳转到工作台登录')
            Workbench().to_login_callback_current(request, model.tenant_code)
            return

    extend_yl_params = token_data.get("extend_yl_params") or ''

    customize_roles = model.customize_roles
    # 没有传，则默认为 自助报表制作者 角色
    if not customize_roles:
        customize_roles = ingrate_repository.get_self_service_maker_role()

    g.code = model.tenant_code
    g.account = model.account
    set_grayscale_project(request, response, g.code)

    # 兼容自助报表第三方登录无用户id, 使用虚拟用户
    if customize_roles:
        # 若指定了角色，此时可以无需指定用户
        user = SELF_SERVICE_VIRTUAL_USER_INFO
        user['account'] = model.account
        user['name'] = model.account
        group_ids = []
    else:
        user = user_service.get_user_by_account(model.account, ['pwd', 'id', 'group_id'])
        if not user:
            raise UserError(message="用户不存在")
        group_ids = user_service.get_cur_user_group_ids()
    g.group_ids = group_ids
    # 赋值角色
    g.customize_roles = customize_roles
    # acc
    domain = request.host
    # 直接设置登录状态
    user_service.set_login_status(
        response,
        domain,
        model.tenant_code,
        user['id'],
        model.account,
        group_ids,
        **{"customize_roles": customize_roles, "external_user_id": model.user_id,
           "extend_yl_params": extend_yl_params}
    )

    redirect_url = f'{config.get("Domain.dmp")}/{model.report_type}'

    # 透传其它参数
    # redirect_url = url_add_param(redirect_url, kwargs)
    # 设置跨域
    set_cookies_samesite_property(response=response)

    return redirect_url


if __name__ == "__main__":
    run_code = 0

