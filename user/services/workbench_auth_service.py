#!/usr/bin/env python
# -*- coding: utf-8 -*-
import datetime
import logging
import urllib.parse

import jwt

from base import repository
from base.enums import SkylineApps
from components.app_hosts import AppHosts
from components.workbench_auth_api import WorkbenchAuth, AuthConstants
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from user.models import WorkbenchTokenData, WorkbenchJwt
from user.services.user_service import set_login_status


class Workbench(object):

    def __init__(self, access_token=None):
        self.auth = WorkbenchAuth.get()
        self._init_token_model(access_token)
        self.expires = 3 * 24 * 60 * 60
        g.account = ''

    def _init_token_model(self, access_token):
        self.token_data: WorkbenchTokenData = WorkbenchTokenData()
        self.token_data.access_token = access_token
        self.token_data.access_payload = WorkbenchJwt(**self._parse_jwt_payload(access_token))

    @staticmethod
    def _parse_jwt_payload(token):
        if not token:
            return {}
        header = jwt.get_unverified_header(token)
        payload = jwt.decode(token, '', algorithms=header['alg'], options={'verify_signature': False})
        if not payload:
            return {}
        return payload

    def workbench_login_without_pwd(self, response, tenant_code, account, source=None):
        logging.error(f'正在进行工作台登录服务免密登录:{tenant_code}, {account}')
        access_token = self.auth.do_login_without_pwd(tenant_code, account, source)
        self._init_token_model(access_token)
        if access_token:
            # 记录工作台token，用于退出登录清除工作台登录状态
            response.set_cookie(
                name='workbench_token', value=access_token, max_age=self.expires, domain=config.get('Domain.dmp'), path='/',
                secure=False, http_only=False
            )
        return self.token_data.access_payload

    def get_token(self, auth_code):
        self.token_data = WorkbenchAuth.get().get_token(AuthConstants.AUTHORIZATION_GRANT_TYPE, auth_code)
        return self.token_data.access_payload

    def login(self, auth_code, request, response):
        logging.error(f'工作台登录服务回调数见登录:{auth_code}')
        payload = self.is_jwt_str(auth_code)
        if payload:
            self.token_data.access_token = payload
        else:
            self.token_data = WorkbenchAuth.get().get_token(AuthConstants.AUTHORIZATION_GRANT_TYPE, auth_code)
        # 验证token是否正确
        self.check_token()
        self._dmp_login(request, response)
        return self.token_data.access_payload

    def is_jwt_str(self, s):
        try:
            header = jwt.get_unverified_header(s)
            payload = jwt.decode(s, '', algorithms=header['alg'], options={'verify_signature': False})
            return payload
        except Exception as e:
            return None

    def _dmp_login(self, request, response):
        payload = self.token_data.access_payload
        g.code = payload.tenant_code
        # 第三方登录
        self._third_way_login(request, response)
        # 查询用户是否在对应租户中，如果不存在则插入数据
        # self._init_dmp_user()
        # 登录用户到数见
        # self._set_login(request, response)
        return True

    def _third_way_login(self, request, response):
        from user.services import ingrate_service
        payload = self.token_data.access_payload
        g.code = payload.tenant_code
        ingrate_service._third_way(request, response, payload.tenant_code,payload.user_code, payload.user_guid, None)

    def _set_login(self, request, response):
        domain = request.host or urllib.parse.urlsplit(config.get('Domain.dmp')).netloc
        payload = self.token_data.access_payload
        set_login_status(
            response, domain, payload.tenant_code, payload.user_guid,
            payload.user_code, [], **{"expires": self.expires, "is_developer": 0}
        )
        # 记录工作台token，用于退出登录清除工作台登录状态
        response.set_cookie(
            name='workbench_token', value=self.token_data.access_token, max_age=self.expires, domain=domain, path='/',
            secure=False, http_only=False
        )
        return True

    def check_token(self):
        payload = self.token_data.access_payload
        if not payload:
            raise UserError(message='获取access_token为空')
        exp_date = datetime.datetime.fromtimestamp(payload.exp)
        if exp_date <= datetime.datetime.now():
            # 过期则刷新token
            self.token_data = self.auth.get_token(AuthConstants.REFRESH_GRANT_TYPE,
                                                  refresh_token=self.token_data.refresh_token)

    def logout(self):
        token = self.token_data.access_token
        if not token:
            return
        try:
            self.auth.logout(token, AuthConstants.ACCESS_TOKEN_TYPE)
        except Exception as e:
            logging.error(f'注销工作台账号登录状态异常:{str(e)}')

    def to_login_callback_current(self, request, tenant_code, state=None, inside_url=False):
        if not state:
            state = seq_id()
        redirect_url = AppHosts.get(SkylineApps.DMP, inside_url) + request.path + '?' + replace_code_parameter(request)
        return self.auth.do_auth_redirect(redirect_url, state, tenant_code)

    def to_login(self, redirect_url, tenant_code='', state=None):
        if not state:
            state = seq_id()
        return self.auth.do_auth_redirect(redirect_url, state, tenant_code)

    def _init_dmp_user(self):
        # 将开发者账号初始化到数见用户
        payload = self.token_data.access_payload
        g.code = payload.tenant_code
        dmp_user = {'id': payload.user_guid, 'name': payload.user_name, 'account': payload.user_code}
        # 判断账号是否已经存在
        data = repository.get_one('user', {'account': payload.user_code}, ['account', 'is_disabled'])
        if not data:
            repository.add_data('user', dmp_user)
            from hd_upgrade.services.role_upgrade_service import RoleUpgrade
            self.init_role(RoleUpgrade.READER_ROLE_ID, payload.user_guid)
            return
        if data.get('is_disabled') == 1:
            raise UserError(message='该用户已被禁用，请联系管理员')

    @staticmethod
    def init_role(role_id, user_id):
        if not role_id:
            return
        data = []
        data.append({'role_id': role_id, 'user_id': user_id})
        if data:
            repository.replace_list_data('user_user_role', data, ['role_id', 'user_id'])




def replace_code_parameter(request, alias='tenant_code'):
    querystring:str = request.query_string
    if not querystring:
        return querystring

    logging.error(f'替换身份认证URL中code参数名为{alias}，原始queryString={querystring}')
    code = request.get_param('code')
    if not code:
        return querystring
    tenant_code = request.get_param(alias)
    if not tenant_code or tenant_code == code:
        request.params[alias] = code
        del request.params['code']
        from urllib.parse import urlencode
        querystring = urlencode(request.params)
        logging.error(f'替换URL中code参数完成，原始querystring={request.query_string}，新querystring={querystring}')
        return querystring
    else:
        logging.error(f'替换URL中code参数失败，{alias}已存在且不等于code的值')
        return querystring

