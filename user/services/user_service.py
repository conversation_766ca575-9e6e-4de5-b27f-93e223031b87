#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2017/3/26.
"""
import hashlib
import json
import logging
import random
import re
import os
import string
import datetime
from _sha1 import sha1
from urllib.parse import urlparse
import urllib.parse
import bcrypt
import time
import uuid
import requests
import jwt
import hug
from falcon import HTTP<PERSON><PERSON>uthorized, falcon
from falcon.request import Request
# from suds.client import Client
from components.mip_ad_service import MIPADService
from components.utils import system_arch

from base import repository
from base.dmp_constant import (
    USER_ROLE_CACHE_KEY,
    ERP_USER,
    MOBILE,
    DATASET_ROW_PERMISSION_USER_ID,
    USER_LAST_ACTIVE,
    SELF_SERVICE_VIRTUAL_USER_INFO,
    SELF_SERVICE_VIRTUAL_USER_ID,
    KEEP_LOGIN_DEFAULT_EXPIRE,
)
from base.enums import (
    EmailTemplateType,
    AccountMode,
    AddMode,
    DatasetBusinessType,
    LoginCode,
    LoginAuthMode,
    UserChannel,
    DataSourceType,
    ApplicationTypeAccessReleased,
    ApplicationTypeAccessReleasedSourceStr,
    TokenAuthFrom
)
from components import mail, wait_lock, dynamics_config
from components.grayscale import set_grayscale_project, check_grayscale
from components.storage_setting import get_dmp_env_sign
from dmplib import config
from dmplib.constants import ADMINISTRATORS_ID, DEFAULT_GROUP_ID, ADMINISTRATORS_GROUP_ID
from dmplib.hug import g
from dmplib.redis import conn as conn_redis, RedisCache
from dmplib.saas import project
from dmplib.utils.captcha import generate_verify_image
from dmplib.utils.errors import UserError
from dmplib.utils.jwt_login import LoginToken
from dmplib.utils.strings import seq_id
from message.models import MessageModel
from message.services import message_service
from rbac.services import func_auth_service
from system.system_constant import USER_SYNC, USER_SYNC_ENCRYPT
from user.repositories import email_repository
from user.repositories import user_repository
from user.services import safe_service, idm_service
from user_group.services import user_group_service
from dashboard_chart.services import dashboard_lock_service
from user_group.repositories import user_group_repository
from user.repositories import user_system_setting_repository
from user.commons.password_encrypt import MD5Encrypt
from feed.repositories import dashboard_feeds_repository
from falcon.response import Response
from typing import Any, Dict, List, Union, Optional, Tuple
from user.models import UserLoginModel, UserModel, OaUserLoginModel, YzsUserLoginModel, SingleUserLoginModel
from base.dmp_constant import YZS_SYNC_DATA_PAGE_SIZE
from rbac import external_service as external_rbac_service
from components.url import url_add_param
from urllib import request
from components.storage_setting import get_setting, get_project_setting

USER_INFO_CACHE_KEY = 'dmp:user:cache:%s'
_password_special_symbols = '!@#$%^&*'
rdc_auth_role_ids = ['********-0000-0000-0000-0000********', '39e47d2d-3f20-d160-776e-9db51a51eaee']
PREFIX_LOGIN_MODEL = "dmp:login_model_config"
logger = logging.getLogger(__name__)


def get_cur_user_id() -> str:
    if hasattr(g, 'userid'):
        return g.userid
    return None


def get_user_id_by_account(account):
    """
    根据account获取用户ID
    :param account:
    :return:
    """
    return repository.get_data_scalar("user", {"account": account}, "id")


def refresh_cur_rold_id(user_id=None, role_id=None):
    if user_id:
        key = USER_ROLE_CACHE_KEY % user_id
        # 出错直接pass
        try:
            conn_redis().delete(key)
            refresh_dataset_row_permissions()
        except Exception as e:
            logger.exception(e)
    if role_id:
        # 查出当前role_id下所有user_ids
        user_ids = repository.get_data('user_user_role', {'role_id': role_id}, ['user_id'], multi_row=True)
        for k in user_ids:
            refresh_cur_rold_id(user_id=k.get('user_id'))


def delete_user_role_cache(user_ids: list):
    if user_ids:
        keys = [USER_ROLE_CACHE_KEY % user_id for user_id in user_ids]
        # 出错直接pass
        try:
            conn_redis()._connection.delete(*keys)
            conn_redis().delete(DATASET_ROW_PERMISSION_USER_ID)
        except Exception as e:
            logger.exception(e)


def refresh_dataset_row_permissions():
    # 刷新数据集行列权限
    try:
        conn_redis().delete(DATASET_ROW_PERMISSION_USER_ID)
    except Exception:
        pass


def _get_user_account(user_id):
    """
    获取用户account
    :param user_id:
    :return:
    """
    if user_id:
        return repository.get_data_scalar("user", {"id": user_id}, col_name="account")
    return None


def get_cur_role_id(user_id: None = None, account: None = None) -> List[str]:  # NOSONAR
    # todo
    # 更新用户的所在组合所属角色时清除缓存
    if hasattr(g, 'customize_roles') and g.customize_roles:
        return g.customize_roles
    if hasattr(g, 'userid') or user_id:
        user_id = user_id if user_id else g.userid

        # 没有传account的情况下， user_id和account应该是同一个用户
        if not account:
            account = _get_user_account(user_id)

        key = USER_ROLE_CACHE_KEY % user_id
        role_ids = conn_redis().get_data(key)

        # 添加缓存控制 0 开启  1 关闭
        close_role_privilege_cache = config.get("Cache.close_role_privilege_cache", "0")

        # 没有缓存
        if not role_ids:
            # 获取 所在组
            group_role_ids = []
            user_group_ids = get_cur_user_group_ids(account=account)
            # 查看组内角色
            if user_group_ids:
                from user_group.services.user_group_service import get_all_parent_group_by_group_ids
                group_ids = get_all_parent_group_by_group_ids(user_group_ids)
                user_group_ids = list(set(group_ids + user_group_ids))
                group_role_ids = user_repository.get_group_role(user_group_ids) or []
                group_role_ids = [group_role_id.get('role_id') for group_role_id in group_role_ids]
            user_role_ids = user_repository.get_user_role_id(user_id) or []
            user_role_ids = [user_role_id.get('role_id') for user_role_id in user_role_ids]
            # 取并集
            role_ids = list(set(group_role_ids + user_role_ids))
            if role_ids:
                conn_redis().set(key, json.dumps(role_ids), 3600)
                # 添加更新缓存记录
                logger.error(f"set_user_role_cache redis_key：{key} role_ids：{str(role_ids)}")

        elif close_role_privilege_cache.isdigit() and int(close_role_privilege_cache) == 1:
            # 开启缓存
            if hasattr(g, "ignore_close_cache_config") and g.ignore_close_cache_config == 1:
                return role_ids
            # 获取 所在组
            group_role_ids = []
            user_group_ids = get_cur_user_group_ids(account=account)
            # 查看组内角色
            if user_group_ids:
                from user_group.services.user_group_service import get_all_parent_group_by_group_ids
                group_ids = get_all_parent_group_by_group_ids(user_group_ids)
                user_group_ids = list(set(group_ids + user_group_ids))
                group_role_ids = user_repository.get_group_role(user_group_ids) or []
                group_role_ids = [group_role_id.get('role_id') for group_role_id in group_role_ids]
            user_role_ids = user_repository.get_user_role_id(user_id) or []
            user_role_ids = [user_role_id.get('role_id') for user_role_id in user_role_ids]
            # 取并集
            role_ids = list(set(group_role_ids + user_role_ids))

        return role_ids

    return None


def get_cur_user_group_ids(account: None = None) -> List[str]:
    return user_repository.get_cur_user_group_ids(account=account)


def update_user_cache(account):
    # 更新缓存
    if not account:
        raise ValueError('account')
    cache_key = USER_INFO_CACHE_KEY % hash(account)
    cache = conn_redis()
    return cache.delete(cache_key)


def update_users_group(user_ids, group_id):
    """
    批量更新用户group_id
    :param user_ids:
    :param group_id:
    :return:
    """
    user_repository.update_users_group(user_ids, group_id)
    if user_ids:
        for user_id in user_ids:
            refresh_cur_rold_id(user_id=user_id)
    return True


def get_user_basic_c(account):
    """
    获取用户基本信息(优先从缓存读取)
    :param account: str
    :return:
    """
    if not account:
        return None
    key = USER_INFO_CACHE_KEY % hash(account)
    redis = conn_redis()
    user_data = redis.get(key)
    if user_data:
        user_data = json.loads(user_data.decode("UTF-8"))
    else:
        user_data = user_repository.get_user_info_for_cache(account)
        if user_data:
            redis.set(key, json.dumps(user_data), 3600)
    return user_data


def set_user_theme(user_agent, theme):
    """
    设置用户主题
    :param user_agent:
    :param theme:
    :return:
    """
    if not user_agent or not theme:
        raise UserError(message='缺少关键参数')
    user_id = get_cur_user_id()
    with wait_lock.WaitLocker("set_user_theme:%s" % user_id, 30) as locker:
        if not locker.lock():
            raise UserError(code=403, message="操作太频繁")
        condition = {'user_id': user_id, 'ua_type': get_type_by_user_agent(user_agent)}
        data = condition.copy()
        data['theme'] = theme
        if repository.data_is_exists('user_theme', condition):
            repository.update_data('user_theme', data, condition)
        else:
            repository.add_data('user_theme', data)
    return True


def get_user_theme(user_agent):
    """
    获取用户主题
    :param user_agent:
    :return:
    """
    if not user_agent:
        raise UserError(message='缺少关键参数')
    condition = {'user_id': get_cur_user_id(), 'ua_type': get_type_by_user_agent(user_agent)}
    return repository.get_data_scalar('user_theme', condition, 'theme')


def get_type_by_user_agent(user_agent: str) -> str:
    """
    获取UA标识
    :param user_agent:
    :return:
    """
    # pylint: disable=line-too-long
    pattern = (
        'phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|'
        'JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone'
    )
    if user_agent and re.compile(pattern).search(user_agent):
        return 'Mobile'
    else:
        return 'PC'


def _generate_random_password(length=None):
    """
    生成随机密码
    :param int length:
    :return:
    """
    chart_list = r'1234567890abcdefghijkmnopqrstuvwxyzABCDEFGHIJKLMNPQRSTUVWXYZ!@#$%&*()<>/|\+-='
    return ''.join(random.sample(chart_list, length if length and int(length) > 0 else 10))


def get_user_by_account(account, fields=None):
    if not account:
        return None
    if fields is None:
        fields = ['id', 'name', 'account', 'mobile', 'email', 'group_id']
    user = user_repository.get_data({'account': account}, fields)
    if user:
        group_ids = repository.get_columns('user_group_user', {'user_id': user.get("id")}, 'group_id')
        user['group_ids'] = group_ids
    return user


def get_user_by_id(userid, fields=None):
    if fields is None:
        fields = ['id', 'name', 'account', 'mobile', 'email', 'group_id']
    result = user_repository.get_user_with_groups(userid, fields)
    # 查询role
    result["role"] = user_repository.get_role_by_user_id(userid)
    return result


def get_user(user_id: str) -> UserModel:
    """
    获取用户
    :param str user_id:
    :return:
    """
    if user_id == SELF_SERVICE_VIRTUAL_USER_ID:
        SELF_SERVICE_VIRTUAL_USER_INFO['name'] = getattr(g, 'account')
        SELF_SERVICE_VIRTUAL_USER_INFO['account'] = getattr(g, 'account')
        return UserModel(**SELF_SERVICE_VIRTUAL_USER_INFO)
    if not user_id:
        raise UserError('缺少用户id')
    fields = ['id', 'name', 'account_mode', 'account', 'mobile', 'email', 'group_id', 'created_on']
    data = user_repository.get_data({'id': user_id}, fields)
    if not data:
        raise UserError(message='用户不存在')
    user = UserModel(**data)
    return user


def upset_user(model, group_id=None):
    """
    更新或新增用户
    :param user.models.UserModel model:
    :param group_id:
    :return:
    """
    if not model.account:
        raise UserError(400, 'account不能为空')

    if not group_id:
        group_id = DEFAULT_GROUP_ID

    model.group_id = group_id
    if not model.group_ids and group_id:
        model.group_ids = [group_id]

    # 如果用户存在,则更新
    user_id = repository.get_data_scalar('user', {'account': model.account}, 'id')
    if user_id:
        # update user
        modified_field = {}
        model.id = user_id
        if model.name:
            modified_field['name'] = model.name
        if model.mobile:
            modified_field['mobile'] = model.mobile
        if model.email:
            modified_field['email'] = model.email
        if group_id:
            modified_field['group_id'] = group_id
        refresh_cur_rold_id(user_id=user_id)
        result = user_repository.update_data(modified_field, {'account': model.account})
        # 添加用户和用户组关系
        if model.group_ids and isinstance(model.group_ids, list) and len(model.group_ids) > 0:
            user_group_service.update_user_user_group(model.id, model.group_ids)
        return result
    else:
        model.id = seq_id()
        data = model.get_dict(['id', 'name', 'account', 'mobile', 'email', 'group_id'])
        data['pwd'] = ''
        result = user_repository.add_data(data)
        # 添加用户和用户组关系
        if model.group_ids and isinstance(model.group_ids, list) and len(model.group_ids) > 0:
            user_group_service.update_user_user_group(model.id, model.group_ids)
        return result


def _validate_password(password, account):
    if not password:
        raise UserError(message='请输入项目密码')
    if len(password) < 8:
        raise UserError(message='为了您的账号安全，密码长度须不少于8位字符')
    pattern = r'(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[\W_]).{8,}'
    if re.match(pattern, password) is None:
        raise UserError(message='为了您的账号安全，密码须为字母、数字和特殊字符组合')
    if account in password:
        raise UserError(message='为了您的账号安全，密码中不可包含用户名信息')

    for idx in range(2, len(password)):
        pre2 = password[idx - 2]
        pre1 = password[idx - 1]
        cur = password[idx]
        if pre2 == pre1 == cur:
            raise UserError(message='为了您的账号安全，密码中不可存在连续数字或字母')
        if pre2.isalnum() and pre1.isalnum() and cur.isalnum() and (ord(pre2) + 2 == ord(pre1) + 1 == ord(cur)):
            raise UserError(message='为了您的账号安全，密码中不可存在连续数字或字母')


def _generate_password():
    clist = [string.ascii_letters, string.digits, _password_special_symbols]
    rest = clist.copy()
    random.shuffle(clist)

    chars = []
    for _ in range(16):
        cset = rest.pop()
        chars.extend(random.sample(cset, 1))
        if not rest:
            rest = clist.copy()
            random.shuffle(rest)
    return ''.join(chars)


def get_reset_email_content(name, account, reset_passwd_url, template_type):
    """
    获取添加用户邮件内容
    :param account:
    :param name:
    :param reset_passwd_url:
    :param template_type:
    :return:
    """

    # 获取邮件模板
    email_template_data = email_repository.get_email_template(template_type)
    if not email_template_data:
        raise UserError(message='邮件模板未找到')
    url = config.get('Domain.dmp')
    replace_dict = {
        '{姓名}': name,
        '{企业域地址}': url,
        '{企业代码}': getattr(g, 'code'),
        '{用户名}': account,
        '{链接}': reset_passwd_url,
        '{域名}': url if url.endswith('/') else url + "/",
    }
    return email_template_data, mail.replace_content(email_template_data.get('content'), replace_dict)


def add_user(model: UserModel, group_id=None, send_mail=True, is_register_developer=False):
    """
    添加 用户
    :param is_register_developer:
    :param send_mail:
    :param group_id:
    :param user.models.UserModel model:
    :return:
    """
    user_id = model.id
    model.id = seq_id()
    model.validate()

    if not model.group_ids and group_id:
        model.group_ids = [group_id]

    if repository.data_is_exists('user', {'account': model.account}):
        raise UserError(message='该账号已经被使用')

    pwd = model.password
    code = getattr(g, 'code')

    user = model.get_dict(
        ['id', 'name', 'account', 'mobile', 'email', 'group_id', 'account_mode', 'add_mode', 'user_source_id']
    )
    user['old_pwd'] = 0

    if user['group_id'] is None:
        user['group_id'] = ''

    # 兼容旧数据account_mode为空的情况，默认为DMP
    account_mode = model.account_mode or AccountMode.DMP.value

    if account_mode == AccountMode.ERP.value:
        # 当前租户为ERP模式
        result = add_user_as_erp(user_id, model)
        return result

    # 同步用户模式下也只需添加到数据库，不需要走下面发邮件等接口
    if account_mode == AccountMode.SYNC.value:
        return add_user_from_external_user(user_id, model)

    # 同步模式下邮箱非必填
    if account_mode != AccountMode.SYNC.value and not model.email:
        raise UserError(message='邮箱不能为空')
    if repository.data_is_exists('user', {'email': model.email, 'is_developer': 0}):
        raise UserError(message='该邮箱已经被使用')

    if account_mode == AccountMode.DOMAIN.value:
        # 当前租户为DOMAIN模式, 域账号新增默认使用 默认随机密码
        pwd = _generate_password()
        while model.account in pwd:
            pwd = _generate_password()
        user['account_mode'] = AccountMode.DOMAIN.value

    if account_mode == AccountMode.DMP.value:
        # 当前租户为DMP模式
        _validate_password(pwd, model.account)
        user['account_mode'] = AccountMode.DMP.value

    user['pwd'] = bcrypt.hashpw(sha1(pwd.encode()).hexdigest().encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    if send_mail:
        # 发送邮件
        if user['account_mode'] == AccountMode.DMP.value:
            data = {'account': model.account, "code": code}
            token = jwt.encode(data, config.get('JWT.init_password_secret'))
            domain = config.get('Domain.dmp')
            reset_passwd_url = domain + '/' + 'init_password' + '?' + 'token=' + token
            email_template_data, content = get_reset_email_content(
                model.name, model.account, reset_passwd_url, template_type=EmailTemplateType.InitPassword.value
            )

            mail.send(
                mail.Mail(
                    subject=email_template_data.get('subject'),
                    body=content,
                    receiver=[mail.MailContact(name=model.name, mail=model.email)],
                ),
                subtype='html',
            )
            # user_reset_passwd add token
            user_repository.add_user_reset_password({'code': code, 'account': model.account, 'token': token})
        else:
            email_template_type = (
                EmailTemplateType.AddUser.value
                if not is_register_developer
                else EmailTemplateType.RegisterDeveloper.value
            )
            email_template_data, content = get_user_email_content(
                model.name, account_mode, model.account, pwd, email_template_type
            )
            mail.send(
                mail.Mail(
                    subject=email_template_data.get('subject'),
                    body=content,
                    receiver=[mail.MailContact(name=model.name, mail=model.email)],
                ),
                subtype=email_template_data.get('send_mode') if email_template_data.get('send_mode') else 'html',
            )

    # 添加用户到数据库
    result = user_repository.add_data(user)
    # 添加用户和用户组关系
    if model.group_ids and isinstance(model.group_ids, list) and len(model.group_ids) > 0:
        user_group_service.update_user_user_group(model.id, model.group_ids)

    return result


def add_user_as_erp(user_id, model):
    """
    ERP模式添加用户，需要添加user_organization
    :param user_id:
    :param model:
    :return:
    """
    data = model.get_dict(
        ['id', 'name', 'account', 'mobile', 'email', 'group_id', 'account_mode', 'add_mode', 'user_source_id']
    )
    data['old_pwd'] = 0
    if not user_id:
        raise UserError(message='ERP账号模式id不能为空')

    if repository.data_is_exists('user', {'id': user_id}):
        raise UserError(message='该user.id: %s已经被使用' % user_id)

    model.id = user_id
    user_source = repository.get_one('user_source', {'id': data.get('user_source_id')})
    is_erp_user_source = False if user_source and user_source.get('type') == UserChannel.Others.value else True
    user_source_table_name = ERP_USER if is_erp_user_source else 'user_source_user'
    erp_user = repository.get_data(user_source_table_name, {"id": model.id})
    data["id"] = user_id
    data['pwd'] = erp_user.get("pwd")
    data['account_mode'] = AccountMode.ERP.value
    data['add_mode'] = AddMode.MANUAL.value
    data['old_pwd'] = 1
    # 添加用户
    result = user_repository.add_data(data)
    if is_erp_user_source:
        # 添加user_organization
        erp_user_group_ids = repository.get_columns(ERP_USER, {"id": user_id}, "group_id")
        if erp_user_group_ids:
            for group_id in erp_user_group_ids:
                user_group_service.add_user_organization(user_id, group_id)
        else:
            if erp_user.get("group_id"):
                user_group_service.add_user_organization(user_id, erp_user.get("group_id"))

    return result


def add_user_from_external_user(user_id, model, account_mode=AccountMode.SYNC.value):
    """
    external_user表中添加用户，需要添加user_organization
    :param user_id:
    :param model:
    :param account_mode:
    :return:
    """
    data = model.get_dict(
        ['id', 'name', 'account', 'mobile', 'email', 'group_id', 'account_mode', 'add_mode', 'user_source_id']
    )
    if not user_id:
        raise UserError(message='用户同步账号模式id不能为空')

    if repository.data_is_exists('user', {'id': user_id}):
        raise UserError(message='该user.id: %s已经被使用' % user_id)

    model.id = user_id
    user_source = repository.get_one('user_source', {'id': data.get('user_source_id')})
    if not user_source:
        raise UserError(message='渠道不存在')
    is_erp_user_source = False if user_source.get('type') == UserChannel.Others.value else True
    user_source_table_name = ERP_USER if is_erp_user_source else 'user_source_user'
    external_user = repository.get_data(user_source_table_name, {"id": model.id}) or {}
    data["id"] = user_id
    # 修改，前端不能自由输入密码，只能从外部表中取
    # 前端可以传，或者用后端的。如果external_user有密码，优先使用external_user
    data['pwd'] = external_user.get("pwd")
    data['account_mode'] = account_mode
    data['add_mode'] = AddMode.MANUAL.value
    data['old_pwd'] = 1
    # 添加用户
    result = user_repository.add_data(data)

    # 用户添加的时候页面上中选择了用户组
    # 添加用户和用户组关系
    if model.group_ids and isinstance(model.group_ids, list) and len(model.group_ids) > 0:
        user_group_service.update_user_user_group(model.id, model.group_ids)
    elif is_erp_user_source:
        # 添加user_organization
        external_user_group_ids = repository.get_columns(ERP_USER, {"id": user_id}, "group_id")
        if external_user_group_ids:
            for group_id in external_user_group_ids:
                user_group_service.add_user_organization(user_id, group_id)
        else:
            if external_user.get("group_id"):
                user_group_service.add_user_organization(user_id, external_user.get("group_id"))

    return result


def get_user_email_content(name, account_mode, account, pwd, template_type):
    """
    获取添加用户邮件内容
    :param name:
    :param account:
    :param pwd:
    :param template_type:
    :return:
    """
    # 根据user的模式判断
    if account_mode == AccountMode.DOMAIN.value:
        pwd = '请使用明源云域账户密码登录'
        # 域账户登录不支持修改密码
        template_type = EmailTemplateType.AddDomainUser.value

    # 获取邮件模板
    email_template_data = email_repository.get_email_template(template_type)
    if not email_template_data:
        raise UserError(message='不存在邮件模板:%s' % template_type)
    url = config.get('Domain.dmp')
    replace_dict = {
        '{姓名}': name,
        '{企业域地址}': url,
        '{企业代码}': getattr(g, 'code'),
        '{用户名}': account,
        '{密码}': pwd,
        '{域名}': url if url.endswith('/') else url + "/",
    }
    return email_template_data, mail.replace_content(email_template_data.get('content'), replace_dict)


def modify_mobile(model: UserModel):
    return not (model.mobile and '*' in model.mobile)


def update_user(model):
    """
    修改用户
    :param user.models.UserModel model:
    :return:
    """
    model.validate()

    if not repository.data_is_exists('user', {'id': model.id}):
        raise UserError(message='该用户不存在')

    # 兼容旧数据account_mode为空的情况，默认为DMP
    user_info = repository.get_one('user', {'id': model.id}, ['account_mode', 'is_developer']) or {}
    account_mode = user_info.get('account_mode') or AccountMode.DMP.value
    is_developer = user_info.get('is_developer') or 0

    if (
        account_mode != AccountMode.ERP.value
        and model.account_mode != AccountMode.SYNC.value
        and repository.data_is_exists('user', {'email': model.email, 'is_developer': is_developer}, {'id': model.id})
    ):
        raise UserError(message='该邮箱已经被使用')

    if not modify_mobile(model):
        fields = ['name', 'email', 'group_id']
    else:
        fields = ['name', 'mobile', 'email', 'group_id']

    if model.group_ids and len(model.group_ids) > 0:
        model.group_id = model.group_ids[0]
    else:
        model.group_ids = [] if not model.group_id else [model.group_id]

    success = user_group_repository.update_user_group(model.id, model.group_ids)
    if not success:
        raise UserError(400, '更新用户组关系失败')
    # 更新缓存
    user = get_user_by_id(model.id)
    update_user_cache(user['account'])
    # 刷新缓存
    refresh_cur_rold_id(model.id)
    user_repository.update_model(model, {'id': model.id}, fields)
    if user["email"] and user["email"] != model.email:
        # 用户更新邮件时，同步更新邮件简讯用户的邮箱
        update_dashboard_feed_recipients_email(user["email"], model.email)

    return model.get_dict(fields)


def update_dashboard_feed_recipients_email(old_email_name, new_email_name):
    """
    更新邮件订阅发送用户的邮件信息
    :param old_email_name:
    :param new_email_name:
    :return:
    """
    if old_email_name == new_email_name:
        return False
    feed_recipients_list = dashboard_feeds_repository.get_dashboard_feeds_by_email_name(old_email_name)
    if not feed_recipients_list:
        return False
    for feed in feed_recipients_list:
        recipients = feed.get("recipients")
        if recipients:
            feed_id = feed.get("id")
            is_update = False
            recipients_list = json.loads(recipients)
            for user in recipients_list:
                if user.get("email") == old_email_name:
                    is_update = True
                    user["email"] = new_email_name
            recipients_list_json = json.dumps(recipients_list)
            if is_update:
                # 更新相关邮件简讯的邮箱名称
                dashboard_feeds_repository.update_dashboard_feeds_email(feed_id, recipients_list_json)


def disable_user(user_id, is_disabled):
    """
    禁用用户
    :param user_id:
    :return:
    """
    cur_user_id = get_cur_user_id()
    if cur_user_id == user_id:
        raise UserError(message=u'不能禁用自身账号！')
    # 管理员才有权限禁用账号
    if not repository.data_is_exists(
        "user", {"id": cur_user_id, "group_id": ADMINISTRATORS_GROUP_ID}
    ) and not user_repository.is_admin_by_user_id(cur_user_id):
        raise UserError(message=u'管理员才有权限禁用账号!！')
    if is_disabled is None or int(is_disabled) not in [0, 1]:
        raise UserError(message=u'参数is_disabled错误，请输入0、1!')
    user_repository.update_data({"is_disabled": is_disabled}, {"id": user_id})
    # 如果是禁用用户，下线禁用的用户
    if int(is_disabled) == 1:
        forced_offline(user_id, g.code)
    return True


def delete_user(user_id):
    """
    删除用户
    :param str user_id:
    :return:
    """
    if not user_id:
        raise UserError(message='缺少用户id')
    if user_id.lower() == ADMINISTRATORS_ID.lower():
        raise UserError(message='超级管理员不允许删除')
    user = get_user(user_id)
    user_repository.delete_user_relate(user.id, user.account)
    repository.delete_data('user_2_third_party', {'user_id': user_id})
    # 刷新缓存

    refresh_cur_rold_id(user_id=user.id)
    return user


def reset_user_password(user_id, password):
    """
    重设用户密码（随机密码）
    :param str user_id:
    :param str password:
    :return:
    """
    user = get_user(user_id)
    user.validate()

    _validate_password(password, user.account)

    # 同步模式不允许重置密码
    if user.account_mode == AccountMode.SYNC.value:
        raise UserError(message='同步的用户不允许重置密码')

    email_template_data, content = get_user_email_content(
        user.name, user.account_mode, user.account, password, EmailTemplateType.ResetPassword.value
    )

    mail.send(
        mail.Mail(
            subject=email_template_data.get('subject'),
            body=content,
            receiver=[mail.MailContact(name=user.name, mail=user.email)],
        ),
        subtype=email_template_data.get('send_mode') if email_template_data.get('send_mode') else 'html',
    )
    encrypted_pwd = bcrypt.hashpw(sha1(password.encode()).hexdigest().encode('utf-8'), bcrypt.gensalt())

    return user_repository.update_data(
        {
            'pwd': encrypted_pwd.decode('utf-8'),
            'old_pwd': 0,
            'last_password_change': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        },
        {'id': user_id},
    )


def change_password(model):
    """
    修改用户名密码
    :param user.models.ChangePasswordModel model:
    :return:
    """
    model.validate()
    fields = ['id', 'pwd', 'group_id', 'account_mode']
    data = user_repository.get_data({'id': model.id}, fields)
    if not data:
        raise UserError(message='用户不存在')
    if data.get("account_mode") in [AccountMode.SYNC.value, AccountMode.ERP.value, AccountMode.DOMAIN.value]:
        raise UserError(message='同步的用户不允许修改密码')
    # 校验旧密码
    pwd = data.get('pwd').encode('utf-8')
    if bcrypt.hashpw(model.old_password.encode('utf-8'), pwd) != pwd:
        raise UserError(message='原密码校验失败')
    if model.old_password == model.new_password:
        raise UserError(message='新密码不能和旧密码相同')

    encrypted_pwd = bcrypt.hashpw(model.new_password.encode('utf-8'), bcrypt.gensalt())
    return user_repository.update_data(
        {
            'pwd': encrypted_pwd.decode('utf-8'),
            'last_password_change': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        },
        {'id': model.id},
    )


def get_user_list(query_model):
    """
    获取用户列表
    :param user.models.UserQueryModel query_model:
    :return user.models.UserQueryModel:
    """
    query_model.validate()
    # 通过角色权限控制
    # user_group_service.validate_group_auth(query_model.group_id)
    user_lists = user_repository.get_user_list(query_model).get_result_dict()
    return user_lists


def get_erp_user_list(query_model):
    """
    获取ERP用户列表
    :param user.models.UserQueryModel query_model:
    :return user.models.UserQueryModel:
    """
    query_model.validate()
    return user_repository.get_erp_user_list(query_model)


def get_user_role(account=None, name=None, dataset_id=None):
    if account:
        user = repository.get_data("user", {"account": account}, ["id", "account"])
    elif name:
        user = repository.get_data("user", {"name": account}, ["id", "account"])
    else:
        raise UserError(message=u'请填写正确的account或name')
    if not user:
        raise UserError(message=u'未找到用户，请填写正确的account或name')
    account = user.get("account")
    result = {}
    # no use cache
    role_ids = user_repository.get_role_by_account(account)
    group_role_ids = user_repository.get_group_role_by_account(account)
    db_role_ids = []
    for single in role_ids + group_role_ids:
        if single.get("role_id") not in db_role_ids:
            db_role_ids.append(single.get("role_id"))
    if db_role_ids:
        result["db"] = user_repository.get_role_by_id(db_role_ids)
    else:
        result["db"] = []

    # use cache
    cache_role_ids = get_cur_role_id(user_id=user.get("id"), account=account)
    result["cache"] = user_repository.get_role_by_id(cache_role_ids)
    # 处理数据集权限
    if dataset_id:
        result = deal_dataset_permission(dataset_id, result)
    # 判断是否一致
    if len(result.get("db")) != len(result.get("cache")):
        result["flag"] = "不一致"
        return result
    else:
        result["flag"] = "一致"
        for row in result.get("db"):
            if row not in result.get("cache"):
                result["flag"] = "不一致"
                return result
    return result


def deal_dataset_permission(dataset_id, result):
    from rbac.services import data_permissions, grant_service

    for single in result.get("db"):
        single["view"] = data_permissions.check_data_permission_by_role_ids(
            [single.get("id")], "dataset", "view", data_id=dataset_id
        )
        # 行列权限
        single["dataset_filter"] = deal_dataset_filter(dataset_id, single.get('id'))
    for single in result.get("cache"):
        single["view"] = data_permissions.check_data_permission_by_role_ids(
            [single.get("id")], "dataset", "view", data_id=dataset_id
        )
        single["dataset_filter"] = deal_dataset_filter(dataset_id, single.get('id'))
    return result


def deal_dataset_filter(dataset_id, role_id):
    from rbac.services import grant_service
    from dataset.repositories import dataset_field_repository

    dataset_filters = grant_service.get_dataset_filter(dataset_id, role_id) or []
    dataset = repository.get_data("dataset", {"id": dataset_id}, fields=["name"])
    if dataset_filters:
        hide_field_ids = dataset_filters[0].get("hide_field_ids")
        if hide_field_ids:
            fields = dataset_field_repository.get_dataset_field_name_by_ids(hide_field_ids)
            dataset_filters[0]["hide_field_ids"] = [
                row.get("alias_name") if row.get("alias_name") else row.get("col_name") for row in fields
            ]
        dataset_filter = dataset_filters[0].get("dataset_filter")
        if dataset_filter:
            dataset_filter = json.loads(dataset_filter)
            # 关联用户属性
            if dataset_filter and dataset_filter[0].get("operator") == "relate_user_organization":
                temp_value_list = []
                for row in dataset_filter:
                    field = dataset_field_repository.get_dataset_field_info(row.get("dataset_field_id"))
                    temp_value_list.append(
                        "{alias_name} = {col_value} ".format(
                            alias_name=field.get("alias_name") if field.get("alias_name") else field.get("col_name"),
                            col_value=row.get("col_value"),
                        )
                    )
                dataset_filters[0]["dataset_filter"] = ",".join(temp_value_list)
            else:
                temp_value = ",".join(
                    "{alias_name} {operator} {col_value} ".format(
                        alias_name=_.get("alias_name") if _.get("alias_name") else _.get("col_name"),
                        operator=_.get("operator"),
                        col_value=_.get("col_value"),
                    )
                    for _ in dataset_filter
                )
                dataset_filters[0]["dataset_filter"] = temp_value
        dataset_filters[0]["dataset_name"] = dataset.get("name")
    return dataset_filters


def get_user_profile(request: Optional[Request] = None, response: Optional[Response] = None) -> Dict[str, Any]:
    """
    获取用户配置
    :param response:
    :param falcon.request.Request request:
    :return:
    """
    user_id = get_cur_user_id()
    if not user_id:
        raise HTTPUnauthorized('Authentication Required', "Without userid in context", [])
    check_grayscale(request, response, g.code)
    user_group_ids = g.group_ids

    user = get_user(user_id).get_dict()

    project_data = user_repository.get_user_project_profile()

    funcs_set = func_auth_service.get_user_funcs_set(user_id, user_group_ids) or {}
    funcs_set = filter_func(funcs_set)
    user['funcs_map'] = funcs_set
    # is_application_all=0 user/profile 接口，只获取后台菜单门户，不获取后台菜单门户+用户门户列表全部数据
    user['app'] = user_group_service.get_user_func_tree(
        {'user_id': user_id, 'group_ids': user_group_ids, 'is_application_all': 0},
        funcs_set, True, project_type=project_data.get('type')
    )
    # 当前用户是否存在有权限的门户
    user['app_exists'] = user_group_service.check_app_allow_exists(user_id, user_group_ids)
    if request and isinstance(request, Request):
        user['theme'] = repository.get_data_scalar(
            'user_theme', {'user_id': user_id, 'ua_type': get_type_by_user_agent(request.user_agent)}, 'theme'
        )
    _url = urlparse(request.url)
    domain_url = _url.netloc

    # 判断租户是否是saas模式，1是，0否
    project_data['saas_mode'] = int(config.get('App.saas_mode', 0))
    # DMP环境版本标识，可选值 hd：for HighData，shujian:for数见，cloud：for 三云，dmp:招商、华宇、荣盛
    # project_data['dmp_env_sign'] = config.get('App.dmp_env_sign', 'cloud')
    project_data['dmp_env_sign'] = get_dmp_env_sign(project_code=g.code)
    # # 测试环境指定租户为for HD版本，其他租户不受影响
    # if config.get('App.runtime') == 'test':
    #     if g.code in ['luyy', 'update_test', 'JKZDH_forHD']:
    #         project_data['dmp_env_sign'] = get_dmp_env_sign(project_code=g.code)
    #     else:
    #         project_data['dmp_env_sign'] = 'cloud'
    project_data['is_old_erp_sync_mode'] = project_data.get(
        'account_mode'
    ) == AccountMode.ERP.value and project_data.get('erp_data_source_code')

    user['project'] = project_data
    user['yzs_config'] = repository.get_data('project_yzs_config', {'code': getattr(g, 'code')}, from_config_db=True)
    user['domain'] = domain_url

    user['disable_skin_button'] = int(config.get('App.disable_skin_button'))

    # track 指标
    user_opt_info = user_repository.get_user_opt_info(user_id) or {}
    user['log'] = {
        "env_code": os.environ.get('CONFIG_AGENT_CLIENT_CODE'),
        'user_name': user.get('name') if user.get('name') else user_opt_info.get('user_name'),
        'user_org': user_opt_info.get('user_org'),
    }

    # 报告锁定时长
    user['dashboard_lock_seconds'] = dashboard_lock_service.get_config_lock_seconds()
    # 用户设置
    user['setting'] = user_system_setting_repository.get_setting(get_cur_user_id()) or {}

    # 模板中心入口是否打
    user["template_center_switch"] = int(config.get("Function.template_center_switch", 0))

    # 如果该用户是灰度用户，刷新灰度用户的标识

    if config.get('Grayscale.white_list'):
        gray_user = json.loads(config.get('Grayscale.white_list'))

        response.set_cookie(
            name='grayscale_user', value='', domain=request.host, max_age=1, path='/', secure=False, http_only=False
        )

        if gray_user.get(g.code) and user.get('account') in gray_user.get(g.code):
            response.set_cookie(
                name='grayscale_user',
                value=user.get('account'),
                domain=request.host,
                max_age=360_000,
                path='/',
                secure=False,
                http_only=False,
            )

    from rbac import external_service
    project_value_aded_func = external_service.get_project_value_added_func()
    user['is_support_multi_external_subjects'] = 1 #数芯qinjq要求ads也需要显示，也就是所有场景都需要
    # TODO 这里后面需要前后端一起改掉
    user['is_support_external_subjects'] = 1 if "self-service" in project_value_aded_func else 0
    user['is_support_multi_dim_subjects'] = 1 if "is-use-multi-dim" in project_value_aded_func else 0
    user['is_support_active_reports'] = 1 if "active_reports" in project_value_aded_func else 0
    user['value_added_func'] = project_value_aded_func

    beauty_projects = config.get('App.beauty_projects', "") or ""
    beauty_projects = beauty_projects.split(",")
    user['smart_beauty_on'] = 1 if project_data.get('code') in beauty_projects else 0
    user['show_filling_front'] = user_repository.check_is_show_filling_front(user_id)
    user['show_self_service'] = user_repository.check_is_show_by_code(user_id, 'self-service')
    user['show_ppt'] = user_repository.check_is_show_by_code(user_id, 'ppt')

    # 指标模型
    user['is_show_hd_dataset'] = config.get("HighDataInspection.enable") in [1, '1']
    user['is_show_mysoftshuxin'] = bool(repository.get_data_scalar("data_source", {"type": DataSourceType.MysoftShuXin.value}, col_name='id'))
    is_admin = user_repository.is_admin_by_user_id(g.userid)
    user["is_admin"] = is_admin
    # 是否开启两套语法
    user["is_open_double_sql"] = user_repository.is_open_double_sql()
    user["is_open_indicator_asset"] = config.get("ShuXin.open_indicator_asset") in [1, '1']

    # 动态参数
    user["dynamic_config"] = {}
    user["dynamic_config"]["is_open_indicator_detail"] = dynamics_config.get("ShuXin.is_open_indicator_detail") in [1, '1']
    user["dynamic_config"]["is_open_authority_control"] = dynamics_config.get("App.is_open_authority_control") in [1, '1']
    user["dynamic_config"]["is_application_snapshot"] = dynamics_config.get('App.application_snapshot_status') in [1, '1']
    # 数据集是否是改造后入口，如果没有记录就是默认新入口
    dataset_is_combine_entrance = True if get_project_setting("is_data_cloud_1_5_enabled", "0") in (1, '1') else False
    user["dataset_is_combine_entrance"] = dataset_is_combine_entrance
    # 统一域名开关
    user['one_domain'] = config.get('Domain.one_domain')
    user['is_arm'] = system_arch()
    return user


def generate_captcha(model):
    """
    生成验证码
    :param model:
    :return:
    """
    model.tenant_code = project.set_correct_project_code(model.tenant_code)
    setattr(g, "code", model.tenant_code)
    setattr(g, "cache", RedisCache())
    mstream, strs = generate_verify_image()
    captcha_cache_key = 'captcha:account:' + model.account + "|tenant_code:" + model.tenant_code
    conn_redis().set(captcha_cache_key, strs.lower(), 1800)

    return "data:image/gif;base64," + mstream


def _check_captcha_only(model: UserLoginModel, user) -> None:
    cache = conn_redis()
    try:
        captcha_cache_key = 'captcha:account:' + model.account + "|tenant_code:" + model.tenant_code
        captcha_code = cache.get(captcha_cache_key)
        captcha_code = captcha_code.decode('utf-8') if captcha_code else ''
        if model.captcha and model.captcha.lower() != captcha_code:
            raise UserError(message='验证码错误')
    except UserError as e:
        user and safe_service.inc_auth_failed_times(user.get('id'))
        model and safe_service.inc_auth_failed_times(get_user_account_key(model))
        raise e


def _check_captcha(pwd_status: bool, model: UserLoginModel, user) -> None:
    cache = conn_redis()
    try:
        captcha_cache_key = 'captcha:account:' + model.account + "|tenant_code:" + model.tenant_code
        error_count_cache_key = 'user_pwd_error_count_account:' + model.account + "|tenant_code:" + model.tenant_code
        captcha_code = cache.get(captcha_cache_key)
        captcha_code = captcha_code.decode('utf-8') if captcha_code else ''

        # 密码错误
        if not pwd_status:
            # 首次错误时密码错误时提示：账号名或登录密码错误；
            # 连续2次提示：账户名或登录密码错误，已错误2次，错误10次将锁定账号10分钟。
            error_count = cache.get(error_count_cache_key)
            if isinstance(error_count, bytes):
                error_count = int(error_count.decode())
            elif isinstance(error_count, str):
                error_count = int(error_count)
            error_msg = '账号名或登录密码错误'
            logger.error(f'error_count: {error_count}')
            error_count = error_count or 0
            if not error_count:
                cache.add(error_count_cache_key, 1, 600)  # 10min
                error_count += 1
            else:
                cache.incr(error_count_cache_key, 1)
                error_count += 1
                if error_count >= 2:
                    error_msg = f'{error_msg}，已错误{error_count}次，错误10次将锁定账号10分钟'
            raise UserError(message=error_msg)

        # 验证码校验
        if model.captcha and model.captcha.lower() == captcha_code:
            cache.delete(captcha_cache_key)
            cache.delete(error_count_cache_key)
        elif model.captcha and model.captcha.lower() != captcha_code:
            raise UserError(message='验证码错误')

    except UserError as e:
        user and safe_service.inc_auth_failed_times(user.get('id'))
        model and safe_service.inc_auth_failed_times(get_user_account_key(model))
        raise e


def check_need_captcha(model: UserLoginModel) -> int:
    """
    校验是否需要验证码
    :param user.models.UserLoginModel model:
    :return: 1 需要 0 不需要
    """
    error_count = 0
    setattr(g, "code", model.tenant_code)
    setattr(g, "cache", RedisCache())
    cache = conn_redis()
    if model.tenant_code and model.account:
        error_count_cache_key = 'user_pwd_error_count_account:' + model.account + "|tenant_code:" + model.tenant_code
        error_count = cache.get(error_count_cache_key) if cache.get(error_count_cache_key) else error_count
    return 1 if int(error_count) >= 3 else 0


def refresh(
    token: str, keep_login: bool, userid: Optional[str] = None, code: Optional[str] = None, auto_logout: bool = False,
    is_auto_login=False
) -> None:
    userid = userid if userid else getattr(g, 'userid', None)
    code = code if code else getattr(g, 'code', None)
    if userid and code:
        expires = int(config.get('JWT.expires')) or 1800
        LoginToken().refresh_user_status(
            token, userid, code, keep_login=keep_login, keep_expire=expires, auto_logout=auto_logout
        )
        # 不是免登场景才更新用户活跃时间
        if keep_login and not is_auto_login:
            _set_user_last_active_time(code, userid)


def _set_user_last_active_time(code, userid):
    # 保存用户最后活跃时间, redis需要加前缀
    setattr(g, "code", code)
    setattr(g, "cache", RedisCache())
    cache = conn_redis()
    last_active_time = int(time.time())
    cache.hset(USER_LAST_ACTIVE, userid, last_active_time)
    repository.update("user", {"last_active_time": last_active_time}, {"id": userid})


def forced_offline(user_id: str, code: str, only_this_token=None, workbench_token = None) -> None:
    if not user_id or not code:
        raise UserError(message="Lack of parameter: user_id or code")
    # 注销当前账号工作台登录服务状态
    if workbench_token:
        from user.services.workbench_auth_service import Workbench
        Workbench(workbench_token).logout()
    # 强制下线用户
    return LoginToken().delete_token(user_id, code, only_this_token)


def login_as_domain(model, user=None):
    login_service_url = config.get('LoginWebServer.service')
    if not login_service_url:
        raise UserError(message='缺少登录服务配置')
    login_service_method = config.get('LoginWebServer.method')
    if not login_service_method:
        raise UserError(message='缺少登录服务校验方法配置')
    try:
        pwd_status = getattr(MIPADService(login_service_url), login_service_method)(model.account, model.password)
    except Exception as e:
        logger.exception(e)
        raise UserError(message='登录服务调用错误')

    _check_captcha(pwd_status, model, user)


def login_as_erp(model, user):
    hl = hashlib.md5()
    hl.update(model.password.encode('utf-8'))
    password = hl.hexdigest().upper()
    pwd_status = password == user.get('pwd').upper()
    _check_captcha(pwd_status, model, user)


def login_as_dmp(model: UserLoginModel, user: Dict[str, Union[int, str]]) -> None:
    pwd = user.get('pwd').encode('utf-8')
    pwd_status = bcrypt.hashpw(model.password.encode('utf-8'), pwd) == pwd
    _check_captcha(pwd_status, model, user)
    # 更新明文传输密码
    _pwd_to_sha1_pwd(model, user)


def login_as_external_sync(model, user):
    alg = None
    user_source = repository.get_one('user_source', {'id': user.get('user_source_id')})
    if user_source and user_source.get('data'):
        user_source_data = user_source.get('data')
        data = json.loads(user_source_data)
        alg = data.get('encrypt')
    if not alg or alg.lower() == 'md5':
        pwd_status = MD5Encrypt.check(model.password, user.get('pwd'), ignore_case=True)
    else:
        raise UserError(message='不支持的登录类型')
    _check_captcha(pwd_status, model, user)


def validate_tenant_code(code, model: UserLoginModel = None):
    if not user_repository.tenant_code_is_exists(code):
        # setattr(g, "code", model.tenant_code)
        setattr(g, "cache", RedisCache())
        count = check_ip_repeated_login(model)
        raise UserError(message=f'登录失败，当前第{count}次失败，失败10次将锁定账号')


def validate_user_auth(model: UserLoginModel, user):
    account_mode = get_login_account_mode(user, model.tenant_code)
    # 混合模式，根据当前登录用户account_mode决定登录逻辑
    login_as = {
        AccountMode.DOMAIN.value: login_as_domain,
        AccountMode.DMP.value: login_as_dmp,
        AccountMode.IMPORT.value: login_as_dmp,
        AccountMode.SYNC.value: login_as_external_sync,
        AccountMode.ERP.value: login_as_erp,
        AccountMode.IDM.value: login_as_idm,
    }
    if account_mode not in login_as:
        raise UserError(message=f'抱歉，不支持 {account_mode} 登陆，请联系管理员!')
    # 先验证验证码
    _check_captcha_only(model, user)
    login_as[account_mode](model, user)


def get_login_account_mode(user, tenant_code):
    """
    获取登录用户的模式
    如果登录开启了IDM方式，则所有用户都是通过IDM用户认证
    :param user:
    :param tenant_code:
    :return:
    """
    account_mode = user.get('account_mode') or AccountMode.DMP.value
    idm_config = get_idm_third_party_config(tenant_code)
    if idm_config:
        account_mode = AccountMode.IDM.value
    return account_mode


def get_login_user(model: UserLoginModel):
    user = user_repository.get_login_user(model)
    if not user:
        setattr(g, "code", model.tenant_code)
        setattr(g, "cache", RedisCache())
        count = check_ip_repeated_login(model)
        check_user_locked(user, model)
        _check_captcha(True, model, user)
        raise UserError(message=f'登录失败，当前第{count}次失败，失败10次将锁定账号')
        # raise UserError(message='用户名或密码错误')
    return user


def check_reset_passwd(model, account_mode):
    account_mode = account_mode or AccountMode.DMP.value
    if account_mode == AccountMode.DMP.value:
        user_reset_record = user_repository.check_if_reset_passwd(model.tenant_code, model.account)
        if user_reset_record:
            data = {"token": user_reset_record.get('token'), "code": LoginCode.InitPassword.value}
            return False, "新用户首次登陆需要重置密码", data
    return True, '', {}


def set_g(model: UserLoginModel):
    # 解决登录前，g.code没有值的问题，g.cache中初始化时没有g.code导致的一系列问题
    model.tenant_code = project.set_correct_project_code(model.tenant_code)
    setattr(g, "code", model.tenant_code)
    setattr(g, "cache", RedisCache())


def generate_email_code(model: UserLoginModel):
    if config.get('App.auth_mode', 'normal') != LoginAuthMode.Email.value:
        raise UserError(message='当前环境不支持邮箱验证模式')
    model.validate()
    validate_tenant_code(model.tenant_code, model)
    user = get_login_user(model)
    set_g(model)
    check_user_locked(user, model)
    validate_user_auth(model, user)

    code = safe_service.generate_email_code(user.get('id'))
    mail.send(
        mail.Mail(
            subject='Dmp邮箱验证码',
            body=f'【明源云数见】验证码{code}，您正在登录，若非本人操作，请勿泄露。',
            receiver=[mail.MailContact(name=user.get('name'), mail=user.get('email'))],
        ),
        subtype='html',
    )


def check_email_code(model, user):
    # 校验邮箱验证码
    if config.get('App.auth_mode', 'normal') == LoginAuthMode.Email.value:
        # 如果失败次数过多, 返回校验失败次数过多, 稍后再试
        if not safe_service.check_email_code(user.get('id'), model.mail_code):
            safe_service.inc_email_failed_times(user.get('id'))
            raise UserError(message='验证码校验失败')
        else:
            safe_service.clear_email_failed_times(user.get('id'))


def check_password_overdue(user):
    # 超过半年进行消息提醒
    now = datetime.datetime.now()
    last = user.get('last_password_change')
    if (now - last).total_seconds() <= 3600 * 24 * 180:
        return
    _message = {
        'source_id': user.get('id') + '_' + datetime.datetime.now().strftime("%Y%m%d%H%M%S"),
        'user_id': user.get('id'),
        'source': '通知',
        'type': '个人消息',
        'title': '您的密码已经超过半年未更新, 请及时更新密码',
        'url': '',
    }
    message_service.message_add(MessageModel(**_message))


def update_last_login_info(model, userid):
    # 更新最后一次登录信息：登录时间和ip
    last_login_time = int(time.time())
    last_login_ip = model.request.remote_addr
    user_repository.update_last_login_info(
        tenant_code=model.tenant_code, user_id=userid, last_login_time=last_login_time, last_login_ip=last_login_ip
    )


def generate_token_and_login(model: UserLoginModel, user):
    # 生成jwt
    domain = model.request.host
    account = user.get('account')
    userid = user.get('id')
    group_ids = user_repository.get_login_user_group_ids(model)

    expires = None
    if model.keep_alive:
        expires = int(config.get('JWT.expires')) + KEEP_LOGIN_DEFAULT_EXPIRE

    # 只有打开限制登录，才需要判断哪个agent
    login_limit = config.get('LoginConfig.limit_single_user_login', 0)
    pc_agent = False
    # 判断pc端还是移动端（pc端限制单用户登录）
    if login_limit in [1, '1']:
        pc_agent = True
        user_agent = model.request.user_agent or ""
        for agent in MOBILE:
            if user_agent.find(agent) != -1:
                pc_agent = False
                break

    token = set_login_status(
        model.response,
        domain,
        model.tenant_code,
        userid,
        account,
        group_ids,
        **{"expires": expires, "single": login_limit and pc_agent},
    )
    return token


def set_user_g(user):
    setattr(g, 'userid', user.get('id'))
    setattr(g, 'account', user.get('account'))


def check_user_locked(user, model: UserLoginModel):
    if (
        user and safe_service.is_account_locked(user.get('id'))
    ) or (
        model and safe_service.is_account_locked(get_user_account_key(model))
    ):
        raise UserError(message='该账户已被锁定，请10分钟后再试')


def get_user_account_key(model: UserLoginModel):
    return f'{model.tenant_code}-{model.account}'


def check_ip_repeated_login(model: UserLoginModel):
    request = model.request
    ip = request.remote_addr
    if not ip:
        return
    if safe_service.is_ip_locked(ip):
        raise UserError(message=f'登录失败次数过多，请稍后再试')
    count = safe_service.inc_ip_login_count(ip)
    return count


def after_login_success(model, user):
    # 设置用户全局变量
    set_user_g(user)

    # 更新最后一次登录信息
    update_last_login_info(model, user.get('id'))

    # 检查密码是否过期, 过期需要发送信息
    if user.get('account_mode') == AccountMode.DMP.value:
        check_password_overdue(user)

    # 清除验证失败次数
    safe_service.clear_ip_login_count(model.request.remote_addr)
    safe_service.clear_auth_failed_times(user.get('id'))
    if config.get('App.auth_mode', 'normal') != LoginAuthMode.Email.value:
        safe_service.clear_email_failed_times(user.get('id'))


def login(model: UserLoginModel) -> Tuple[bool, str, object]:
    """
    用户登录
    :param user.models.UserLoginModel model:
    :return:
    """
    model.validate()
    # # 检查ip是否重复登录多次（应该是登录失败得场景才判断ip重复， 所以先注掉）
    # check_ip_repeated_login(model)
    # 校验企业代码
    validate_tenant_code(model.tenant_code, model)
    # 检查灰度
    check_grayscale(model.request, model.response, model.tenant_code)
    # 获取登录用户
    user = get_login_user(model)
    # 设置租户全局变量
    set_g(model)
    # 登陆的时候进行租户license校验
    tenant_license_validate(model)
    # 检查用户是否被锁定
    check_user_locked(user, model)
    # 验证用户名密码
    validate_user_auth(model, user)
    # 验证邮箱验证码
    check_email_code(model, user)

    # 检查是否需要重置密码
    account_mode = get_login_account_mode(user, model.tenant_code)
    rv = check_reset_passwd(model, account_mode)
    if not rv[0]:
        return rv

    # 生成token, 设置登录状态
    token = generate_token_and_login(model, user)

    # 登录成功后的一些处理
    after_login_success(model, user)

    return True, "登录成功", token if model.return_token else user.get('id')


def get_project_license(code):
    """获取租户级的license"""
    sql = """SELECT p.code, p.use_license, tl.product_start, tl.product_end, tl.report_num FROM project p LEFT JOIN tenant_license tl ON p.code = tl.code WHERE p.code = %s LIMIT 1"""
    result = repository.get_data_by_sql(sql=sql, params=(code,), from_config_db=True)  # noqa
    # 到了这里肯定存在project， result肯定有值
    return result[0] if result else {}

def get_hw_license_expire(code):
    sql = """select expire_time from hw_market_instance t1 join hw_market_tenant t2 on t1.instance_id = t2.instance_id where t2.tenant_code = %s limit 1"""
    result = repository.get_data_by_sql(sql=sql, params=(code,), from_config_db=True)  # noqa
    return result

def tenant_license_validate(model: UserLoginModel):
    # 华为应用市场license校验
    hw_license_expire = get_hw_license_expire(model.tenant_code)
    if hw_license_expire:
        if datetime.datetime.now() > hw_license_expire[0]['expire_time']:
            raise UserError(message='当前租户使用时间已到期')

    project = get_project_license(model.tenant_code)

    now = datetime.datetime.now()
    product_start = project['product_start']
    product_end = project['product_end']
    # 如果未开启license不需要验证过期
    if project['use_license'] == 0:
        return

    error_msg = ''
    if product_start and product_end:
        if now < product_start:
            error_msg = '当前租户使用时间未到'
        elif now > product_end:
            error_msg = '当前租户使用时间已到期'

    if error_msg:
        raise UserError(message=error_msg)


def check_oa_token(loginid: str, stamp: int, token: str):
    return (
        sha1("".join([config.get("LoginConfig.oa_login_secretkey"), loginid, str(stamp)]).encode("utf-8")).hexdigest()
        == token
    )


def oa_login(model: OaUserLoginModel) -> str:
    """
    OA用户登陆
    :param user.models.UserLoginModel model:
    :return:
    """
    user_model = UserLoginModel(
        **{
            "account": model.loginid,
            "tenant_code": model.tenant_code,
            "response": model.response,
            "request": model.request,
        }
    )
    # 校验企业代码
    if not user_repository.tenant_code_is_exists(user_model.tenant_code):
        raise UserError(message='抱歉，您访问的租户不存在，请联系DMP管理员010-********')
    set_grayscale_project(model.request, model.response, model.tenant_code)
    # 校验账号
    user = user_repository.get_login_user(user_model)
    if not user:
        raise UserError(message='您尚未开通DMP数芯访问权限，请联系DMP管理员010-********')
    # 校验
    if not check_oa_token(model.loginid, model.stamp, model.token):
        raise UserError(message='抱歉，您使用了非法的方式进行访问，请联系DMP管理员010-********')

    # 从数据库中获取是否域账户登录
    # 解决登录前，g.code没有值的问题，g.cache中初始化时没有g.code导致的一系列问题
    tenant_code = project.set_correct_project_code(model.tenant_code)
    user_model.tenant_code = model.tenant_code = tenant_code
    setattr(g, "code", user_model.tenant_code)
    setattr(g, "cache", RedisCache())

    # 生成jwt
    domain = user_model.request.host
    account = user.get('account')
    userid = user.get('id')
    group_ids = user_repository.get_login_user_group_ids(user_model)

    expires = None
    if user_model.keep_alive:
        expires = int(config.get('JWT.expires')) + KEEP_LOGIN_DEFAULT_EXPIRE

    # 只有打开限制登录，才需要判断哪个agent
    login_limit = config.get('LoginConfig.limit_single_user_login', 0)
    pc_agent = False
    # 判断pc端还是移动端（pc端限制单用户登录）
    if login_limit in [1, '1']:
        pc_agent = True
        user_agent = user_model.request.user_agent or ""
        for agent in MOBILE:
            if user_agent.find(agent) != -1:
                pc_agent = False
                break
    try:
        token = set_login_status(
            user_model.response,
            domain,
            user_model.tenant_code,
            userid,
            account,
            group_ids,
            **{"expires": expires, "single": login_limit and pc_agent},
        )
    except Exception as e:
        raise UserError(message='抱歉登陆发生错误，原始错误信息 {msg}， 请联系DMP管理员010-********'.format(msg=str(e)))

    # 更新最后一次登录信息：登录时间和ip
    last_login_time = int(time.time())
    last_login_ip = user_model.request.remote_addr
    user_repository.update_last_login_info(
        tenant_code=user_model.tenant_code, user_id=userid, last_login_time=last_login_time, last_login_ip=last_login_ip
    )
    if model.return_token:
        return token
    return userid


def set_login_status(
    response: Response, domain: str, tenant_code: str, userid: str, account: str, group_ids: List[str], **kwargs
) -> str:
    """
    设置登录的状态
    :param group_ids:
    :param response:
    :param domain:
    :param tenant_code:
    :param userid:
    :param account:
    :return:
    """
    # 保证写入cookie中的tenant_code大小写一定正确
    tenant_code = project.set_correct_project_code(tenant_code)
    expires = kwargs.get("expires", None)
    single = kwargs.get("single", False)
    customize_roles = kwargs.get("customize_roles", [])
    external_user_id = kwargs.get("external_user_id")
    extend_yl_params = kwargs.get("extend_yl_params", '')
    is_developer = kwargs.get('is_developer', 0)
    dataset_ids = kwargs.get('dataset_ids', [])
    site_from = getattr(g, 'site_from', '')
    # 解决登录前，g.code没有值的问题，g.cache中初始化时没有g.code导致的一系列问题
    setattr(g, "code", tenant_code)
    # 判断用户是否被禁用
    if repository.data_is_exists("user", {"id": userid, "is_disabled": 1}):
        raise UserError(message='用户被禁用，请联系管理员！')
    # 判断角色是否存在
    if customize_roles:
        db_roles_map = {role.get("id"): role for role in external_rbac_service.get_all_roles()}
        for role in customize_roles:
            if role not in db_roles_map:
                raise UserError(message='角色:%s不存在' % role)
    extra = {
        'account': account,
        'code': tenant_code,
        'group_ids': group_ids,
        'customize_roles': customize_roles,
        'external_user_id': external_user_id,
        'extend_yl_params': extend_yl_params,
        'is_developer': is_developer,
        'dataset_ids': dataset_ids
    }
    if site_from:
        extra['__from'] = site_from
    token = LoginToken().create_token(
        userid,
        extra=extra,
        code=tenant_code,
        expire_in=expires,
        single=single,
    )

    refresh(token, userid=userid, code=tenant_code, keep_login=True, auto_logout=False, is_auto_login=kwargs.get('is_auto_login'))

    response.set_cookie(
        name='token', value=token, max_age=expires, domain=domain, path='/', secure=False, http_only=False
    )
    response.set_cookie(
        name='tenant_code', value=tenant_code, max_age=expires, domain=domain, path='/', secure=False, http_only=False
    )
    response.set_cookie(
        name='account',
        value=urllib.parse.quote(account),
        max_age=expires,
        domain=domain,
        path='/',
        secure=False,
        http_only=False,
    )
    response.set_cookie(
        name='extend_yl_params', value=extend_yl_params, max_age=expires, domain=domain, path='/',
        secure=False, http_only=False
    )

    # 如果该用户是灰度用户，则输出给前端标识灰度

    response.set_cookie(
        name='grayscale_user', value='', max_age=1, domain=domain, path='/', secure=False, http_only=False
    )

    if config.get('Grayscale.white_list'):
        gray_user = json.loads(config.get('Grayscale.white_list'))

        if gray_user.get(tenant_code) and account in gray_user.get(tenant_code):
            response.set_cookie(
                name='grayscale_user',
                value=urllib.parse.quote(account),
                max_age=expires,
                domain=domain,
                path='/',
                secure=False,
                http_only=False,
            )
    write_ty_cookie(domain, response, userid)
    return token


def login_mode(model: UserLoginModel) -> int:
    """
    用户登录模式（密码是否为明文传输模式 ）
    :param user.models.UserLoginModel model:
    :return:
    """
    if not model.tenant_code or not model.account:
        raise UserError(message='缺少参数')
    check_status = True
    if not user_repository.tenant_code_is_exists(model.tenant_code):
        setattr(g, "cache", RedisCache())
        count = check_ip_repeated_login(model)
        raise UserError(message=f'账号名或登录密码错误，已错误{count}次，错误10次将锁定账号10分钟')
    model.tenant_code = project.set_correct_project_code(model.tenant_code)
    # 租户是否禁用校验
    check_tenant_is_enable(model.tenant_code)
    if check_status:
        user = user_repository.get_login_user(model)
        if not user:
            setattr(g, "cache", RedisCache())
            count = check_ip_repeated_login(model)
            setattr(g, "code", model.tenant_code)
            check_user_locked(user, model)
            _check_captcha(False, model, user)
            raise UserError(message=f'登录失败，当前第{count}次失败，失败10次将锁定账号')
    # 兼容旧数据account_mode为空的情况，默认为DMP
    account_mode = get_login_account_mode(user, model.tenant_code)
    # 如果是域账号，IDM用户模式，则用户密码为明文
    if account_mode in [AccountMode.DOMAIN.value, AccountMode.IDM.value]:
        pwd_is_encry = 1
    else:
        pwd_is_encry = user.get('old_pwd')
    return pwd_is_encry


def _pwd_to_sha1_pwd(model: UserLoginModel, user: Dict[str, Union[int, str]]) -> None:
    """
    :param user.models.UserLoginModel model:
    :param dict user:
    :return:
    """
    if not model or not user or not user.get('old_pwd'):
        return
    sha1_pwd = sha1(model.password.encode()).hexdigest()
    encrypted_pwd = bcrypt.hashpw(sha1_pwd.encode('utf-8'), bcrypt.gensalt())
    user_repository.update_login_user_pwd(model, encrypted_pwd)


def set_user_setting(setting):
    return user_system_setting_repository.update_setting(get_cur_user_id(), setting)


def get_user_info(user_id=None, account=None):
    """
    OpenAPI获取用户基础信息以及用户组信息
    :param user_id:
    :param account:
    :return:
    """
    if hasattr(g, 'userid') or user_id:
        user_id = user_id if user_id else g.userid
    if hasattr(g, 'account') or account:
        account = account if account else g.account

    user_info = user_repository.get_user_by_account_or_user_id(user_id, account)
    if not user_info:
        raise UserError(400, '用户不存在')

    user_info["roles"] = user_repository.get_role_by_user_id_v2(user_info["id"]) or []

    user_info['groups'] = {}
    user_groups = user_repository.get_user_groups_by_user_id(user_info.get('id'))
    if not user_groups:
        return user_info

    user_info['groups'] = {'groups': user_groups, 'parent_groups_data': []}

    level_codes = set()
    for r in user_groups:
        code = r.get('code')
        while len(code) >= 10:
            code = code[:-5]
            level_codes.add(code)
    if level_codes:
        parent_groups = user_repository.get_user_groups_by_codes(list(level_codes))
        user_info['groups']['parent_groups_data'] = parent_groups

    return user_info


def get_redirect_url(user_id, group_ids, code) -> str:
    """
    根据用户权限获取用户重定向页面
    :return:
    """

    # 通过/user/profile接口中获取的apps的方法来判断跳转页面
    project_data = user_repository.get_user_project_profile()
    funcs_set = func_auth_service.get_user_funcs_set(user_id, group_ids) or {}
    apps = user_group_service.get_user_func_tree(
        {'user_id': user_id, 'group_ids': group_ids}, funcs_set, True, project_type=project_data.get('type')
    )

    # 跳转逻辑
    # 1. 任意菜单权限 2. 应用门户权限
    # 1 & 2 -> /home
    # !1 & 2 -> 第一个其他应用门户app_preview链接
    # 1 & !2 -> /home
    # !1 & !2 -> /home
    # 除了dmp应用外, 有pc应用门户, 且dmp应用中无菜单权限(!1 & 2)
    if len(apps) > 1 and (apps[0].get('function') is None or len(apps[0]['function']) == 0):
        app = apps[1]
        menu_id = None
        menu_url = None
        menus = app.get('function')
        if not menus:
            menu_id = app.get('id')
            menu_url = app.get('url')

        while menu_id is None:
            menu = menus[0]
            menus = menu.get('sub')
            if not menus:
                menu_id = menu.get('id')
                menu_url = menu.get('url')

        # url, app_id + menu_id + menu_url
        return f'/app_preview/index/{app["id"]}/{menu_id}/{menu_url}?code={code}'

    return '/home'


def get_sync_setting():
    # 获取用户同步设置
    result = repository.get_data("system_setting", {"item": USER_SYNC}, fields=["value"])
    if result and result.get("value"):
        return json.loads(result.get("value"))
    return {}


def update_dataset_business(dataset_id):
    # 绝大部分的场景应该是不更新，所有先判断下是否存在
    if not repository.data_is_exists(
        "dataset", {"id": dataset_id, "business_type": DatasetBusinessType.UserSync.value}
    ):
        repository.update_data("dataset", {"business_type": DatasetBusinessType.UserSync.value}, {"id": dataset_id})


def check_sync_data(data: dict, is_erp_user_source) -> dict:
    encrypt_type = data.get("encrypt")
    if not encrypt_type:
        raise UserError(message="加密方式必填")
    # 对前端传过来的data做简单校验和加工
    user_field = data.get("user", {}).get("field_relation")
    user_dataset_id = data.get("user", {}).get("source_dataset_id")
    if not user_field or not user_dataset_id:
        raise UserError(message='缺少用户表设置')

    # 必填项检查
    user_field_required = ["id", "name", "account"]
    for k in user_field_required:
        if not user_field.get(k):
            raise UserError(message='用户表中{}字段为必选项'.format(k))

    if not data.get("encrypt"):
        raise UserError(message='缺少用户加密方式')

    user_field_values = [x for x in user_field.values() if x and x != '']
    set_user_field_list = list(set(user_field_values))  # 去重
    if len(user_field_values) != len(set_user_field_list):
        raise UserError(message='用户属性配置字段配置不能重复')

    user_group_field = data.get("user_group", {}).get("field_relation")
    user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id")
    if is_erp_user_source:
        if not user_group_field or not user_group_dataset_id:
            raise UserError(message='缺少组织表设置')
        # 必填项检查(用户组)
        group_field_required = ["id", "name", "parent_id", "hierarchy"]
        for k in group_field_required:
            if not user_group_field.get(k):
                raise UserError(message='组织表中{}字段为必选项'.format(k))

    set_list = list(set(user_group_field.values()))  # 去重
    if len(user_group_field.values()) != len(set_list):
        raise UserError(message='组织属性配置字段配置不能重复')

    # 默认添加前端没有传的字段
    user_options = ["pwd", "mobile", "email", "group_id", "account_mode", "is_disabled"]
    user_options.append('group_id') if is_erp_user_source else user_options.append('user_source_group_id')
    for k in user_options:
        if k not in user_field.keys():
            user_field[k] = ''

    user_dataset_id = data.get("user", {}).get("source_dataset_id")
    user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id")
    if not repository.data_is_exists("dataset", {"id": user_dataset_id}):
        raise UserError(message='用户数据集不存在')
    if user_group_dataset_id and not repository.data_is_exists("dataset", {"id": user_group_dataset_id}):
        raise UserError(message='组织数据集不存在')

    user_dataset_fields = repository.get_data(
        "dataset_field", {"dataset_id": user_dataset_id}, ["col_name"], multi_row=True
    )
    check_dataset_setting(user_field, user_dataset_fields, "用户属性配置")

    user_group_dataset_fields = repository.get_data(
        "dataset_field", {"dataset_id": user_group_dataset_id}, ["col_name"], multi_row=True
    )
    check_dataset_setting(user_group_field, user_group_dataset_fields, "组织属性配置")

    return data


def check_dataset_setting(setting_field: dict, dataset_fields: list, msg: string):
    for key, value in setting_field.items():
        if not value:
            continue
        is_exist = False
        for dataset_field in dataset_fields:
            if value == dataset_field.get("col_name"):
                is_exist = True
                break
        if not is_exist:
            raise UserError(message='{}数据集{}字段不存在'.format(msg, value))


def deal_old_user_sync_depend_on(new_user_sync):
    user_source_id = new_user_sync.get('user_source_id')
    old_user_sync_data = repository.get_data("user_source", {"id": user_source_id}, fields=["data"])
    data = json.loads(old_user_sync_data.get("data")) if old_user_sync_data.get("data") else {}
    # 数据库中的历史同步用户表，用户组
    old_user_dataset_id = data.get("user", {}).get("source_dataset_id")
    old_user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id")

    # 避免相互引用
    from dataset.repositories import dataset_subject_repository

    # 判断 old_user_dataset_id 和 old_user_group_dataset_id 是否为主题包中的数据集
    subject_user = dataset_subject_repository.is_subject_table(old_user_dataset_id)
    old_user_dataset_id = subject_user.get("id") if subject_user else old_user_dataset_id

    subject_user_group = dataset_subject_repository.is_subject_table(old_user_group_dataset_id)
    old_user_group_dataset_id = subject_user_group.get("id") if subject_user_group else old_user_group_dataset_id

    # 用户修改后的
    new_user_dataset_id = new_user_sync.get("user", {}).get("source_dataset_id")
    new_user_group_dataset_id = new_user_sync.get("user_group", {}).get("source_dataset_id")

    # 数据集有改动的话，清理依赖调度
    if (
        old_user_dataset_id
        and old_user_group_dataset_id
        and (old_user_dataset_id != new_user_dataset_id or old_user_group_dataset_id != new_user_group_dataset_id)
    ):
        repository.delete_data(
            "dataset_depend", {"source_dataset_id": old_user_dataset_id, "depend_id": old_user_group_dataset_id}
        )


def write_ty_cookie(domain, response, user_id=None, user_info=None):
    # 写天眼上报需要的数据
    expired = datetime.datetime.now() + datetime.timedelta(days=3650)

    def write_cookie(user_name="", user_org="", user_account="", user_id=""):
        if user_name:
            user_name = urllib.parse.quote(json.dumps(user_name))
        if user_org:
            user_org = urllib.parse.quote(json.dumps(user_org))
        if user_account:
            user_account = urllib.parse.quote(user_account)
        if user_id:
            user_id = urllib.parse.quote(user_id)

        response.set_cookie(
            name="user_name", value=user_name, domain=domain, path='/', secure=False, http_only=False, expires=expired
        )
        response.set_cookie(
            name="user_org", value=user_org, domain=domain, path='/', secure=False, http_only=False, expires=expired
        )
        response.set_cookie(
            name="account", value=user_account, domain=domain, path='/', secure=False, http_only=False, expires=expired
        )
        response.set_cookie(
            name="user_id", value=user_id, domain=domain, path='/', secure=False, http_only=False, expires=expired
        )

    def write_account(user_account=""):
        # 保留之前的逻辑中的user_account不丢失
        if user_account:
            user_account = urllib.parse.quote(user_account)
        response.set_cookie(
            name="_account", value=user_account, domain=domain, path='/', secure=False, http_only=False, expires=expired
        )

    try:
        # 一般情况下，都会通过userid来获取用户信息,
        # 但当4云使用dashboar/login接口时, 会没有用户信息, 此时使用user_info来获取用户信息
        # user_info用户信息处理按以下优先级
        # 如果有外部传入的external_user_account和external_user_name, 优先使用外部传入的external_user_account和external_user_name

        # 有user_id没有user_info的情况
        if not user_info and user_id:
            info = get_user(user_id)
            if info:
                user = info.get_dict()
                user_opt_info = user_repository.get_user_opt_info(user_id) or {}
                write_cookie(user.get("name") or "", user_opt_info.get("user_org") or "", user.get("account") or "")
                return

        # 有user_info的情况
        if user_info:
            print('user_info:::::::', user_info)
            user_id = user_info.get('user_id', '')
            account = user_info.get('external_user_account') or user_info.get('account') or ''
            user_name = user_info.get('external_user_name') or user_info.get('user_name') or ''
            user_opt_info = user_repository.get_user_opt_info(user_info.get('id')) or {}
            user_org = user_opt_info.get('user_org') or ''
            write_cookie(user_name, user_org, account, user_id)
            write_account(user_info.get('_account', ''))
    except Exception as e:
        logger.error(msg="设置cookie参数`user-log`异常：{}".format(str(e)))


def get_role_list():
    return user_repository.get_role_list()


def get_sync_user(params_seq):
    try:
        params_arr = str(params_seq).split("$%$")
        last_user_id = params_arr[0] if len(params_arr) >= 2 else None
        last_sync_time = params_arr[1] if len(params_arr) >= 2 else None
        del_user_tag = params_arr[2] if len(params_arr) == 3 else None
        sync_user_info = {'errcode': 0, 'is_complete': 0, 'data': [], 'data_del': [], 'new_seq': ''}
        # 同步标志
        new_seq = {}
        # 获取同步人员
        add_or_update_user_list = user_repository.get_sync_user_list(
            last_user_id, last_sync_time, YZS_SYNC_DATA_PAGE_SIZE
        )
        # 获取用户组织架构
        user_ids = [r['user_guid'] for r in add_or_update_user_list]
        user_group_map = user_repository.get_user_dept_by_user_ids(user_ids)
        top_dept = user_repository.get_top_dept()
        for user in add_or_update_user_list:
            user_dept_list = user_group_map.get(user['user_guid'])
            # user['depts'] = (
            #     [r['group_id'] for r in user_dept_list]
            #     if user_dept_list
            #     else [top_dept.get('dept_guid') if top_dept else None]
            # )
            # fix sornar issue
            if user_dept_list:
                user['depts'] = [r['group_id'] for r in user_dept_list]
            elif top_dept:
                user['depts'] = [top_dept.get('dept_guid')]
            else:
                user["depts"] = [None]
        sync_user_info['data'] = copy_sync_data(
            add_or_update_user_list, ["user_name", "user_code", "tel", "user_guid", "email", "is_disabled", "depts"]
        )
        # 本次同步人员数量
        add_or_update_user_list_count = len(add_or_update_user_list)
        # 取最后一条同步人员记录
        if add_or_update_user_list_count > 0:
            last_user = add_or_update_user_list[add_or_update_user_list_count - 1]
            new_seq['user_guid'] = last_user['user_guid']
            new_seq['modified_on'] = last_user['modified_on']
        elif add_or_update_user_list_count == 0:
            # 正好上一次同步完,为实现断点续传，保留上次同步的标记
            new_seq['user_guid'] = last_user_id
            new_seq['modified_on'] = last_sync_time
        # 记录本次同步人员记录
        user_repository.add_user_sync_record(user_ids)
        # 新增和修改同步完成
        if add_or_update_user_list_count < YZS_SYNC_DATA_PAGE_SIZE:
            # 删除上次同步成功的人员
            user_repository.delete_user_sync_record(del_user_tag)
            # 获取需要同步删除的人员
            delete_user_list = user_repository.get_sync_delete_user_list(YZS_SYNC_DATA_PAGE_SIZE)
            # 删除人员同步未完成
            if len(delete_user_list) > 0:
                del_user_guid_list = [r['id'] for r in delete_user_list]
                del_user_tag = str(uuid.uuid1())
                new_seq['del_user_tag'] = del_user_tag
                # 标记需要同步删除的人员，本次接口请求后，不能保证云助手能成功同步，不能直接删除。通过标记本次同步删除的人员，待下次请求入参中del_user_tag是否为本次传入的del_user_tag值判断本次云助手是否同步成功，下次请求时，通过del_user_tag来删除数据
                user_repository.set_user_sync_record_deleted_tag(del_user_guid_list, del_user_tag)
                sync_user_info['data_del'] = del_user_guid_list
            else:
                sync_user_info['is_complete'] = 1  # 删除同步完成，整个同步过程完成
        sync_user_info_arr = [str(new_seq.get('user_guid')), str(new_seq.get('modified_on'))]
        if new_seq.get('del_user_tag'):
            sync_user_info_arr.appent(str(new_seq.get('del_user_tag')))
        sync_user_info['new_seq'] = '$%$'.join(sync_user_info_arr)
    except Exception as e:
        sync_user_info['errcode'] = str(e)
        sync_user_info['is_complete'] = 1
    return sync_user_info


def get_sync_dept(params_seq):
    sync_dept_info = {'errcode': 0, 'is_complete': 0, 'data': [], 'data_del': [], 'new_seq': ''}
    try:
        params_arr = str(params_seq).split("$%$")
        last_dept_id = params_arr[0] if len(params_arr) >= 2 else None
        last_sync_time = params_arr[1] if len(params_arr) >= 2 else None
        del_dept_tag = params_arr[2] if len(params_arr) == 3 else None
        # 同步标志
        new_seq = {}
        # 获取同步组织
        add_or_update_dept_list = user_repository.get_sync_dept_list(
            last_dept_id, last_sync_time, YZS_SYNC_DATA_PAGE_SIZE
        )
        # 获取组织架构
        sync_dept_info['data'] = copy_sync_data(
            add_or_update_dept_list, ["dept_guid", "dept_name", "parent_guid", "sort", "is_company", "is_end_company"]
        )
        # 本次同步组织数量
        add_or_update_dept_list_count = len(add_or_update_dept_list)
        if add_or_update_dept_list_count > 0:
            last_dept = add_or_update_dept_list[add_or_update_dept_list_count - 1]
            new_seq['dept_guid'] = last_dept['dept_guid']
            new_seq['modified_on'] = last_dept['modified_on']
        elif add_or_update_dept_list_count == 0:
            new_seq['dept_guid'] = last_dept_id
            new_seq['modified_on'] = last_sync_time
        # 记录本次同步组织记录
        dept_ids = [r['dept_guid'] for r in add_or_update_dept_list]
        user_repository.add_dept_sync_record(dept_ids)
        # 新增和修改同步完成
        if add_or_update_dept_list_count < YZS_SYNC_DATA_PAGE_SIZE:
            # 删除上次同步成功的组织
            user_repository.delete_dept_sync_record(del_dept_tag)
            # 获取需要同步删除的组织
            delete_dept_list = user_repository.get_sync_delete_dept_list(YZS_SYNC_DATA_PAGE_SIZE)
            # 删除组织同步未完成
            if len(delete_dept_list) > 0:
                del_dept_guid_list = [r['id'] for r in delete_dept_list]
                del_dept_tag = str(uuid.uuid1())
                new_seq['del_dept_tag'] = del_dept_tag
                user_repository.set_dept_sync_record_deleted_tag(del_dept_guid_list, del_dept_tag)
                sync_dept_info['data_del'] = del_dept_guid_list
            else:
                sync_dept_info['is_complete'] = 1  # 删除同步完成，整个同步过程完成
        sync_dept_info_arr = [str(new_seq.get('dept_guid')), str(new_seq.get('modified_on'))]
        if new_seq.get('del_dept_tag'):
            sync_dept_info_arr.appent(str(new_seq.get('del_dept_tag')))
        sync_dept_info['new_seq'] = '$%$'.join(sync_dept_info_arr)
    except Exception as e:
        sync_dept_info['errcode'] = str(e)
        sync_dept_info['is_complete'] = 1
    return sync_dept_info


def copy_sync_data(source, fields):
    targets = []
    if source:
        for data in source:
            data_dic = dict(data)
            target = {}
            targets.append(target)
            for field in fields:
                if field in data_dic.keys():
                    target[field] = data_dic.get(field)
    return targets


def yzs_user_login(model: YzsUserLoginModel) -> str:
    """
    云助手用户登陆
    :param user.models.YzsUserLoginModel model:
    :return:
    """
    user_model = UserLoginModel(
        **{
            "account": model.account,
            "tenant_code": model.tenant_code,
            "response": model.response,
            "request": model.request,
        }
    )
    # 校验企业代码
    if not user_repository.tenant_code_is_exists(user_model.tenant_code):
        raise UserError(message='抱歉，您访问的租户不存在，请联系DMP管理员010-********')
    set_grayscale_project(model.request, model.response, user_model.tenant_code)

    # 校验账号
    user = user_repository.get_login_user(user_model)
    if not user:
        raise UserError(message='您尚未开通DMP数芯访问权限，请联系DMP管理员010-********')

    # 从数据库中获取是否域账户登录
    # 解决登录前，g.code没有值的问题，g.cache中初始化时没有g.code导致的一系列问题
    tenant_code = project.set_correct_project_code(model.tenant_code)
    user_model.tenant_code = model.tenant_code = tenant_code
    setattr(g, "code", user_model.tenant_code)
    setattr(g, "cache", RedisCache())

    # 生成jwt
    domain = user_model.request.host
    account = user.get('account')
    setattr(g, "account", account)
    userid = user.get('id')
    setattr(g, "userid", user.get('id'))
    group_ids = user_repository.get_login_user_group_ids(user_model)
    setattr(g, "group_ids", group_ids)

    expires = None
    if user_model.keep_alive:
        expires = int(config.get('JWT.expires')) + KEEP_LOGIN_DEFAULT_EXPIRE

    # 只有打开限制登录，才需要判断哪个agent
    login_limit = config.get('LoginConfig.limit_single_user_login', 0)
    pc_agent = False
    # 判断pc端还是移动端（pc端限制单用户登录）
    if login_limit in [1, '1']:
        pc_agent = True
        user_agent = user_model.request.user_agent or ""
        for agent in MOBILE:
            if user_agent.find(agent) != -1:
                pc_agent = False
                break
    try:
        token = set_login_status(
            user_model.response,
            domain,
            user_model.tenant_code,
            userid,
            account,
            group_ids,
            **{"expires": expires, "single": login_limit and pc_agent},
        )
    except Exception as e:
        raise UserError(message='抱歉登陆发生错误，原始错误信息 {msg}， 请联系DMP管理员010-********'.format(msg=str(e)))

    # 更新最后一次登录信息：登录时间和ip
    last_login_time = int(time.time())
    last_login_ip = user_model.request.remote_addr
    user_repository.update_last_login_info(
        tenant_code=user_model.tenant_code, user_id=userid, last_login_time=last_login_time, last_login_ip=last_login_ip
    )
    if model.return_token:
        return token
    return userid


_yzs_host = config.get('Yzs.domain')


def yzs_login(request, response, kwargs) -> Tuple[str, str, list]:
    corpid = kwargs.get('tenant_id')
    code = kwargs.get('code')
    appsecret = kwargs.get('app_secret')
    if code is None or corpid is None:
        raise UserError(500, "corpid或code不存在")

    # 获取access_token
    get_access_token_url = f"{_yzs_host}/api/mingy-app-open/get-access-token"
    access_token_info = http_request(get_access_token_url, {'corpid': corpid, 'corpsecret': appsecret}, 10, 'post')
    if int(access_token_info.get('errcode')) != 0:
        raise UserError(500, "获取access_token失败：{0}".format(access_token_info.get('errmsg')))
    access_token = access_token_info.get('data').get('access_token')
    kwargs['access_token'] = access_token

    # 获取用户信息
    yzs_login_url = f"{_yzs_host}/api/tenant-open/sso-user-info?access_token={access_token}"
    yzx_login_info = http_request(yzs_login_url, {'code': code}, 10, 'post')
    if int(yzx_login_info.get('errcode')) != 0:
        raise UserError(500, "获取用户信息失败：{0}".format(yzx_login_info.get('errmsg')))

    user_code = yzx_login_info.get('data').get('user_code')
    # tenant_code = yzx_login_info.get('data').get('tenant_code')
    tenant_code = ""
    if not tenant_code:
        # 根据corpid获取dmp 租户code
        yzs_conf = repository.get_data(
            'project_yzs_config', {'tenant_id': corpid}, fields=["code"], from_config_db=True
        )
        if not yzs_conf:
            raise UserError(code=404, message="报告不存在")
        tenant_code = yzs_conf.get("code")
    # 登录成功后，DMP进行登录，成功后进行跳转
    model = YzsUserLoginModel()
    model.request = request
    model.response = response
    model.account = user_code
    model.tenant_code = tenant_code
    model.validate()
    user_id = yzs_user_login(model)
    group_ids = user_repository.get_login_user_group_ids(model)
    return user_id, tenant_code, group_ids


def yzs_login_debug(request, response, account, tenant_code) -> Tuple[str, str, list]:
    model = YzsUserLoginModel()
    model.request = request
    model.response = response
    model.account = account
    model.tenant_code = tenant_code
    model.validate()

    user_id = yzs_user_login(model)

    group_ids = user_repository.get_login_user_group_ids(model)
    # 根据用户权限获取用户重定向页面

    return user_id, tenant_code, group_ids


def http_request(url, params, timeout, type='get'):
    if type.lower() == 'get':
        response = requests.get(url=url, params=params, timeout=10)
    elif type.lower() == 'post':
        response = requests.post(url=url, json=params, timeout=timeout)
    else:
        raise UserError(message='仅支持get,post调用')

    if response.status_code != 200:
        raise UserError(message='调用云助手请求失败:' + response.text)
    data = response.json()
    return data


def init_password(**kwargs):
    """
    修改用户名密码
    :param user.models.ChangePasswordModel model:
    :return:
    """

    token = kwargs.get('token')
    new_password = kwargs.get('password')
    try:
        token = jwt.decode(token, config.get('JWT.init_password_secret'), algorithms="HS256")
    except jwt.DecodeError:
        raise UserError(code=400, message="token 格式无效")
    code = token.get('code')
    account = token.get('account')
    # 解决登录前，g.code没有值的问题，g.cache中初始化时没有g.code导致的一系列问题
    code = project.set_correct_project_code(code)
    setattr(g, "code", code)
    setattr(g, "account", account)
    setattr(g, "cache", RedisCache())
    # 租户admin用户code等于account
    user = user_repository.get_user_by_account(account, code)
    if not user:
        raise UserError(message='用户不存在')
    if user.get("account_mode") == AccountMode.SYNC.value:
        raise UserError(message='同步的用户不允许修改密码')
    validate_passwd(account, new_password)

    # 新老密码不能相同
    old_pwd = user.get('pwd').encode('utf-8')
    new_pwd = sha1(new_password.encode()).hexdigest().encode('utf-8')
    pwd_status = bcrypt.hashpw(new_pwd, old_pwd) == old_pwd
    if pwd_status:
        raise UserError(message='新密码不能和旧密码相同')

    passwd = bcrypt.hashpw(new_pwd, bcrypt.gensalt()).decode('utf-8')
    # 清除初始化记录
    user_repository.delete_user_record(code, account)
    return user_repository.update_user(code, account, passwd)


def validate_passwd(account, passwd):
    if not passwd:
        raise UserError(message='请输入项目密码')
    if len(passwd) < 8:
        raise UserError(message='为了您的账号安全，密码长度须不少于8位字符')
    pattern = r'(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[\W_]).{8,}'
    if re.match(pattern, passwd) is None:
        raise UserError(message='为了您的账号安全，密码须为字母、数字和特殊字符组合')
    if account in passwd:
        raise UserError(message='为了您的账号安全，密码中不可包含用户名信息')

    for idx in range(2, len(passwd)):
        pre2 = passwd[idx - 2]
        pre1 = passwd[idx - 1]
        cur = passwd[idx]
        if pre2 == pre1 == cur:
            raise UserError(message='为了您的账号安全，密码中不可存在连续数字或字母')
        if pre2.isalnum() and pre1.isalnum() and cur.isalnum() and (ord(pre2) + 2 == ord(pre1) + 1 == ord(cur)):
            raise UserError(message='为了您的账号安全，密码中不可存在连续数字或字母')


def upset_user_organization(user_id, org_id, org_name, org_level):
    data = {"user_id": user_id, "group_id": org_id, "org_name": org_name, "org_level": org_level}
    if repository.data_is_exists("user_organization", {"user_id": user_id, "group_id": org_id}):
        return repository.update("user_organization", data, {"user_id": user_id, "group_id": org_id})
    return repository.add_data("user_organization", data)


def get_yzs_auth_apps(project_code):
    """
    按照是否集成了云助手，显示数见应用
    :param project_code:租户编码
    :return:有权限的数见应用
    """
    data_list = []
    if project_code:
        yzs_config = repository.get_data('project_yzs_config', {'code': project_code}, from_config_db=True)
        auth_status = 1 if yzs_config and yzs_config.get('tenant_id') else 0
        data_list.append({'app_code': 'shujianmsg', 'auth_status': auth_status})
        data_list.append({'app_code': 'shujianreport', 'auth_status': auth_status})
    return data_list


def get_my_portals():
    """获取我的门户"""
    user_id = get_cur_user_id()
    if not user_id:
        raise HTTPUnauthorized('Authentication Required', "Without userid in context", [])
    user_group_ids = g.group_ids
    project_data = user_repository.get_user_project_profile()
    funcs_set = func_auth_service.get_user_funcs_set(user_id, user_group_ids) or {}
    return user_group_service.get_user_func_tree(
        {'user_id': user_id, 'group_ids': user_group_ids}, funcs_set, True, project_type=project_data.get('type')
    )


def get_third_party_url(project_code):
    """
    第三方登录跳转
    :param project_code:租户编码
    :return:有权限的数见应用
    """
    login_model_setting = get_third_party_config(project_code)
    value = json.loads(login_model_setting.get('value'))
    login_model = value.get('model')
    if login_model == "third_party":
        data = value.get('data')
        url = data.get('authorize_uri')
        client_id = data.get('client_id')
        redirect_uri = config.get("Domain.dmp") + f'/login?from=third_party'
        url = url_add_param(url, {"client_id": client_id})
        url = url_add_param(url, {"redirect_uri": redirect_uri})
        url = url_add_param(url, {"response_type": "code"})
        return url
    return None


def third_party_login(request, response, access_code, project_code):
    """
    第三方登录处理
    :param request:
    :param response:
    :param access_code:
    :param project_code:
    :return:
    """

    login_model_setting = get_third_party_config(project_code)
    access_token = third_party_get_access_token(access_code, login_model_setting, project_code)
    if not access_token:
        raise UserError("获取access_token失败")

    user_profile = third_party_get_profile(access_token, login_model_setting)
    if not user_profile:
        raise UserError("获取用户信息失败")

    login_model = SingleUserLoginModel()
    login_model.request = request
    login_model.response = response
    login_model.account = user_profile.get('id')
    login_model.tenant_code = project_code
    login_model.validate()
    return single_user_login(login_model)


def validate_login_model_setting(login_model_setting):
    """
    校验用户登录配置
    :param login_model_setting:
    :return:
    """
    if not login_model_setting or not login_model_setting.get('value'):
        raise UserError(message="不存在登录模式配置")
    value = json.loads(login_model_setting.get('value'))
    if value.get('model') == "third-party":
        data = value.get('data')
        if not data:
            raise UserError(message="不存在第三发配置参数")
        if not data.get('name'):
            raise UserError(message="不存在name")
        if not data.get('client_id'):
            raise UserError(message="不存在client_id")
        if not data.get('client_secret'):
            raise UserError(message="不存在client_secret")
        if not data.get('authorize_uri'):
            raise UserError(message="不存在authorize_uri")
        if not data.get('access_token_uri'):
            raise UserError(message="不存在access_token_uri")
        if not data.get('profile_uri'):
            raise UserError(message="不存在profile_uri")
    elif value.get('model') == "idm":
        data = value.get('data')
        if not data:
            raise UserError(message="不存在IDM登录配置参数")
        if not data.get('idm_name'):
            raise UserError(message="不存在idm_name")
        if not data.get('host'):
            raise UserError(message="不存在host")
        if not data.get('appuser'):
            raise UserError(message="不存在appuser")
        if not data.get('privatekey'):
            raise UserError(message="不存在privatekey")


def get_third_party_config(project_code):
    """
    从缓存获取系统登录模式信息
    params: project_code
    """
    # 先从缓存中获取
    cache = RedisCache(key_prefix=f"{PREFIX_LOGIN_MODEL}:")
    login_model_config = cache.get(key=project_code)
    # 缓存中不存在，则从数据库中获取
    if not login_model_config:
        project = repository.get_data('project', {"code": project_code}, from_config_db=True)
        if not project:
            raise UserError(message=f"当前项目{project_code}不存在")
        g.code = project_code
        login_model_config = repository.get_data('system_setting', {"category": "user", "item": "login_model"})
        validate_login_model_setting(login_model_config)
        # 设置缓存 7天过期： 60 * 60 * 24 * 7
        cache.set(key=project_code, value=json.dumps(login_model_config), time=604800)
    if isinstance(login_model_config, bytes):
        login_model_config = json.loads(login_model_config.decode())
    return login_model_config


def third_party_get_access_token(access_code, login_model_setting, project_code):
    value = json.loads(login_model_setting.get('value'))
    login_model = value.get('model')
    if login_model == "third_party":
        data = value.get('data')
        access_token_uri = data.get('access_token_uri')
        redirect_uri = config.get("Domain.dmp") + f'/login?from=third_party'
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        params = {"client_id": client_id, "client_secret": client_secret, "grant_type": "authorization_code", "redirect_uri": redirect_uri,
                  "code": access_code}
        try:
            response = requests.post(access_token_uri, data=params, timeout=60)
            if response.status_code != 200:
                raise UserError(message='状态：' + str(response.status_code) + ' , ' + response.reason + '。' + response.text)
            data = response.text.split('&')
            access_token = data[0].replace("access_token=", "")
            return access_token
        except:
            raise UserError(message="第三方登录异常，请重新回到登录页面再试")
    return None


def third_party_get_profile(access_token, login_model_setting):
    value = json.loads(login_model_setting.get('value'))
    login_model = value.get('model')
    if login_model == "third_party":
        profile_uri = value.get('data').get('profile_uri')
        try:
            params = {"access_token": access_token}
            response = requests.post(profile_uri, data=params, timeout=60)
            if response.status_code != 200:
                raise UserError(message='状态：' + str(response.status_code) + ' , ' + response.reason + '。' + response.text)
            return json.loads(response.text)
        except:
            raise UserError(message="第三方登录异常，请重新回到登录页面再试")


def single_user_login(model: SingleUserLoginModel) -> str:
    """
    单点登录
    :param user.models.YzsUserLoginModel model:
    :return:
    """
    user_model = UserLoginModel(
        **{
            "account": model.account,
            "tenant_code": model.tenant_code,
            "response": model.response,
            "request": model.request,
        }
    )
    # 校验企业代码
    if not user_repository.tenant_code_is_exists(user_model.tenant_code):
        raise UserError(message='抱歉，您访问的租户不存在，请联系数见管理员010-********')
    set_grayscale_project(model.request, model.response, user_model.tenant_code)

    # 校验账号
    user = user_repository.get_login_user(user_model)
    if not user:
        raise UserError(message=f'当前用户【{model.account}】尚未开通数见访问权限，请联系数见管理员010-********')

    # 从数据库中获取是否域账户登录
    # 解决登录前，g.code没有值的问题，g.cache中初始化时没有g.code导致的一系列问题
    tenant_code = project.set_correct_project_code(model.tenant_code)
    user_model.tenant_code = model.tenant_code = tenant_code
    setattr(g, "code", user_model.tenant_code)
    setattr(g, "cache", RedisCache())

    # 生成jwt
    domain = user_model.request.host
    account = user.get('account')
    setattr(g, "account", account)
    userid = user.get('id')
    setattr(g, "userid", user.get('id'))
    group_ids = user_repository.get_login_user_group_ids(user_model)
    setattr(g, "group_ids", group_ids)

    expires = None
    if user_model.keep_alive:
        expires = int(config.get('JWT.expires')) + KEEP_LOGIN_DEFAULT_EXPIRE

    # 只有打开限制登录，才需要判断哪个agent
    login_limit = config.get('LoginConfig.limit_single_user_login', 0)
    pc_agent = False
    # 判断pc端还是移动端（pc端限制单用户登录）
    if login_limit in [1, '1']:
        pc_agent = True
        user_agent = user_model.request.user_agent or ""
        for agent in MOBILE:
            if user_agent.find(agent) != -1:
                pc_agent = False
                break
    try:
        set_login_status(
            user_model.response,
            domain,
            user_model.tenant_code,
            userid,
            account,
            group_ids,
            **{"expires": expires, "single": login_limit and pc_agent},
        )
    except Exception as e:
        raise UserError(message='抱歉登陆发生错误，原始错误信息 {msg}， 请联系DMP管理员010-********'.format(msg=str(e)))

    # 更新最后一次登录信息：登录时间和ip
    last_login_time = int(time.time())
    last_login_ip = user_model.request.remote_addr
    user_repository.update_last_login_info(
        tenant_code=user_model.tenant_code, user_id=userid, last_login_time=last_login_time, last_login_ip=last_login_ip
    )
    return True, userid


def get_idm_third_party_config(project_code):
    """
    获取IDM类型的第三方登录账号配置
    :param project_code:
    :return:
    """
    third_party_config = get_third_party_config(project_code)
    value = json.loads(third_party_config.get('value'))
    login_model = value.get('model') if value.get('model') else ''
    data = {}
    if login_model.lower() == "idm":
        data = value.get('data')
    return data


def idm_validate(request, response, project_code, ticket_type, ticket_name, ticket_value):
    """
    IDM免登到数见（用于路劲）
    :param request:
    :param response:
    :param project_code:
    :param ticket_type:
    :param ticket_name:
    :param ticket_value:
    :return:
    """
    idm_config = get_idm_third_party_config(project_code)
    idm_login_api = idm_service.IdmLoginApi(idm_config)
    account = idm_login_api.tgt_validate(ticket_type, ticket_name, ticket_value)
    if not account:
        raise UserError(message="用户校验失败，登录失败")

    return third_party_login_by_account(request, response, project_code, account)


def third_party_login_by_account(request, response, project_code, account):
    """
    按用户名单点登录
    :param request:
    :param response:
    :param project_code:
    :param account:
    :return:
    """
    login_model = SingleUserLoginModel()
    login_model.request = request
    login_model.response = response
    login_model.account = account
    login_model.tenant_code = project_code
    login_model.validate()
    return single_user_login(login_model)


def login_as_idm(model, user):
    """
    IDM用户，密码登录
    :param model:
    :param user:
    :return:
    """
    try:
        idm_config = get_idm_third_party_config(model.tenant_code)
        idm_login_api = idm_service.IdmLoginApi(idm_config)
        account = idm_login_api.tgt_auth(model.account, model.password)
        pwd_status = True if account else False
    except Exception as e:
        msg = 'IDM登录认证错误，errs：'+str(e)
        logger.exception(msg)
        raise UserError(message=msg)

    _check_captcha(pwd_status, model, user)


def _set_group_ids(request):
    """
    从token中获取、设置组织id
    :param request:
    :return:
    """
    g.group_ids = []
    g.group_id = ''

    token = request.cookies.get('token')
    data = jwt.decode(token, config.get('JWT.secret'), algorithms='HS256')

    if data.get('group_id'):
        g.group_id = data.get('group_id')
        g.group_ids = [g.group_id]
    if data.get('group_ids'):
        g.group_ids = data.get('group_ids')


def get_user_application_index(request, kwargs):
    """
    获取用户的门户列表数据
    :param request:
    :param kwargs:
    :return:
    """
    user_id = get_cur_user_id()
    # 第三方可能没有user_id
    # if not user_id:
    #     raise HTTPUnauthorized('Authentication Required', "Without userid in context", [])
    # 获取token中的组织id
    _set_group_ids(request)
    user_group_ids = g.group_ids
    project_data = user_repository.get_user_project_profile()
    funcs_set = func_auth_service.get_user_funcs_set(user_id, user_group_ids) or {}
    platform = kwargs.get("platform")
    app = user_group_service.get_user_func_tree(
        {'user_id': user_id, 'group_ids': user_group_ids, 'platform': platform}, funcs_set, True,
        project_type=project_data.get('type')
    )
    remove_dmp_portal(app)
    set_app_source_from(app)
    return app


def remove_dmp_portal(app):
    try:
        if app and app[0].get('name') == 'DMP' and app[0].get('id') == user_group_service.buildin_application_id:
            app.pop(0)
    except:
        pass


def set_app_source_from(app_data: list):
    for app in app_data:
        if app.get('type_access_released') == ApplicationTypeAccessReleased.ThirdParty.value:
            app['source_from'] = ApplicationTypeAccessReleasedSourceStr.MipAuth.value
            # auth_from是在对应的集成登录中指定的第三方认证模式
            auth_from = getattr(g, 'auth_from', '')
            if auth_from == TokenAuthFrom.ThirdCloudAuth.value:
                app['source_from'] = ApplicationTypeAccessReleasedSourceStr.ThirdParty.value


def filter_func(func_set: dict):
    # 权限过滤
    from dmplib.saas.project import get_project_info
    if not func_set:
        return {}

    # 1. 租户已经过期, 只开启部分权限
    project_info = get_project_info(g.code)
    if project_info.get('is_expire'):
        filter_value = 'edit'
        func_code_list = [
            'user-group', 'user-role', 'data-permission', 'app-site',
            'report_center', 'feeds', 'add-dataset', 'operation_center',
            'data_sync', 'meta_service', 'add-datasource', 'width_table_monitor'
        ]
        for key, func in func_set.items():
            if key in func_code_list:
                continue
            func_set[key] = list(filter(lambda x: x != filter_value, func))
    # 2. 租户开启了开发者登录, 只有开发者有权限
    if project_info.get('is_rdc_auth') == 1 and getattr(g, 'is_developer', 0) != 1:
        for key, func in func_set.items():
            if key in ['self-service', 'ppt', 'Home']:
                continue
            func_set[key] = []
    return func_set


def check_tenant_is_enable(tenant_code):
    """
    检查租户是否禁用
    :param tenant_code:
    :return:
    """
    project_info = project.get_project_info(tenant_code)
    project_enabled = project_info.get("project_enabled")
    if project_info and project_enabled in [0, '0']:
        project_disable_reason = project_info.get("project_disable_reason")
        raise UserError(code=1001, message=project_disable_reason)

def is_tenant_exists(code):
    project = repository.get_one('project', {'code': code}, ['code'], from_config_db=True)
    return project

if __name__ == "__main__":
    kwargs = {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhY2NvdW50IjoiaW5pdF8zIiwiY29kZSI6ImluaXRfMyJ9.XSKTa-DPnxog5c5basoo3r8aO4AlWKvOyQQP6neHLhw",
        "password": "Aaron_313%",
    }
    init_password(**kwargs)
