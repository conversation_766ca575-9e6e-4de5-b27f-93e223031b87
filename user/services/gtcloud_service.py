import requests
import curlify
from loguru import logger
from datetime import datetime
import json
from urllib.parse import urljoin, quote, urlencode, parse_qs, urlparse, urlunparse

from dmplib.hug import debugger, g
from dmplib import config
from dmplib.utils.errors import UserError
from base import repository

from user.models import UserLoginModel
from user.services.user_service import generate_token_and_login

_debugger = debugger.Debug(__name__)


class GtCloudSSo(object):

    def __init__(self, request, response):
        self.timeout = 10
        self.request = request
        self.response = response

    @staticmethod
    def record_log_of_gtcloud(url, params, headers, start_time, api_result, curl_info, is_success):
        """
        记录mip日志
        :param url:
        :param params:
        :param headers:
        :param start_time:
        :param api_result:
        :param curl_info:
        :param is_success:
        :return:
        """
        try:
            from components.fast_logger import FastLogger

            end_time = datetime.now()

            FastLogger.ApiFastLogger(
                action="request_gtcloud_sso",
                org_code=getattr(g, 'code', None),
                api_url=url,
                headers=headers,
                api_param=str(params) if params else '',
                is_success=is_success,
                start_time=start_time,
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                duration=(end_time-start_time).total_seconds(),
                curl=curl_info,
                api_result=json.dumps(api_result, ensure_ascii=False)
            ).record()
        except Exception as e:
            logger.exception(f"记录日志失败:{e}")

    @property
    def headers(self):
        return {'Content-type': 'Application/x-www-form-urlencoded', 'accept':'application/json'}

    def _request(self, url, method="GET", params=None, headers=None):
        result = ''
        curl_info = ''
        is_success = 0
        start_time = datetime.now()
        headers = headers or self.headers
        try:
            if method.upper() == "GET":
                res = requests.get(url, params=params, headers=headers, timeout=self.timeout)
            else:
                res = requests.post(url, data=params, headers=headers, timeout=self.timeout)
            result = res.text
            curl_info = curlify.to_curl(res.request, compressed=True)
            _debugger.log({
                '访问gtcloud异常': curlify.to_curl(res.request, compressed=True),
                '返回的信息': res.text
            })
            is_success = 1
            return res.json()
        except Exception as e:
            headers = ''
            is_success = 0
            message = "访问gtcloud异常: {}".format(str(e))
            logger.error(message)
            raise UserError(message=message) from e
        finally:
            self.record_log_of_gtcloud(url, params, headers, start_time, result, curl_info, is_success)

    def get_user_info(self, ticket):
        """
        用ticket获取用户信息
        :param ticket:
        :param request:
        :return:
        """

        def _get_service():
            parsed_url = urlparse(self.request.url)
            query_params = parse_qs(parsed_url.query, keep_blank_values=True)
            query_params.pop("ticket", None)
            new_query_string = urlencode(query_params, doseq=True)
            service = urlunparse((
                "https",
                parsed_url.netloc,
                parsed_url.path,
                parsed_url.params,
                new_query_string,
                parsed_url.fragment
            ))
            return quote(service, safe='')

        sso_host = config.get("Domain.gtcloud_sso_host") or 'https://sso.gtcloud.cn'

        result = self._request(
            url=f"{urljoin(sso_host, '/cas/p3/serviceValidate')}?format=JSON&ticket={ticket}&service={_get_service()}"
        )

        # result = self._request(
        #     url=urljoin(sso_host, '/cas/p3/serviceValidate'),
        #     params={
        #         "ticket": ticket,
        #         "service": _get_service(),
        #         "format": "JSON"
        #     }
        # ) or {}
        if result.get("serviceResponse", {}).get('authenticationFailure'):
            raise UserError(message=f"获取用户信息失败:{result.get('serviceResponse', {}).get('authenticationFailure', {}).get('code')}")
        account = result.get("serviceResponse", {}).get("authenticationSuccess", {}).get("user")
        if not account:
            raise UserError(message=f"从{sso_host}未获取到用户")
        # 获取数见用户
        user = repository.get_data("user", {'account': account})
        if not user:
            raise UserError(message=f"用户{account}在数见不存在，请添加用户")
        return user

    def sso_login(self, ticket):
        """
        sso登录
        :return:
        """
        # 用ticket获取用户
        user = self.get_user_info(ticket)
        # 登录
        model = UserLoginModel(**user)
        model.request = self.request
        model.response = self.response
        token = generate_token_and_login(model, user)

        return token
