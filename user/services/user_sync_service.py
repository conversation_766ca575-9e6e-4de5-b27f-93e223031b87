import json
import string
from base.enums import DatasetBusinessType, UserChannel, AccountMode, UserSourceType
from components import common_service
from dataset.repositories import dataset_subject_repository
from base import repository
from base.dmp_constant import ERP_SYNC_FLOW_ID
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from flow.services import flow_service, user_sync_flow_service
from data_source.services import data_source_service
from user_group.services.user_source_service import save_api_type_user_source, clear_user_source_data, save_ad_type_user_source

__all__ = [
    'update_sync_setting',
    'old_erp_user_sync',
    'pre_import_check',
    'do_import',
]


def _get_user_source(user_source_id):
    user_source = repository.get_data('user_source', {'id': user_source_id}, ['id', 'type', 'name'])
    if not user_source:
        raise UserError(message='渠道不存在')
    return user_source

def _get_erp_user_source():
    user_source = repository.get_data('user_source', {'type': UserChannel.Erp.value}, ['id', 'type', 'name'])
    if not user_source:
        raise UserError(message='ERP渠道不存在')
    return user_source

def pre_import_check(user_source_id):
    user_source = _get_user_source(user_source_id)
    is_erp_user_source = user_source.get('type') == UserChannel.Erp.value
    sync_table = 'external_user' if is_erp_user_source else 'user_source_user'
    conditions = {} if is_erp_user_source else {'user_source_id': user_source_id}
    accounts = repository.get_data(sync_table, conditions, ['account'], multi_row=True)
    if not accounts:
        raise UserError(message='渠道暂无用户, 请等待数据集用户同步成功')
    accounts = [item.get('account') for item in accounts]

    query_sql = 'select account, name from user where account in %(accounts)s and (user_source_id is null or user_source_id!=%(user_source_id)s)'
    users = repository.get_data_by_sql(query_sql, {'accounts': accounts, 'user_source_id': user_source_id})
    return users


def do_import(user_source_id, import_org, import_user, import_role, auto_sync=0):
    if auto_sync:
        import_org = True
        import_user = True
        import_role = True
        # 查询是否有别的渠道绑定了自动同步
        data = repository.get_one('user_source', {'auto_sync': 1, 'id!=': user_source_id})
        if data:
            msg = f'对应渠道({data.get("name")})已经开启了用户自动同步，请关闭后在执行此操作'
            raise UserError(message=msg)
    user_source = _get_user_source(user_source_id)
    flow_id = user_sync_flow_service.run_import_flow(user_source, import_org, import_user, import_role)
    bind_user_source_and_import_user_flow(user_source_id, flow_id, auto_sync)
    return flow_id


def bind_user_source_and_import_user_flow(user_source_id, import_user_id, auto_sync=0):
    if auto_sync:
        data_bool = repository.data_is_exists(
            'dataset_depend', {'source_dataset_id': user_source_id, 'depend_id': import_user_id}
        )
        if not data_bool:
            repository.add_data("dataset_depend", {"source_dataset_id": user_source_id, "depend_id": import_user_id})
    else:
        repository.delete_data('dataset_depend', {"depend_id": import_user_id})
    repository.update_data('user_source', {'auto_sync': auto_sync}, {'id': user_source_id})


def old_erp_user_sync():
    """旧erp同步流程。"""
    project_config = repository.get_data(
        'project', {'code': getattr(g, 'code')}, ['account_mode', 'erp_data_source_code'], from_config_db=True
    )
    # 检查当前租户是否支持
    # 检查erp数据源是否存在
    if not project_config:
        raise UserError(code=404, message='无效的project')
    if project_config.get('account_mode') != AccountMode.ERP.value:
        raise UserError(message='当前租户不支持旧ERP同步模式')
    if not project_config.get('erp_data_source_code'):
        raise UserError(message="ERP数据源编码不能为空！")
    data_source = data_source_service.get_data_source("", data_source_code=project_config.get('erp_data_source_code'))
    if not data_source:
        raise UserError(message="{code}数据源不存在！".format(code=project_config.get('erp_data_source_code')))

    # 立即运行流程
    flow_service.run_flow(ERP_SYNC_FLOW_ID)
    flow_service.enable_flow(ERP_SYNC_FLOW_ID, queue_name=config.get('RabbitMQ.queue_name_flow', 'Flow'))


def update_sync_setting(data: dict):
    user_source_id = data.get('user_source_id')
    user_source = repository.get_one('user_source', {'id': user_source_id})
    if not user_source:
        raise UserError(message="渠道来源不存在，刷新页面再试")
    is_erp_user_source = user_source.get('type') == UserChannel.Erp.value
    source_type = data.get('source_type', UserSourceType.DATASET.value)
    if source_type == UserSourceType.API.value and is_erp_user_source:
        raise UserError(message="ERP渠道不支持API模式")

    # 判断同步类型方式是否变更
    old_user_source = json.loads(user_source.get("data")) if user_source.get("data") else {}
    if source_type != old_user_source.get('source_type', UserSourceType.DATASET.value):
        api_info = old_user_source.get('api_info', {})
        third_party_id = api_info.get('third_party_id', '') if api_info else ''
        clear_user_source_data(user_source_id, third_party_id)

    # 判断是否为api模式，为api模式则走api同步数据的相关逻辑
    if source_type == UserSourceType.API.value:
        return save_api_type_user_source(data, user_source)
    elif source_type == UserSourceType.AD.value:
        return save_ad_type_user_source(data, user_source)

    # 检查数据合法性
    _check_sync_data(data, is_erp_user_source)

    # 修改的时候需要记录对比下数据集是否跟之前的一致，跟之前不一致，需要清理依赖调度
    _deal_old_user_sync_depend_on(data)
    repository.update_data('user_source', {"data": json.dumps(data)}, {"id": user_source_id})

    # 修改这两个数据集业务类型
    user_dataset_id = data.get("user", {}).get("source_dataset_id")
    _update_dataset_business(user_dataset_id)
    user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id")
    _update_dataset_business(user_group_dataset_id)
    role_dataset_id = data.get("role", {}).get("source_dataset_id")
    role_user_dataset_id = data.get("role_user", {}).get("source_dataset_id")
    if data.get('sync_role') == 1:
        _update_dataset_business(role_dataset_id)
        _update_dataset_business(role_user_dataset_id)

    # 创建/更新依赖调度, 并执行调度
    user_sync_flow_service.create_or_update_flow(
        user_source, user_dataset_id, user_group_dataset_id, trigger_flow=True, role_dataset_id=role_dataset_id, role_user_dataset_id=role_user_dataset_id
    )


def _check_sync_data(data: dict, is_erp_user_source) -> dict:
    encrypt_type = data.get("encrypt")
    if not encrypt_type:
        raise UserError(message="加密方式必填")

    # 对前端传过来的data做简单校验和加工
    user_field = data.get("user", {}).get("field_relation")
    user_dataset_id = data.get("user", {}).get("source_dataset_id")
    if not user_field or not user_dataset_id:
        raise UserError(message='缺少用户表设置')

    # 必填项检查
    user_field_required = ["id", "name", "account"]
    for k in user_field_required:
        if not user_field.get(k):
            raise UserError(message='用户表中{}字段为必选项'.format(k))

    if not data.get("encrypt"):
        raise UserError(message='缺少用户加密方式')

    user_field_values = [x for x in user_field.values() if x and x != '']
    set_user_field_list = list(set(user_field_values))  # 去重
    if len(user_field_values) != len(set_user_field_list):
        raise UserError(message='用户属性配置字段配置不能重复')

    user_group_field = data.get("user_group", {}).get("field_relation")
    user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id")
    if is_erp_user_source:
        if not user_group_field or not user_group_dataset_id:
            raise UserError(message='缺少组织表设置')
        # 必填项检查(用户组)
        group_field_required = ["id", "name", "parent_id", "hierarchy"]
        for k in group_field_required:
            if not user_group_field.get(k):
                raise UserError(message='组织表中{}字段为必选项'.format(k))

    set_list = list(set(user_group_field.values()))  # 去重
    if len(user_group_field.values()) != len(set_list):
        raise UserError(message='组织属性配置字段配置不能重复')

    # 默认添加前端没有传的字段
    user_options = ["pwd", "mobile", "email", "group_id", "account_mode", "is_disabled"]
    user_options.append('group_id' if is_erp_user_source else 'user_source_group_id')
    for k in user_options:
        if k not in user_field.keys():
            user_field[k] = ''

    user_dataset_id = data.get("user", {}).get("source_dataset_id")
    user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id")
    if not repository.data_is_exists("dataset", {"id": user_dataset_id}):
        raise UserError(message='用户数据集不存在')
    if user_group_dataset_id and not repository.data_is_exists("dataset", {"id": user_group_dataset_id}):
        raise UserError(message='组织数据集不存在')

    # 检查数据集字段是否存在
    user_dataset_fields = repository.get_data(
        "dataset_field", {"dataset_id": user_dataset_id}, ["col_name"], multi_row=True
    )
    _check_dataset_setting(user_field, user_dataset_fields, "用户属性配置")

    user_group_dataset_fields = repository.get_data(
        "dataset_field", {"dataset_id": user_group_dataset_id}, ["col_name"], multi_row=True
    )
    _check_dataset_setting(user_group_field, user_group_dataset_fields, "组织属性配置")

    if data.get('sync_role') == 1:
        check_role_config(data)

    return data


def check_role_config(data: dict):
    role_dataset_id = data.get("role", {}).get("source_dataset_id")
    role_user_dataset_id = data.get("role_user", {}).get("source_dataset_id")
    if not repository.data_is_exists("dataset", {"id": role_dataset_id}):
        raise UserError(message='角色数据集不存在')
    if not repository.data_is_exists("dataset", {"id": role_user_dataset_id}):
        raise UserError(message='角色用户数据集不存在')
    role_field = data.get("role", {}).get("field_relation")
    role_user_field = data.get("role_user", {}).get("field_relation")
    # 检查数据集字段是否存在
    role_dataset_fields = repository.get_data(
        "dataset_field", {"dataset_id": role_dataset_id}, ["col_name"], multi_row=True
    )
    _check_dataset_setting(role_field, role_dataset_fields, "角色属性配置")

    role_user_dataset_fields = repository.get_data(
        "dataset_field", {"dataset_id": role_user_dataset_id}, ["col_name"], multi_row=True
    )
    _check_dataset_setting(role_user_field, role_user_dataset_fields, "角色用户属性配置")

    role_field_values = [x for x in role_field.values() if x and x != '']
    set_role_field_list = list(set(role_field_values))  # 去重
    if len(role_field_values) != len(set_role_field_list):
        raise UserError(message='角色属性配置字段不能重复')

    role_user_field_values = [x for x in role_user_field.values() if x and x != '']
    set_role_user_field_list = list(set(role_user_field_values))  # 去重
    if len(role_user_field_values) != len(set_role_user_field_list):
        raise UserError(message='角色与用户映射配置字段不能重复')


def _update_dataset_business(dataset_id):
    # 绝大部分的场景应该是不更新，所有先判断下是否存在
    if not repository.data_is_exists(
        "dataset", {"id": dataset_id, "business_type": DatasetBusinessType.UserSync.value}
    ):
        repository.update_data("dataset", {"business_type": DatasetBusinessType.UserSync.value}, {"id": dataset_id})


def _check_dataset_setting(setting_field: dict, dataset_fields: list, msg: string):
    for key, value in setting_field.items():
        if not value:
            continue
        is_exist = False
        for dataset_field in dataset_fields:
            if value == dataset_field.get("col_name"):
                is_exist = True
                break
        if not is_exist:
            raise UserError(message='{}数据集{}字段不存在'.format(msg, value))


def _deal_old_user_sync_depend_on(new_user_sync):
    user_source_id = new_user_sync.get('user_source_id')
    old_user_sync_data = repository.get_data("user_source", {"id": user_source_id}, fields=["data"])
    data = json.loads(old_user_sync_data.get("data")) if old_user_sync_data.get("data") else {}
    # 数据库中的历史同步用户表，用户组
    old_user_dataset_id = data.get("user", {}).get("source_dataset_id")
    old_user_group_dataset_id = data.get("user_group", {}).get("source_dataset_id")
    old_role_dataset_id = data.get("role", {}).get("source_dataset_id")
    old_role_user_dataset_id = data.get("role_user", {}).get("source_dataset_id")

    # 判断 old_user_dataset_id 和 old_user_group_dataset_id 是否为主题包中的数据集
    subject_user = dataset_subject_repository.is_subject_table(old_user_dataset_id)
    old_user_dataset_id = subject_user.get("id") if subject_user else old_user_dataset_id

    subject_user_group = dataset_subject_repository.is_subject_table(old_user_group_dataset_id)
    old_user_group_dataset_id = subject_user_group.get("id") if subject_user_group else old_user_group_dataset_id

    # 用户修改后的
    new_user_dataset_id = new_user_sync.get("user", {}).get("source_dataset_id")
    new_user_group_dataset_id = new_user_sync.get("user_group", {}).get("source_dataset_id")
    new_role_dataset_id = new_user_sync.get("role", {}).get("source_dataset_id")
    new_role_user_dataset_id = new_user_sync.get("role_user", {}).get("source_dataset_id")

    # 数据集有改动的话，清理依赖调度(清理用户数据集和组织数据集的依赖)
    if (
        old_user_dataset_id
        and old_user_group_dataset_id
        and (old_user_dataset_id != new_user_dataset_id or old_user_group_dataset_id != new_user_group_dataset_id)
    ):
        repository.delete_data(
            "dataset_depend", {"source_dataset_id": old_user_dataset_id, "depend_id": old_user_group_dataset_id}
        )
    # 数据集有改动的话，清理依赖调度(清理用户数据集和角色据集的依赖)
    if (
        old_user_dataset_id and
        old_user_dataset_id != new_user_dataset_id or old_role_dataset_id != new_role_dataset_id
    ):
        repository.delete_data(
            "dataset_depend", {"source_dataset_id": old_user_dataset_id, "depend_id": old_role_dataset_id}
        )
    # 数据集有改动的话，清理依赖调度(清理组织数据集和角色据集的依赖)
    if (
        old_user_group_dataset_id and
        old_user_group_dataset_id != new_user_group_dataset_id or old_role_dataset_id != new_role_dataset_id
    ):
        repository.delete_data(
            "dataset_depend", {"source_dataset_id": old_user_group_dataset_id, "depend_id": old_role_dataset_id}
        )
    # 数据集有改动的话，清理依赖调度(清理角色数据集和角色用户据集的依赖)
    if (
        old_role_dataset_id and
        old_role_dataset_id != new_role_dataset_id or old_role_user_dataset_id != new_role_user_dataset_id
    ):
        repository.delete_data(
            "dataset_depend", {"source_dataset_id": old_role_dataset_id, "depend_id": old_role_user_dataset_id}
        )
