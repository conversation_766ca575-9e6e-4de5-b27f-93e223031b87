#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/12/6.
"""
from dmplib.db.mysql_wrapper import get_db as _get_db


def get_email_template(template_type):
    """
    获取邮件模板
    :param template_type:
    :return:
    """
    with _get_db() as db:
        sql = 'SELECT `id`,`name` ,`type`,`subject`,`content`,`send_mode` ' 'FROM email_template ' 'WHERE `type`=%s '
        return db.query_one(sql, (template_type,))
