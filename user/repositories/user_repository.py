#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2017/3/26.
"""
import logging
import time

from copy import deepcopy

from base import repository
from base.dmp_constant import ERP_USER
from base.enums import AccountMode, UserChannel
from components.crypt import MysqlAESCrypt
from dmplib import config
from dmplib.constants import ADMIN_ROLE_ID, ADMINISTRATORS_GROUP_ID
from dmplib.hug import g
from dmplib.db.mysql_wrapper import get_db as get_master_db
from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id
from user.models import UserLoginModel

from typing import Dict, List

MYSQL_AES_KEY = config.get('Security.mysql_aes_key', 'qwertyuiasdfghjk')


def encrypt_field(field):
    if not field:
        return field
    aes = MysqlAESCrypt(MYSQL_AES_KEY)
    return aes.encrypt(field)


def decrypt_field(field):
    try:
        if not field:
            return field
        aes = MysqlAESCrypt(MYSQL_AES_KEY)
        return aes.decrypt(field).split(".")[0]
    except Exception:
        return None


need_encrypt_fields = ['mobile']


def get_data(*args, **kwargs):
    rv = repository.get_data('user', *args, **kwargs)
    if not rv:
        return rv
    for field in need_encrypt_fields:
        if isinstance(rv, dict) and rv.get(field):
            rv[field] = decrypt_field(rv[field])
        if isinstance(rv, list):
            for item in rv:
                if not item.get(field):
                    continue
                item[field] = decrypt_field(item[field])
    return rv

def update_data(ori_data, condition, commit=True):
    data = deepcopy(ori_data)
    for field in need_encrypt_fields:
        if data.get(field):
            data[field] = encrypt_field(data[field])
    return repository.update_data('user', data, condition, commit=commit)

def add_data(ori_data, commit=True):
    data = deepcopy(ori_data)
    for field in need_encrypt_fields:
        if data.get(field):
            data[field] = encrypt_field(data[field])
    return repository.add_data('user', data, commit=commit)

def update_model(model, condition, fields=None):
    data = model.get_dict(fields)
    return update_data(data, condition)


def get_cur_user_id():
    """
    获取当前用户id
    :return:
    """
    account = getattr(g, 'account')
    if not account:
        return None
    sql = 'SELECT id FROM `user` where account=%(account)s LIMIT 1 '
    with get_db() as db:
        return db.query_scalar(sql, {'account': account})


def get_user_info_for_cache(account):
    if not account:
        return None
    return get_db().query_one(
        'select id, account, group_id from `user` where account=%(account)s limit 1', {'account': account}
    )


def get_user_role_id(user_id: str) -> List[Dict[str, str]]:
    if not user_id:
        return None
    return get_db().query('select role_id from `user_user_role` where user_id=%(user_id)s', {'user_id': user_id})


def get_role_id_by_account(account):
    if not account:
        return None
    return get_db().query(
        'select user_user_role.role_id from user LEFT JOIN user_user_role on '
        'user.id = user_user_role.user_id WHERE user.account = %(account)s',
        {'account': account},
    )


def get_user_with_groups(user_id, fields):
    if not user_id:
        return None

    selects = '*'
    if fields and len(fields) > 0:
        selects = ','.join(fields)

    db = get_db()
    user_data = db.query_one('select ' + selects + ' from user where id=%(id)s', {'id': user_id})
    if not user_data:
        return None
    group_ids = db.query_columns('select group_id from user_group_user where user_id=%(user_id)s', {'user_id': user_id})
    user_data['group_ids'] = group_ids
    return user_data


def is_admin_by_user_id(user_id):
    if repository.data_is_exists("user_user_role", {"user_id": user_id, "role_id": ADMIN_ROLE_ID}):
        return True
    # 用户组判断
    group_ids = get_cur_user_group_ids()
    if not group_ids:
        return False
    role_ids = [row.get("role_id") for row in get_group_role(group_ids)]
    if ADMIN_ROLE_ID in role_ids:
        return True
    return False


def is_open_double_sql(code: str = None):
    res = repository.get_data(
        "dataset_edit_config",
        {'enable_mysql_tenant_list like': "%" + f"{code or g.code}" + "%"}, ['id'], from_config_db=True
    )
    return bool(res)


def get_cur_user_group_ids(account: None = None):
    """
    获取当前用户所在用户组id
    :return:
    """

    if not account and hasattr(g, 'account'):
        account = getattr(g, 'account')

    if not account:
        return []
    sql = (
        'SELECT `user_group_user`.group_id FROM `user_group_user` '
        'left join `user` on `user`.id = `user_group_user`.user_id '
        ' where `user`.account=%(account)s '
    )
    with get_db() as db:
        return db.query_columns(sql, {'account': account})


def get_role_by_account(account):
    sql = (
        "select user_user_role.role_id,user_role.name from user LEFT JOIN user_user_role "
        "on user.id = user_user_role.user_id LEFT JOIN user_role "
        "on user_user_role.role_id = user_role.id WHERE user.account = %(account)s ;"
    )
    with get_db() as db:
        return db.query(sql, {'account': account})


def get_group_role_by_account(account):
    sql = (
        "select user_group_role.role_id,user_role.name from user "
        "inner JOIN user_group_user on user.id = user_group_user.user_id "
        "inner JOIN user_group_role on user_group_user.group_id = user_group_role.group_id "
        "inner JOIN user_role on user_group_role.role_id = user_role.id WHERE user.account = %(account)s ;"
    )
    with get_db() as db:
        return db.query(sql, {'account': account})


def get_role_by_id(role_ids):
    sql = "select id, name from user_role where id in %(role_ids)s"
    with get_db() as db:
        return db.query(sql, {'role_ids': role_ids})


def get_group_role(group_ids: List[str]):
    """
    获取当前group_id用户组获取role_id
    :return:
    """
    sql = 'SELECT role_id FROM `user_group_role` where group_id in %(group_ids)s'
    with get_db() as db:
        return db.query(sql, {'group_ids': group_ids})


def deal_sorts(query_model):
    sorts = []
    if query_model.sorts:
        for sort in query_model.sorts:
            if sort.id == 'name':
                sorts.append('convert(u.`name` USING gbk) COLLATE gbk_chinese_ci ' + sort.method)
            if sort.id == 'account':
                sorts.append('u.account ' + sort.method)
            if sort.id == 'last_active_time':
                sorts.append('u.last_active_time ' + sort.method)
    return sorts


def _timestamp_to_time(timestamp):
    time_local = time.localtime(timestamp)
    return time.strftime("%Y-%m-%d %H:%M:%S", time_local)

def _mask_mobile(mobile):
    if not mobile:
        return ""
    if len(mobile) >= 7:
        return f'{mobile[:3]}****{mobile[7:]}'
    return '*' * len(mobile)

def _build_get_user_list_params_and_conditions(query_model):
    conditions = []
    params = {}
    if query_model.group_id is not None and query_model.group_id != '':
        condition = ' ugu.group_id=%(group_id)s '
        params['group_id'] = query_model.group_id
        conditions.append(condition)

    if query_model.nor_group_id is not None and query_model.nor_group_id != '':
        condition = ' ugu.group_id <> %(group_id)s '
        params['group_id'] = query_model.nor_group_id
        conditions.append(condition)

    if query_model.user_id:
        conditions.append(' u.`id`=%(user_id)s ')
        params['user_id'] = query_model.user_id

    if query_model.keyword:
        conditions.append(' ( u.`name` LIKE %(keyword)s OR account LIKE %(keyword)s OR email LIKE %(keyword)s) ')
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    if query_model.is_developer is not None and query_model.is_developer != '':
        conditions.append(' u.`is_developer`=%(is_developer)s ')
        params['is_developer'] = int(query_model.is_developer)
    return params, conditions


def get_user_list(query_model):
    """
    获取用户列表
    :param user.models.UserQueryModel query_model:
    """

    params, conditions = _build_get_user_list_params_and_conditions(query_model)
    sorts = deal_sorts(query_model)

    # sql = f'''
    # SELECT DISTINCT u.id, u.`name`, u.account,
    #     convert(aes_decrypt(unhex(u.mobile), '{MYSQL_AES_KEY}') using utf8) as mobile,
    #     u.email, u.add_mode,IF(u.last_login_time>0,from_unixtime(u.last_login_time),"") as last_login_time,
    #     u.last_login_ip, u.account_mode, u.is_disabled, u.created_on,u.last_active_time
    #     FROM `user` u
    #     left join user_group_user ugu on u.id = ugu.user_id
    #     left join `user_group` ug on ugu.group_id=ug.id
    # '''
    sql = f'''
        SELECT DISTINCT u.id, u.`name`, u.account, u.mobile, 
            u.email, u.add_mode,IF(u.last_login_time>0,from_unixtime(u.last_login_time),"") as last_login_time,
            u.last_login_ip, u.account_mode, u.is_disabled, u.is_developer, u.created_on,u.last_active_time
            FROM `user` u 
            left join user_group_user ugu on u.id = ugu.user_id 
            left join `user_group` ug on ugu.group_id=ug.id 
        '''

    sql_total = """
                select count(DISTINCT u.id) from `user` as u
                left join user_group_user ugu on u.id = ugu.user_id
                left join `user_group` ug on ugu.group_id=ug.id
                """

    if len(conditions) > 0:
        sql = ' '.join([sql, ' where ', ' and '.join(conditions)])
        sql_total = ' '.join([sql_total, ' where ', ' and '.join(conditions)])
    else:
        params = None
    sql += ' ORDER BY ' + (','.join(sorts) if sorts else 'u.`created_on` DESC')
    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)

    with get_db() as db:
        query_model.items = db.query(sql, params)

        if len(query_model.items) == 0:
            return query_model

        user_ids = [row['id'] for row in query_model.items]

        for i, item in enumerate(query_model.items):
            if item["last_active_time"]:
                item["last_active_time"] = _timestamp_to_time(item["last_active_time"])
            if not item['account_mode']:
                query_model.items[i]['account_mode'] = 'DMP'
            if item['mobile']:
                item['mobile'] = decrypt_field(item['mobile'])
                item['mobile'] = _mask_mobile(item['mobile'])

        # query groups
        sql_group = (
            'select ugu.group_id, ug.name as group_name, ugu.user_id from user_group_user ugu '
            ' inner join user_group ug on ugu.group_id=ug.id where ugu.user_id in %(user_ids)s'
        )
        groups = db.query(sql_group, {'user_ids': user_ids})
        user_groups_map = {}
        for group_item in groups:
            usr_id = group_item['user_id']
            if usr_id not in user_groups_map:
                user_groups_map[usr_id] = []
            user_groups_map[usr_id].append({'group_id': group_item['group_id'], 'group_name': group_item['group_name']})

        # query roles
        sql_role = (
            'select a.role_id, b.name as role_name, a.user_id from user_user_role a '
            ' inner join user_role b on a.role_id=b.id where user_id in %(user_ids)s'
        )
        roles = db.query(sql_role, {'user_ids': user_ids})
        user_roles_map = {}
        for role_item in roles:
            usr_id = role_item['user_id']
            if usr_id not in user_roles_map:
                user_roles_map[usr_id] = []
            user_roles_map[usr_id].append({'role_id': role_item['role_id'], 'role_name': role_item['role_name']})

        # 加入用户组下的角色
        sql_group_role = """
        select ur.id as role_id, ur.name as role_name, ugu.user_id from user_group_user ugu
        inner join user_group ug on ugu.group_id=ug.id
        INNER JOIN user_group_role ugr on ugr.group_id = ug.id
        INNER JOIN user_role ur on ur.id = ugr.role_id
        where ugu.user_id in %(user_ids)s
        """
        group_roles = db.query(sql_group_role, {'user_ids': user_ids})
        group_roles_map = {}
        for group_role in group_roles:
            usr_id = group_role.get('user_id')
            if usr_id not in group_roles_map:
                group_roles_map[usr_id] = []
            group_roles_map[usr_id].append({'role_id': group_role['role_id'], 'role_name': group_role['role_name']})

        # query organization
        sql_organization = (
            'select user_id, org_name, org_level from user_organization  ' 'where user_id in %(user_ids)s'
        )
        organizations = db.query(sql_organization, {'user_ids': user_ids})
        user_organizations_map = {}
        for organization_item in organizations:
            usr_id = organization_item['user_id']
            if usr_id not in user_organizations_map:
                user_organizations_map[usr_id] = []
            user_organizations_map[usr_id].append(
                {'org_name': organization_item['org_name'], 'org_level': organization_item['org_level']}
            )

        for user_row in query_model.items:
            role_data = user_roles_map.get(user_row['id'])
            group_role_data = group_roles_map.get(user_row['id'])
            group_data = user_groups_map.get(user_row['id'])
            organization_data = user_organizations_map.get(user_row['id'])
            user_row['roles'] = get_user_roles(role_data, group_role_data)
            user_row['groups'] = group_data or []
            user_row['organizations'] = organization_data or []

        query_model.total = db.query_scalar(sql_total, params)
    return query_model


def get_role_by_user_id(user_id):
    sql_group_role = """SELECT user_role.name, user_role.id, user_role.description FROM user_role
          INNER JOIN user_group_role on user_group_role.role_id = user_role.id
          INNER JOIN user_group_user ON user_group_role.group_id = user_group_user.group_id
        WHERE user_group_user.user_id = %(user_id)s;"""
    sql_user_role = """SELECT user_role.name, user_role.id, user_role.description FROM user_role
          INNER JOIN user_user_role on user_user_role.role_id = user_role.id
        WHERE user_user_role.user_id = %(user_id)s;"""
    with get_db() as db:
        groups_role = db.query(sql_group_role, {'user_id': user_id})
        user_role = db.query(sql_user_role, {'user_id': user_id})
        return list(set([row.get("name") for row in groups_role] + [row.get("name") for row in user_role]))


def get_role_by_user_id_v2(user_id):
    sql_group_role = """SELECT user_role.name, user_role.id, user_role.description, user_role.account_mode FROM user_role
          INNER JOIN user_group_role on user_group_role.role_id = user_role.id
          INNER JOIN user_group_user ON user_group_role.group_id = user_group_user.group_id
        WHERE user_group_user.user_id = %(user_id)s;"""
    sql_user_role = """SELECT user_role.name, user_role.id, user_role.description, user_role.account_mode FROM user_role
          INNER JOIN user_user_role on user_user_role.role_id = user_role.id
        WHERE user_user_role.user_id = %(user_id)s;"""
    with get_db() as db:
        groups_role = db.query(sql_group_role, {'user_id': user_id})
        user_role = db.query(sql_user_role, {'user_id': user_id})
        return [*groups_role, *user_role]


def get_user_roles(role_data, group_role_data):
    """
    获取用户的角色，以及用户所属用户组的角色
    :param role_data:
    :param group_role_data:
    :return:
    """
    if not role_data and not group_role_data:
        return []
    role_map = {}
    if group_role_data:
        for group_role in group_role_data:
            role_map[group_role.get("role_id")] = group_role
            role_map[group_role.get("role_id")]["is_group"] = True
    # 角色区分 是用户配置的还是用户组
    if role_data:
        for role in role_data:
            role_map[role.get("role_id")] = role
            role_map[role.get("role_id")]["is_group"] = False
    return list(role_map.values())


def get_user_list_exclusion_org(query_model):
    """
    获取用户列表（排除user_organization表关联的用户）
    :param query_model:
    :return:
    """
    sql = f'''
    select DISTINCT user.id, user.`name`, user.account, user.mobile,
        user.email, user.account_mode, user.add_mode 
        from user left join user_organization ON user_organization.user_id = user.id
        where user_organization.user_id is null
    '''


    conditions = []
    params = {}
    if query_model.keyword:
        conditions.append(' ( user.`name` LIKE %(keyword)s ) ')
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sql_total = (
        'select count(user.id) from user '
        'left join user_organization ON user_organization.user_id = user.id '
        'where user_organization.user_id is null '
    )

    if len(conditions) > 0:
        sql = ' '.join([sql, 'and', ' and '.join(conditions)])
        sql_total = ' '.join([sql_total, 'and', ' and '.join(conditions)])
    else:
        params = None

    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)

    with get_db() as db:
        query_model.items = db.query(sql, params)
        for item in query_model.items:
            item["mobile"] = decrypt_field(item.get("mobile"))
        query_model.total = db.query_scalar(sql_total, params)
    return query_model


def get_erp_user_list(query_model):
    """
    获取ERP用户列表
    :param user.models.UserQueryModel query_model:
    :return user.models.UserQueryModel:
    """
    conditions = []
    params = {}

    if query_model.keyword:
        conditions.append(' ( u.`name` LIKE %(keyword)s OR account LIKE %(keyword)s OR email LIKE %(keyword)s) ')
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sql = (
            'SELECT DISTINCT u.id, u.`name`, u.account, u.mobile, u.email, u.pwd as password, u.account_mode FROM `'
            + ERP_USER
            + '` u '
    )
    sql_total = 'SELECT count(u.id) FROM `' + ERP_USER + '` u '
    if len(conditions) > 0:
        sql = ' '.join([sql, ' where ', ' and '.join(conditions)])
        sql_total = ' '.join([sql_total, 'where', ' and '.join(conditions)])
    else:
        params = None

    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
    with get_db() as db:
        query_model.items = db.query(sql, params)
        query_model.total = db.query_scalar(sql_total, params)
    return query_model


def get_user_project_profile():
    sql = (
        'SELECT `id`, `logo_uri`, `small_logo`, `portal_logo`, `small_portal_logo`, `site_title`,'
        ' `site_icon`, `code`, `title`, `description`, `type`, `is_domain_account`, `storage_type`, '
        '`dmp_env_sign`, `dataset_permission_model`, `is_rdc_auth`, `auth_mode`, `is_expire`, '
        ' account_mode, erp_data_source_code, allow_dashboard_type,show_print,is_key_screen,'
        '`project_enabled`,`project_disable_reason` FROM project WHERE `code`=%(code)s'
    )
    with get_master_db() as db:
        return db.query_one(sql, {'code': getattr(g, 'code')})


def get_user_opt_info(user_id: str) -> Dict[str, str]:
    """
    获取用户附加信息
    :param tenant_code:
    :return:
    """

    if not user_id:
        return False

    sql = (
        "select EU.id as user_id,EU.`name` as user_name, GROUP_CONCAT(G.name) as user_org from external_user  EU "
        "left join external_user_group G ON EU.group_id=G.id   where EU.id=%(user_id)s"
        " group by EU.id, user_name"
    )

    user = get_db().query_one(sql, {'user_id': user_id})

    # 若在erp组织结构里没查询到，就直接从user_group里查询
    if not user or not user.get('user_org'):
        sql = (
            "select U.id as user_id,U.`name` as user_name , GROUP_CONCAT(UG.name)  as user_org from  user U LEFT join user_group_user UGU "
            " ON U.id=UGU.user_id left join user_group UG ON UG.id=UGU.group_id where U.id=%(user_id)s"
            " group by U.id, user_name"
        )
        user = get_db().query_one(sql, {'user_id': user_id})

    return user


def tenant_code_is_exists(tenant_code: str):
    """
    企业代码是否存在
    :param tenant_code:
    :return:
    """
    with get_master_db() as db:
        return db.query_scalar('SELECT id FROM project WHERE `code`=%(code)s LIMIT 1 ', {'code': tenant_code})


def get_login_user(model: UserLoginModel):
    """
    获取用户
    :param user.models.UserLoginModel model:
    :return:
    """
    sql = (
        'SELECT `id`, `name`, `account`, `email`, `pwd`, `old_pwd`, `group_id`, `account_mode`,`user_source_id`,`last_password_change` FROM `user` '
        'WHERE `account`=%(account)s and `is_developer`=0 LIMIT 1 '
    )
    with get_db(model.tenant_code) as db:
        return db.query_one(sql, {'account': model.account})


def get_login_user_group_ids(model: UserLoginModel):
    """
    获取登录用户所在用户组ids
    :return:
    """
    if not model:
        return []
    sql = (
        'SELECT `user_group_user`.group_id FROM `user_group_user` '
        'left join `user` on `user`.id = `user_group_user`.user_id '
        ' where `user`.account=%(account)s '
    )
    with get_db(model.tenant_code) as db:
        return db.query_columns(sql, {'account': model.account})


def update_login_user_pwd(model, new_pwd):
    """
    更新登录密码
    :param user.models.UserLoginModel model:
    :param str new_pwd:
    :return:
    """
    sql = 'UPDATE `user` SET `pwd`=%(new_pwd)s ,old_pwd=0 WHERE `account`=%(account)s AND old_pwd=1'
    with get_db(model.tenant_code) as db:
        return db.exec_sql(sql, {'account': model.account, 'new_pwd': new_pwd})


def update_last_login_info(tenant_code: str, user_id: str, last_login_time: int, last_login_ip: str):
    """
    更新登录密码
    :param str tenant_code
    :param str user_id
    :param str last_login_time
    :param str last_login_ip
    :return:
    """
    sql = 'UPDATE `user` SET `last_login_time`=%(last_login_time)s ,last_login_ip=%(last_login_ip)s WHERE `id`=%(user_id)s'
    with get_db(tenant_code) as db:
        return db.exec_sql(
            sql, {'user_id': user_id, 'last_login_ip': last_login_ip, 'last_login_time': last_login_time}
        )


def update_users_group(user_ids, group_id):
    if len(user_ids) == 0:
        return
    if not group_id:
        raise ValueError('group_id is null')
    with get_db() as db:
        for user_id in user_ids:
            if not repository.data_is_exists('user_group_user', {'user_id': user_id, 'group_id': group_id}):
                db.insert('user_group_user', {'user_id': user_id, 'group_id': group_id, 'id': seq_id()}, commit=False)
        db.commit()


def check_has_func(role_ids, func_code, func_action_code):
    """
    检测某组角色是否拥有某个功能权限
    :param role_ids:
    :param func_code:
    :param func_action_code:
    :return:
    """
    if not role_ids:
        return False
    # 转换结构
    role_ids = [role.get('role_id') for role in role_ids]
    role_ids = [role_ids[0]]
    if len(role_ids) > 1:
        sql = (
            'select 1 from user_role_func WHERE func_code=%(func_code)s and func_action_code=%(func_action_code)s '
            'and role_id in {role_ids}'.format(role_ids=tuple(role_ids))
        )
    else:
        sql = (
            'select 1 from user_role_func WHERE func_code=%(func_code)s and func_action_code=%(func_action_code)s '
            'and role_id=%(role_id)s'
        )

    db = get_db()
    return db.exec_sql(sql, {'role_id': role_ids[0], 'func_code': func_code, 'func_action_code': func_action_code})


def add_multi_external_user(user_source, user_models, all_user_models):
    """
    批量添加外部用户组
    :param user_models:
    :param all_user_models:
    :return:
    """
    db = get_db()
    try:
        insert_user_models = []
        insert_user_fields = ['id', 'name', 'account', 'pwd', 'mobile', 'email', 'group_id', 'account_mode',
                              'user_source_id']
        update_user_fields = ['name', 'account', 'mobile', 'email', 'group_id', 'account_mode']
        # 用户直接写入dmp user表
        for user_model in user_models:
            exist_user = get_data({'account': user_model.account})
            if exist_user:
                if not exist_user.get('user_source_id') \
                        or exist_user.get('user_source_id') == user_source.get('id'):  # 同一个渠道用户进行修改
                    user_data = user_model.get_dict(update_user_fields)
                    update_data(user_data, condition={'account': user_model.account}, commit=False)
            else:
                user_data = user_model.get_dict(insert_user_fields)
                user_data['old_pwd'] = 0
                user_data['user_source_id'] = user_source.get('id')
                add_data(user_data, commit=False)
                insert_user_models.append(user_model)
        # 将用户写入外部user表，方便以后使用。
        is_erp_user_source = True if user_source.get('type') == UserChannel.Erp.value else False
        if is_erp_user_source:
            for all_user_model in all_user_models:
                db.delete(
                    "external_user",
                    {
                        'account': all_user_model.account,
                        'group_id': all_user_model.group_id,
                        'account_mode': AccountMode.IMPORT.value,
                    },
                    commit=False,
                )
                all_user_data = all_user_model.get_dict(insert_user_fields)
                db.insert("external_user", all_user_data, commit=False)
        else:
            insert_user_fields.remove('group_id')
            insert_user_fields.append('user_source_group_id')
            insert_user_fields.append('user_id')
            for all_user_model in all_user_models:
                db.delete(
                    "user_source_user",
                    {
                        'user_source_id': user_source.get('id'),
                        'account': all_user_model.account,
                        'user_source_group_id': all_user_model.group_id,
                        'account_mode': AccountMode.IMPORT.value,
                    },
                    commit=False,
                )
                all_user_data = all_user_model.get_dict(insert_user_fields)
                all_user_data['user_id'] = all_user_model.id
                all_user_data['id'] = seq_id()
                all_user_data['user_source_id'] = user_source.get('id')
                all_user_data['user_source_group_id'] = all_user_model.group_id
                db.insert("user_source_user", all_user_data, commit=False)
        db.commit()
    except Exception as e:
        db.rollback()
        msg = "批量添加外部用户错误：" + str(e)
        logging.error(msg, exc_info=True)

    return insert_user_models


def delete_user_relate(user_id, account):
    """
    删除用户关联表
    :param user_id:
    :param account:
    :return:
    """
    with get_db() as db:
        db.delete("user", {"id": user_id}, commit=False)
        db.delete("user_user_role", {"user_id": user_id}, commit=False)
        db.delete("user_group_user", {"user_id": user_id}, commit=False)
        db.delete("user_organization", {"user_id": user_id}, commit=False)
        db.commit()
    with get_master_db() as m_db:
        m_db.delete('developer_auth_user', {'code': g.code, 'account': account})
        developer_account = str(account).replace('developer_', '')
        m_db.delete('developer_login_log', {'tenant_code': g.code, 'developer_account': developer_account})


def get_user_info(account):
    """
    获取用户信息
    :param account:
    :return:
    """
    sql = '''SELECT `id`,`name`,`account`,`pwd`,`old_pwd`, `group_id`, account_mode FROM `user` WHERE
          `account`=%(account)s LIMIT 1'''

    with get_db() as db:
        return db.query_one(sql, {'account': account})


def get_accounts_by_user_ids(user_ids):
    """
    根据user_ids获取accounts
    :param user_ids:
    :return:
    """
    sql = """ select id, account, name from `user` where `id` in %(user_ids)s""".format(user_ids=user_ids)

    with get_db() as db:
        return db.query(sql, {'user_ids': user_ids})


def get_user_groups_by_user_id(user_id):
    sql = '''
    SELECT ug.id, ug.name, ug.parent_id, ug.code
    FROM user_group_user AS ugu
    INNER JOIN user_group AS ug ON ugu.group_id = ug.id
    WHERE ugu.user_id = %(user_id)s
    '''
    params = {'user_id': user_id}
    with get_db() as db:
        return db.query(sql, params)


def get_user_groups_by_codes(codes):
    sql = '''SELECT id, name, parent_id, code FROM user_group WHERE code in %(codes)s'''
    params = {'codes': codes}
    with get_db() as db:
        return db.query(sql, params)


def get_user_by_account_or_user_id(user_id=None, account=None):
    if not user_id and not account:
        return {}

    where = 'id = %(user_id)s' if user_id else 'account = %(account)s'
    sql = f'''SELECT id, `name`, account, mobile, 
            email, last_login_time,last_active_time,account_mode 
              from user where {where}'''
    params = {'account': account, 'user_id': user_id}
    with get_db() as db:
        user = db.query_one(sql, params)
        if user:
            user["mobile"] = decrypt_field(user.get("mobile"))
        return user


def get_role_list():
    return repository.get_list("user_role", {})


# 用户同步相关
# 获取用户同步增量记录
def get_sync_user_list(last_user_id, last_sync_time, page_size):
    sql = f'''select id as user_guid,`name`as user_name,account as user_code, mobile as tel,
             email,is_disabled,modified_on from `user` '''
    params = {"page_size": page_size}
    # 非第一次同步
    if last_user_id is not None and last_sync_time is not None:
        sql += ''' where  (modified_on = %(last_sync_time)s and id >%(last_user_id)s ) or modified_on > %(last_sync_time)s  '''
        params['last_user_id'] = last_user_id
        params['last_sync_time'] = last_sync_time
    sql += '''order by modified_on asc ,id asc limit %(page_size)s'''
    with get_db() as db:
        user_list = db.query(sql, params)
        if user_list:
            for user in user_list:
                user["mobile"] = decrypt_field(user.get("mobile"))
        return user_list


# 获取需要删除的用户同步记录
def get_sync_delete_user_list(page_size):
    #
    sql = '''select id from user_sync_record 
    where id not in (select id from `user`) 
    order by modified_on asc ,id asc limit %(page_size)s '''
    with get_db() as db:
        return db.query(sql, {"page_size": page_size})


# 标记需要删除的用户同步记录
def set_user_sync_record_deleted_tag(user_ids, tag):
    sql = ''' update user_sync_record set delete_tag=%(tag)s  where id in  %(user_ids)s'''
    with get_db() as db:
        return db.exec_sql(sql, {"tag": tag, "user_ids": user_ids})


# 删除用户同步记录
def delete_user_sync_record(delete_tag):
    if delete_tag != "" and delete_tag is not None:
        delete_sql = '''delete from user_sync_record where delete_tag=%(delete_tag)s and delete_tag is not null'''
        with get_db() as db:
            db.exec_sql(delete_sql, {"delete_tag": delete_tag})


# 添加同步用户信息
def add_user_sync_record(user_ids: list):
    if user_ids:
        sql_exist = '''select id from user_sync_record where id in %(user_ids)s'''
        with get_db() as db:
            user_sync_record = []
            exist_user_sync_ids = db.query(sql_exist, {'user_ids': user_ids})
            exist_user_ids = [r['id'] for r in exist_user_sync_ids]
            for user_id in user_ids:
                if user_id not in exist_user_ids:
                    user_sync_record.append({"id": user_id})
            db.insert_multi_data('user_sync_record', user_sync_record, ['id'])


# 组织同步相关
# 获取组织同步增量记录
def get_sync_dept_list(last_dept_id, last_sync_time, page_size):
    sql = f"select id as dept_guid,`name` as dept_name," \
          f"case when id='********-0000-0000-1111-********0000' then null " \
          f"when id='********-0000-0000-1000-********0000' then '********-0000-0000-1111-********0000' " \
          f"else  parent_id end as parent_guid," \
          f"`code` as sort, null as is_company,null as is_end_company, modified_on  from user_group "
    params = {"page_size": page_size}
    # 非第一次同步
    if last_dept_id is not None and last_sync_time is not None:
        sql += ''' where  (modified_on = %(last_sync_time)s and id >%(last_dept_id)s ) or modified_on > %(last_sync_time)s  '''
        params['last_sync_time'] = last_sync_time
        params['last_dept_id'] = last_dept_id
    sql += '''order by modified_on asc ,id asc limit %(page_size)s'''
    with get_db() as db:
        return db.query(sql, params)


# 获取需要删除的组织同步记录
def get_sync_delete_dept_list(page_size):
    sql = '''select id from user_group_sync_record 
    where id not in (select id from `user_group`) 
    order by modified_on asc ,id asc   limit  %(page_size)s  '''
    with get_db() as db:
        return db.query(sql, {"page_size": page_size})


# 标记需要删除的组织同步记录
def set_dept_sync_record_deleted_tag(dept_ids, tag):
    sql = ''' update user_group_sync_record set delete_tag=%(tag)s where id in  %(dept_ids)s'''
    with get_db() as db:
        return db.exec_sql(sql, {"tag": tag, "dept_ids": dept_ids})


# 删除组织同步记录
def delete_dept_sync_record(delete_tag):
    if delete_tag != "" and delete_tag is not None:
        delete_sql = '''delete from user_group_sync_record where delete_tag=%(delete_tag)s and delete_tag is not null'''
        with get_db() as db:
            db.exec_sql(delete_sql, {"delete_tag": delete_tag})


# 添加组织同步记录
def add_dept_sync_record(dept_ids: list):
    if dept_ids:
        sql_exist = '''select id from user_group_sync_record where id in %(dept_ids)s'''
        with get_db() as db:
            dept_sync_record = []
            exist_dept_sync_ids = db.query(sql_exist, {'dept_ids': dept_ids})
            exist_dept_ids = [r['id'] for r in exist_dept_sync_ids]
            for dept_id in dept_ids:
                if dept_id not in exist_dept_ids:
                    dept_sync_record.append({"id": dept_id})
            db.insert_multi_data('user_group_sync_record', dept_sync_record, ['id'])


# 获取用户所属组织
def get_user_dept_by_user_ids(user_ids: list):
    sql_dept = '''select user_id,group_id from user_group_user where user_id in %(user_ids)s'''
    with get_db() as db:
        groups = db.query(sql_dept, {'user_ids': user_ids})
    user_dept_map = {}
    for group_item in groups:
        usr_id = group_item['user_id']
        if usr_id not in user_dept_map:
            user_dept_map[usr_id] = []
        user_dept_map[usr_id].append({'group_id': group_item['group_id']})
    return user_dept_map


def get_top_dept():
    sql = r"select id as dept_guid,`name` as dept_name,parent_id as parent_guid,`code` as sort, " \
          r" null as is_company,null as is_end_company, modified_on  from user_group" \
          r" where parent_id is null or parent_id='' or parent_id='********-0000-0000-0000-********0000' " \
          r" order by `code` limit 1"
    with get_db() as db:
        return db.query_one(sql)

def get_user_by_account(account, code):
    """
    获取用户
    :param code:
    :param account:
    :return:
    """
    sql = (
        'SELECT `id`, `name`, `account`, `pwd`, `old_pwd`, `group_id`, `account_mode` FROM `user` '
        'WHERE `account`=%(account)s LIMIT 1 '
    )
    with get_db(code) as db:
        return db.query_one(sql, {'account': account})


def update_user(code, account, passwd):
    with get_db(code) as db:
        data = {'pwd': passwd}
        conditons = {'account': account}
        return db.update('user', data, conditons)


def check_if_reset_passwd(code, account):
    sql = 'SELECT `account`, `token` FROM `user_reset_passwd` WHERE `account`=%(account)s AND `code`=%(code)s'
    with get_master_db() as db:
        return db.query_one(sql, {'account': account, 'code': code})


def delete_user_record(code, account):
    with get_master_db() as db:
        return db.delete('user_reset_passwd', {'code': code, 'account': account})


def add_user_reset_password(data: dict):
    """
    新增一条新用户记录，标记用户未重置过密码
    :param data:
    :return:
    """
    with get_master_db() as db:
        return db.insert('user_reset_passwd', data)


def get_external_secret(code):
    sql = 'SELECT `external_secret_key` FROM `project` where `code`=%(tenant_code)s'
    with get_master_db() as db:
        params = {"tenant_code": code}
        return db.query_one(sql, params=params)


def get_source_user_list(query_model, user_source, user_source_id, user_source_group_id):
    """
    按条件获取渠道用户，分页查询
    :param user.models.UserQueryModel query_model:
    :return user.models.UserQueryModel:
    """
    query_conditions = []
    params = {}
    is_erp_user_source = True if user_source.get('type') == UserChannel.Erp.value else False
    table_name = ERP_USER if is_erp_user_source else 'user_source_user'
    sql_order = ' order by max(u.group_id),max(u.name) '
    sql_group_by = ' group by u.id '
    if not is_erp_user_source:
        sql_order = ' order by u.user_source_group_id,u.name '
        sql_group_by = ' group by u.user_id '
        query_conditions.append(" u.user_source_id = %(user_source_id)s ")
        params['user_source_id'] = user_source_id
    if user_source_group_id:
        if is_erp_user_source:
            query_conditions.append(" u.group_id = %(user_source_group_id)s ")
        else:
            query_conditions.append(" u.user_source_group_id = %(user_source_group_id)s ")
        params['user_source_group_id'] = user_source_group_id

    if query_model.keyword:
        query_conditions.append(' ( u.`name` LIKE %(keyword)s OR account LIKE %(keyword)s OR email LIKE %(keyword)s) ')
        params['keyword'] = '%' + query_model.keyword_escape + '%'

    sql = f"SELECT max(u.id) id, max(u.`name`) as `name`, max(u.account) as `account`, max(u.mobile) as `mobile`, max(u.email) as `email`, max(u.pwd) as `pwd`, max(u.account_mode) as `account_mode`, '{user_source_id}' as user_source_id FROM `{table_name}` u"
    sql_total = 'SELECT count(u.id) FROM `' + table_name + '` u '
    if len(query_conditions) > 0:
        sql = ' '.join([sql, ' where ', ' and '.join(query_conditions)])
        sql_total = ' '.join([sql_total, 'where', ' and '.join(query_conditions)])
    else:
        params = None
    sql += sql_group_by + sql_order
    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
    with get_db() as db:
        data = db.query(sql, params)
        query_model.items = format_source_user_list(data)
        query_model.total = db.query_scalar(sql_total, params)
    return query_model


def format_source_user_list(data):
    """
    数据格式化
    1、用户密码 pwd 置空，不返回前端，解决安全问题
    :param data:
    :return:
    """
    for item in data:
        if item.get("account_mode") == AccountMode.SYNC.value and item.get("pwd"):
            item["pwd"] = ""
    return data


def add_rdc_auth_user(rdc_auth_user):
    with get_master_db() as db:
        db.delete('rdc_auth_user', {'code': rdc_auth_user.get('code'), 'account': rdc_auth_user.get('account')})
        db.insert('rdc_auth_user', rdc_auth_user)


def check_is_show_filling_front(user_id):
    from user_group.services.user_group_service import get_all_parent_group_by_group_ids

    # 是否是审核人
    is_reviewer = repository.get_data_scalar("filling_reviewer", {"user_id": user_id}, col_name="id")
    if is_reviewer:
        return True
    # 是否是填报人
    role_ids = repository.get_column("user_user_role", {"user_id": user_id}, fields=['role_id']) or []
    # 获取用户组织关联的角色
    group_ids = repository.get_column("user_group_user", {'user_id': user_id}, fields=['group_id']) or []
    if group_ids:
        group_ids = get_all_parent_group_by_group_ids(deepcopy(group_ids)) + group_ids
        group_role_ids = repository.get_column("user_group_role", {"group_id": group_ids}, fields=['role_id']) or []
        role_ids.extend(group_role_ids)
    if role_ids:
        user = repository.get_data("filling_user", {"role_id": role_ids}, fields=["id"])
        return bool(user)
    return False


def check_is_show_by_code(user_id, func_code):
    from user_group.services.user_group_service import get_all_parent_group_by_group_ids
    # 查询对应租户中是否有开启自助报表的增值功能
    data = repository.get_column('project_value_added_func', {'project_code': g.code, 'func_code': func_code}, 'id', from_config_db=True)
    if not data:
        return False
    # 获取用户角色
    role_ids = repository.get_column("user_user_role", {"user_id": user_id}, fields=['role_id']) or []
    # 获取第三方自定义角色
    third_role_ids = g.customize_roles if hasattr(g, "customize_roles") else []
    if isinstance(third_role_ids, list) and third_role_ids:
        role_ids = role_ids + third_role_ids
    # 获取用户组织关联的角色
    group_ids = repository.get_column("user_group_user", {'user_id': user_id}, fields=['group_id']) or []
    if group_ids:
        group_ids = get_all_parent_group_by_group_ids(deepcopy(group_ids)) + group_ids
        group_role_ids = repository.get_column("user_group_role", {"group_id": group_ids}, fields=['role_id']) or []
        role_ids.extend(group_role_ids)
    if role_ids and ADMIN_ROLE_ID in role_ids:
        return True
    if role_ids:
        data = repository.get_one('user_role_func', {'func_code': func_code, 'func_action_code': 'view', 'role_id': role_ids})
        return True if data else False
    return False



if __name__ == "__main__":
    print(encrypt_field("15827158090"))
    print(decrypt_field("B62EB8AA44C6F0278E8359FA1D28FCDE"))
