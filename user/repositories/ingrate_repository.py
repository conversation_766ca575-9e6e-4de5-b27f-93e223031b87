#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :ingrate_repository.py
# @Time      :2022/5/16 15:45
# <AUTHOR>
import loguru

from base import repository
from dmplib.utils.errors import UserError


def get_dashboard_share_type(report_id):
    """
    获取报告分享类型
    """
    type_access_released = repository.get_data_scalar(
        "dashboard", conditions={"id": report_id}, col_name="type_access_released"
    )
    if type_access_released is None:
        raise UserError(code=5002, message="报告不存在，请联系管理员")
    return type_access_released


def get_portal_share_type(report_id):
    """
    获取门户分享类型
    """
    type_access_released = repository.get_data_scalar(
        "application", conditions={"id": report_id}, col_name="type_access_released"
    )
    if type_access_released is None:
        raise UserError(code=5003, message="门户不存在，请联系管理员")
    return type_access_released


def get_dashboard_terminal_type(report_id):
    """
    获取报告terminal_type
    """
    terminal_type = repository.get_data_scalar(
        "dashboard", conditions={"id": report_id}, col_name="terminal_type"
    )
    if terminal_type is None:
        raise UserError(message="报告不存在，请联系管理员")
    return terminal_type


SELF_SERVICE_MAKER_ROLE = '39fa0260-1267-137a-eb71-1d09ee826daf'
SELF_SERVICE_MAKER_ROLE_NAME = '自助报表制作者'


def get_self_service_maker_role():
    """
    获取自助报表制作者角色
    :return:
    """
    role_ids = repository.get_columns('user_role', {'id': SELF_SERVICE_MAKER_ROLE}, col_name='id')
    if not role_ids:
        role_ids = repository.get_columns('user_role', {'name': SELF_SERVICE_MAKER_ROLE_NAME}, col_name='id')
    if not role_ids:
        raise UserError(message="当前租户没有【自助报表制作者】角色，请添加")
    return role_ids


def get_release_type_of_ar(report_id):
    """
    获取统计报表发布类型
    :param report_id:
    :return:
    """
    try:
        release_type = repository.get_data_scalar(
            'dashboard', conditions={'id': report_id}, col_name='type_access_released'
        )
        if release_type is None:
            release_type = repository.get_data_scalar(
                'myrptdetail', conditions={'MyRptDetailId': report_id}, col_name='ReleaseType'
            )
    except Exception as e:
        loguru.logger.error(f"获取统计报表类型失败：{e}")
        release_type = None
    return release_type


if __name__ == "__main__":
    run_code = 0
