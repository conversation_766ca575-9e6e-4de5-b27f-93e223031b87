#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    function service
    <NAME_EMAIL> on 2017/6/29.
"""
import re
import hug
from operator import itemgetter
import os
from loguru import logger

from dmplib.hug import g

from app_menu.models import FunctionModel, FunctionIconModel
from app_menu.repositories import function_repository
from app_menu.repositories.application_repository import get_application_by_func_id
from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from level_sequence.models import FunctionLevelSequenceModel
from level_sequence.services import level_sequence_service
from app_menu.repositories import application_guide_img_repository, application_guide_user_log_repository, function_collection_repository
from base.enums import FunctionIconType, DashboardPlatforms, ApplicationType, DashboardStatus, TokenAuthFrom, \
    ThirdFuncFilterType, ApplicationTypeAccessReleased
from dashboard_chart.repositories import external_dashboard_repository
from app_menu.services import application_auth_service, external_service
from dmplib import config
from components.mip_auth_application import ApplicationDashboardOperation, ApplicationOperation
from components.versioned_query import shield_versioned

from typing import Dict, List, Optional, Union

from rbac.repositories.func_repository import get_func_table
from dmplib.hug import debugger

_debugger = debugger.Debug(__name__)


def get_function(function_id, is_release=False):
    """
    获取菜单
    :param str function_id:
    :param bool is_release:
    :return:
    """
    if not function_id:
        raise UserError(message='缺少功能菜单id')
    fields = ['id', 'name', 'parent_id', 'level_code', 'icon', 'url', 'target', 'application_id', 'icon_url',
              'report_type', 'description']
    func_table = 'release_function' if is_release else '`function`'
    func = repository.get_data(func_table, {'id': function_id}, fields)
    if not func:
        raise UserError(message='功能菜单不存在')
    func['guide_img'] = application_guide_img_repository.get_img_list_by_function_id(function_id)
    # 自定义图标数组
    func["custom_icons"] = get_custom_icon_list()
    func_model = FunctionModel(**func)
    url_info = ''
    if func.get('url') and not str(func.get('url', '')).startswith('http'):
        if func.get('report_type') not in [0, 2, 6]:
            with shield_versioned():
                dashboard = repository.get_data('dashboard', {'id': func.get('url')})
        else:
            dashboard = repository.get_data('dashboard', {'id': func.get('url')})
        if dashboard:
            url_info = dashboard
    setattr(func_model, 'url_info', url_info)
    return func_model


def get_function_list(application_id, is_release=False):
    """
    获取功能菜单列表
    :param str application_id:
    :param bool is_release:
    :return list:
    """
    if not application_id:
        raise UserError(message='缺少应用id')
    return function_repository.get_function_list(application_id, is_release)


def get_all_function(user_id: None = None, is_release: bool = False) -> List[Dict[str, Union[str, None]]]:
    """
    获取全部功能菜单
    :return:
    """
    func_table = 'release_function' if is_release else '`function`'
    fields = ['id', 'name', 'parent_id', 'level_code', 'icon', 'url', 'target', 'application_id']
    order_by = [('application_id', 'ASC'), ('level_code', 'ASC')]
    func_from_tenant = []
    if user_id:
        role_ids = repository.get_data('user_user_role', {'user_id': user_id}, fields=['role_id'], multi_row=True) or ''
        # 检测是否有app的view权限
        from user.repositories import user_repository

        if user_repository.check_has_func(role_ids, "app-site", "view"):
            func_from_tenant = repository.get_data(func_table, {}, fields, True, order_by) or []
    else:
        func_from_tenant = repository.get_data(func_table, {}, fields, True, order_by) or []
    fields.append('func_code')
    func_from_config = repository.get_data(get_func_table('function'), {}, fields, True, order_by,
                                           from_config_db=True) or []
    funcs = func_from_tenant + func_from_config
    funcs = sorted(funcs, key=itemgetter('level_code'), reverse=False)
    return funcs


def generate_func_tree(func_list):
    """
    生成功能菜单树
    :param list func_list:
    :return list:
    """
    if not func_list:
        return None
    result = []
    tmp_dict = {}
    for func in func_list:
        if not isinstance(func, dict):
            continue
        func['sub'] = []
        # 功能菜单level_code格式: 0001-、0001-0001-
        func_code = func.get('level_code')
        parent_organ_code = func_code[0: len(func_code) - 5]
        if parent_organ_code not in tmp_dict:
            result.append(func)
            tmp_dict[func_code] = func
        else:
            tmp_dict.get(parent_organ_code)['sub'].append(func)
            tmp_dict[func_code] = func
    return result


def generate_func_tree_v2(
        func_list: Optional[Union[List[Dict[str, str]], List[Union[Dict[str, str], Dict[str, Union[str, None]]]]]]
) -> Union[
    List[Dict[str, str]],
    List[
        Union[
            Dict[str, str],
            Dict[str, Union[str, List[Union[Dict[str, str], Dict[str, Union[str, None]]]]]],
            Dict[str, Union[str, List[Dict[str, str]]]],
            Dict[str, Union[str, None, List[Dict[str, Union[str, None]]]]],
        ]
    ],
    List[Dict[str, Union[str, List[Dict[str, str]]]]],
    List[Union[Dict[str, Union[str, List[Dict[str, str]]]], Dict[str, str]]],
]:
    if not func_list:
        return []
    func_list_dict = {item['level_code']: item for item in func_list}  # 去重
    roots = [item for item in func_list_dict.values() if len(item['level_code']) == 5]
    for root in roots:
        __append_child(root, func_list_dict.values())
    return sorted(roots, key=itemgetter('level_code'), reverse=False)


def __append_child(current_func, func_list):
    # 向下递归查找所有菜单
    for func in func_list:
        level_code = func.get('level_code')
        if level_code[0: len(level_code) - 5] == current_func.get('level_code'):
            if current_func.get('sub'):
                current_func['sub'].append(func)
            else:
                current_func['sub'] = [func]
            __append_child(func, func_list)


def fill_up_func_is_dashboard_exist(func_list):
    """
    判断菜单访问的目标报告是否存在
    :param list func_list:
    :return:
    """
    if not func_list:
        return func_list
    func_urls = [i.get('url') for i in func_list if i.get('url') and not str(i.get('url')).startswith('http')
                 and not i.get("report_type")]
    exsit_dashboard_ids = []
    if func_urls:
        query_data = external_dashboard_repository.get_dashboards_by_dashboard_ids(list(set(func_urls)))
        exsit_dashboard_ids = [i.get('id') for i in query_data if i.get('id')] if query_data else []
    if exsit_dashboard_ids:
        for func in func_list:
            func['is_dashboard_exist'] = 1 if func.get('url') in exsit_dashboard_ids else 0


def _judge_auth_way():
    auth_from = getattr(g, 'auth_from', '')
    if auth_from == TokenAuthFrom.MipAuth.value:
        # 基础数据平台授权门户
        return ThirdFuncFilterType.MipAuthWay.value
    else:
        # 多云门户集成门户
        return ThirdFuncFilterType.MultiCloudWay.value


def get_function_tree(application_id, auth_filter_flag=False, auth_way='', is_release=False):
    """
    获取功能菜单树
    :param str application_id:
    :param bool auth_filter_flag: 是否需要根据权限过滤报告
    :param str auth_way: 指定菜单权限过滤方式
    :param bool is_release: 是否运行时
    :return:
    """
    func_list = get_function_list(application_id, is_release=is_release)
    if hasattr(g, 'snap_id'):
        # 拍照场景下需要把不支持拍照的报告重原始表中获取
        get_dashboard_application_type_for_snap(func_list)
    if auth_filter_flag:
        # 如果没有指定就从登录信息中获取
        third_func_way = _judge_auth_way() if not auth_way else auth_way
        if third_func_way == ThirdFuncFilterType.MipAuthWay.value:
            func_list = application_auth_service.filter_funcs_by_mip_auth(func_list)
        else:
            func_list = application_auth_service.filter_funcs_by_auth(func_list)
    if not func_list:
        return None
    # 菜单访问的目标报告是否存在，前端未使用。不用查询
    # fill_up_func_is_dashboard_exist(func_list)
    # 用户收藏的门户菜单
    user_func_collection = function_collection_repository.get_func_collection_by_application_id(application_id, g.account)
    img_list = application_guide_img_repository.get_img_list_by_application_id_for_function(application_id)
    id2function = {}
    for func in func_list:
        id2function[func['id']] = func
        func['guide_img'] = []
        func['is_collect'] = func.get('id') in user_func_collection
    if img_list:
        for img in img_list:
            function_id = img['function_id']
            if not function_id:
                continue
            func = id2function.get(function_id)
            if not func:
                continue
            func['guide_img'].append(img['url'])
    return generate_func_tree(func_list)


def get_dashboard_application_type_for_snap(func_list: list):
    if not func_list:
        return func_list
    dashboard_ids = [func.get('url') for func in func_list if func.get('report_type') not in [0, 2, 6] and not func.get('url').startswith('http')]
    if dashboard_ids:
        with shield_versioned():
            dashboard_info = repository.get_list('dashboard', {'id': list(set(dashboard_ids))}, ['id', 'application_type', 'background'])
            dashboard_info = {dashboard.get('id'): dashboard for dashboard in dashboard_info}
            for func in func_list:
                dashboard_id = func.get('url')
                if dashboard_info.get(dashboard_id):
                    func['dashboard_background'] = dashboard_info.get(dashboard_id, {}).get('background')
                    func['application_type'] = dashboard_info.get(dashboard_id, {}).get('application_type')
    return func_list


def _generate_level_code(model):
    """

    :param app_menu.models.FunctionModel model:
    :return:
    """
    return level_sequence_service.generate_level_code(
        FunctionLevelSequenceModel(level_id=model.parent_id, attach_identify=model.application_id)
    )


def add_function(model):
    """
    添加功能菜单
    :param app_menu.models.FunctionModel model:
    :param model:
    :return:
    """
    if not model.id:
        model.id = seq_id()
    model.validate()
    if model.parent_id:
        parent_function = get_function(model.parent_id)
        if parent_function.url:
            raise UserError(message='上级功能菜单已配置链接，不允许添加下级')
    model.level_code = _generate_level_code(model)
    fields = ['id', 'name', 'parent_id', 'level_code', 'icon', 'url', 'target', 'application_id', 'icon_url',
              'report_type', 'description']
    result = repository.add_model('`function`', model, fields)
    application = repository.get_one('application', {'id': model.application_id}, ['type_access_released', 'enable'])
    if application.get('enable') == 1:
        repository.update('application', {'enable': 2}, {'id': model.application_id})
    return result


def update_function(model):
    """
    修改功能菜单
    :param app_menu.models.FunctionModel model:
    :return:
    """
    model.validate()
    if not repository.data_is_exists('function', {'id': model.id}):
        raise UserError(message='功能菜单不存在')
    if repository.data_is_exists('function', {'parent_id': model.id}) and model.url:
        raise UserError(message='已存在子级功能，不允许设置当前功能链接')
    fields = ['name', 'icon', 'url', 'target', 'icon_url', 'report_type', 'description']
    application_guide_img_repository.update_img_list(model.application_id, model.id, model.guide_img)
    result = repository.update_model('`function`', model, {'id': model.id}, fields)
    # 获取门户的相关状态，发布状态的第三方门户需要同步到基础数据平台
    application = repository.get_one('application', {'id': model.application_id}, ['type_access_released', 'enable'])
    if application.get('enable') == 1:
        repository.update('application', {'enable': 2}, {'id': model.application_id})
    return result


def delete_function(func_model: FunctionModel):
    """
    删除功能菜单
    :param FunctionModel func_model:
    :return:
    """
    if not func_model.id:
        raise UserError(message='功能菜单不存在，删除错误')
    # 获取目标菜单的所有子菜单id
    function_id_list = function_repository.get_function_by_level_code(func_model.application_id, func_model.level_code)
    application_guide_img_repository.delete_by_function_id(function_id_list)
    application_guide_user_log_repository.delete_log_by_function_id(function_id_list)
    function_collection_repository.delete_collection_by_function_id(function_id_list)
    result = function_repository.delete_function_by_id_list(function_id_list)
    application = repository.get_one('application', {'id': func_model.application_id}, ['type_access_released', 'enable'])
    if application.get('enable') == 1:
        repository.update('application', {'enable': 2}, {'id': func_model.application_id})
    return result


def delete_function_by_application_id(application_id):
    """
    删除应用下所有功能菜单
    :param str application_id:
    :return:
    """
    return function_repository.delete_function_by_application_id(application_id)


def update_function_rank(source_function_id, target_function_id):
    """
    修改菜单 排序
    :param source_function_id:
    :param target_function_id:
    :return:
    """
    source_function = get_function(source_function_id)
    target_function = get_function(target_function_id) if target_function_id else None
    if target_function:
        if source_function.parent_id != target_function.parent_id:
            raise UserError(message='只支持同级排序')
        elif source_function.application_id != target_function.application_id:
            raise UserError(message='不允许夸应用排序')
    source_level_code = source_function.level_code
    cur_level_code = target_function.level_code if target_function else _generate_level_code(source_function)
    if source_level_code < cur_level_code:
        rank = int(cur_level_code[-5:-1]) - 1
        cur_level_code = cur_level_code[0:-5] + '%04d-' % (rank if rank else 1,)
    return function_repository.update_function_level_code(
        source_function.application_id, source_function.level_code, cur_level_code
    )


def _build_report_auth(report_id, permission_data):
    """
    权限，示例dda3e8da-f69b-4445-925d-a576e234717e,1111 ，格式为{report_id},{permission}
    var readRight = right.ActionList.Exists(a => a.ActionCode == "00") ? "1" : "0";
    var viewRight = "1";
    var printRirht = right.ActionList.Exists(a => a.ActionCode == "01") ? "1" : "0";
    var exportRight = right.ActionList.Exists(a => a.ActionCode == "02") ? "1" : "0";
    dto.RptRights = $"{readRight}{viewRight}{printRirht}{exportRight}";
    """
    permission = permission_data.get(report_id, '')
    read = 1 if 'view' in permission else 0
    print = 1 if 'print' in permission else 0
    download = 1 if 'download' in permission else 0
    return f'{report_id},{read}1{print}{download}'


def _build_default_report_auth(report_id):
    # 放开全部权限
    return f'{report_id},1111'


def build_redirect_extra(func_id, real_report_id):
    # 这里需要使用菜单的id取基础数据平台取回权限数据
    from app_menu.services.application_service import is_mip_third_way_portal

    application = get_application_by_func_id(func_id)
    if is_mip_third_way_portal(application.get('type_access_released')):
        # 1. 配置了基础数据平台
        permission_data = ApplicationDashboardOperation().get_reports_permission_data(report_ids=[func_id]) or {}
        if permission_data.get(func_id):
            # 转换成报告的权限数据
            permission_data[real_report_id] = permission_data.pop(func_id, '')
        permission_str = _build_report_auth(real_report_id, permission_data)
        extra = {'permission': permission_str}
        logger.info(f'构建的基础数据平台的授权数据： {permission_data}, permission_str: {permission_str}')
        _debugger.log(f'构建的基础数据平台的授权数据： {permission_data}, permission_str: {permission_str}')
        # 处理没有userid
        if not getattr(g, 'userid', ''):
            from user.repositories import user_repository
            account_info = user_repository.get_user_info(getattr(g, 'account', '')) or {}
            if account_info:
                g.userid = account_info.get('id', '')
            else:
                # 必须要一个用户id, 这里指定一个固定的id
                g.userid = '********-1111-2222-3333-************'
    else:
        # 2. 没有配置基础数据平台
        permission_str = _build_default_report_auth(real_report_id)
        extra = {'permission': permission_str}
        logger.info(f'使用默认的授权数据, permission_str: {permission_str}')
        _debugger.log(f'使用默认的授权数据, permission_str: {permission_str}')
    return extra


def build_final_url(report_type, func_id, real_report_id):
    """
    report_type: 外部报表类型
    func_id: 菜单id
    real_report_id: 真正的外部报表id
    """
    from ppt.services.ppt_service import build_ppt_redirect_url

    if report_type == 'active_report':
        extra = build_redirect_extra(func_id, real_report_id)
        url = build_ppt_redirect_url(
            "/login_by_jwt", from_type='active_report', params={'id': real_report_id}, backend=False, extra=extra
        )
        logger.info(f"统计报告跳转URL：{url}")
        return url

    elif report_type == 'erp_report':
        extra = build_redirect_extra(func_id, real_report_id)
        set_erp_account_user_id()
        url = build_ppt_redirect_url(
            "/sso/dmp", from_type='erp_report', params={'id': real_report_id}, backend=False, extra=extra
        )
        logger.info(f"erp报告跳转URL：{url}")
        return url

    else:
        raise UserError(message=f'暂时未支持的跳转报告类型：{report_type}')


def set_erp_account_user_id():
    # 取erp的用户id
    from feed.repositories.dashboard_feeds_repository import get_erp_user_by_account
    account = getattr(g, 'account', '')
    accounts_tmp = get_erp_user_by_account([account])
    logger.info(f'获取的erp用户信息: {accounts_tmp}')
    _debugger.log(f'获取的erp用户信息: {accounts_tmp}')
    if accounts_tmp:
        account_info = accounts_tmp[0]
        user_id = account_info.get('user_id', '')
        name = account_info.get('name', '')
        if user_id:
            g.userid = user_id
            g.name = name


def function_link_to(request, response, function_id, is_release=False):
    """
    外链跳转
    :param request:
    :param function_id:
    :param bool is_release:
    :return:
    """
    func = get_function(function_id, is_release=is_release)
    url_pattern = r'^(?:http|ftp)s?://'
    # 门户挂载报告的类型
    report_type = func.report_type
    # HD报告处理成外链
    if func.url and not re.match(url_pattern, func.url, re.IGNORECASE):
        # HighData、老移动报告 类型的报告
        if report_type == 3:
            url = build_final_url(report_type='active_report', func_id=func.id, real_report_id=func.url)
        elif report_type == 5:
            # 跳转到erp报表
            # {报表前台地址}/sso/dmp?token={token}&redirect_uri=/report/{report_id}/preview
            # 从MIP获取erp实际权限，透传权限
            url = build_final_url(report_type='erp_report', func_id=func.id, real_report_id=func.url)
        elif report_type in [1, 2]:
            # 获取门户的类型
            application_platform = external_service.get_application_platform(func.application_id, is_release=is_release)
            application_type = 'dashboard' if application_platform == DashboardPlatforms.PC.value else 'report'
            # 获取跳转HighData的报告地址
            from integrate import external_service as integrate_external_service
            # HighData报告查
            if report_type == 1:
                url = integrate_external_service.go_hd_frontend_report_url(func.url, application_type)
            else:
                # 老移动报表查看
                old_report_url = 'home/release/index?reportId=' + func.url
                url = integrate_external_service.go_hd_frontend_assign_url(old_report_url)

            logger.info(f"HighData报告URL：{url}")
        elif report_type == 4:
            # 报表中心
            dashboard_info = getattr(func, 'url_info')
            dashboard_id = func.url
            if not dashboard_info:
                logger.error(f"报表中心报表不存在，id：{dashboard_id}")
                return
            # 报告未发布的则提示
            if dashboard_info.get("status") == DashboardStatus.Drafted.value:
                logger.error(f"报告未发布不能查看")
                raise UserError(message="报告未发布不能查看")
            application_type = dashboard_info.get("application_type")
            # 简单报表
            if application_type == ApplicationType.SelfService.value:
                project_code = g.code
                intelligent_url = f'/intelligent-report/share/{dashboard_id}?code={project_code}'
                url = config.get("Domain.dmp") + intelligent_url
            else:
                # 复杂报表
                url = build_final_url(report_type='active_report', func_id=func.id, real_report_id=func.url)
            logger.error(f"报表中心(application_type：{application_type})报告URL：{url}")
        else:
            from dashboard_chart import external_service as dashboard_chart_external_service
            url = dashboard_chart_external_service.get_url_for_external_report_redirect(func.url)

        func.url = url if url else func.url

    if not func.url or not re.match(url_pattern, func.url, re.IGNORECASE):
        response.set_header('Content-Type', 'text/html; charset=utf-8')
        content = ''
        file_path = os.path.join(os.path.dirname(__file__), 'invalid_link_error.html')
        with open(file_path, encoding='utf-8') as f:
            content = f.read()
        response.body = content
        return
    # 历史场景 兼容
    if not report_type:
        func.url += ('?' if '?' not in func.url else '&') + 'token=' + request.cookies.get('token')
    return hug.redirect.to(func.url)


def has_function_by_application(application_id):
    """
    应用是否存在菜单
    :param str application_id:
    :return:
    """
    return repository.data_is_exists('function', {'application_id': application_id})


def add_custom_icon(icon_model: FunctionIconModel):
    """
    添加自定义图标
    :param icon_model:
    :return:
    """
    icon_model.id = seq_id()
    # 默认新增图标都是自定义图标类型
    icon_model.icon_type = FunctionIconType.Custom.value

    # 校验参数
    icon_model.validate()

    repository.add_model("function_icon", icon_model, ["id", "name", "description", "icon_url", "icon_type"])
    return icon_model.id


def get_custom_icon_list():
    """
    获取自定义图标列表
    :return:
    """
    return repository.get_list(
        "function_icon",
        {"icon_type": FunctionIconType.Custom.value, "status": 0},
        ["id", "name", "description", "icon_url", "icon_type"],
    )
