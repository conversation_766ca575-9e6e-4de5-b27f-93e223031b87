#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401

"""

    <NAME_EMAIL> on 2017/6/28.
"""
import json
import re
import hug
import jwt
from jwt import DecodeError
import logging

from app_menu.models import ApplicationModel, FunctionModel, ApplicationConfigModel
from app_menu.repositories import application_repository, function_repository
from app_menu.services import function_service, application_auth_service
from app_menu.repositories import application_guide_img_repository, application_guide_user_log_repository, function_collection_repository
from base import repository, service
from base.enums import DashboardPlatforms, SkylineApps, AddFuncType, TaskDataType
from components.app_hosts import AppHosts
from components.mip_auth_application import ApplicationOperation
from dmplib.hug import debugger
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from rbac.services.data_permissions import data_permission_edit_filter, data_permission_filter, filter_application_sub

from app_menu.models import ApplicationQueryModel
from typing import Dict, List, Union, Optional
from urllib.parse import urlparse, urljoin, urlencode, urlunparse
from dmplib import config
from dmplib.hug import g
from base.enums import ApplicationTypeAccessReleased, TokenAuthFrom, ApplicationTypeAccessReleasedSourceStr
from dmplib.saas.project import get_db
from user_group.services.user_group_service import check_mip_app_allow_exists

_debugger = debugger.Debug(__name__)
logger = logging.getLogger(__file__)


@data_permission_filter('application-view')
def get_application_list(query_model: ApplicationQueryModel) -> Dict[str, Union[int, List[Dict[str, Union[int, str]]]]]:
    """
    获取应用列表
    :param app_menu.models.ApplicationQueryModel query_model:
    :return:
    """
    return application_repository.get_application_list(query_model).get_result_dict()


def get_application_by_id_list(id_list):
    """
    获取应用
    :param list id_list:
    :return:
    """
    if not id_list:
        return None
    return application_repository.get_application_by_id_list(id_list)


def get_application_info(application_id):
    """
    获取门户信息
    :param application_id:
    :return:
    """
    fields = [
        'id',
        'name',
        'platform',
        'description',
        'icon',
        'url',
        'target',
        'is_buildin',
        'is_system',
        'rank',
        'enable',
        'nav_type',
        'type_access_released',
        'distribute_type',
        'relation_id'
    ]
    return repository.get_data('application', {'id': application_id}, fields)


@data_permission_edit_filter('application-view')
def get_application(application_id, is_release=False):
    """
    获取应用
    :param str application_id:
    :param bool is_release:
    :return:
    """
    return get_application_without_permission(application_id, is_release)


def get_application_without_permission(application_id, is_release=False):
    """
    获取应用
    :param str application_id:
    :param bool is_release:
    :return:
    """
    if not application_id:
        raise UserError(message='缺少应用id')
    fields = [
        'id',
        'name',
        'platform',
        'description',
        'icon',
        'url',
        'target',
        'is_buildin',
        'rank',
        'enable',
        'collapse',
        'nav_type',
        'use_guide',
        'theme',
        'menu_display_type',
        'type_access_released',
        'distribute_type',
        'user_defined_style',
        'is_show_banner',
        'banner_url',
        'bottom_color',
        'relation_id',
        'is_cache',
        'report_type',
        'common_config',
        'enable_snapshot',
        'enable_filter',
        'filter_config',
        'created_by',
        'modified_on'
    ]
    table = 'release_application' if is_release else 'application'
    data = repository.get_data(table, {'id': application_id}, fields)
    if not data:
        return None
    model = ApplicationModel(**data) if data else None
    return model


def get_application_and_sub(application_id, list_func, is_release=False):
    """
    获取应用及其子菜单
    :param application_id:
    :param list_func:
    :param bool is_release:
    :return:
    """
    app_data = get_application(application_id, is_release=is_release)
    if not app_data:
        raise UserError(message='抱歉,您访问的应用不存在!')
    app_data.function = []

    if list_func:
        app_data.function = filter_application_sub(function_service.get_function_tree(application_id, is_release=is_release))
    app_data.guide_img = application_guide_img_repository.get_img_list_by_application_and_function_id(
        application_id, None
    )
    set_app_url_info(app_data)
    return app_data


def get_application_and_sub_without_permission(application_id, list_func, is_release=False):
    """
    获取应用及其子菜单
    :param application_id:
    :param list_func:
    :param bool is_release:
    :return:
    """
    app_data = get_application_without_permission(application_id, is_release=is_release)
    if not app_data:
        raise UserError(message='抱歉,您访问的应用不存在!')
    app_data.function = []

    if list_func:
        app_data.function = function_service.get_function_tree(application_id, auth_filter_flag=True, is_release=is_release)
    app_data.guide_img = application_guide_img_repository.get_img_list_by_application_and_function_id(
        application_id, None
    )
    set_app_url_info(app_data)
    return app_data


def set_app_url_info(app_data: ApplicationModel):
    url_info = ''
    if app_data.url and not app_data.url.startswith('http'):
        dashboard = repository.get_data('dashboard', {'id': app_data.url})
        if dashboard:
            url_info = dashboard
    setattr(app_data, 'url_info', url_info)

    # 设置前端门户来源标识source_from
    # 添加app来源
    if app_data.type_access_released == ApplicationTypeAccessReleased.ThirdParty.value:
        if is_mip_third_way_portal(app_data.type_access_released):
            # 基础数据第三方门户
            app_data.source_from = ApplicationTypeAccessReleasedSourceStr.MipAuth.value
        else:
            # 三云第三方门户
            app_data.source_from = ApplicationTypeAccessReleasedSourceStr.ThirdParty.value
    elif app_data.type_access_released == ApplicationTypeAccessReleased.UserRole.value:
        app_data.source_from = ApplicationTypeAccessReleasedSourceStr.UserRole.value
    else:
        app_data.source_from = ''


# 门户第三方是否是基础数据平台
def is_mip_third_way_portal(type_access_released):
    if type_access_released == ApplicationTypeAccessReleased.ThirdParty.value:
        auth_from = getattr(g, 'auth_from', '') or ''
        if auth_from == TokenAuthFrom.MipAuth.value:
            return True
        return False
    else:
        return False


@data_permission_edit_filter('application-view')
def check_can_view(application_id):
    """
    获取应用
    :param str application_id:
    :return:
    """
    return application_id


@data_permission_edit_filter('application-edit')
def check_can_edit(application_id):
    """
    获取应用
    :param str application_id:
    :return:
    """
    return application_id


def get_all_application(platform: Optional[str] = None, is_release: bool = False) -> List[Dict[str, Union[int, str]]]:
    """
    获取所有应用
    :platform str pc|mobile|tv
    :return list:
    """
    app_table = 'release_application' if is_release else 'application'
    fields = [
        'id', 'name', 'platform', 'description', 'icon', 'url', 'target', 'is_buildin', 'rank', 'enable',
        'collapse', 'nav_type', 'use_guide', 'theme', 'menu_display_type', 'type_access_released',
        'distribute_type', 'user_defined_style', 'is_show_banner', 'banner_url', 'bottom_color',
        'relation_id', 'is_cache', 'report_type', 'common_config', 'enable_snapshot', 'enable_filter',
        'filter_config', 'modified_on', 'created_by'
    ]
    order_by = [('rank', 'ASC'), ('created_on', 'ASC')]

    condition = {}
    if platform in ['pc', 'tv', 'mobile', 'mobile_screen']:
        condition = {"platform": platform}

    return repository.get_data(app_table, condition, fields, True, order_by)


def add_application(model, app_id=None):
    """
    添加应用
    :param app_menu.models.ApplicationModel model:
    :param app_id: 指定应用id
    :return:
    """
    model.id = seq_id()
    if app_id:
        model.id = app_id
    model.rank = repository.get_data_max_rank('application', 'rank')
    model.validate()
    fields = [
        'id',
        'name',
        'platform',
        'description',
        'icon',
        'url',
        'target',
        'rank',
        'nav_type',
        'theme',
        'menu_display_type',
        'user_defined_style',
        'is_show_banner',
        'banner_url',
        'bottom_color',
        'is_cache',
        'common_config',
        'enable',
        'enable_snapshot',
        'enable_filter',
        'filter_config'
    ]
    return repository.add_model('application', model, fields)


@data_permission_edit_filter('application-edit')
def update_application(model):
    """
    修改应用
    :param app_menu.models.ApplicationModel model:
    :return:
    """
    model.validate()
    application_model = get_application(model.id)
    if not application_model:
        raise UserError(message='应用不存在')
    if model.url and function_service.has_function_by_application(model.id):
        raise UserError(message='应用存在子菜单')
    if application_model.enable is not None and application_model.enable != 0:
        model.enable = 2
    fields = [
        'name',
        'description',
        'icon',
        'url',
        'target',
        'use_guide',
        'theme',
        'menu_display_type',
        'user_defined_style',
        'collapse',
        'is_show_banner',
        'banner_url',
        'bottom_color',
        'is_cache',
        'common_config',
        'enable',
        'enable_snapshot',
        'enable_filter',
        'filter_config',
        'report_type',
    ]
    application_guide_img_repository.update_img_list(model.id, None, model.guide_img)
    return repository.update_model('application', model, {'id': model.id}, fields)


def relation_application(app_id, relation_app_id=""):
    """
    门户关联的添加，修改，删除处理
    :param app_id:
    :param relation_app_id: 目标门户id为空，则是取消关联
    :return:
    """
    cur_app_model = get_application(app_id)
    if not cur_app_model:
        raise UserError(message='门户不存在')

    # 存在被关联门户
    if relation_app_id:
        relation_app = get_application_without_permission(relation_app_id)
        if not relation_app:
            raise UserError(message='被关联的门户不存在')
        # 检查被关联门户是否已被关联
        application_info = application_repository.get_application_by_relation(relation_app_id)
        if application_info and application_info.get("id") != app_id:
            raise UserError(message=f'选中的【{relation_app.name}】门户已经被【{application_info.get("name")}】门户关联了')
        # 现在门户平台类型不考虑tv，mobile，如果存在这样数据，则不开放关联入口
        if cur_app_model.platform == relation_app.platform:
            raise UserError(message='当前门户与被关联的门户平台类型不能一样')
    else:
        if not cur_app_model.relation_id:
            raise UserError(message='门户不存在关联，不需要取消关联')

    return application_repository.update_application_relation(cur_app_model, relation_app_id)


def clear_application_relation(app_id):
    """
    清除指定门户的关联信息
    :param app_id:
    :return:
    """
    if not app_id:
        return False
    return application_repository.clear_application_relation(app_id)


def update_application_rank(model):
    """
    更新应用顺序
    :param app_menu.models.ApplicationRankModel model:
    :return:
    """
    model.validate()
    rows = service.update_data_rank(model)
    # 将顺序同步到运行时
    ApplicationOperation(application_id='').upload_application_rank_to_mip()
    service.sync_rank_to_release()
    return rows


@data_permission_edit_filter('application-edit')
def enable_application(application_id, type_access_released):
    """
    启用应用
    :param str application_id:
    :param int type_access_released:
    :return:
    """
    if type_access_released not in [e.value for e in ApplicationTypeAccessReleased.__members__.values()]:
        raise UserError("参数`type_access_released`不合法")
    return repository.update_data(
        'application', {'enable': 1, "type_access_released": type_access_released}, {'id': application_id}
    )


def release_application(application_id, type_access_released):
    """
    发布门户
    :param str application_id:
    :param int type_access_released:
    :return:
    """
    if type_access_released not in [e.value for e in ApplicationTypeAccessReleased.__members__.values()]:
        raise UserError(message="参数`type_access_released`不合法")
    application = repository.get_one('application', {'id': application_id})
    if not application:
        raise UserError(message='门户不存在')
    application['enable'] = 1
    application['type_access_released'] = type_access_released
    # 获取门户下的所有菜单信息
    func_list = repository.get_list('function', {'application_id': application_id}) or []
    parent_func_list = list(set([func.get('parent_id') for func in func_list if func.get('parent_id')] or []))
    func_fields = func_list[0].keys() if func_list else []
    with get_db() as db:
        try:
            repository.update_data(
                'application', {'enable': 1, "type_access_released": type_access_released},
                {'id': application_id}, commit=False
            )
            repository.delete_data('release_application', {'id': application_id}, commit=False)
            repository.delete_data('release_function', {'application_id': application_id}, commit=False)
            if func_list:
                repository.add_list_data('release_function', func_list, func_fields, commit=False)
            repository.add_data('release_application', application, commit=False)
            if parent_func_list:
                repository.delete_data('function_collection', {'function_id': parent_func_list}, commit=False)
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=str(e))


@data_permission_edit_filter('application-edit')
def enable_application_and_sync_mip(application_id, type_access_released):
    return enable_application_and_sync_mip_internal(application_id, type_access_released)


def enable_application_and_sync_mip_internal(application_id, type_access_released):
    re = release_application(application_id, type_access_released)
    # 同步门户信息到基础数据平台
    if type_access_released == ApplicationTypeAccessReleased.ThirdParty.value:
        # 第三方发布时
        ApplicationOperation(application_id=application_id).upload_data_to_mip()
        # 门户启用
        ApplicationOperation(application_id=application_id).upload_status_to_mip(operation_type=1)
    else:
        # 不是，就下线基础数据的门户展示
        ApplicationOperation(application_id=application_id).upload_status_to_mip(operation_type=2)
    return re


@data_permission_edit_filter('application-edit')
def disable_application(application_id):
    """
    关闭应用
    :param str application_id:
    :return:
    """
    application = repository.get_one('application', {'id': application_id})
    if not application:
        raise UserError(message='门户不存在')
    with get_db() as db:
        try:
            repository.update_data(
                'application',
                {'enable': 0, "type_access_released": ApplicationTypeAccessReleased.UserRole.value},
                {'id': application_id},
            )
            repository.delete_data('release_application', {'id': application_id}, commit=False)
            repository.delete_data('release_function', {'application_id': application_id}, commit=False)
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=str(e))


@data_permission_edit_filter('application-edit')
def delete_application(application_id):
    """
    删除应用
    :param application_id:
    :return:
    """
    from components.snapshot_service import ApplicationSnapshot
    record = function_service.delete_function_by_application_id(application_id)
    record += repository.delete_data('application', {'id': application_id})
    record += application_guide_img_repository.delete_by_application_id(application_id)
    record += application_guide_user_log_repository.delete_log_by_application_id(application_id)
    record += function_collection_repository.delete_collection_by_application_id(application_id)
    record += repository.delete_data('release_application', {'id': application_id})
    record += repository.delete_data('release_function', {'application_id': application_id})
    # 删除拍照数据
    snap_ids = repository.get_column('snapshot', {'snap_type': '门户', 'service_id': application_id}, 'snap_id')
    if snap_ids:
        ApplicationSnapshot.delete_application_snapshot(snap_ids)
    return record


def application_link_to(request, function_id, check_perm=True, is_release=False):
    """
    外链跳转
    :param request:
    :param function_id:
    :param check_perm:
    :param is_release:
    :return:
    """
    if check_perm:
        app = get_application(function_id, is_release)
    else:
        app = get_application_without_permission(function_id, is_release)
    if not app:
        raise UserError("应用不存在或未发布")
    url_pattern = r'^(?:http|ftp)s?://'

    # HD报告、外部报告处理成外链
    if app.url and not re.match(url_pattern, app.url, re.IGNORECASE):
        from dashboard_chart import external_service as dashboard_chart_external_service
        url = dashboard_chart_external_service.get_url_for_external_report_redirect(app.url)
        app.url = url if url else app.url

    if not app.url or not re.match(url_pattern, app.url, re.IGNORECASE):
        raise UserError(message='无效外链地址')
    app.url += ('?' if '?' not in app.url else '&') + 'token=' + request.cookies.get('token')
    hug.redirect.to(app.url)


def delete_application_guide_user_log(application_id):
    """清除指引的浏览记录"""
    return application_guide_user_log_repository.delete_log_by_application_id(application_id)


def get_application_guide_user_log(application_id, function_id):
    exist = application_guide_user_log_repository.exist_log(g.account, application_id, function_id)
    if not exist:
        application_guide_user_log_repository.add_log(g.account, application_id, function_id)
    return exist


def close_guild(application_id: str):
    """关闭用户的门户指引"""
    logs = []
    if not application_guide_user_log_repository.exist_log(g.account, application_id):
        # 这里必须要查询是否已经浏览过了，直接replace into table会出现重复数据
        logs.append({'application_id': application_id, 'account': g.account, 'function_id': None})
    function_ids = function_repository.get_function_ids_by_application_id(application_id, is_release=True) or []
    function_logs = [
        {'application_id': application_id, 'account': g.account, 'function_id': f_id} for f_id in function_ids
    ]
    logs.extend(function_logs)
    return application_guide_user_log_repository.add_logs(logs)


def get_custom_redirect_url(application_id, need_normal_share_url_flag=True):
    """
    获取第三方自定义跳转url
    :param application_id:
    :param need_normal_share_url_flag:
    :return:
    """
    if not application_id:
        raise UserError(message="门户ID不能为空")

    application_info = repository.get_one("application", conditions={"id": application_id}, fields=["platform"])
    app_platform = application_info.get("platform") if application_info else ""
    suffix_path = generate_path_by_platform(application_id, app_platform)

    base_redirect_url = config.get("ThirdParty.portal_base_redirect_url", "")
    normal_share_url = urljoin(base=config.get("Domain.dmp"), url=suffix_path)
    if not base_redirect_url:
        return normal_share_url if need_normal_share_url_flag else ""

    # 拼接后的url请求参数
    parsed_url = urlparse(base_redirect_url)
    extend_url_params = urlencode({"portal_id": application_id, "code": g.code})
    new_query_params = parsed_url.query + "&" + extend_url_params if parsed_url.query else extend_url_params

    # 重新组装url
    new_redirect_url = urlunparse(
        [
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query_params,
            parsed_url.fragment,
        ]
    )
    return new_redirect_url


def get_custom_normal_redirect_url(application_id):
    if not application_id:
        raise UserError(message="门户ID不能为空")

    application_info = repository.get_one("application", conditions={"id": application_id}, fields=["platform"])
    app_platform = application_info.get("platform") if application_info else ""
    suffix_path = generate_path_by_platform(application_id, app_platform)
    normal_share_url = urljoin(base=config.get("Domain.dmp") + '/', url=suffix_path.lstrip('/'))
    return normal_share_url


def generate_path_by_platform(application_id, app_platform):
    """
    生成门户访问路径
    注：访问路径区分pc门户、旧的移动门户、新移动门户
    :param application_id:
    :param app_platform:
    """
    suffix_path = "/app/index/%s"
    if app_platform == "mobile":
        suffix_path = "/app/index/%s/mobile"
    elif app_platform == "mobile_screen":
        suffix_path = "/dataview-mobile/portal/%s"
    return suffix_path % application_id


def check_from_application_third_party(req_token, kwargs):
    """
    检查是否来源门户第三方鉴权访问
    :param req_token:
    """
    application_id = kwargs.get('id')
    is_from_third_party = False
    if not req_token:
        return is_from_third_party
    try:
        token_data = jwt.decode(req_token, config.get('JWT.secret'), algorithms="HS256")
    except DecodeError:
        return is_from_third_party

    application = get_application_without_permission(application_id, is_release=True)
    if application and application.type_access_released == ApplicationTypeAccessReleased.ThirdParty.value:
        # 第三方默认是基础数据权限
        auth_from = TokenAuthFrom.MipAuth.value
        external_params = token_data.get('external_params', {})
        if external_params and external_params.get('portal_id') \
                and token_data.get('auth_from', '') == TokenAuthFrom.ThirdCloudAuth.value:
            # 三云权限
            auth_from = TokenAuthFrom.ThirdCloudAuth.value

        is_from_third_party = True
        setattr(g, 'auth_from', auth_from)
        _debugger.log(f'门户<{application_id}>的权限来源：{auth_from}')
    return is_from_third_party


def get_application_relation_info(app_id, user_agent):
    """
    检查门户的关联门户信息
    :param app_id:
    :param user_agent:
    :return:
    """
    app_model = get_application(app_id, is_release=True)
    relation_url = ""
    relation_info = {"relation_id": "", "relation_url": relation_url}
    if app_model and app_model.relation_id:
        relation_app_id = app_model.relation_id
        relation_model = get_application(relation_app_id)
        cur_platform = get_platform_by_user_agent(user_agent)
        if app_model.platform in [DashboardPlatforms.TV.value, DashboardPlatforms.PC.value] \
                and relation_model and cur_platform == 'mobile':
            relation_url = f"/dataview-mobile/portal/{relation_app_id}"
        elif app_model.platform in [DashboardPlatforms.Mobile.value, DashboardPlatforms.Mobile_New.value] \
                and relation_model and cur_platform == 'pc':
            relation_url = f"/app/index/{relation_app_id}"
        relation_info["relation_id"] = app_model.relation_id
        relation_info["relation_url"] = relation_url
    return relation_info


def get_platform_by_user_agent(user_agent: str) -> str:
    """
    获取UA标识
    :param user_agent:
    :return:
    """
    pattern = (
        'phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|'
        'JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone'
    )
    if user_agent and re.compile(pattern).search(user_agent):
        # ipad作为pc处理
        if re.compile("iPad|pad").search(user_agent):
            return 'pc'
        return 'mobile'
    else:
        return 'pc'


def get_pc_portal_index_redirect_url(project_code):
    suffix_path = f'/app_preview/pc_portal?code={project_code}'
    return urljoin(base=config.get("Domain.dmp") + '/', url=suffix_path.lstrip('/'))


def enable_application_by_api(application_id_list):
    """
    批量启用、同步门户数据
    :param application_id_list:
    :return:
    """
    if isinstance(application_id_list, str):
        application_id_list = application_id_list.split(',')

    logger.error(f"分发、导入时批量发布门户并同步第三方门户，application_id_list：{json.dumps(application_id_list)}")
    errs = []
    app_info = repository.get_list('application', {'id': application_id_list}, ['id', 'type_access_released']) or []
    for app in app_info:
        application_id = app.get('id')
        type_access_released = app.get('type_access_released')
        release_application(application_id, type_access_released)
        # 同步门户信息到基础数据平台
        if type_access_released == ApplicationTypeAccessReleased.ThirdParty.value:
            try:
                # 第三方发布时
                ApplicationOperation(application_id=application_id).upload_data_to_mip()
                # 门户启用
                ApplicationOperation(application_id=application_id).upload_status_to_mip(operation_type=1)
            except Exception as e:
                msg = f"门户同步错误，application_id:{application_id} errs：{str(e)}"
                errs.append(msg)
                logger.error(msg)

    if errs:
        return False, errs
    return True, []

def resync_application():
    data = repository.get_data('specific_task_record', {'data_type': TaskDataType.APPLICATION_PUBLISH_SYNC.value}, from_config_db=True, multi_row=True)
    if not data:
        return
    app_map = {}
    for item in data:
        tenant_code = item.get('tenant_code')
        ids = app_map.get(tenant_code, [])
        ids.append(item.get('data_id'))
        app_map[tenant_code] = ids
    logger.info(f'定时重新发布门户:{app_map}')
    for k, v in app_map.items():
        g.code = k
        g.account = k
        succ, res = enable_application_by_api(list(set(v)))
        if succ :
            repository.delete_data('specific_task_record', {'data_id': v,'data_type': TaskDataType.APPLICATION_PUBLISH_SYNC.value, 'tenant_code': k}, from_config_db=True)
            logger.info(f'租户{k}门户已成功同步到基础数据')
        else:
            logger.error(f'租户{k}门户同步到基础数据失败: {res}')
    logger.info(f'定时重新发布门户任务完成')


def init_app_sync_task():
    from flow.services.flow_service import update_flow_schedule
    from flow.models import FlowModel, FlowNodeModel
    from base.enums import FlowType, FlowStatus, FlowNodeType
    task_id = '********-0000-0000-0000-0appsynctask'
    task_name = '门户重新同步基础数据'
    cron = '0 0/10 * * * ? *'
    data = {'id': task_id, 'name': task_name, 'schedule': cron}
    flow = FlowModel(**data)
    flow.status = FlowStatus.Enable.value
    flow.type = FlowType.Download.value
    flow.nodes = [FlowNodeModel(name=task_name, type=FlowNodeType.Download.value)]
    update_sync_task_flow(flow)
    import app_celery
    # 注册Rundeck任务
    update_flow_schedule(task_id, command=get_command(task_id, 'resync_application_task'))
    return app_celery.resync_application_task.apply_async(kwargs={})


def update_sync_task_flow(flow_model):
    from flow.services.flow_service import add_flow, update_flow
    flow = repository.get_data("flow", {"id": flow_model.id})
    if flow:
        update_flow(flow_model, False)
    else:
        add_flow(flow_model)
    return flow

def get_command(flow_id, func, queue_name='celery'):
    """
    获取rundeck执行celery的command命令
    :param flow_id:
    :param queue_name:
    :return:
    """
    from dmplib import config
    celery_task_name = "app_celery." + func
    cmd_template_snap = config.get(
        "Rundeck.cmd_template_celery",
        "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
    )
    command = '%s %s %s %s %s' % (cmd_template_snap, g.code, celery_task_name, flow_id, queue_name)
    return command

def get_global_params(application_id):
    #  获取门户直接挂接的报告
    application_dashboard_url = repository.get_column('application', {'url !=': '', 'report_type': [0, 2], 'id': application_id}, ['url']) or ''
    if not str(application_dashboard_url).startswith('http') and len(str(application_dashboard_url)) == 36:
        dashboard_info = repository.get_one('dashboard_released_snapshot_dashboard', {'id': application_dashboard_url, 'data_type': 1}, ['id', 'name', 'global_params']) or []
        dashboard_return = {'func_id': '', 'name': '', 'dashboard_id': application_dashboard_url, 'dashboard_name': dashboard_info.get('name')}
        global_param = dashboard_info.get('global_params')
        if global_param:
            global_params = json.loads(global_param)
            for params in global_params:
                params_arr = dict()
                params_arr['id'] = params.get('id')
                params_arr['name'] = params.get('name')
                params_arr['alias_name'] = params.get('alias_name')
                dashboard_return['global_params'].append(params_arr)
        return [dashboard_return]
    # 获取门户下菜单挂接的报告ID
    func_dashboard_list = []
    func_url_list = repository.get_list('function', {'url !=': '', 'report_type': [0, 2, 6], 'application_id': application_id}, ['id', 'name', 'url']) or []
    dashboard_ids = []
    for func_info in func_url_list:
        url = func_info.get('url', '')
        if not str(url).startswith('http') and len(str(url)) == 36:
            func_dashboard_list.append({'func_id': func_info.get('id'), 'name': func_info.get('name'), 'dashboard_id': url})
            dashboard_ids.append(url)
    if dashboard_ids:
        dashboard_ids = list(set(dashboard_ids))
        data_list = repository.get_list('dashboard_released_snapshot_dashboard', {'id': dashboard_ids, 'data_type': 1}, ['id', 'name', 'global_params']) or []
        for func_dashboard in func_dashboard_list:
            for data in data_list:
                if func_dashboard.get('dashboard_id') == data.get('id'):
                    global_params = data.get('global_params')
                    func_dashboard['dashboard_name'] = data.get('name')
                    func_dashboard['global_params'] = []
                    if global_params:
                        global_params = json.loads(global_params)
                        for params in global_params:
                            params_arr = dict()
                            params_arr['id'] = params.get('id')
                            params_arr['name'] = params.get('name')
                            params_arr['alias_name'] = params.get('alias_name')
                            func_dashboard['global_params'].append(params_arr)
    return func_dashboard_list


def save_application_config(application_config, application_config_type):
    data = {'application_config': json.dumps(application_config), 'application_config_type': application_config_type}
    model = ApplicationConfigModel(**data)
    result = repository.get_one(ApplicationConfigModel.__table__, {'application_config_type': application_config_type})
    if result:
        repository.update_model(ApplicationConfigModel.__table__, model, {'application_config_type': model.application_config_type})
    else:
        repository.add_model(ApplicationConfigModel.__table__, model)
    return True


def get_application_config_by_config_type(application_config_type):
    application_config = repository.get_data_scalar(ApplicationConfigModel.__table__, {'application_config_type': application_config_type}, 'application_config')
    try:
        application_config = json.loads(application_config) if application_config else application_config
    except Exception as e:
        logger.error(str(e))
    return application_config


def get_permission_granted_pc_applications(transfer_to_workbench_fields:bool = False, application_id = None):
    # 获取当前用户已授权的门户
    granted_apps = check_mip_app_allow_exists()
    result = []
    if application_id:
        for app in granted_apps:
            if application_id == app.get('id'):
                result = [app]
                break
    else:
        result = granted_apps
    app_ids = [item.get('id') for item in result]
    if app_ids:
        result = repository.get_list('application', {'id': app_ids, 'platform':'pc'}, ['id','name'])
    if transfer_to_workbench_fields:
        return transfer_app_field_name(result)
    else:
        return result


def get_permission_granted_pc_menu_list(transfer_to_workbench_fields:bool = False, params={}):
    # 获取当前用户已授权的门户的菜单列表
    granted_apps = get_permission_granted_pc_applications(False, params.get('application_id'))
    if not granted_apps:
        return []
    result = []
    from app_menu.services.function_service import get_function_list
    dashboard_name = params.get('dashboard_name')
    for app in granted_apps:
        func_list = get_function_list(app.get('id'), is_release=True)
        func_list = application_auth_service.filter_funcs_by_mip_auth(func_list)
        dashboard_list = []
        for func in func_list:
            if not func.get('url'):
                continue
            if dashboard_name and dashboard_name not in func.get('name', ''):
                continue
            func['application_name'] = app.get('name')
            func['application_id'] = app.get('id')
            dashboard_list.append(func)
        result += dashboard_list
    # 内存分页
    total = len(result)
    page_num = params.get('current', 1)
    page_size = params.get('page_size', 10)
    total_page, remain = divmod(total, page_size)
    if remain > 0:
        total_page += 1
    result = result[page_num * page_size - page_size: page_num * page_size]
    if transfer_to_workbench_fields:
        result = transfer_menu_field_name(result)
    return {'records': result, 'total': total, 'size': page_size, 'current': page_num, 'pages': total_page}


def save_template_data(params):
    # 保存模板配置的菜单数据(工作台常用报表)
    template_id = params.get('template_id')
    if not template_id:
        raise UserError(message='templateId为空')
    data = params.get('data')
    repository.delete_data('template_data_config',{'template_id': template_id, 'user_id':g.account})
    if data:
        to_save = []
        size = len(data)
        for idx, item in enumerate(data):
            item['id'] = seq_id()
            item['template_id'] = template_id
            item['sort'] = size - idx
            to_save.append(item)
        repository.add_list_data('template_data_config', to_save, list(data[0].keys()))


def list_template_data(transfer_to_workbench_fields:bool = False, params = {}):
    # 查询模板配置数据
    template_id = params.get('template_id')
    user_id = g.account
    if not template_id:
        raise UserError(message='templateId为空')
    page_size = int(params.get('page_size', 10))
    current = int(params.get('current', 1))
    page_info = [current, page_size]
    where = {'template_id': template_id}
    if user_id:
        where['user_id'] = user_id
    data = repository.get_list('template_data_config', where, order_by='sort desc', page=page_info)
    total = repository.get_value('template_data_config', where, 'count(*)')
    total_page, remain = divmod(total, page_size)
    if remain > 0:
        total_page += 1
    if transfer_to_workbench_fields:
        res = []
        for item in data:
            res.append({
                'userGuid':item.get('user_id'),
                'superWorkbenchUsedChartsGUID':item.get('workbench_used_id'),
                'chartsDetailGUID':item.get('data_id'),
                'chartsName':item.get('data_name'),
                'dmpReportType':item.get('data_type'),
                'templateGuid':item.get('template_id'),
                'sourceGUID': '',
                'sourceType': 1,
             })
        data = res
    return {'records': data, 'total': total, 'size': page_size, 'current': current, 'pages': total_page}

def get_menu_access_url(kwargs):
    menu_id = kwargs.get('menu_id')
    if not menu_id:
        raise UserError(message='menu_id为空')
    menu = repository.get_one('function', {'id': menu_id})
    if not menu:
        raise UserError(message='菜单不存在')
    report_type = menu.get('report_type')
    dashboard_id = menu.get('url')
    res = ''
    if report_type == 0 or report_type == 6:
        res = f'{AppHosts.get(SkylineApps.DMP, False)}/api/user/superportal/dashboard?report_id={dashboard_id}'
    else:
        row = repository.get_one('dashboard', {'id': dashboard_id}, ['type_access_released'])
        res = f"{AppHosts.get(SkylineApps.DMP, False)}/api/user/superportal/dashboard?report_id={dashboard_id}&type={AddFuncType.ActiveReport.value}&release_type={row.get('type_access_released')}"
    return res


def transfer_template_data_field_name(data):
    """
        字段名转换
        :param kwargs:
        :return:
        """
    result = []
    for item in data:
        result.append({
            'chartsDetailGUID': item.get('data_id'),
            'chartsName': item.get('data_name'),
            'dmpReportType': item.get('data_type'),
            'templateGuid': item.get('template_id'),
            'parentGUID': '',
            'towerCategory': 0,
            'typeSeq': 0,
        })
    return result


def transfer_app_field_name(data:list):
    """
    字段名转换
    :param kwargs:
    :return:
    """
    result = []
    for item in data:
        result.append({
            'chartsTypesGUID': item.get('id'),
            'typeName': item.get('name'),
            'isTop': 0,
            'parentGUID': '',
            'towerCategory': 0,
            'typeSeq': 0,
       })
    return result

def transfer_menu_field_name(data:list):
    """
    字段名转换
    :param kwargs:
    :return:
    """
    report_type_map = {0:'仪表板', 1:'统计报表', 3:'统计报表', 4:'统计报表', 5:'统计报表', 6:'大屏报告'}
    result = []
    for item in data:
        result.append({
            'chartsSuperUrl':'',
            'chartsName': item.get('name'),
            'chartsDescription': item.get('description'),
            'chartsDetailGUID': item.get('id'),
            'typeName': item.get('application_name'),
            'chartsTypesGUID': item.get('application_id'),
            'dmpReportType': report_type_map.get(item.get('report_type')),
            'isAdd': 0,
            'sourceType': 1,
            'sourceGUID': '',
       })
    return result

def list_rdc_template(**kwargs):
    data = repository.get_list('publish_center_template', kwargs, order_by='modified_on desc', from_config_db=True)
    return data
