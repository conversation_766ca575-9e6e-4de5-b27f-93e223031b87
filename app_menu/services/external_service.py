#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import json
import jwt
import time
from collections import defaultdict
import logging
from urllib.parse import quote, unquote

# ---------------- 业务模块 ----------------
from base import repository
from dmplib import config
from dmplib.utils.errors import UserError
from dashboard_chart.repositories import external_dashboard_repository
from app_menu.repositories import external_application_repository
from base.enums import DashboardJumpType, DashboardPlatforms
from integrate.services.high_data_service import HighDataService

logger = logging.getLogger(__file__)


def _parse_released_jump_configs(jump_configs: list):
    target_dashboard_ids = set()
    if not jump_configs:
        return target_dashboard_ids
    for single_jump_config in jump_configs:
        try:
            decoded_single_jump_config = json.loads(single_jump_config)
        except:
            decoded_single_jump_config = []
        if not decoded_single_jump_config:
            continue
        for i in decoded_single_jump_config:
            if i and i.get('target_type') == DashboardJumpType.Dashboard.value:
                target_dashboard_ids.add(i.get('target'))
    return target_dashboard_ids


def recursive_get_target_dashboard_ids(query_dashboard_ids: list, results: set):
    """
    递归获取门户所有的跳转报告
    注：需要避免可能存在的报告互相跳转导致的递归死循环
    :param list query_dashboard_ids:
    :param list results:
    :return:
    """
    if not query_dashboard_ids:
        return
    jump_config_rows = external_dashboard_repository.batch_get_released_jump_config(query_dashboard_ids)
    if not jump_config_rows:
        return
    jump_configs = [i.get('jump') for i in jump_config_rows]
    target_dashboard_ids = _parse_released_jump_configs(jump_configs)
    new_target_dashboard_ids = target_dashboard_ids - results
    if new_target_dashboard_ids:
        results |= new_target_dashboard_ids
        recursive_get_target_dashboard_ids(list(new_target_dashboard_ids), results)


def check_dashboard_in_application(application_id: str, dashboard_id: str):
    """
    检查目标报告是否在当前门户报告链中
    :param str application_id:
    :param str dashboard_id:
    :return:
    """
    target_urls = repository.get_data('function', {'application_id': application_id}, fields=['url'], multi_row=True)
    if not target_urls:
        return False
    dashboard_ids = [i.get('url') for i in target_urls if i.get('url') and not str(i.get('url', '')).startswith('http')]
    if dashboard_id in dashboard_ids:
        return True
    urls = [i.get('url') for i in target_urls if i.get('url') and str(i.get('url', '')).startswith('http')]
    for url in urls:
        if dashboard_id in url:
            return True
    all_target_dashboard_ids = set()
    recursive_get_target_dashboard_ids(dashboard_ids, all_target_dashboard_ids)
    if dashboard_id in all_target_dashboard_ids:
        return True
    return False


def get_relation_dashboard_ids(application_ids: list):
    """
    获取门户包含的所有报告(包括跳转目标报告)
    :param str application_ids:
    :return:
    """
    if not application_ids:
        return []
    target_urls = external_application_repository.batch_get_urls_by_application_ids(application_ids, is_release=True)
    if not target_urls:
        return []
    dashboard_ids = [i.get('url') for i in target_urls if i.get('url') and not str(i.get('url', '')).startswith('http')]
    all_target_dashboard_ids = set()
    recursive_get_target_dashboard_ids(dashboard_ids, all_target_dashboard_ids)
    if all_target_dashboard_ids:
        dashboard_ids.extend(list(all_target_dashboard_ids))
    return dashboard_ids


def get_relational_dashboards(application_ids: list):
    """
    获取门户包含的所有报告(包括跳转目标报告)
    :param str application_ids:
    :return:
    """
    relation_dashboard_ids = get_relation_dashboard_ids(application_ids)
    if not relation_dashboard_ids:
        return []
    return external_dashboard_repository.get_info_by_dashboard_ids(relation_dashboard_ids)


def get_func_dashboard_ids(application_ids: list):
    """
    获取菜单绑定的报告
    :param str application_ids:
    :return:
    """
    if not application_ids:
        return []
    target_urls = external_application_repository.batch_get_urls_by_application_ids(application_ids, is_release=True)
    if not target_urls:
        return []
    target_dashboard_ids = [
        i.get('url') for i in target_urls if i.get('url') and not str(i.get('url', '')).startswith('http')
    ]
    return target_dashboard_ids


def _parse_and_get_relation_dict(jump_configs: list):
    """
    解析jump内容，获取起跳和被跳报告
    :param str jump_configs:
    :return:
    """
    result = defaultdict(list)
    if not jump_configs:
        return result
    for single_jump_config in jump_configs:
        try:
            decoded_single_jump_config = json.loads(single_jump_config)
        except:
            decoded_single_jump_config = []
        if not decoded_single_jump_config:
            continue
        for i in decoded_single_jump_config:
            if i and i.get('target_type') == DashboardJumpType.Dashboard.value:
                result[i.get('target')].append(i.get('dashboard_id'))
    return result


def recursive_get_jump_dashboard_auths(op_dashboard_ids: list, dashboard_auths: dict):
    """
    递归获取跳转目标报告，并设置为起跳报告的权限
    :param str op_dashboard_ids:
    :param str dashboard_auths:
    :return:
    """
    jump_config_rows = external_dashboard_repository.batch_get_released_jump_config(op_dashboard_ids)
    if not jump_config_rows:
        return
    jump_configs = [i.get('jump') for i in jump_config_rows]
    jump_relation_dict = _parse_and_get_relation_dict(jump_configs)
    if not jump_relation_dict:
        return
    filtered_to_dashboard_ids = list(filter(lambda i: i not in dashboard_auths, jump_relation_dict.keys()))
    if not filtered_to_dashboard_ids:
        return
    for to_dashboard_id in filtered_to_dashboard_ids:
        from_dashboard_ids = jump_relation_dict.get(to_dashboard_id)
        if not from_dashboard_ids:
            continue
        from_dashboard_auths = [dashboard_auths.get(i, '') for i in from_dashboard_ids]
        from_dashboard_auths_set = [set(str(i).split(',')) for i in from_dashboard_auths if i]
        total_auths_set = set()
        for i in from_dashboard_auths_set:
            total_auths_set = total_auths_set.union(i)
        dashboard_auths[to_dashboard_id] = ','.join(total_auths_set)
    recursive_get_jump_dashboard_auths(filtered_to_dashboard_ids, dashboard_auths)


def application_highdata_auth_report_filter(account, data, allow_ids):
    """
    对门户中的HighData报告进行权限校验和报告过滤
    :param account:
    :param data:
    :param allow_ids:
    :return:
    """
    # 检查门户中是否存在HighData的报告
    highdata_id_map = _deal_is_highdata_data(data)
    if not highdata_id_map:
        return data
    # 门户function的记录id
    func_ids = list(highdata_id_map.keys())
    # HD的报告id
    highdata_ids = list(highdata_id_map.values())

    # 将门户中的所有的HD报告全部去除，如有权限则会添加回来
    allow_ids = [id for id in allow_ids if id not in func_ids]
    # 获取门户的类型，移动还是pc
    first_row = data[0]
    application_id = first_row.get("application_id")
    platform = get_application_platform(application_id)
    if platform == DashboardPlatforms.PC.value:
        report_type = 'dashboard'
    else:
        report_type = 'm_report'
    # 获取HighData的报告id列表
    hd_auth_ids = get_highdata_auth_report_id(account, report_type)
    # 获取有权限报告失败或无权限
    if not hd_auth_ids:
        data = _format_not_allow_highdata_data(data, allow_ids)
        return data

    # 拥有权限的HD报告id
    set_highdata_ids = set(highdata_ids)
    set_auth_ids = set(hd_auth_ids)
    hd_rs_ids = set_auth_ids & set_highdata_ids
    # 没有任何hd报告权限
    if not hd_rs_ids:
        data = _format_not_allow_highdata_data(data, allow_ids)
        return data
    # 通过有权限的hd报告id，查找到function的记录id
    allow_func_ids = []
    for k, v in highdata_id_map.items():
        if v in hd_rs_ids:
            allow_func_ids.append(k)
    allow_ids += allow_func_ids
    data = _format_not_allow_highdata_data(data, allow_ids)
    return data


def get_highdata_auth_report_id(account, report_type):
    """
    请求HighData获取当前用户有权限的报告
    :param account:
    :param report_type:
    :return:
    """
    hd_servie = HighDataService()
    return hd_servie.get_auth_report_id_list(account, report_type)


def _deal_is_highdata_data(data):
    """
    查找门户中是否存在HighData，老移动的报告
    :param data:
    :return:
    """
    highdata_id_map = {}
    for k in data:
        if not isinstance(k, dict):
            continue
        if k.get('sub'):
            tmp_map = _deal_is_highdata_data(k.get('sub'))
            highdata_id_map.update(tmp_map)

        # 自己创建的资源不做权限限制
        if k.get('url') and k.get('report_type') in [1, 2]:
            highdata_id_map[k.get('id')] = k.get('url')
    return highdata_id_map


def _get_data_by_list(data, new_data):
    """
    树形菜单平铺
    :param data:
    :return:
    """
    for k in data:
        if k.get('sub'):
            _get_data_by_list(k.get('sub'), new_data)
        new_data.append(k)


def _format_not_allow_highdata_data(data, allow_ids):
    """
    HighData的报告挂载在数见门户后，由数见菜单权限，叠加HighData的报告权限，共同决定数见HD菜单是否展示
    其中需要解决的场景有：
    案例：
    数见有一个菜单目录A，A下面有这样几个菜单，数见报告菜单A1,A2，HD报告菜单A3,A4。
    1）场景：
    只授权菜单目录，不授权菜单目录下的菜单。菜单目录A在数见授权了，但A下面的菜单都没有勾选权限。
    用例：
    1、菜单目录A整体授权了，HD报告在HD中没有授权，预期：A1,A2显示。A3，A4不显示
    2、菜单目录A整体授权了，HD报告在HD中授权了A3报告，预期：A1,A2显示。A3显示，A4不显示
    2）场景：
    授权菜单目录，且授权菜单目录下的菜单.菜单目录A在数见授权了，且A下面的菜单也授权。
    用例：
    1、A下面数见菜单A1，A3在数见授权了，A2，A4没有授权。HD报告A3在HighData中没有授权。预期：A1显示，A3不显示，A2，A4不显示
    1、A下面数见报告A1在数见授权了，A2,A3,A4都没有授权。HD报告A3在HighData中授权了。预期：A1显示，A3不显示，A2，A4不显示
    3）场景
    门户的菜单添加在根目录，不在任何菜单目录下。数见报告菜单A1,A2，HD报告菜单A3,A4
    用例：
    1、A1数见授权，A3数见授权，A3 HD不授权。预期：A1显示，A3不显示，A2，A4不显示
    2、A1数见授权，A3数见授权，A3 HD授权。预期：A1显示，A3显示，A2，A4不显示
    3、A1数见授权，A3数见不授权，A3 HD授权。预期：A1显示，A3不显示，A2，A4不显示

    :param data: 菜单列表
    :param allow_ids: 有权限的菜单id
    :return:
    """
    new_data = []
    _get_data_by_list(data, new_data)

    allow_data = []
    for k in new_data:
        # HD的报告需要判断权限，有权限的则展示，无权限则不展示
        if k.get('report_type') in [1, 2]:
            if k.get('id') in allow_ids:
                allow_data.append(k)
        else:
            allow_data.append(k)

    allow_data = sorted(allow_data, key=lambda x: x.get("level_code"))
    # 平铺菜单转换为树形
    return generate_func_tree(allow_data)


def generate_func_tree(func_list):
    """
    生成功能菜单树
    :param list func_list:
    :return list:
    """
    if not func_list:
        return None
    result = []
    tmp_dict = {}
    for func in func_list:
        if not isinstance(func, dict):
            continue
        func['sub'] = []
        # 功能菜单level_code格式: 0001-、0001-0001-
        func_code = func.get('level_code')
        parent_organ_code = func_code[0: len(func_code) - 5]
        if parent_organ_code not in tmp_dict:
            result.append(func)
            tmp_dict[func_code] = func
        else:
            tmp_dict.get(parent_organ_code)['sub'].append(func)
            tmp_dict[func_code] = func
    return result


def get_application_platform(application_id, is_release=False):
    """
    获取门户平台类型
    :param application_id:
    :param bool is_release:
    :return:
    """
    app_table = 'release_application' if is_release else 'application'
    return repository.get_data_scalar(app_table, {'id': application_id}, 'platform')


def get_token_url(url):
    from dmplib.hug import g
    data = {
        'account': g.account, 'user_id': g.userid, 'is_developer': g.is_developer if hasattr(g, 'is_developer') else 0,
        'exp': int(time.time()) + 300, 'code': g.code, 'group_ids': g.group_ids if hasattr(g, 'group_ids') else []
    }
    token = jwt.encode(data, config.get('JWT.secret'))
    domain = config.get('Domain.dmp').rstrip('/')
    return '{}/api/system/redirect?token={}&redirect_url={}'.format(domain, token, quote(url))


def set_app_distribute_type(app_id, distribute_type):
    if app_id:
        # 查询报表是否存在
        data = repository.get_one('application', {'id': app_id}, ['id'])
        if not data:
            raise UserError(message='报告不存在')
        repository.update_data('application', {'distribute_type': distribute_type}, {'id': app_id})
    return True


def check_token_and_login(request, response, **kwargs):
    from user.services.user_service import set_login_status
    import urllib.parse
    try:
        token = kwargs.get('token')
        url = kwargs.get('redirect_url')
        if not token:
            logger.error('token不存在')
            return '/'
        jwt_data = jwt.decode(token, config.get('JWT.secret'), algorithms='HS256')
        domain = getattr(request, 'new_host', request.host) or urllib.parse.urlsplit(config.get('Domain.dmp')).netloc
        set_login_status(
            response, domain, jwt_data.get('code'), jwt_data.get('user_id'),
            jwt_data.get('account'), jwt_data.get('group_ids'), **{"expires": 300, "is_developer": jwt_data.get('is_developer')}
        )
        # 处理url拼接的问题防止多拼接/或没有/的问题
        url = '/{}'.format(url.lstrip('/')) if url else '/'
        return unquote(url)
    except Exception as e:
        logger.error(str(e))
        return '/'

