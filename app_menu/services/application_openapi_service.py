#!/usr/local/bin python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import logging
from urllib.parse import urlencode

# ---------------- 业务模块 ----------------
from app_menu.models import ApplicationOpenAPIQueryModel
from app_menu.repositories import external_application_repository
from app_menu.services import external_service
from rbac.repositories import data_permission_repository
from user.repositories import user_repository
from user.services.user_service import get_cur_role_id
from dmplib.constants import ADMIN_ROLE_ID
from dmplib.hug import debugger
from dmplib import config
from base.enums import LoginFrom


logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)


def get_portal_list_for_openapi(**kwargs):
    user_account = kwargs.get('user_account', None) or kwargs.get('userCode', None)
    if user_account is not None:
        from dmplib.hug import g

        g.account = user_account
        has_role, role_ids = get_user_role_ids(user_account)
        if not has_role:
            return []
        else:
            if ADMIN_ROLE_ID not in role_ids and not data_permission_repository.get_permission_by_role_ids(
                role_ids, 'application', 'view'
            ):
                allow_ids = data_permission_repository.get_allow_ids(role_ids, 'application', 'view') or []
                own_ids = external_application_repository.get_user_create_application_ids(user_account, is_release=True)
                allow_ids.extend(own_ids or [])
                if not allow_ids:
                    return []
                kwargs['application_ids'] = list(set(allow_ids))
    query_model = ApplicationOpenAPIQueryModel(**kwargs)
    portal_list = external_application_repository.get_application_list_by_query_model(query_model, is_release=True) or []
    for item in portal_list:
        platform = item.get("platform")
        preview_url = ''
        if platform == 'pc':
            preview_url = _get_pc_portal_url(item.get('id'))
        elif platform == 'mobile_screen':
            preview_url = _get_mobile_screen_portal_url(item.get('id'))
        item['preview_url'] = preview_url
    # 附加门户首页节点
    _init_portal_list_data(query_model, portal_list)
    return portal_list


def _get_pc_portal_url(report_id='', report_type='portal'):
    params = {'__from': LoginFrom.SuperPortal.value, 'report_id': report_id or '', 'report_type': report_type}
    return f"{config.get('Domain.dmp')}/api/user/superportal/dashboard?{urlencode(params)}"


def _get_mobile_screen_portal_url(report_id='', report_type='portal'):
    params = {'__from': LoginFrom.SuppApp.value, 'report_id': report_id or '', 'report_type': report_type}
    return f"{config.get('Domain.dmp')}/api/user/superapp?{urlencode(params)}"


def _init_portal_list_data(query_model: ApplicationOpenAPIQueryModel, portal_list: list):
    """
    提供给超级APP、超级工作台的门户选择接口中，需要加上门户首页的节点，以供选择门户首页
    :param query_model:
    :param portal_list:
    :return:
    """
    platform = query_model.platform
    portal_index_node_list = [_portal_index_pc(), _portal_index_mobile_screen()]
    if platform == 'pc':
        portal_index_node_list = [_portal_index_pc()]
    elif platform == 'mobile_screen':
        portal_index_node_list = [_portal_index_mobile_screen()]

    if portal_index_node_list:
        portal_list[:0] = portal_index_node_list


def _portal_index_mobile_screen():
    return {
            "id": "********-1111-1111-1111-000000000002",
            "name": "移动门户首页",
            "platform": "mobile_screen",
            "nav_type": 0,
            "description": "",
            "target": "",
            "rank": 1,
            "enable": 1,
            "icon": "",
            "type_access_released": 3,
            "created_on": "2023-6-19 17:27:44",
            "created_by": "uitest",
            "preview_url": _get_mobile_screen_portal_url('', 'portal_list')
        }


def _portal_index_pc():
    return {
            "id": "********-1111-1111-1111-************",
            "name": "PC门户首页",
            "platform": "pc",
            "nav_type": 0,
            "description": "",
            "target": "",
            "rank": 1,
            "enable": 1,
            "icon": "",
            "type_access_released": 3,
            "created_on": "2023-6-19 17:27:44",
            "created_by": "uitest",
            "preview_url": _get_pc_portal_url('', 'portal_list')
        }


def get_user_role_ids(user_account):
    user_id = user_repository.get_cur_user_id()
    if not user_id:
        return False, []

    role_ids = get_cur_role_id(user_id=user_id, account=user_account)
    if not role_ids:
        return False, []
    return True, role_ids


def get_relational_dashboards(portal_id):
    return external_service.get_relational_dashboards([portal_id]) or []
