#!/usr/local/bin python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
from hashlib import md5
import logging
import json

# ---------------- 业务模块 ----------------
from dmplib.redis import conn as redis_conn
from dmplib.hug import debugger
from dmplib.utils.errors import UserError
from dashboard_chart.repositories import external_dashboard_repository
from dmplib.hug import g
from app_menu.services import external_service
from components.mip_auth_application import ApplicationDashboardOperation
from dashboard_chart.services.mip_auth_adapter import MIPApplicationAdapter

logger = logging.getLogger(__name__)
_debugger = debugger.Debug(__name__)


def generate_session_id(auth_id):
    salt = 'ycdmp'
    obj = md5(auth_id.encode('utf-8'))
    obj.update(salt.encode('utf-8'))
    return obj.hexdigest()[8:-8]


def get_auth_cache_key(session_id):
    prefix = 'portal_dashboard_auths'
    return prefix + ':' + session_id


def set_auth_cache(session_id, auths):
    cache_key = get_auth_cache_key(session_id)
    return redis_conn().hmset(cache_key, auths)


def del_auth_cache(session_id):
    cache_key = get_auth_cache_key(session_id)
    return redis_conn().delete(cache_key)


def get_auth_cache(session_id, dashboard_id):
    cache_key = get_auth_cache_key(session_id)
    return redis_conn().hget(cache_key, dashboard_id)


def get_all_auth_cache(session_id):
    cache_key = get_auth_cache_key(session_id)
    return redis_conn().hgetall(cache_key)


def get_auth_cache_by_keys(session_id, dashboard_ids):
    cache_key = get_auth_cache_key(session_id)
    return redis_conn().hmget(cache_key, dashboard_ids)


def upload_auth(auth_id, portal_id, dashboard_auths):
    _debugger.log({'上传第三方门户报表权限': {'auth_id': auth_id, 'auths': json.dumps(dashboard_auths)}})
    biz_codes = [i.get('biz_code') for i in dashboard_auths if i.get('biz_code')]
    if len(biz_codes) != len(dashboard_auths):
        raise UserError(message='参数`biz_code`不能为空')
    dashboard_infos = external_dashboard_repository.get_info_by_biz_codes(biz_codes)
    if not dashboard_infos:
        raise UserError(message='获取报表信息异常')
    if len(dashboard_infos) != len(biz_codes):
        raise UserError(message='存在已失效报表')

    biz_code_dashboard_ids = {i.get('biz_code'): i.get('id') for i in dashboard_infos}
    cache_dashboard_auths = {}
    for i in dashboard_auths:
        dashboard_id = biz_code_dashboard_ids.get(i.get('biz_code'))
        cache_dashboard_auths[dashboard_id] = i.get('auth')

    # 门户内被跳报告的权限继承于菜单绑定的起跳报表
    func_dashboard_ids = external_service.get_func_dashboard_ids([portal_id])
    func_dashboard_auths = {i: cache_dashboard_auths.get(i, '') for i in func_dashboard_ids}
    external_service.recursive_get_jump_dashboard_auths(func_dashboard_ids, func_dashboard_auths)

    try:
        session_id = generate_session_id(auth_id)
        del_auth_cache(session_id)
        set_auth_cache(session_id, func_dashboard_auths)
    except Exception as e:
        logger.error(msg='设置第三方门户报表权限缓存异常，异常信息:{}'.format(str(e)))
        raise UserError(message='获取session_id异常')
    _debugger.log({'第三方门户报表权限生成session_id': session_id})
    return session_id


# 基础数据平台第三方门户授权
def filter_funcs_by_mip_auth(funcs: list):
    if not funcs:
        return funcs

    # 基础数据平台
    # 1. 先获取所有的叶子节点菜单
    mip_application_adapter = MIPApplicationAdapter()
    node_funcs = mip_application_adapter.get_all_node_func_id_by_all_funcs(funcs)

    # 2. 请求基础数据平台
    node_func_ids = [f.get('id', '') for f in node_funcs]
    permission_data = ApplicationDashboardOperation().get_reports_permission_data(report_ids=node_func_ids) or {}
    # permission_data: {
    #     '3a0ab404-3bae-0f72-4d92-15b9687bcf19': 'view',
    #     '3a0ab404-3dca-d6fb-8a63-80db8ce300ad': 'view'
    # }
    _debugger.log({'基础数据平台取回的权限数据': json.dumps(permission_data)})

    # 3. 菜单权限过滤
    view_auth_funcs = []
    view_auth_func_ids = [auth_func_id for auth_func_id, auth in permission_data.items() if 'view' in auth]
    funcs_map = {f.get('id'): f for f in funcs}
    for func in funcs:
        func_id = func.get('id')
        # 存在关联报告的菜单，需要校验是否有view权限
        if func_id not in view_auth_func_ids:
            continue
        _collect_all_parent(func, view_auth_funcs, funcs_map)
        view_auth_funcs.append(func)
    # 重新按照level_code排序下，保证后面的build_tree正确
    view_auth_funcs = sorted(view_auth_funcs, key=lambda d: d.get('level_code'))

    return view_auth_funcs


# 递归收集所有父级菜单信息
def _collect_all_parent(func: dict, view_auth_funcs: list, funcs_map: dict):
    parent_id = func.get('parent_id')
    if not parent_id:
        return
    parent_func = funcs_map.get(parent_id)
    if parent_func and parent_func not in view_auth_funcs:
        view_auth_funcs.append(parent_func)
        _collect_all_parent(parent_func, view_auth_funcs, funcs_map)


# 默认的三云第三方集成
def filter_funcs_by_auth(funcs: list):
    # auth_cache_data: {
    #     "39edb28e-f622-647d-5082-68cc200a605e": "\"\"",
    #     "39f4481b-236e-489a-f8bb-53488ba422f4": "\"\"",
    #     "39f5055f-7ed1-fc58-13e9-609e29cb1599": "\"\"",
    #     "39fc257c-12f4-7543-36eb-33e686202c94": "\"\"",
    #     "39f9119c-83a9-dbc1-a234-b9e1e1e46f47": "\"\"",
    #     "39f866be-114e-8c8a-f737-a8a2f7576529": "\"view\"",
    #     "39f85616-1744-1d46-4954-638ec32059bd": "\"\"",
    #     "39f85b2b-3526-d99b-01af-7babba7bbd89": "\"\"",
    #     "39fe17af-6747-2d96-73cd-8e1a97110f50": "\"\""
    # }\
    if not funcs:
        return funcs
    external_params = getattr(g, 'external_params', {})
    session_id = external_params.get('session_id', '') if external_params else ''
    if not session_id:
        return funcs

    auth_cache_data = get_all_auth_cache(session_id) or {}
    _debugger.log({'根据session_id获取到的权限数据': json.dumps(auth_cache_data)})

    view_auth_funcs = []
    view_auth_dashboard_ids = [
        auth_dashboard_id for auth_dashboard_id, auth in auth_cache_data.items() if 'view' in auth
    ]
    valid_parent_funcs = {i.get('parent_id'): [] for i in funcs if i.get('parent_id')}
    for i in funcs:
        func_id = i.get('id')
        url = i.get('url')
        parent_id = i.get('parent_id')
        # 存在关联报告的菜单，需要校验是否有view权限
        if url and not str(url).startswith('http') and url not in view_auth_dashboard_ids:
            continue
        if parent_id:
            valid_parent_funcs[parent_id].append(func_id)
        view_auth_funcs.append(i)

    result = [
        i for i in view_auth_funcs if i.get('id') not in valid_parent_funcs or valid_parent_funcs.get(i.get('id'))
    ]
    return result


def check_portal_permission(session_id, dashboard_id, action):
    flag = False
    auth_res = get_auth_cache(session_id, dashboard_id)
    if auth_res and auth_res.find(action) != -1:
        flag = True
    return flag
