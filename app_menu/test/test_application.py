# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-
#
# """
#     <NAME_EMAIL> on 2018/4/13.
# """
# from app_menu.models import ApplicationQueryModel
# from app_menu.services import application_service, function_service
# from dmplib.hug import g
# from tests.base import BaseTest
#
# import unittest
#
#
# class TestApplicationService(BaseTest):
#     def __init__(self, method_name="runTest"):
#         super().__init__(method_name, code='dev', account='admin')
#
#     def test_get_applications_list(self):
#         """
#         获取应用list
#         :return:
#         """
#         kwargs = {'page': 1, 'page_size': 1000}
#         g.userid = '22b11db4-e907-4f1f-8835-b9daab6e1f23'
#         result = application_service.get_application_list(ApplicationQueryModel(**kwargs))
#         self.assertIsInstance(result, dict, "测试结果返回类型不匹配")
#
#     def test_get_application(self):
#         result = application_service.get_application('39e536c5-f309-b1ab-51d9-b6d18fc53058')
#         self.assertIsInstance(result, dict, "测试结果返回类型不匹配")
#
#     def test_get_function_tree(self):
#         result = function_service.get_function_list('39e6b3a0-0087-e8e4-b0f1-aea5b4f85338')
#         print(result)
#
#     def test_get_application_and_sub(self):
#         g.userid = '39e6e174-a5a9-b926-1036-4990995d1d11'
#         application_id = '39e6a471-141a-4659-3ee1-74f15994d474'
#         list_func = True
#         app = application_service.get_application_and_sub(application_id, list_func)
#         print(app)
#
#
# if __name__ == '__main__':
#     unittest.main()
