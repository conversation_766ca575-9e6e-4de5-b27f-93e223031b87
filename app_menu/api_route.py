#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/6/28.
"""
import hug
from app_menu.models import (
    ApplicationModel,
    ApplicationQueryModel,
    ApplicationRankModel,
    FunctionModel,
    FunctionIconModel,
    MoveCollectionModel
)
from app_menu.services import application_service, function_service, function_collection_service
from dmplib.hug import APIWrapper, g
from dmplib.utils.errors import UserError
from rbac.validator import PermissionValidator
from user.api_route import data_api
from user_log.models import UserLogModel
from components.common_admin_route import CommonProxyAPIWrapper
from components.mip_auth_application import ApplicationOperation
from components.versioned_query import versioned_query_support

api = APIWrapper(__name__)
common_admin_api = CommonProxyAPIWrapper(__name__)


@common_admin_api.admin_with_third_party_route.get('/app/get')
@versioned_query_support
def get_application(request, response, **kwargs):
    from app_menu.services.snapshot_application_service import get_snapshot_filter_config
    """
    获取应用
    :param kwargs:
    :return:
    """
    token = request.cookies.get('token')
    # 添加是否使用角色cache开关
    ignore_close_cache_config = kwargs.get("ignore_close_cache_config", 0)
    setattr(g, 'ignore_close_cache_config', ignore_close_cache_config)
    is_from_third_party = application_service.check_from_application_third_party(token, kwargs)
    if not is_from_third_party:
        result = application_service.get_application_and_sub(kwargs.get('id'), kwargs.get('list_func'), is_release=True)
    else:
        result = application_service.get_application_and_sub_without_permission(
            kwargs.get('id'), kwargs.get('list_func'), is_release=True
        )
    if hasattr(g, 'snap_id'):
        filter_config = get_snapshot_filter_config(g.snap_id)
        result.filter_config = filter_config
    return True, '', result


@api.admin_route.get('/app/design/get', validate=PermissionValidator('app-site.view'))
def get_design_application(**kwargs):
    """
    :param kwargs:
    :return:
    """
    result = application_service.get_application_and_sub_without_permission(kwargs.get('id'), kwargs.get('list_func'))
    return True, '', result


@common_admin_api.admin_with_third_party_route.get('/app/get_relation')
def get_application_relation(request, **kwargs):
    """
    获取门户的关联门户信息
    :param request:
    :param kwargs:
    :return:
    """
    app_id = kwargs.get('id')
    if not app_id:
        raise UserError(message="应用门户不存在")
    return True, '', application_service.get_application_relation_info(kwargs.get('id'), request.user_agent)


@api.admin_route.get('/app/check')
def check_app_permission(**kwargs):
    """
    检测是否有权限查看该资源
    :param dict kwargs:
    :return:
    """
    action_code = kwargs.get('action_code')
    if action_code not in ['view', 'edit']:
        raise UserError(message=u'action_code 只支持view, edit')
    if action_code == 'view':
        application_service.check_can_view(kwargs.get('id'))
    else:
        application_service.check_can_edit(kwargs.get('id'))
    return True, None, kwargs.get('id')


@api.admin_route.get('/app/list', validate=PermissionValidator('app-site.view'))
def get_application_list(request, **kwargs):
    """
    /*
    @apiVersion 1.0.6
    @api {get} /api/app/list 应用门户列表
    @apiGroup  app_menu
    @apiParam query {number}  page 页码
    @apiParam query {number}  page_size 每页条数
    @apiParam query {number}  enable 门户启用状态
    @apiParam query {string}  begin_time 开始时间
    @apiParam query {string}  end_time 结束时间
    @apiParam query {number}  skip_paging_flag 是否跳过分页数据返回
    @apiResponse  200 {
            "result": true,
            "msg": "ok",
            "data": {
                "items": [
                    {
                        "name": "跳转验证",
                        "created_by": "lyy",
                        "platform": "mobile",
                        "icon": "",
                        "rank": 5,
                        "id": "39ea2c80-e63b-7a3b-f154-824baf6dd214",
                        "actions": [
                            {
                                "action_name": "编辑",
                                "action_code": "edit"
                            }
                        ],
                        "is_buildin": 0,
                        "enable": 1,
                        "url": "",
                        "target": "",
                        "description": "",
                        "theme{主题}": "深色",
                        "created_on": ""
                    }
                ],
                "total": 120
            }
        }
    */
    """
    UserLogModel.log_setting(request=request, log_data={'action': 'list_app', 'content': '查看应用门户列表'})
    return True, None, application_service.get_application_list(ApplicationQueryModel(**kwargs))


@api.admin_route.post('/app/add', validate=PermissionValidator('app-site.edit'))
def add_application(request, **kwargs):
    """
    /**
    @apiVersion 1.0.4
    @api {post} /api/app_menu/app/add 添加应用
    @apiGroup app_menu
    @apiBodyParam {
        "platform{平台类型}": "pc",
        "nav_type{导航方式 0底部导航 1九宫格导航 2列表导航 3多级宫格导航}": 0,
        "name{应用名称}": "应用名称",
        "description{应用描述}": "应用描述",
        "theme{主题：深色、浅色}": "浅色"
    }
    @apiResponse  200 {
        "result{成功}": true,
        "data{新建应用ID}": "39ebf7cd-522f-8359-c8b8-2d4d16e7b7be",
        "msg{消息}": "添加成功"
    }
    **/
    """
    model = ApplicationModel(**kwargs)
    application_service.add_application(model)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'add_app',
            'id': model.id,
            'content': '添加应用门户 [ {app_name} ] 成功'.format(app_name=model.name),
        },
    )

    return True, '添加成功', model.id


@api.admin_route.post('/app/update')
def update_application(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/app_menu/app/update 更新应用
    @apiGroup app_menu
    @apiBodyParam {
        "platform{平台类型}": "pc",
        "name{应用名称}": "应用名称",
        "description{应用描述}": "应用描述",
        "theme{主题：深色、浅色}": "浅色",
        "enable{可用}": 1,
        "guide_img{指引图片列表}": ["http://a.com/b.png"],
        "icon{图标}": "",
        "use_guide{使用指引}":0,
        "target{目标}":"",
        "url{链接}":"http://www.google.com/"
    }
    @apiResponse  200 {
        "result{成功}": true,
        "data{新建应用ID}": 1,
        "msg{消息}": "修改成功"
    }
    **/
    """
    model = ApplicationModel(**kwargs)
    re = application_service.update_application(model)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_app',
            'id': kwargs.get('id'),
            'content': '应用门户 [ {app_name} ] 更新成功'.format(app_name=model.name),
        },
    )
    return True, '修改成功', re


@api.admin_route.post('/app/delete')
def delete_application(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/app_menu/app/delete 删除门户
    @apiGroup app_menu
    @apiBodyParam {
        "id": "39e7293c-ac45-f084-9e5d-cd08ea51ca06"
    }
    @apiResponse  200 {
        "data": 1,
        "msg": "删除成功",
        "result": true
    }
    **/
    """
    application = application_service.get_application_info(kwargs.get('id'))
    if not application:
        raise UserError(message='应用不存在')
    if application.get('is_buildin'):
        raise UserError(message='内置应用不允许删除')
    if application.get('is_system'):
        raise UserError(message='系统门户不允许删除')
    re = application_service.delete_application(kwargs.get('id'))
    # 存在关联门户，清除数据
    if application.get('relation_id'):
        application_service.clear_application_relation(application.get('relation_id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_app',
            'id': kwargs.get('id'),
            'content': '应用门户 [ {app_name} ] 删除成功'.format(app_name=application.get('name')),
        },
    )
    # 门户删除
    ApplicationOperation(application_id=kwargs.get('id')).upload_status_to_mip(operation_type=3, application=application)
    return True, '删除成功', re


@api.admin_route.post('/app/enable', validate=PermissionValidator('app-site.edit'))
def enable_application(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/app_menu/app/enable 启用门户
    @apiGroup app_menu
    @apiBodyParam {
        "id": "39e7293c-ac45-f084-9e5d-cd08ea51ca06"
    }
    @apiResponse  200 {
        "data": 1,
        "msg": "启用成功",
        "result": true
    }
    **/
    """
    application = application_service.get_application_info(kwargs.get('id'))
    if not application:
        raise UserError(message='应用不存在')
    re = application_service.enable_application_and_sync_mip(kwargs.get('id'), kwargs.get('type_access_released'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'enable_app',
            'id': kwargs.get('id'),
            'content': '应用门户 [ {app_name} ] 发布成功'.format(app_name=application.get('name')),
        },
    )
    return True, '发布成功', re


@api.admin_route.post('/app/disable')
def disable_application(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/app_menu/app/disable 禁用门户
    @apiGroup app_menu
    @apiBodyParam {
        "id": "39e7293c-ac45-f084-9e5d-cd08ea51ca06"
    }
    @apiResponse  200 {
        "data": 1,
        "msg": "禁用成功",
        "result": true
    }
    **/
    """
    application = application_service.get_application_info(kwargs.get('id'))
    if not application:
        raise UserError(message='应用不存在')
    re = application_service.disable_application(kwargs.get('id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'disable_app',
            'id': kwargs.get('id'),
            'content': '应用门户 [ {app_name} ] 取消发布'.format(app_name=application.get('name')),
        },
    )
    # 门户禁用
    ApplicationOperation(application_id=kwargs.get('id')).upload_status_to_mip(operation_type=2)
    return True, '取消发布', re


@api.admin_route.post('/app/update_rank', validate=PermissionValidator('app-site.edit'))
def update_application_rank(request, **kwargs):
    """
    修改应用排序
    :param kwargs:
    :return:
    """
    model = ApplicationRankModel(**kwargs)
    re = application_service.update_application_rank(model)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_app_rank',
            'id': kwargs.get('source_id'),
            'content': '修改门户 [ {app_id} ] 排序成功'.format(app_id=kwargs.get('source_id')),
        },
    )
    return True, '修改成功', re


@common_admin_api.admin_with_third_party_route.get('/app/link_to')
@versioned_query_support
def application_link_to(request, response, **kwargs):
    """
    外链菜单跳转
    :param request:
    :param kwargs:
    :return:
    """
    return application_service.application_link_to(request, kwargs.get('id'), False, True)


@api.admin_route.get('/app/design/link_to')
def application_link_to(request, response, **kwargs):
    """
    外链菜单跳转
    :param request:
    :param kwargs:
    :return:
    """
    return application_service.application_link_to(request, kwargs.get('id'), False)


@api.admin_route.get('/app/get_redirect_url')
def get_redirect_url(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/app_menu/app/get_redirect_url 获取第三方自定义跳转url
    @apiParam query {string}  application_id 门户id
    @apiGroup  app_menu
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {
            "redirect_url": "https://dmp-test.mypaas.com.cn/app/index/39e7293c-ac45-f084-9e5d-cd08ea51ca06"
        }
    }
    **/
    """
    redirect_url = application_service.get_custom_redirect_url(kwargs.get("application_id"))
    return True, "", {"redirect_url": redirect_url}


@api.admin_route.post('/app/relation')
def relation_application(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/app_menu/app/relation 门户关联
    @apiGroup app_menu
    @apiBodyParam {
        "app_id{门户id}": "12123123",
        "relation_app_id{被关联门户id}": "2222222"
    }
    @apiResponse  200 {
        "result{成功}": true,
        "msg{消息}": "关联完成"
    }
    **/
    """
    app_id = kwargs.get("app_id")
    relation_app_id = kwargs.get("relation_app_id", "")
    result, msg = application_service.relation_application(app_id, relation_app_id)
    if not result:
        return result, msg
    return True, '关联完成'


@api.admin_route.get('/func/get')
def get_function(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {get} /api/app/func/get 获取功能菜单
    @apiParam query {string}  id 菜单ID
    @apiGroup  app_menu
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": {
            "application_id": "39ed371f-2c63-417b-76df-b983763d07b6",
            "guide_img": [],
            "icon": "dmpicon-cash",
            "id": "39ed3720-0fe6-b131-8a93-cda55cdacec6",
            "level_code": "0001-",
            "name": "一级菜单1",
            "parent_id": "",
            "target": "",
            "url": "",
            "theme{风格}": "深色",
            "custom_icons": []
        }
    }
    **/
    """
    return True, '', function_service.get_function(kwargs.get('id'))


@api.admin_route.get('/func/tree')
def get_function_tree(**kwargs):
    """
    获取功能菜单树
    :param kwargs:
    :return:
    """
    return True, '', function_service.get_function_tree(kwargs.get('application_id'))


@api.admin_route.post('/func/add')
def add_function(request, **kwargs):
    """
    /*
    @apiVersion 1.0.2
    @api {post} /api/app_menu/func/add 添加门户菜单
    @apiGroup  app_menu
    @apiBodyParam {
            "application_id{门户ID}": "39e97950-ce2f-6f2c-bc25-1145946c62b5",
            "name{名称}": "一级菜单12",
            "icon{图标}": "dmpicon-cash",
            "target{目标}": "xx-xx",
            "url{链接}": "https://a.com"
        }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    */
    """
    model = FunctionModel(**kwargs)
    function_service.add_function(model)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'add_app_menu',
            'id': model.id,
            'content': '应用门户菜单 [ {menu_name} ] 添加成功'.format(menu_name=model.name),
        },
    )
    return True, '添加成功', model.id


@api.admin_route.post('/func/update')
def update_function(request, **kwargs):
    """
    /**
    @apiVersion 1.0.3
    @api {post} /api/app_menu/func/update 修改功能菜单
    @apiGroup app_menu
    @apiBodyParam {
        "application_id": "39ed371f-2c63-417b-76df-b983763d07b6",
        "guide_img": [],
        "icon": "dmpicon-helmet",
        "id": "39ed3720-0fe6-b131-8a93-cda55cdacec6",
        "level_code": "0001-",
        "name": "一级菜单1",
        "parent_id": "",
        "target": "",
        "url": "39ec39d9-4c48-a6d2-ab38-78ec94540740",
        "icon_url": "http://xxx.com.cn/xx/xx"
    }
    @apiResponse  200 {
        "data": 1,
        "msg": "修改成功",
        "result": true
    }
    **/
    """
    model = FunctionModel(**kwargs)
    re = function_service.update_function(model)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_app_menu',
            'id': model.id,
            'content': '应用门户菜单 [ {menu_name} ] 更新成功'.format(menu_name=model.name),
        },
    )
    return True, '修改成功', re


@api.admin_route.post('/func/update_rank')
def update_function_rank(request, **kwargs):
    re = function_service.update_function_rank(kwargs.get('source_id'), kwargs.get('target_id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_app_menu_rank',
            'id': kwargs.get('source_id'),
            'content': '更新应用门户菜单排 [ {menu_id} ] 序成功'.format(menu_id=kwargs.get('source_id')),
        },
    )
    return True, '修改成功', re


@api.admin_route.post('/func/delete')
def delete_function(request, **kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/app_menu/func/delete 删除功能菜单
    @apiGroup app_menu
    @apiBodyParam {
        "id": "39ed3720-0fe6-b131-8a93-cda55cdacec6"
    }
    @apiResponse  200 {
        "data": 1,
        "msg": "删除成功",
        "result": true
    }
    **/
    """
    func_model = function_service.get_function(kwargs.get('id'))
    re = function_service.delete_function(func_model)
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_app_menu',
            'id': kwargs.get('id'),
            'content': '删除菜单 [ {name} ] 成功'.format(name=func_model.name),
        },
    )
    return True, '删除成功', re


@common_admin_api.admin_with_third_party_route.get('/func/link_to')
@versioned_query_support
def function_link_to(request, response, **kwargs):
    """
    外链菜单跳转
    :param request:
    :param kwargs:
    :return:
    """
    return function_service.function_link_to(request, response, kwargs.get('id'), is_release=True)


@api.admin_route.get('/func/design/link_to')
def function_design_link_to(request, response, **kwargs):
    """
    外链菜单跳转
    :param request:
    :param kwargs:
    :return:
    """
    return function_service.function_link_to(request, response, kwargs.get('id'))


@api.admin_route.delete('/application_guide_log/{application_id}', validate=PermissionValidator('app-site.edit'))
def delete_application_guide_user_log(application_id, request):
    """
    /*
    @apiVersion 1.0.1
    @api {delete} /api/app_menu/application_guide_log/ 删除门户指引浏览记录
    @apiGroup  app_menu
    @apiBodyParam {
        "application_id": "门户ID"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    */
    """
    application_service.delete_application_guide_user_log(application_id)
    UserLogModel.log_setting(
        request=request, log_data={'action': 'application_guide_log', 'id': application_id, 'content': '删除应用门户指引记录成功'}
    )
    return True, '删除成功'


@common_admin_api.admin_with_third_party_route.get('/application_guide_log/{application_id}')
def get_application_guide_user_log(application_id, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/app_menu/v2/application_guide_log 获取指引是否被浏览过
    @apiParam query {string}  application_id 门户ID
    @apiGroup  dashboard_chart
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": true
    }
    **/
    """
    result = application_service.get_application_guide_user_log(application_id, kwargs.get('function_id'))
    return True, '', result


@common_admin_api.admin_with_third_party_route.post('/application_guide_log/close')
def close_guild(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/app_menu/application_guide_log/close 关闭当前用户的应用门户指引
    @apiGroup  app_menu
    @apiBodyParam {
        "application_id{门户id}": "门户ID"
    }
    @apiResponse  200 {
        "msg": "",
        "result": true,
        "data": true
    }
    **/
    """
    return True, '', application_service.close_guild(kwargs.get('application_id'))


@api.admin_route.post('/func_icon/add')
def add_custom_icon(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/app_menu/func_icon/add 添加用户自定义图标
    @apiGroup  app_menu
    @apiBodyParam {
            "name{图标名称}": "icon name",
            "description{描述}": "描述",
            "icon_url{图标地址}": "http://xxx.com.cn/xx/xx"
        }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {"icon_id": "39e70a85-f414-ec93-a5e2-ef6898d99739"}
        }
    */
    """
    icon_id = function_service.add_custom_icon(FunctionIconModel(**kwargs))
    return True, '添加成功', {"icon_id": icon_id}


@common_admin_api.admin_with_third_party_route.post('/function/collect')
def function_collection(**kwargs):
    """
    收藏门户菜单
    :param kwargs:
    :return:
    """
    function_id = kwargs.get('function_id')
    application_id = kwargs.get('application_id')
    flag = kwargs.get('flag')
    if flag == 'add':
        function_collection_service.save_func_collection(application_id, function_id)
        msg = '收藏成功'
    elif flag == 'remove':
        function_collection_service.remove_func_collection(application_id, function_id)
        msg = '取消收藏成功'
    else:
        return False, '不支持的flag类型', ''
    return True, msg, ''


@common_admin_api.admin_with_third_party_route.get('/get_function_collect')
def function_collection(request, **kwargs):
    """
    获取移动端收藏门户菜单列表
    :return:
    """
    application_id = kwargs.get('id')
    if not application_id:
        return False, '门户ID不能为空', ''
    token = request.cookies.get('token')
    is_from_third_party = application_service.check_from_application_third_party(token, kwargs)
    if not is_from_third_party:
        auth_app = application_service.get_application_and_sub(application_id, 1, is_release=True)
    else:
        auth_app = application_service.get_application_and_sub_without_permission(application_id, 1, is_release=True)
    function_tree = auth_app.function or []
    function_ids = function_collection_service.get_auth_function_ids_by_tree(function_tree) or []
    result = function_collection_service.get_function_collection_by_function_ids(function_ids)
    return True, '', result


@common_admin_api.admin_with_third_party_route.get('/get_user_function_collect')
def function_collection(request, **kwargs):
    return True, '', function_collection_service.get_user_function_collection_by_function_ids(request, kwargs)


@common_admin_api.admin_with_third_party_route.post('/move_function_collect')
def move_function_collection(**kwargs):
    model = MoveCollectionModel(**kwargs)
    if not model.function_id or not model.application_id:
        return False, '缺失参数，请重试', ''
    return True, '移动成功', function_collection_service.move_function_collection(model)


@api.admin_route.post('/snapshot_application')
def snapshot_application(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/app_menu/snapshot_application 门户快照
    @apiGroup  app_menu
    @apiBodyParam {
        "application_id": "门户ID"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    **/
    """
    from app_menu.services.snapshot_application_service import snapshot_application
    application_id = kwargs.get('application_id')
    if not application_id:
        return False, '门户ID不能为空', ''
    title = kwargs.get('title')
    filter_config = kwargs.get('filter_config', '')
    return True, '门户拍照成功', snapshot_application(application_id, title, g.code, filter_config)


@api.admin_route.get('/delete_snapshot_application')
def delete_snapshot_application(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/app_menu/snapshot_application 门户快照删除
    @apiGroup  app_menu
    @apiBodyParam {
        "snap_id": "拍照ID"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    **/
    """
    from app_menu.services.snapshot_application_service import delete_snapshot_application
    snap_id = kwargs.get('snap_id')
    delete_snapshot_application(snap_id)
    return True, '门户拍照删除成功', ''


@api.admin_route.get('/get_snapshot_application_list')
def get_snapshot_application_list(**kwargs):
    from app_menu.services.snapshot_application_service import get_snapshot_application_list
    application_id = kwargs.get('application_id')
    if not application_id:
        return False, '门户ID不能为空', ''
    return True, '', get_snapshot_application_list(application_id)


@api.admin_route.get('/snapshot_rename')
def snapshot_rename(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/app_menu/snapshot_rename 门户快照重命名
    @apiGroup  app_menu
    @apiBodyParam {
        "snap_id": "拍照ID",
        "snap_name": "拍照名称"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": ""
        }
    **/
    """
    from app_menu.services.snapshot_application_service import snapshot_rename
    snap_id = kwargs.get('snap_id')
    title = kwargs.get('title')
    if not snap_id or not title:
        return False, '参数错误', ''
    return True, '修改成功', snapshot_rename(snap_id, title)


@api.admin_route.get('/get_global_params')
def get_global_params(**kwargs):
    application_id = kwargs.get('application_id')
    if not application_id:
        return False, '门户ID不能为空', ''
    return True, '', application_service.get_global_params(application_id)


@api.admin_route.post('/application/save_config')
def save_config(**kwargs):
    application_config = kwargs.get('application_config')
    if not application_config:
        return False, '门户配置不能为空', ''
    application_config_type = kwargs.get('application_config_type')
    if not application_config_type:
        return False, '门户配置类型不能为空', ''
    return True, '', application_service.save_application_config(application_config, application_config_type)


@common_admin_api.admin_with_third_party_route.get('/application/get_config')
def get_config(**kwargs):
    application_config_type = kwargs.get('application_config_type')
    if not application_config_type:
        return False, '门户配置类型不能为空', ''
    return True, '', application_service.get_application_config_by_config_type(application_config_type)


@api.admin_route.get('/get_token_url')
def get_token_url(**kwargs):
    from app_menu.services.external_service import get_token_url
    app_id = kwargs.get('app_id')
    if not app_id:
        return False, '门户ID不能为空', ''
    redirect = '/dataview-mobile/portal_preview/{}'.format(app_id)
    url = get_token_url(redirect)
    return True, '', url


@api.admin_route.get('/set_application_lock_status')
def set_dashboard_lock_status(**kwargs):
    from app_menu.services.external_service import set_app_distribute_type
    app_id = kwargs.get('id')
    try:
        lock_status = int(kwargs.get('status', 0))
    except ValueError:
        lock_status = 0
    if not app_id:
        return False, '门户ID不能为空', ''
    return True, '', set_app_distribute_type(app_id, lock_status)


@api.admin_route.get('/init_sync_app_task')
def init_sync_app_task(request, response, **kwargs):
    application_service.init_app_sync_task()
    return True, '', {}


@api.admin_route.get('/rdc_template')
def init_sync_app_task(request, response, **kwargs):
    data = application_service.list_rdc_template(**kwargs)
    return True, '', data


