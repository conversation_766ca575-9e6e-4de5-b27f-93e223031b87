#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/6/28.
"""
from base.models import BaseModel, RankModel, QueryBaseModel


class ApplicationModel(BaseModel):
    __slots__ = [
        'id',
        'name',
        'platform',
        'description',
        'icon',
        'url',
        'target',
        'is_buildin',
        'rank',
        'enable',
        'function',
        'nav_type',
        'guide_img',
        'theme',
        'use_guide',
        'menu_display_type',
        'user_defined_style',
        'common_config',
        'collapse',
        'is_show_banner',
        'banner_url',
        'bottom_color',
        'relation_id',
        'is_cache',
        'source_from',
        'enable_snapshot',
        'enable_filter',
        'filter_config',
        'application_config',
        'modified_on'
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.description = ''
        self.icon = ''
        self.url = ''
        self.target = ''
        self.is_buildin = 0
        self.rank = None
        self.enable = 0
        self.platform = 'pc'
        self.function = []
        self.nav_type = 0
        self.guide_img = None
        self.use_guide = 0
        self.theme = ''
        self.menu_display_type = 0
        self.type_access_released = 0
        self.user_defined_style = ''
        self.collapse = 0
        self.is_show_banner = 0
        self.banner_url = ""
        self.bottom_color = ""
        self.relation_id = ""
        self.is_cache = 1
        self.common_config = ""
        self.report_type = 0
        self.source_from = ""
        self.enable_snapshot = 0
        self.enable_filter = 0
        self.filter_config = ''
        self.application_config = ''
        self.modified_on = None
        self.created_by = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 254}))
        rules.append(('platform', 'in_range', {'range': ['mobile', 'pc', 'tv', 'mobile_screen']}))
        rules.append((['icon', 'url', 'target'], 'string', {'max': 1024, 'required': False}))
        rules.append(('description', 'string', {'max': 1000, 'required': False}))
        rules.append(('nav_type', 'in_range', {'range': range(6)}))
        return rules


class ApplicationRankModel(RankModel):
    def __init__(self, **kwargs):
        """
        应用排序
        :param kwargs:
        """
        super().__init__(**kwargs)
        self.table_name = 'application'
        self.rank_col_name = 'rank'
        self.id_col_name = 'id'


class ApplicationQueryModel(QueryBaseModel):
    __slots__ = ['is_buildin', 'enable', 'begin_time', 'end_time', 'skip_paging_flag']

    def __init__(self, **kwargs) -> None:
        self.is_buildin = 0
        self.enable = None
        self.begin_time = None
        self.end_time = None
        self.skip_paging_flag = None
        super().__init__(**kwargs)


class FunctionModel(BaseModel):
    __slots__ = [
        'id',
        'name',
        'parent_id',
        'level_code',
        'icon',
        'url',
        'target',
        'application_id',
        'description',
        'guide_img',
        'icon_url',
        'custom_icons',
        'report_type'
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.name = ''
        self.parent_id = ''
        self.level_code = ''
        self.icon = ''
        self.url = ''
        self.target = ''
        self.application_id = ''
        self.description = ''
        self.guide_img = None
        self.icon_url = ''
        self.custom_icons = []
        # 挂载的报告类型，0：数见，1：HighData，2：老移动报表, 3: 统计报表，4: 报表中心 5: erp报表
        self.report_type = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['id', 'application_id'], 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 254}))
        return rules


class FunctionIconModel(BaseModel):
    __slots__ = ["id", "name", "description", "icon_url", "icon_type", "status"]

    def __init__(self, **kwargs):
        self.id = ""
        self.name = ""
        self.description = ""
        self.icon_url = ""
        self.icon_type = 0
        self.status = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append((["name", "icon_url"], "string", {"max": 1024, "required": True}))

        return rules


class ApplicationOpenAPIQueryModel(QueryBaseModel):
    __slots__ = ['application_ids', 'enable', 'platform', 'created_by', 'nav_type', 'is_buildin']

    def __init__(self, **kwargs):
        self.application_ids = None
        self.platform = None
        self.enable = None
        self.nav_type = None
        self.created_by = None
        self.is_buildin = 0

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('application_ids', 'array', {'required': False}))
        rules.append(('platform', 'in_range', {'range': ['tv', 'pc', 'mobile', 'mobile_screen'], 'required': False}))
        rules.append(('enable', 'in_range', {'range': [0, 1, '0', '1'], 'required': False}))
        rules.append(('nav_type', 'in_range', {'range': [0, 1, 2, 3, 4, 5, '0', '1', '2', '3', '4', '5'], 'required': False}))
        return rules


class ApplicationConfigModel(BaseModel):
    __table__ = 'application_config'

    def __init__(self, **kwargs):
        self.application_config = None
        self.application_config_type = None
        super().__init__(**kwargs)


class MoveCollectionModel(BaseModel):

    def __init__(self, **kwargs):
        self.function_id = None
        self.application_id = None
        self.target_id = None
        self.application_target_id = None
        self.sort_field = 'sort'
        super().__init__(**kwargs)
