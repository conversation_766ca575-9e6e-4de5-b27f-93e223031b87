#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/6/29.
"""
from operator import itemgetter

from dmplib.saas.project import get_db
from dmplib.db import mysql_wrapper

from base import repository
import json

from rbac.repositories.func_repository import get_func_table


def get_function_list(application_id, is_release=False):
    """
    获取功能菜单列表
    :param str application_id:
    :param bool is_release:
    :return list:
    """
    func_table = get_func_table("`function`")
    sql_for_config = f"""
        select `id`,`name`,`parent_id`,`level_code`,`icon`,`icon_url`,`url`,`target`,`application_id`,`created_by`  
        from {func_table} where application_id=%(application_id)s
        order by level_code
    """
    table = 'release_function' if is_release else 'function'
    sql_for_tenant = """
        select f.`id`,f.`name`,f.`parent_id`,f.`level_code`,f.`icon`,f.`icon_url`, f.`description`,
        f.`url`,f.`target`,f.`application_id`,f.`report_type`,f.`created_by`,d.`background` as dashboard_background,d.application_type
        from `{}` f LEFT JOIN `dashboard` d ON f.url=d.id where f.application_id=%(application_id)s
        order by f.level_code
    """.format(table)

    config_db = mysql_wrapper.get_db()
    params = {'application_id': application_id}
    funcs_from_config = config_db.query(sql_for_config, params) or []
    tenatn_db = get_db()
    funcs_from_tenant = tenatn_db.query(sql_for_tenant, params) or []
    funcs = funcs_from_config + funcs_from_tenant
    funcs = sorted(funcs, key=itemgetter('level_code'), reverse=False)
    _funcs_field_to_loads(funcs)
    return funcs


def _funcs_field_to_loads(funcs):
    """
    处理需要反序列化的字段
    :param funcs:
    :return:
    """
    for f in funcs:
        if "dashboard_background" not in f.keys():
            f["dashboard_background"] = {}
        if f.get("dashboard_background"):
            try:
                f["dashboard_background"] = json.loads(f.get("dashboard_background"))
            except Exception:
                pass


def delete_function(function_id):
    """
    删除功能菜单
    :param str function_id:
    :return int:
    """
    sql = 'delete from `function` where id=%(function_id)s or parent_id =%(function_id)s '
    with get_db() as db:
        return db.exec_sql(sql, {'function_id': function_id})


def delete_function_by_id_list(function_id_list):
    """
    按菜单id删除功能菜单
    :param str function_id_list:
    :return int:
    """
    if not function_id_list:
        return False
    return repository.delete_data('function', {'id': function_id_list})


def delete_function_by_application_id(application_id):
    """
    删除应用下所有功能菜单
    :param str application_id:
    :return int:
    """
    sql = 'delete from `function` where application_id=%(application_id)s '
    with get_db() as db:
        return db.exec_sql(sql, {'application_id': application_id})


def update_function_level_code(application_id, old_code, new_code):
    """
    更新层级code
    :param str application_id:
    :param str old_code:
    :param str new_code:
    :return:
    """
    # 查找原始节点的所有子节点id
    source_id_list = get_function_by_level_code(application_id, old_code)
    sql = (
        'update `function` '
        'set level_code = CONCAT(%(new_code)s,substr(level_code,LENGTH(%(old_code)s)+1)) '
        'WHERE application_id=%(application_id)s and level_code LIKE %(level_code)s'
    )
    with get_db() as db:
        record = db.exec_sql(
            sql,
            {
                'application_id': application_id,
                'old_code': old_code,
                'new_code': new_code,
                'level_code': old_code + '%',
            },
        )
    if old_code < new_code:
        sql = (
            'update `function` '
            'set level_code=CONCAT('
            'substring(level_code,1,LENGTH(%(old_code)s)-5),'
            'lpad(convert(substring(substring(level_code,1,LENGTH(%(old_code)s)),-5,4),signed)-1,4,\'0\'),'
            '\'-\','
            'substring(level_code,LENGTH(%(old_code)s)+1)) '
            'WHERE substring(level_code,1,LENGTH(%(old_code)s))> %(old_code)s '
            'AND substring(level_code,1,LENGTH(%(old_code)s))<=%(new_code)s'
        )
    else:
        sql = (
            'update `function` '
            'set level_code=CONCAT('
            'substring(level_code,1,LENGTH(%(old_code)s)-5),'
            'lpad(convert(substring(substring(level_code,1,LENGTH(%(old_code)s)),-5,4),signed)+1,4,\'0\'),'
            '\'-\','
            'substring(level_code,LENGTH(%(old_code)s)+1)) '
            'WHERE substring(level_code,1,LENGTH(%(old_code)s)) >= %(new_code)s '
            'AND substring(level_code,1,LENGTH(%(old_code)s))<=%(old_code)s'
        )
    sql += (
        ' and id not in %(id)s and parent_id not in %(id)s and application_id=%(application_id)s '
        ' and level_code like CONCAT( substring(%(new_code)s,1,LENGTH(%(new_code)s)-5),\'%%\')'
    )
    params = {'id': source_id_list, 'application_id': application_id, 'old_code': old_code, 'new_code': new_code}
    with get_db() as db:
        record += db.exec_sql(sql, params)
    return record


def get_function_ids_by_application_id(application_id: str, is_release=False):
    table = 'release_function' if is_release else '`function`'
    return repository.get_column(table, {'application_id': application_id}, 'id')


def get_function_by_level_code(application_id: str, level_code: str):
    return repository.get_column('`function`',
                                 {'application_id': application_id, "level_code like": level_code + '%'}, 'id')


def get_function_id_by_id(func_id: str):
    return repository.get_column('function', {'id': func_id}, 'url')


def get_function_ids_by_urls(urls: list):
    if not urls:
        return []
    return repository.get_list('function', conditions={'url': urls}) or []
