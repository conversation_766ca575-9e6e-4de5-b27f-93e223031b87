from dmplib.saas.project import get_db

from dmplib.utils.strings import seq_id
from base import repository
from app_menu.repositories import application_guide_user_log_repository
from app_menu.repositories.application_guide_user_log_repository import where_for_application_and_function_id

# 定义操作的数据库表名
TABLE = 'application_guide_img'


def get_img_list_by_application_and_function_id(application_id, function_id):
    """通过application_id 和 function_id 取指引列表"""
    sql = """
    select url from `%s` %s
    order by `rank` ASC
    """ % (
        TABLE,
        where_for_application_and_function_id(function_id),
    )
    img_list = get_db().query(sql, {"application_id": application_id, "function_id": function_id})
    return _get_url(img_list)


def get_img_list_by_application_id_for_function(application_id):
    """获取菜单下的指引"""
    sql = (
        """
    select function_id, url
    from `%s`
    where application_id=%%(application_id)s and function_id is not NULL
    order by `rank` ASC
    """
        % TABLE
    )
    return get_db().query(sql, {'application_id': application_id})


def get_img_list_by_function_id(function_id):
    sql = (
        """
        select url from `%s`
        where function_id=%%(function_id)s
        order by `rank` ASC
    """
        % TABLE
    )
    img_list = get_db().query(sql, {"function_id": function_id})
    return _get_url(img_list)


def update_img_list(application_id, function_id, img_list):
    """更新指引列表"""
    delete_by_application_and_function_id(application_id, function_id)
    if img_list:
        data_list = []
        for rank, url in enumerate(img_list):
            data_list.append(
                {'id': seq_id(), 'application_id': application_id, "function_id": function_id, 'url': url, 'rank': rank}
            )
        return get_db().insert_multi_data(TABLE, data_list, list(data_list[0].keys()))
    else:
        # 没有指引的时候把记录也删除了
        application_guide_user_log_repository.delete_log_by_application_and_function_id(application_id, function_id)
    return 0


def delete_by_application_id(application_id):
    return repository.delete_data(TABLE, {"application_id": application_id})


def delete_by_application_and_function_id(application_id=None, function_id=None):
    sql = 'DELETE FROM `%s`' % TABLE
    sql += where_for_application_and_function_id(function_id)
    return get_db().exec_sql(sql, {"application_id": application_id, "function_id": function_id})


def delete_by_function_id(function_id):
    return repository.delete_data(TABLE, {"function_id": function_id})


def _get_url(img_list):
    if img_list:
        return [i['url'] for i in img_list]
    else:
        return []
