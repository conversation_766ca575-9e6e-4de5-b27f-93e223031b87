from base import repository
from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id

TABLE = 'application_guide_user_log'


def exist_log(account: str, application_id: str = None, function_id=None):
    """是否存在浏览记录"""
    sql = "SELECT id FROM `%s` WHERE account=%%(account)s AND function_id" % TABLE
    params = {"account": account, "function_id": function_id}
    if function_id:
        sql += '=%(function_id)s'
    else:
        sql += ' is %(function_id)s'
    if application_id:
        # 查询中可以不包括application_id
        sql += ' AND application_id=%(application_id)s'
        params["application_id"] = application_id
    return bool(get_db().query(sql, params))


def add_log(account: str, application_id: str, function_id: str):
    """增加浏览记录"""
    return repository.add_data(
        TABLE, {"id": seq_id(), "application_id": application_id, "function_id": function_id, "account": account}
    )


def add_logs(logs: list):
    if not logs:
        return False
    for log in logs:
        log['id'] = seq_id()
    return repository.replace_list_data(TABLE, logs, ['id', 'application_id', 'function_id', 'account'])


def delete_log_by_application_id(application_id: str):
    """删除浏览记录"""
    return repository.delete_data(TABLE, {"application_id": application_id})


def delete_log_by_function_id(function_id: str):
    """删除浏览记录"""
    return repository.delete_data(TABLE, {"function_id": function_id})


def delete_log_by_application_and_function_id(application_id: str, function_id: str):
    where = where_for_application_and_function_id(function_id)
    sql = 'delete from `{}` {}'.format(TABLE, where)
    return get_db().exec_sql(sql, {"application_id": application_id, "function_id": function_id})


def where_for_application_and_function_id(function_id):
    operator = '='
    if function_id is None:
        operator = ' IS '
    return ' WHERE application_id=%(application_id)s and function_id{}%(function_id)s'.format(operator)
