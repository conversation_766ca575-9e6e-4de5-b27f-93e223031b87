#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
external application repository
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from base import repository
from dmplib.saas.project import get_db
from app_menu.models import ApplicationOpenAPIQueryModel


def get_application_list_by_query_model(query_model: ApplicationOpenAPIQueryModel, is_release=False):
    """
    获取门户数据
    """
    if not isinstance(query_model, ApplicationOpenAPIQueryModel):
        return []
    app_table = 'release_application' if is_release else 'application'

    sql = '''select id,name,platform,nav_type,description,target,`rank`,enable,
            icon,type_access_released,created_on,created_by from {} '''.format(app_table)
    params = {}
    wheres = []
    query_model.validate()
    if query_model.application_ids:
        wheres.append('id in %(application_ids)s')
        params['application_ids'] = query_model.application_ids
    if query_model.platform:
        wheres.append('platform = %(platform)s')
        params['platform'] = query_model.platform
    if query_model.enable is not None:
        if isinstance(query_model.enable, str):
            query_model.enable = query_model.enable.split(",")
        if isinstance(query_model.enable, list):
            query_model.enable = [int(i) for i in query_model.enable]
            wheres.append('`enable` in %(enable)s')
        else:
            wheres.append('`enable` = %(enable)s')
        params['enable'] = query_model.enable
    if query_model.nav_type is not None:
        wheres.append('nav_type = %(nav_type)s')
        params['nav_type'] = query_model.nav_type
    if query_model.created_by:
        wheres.append('created_by = %(created_by)s')
        params['created_by'] = query_model.created_by
    if query_model.is_buildin is not None:
        wheres.append('is_buildin=%(is_buildin)s')
        params['is_buildin'] = query_model.is_buildin

    sql += ('WHERE ' + ' AND '.join(wheres)) if wheres else ''
    sql += ' order by `rank`'
    with get_db() as db:
        return db.query(sql, params)


def get_user_create_application_ids(user_account, is_release=False):
    app_table = 'release_application' if is_release else 'application'
    sql = '''select id from {} where created_by = %(user_account)s'''.format(app_table)
    params = {'user_account': user_account}
    with get_db() as db:
        return db.query_columns(sql, params)


def batch_get_urls_by_application_ids(application_ids, is_release=False):
    """
    批量获取菜单关联的url
    :param list application_ids:
    :param bool is_release:
    :return int:
    """
    func_table = 'release_function' if is_release else 'function'
    # 还要查询单门户的挂接数据
    app_table = 'release_application' if is_release else 'application'
    if not application_ids:
        return []
    sql = 'SELECT `url` FROM `{}` WHERE application_id IN %(application_ids)s '.format(func_table)
    sql_app = 'SELECT `url` FROM `{}` WHERE id IN %(application_ids)s '.format(app_table)
    with get_db() as db:
        func_data = db.query(sql, {'application_ids': application_ids}) or []
        app_data = db.query(sql_app, {'application_ids': application_ids}) or []
        return func_data + app_data


def batch_get_application_info(application_ids, is_release=False):
    """
    获取门户信息
    :param application_ids:
    :param bool is_release:
    :return:
    """
    if not application_ids:
        return []
    app_table = 'release_application' if is_release else 'application'
    sql = """SELECT `id`,`name`,`created_on`,`modified_on` FROM `{}`
        WHERE id IN %(application_ids)s ORDER BY `created_on` DESC """.format(app_table)
    with get_db() as db:
        return db.query(sql, {'application_ids': application_ids})


def batch_get_all_applications(application_ids, is_release=False):
    """
    获取门户所有信息
    :param application_ids:
    :param bool is_release:
    :return:
    """
    if not application_ids:
        return []
    app_table = 'release_application' if is_release else 'application'
    sql = """SELECT * FROM `{}` WHERE id IN %(application_ids)s """.format(app_table)
    with get_db() as db:
        return db.query(sql, {'application_ids': application_ids})


def batch_get_all_application_guide_imgs(application_ids):
    """
    获取所有报告指引图
    :param application_ids:
    :return:
    """
    if not application_ids:
        return []
    sql = """SELECT * FROM `application_guide_img` WHERE application_id IN %(application_ids)s """
    with get_db() as db:
        return db.query(sql, {'application_ids': application_ids})


def batch_get_all_functions(application_ids, is_release=False):
    """
    获取所有报告指引图
    :param application_ids:
    :param bool is_release:
    :return:
    """
    if not application_ids:
        return []
    func_table = 'release_function' if is_release else 'function'
    sql = """SELECT * FROM `{}` WHERE application_id IN %(application_ids)s """.format(func_table)
    with get_db() as db:
        return db.query(sql, {'application_ids': application_ids})


def batch_get_all_function_icons():
    """
    获取所有菜单自定义icon
    :return:
    """
    return repository.get_data(table_name="function_icon", conditions=None, multi_row=True)
