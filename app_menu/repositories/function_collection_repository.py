#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from dmplib.hug import g
from base import repository
from dmplib.utils.errors import UserError
from dmplib.saas.project import get_db


def delete_collection_by_application_id(application_id):
    return repository.delete_data('function_collection', {'application_id': application_id})


def delete_collection_by_function_id(function_id):
    return repository.delete_data('function_collection', {'function_id': function_id})


def get_func_collection_by_application_id(application_id, account=None):
    if not account:
        account = g.account
    return repository.get_columns('function_collection', {'account': account, 'application_id': application_id}, 'function_id') or []


def get_function_collection_by_account(account, function_ids: list, sort_field):
    sql = """
    select a.function_id as `id`,a.`application_id`, c.`name` as app_name,a.`sort`,a.`rank`,b.`name`,b.`description`,b.`icon`,b.`icon_url`,b.`url`,b.`report_type`,d.`application_type` 
    from `function_collection` a INNER JOIN `release_function` b on a.`function_id` = b.`id` and a.`function_id` in {}
    INNER JOIN `release_application` c on a.`application_id` = c.`id` LEFT JOIN dashboard d on b.`url` = d.`id` 
    where a.`account`=%(account)s order by a.`{}`, a.`created_on`;
    """.format(tuple(function_ids), sort_field)
    params = {'account': account}
    with get_db() as db:
        result = db.query(sql, params) or []
        return result


def get_max_sort_by_application_id(application_id, account):
    sql = """select max(`sort`) as `sort` from `function_collection` where `application_id` = '{}' and `account` = '{}'""".format(application_id, account)
    with get_db() as db:
        sort = db.query_scalar(sql) or 0
        return sort + 1


def get_max_rank_by_account(account):
    sql = """select max(`rank`) as `rank` from `function_collection` where `account` = '{}'""".format(account)
    with get_db() as db:
        rank = db.query_scalar(sql) or 0
        return rank + 1


def update_function_collect_sort(collect_id, application_id, sort, account):
    with get_db() as db:
        try:
            db.begin_transaction()
            sql = "update `function_collection` set `sort` = `sort` + 1 where `application_id` = '{}' and `account` = '{}' and sort >= {}".format(application_id, account, sort)
            db.exec_sql(sql)
            repository.update('function_collection', {'sort': sort}, {'id': collect_id})
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=str(e))


def update_all_function_collect_rank(collect_id, rank, account):
    with get_db() as db:
        try:
            db.begin_transaction()
            sql = "update `function_collection` set `rank` = `rank` + 1 where `account` = '{}' and `rank` >= {}".format(account, rank)
            db.exec_sql(sql)
            repository.update('function_collection', {'rank': rank}, {'id': collect_id})
            db.commit()
        except Exception as e:
            db.rollback()
            raise UserError(message=str(e))
