# pylint: skip-file
import os

os.environ.setdefault("prometheus_multiproc_dir", ".")
from components.query_structure_sql import handler

if __name__ == '__main__':

    try:
        from wsgiref.simple_server import make_server

        with make_server('', 8009, handler) as httpd:
            print("Serving on port 8009...")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("Goodbye!")
