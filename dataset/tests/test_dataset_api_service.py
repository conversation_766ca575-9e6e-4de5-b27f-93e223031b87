# -*- coding: utf-8 -*-
# pylint: skip-file
"""
Created on 2017年6月16日

@author: chenc04
"""
import json
import unittest
import time
import os

from dashboard_chart.services import dashboard_login_service
from dataset.query.chart_query import FieldObj

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)

from tests.base import BaseTest
from components.query_models import Prop, ModelEncoder

from dmplib.hug import g
from base.enums import DatasetType, FlowStatus
from components.rundeck import RundeckScheduler
from data_source.services import data_source_service
from dataset.models import DatasetModel
from dataset.services.dataset_api_service import DatasetAPIService
from flow.models import FlowModel

from components.external_api import ExternalAPI


class TestDatasetAPIService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='fangzhiadmin_test', account='fangzhiadmin_test')
        self.dataset_service = DatasetAPIService()

    @staticmethod
    def rundeck_1():
        model = FlowModel()
        model.id = '123'
        model.name = '0_cc_1'
        model.schedule = '0 10 16 ? * * *'
        model.status = FlowStatus.Enable.value
        scheduler = RundeckScheduler(model)
        d = scheduler.rundeck.list_job_executions("dev_123")
        print(d)
        e_id = d[0].get('id')
        a = scheduler.rundeck.api.connection.call("delete", 'execution/{0}'.format(e_id), parse_response=False)
        print(a)

    def run_get_data_1(self):
        token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************.-Ca-AXjyBmLx6rp2T_KbiH20onhM0KDlbTGs_jT8GYI'
        login_service = dashboard_login_service.DashboardLoginService()
        success, data = login_service.verify_dmp_cookie_token(token)
        userid = data.get('userid')
        project_code = data.get('org_code')
        dashboard_id = data.get('dashboard_id')
        g.code = project_code
        g.account = 'dashboard_sso'
        g.userid = userid
        g.dashboard_id = dashboard_id
        g.data = data

        payload = {
            'org_code': g.code if hasattr(g, 'code') else '',
            'userid': g.userid if hasattr(g, 'userid') else '',
            "exp": int(time.time()) + 120,
        }
        if hasattr(g, 'data') and g.data:
            payload = dict(payload, **g.data)

        g.code = 'fangzhiadmin_test'
        dataset_model = DatasetModel()
        dataset_model.type = DatasetType.Api.value
        dataset_model.content = (
            '{"url":"https://test-qmyxcg.myscrm.com.cn/api/index.php?r=bigdata/api-data-set/get-sql-data-set",'
            '"auth_key":"token=0UZR4h#@",'
            '"auth_method":"jwt",'
            '"is_sync":"不同步",'
            '"method":"POST",'
            '"data":{"sql":"select master_id, proj_id from fdw_s_order"}}'
        )
        # self.dataset_service.run_get_data(dataset_model, {})

        self.dataset_service.validation_dataset(dataset_model)
        self.dataset_service.payload = payload
        data_list = self.dataset_service.run_get_data(dataset_model, {})
        print(data_list)

    def generate(self):
        flow_id = "39e3427c-7d69-3c9a-984e-73ffda768427"
        flow_instance_id = "39e3427c-8700-9327-9cfe-a98dea173faa"
        self.dataset_service.generate('dev', flow_id, flow_instance_id)

    def nest_index(self):
        # ( col9 - col17 ) - count ( col9 )
        # api = ChartQueryApi()
        expressions = [
            {"op": "COUNT"},
            {"op": "("},
            {"id": "39e43930-44da-3aea-406e-4e51bcb97907", "title": "col9", "col_name": "A"},
            {"op": ")"},
            {"op": "-"},
            {"op": "("},
            {"id": "39e43930-44da-3aea-406e-4e51bcb97907", "title": "col9", "col_name": "A"},
            {"op": "+"},
            {"id": "39e43930-44da-3f69-0ac7-ab19e36b6721", "title": "col17", "col_name": "B"},
            {"op": ")"},
        ]

        filed = FieldObj()
        filed.expressions = expressions
        # xxx = api.get_senior_field_prop(filed)

        prop = Prop()
        # prop.props.append(xxx)

        a = json.loads(json.dumps(prop, cls=ModelEncoder))

        print(a)

    def test_run_get_data(self):
        dataset_model = DatasetModel()
        dataset_model.type = DatasetType.Api.value
        dataset_model.content = (
            '{"data_source_id":"39e64ff8-af81-0c66-4776-553c9de53992",'
            '"table_name":"1zeng_3","params":[],"sql":"select 1zeng_3_copy.* from 1zeng_3_copy INNER JOIN a_copy on a_copy.name = 1zeng_3_copy.1zeng_3", "is_advanced_mode":"1"}'
        )

        dataset_model.content = (
            '{"data_source_id":"39e64ff8-af81-0c66-4776-553c9de53992",'
            '"table_name":"1zeng_3","params":[],"sql":"select * from 1zeng_3", "is_advanced_mode":"1"}'
        )

        # 本地开发测试
        dataset_model.content = {
            "dataset_id": "39eea3dc-fb99-ecf5-a074-f18392a571c8",
            "data_source_id": "39eaf533-d998-8778-bea3-ae5a40611472",
            "sql": "select proj_name,master_id from dim_known_way",
            "params": [],
        }

        dataset_model.relation_content = {
            "nodeDataArray": [
                {
                    "id": "6030c28fcc3200781c8ea03714c5cfdb",
                    "name": "dim_known_way",
                    "comment": "认知途径维表",
                    "alias_name": "dim_known_way",
                    "fields": [
                        {"type": "varchar(200)", "name": "proj_name", "comment": "主项目名称", "alias_name": "主项目名称"},
                        {"type": "char(36)", "name": "master_id", "comment": "主键id", "alias_name": "主键id"},
                        {
                            "type": "datetime",
                            "name": "modified_on_odps",
                            "comment": "",
                            "alias_name": "modified_on_odps",
                        },
                        {"type": "varchar(512)", "name": "known_way", "comment": "认知途径", "alias_name": "认知途径"},
                        {"type": "int(10)", "name": "is_deleted", "comment": "是否删除", "alias_name": "是否删除"},
                        {
                            "type": "varchar(512)",
                            "name": "parent_known_way",
                            "comment": "父级认知途径",
                            "alias_name": "父级认知途径",
                        },
                        {
                            "type": "varchar(200)",
                            "name": "parent_known_id",
                            "comment": "父级认知途径id",
                            "alias_name": "父级认知途径id",
                        },
                        {
                            "type": "varchar(200)",
                            "name": "dt_timestamp_unique",
                            "comment": "唯一时间记录值",
                            "alias_name": "唯一时间记录值",
                        },
                        {"type": "char(36)", "name": "relation_id", "comment": "项目id", "alias_name": "项目id"},
                        {"type": "char(36)", "name": "proj_id", "comment": "主项目id", "alias_name": "主项目id"},
                        {"type": "varchar(200)", "name": "known_id", "comment": "认知途径id", "alias_name": "认知途径id"},
                        {"type": "timestamp", "name": "dt_timestamp", "comment": "数据中心增量字段", "alias_name": "数据中心增量字段"},
                    ],
                }
            ],
            "linkDataArray": [],
        }

        g.cookie = {'token': 'adfasd'}
        data_source_model = data_source_service.get_data_source("39eaf533-d998-8778-bea3-ae5a40611472")
        dataset_service = DatasetAPIService(dataset_model, data_source_model)
        a = dataset_service.run_get_data()
        print(a)

    def test_run_get_data_count(self):
        dataset_model = DatasetModel()
        dataset_model.type = DatasetType.Api.value
        dataset_model.content = (
            '{"data_source_id":"39e64ff8-af81-0c66-4776-553c9de53992",'
            '"table_name":"1zeng_3","params":[],"sql":"select * from 1zeng_3", "is_advanced_mode":"1"}'
        )
        g.cookie = {'token': 'adfasd'}
        data_source_model = data_source_service.get_data_source("39e64ff8-af81-0c66-4776-553c9de53992")
        dataset_service = DatasetAPIService(dataset_model, data_source_model)
        a = dataset_service.run_get_data_count()
        print(a)

    def test_api(self):
        host = """https://test-extapi.myscrm.cn/index.php/api/index.php?r=bigdata/data-source"""
        access_secret = """0UZR4h23sdfe#%!*"""
        api = ExternalAPI(host, access_secret, g.cookie if hasattr(g, 'cookie') else {})
        print(api)


if __name__ == '__main__':
    unittest.main()
