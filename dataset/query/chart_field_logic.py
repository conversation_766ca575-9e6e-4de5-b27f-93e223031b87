#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: disable=W0123,R1710

"""
    Class ChartFieldLogic
    Author:<EMAIL>
    Created: 2017/9/11
"""

from dmplib.utils.errors import InvalidArgumentError
from base.enums import SqlWhereOperator, DatasetFieldDataType
import json


class ChartFieldLogic:
    """ Summary
     数据集字段逻辑操作类
    Attributes:
        :
    """

    __slots__ = [
        'own_operator',
        'common_operator',
        'between_operator',
        'in_operator',
        'far_operator',
        'where_str',
        'field',
        'field_func',
        'field_type',
        'field_func_format',
        'field_value',
        'operator',
    ]

    def __init__(self, field_obj):
        self.where_str = ''

        self.field = field_obj.field
        self.field_func = field_obj.field_func
        self.field_func_format = field_obj.field_func_format
        self.field_type = field_obj.field_type
        self.operator = field_obj.operator.upper()
        self.field_value = field_obj.field_value

        # 拥有的操作方法
        self.own_operator = []

        self.common_operator = self.get_common_operator()
        self.between_operator = self.get_between_operator()
        self.in_operator = self.get_in_operator()
        self.far_operator = self.get_far_operator()

    @staticmethod
    def get_common_operator():
        """
        获取比较型操作符
        :return:
        """
        return [
            SqlWhereOperator.Gt.value,
            SqlWhereOperator.Lt.value,
            SqlWhereOperator.Eq.value,
            SqlWhereOperator.Gte.value,
            SqlWhereOperator.Lte.value,
            SqlWhereOperator.Neq.value,
            SqlWhereOperator.Like.value,
        ]

    @staticmethod
    def get_between_operator():
        """
        获取区间型操作符
        :return:
        """
        return [SqlWhereOperator.Between.value, SqlWhereOperator.Notbetween.value]

    @staticmethod
    def get_in_operator():
        """
        获取in型操作符
        :return:
        """
        return [SqlWhereOperator.In.value, SqlWhereOperator.Notin.value]

    @staticmethod
    def get_far_operator():
        """
        获取日期间隔型操作符
        :return:
        """
        return [
            SqlWhereOperator.FromDay.value,
            SqlWhereOperator.FromYesterday.value,
            SqlWhereOperator.FromWeek.value,
            SqlWhereOperator.FromMonth.value,
            SqlWhereOperator.FromQuarter.value,
            SqlWhereOperator.FromYear.value,
        ]

    def where_operator_common(self):
        """
        :常用操作符 ['GT', 'LT', 'EQ', 'GTE', 'LTE', 'NEQ', 'LIKE']
        筛选器 等于 不等于
        :date 2017/9/8
        :return :
        """

        self.where_str = "( {field} {operator} '{value}' )".format(
            field=self.field, operator=self.operator, value=self.field_value
        )
        return self

    def where_operator_in(self):
        """
        :in 操作符,包含，不包含
        :date 2017/9/8
        :return :
        """

        in_str = ""

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)

        for v in value_list:

            # 如果字符串为空，转换成 NULL和空字符串
            if v == '':
                tmp_str = "'',NULL"
            else:
                tmp_str = "'%s'" % v

            if in_str:
                in_str += "," + tmp_str
            else:
                in_str = tmp_str

        value = "(" + in_str + ")"

        self.where_str = "( {field} {operator} {value} )".format(field=self.field, operator=self.operator, value=value)

        return self

    def where_operator_between(self):
        """
        :between  not between 操作
        :date 2017/9/9
        :return :
        """

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)
        value = "'%s' AND '%s'" % (value_list[0], value_list[1])
        self.where_str = "( {field} {operator} {value} )".format(field=self.field, operator=self.operator, value=value)

        return self

    def where_operator_from_date(self):
        pass

    def get_where_str(self):

        if self.operator.upper() not in self.own_operator:
            raise InvalidArgumentError(message='字符串不支持该操作符')

        if self.operator.upper() in self.common_operator:
            # 如果是 'xxx'='*' 不作为where条件 取出所有
            if (
                self.operator == SqlWhereOperator.Eq.value
                and isinstance(self.field_value, str)
                and self.field_value == '*'
            ):
                return self.where_str
            self.where_operator_common()
            return self.where_str
        if self.operator.upper() in self.in_operator:
            self.where_operator_in()
            return self.where_str
        if self.operator.upper() in self.between_operator:
            self.where_operator_between()
            return self.where_str
        if self.operator.upper() in self.far_operator:
            self.where_operator_from_date()
            return self.where_str


class DescriptionField(ChartFieldLogic):
    """
    :字符串类型
    :date 2017/9/9
    :param self:
    :return :
    """

    def __init__(self, field_obj):
        super().__init__(field_obj)

        self.own_operator = self.common_operator + self.in_operator + self.between_operator

    def where_operator_common(self):
        """
        :常用操作符 ['GT', 'LT', 'EQ', 'GTE', 'LTE', 'NEQ', 'LIKE']
        筛选器 等于 不等于
        :date 2017/9/8
        :return :
        """

        # 如果字符串为空，需要设置成 col='' or col is null
        if self.field_value == '':
            if self.operator.upper() in [SqlWhereOperator.Eq.value, SqlWhereOperator.Like.value]:
                self.where_str = "( {field} = ''  OR {field} is NULL )".format(field=self.field)
            elif self.operator.upper() in [SqlWhereOperator.Neq.value]:
                self.where_str = "( {field} != '' AND {field} is not NULL )".format(field=self.field)
        else:
            self.where_str = "( {field} {operator} '{value}' )".format(
                field=self.field, operator=self.operator, value=self.field_value
            )

        return self

    def where_operator_in(self):
        """
        :in 操作符,包含，不包含
        :date 2017/9/8
        :return :
        """

        in_str = ""
        if isinstance(self.field_value, list):
            value_list = self.field_value
        else:
            try:
                value_list = json.loads(self.field_value)
            except:
                value_list = [self.field_value]

        hav_null = False
        for v in value_list:

            # 如果字符串为空，转换成 NULL和空字符串
            if v == '':
                tmp_str = "''"
                hav_null = True
            else:
                tmp_str = "'%s'" % v

            if in_str:
                in_str += "," + tmp_str
            else:
                in_str = tmp_str

        value = "(" + in_str + ")"

        or_str = ''
        if hav_null and self.operator == SqlWhereOperator.In.value:
            or_str += ' OR {field} {operator} {value}'.format(field=self.field, operator='=', value='null')
        elif hav_null and self.operator == SqlWhereOperator.Notin.value:
            or_str = ' OR {field} {operator} {value}'.format(field=self.field, operator='<>', value='null')
        self.where_str = "( {field} {operator} {value} {or_str})".format(
            field=self.field, operator=self.operator, value=value, or_str=or_str
        )
        return self

    def where_operator_between(self):
        """
        :between  not between 操作
        :date 2017/9/9
        :return :
        """

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)
        value = "'%s' AND '%s'" % (value_list[0], value_list[1])
        self.where_str = "( {field} {operator} {value} )".format(field=self.field, operator=self.operator, value=value)

        return self


class EnumerationField(ChartFieldLogic):
    """
    :枚举型
    :date 2017/9/9
    :param self:
    :return :
    """

    def __init__(self, field_obj):
        super().__init__(field_obj)
        self.own_operator = self.common_operator + self.in_operator + self.between_operator

    def where_operator_between(self):
        """
        :between  not between 操作
        :date 2017/9/9
        :return :
        """

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)

        value = "'%s' AND '%s'" % (value_list[0], value_list[1])
        self.where_str = "( {field} {operator} {value} )".format(field=self.field, operator=self.operator, value=value)

        return self


class NumberField(ChartFieldLogic):
    """
    :枚举型
    :date 2017/9/9
    :param self:
    :return :
    """

    def __init__(self, field_obj):
        super().__init__(field_obj)
        self.own_operator = self.common_operator + self.in_operator + self.between_operator

    def where_operator_common(self):
        """
        :常用操作符 ['GT', 'LT', 'EQ', 'GTE', 'LTE', 'NEQ', 'LIKE']
        筛选器 等于 不等于
        :date 2017/9/8
        :return :
        """
        # 如果字符串为空，需要设置成 col='' or col is null
        if self.field_value == '':
            if self.operator.upper() in [SqlWhereOperator.Eq.value, SqlWhereOperator.Like.value]:
                self.where_str = "( {field} = ''  OR {field} is NULL )".format(field=self.field)
            elif self.operator.upper() in [SqlWhereOperator.Neq.value]:
                self.where_str = "( {field} != '' AND {field} is not NULL )".format(field=self.field)
        else:
            self.where_str = "( {field} {operator} {value} )".format(
                field=self.field, operator=self.operator, value=self.field_value
            )
        return self

    def where_operator_in(self):
        """
        :in 操作符,包含，不包含
        :date 2017/9/8
        :return :
        """

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)
        value = ','.join([str(i) for i in value_list])

        self.where_str = "( {field} {operator} ({value}) )".format(
            field=self.field, operator=self.operator, value=value
        )

        return self

    def where_operator_between(self):
        """
        :between  not between 操作
        :date 2017/9/9
        :return :
        """

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)
        value = "%s AND %s" % (value_list[0], value_list[1])
        self.where_str = "( {field} {operator} {value} )".format(field=self.field, operator=self.operator, value=value)

        return self


class DatetimeField(ChartFieldLogic):
    """
    :枚举型
    :date 2017/9/9
    :param self:
    :return :
    """

    def __init__(self, field_obj):
        super().__init__(field_obj)
        self.own_operator = self.common_operator + self.in_operator + self.between_operator + self.far_operator

    def where_operator_common(self):
        """
        :常用操作符 ['GT', 'LT', 'EQ', 'GTE', 'LTE', 'NEQ', 'LIKE']
        筛选器 等于 不等于
        :date 2017/9/8
        :return :
        """

        field = self.field
        if self.field_func:
            field = self.field_func + '(' + self.field + ', "' + self.field_func_format + '")'

        self.where_str = "( {field} {operator} '{value}' )".format(
            field=field, operator=self.operator, value=self.field_value
        )

        return self

    def where_operator_from_date(self):
        """
        :far 操作符 距今日、月、年操作，大于某个时间点
        :param self:条件值
        :date 2017/9/8
        :return :
        """
        self.field_value = int(self.field_value)

        date_type_map = {
            SqlWhereOperator.FromDay.value: {
                "end_date": "CURDATE()",
                "start_date": "DATE_SUB(CURDATE(), INTERVAL {sub} DAY)".format(sub=self.field_value),
            },
            SqlWhereOperator.FromYesterday.value: {
                "end_date": "DATE_SUB(CURDATE(), INTERVAL 1 DAY)",
                "start_date": "DATE_SUB(CURDATE(), INTERVAL {sub} DAY)".format(sub=self.field_value),
            },
            SqlWhereOperator.FromWeek.value: {
                "end_date": "CURDATE()"
                if self.field_value == 0
                else "DATE_SUB(DATE_SUB(DATE_SUB(CURDATE(),INTERVAL"
                " WEEKDAY(CURDATE()) DAY), INTERVAL {sub} WEEK), "
                "INTERVAL 1 DAY)".format(sub=self.field_value - 1),
                "start_date": "DATE_SUB(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) DAY), "
                "INTERVAL {sub} WEEK)".format(sub=self.field_value),
            },
            SqlWhereOperator.FromMonth.value: {
                "end_date": "CURDATE()"
                if self.field_value == 0
                else "DATE_SUB(DATE_SUB(DATE_SUB(CURDATE(), "
                "INTERVAL DAYOFMONTH(CURDATE()) - 1 DAY), "
                "INTERVAL {sub} MONTH), "
                "INTERVAL 1 DAY)".format(sub=self.field_value - 1),
                "start_date": "DATE_SUB(DATE_SUB(CURDATE(), INTERVAL DAYOFMONTH(CURDATE()) - 1 DAY), "
                "INTERVAL {sub} MONTH)".format(sub=self.field_value),
            },
            SqlWhereOperator.FromQuarter.value: {
                "end_date": "CURDATE()"
                if self.field_value == 0
                else "DATE_SUB(DATE_SUB(DATE_SUB(DATE_SUB(CURDATE(),"
                "INTERVAL (MONTH(CURDATE())-1)%3 MONTH) ,"
                "INTERVAL DAY(CURDATE())-1 DAY), "
                "INTERVAL {sub} QUARTER), "
                "INTERVAL 1 DAY)".format(sub=self.field_value - 1),
                "start_date": "DATE_SUB(DATE_SUB(DATE_SUB(CURDATE(), "
                "INTERVAL (MONTH(CURDATE())-1)%3 MONTH) ,"
                "INTERVAL DAY(CURDATE())-1 DAY), INTERVAL {sub} QUARTER)".format(sub=self.field_value),
            },
            SqlWhereOperator.FromYear.value: {
                "end_date": "CURDATE()"
                if self.field_value == 0
                else "DATE_SUB(DATE_SUB(DATE_SUB(CURDATE(), "
                "INTERVAL DAYOFYEAR(CURDATE()) - 1 DAY), "
                "INTERVAL {sub} YEAR), "
                "INTERVAL 1 DAY)".format(sub=self.field_value - 1),
                "start_date": "DATE_SUB(DATE_SUB(CURDATE(), INTERVAL DAYOFYEAR(CURDATE()) - 1 DAY), "
                "INTERVAL {sub} YEAR)".format(sub=self.field_value),
            },
        }

        operator_key = self.operator.upper()
        if operator_key not in date_type_map:
            raise InvalidArgumentError(message='非法操作符')

        self.where_str = "date_format({field}, '{formula_mode}') BETWEEN {start_date} AND {end_date}".format(
            field=self.field,
            formula_mode=self.field_func_format,
            start_date=date_type_map[operator_key]['start_date'],
            end_date=date_type_map[operator_key]['end_date'],
        )

        return self

    def where_operator_between(self):
        """
        :between  not between 操作
        :date 2017/9/9
        :return :
        """
        relative_date_map = {
            'today': 'CURDATE()',
            'yesterday': 'DATE_SUB(CURDATE(), INTERVAL 1 DAY)',
            'lastweek': 'DATE_SUB(DATE_SUB(CURDATE(),INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 1 DAY)',
            'lastmonth': 'DATE_SUB(DATE_SUB(CURDATE(), INTERVAL  DAYOFMONTH(CURDATE()) - 1 DAY), INTERVAL 1 DAY)',
            'lastquarter': 'DATE_SUB(DATE_SUB(DATE_SUB(CURDATE(),INTERVAL (MONTH(CURDATE())-1)%3 MONTH) ,'
            'INTERVAL DAY(CURDATE())-1 DAY), INTERVAL 1 DAY)',
            'lastyear': 'DATE_SUB(DATE_SUB(CURDATE(), INTERVAL DAYOFYEAR(CURDATE()) - 1 DAY), INTERVAL 1 DAY)',
        }

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)

        if isinstance(value_list[1], str) and value_list[1] in relative_date_map.keys():
            value = "'%s' AND %s" % (value_list[0], relative_date_map.get(value_list[1]))
        else:
            value = "'%s' AND '%s'" % (value_list[0], value_list[1])
        self.where_str = "( {field} {operator} {value} )".format(field=self.field, operator=self.operator, value=value)
        return self

    def where_operator_in(self):
        """
        :in 操作符,包含，不包含
        :date 2017/9/8
        :return :
        """

        in_str = ""

        value_list = self.field_value if isinstance(self.field_value, list) else json.loads(self.field_value)

        for v in value_list:

            # 如果字符串为空，转换成 NULL和空字符串

            tmp_str = "'%s'" % v

            if in_str:
                in_str += "," + tmp_str
            else:
                in_str = tmp_str

        value = "(" + in_str + ")"

        field = self.field
        if self.field_func:
            field = self.field_func + '(' + self.field + ', "' + self.field_func_format + '")'

        self.where_str = "( {field} {operator} {value} )".format(field=field, operator=self.operator, value=value)

        return self


class AddressField(ChartFieldLogic):
    """
    :枚举型
    :date 2017/9/9
    :param self:
    :return :
    """

    def __init__(self, field_obj):
        super().__init__(field_obj)
        self.own_operator = self.common_operator

    def where_operator_common(self):
        """
        :常用操作符 ['GT', 'LT', 'EQ', 'GTE', 'LTE', 'NEQ', 'LIKE']
        筛选器 等于 不等于
        :date 2017/10/20
        :return :
        """

        field = self.field
        if self.field_func:
            field = self.field_func + '(' + self.field + ', "' + self.field_func_format + '")'

        self.where_str = "( {field} {operator} '{value}' )".format(
            field=field, operator=self.operator, value=self.field_value
        )

        return self


def field_factory(field_obj):
    """
    :创建field type工厂方法
    :date 2017/9/13
    :param field_obj 字段对象
    :return field object:
    """

    for item in DatasetFieldDataType.__members__.values():
        if field_obj.field_type == item.value:
            return eval(item.name + 'Field(field_obj)')
    raise InvalidArgumentError(message='字段类型不支持')
