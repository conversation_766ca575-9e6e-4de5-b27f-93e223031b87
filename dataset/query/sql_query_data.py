#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Sql类型查询数据类
    <NAME_EMAIL> on 2018/9/18.
"""
import json

from typing import Dict
from components import query_sql_encoder
from components.query_models import ModelEncoder
from dataset.query.query_data import QueryData
from dataset.query.result_data import DatasetQueryJsonToSqlException, status_code
from dataset.services.dataset_base_service import DatasetBaseService
from dataset.services.dataset_var_service import get_dataset_vars

from components.query_structure_sql import QueryStructure
from dmplib.db.mysql_wrapper import SimpleMysql


class SqlQueryData(QueryData):
    __slots__ = ['query_structure', 'connection_db', 'data_source_model']

    def __init__(self, query_structure: QueryStructure, connection_db: SimpleMysql, dataset_data: Dict = None) -> None:
        """
        初始化QueryStructure对象、DB连接对象
        :param components.query_models.QueryStructure query_structure:
        :param connection_db:
        """
        self.query_structure = query_structure
        self.connection_db = connection_db
        self.data_source_model = None
        self.dataset_data = dataset_data
        super().__init__(query_structure)

    def generate_sql(self) -> str:
        """
        根据QueryStructure对象生成可执行的sql语句
        :return:
        """
        self.replace_dataset_sql_vars()
        query_structure = self.get_query_structure()
        try:
            sql = query_sql_encoder.encode_query(query_structure)
        except Exception as e:
            query_structure_json = json.dumps(query_structure, cls=ModelEncoder)
            raise DatasetQueryJsonToSqlException(
                msg=status_code.get(502) + str(e), query_structure_json=query_structure_json
            ) from e
        return sql

    def replace_dataset_sql_vars(self):
        """
        替换API数据集SQL模式的数据集变量为报告传入的值或者使用数据集变量的默认值
        :return:
        """
        # if self.dataset_data.get('edit_mode') != DatasetEditMode.Sql.value:
        #    return
        # sql模式视图模式都支持变量
        if not self.dataset_data or not self.dataset_data['id']:
            return

        dataset_id = self.dataset_data['id']
        dataset_vars = get_dataset_vars(dataset_id)
        if not dataset_vars:
            return

        dataset_vars_map = {r['id']: r for r in dataset_vars}
        query_vars = self.query_structure.vars
        query_vars_map = {}
        for query_var in query_vars:
            var_id = query_var.var_id
            var_data = query_var.get_dict()
            var_data['id'] = var_id
            query_vars_map[var_id] = var_data
        dataset_vars_map.update(query_vars_map)
        vars_data_list = list(dataset_vars_map.values())

        for query_object in self.query_structure.object:
            query_object.name = DatasetBaseService.replace_dataset_sql_var_id_as_values(
                query_object.name, vars_data_list
            )
        for where_obj in self.query_structure.where:
            if where_obj:
                DatasetBaseService.relace_where_sql_vars(where_obj, vars_data_list)

    def get_query_data(self, sql=None):
        """
        根据QueryStructure对象获取查询数据（重写父类的查询数据方法）
        :return:
        """
        raise NotImplementedError()

    @staticmethod
    def is_dynamic_sql(sql):
        """是否是动态sql"""
        raise NotImplementedError()


class DirectSqlQueryData(SqlQueryData):
    def replace_dataset_sql_vars(self):
        raise NotImplementedError()
