# -*- coding: utf-8 -*-

"""
    数据集结果数据辅助函数（提供数据转换加工）
    <NAME_EMAIL> on 2018/9/18.
"""
from decimal import Decimal
from datetime import timedelta
from base.enums import DatasetFieldType
from dataset.common.dataset_data_helper import transfer_timedelta_to_string


from typing import Dict, List, Union


def process_result(
    dataset_result: list, dataset_fields: list, permit_fields: list
) -> Union[
    List[Dict[str, Union[str, float]]],
    List[Dict[str, float]],
    List[Union[Dict[str, Union[str, None]], Dict[str, Union[str, float]]]],
]:
    """
    数据集结果数据统一处理
    :param dataset_result:
    :param dataset_fields:
    :param permit_fields:
    :return:
    """
    if not dataset_result:
        return []

    permission_field_hide = {}
    if isinstance(dataset_result[0], dict):
        for key in dataset_result[0]:
            # 权限字段
            permission_field_hide[key] = match_permission_field(key, dataset_fields, permit_fields)

    for r in dataset_result:
        if not isinstance(r, dict):
            continue

        for key in r:
            # 格式化Decimal为float
            if isinstance(r.get(key), Decimal):
                r[key] = float(r.get(key))
            # 处理ADS数据集的time字段类型
            if isinstance(r.get(key), timedelta):
                r[key] = transfer_timedelta_to_string(r.get(key))

            # 权限字段不可见，进行数据为空处理
            if permission_field_hide.get(key):
                r[key] = None

    return dataset_result


def match_permission_field(key: str, dataset_fields: list, permit_fields: list) -> bool:
    """
    权限字段是否匹配
    :param key:
    :param dataset_fields:
    :param permit_fields:
    :return:
    """
    flag = False

    if not permit_fields:
        return flag

    for field_id in permit_fields:
        for dataset_field in dataset_fields:
            if (
                dataset_field.get('type') == DatasetFieldType.Normal.value
                and field_id == dataset_field.get('id')
                and (dataset_field.get('col_name') == key or key.find('_' + dataset_field.get('col_name')) > -1)
            ):
                flag = True
                break
    return flag
