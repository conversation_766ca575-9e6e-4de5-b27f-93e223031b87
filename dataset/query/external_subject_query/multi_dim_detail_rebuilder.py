#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/10/19 16:04
# <AUTHOR> caoxl
# @File     : query_structure_builder.py
# pylint: disable=R0201
import copy
from collections import defaultdict
from typing import List, Tuple, Dict, Set, Union

from base.dmp_constant import (
    EXTERNAL_SUBJECT_BASE_DIM_TEMP_TABLE_NAME,
    EXTERNAL_CUBE_SUBJECT_COUNT_ALIAS,
    EXTERNAL_SUBJECT_COUNT_TEMP_TABLE_NAME,
    EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX, EXTERNAL_SUBJECT_COMMON_CONDITION_ALIAS)
from base.enums import DatasetFieldType, AnalysisMode, DatasetFieldDataType, DatasetFieldGroup
from components.query_structure_sql import Where
from dataset.query.external_subject_query.external_subject_utils import BaseFieldInfo
from dataset.query.external_subject_query.models import (
    <PERSON><PERSON><PERSON>,
    ESTableCategory,
    E<PERSON>ieldType,
    Node,
    Map,
    ESTable)
from dataset.query.external_subject_query.multi_dim_external_subject import ExternalSubject
from dataset.query.result_data import MultiDimQueryException, MultiDimDataException
from . import external_subject_utils
from components import query_structure_sql


class MultiDimDetailRebuilder:
    def __init__(self, original_query_structure, external_subject_id, dataset_data, dataset_fields, raw_external_subject):
        self.original_query_structure = original_query_structure
        self.external_subject_id = external_subject_id
        self.dataset_data = dataset_data
        self.dataset_fields = dataset_fields
        self.dataset_field_map = {field.get('id'): field for field in dataset_fields}
        # col_name和字段的映射, 用于处理计算指标的字段查找问题
        self.colname_map: Dict[str, BaseFieldInfo] = None

        # 相关字段和相关表
        self.related_all_fields: Set[BaseFieldInfo] = None
        self.related_tables: Set[str] = None
        self.related_num_tables: Set[str] = None
        self.select_related_tables: Set[str] = None

        # select中dim 字段/片段相关数据
        self.select_dim_fields: Set[BaseFieldInfo] = None
        self.select_dim_slices: List[dict] = None

        # select中num 字段/片段相关数据（有聚合计算函数的即为num片段，相同表的num放到一起）
        self.select_table_num_fields_map: Dict[str, set] = None
        self.select_table_num_slices_map: Dict[str, list] = None
        self.select_num_slice_fields: List[Tuple[query_structure_sql.Select, Set[BaseFieldInfo]]] = None

        # 每个表对应的where字段和slice, 对where进行拆分重组, 将condition放入对应的包含该表的view拼接中
        self.where_table_fields_slices_map: Dict[str, List[Tuple[dict, Set[BaseFieldInfo]]]] = None

        # 记录每个内层View拼接的路径
        self.view_roads_map: Dict[str, List[Tuple[str, str]]] = dict()

        # 指标字段数据
        self.indicator_fields: Set[BaseFieldInfo] = None

        # sql拼接模式
        # 只有维度模式，只有度量模式，混合模式
        self.analysis_mode = AnalysisMode.Mix
        # 是否是count模式
        self.count_mode = False
        # 是否是subtotal模式
        self.subtotal_mode = False
        self.subtotal_dim_fields: Set[BaseFieldInfo] = None
        self.subtotal_slices: List[query_structure_sql.Select] = None
        self.subtotal_dim_slices: List[query_structure_sql.Select] = None

        # 主题相关基础数据, 包括表信息, 字段信息和关系信息
        self.subject: ExternalSubject = None

        # 表图, 用于搜索和路径查询
        self.map: Map = None

        # 外部主题数据库数据
        self.raw_external_subject = raw_external_subject

        # 可选筛选条件, 如果路径中有相关表, 加入筛选, 不尝试加入join路径
        self.optional_wheres: List[Where] = None
        self.optional_where_table_fields_slices_map: Dict[str, List[Tuple[dict, Set[BaseFieldInfo]]]] = None


    def _build_related_tables(self):
        """构建所有相关表"""
        self.select_related_tables = set()
        self.related_tables = set()
        self.related_num_tables = set()

        self.select_related_tables = {related_field.table for related_field in {*self.select_fields}}
        related_tables = {related_field.table for related_field in {*self.select_fields, *self.where_fields}}
        for table_name in related_tables:
            table = self.subject.table_map.get(table_name)
            if table.category in [ESTableCategory.Dwd, ESTableCategory.Dws]:
                self.related_tables.add(table_name)
                self.related_num_tables.add(table_name)
            if table.category in [ESTableCategory.Dim]:
                self.related_tables.add(table_name)

    def _build_related_fields(self):
        """获取相关的维度/度量/指标, 字段/slice"""
        self.indicator_fields = set()
        measure_fields = set()
        table_measure_field_map = defaultdict(set)

        for tb, fields in self.select_table_num_fields_map.items():
            for base_field in fields:
                field = self.subject.field_map.get(base_field)
                if field.dataset_field_type == DatasetFieldType.Normal:
                    table_measure_field_map[tb].add(field)
                    measure_fields.add(field)

        self.related_all_fields = {*self.select_dim_fields, *measure_fields, *self.indicator_fields}

    def _build_colname_map(self):
        """构建colname和字段的映射关系"""
        self.colname_map = {}
        for field in self.dataset_fields:
            self.colname_map[field.get('col_name')] = BaseFieldInfo(
                table=field.get('origin_table_name'),
                field=field.get('origin_col_name'),
                dataset_field_id=field.get('id'),
            )

    def _check_multi_table(self):
        """事实表个数不能大于一个"""
        dw_tables = [
            table
            for table in self.related_tables
            if self.subject.table_map.get(table).category in [ESTableCategory.Dwd, ESTableCategory.Dws]
        ]
        if len(dw_tables) > 1:
            raise MultiDimQueryException(msg='取数失败: 明细模式只能拖入单事实表字段')


    def _bfs_dws_table_related_dims(self, root: Node) -> Set[str]:
        related_dims = set()
        queue = []
        queue.append(root)
        while queue:
            node: Node = queue.pop(0)
            node.visited = 1
            roads = self.map.road_map.get(node.table.table_name, set())
            for target in roads:
                target_node = self.map.node_map.get(target)
                if target_node.visited:
                    continue
                if not target_node.reachable:
                    continue
                queue.append(target_node)
                related_dims.add(target)
        return related_dims

    def _build_table_related_dims(self):
        """构建所有表的可达维度表"""

        def make_func(table_name):
            return lambda node: (node.table.table_name == table_name) or (node.table.category == ESTableCategory.Dim)

        for table in self.subject.table_map.values():
            root = self.map.node_map.get(table.table_name)
            if not root:
                raise MultiDimDataException(msg=f'数据错误: 表<{table.name}>在图中不存在')
            if root.table.category not in [ESTableCategory.Dwd, ESTableCategory.Dws]:
                continue
            self.map.reset()

            self.map.set_reachable_node(make_func(table.table_name))

            root.table.related_dims = self._bfs_dws_table_related_dims(root)

    def _check_only_dim_mode(self):
        """检查是否是维度模式"""
        # 只包含维度字段
        return len(self.select_table_num_fields_map) == 0

    def _check_only_num_mode(self):
        return len(self.select_dim_slices) == 0

    def _check_count_mode(self):
        """检查是否是取总数模式"""
        first_slice = self.original_query_structure.select[0]
        return first_slice.alias == EXTERNAL_CUBE_SUBJECT_COUNT_ALIAS

    def _check_subtotal_mode(self):
        first_slice = self.original_query_structure.select[0]
        return first_slice.alias.endswith(EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX)

    def _remove_first_field_from_query_structure(self):
        self.original_query_structure.select = self.original_query_structure.select[1:]

    def _remove_subtotal_field_from_query_structure(self):
        self.subtotal_slices = []
        idx = 0
        # 移除小计flag字段
        for field in self.original_query_structure.select:
            if not field.alias.endswith(EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX):
                break
            field.alias = field.alias[:-len(EXTERNAL_CUBE_SUBJECT_SUBTOTAL_ALIAS_SUFFIX)]
            self.subtotal_slices.append(field)

            idx += 1
        self.original_query_structure.select = self.original_query_structure.select[idx:]

    def _replace_subtotal_fields(self):
        # 置换小计和原始的度量字段
        if len(self.related_tables) == 1:
            self.original_query_structure.select = self.subtotal_slices
            new_group_by_slices = []
            for group_by_slice_ in self.original_query_structure.group_by:
                group_by_slice = copy.deepcopy(group_by_slice_)
                base_fields = self.get_tables_with_fields_by_structure_slice_with_class(group_by_slice)
                if len(base_fields) != 1:
                    raise MultiDimDataException(msg="数据错误: 维度只能包含一个字段")
                field = list(base_fields)[0]
                if field not in self.subtotal_dim_fields:
                    continue
                new_group_by_slices.append(group_by_slice)
            self.original_query_structure.group_by = new_group_by_slices
            return
        new_select = []
        for slice in self.original_query_structure.select:
            # 取原select中的维度字段
            base_fields = self.get_tables_with_fields_by_structure_slice_with_class(slice)
            if len(base_fields) > 1 or len(base_fields) == 0:
                raise MultiDimQueryException(msg='取数失败: 明细模式不支持派生指标')
            field = list(base_fields)[0]
            if self._is_num(field):
                continue
            new_select.append(slice)
        for slice in self.subtotal_slices:
            # 取小计中的度量字段
            base_fields = self.get_tables_with_fields_by_structure_slice_with_class(slice)
            if len(base_fields) > 1 or len(base_fields) == 0:
                raise MultiDimQueryException(msg='取数失败: 明细模式不支持派生指标')
            field = list(base_fields)[0]
            if not self._is_num(field):
                continue
            new_select.append(slice)
        self.original_query_structure.select = new_select

    def _get_field_alias(self, field: BaseFieldInfo):
        dataset_field = self.dataset_field_map.get(field.dataset_field_id)
        if dataset_field:
            return dataset_field.get('alias') or dataset_field.get('alias_name')
        return f'<{field.table}>.<{field.field}>'

    def _get_table_alias(self, table_name):
        table = self.subject.table_map.get(table_name)
        if table:
            return table.name
        return table_name

    def _check_and_build(self):
        """校验数据正确性并构建有向图"""
        # 基础数据
        self._build_colname_map()
        self.subject = ExternalSubject()
        self.subject.build_data(self.raw_external_subject, self.dataset_field_map)

        # count模式
        self.count_mode = self._check_count_mode()
        if self.count_mode:
            # 区分count模式, 通过在查询中的第一个字段设置了标记, 因此需要移除标记
            self._remove_first_field_from_query_structure()

        # 小计模式
        self.subtotal_mode = self._check_subtotal_mode()
        if self.subtotal_mode:
            # 移除并记录小计字段
            self._remove_subtotal_field_from_query_structure()

        # 通用筛选
        self._extract_optional_wheres()

        # 从select/where中获取维度/度量字段
        if self.subtotal_mode:
            self._build_fields_from_subtotal()
        self._build_fields_from_select()
        self._build_fields_from_where()
        self._build_fields_from_optional_where()

        # 获取相关表
        self._build_related_tables()
        if self.subtotal_mode:
            self._replace_subtotal_fields()

        # 如果只有一张表, 无需拼接, 无需做后续初始化
        if len(self.related_tables) == 1:
            return

        if self._check_only_dim_mode():
            self.analysis_mode = AnalysisMode.OnlyDim
        if self._check_only_num_mode():
            self.analysis_mode = AnalysisMode.OnlyNum
        self._build_related_fields()
        self.map = Map()
        self.map.build_map(self.subject.table_map.values(), self.subject.relations)
        self._build_table_related_dims()

        self._check_all_field_exist()
        self._check_all_table_exist()
        self._check_multi_table()

    def _check_all_field_exist(self):
        """检查解析出来的字段是否都存在于主题中"""
        rv = self.related_all_fields - set(self.subject.field_map.keys())
        if not rv:
            return
        msg = ','.join([f'<{self._get_field_alias(item)}>' for item in rv])
        raise MultiDimDataException(msg=f'数据错误: 字段{msg}在主题中不存在')

    def _check_all_table_exist(self):
        """检查解析出来的表是否都存在于主题中"""
        rv = self.related_tables - set(self.subject.table_map.keys())
        if not rv:
            return
        msg = ','.join([f'<{self._get_table_alias(item)}>' for item in rv])
        raise MultiDimDataException(msg=f'数据错误: 表{msg}在主题中不存在')

    def generate_structure(self):
        """
        # 0. 构建和检查数据
        # 1. 只有一个表的无需要拼接(select 和 where中)
        # 2. 获取所有where字段, group 字段，order by 字段
        # 3. 解析获取所有带有聚合计算的字段即为num字段, 其他select字段中的片段为dim字段

        :return:
        """
        self._check_and_build()

        _gen_func = self._generate_structure
        if len(self.related_tables) == 1:
            _gen_func = self._generate_single_table_structure

        if self.count_mode:
            return self._count_wrapper(_gen_func())
        return _gen_func()

    def _extract_optional_wheres(self):
        new_where = []
        self.optional_wheres = []
        for where in self.original_query_structure.where:
            is_optional = self._check_is_optional_and_remove_flag(where)
            if is_optional:
                self.optional_wheres.append(where)
            else:
                new_where.append(where)
        self.original_query_structure.where = new_where

    def _check_is_optional_and_remove_flag(self, where: Where) -> bool:
        optional_where = False
        if hasattr(where, "conditions"):
            for condition in where.conditions:
                if hasattr(condition, "left") and hasattr(condition.left, "alias") and \
                        condition.left.alias == EXTERNAL_SUBJECT_COMMON_CONDITION_ALIAS:
                    optional_where = True
                    condition.left.alias = ""
        return optional_where

    def _generate_single_table_structure(self):
        # 如果是单表，简单的按单表逻辑，不做拼接, 需要处理可选筛选的问题
        table_name = list(self.related_tables)[0]
        for slice, _ in self.optional_where_table_fields_slices_map.get(table_name, []):
            self.original_query_structure.where.append(slice)
        self.original_query_structure.object = [query_structure_sql.Object(**{"name": table_name})]
        return self.original_query_structure

    def _generate_structure(self):
        # 1. 拷贝原有query_structure
        new_query_structure: query_structure_sql.QueryStructure = copy.deepcopy(self.original_query_structure)
        new_query_structure.select = []
        new_query_structure.object = []
        new_query_structure.where = []

        # 2. 找到join基表
        base_table = self._get_base_table()
        related_dim_tables = {
            table for table in self.related_tables if self.subject.table_map.get(table).category == ESTableCategory.Dim and table != base_table
        }

        # 3. 生成维表间的join关系
        # 生成select, group by, where片段和维表join关系
        view_slice = query_structure_sql.QueryStructure()
        # 生成视图的select, where, group_by部分 和 事实表和维度表的join关系
        view_slice.select = self._generate_inner_select_slices(base_table)
        view_slice.object = self._generate_inner_join_slices(base_table, related_dim_tables)
        view_slice.where = self._generate_inner_where_slices(base_table)
        view_slice.group_by = self.original_query_structure.group_by

        # 4. 对内层视图表进行包装
        new_query_structure.object = self._combine_object_slices_with_left_join([view_slice], [])

        # 5. 生成外层的select, group by, order by, limit部分
        new_query_structure.select = self._generate_outer_select_slices()
        new_query_structure.order_by = self._generate_outer_order_by_slices()
        new_query_structure.group_by = self._generate_outer_group_by_slices()
        if not new_query_structure.limit:
            new_query_structure.limit = self._get_default_limit_slice()

        return new_query_structure

    def _count_wrapper(self, query_structure: query_structure_sql.QueryStructure):
        from ..query_dataset_service import QueryDatasetService  # pylint: disable=C0415

        outer = query_structure_sql.QueryStructure()
        query_client = QueryDatasetService.get_query_client(self.dataset_data, query_structure, self.dataset_fields)
        outer.select = [
            query_structure_sql.Select(
                **{
                    "alias": 'total',
                    "func": "count",
                    "props": [{"value": "*"}],
                }
            )
        ]
        outer.object = [
            query_structure_sql.Object(
                **{
                    "name": " ".join(
                        ["(", query_client.generate_sql(), ")", "AS", EXTERNAL_SUBJECT_COUNT_TEMP_TABLE_NAME]
                    )
                }
            )
        ]
        return outer

    def _get_base_table(self) -> Union[str, None]:
        # 如果有度量表取度量表
        related_num_tables = [
            table for table in self.related_tables if self.subject.table_map.get(table).category in [ESTableCategory.Dwd, ESTableCategory.Dws]
        ]
        if len(related_num_tables) >= 2:
            raise MultiDimQueryException(msg='取数错误: 多汇总表不支持只拖维度')
        if related_num_tables:
            return related_num_tables[0]

        for table_name in self.related_tables:
            table = self.subject.table_map.get(table_name)
            rv = table.indirect_child_tables & self.related_tables
            if not rv:
                return table_name
        raise MultiDimQueryException(msg='取数错误: 维度表间不存在父子关系')

    def _generate_outer_select_dim_slices(self):
        """生成外层维度片段"""
        select_dim_slices = self.subtotal_dim_slices if self.subtotal_mode else self.select_dim_slices
        return self._generate_outer_dim_slices([*copy.deepcopy(select_dim_slices)])
        # if self.analysis_mode != AnalysisMode.OnlyDim:
        #     return self._generate_outer_dim_slices([*copy.deepcopy(select_dim_slices)])
        # else:
        #     return self._modify_outer_slices([*copy.deepcopy(select_dim_slices)])

    def _generate_outer_select_num_slices(self):
        """生成外层度量片段"""
        slices = []

        # 普通度量和普通指标沿用之前的方法, 直接修改外部slice
        for num_slice, fields in self.select_num_slice_fields:
            field, dataset_field_type = self._get_num_slice_type(fields)
            if (dataset_field_type in (DatasetFieldType.Normal, DatasetFieldType.Indicator)):
                slices.append(self._generate_outer_normal_num_slice(field, num_slice))

        return slices

    def _generate_outer_normal_num_slice(self, field: ESField, num_slice):
        """生成外层度量/普通指标片段"""

        # 使用内层字段的表名和别名
        new_slice = query_structure_sql.Prop()
        if num_slice.func:
            new_slice.func = num_slice.func
        elif num_slice.prop_raw:
            new_slice.func = external_subject_utils.parse_agg_func(num_slice.prop_raw, '')
        inner_slice = query_structure_sql.Prop()
        if hasattr(num_slice, "obj_name"):
            inner_slice.obj_name = self._get_view_alias()
        if hasattr(num_slice, "prop_name") and hasattr(num_slice, "alias"):
            inner_slice.prop_name = str.strip(num_slice.alias, '"') or num_slice.prop_name
            new_slice.alias = num_slice.alias
        new_slice.props = [inner_slice]
        return new_slice

    def _generate_outer_select_slices(self):
        """生成外层select片段"""
        dim_slices = self._generate_outer_select_dim_slices()
        num_slices = self._generate_outer_select_num_slices()

        if self.count_mode or (self.analysis_mode == AnalysisMode.OnlyDim):
            return dim_slices

        slices = [*dim_slices, *num_slices]

        return slices

    def _generate_outer_order_by_slices(self):
        # 找到order by字段对应的select字段，并用select字段的alias作为排序
        slices = self._get_outer_orderby_from_select_slices()
        real_order_by_slices = []
        for order_slice, select_slice in slices:
            order_obj = query_structure_sql.Order(
                **{
                    'method': order_slice.method,
                    'prop_name': select_slice.alias or select_slice.prop_name,
                }
            )
            real_order_by_slices.append(order_obj)
        return real_order_by_slices

    def _get_num_slice_type(self, fields: Set[BaseFieldInfo]) -> Tuple[ESField, DatasetFieldType]:
        if len(fields) == 0:
            raise MultiDimDataException(msg='数据错误: 不支持常量类型的度量')
        if len(fields) == 1:
            field = list(fields)[0]
            field = self.subject.field_map.get(field)
            return field, field.dataset_field_type
        for field in fields:
            field = self.subject.field_map.get(field)
            if field.dataset_field_type in [DatasetFieldType.CalculateIndicator, DatasetFieldType.Indicator]:
                return field, field.dataset_field_type
        msg = ','.join([f'<{self._get_field_alias(field)}>' for field in fields])
        raise MultiDimDataException(msg=f'数据错误: 字段{msg}中没有指标字段')

    def _get_outer_orderby_from_select_slices(self) -> List:
        """
        获取ordery 片段 和 select 片段的映射
        :return:
        """
        orderby_slices = self.original_query_structure.order_by
        last_orderby_slices = []
        for orderby_slice in orderby_slices:
            orderby_slice_dict = orderby_slice.get_dict()
            del orderby_slice_dict["method"]
            for select_slice in self.original_query_structure.select:
                select_slice_dict = select_slice.get_dict()
                select_slice_dict["alias"] = ""
                if external_subject_utils.cmp_dict(orderby_slice_dict, select_slice_dict):
                    last_orderby_slices.append([orderby_slice, copy.deepcopy(select_slice)])
        return last_orderby_slices

    def _get_default_limit_slice(self):
        """
        获取默认limit
        :return:
        """
        return query_structure_sql.Limit(**{"offset": 0, "row": 10000})

    def _get_sub_alias(self, original_alias):
        """
        获取子别名 原始别名_sub 形式
        :param original_alias:
        :return:
        """
        return original_alias

    def _generate_outer_group_by_slices(self):
        """生成full join模式的外层group by片段"""
        if self.subtotal_mode:
            group_by_slices = copy.deepcopy(self.subtotal_dim_slices)
            return self._generate_outer_dim_slices(group_by_slices)
        else:
            return []

    def _generate_outer_dim_slices(self, outer_slices: List) -> List:
        return [query_structure_sql.Select(**{
            # "alias": slice.alias or slice.prop_name,
            "obj_name": self._get_view_alias(),
            "prop_name": slice.alias or slice.prop_name
        }) for slice in outer_slices]


    def _modify_outer_slices(self, outer_slices: List) -> List:
        """
        修改外部片段
        维度需要加上基础维度临时表表名
        度量需要将计算字段更改为临时计算字段
        :param outer_slices:
        :return:
        """
        new_outer_slices = []
        for outer_slice in outer_slices:
            new_slice = copy.deepcopy(outer_slice)
            use_sub_alias = False
            if external_subject_utils.has_agg_func(outer_slice):
                # 如果包含聚合函数, 是度量, 有聚合计算的为数值字段需要将表名置为空，同时更改字段名
                use_sub_alias = True
            elif getattr(new_slice, 'func', None):
                # 维度如果有函数, 清空函数，使用内层字段名
                new_slice.func = ''
                new_slice.props = []
            self._rebuild_outer_slice(new_slice, use_sub_alias)
            new_outer_slices.append(new_slice)
        return new_outer_slices

    def _rebuild_outer_slice(self, outer_slice, use_sub_alias, outer_alias_name=''):
        """
        重新构建外部片段
        将表名置为空
        用别名替换字段名
        :param outer_slice:
        :param use_sub_alias:
        :return:
        """
        if hasattr(outer_slice, "obj_name"):
            if use_sub_alias:
                outer_slice.obj_name = ""
            else:
                outer_slice.obj_name = EXTERNAL_SUBJECT_BASE_DIM_TEMP_TABLE_NAME
        if hasattr(outer_slice, "prop_name") and hasattr(outer_slice, "alias"):
            outer_alias_name = self._op_outer_slice_propname_and_alias(outer_alias_name, outer_slice, use_sub_alias)
        for attr in external_subject_utils.get_obj_public_attr_names(outer_slice):
            attr_val = getattr(outer_slice, attr)
            if not isinstance(attr_val, (tuple, list)):
                continue
            for item in attr_val:
                self._rebuild_outer_slice(item, use_sub_alias, outer_alias_name)

    def _op_outer_slice_propname_and_alias(self, outer_alias_name, outer_slice, use_sub_alias):
        if use_sub_alias:
            if outer_slice.prop_name and not outer_slice.alias:
                outer_slice.prop_name = self._get_sub_alias(outer_alias_name)
            if not outer_slice.prop_name and outer_slice.alias:
                outer_alias_name = str.strip(outer_slice.alias, '"')
            if hasattr(outer_slice, "props") and outer_slice.props and outer_slice.alias:
                outer_slice.props = [query_structure_sql.Prop(**{"prop_name": self._get_sub_alias(outer_alias_name)})]
        else:
            outer_slice.prop_name = str.strip(outer_slice.alias, '"') or outer_slice.prop_name
            outer_slice.alias = ""
        return outer_alias_name

    def _get_view_alias(self):
        return EXTERNAL_SUBJECT_BASE_DIM_TEMP_TABLE_NAME

    def _combine_only_num_mode_object_slices(self, num_views: List) -> List[query_structure_sql.Object]:
        from ..query_dataset_service import QueryDatasetService  # pylint: disable=C0415

        num_view_sqls = []
        for (num_table_name, view_slice) in num_views:
            view_name = self._get_view_alias()
            query_client = QueryDatasetService.get_query_client(self.dataset_data, view_slice, self.dataset_fields)
            num_view_sql = " ".join(["(", query_client.generate_sql(), ")", "AS", view_name])
            num_view_sqls.append(num_view_sql)
        return [query_structure_sql.Object(**{"name": num_view_sqls[0]})]

    def _build_fields_from_optional_where(self):
        self.optional_where_table_fields_slices_map = defaultdict(list)
        where_conditions = self._build_where_conditions(self.optional_wheres)

        for condition_slice in where_conditions:
            base_fields = self.get_tables_with_fields_by_structure_slice_with_class(condition_slice)
            tables = {item.table for item in base_fields}
            for table in tables:
                self.optional_where_table_fields_slices_map[table].append((condition_slice, base_fields))

    def _combine_object_slices_with_left_join(
        self, dim_view_bases: List[query_structure_sql.QueryStructure], num_views: List
    ) -> List[query_structure_sql.Object]:
        """拼接生成维度表视图和事实表视图"""
        from ..query_dataset_service import QueryDatasetService  # pylint: disable=C0415

        dim_base_sqls, num_view_sqls = [], []

        # 1. 生成每个维度表视图子查询
        for dim_base in dim_view_bases:
            query_client = QueryDatasetService.get_query_client(self.dataset_data, dim_base, self.dataset_fields)
            dim_base_sqls.append(" ".join(["(", query_client.generate_sql(), ")"]))

        # 2. 通过union拼接生成维度表视图
        dim_view_sql = " ".join(["(", " UNION ".join(dim_base_sqls), ")", EXTERNAL_SUBJECT_BASE_DIM_TEMP_TABLE_NAME])

        # 3. 生成每个事实表视图
        dim_aliases = self._get_dim_alias(self.select_dim_slices)
        for num_table_name, view_slice in num_views:
            temp_table_name = self._get_view_alias()
            query_client = QueryDatasetService.get_query_client(self.dataset_data, view_slice, self.dataset_fields)
            on_conditions = []
            for dim_alias in dim_aliases:
                on_conditions.append(
                    " ".join(
                        [
                            ".".join([temp_table_name, dim_alias]),
                            "=",
                            ".".join([EXTERNAL_SUBJECT_BASE_DIM_TEMP_TABLE_NAME, dim_alias]),
                        ]
                    )
                )
            num_view_sql = " ".join(
                ["(", query_client.generate_sql(), ")", "AS", temp_table_name, "ON", " AND ".join(on_conditions)]
            )
            num_view_sqls.append(num_view_sql)

        # 4. 通过left join关联维度表视图和事实表视图
        return [query_structure_sql.Object(**{"name": " LEFT JOIN ".join([dim_view_sql, *num_view_sqls])})]

    def _get_dim_alias(self, dim_slices: List) -> List:
        return [dim_slice.alias for dim_slice in dim_slices]

    def _convert_to_relation_roads(self, join_roads: List[Tuple[str, str]]):
        # TODO: 一个表和另一个表可能有多个关联关系, 现在只处理成一个
        real_roads = []
        for source_table_name, target_table_name in join_roads:
            rv = self.subject.relation_map.get((source_table_name, target_table_name))
            if not rv:
                raise MultiDimDataException(f"数据错误: 路径<{source_table_name} -> {target_table_name}>找不到关联关系")
            source_field_name, target_field_name = rv
            real_roads.append(
                {
                    'from_table': source_table_name,
                    'from_field': source_field_name,
                    'to_table': target_table_name,
                    'to_field': target_field_name,
                }
            )
        return real_roads

    def _generate_inner_where_slices(self, main_table_name: str):
        """生成内层where条件, 根据生成当前view的事实表和拼接路径, 找到所有相关的where条件"""

        # 1. 获取事实表相关的join路径
        roads = self.view_roads_map.get(main_table_name, [])
        # 2. 集合路径上所有相关的维表
        related_dim_tables = set()
        for source, target in roads:
            table = self.subject.table_map.get(source)
            if table.category == ESTableCategory.Dim:
                related_dim_tables.add(source)
            table = self.subject.table_map.get(target)
            if table.category == ESTableCategory.Dim:
                related_dim_tables.add(target)

        # 3. 过滤出where条件中属于当前事实表和相关维表的条件
        conditions = []
        for table_name, condition_slices in [*self.where_table_fields_slices_map.items(),
                                             *self.optional_where_table_fields_slices_map.items()]:
            table = self.subject.table_map.get(table_name)
            if (table.table_name == main_table_name) or (
                table.category == ESTableCategory.Dim and table_name in related_dim_tables
            ):
                conditions.extend([copy.deepcopy(slice_).get_dict() for (slice_, _) in condition_slices])

        # 4. 拼接新的where片段
        for idx, condition in enumerate(conditions):
            if idx == 0:
                condition['logical_relation'] = ''
            else:
                condition['logical_relation'] = 'AND'

        where_slices = (
            [
                query_structure_sql.Where(
                    **{
                        "conditions": conditions,
                        "left": query_structure_sql.Prop().get_dict(),
                        "logical_relation": "",
                        "operator": "",
                        "prop_raw": None,
                        "prop_ref": None,
                        "right": query_structure_sql.Prop().get_dict(),
                    }
                )
            ]
            if conditions
            else []
        )
        return where_slices

    def _generate_inner_select_slices(self, num_table_name):
        # 生成维度部分
        select_slices = [*self.select_dim_slices]

        # 生成度量部分
        if not self.count_mode:
            normal_indicators = []
            num_slices = self.select_table_num_slices_map.get(num_table_name, [])
            # 对于普通度量和普通指标, 去除聚合函数
            for slice_, fields in num_slices:
                field, dataset_field_type = self._get_num_slice_type(fields)
                slice_ = copy.deepcopy(slice_)
                if self.subtotal_mode:
                    setattr(slice_, 'func', None)
                if (dataset_field_type in [DatasetFieldType.Normal]) or (not field.expression_advance_tmpl):
                    select_slices.append(slice_)
                    continue
                if dataset_field_type == DatasetFieldType.Indicator:
                    normal_indicators.append((field, slice_))
            rv = self._generate_inner_normal_indicators_slices(normal_indicators)
            select_slices.extend(rv)

        return select_slices

    def _generate_inner_normal_indicators_slices(self, field_slices: List[Tuple[ESField, query_structure_sql.Select]]):
        # 解决重名问题，使用内部带表名的字段生成最终表达式
        slices = []
        for field, slice_ in field_slices:
            var_values = {}
            for var in field.expression_advance_vars:
                key = var.get('key')
                value = var.get('value')
                var_values[key] = value.get('origin_express')

            slices.append(
                query_structure_sql.Select(
                    **{
                        "alias": slice_.alias,
                        "func": slice_.func,
                        "props": [{
                            "prop_raw": field.expression_advance_tmpl.format(**var_values)
                        }]
                    }
                )
            )
        return slices

    def _generate_inner_join_slices(
        self, table_name: str, related_dim_tables: Set[str]
    ) -> List[query_structure_sql.Object]:
        """生成事实表和维度表的join关系"""
        if len(related_dim_tables) == 0:
            return [query_structure_sql.Object(**{"name": table_name})]
        source_node = self.map.node_map.get(table_name)
        join_roads = list()
        for target_dim_table in related_dim_tables:
            if target_dim_table == table_name:
                continue
            self.map.reset()
            self.map.set_reachable_node(
                lambda node: (node.table.table_name == table_name) or (node.table.category in [ESTableCategory.Dim])
            )
            target_node = self.map.node_map.get(target_dim_table)
            roads = self._bfs_road(source_node, target_node, related_dim_tables)
            if roads:
                # 这里保证dws表在第一个, 保证有序
                join_roads.extend([road for road in roads if road not in join_roads])
        self.view_roads_map[table_name] = join_roads

        real_roads = [{'from_table': table_name}]
        real_roads.extend(self._convert_to_relation_roads(join_roads))
        return self._generate_inner_join_structure_by_road(real_roads)

    def _bfs_road(self, source_node: Node, target_node: Node, all_target: Set[str]):
        queue: List[Node] = []
        queue.append((None, source_node))
        road = []
        node: Node = None
        while queue:
            (from_, node) = queue.pop(0)
            # 如果已经遍历过, 跳过
            if node.visited:
                continue

            # 设置结点的来源, 如果等于目标结点, 跳出
            node.visited = 1
            node.from_ = from_
            if node.table.table_name == target_node.table.table_name:
                break

            # 获取该结点的所有路径, 优先压入目标结点, 用于路径合并
            roads = self.map.road_map.get(node.table.table_name, set())
            low_priority_nodes = set()
            for r in roads:
                next_node = self.map.node_map.get(r)
                if not next_node.reachable or next_node.visited:
                    continue
                if node.table.name in all_target:
                    queue.append((node, next_node))
                else:
                    low_priority_nodes.add(next_node)

            # 后压入没有父子关系的结点
            for next_node in low_priority_nodes:
                queue.append((node, next_node))

        if ((not node) or (node.table.table_name != target_node.table.table_name)):
            if (self.analysis_mode == AnalysisMode.OnlyDim) and (target_node.table.table_name in self.select_related_tables):
                # 公共维度或者维度模式下的select字段，必须存在join路径
                raise MultiDimQueryException(
                    msg=f'取数失败: 表<{source_node.table.name}>与表<{self._get_table_alias(target_node.table.table_name)}>的维度关联关系不存在'
                )
            else:
                # 非公共维度用于where条件, 可以不存在join路径
                return []

        # 取路径
        while node.from_:
            road.append((node.from_.table.table_name, node.table.table_name))
            node = node.from_
        return list(reversed(road))

    def _generate_inner_join_structure_by_road(self, road: List) -> List:
        """
        根据路径生成inner join 结构 (如果同一个表出现多次，则该表需要使用and条件转换为同一个表)
        :param road:
        :return:
        """
        table_objs = []
        for item in road:
            obj = query_structure_sql.Object(**{"name": item["from_table"], "join_type": "INNER"})
            if "to_table" in item:
                obj.name = item["to_table"]
                obj.ref_clause = [
                    query_structure_sql.RefClause(
                        **{
                            "left": {"obj_name": item["from_table"], "prop_name": item["from_field"]},
                            "operator": "=",
                            "right": {"obj_name": item["to_table"], "prop_name": item["to_field"]},
                        }
                    )
                ]
            table_objs.append(obj)
        return table_objs

    def _build_fields_from_subtotal(self):
        """获取小计中的维度字段"""
        self.subtotal_dim_slices = []
        self.subtotal_dim_fields = set()
        for subtotal_slice_ in self.subtotal_slices:
            subtotal_slice = copy.deepcopy(subtotal_slice_)
            base_fields = self.get_tables_with_fields_by_structure_slice_with_class(subtotal_slice)
            # 小计模式带聚合函数就是度量
            if self._is_agg_slice(subtotal_slice):
                continue

            # 其余是维度字段
            if (not self.subtotal_mode and external_subject_utils.has_agg_func(subtotal_slice)) or len(base_fields) > 1:
                raise MultiDimQueryException(msg='取数失败: 明细模式不支持派生指标')

            field = list(base_fields)[0]
            self.subtotal_dim_fields.add(field)
            self.subtotal_dim_slices.append(subtotal_slice)

    def _is_agg_slice(self, slice):
        return external_subject_utils.has_agg_func(slice)

    def _build_fields_from_select(self):
        """获取dim和num的原始片段"""
        self.select_dim_slices = []
        self.select_dim_fields = set()
        self.select_table_num_fields_map = defaultdict(set)
        self.select_table_num_slices_map = defaultdict(list)
        self.select_num_slice_fields = []

        for select_slice_ in self.original_query_structure.select:
            select_slice = copy.deepcopy(select_slice_)
            base_fields = self.get_tables_with_fields_by_structure_slice_with_class(select_slice)

            if (not self.subtotal_mode and external_subject_utils.has_agg_func(select_slice)) or len(base_fields) > 1:
                raise MultiDimQueryException(msg='取数失败: 明细模式不支持派生指标')

            field = list(base_fields)[0]
            if self._is_num(field):
                self.select_table_num_fields_map[field.table].add(field)
                self.select_table_num_slices_map[field.table].append((select_slice, base_fields))
                self.select_num_slice_fields.append((select_slice, set(base_fields)))
            else:
                self.select_dim_fields.add(field)
                self.select_dim_slices.append(select_slice)

    def _is_num(self, field: BaseFieldInfo):
        table = self.subject.table_map.get(field.table)
        detail_field = self.dataset_field_map.get(field.dataset_field_id)
        return table.category in [ESTableCategory.Dws, ESTableCategory.Dwd] and detail_field.get('field_group') == DatasetFieldGroup.Measure.value

    def _is_configion(self, where_slice):
        return bool(where_slice.operator)

    def _build_where_conditions(self, where_slices):
        conditions = []
        for where_slice in where_slices:
            if self._is_configion(where_slice):
                conditions.append(copy.deepcopy(where_slice))
            if where_slice.conditions:
                conditions.extend(self._build_where_conditions(where_slice.conditions))
        return conditions

    def _build_fields_from_where(self):
        """获取where条件中的dim和num字段"""
        self.where_table_fields_slices_map = defaultdict(list)
        where_conditions = self._build_where_conditions(self.original_query_structure.where)

        for condition_slice in where_conditions:
            base_fields = self.get_tables_with_fields_by_structure_slice_with_class(condition_slice)
            tables = {item.table for item in base_fields}
            for table in tables:
                self.where_table_fields_slices_map[table].append((condition_slice, base_fields))
            for base_field in base_fields:
                field: ESField = self.subject.field_map.get(base_field)
                if field and field.type in [ESFieldType.Indicator, ESFieldType.Measure]:
                    table = self.subject.table_map.get(base_field.table)
                    if table.category != ESTableCategory.Ads:
                        self.select_table_num_fields_map[base_field.table].add(base_field)

    def convert_from_colname(self, raw_field: BaseFieldInfo) -> BaseFieldInfo:
        field = self.colname_map.get(raw_field.field)
        return field if field else raw_field

    def get_tables_with_fields_by_structure_slice_with_class(self, structure_slice) -> Set[BaseFieldInfo]:
        fields = external_subject_utils.get_tables_with_fields_by_structure_slice(structure_slice, cube=True)
        fields = {self.convert_from_colname(BaseFieldInfo(**field)) for field in fields}
        return fields

    @property
    def select_fields(self) -> List[BaseFieldInfo]:
        """
        获取select所涉及的所有表和字段名
        :return:
        """
        attr_name = '_select_fields'
        if not hasattr(self, attr_name):
            select_fields = self.get_tables_with_fields_by_structure_slice_with_class(
                self.original_query_structure.select
            )
            setattr(self, attr_name, select_fields)
        return getattr(self, attr_name)

    @property
    def where_fields(self) -> List[BaseFieldInfo]:
        """
        获取where所涉及的所有表和字段名
        :return:external_subject_utils
        """
        attr_name = '_where_fields'
        if not hasattr(self, attr_name):
            where_fields = self.get_tables_with_fields_by_structure_slice_with_class(
                self.original_query_structure.where
            )
            setattr(self, attr_name, where_fields)
        return getattr(self, attr_name)
