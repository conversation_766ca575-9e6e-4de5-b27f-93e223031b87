#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
ps: 迁移自可视化报告弃用的dashboard目录模块
"""

from dmplib.utils.errors import InvalidArgumentError
from base.enums import SqlWhereOperator
from dataset.query.chart_field_logic import field_factory


class FieldObj(object):
    """
    字段父类
    field：字段名
    field_func：字段函数
    field_func_format：字段函数格式化（可选）
    alias：别名
    is_senior_field：是否高级字段
    expression：高级字段表达式
    """

    __slots__ = [
        'field',
        'field_func',
        'field_type',
        'field_func_format',
        'alias',
        'is_senior_field',
        'expressions',
        'specifier',
        "field_ref",
        "field_raw",
    ]

    def __init__(self, **kwargs):
        self.field = kwargs.get("field")
        self.field_func = kwargs.get("field_func")
        self.field_func_format = kwargs.get("field_func_format")
        self.alias = kwargs.get("alias")
        self.field_type = kwargs.get("field_type")
        self.is_senior_field = kwargs.get("is_senior_field")
        self.expressions = kwargs.get("expressions")
        self.field_ref = kwargs.get("field_ref")
        self.field_raw = kwargs.get("field_raw")


class SelectField(FieldObj):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class GroupField(FieldObj):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)


class OrderField(FieldObj):
    __slots__ = ['field_sort']

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.field_sort = kwargs.get("field_sort")

        if not self.field_sort:
            self.field_sort = ''

        if self.field_sort.upper() not in ['ASC', 'DESC', '']:
            raise InvalidArgumentError(message='参数指定排序方式不支持')


class OrderCustomField(FieldObj):
    __slots__ = ['items', 'col', 'field_sort']

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.field_sort = kwargs.get("field_sort")

        if not self.field_sort:
            self.field_sort = ''

        if self.field_sort.upper() not in ['ASC', 'DESC', '', 'CUSTOM']:
            raise InvalidArgumentError(message='参数指定排序方式不支持')
        self.items = kwargs.get("items")


class LimitField(FieldObj):
    __slots__ = ['offset', 'limit']

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.offset = kwargs.get("offset")
        self.limit = kwargs.get("limit")

        if not self.offset:
            self.offset = 0


class WhereField(FieldObj):
    __slots__ = ['field_value', 'operator', 'complex', 'complex_logic', 'logic', 'dn']

    def __init__(self, **kwargs):
        """
        :param col_type:字段类型
        :param col_name:字段名称
        :param operator:操作符 <,>,=,!=,<=,>=,between,in,not in,far,like,far_yesterday,from_week,from_month,from_year,from_quarter
        :param col_value:字段值
        """
        super().__init__(**kwargs)
        self.dn = None
        self.field_value = kwargs.get("field_value")
        self.operator = kwargs.get("operator")

        self.complex = kwargs.get("complex")
        self.complex_logic = kwargs.get("complex_logic")
        self.logic = kwargs.get("logic")

        if not kwargs.get('logic'):
            self.logic = ' AND '
        else:

            if kwargs.get('logic').upper() not in ['AND', 'OR', 'XOR']:
                raise InvalidArgumentError(message='参数指定逻辑操作符不支持')

            self.logic = kwargs.get('logic')

        if kwargs.get('complex'):
            self.complex = kwargs.get('complex')
        else:
            self.complex = None

        if kwargs.get('operator') and kwargs.get('operator') not in [
            e.value for e in SqlWhereOperator.__members__.values()
        ]:
            raise InvalidArgumentError(message='参数指定操作方法不支持')


class ChartQuery(object):
    """
    图表查询类
    usage: ChartQuery('dataset_123123').field(['a','b'])
    .where([{'field':'a','value':123,'operator':'Gt'}])
    .group(['a','b'])
    .order([{'field':'a','field_sort':'desc'}])
    .limit('0,10')
    .get_sql()

    """

    __slots__ = [
        '_select_field_str',
        'where_str',
        '_order_str',
        '_group_str',
        '_limit_str',
        'table_name',
        'time_formula_mode',
        'complex_logic',
    ]

    def __init__(self, table_name=None):

        self._select_field_str = ''
        self.where_str = ''
        self._order_str = ''
        self._group_str = ''
        self._limit_str = ''
        self.complex_logic = 'AND'

        if table_name:
            self.table_name = table_name

    def get_sql(self, table_name=''):
        """
        :把所有查询组装成sql
        :date 2017/9/4
        :param self:
        :return :
        """

        if not self.table_name and not table_name:
            raise InvalidArgumentError(message='没有table_name')

        if not self._select_field_str:
            raise InvalidArgumentError(message='查询字段不能为空')

        sql = "SELECT {select_field} FROM {table_name} {where} {group} {order} {limit}".format(
            select_field=self._select_field_str,
            table_name=table_name if table_name else self.table_name,
            where=self.where_str,
            order=self._order_str,
            group=self._group_str,
            limit=self._limit_str,
        )
        return sql

    def get_count_sql(self, table_name=''):
        """
        :把所有查询组装成获取查询总量的sql
        :param str table_name: 表名称
        :return :
        """

        if not self.table_name and not table_name:
            raise InvalidArgumentError(message='没有table_name')

        sql = "SELECT count(*) as total FROM {table_name} {where} {group}".format(
            table_name=table_name if table_name else self.table_name, where=self.where_str, group=self._group_str
        )
        return sql

    def get_where_str(self):
        return self.where_str

    def table(self, table_name):

        self.table_name = table_name

        return self

    def field(self, select_field):
        """
        :param select_field:查询字段
        :把所有查询目标field组装
        :date 2017/9/4
        :param self:
        :return :
        """

        if not select_field:
            raise InvalidArgumentError(message='查询字段必须')

        # 添加对字符串的支持，例如‘*’或者直接传字段的字符串列表
        if isinstance(select_field, str):
            self._select_field_str = select_field
            return self

        field_list = []
        for field in select_field:

            alias = ''

            if field.alias:
                alias = " AS `" + field.alias + '`'

            if field.field_func:

                format_str = ''

                if field.field_func_format:
                    format_str = ",'" + field.field_func_format + "'"

                field_str = "{func}({field}{format}) {alias}".format(
                    func=field.field_func, field=field.field, format=format_str, alias=alias
                )
                field_list.append(field_str)
            else:
                field_list.append(field.field + alias)

        self._select_field_str = ','.join(field_list)
        return self

    def where(self, condition_list):
        """
        :param condition_list 条件组装
        :param complex_logic 是否有组合查询
        :date 2017/9/4
        :param self:
        :return :
        """

        if not condition_list:
            return self

        where_str = ''
        for condition in condition_list:

            if condition.complex_logic:
                self.complex_logic = condition.complex_logic

            # 如果存在复合查询条件，则需要递归遍历
            if condition.complex:
                self.where(condition.complex)

                continue

            # 拼装where_str

            tmp_str = field_factory(condition).get_where_str()

            # 如果where_str空，不需要添加AND等操作符
            if where_str:
                where_str += ' ' + condition.logic + ' ' + tmp_str if tmp_str else ' '
            else:
                where_str = tmp_str

        if where_str:
            if self.where_str:
                self.where_str += ' ' + self.complex_logic + ' ( ' + where_str + ' ) '
            else:
                self.where_str = 'WHERE (' + where_str + ')'

        return self

    def order(self, order_list):
        """
        :排序
        :param self:
        :param order_list:排序list:
        :date 2017/9/5
        :return :
        """

        if not order_list:
            return self
        field_list = []
        for field in order_list:
            tmp_str = ""
            if type(field) == OrderField:
                if not field.field_sort:
                    field.field_sort = 'ASC'
                if field.field_func:

                    format_str = ''

                    if field.field_func_format:
                        format_str = ",'" + field.field_func_format + "'"
                    tmp_str = "{func}({field}{format_str}) {sort}".format(
                        func=field.field_func, field=field.field, sort=field.field_sort, format_str=format_str
                    )
                else:
                    tmp_str = field.field + " " + field.field_sort
            elif type(field) == OrderCustomField:
                if field.field_sort == "CUSTOM":
                    if not field.items or len(field.items) == 0:
                        continue
                    tmp_str = " case %s " % field.field
                    tmp_str += " ".join(
                        ["when '%s' then %d" % (elem, 100 - idx) for idx, elem in enumerate(field.items)]
                    )
                    tmp_str += " else 0 end desc"
                elif field.field_sort != '':
                    tmp_str = field.field + " " + field.field_sort
            if tmp_str != '':
                field_list.append(tmp_str)
        if len(field_list) > 0:
            self._order_str = "ORDER BY " + ','.join(field_list)
        return self

    def group(self, group_list):
        """
        :param group_list:group 分组
        :group by
        :date 2017/9/5
        :param self:
        :return :
        """

        if not group_list:
            return self

        field_list = []
        for field in group_list:

            if field.field_func:

                format_str = ''

                if field.field_func_format:
                    format_str = ",'" + field.field_func_format + "'"

                tmp_str = "{func}({field}{format})".format(func=field.field_func, field=field.field, format=format_str)
            else:
                tmp_str = field.field
            field_list.append(tmp_str)

        self._group_str = "GROUP BY %s" % ','.join(field_list)

        return self

    def limit(self, limit):
        """
        :param limit:limit offset对象 或者是 字符串
        :date 2017/9/5
        :param self:
        :return :
        """

        if not limit:
            return self

        if isinstance(limit, str) or isinstance(limit, int):
            self._limit_str = "limit %s" % limit
        else:
            self._limit_str = "limit {limit} offset {offset}".format(limit=limit.limit, offset=limit.offset)

        return self
