#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    明源api类型查询数据类
    <NAME_EMAIL> on 2018/9/18 .
"""
import json
import os
import traceback
from typing import Union

import loguru

from base.enums import DBEngine, OrderType, DatasetEditMode, QueryDataError, DatasetQueryType
from components import query_sql_encoder
from components.log_setting import Debug
from components.query_models import ModelEncoder
from components.query_structure_sql import Order, Group, Prop
from dataset.common import sql_helper
from dataset.models import DatasetApiContentModel
from dataset.query.query_data import QueryData
from dataset.query.result_data import DatasetQuerySqlException, DatasetQueryTimeOutException
from dataset.services.dataset_api_service import DatasetAPIService
from dataset.services.dataset_base_service import DatasetBaseService
from dataset.repositories import dataset_var_repository
from dataset.services.dataset_var_service import get_dataset_vars
from dmplib.utils.errors import UserError

debugger = Debug("dataset_api_query")


class ApiQueryData(QueryData):
    __slots__ = [
        'query_structure',
        'data_source_model',
        'dataset_data',
        'web_time_out',
        'dataset_fields',
        'is_order_master_id',
    ]

    def __init__(self, query_structure, data_source_model, dataset_data, dataset_fields=None, is_order_master_id=False):
        """
       初始化QueryStructure 对象
       :param components.query_models.QueryStructure query_structure:
       :param data_source.models.DataSourceModel data_source_model:
       :param dict dataset_data:
       """
        self.data_source_model = data_source_model
        self.dataset_data = dataset_data
        self.web_time_out = int(os.environ.get('TIMEOUT', 60)) - 1
        self.dataset_fields = dataset_fields
        self.is_order_master_id = is_order_master_id
        super().__init__(query_structure)

    def append_ads_order_by(self, params):
        is_ads = False
        for param in params:
            if (
                    param.get("name")
                    and param.get("value")
                    and param.get("name") == "db_engine"
                    and param.get("value") == DBEngine.ADS.value
            ):
                is_ads = True
                break
        # ads引擎
        if not (is_ads and self.dataset_fields):
            return

        self.dataset_fields = sorted(self.dataset_fields, key=lambda e: e.__getitem__('rank'))
        if self.is_order_master_id and not self.query_structure.group_by:
            self.add_master_id_order_by()
        elif not self.is_order_master_id and self.query_structure.group_by:
            append_order_by = self._get_append_order_by()
            if append_order_by:
                self.query_structure.order_by = [*self.query_structure.order_by, *append_order_by]

    @staticmethod
    def convert_group2order(group: Group) -> Order:
        order = Order()
        for slot in group.__slots__:
            setattr(order, slot, getattr(group, slot))
        order.method = 'ASC'
        return order

    def get_group_by_prop_name_dict(self) -> dict:
        prop_name_dict = {}
        if not self.query_structure.group_by:
            return prop_name_dict
        for idx, obj in enumerate(self.query_structure.group_by):
            prop_name = self._get_prop_name(obj)
            if prop_name is None:
                continue
            prop_name_dict[self._get_prop_name(obj)] = obj
        return prop_name_dict

    def get_order_by_prop_name_dict(self) -> dict:
        prop_name_dict = {}
        if not self.query_structure.order_by:
            return prop_name_dict
        for idx, obj in enumerate(self.query_structure.order_by):
            prop_name = self._get_prop_name(obj)
            if prop_name is None:
                continue
            prop_name_dict[self._get_prop_name(obj)] = obj
        return prop_name_dict

    def _get_append_order_by(self) -> list:
        group_by_prop_name_dict = self.get_group_by_prop_name_dict()
        if not group_by_prop_name_dict:
            return []
        order_by_prop_name_dict = self.get_order_by_prop_name_dict()
        diff_prop_name = sorted(
            list(set(group_by_prop_name_dict.keys()).difference(set(order_by_prop_name_dict.keys())))
        )
        if not diff_prop_name:
            return []
        return [self.convert_group2order(group_by_prop_name_dict[prop_name]) for prop_name in diff_prop_name]

    def _get_prop_name(self, data: Prop) -> Union[str, None]:
        if getattr(data, "prop_name", None):
            return data.prop_name
        for attr in ["props", "conditions"]:
            list_data = getattr(data, attr, [])
            for prop in list_data:
                prop_name = self._get_prop_name(prop)
                if prop_name is not None:
                    return prop_name
        for attr in ["left", "right"]:
            prop = getattr(data, attr, None)
            if prop:
                prop_name = self._get_prop_name(prop)
                if prop_name is not None:
                    return prop_name
        return None

    def add_master_id_order_by(self):
        for dataset_field in self.dataset_fields:
            if dataset_field.get("origin_col_name") != "master_id":
                continue
            order = Order()
            order.obj_name = (
                dataset_field.get("origin_table_alias_name")
                if dataset_field.get("origin_table_alias_name")
                else dataset_field.get("origin_table_name")
            )
            order.prop_name = dataset_field.get("origin_col_name")
            order.method = OrderType.Asc.value
            self.query_structure.order_by.append(order)
            break

    def generate_sql(self) -> str:
        return query_sql_encoder.encode_query(self.query_structure)

    def get_query_data(self, sql_=None):
        """
        根据QueryStructure对象获取查询数据（重写父类的查询数据函数）
        :return:
        """
        dataset_content = DatasetBaseService.load_dataset_content(self.dataset_data.get("content"))
        api = DatasetAPIService.get_api(self.data_source_model)

        content_model = DatasetApiContentModel(**dataset_content)
        params = DatasetAPIService.merge_params(self.data_source_model.conn_str.params, content_model.params)

        self.get_query_structure()
        sql = self.generate_sql()
        table_names = sql_helper.extract_tables(sql)
        complex_params = DatasetAPIService.get_complex_params(table_names, self.data_source_model)
        third_user_info = DatasetAPIService.get_third_user_info(self.data_source_model)
        try:
            result_data = api.get_data_list(
                params,
                json.loads(json.dumps(self.query_structure, cls=ModelEncoder)),
                table_names=table_names,
                timeout=self.web_time_out,
                complex_params=complex_params,
                third_user_info=third_user_info,
            )
        except UserError as ue:
            if ue.message.startswith("请求接口超时"):
                raise DatasetQueryTimeOutException(
                    msg="运行api数据集sql时长超过{}秒，{}".format(str(self.web_time_out), "请求接口超时"), sql=sql,
                    error_code=QueryDataError.TimeoutError.value,
                    dataset_query_type=DatasetQueryType.Api.value
                )
            raise DatasetQuerySqlException(
                msg="运行api数据集内部错误，" + ue.message, sql=sql,
                error_code=QueryDataError.OtherError.value,
                dataset_query_type=DatasetQueryType.Api.value
            )

        if not result_data.get('result'):
            msg = result_data.get('msg')
            if "熔断" in msg:
                error_code = QueryDataError.FusingError.value
            else:
                error_code = QueryDataError.ExternalError.value
            raise DatasetQuerySqlException(
                msg="运行api数据集外部错误，" + result_data.get('msg'), sql=sql, error_code=error_code,
                dataset_query_type=DatasetQueryType.Api.value
            )

        self._group_concat_distinct(result_data)
        return result_data.get('data'), sql, result_data.get('data_update_time')

    def replace_dataset_sql_vars(self):
        """
        替换API数据集SQL模式的数据集变量为报告传入的值或者使用数据集变量的默认值
        :return:
        """
        # if self.dataset_data['edit_mode'] != DatasetEditMode.Sql.value
        #    return
        # 视图模式也存在变量

        dataset_id = self.dataset_data['id']
        dataset_vars = get_dataset_vars(dataset_id)
        if not dataset_vars:
            return

        dataset_vars_map = {r['id']: r for r in dataset_vars}
        query_vars = self.query_structure.vars
        query_vars_map = {}
        for query_var in query_vars:
            var_id = query_var.var_id
            var_data = query_var.get_dict()
            var_data['id'] = var_id
            query_vars_map[var_id] = var_data
        dataset_vars_map.update(query_vars_map)
        vars_data_list = list(dataset_vars_map.values())

        for query_object in self.query_structure.object:
            query_object.name = DatasetAPIService.replace_dataset_sql_var_id_as_values(
                query_object.name, vars_data_list
            )
        for where_obj in self.query_structure.where:
            if where_obj:
                DatasetAPIService.relace_where_sql_vars(where_obj, vars_data_list, is_api=True)

    def set_default_order_by(self):
        self.group2order()
        self.filed2order(self.dataset_fields, field_key='origin_col_name')

    def get_query_structure(self):
        dataset_content = DatasetBaseService.load_dataset_content(self.dataset_data.get("content"))
        content_model = DatasetApiContentModel(**dataset_content)
        params = DatasetAPIService.merge_params(self.data_source_model.conn_str.params, content_model.params)

        # ads 数据库需要追加排序字段 (后期改为数据集定义指定排序字段)
        self.append_ads_order_by(params)

        # 如果没有排序，则指定第一个字段为排序字段
        self.set_default_order_by()

        # 调用python解析sql
        self.replace_dataset_sql_vars()
        return self.query_structure

    def _group_concat_distinct(self, result):
        data = result.get('data', [])
        try:
            for item in data:
                for k, v in item.items():
                    if k.startswith('group_concat_'):
                        # item[k] = ','.join(set((item[k] or '').split(',')))
                        item[k] = ','.join(set(item[k].split(','))) if isinstance(item[k], str) else item[k]
        except:
            loguru.logger.error('api数据集group_concat_distinct错误：%s' % traceback.format_exc(()))
