#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Mysql类型查询数据类
    <NAME_EMAIL> on 2018/9/18.
"""
import logging

from dmplib.db import errors
from components.log_setting import Debug
from dataset.query.result_data import DatasetQuerySqlException
from typing import Optional
from dataset.query.sql_query_data import SqlQueryData
from components.analysis_time import AnalysisTimeUtils

debugger = Debug("mysql_query_data")

logger = logging.getLogger(__name__)


class MysqlQueryData(SqlQueryData):
    def get_query_data(self, sql: Optional[str] = None):
        """
        根据QueryStructure对象获取查询数据（重写父类的查询数据方法）
        :return:
        """
        st = AnalysisTimeUtils.now()
        sql = sql if sql else self.generate_sql()
        debugger.log("数据集查询数据sql：{}", sql)
        try:
            with self.connection_db as db:
                result = db.query(sql), sql, None
                AnalysisTimeUtils.record(
                    step=None, sql=sql, db_type=AnalysisTimeUtils.db_type.Mysql.value, start_time=st,
                    extra={}, need_type_inference=True
                )
                return result
        except (errors.ColumnNotFoundError, errors.TableNotFoundError) as ne:
            raise DatasetQuerySqlException(code=404, msg=str(ne), sql=sql) from ne
        except Exception as e:
            msg = "数据集查询数据错误：{}，sql：{}".format(str(e), sql)
            raise DatasetQuerySqlException(msg=msg, sql=sql) from e

    @staticmethod
    def is_dynamic_sql(sql):
        return sql.find("CURDATE()") > -1 or sql.find("CURTIME()") > -1
