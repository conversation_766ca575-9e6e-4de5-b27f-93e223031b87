#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Mysql类型查询数据类
    <NAME_EMAIL> on 2018/9/18.
"""
import logging
from collections import namedtuple
from typing import Dict
import uuid
import json

from base.enums import DatasetEditMode, DBEngine, SqlComplexOrNot, DataSourceType, DatasetConnectType, DatasetQueryType, \
    QueryDataError
from dataset.services.dataset_base_service import DatasetBaseService
from dataset.services.dataset_var_service import get_dataset_vars
from dmplib.utils.errors import UserError
from dmplib.hug import g
from components.log_setting import Debug
from components import query_sql_encoder
from components.data_center_api import get_data_by_sql, get_preview_data_of_sql, get_new_erp_datasource_model
from components.query_structure_sql import QueryStructure, Object, Order
from dataset.query.result_data import DatasetQuerySqlException, DatasetQueryException
from typing import Optional
from dataset.query.query_data import QueryData
from data_source.models import DataSourceModel
from data_source.services.data_source_service import load_data_source_conn_str
from components.analysis_time import AnalysisTimeUtils

debugger = Debug("mysql_query_data")

logger = logging.getLogger(__name__)

mdc_primary_key = "MdcDataGUID"


class MySoftNewErpQueryData(QueryData):
    __slots__ = ['query_structure', 'data_source_model', 'dataset_data', 'connection_db', 'Db',
                 'complex_sql', 'table_placeholder', 'is_complex', 'db_engine', 'AppLevelCode', '_dc_data_source',
                 'real_model']

    def __init__(self, query_structure: QueryStructure, data_source_model: DataSourceModel, dataset_data: Dict) -> None:
        super().__init__(query_structure)
        self.query_structure = query_structure
        self.data_source_model = data_source_model
        self.Db = namedtuple("Db", ['conn', 'host', 'db', 'sql'])
        if not hasattr(self.data_source_model.conn_str, "AppLevelCode"):
            self.AppLevelCode = "mysql"
        else:
            self.AppLevelCode = self.data_source_model.conn_str.AppLevelCode
        self.connection_db = self.Db("mysoft_new_erp", self.AppLevelCode,
                                     self.data_source_model.conn_str.Database, "")
        self.dataset_data = dataset_data
        self.complex_sql = ""
        self.table_placeholder = ""
        self.is_complex = SqlComplexOrNot.Sample.value
        self.db_engine = self.data_source_model.conn_str.DbType.lower()
        self._dc_data_source = None
        self.real_model = None

    def _generate_sql_of_direct_model(self, target_engine=None):
        """
        生成直连模式sql
        :param target_engine: 数据服务中心复杂sql数据存储的db类型
        :return:
        """
        # 复杂sql用占位符替换
        if self.is_complex == SqlComplexOrNot.Complex.value:
            if len(self.query_structure.object) == 1:
                if not self.table_placeholder:
                    self.table_placeholder = self.__generate_placeholder_of_table_name()
                if not self.complex_sql:
                    self.complex_sql = self.query_structure.object[0].name.strip()
                    if self.complex_sql.startswith("("):
                        self.complex_sql = self.complex_sql[1:][:-1]
                self.query_structure.object = [Object(name=self.table_placeholder)]
            else:
                raise UserError(message=f"错误的复杂sql object:{str(self.query_structure.object)}")
        if target_engine in [DBEngine.MSSQL.value, DBEngine.SqlServer.value] or \
                self.db_engine in [DBEngine.MSSQL.value, DBEngine.SqlServer.value]:
            sql = self._generate_mssql()
        elif self.db_engine == DBEngine.ORACLE.value:
            sql = query_sql_encoder.encode_query(self.query_structure, DBEngine.ORACLE.value)
        elif self.db_engine == DBEngine.DM.value:
            sql = query_sql_encoder.encode_query(self.query_structure, DBEngine.DM.value)
        else:
            sql = query_sql_encoder.encode_query(self.query_structure)
        return sql

    def _generate_mssql(self):
        sql = query_sql_encoder.encode_query(self.query_structure, self.db_engine)
        # groups = re.findall(r"DATE_FORMAT\((.*?)\)", sql)
        # if groups:
        #     for item in groups:
        #         field, data_str = item.split(",")
        #         sql = sql.replace(item, f"VARCHAR(19),{field},20")
        #     sql = sql.replace("DATE_FORMAT", "CONVERT")
        return sql

    def _generate_sql_of_schedule_model(self):
        return self._generate_mssql()

    def generate_sql(self) -> str:
        # exp = get_setting("mdc_direct_cache", "data_center")
        # 区分调度模式和直连模式，获取的数据源不同

        if self.dataset_data.get("connect_type") == DatasetConnectType.Directly.value:
            model = self.data_source_model
            # 获取简单sql和复杂sql标识
            self.is_complex = int(self.dataset_data.get("is_complex", 0))
            # cache_type = "r"
            # 复杂sql的 取数sql语法 以数据服务中心的db_type为准
            if self.is_complex == SqlComplexOrNot.Complex.value:
                self.db_engine = self.dc_datasource_model.conn_str.DbType.lower()
                # 实时复杂sql数据集添加mdc主键排序
                # self.add_mdc_primary_key_order_by()
        else:
            # MysoftNewERP需要获取特定的数据源
            # 获取mysoftNewErpSource
            model = self.dc_datasource_model
            self.db_engine = model.conn_str.DbType.lower()
            self.is_complex = 1
            # 定时数据集添加mdc主键排序
            # self.add_mdc_primary_key_order_by()
            # cache_type = "m"

        self.replace_dataset_sql_vars()

        # 兼容数据升级场景
        if self.is_complex == SqlComplexOrNot.Unknown.value:
            # preview_sql = json.loads(self.dataset_data.get("content", '"{}"')).get("sql")
            preview_sql = self.replace_sql_vars()
            rs = get_preview_data_of_sql(preview_sql, model, is_need_column_struct=False)
            self.is_complex = SqlComplexOrNot.Complex.value if rs.get("IsComplexSql",
                                                                      False) is True else SqlComplexOrNot.Sample.value
        if self.dataset_data.get("type") in ["UNION", "EXCEL"]:
            self.db_engine = model.db_type.lower() or self.db_engine
        self.real_model = model

        if self.dataset_data.get("connect_type") == DatasetConnectType.Directly.value:
            sql = self._generate_sql_of_direct_model()
        else:
            sql = self._generate_sql_of_schedule_model()

        self.connection_db = self.Db("mysoft_new_erp", self.AppLevelCode,
                                     self.data_source_model.conn_str.Database, sql)
        return sql

    def add_mdc_primary_key_order_by(self):
        # 存在排序就在后面添加mdc主键排序 不存在就不需要管
        if not self.query_structure.order_by:
            self.query_structure.order_by = []

        if len(self.query_structure.order_by) >= 0:
            my_order_by = Order()
            my_order_by.prop_name = mdc_primary_key
            my_order_by.method = 'ASC'
            self.query_structure.order_by.append(my_order_by)

    @staticmethod
    def __generate_placeholder_of_table_name():
        """
        生成复杂sql占位符
        """
        return f"dataset_tmp_{str(uuid.uuid4()).replace('-', '')}"

    def replace_dataset_sql_vars(self):
        """
        替换API数据集SQL模式的数据集变量为报告传入的值或者使用数据集变量的默认值
        :return:
        """
        # if self.dataset_data.get('edit_mode') != DatasetEditMode.Sql.value:
        #    return
        # sql模式视图模式都支持变量

        setattr(g, 'db_engine', self.db_engine)

        dataset_id = self.dataset_data['id']
        dataset_vars = get_dataset_vars(dataset_id)
        if not dataset_vars:
            return

        dataset_vars_map = {r['id']: r for r in dataset_vars}
        query_vars = self.query_structure.vars
        query_vars_map = {}
        for query_var in query_vars:
            var_id = query_var.var_id
            var_data = query_var.get_dict()
            var_data['id'] = var_id
            query_vars_map[var_id] = var_data
        dataset_vars_map.update(query_vars_map)
        vars_data_list = list(dataset_vars_map.values())

        for query_object in self.query_structure.object:
            query_object.name = DatasetBaseService.replace_dataset_sql_var_id_as_values(
                query_object.name, vars_data_list
            )
        for where_obj in self.query_structure.where:
            if where_obj:
                DatasetBaseService.relace_where_sql_vars(where_obj, vars_data_list)

    def replace_sql_vars(self, ):
        """
        替换API数据集SQL模式的数据集变量为报告传入的值或者使用数据集变量的默认值
        :return:
        """
        # if self.dataset_data.get('edit_mode') != DatasetEditMode.Sql.value:
        #    return

        # sql模式视图模式都支持变量
        dataset_id = self.dataset_data['id']
        dataset_vars = get_dataset_vars(dataset_id)
        if not dataset_vars:
            return

        dataset_vars_map = {r['id']: r for r in dataset_vars}
        query_vars = self.query_structure.vars
        query_vars_map = {}
        for query_var in query_vars:
            var_id = query_var.var_id
            var_data = query_var.get_dict()
            var_data['id'] = var_id
            query_vars_map[var_id] = var_data
        dataset_vars_map.update(query_vars_map)
        vars_data_list = list(dataset_vars_map.values())

        sql = json.loads(self.dataset_data.get("content", '"{}"')).get("sql")

        return DatasetBaseService.replace_dataset_sql_var_id_as_values(
            sql, vars_data_list
        )



    @property
    def dc_datasource_model(self):
        """
        数据服务中心数据源
        """
        if self._dc_data_source is None:
            self._dc_data_source = get_new_erp_datasource_model(
                self.dataset_data.get("content"),
                self.data_source_model.conn_str
            )
        return self._dc_data_source

    def get_query_data(self, sql: Optional[str] = None):
        """
        根据QueryStructure对象获取查询数据（重写父类的查询数据方法）
        :return:
        """
        sql = sql if sql else self.generate_sql()
        model = self.real_model
        debugger.log("数据集查询数据sql：{}", sql)
        st = AnalysisTimeUtils.now()
        try:
            # 目前默认数据服务中心目标db类型为sqlserver
            sql_target_text = self._generate_sql_of_direct_model(target_engine=self.db_engine)

            # 直连数据集设置缓存需设置过期时间， 调度数据集在清洗后清理缓存
            # 直连数据集从redis取， 调度数据集从mysql取
            data = []
            if not data:
                dataset_id = self.dataset_data.get('id') or ''
                result = get_data_by_sql(sql, model, sql_type=self.is_complex,
                                         table_place_holder=self.table_placeholder, table_text=self.complex_sql,
                                         sql_target_text=sql_target_text, dataset_id=dataset_id)
                data = result["Data"]
            AnalysisTimeUtils.record(
                step=None, sql=sql, db_type=AnalysisTimeUtils.db_type.MysoftNewERP.value, start_time=st,
                extra={}, need_type_inference=True
            )
            return data, sql, None
        except UserError as e:
            raise DatasetQueryException(
                msg=e.message, sql=sql, error_code=getattr(e, 'error_code', QueryDataError.AnyError.value),
                dataset_query_type=DatasetQueryType.DataCenter.value
            ) from e
        except Exception as e:
            self.judge_error_404_info(e, sql)
            msg = "数据集查询数据错误：{}，sql：{}".format(str(e), sql)
            raise DatasetQuerySqlException(
                msg=msg, sql=sql,
                error_code=getattr(e, 'error_code', QueryDataError.AnyError.value),
                dataset_query_type=DatasetQueryType.DataCenter.value
            ) from e

    def judge_error_404_info(self, e, sql):
        dataset_name = self.dataset_data.get("table_name")
        if f"对象名 '{dataset_name}' 无效" in str(e):  # sqlserver
            raise DatasetQuerySqlException(code=404, msg=str(e), sql=sql) from e  # NOSONAR
        elif f"{dataset_name}' doesn't exist" in str(e):  # mysql
            raise DatasetQuerySqlException(code=404, msg=str(e), sql=sql) from e  # NOSONAR

    @staticmethod
    def is_dynamic_sql(sql):
        sql = sql.lower()
        slices = [
            "getdate()", "day()", "month()", "year()",  # SqlServer
            "curdate()", "curtime()"  # mysql
        ]
        for s in slices:
            if sql.find(s) > -1:
                return True
        return False
