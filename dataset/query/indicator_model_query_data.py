import json
import time
import traceback
from copy import deepcopy
from random import choice
from loguru import logger
import jwt

from components import dynamics_config
from components.pular_api import PulsarApi
from components.redis_utils import stale_cache
from components.utils import get_default_account
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.constants import ADMINISTRATORS_ID
from dataset.query.result_data import DatasetQuerySqlException, DatasetQueryException
from base.models import BaseModel
from base.enums import ExternalDatasetType, PulsarQueryMode, QueryDataError, DatasetQueryType
from base import repository
from components.analysis_time import AnalysisTimeUtils
from components.common_external_api_query_deal import deal_external_api_biz_params
from dataset.query.query_data import QueryData

FORMULA_MODE_MAP = {
    "year": "DatetimeFormatYear",
    "month": "DatetimeFormatMonth",
    "quarter": "DatetimeFormatQuarter",
    "week": "DatetimeFormatWeek",
    "day": "DatetimeFormatDay",
    "hour": "DatetimeFormatHour",
    "minute": "DatetimeFormatMinute",
    "second": "DatetimeFormatSecond",
    "group_concat": None  # 前端历史bug, 普通筛选不应该在num里面传group_concat
}

DATA_FORMAT_VALUE_MAP = {
    "%Y": "DatetimeFormatYear",
    "%Y-%m": "DatetimeFormatMonth",
    "%Y-%m-%d": "DatetimeFormatDay",
    "%Y-%m-%d %H": "DatetimeFormatHour",
    "%Y-%m-%d %H:%i": "DatetimeFormatMinute",
    "%Y-%m-%d %H:%i:%S": "DatetimeFormatSecond",
}

PREDICATE_TYPE = {
    "between": "Between",
    "in": "InList",
    "not in": "InList",
    "like": "Like",
    ">": "Comparison",
    ">=": "Comparison",
    "<": "Comparison",
    "<=": "Comparison",
    "is not": "Comparison",
    "is": "Comparison",
}

OPT_TYPE = {
    "=": "Eq",
    "is": "IsNull",
    "!=": "Neq",
    "<>": "Neq",
    "is null": "IsNull",
    "is not null": "IsNotNull",
    ">": "Gt",
    ">=": "Gte",
    "<": "Lt",
    "<=": "Lte",
    "is not": "IsNotNull"
}

VALUE_TYPE = {
    "CASE_WHEN": "IntegerValue",
    "数值": "FloatValue",
    "字符串": "StringValue",
    None: "NullValue"
}

PPT_DATA_FORMAT_NAME_MAP = {
    "%Y": "year",
    "%Y-%m": "month",
    "%Y-%m-%d": "day",
    "%Y-%m-%d %H": "hour",
    "%Y-%m-%d %H:%i": "minute",
    "%Y-%m-%d %H:%i:%S": "second",
    "%Y-%W": "week",
    "%Y-%Q": "quarter",
}


class IndicatorModelQuery(QueryData):

    def __init__(self, data_source_model, dataset_data, chart_data_model, dataset_fields=None, field_map=None,
                 query_structure=None):
        self.data_source_model = data_source_model
        self.dataset_data = dataset_data
        self.dataset_fields = dataset_fields
        self.pulsar_api = PulsarApi()
        self.chart_data_model = chart_data_model
        self.field_maps = field_map or {item.get('id'): item for item in dataset_fields}
        self.col_name_map = {item.get('col_name'): item for item in dataset_fields}
        self.query_structure = query_structure
        self.select_fields_map = self.get_select_field_map()
        self.query_mode = "Agg"
        super().__init__(query_structure)

    @staticmethod
    def dim_formula_mode_trans(formula_mode: str):
        if formula_mode not in FORMULA_MODE_MAP:
            raise UserError(message="不支持的格式处理")
        return FORMULA_MODE_MAP.get(formula_mode)

    @staticmethod
    def get_col_alias(formula_mode, col_name, alias_name):
        from dashboard_chart.convertor.field_base import FieldBase

        return FieldBase().get_dataset_field_alias(formula_mode, col_name, alias_name)

    def get_query_item(self, item: dict, field_id: str):
        """
        获取 item
        :param item:
        :param field_id: dim   num
        :return:
        """
        field = self.field_maps.get(item.get(field_id))
        alias = self.get_col_alias(item.get('formula_mode'), field.get("col_name"), field.get("alias"))
        if item.get("query_type") == "desire":
            alias = f"desire_{alias}"
        if field.get("field_group") == "维度":
            return {
                "expression": {
                    "type": "Field",
                    "field_content": {
                        "field_type": "Dimension",
                        "table_name": field.get('origin_table_name'),
                        "field_name": field.get("origin_col_name"),
                        "op_type": self.dim_formula_mode_trans(item.get('formula_mode')) if item.get(
                            'formula_mode') else "NoFieldOpType"
                    }
                },
                "alias": alias
            }
        else:
            return {
                "alias": alias,
                "expression": {
                    "type": "Field",
                    "field_content": {
                        "field_type": "Indicator",
                        "code": field.get("external_id")
                    }
                }}

    def get_custom_order_item(self, item, select_alias, field):
        """
        获取自定义排序item
        :param item:
        :param select_alias:
        :param field:
        :return:
        """
        alias = select_alias or item.alias

        def case_when_trans_op_params():
            op_params = []
            for prop in item.props:
                data_type = field.get('data_type')
                if prop.specifier == "ELSE":
                    value2 = prop.props[0].value
                    field['col_value'] = str(value2) if not isinstance(value2, str) else value2   # 数芯要求这个地方类型是integer , 值传string
                    field['data_type'] = "CASE_WHEN"
                    op_params.append(self.get_value_content(field, _type='case'))
                else:
                    sub = prop.conditions[0]
                    value1 = sub.right.value
                    field['col_value'] = value1
                    op_params.append(self.get_value_content(field, _type='case'))
                    value2 = prop.props[0].value
                    field['col_value'] = str(value2) if value2 else ''
                    field['data_type'] = "CASE_WHEN"
                    op_params.append(self.get_value_content(field, _type='case'))
                del field['col_value']
                field['data_type'] = data_type
            return op_params

        if field.get("field_group") == "维度":
            return {
                "expression": {
                    "type": "Field",
                    "field_content": {
                        "field_type": "Alias",
                        "alias_name": alias,
                        "op_type": "CaseWhenEqual",
                        "op_params": case_when_trans_op_params()
                    }
                },
                "alias": alias
            }
        else:
            raise UserError(message=f"指标字段不支持自定义排序")

    def get_query_item_v2(self, field: dict):
        """
        获取 item
        :param field:
        :return:
        """
        alias = field.get('alias')

        def get_item(field_type):
            return {
                "expression": {
                    "type": "Field",
                    "field_content": {
                        "field_type": field_type,
                        "table_name": field.get('origin_table_name'),
                        "field_name": field.get("origin_col_name"),
                        "op_type": self.dim_formula_mode_trans(field.get('formula_mode')) if field.get(
                            'formula_mode') else "NoFieldOpType"
                    }
                },
                "alias": alias
            }

        if field.get("field_group") == "维度":
            return get_item('Dimension')
        elif self.query_mode in [
                PulsarQueryMode.DETAIL.value, PulsarQueryMode.DETAILCOUNT.value] \
                and field.get("field_group") == "度量":
            return get_item('Measure')
        else:
            return {
                "alias": alias,
                "expression": {
                    "type": "Field",
                    "field_content": {
                        "field_type": "Indicator",
                        "code": field.get("external_id")
                    }
                }}

    def get_select_field(self, items: list):
        """
        dim: 表名 + 字段名
        :param items:
        :return:
        """
        dimensions = []
        for item in items or []:
            dimensions.append(self.get_query_item(item, 'dataset_field_id'))
        return dimensions

    def get_select_of_num(self, nums: list):
        """
        num: indicator_id
        :param nums:
        :return:
        """
        indicators = []
        for num in nums or []:
            formula_mode = num.get('formula_mode')
            if formula_mode:
                raise UserError(message='指标字段不支持聚合')
            indicators.append(self.get_query_item(num, 'dataset_field_id'))
        return indicators

    def get_field_type(self, field_group: str):
        if field_group == "度量" and self.query_mode in [PulsarQueryMode.DETAIL.value, PulsarQueryMode.DETAILCOUNT.value]:
            return "Measure"
        elif field_group in ["度量"]:
            return "Indicator"
        else:
            return "Dimension"

    def get_field_content(self, item, field):
        field_type = self.get_field_type(item.get("field_group"))
        if field_type in ["Dimension", "Measure"]:
            return {
                "field_type": field_type,
                "table_name": field.get("origin_table_name"),
                "field_name": field.get('origin_col_name')
            }
        else:
            return {
                "field_type": field_type,
                "code": field.get("external_id")
            }

    @staticmethod
    def get_value_type(data_type):
        return VALUE_TYPE.get(data_type, "StringValue")

    def get_value_content(self, item, _type="single"):
        if _type == "list":
            col_value = json.loads(item.get("col_value"))
            if not isinstance(col_value, list):
                raise UserError(message=f"col_value 错误：{col_value}")
            return [
                {
                    "type": "Value",
                    "value_content": {
                        "type": self.get_value_type(item.get("data_type")),
                        "value": value
                    }
                } for value in col_value
            ]
        elif _type == 'between':
            col_value = json.loads(item.get("col_value"))
            if not isinstance(col_value, list):
                raise UserError(message=f"col_value 错误：{col_value}")
            return {
                "lower": {
                    "type": "Value",
                    "value_content": {
                        "type": self.get_value_type(item.get("data_type")),
                        "value": col_value[0]
                    }
                },
                "upper": {
                    "type": "Value",
                    "value_content": {
                        "type": self.get_value_type(item.get("data_type")),
                        "value": col_value[-1]
                    }
                }}
        elif _type == "case":
            return {
                "type": "Value",
                "value_content": {
                    "type": self.get_value_type(item.get("data_type")),
                    "value": item.get('col_value')
                }
            }
        else:
            return {
                "type": "Value",
                "value_content": {
                    "type": self.get_value_type(item.get("data_type")),
                    "value": item.get('col_value')
                }
            }

    def get_predicate(self, item):
        predicate_type = PREDICATE_TYPE.get(item.get('operator'), "Comparison")
        if predicate_type == "Comparison":
            return {
                "type": "Comparison",
                "comparison": {
                    "op": OPT_TYPE.get(item.get('operator')),
                    "right": self.get_value_content(item)
                }
            }
        elif predicate_type == "InList":
            return {
                "type": "InList",
                "in_list": {
                    "not": "not " in item.get('operator'),
                    "values": self.get_value_content(item, _type='list')
                }
            }
        elif predicate_type == "Between":
            return {
                "type": "Between",
                "between": self.get_value_content(item, _type='between')
            }
        elif predicate_type == "Like":
            return {
                "type": "Like",
                "like": {
                    "values": self.get_value_content(item)
                }
            }
        else:
            raise UserError(message=f"暂不支持的类型: {predicate_type}")

    def get_value_content_v3(self, item, _type="single"):
        field = self.get_field(item.left)
        field_type = self.get_value_type(field.get("data_type"))
        col_value = item.right.value
        if _type == "list":
            if not isinstance(col_value, list):
                raise UserError(message=f"col_value 错误：{col_value}")
            return [
                {
                    "type": "Value",
                    "value_content": {
                        "type": field_type,
                        "value": str(value) if value is not None and not isinstance(value, str) else value
                    }
                } for value in col_value
            ]
        elif _type == 'between':
            if not isinstance(col_value, list):
                raise UserError(message=f"col_value 错误：{col_value}")
            return {
                "lower": {
                    "type": "Value",
                    "value_content": {
                        "type": field_type,
                        "value": str(col_value[0]) if col_value[0] is not None and not isinstance(col_value[0], str) else col_value[0]
                    }
                },
                "upper": {
                    "type": "Value",
                    "value_content": {
                        "type": field_type,
                        "value": str(col_value[-1]) if col_value[-1] is not None and not isinstance(col_value[-1], str) else col_value[-1]
                    }
                }}
        else:
            value = item.right.value
            if value is None:
                field_type = "NullValue"
            return {
                "type": "Value",
                "value_content": {
                    "type": field_type,
                    "value": str(value) if value is not None and not isinstance(value, str) else value
                }
            }

    def get_predicate_v3(self, item):
        operator = (getattr(item, 'operator', '') or '').lower()
        predicate_type = PREDICATE_TYPE.get(operator, "Comparison")
        if predicate_type == "Comparison":
            # isnull 需要单独处理
            op = OPT_TYPE.get(operator)
            return {
                "type": "Comparison",
                "comparison": {
                    "op": op,
                    "right": self.get_value_content_v3(item)
                }
            }
        elif predicate_type == "InList":
            return {
                "type": "InList",
                "in_list": {
                    "not": "not " in operator,
                    "values": self.get_value_content_v3(item, _type='list')
                }
            }
        elif predicate_type == "Between":
            return {
                "type": "Between",
                "between": self.get_value_content_v3(item, _type='between')
            }
        elif predicate_type == "Like":
            return {
                "type": "Like",
                "like": {
                    "value": self.get_value_content_v3(item)
                }
            }
        else:
            raise UserError(message=f"暂不支持的类型: {predicate_type}")

    def get_sub_filters(self, conditions: list):
        """
        single filters
        :param conditions:
        :return:
        """
        sub_filters = []
        if not conditions:
            return None
        where_obj = {
            "type": "FilterGroup",
            "pre_not": False,
            "associate_type": "And",
            "sub_filters": sub_filters
        }
        for item in conditions:
            copy_item = deepcopy(item)
            copy_item.update(copy_item.get("dim", {}))  # 兼容筛选场景
            field = self.field_maps.get(copy_item.get('dataset_field_id'))
            sub_filters.append({
                "type": "Predicate",
                "subject": {
                    "type": "Field",
                    "field_content": self.get_field_content(copy_item, field)
                },
                "predicate": self.get_predicate(copy_item)
            })
        return where_obj

    def get_where(self, filters, chart_filter_conditions):
        """
        filter
        :return:
        """
        if not filters and not chart_filter_conditions:
            return None
        sub_filters = []
        where_obj = {
            "type": "FilterGroup",
            "pre_not": False,
            "associate_type": "And",
            "sub_filters": sub_filters
        }

        # 过滤条件
        filter_1 = self.get_sub_filters(filters)
        filter_1 and sub_filters.append(filter_1)

        # 筛选
        filter_2 = self.get_sub_filters(chart_filter_conditions)
        filter_2 and sub_filters.append(filter_2)

        return where_obj

    def get_order(self, orders: list):
        """
        order
        :return:
        """
        if not orders:
            return []
        ordering_map = {
            "desc": "Desc",
            "asc": "Asc"
        }
        sorts = []
        for item in orders:
            dataset_field_id = item.get('dataset_field_id')
            if item.get("field_source") == "dims":
                item = repository.get_data("dashboard_chart_dim", {'dim': dataset_field_id})
            else:
                item = repository.get_data("dashboard_chart_num", {'num': item.get('dataset_field_id')})
            item['dataset_field_id'] = dataset_field_id
            sorts.append({
                "query_item": self.get_query_item(item, 'dataset_field_id'),
                "ordering": ordering_map.get(item.get("sort", "").lower(), 'Asc')
            })
        return sorts

    def get_select_alias_of_order(self, field):
        """
        get_select_field_of_order
        """
        for i in self.query_structure.select:
            item = self.get_field(i)
            if item.get('col_name') == field.get('col_name'):
                return i.alias
        return ""

    def get_order_v3(self):
        """
        order
        :return:
        """
        ordering_map = {
            "desc": "Desc",
            "asc": "Asc"
        }
        sorts = []
        for item in getattr(self.query_structure, 'order_by', []):
            field = self.get_field(item)
            select_alias = self.get_select_alias_of_order(field)
            # order by 的item.alias需要和select字段保持一致, 后面需要数芯支持 order by不用传alias
            if getattr(item, 'specifier', None) == "CASE":
                order_item = self.get_custom_order_item(item, select_alias, field)
            else:
                order_item = self.get_query_item_v2(field)

            order_item['alias'] = order_item['alias'] or select_alias
            sorts.append({
                "query_item": order_item,
                "ordering": ordering_map.get(item.method.lower(), 'Asc')
            })
        return sorts

    def get_limit(self, pagination: dict):
        """
        limit
        :return:
        """
        if not pagination:
            return None
        if isinstance(pagination, BaseModel):
            pagination = pagination.get_dict()
        page = pagination.get('page') or 1
        size = pagination.get('page_size')
        if not size:
            return {}
        return {
            "offset": (page - 1) * size,
            "limit": size
        }

    def get_limit_v3(self):
        """
        limit
        :return:
        """
        if getattr(self.query_structure, 'limit', {}) and self.query_structure.limit.row:
            return {
                "offset": self.query_structure.limit.offset,
                "limit": self.query_structure.limit.row
            }
        else:
            return {}

    def get_query_structure(self) -> dict:
        """
        构造query结构
        :return:
        """
        # 只能是明细模式
        # 维度字段只支持日期格式化
        # 只支持 筛选、过滤
        # 不能简单的根据nums 和 dims 区分维度和度量， 因为nums里面也有维度(筛选)
        select_fields = []
        if self.chart_data_model.nums:
            select_fields += self.chart_data_model.nums
        if self.chart_data_model.dims:
            select_fields += self.chart_data_model.dims
        # desires 类型字段名称需要特殊处理
        if self.chart_data_model.desires:
            for item in self.chart_data_model.desires:
                item['query_type'] = "desire"
            select_fields += self.chart_data_model.desires
        select_fields_map = {"nums": [], "dims": []}
        for item in select_fields:
            if item.get('field_group') == "维度":
                item['dataset_field_id'] = item.get('dim') or item.get('num') or item.get("dataset_field_id")
                select_fields_map['dims'].append(item)
            else:
                select_fields_map['nums'].append(item)
                item['dataset_field_id'] = item.get('num') or item.get('dim') or item.get("dataset_field_id")
        return {
            "indicators": self.get_select_field(select_fields_map['nums']),
            "dimensions": self.get_select_field(select_fields_map['dims']),
            "filter": self.get_where(getattr(self.chart_data_model, "filters", []),
                                     getattr(self.chart_data_model, "chart_filter_conditions", [])),
            "sorts": self.get_order(
                getattr(self.chart_data_model, "new_order", []) or getattr(self.chart_data_model, "field_sorts", [])),
            "limit": self.get_limit(getattr(self.chart_data_model, "pagination", {}))
        }

    def get_select_field_map(self):  # NOSONAR:
        """
        get maps
        """
        select_fields = []
        select_fields_map = {}
        if self.chart_data_model:
            if self.chart_data_model.nums:
                select_fields += self.chart_data_model.nums
            if self.chart_data_model.dims:
                select_fields += self.chart_data_model.dims
            if self.chart_data_model.desires:
                select_fields += self.chart_data_model.desires
            if self.chart_data_model.comparisons:
                select_fields += self.chart_data_model.comparisons
            for item in select_fields:
                field_id = item.get('num') or item.get('dim') or item.get("dataset_field_id")
                select_fields_map[field_id] = item
        else:
            select_fields = self.dataset_fields
            for item in select_fields:
                item['dataset_field_id'] = item.get('id')
                select_fields_map[item.get('id')] = item

        return select_fields_map

    @staticmethod
    def set_filed_formula_mode(filed, item):
        try:
            if not filed['formula_mode']:
                query_chart_data_source_ppt = getattr(g, 'query_chart_data_source_ppt', '0') or '0'
                if query_chart_data_source_ppt and query_chart_data_source_ppt == '1':
                    if getattr(item, 'func', None) == "date_format":
                        date_formula_mode = PPT_DATA_FORMAT_NAME_MAP.get(item.props[1].value) or ''
                        if date_formula_mode:
                            filed['formula_mode'] = date_formula_mode
        except Exception as e:
            pass


    def get_field(self, item):
        """
        get field
        """
        if getattr(item, 'specifier', None) == "CASE":
            if item.props[0].conditions[0].left.props:
                col_name = item.props[0].conditions[0].left.props[0].prop_name
            else:
                col_name = item.props[0].conditions[0].left.prop_name
        elif getattr(item, "props", []):
            col_name = item.props[0].prop_name
        else:
            col_name = item.prop_name
        filed = self.col_name_map.get(col_name)
        if not filed:
            raise UserError(message=f"查询的字段{col_name}不在字段列表中")
        selected_field = self.select_fields_map.get(filed.get("id"))
        # if not selected_field:
        #     raise UserError(message=f"未获取到select字段：{col_name}")
        if selected_field:
            filed['formula_mode'] = selected_field.get("formula_mode")
        else:
            filed['formula_mode'] = ''
        self.set_filed_formula_mode(filed, item)
        filed['alias'] = item.alias
        return filed

    def get_select_fields_v3(self):
        """
        get select fields
        """
        indicators = []
        dimensions = []
        for item in self.query_structure.select:
            # 兼容 get total 场景, 改为查询任意一个字段
            is_count = False
            if getattr(item, 'func', '') == "count":
                select_field = self.select_fields_map[choice(list(self.select_fields_map.keys()))]
                field_id = select_field.get('num') or select_field.get('dim') or select_field.get("dataset_field_id")
                field = self.field_maps[field_id]
                field['alias'] = item.alias
                is_count = True
                if self.dataset_data.get('external_type') == ExternalDatasetType.PulsarIndicator.value:
                    self.query_mode = PulsarQueryMode.COUNT.value
                else:
                    self.query_mode = PulsarQueryMode.DETAILCOUNT.value
            else:
                field = self.get_field(item)

            if field.get("field_group") == "维度":
                dimensions.append(self.get_query_item_v2(field))
            else:
                indicators.append(self.get_query_item_v2(field))
            if is_count:
                break
        return indicators, dimensions

    def get_field_content_v3(self, item):
        field = self.get_field(item)
        field_type = self.get_field_type(field.get('field_group'))
        if field_type == "Dimension" or self.query_mode in [
            PulsarQueryMode.DETAIL.value, PulsarQueryMode.DETAILCOUNT.value
        ]:
            return {
                "field_type": field_type,
                "table_name": field.get("origin_table_name"),
                "field_name": field.get('origin_col_name'),
                "op_type": self.filter_data_format(item)
            }
        else:
            return {
                "field_type": field_type,
                "code": field.get("external_id")
            }

    @staticmethod
    def filter_data_format(item):
        op_type = "NoFieldOpType"
        if getattr(item, 'func', '') == "date_format":
            props = getattr(item, 'props', '')
            if props and len(props) == 2:
                prop = props[1]
                value = getattr(prop, 'value', '')
                op_type = DATA_FORMAT_VALUE_MAP.get(value) or "NoFieldOpType"
        return op_type

    @staticmethod
    def get_associate_type(logical_relation: str):
        if not logical_relation or logical_relation.upper() == "AND":
            return "And"
        else:
            return "Or"

    def check_field_type(self, item, field_type):
        if not getattr(item, 'conditions', []):
            field = self.get_field(item.left)
            if field.get('field_group') == field_type:
                return True
            return False
        else:
            # 同一个condition下都是同一个字段
            return self.check_field_type(item.conditions[0], field_type)

    def where_item_trans(self, item, filters: list, sub_filters: list, is_top: bool = True, field_type: str = '维度'):
        """
        trans
        """
        if not getattr(item, 'conditions', []):
            field = self.get_field(item.left)
            if self.query_mode in [
                PulsarQueryMode.DETAIL.value, PulsarQueryMode.DETAILCOUNT.value
            ] or field.get('field_group') == field_type:
                sub_filter = {
                    "type": "Predicate",
                    "associate_type": self.get_associate_type(item.logical_relation),
                    "subject": {
                        "type": "Field",
                        "field_content": self.get_field_content_v3(item.left)
                    },
                    "predicate": self.get_predicate_v3(item)
                }
                filters.append(sub_filter) if is_top else sub_filters.append(sub_filter)
        else:
            sub_filter = []
            where_obj = {
                "type": "FilterGroup",
                "pre_not": False,
                "associate_type": self.get_associate_type(item.logical_relation),
                "sub_filters": sub_filter
            }
            filters.append(where_obj) if is_top else sub_filters.append(where_obj)
            for sub_item in item.conditions:
                # 子树中不能出现为空的结构，否则数芯会解析失败
                if self.query_mode in [
                    PulsarQueryMode.DETAIL.value, PulsarQueryMode.DETAILCOUNT.value
                ] or self.check_field_type(sub_item, field_type):
                    self.where_item_trans(sub_item, filters, sub_filter, False, field_type)

    def get_where_v3(self, field_type='维度'):
        """
        trans where
        """
        if not getattr(self.query_structure, "where", None):
            return {}

        sub_filters = []
        where_obj = {
            "type": "FilterGroup",
            "pre_not": False,
            "associate_type": "And",
            "sub_filters": sub_filters
        }

        for item in self.query_structure.where:
            filters = []
            sub_where_obj = {
                "type": "FilterGroup",
                "pre_not": False,
                "associate_type": self.get_associate_type(item.logical_relation),
                "sub_filters": filters
            }
            self.where_item_trans(item, filters, [], field_type=field_type)
            if filters and self.check_has_filters(filters):
                sub_filters.append(sub_where_obj)

        if not sub_filters:
            return {}
        elif sub_filters and not sub_filters[0].get('sub_filters'):
            return {}

        return where_obj

    def check_has_filters(self, filters):
        if not filters:
            return

        for filter_item in filters:
            if isinstance(filter_item.get('sub_filters'), list) and len(filter_item.get('sub_filters')) == 0:
                return False
            if filter_item.get('sub_filters'):
                return self.check_has_filters(filter_item.get('sub_filters'))

        return True

    @staticmethod
    @stale_cache("vars")
    def get_dataset_var(var_id):
        return repository.get_data("dataset_vars", {'id': var_id})

    def get_variables(self):
        """
        获取变量
        :return:
        """
        vars = getattr(self.query_structure, 'vars', [])
        if not vars:
            return None

        vars_data = []
        for item in vars:
            if item.value:
                var = self.get_dataset_var(item.var_id)
                if var:
                    content = var.get("external_content")
                    content = json.loads(content) if content else {}
                    vars_data.append({
                        "name": var.get('name'),
                        "scope_type": content.get("scope_type"),
                        "single_value_content": {'value': str(item.value) if not isinstance(item.value, list) and item.value else None},
                        "range_value_content": {"left": item.value[0], "right": item.value[-1]} if isinstance(item.value, list) else None
                    })
        if vars_data:
            return vars_data
        return None

    def set_default_order_by(self):
        """
        数芯指标数据集没有排序则将维度字段转化为排序字段
        数芯明细数据集没有排序则优先将第一个维度字段转化为排序字段，没有维度字段则选第一个度量字段
        :return:
        """
        if self.query_mode == PulsarQueryMode.AGG.value:
            self.filed2order(self.dataset_fields, field_key='col_name', only_dim=True)
        if self.query_mode == PulsarQueryMode.DETAIL.value:
            self.filed2order(self.dataset_fields, field_key='col_name')

    def get_query_structure_v3(self) -> dict:
        """
        query_structure transfer
        :return:
        """
        # 如果没有排序，则指定第一个字段为排序字段
        self.set_default_order_by()

        indicators, dimensions = self.get_select_fields_v3()
        return {
            "indicators": indicators,
            "dimensions": dimensions,
            "filter": self.get_where_v3(field_type='维度'),
            "having_filter": self.get_where_v3(field_type="度量"),
            "sorts": self.get_order_v3(),
            "limit": self.get_limit_v3()
        }

    @staticmethod
    def get_biz_params():
        """
        get biz params
        :return:
        """
        if hasattr(g, 'external_params') and g.external_params:
            biz_params = g.external_params
        else:
            biz_params = {}
        return biz_params

    def get_total(self, mode="Count"):
        """
        get total
        :param mode:
        :return:
        """
        # 判断数据集类型
        if self.dataset_data.get('external_type') == ExternalDatasetType.PulsarIndicator.value:
            self.query_mode = PulsarQueryMode.COUNT.value
        else:
            self.query_mode = PulsarQueryMode.DETAILCOUNT.value
        data, _, _ = self.get_query_data(mode=self.query_mode)
        return data[0].get("total") if data else 0

    def get_query_data(self, sql_=None, mode: str = ''):   # NOSONAR
        """
        取数接口
        :param sql_:
        :param mode:  agg - 取数， count - 取总数
        :return:
        """

        # 取数
        try:
            start = time.time()
            # 判断数据集类型
            if getattr(self.chart_data_model, 'mode', None) == "count" and self.dataset_data.get('external_type') == ExternalDatasetType.PulsarIndicator.value:
                self.query_mode = PulsarQueryMode.COUNT.value
            elif getattr(self.chart_data_model, 'mode', None) == "count" and self.dataset_data.get('external_type') != ExternalDatasetType.PulsarIndicator.value:
                self.query_mode = PulsarQueryMode.DETAILCOUNT.value
            elif self.dataset_data.get('external_type') == ExternalDatasetType.PulsarIndicator.value:
                self.query_mode = PulsarQueryMode.AGG.value
            else:
                self.query_mode = PulsarQueryMode.DETAIL.value
            # 构造取数结构
            query_structure = self.get_query_structure_v3()

            biz_params = self.get_biz_params() or {}
            deal_external_api_biz_params(biz_params)
            cookie = getattr(g, 'cookie', None)
            token = IndicatorModelQuery.prepare_token(cookie.get("token"))

            body = {
                "pulsar_code": self.data_source_model.conn_str.project_code,
                "project_code": g.code,
                "class_id": self.dataset_data.get('external_id'),
                "query": query_structure,
                "engine": self.data_source_model.conn_str.engine,
                "biz_params": biz_params,
                "mode": self.query_mode or mode,
                "app_report_info": self._get_report_info(self.chart_data_model.dashboard_id if self.chart_data_model else ''),
                "with_variables": self.get_variables()
            }
            self._add_subtotal_query_params(body)
            if self.query_mode in [PulsarQueryMode.DETAIL.value, PulsarQueryMode.DETAILCOUNT.value]:
                body['model_code'] = self.dataset_data.get('external_id')
            res = self.pulsar_api.query_data(
                body,
                cookies={"dmp_token": token}
            )
            AnalysisTimeUtils.record(
                step=None, sql=None, db_type=AnalysisTimeUtils.db_type.MysoftShuXin.value, start_time=start,
                extra={}, need_type_inference=True
            )
            return res.get("data") or [], res.get('sql'), time.time() - start
        except DatasetQueryException as e:
            e.dataset_query_type = DatasetQueryType.Indicator.value
            raise e
        except Exception as e:
            msg = getattr(e, 'message', '') or str(e)
            raise DatasetQuerySqlException(
                msg=f"数芯取数服务异常：{msg}", error_code=QueryDataError.AnyError.value,
                dataset_query_type=DatasetQueryType.Indicator.value
            )

    def _add_subtotal_query_params(self, body):
        """
        如果当前查询是小计或总计查询，则会回将表格所有的度量和维度传递给数芯
        """
        # 如果不是新版的数芯，则采用原始的聚合方式
        if not dynamics_config.get("ShuXin.is_new_subtotal_col") in [1, "1"]:
            return

        if self.chart_data_model and (self.chart_data_model.is_subtotal_query or self.chart_data_model.is_subtotal_col_query):
            if not self.col_name_map:
                return
            try:
                all_dimensions = []
                all_indicators = []
                for item in self.chart_data_model.origin_select_fields:
                    alias = item.alias
                    field = self.col_name_map.get(item.alias) or self.col_name_map.get(item.field)
                    field["alias"] = alias
                    if field:
                        if field.get("field_group") == "维度":
                            all_dimensions.append(self.get_query_item_v2(field))
                        else:
                            all_indicators.append(self.get_query_item_v2(field))
                body['query']['all_dimensions'] = all_dimensions
                body['query']['all_indicators'] = all_indicators
                if self.chart_data_model.is_subtotal_query:
                    body['mode'] = 'ColTotal'
                elif self.chart_data_model.is_subtotal_col_query:
                    body['mode'] = 'ColSubTotal'
            except Exception as e:
                traceback.print_exc()
                logger.error(e)
                body['all_dimensions'] = []
                body['all_indicators'] = []

    @staticmethod
    @stale_cache(prefix='indicator_report')
    def _get_report_info(dashboard_id):
        """
        数芯天眼上报使用
        :return:
        """
        try:
            dash = repository.get_data("dashboard", {'id': dashboard_id}, fields=['application_type', 'platform', 'new_layout_type'])
            application_type = dash.get('application_type')
            platform = dash.get('platform')
            new_layout_type = dash.get('new_layout_type')
            if application_type in [1, '1']:
                app_scene = '自助报表'
            else:
                if platform == 'pc' and new_layout_type == 0:
                    app_scene = '大屏'
                elif platform == 'pc' and new_layout_type == 1:
                    app_scene = '仪表板'
                elif platform == 'mobile' and new_layout_type == 1:
                    app_scene = '新移动'
                elif platform == 'mobile' and new_layout_type == 0:
                    app_scene = '老移动'
                else:
                    app_scene = '未知'
            name = repository.get_data_scalar('project', {'code': g.code}, col_name='name', from_config_db=True)
            tenant_info = f"{g.code}({name})"
        except Exception as e:
            logger.error(f"get report_info error: {e}")
            app_scene = ''
            tenant_info = ''
        return {
            'app_scene': app_scene,
            'tenant_info': tenant_info
        }

    @staticmethod
    def prepare_token(token):
        from dmplib import config
        from user.services.developer_service import Developer
        from dashboard_chart.utils.common import check_dashboard_release_access
        if not token:
            return token
        try:
            verified_token = jwt.decode(token, config.get('JWT.secret'), algorithms="HS256")
            account = verified_token.get('account', None)
            is_developer = Developer.is_developer_by_account(account)
            if is_developer or check_dashboard_release_access(g):
                verified_token['account'] = get_default_account(getattr(g, 'code', None))
                verified_token['id'] = ADMINISTRATORS_ID
                token = jwt.encode(verified_token, config.get('JWT.secret'))
        except Exception as e:
            logger.error(str(e))
        return token

    def run_get_data_of_detail_model(self):
        """
        明细模式数据集预览
        :return:
        """
