#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
    数据集查询数据业务类
    <NAME_EMAIL> on 2018/9/18.
"""
import copy
import datetime
import hashlib
import json
import logging
import random
import re
import time
import os

# pylint: disable=E0401,R0201
import traceback

from pymysql import DatabaseError

import components
from base import repository
from base.dmp_constant import EXTERNAL_SNOW_SUBJECT_DATASET_ID
from base.enums import DatasetType, DatasetConnectType, DatasetEditMode, DatasetFieldType, DataSourceType, \
    DatasetStorageType, SqlComplexOrNot, MysoftShuXin15QueryType
from components import query_sql_encoder
from components.deal_dataset_transition import deal_exexpression_to_sql
from components.storage_setting import get_storage_type, is_local_storage
from components.data_center_api import get_new_erp_datasource_model
from components.analysis_time import AnalysisTimeUtils
from components.log_setting import Debug
from components.query_models import Object, QueryStructure, ModelEncoder
from components.remove_comment import remove_comment
from data_source.cache import data_source_meta_cache
from data_source.models import DataSourceModel
from data_source.repositories import mysql_data_source_repository, ads_data_source_repository, \
    presto_data_source_repository, mssql_data_source_repository
from data_source.repositories import postgresql_data_source_repository
from data_source.services.data_source_service import load_data_source_conn_str
from dataset.cache import dataset_meta_cache
from dataset.cache.dataset_cache_service import DatasetCache
from dataset.cache.dataset_direct_result_cache import DirectDatasetCacheManager
from dataset.cache.dataset_result_cache import ScheduleDatasetCacheManager
from dataset.common import advance_field_helper, sql_helper
from dataset.query import dataset_data_helper
from dataset.query import query_structure_helper
from dataset.query.api_query_data import ApiQueryData
from dataset.query.direct_mysql_query_data import DirectMysqlQueryData
from dataset.query.direct_mssql_query_data import DirectMssqlQueryData
from dataset.query.direct_pgsql_query_data import DirectPgSQLQueryData
from dataset.query.direct_presto_query_data import DirectPrestoQueryData
from dataset.query.sql_query_data import DirectSqlQueryData
from dataset.query.external_subject_query import external_subject_utils
from dataset.query.mysoft_erp_query_data import MySoftNewErpQueryData
from dataset.query.link_data import LinkData
from dataset.query.mysql_query_data import MysqlQueryData
from dataset.query.query_data import QueryData
from dataset.query.indicator_model_query_data import IndicatorModelQuery
from dataset.query.result_data import (
    DatasetQueryException,
    DatasetQueryJsonException,
    ResultData,
    DatasetDBException,
    QueryStructData,
)
from dataset.services import dataset_group_field, dataset_field_service
from dmplib.constants import LABEL_DETAIL_TABLE_NAME_SUFFIX
from dmplib.hug import g
from dmplib.saas.project import get_data_db
from dmplib.utils.errors import UserError
from dmplib.redis import conn as conn_redis
from label.services import label_service
from message.models import MessageModel
from message.services import message_service
from rbac.services import dataset_rbac_service
from dataset.repositories import dataset_version_repository, dataset_repository, dataset_field_repository
from components.query_structure_sql import Conditions, Object as SQLObject, Prop, QueryStructure as SQLQueryStructure
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple, Union
from self_service import external_service as external_self_service
from .external_subject_query.external_subject_query_data import ExternalSubjectQueryData
from .mysoft_shuxin15_sql_query_data import MysoftShuXin15SqlQuery

debugger = Debug("dataset_query_service")
logger = logging.getLogger(__name__)


class QueryDatasetService:
    __slots__ = [
        'user_id',
        'dataset_id',
        'chart_id',
        'query_structure_json',
        'permit_fields',
        'group_fields',
        'senior_fields',
        'dataset_fields',
        'var_fields',
        'link_datas',
        'is_order_master_id',
        'dataset_data',
        'external_subject',
        'query_dataset_version',
        'external_subject_ids',
        'data_source',
        'chart_data_model'
    ]

    def __init__(self, **kwargs) -> None:
        self.user_id = kwargs.get('user_id')
        self.dataset_id = kwargs.get('dataset_id')
        self.chart_id = kwargs.get('chart_id')
        self.query_structure_json = kwargs.get('query_structure_json')
        self.is_order_master_id = kwargs.get('is_order_master_id', False)
        self.query_dataset_version = kwargs.get('query_dataset_version', None)
        self.external_subject_ids = kwargs.get('external_subject_ids', [])
        self.permit_fields = []
        self.group_fields = []
        self.senior_fields = []
        self.dataset_fields = {}
        self.var_fields = []
        self.link_datas = []
        self.dataset_data = {}
        self.external_subject = {}
        self.data_source = self.get_datasource(kwargs.get('dataset_id'))
        self.chart_data_model = kwargs.get("chart_data_model")

    @staticmethod
    def _verify_query_structure_json(query_structure_json: str) -> QueryStructure:
        """
        校验查询定义json是否合法
        :param query_structure_json:
        :return : components.query_models.QueryStructure model
        """
        try:
            data = json.loads(query_structure_json)
            query_structure = QueryStructure(**data)
            return query_structure
        except Exception as e:
            msg = "数据集数据查询的Json格式错误：{}，原json：{}".format(str(e), query_structure_json)
            logger.error(msg, exc_info=True)
            raise DatasetQueryJsonException(msg=msg) from e

    @staticmethod
    def get_object(
            dataset_data: Dict[str, Union[str, None]], chart_id: Optional[str] = None, is_api_schedule: bool = False
    ) -> SQLObject:
        """
        不同数据集对于不用的表名
        :return:
        """
        try:
            content = json.loads(dataset_data.get('content'))
        except Exception as e:
            msg = "数据集content内容序列化错误：" + str(e)
            logger.error(msg, exc_info=True)
            raise UserError(message=msg) from e

        table = Object()

        if dataset_data.get('type') in [DatasetType.ExternalSubject.value] or is_api_schedule:
            # 高级sql模式，采用a别名
            if content.get('sql'):
                new_sql = content.get('sql').strip().strip(';')
                # 去注释
                new_sql, _ = remove_comment(new_sql)
                table.name = " ({}) ".format(new_sql)
                table.alias = ' a '
            else:
                table.name = content.get("table_name")
        elif dataset_data.get('connect_type') == DatasetConnectType.Directly.value:
            # 直连模式复杂sql支持多行sql
            if dataset_data.get("is_complex") == SqlComplexOrNot.Complex.value:
                new_sql = content.get('sql')
                # new_sql, _ = remove_comment(content.get('sql'))
                table.name = "{}".format(new_sql)
                table.alias = ' a '
            else:
                new_sql = content.get('sql').strip().strip(';')
                # 去注释
                new_sql, _ = remove_comment(new_sql)
                table.name = " ({}) ".format(new_sql)
                table.alias = ' a '
        elif dataset_data.get('type') == DatasetType.Label.value:
            if (
                    content
                    and content.get('label_id')
                    and not label_service.get_label_sync_detail(content.get('label_id'))
                    and chart_id
            ):
                table.name = 'chart_result_' + hashlib.md5(chart_id.encode('utf-8')).hexdigest()[8:-8]
            else:
                table.name = dataset_data.get('table_name') + "_" + LABEL_DETAIL_TABLE_NAME_SUFFIX
        else:
            table.name = dataset_data.get('table_name')

        return table

    def prop_name_to_prop_raw(self, data: Union[Prop, Conditions]) -> Union[Prop, Conditions]:
        if getattr(data, "props", None):
            for index, prop in enumerate(data.props):
                self.prop_name_to_prop_raw(prop)
        if getattr(data, "left", None):
            self.prop_name_to_prop_raw(data.left)
        if getattr(data, "right", None):
            self.prop_name_to_prop_raw(data.right)
        if getattr(data, "conditions", None):
            for condition in data.conditions:
                self.prop_name_to_prop_raw(condition)

        if getattr(data, "prop_name", None):
            if data.prop_name in self.group_fields:
                data.prop_raw = self.deal_prop_ref_to_raw(data.prop_name)
            if data.prop_name in self.senior_fields:
                dataset_field = self.dataset_fields.get(data.prop_name)
                data.prop_raw = self.get_senior_field_prop_raw(dataset_field)
        return data

    def get_senior_field_prop_raw(self, dataset_field):
        """
        获取高级字段的sql
        :param dataset_field:
        :return:
        """
        if dataset_field.get("expression_advance"):
            if dataset_field.get("type") in [
                DatasetFieldType.Indicator.value,
                DatasetFieldType.CalculateIndicator.value,
            ]:
                if self.external_subject_ids and self.dataset_id == EXTERNAL_SNOW_SUBJECT_DATASET_ID:
                    prop_raw = advance_field_helper.snow_indicator_expression_real_name(
                        dataset_field.get("expression_advance"), dataset_field
                    )
                else:
                    prop_raw = advance_field_helper.cube_indicator_expression_real_name(
                        dataset_field.get("expression_advance")
                    )
            else:
                prop_raw = advance_field_helper.expression_real_name(
                    dataset_field.get("expression_advance"), self.dataset_id
                )
            prop_raw = advance_field_helper.transform_var(prop_raw, self.var_fields)
            return prop_raw
        else:
            expression_advance = advance_field_helper.expression_convertor(dataset_field.get("expression"))
            prop_raw = advance_field_helper.expression_real_name(expression_advance, self.dataset_id)
            return prop_raw

    def add_prop_raw(self, query_structure: SQLQueryStructure) -> SQLQueryStructure:
        """
        检测select,group by,order by中是否有prop_ref，有则需要添加prop_raw
        :param query_structure:
        :return:
        """
        if query_structure.select:
            for row in query_structure.select:
                self.set_special_func_prop_row(row)
                self.prop_name_to_prop_raw(row)
        if query_structure.group_by:
            for row in query_structure.group_by:
                self.prop_name_to_prop_raw(row)
        if query_structure.order_by:
            for row in query_structure.order_by:
                self.prop_name_to_prop_raw(row)
        if query_structure.where:
            for row in query_structure.where:
                self.prop_name_to_prop_raw(row)
        return query_structure

    def set_special_func_prop_row(self, data: Union[Prop]):
        """
        特殊数据源的语法转换成高级字段实现
        """
        if self.data_source and getattr(data, "func", None) and getattr(data, "props", None) and len(data.props) > 0:
            # Presto数据源，group_concat需要特殊处理
            if self.data_source.type == DataSourceType.Presto.value or self.data_source.db_type == DataSourceType.Presto.value:
                if data.func == 'group_concat':
                    field_name = data.props[0].prop_name
                    data.prop_raw = f"array_join(array_agg({field_name}), ',')"
                    data.func = ''
                    data.props = []
            # api的starRocks特殊处理(三云api使用的是数芯数据库)
            elif self.data_source.type == DataSourceType.API.value and (
                    (self.data_source.db_type == DataSourceType.StarRocks.value) or self.asset_api_conn_str_db_engine(
                    ['starrockssaas', 'starrocks'])):
                if data.func == 'group_concat':
                    field_name = data.props[0].prop_name
                    data.prop_raw = f"group_concat({field_name}, ',')"
                    data.func = ''
                    data.props = []
                elif data.func == 'limit1':
                    field_name = data.props[0].prop_name
                    data.prop_raw = f"max({field_name})"
                    data.func = ''
                    data.props = []
            # 数芯 1.5
            elif self.data_source.type == DataSourceType.MysoftShuXin15.value:
                # if self.data_source.conn_str.engine == 'StarRocksSaaS':
                if data.func == 'group_concat':
                    field_name = data.props[0].prop_name
                    tbl_name = data.props[0].obj_name
                    if tbl_name:
                        data.prop_raw = f"group_concat(distinct {tbl_name}.{field_name} separator ',')"
                    else:
                        data.prop_raw = f"group_concat(distinct {field_name} separator ',')"
                    data.func = ''
                    data.props = []
                if data.func == 'limit1':
                    d_data = copy.deepcopy(data)
                    # 将字段先尝试进行高级字段翻译，如果是的话使用翻译的高级字段
                    self.prop_name_to_prop_raw(d_data.props[0])
                    if d_data.props[0].prop_raw:
                        # 已经是高级字段
                        data.prop_raw = f"max({d_data.props[0].prop_raw})"
                        data.func = ''
                        data.props = []
                    else:
                        # 普通字段构造成高级字段
                        field_name = data.props[0].prop_name
                        data.prop_raw = f"max({field_name})"
                        data.func = ''
                        data.props = []

    def asset_api_conn_str_db_engine(self, targets: list):
        for param in self.data_source.conn_str.params or []:
            if param.get('key') == 'engine' and str(param.get('value')).lower() in targets:
                return True
        return False

    def deal_prop_ref_to_raw(self, prop_name):
        if not prop_name:
            return ""
        dataset_field = dataset_group_field.deal_group_field_by_dataset_filed(self.dataset_fields, prop_name)
        prop_raw = deal_exexpression_to_sql(self.dataset_data, dataset_field)
        return prop_raw

    def init_special_field(self, dataset_fields: List[Dict[str, Union[str, int, None]]]) -> None:
        """
        初始化分组字段和高级字段的值
        :param dataset_fields:
        :return:
        """
        for dataset_field in dataset_fields:
            if dataset_field.get("type") == DatasetFieldType.Group.value:
                self.group_fields.append(dataset_field.get("col_name"))
            elif dataset_field.get("type") in [
                DatasetFieldType.Customer.value,
                DatasetFieldType.Calculate.value,
                DatasetFieldType.Indicator.value,
                DatasetFieldType.CalculateIndicator.value,
            ]:
                self.senior_fields.append(dataset_field.get("col_name"))

    def _get_real_dataset_fields(self, dataset_id: str, external_subject_ids: List[str] = None):
        if external_subject_ids:
            return dataset_field_service.get_original_external_subjects_fields(external_subject_ids)
        return dataset_meta_cache.get_dataset_field_cache(dataset_id)

    def generate_query_structure(
            self,
            query_structure_json: str,
            dataset_data: Dict[str, Union[str, None]],
            table_name: str = "",
            external_subject_ids: List[str] = None,
            is_api_schedule: bool = False,
            table_alias: str = "",
    ) -> QueryStructure:
        """
        根据查询Json生成QueryStructure对象
        1、校验json的合法性
        2、如果存在多表联合需要转换Object对象
        3、如果存在权限需要转换Where对象
        :param str query_structure_json :
        :param dict dataset_data :
        :param str table_name: 查询表名
        :param str table_alias: 指定查询表别名，只在table_name存在时生效
        :return: components.query_models.QueryStructure model
        """
        # 1、校验json的合法性
        query_structure = self._verify_query_structure_json(query_structure_json)

        # 2、处理分组字段、高级字段、变量 (区分外部主题和数据集)
        dataset_fields = self._get_real_dataset_fields(self.dataset_id, self.external_subject_ids)
        self.init_special_field(dataset_fields)
        self.var_fields = query_structure.vars
        self.dataset_fields = {dataset_field.get("col_name"): dataset_field for dataset_field in dataset_fields}
        dataset_fields_origin_key = {dataset_field.get("origin_col_name"): dataset_field for dataset_field in
                                     dataset_fields}
        AnalysisTimeUtils.recode_time_node('开始处理高级字段query_structure')
        self.add_prop_raw(query_structure)
        AnalysisTimeUtils.recode_time_node('完成处理高级字段query_structure')

        # 3、其他类型数据集，如果存在多表联合需要转换Object对象进行拼接
        if (
                dataset_data.get("edit_mode") == DatasetEditMode.Relation.value
                and (dataset_data.get('connect_type') == DatasetConnectType.Directly.value or is_api_schedule)
                and not table_name
        ):
            self.link_datas = dataset_meta_cache.get_dataset_link_data_cache(self.dataset_id)
            if not self.link_datas:
                self.link_datas = self.deal_single_link_datas(dataset_fields)
            objects = query_structure_helper.transform_object(self.link_datas)
            query_structure.object = objects
            # 处理简单实时组合数据集的sql
            if (dataset_data.get('type') == DatasetType.Union.value
                    and dataset_data.get('is_complex') != SqlComplexOrNot.Complex.value):
                query_structure = self.generate_query_structure_union_relation_dataset(objects, dataset_data,
                                                                                       query_structure,
                                                                                       dataset_fields_origin_key)

        elif table_name:
            table = Object()
            table.name = table_name
            table.alias = table_alias  # 支持直接指定table，alias
            query_structure.object = [table]
        else:
            table = self.get_object(dataset_data, chart_id=self.chart_id, is_api_schedule=is_api_schedule)
            query_structure.object = [table]

        # 判断table_name是否是一个get_total的子查询, 这里的') count_table'依赖CountQuery的代码
        is_subquery = False
        if (
                (table_name and table_name.strip().endswith(') count_table'))
                or table_alias == 'count_table'
        ):
            is_subquery = True

        # api调度的场景不需要权限过滤
        if not is_api_schedule:
            # 4、如果存在权限需要转换Where对象
            # （1）数据存在DMP当中都是单表查询
            # （2）目前直连数据集不支持权限(api已支持)
            self._generate_permission_structure(dataset_data, is_subquery, query_structure)

        # 5、视图模式的实时组合数据集不需要添加变量
        if not (dataset_data.get("edit_mode") == DatasetEditMode.Relation.value and
                dataset_data.get('type') == DatasetType.Union.value and
                dataset_data.get('is_complex') != SqlComplexOrNot.Complex.value):
            self._generate_filter_structure(dataset_data, dataset_fields, is_subquery, query_structure, table_name)

        # 直连组合数据集需要替换为最新的定时数据集表
        if dataset_data.get('type') == DatasetType.Union.value and dataset_data.get(
                'connect_type') == DatasetConnectType.Directly.value and query_structure.object and \
                query_structure.object[0]:
            table_names = sql_helper.extract_tables(query_structure.object[0].name, is_alias=True)
            dataset_tables = sql_helper.parse_table_alias_names(table_names)
            dataset_tables = self.get_dataset_metadata(query_structure.object[0].name, dataset_tables)
            query_structure.object[0].name, source_dataset_ids = sql_helper.parse_custom_sql(
                query_structure.object[0].name, dataset_tables)

        # 6. 如果是外部组合主题类型则执行独立逻辑 （此块后续重新规划）
        if dataset_data.get("type") == DatasetType.ExternalSubject.value:
            self.load_external_subjects()
            # pylint: disable=C0415
            from .external_subject_query.multi_dim_rebuilder import QueryStructureRebuilder

            query_structure = QueryStructureRebuilder(
                query_structure, external_subject_ids[0], dataset_data, dataset_fields,
                self.external_subject.get(external_subject_ids[0])
            ).generate_structure()
        return query_structure

    #  处理简单实时组合数据集的sql的 select  , where ,group
    def generate_query_structure_union_relation_dataset(self, objects, dataset_data, query_structure,
                                                        dataset_fields_origin_key):
        def where_replace_name(where):
            for condition in where.conditions:
                if condition:
                    where_replace_name(condition)
            if not where or not where.left:
                return
            if where.left.func:
                for prop in where.left.props:
                    prop.obj_name = ''
                    prop.prop_name = dataset_fields_origin_key.get(prop.prop_name, {}).get('col_name', '')
            if where.left.prop_name:
                where.left.prop_name = dataset_fields_origin_key.get(where.left.prop_name, {}).get('col_name', '')
                where.left.obj_name = ' a '

        def query_structure_replace_prop_name(obj_prop, is_select=False):
            for obj in obj_prop:
                if not obj.func:  # 关系型 计算字段时不进行任何替换
                    if is_select:
                        obj.prop_name = obj.alias
                    else:
                        obj.prop_name = dataset_fields_origin_key.get(obj.prop_name, {}).get('col_name', '')
                    obj.obj_name = ' a '
                    continue
                #  提前处理的  真正的字段在props
                for prop in obj.props:
                    prop.obj_name = ''
                    prop.prop_name = dataset_fields_origin_key.get(prop.prop_name, {}).get('col_name', '')

        # 视图模式取视图sql
        content = json.loads(dataset_data.get('relation_sql'))
        new_sql = content.get('sql', '').strip().strip(';')
        # 去注释
        new_sql, _ = remove_comment(new_sql)
        objects[0].name = " ({}) ".format(new_sql)
        objects[0].alias = ' a '
        query_structure_replace_prop_name(query_structure.select, is_select=True)
        query_structure_replace_prop_name(query_structure.group_by)
        query_structure_replace_prop_name(query_structure.order_by)
        for where in query_structure.where:
            if where:
                where_replace_name(where)
        objects = objects[0:1]
        query_structure.object = objects
        return query_structure

    def get_dataset_metadata(self, sql, dataset_tables):
        """
        获取数据集元数据
        :return:
        """
        query_dataset_names = [table.get("sql_table_name") for table in dataset_tables]
        # 这里可能有自关联的场景，
        query_dataset_names = list(set(query_dataset_names))
        query_dataset_metadata = dataset_repository.get_dataset_by_name(query_dataset_names)
        if not query_dataset_metadata or len(query_dataset_metadata) != len(query_dataset_names):
            raise UserError(
                message='{sql} 中存在无效表名 {table_names}'.format(sql=sql,
                                                                    table_names=','.join(query_dataset_names))
            )
        sort_query_dataset_metadata = self.sort_dict_list(query_dataset_metadata, 'name')
        for dataset_metadata in sort_query_dataset_metadata:
            for table in dataset_tables:
                if dataset_metadata.get("name") == table.get("sql_table_name"):
                    dataset_metadata["sql_table_name"] = table.get("sql_table_name")
                    dataset_metadata["sql_table_alias_name"] = table.get("sql_table_alias_name")

            dataset_field_metadata = dataset_field_repository.get_dataset_field(
                dataset_metadata.get("id"), {"type": DatasetFieldType.Normal.value}
            )
            if not dataset_field_metadata:
                raise UserError(
                    message='{sql_table_name}表没有字段'.format(sql_table_name=dataset_metadata.get("name")))
            dataset_metadata["dataset_fields"] = self.sort_dict_list(dataset_field_metadata, 'alias_name')
        return sort_query_dataset_metadata

    @staticmethod
    def sort_dict_list(sort_list, sort_name):
        """
        根据排序名字符长度从大道小排序
        :param sort_list:
        :param sort_name:
        :return:
        """
        flag = 1
        for i in range(len(sort_list) - 1, 0, -1):
            if flag:
                flag = 0
                for j in range(i):
                    if sort_list[j].get(sort_name) and len(sort_list[j].get(sort_name)) < len(
                            sort_list[j + 1].get(sort_name)
                    ):
                        sort_list[j], sort_list[j + 1] = sort_list[j + 1], sort_list[j]
                        flag = 1
            else:
                break
        return sort_list

    def _generate_filter_structure(self, dataset_data, dataset_fields, is_subquery, query_structure, table_name):
        if self.external_subject_ids:
            for external_subject_id in self.external_subject_ids:
                self._generate_single_filter_structure(
                    dataset_data, dataset_fields, external_subject_id, is_subquery, query_structure, table_name
                )
        else:
            self._generate_single_filter_structure(
                dataset_data, dataset_fields, None, is_subquery, query_structure, table_name
            )

    def _generate_single_filter_structure(
            self, dataset_data, dataset_fields, external_subject_id, is_subquery, query_structure, table_name
    ):
        filter_content = dataset_meta_cache.get_dataset_filter_cache(external_subject_id or self.dataset_id)
        if filter_content and not is_subquery:
            if hasattr(g, "is_api_schedule") and g.is_api_schedule is True:
                filter_content = query_structure_helper.transform_filter_content(filter_content, self.link_datas)
            elif (
                    # dataset_data.get('type') not in [DatasetType.Api.value]
                    # and dataset_data.get('connect_type') != DatasetConnectType.Directly.value
                    dataset_data.get('connect_type')
                    != DatasetConnectType.Directly.value
            ):
                filter_content = query_structure_helper.transform_filter_to_dataset_data(
                    filter_content, dataset_fields, table_name or dataset_data.get("table_name")
                )
            else:
                filter_content = query_structure_helper.transform_filter_content(filter_content, self.link_datas)
            where = query_structure_helper.transform_filter_where(filter_content)
            if query_structure.where:
                where.logical_relation = "AND"
            query_structure.where.append(where)

    def _generate_permission_structure(self, dataset_data, is_subquery, query_structure):
        if self.external_subject_ids:
            for external_subject_id in self.external_subject_ids:
                self._generate_single_permission_structure(
                    dataset_data, external_subject_id, is_subquery, query_structure
                )
        else:
            self._generate_single_permission_structure(dataset_data, None, is_subquery, query_structure)

    def _generate_single_permission_structure(self, dataset_data, external_subject_id, is_subquery, query_structure):
        from dmplib.saas.project import get_project_info
        from base.enums import DatasetPermissionModel
        project = get_project_info(g.code)
        if project and project.get("dataset_permission_model") == DatasetPermissionModel.Alone.value:
            fields_auth = dataset_rbac_service.get_permission_of_dataset(
                dataset_data.get('id'), self.user_id, external_subject_id
            )
        else:
            fields_auth = dataset_rbac_service.get_permission_of_user(
                dataset_data.get('id'), self.user_id, external_subject_id
            )
        self.permit_fields.extend(fields_auth.get('fields'))
        permission_where = query_structure_helper.transform_permission_where(
            fields_auth, dataset_data, external_subject_id
        )
        if permission_where and not is_subquery:
            if not query_structure.where:
                permission_where.logical_relation = ""
            query_structure.where.append(permission_where)

    @staticmethod
    def deal_single_link_datas(dataset_fields):
        # 转换成前端传下来的格式
        link_datas = []
        new_node_datas = {}
        for dataset_field in dataset_fields:
            # 视图模式下，origin_table_id一定不能为空
            if not dataset_field.get("origin_table_id"):
                continue
            fields = {
                "type": dataset_field.get("origin_field_type"),
                "name": dataset_field.get("origin_col_name"),
                "comment": dataset_field.get("note"),
                "alias_name": dataset_field.get("alias_name"),
            }
            if dataset_field.get('origin_table_id') not in new_node_datas.keys():
                temp_dict = dict()
                temp_dict["id"] = dataset_field.get('origin_table_id')
                temp_dict["name"] = dataset_field.get('origin_table_name')
                temp_dict["comment"] = dataset_field.get('origin_table_comment')
                temp_dict["alias_name"] = dataset_field.get('origin_table_alias_name')
                temp_dict["fields"] = [fields]
            # 追加fields
            else:
                temp_dict = new_node_datas.get(dataset_field.get('origin_table_id'))
                temp_dict["fields"].append(fields)
            new_node_datas[dataset_field.get('origin_table_id')] = temp_dict
        # 组装nodeDatas
        node_data_array = [v for k, v in new_node_datas.items()]
        # 单表的情况下，nodeDatas有值，link_datas中没有值，需要写一条link_datas的记录
        if node_data_array:
            link_data = dict()
            link_data["from_id"] = node_data_array[0].get("id")
            link_data["from_table_name"] = node_data_array[0].get("name")
            link_datas.append(LinkData(**(link_data)))
        return link_datas

    @staticmethod
    def get_data_source_model(dataset_content):
        """
        获取数据源model
        :param str dataset_content:
        :return:
        """
        try:
            content = json.loads(dataset_content)
        except Exception as e:
            raise UserError(message="数据集内容格式化错误：" + str(e)) from e
        data_source_id = content.get("data_source_id")
        data_source_data = data_source_meta_cache.get_data_source_cache(data_source_id)
        if not data_source_data:
            raise UserError(message=f"数据集引用数据源不能为空：{data_source_id}")
        data_source_model = DataSourceModel(**data_source_data)
        load_data_source_conn_str(data_source_model)
        return data_source_model

    @staticmethod
    def get_shuxin_data_source():
        """
        获取数芯数据源
        :return:
        """
        data_source = repository.get_one("data_source", {'type': DataSourceType.MysoftShuXin.value})
        if not data_source:
            raise UserError(message="未配置数芯数据源")
        data_source_model = DataSourceModel(**data_source)
        load_data_source_conn_str(data_source_model)
        return data_source_model

    @staticmethod
    def get_dmp_data_source():
        """
        获取数见data数据源
        :return:
        """
        data_source = repository.get_one("data_source", {'code': 'proj_data'})
        if not data_source:
            raise UserError(message="获取数见data数据源")
        data_source_model = DataSourceModel(**data_source)
        load_data_source_conn_str(data_source_model)
        return data_source_model

    @staticmethod
    def get_query_client(
            dataset_data: dict,
            query_structure: components.query_models.QueryStructure,
            dataset_fields: list = None,
            is_order_master_id: bool = False,
            is_api_schedule: bool = False,
            chart_data_model=None,
            user_id=None,  # 用于第三方或者数芯接口获取数据时 需要传用户身份code
    ) -> QueryData:
        """
        获取查询类
        :param dict dataset_data:
        :param components.query_models.QueryStructure query_structure:
        :param list dataset_fields:
        :param bool is_order_master_id:
        :param bool is_api_schedule:
        :param bool chart_data_model:
        :return:
        """
        from dataset.services.dataset_service import set_info_dataset
        set_info_dataset(dataset_data)

        if is_api_schedule:
            if not dataset_data.get("content"):
                raise UserError(message="数据集内容content不能为空：" + str(dataset_data))
            data_source_model = QueryDatasetService.get_data_source_model(dataset_data.get("content"))
            # api调度时候取数
            if dataset_data.get("type") == DatasetType.Api.value:
                query_data = ApiQueryData(
                    query_structure,
                    data_source_model,
                    dataset_data,
                    dataset_fields=dataset_fields,
                    is_order_master_id=is_order_master_id,
                )
            elif data_source_model.type == DataSourceType.MysoftShuXin15.value:
                # 数芯1.5调度时候的取数
                query_data = MysoftShuXin15SqlQuery(
                    query_type=MysoftShuXin15QueryType.query_structure.value,
                    query_structure=query_structure, data_source_model=data_source_model,
                    dataset_data=dataset_data, dataset_fields=dataset_fields,
                    user_id=user_id,  # 用于第三方或者数芯接口获取数据时 需要传用户身份code
                )
        elif dataset_data.get("type") == DatasetType.Indicator.value:
            # 此处有分流场景，数芯1.0 和数芯15都可能有指标
            data_source_model = QueryDatasetService.get_data_source_model(dataset_data.get("content"))
            if not data_source_model or data_source_model and data_source_model.type == DataSourceType.MysoftShuXin.value:
                data_source_model = QueryDatasetService.get_shuxin_data_source()
                query_data = IndicatorModelQuery(
                    data_source_model,
                    dataset_data,
                    chart_data_model,
                    dataset_fields,
                    query_structure=query_structure
                )
            else:
                query_data = MysoftShuXin15SqlQuery(
                    query_type=MysoftShuXin15QueryType.query_indicator.value,
                    query_structure=query_structure, data_source_model=data_source_model,
                    dataset_data=dataset_data, chart_data_model=chart_data_model,
                    dataset_fields=dataset_fields,
                    user_id=user_id,  # 用于第三方或者数芯接口获取数据时 需要传用户身份code
                )
        elif dataset_data.get('connect_type') == DatasetConnectType.Directly.value:
            if dataset_data.get("type") == DatasetType.Api.value:
                if not dataset_data.get("content"):
                    raise UserError(message="数据集内容content不能为空：" + str(dataset_data))
                data_source_model = QueryDatasetService.get_data_source_model(dataset_data.get("content"))
                query_data = ApiQueryData(
                    query_structure,
                    data_source_model,
                    dataset_data,
                    dataset_fields=dataset_fields,
                    is_order_master_id=is_order_master_id,
                )
            else:
                query_data = QueryDatasetService.get_directly_query_data(query_structure, dataset_data, dataset_fields, user_id)
        elif dataset_data.get('type') == DatasetType.ExternalSubject.value:
            query_data = ExternalSubjectQueryData(query_structure, dataset_data, dataset_fields)
        else:
            # 调度模式区分本地存储、云端存储
            if is_local_storage(g.code):
                # 落地的组合数据集 data_source_model为数据服务中心数据源
                if dataset_data.get("type").upper() in ["UNION", "EXCEL"]:
                    data_source_model = get_new_erp_datasource_model(dataset_data.get('content'))
                else:
                    data_source_model = QueryDatasetService.get_data_source_model(dataset_data.get("content"))
                query_data = MySoftNewErpQueryData(query_structure, data_source_model, dataset_data)
            else:
                data_db = get_data_db()
                query_data = MysqlQueryData(query_structure, data_db)
        return query_data

    @staticmethod
    def get_directly_query_data(query_structure, dataset_data, dataset_fields, user_id=None):
        # 根据数据集类型获取sql的db连接
        # 1、DMP落地数据集获取data库的db连接
        # 2、直连数据集根据数据源获取db连接（目前只支持mysql）
        if not dataset_data.get("content"):
            raise DatasetQueryException(msg="数据集内容content不能为空：" + str(dataset_data))
        data_source_model = None
        if dataset_data.get("type") == DatasetType.Union.value:
            # 实时组合数据集只能选择定时组合数据集 所以数据集只能报错在云端或本地
            dataset_content = json.loads(dataset_data.get('content'))
            if dataset_content.get("running_way") == "cloud":
                return MysqlQueryData(query_structure, get_data_db(), dataset_data)
            else:
                # 落地的组合数据集 data_source_model为数据服务中心数据源
                data_source_model = get_new_erp_datasource_model(dataset_data.get('content'))
        else:
            data_source_model = QueryDatasetService.get_data_source_model(dataset_data.get("content"))
        if data_source_model.type == DataSourceType.PostgreSQL.value:
            db = postgresql_data_source_repository.get_postgresql_db(data_source_model)
            query_data = DirectPgSQLQueryData(query_structure, dataset_data, db)
        elif data_source_model.type == DataSourceType.MSSQL.value:
            db = mssql_data_source_repository.get_mssql_db(data_source_model)
            query_data = DirectMssqlQueryData(query_structure, dataset_data, db)
        elif data_source_model.type == DataSourceType.Presto.value:
            db = presto_data_source_repository.get_presto_db(data_source_model)
            query_data = DirectPrestoQueryData(query_structure, dataset_data, db)
        elif data_source_model.type == DataSourceType.MysoftNewERP.value:
            query_data = MySoftNewErpQueryData(query_structure, data_source_model, dataset_data)
        elif data_source_model.type in (DataSourceType.Mysql.value, DataSourceType.ADS.value):
            try:
                if data_source_model.type == DataSourceType.Mysql.value:
                    db = mysql_data_source_repository.get_mysql_db(data_source_model)
                elif data_source_model.type == DataSourceType.ADS.value:
                    db = ads_data_source_repository.get_ads_db(data_source_model.conn_str)
            except DatabaseError as de:
                # 直连数据集，db连接错误，发送消息
                QueryDatasetService.send_message(data_source_model)
                raise DatasetDBException(
                    db=data_source_model.conn_str.database,
                    host=data_source_model.conn_str.host,
                    msg='数据源连接错误：' + str(de) + "，采用缓存数据显示。",
                ) from de
            query_data = DirectMysqlQueryData(query_structure, dataset_data, db)
        elif data_source_model.type == DataSourceType.MysoftShuXin15.value:
            query_data = MysoftShuXin15SqlQuery(query_type=MysoftShuXin15QueryType.query_structure.value,
                                                query_structure=query_structure, data_source_model=data_source_model,
                                                dataset_data=dataset_data, dataset_fields=dataset_fields, user_id=user_id)
        else:
            raise UserError(message="直连模式不支持该数据源：" + str(data_source_model.type))
        return query_data

    def get_query_data(self, table_name: str = "", table_alias="", is_api_schedule=False) -> ResultData:
        """
        根据QueryStructure对象获取查询数据（重写父类的查询数据方法）
        :return:
        """
        AnalysisTimeUtils.recode_time_node('开始数据源取数前的query_structure构造')
        # 1、获取数据集/主题元数据
        # dataset_data 在内部还需要使用，避免每次传参，放到self中
        self.dataset_data = dataset_meta_cache.get_dataset_cache(self.dataset_id, self.query_dataset_version)
        AnalysisTimeUtils.recode_time_node('疑似代码位置-4')

        if not self.dataset_data:
            raise DatasetQueryException(msg="数据集不存在，数据集ID：" + self.dataset_id)
        if self.external_subject_ids:
            self.load_external_subjects()
        dataset_name = (
            self.external_subject[self.external_subject_ids[0]].get("name")
            if self.external_subject
            else self.dataset_data.get('name')
        )
        dataset_fields = self._get_real_dataset_fields(self.dataset_id, self.external_subject_ids)

        AnalysisTimeUtils.recode_time_node('开始数据源取数前的query_structure构造-1')

        # 2、生成最终查询json
        query_structure = self.generate_query_structure(
            self.query_structure_json,
            self.dataset_data,
            table_name=table_name,
            table_alias=table_alias,
            external_subject_ids=self.external_subject_ids,
            is_api_schedule=is_api_schedule,
        )
        debugger.log({"增加权限最终数据集查询数据json": json.dumps(query_structure, cls=ModelEncoder)})
        # 判断是否缓存取数结果
        need_cache = True
        if (query_structure.limit.offset and int(query_structure.limit.offset) >= 500) or (
                query_structure.limit.row and int(query_structure.limit.row) > 1500 or os.environ.get(
            'CELERY_APP') == '1'
        ):
            need_cache = False

        AnalysisTimeUtils.recode_time_node('结束数据源取数前的query_structure构造')
        AnalysisTimeUtils.recode_time_node('开始数据源取数')
        # 3、查询数据集结果
        data_update_time = None
        start_time = time.time()
        try:
            query_client = self.get_query_client(
                self.dataset_data,
                query_structure,
                dataset_fields=dataset_fields,
                is_order_master_id=self.is_order_master_id,
                is_api_schedule=is_api_schedule,
                chart_data_model=self.chart_data_model,
                user_id=self.user_id
            )
        except DatasetDBException as de:
            # 直连数据集db连接错误
            sql = query_sql_encoder.encode_query(query_structure)
            mng = DirectDatasetCacheManager(g.code, self.dataset_id, de.db, de.host, sql)
            result = mng.get_result_cache(cal_visit=False)
            result_data = self.assembly_result(
                sql, result, 0, dataset_fields, query_structure, **{"msg": de.msg, "code": de.code}
            )
            result_data.dataset_name = dataset_name
            return result_data

        if self.dataset_data.get("type") in [DatasetType.ExternalSubject.value]:
            result, sql, data_update_time = query_client.get_query_data()
        elif is_api_schedule and self.dataset_data.get("type") == DatasetType.Api.value:
            result, sql, data_update_time = query_client.get_query_data()
        elif (self.dataset_data.get('connect_type') == DatasetConnectType.Directly.value) and isinstance(
                query_client, ApiQueryData
        ):
            result, sql, data_update_time = query_client.get_query_data()
        elif (self.dataset_data.get('connect_type') == DatasetConnectType.Directly.value) and (
                isinstance(query_client, DirectMssqlQueryData)
        ):
            use_cache = bool(self.dataset_data.get('use_cache'))
            result, sql = self.query_direct_sql_data(query_client, use_cache, need_cache)
        elif (self.dataset_data.get('connect_type') == DatasetConnectType.Directly.value) and (
                isinstance(query_client, DirectSqlQueryData)
        ):
            debugger.log({"直连-db：查询db数据或查询数据缓存前是否存在db连接": bool(query_client.connection_db.conn)})
            use_cache = bool(self.dataset_data.get('use_cache'))
            result, sql = self.query_direct_sql_data(query_client, use_cache, need_cache)
        elif isinstance(query_client, MysqlQueryData):
            debugger.log({"data-db：查询db数据或查询数据缓存前是否存在db连接": bool(query_client.connection_db.conn)})
            result, sql, _ = self.query_mysql_data(query_client, need_cache)
        # 数据服务中心取数查询
        elif isinstance(query_client, MySoftNewErpQueryData):
            result, sql, _ = self.query_mysoftNewErp_data(query_client, need_cache)
        else:
            result, sql, _ = query_client.get_query_data()

        end_time = time.time()
        # 4、组装返回结果对象
        result_data = self.assembly_result(
            sql, result, int(round((end_time - start_time) * 1000)), dataset_fields, query_structure
        )
        dataset_cache = DatasetCache(g.code, self.dataset_id, conn_redis())
        result_data.meta_version, result_data.data_version = self._get_dataset_version(self.dataset_id, dataset_cache)
        result_data.dataset_name = dataset_name
        # 如果上游返回了数据版本号, 则直接使用上游的版本号(API数据集由API返回数据版本号)
        if data_update_time:
            try:
                date = datetime.datetime.strptime(data_update_time, "%Y-%m-%d %H:%M:%S")
                data_version = '%s_%s' % (date.strftime('%Y%m%d%H%M%S'), '019')
                result_data.data_version = data_version
                result_data.meta_version = data_version
            except Exception as e:
                raise DatasetQueryException(msg="API数据集返回数据更新时间格式错误：" + str(data_update_time)) from e
        AnalysisTimeUtils.recode_time_node('结束数据源取数')
        return result_data

    def load_external_subjects(self):
        if not self.external_subject:
            for external_subject_id in self.external_subject_ids:
                external_subject = external_self_service.get_external_subject(external_subject_id)
                if not external_subject:
                    raise DatasetQueryException(msg=f"外部主题 {external_subject_id} 不存在!")
                self.external_subject[external_subject_id] = external_subject

    def get_query_sql(self, table_name: str = "", table_alias: str = "") -> str:
        """
        获取查询SQL
        :return:
        """
        # 1、获取数据集/主题元数据
        # dataset_data 在内部还需要使用，避免每次传参，放到self中
        self.dataset_data = dataset_meta_cache.get_dataset_cache(self.dataset_id, self.query_dataset_version)
        if not self.dataset_data:
            raise DatasetQueryException(msg="数据集不存在，数据集ID：" + self.dataset_id)
        if self.external_subject_ids:
            self.load_external_subjects()
        dataset_fields = self._get_real_dataset_fields(self.dataset_id, self.external_subject_ids)

        # 2、生成最终查询json
        query_structure = self.generate_query_structure(
            self.query_structure_json,
            self.dataset_data,
            table_name=table_name,
            table_alias=table_alias,
            external_subject_ids=self.external_subject_ids,
        )
        debugger.log({"增加权限最终数据集查询数据json": json.dumps(query_structure, cls=ModelEncoder)})
        # 3、生成SQL
        try:
            query_client = self.get_query_client(
                self.dataset_data,
                query_structure,
                dataset_fields=dataset_fields,
                is_order_master_id=self.is_order_master_id,
                chart_data_model=self.chart_data_model
            )
        except DatasetDBException:
            # 直连数据集db连接错误
            sql = query_sql_encoder.encode_query(query_structure)
            return sql
        return query_client.generate_sql()

    def _get_dataset_version(self, dataset_id, dataset_cache):
        """
        获取版本信息
        :param dataset_id:
        :param dataset_cache:
        :return:
        """
        version, data_version = self.get_directly_dataset_version(dataset_id)
        if data_version:
            return version, data_version
        version, data_version = dataset_cache.hmget(
            (DatasetCache.dataset_meta_version, DatasetCache.dataset_data_version)
        )
        if not data_version:
            dataset_version = dataset_version_repository.get_dataset_current_version(dataset_id)
            # 如果没有获取到时间 将当前时间赋值为更新时间
            if not dataset_version:
                version = data_version = '%s_%s' % (time.strftime('%Y%m%d%H%M%S'), random.randint(1, 1000))
            else:
                # 将version和data_version统一起来
                version = data_version = (
                        re.sub(r"\s|-|:", "", str(dataset_version["created_on"]))
                        + "_"
                        + str(dataset_version["version_number"])
                )
            dataset_cache.set_prop(dataset_cache.dataset_meta_version, version)
            dataset_cache.set_prop(dataset_cache.dataset_data_version, data_version)
        return version, data_version

    @staticmethod
    def get_directly_dataset_version(dataset_id):
        """
        直连数据集获取数据版本、元数据版本
        :param dataset_id:
        :return:
        """
        data = dataset_meta_cache.get_dataset_cache(dataset_id)
        version = data_version = None
        if data.get("connect_type") == DatasetConnectType.Directly.value:
            # 直连缓存模式
            if data['use_cache'] and data.get('cache_flow_id'):
                directly_instance = dataset_version_repository. \
                    get_directly_dataset_current_version(data.get('cache_flow_id'))
                if directly_instance:
                    version = data_version = (
                            re.sub(r"\s|-|:", "", str(directly_instance["end_time"]))
                            + "_"
                            + str(random.randint(1, 1000))
                    )
            # 直连无缓存或者直连缓存未形成版本
            if not data_version:
                version = data_version = '%s_%s' % (time.strftime('%Y%m%d%H%M%S'), random.randint(1, 1000))
        return version, data_version

    def get_query_structure(self, table_name="", table_alias=""):
        """
        获取query_structure
        :param table_name:
        :return:
        """
        # 1、获取数据集元数据
        self.dataset_data = dataset_meta_cache.get_dataset_cache(self.dataset_id)
        if not self.dataset_data:
            raise DatasetQueryException(msg="数据集不存在，数据集ID：" + self.dataset_id)

        # 2、生成最终查询json
        query_structure = self.generate_query_structure(
            self.query_structure_json,
            self.dataset_data,
            table_name=table_name,
            table_alias=table_alias,
            external_subject_ids=self.external_subject_ids,
        )

        dataset_fields = self._get_real_dataset_fields(self.dataset_id, self.external_subject_ids)
        query_client = self.get_query_client(
            self.dataset_data,
            query_structure,
            dataset_fields=dataset_fields,
            is_order_master_id=self.is_order_master_id,
            chart_data_model=self.chart_data_model
        )
        query_structure = query_client.get_query_structure()
        return query_structure

    def get_query_struct(self, table_name="", table_alias=""):
        """
        根据QueryStructure对象获取查询结构
        :return: QueryStructData
        """
        query_structure = self.get_query_structure(table_name, table_alias)

        # 3. 获取sql
        db_engine = None
        if self.external_subject_ids:
            db_engine = external_subject_utils.get_external_subject_db_engine()
        sql = query_sql_encoder.encode_query(query_structure, db_engine=db_engine)

        dataset_name = self.dataset_data.get('name')
        # 3. 获取缓存
        dataset_cache = DatasetCache(g.code, self.dataset_id, conn_redis())
        meta_version = dataset_cache.hmget((DatasetCache.dataset_meta_version,))
        return QueryStructData(
            **{
                "query_structure": self.query_struct_model_to_dict(query_structure),
                "dataset_name": dataset_name,
                "meta_version": meta_version[0] if meta_version else "",
                "sql": sql,
            }
        )

    def query_direct_sql_data(self, query_client: DirectSqlQueryData, use_cache: bool = False, need_cache: bool = True):
        sql = query_client.generate_sql()
        # sql中存在动态时间计算不需要走缓存
        if query_client.is_dynamic_sql(sql):
            res = query_client.get_query_data(sql=sql)
            return res[0], res[1]
        mng = DirectDatasetCacheManager(
            g.code, self.dataset_id, query_client.connection_db.db, query_client.connection_db.host, sql
        )
        # 先从缓存中获取数据集结果数据
        result = None
        if use_cache:
            result = mng.get_result_cache()

        # 缓存中没有数据，从数据库中获取数据
        if not result:
            hit_cache = False
            AnalysisTimeUtils.recode_time_node('开始实际客户数据源SQL取数')
            res = query_client.get_query_data(sql=sql)
            AnalysisTimeUtils.recode_time_node('结束实际客户数据源SQL取数')
            result, sql = res[0], res[1]
            if use_cache:
                mng.set_result_cache(result)
        else:
            hit_cache = True
        if use_cache:
            self.record_cache_hit_statistics(hit_cache, need_cache)
        return result, sql

    def common_query_data_with_scheduled_dataset(
            self,
            query_client: Union[MysqlQueryData, MySoftNewErpQueryData],
            need_cache: bool = True
    ) -> Union[
        Tuple[List[Dict[str, float]], str, str],
        Tuple[List[Dict[str, Union[str, float]]], str, str],
        Tuple[List[Union[Dict[str, Union[str, None]], Dict[str, Union[str, Decimal]]]], str, str],
    ]:
        """通用的调度数据集组件取数SQL缓存流程"""
        sql = query_client.generate_sql()
        result_cache = ScheduleDatasetCacheManager(
            g.code, self.dataset_id, query_client.connection_db.db, query_client.connection_db.host, sql
        )
        # sql中存在动态时间计算不需要走缓存
        if query_client.is_dynamic_sql(sql):
            return query_client.get_query_data(sql=sql)
        # 先从缓存中获取数据集结果数据
        result = result_cache.get_result_cache()
        # 缓存中没有数据，从数据库中获取数据
        if not result:
            hit_cache = False
            res = query_client.get_query_data(sql=sql)
            result, sql = res[0], res[1]
            if need_cache:
                result_cache.set_result_cache(result)
        else:
            hit_cache = True
        self.record_cache_hit_statistics(hit_cache, need_cache)
        return result, sql, ""

    def record_cache_hit_statistics(
            self,
            # query_client: Union[MysqlQueryData, MySoftNewErpQueryData, DirectSqlQueryData],
            # result_cache: Union[ScheduleDatasetCacheManager, DirectDatasetCacheManager],
            hit_cache, need_cache
    ):

        import app_celery
        try:
            # source_class = query_client.__class__.__name__
            # cache_class = result_cache.__class__.__name__
            dashboard_id = ""
            model = getattr(self, "chart_data_model", None)
            if model:
                dashboard_id = getattr(model, "dashboard_id", "")
            g_request_data = getattr(g, 'request_data', {})
            if not g_request_data:
                return
            hit_date = datetime.datetime.now().strftime('%Y-%m-%d')
            if self.data_source:
                data_source_id = self.data_source.id
            else:
                try:
                    data_source = json.loads(self.dataset_data.get('content', '{}'))
                    data_source_id = data_source.get('data_source_id', '')
                except Exception as e:
                    logger.error(f'解析data_source_id失败： {str(e)}')
                    data_source_id = ''
            kwargs = {
                # "source_class": source_class,
                # "cache_class": cache_class,
                "dataset_id": getattr(self, "dataset_id", ""),
                "data_source_id": data_source_id,  # 冗余字段
                "chart_id": getattr(self, "chart_id", ""),
                "dashboard_id": dashboard_id,
                "hit_cache": hit_cache,
                "hit_date": hit_date,
                "need_cache": need_cache,
                "code": g.code,
                # "user_id": getattr(g, "userid", ""),
                "account": getattr(g, "account", ""),
                "g_request_data": g_request_data,
            }
            app_celery.cache_hit_statistics.apply_async(kwargs=kwargs, queue='parser')
        except Exception as e:
            logger.error(f'添加缓存异步命中统计任务失败： {str(e)}, 详细原因： {traceback.format_exc()}')

    def query_mysql_data(
            self, query_client: MysqlQueryData, need_cache: bool = True
    ):
        """查询数据落地在DMP中mysql的数据"""
        return self.common_query_data_with_scheduled_dataset(query_client, need_cache)

    def query_mysoftNewErp_data(
            self, query_client: MySoftNewErpQueryData, need_cache: bool = True
    ):
        """查询数据在数据服务中心的数据"""
        # 1. 直连不走缓存
        if self.dataset_data.get("connect_type") == DatasetConnectType.Directly.value:
            return query_client.get_query_data()
        else:
            return self.common_query_data_with_scheduled_dataset(query_client, need_cache)

    @staticmethod
    def query_struct_model_to_dict(query_structure: QueryStructure) -> Dict[str, Any]:
        return json.loads(json.dumps(query_structure, cls=ModelEncoder))

    def assembly_result(
            self,
            sql: str,
            result: Union[
                List[Dict[str, float]],
                List[Union[Dict[str, Union[str, None]], Dict[str, Union[str, Decimal]]]],
                List[Dict[str, Union[str, float]]],
            ],
            sql_execute_time: int,
            dataset_fields: List[Dict[str, Union[str, int, None]]],
            query_structure: QueryStructure,
            **kwargs,
    ) -> ResultData:
        msg = kwargs.get("msg", "")
        code = kwargs.get("code", 200)
        result_data = ResultData()
        result_data.sql = sql
        result_data.code = code
        result_data.sql_execute_time = sql_execute_time
        result_data.query_structure = self.query_struct_model_to_dict(query_structure)
        if result:
            # 多角色隐藏字段数据取其交集
            permit_field_set = None
            for permit_field in self.permit_fields:
                # 判断类型，这里必须用set,前面有可能传的是list
                if isinstance(permit_field, list):
                    permit_field = set(permit_field)
                if permit_field_set is None:
                    permit_field_set = permit_field
                else:
                    permit_field_set = permit_field_set & permit_field
            data = dataset_data_helper.process_result(result, dataset_fields, permit_field_set)
            result_data.data = data
        else:
            result_data.sql = sql
        result_data.msg = msg
        return result_data

    @staticmethod
    def send_message(data_source_model):
        # 一小时内只保存一条消息
        _message = {
            'source_id': data_source_model.name + datetime.datetime.now().strftime("%Y-%m-%d %H"),
            'source': '数据源',
            'type': '系统消息',
            'title': '{}数据源连接失败'.format(data_source_model.name),
            'url': '/datasource/detail/mysql/{}'.format(data_source_model.id),
        }
        message_service.message_add(MessageModel(**_message))

    @staticmethod
    def get_datasource(dataset_id):
        try:
            dataset_data = dataset_meta_cache.get_dataset_cache(dataset_id)
            return QueryDatasetService.get_data_source_model(dataset_data.get("content"))
        except:
            return None
