#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    直连Mysql类型查询数据类
    <NAME_EMAIL> on 2018/9/18.
"""
import logging
import os
from typing import Dict
from sqlalchemy import text

from base.enums import DatasetEditMode
from base.enums import DBEngine
from components.log_setting import Debug
from components.timeout import CustomTimeoutError, timeout
from components.orm import OrmDb
from dataset.query.result_data import DatasetQuerySqlException, DatasetQuerySqlTimeOutException
from dataset.query.sql_query_data import QueryData
from dataset.services.dataset_mssql_service import DatasetMssqlService
from dataset.services.dataset_mysql_service import DatasetMysqlService
from dataset.services.dataset_var_service import get_dataset_vars
from dataset.common import dataset_data_helper
import json
from components import query_sql_encoder
from components.query_models import ModelEncoder
from dataset.query.result_data import DatasetQueryJsonToSqlException, status_code
from components.analysis_time import AnalysisTimeUtils


from components.query_structure_sql import QueryStructure
from dmplib.db.mysql_wrapper import SimpleMysql

debugger = Debug("direct_mysql_query_data")

logger = logging.getLogger(__name__)
web_time_out = int(os.environ.get('TIMEOUT', 10)) - 1


class DirectMssqlQueryData(QueryData):

    __slots__ = ['query_structure', 'dataset_data', 'connection_db']

    def __init__(self, query_structure: QueryStructure, dataset_data: Dict, connection_db):
        super().__init__(query_structure)
        self.connection_db = connection_db
        self.connection_db.db = connection_db.bind.url.database
        self.connection_db.host = connection_db.bind.url.host
        self.query_structure = query_structure
        self.dataset_data = dataset_data

    def replace_dataset_sql_vars(self):
        """
        替换API数据集SQL模式的数据集变量为报告传入的值或者使用数据集变量的默认值
        :return:
        """
        # if self.dataset_data['edit_mode'] != DatasetEditMode.Sql.value:
        #    return
        # 视图模式也存在变量

        dataset_id = self.dataset_data['id']
        dataset_vars = get_dataset_vars(dataset_id)
        if not dataset_vars:
            return

        dataset_vars_map = {r['id']: r for r in dataset_vars}
        query_vars = self.query_structure.vars
        query_vars_map = {}
        for query_var in query_vars:
            var_id = query_var.var_id
            var_data = query_var.get_dict()
            var_data['id'] = var_id
            query_vars_map[var_id] = var_data
        dataset_vars_map.update(query_vars_map)
        vars_data_list = list(dataset_vars_map.values())

        for query_object in self.query_structure.object:
            query_object.name = DatasetMysqlService.replace_dataset_sql_var_id_as_values(
                query_object.name, vars_data_list
            )
        for where_obj in self.query_structure.where:
            if where_obj:
                DatasetMssqlService.relace_where_sql_vars(where_obj, vars_data_list)

    def generate_sql(self) -> str:
        """
        根据QueryStructure对象生成可执行的sql语句
        :return:
        """
        self.replace_dataset_sql_vars()
        query_structure = self.get_query_structure()
        try:
            sql = query_sql_encoder.encode_query(query_structure, db_engine=DBEngine.MSSQL.value)
        except Exception as e:
            query_structure_json = json.dumps(query_structure, cls=ModelEncoder)
            raise DatasetQueryJsonToSqlException(
                msg=status_code.get(502) + str(e), query_structure_json=query_structure_json
            ) from e
        return sql

    def get_query_data(self, sql=None):
        """
        根据QueryStructure对象获取查询数据（重写父类的查询数据方法）
        :return:
        """
        sql = sql if sql else self.generate_sql()
        st = AnalysisTimeUtils.now()
        msg = "直连数据集查询数据sql：{}".format(sql)
        debugger.log(msg)
        try:
            # 判断是celery runtime, 则不设置timeout
            if os.environ.get('CELERY_APP', '0') == '1':
                return self.get_data(sql), sql

            result = self.get_data_with_timeout(sql), sql, None
            AnalysisTimeUtils.record(
                step=None, sql=sql, db_type=AnalysisTimeUtils.db_type.MSSQL.value, start_time=st,
                extra={}, need_type_inference=True
            )
            return result
        except CustomTimeoutError as e:
            msg = "运行sql时长超过{}秒，{}".format(str(web_time_out), "sql执行超时")
            raise DatasetQuerySqlTimeOutException(msg=msg, sql=sql) from e
        except Exception as e:
            msg = "直连数据集查询数据错误：{}，sql：{}".format(str(e), sql)
            raise DatasetQuerySqlException(msg=msg, sql=sql) from e

    def get_data(self, sql):
        with self.connection_db as db:
            data = db.execute(text(sql))
            data = OrmDb.data_format(data)
            return dataset_data_helper.data_processing(data)

    @timeout(web_time_out)
    def get_data_with_timeout(self, sql):
        # CELERY_APP
        return self.get_data(sql)

    @staticmethod
    def is_dynamic_sql(sql):
        sql = sql.lower()
        slices = ["getdate()", "day()", "month()", "year()"]
        for s in slices:
            if sql.find(s) > -1:
                return True
        return False
