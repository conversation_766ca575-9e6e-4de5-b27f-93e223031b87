#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    数据集查询数据基类
    <NAME_EMAIL> on 2018/9/18.
"""
import os
from copy import deepcopy

from base.errors import InvalidCallError
from base.enums import DatasetFieldGroup
from components.query_structure_sql import QueryStructure
from components.query_structure_sql import Order


class QueryData:
    __slots__ = ['query_structure', 'web_time_out', 'dataset_fields']

    def __init__(self, query_structure: QueryStructure) -> None:
        self.query_structure = query_structure
        self.web_time_out = int(os.environ.get('TIMEOUT', 10)) - 1

    def get_query_data(self, sql=None):
        """
        根据QueryStructure对象获取查询数据 （子类需要实现该方法）
        :return:
        """
        raise NotImplementedError()

    def get_query_structure(self):
        return self.query_structure

    def generate_sql(self) -> str:
        raise InvalidCallError(message="请在子类中实现该方法")

    def group2order(self):
        """
        group by 转 order by
        :return:
        """
        if self.query_structure.order_by:
            return

        groups = self.query_structure.group_by
        if not groups:
            return

        for group in groups:
            if getattr(group, 'func', '') == 'limit1':
                continue
            order = Order()
            for slot in group.__slots__:
                setattr(order, slot, getattr(group, slot))
            order.method = 'ASC'
            order.alias = ""
            self.query_structure.order_by.append(order)

    def get_col_name(self, item):
        if getattr(item, "props", []):
            for prop in item.props:
                return self.get_col_name(prop)
        elif getattr(item, 'prop_name', None):
            return item.prop_name
        else:
            return None

    def filed2order(self, dataset_fields: list, field_key: str, only_dim: bool = False):
        """
        大数据数据源使用，
        没有排序 则以第一个维度字段排序；
        没有维度字段则以
        :return:
        """
        if self.query_structure.order_by:
            return

        select_list = self.query_structure.select
        if not all([select_list, dataset_fields]):
            return

        field_map = {item.get(field_key): item.get('field_group') for item in dataset_fields}
        first_num, first_dim = None, None
        for select in select_list:
            if getattr(select, 'func', '') == 'limit1':
                continue
            col_name = self.get_col_name(select)
            if col_name not in field_map:
                continue
            if field_map.get(col_name) == DatasetFieldGroup.Dimension.value:
                first_dim = deepcopy(select)
                break
            if only_dim is False and not first_num:
                first_num = deepcopy(select)
        order = first_dim or first_num
        if order:
            order.method = "Asc"
            order.alias = ""
            self.query_structure.order_by.append(order)




