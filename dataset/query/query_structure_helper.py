#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    QueryStructure辅助类（提供多表关联Object转换、权限Where追加）
    <NAME_EMAIL> on 2018/9/18.
"""
import copy
import json

from base.enums import SqlWhereOperator, SqlWhereDateType, DatasetFieldDataType, DatasetType, DBEngine
from components.query_models import Object, RefClause, Prop, Where
from components.utils import DateUtil
from dataset.cache import dataset_meta_cache
from dataset.services import dataset_field_service
from dmplib.utils.errors import UserError

from typing import Any, Dict, List, Union
from base.enums import DatasetConnectType


def transform_object(link_datas):
    """
    转换Object对象，（Object最终会拼接成查询的from join）
    :param dataset.query.link_data.LinkData [] link_datas:
    :param db_engine:
    :return: components.query_models.Object []
    """
    if not link_datas:
        raise UserError(message="关联表数据不能为空")
    objects = []
    # 获取开始节点
    start_from_id = _get_start_node(link_datas)
    start_object = Object()
    start_from_name = ""
    start_alias_name = ""
    for link_data in link_datas:
        if start_from_id == link_data.from_id:
            start_from_name = link_data.from_table_name
            start_alias_name = link_data.from_alias_name
            break
    start_object.name = start_from_name
    start_object.alias = start_alias_name
    objects.append(start_object)

    for link_data in link_datas:
        # 有 to_table_name和join_type 才进行join
        if link_data.to_table_name and link_data.join_type:
            join_object = Object()
            join_object.name = link_data.to_table_name
            join_object.alias = link_data.to_alias_name
            join_object.join_type = link_data.join_type.split(" ")[0]
            ref_clauses = _transform_ref_clause(link_data)
            join_object.ref_clause = ref_clauses
            objects.append(join_object)

    return objects


def _get_start_node(link_datas):
    """
    获取开始节点id
    :param link_datas:
    :return:
    """
    start_from_id_set = set()
    for link in link_datas:
        if not link.to_id:
            start_from_id_set.add(link.from_id)
        is_ref = False
        for subset in link_datas:
            if link.from_id == subset.to_id:
                is_ref = True
                break
        if not is_ref:
            start_from_id_set.add(link.from_id)
    if not start_from_id_set:
        raise UserError(message="数据格式错误，没有开始节点。")
    if start_from_id_set and len(start_from_id_set) > 1:
        raise UserError(message="数据格式错误，存在多个开始节点。")

    start_from_id = ""
    for start_id in start_from_id_set:
        start_from_id = start_id

    return start_from_id


def _get_next_node(current_id, link_datas):
    """
    根据当前节点id获取下一级节点
    :param current_id:
    :param link_datas:
    :return:
    """
    next_link_datas = []
    for link in link_datas:
        if current_id == link.from_id:
            next_link_datas.append(link)
    return next_link_datas


def _get_ref_clause(link_data):
    """
    根据连接字段数据获取RefClause对象(on 条件嵌套使用)
    :param link_data:
    :return:
    """

    transform_join_fields = _transform_ref_clause(link_data)

    for i, transform_join_field in enumerate(transform_join_fields):
        next_index = i + 1
        if next_index <= len(transform_join_fields):
            transform_join_field.conditions.append(transform_join_fields[next_index])

    return transform_join_fields[0]


def _transform_ref_clause(link_data):
    """
    将join_fields转换成RefClause对象集合
    :param link_data:
    :return:
    """
    transform_join_fields = []
    for index, join_field in enumerate(link_data.join_fields):
        left_prop = Prop()
        left_prop.obj_name = link_data.from_alias_name if link_data.from_alias_name else link_data.from_table_name
        left_prop.prop_name = join_field.left

        right_prop = Prop()
        right_prop.obj_name = link_data.to_alias_name if link_data.to_alias_name else link_data.to_table_name
        right_prop.prop_name = join_field.right

        ref_clause = RefClause()
        ref_clause.left = left_prop
        ref_clause.operator = join_field.operator if join_field.operator else '='
        ref_clause.right = right_prop
        if index != 0:
            ref_clause.logical_relation = join_field.logical_relation

        transform_join_fields.append(ref_clause)

    return transform_join_fields


def _get_real_dataset_fields(dataset_id: str, external_subject_id: str = None):
    if external_subject_id:
        return dataset_field_service.get_original_external_subject_fields(external_subject_id)
    return dataset_meta_cache.get_dataset_field_cache(dataset_id)


def transform_permission_where(
        fields_auth: Dict[str, List[Any]], dataset_data: Dict[str, Union[str, None]], external_subject_id: str = None
) -> Union[None, Where]:
    """
    转换权限Where对象，（Where最终会拼接到查询的where json）
    :param fields_auth:
    :param dataset_data:
    :param external_subject_id:
    :return: components.query_models.Where
    """
    if not fields_auth or not fields_auth.get('filters'):
        return None

    dataset_field_dict = {}
    dataset_field_list = _get_real_dataset_fields(dataset_data.get('id'), external_subject_id)
    for dataset_field in dataset_field_list:
        dataset_field_dict[dataset_field.get("id")] = dataset_field

    # 多角色权限集合
    permit_conditions = get_permit_conditions(fields_auth.get('filters'))
    if not permit_conditions or len(permit_conditions) < 1:
        return None

    where_obj = Where()
    where_obj.logical_relation = "AND"
    left_prop = Prop()
    where_obj.left = left_prop
    right_prop = Prop()
    where_obj.right = right_prop
    where_obj.conditions = []
    for i, role_conditions in enumerate(permit_conditions):
        role_where = get_role_where(role_conditions, dataset_data, dataset_field_dict)
        # 不同角色的权限关系是OR
        if i == 0:
            role_where.logical_relation = ""
        else:
            role_where.logical_relation = "OR"
        where_obj.conditions.append(role_where)
    return where_obj


def get_permit_conditions(permit_conditions):
    # 如果是 'xxx'='*'  不作为where条件 取出所有
    new_permit_conditions = []
    for role_conditions in permit_conditions:
        new_role_conditions = []
        for condition in role_conditions:
            if condition:
                if (
                        condition.get("operator").upper() == SqlWhereOperator.Eq.value
                        and isinstance(condition.get('col_value'), str)
                        and condition.get('col_value') == '*'
                ):
                    continue
                new_role_conditions.append(condition)
        if new_role_conditions:
            new_permit_conditions.append(new_role_conditions)
    return new_permit_conditions


def get_role_where(role_conditions, dataset_data, dataset_field_dict):
    role_where = Where()
    for i, condition in enumerate(role_conditions):

        # 目前权限数据集数据都是在dmp的单表
        # 后续支持多表情况下需要考虑源表名
        dataset_field = dataset_field_dict.get(condition.get('dataset_field_id'))

        if not dataset_field:
            raise UserError(message="权限使用的数据集字段被删除，请重新修改数据集权限：" + str(condition))

        if dataset_data.get('type') in [DatasetType.ExternalSubject.value] \
                or (dataset_data.get('type') in [DatasetType.Api.value, DatasetType.Sql.value]
                    and dataset_data.get('connect_type') == DatasetConnectType.Directly.value):
            condition['table_name'] = dataset_field.get('origin_table_alias_name') or dataset_field.get(
                'origin_table_name'
            )
            condition['col_name'] = dataset_field.get('origin_col_name') or dataset_field.get('col_name')
        else:
            condition['table_name'] = dataset_data.get("table_name")
            condition['col_name'] = dataset_field.get('col_name')
        where_end = None

        # 同角色内的条件都是 AND
        if condition.get('operator').upper() in [SqlWhereOperator.In.value, SqlWhereOperator.Notin.value]:
            where = where_operator_in(condition, "AND")

        elif condition.get('operator').upper() in get_from_dates():
            where_start, where_end = where_operator_far(condition)
            where = where_start

        elif condition.get('operator').upper() in [SqlWhereOperator.Between.value, SqlWhereOperator.Notbetween.value]:
            where_start, where_end = where_operator_between(condition)
            where = where_start

        else:
            where = where_operator_common(condition, "AND")

        if i == 0:
            where.logical_relation = ""

        role_where.conditions.append(where)
        if where_end:
            role_where.conditions.append(where_end)

    return role_where


def get_from_dates():
    return [
        SqlWhereOperator.FromDay.value,
        SqlWhereOperator.FromYesterday.value,
        SqlWhereOperator.FromWeek.value,
        SqlWhereOperator.FromMonth.value,
        SqlWhereOperator.FromQuarter.value,
        SqlWhereOperator.FromYear.value,
    ]


def where_operator_common(condition, logical_relation, is_like=False):
    """
    常用 操作符
    :param condition:
    :param logical_relation:
    :param is_like:
    :return:
    """
    where_obj = Where()
    where_obj.logical_relation = logical_relation
    where_obj.operator = condition.get('operator')
    where_obj.left = generate_left_prop(condition)
    right_prop = Prop()
    if is_like:
        like_type = condition.get('like_type', 'contains')
        like_format_str = "{sign}{col_value}{sign}"
        if like_type == 'start':
            like_format_str = '{col_value}{sign}'
        elif like_type == 'end':
            like_format_str = '{sign}{col_value}'
        right_prop.value = like_format_str.format(sign="%", col_value=condition.get('col_value'))
    else:
        right_prop.value = condition.get('col_value')
    right_prop.value_type = condition.get('col_value_type', '固定值')  # 固定值或者变量

    where_obj.right = right_prop
    return where_obj


def where_operator_in(condition, logical_relation):
    """
    in notin 操作符
    :param condition:
    :param logical_relation:
    :return:
    """
    where_obj = Where()
    where_obj.logical_relation = logical_relation
    where_obj.operator = condition.get('operator')
    where_obj.left = generate_left_prop(condition)
    right_prop = Prop()
    right_prop.value_type = condition.get('col_value_type', '固定值')  # 固定值或者变量
    if isinstance(condition.get('col_value'), list) or right_prop.value_type == '变量':
        value = condition.get('col_value')
    else:
        value = [condition.get('col_value')]

    right_prop.value = value
    where_obj.right = right_prop
    return where_obj


def where_operator_far(condition):
    """
    far 操作符
    :param condition:
    :return:
    """
    operator = condition.get('operator').upper()
    date_util = DateUtil()
    field_value = int(condition.get('col_value'))  # todo: 分析value是变量场景下如何处理

    start_where = Where()
    end_where = Where()

    if SqlWhereOperator.FromDay.value == operator:
        value = date_util.get_from_today(field_value)
        start_where = get_far_where(condition, SqlWhereOperator.Gte.value, value, 'AND')
        end_value = date_util.get_today()
        end_where = get_far_where(condition, SqlWhereOperator.Lte.value, end_value, 'AND')

    elif SqlWhereOperator.FromYesterday.value == operator:
        value = date_util.get_from_today(field_value)
        start_where = get_far_where(condition, SqlWhereOperator.Gte.value, value, 'AND')
        end_value = date_util.get_yesterday()
        end_where = get_far_where(condition, SqlWhereOperator.Lte.value, end_value, 'AND')
    elif SqlWhereOperator.FromWeek.value == operator:
        # 本周 field_value == 0
        # 上周 field_value == 1
        week_start, week_end = date_util.get_last_week() if field_value == 1 else date_util.get_this_week()
        start_where = get_far_where(condition, SqlWhereOperator.Gte.value, week_start, 'AND')
        end_where = get_far_where(condition, SqlWhereOperator.Lte.value, week_end, 'AND')
    elif SqlWhereOperator.FromMonth.value == operator:
        # 本月 field_value == 0:
        # 上月 if field_value == 1
        month_start, month_end = date_util.get_last_month() if field_value == 1 else date_util.get_this_month()
        start_where = get_far_where(condition, SqlWhereOperator.Gte.value, month_start, 'AND')
        end_where = get_far_where(condition, SqlWhereOperator.Lte.value, month_end, 'AND')
    elif SqlWhereOperator.FromQuarter.value == operator:
        # 本季度 field_value == 0
        # 上季度 field_value == 1
        quarter_start, quarter_end = date_util.get_last_quarter() if field_value == 1 else date_util.get_this_quarter()
        start_where = get_far_where(condition, SqlWhereOperator.Gte.value, quarter_start, 'AND')
        end_where = get_far_where(condition, SqlWhereOperator.Lte.value, quarter_end, 'AND')
    elif SqlWhereOperator.FromYear.value == operator:
        # 本年 field_value == 0
        # 上年 field_value == 1
        year_start, year_end = date_util.get_last_year() if field_value == 1 else date_util.get_this_year()
        start_where = get_far_where(condition, SqlWhereOperator.Gte.value, year_start, 'AND')
        end_where = get_far_where(condition, SqlWhereOperator.Lte.value, year_end, 'AND')

    return start_where, end_where


def get_far_where(condition, operator, value, logical_relation):
    """
    获取far的where对象
    :param condition:
    :param operator:
    :param value:
    :param logical_relation:
    :return:
    """
    where_obj = Where()
    where_obj.logical_relation = logical_relation
    where_obj.operator = operator

    prop = Prop()
    prop.func = 'date_format'
    field_prop = Prop()
    field_prop.obj_name = condition.get('table_name')
    field_prop.prop_name = condition.get('col_name')
    prop.props.append(field_prop)
    format_prop = Prop()
    format_prop.operator = ','
    format_prop.value = '%Y-%m-%d'
    prop.props.append(format_prop)

    where_obj.left = prop
    right_prop = Prop()
    right_prop.value = value
    where_obj.right = right_prop
    return where_obj


def get_between_where(condition, operator, value, logical_relation):
    """
    获取between 的 where对象
    :param condition:
    :param operator:
    :param value:
    :param logical_relation:
    :return:
    """
    where_obj = Where()
    where_obj.logical_relation = logical_relation
    where_obj.operator = operator
    left_prop = generate_left_prop(condition)
    where_obj.left = left_prop
    right_prop = Prop()
    right_prop.value = value
    where_obj.right = right_prop
    return where_obj


def get_condition_col_value(condition):
    if isinstance(condition.get('col_value'), list):
        value_list = condition.get('col_value')
    else:
        try:
            value_list = json.loads(condition.get('col_value'))
        except Exception:
            value_list = [condition.get('col_value')]
    return value_list


def where_operator_between(condition):
    """
    between 操作符
    :param condition:
    :return:
    """
    value_list = get_condition_col_value(condition)

    if not value_list or len(value_list) < 2:
        raise UserError(message="权限设置格式错误，value长度不能少于2：" + str(condition))

    if condition.get("data_type") == DatasetFieldDataType.Datetime.value:
        where_before = get_far_where(condition, SqlWhereOperator.Gte.value, value_list[0], 'AND')
    else:
        where_before = get_between_where(condition, SqlWhereOperator.Gte.value, value_list[0], 'AND')
    value = value_list[1]
    date_util = DateUtil()
    if SqlWhereDateType.Today.value == value:
        value = date_util.get_today()
    elif SqlWhereDateType.Yesterday.value == value:
        value = date_util.get_yesterday()
    elif SqlWhereDateType.Lastweek.value == value:
        start, end = date_util.get_last_week()
        value = end
    elif SqlWhereDateType.Lastmonth.value == value:
        start, end = date_util.get_last_month()
        value = end
    elif SqlWhereDateType.Lastquarter.value == value:
        start, end = date_util.get_last_quarter()
        value = end
    elif SqlWhereDateType.Lastyear.value == value:
        start, end = date_util.get_last_year()
        value = end
    if condition.get("data_type") == DatasetFieldDataType.Datetime.value:
        where_later = get_far_where(condition, SqlWhereOperator.Lte.value, value, 'AND')
    else:
        where_later = get_between_where(condition, SqlWhereOperator.Lte.value, value, 'AND')
    return where_before, where_later


def where_operator_null(condition, logical_relation):
    """
    is null 、is not null 操作符
    :param condition:
    :param logical_relation:
    :return:
    """
    where_obj = Where()
    where_obj.logical_relation = ""
    where_obj.left = generate_left_prop(condition)
    right_prop = Prop()
    right_prop.prop_raw = condition.get('operator')
    where_obj.right = right_prop

    where = Where()
    where.logical_relation = logical_relation
    where.conditions.append(where_obj)

    if condition.get('col_type') not in [DatasetFieldDataType.Number.value, DatasetFieldDataType.Datetime.value]:
        where_obj1 = Where()
        where_obj1.logical_relation = "OR" if condition.get('operator') == SqlWhereOperator.IsNull.value else "AND"
        where_obj1.left = generate_left_prop(condition)
        right_prop1 = Prop()
        right_prop1.prop_raw = " = '' " if condition.get('operator') == SqlWhereOperator.IsNull.value else " <> '' "
        where_obj1.right = right_prop1
        where.conditions.append(where_obj1)

    return where


def generate_left_prop(condition):
    left_prop = Prop()
    col_name_type = condition.get('col_name_type', 'col')
    if col_name_type == 'var':
        left_prop.value = condition.get('col_name')
        left_prop.value_type = '变量'
    else:
        left_prop.obj_name = condition.get('table_name')
        left_prop.prop_name = condition.get('col_name')
    return left_prop


def transform_filter_to_dataset_data(filter_content, dataset_fields, dataset_table_name):
    """
    转换过滤器的表名和字段
    原始表名和字段转换为落地数据集的表名和hash字段
    :param filter_content:
    :param dataset_fields:
    :param dataset_table_name:
    :return:
    """
    if not filter_content:
        return filter_content

    if not dataset_fields:
        return filter_content

    if filter_content and filter_content[0].get('filter_type', 'list') == 'json':  # JSON格式的condition 处理
        filter_json_obj = json.loads(filter_content[0].get('json_value', '{}'))
        filter_json_obj = transform_filter_to_dataset_data_json(filter_json_obj, dataset_fields, dataset_table_name)
        filter_content[0]['json_value'] = json.dumps(filter_json_obj)
        return filter_content
    else:
        return transform_filter_to_dataset_data_list(filter_content, dataset_fields, dataset_table_name)


def transform_filter_to_dataset_data_list(filter_content, dataset_fields, dataset_table_name):
    """
    转换过滤器的表名和字段
    原始表名和字段转换为落地数据集的表名和hash字段
    :param filter_content:
    :param dataset_fields:
    :param dataset_table_name:
    :return:
    """
    if not filter_content:
        return filter_content

    if not dataset_fields:
        return filter_content

    new_filter_content = []

    for data in filter_content:
        new_data = data.copy()
        new_data['table_name'] = dataset_table_name
        for dataset_field in dataset_fields:
            if dataset_field.get("origin_table_id") == new_data.get("table_id") and dataset_field.get(
                    "origin_col_name"
            ) == new_data.get("col_name"):
                new_data['col_name'] = dataset_field.get('col_name')
                break
        new_filter_content.append(new_data)
    return new_filter_content


def transform_filter_to_dataset_data_json(filter_content, dataset_fields, dataset_table_name):
    """
    转换过滤器的表名和字段
    原始表名和字段转换为落地数据集的表名和hash字段
    :param filter_content:
    :param dataset_fields:
    :param dataset_table_name:
    :return:
    """
    if not filter_content:
        return filter_content

    if not dataset_fields:
        return filter_content
    if isinstance(filter_content, dict):  # dict 套 list
        for key, filter_obj in filter_content.items():
            if key in ('or', 'and'):  # 需要递归计算的条件
                filter_content[key] = transform_filter_to_dataset_data_json(filter_obj, dataset_fields,
                                                                            dataset_table_name)
            else:
                filter_content[key] = transform_filter_to_dataset_data_list([filter_obj], dataset_fields,
                                                                            dataset_table_name)
        return filter_content

    for i, filter_obj in enumerate(filter_content):  # list 套 dict
        if len(filter_obj) == 1:
            filter_content[i] = transform_filter_to_dataset_data_json(filter_obj, dataset_fields, dataset_table_name)
        else:
            filter_content[i] = transform_filter_to_dataset_data_list([filter_obj], dataset_fields, dataset_table_name)
    return filter_content


def transform_filter_content(filter_content, link_datas):
    """
    转换过滤器的表别名
    :param filter_content:
    :param link_datas:
    :return:
    """
    if not filter_content:
        return filter_content

    if not link_datas:
        return filter_content

    if filter_content and filter_content[0].get('filter_type', 'list') == 'json':  # JSON格式的condition 处理
        filter_json_obj = json.loads(filter_content[0].get('json_value', '{}'))
        filter_json_obj = transform_filter_content_json(filter_json_obj, link_datas)
        filter_content[0]['json_value'] = json.dumps(filter_json_obj)
        return filter_content
    else:
        return transform_filter_content_list(filter_content, link_datas)


def transform_filter_content_list(filter_content, link_datas):
    """
    转换过滤器的表别名
    :param filter_content:
    :param link_datas:
    :return:
    """
    for data in filter_content:
        for link_data in link_datas:
            if link_data.from_id == data.get("table_id") and link_data.from_alias_name:
                data['table_name'] = link_data.from_alias_name
            if link_data.to_id == data.get("table_id") and link_data.to_alias_name:
                data['table_name'] = link_data.to_alias_name
        if data.get('col_type', '') == '数值' and data.get('col_value_type', '') == '固定值':
            try:
                data['col_value'] = float(data.get('col_value', ''))
            except ValueError:  # 不能转换浮点数
                continue
    return filter_content


def transform_filter_content_json(filter_json_obj, link_datas):
    """
    转换过滤器的表别名--json 嵌套模式
    :param filter_content:
    :param link_datas:
    :return:
    """
    if not filter_json_obj:
        return {}
    if isinstance(filter_json_obj, dict):  # dict 套 list
        for key, filter_obj in filter_json_obj.items():
            if key in ('or', 'and'):  # 需要递归计算的条件
                filter_json_obj[key] = transform_filter_content_json(filter_obj, link_datas)
            else:
                filter_json_obj[key] = transform_filter_content_list([filter_obj], link_datas)
        return filter_json_obj

    for i, filter_obj in enumerate(filter_json_obj):  # list 套 dict
        if len(filter_obj) == 1:
            filter_json_obj[i] = transform_filter_content_json(filter_obj, link_datas)
        else:
            filter_json_obj[i] = transform_filter_content_list([filter_obj], link_datas)
    return filter_json_obj


def transform_filter_where(filter_content):
    if filter_content and filter_content[0].get('filter_type', 'list') == 'json':  # JSON格式的condition 处理
        filter_json_obj = json.loads(filter_content[0].get('json_value', '{}'))
        where = transform_filter_where_json(filter_json_obj)
    else:
        where = transform_filter_where_list(filter_content)
    return where


def transform_filter_where_list(filter_content):
    """
    转换数据集过滤器Where对象，（Where最终会拼接到查询的where json）
    :param filter_content:
    :return: components.query_models.Where
    """
    where_obj = Where()
    left_prop = Prop()
    where_obj.left = left_prop
    right_prop = Prop()
    where_obj.right = right_prop
    where_obj.conditions = []

    for i, condition in enumerate(filter_content):

        if condition.get('operator').upper() in [SqlWhereOperator.In.value, SqlWhereOperator.Notin.value]:
            where = where_operator_in(condition, condition.get("logical_relation"))

        elif condition.get('operator').upper() in get_from_dates():
            where = Where()
            where_start, where_end = where_operator_far(condition)
            where_start.logical_relation = ""
            where.conditions.append(where_start)
            where.conditions.append(where_end)
            where.logical_relation = condition.get("logical_relation")

        elif condition.get('col_value_type', '') != '变量' and condition.get('operator').upper() in [
            SqlWhereOperator.Between.value, SqlWhereOperator.Notbetween.value]:
            where = Where()
            where_start, where_end = where_operator_between(condition)
            where_start.logical_relation = ""
            where.conditions.append(where_start)
            where.conditions.append(where_end)
            where.logical_relation = condition.get("logical_relation")

        elif condition.get('operator').upper() in [SqlWhereOperator.IsNull.value, SqlWhereOperator.IsNotNull.value]:
            where = where_operator_null(condition, condition.get("logical_relation"))

        elif condition.get('operator').upper() in [SqlWhereOperator.Like.value, SqlWhereOperator.Nlike.value]:
            where = where_operator_common(condition, condition.get("logical_relation"), is_like=True)

        else:
            where = where_operator_common(condition, condition.get("logical_relation"))

        if i == 0:
            where.logical_relation = ""

        where_obj.conditions.append(where)

    return where_obj


def transform_filter_where_json(filter_json_obj, logical_relation=''):
    if not filter_json_obj:
        return Where()
    result = Where()
    list_conditions = []
    if isinstance(filter_json_obj, dict):  # dict 套 list
        for key, filter_obj in filter_json_obj.items():
            if key in ('or', 'and'):  # 需要递归计算的条件
                fiter_tran = transform_filter_where_json(filter_obj, key)
            else:
                fiter_tran = transform_filter_where_list([filter_obj]).conditions[0]
            if len(filter_json_obj) == 1:
                fiter_tran.logical_relation = ""
                return fiter_tran
            fiter_tran.logical_relation = logical_relation
            list_conditions.append(fiter_tran)
        list_conditions[0].logical_relation = ""
        result.conditions.extend(list_conditions)
        return result

    for i, filter_obj in enumerate(filter_json_obj):  # list 套 dict
        if len(filter_obj) == 1:
            first_key = next(iter(filter_obj))
            fiter_tran = transform_filter_where_json(filter_obj, first_key)
        else:
            fiter_tran = transform_filter_where_list([filter_obj]).conditions[0]
        fiter_tran.logical_relation = logical_relation
        if len(filter_json_obj) == 1:
            fiter_tran.logical_relation = ""
            return fiter_tran
        list_conditions.append(fiter_tran)
    list_conditions[0].logical_relation = ""
    result.conditions.extend(list_conditions)
    return result


def convert_between_where(where_obj):
    #  参考between 转换手法 可能有 'xx' and 'xx' 或者 1 and 2  或者 null and null
    value_list = where_obj.right.value.split(" AND ")
    if len(value_list) != 2:
        return where_obj  # 错误的值 不进行任何替换
    first_right = copy.deepcopy(where_obj.right)
    first_right.value = value_list[0].strip().strip("'")
    where_obj_first = Where(
        logical_relation='',
        left=where_obj.left,
        operator=SqlWhereOperator.Gte.value,
        prop_ref=where_obj.prop_ref,
        prop_raw=where_obj.prop_raw,
        right=first_right
    )
    second_right = copy.deepcopy(where_obj.right)
    second_right.value = value_list[1].strip().strip("'")
    where_obj_second = Where(
        logical_relation="AND",  # and 关系
        left=where_obj.left,
        operator=SqlWhereOperator.Lte.value,
        prop_ref=where_obj.prop_ref,
        prop_raw=where_obj.prop_raw,
        right=second_right
    )
    where_obj_convert = Where()
    where_obj_convert.conditions = [where_obj_first, where_obj_second]
    where_obj_convert.logical_relation = where_obj.logical_relation  # 继承原始的关系
    return where_obj_convert


def convert_in_where(where_obj):
    #  参考in 转换手法 可能有 (null),(1,2,3),('null','xxxx')
    value_str = where_obj.right.value.strip().strip("()")
    if value_str.startswith("'"):
        value_list = value_str[1:len(value_str) - 1].split("','")
    else:
        value_list = value_str.split(",")
    where_obj_convert = Where()
    for value in value_list:
        child_right = copy.deepcopy(where_obj.right)
        child_right.value = value
        where_obj_child = Where(
            logical_relation="OR",  # or 关系
            left=where_obj.left,
            operator=SqlWhereOperator.Eq.value,
            prop_ref=where_obj.prop_ref,
            prop_raw=where_obj.prop_raw,
            right=child_right
        )
        where_obj_convert.conditions.append(where_obj_child)
    where_obj_convert.conditions[0].logical_relation = ""
    where_obj_convert.logical_relation = where_obj.logical_relation  # 继承原始的关系
    return where_obj_convert

def convert_in_where_2(where_obj):
    where_obj_convert = copy.deepcopy(where_obj)
    str_empty = "''"
    where_obj_convert.operator = f'{where_obj.operator} {where_obj.right.value}  and {str_empty} = '
    where_obj_convert.right.value = ''
    where_obj_convert.logical_relation = ''
    convert = Where()
    convert.conditions.append(where_obj_convert)
    return convert


def convert_notin_where(where_obj):
    #  参考in 转换手法 可能有 (null),(1,2,3),('null','xxxx')
    value_str = where_obj.right.value.strip().strip("()")
    if value_str.startswith("'"):
        value_list = value_str[1:len(value_str) - 1].split("','")
    else:
        value_list = value_str.split(",")
    where_obj_convert = Where()
    for value in value_list:
        child_right = copy.deepcopy(where_obj.right)
        child_right.value = value
        where_obj_child = Where(
            logical_relation="AND",  # and 关系
            left=where_obj.left,
            operator=SqlWhereOperator.Neq.value,
            prop_ref=where_obj.prop_ref,
            prop_raw=where_obj.prop_raw,
            right=child_right
        )
        where_obj_convert.conditions.append(where_obj_child)
    where_obj_convert.conditions[0].logical_relation = ""
    where_obj_convert.logical_relation = where_obj.logical_relation  # 继承原始的关系
    return where_obj_convert
