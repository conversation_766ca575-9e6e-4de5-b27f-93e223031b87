#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
import json
import re
from urllib.parse import urlparse

from base.enums import OperationFlowType
from dashboard_chart.models import ChartDownloadModel
from dashboard_chart.services.download import download_service
from dataset.cache import dataset_meta_cache
from dataset.models import (
    DatasetModel,
    OperateRecordQueryModel,
    DatasetVersionModel,
    VsersionQueryModel,
    DatasetFieldModel,
    DatasetIndexDataModel,
    DatasetGroupFieldModel,
    DatasetVarModel,
    DatasetInspectionQueryModel,
    DatasetPermissionModel,
    DatasetTreeAdaptHDModel,
    DatasetFieldGroupModel,
    DatasetFieldGroupRelationModel,
)
from dataset.repositories import dataset_repository
from dataset.services import (
    advanced_field_service,
    dataset_field_service,
    dataset_define_service,
    dataset_async_service,
    dataset_service,
    dataset_version_service,
    dataset_group_field,
    dataset_var_service,
    dataset_subject_service,
    dataset_field_group_service, indicator_service, dataset_define_import_service,
)
from dmplib.db import errors
from dmplib.hug import APIWrapper
from dmplib.hug import g
from dmplib.utils.errors import UserError, DatasetQueryError
from components.global_utils import kwargs_aes_decode, kwargs_base64decode
from components.storage_setting import get_storage_type
from rbac.validator import PermissionValidator
from user_log.models import UserLogModel
from dataset.services import dataset_permission_service

api = APIWrapper(__name__)


@api.admin_route.post('/get_excel_worksheet', validate=PermissionValidator('add-dataset.view'))
def get_excel_worksheet(**kwargs):
    """
    获取Excel工作表
    :param dict kwargs:
    :return tuple:
    """
    return True, '', dataset_async_service.async_get_excel_worksheet(DatasetModel(**kwargs))


@api.admin_route.post('/async_preview')
def async_preview(**kwargs):
    """
    异步预览数据集
    :param dict kwargs:
    :return tuple:
    """
    return True, '', dataset_async_service.async_preview(DatasetModel(**kwargs))


@api.admin_route.post('/async_download')
def async_download(**kwargs):
    """
    异步下载数据集
    :param dict kwargs:
    :return tuple:
    """
    return (True, '', dataset_async_service.async_download(kwargs.get('id'), version_id=kwargs.get('version_id')))


@api.admin_route.get('/get_task_data', validate=PermissionValidator('add-dataset.view'))
def get_task_data(**kwargs):
    """
    获取预览数据
    :param dict kwargs:
    :return tuple:
    """
    return True, '', dataset_service.get_task_data(kwargs.get('task_id'))


@api.route.get('/generate_api_dataset')
def generate_dataset(request, **kwargs):
    """
    生成api数据集
    :param dict kwargs:
    :return tuple:
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    return (
        True,
        '',
        dataset_service.generate_api_dataset(
            kwargs.get("project_code"), kwargs.get("flow_id"), kwargs.get("flow_instance_id")
        ),
    )


@api.admin_route.post('/relate_objects')
def relate_objects(**kwargs):
    """
    关联对象
    :return:
    """
    return True, '操作成功', dataset_service.relate_objects(kwargs.get('id'))


@api.admin_route.post('/relate_dashboard')
def relate_dashboard(**kwargs):
    """
    关联报告
    :return:
    """
    return True, '操作成功', dataset_service.relate_dashboard(kwargs.get('id'))


@api.admin_route.get('/relate_dataset')
def relate_dataset(**kwargs):
    """
    /*
    @apiVersion 1.0.2
    @api {get} /api/dataset/relate_dataset 关联数据集
    @apiGroup  dataset
    @apiParam query {string}  dataset_id  数据集ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [
                {
                    "id": "39e70b36-840c-042d-f437-ba1e7fcfd4c8",
                    "parent_id": "39e6040d-3ece-3563-1ccc-1fcb01b57d56",
                    "type": "UNION",
                    "level_code": "0001-0016-",
                    "folder": "cc",
                    "name": "c_6"
                },
                {
                    "id": "39e70e7b-b4a6-9f14-ca8a-d07cfd3c96bb",
                    "parent_id": "39e70e73-df15-abab-425a-5c7c21b1a780",
                    "type": "UNION",
                    "level_code": "0001-0017-0006-",
                    "folder": "cc/0614",
                    "name": "test_1"
                }
                ]
    }
    */
    """
    return True, '操作成功', dataset_service.relate_dataset(kwargs.get('dataset_id'))


@api.admin_route.get('/union_depend_update', validate=PermissionValidator('add-dataset.view'))
def union_depend_update():
    """
    组合数据集关联数据升级
    :return:
    """
    return True, '操作成功', dataset_service.union_depend_update()


@api.admin_route.post('/operate_record', validate=PermissionValidator('add-dataset.view'))
def operate_record(**kwargs):
    """
    操作记录
    :return:
    """
    return True, '操作成功', dataset_service.get_operate_record_list(OperateRecordQueryModel(**kwargs)).get_dict()


@api.admin_route.get('/flow_status', validate=PermissionValidator('add-dataset.view'))
def get_flow_status(**kwargs):
    """
    获取数据集流程中最后一次状态
    :return:
    """
    return True, '操作成功', dataset_service.get_flow_status(kwargs.get('dataset_id'), kwargs.get('is_cache_flow'))


@api.admin_route.get('/check_clean_status', validate=PermissionValidator('add-dataset.view'))
def check_clean_status(**kwargs):
    """
    /*
    @apiVersion 1.0.2
    @api {get} /api/dataset/check_clean_status 获取数据集版本号
    @apiGroup  dataset
    @apiParam query {string}  dataset_id 数据集ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": true
    }
    */
    """
    return True, '操作成功', dataset_service.check_clean_status_by_dataset_id(kwargs.get('dataset_id'))


@api.admin_route.get('/version/get_version_no', validate=PermissionValidator('add-dataset.view'))
def get_version_no(**kwargs):
    """
    /*
    @apiVersion 1.0.2
    @api {get} /api/dataset/version/get_version_no 获取数据集版本号
    @apiGroup  dataset
    @apiParam query {string}  dataset_id 数据集ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data": [1,2,3]
    }
    */
    """
    return True, '操作成功', dataset_version_service.get_dataset_version_no(kwargs.get('dataset_id'))


@api.admin_route.get('/version/get', validate=PermissionValidator('add-dataset.view'))
def get_version(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /api/dataset/version/get 获取数据集版本
    @apiGroup  dataset
    @apiParam query {string}  id 数据集版本ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              {
                "id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                "dataset_id": "39e50659-8d8d-d1cd-30ab-2da709dd2012",
                "version_number": 1,
                "version_name": "V1",
                "table_name": "dataset_110f1bc7bcd2584a_1",
                "content": "第一个版本"
              }
    }
    */
    """
    return True, '操作成功', dataset_version_service.get_dataset_version(kwargs.get("id"))


@api.admin_route.get('/version/list', validate=PermissionValidator('add-dataset.view'))
def get_version_list(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /api/dataset/version/list 获取数据集版本列表
    @apiGroup  dataset
    @apiParam query {string}  dataset_id 数据集ID
    @apiResponse  200 {
            "result": true,
            "msg": "ok",
            "data": {
            "result": true,
            "msg": "ok",
            "data": {
                 "items": [
                  {
                    "id": "22b11db4-e907-4f1f-8835-b9daab6e1f23",
                    "dataset_id": "39e50659-8d8d-d1cd-30ab-2da709dd2012",
                    "type": "手动",
                    "version_type": "历史版本",
                    "status": "正常",
                    "version_number": 1,
                    "version_name": "V1",
                    "table_name": "dataset_110f1bc7bcd2584a_1",
                    "content": "第一个版本",
                    "created_on": "1900-01-01T00:00:00",
                    "created_by": "超级管理员",
                    "modified_on": "1900-01-01T00:00:00",
                    "modified_by": "超级管理员",
                    "dataset_name": "测试数据集",
                    "dataset_content": {"file_name": "aa月报-excel测试.xlsx"}
                  }
                ],
                "total": 1
            }
        }
    }
    */
    """
    return True, '操作成功', dataset_version_service.get_dataset_version_list(VsersionQueryModel(**kwargs)).get_dict()


@api.admin_route.post('/version/add')
def add_dataset_version(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dataset/version/add 添加数据集版本
    @apiGroup  dataset
    @apiBodyParam {
        "dataset_id{数据集ID}": "39e41b58-acd8-ab00-de05-37e39b170604",
        "version_name{版本名称}": "V1",
        "content{描述}": "第一个版本"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
        }
    */
    """
    model = DatasetVersionModel(**kwargs)
    dataset_version_service.add_dataset_version(model)
    return True, '添加成功', model.id


@api.admin_route.post('/version/delete')
def delete_dataset_version(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dataset/version/delete 删除数据集版本
    @apiGroup  dataset
     @apiParam query {string}  id 版本id
     @apiParam query {string}  table_name 版本表名
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
     }
    */
    """
    return (
        True,
        '删除成功',
        dataset_version_service.delete_dataset_version(
            kwargs.get('dataset_id'), kwargs.get('id'), kwargs.get('table_name')
        ),
    )


@api.admin_route.post('/version/replace_table')
def replace_table(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dataset/version/replace_table 替换数据集表
    @apiGroup  dataset
     @apiParam query {string}  dataset_id 数据集ID
     @apiParam query {string}  version_id 数据集版本ID
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
     }
    */
    """

    result = dataset_version_service.replace_dataset_table(kwargs.get('dataset_id'), kwargs.get('version_id'))

    dataset = dataset_repository.get_dataset_name(kwargs.get('dataset_id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_dataset',
            'id': kwargs.get('id'),
            'content': '应用数据集 [ {dataset_name} ] 版本'.format(dataset_name=dataset.get("name")),
        },
    )

    return True, '替换成功', result


@api.admin_route.post('/data_replacement')
def data_replacement(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dataset/data_replacement 数据集数据替换
    @apiGroup  dataset
     @apiParam query {string}  content 数据集数据替换内容
     @apiParam query {string}  id 数据集ID
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
     }
    */
    """
    result = dataset_service.data_replacement(DatasetModel(**kwargs), kwargs.get("head"))

    # 前端没有传数据集名称，且暂时不加
    dataset = dataset_repository.get_dataset_name(kwargs.get('id'))

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_dataset',
            'id': kwargs.get('id'),
            'content': '替换数据集 [ {dataset_name} ] '.format(dataset_name=dataset.get("name")),
        },
    )

    return True, 'ok', result


@api.admin_route.post('/data_append')
def data_append(request, **kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dataset/data_replacement excel追加数据
    @apiGroup  dataset
     @apiParam query {string}  content 数据集数据替换内容
     @apiParam query {string}  id 数据集ID
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
     }
    */
    """
    result = dataset_service.data_replacement(DatasetModel(**kwargs), kwargs.get("head"), append=True)

    dataset = dataset_repository.get_dataset_name(kwargs.get('id'))
    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_dataset',
            'id': kwargs.get('id'),
            'content': '追加数据集 [ {dataset_name} ] '.format(dataset_name=dataset.get("name")),
        },
    )

    return True, 'ok', result


@api.admin_route.post('/upload')
def upload(file, **kwargs):
    """
    上传文件
    :param dict file:
    :return tuple:
    """
    return True, '', dataset_define_service.upload(file, kwargs)


@api.admin_route.post('/validation_name')
def validation_name(**kwargs):
    """
    校验数据集名称
    :param dict kwargs:
    :return tuple:
    """
    model = DatasetModel(**kwargs)
    model.name = re.sub(r"\s{2,}", " ", model.name).strip()
    dataset_define_service.validate_dataset_name(model)
    return True, '校验成功'


@api.admin_route.post('/multi_add', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def multi_add(request, dataset_list):
    """
    添加数据集
    :param dict kwargs:
    :return tuple:
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    dataset_ids = dataset_define_service.multi_add_dataset(dataset_list)
    return True, '添加成功', dataset_ids


@api.admin_route.post('/add', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def add_dataset(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/dataset/add 添加数据集
    @apiGroup  dataset
    @apiBodyParam {
    "id{数据集id}": "01a5252d-d76d-47ff-ab62-a0da8f34949c",
    "name{数据集名称}": "dataset_name",
    "type{数据集类型}": "SQL",
    "connect_type{连接类型}": "",
    "flow{调度配置信息}": {
            "schedule": "0 0 0 ? * * *",
            "depend_flow_id": "",
            "status": "禁用"
        },
    "content{连接信息}":{
            "data_source_id": "00000000-1111-1111-2222-000000000000",
            "count": 2,
            "sql": "elect aaa from 1pdo",
            "create_table_sql": "create table if not exists dataset_tmp_5cad5650255af (`AAA_3279161851` text )",
            "tmp_table_name": "dataset_tmp_5cad5650255af"
        },
    "field{字段信息}": [{
          "data_type": "字符串",
          "visible": 1,
          "origin_table_id": "",
          "origin_table_name": "",
          "origin_table_alias_name": "",
          "col_name": "AAA_3279161851",
          "origin_table_comment": "",
          "origin_field_type": "",
          "alias_name": "aaa",
          "field_group": "维度",
          "format": "",
          "origin_col_name": ""
        }]
    }
    @apiResponse  200 {
        "result": true,
        "msg": "添加成功",
        "data": "39ed1746-039b-5652-1b20-f3549d8988f8"
    }
    **/
    """

    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    kwargs['type'] = kwargs.get('type').upper()
    model = DatasetModel(**kwargs)

    dataset_define_service.add_dataset(model)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'add_dataset',
            'id': model.id,
            'content': '新增数据集 [ {dataset_name} ] '.format(dataset_name=kwargs.get('name')),
        },
    )

    return True, '添加成功', model.id


@api.admin_route.post('/validate_normal_field')
def validate_normal_field(**kwargs):
    """
    判断普通字段是否更改以及被单图引用
    :param dict kwargs:
    :return tuple:
    """
    model = DatasetModel(**kwargs)
    return True, '校验普通字段成功', dataset_define_service.validate_normal_field(model)


@api.admin_route.post('/validate_senior_field')
@kwargs_aes_decode()
def validate_senior_field(request, **kwargs):
    """
    判断高级字段的引用字段是否被删除
    :param request
    :param dict kwargs:
    :return tuple:
    """
    model = DatasetModel(**kwargs)
    dataset_define_service.validate_senior_field(model)
    return True, '校验高级字段成功', model.id


@api.admin_route.post('/validate_update_field')
def validate_update_field(**kwargs):
    """
    判断数据结构是否变化
    :param dict kwargs:
    :return tuple:
    """
    model = DatasetModel(**kwargs)
    dataset_define_service.validate_update_field(model)
    return True, '校验普通字段成功', model.id


@api.admin_route.post('/update', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def update_dataset(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/dataset/update 修改数据集
    @apiGroup  dataset
    @apiBodyParam {
    "id{数据集id}": "01a5252d-d76d-47ff-ab62-a0da8f34949c",
    "name{数据集名称}": "dataset_name",
    "type{数据集类型}": "SQL",
    "connect_type{连接类型}": "",
    "flow{调度配置信息}": {
            "schedule": "0 0 0 ? * * *",
            "depend_flow_id": "",
            "status": "禁用"
        },
    "content{连接信息}":{
            "data_source_id": "00000000-1111-1111-2222-000000000000",
            "count": 2,
            "sql": "elect aaa from 1pdo",
            "create_table_sql": "create table if not exists dataset_tmp_5cad5650255af (`AAA_3279161851` text )",
            "tmp_table_name": "dataset_tmp_5cad5650255af"
        },
    "field{字段信息}": [{
          "data_type": "字符串",
          "visible": 1,
          "origin_table_id": "",
          "origin_table_name": "",
          "origin_table_alias_name": "",
          "col_name": "AAA_3279161851",
          "origin_table_comment": "",
          "origin_field_type": "",
          "alias_name": "aaa",
          "field_group": "维度",
          "format": "",
          "origin_col_name": ""
        }]
    }
    @apiResponse  200 {
        "result": true,
        "msg": "修改成功",
        "data": "39ed1746-039b-5652-1b20-f3549d8988f8"
    }
    **/
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    model = DatasetModel(**kwargs)
    dataset_name = dataset_define_service.update_dataset(model.id, model)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_dataset',
            'id': kwargs.get('id'),
            'content': '修改数据集 [ {dataset_name} ] '.format(dataset_name=dataset_name),
        },
    )

    return True, '修改成功', model.id


@api.admin_route.post('/update_cache_flow')
@kwargs_base64decode()
def update_cache_flow(request, **kwargs):
    """修改数据集缓存调度"""
    use_cache = kwargs.get('use_cache', None)
    if use_cache is None:
        raise UserError(message='use_cache参数必传')

    model = DatasetModel(**kwargs)
    dataset_name = dataset_define_service.update_cache_flow(model.id, model)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_dataset',
            'id': kwargs.get('id'),
            'content': '修改数据集 [ {dataset_name} ] '.format(dataset_name=dataset_name),
        },
    )
    return True, '修改成功', model.id


@api.admin_route.post('/update_flow')
@kwargs_aes_decode()
def update_flow(request, **kwargs):
    """
    修改数据集调度
    :param dict kwargs:
    :return tuple:
    """
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    model = DatasetModel(**kwargs)
    dataset_name = dataset_define_service.update_flow(model.id, model)

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_dataset',
            'id': kwargs.get('id'),
            'content': '修改数据集 [ {dataset_name} ] '.format(dataset_name=dataset_name),
        },
    )

    return True, '修改成功', model.id


@api.admin_route.post('/delete')
def delete_dataset(request, **kwargs):
    """
    删除数据集
    :param kwargs:
    :return:
    """

    result, dataset_name = dataset_define_service.delete_dataset(kwargs.get('id'))

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_dataset',
            'id': kwargs.get('id'),
            'content': '删除数据集 [ {dataset_name} ]'.format(dataset_name=dataset_name),
        },
    )

    return True, '删除成功', result


@api.admin_route.get('/tree')
def get_dataset_tree(request, **kwargs):
    """
    获取数据集树
    :return:
    """
    token = request.cookies.get('token')

    return (
        True,
        '操作成功',
        dataset_service.get_dataset_tree_for_dataset_ids(
            kwargs.get('parent_id'),
            kwargs.get('exclude_types'),
            start_time=kwargs.get('begin_time', None),
            end_time=kwargs.get('end_time', None),
            filter_vars=kwargs.get('filter_vars', False),
            token=token
        ),
    )


@api.admin_route.get('/tree_with_filterable')
def get_dataset_tree_with_filterable(**kwargs):
    """
    获取带是否可过滤字段的数据集树
    :return:
    """

    return (
        True,
        '操作成功',
        dataset_service.get_dataset_tree(
            kwargs.get('parent_id'),
            kwargs.get('exclude_types'),
            start_time=kwargs.get('begin_time', None),
            end_time=kwargs.get('end_time', None),
            with_filterable=True,
        ),
    )


@api.admin_route.post('/tree_adapt_hd')
def get_dataset_tree_adapt_hd(**kwargs):
    """
    获取带是否可过滤字段的数据集树
    action_name=Dataset/SelectDataset&datasetScope=2
    :return:
    """
    model = DatasetTreeAdaptHDModel(**kwargs)
    return (
        True,
        '操作成功',
        dataset_service.get_dataset_tree_adapt_hd(model),
    )


@api.admin_route.post('/move')
def move_dataset(**kwargs):
    """
    移动数据集
    :return:
    """
    return True, '操作成功', dataset_define_service.move_dataset(kwargs.get('id'), kwargs.get('target_id'))


@api.admin_route.post('/batch_move')
def batch_move_dataset(**kwargs):
    """
    移动数据集
    :return:
    """
    return True, '操作成功', dataset_define_service.batch_move_dataset(kwargs.get('ids'), kwargs.get('target_id'))


@api.admin_route.get('/get')
def get_dataset(**kwargs):
    """
    获取单个数据集
    :return:
    """
    return True, '', dataset_service.get_dataset(kwargs.get('id'))


@api.admin_route.get('/get_modify_time')
def get_modify_time(**kwargs):
    """
    获取单个数据集更新时间
    :return:
    """
    dataset_id = kwargs.get('id')
    if not dataset_id:
        return False, 'Bad id'
    return True, '', dataset_service.get_dataset_data_time(dataset_id)


@api.admin_route.post('/rename')
def rename_dataset(**kwargs):
    """
    /**
    @apiVersion 1.0.1
    @api {post} /api/dataset/rename 数据集重命名
    @apiGroup  dataset
    @apiBodyParam {
    "id{数据集id}": "01a5252d-d76d-47ff-ab62-a0da8f34949c",
    "name{新数据集名称}": "new_dataset_name",
    "parent_id{上传目录id}":"39ed1356-4469-b380-86ab-fc2526296567",
    "type{数据集类型}": "EXCEL"
    }
    @apiResponse  200 {
        "result": true,
        "msg": "操作成功",
        "data": 1
    }
    **/
    """
    return (
        True,
        '操作成功',
        dataset_define_service.rename_dataset(
            kwargs.get('id'),
            kwargs.get('name'),
            kwargs.get('type'),
            kwargs.get('parent_id') if 'parent_id' in kwargs else '',
        ),
    )


@api.admin_route.get('/get_result_data')
def get_result_data(request, **kwargs):
    """
    获取数据集的结果数据
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    if hasattr(g, "code") and g.code:
        # 获取存储类型
        setattr(g, "storage", get_storage_type(g.code))
    try:
        data = dataset_service.get_dataset_result_data(kwargs.get('id'))
    except (errors.TableNotFoundError, errors.ColumnNotFoundError, DatasetQueryError) as ue:
        raise UserError(code=404, message=str(ue)) from ue
    return True, '获取成功', data


@api.admin_route.get('/get_result_count')
def get_result_count(request, **kwargs):
    """
    获取数据集的结果数据
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    return True, '获取成功', dataset_service.get_dataset_result_count(kwargs.get('id'))


@api.admin_route.post('/run')
def run_flow(**kwargs):
    """
    刷新数据集
    :param kwargs:
    :return:
    """
    # 手动更新需要添加操作日志
    return (
        True,
        '流程已进入队列，等待运行',
        dataset_service.run_dataset(kwargs.get('id'), kwargs.get('is_cache_flow'), kwargs.get('run_all_cache_flow')),
    )


@api.admin_route.post('/run_get_data')
@kwargs_aes_decode()
def run_get_data(request, **kwargs):
    """
    运行数据集， 产生数据集数据和数据集字段
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    if hasattr(g, "code") and g.code:
        # 获取存储类型
        setattr(g, "storage", get_storage_type(g.code))
    # kwargs = {'edit_mode': 'relation', 'type': 'UNION', 'content': '{"dataset_id":"3a0d524b-48c0-e630-4ccc-667db0e5a27c","sql":"","running_way":"local"}', 'relation_content': '{"nodeDataArray":[{"id":"b085ffa54aa9fa55f590e1a146d8f0c5","name":"测试数据集1111","comment":null,"alias_name":"测试数据集1111","fields":[{"name":"DXWB_13092513382","type":"text","comment":"单行文本","col_type":"字符串","alias_name":"单行文本"},{"name":"DXWB_13479701547","type":"varchar(500)","comment":"多行文本","col_type":"字符串","alias_name":"多行文本"},{"name":"SZ_8480735345","type":"decimal(18,4)","comment":"数值","col_type":"数值","alias_name":"数值"},{"name":"BATCHID_5055188467","type":"char(36)","comment":"batch_id","col_type":"字符串","alias_name":"batch_id"},{"name":"BATCHNAME_5544873671","type":"varchar(256)","comment":"batch_name","col_type":"字符串","alias_name":"batch_name"},{"name":"BATCHUPDATETIME_7483822519","type":"varchar(30)","comment":"batch_update_time","col_type":"字符串","alias_name":"batch_update_time"},{"name":"CURFILLUSERID_7205884255","type":"char(36)","comment":"cur_fill_user_id","col_type":"字符串","alias_name":"cur_fill_user_id"},{"name":"CURFILLUSERNAME_7810388531","type":"varchar(512)","comment":"cur_fill_user_name","col_type":"字符串","alias_name":"cur_fill_user_name"},{"name":"STATUS_4607970665","type":"char(36)","comment":"status","col_type":"字符串","alias_name":"status"},{"name":"REMARK_4601154887","type":"text","comment":"remark","col_type":"字符串","alias_name":"remark"},{"name":"REVIEWER_5072490030","type":"text","comment":"reviewer","col_type":"字符串","alias_name":"reviewer"},{"name":"REVIEWTIME_5826285413","type":"varchar(30)","comment":"review_time","col_type":"字符串","alias_name":"review_time"}]}],"linkDataArray":[]}', 'filter_content': [{'table_id': '', 'table_name': '', 'col_name': '', 'operator': '=', 'logical_relation': 'and', 'col_value': '11', 'col_type': '字符串','filter_type':'json','json_value':'{"and":[ {"table_id": "b085ffa54aa9fa55f590e1a146d8f0c5", "table_name": "{测试数据集1111}", "col_name": "DXWB_13092513382", "operator": "=", "logical_relation": "and", "col_value": "11", "col_type": "字符串","col_value_type":"固定值"}, {"or":[{"table_id": "b085ffa54aa9fa55f590e1a146d8f0c5", "table_name": "{测试数据集1111}", "col_name": "DXWB_13092513382", "operator": "!=", "logical_relation": "and", "col_value": "22", "col_type": "字符串","col_value_type":"固定值"},{"table_id": "b085ffa54aa9fa55f590e1a146d8f0c5", "table_name": "{测试数据集1111}", "col_name": "DXWB_13092513382", "operator": "!=", "logical_relation": "and", "col_value": "${e94c05d4-4893-11ee-b7aa-2b78f7ce9e22}", "col_type": "字符串","col_value_type":"变量"}]}]}'}],'var_content': [{'id': 'e94c05d4-4893-11ee-b7aa-2b78f7ce9e22','name': 'aa', 'description': '', 'var_type': 1, 'value_type': 2, 'dataset_id': '3a0d430d-7b7a-5a5f-f2c3-968fd982143a', 'default_value': '"0"', 'accurate_type': 1,'keyword_list': [],		'default_value_type': 2	}], 'disable_procedure': 1}
    return True, '操作成功', dataset_service.run_get_data(DatasetModel(**kwargs))


@api.admin_route.post('/run_get_data_count')
@kwargs_aes_decode()
def run_get_data_count(request, **kwargs):
    """
    运行数据集，获取总条数
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    return True, '操作成功', dataset_service.run_get_data_count(DatasetModel(**kwargs))


@api.admin_route.post('/run_get_field_values')
@kwargs_aes_decode()
def run_get_field_values(request, **kwargs):
    """
    运行数据集，获取总条数
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    return (
        True,
        '操作成功',
        dataset_service.run_get_field_values(
            DatasetModel(**kwargs), kwargs.get("field_name"), kwargs.get("origin_field_name")
        ),
    )


@api.admin_route.get('/get_label_data')
def get_label_data(**kwargs):
    """
    从标签指标表里产生数据集数据和数据集字段
    :return:
    """
    return True, '操作成功', dataset_service.get_label_data(kwargs.get('label_id'))


@api.admin_route.get('/dataset_field/get')
def get_dataset_field(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {get} /api/dataset/dataset_field/get 获取数据集字段信息及变量信息
     @apiParam query {string}  dataset_id 数据集ID
     @apiParam query {number}  is_not_category  是否需要分类
     @apiParam query {number}  include_dataset_vars  是否包含数据集变量
     @apiGroup  dataset
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": {
            "group_type{度量|维度|变量}": [
                {
                    "id": "变量ID",
                    "name": "变量名称",
                    "description": "变量描述",
                    "var_type": "数据类型, 1:文本 2:日期 3:数值",
                    "value_type": "值类型 1: 列表 2: 任意值 3: 区间",
                    "default_value": "默认值 a,b",
                    "dataset_id": "所属数据集ID",
                    "include_vars": ["变量1", "变量2"]
                }
            ]
         }
    }
     */
    """
    # 高级字段保存时，在带有单引号的输入框值里面强行加入了双斜杠，返回给前端时需要去除双斜杠
    dataset_fields = dataset_field_service.get_dataset_field(
        kwargs.get('dataset_id'), kwargs.get('is_not_category'), kwargs.get("include_dataset_vars")
    )
    if not dataset_fields:
        dataset_fields = []
    if not kwargs.get('is_not_category'):
        for k, v in dataset_fields.items():
            dataset_fields[k] = advanced_field_service.del_diagonal(v)
    else:
        dataset_fields = advanced_field_service.del_diagonal(dataset_fields)

    return True, '获取成功', dataset_fields


@api.admin_route.post('/dataset_field/add')
def add_dataset_field(**kwargs):
    """
    新增数据集字段
    :return:
    """
    return True, '添加成功', dataset_field_service.add_dataset_field(kwargs.get('fields'))


@api.admin_route.post('/dataset_field/update')
def update_name_type(**kwargs):
    """
    编辑数据集字段
    :return:
    """
    model = DatasetFieldModel(**kwargs)
    return True, '添加成功', dataset_field_service.update_dataset_field(model)


@api.admin_route.post('/dataset_field/can_update')
def can_update_dataset_field(**kwargs):
    """
    编辑数据集字段
    :return:
    """
    model = DatasetFieldModel(**kwargs)
    return True, '添加成功', dataset_field_service.can_update_dataset_field(model)


@api.admin_route.post('/dataset_field/get_field_values')
def get_field_values(**kwargs):
    """
    获取数据集字段group后的值
    :return:
    """
    return (
        True,
        '操作成功',
        dataset_service.get_field_values(
            kwargs.get("dataset_id"), kwargs.get("dataset_field_id"), keyword=kwargs.get("keyword")
        ),
    )


@api.admin_route.get('/inspection/get')
def get_inspection_title(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/dataset/inspection/get 获取数据集巡检结果
    @apiGroup  dataset
    @apiParam query {string}  inspection_id 数据集巡检ID
    @apiResponse  200 {
        "result": true,
        "msg": "ok",
        "data":
              {
                "data":[
                    {
                        "field_group":"维度",
                        "unusual_count":1,
                        "inspection_rule":"a=1",
                        "data_type":"字符串",
                        "col_name":"a"
                    }
                ],
                "title":{
                    "dataset_id":"39e6042b-b804-e44e-dc51-e76a3440fc65",
                    "created_on":"2018-04-24T11:06:37",
                    "table_name":"inspection_9ac1cbc1371df2c8",
                    "dataset_total":1,
                    "created_by":"dmp-flow",
                    "id":"39e607cd-53e1-66b1-f2f2-b51bd004b2ea",
                    "content":"",
                    "unusual_total":1
                }
            }
    }
    **/
    """
    return True, '获取成功', dataset_service.get_inspection_list(kwargs.get('inspection_id'))


@api.admin_route.get('/version/inspection')
def get_version_inspection(inspection_id):
    """

    :param inspection_id:
    :return:
    """
    return True, '获取成功', dataset_service.get_version_inspection_result(inspection_id)


@api.admin_route.get('/get_dataset_index')
def get_dataset_index(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {get} /api/dataset/get_dataset_index 获取数据集数据表索引信息
     @apiGroup  openapi
     @apiParam query {string}  dataset_id 数据集ID
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": [{
                     "id": "id",
                     "dataset_id{数据集id}": "01a5252d-d76d-47ff-ab62-a0da8f34949c",
                     "index_name":"index_name_col",
                     "column_list": ["id"]
                     },
                    {
                     "id": "id2",
                     "dataset_id{数据集id}": "01a5252d-d76d-47ff-ab62-a0da8f34949c",
                     "index_name":"index_name_col2",
                     "column_list": ["name1", "name2"]
                    }
        ]
    }
     */
    """
    return True, None, dataset_service.get_dataset_index(kwargs.get("dataset_id"))


@api.admin_route.post('/update_dataset_index')
def update_dataset_index(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/update_dataset_index 修改数据集数据表索引信息
     @apiGroup  openapi
     @apiBodyParam [{
        "dataset_id{数据集id}": "01a5252d-d76d-47ff-ab62-a0da8f34949c",
        "id": "id",
        "index_name":"index_name_col",
        "column_list": ["id"]
     }]
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "01a5252d-d76d-47ff-ab62-a0da8f34949c"
    }
     */
    """
    index_datas = []
    index_datas.append(DatasetIndexDataModel(**kwargs))
    return True, None, dataset_service.update_dataset_indexes(index_datas)


@api.admin_route.post('/update_dataset_excel_index')
def update_dataset_excel_index(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/update_dataset_index 修改数据集数据表索引信息
     @apiGroup  openapi
     @apiBodyParam {
        "dataset_id{数据集id}": "01a5252d-d76d-47ff-ab62-a0da8f34949c",
        "id": "id",
        "index_name":"index_name_col",
        "column_list": ["id"]
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "01a5252d-d76d-47ff-ab62-a0da8f34949c"
    }
     */
    """
    index_datas = []
    if not kwargs:
        return
    dataset_id = kwargs.get("dataset_id")
    if not dataset_id:
        raise UserError(message=u'请传入参数dataset_id')
    for kwarg in kwargs.get("data"):
        index_datas.append(DatasetIndexDataModel(**kwarg))
    return True, None, dataset_service.update_dataset_indexes(index_datas, True, dataset_id)


@api.admin_route.post('/delete_dataset_index')
def delete_dataset_index(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/delete_dataset_index 删除数据集数据表索引信息
     @apiGroup  openapi
     @apiBodyParam {
        "id": "id"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "",
         "data": "01a5252d-d76d-47ff-ab62-a0da8f34949c"
    }
     */
    """
    return True, None, dataset_service.delete_dataset_index(kwargs.get("id"))


@api.admin_route.get('/clear_dataset_cache')
def clear_dataset_cache(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/clear_dataset_cache 清空数据集缓存
     @apiGroup  dataset
     @apiParam query {string}  dataset_id 数据集ID
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
    }
     */
    """
    dataset_id = kwargs.get("dataset_id")
    dataset_meta_cache.del_dataset_cache(dataset_id)
    dataset_meta_cache.del_dataset_field_cache(dataset_id)
    return True, "ok"


@api.admin_route.get('/get_group_field')
def get_group_field(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {get} /api/dataset/get_group_field 获取单个分组字段信息
     @apiGroup  dataset
     @apiParam query {string}  dataset_field_id 数据集分组字段id
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
    }
     */
    """
    return True, None, dataset_group_field.get_group_field(kwargs.get("dataset_field_id"))


@api.admin_route.post('/update_group_field')
def update_group_field(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/update_group_field 新增、修改单个分组字段信息
     @apiGroup  dataset
     @apiBodyParam {
         "id": "id",
         "dataset_id":"dataset_id",
         "origin_dataset_field_id": "dddd",
         "origin_col_name": "dddd",
         "alias_name": "aaa",
         "origin_data_type": "字符串",
         "group_type": "conditions",
         "expression": []
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": "dataset_field_id"
    }
     */
    """
    model = DatasetGroupFieldModel(**kwargs)
    return True, None, dataset_group_field.update_group_field(model)


@api.admin_route.post('/delete_group_field')
def delete_group_field(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {get} /api/dataset/delete_group_field 删除数据集分组字段
     @apiGroup  dataset
     @apiParam query {string}  dataset_field_id 数据集分组字段id
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
    }
     */
    """
    return True, None, dataset_group_field.delete_group_field(kwargs.get("dataset_field_id"))


@api.admin_route.get('/filter/field_info')
def get_dataset_field_values(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {get} /api/dataset//filter/field_info  获取字段所有数据
     @apiGroup  dataset
     @apiParam query {string}  dataset_id 数据集id
     @apiParam query {string}  dataset_field_id 数据集字段id
     @apiResponse  200 {
         "result": true,
         "msg": "ok"
    }
     */
    """
    if not kwargs.get("dataset_id"):
        raise UserError(message=u'dataset_id必填')
    if not kwargs.get("dataset_field_id"):
        raise UserError(message=u'dataset_field_id必填')
    return (
        True,
        None,
        dataset_field_service.get_dataset_field_values(kwargs.get("dataset_id"), kwargs.get("dataset_field_id")),
    )


@api.admin_route.post('/dataset_var/edit')
def edit_dataset_var(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/dataset_var/edit 新增||修改数据集变量
     @apiGroup  dataset
     @apiBodyParam {
     "id": "id",
     "name":"名称",
     "description": "描述",
     "var_type": "1: 文本，2：日期，3：数值",
     "value_type": "1：列表，2：任意值，3：区间",
     "dataset_id": "数据集ID"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": {
            "id": "id",
             "name":"名称",
             "description": "描述",
             "var_type": "1: 文本，2：日期，3：数值",
             "value_type": "1：列表，2：任意值，3：区间",
             "default_value": "a,b",
             "dataset_id": "数据集ID"
         }
    }
     */
    """
    _, data = dataset_var_service.edit_dataset_var(kwargs)
    return True, None, data


@api.admin_route.post('/dataset_var/batch_get_default_values')
def batch_get_dataset_var_default_values(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/dataset_var/batch_get_default_values 批量获取变量默认值
     @apiGroup  dataset
     @apiBodyParam {
     "dataset_vars": ["aaa", "bbb"]
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": {
            "id":"value"
         }
    }
     */
    """
    result = dataset_var_service.batch_get_dataset_var_default_values(
        kwargs.get("dataset_vars"), kwargs.get("dashboard_id", "")
    )
    result = dict((key, json.dumps(val)) for key, val in result.items())
    return True, None, result


@api.admin_route.post('/dataset_var/delete')
def delete_dataset_var(**kwargs):
    """
     /*
     @apiVersion 1.0.0
     @api {post} /api/dataset/dataset_var/delete 删除变量
     @apiGroup  dataset
     @apiBodyParam {
     "id": "id"
     }
     @apiResponse  200 {
         "result": true,
         "msg": "ok",
         "data": true
    }
     */
    """
    var_id = kwargs.get("id")
    if not var_id:
        raise UserError(message="请指定需要删除的变量！")
    result = dataset_var_service.delete_dataset_var(var_id)
    return (result, None, result)


@api.admin_route.get('/download/message')
def download_message(**kwargs):
    """
     /*
     @apiVersion 1.0.3
     @api {get} /api/dataset/download/message 数据集下载进度消息
     @apiGroup  dataset
     @apiParam query {string}  download_id 数据集数据下载id
     @apiParam query {string}  code 数据集字段id
     @apiResponse  200 {
        "data":{
            "status":2,
            "download_url":"https://dmp-test.oss-cn-shenzhen.aliyuncs.com/export/order_分页表-1_CHhS.zip"
        },
        "result":true,
        "msg":"单图数据下载文件已生成，点击下载"
    }
     */
    """
    download_model = ChartDownloadModel(**kwargs)
    if not download_model.download_id:
        raise UserError(message="download_id不能为空")
    msg, result_data = download_service.get_download_task_data(download_model)
    return True, msg, result_data


@api.admin_route.get('/subject/preview')
def get_subject_tables(subject_id: str):
    """
     /*
     @apiVersion 1.0.1
     @api {get} /api/dataset/subject/preview HighData主题数据集数据表列表预览数据
     @apiGroup  dataset
     @apiParam query {string}  subject_id 主题ID
     @apiResponse  200 {
        "data": [
            {
                "subject_table_id{主题数据表ID}": "",
                "table_name{数据表table_name}": "数据表table_name",
                "description{数据表名称}": "数据表名称"
            }
        ],
        "result": true,
        "msg": "success"
    }
     */
    """
    return True, 'success', dataset_subject_service.get_subject_tables_list(subject_id)


@api.admin_route.get('/subject/table/preview')
def get_subject_table_preview_data(subject_table_id: str):
    """
     /*
     @apiVersion 1.0.0
     @api {get} /api/dataset/subject/table/preview HighData主题数据集数据表预览数据
     @apiGroup  dataset
     @apiParam query {string}  subject_table_id 主题数据表ID
     @apiResponse  200 {
        "data":{
            "count": 5,
            "create_table_sql": "create table if not exists dataset_tmp_5cfdb7678a1ae (`ID_3177777697` int(11) ,
            `NAME_3539274485` varchar(20) )",
            "data": [
                {
                    "ID_3177777697": 1,
                    "NAME_3539274485": "\u7535\u5b50"
                }
            ],
            "description": "\u4e13\u4e1a",
            "field": {
                "\u7ef4\u5ea6": [
                    {
                        "origin_col_name": "name",
                        "origin_table_id": "",
                        "origin_table_comment": "",
                        "origin_table_name": "",
                        "origin_table_alias_name": "",
                        "col_name": "NAME_3539274485",
                        "origin_field_type": "",
                        "alias_name": "\u540d\u79f0",
                        "visible": 1,
                        "format": "",
                        "field_group": "\u7ef4\u5ea6",
                        "data_type": "\u5b57\u7b26\u4e32"
                    }
                ],
                "\u5ea6\u91cf": [
                    {
                        "origin_col_name": "id",
                        "origin_table_id": "",
                        "origin_table_comment": "",
                        "origin_table_name": "",
                        "origin_table_alias_name": "",
                        "col_name": "ID_3177777697",
                        "origin_field_type": "",
                        "alias_name": "ID",
                        "visible": 1,
                        "format": "",
                        "field_group": "\u5ea6\u91cf",
                        "data_type": "\u6570\u503c"
                    }
                ]
            },
            "head": [
                {
                    "origin_col_name": "id",
                    "origin_table_id": "",
                    "origin_table_comment": "",
                    "origin_table_name": "",
                    "origin_table_alias_name": "",
                    "col_name": "ID_3177777697",
                    "origin_field_type": "",
                    "alias_name": "ID",
                    "visible": 1,
                    "format": "",
                    "field_group": "\u5ea6\u91cf",
                    "data_type": "\u6570\u503c"
                }
            ],
            "id": "39ee5141-44b4-e032-8454-49e4f6a35bef",
            "origin_table_name": "subjects",
            "tmp_table_name": "dataset_tmp_5cfdb7678a1ae"
        },
        "result":true,
        "msg":"success"
    }
     */
    """
    return True, 'success', dataset_subject_service.get_subject_table_preview_data(subject_table_id)


@api.admin_route.get('/var/check_used')
def check_dataset_var_is_used(dataset_var_id: str):
    """
     /*
     @apiVersion 1.0.1
     @api {get} /api/dataset/var/check_used 检查数据集变量是否被引用
     @apiGroup  dataset
     @apiParam query {string}  dataset_var_id 数据集ID
     @apiResponse  200 {
        "data": true,
        "result": true,
        "msg": "success"
    }
     */
    """
    return True, 'success', dataset_var_service.check_dataset_var_is_used(dataset_var_id)


@api.admin_route.post('/sql/transfer')
@kwargs_aes_decode()
def get_dataset_transferred_sql(request, **kwargs):
    """
     /*
     @apiVersion 1.0.1
     @api {post} /api/dataset/sql/transfer 获取变量转换后的数据集sql语句
     @apiGroup  dataset
     @apiResponse  200 {
        "data": "sql",
        "result": true,
        "msg": "success"
    }
     */
    """
    cookie = request.cookies
    g.cookie = cookie
    return True, 'success', dataset_service.get_dataset_transferred_sql(DatasetModel(**kwargs))


@api.admin_route.get('/subject/inspections')
def get_dataset_subject_inspections_list(**kwargs):
    """
    /*
    @apiVersion 1.0.2
    @api {get} /api/dataset/subject/inspections 主题数据集巡检列表接口
    @apiGroup  dataset
    @apiParam query {string}  [status] 巡检结果状态，可选值:'正常','巡检中','警告','错误'
    @apiParam query {string}  [begin_date] 开始时间
    @apiParam query {string}  [end_date] 结束时间
    @apiParam query {string}  [page] 页码
    @apiParam query {string}  [page_size] 单页记录数
    @apiParam query {string} [data_source_type] 数据源类型，可选值：subject、api
    @apiResponse  200 {
        "result": true,
        "msg": "success",
        "data": {
            "items": [
                {
                    "subject_id{主题ID}": "0a8c2d3e-827c-11e9-80c6-f0189806aefb",
                    "subject_name{主题名称}": "谭志颖-主题",
                    "start_time{巡检开始时间}": "",
                    "end_time{巡检结束时间}": "",
                    "status{巡检结果状态}": "正常",
                    "schedule{巡检调度参数}": "",
                    "schedule_status{调度是否开启,可选值：启用、禁用}": "禁用",
                    "data_source_type{数据源类型，可选值：subject、api}": "subject"
                }
            ],
            "total": 1
        }
    }
    */
    """
    return (
        True,
        'success',
        dataset_subject_service.get_dataset_subject_inspections_list(DatasetInspectionQueryModel(**kwargs)),
    )


@api.admin_route.get('/subject/inspections/{subject_id}')
def get_dataset_subject_inspection_detail(subject_id):
    """
    /*
    @apiVersion 1.0.2
    @api {get} /api/dataset/subject/inspections/{subject_id} 主题数据集巡检详情接口
    @apiGroup  dataset
    @apiParam path {string}  subject_id 主题ID
    @apiResponse  200 {
        "result": true,
        "msg": "success",
        "data": {
            "subject_id": "0a8c2d3e-827c-11e9-80c6-f0189806aefb",
            "subject_name": "谭志颖-主题",
            "start_time": "",
            "end_time{巡检完成时间}": "",
            "status": "正常",
            "schedule": "",
            "schedule_status": "禁用",
            "inspect_result{巡检结果详情}": "",
            "inspect_error{巡检错误数}": 0,
            "inspect_warning{巡检警告提醒数}": 0,
            "data_source_type{数据类型}": "api"
        }
    }
    */
    """
    return True, 'success', dataset_subject_service.get_dataset_subject_inspection_detail(subject_id)


@api.admin_route.patch('/subject/inspections')
def update_dataset_subject_inspection(**kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {patch} /api/dataset/subject/inspections 修改主题数据集巡检调度配置
    @apiGroup  dataset
    @apiBodyParam {
        "subject_id{主题ID}": "0a8c2d3e-827c-11e9-80c6-f0189806aefb",
        "subject_name{主题名称}": "主题名称",
        "schedule{调度信息，如：0 0 4 1 * ? *}": "",
        "schedule_status{是否开始调度，值为:启用/禁用}": "禁用"
    }
    @apiResponse  200 {
        "result": true,
        "msg": "success",
        "data": true
    }
    */
    """
    return True, 'success', dataset_subject_service.update_dataset_inspection(kwargs)


@api.admin_route.post('/subject/inspections/run')
def run_dataset_subject_inspection(**kwargs):
    """
    /*
    @apiVersion 1.0.1
    @api {post} /api/dataset/subject/inspections/run 立即巡检主题数据集
    @apiGroup  dataset
    @apiBodyParam {
        "subject_id{主题ID}": "0a8c2d3e-827c-11e9-80c6-f0189806aefb"
    }
    @apiResponse  200 {
        "result": true,
        "msg": "success",
        "data": true
    }
    */
    """
    subject_id = kwargs.get('subject_id')
    if not subject_id:
        raise UserError(message='缺少subject_id')
    success, send_data = dataset_subject_service.run_dataset_inspection(subject_id)
    word = '运行成功' if success else '运行失败'
    return success, word, send_data


@api.admin_route.get('/name/map')
def get_dataset_map(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {get} /api/dataset/name/map 获取数据集id, 名字map
    @apiGroup  dataset
    @apiBodyParam {
        "dataset_ids": []
    }
    @apiResponse  200 {
        "result": true,
        "msg": "success",
        "data": [
            {'id': '39e335-1574-edcf-61f1-0275819d', 'name': '1004测试api'},
            {'id': '39e35ace-3220-b1d4-5805-4075ce1b30c3', 'name': '营销-主数据按区域汇总'}
        ]
    }
    */
    """
    return True, "success", dataset_service.get_dataset_map(**kwargs)


@api.admin_route.post('/add_dataset_permission', validate=PermissionValidator('data-permission.edit'))
def add_dataset_permission(**kwargs):
    # 新增，修改数据集权限
    dataset_permission = DatasetPermissionModel(**kwargs)
    dataset_permission.validate()
    dataset_permission_id = dataset_permission_service.add_dataset_permission(dataset_permission)
    return True, 'ok', {'id': dataset_permission_id}


@api.admin_route.post('/delete_dataset_permission', validate=PermissionValidator('data-permission.edit'))
def delete_dataset_permission(request, **kwargs):
    """
    删除数据集权限
    :param kwargs:
    :return:
    """
    dataset_permission = dataset_permission_service.get_dataset_permission_by_id(kwargs.get("id"))
    result = dataset_permission_service.delete_dataset_permission(kwargs.get('id'))
    if result:
        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'delete_user_source',
                'id': kwargs.get('id'),
                'content': '删除数据集权限 [ {name} ] '.format(name=dataset_permission.get("name")),
            },
        )
    return True, '删除成功', result


@api.admin_route.get('/get_dataset_permission_list', validate=PermissionValidator('data-permission.view'))
def get_dataset_permission_list():
    return True, 'success', dataset_permission_service.get_dataset_permission_list()


@api.admin_route.post('/save_field_folder')
def save_field_folder(**kwargs):
    """
        /*
        @apiVersion 1.0.0
        @api {get} /api/dataset/save_field_folder
        @apiGroup  dataset
        @apiBodyParam {
             "id": "分组ID",
             "dataset_id":"数据集ID",
             "group_name": "分组名称",
             "parent_id": "父级ID",
             "group_type": "分组类型（1：维度；2：度量）",
             "sort": "排序字段",
        }
        @apiResponse  200 {
             "result": true,
             "msg": "保存成功",
             "data": {
                 "id": "分组ID",
                 "dataset_id":"数据集ID",
                 "group_name": "分组名称",
                 "parent_id": "父级ID",
                 "group_type": "分组类型（1：维度；2：度量）",
                 "sort": "排序字段",
             }
        }
        */
    """
    model = DatasetFieldGroupModel(**kwargs)
    try:
        data = dataset_field_group_service.save_dataset_field_group(model)
        return True, '保存成功', data
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else '', {}


@api.admin_route.get('/delete_field_folder')
def delete_field_folder(**kwargs):
    """
        /*
        @apiVersion 1.0.0
        @api {get} /api/dataset/delete_field_folder 删除字段目录
        @apiGroup  dataset
        @apiBodyParam id
        @apiResponse  200 {
             "result": true,
             "msg": "删除成功",
             "data": {}
        }
        */
    """
    try:
        dataset_field_group_service.delete_field_group(kwargs.get('id', None))
        return True, '删除成功', {}
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else e.args, {}


@api.admin_route.post('/change_folder_sort')
def change_folder_sort(**kwargs):
    """
        /*
        @apiVersion 1.0.0
        @api {get} /api/dataset/change_folder_sort 修改目录顺序
        @apiGroup  dataset
        @apiBodyParam {
            "39fd91ed-bd9e-2695-801d-a04355b0c900": 1,
            "39fd91ee-4603-d99a-df25-8fcedc994ba5": 2
        }
        @apiResponse  200 {
             "result": true,
             "msg": "保存成功",
             "data": {}
        }
        */
    """
    try:
        dataset_field_group_service.change_group_sort(kwargs)
        return True, '保存成功', {}
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else e.args, {}


@api.admin_route.post('/save_field_folder_relation')
def save_field_folder_relation(**kwargs):
    """
        /*
        @apiVersion 1.0.0
        @api {get} /api/dataset/save_field_folder_relation 添加分组绑定字段
        @apiGroup  dataset
        @apiBodyParam {
            "relation": [
                {
                    "group_id": "39fd91ee-4603-d99a-df25-8fcedc994ba5",
                    "field_id": "39fc2883-ac92-b8b0-05e0-5da2823b1ec0",
                    "sort": 1
                },
                {
                    "group_id": "39fd91ee-4603-d99a-df25-8fcedc994ba5",
                    "field_id": "39fc2883-ac92-b8bc-fc1d-1edfd3fd0535",
                    "sort": 2
                }
            ]
        }
        @apiResponse  200 {
             "result": true,
             "msg": "保存成功",
             "data": {}
        }
        */
    """
    try:
        model_list = []
        data = kwargs.get('relation', [])
        dataset_id = kwargs.get('dataset_id', '')
        if not dataset_id:
            return False, '数据集ID不能为空', {}
        for relation in data:
            model = DatasetFieldGroupRelationModel(**relation)
            model.dataset_id = dataset_id
            model_list.append(model)
        # 删除数据集对应下所有字段的绑定关系
        dataset_field_group_service.delete_field_group_by_dataset_id(dataset_id)
        data = dataset_field_group_service.save_field_group_relation(model_list)
        return True, '添加成功', data
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else e.args, {}


@api.admin_route.post('/delete_field_folder_relation')
def delete_field_folder_relation(**kwargs):
    """
        /*
        @apiVersion 1.0.0
        @api {get} /api/dataset/delete_field_folder_relation 移除绑定字段
        @apiGroup  dataset
        @apiBodyParam {
            "field_id": [
                "39fd9608-2868-6341-2677-90d0b2c0fa9a",
                "39fd9608-31a8-7c5d-f3c0-eb402cd2edad"
            ]
        }
        @apiResponse  200 {
             "result": true,
             "msg": "保存成功",
             "data": {}
        }
        */
    """
    try:
        data = kwargs.get('field_id', [])
        dataset_field_group_service.delete_field_group_relation(data)
        return True, '删除成功', {}
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else e.args, {}


@api.admin_route.post('/change_folder_field_sort')
def change_folder_field_sort(**kwargs):
    """
        /*
        @apiVersion 1.0.0
        @api {get} /api/dataset/change_folder_field_sort 修改目录中字段顺序
        @apiGroup  dataset
        @apiBodyParam {
            "39fd9621-94b4-0a84-174d-596dedc0cc37": 10,
            "39fd9621-94b4-0a89-4c80-2b7c9ed07372": 11
        }
        @apiResponse  200 {
             "result": true,
             "msg": "保存成功",
             "data": {}
        }
        */
    """
    try:
        dataset_field_group_service.change_group_field_sort(kwargs)
        return True, '保存成功', {}
    except Exception as e:
        return False, e.message if hasattr(e, 'message') else e.args, {}


@api.admin_route.get('/get_folder_field_list')
def get_folder_field_list(dataset_id):
    field_group_list = dataset_field_group_service.get_field_group_by_dataset_id(dataset_id)
    return True, '', field_group_list


@api.admin_route.get('/operation/flow')
def update_operation_flow(**kwargs):
    # status: 1 开启   2 关闭
    status = int(kwargs.get("status", 0))
    just_exec = int(kwargs.get("just_exec", 0))
    if status not in [OperationFlowType.START.value, OperationFlowType.STOP.value]:
        raise UserError(message="不支持的参数类型，status=1 开启， status=2关闭")
    dataset_define_service.update_operation_flow(int(status), just_exec)
    return True, '', "ok"


@api.admin_route.get('/rundeck_job/run')
def rundeck_job_run(**kwargs):
    """
    立即执行rundeck job
    :param kwargs: job_id : code + flow_id
    :return:
    """
    from components.rundeck import CommonRunDeckScheduler
    job_id = kwargs.get("job_id")
    if not job_id:
        raise UserError(message="缺少参数 job_id")
    rundeck = CommonRunDeckScheduler()
    result = rundeck.run_job(job_id)
    return True, '', result


@api.admin_route.get('/field/export')
def export_dataset_field(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dataset/field/export 导出数据集字段信息
    @apiGroup  flow
    @apiBodyParam {
        "dataset_id{数据集id}": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok",
          "data": {
              "oss_url": ""
          }
        }
    */
    """
    oss_url = dataset_field_service.export_dataset_field(kwargs.get("dataset_id"))
    return True, '', {"oss_url": oss_url}


@api.admin_route.post('/field/import')
def import_dataset_field(**kwargs):
    """
    /*
    @apiVersion 1.0.0
    @api {post} /api/dataset/field/export 导入数据集字段信息
    @apiGroup  flow
    @apiBodyParam {
        "oss_url{文件对象存储地址}": "https://dmp-test.hangzhou-oss.com.cm/aa.xls",
        "dataset_id{数据集id}": "39e50659-8d8d-d1cd-30ab-2da709dd2012"
    }
    @apiResponse  200 {
          "result": true,
          "msg": "ok"
        }
    */
    """
    result_data = dataset_field_service.import_dataset_field(kwargs)
    return True, '', result_data


@api.route.get('/get_dataset_used_table')
def get_dataset_used_table(**kwargs):
    from dataset.services.dataset_used_table_service import get_used_table_list
    code = kwargs.get('code', None)
    table_name = kwargs.get('table_name', None)
    if code:
        g.code = code
    result = get_used_table_list(table_name)
    return True, '', result


@api.admin_route.get("/fill/get_dataset_field_columns")
def get_dataset_fields_of_fill(**kwargs):
    """
    获取字段类型， 兼容本地和云端，转换为mysql字段类型
    """
    dataset_id = kwargs.get("dataset_id")
    if not dataset_id:
        raise UserError(message="缺少参数dataset_id")
    return True, '', dataset_field_service.get_dataset_fields_of_filling(dataset_id)


@api.admin_route.get("/columns_of_schedule_dataset")
def get_columns_of_schedule_dataset(**kwargs):
    """
    获取字段类型
    :param kwargs:
    :return:
    """
    dataset_id = kwargs.get("dataset_id")
    if not dataset_id:
        raise UserError(message="缺少参数dataset_id")
    return True, '', dataset_field_service.get_columns_of_schedule_dataset(dataset_id)


@api.admin_route.get('/copy_dataset')
def copy_dataset(**kwargs):
    dataset_id = kwargs.get('dataset_id')
    if not dataset_id:
        return False, '数据集ID不能为空', ''
    try:
        return True, '复制成功', dataset_service.copy_dataset(dataset_id)
    except Exception as ex:
        return False, '复制失败：' + str(ex), dataset_id



@api.admin_route.get('/indicator_model/async')
def indicator_model_async(**kwargs):
    """
    立即指标同步
    :param kwargs:
    :return:
    """
    import app_celery
    from base.enums import PulsarSyncMode

    instance_id = indicator_service.create_flow_instance(mode=PulsarSyncMode.ALL.value, queue_name='parser')

    # 手动触发的任务优先处理， 丢到parser队列， 其他场景丢到celery-slow
    app_celery.sync_indicator_model.apply_async(
        kwargs={'code': g.code, 'instance_id': instance_id}, queue='parser'
    )
    # app_celery.sync_indicator_model(code=g.code, instance_id=instance_id)

    return True, "ok", ""


@api.admin_route.get('/indicator_model/task')
def indicator_model_async(**kwargs):
    """
    获取同步任务状态
    :param kwargs:
    :return:
    """
    # status: 0 已创建 1 运行中  2 成功  3  失败
    status, msg, end_time, remain_task_amount = indicator_service.get_pulsar_dataset_status()

    msg = {'status': status, 'msg': msg, 'end_time': end_time, 'remain_task_amount': remain_task_amount}
    return True, "ok", msg


@api.admin_route.get('/pulsar_cache/clear')
def pulsar_clear_cache(**kwargs):
    """
    清空数芯缓存
    :param kwargs:
    :return:
    """
    from dataset.services.pulsar_sync_v2_service import PulsarSync

    try:
        PulsarSync().clear_cache()
        msg = "成功"
    except Exception as e:
        msg = f"缓存清理失败: {e}"
    return True, 'ok', msg


@api.admin_route.get('/clean/data_table')
def clean_data_table(**kwargs):
    """
    清理数据集垃圾表
    """
    import app_celery
    project_code = kwargs.get('project_code')
    if not project_code:
        return False, '租户code不能为空', {}
    app_celery.clean_dataset_history_table.apply_async(kwargs=kwargs)
    return True, "ok", '已注册celery任务,具体信息请查看dmp_celery日志'


@api.admin_route.get('/redirect/indicator_asset')
def redirect_indicator_asset(**kwargs):
    indicator_code = kwargs.get('indicator_code')
    indicator_service.redirect_indicator_asset(indicator_code)


@api.admin_route.get('/indicator_model_dataset/tree')
def indicator_model_dataset_tree(request, **kwargs):
    """
    独立获取数见数据集树，指标树，数芯明细树
    :return:
    """
    token = request.cookies.get('token')

    return (
        True,
        '操作成功',
        dataset_service.get_indicator_model_dataset_tree_for_dataset_ids(
            kwargs.get('parent_id'),
            kwargs.get('exclude_types'),
            start_time=kwargs.get('begin_time', None),
            end_time=kwargs.get('end_time', None),
            filter_vars=kwargs.get('filter_vars', False),
            token=token
        ),
    )


@api.admin_route.get('/tree_tables')
def tree_tables(request, **kwargs):
    """
    独立获取数见数据集树，指标树，数芯明细树
    :return:
    """
    return True, 'ok', dataset_service.get_tree_tables(request, **kwargs),


@api.admin_route.get('/indicator/search')
def indicator_search(**kwargs):
    key = kwargs.get('key')
    if not key:
        return False, '请输入需要搜索的指标名称', ''
    return True, "ok", dataset_service.indicator_search_by_key(key)


@api.admin_route.get('/check')
def check_dataset_permission(**kwargs):
    """
    检测是否有权限查看该资源
    :param dict kwargs:
    :return:
    """
    action_code = kwargs.get('action_code')
    if action_code not in ['view', 'edit']:
        raise UserError(message=u'action_code 只支持view, edit')
    if action_code == 'view':
        dataset_service.check_can_view(kwargs.get('id'))
    else:
        dataset_service.check_can_edit(kwargs.get('id'))
    return True, None, kwargs.get('id')


@api.admin_route.post('/import_table', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def import_table(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/dataset/add 引入添加数据集
    @apiGroup  dataset
    @apiResponse  200 {
        "result": true,
        "msg": "添加成功",
        "data": "39ed1746-039b-5652-1b20-f3549d8988f8"
    }
    **/
    """
    # kwargs = {
    #     'id': '',
    #     'connect_type': '直连',
    #     'is_complex': 1,
    #     'field': [],
    #     'name': '成本cbxt(data_wide_cb_cbxt22121',
    #     'parent_id': '3a0cccde-ac26-6240-6406-e4ba5fb091a4',
    #     'type': 'SQL',
    #     'user_group_id': '39f91135-ecd3-971e-2c1f-3bb1c813d353',
    #     'edit_mode': 'sql',
    #     'clear_time': 30,
    #     'content': {'data_source_id': '39fc8f8f-bc68-50d9-3aa3-643e61c9c596', 'count': 1,
    #                 'sql': 'select * from mdc_table',
    #                 'create_table_sql': '',
    #                 'tmp_table_name': '',
    #                 'bind_source_id': '39fc8f8f-bc68-50d9-3aa3-643e61c9c596'},
    #     'flow': {
    #         'schedule': '0287?***',
    #         'depend_flow_id': '',
    #         'status': '启用',
    #         'state_trigger': 0
    #     },
    #     'use_cache': 0,
    #     'cache_flow': {
    #         'schedule': '000-23/1?***'
    #     },
    #     'relation_content': {
    #         'nodeDataArray': [],
    #         'linkDataArray': []
    #     },
    #     'filter_content': [],
    #     'var_content': [],
    #     'is_need_procedure': 2,
    #     'disable_procedure': 1
    # }

    if hasattr(g, "code") and g.code:
        # 获取存储类型
        setattr(g, "storage", get_storage_type(g.code))
    _url = urlparse(request.url)
    g.url = _url.scheme + '://' + _url.netloc
    kwargs['type'] = kwargs.get('type').upper()
    model = DatasetModel(**kwargs)
    dataset_define_import_service.import_table(model)
    return True, '添加成功', model.id


@api.admin_route.get('/refresh_import_table', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def refresh_import_table(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/dataset/add 刷新引入添加数据集
    @apiGroup  dataset
    @apiResponse  200 {
        "result": true,
        "msg": "刷新成功",
        "data": "39ed1746-039b-5652-1b20-f3549d8988f8"
    }
    **/
    """
    dataset_id = kwargs.get('dataset_id')
    is_repair = kwargs.get('is_repair')
    dataset_id_list = dataset_id if isinstance(dataset_id, list) else dataset_id.split(",")
    for dataset_id in dataset_id_list:
        if is_repair:  # 修复历史模型的col_name,谨慎使用
            dataset_define_import_service.repair_import_table_col_name(dataset_id)
        dataset_define_import_service.refresh_import_table(dataset_id)
    return True, '刷新成功', dataset_id


@api.admin_route.get('/run/flows', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def run_flows(request, **kwargs):
    """
    清洗所有的调度数据集（数据服务中心数据源或者数芯数据源）
    """
    is_all = kwargs.get('is_all')
    is_all = is_all if is_all else False
    is_shuxin = kwargs.get('is_shuxin')
    is_mysoft_shuxin = is_shuxin if is_shuxin else True
    dataset_id = kwargs.get('dataset_id')
    dataset_id_list = []
    if dataset_id:
        dataset_id_list = dataset_id if isinstance(dataset_id, list) else dataset_id.split(",")
    folder_id = kwargs.get('folder_id')
    datasets = dataset_service.get_dataset_tree_for_dataset_ids(folder_id)
    dataset_id_list.extend([dataset.get('id') for dataset in datasets])

    return True, 'ok', dataset_service.run_flows(is_all, is_mysoft_shuxin, dataset_id, dataset_id_list),




@api.admin_route.post('/get_shuxin15_table_param', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def get_shuxin15_table_param(request, **kwargs):
    """
    /**
   {'sql': 'xxx','data_source_id':""}
    **/
    """
    dataset_sql = kwargs.get('dataset_sql')
    data_source_id = kwargs.get('data_source_id')
    var_list = dataset_service.get_shuxin15_table_param(dataset_sql, data_source_id)
    return True, '添加成功', var_list

@api.admin_route.get('/get_shuxin15_table_param_test', validate=PermissionValidator('add-dataset.edit'))
def get_shuxin15_table_param(request, **kwargs):
    """
    /**
   {'sql': 'xxx','data_source_id':""}
    **/
    """
    dataset_sql = kwargs.get('dataset_sql')
    data_source_id = kwargs.get('data_source_id')
    var_list = dataset_service.get_shuxin15_table_param(dataset_sql, data_source_id)
    return True, '添加成功', var_list


@api.admin_route.get('/refresh_dataset_col_name', validate=PermissionValidator('add-dataset.edit'))
@kwargs_aes_decode()
def refresh_dataset_col_name(request, **kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {post} /api/dataset/add 刷新引入添加数据集
    @apiGroup  dataset
    @apiResponse  200 {
        "result": true,
        "msg": "刷新成功",
        "data": "39ed1746-039b-5652-1b20-f3549d8988f8"
    }
    **/
    """

    dataset_id = kwargs.get('dataset_id')
    if not dataset_id:
        dataset_field_service.refresh_dataset_col_name()
    else:
        dataset_id_list = dataset_id if isinstance(dataset_id, list) else dataset_id.split(",")
        if dataset_id_list:
            for dataset_id in dataset_id_list:
                dataset_field_service.refresh_dataset_col_name(dataset_id)

    return True, '刷新成功', dataset_id
