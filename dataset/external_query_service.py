# -*- coding: utf-8 -*-
"""
    数据集外部查询服 务类
    ~~~~~~~~~~~~~~~~~~~~
    Implements various data sets.
    <NAME_EMAIL> on 2018/9/15.
"""
import copy
import json
import logging
import time
import datetime
import traceback
from base.enums import (
    DatasetType,
    DatasetConnectType,
    DatasetFieldGroup,
    InspectNodeNames,
    InspectNodeStatus,
    InspectTeam,
    DatasetFieldType,
)
from base.errors import DatasetVersionNotExistError
from components.log_setting import Debug
from components import metrics
from dataset.cache import dataset_meta_cache
from dataset.query.query_dataset_service import QueryDatasetService
from dataset.query.result_data import ResultData, DatasetQueryException, QueryStructData, MultiDimQueryException
from dataset.models import DatasetModel
from dataset.repositories import dataset_repository, dataset_subject_repository, dataset_version_repository
from dataset.services import (
    advanced_field_service,
    dataset_field_service,
    dataset_var_service,
    dataset_service,
    dataset_subject_service,
    dataset_define_service,
    dataset_field_check_service,
)
from dmplib.db.mysql_wrapper import ConnectException
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from label.services import label_service
from dataset.services import dataset_permission_service
from typing import Any, Dict, List, Union
from dmplib.hug import g

debugger = Debug("dataset_external_service")

logger = logging.getLogger(__name__)


def get_dataset_tree(parent_id=''):
    """
    获取数据集树形结构数据
    :param str parent_id: 数据集父级ID，默认为空返回全部数据集
    :return:
    """
    return dataset_service.get_dataset_tree(parent_id)


def get_dataset_date_history_version_id(dataset_id: str, before_day=0):
    """
    获取数据集最新历史版本（支持多天）
    :param dataset_id:
    :param before_day:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    if before_day:
        try:
            int(before_day)
        except ValueError:
            raise UserError(message="before_day参数仅支持整数")

    begin = datetime.datetime.now()
    if before_day != 0:
        begin = begin - datetime.timedelta(days=before_day)

    # 最大时间
    end = begin + datetime.timedelta(days=1)
    version_id = dataset_version_repository.get_dataset_date_history_version_id(
        dataset_id, begin.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
    )

    if not version_id:
        raise DatasetVersionNotExistError(message="数据集版本不存在，数据集ID：%s。" % dataset_id)
    return version_id


def get_dataset_last_history_version_id(dataset_id: str, before_current: int):
    """
    获取数据集上N个版本
    :param dataset_id:
    :param before_day:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    if before_current:
        try:
            int(before_current)
        except ValueError:
            raise UserError(message="before_current参数仅支持整数")
    version_id = dataset_version_repository.get_dataset_last_history_version_id(dataset_id, before_current)

    if not version_id:
        raise DatasetVersionNotExistError(message="数据集版本不存在，数据集ID：%s。" % dataset_id)
    return version_id


def get_dataset_release_version_id(dataset_id: str):
    return dataset_version_repository.get_dataset_release_version_id(dataset_id)


def get_dataset_versions(dataset_ids):
    """
    批量获取多个数据集的版本号
    :param dataset_ids:
    :return:
    """
    if not dataset_ids:
        return None

    return dataset_meta_cache.get_dataset_versions(dataset_ids)


def get_dataset_name_by_id(dataset_id):
    rv = dataset_repository.get_dataset_name(dataset_id)
    return rv.get('name') if rv else None


def get_dataset(
    dataset_id: str, is_cache: bool = True, dataset_version_id: str = None
) -> Dict[str, Union[str, None, datetime.datetime, bool]]:
    """
    获取数据集内容
    :param str dataset_id: 数据集ID
    :param bool is_cache: 默认从缓存中获取数据
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    if is_cache:
        dataset_data = dataset_meta_cache.get_dataset_cache(dataset_id, dataset_version_id)
    else:
        dataset_data = dataset_repository.get_dataset(dataset_id)

    # 获取是否大数据量标签数据集
    if not dataset_data:
        # raise UserError(message="数据集ID{}对应数据集为空".format(dataset_id))
        return dataset_data
    dataset_data['big_lab_dataset'] = False
    try:

        content = json.loads(dataset_data.get('content'))
    except Exception as e:
        logger.exception(e)
        raise UserError(message="数据集content内容序列化错误：" + str(e))
    if (
        dataset_data.get('type') == DatasetType.Label.value
        and content
        and not label_service.get_label_sync_detail(content.get('label_id'))
    ):
        dataset_data['big_lab_dataset'] = True

    return dataset_data


def get_external_subject_dataset_fields(external_subject_id: str) -> List:
    """
    获取外部主题对应的字段(col_name进行过转换)
    :param external_subject_id:
    :return:
    """
    if not external_subject_id:
        raise ValueError("外部主题ID不能为空！")
    original_dataset_fields = dataset_field_service.get_original_external_subject_fields(external_subject_id)
    for dataset_field in original_dataset_fields:
        _convert_table_and_field_data(dataset_field)
    return original_dataset_fields


def get_original_external_subject_dataset_fields(external_subject_id: str) -> List:
    """
    获取外部主题原始字段信息
    :param external_subject_id:
    :return:
    """
    if not external_subject_id:
        raise ValueError("外部主题ID不能为空！")
    return dataset_field_service.get_original_external_subject_fields(external_subject_id)


def get_dataset_fields(
    dataset_id: str, is_group: bool = False, is_cache: bool = True, dataset_version_id: str = None
) -> List[Dict[str, Union[str, int, None]]]:
    """
    获取数据集字段集合
    :param str dataset_id: 数据集ID
    :param bool is_group: 是否按照度量和维度分组，默认不分组。
    :param bool is_cache: 默认从缓存中获取数据
    :return:
    """
    if not dataset_id:
        raise ValueError("数据集ID不能为空")

    if is_cache:
        dataset_data = dataset_meta_cache.get_dataset_cache(dataset_id, dataset_version_id)
        dataset_field_data = dataset_meta_cache.get_dataset_field_cache(dataset_id)
    else:
        dataset_data = dataset_repository.get_dataset(dataset_id)
        dataset_field_data = dataset_field_service.get_dataset_field_no_convert(dataset_id, bool(not is_group))

    for dataset_field in dataset_field_data:
        # 不落地数据集，需要替换table_name、col_name、alias
        if (
            dataset_data.get('type') in [DatasetType.ExternalSubject.value]
            or dataset_data.get('connect_type') == DatasetConnectType.Directly.value
            or (dataset_data.get('external_type') in ["dim", "dws", "dwd"])
        ):
            _convert_table_and_field_data(dataset_field)
        else:
            dataset_field["table_name"] = dataset_data.get("table_name")

    if is_group:
        if dataset_field_data:
            field_group = dataset_field_service.get_field_group(dataset_field_data)
            dimension_fields = field_group.get(DatasetFieldGroup.Dimension.value)
            field_group[DatasetFieldGroup.Dimension.value] = dataset_field_service.sort_dimension_field(
                dimension_fields
            )
            result = field_group
        else:
            result = []
    else:
        result = dataset_field_data
    return result


def _convert_table_and_field_data(dataset_field: dict):
    if dataset_field.get("origin_table_alias_name"):
        dataset_field["table_name"] = dataset_field.get("origin_table_alias_name")
    else:
        dataset_field["table_name"] = dataset_field.get("origin_table_name")
    # 视图模式(改为api和直连数据集)
    dataset_field["alias"] = dataset_field.get("col_name")
    if dataset_field.get("origin_col_name") and dataset_field.get("type") not in [
        DatasetFieldType.CalculateIndicator.value,
        DatasetFieldType.Indicator.value,
    ]:
        dataset_field["col_name"] = dataset_field.get("origin_col_name")


def get_multi_dataset_fields(
    dataset_field_ids: List[str], is_cache: bool = True
) -> List[Dict[str, Union[str, int, None]]]:
    """
    获取多个数据集字段数据
    :param list dataset_field_ids: 数据集字段ID集合
     :param bool is_cache: 默认从缓存中获取数据
    :return: []
    """
    if not dataset_field_ids or len(dataset_field_ids) < 1:
        raise UserError(message="数据集字段ID集合不能为空")

    if is_cache and not hasattr(g, "snap_id"):
        dataset_fields = dataset_meta_cache.get_multi_dataset_field_cache(dataset_field_ids)
    else:
        dataset_fields = dataset_field_service.get_multi_dataset_fields(dataset_field_ids)

    for dataset_field in dataset_fields:
        # 不落地数据集，需要替换table_name、col_name、alias
        if (
            dataset_field.get('dataset_type') in [DatasetType.ExternalSubject.value]
            or dataset_field.get('dataset_connect_type') == DatasetConnectType.Directly.value
        ):
            if dataset_field.get("origin_table_alias_name"):
                dataset_field["table_name"] = dataset_field.get("origin_table_alias_name")
            else:
                dataset_field["table_name"] = dataset_field.get("origin_table_name")
            # 视图模式(改为api和直连数据集)
            if (
                dataset_field.get('dataset_type') in [DatasetType.ExternalSubject.value]
                or dataset_field.get('dataset_connect_type') == DatasetConnectType.Directly.value
            ):
                dataset_field["alias"] = dataset_field.get("col_name")
            if dataset_field.get("origin_col_name"):
                dataset_field["col_name"] = dataset_field.get("origin_col_name")
        else:
            dataset_field["table_name"] = dataset_field.get("dataset_table_name")
    return dataset_fields


def get_dataset_data(**params) -> Dict[str, Any]:
    """
    获取数据集数据
    :param str user_id: 用户ID
    :param str dataset_id: 数据集ID
    :param str chart_id: 单图ID
    :param str query_structure_json: 数据集对外查询定义json
    :param bool is_order_master_id: 是否追加master_id排序，默认否 (可选参数)
    :param str external_subject_ids: 外部主体列表
    :param list table_name: 表名，可选
    :return: {
        "data":[{"name":"张三"}],
        "sql":"select name from a",
        "sql_execute_time": 500,
        "data_version": "201812031201_2321",
        "code":"200成功，500服务器错误、501json格式错误、, 502Json解析Sql错误、503sql执行错误、504请求超时",
        "msg":""
    }
    """
    user_id = params.get("user_id")
    dataset_id = params.get("dataset_id")
    chart_id = params.get("chart_id")
    query_structure_json = params.get("query_structure_json")
    is_order_master_id = params.get("is_order_master_id", False)
    dataset_version = params.get("dataset_version")
    if not dataset_version and hasattr(g, "dataset_version_id"):
        dataset_version = g.dataset_version_id
    external_subject_ids = params.get("external_subject_ids", [])
    table_name = params.get("table_name")
    table_alias = params.get("table_alias")
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    if not query_structure_json:
        raise UserError(message="查询json不能为空")

    debugger.log({"单图ID：": chart_id, "数据集ID：": dataset_id})
    debugger.log({"新版单图查询数据集数据json：": query_structure_json})

    query_data = QueryDatasetService(
        **{
            "user_id": user_id,
            "dataset_id": dataset_id,
            "chart_id": chart_id,
            "query_structure_json": query_structure_json,
            "is_order_master_id": is_order_master_id,
            "query_dataset_version": dataset_version,
            "external_subject_ids": external_subject_ids,
            "chart_data_model": params.get("chart_data_model")
        }
    )

    dataset_prom = metrics.prom_dataset_query_duration
    dataset_prom.start()
    result_data = ResultData()
    try:
        result_data = query_data.get_query_data(table_name, table_alias)
    except MultiDimQueryException as e:
        result_data = ResultData(**e.get_dict())
    except DatasetQueryException as e:
        logger.error(f"dataset query exception: {traceback.format_exc()}")
        result_data = ResultData(**e.get_dict())
    except Exception as be:
        logger.error(f"base exception: {traceback.print_exc()}")
        if not isinstance(be, (UserError, ConnectException)):
            logging.exception(be)
        result_data = ResultData()
        result_data.code = be.code if isinstance(be, UserError) else 500
        result_data.msg = str(be)
    finally:
        if result_data and result_data.dataset_name and result_data.code >= 500:
            logger.error(f"数据集查询异常, code<{result_data.code}>, {result_data.msg}")
        dataset_prom.end(result_data.dataset_name)

    return result_data.get_dict()


def get_dataset_query_struct(**params):
    """
    获取数据集查询结构
    :param str user_id:  用户ID
    :param str dataset_id: 数据集ID
    :param str chart_id: 单图ID
    :param str query_structure_json: 数据集对外查询定义json
    :param bool is_order_master_id: 是否追加master_id排序，默认否 (可选参数)
    :param string table_name: 表名(可选参数)
    :param list external_subject_ids: 外部主体
    :return: {
        "select":[],
        "where":[],
        "order": [],
        "limit": {},
        "group": []
    }
    """
    user_id = params.get("user_id")
    dataset_id = params.get("dataset_id")
    chart_id = params.get("chart_id")
    query_structure_json = params.get("json_struct")
    is_order_master_id = params.get("is_order_master_id", False)
    external_subject_ids = params.get("external_subject_ids")
    table_name = params.get("table_name")
    table_alias = params.get("table_alias")
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    if not query_structure_json:
        raise UserError(message="查询json不能为空")

    debugger.log({"单图ID：": chart_id, "数据集ID：": dataset_id})
    debugger.log({"新版单图查询数据集数据json：": query_structure_json})

    query_data = QueryDatasetService(
        **{
            "user_id": user_id,
            "dataset_id": dataset_id,
            "chart_id": chart_id,
            "query_structure_json": query_structure_json,
            "is_order_master_id": is_order_master_id,
            "external_subject_ids": external_subject_ids,
        }
    )

    dataset_prom = metrics.prom_dataset_query_duration
    dataset_prom.start()
    struct_data_model = QueryStructData()
    try:
        struct_data_model = query_data.get_query_struct(table_name, table_alias)
    except DatasetQueryException as e:
        struct_data_model = QueryStructData(**e.get_dict())
    except Exception as be:
        if not isinstance(be, (UserError, ConnectException)):
            logging.exception(be)
            struct_data_model = QueryStructData()
        struct_data_model.code = 500
        struct_data_model.msg = str(be)
    finally:
        dataset_prom.end(struct_data_model.dataset_name)

    return struct_data_model.get_dict()


def get_dataset_query_sql(**params) -> str:
    """
    获取查询语句
    :param str user_id: 用户ID
    :param str dataset_id: 数据集ID
    :param str chart_id: 单图ID
    :param str query_structure_json: 数据集对外查询定义json
    :param bool is_order_master_id: 是否追加master_id排序，默认否 (可选参数)
    :param str table_name: 表名，可选
    :param list external_subject_ids: 外部主体
    :return: {
        "data":[{"name":"张三"}],
        "sql":"select name from a",
        "sql_execute_time": 500,
        "data_version": "201812031201_2321",
        "code":"200成功，500服务器错误、501json格式错误、, 502Json解析Sql错误、503sql执行错误、504请求超时",
        "msg":""
    }
    """
    user_id = params.get("user_id")
    dataset_id = params.get("dataset_id")
    chart_id = params.get("chart_id")
    query_structure_json = params.get("query_structure_json")
    is_order_master_id = params.get("is_order_master_id", False)
    dataset_version = params.get("dataset_version")
    external_subject_ids = params.get("external_subject_ids", None)
    table_name = params.get("table_name")
    table_alias = params.get("table_alias")
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    if not query_structure_json:
        raise UserError(message="查询json不能为空")
    query_data = QueryDatasetService(
        **{
            "user_id": user_id,
            "dataset_id": dataset_id,
            "chart_id": chart_id,
            "query_structure_json": query_structure_json,
            "is_order_master_id": is_order_master_id,
            "query_dataset_version": dataset_version,
            "external_subject_ids": external_subject_ids,
        }
    )
    return query_data.get_query_sql(table_name, table_alias)


def delete_dataset_advanced_field(dataset_advanced_field_id):
    """
    删除数据集高级字段
    :param str dataset_advanced_field_id:
    :return:
    """
    return advanced_field_service.delete_dataset_field(dataset_advanced_field_id)


def save_or_update_dataset_advanced_field(dataset_field_model):
    """
    新增和修改数据集高级字段（id为空代表新增，id有值代表修改）
    :param dataset.models.DatasetFieldModel dataset_field_model:
    :return:
    """
    return advanced_field_service.save_dataset_field(dataset_field_model)


def batch_get_dataset_include_vars(dataset_ids: list) -> List[Any]:
    """
    批量获取数据集字段引用变量信息
    :param dataset_ids:
    :return:
    """
    return dataset_var_service.batch_get_dataset_include_vars(dataset_ids)


def batch_get_dataset_vars(dataset_ids: list):
    """
    批量获取数据集的变量
    :param dataset_ids:
    :return:
    """
    return dataset_var_service.batch_get_dataset_vars(dataset_ids)


def multi_add_subject_dataset(data_list: list):
    if not isinstance(data_list, list):
        raise UserError(message='参数错误，必须为列表')

    result = []
    for r in data_list:
        subject_id, table_name = r.get('dataset_subject_id', ''), r.get('table_name', '')
        subject_table_data = dataset_subject_repository.get_dataset_subject_table_by_table_name(subject_id, table_name)
        if not subject_table_data:
            continue

        dataset_id = add_subject_dataset(subject_table_data.get('id'))
        r['dataset_id'] = dataset_id
        result.append(r)
    return result


def add_subject_dataset(subject_table_id: str):
    """
    添加主题数据集,OpenAPI自动调用使用
    :param subject_table_id: dataset_subject_table表的ID
    :return:
    """
    data = dataset_subject_service.get_subject_table_preview_data(subject_table_id)
    subject_data = dataset_subject_repository.get_subject_data_by_subject_table_id(subject_table_id)
    dataset_id = subject_data.get('dataset_folder_id')
    dataset_folder_data = dataset_repository.get_dataset(dataset_id) or {} if dataset_id else {}
    data_folder_content = json.loads(dataset_folder_data.get('content')) if dataset_folder_data.get('content') else {}
    data_source_id = data_folder_content.get('data_source_id', '')
    dataset_data = {
        'id': data.get('dataset_id'),
        'name': data.get('description'),
        'description': data.get('description'),
        'type': DatasetType.Sql.value,
        'field': data.get('head'),
        'parent_id': dataset_folder_data.get('id'),
        'content': {
            'create_table_sql': data.get('create_table_sql'),
            'tmp_table_name': data.get('tmp_table_name'),
            'count': data.get('count'),
            'origin_table_name': data.get('origin_table_name'),
            'subject_id': subject_data.get('id'),
            'data_source_id': data_source_id,
        },
        'flow': {},
    }
    model = DatasetModel(**dataset_data)
    model.name = get_dataset_name(model.name)
    dataset_define_service.add_dataset(model, is_subject=True)
    return model.id


def get_cited_fields_by_dataset(dataset_id):
    """
    :param dataset_id:
    :return:
    """
    return dataset_field_check_service.DatasetFieldCompareService(dataset_id, []).get_cited_fields()


def get_dataset_name(name: str, number=0):
    if number > 0:
        name = name.split('(')[0] + '(' + str(number) + ')'
    data = dataset_repository.get_dataset_by_name([name])
    if not data:
        return name
    number += 1
    return get_dataset_name(name, number)


def get_dataset_sql_used_var_ids_by_dataset_id(dataset_id: str):
    """
    获取数据集sql中使用的变量ID
    :param dataset_id:
    :return: 变量id列表
    """
    return dataset_var_service.get_dataset_sql_used_var_ids_by_dataset_id(dataset_id)


def get_dataset_sql_used_var_ids_by_dataset_ids(dataset_ids: list):
    """
    批量获取数据集sql中使用的变量id
    :param dataset_ids:
    :return: map结构 dataset_id -> 变量id列表
    """
    var_ids_map = {}
    for dataset_id in dataset_ids:
        var_ids = get_dataset_sql_used_var_ids_by_dataset_id(dataset_id)
        var_ids_map[dataset_id] = var_ids
    return var_ids_map


def batch_get_dataset_var_info(dataset_vars: list, dashboard_id: str):
    """
    批量获取指定报告，指定变量的信息
    :param dataset_vars:
    :param dashboard_id:
    :return:
    """
    return dataset_var_service.batch_get_dataset_var_info(dataset_vars, dashboard_id)


def receive_dataset_subject_inspect_result(data: dict):
    """
    接收主题包巡检结果
    :param data:
    :param data_source_type:
    :return:
    """
    subject_id = data.get("subject_id")
    subject_inspection = dataset_subject_service.get_dataset_subject_inspection_by_id(subject_id)
    if not subject_inspection:
        raise UserError(message="巡检记录不存在!")
    self_nodes = [
        InspectNodeNames.BusinessDataPreparationNode.value,
        InspectNodeNames.HighDataDataCleaningNode.value,
        InspectNodeNames.HighDataDataPushNode.value,
    ]
    try:
        validate_subject_inspect_result_data(data, self_nodes)
    except Exception as e:
        # 更新节点为失败
        end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        filed_values = {
            "end_time": end_time,
            "status": InspectNodeStatus.InspectNodeFailed.value,
            "inspect_result": {
                "node_type": "",
                "node_name": "推送巡检结果数据格式非法",
                "check_time": end_time,
                "check_items": [
                    {
                        "msg": f"HighData推送的巡检结果数据未通过DMP格式校验，错误信息 {str(e)}",
                        "name": "推送巡检结果数据格式非法",
                        "owner": InspectTeam.HdTeam.value,
                        "url": "",
                        "result": InspectNodeStatus.InspectNodeFailed.value,
                    }
                ],
                "error_num": 1,
                "warning_num": 0,
                "status": InspectNodeStatus.InspectNodeFailed.value,
            },
        }
        for node in self_nodes:
            data = copy.deepcopy(filed_values)
            filed_values["inspect_result"]["node_type"] = node
            dataset_subject_service.upset_subject_inspection_node(subject_id, node, data)
        raise UserError(message=str(e))
    version = data.get("version")
    if not version:
        raise UserError(message='缺少版本version')
    subject_inspection = dataset_subject_service.get_dataset_subject_inspection_by_id(subject_id)
    if not subject_inspection:
        raise UserError(message="巡检记录不存在!")
    if subject_inspection["inspect_version"] != version:
        raise UserError(message=f"当前巡检版本 {subject_inspection['inspect_version']} 与请求版本 {version} 不一致")
    result = dataset_subject_service.generate_db_subject_inspect_result(data["inspection_result"])
    for node_name, item in result.items():
        data = {'end_time': item['check_time'], 'status': item["status"], 'inspect_result': json.dumps(item)}
        dataset_subject_repository.upset_dataset_subject_inspection_node(subject_id, node_name, data)
    return subject_id


def receive_dataset_api_inspect_result(data: dict):
    """
    接收api数据源巡检结果
    :param data:
    :return:
    """
    subject_id = data.get("subject_id")
    subject_inspection = dataset_subject_service.get_dataset_subject_inspection_by_id(subject_id)
    if not subject_inspection:
        raise UserError(message="巡检记录不存在!")
    self_nodes = [InspectNodeNames.BusinessDataPreparationNode.value, InspectNodeNames.ApiDataDataCleaningNode.value]
    try:
        validate_subject_inspect_result_data(data, self_nodes)
    except Exception as e:
        # 更新节点为失败
        end_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        filed_values = {
            "end_time": end_time,
            "status": InspectNodeStatus.InspectNodeFailed.value,
            "inspect_result": {
                "node_type": "",
                "node_name": "推送巡检结果数据格式非法",
                "check_time": end_time,
                "check_items": [
                    {
                        "msg": f"HighData推送的巡检结果数据未通过DMP格式校验，错误信息 {str(e)}",
                        "name": "推送巡检结果数据格式非法",
                        "owner": InspectTeam.YkTeam.value,
                        "url": "",
                        "result": "失败",
                    }
                ],
                "error_num": 1,
                "warning_num": 0,
            },
        }
        for node in self_nodes:
            filed_values["inspect_result"]["node_type"] = node
            dataset_subject_service.upset_subject_inspection_node(subject_id, node, filed_values)
        raise UserError(message=str(e))
    result = dataset_subject_service.generate_db_subject_inspect_result(data["inspection_result"])
    for node_name, item in result.items():
        data = {
            'end_time': item['check_time'],
            'status': item["status"],
            'inspect_result': json.dumps(item['inspect_result']),
        }
        dataset_subject_repository.upset_dataset_subject_inspection_node(subject_id, node_name, data)
    return subject_id


def validate_subject_inspect_result_data(data: dict, not_empty_nodes: list):
    if not data.get('project_code'):
        raise UserError(message='缺少项目project_code')
    if not data.get('inspection_result'):
        raise UserError(message='缺少巡检结果inspection_result')
    if not isinstance(data['inspection_result'], list):
        raise UserError(message='巡检结果inspection_result必须为json数组类型')

    if not data['inspection_result']:
        raise UserError(message='巡检结果inspection_result不能为空')

    current_nodes = []
    for node_result in data['inspection_result']:
        if not node_result.get('node_type'):
            raise UserError(message='缺少node_type')
        if not node_result.get('check_items'):
            raise UserError(message='缺少check_items')
        if not node_result.get('check_time'):
            raise UserError(message='缺少check_time')
        _validate_time(node_result['check_time'])

        if not isinstance(node_result['check_items'], list):
            raise UserError(message='check_items')
        for r in node_result['check_items']:
            validate_check_item(r)
        current_nodes.append(node_result.get('node_type'))
    db_nodes_map = {
        node["node_name"]: node
        for node in dataset_subject_repository.get_dataset_subject_inspection_nodes(data["subject_id"], not_empty_nodes)
    }
    for node in not_empty_nodes:
        db_node = db_nodes_map.get(node, {})
        if not db_node:
            continue
        if db_node.get("status") != InspectNodeStatus.InspectNodeInspecting.value:
            raise UserError(message=f"巡检节点 {node} 巡检已经结束，不能重复提交")
        if node not in current_nodes:
            raise UserError(message=f"巡检结果inspection_result缺少 {node} 节点")


def validate_check_item(r: dict):
    if not r.get('name'):
        raise UserError(message='检查项check_item缺少name')
    if not r.get('result'):
        raise UserError(message='检查项check_item缺少result')
    if not r.get('msg'):
        raise UserError(message='检查项check_item缺少msg')


def _validate_time(s: str):
    try:
        datetime.datetime.strptime(s, '%Y-%m-%d %H:%M:%S')
    except:
        raise UserError(message='日期字符串格式错误,格式为：yyyy-mm-dd hh:mm:ss')


def get_user_dataset_permission_field_value(user_id: str, dataset_permission_id: str):
    # 获取权限数据集对应用户可查询参数
    return dataset_permission_service.get_user_dataset_permission_field_value(user_id, dataset_permission_id)


def add_dataset(model):
    """
    新增数据集，供数据升级，外部调用
    :param data:
    :return:
    """
    dataset_define_service.add_dataset(model)
    return model.id


def run_get_data(model):
    """
    运行数据集， 产生数据集数据和数据集字段
    :return:
    """
    return dataset_service.run_get_data(model)


def generate_excel_dataset(content):
    """
    生成Excel数据集
    :param task_id:
    :param content:
    :return tuple:
    """
    from dataset.services import dataset_async_service
    task_id = dataset_async_service.get_task_id('dataset', DatasetType.Excel.value, g.code, seq_id())
    dataset_service.generate_excel_dataset(task_id, content)
    return {'task_id': task_id}


def get_task_data(task_id):
    """
    获取预览数据
    :param dict task_id:
    :return tuple:
    """
    return dataset_service.get_task_data(task_id)


def generate_permission_filter(user_id, dataset_id, filter_json, query):
    """
    添加权限过滤where条件
    :param user_id:
    :param dataset_id:
    :param filter_json:
    :param query:
    :return:
    """
    from dataset.query import query_structure_helper
    from dataset.cache import dataset_meta_cache
    from rbac.external_service import gen_permission_filter

    dataset_data = dataset_meta_cache.get_dataset_cache(dataset_id)

    fields_auth = gen_permission_filter(user_id, dataset_id, filter_json)

    permission_where = query_structure_helper.transform_permission_where(
        fields_auth, dataset_data
    )
    if permission_where:
        if not query.where:
            permission_where.logical_relation = ""
        query.where.append(permission_where)
