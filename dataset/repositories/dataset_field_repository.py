# !/usr/bin/env python3
# -*- coding: utf-8 -*-
from base import repository
from typing import List, Union

from components.db_engine_transform import OracleToMysqlTransformService
from components.query_structure_sql import QueryStructure, Select, Where, Object, Prop, Group, Limit
from components.query_sql_encoder import encode_query
from components.storage_setting import is_local_storage
from components.data_center_api import get_new_erp_datasource_model, get_data_by_sql
from dmplib.saas.project import get_db, get_data_db
from dmplib.utils.sql_util import Description
from base.enums import DBEngine, MysoftNewERPDataBaseType


def get_dataset_field_v2(conditions: Union[None, dict]=None):
    """
    获取数据集字段
    :param dataset_id:
    :param condition:
    :return:
    """
    dataset_fields = [
        'id',
        'dataset_id',
        'alias_name',
        'col_name',
        'origin_col_name',
        'origin_field_type',
        'origin_table_id',
        'origin_table_comment',
        'origin_table_name',
        'origin_table_alias_name',
        'data_type',
        'group_type',
        'rank',
        'visible',
        'field_group',
        'format',
        'type',
        'expression',
        'expression_advance',
        'inspection_rules',
        'note',
    ]
    return repository.get_list('dataset_field', conditions, dataset_fields)


def get_dataset_field(dataset_id: str, condition: Union[None, dict] = None):
    """
    获取数据集字段
    :param dataset_id:
    :param condition:
    :return:
    """
    dataset_fields = [
        'id',
        'dataset_id',
        'alias_name',
        'col_name',
        'origin_col_name',
        'origin_field_type',
        'origin_table_id',
        'origin_table_comment',
        'origin_table_name',
        'origin_table_alias_name',
        'data_type',
        'group_type',
        'rank',
        'visible',
        'field_group',
        'format',
        'type',
        'expression',
        'expression_advance',
        'inspection_rules',
        'note',
        'external_id',
        'origin_dim_type',
        'relation_fields',
        'caliber',
        'business_note'
    ]
    sql = 'SELECT {cols} FROM dataset_field'.format(cols='`' + '`,`'.join(dataset_fields) + '`')
    sql += ' WHERE dataset_id=%(dataset_id)s '
    if condition:
        where = ' AND '.join([key + '=' + '"' + value + '"' for key, value in condition.items()])
        sql += " AND " + where
    sql += ' ORDER BY `rank`'

    params = {'dataset_id': dataset_id}
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_field_name_by_ids(dataset_field_ids):
    """
    根据数据集字段ID集合获取数据集字段数据
    :param dataset_field_ids:
    :return:
    """
    sql = '''SELECT `alias_name`, `col_name` FROM `dataset_field` where `id` in %(ids)s'''

    with get_db() as db:
        return db.query(sql, {'ids': dataset_field_ids})


def get_col_name_by_ids(field_ids):
    """
    获取字段名
    :param field_ids:
    :return:
    """
    if not field_ids:
        return []
    with get_db() as db:
        sql = '''SELECT `col_name` FROM `dataset_field` where `id` in %(ids)s'''
        return db.query_columns(sql, {"ids": list(field_ids)})


def get_dataset_field_by_ids(dataset_field_ids: List[str]):
    """
    根据数据集字段ID集合获取数据集字段数据
    :param dataset_field_ids:
    :return:
    """
    sql = '''SELECT `dataset_field`.`id`, `dataset_field`.dataset_id, `dataset_field`.alias_name,
            `dataset_field`.col_name, `dataset_field`.origin_table_alias_name, `dataset_field`.origin_table_name,
            `dataset_field`.origin_col_name, `dataset_field`.origin_field_type, `dataset_field`.data_type,
            `dataset_field`.rank, `dataset_field`.visible, `dataset_field`.field_group, `dataset_field`.type,
            `dataset_field`.expression, `dataset`.`type` as dataset_type,
             `dataset`.`connect_type` as dataset_connect_type, `dataset`.`edit_mode` as dataset_edit_mode,
             `dataset_field`.`relation_fields`
             FROM `dataset_field` LEFT JOIN `dataset` ON `dataset`.`id` = `dataset_field`.`dataset_id`
             where dataset_field.`id` in %(ids)s'''

    with get_db() as db:
        return db.query(sql, {'ids': dataset_field_ids})


def get_dataset_field_by_dataset_ids(dataset_ids):
    """
    根据数据集ids获取数据
    :param dataset_ids:
    :return:
    """
    sql = 'SELECT * FROM dataset_field WHERE dataset_id in ' + dataset_ids
    with get_db() as db:
        return db.query(sql)


def get_dataset_field_info(filed_id):
    sql = """SELECT d.table_name,d.name,d.id,d.user_group_id,d.type,d.description,d.level_code, d.connect_type,
             df.col_name,df.id,df.data_type,df.visible, df.group_type, df.origin_table_name,
             df.origin_col_name, df.field_group, df.alias_name, df.type AS field_type,
             df.expression, df.expression_advance, df.origin_table_alias_name, df.origin_col_name, df.field_group,
             df.alias_name, df.expression
             FROM dataset AS d INNER JOIN dataset_field  AS df ON df.dataset_id = d.id and df.id = %(filed_id)s"""
    params = {'filed_id': filed_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dataset_field_info_col_name(dataset_id, col_name):
    sql = """SELECT d.table_name,d.name,d.id,d.user_group_id,d.type,d.description,d.level_code, d.connect_type,
              df.col_name,df.id,df.data_type,df.visible, df.group_type, df.origin_table_name,
              df.origin_col_name, df.field_group, df.alias_name,
              df.expression FROM dataset AS d INNER JOIN
              dataset_field  AS df ON df.dataset_id = d.id and df.col_name = %(col_name)s where d.id = %(dataset_id)s"""
    params = {'dataset_id': dataset_id, 'col_name': col_name}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dataset_field_values(
    table_name, col_name, condition=None, is_senior_field=False, is_group_field=False, field_col_name=None
):
    """
    根据dataset_field_id获取单个dataset记录
    :param str table_name:
    :param str col_name:
    :return:
    """
    where_str = ''
    if condition:
        where_str = " %s " % condition

    # 增加limit 1500 限制避免大数据量
    if is_senior_field:
        if is_group_field:
            select = """{} as "{}" """.format(col_name, field_col_name)
            sql = 'SELECT %s FROM `%s` %s GROUP BY %s  limit 1500' % (select, table_name, where_str, col_name)
        else:
            sql = 'SELECT %s FROM `%s` %s GROUP BY %s  limit 1500' % (col_name, table_name, where_str, col_name)
    else:
        sql = 'SELECT %s FROM `%s` %s GROUP BY %s  limit 1500' % (
            '`' + col_name + '`',
            table_name,
            where_str,
            '`' + col_name + '`',
        )

    with get_data_db() as db:
        return db.query(sql)


def get_col_name(dataset_id):
    """
    获取字段名
    :param dataset_id:
    :return:
    """
    with get_db() as db:
        sql = "SELECT col_name FROM dataset_field WHERE dataset_id=%(dataset_id)s"
        return db.query_columns(sql, {"dataset_id": dataset_id})


def get_col_name_and_origin_col_name_and_origin_table_alias_name(dataset_id):
    """
    获取字段名
    :param dataset_id:
    :return:
    """
    with get_db() as db:
        sql = "SELECT col_name,origin_col_name,origin_table_name,origin_table_alias_name FROM dataset_field WHERE dataset_id=%(dataset_id)s"
        return db.query(sql, {"dataset_id": dataset_id})


def get_one_origin_field_name(dataset_id):
    """
    随机回去一个字段名
    :param dataset_id:
    :return:
    """
    with get_db() as db:
        sql = """SELECT origin_table_name,origin_col_name,col_name FROM dataset_field WHERE dataset_id=%(dataset_id)s
              and `type` = '普通'  limit 1 """
        return db.query_one(sql, {"dataset_id": dataset_id})


def get_alias_name_and_origin_col_name_and_origin_table_alias_name(dataset_id):
    """
    获取字段名
    :param dataset_id:
    :return:
    """
    with get_db() as db:
        sql = "SELECT alias_name,origin_col_name,origin_table_name,origin_table_alias_name FROM dataset_field WHERE dataset_id=%(dataset_id)s"
        return db.query(sql, {"dataset_id": dataset_id})


def get_alias_name(dataset_id):
    """
    获取字段名
    :param dataset_id:
    :return:
    """
    with get_db() as db:
        sql = "SELECT alias_name FROM dataset_field WHERE dataset_id=%(dataset_id)s"
        return db.query_columns(sql, {"dataset_id": dataset_id})


def get_dataset_field_by_type(types, columns):
    """
    通过dataset_field.type获取dataset_field
    :param types: 类型tuple
    :param columns: 查询字段tuple
    :return:
    """
    with get_db() as db:
        sql = (
            "SELECT `%s` " % '`,`'.join(columns) + "FROM dataset_field "
            "WHERE `type` in %(types)s AND ISNULL(`expression_advance`) "
        )

        return db.query(sql, {'types': types})


def delete_dataset_indexs(index_ids):
    """
    删除数据集索引
    :param index_id:
    :return:
    """
    with get_db() as db:
        sql = "DELETE FROM dataset_index WHERE id in %(index_ids)s"
        return db.exec_sql(sql, {"index_ids": index_ids})


def validate_field_id_by_dataset_id(fields: list, dataset_id: str) -> bool:
    """
    检测数据集字段id是否有效（无效返回True），判断依据是所有的id是否有属于其他数据集的
    :param fields:
    :param dataset_id:
    :return:
    """
    sql = "select count(*) from dataset_field where id in %(fields)s and dataset_id != %(dataset_id)s"
    with get_db() as db:
        result = db.query_scalar(sql, {'fields': fields, "dataset_id": dataset_id})
        return bool(result)


def get_dataset_field_by_where(conditions: dict):
    """
    获取field信息
    :param conditions:
    :param fields:
    :return:
    """
    dataset_fields = [
        'id',
        'dataset_id',
        'alias_name',
        'col_name',
        'data_type',
        'group_type',
        'visible',
        'field_group',
        'inspection_rules',
        "type"
    ]

    return repository.get_data('dataset_field', conditions, dataset_fields)


def get_dataset_by_id(dataset_id):
    return repository.get_data('dataset', {"id": dataset_id})


def get_field_of_cloud(table_name):
    with get_data_db() as db:
        db.exec_sql(f"select * from {table_name} limit 1")
        columns = [Description(*col) for col in list(db.cur.description)]
        structs = []
        for column in columns:
            struct = {
                "col_name": column.name,
                "data_type": column.col_type.lower(),
                "comment": column.alias_name,
                "origin_field_type": column.col_type.lower(),
            }
            structs.append(struct)
    return structs


def get_field_of_local(table_name, dataset_id):
    from data_source.enums.mysoft_new_erp_enums import MysoftNewERPTableSql
    from components.db_engine_transform import MssqlToMysqlTransformService
    from dataset.cache.dataset_meta_cache import get_dataset_cache

    dataset = get_dataset_cache(dataset_id)

    data_source_model = get_new_erp_datasource_model(dataset.get('content'))
    if data_source_model.db_type.lower() == MysoftNewERPDataBaseType.Mysql.value.lower():
        sql = MysoftNewERPTableSql.GET_TABLE_MYSQL_COLUMN_SQL.value
    elif data_source_model.db_type.lower() == MysoftNewERPDataBaseType.DM.value.lower():
        sql = MysoftNewERPTableSql.GET_TABLE_DM_COLUMN_SQL.value
    else:
        sql = MysoftNewERPTableSql.GET_COLUMN_SQL_MDC.value
    sql = sql.format(table_name=table_name)
    rs = get_data_by_sql(sql, data_source_model=data_source_model)
    column_list = rs.get("Data", [])
    fields = []
    if data_source_model.db_type.lower() == MysoftNewERPDataBaseType.DM.value.lower():
        transfer = OracleToMysqlTransformService(dataset_id=dataset_id)
    else:
        transfer = MssqlToMysqlTransformService(dataset_id=dataset_id)
    for column in column_list:
        data_type = transfer.get_data_type(column.get("type")).lower()
        col_name = column.get("name")
        struct = {
            "col_name": col_name,
            "data_type": data_type,
            "comment": column.get("comment"),
            "origin_field_type": data_type
        }
        fields.append(struct)
    return fields, data_source_model.db_type.lower()


def trans_id_data_type(data_type, value: dict):
    col_name = value.get("col_name")
    alias_name = value.get("alias_name")
    if ("char" in data_type or data_type in ["text"]) and (
        (col_name and col_name.lower().endswith("id")) or
        (col_name and col_name.split("_")[0].lower().endswith("id")) or
        (alias_name and alias_name.lower().endswith("id"))
    ):
        return "char(50)"
    return data_type


def update_dataset_field_list(dataset_fields):
    with get_db() as db:
        db.replace_multi_data("dataset_field", dataset_fields, dataset_fields[0].keys(), commit=False)

def delete_field(field_list: list):
    if field_list:
        return repository.delete_data("dataset_field", {'id': field_list})