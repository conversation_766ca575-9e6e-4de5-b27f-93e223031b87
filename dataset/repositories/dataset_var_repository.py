#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/4/2 11:10
# <AUTHOR> caoxl
# @File     : dataset_var_repository.py
from dmplib.saas.project import get_db
from base.repository import build_in_query


def batch_get_dataset_include_vars(dataset_ids: list):
    if not dataset_ids:
        return []
    datasets_str, params = build_in_query(dataset_ids, "dataset")
    sql = (
        "SELECT v.name, v.description, v.var_type, v.value_type,v.default_value,v.default_value_type,"
        "i.dataset_id, i.field_id, i.var_id, "
        "v.accurate_type FROM `dataset_field_include_vars` i LEFT JOIN `dataset_vars` v ON i.var_id=v.id "
        "WHERE i.dataset_id  IN  ({datasets})"
    ).format(datasets=datasets_str)
    with get_db() as db:
        return db.query(sql, params)


def batch_get_dataset_vars(dataset_ids: list):
    datasets_str, params = build_in_query(dataset_ids, "dataset")
    sql = (
        "SELECT id,name,description,var_type,value_type,dataset_id,default_value,"
        "default_value_type,created_on,accurate_type "
        "FROM `dataset_vars` "
        "WHERE `dataset_id`  IN  ({datasets})"
    ).format(datasets=datasets_str)
    with get_db() as db:
        return db.query(sql, params)


def get_dataset_var_by_ids(dataset_var_ids):
    """
    根据变量ID集合获取变量字段数据
    :param dataset_var_ids:
    :return:
    """
    sql = '''SELECT `id`,`name`,`description`,`var_type`,`value_type`,`dataset_id`,`default_value`,`default_value_type`
    ,`accurate_type`,external_content  FROM  `dataset_vars` WHERE `id` in %(ids)s'''
    with get_db() as db:
        return db.query(sql, {'ids': dataset_var_ids})


def get_dataset_var_by_ids_and_did(dataset_var_ids, dashboard_id):
    """
    从新表获取变量数据
    :param dataset_var_ids:
    :param dashboard_id:
    :return:
    """
    sql = '''SELECT dv.id,dv.id as var_id,dv.var_type,dv.value_type,dv.default_value,dv.default_value_type,
            dvs.value_source,dvs.value_identifier
            FROM  `dataset_vars` dv
            JOIN `dashboard_value_source` dvs
            JOIN `dashboard_vars_value_source_relation` dvvsr
            WHERE dv.id=dvvsr.var_id AND dvvsr.value_source_id=dvs.id
            AND dv.`id` in %(ids)s AND dvvsr.dashboard_id=%(dashboard_id)s '''
    with get_db() as db:
        return db.query(sql, {'ids': dataset_var_ids, 'dashboard_id': dashboard_id})
