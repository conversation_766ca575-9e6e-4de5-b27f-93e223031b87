# !/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    <NAME_EMAIL> on 2023/6/12.
"""
import copy
from functools import lru_cache

from dmplib.saas.project import get_db
from dmplib.hug import g
from dmplib.utils.errors import UserError
from components.utils import array_generator
from base import repository


def save_dataset_data(export_dataset_data, include_folder):  # NOSONAR
    """
    根据租户code保存数据集相关数据
    :param project_code:
    :param export_dataset_data:
    :param include_folder:
    :return:
    """
    dataset = export_dataset_data.get("dataset")
    dataset_fields = export_dataset_data.get("dataset_fields") or []
    dataset_folders = export_dataset_data.get("dataset_folders")

    dataset_field_deletes = export_dataset_data.get("dataset_field_delete")
    dataset_filters = export_dataset_data.get("dataset_filter")
    dataset_vars = export_dataset_data.get("dataset_vars")
    dataset_field_include_vars = export_dataset_data.get("dataset_field_include_vars")
    dataset_field_groups = export_dataset_data.get("dataset_field_groups")
    dataset_field_group_relations = export_dataset_data.get("dataset_field_group_relations")

    with get_db() as db:
        if dataset:
            db.replace_multi_data("dataset", [dataset], dataset.keys(), commit=False)
            # db.delete("dataset_field", {"dataset_id": dataset.get("id"), "created_by": "celery"}, commit=False)
            # db.delete("dataset_field", {"dataset_id": dataset.get("id"), "type": "普通"}, commit=False)
            # db.delete("dataset_depend", {"depend_id": dataset.get("id")}, commit=False)
        if dataset_fields and len(dataset_fields) > 0:
            db.replace_multi_data("dataset_field", dataset_fields, dataset_fields[0].keys(), commit=False)
        if include_folder and dataset_folders and len(dataset_folders) > 0:
            db.replace_multi_data("dataset", dataset_folders, dataset_folders[0].keys(), commit=False)
        _replace_dataset_field_deletes(dataset_field_deletes, db)
        _replace_dataset_filters(dataset_filters, db, dataset.get("id"))
        _replace_dataset_field_group(dataset_field_groups, db, dataset.get('id'))
        _replace_dataset_field_group_relations(dataset_field_group_relations, db, dataset.get('id'))
        # 更新数据集-变量相关的表
        _replace_dataset_vars(db, dataset_vars, dataset_field_include_vars, dataset)
        # 字段分组导入

        db.commit()
    return [i.get('id') for i in array_generator(dataset_fields)]


def _replace_dataset_filters(dataset_filters, db, dataset_id):
    if dataset_filters and len(dataset_filters) > 0:
        db.delete("dataset_filter", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dataset_filter", dataset_filters, dataset_filters[0].keys(), commit=False)


def _replace_dataset_field_group(dataset_field_groups, db, dataset_id):
    if dataset_field_groups and len(dataset_field_groups) > 0:
        db.delete("dataset_field_group", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dataset_field_group", dataset_field_groups, dataset_field_groups[0].keys(), commit=False)


def _replace_dataset_field_group_relations(dataset_field_group_relations, db, dataset_id):
    if dataset_field_group_relations and len(dataset_field_group_relations) > 0:
        db.delete("dataset_field_group_relation", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dataset_field_group_relation", dataset_field_group_relations, dataset_field_group_relations[0].keys(), commit=False)


def _replace_dataset_field_deletes(dataset_field_deletes, db):
    if dataset_field_deletes and len(dataset_field_deletes) > 0:
        db.replace_multi_data(
            "dataset_field_delete", dataset_field_deletes, dataset_field_deletes[0].keys(), commit=False
        )


def _replace_dataset_vars(db, dataset_vars, dataset_field_include_vars, dataset):
    """
    更新变量相关的表
    :param db:
    :param dataset_vars:
    :param dataset_field_include_vars:
    :param dataset:
    :return:
    """
    if dataset_vars and len(dataset_vars) > 0:
        db.delete("dataset_vars", {"dataset_id": dataset.get("id"), "created_by": "celery"}, commit=False)
        db.replace_multi_data("dataset_vars", dataset_vars, dataset_vars[0].keys(), commit=False)
    if dataset_field_include_vars and len(dataset_field_include_vars) > 0:
        db.delete("dataset_field_include_vars", {"dataset_id": dataset.get("id"), "created_by": "celery"}, commit=False)
        db.replace_multi_data(
            "dataset_field_include_vars",
            dataset_field_include_vars,
            dataset_field_include_vars[0].keys(),
            commit=False,
        )


def get_dataset_level_code_v2(dataset_id, conn=None, parent_id=None):
    """
    判断数据集是否存在，存在不重新生成level_code
    :param dataset_id:
    :param conn:
    :param parent_id:
    :return:
    """
    if parent_id:
        sql = """select level_code from dataset where id=%(dataset_id)s and parent_id=%(parent_id)s"""
    else:
        sql = """select level_code from dataset where id=%(dataset_id)s"""
    params = {"dataset_id": dataset_id, "parent_id": parent_id}
    result = conn.query_one(sql, params)
    if result:
        return result.get("level_code")
    return result


def delete_dataset_ids(project_code, dataset_ids):
    if not dataset_ids:
        return
    sql = "delete from dataset where id in %(dataset_ids)s"
    params = {"dataset_ids": dataset_ids}
    with get_db(project_code) as conn:
        return conn.exec_sql(sql, params)


def get_all_dataset_by_id(parent_id):
    with get_db() as conn:
        level_code = get_dataset_level_code_v2(parent_id, conn=conn)
        # 根目录
        if not level_code:
            return {}
        child_datasets_sql = """select id, name, level_code, parent_id from dataset where level_code like %(level_code)s and 
                type ='FOLDER' """
        child_datasets = conn.query(child_datasets_sql, {"level_code": level_code + "%"}) or []
        child_datasets_dict = {}
        for row in child_datasets:
            if row.get("parent_id") not in child_datasets_dict.keys():
                child_datasets_dict[row.get("parent_id")] = {row.get("name"): row.get("id")}
            else:
                child_datasets_dict[row.get("parent_id")][row.get("name")] = row.get("id")
        return child_datasets_dict


def deal_dataset_folders(parent_id, dataset_folders):
    """
    # 导入的时候，根据分发还是导入，走不同逻辑，分发覆盖目录，导入根据目录名称合并
    # [f1, f2, f3] 当f1存在时，删除f1列表，f2是否存在重复，重复也是删除，不重复，修改f2的parent_id和生成level_code
    :param project_code:
    :param parent_id:
    :param dataset_folders:
    :param is_import:
    :return:
    """
    from dataset.services.dataset_define_service import generate_level_code
    from level_sequence.models import DataSetLevelSequenceModel

    # 覆盖全目录不会走下面逻辑
    if not dataset_folders:
        return dataset_folders
    save_dataset_folders = copy.deepcopy(dataset_folders)
    for i, dataset_folder in enumerate(dataset_folders):
        old_dataset_folder = copy.copy(dataset_folder)
        # 先处理parent_id问题
        if not dataset_folder.get("parent_id"):
            dataset_folder["parent_id"] = parent_id
        # 重新生成level_code
        dataset_folder["created_by"] = g.code
        dataset_folder["modified_by"] = g.code
        with get_db() as conn:
            # 当前的目录是否在该系统下存在
            new_level_code = get_dataset_level_code_v2(dataset_folder.get("id"), conn=conn)
            if not new_level_code:
                new_level_code = generate_level_code(
                    dataset_folder.get("parent_id"))
            dataset_folder["level_code"] = new_level_code
            conn.replace_multi_data("dataset", [dataset_folder], dataset_folders[0].keys())
        save_dataset_folders.remove(old_dataset_folder)
        save_dataset_folders.append(dataset_folder)
    return save_dataset_folders


def deal_dataset(parent_id, dataset, include_folder, is_lock=0):
    """
    :param project_code:
    :param parent_id:
    :param dataset:
    :param import_data:
    :param include_folder:
    :param is_lock:
    :return:
    """
    from dataset.services.dataset_define_service import generate_level_code
    from level_sequence.models import DataSetLevelSequenceModel

    dataset["created_by"] = g.code
    dataset["modified_by"] = g.code
    dataset["is_lock"] = is_lock
    # 平铺的情况都使用主目录parent_id
    if not dataset.get("parent_id") or not include_folder:
        dataset['parent_id'] = parent_id
    # 重新生成level_code
    with get_db() as conn:
        # 当前的目录是否在该系统下存在
        dataset_level_code = get_dataset_level_code_v2(dataset.get("id"), conn=conn, parent_id=dataset.get('parent_id'))
        if not dataset_level_code:
            dataset_level_code = generate_level_code(dataset.get("parent_id"))
        dataset["level_code"] = dataset_level_code
        conn.replace_multi_data("dataset", [dataset], dataset.keys())
    return dataset


def update_created_by(export_dataset_data):
    """
    修改创建人和修改人
    :param export_dataset_data:
    :return:
    """
    if export_dataset_data.get("data_source"):
        export_dataset_data["data_source"]["created_by"] = g.code
        export_dataset_data["data_source"]["modified_by"] = g.code

    if export_dataset_data.get("dataset_fields"):
        for dataset_field in export_dataset_data.get("dataset_fields"):
            dataset_field["created_by"] = "celery"
            dataset_field["modified_by"] = "celery"

    if export_dataset_data.get("dataset_vars"):
        for dataset_var in export_dataset_data.get("dataset_vars"):
            dataset_var["created_by"] = "celery"
            dataset_var["modified_by"] = "celery"

    if export_dataset_data.get("dataset_field_include_vars"):
        for dataset_field_include_var in export_dataset_data.get("dataset_field_include_vars"):
            dataset_field_include_var["created_by"] = "celery"
            dataset_field_include_var["modified_by"] = "celery"


def import_dataset_data(export_dataset_data, parent_id, include_folder=True):  # NOSONAR
    """
    导入数据集
    :param export_dataset_data:
    :param parent_id:
    :param include_folder:
    :return:
    """

    dataset_id = export_dataset_data.get("dataset_id")
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    if include_folder:
        dataset_folders = deal_dataset_folders(
            parent_id,
            export_dataset_data.get("dataset_folders")
        )
        export_dataset_data["dataset_folders"] = dataset_folders

    dataset = deal_dataset(
        parent_id, export_dataset_data.get("dataset"), include_folder
    )
    export_dataset_data["dataset"] = dataset

    # 修改创建人和修改人
    update_created_by(export_dataset_data)

    # 写入数据
    field_ids = save_dataset_data(export_dataset_data, include_folder)

    return field_ids


@lru_cache()
def get_dataset_id(field_id):
    """
    添加内存缓存
    :param field_id:
    :return:
    """
    field = repository.get_data('dataset_field', {'id': field_id}, fields=["dataset_id"]) or {}
    return field.get("dataset_id") or ''
