from typing import Dict

from base import repository

def get_table_metadata(table_names) -> Dict[str, Dict]:
    data = repository.get_dict('dataset_api_table_metadata', {'table_name': table_names}, "table_name,table_name,row_count")
    return data if data else {}

def flush_table_metadata(table_metadata_dict: Dict[str, Dict]):
    repository.replace_list_data('dataset_api_table_metadata', list(table_metadata_dict.values()), ['table_name', 'row_count'])
