# !/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> on 2017/6/14.
"""
from base.enums import DatasetVersionType, DBEngine, DatasetFieldType
from dmplib.saas.project import get_db
from dmplib.saas.project import get_data_db
from dmplib.hug import g

from components.data_center_api import (
    data_clean_by_sql,
    get_new_erp_datasource_model,
    get_data_by_sql,
    local_execute_sql
)
from components.storage_setting import compatible_local_type_no_self, is_history_dataset


def table_exists_local(table_name, content):
    datasource_model = get_new_erp_datasource_model(content)
    db_type = datasource_model.db_type or datasource_model.conn_str.DbType
    if db_type.lower() in [DBEngine.Mysql.value, DBEngine.RDS.value]:
        sql = "SHOW TABLES LIKE '{table_name}'; ".format(table_name=table_name)
    else:
        sql = f"select top 1 * from sysObjects where Id=OBJECT_ID(N'{table_name}') and xtype='U'"
    datasource_model = get_new_erp_datasource_model(content)
    return get_data_by_sql(sql, datasource_model).get("Data")


@compatible_local_type_no_self
def table_exists(table_name, content):
    exists_sql = " SHOW TABLES LIKE '{table_name}'; ".format(table_name=table_name)
    with get_data_db() as db:
        return db.exec_sql(exists_sql)


def create_table_struct_local(new_table_name, old_table_name):
    # 先判断存在删除
    pass


@compatible_local_type_no_self
def create_table_struct(new_table_name, old_table_name):
    # 先判断存在删除
    drop_table_data(new_table_name)
    copy_struct_sql = " CREATE TABLE {new_table_name} LIKE {old_table_name}; ".format(
        new_table_name=new_table_name, old_table_name=old_table_name
    )
    with get_data_db() as db:
        db.exec_sql(copy_struct_sql)


def create_table_data_local(new_table_name, old_table_name, dataset, dataset_fields):
    datasource_model = get_new_erp_datasource_model(dataset.get('content'))
    sql = get_create_table_data_local_sql(old_table_name, dataset_fields, datasource_model)
    data_clean_by_sql(sql, datasource_model, new_table_name, dataset, dataset_fields)


def get_create_table_data_local_sql(old_table_name, dataset_fields, datasource_model):
    """
    按数据集类型和字段列表生成查询SQL
    :param old_table_name:
    :param dataset_fields:
    :param datasource_model:
    :return:
    """
    from components import query_sql_encoder
    from components.query_models import QueryStructure, Prop, Object

    query_structure = QueryStructure()
    for dataset_field in dataset_fields:
        # 字段列表需要排除非“普通”类型字段
        if dataset_field.get("type") == DatasetFieldType.Normal.value:
            prop = Prop()
            prop.obj_name = old_table_name
            prop.prop_name = dataset_field.get("col_name")
            query_structure.select.append(prop)
    # 指定表名
    table_object = Object()
    table_object.name = old_table_name
    query_structure.object = [table_object]
    return query_sql_encoder.encode_query(query_structure, datasource_model.db_type.lower())


@compatible_local_type_no_self
def create_table_data(new_table_name, old_table_name, dataset=None, dataset_fields=None):
    copy_data_sql = " INSERT INTO {new_table_name} SELECT * FROM {old_table_name};  ".format(
        new_table_name=new_table_name, old_table_name=old_table_name
    )
    with get_data_db() as db:
        db.exec_sql(copy_data_sql)


@compatible_local_type_no_self
def rename_table(dataset_table_name, version_table_name, dataset=None, dataset_fields=None):
    drop_table_data(version_table_name)
    sql = 'rename table `%s` to `%s` ' % (dataset_table_name, version_table_name)
    with get_data_db() as db:
        db.exec_sql(sql)


def rename_table_local(dataset_table_name, version_table_name, dataset=None, dataset_fields=None):
    datasource_model = get_new_erp_datasource_model(dataset.get('content') if dataset else dataset)
    if not is_history_dataset(g.code, conn_str=datasource_model.conn_str):
        db_type = datasource_model.db_type or datasource_model.conn_str.DbType
        if db_type.lower() in [DBEngine.Mysql.value, DBEngine.RDS.value]:
            sql = 'rename table `%s` to `%s` ' % (dataset_table_name, version_table_name)
        else:
            sql = """exec sp_rename '{}', '{}'""".format(dataset_table_name, version_table_name)
        local_execute_sql(sql, datasource_model.conn_str, dataset.get('content'))
    else:
        sql = f"select * from {dataset_table_name}"
        data_clean_by_sql(sql, datasource_model, version_table_name, dataset, dataset_fields)


def drop_table_data(table_name):
    drop_sql = " DROP TABLE IF EXISTS {table_name} ;  ".format(table_name=table_name)
    with get_data_db() as db:
        db.exec_sql(drop_sql)


@compatible_local_type_no_self
def get_table_columns(table_name, content):
    sql = (
        "SELECT `column_name` FROM information_schema.columns "
        "WHERE `table_schema`= DATABASE() AND `table_name`=%(table_name)s "
    )
    with get_data_db() as db:
        return db.query(sql, {'table_name': table_name})


def get_table_columns_local(table_name, content):
    """
    获取表字段， 本地模式
    :param table_name:
    :param content:
    :return:
    """
    datasource_model = get_new_erp_datasource_model(content)
    db_type = datasource_model.db_type or datasource_model.conn_str.DbType
    if db_type.lower() in [DBEngine.Mysql.value, DBEngine.RDS.value]:
        sql = (
            "SELECT `column_name` FROM information_schema.columns "
            "WHERE `table_schema`= DATABASE() AND `table_name`='{table_name}'".format(table_name=table_name)
        )
    else:
        sql = """
        SELECT NAME FROM SYSCOLUMNS WHERE ID=OBJECT_ID('{}');
        """.format(table_name)
    result = get_data_by_sql(sql, datasource_model)
    return [item.get("NAME") for item in result.get("Data")]


def get_version_list(query_model):
    """
    获取数据集版本记录
    :param dataset.models.VsersionQueryModel query_model:
    :return tuple:
    """
    sql = (
        'SELECT SQL_CALC_FOUND_ROWS v.`id`,v.dataset_id,v.version_number, v.version_name, v.table_name, '
        'v.type, v.version_type, v.status, v.`content`, v.`created_on`, v.`created_by`, '
        'v.`modified_on`, v.`modified_by` , v.inspection_id,'
        'v.data_source_name, v.dataset_name, v.dataset_content, d.`type` as dataset_type  '
        'FROM dataset_version v '
        'INNER JOIN dataset d on d.id = v.dataset_id '
        'WHERE v.dataset_id = %(dataset_id)s'
    )

    sql += ' ORDER BY v.`created_on` DESC'
    sql += ' LIMIT ' + str(query_model.skip) + ',' + str(query_model.page_size)
    with get_db() as db:
        query_model.items = db.query(sql, {'dataset_id': query_model.dataset_id})
        query_model.total = db.query_scalar('SELECT FOUND_ROWS() AS total;')
    return query_model


def get_version(version_id):
    """
    获取数据集版本
    :param version_id:
    :return:
    """
    sql = """
    SELECT `id`,`dataset_id`,`version_number`,`version_name`,`table_name`,`content` FROM dataset_version
    WHERE id=%(version_id)s
    """
    params = {'version_id': version_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_dataset_version_by_ids(dataset_ids, version_type=DatasetVersionType.RC.value):
    if not dataset_ids:
        return []
    sql = """ select dataset_version.status, dataset_version.inspection_id,dataset.name from dataset_version
            inner join dataset on dataset.id =dataset_version.dataset_id where dataset_id in %(dataset_ids)s and
                version_type=%(version_type)s """
    with get_db() as db:
        return db.query(sql, {"dataset_ids": dataset_ids, "version_type": version_type})


def get_dataset_current_version(dataset_id: str):
    sql = """
    SELECT `id`,`dataset_id`, `version_number`, `version_name`, `source_version`, `table_name`, `data_source_name`,
    `dataset_name`, `created_on` FROM `dataset_version` WHERE `version_type`="正式版本" AND `dataset_id`=%(dataset_id)s
    """
    params = {'dataset_id': dataset_id}
    with get_db() as db:
        return db.query_one(sql, params)


def get_datasets_fixed_version(dataset_ids: list):
    if not dataset_ids:
        return []
    sql = """ select dataset_version.status, dataset_version.version_number, dataset_version.version_name,
            dataset_version.inspection_id, dataset_version.dataset_id, dataset.name from dataset_current_version
            inner join dataset_version on dataset_current_version.version_id =dataset_version.id
            inner join dataset on dataset.id=dataset_version.dataset_id
            where dataset_current_version.dataset_id in %(dataset_ids)s """
    with get_db() as db:
        return db.query(sql, {"dataset_ids": dataset_ids})


def get_dataset_date_history_version_id(dataset_id: str, begin: str, end: str):
    """
    :param dataset_id:
    :param begin:
    :param end:
    :return:
    """

    sql = (
        "select id from dataset_version where dataset_id = %(dataset_id)s and version_type=%(version_type)s and "
        "created_on >= %(begin)s and created_on < %(end)s order by created_on DESC limit 1"
    )
    with get_db() as db:
        return db.query_scalar(
            sql,
            {"dataset_id": dataset_id, "version_type": DatasetVersionType.HISTORY.value, "begin": begin, "end": end},
        )


def get_dataset_last_history_version_id(dataset_id: str, before_current: int):
    sql = (
        "select id from dataset_version where dataset_id = %(dataset_id)s and version_type=%(version_type)s "
        "order by created_on DESC"
    )
    with get_db() as db:
        result = db.query(sql, {"dataset_id": dataset_id, "version_type": DatasetVersionType.HISTORY.value})
        length = len(result)
        begin = before_current - 1
        if not length or begin > length:
            return False
        return result[begin]["id"]


def get_dataset_release_version_id(dataset_id: str):
    sql = (
        "select id from dataset_version where dataset_id = %(dataset_id)s and version_type=%(version_type)s "
        "order by created_on DESC limit 1"
    )
    with get_db() as db:
        return db.query_scalar(sql, {"dataset_id": dataset_id, "version_type": DatasetVersionType.RELEASE.value})


def get_directly_dataset_current_version(flow_id: str):
    # 这里的子查询不知道为啥会比直接查询会快
    sql = """
    select * from (
        select id,`status`, end_time from instance  
        where flow_id = %(flow_id)s
    ) as abc where`status` = '已成功'  order by end_time desc limit 1
		
    """
    params = {'flow_id': flow_id}
    with get_db() as db:
        return db.query_one(sql, params)