# -*- coding: utf-8 -*-
"""
    数据集元数据缓存函数
    <NAME_EMAIL> on 2018/9/15.
"""
import copy
import json
import base64, gzip

from loguru import logger

from base import repository
from base.dmp_constant import DATASET_MULTI_FIELD, DATASET_LINK_DATA_META
from base.errors import DatasetVersionNotExistError
from base.enums import DatasetConnectType
from components import wait_lock
from components.log_setting import Debug
from dataset.cache import dataset_cache_service
from dataset.query.link_data import Jo<PERSON><PERSON><PERSON>, LinkData
from dataset.repositories import dataset_field_repository
from dataset.repositories import dataset_link_repository
from dataset.repositories import dataset_repository
from dmplib.hug import g
from dmplib.redis import conn as conn_redis
from dmplib.utils.errors import UserError
from components.analysis_time import AnalysisTimeUtils
from components.utils import timed_lru_cache

from datetime import datetime
from typing import Any, Dict, List, Union

debugger = Debug("dataset_meta_cache")


def get_dataset_versions(dataset_ids):
    """
    批量获取多个数据集的版本号
    :param dataset_ids:
    :return:
    """
    if not dataset_ids:
        return []

    cache_conn = conn_redis()
    versions = dataset_cache_service.DatasetCache.batch_mget(
        [dataset_cache_service.DatasetCache(g.code, ds_id, cache_conn) for ds_id in dataset_ids],
        ['version', 'data_version'],
        cache_conn,
    )
    return {dataset_ids[i]: {'version': v[0], 'data_version': v[1]} for i, v in enumerate(versions)}


def get_dataset_version(dataset_id, snap_id):
    """
    获取数据集版本id
    :param dataset_id:
    :param snap_id:
    :return:
    """
    return repository.get_data_scalar(
        "dataset_snapshot_relation", {"snap_id": snap_id, "dataset_id": dataset_id}, col_name="dataset_version_id"
    )


def get_dataset_cache(dataset_id: str, dataset_version_id: str = None) -> Dict[str, Union[str, None, datetime]]:
    """
    获取数据集缓存数据
    :param dataset_id:
    :param dataset_version_id:
    :return:
    """
    cache = conn_redis()
    dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
    data = dataset_cache.get_prop(dataset_cache.dataset_meta)
    if data:
        # TODO 零时方案
        version_day = getattr(g, "dataset_version_date", "")
        if version_day:
            from base.repository import get_value  # pylint: disable=C0415

            # 取当天的最后一个版本
            one = get_value(
                "dataset_version",
                {"dataset_id": data["id"], "created_on <": version_day + " 23:23:59", "status": "正常"},
                "table_name",
                "created_on desc",
            )
            if one:
                data['table_name'] = one
        debugger.log("使用数据集元数据缓存数据")
    else:
        data = dataset_repository.get_dataset(dataset_id)
        with wait_lock.WaitLocker("set_dataset_cache:%s" % dataset_id, 2) as locker:
            if data and locker.lock():
                data["data_version"] = data["version"]
                dataset_cache.set_prop(dataset_cache.dataset_meta, data)
    if not data:
        return data
    # 取历史版本(考虑到指标预警只会查询一次，然后保存结果，数据集历史版本数据集元数据不缓存)
    if data.get("connect_type") == DatasetConnectType.Directly.value:
        dataset_version_id = None
    else:
        if getattr(g, "snap_id", None):
            dataset_version_id = get_dataset_version(dataset_id, g.snap_id)
        # if not dataset_version_id:
        #     dataset_version_id = getattr(g, "dataset_version_id", "")
    if dataset_version_id:
        from base.repository import get_value  # pylint: disable=C0415

        version_data = get_value("dataset_version", {"dataset_id": dataset_id, "id": dataset_version_id}, "table_name")
        if not version_data:
            raise DatasetVersionNotExistError(
                message="数据集版本不存在，数据集ID：%s。数据集版本ID: %s" % (dataset_id, dataset_version_id)
            )

        data['table_name'] = version_data
        data["data_version"] = dataset_version_id
    return data


def set_dataset_cache(dataset_id: str, data: Dict[str, Union[str, None, datetime]]) -> None:
    """
    设置数据集数据缓存
     :param dataset_id:
    :param data:
    :return:
    """
    if data:
        with wait_lock.WaitLocker("set_dataset_cache:%s" % dataset_id, 2) as locker:
            if not locker.lock():
                raise UserError(code=403, message="操作太频繁")
            cache = conn_redis()
            dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
            dataset_cache.set_prop(dataset_cache.dataset_meta, data)


def del_dataset_cache(dataset_id):
    """
    删除数据集缓存
    :param dataset_id:
    :return:
    """
    cache = conn_redis()
    dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
    dataset_cache.del_prop(dataset_cache.dataset_meta)


def remove_dataset_cache(dataset_id):
    """
    删除数据集元数据所有属性缓存
    :return:
    """
    cache = conn_redis()
    dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
    dataset_cache.remove()


def get_dataset_field_cache(
    dataset_id: str, is_no_cache_query_db: bool = True
) -> List[Dict[str, Union[str, int, None]]]:
    """
    获取数据集字段缓存数据
    :param dataset_id:
    :param is_no_cache_query_db: 缓存没有数据，默认查询数据库数据
    :return:
    """
    AnalysisTimeUtils.recode_time_node('开始获取数数据集字段缓存')
    cache = conn_redis()
    dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
    data = dataset_cache.get_prop(dataset_cache.dataset_field_meta)
    data = cache_data_get_process(data, dataset_cache)
    AnalysisTimeUtils.recode_time_node('完成获取数数据集字段缓存')
    if data and not hasattr(g, "snap_id"):
        debugger.log("使用数据集字段元数据缓存数据")
        return data
    if not data and not is_no_cache_query_db:
        return []

    is_update_cache = True
    with wait_lock.WaitLocker("set_dataset_field_cache:%s" % dataset_id, 2) as locker:
        if not locker.lock():
            is_update_cache = False

    if hasattr(g, "snap_id"):
        is_update_cache = False

    dataset_field_data = dataset_field_repository.get_dataset_field(dataset_id)
    if is_update_cache:
        cache_data = cache_data_set_process(dataset_field_data)
        set_dataset_field_cache(dataset_id, cache_data, is_lock=False)
    AnalysisTimeUtils.recode_time_node('完成获取数数据集字段缓存更新')
    return dataset_field_data


def cache_data_get_process(data, dataset_cache):
    # 这里的缓存改动背景：
    # 1. 原始的是将整个数据集字段信息缓存到redis中，然后会占用大量内存，取出也会特别慢
    # 2. 改动会数据压缩后存入，取出再解压
    # 3. 切换的过程键兼容旧数据，检查是旧的缓存数据，自动将旧数据删除，重新设置新的缓存
    if not data:
        return data

    if isinstance(data, str) and data.startswith('H4sI'):
        # 是gzip数据，新方案的数据
        data = cache_data_decompress(data)
        data = copy.deepcopy(data)  # 缓存的对象是一个可变对象，后续的操作可能会缓存的对象进行操作，这里直接复制一个出来
        return data
    else:
        # 旧的缓存数据
        # 将旧的数据删除
        dataset_cache.del_prop(dataset_cache.dataset_field_meta)
        return data


@timed_lru_cache(seconds=1*60*60, maxsize=15)
def cache_data_decompress(data):
    try:
        b64_data = base64.b64decode(data)
        g_data = gzip.decompress(b64_data)
        return json.loads(g_data.decode("utf-8"))
    except Exception as e:
        logger.error(f'解析数据集字段缓存失败： {str(e)}, data: {data}')
        return []


def cache_data_set_process(data):
    # 新的缓存数据处理
    g_data = gzip.compress(json.dumps(data).encode())
    b64_data = base64.b64encode(g_data)
    return b64_data.decode()


def set_dataset_field_cache(
    dataset_id: str, data: str, is_lock: bool = True
) -> None:
    """
    设置数据集字段数据缓存
     :param dataset_id:
    :param data:
    :param is_lock: 是否加入redis锁
    :return:
    """
    if data:
        if is_lock:
            with wait_lock.WaitLocker("set_dataset_field_cache:%s" % dataset_id, 2) as locker:
                if not locker.lock():
                    raise UserError(code=403, message="操作太频繁")
        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
        dataset_cache.set_prop(dataset_cache.dataset_field_meta, data)


def del_dataset_field_cache(dataset_id):
    """
    删除数据集字段缓存
    :param dataset_id:
    :return:
    """
    cache = conn_redis()
    dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
    dataset_cache.del_prop(dataset_cache.dataset_field_meta)


def get_advance_filed_dataset_real_name_cache(dataset_id):
    cache = conn_redis()
    key = 'advance_filed_info:%s:%s' % (dataset_id, g.code)
    val = cache.get(key)
    if val:
        if isinstance(val, bytes):
            val = val.decode()
        return json.loads(val)
    return val


def set_advance_filed_dataset_real_name_cache(dataset_id, val):
    cache = conn_redis()
    key = 'advance_filed_info:%s:%s' % (dataset_id, g.code)
    return cache.set(key, json.dumps(val), time=30)


def get_multi_dataset_field_cache(dataset_field_ids: List[str]) -> List[Dict[str, Union[str, int, None]]]:
    """
    获取多数据集字段缓存数据
    :param dataset_field_ids:
    :return:
    """
    cache = conn_redis()
    multi_dataset_field_cache = dataset_cache_service.DatasetCache(g.code, DATASET_MULTI_FIELD, cache)
    data = multi_dataset_field_cache.hmget(dataset_field_ids)

    result = []
    query_ids = []
    for i, dataset_field_cache in enumerate(data):
        if not dataset_field_cache:
            query_ids.append(dataset_field_ids[i])
        else:
            result.append(dataset_field_cache)

    debugger.log({"使用多数据集字段元数据缓存数据": {"cached_fields": result, "query_ids": query_ids}})

    if query_ids:
        dataset_field_data = dataset_field_repository.get_dataset_field_by_ids(query_ids)
        set_multi_dataset_field_cache(dataset_field_data)
        result.extend(dataset_field_data)
    return result


def set_multi_dataset_field_cache(data: List[Dict[str, Union[str, int, None]]]) -> None:
    """
    设置多数据集字段数据缓存
    :param data:
    :return:
    """
    if data:
        cache_data = {}
        for dataset_field in data:
            cache_data[dataset_field.get("id")] = dataset_field

        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, DATASET_MULTI_FIELD, cache)
        dataset_cache.set_mprop(cache_data)


def del_multi_dataset_field_cache(dataset_field_ids):
    """
    删除多数据集字段缓存
    :param dataset_field_ids:
    :return:
    """
    if dataset_field_ids:
        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, DATASET_MULTI_FIELD, cache)
        for dataset_field_id in dataset_field_ids:
            dataset_cache.del_prop(dataset_field_id)


def transform_link(link_datas):
    if not link_datas:
        return []
    new_link_datas = []
    for link_data in link_datas:
        join_fields = []
        if link_data.get('join_fields'):
            for join_field in json.loads(link_data.get('join_fields')):
                join_fields.append(JoinField(**join_field))
        link_data['join_fields'] = join_fields
        new_link_datas.append(LinkData(**link_data))
    return new_link_datas


def get_dataset_link_data_cache(dataset_id):
    """
    获取数据集关联缓存数据
    :param dataset_id:
    :return:
    """
    cache = conn_redis()
    dataset_link_data_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
    data = dataset_link_data_cache.get_prop(DATASET_LINK_DATA_META)

    if data:
        debugger.log("使用数据集关联缓存数据")
        return transform_link(data)
    else:
        link_datas = dataset_link_repository.get_link_datas(dataset_id)
        set_dataset_link_data_cache(dataset_id, link_datas)
        return transform_link(link_datas)


def set_dataset_link_data_cache(dataset_id, data):
    """
    设置数据集关联数据缓存
    :param dataset_id:
    :param data:
    :return:
    """
    if data:
        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
        dataset_cache.set_prop(DATASET_LINK_DATA_META, data)


def del_dataset_link_data_cache(dataset_id):
    """
    删除数据集关联缓存
    :param dataset_id:
    :return:
    """
    if dataset_id:
        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
        dataset_cache.del_prop(DATASET_LINK_DATA_META)


def get_dataset_filter_cache(data_id: str) -> List[Any]:
    """
    获取数据集/外部主题过滤器缓存数据
    :param data_id:
    :return:
    """
    debugger.log("获取数据集过滤器缓存数据")
    cache = conn_redis()
    dataset_filter_cache = dataset_cache_service.DatasetCache(g.code, data_id, cache)
    data = dataset_filter_cache.get_prop(dataset_cache_service.DatasetCache.DATASET_FILTER_META)

    if data is not None:
        debugger.log("使用数据集过滤器缓存数据")
        return data
    else:
        debugger.log("获取不到数据集过滤器缓存数据，重新查询DB并写入缓存")
        dataset_filter = repository.get_data('dataset_filter', {"dataset_id": data_id}, multi_row=True)
        dataset_filter_cache.set_prop(dataset_cache_service.DatasetCache.DATASET_FILTER_META, dataset_filter)
        return dataset_filter


def del_dataset_filter_cache(dataset_id):
    """
    删除数据集过滤器缓存
    :param dataset_id:
    :return:
    """
    if dataset_id:
        cache = conn_redis()
        dataset_cache = dataset_cache_service.DatasetCache(g.code, dataset_id, cache)
        dataset_cache.del_prop(dataset_cache_service.DatasetCache.DATASET_FILTER_META)
