#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    <NAME_EMAIL> on 2017/3/25.
"""
import json
from typing import Optional, List, Union

from base.models import BaseModel, QueryBaseModel
from base import repository
from flow.models import FlowModel
from base.enums import (
    DatasetType,
    FlowType,
    DatasetVarVarType,
    DatasetVarValueType,
    DatasetVarAccurateType,
)
from dmplib.utils.errors import UserError


class DatasetModel(BaseModel):
    __slots__ = [
        "id",
        "name",
        "table_name",
        "icon",
        "parent_id",
        "type",
        "user_group_id",
        "description",
        "content",
        "level_code",
        "flow",
        "connect_type",
        "edit_mode",
        "relation_content",
        "filter_content",
        "var_content",
        "use_cache",
        "cache_flow_id",
        "cache_flow",
        "clear_time",
        "is_complex",
        "design",
        "disable_procedure",
        "external_type",
        "external_id",
        "origin_dim_type",
        "is_import_table",
        "import_table_name",
        "import_table_type"
    ]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.name = None
        self.table_name = None
        self.parent_id = None
        self.type = None
        self.description = None
        self.icon = None
        self.content = None  # sql 表达式等
        self.level_code = None
        self.user_group_id = None
        self.user_group_ids = []
        # 连接方式，直连和数据落地两种方式
        self.connect_type = None
        self.flow = DatasetFlowModel()
        self.field = DatasetFieldModel()
        # 运行模式，目前有SQL编写模式和视图模式
        self.edit_mode = None
        # 视图模式下，结构化的数据
        self.relation_content = None
        self.filter_content = None
        self.var_content = []
        self.is_complex = 0
        # 直连缓存模式
        self.use_cache = 0
        self.cache_flow_id = ''
        self.clear_time = 30
        self.cache_flow = DatasetCacheFlowModel()
        # 一个数据集支持多种sql
        self.design = []
        self.disable_procedure = 0
        self.is_need_procedure = 2
        self.external_id = ''
        self.origin_dim_type = ''
        self.external_type = ''
        self.is_import_table = 0
        self.import_table_name = None
        self.import_table_type = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append(("name", "string", {"max": 254}))
        rules.append(("type", "in_range", {"range": [e.value for e in DatasetType.__members__.values()]}))
        rules.append(("flow",))
        rules.append(("field",))

        # sql 数据集需要校验是否有 data_source_id和 sql 两个字段
        if self.type == DatasetType.Sql.value:
            if not self.content:
                raise UserError(message="sql数据集必须要有sql语句")
            if "data_source_id" not in self.content:
                raise UserError(message="sql数据集不能缺少数据源")
            if "sql" not in self.content and not "subject_id" in self.content:
                raise UserError(message="sql数据集不能缺少sql表达式")
        elif self.type == DatasetType.Union.value:
            if "sql" not in self.content:
                raise UserError(message="组合数据集不能缺少sql表达式")
        elif self.type == DatasetType.Excel.value:
            if "file_name" not in self.content:
                raise UserError(message="excel数据集不能缺少文件名")
            if "oss_url" not in self.content:
                raise UserError(message="excel数据集不能缺少oss_url")

        # 数据集名称暂不支持 ' 和 \
        if self.name.find("'") != -1 or self.name.find("\\") != -1:
            raise UserError(message="数据集名称不能包含 ‘ 和\\ 、/ 等特殊字符")
        return rules


class DatasetFlowModel(FlowModel):
    def __init__(self, **kwargs) -> None:
        """
        数据集流程
        :param kwargs:
        """
        super().__init__(**kwargs)
        self.type = FlowType.Dataset.value


class DatasetCacheFlowModel(FlowModel):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.type = FlowType.DatasetCache.value


class DatasetFieldModel(BaseModel):
    __slots__ = [
        "id",
        "dataset_id",
        "alias_name",
        "rank",
        "col_name",
        "origin_col_name",
        "data_type",
        "visible",
        "field_group",
        "format",
        "type",
        "expression",
        "expression_advance",
        "inspection_rules",
        "group_type",
        "note",
        "external_id",
        "origin_dim_type",
    ]

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.dataset_id = None
        self.alias_name = None
        self.col_name = None
        self.origin_col_name = None
        self.data_type = None
        self.visible = None
        self.field_group = None
        self.rank = None
        self.format = None
        self.type = None
        self.expression = None
        self.expression_advance = None
        self.inspection_rules = None
        self.note = None
        self.group_type = None
        self.external_id = None
        self.origin_dim_type = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("dataset_id", "string", {"max": 36}))
        return rules


class DatasetExcelResultModel(BaseModel):
    __slots__ = ["task_id", "name", "status", "error_msg", "info", "count", "invalid_count"]

    def __init__(self, **kwargs):
        self.task_id = ""
        self.name = ""
        self.status = 0
        self.error_msg = ""
        self.info = []
        self.count = 0
        self.invalid_count = 0
        super().__init__(**kwargs)


class DatasetExceldataModel(BaseModel):
    __slots__ = [
        'id',
        'sheet_name',
        'data',
        'head',
        'field',
        'count',
        'invalid_line',
        'invalid_count',
        'status',
        'error_msg',
        'create_table_sql',
        'tmp_table_name',
        'check_results',
    ]

    def __init__(self, **kwargs):
        self.id = ""
        self.sheet_name = ""
        self.data = []
        self.head = []
        self.field = []
        self.count = 0
        self.invalid_line = 0
        self.invalid_count = 0
        self.status = 0
        self.error_msg = ""
        self.create_table_sql = ""
        self.tmp_table_name = ""
        self.check_results = []
        super().__init__(**kwargs)


class DatasetExcelExportModel(BaseModel):
    __slots__ = ["task_id", "name", "status", "error_msg", "oss_url"]

    def __init__(self, **kwargs):
        self.task_id = ""
        self.name = ""
        self.status = 0
        self.error_msg = ""
        self.oss_url = ""
        super().__init__(**kwargs)


class DatasetApiContentModel(BaseModel):
    __slots__ = ["data_source_id", "table_name", "count", "relation", "params", "sql"]

    def __init__(self, **kwargs):
        self.data_source_id = None
        self.table_name = None
        self.count = None
        self.relation = None
        self.params = []
        self.sql = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("data_source_id",))
        rules.append(("table_name",))
        return rules


class DatasetExcelContentModel(BaseModel):
    __slots__ = ["oss_url", "file_name", "sheet_name", "select_sheets"]

    def __init__(self, **kwargs):
        self.oss_url = None
        self.file_name = None
        self.sheet_name = None
        self.select_sheets = []
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("oss_url",))
        rules.append(("file_name",))
        return rules


class DatasetOperateRecordModel(BaseModel):
    __slots__ = ["id", "dataset_id", "name", "operating_mode", "content", "instance_id", "run_status"]

    def __init__(self, **kwargs):
        self.id = None
        self.dataset_id = None
        self.name = None
        self.operating_mode = None
        self.content = None
        self.instance_id = None
        self.run_status = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("dataset_id",))
        return rules


class OperateRecordQueryModel(QueryBaseModel):
    __slots__ = ["type"]

    def __init__(self, **kwargs):
        # 数据集类型
        self.type = None
        # 数据集ID
        self.dataset_id = None
        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(("dataset_id",))
        rules.append(
            ("type", "in_range", {"range": [e.value for e in DatasetType.__members__.values()], "required": False})
        )
        return rules


class DatasetVersionModel(BaseModel):
    __slots__ = [
        "id",
        "dataset_id",
        "version_number",
        "version_name",
        "table_name",
        "content",
        "field_struct",
        "data_source_name",
        "dataset_name",
        "dataset_content",
        "type",
        "status",
        "inspection_id",
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.dataset_id = None
        self.version_number = None
        self.version_name = None
        self.table_name = None
        self.content = None
        self.field_struct = None
        self.data_source_name = None
        self.dataset_name = None
        self.type = None
        self.dataset_content = None
        self.status = None
        self.inspection_id = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append(("version_name", "string", {"max": 254}))
        rules.append(("dataset_id",))
        return rules


class VsersionQueryModel(QueryBaseModel):
    __slots__ = ["dataset_id", "type"]

    def __init__(self, **kwargs):
        # 数据集ID
        self.dataset_id = None
        # 数据集类型
        self.type = None
        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(("dataset_id",))
        types = DatasetType.__members__.values()
        # 排除api数据集
        types.remove(DatasetType.Api.value)
        # 排除外部主题数据集
        types.remove(DatasetType.ExternalSubject.value)
        rules.append(("type", "in_range", {"range": [e.value for e in types], "required": False}))
        return rules


class DatasetDataModel(BaseModel):
    __slots__ = [
        "dataset_id",
        "result_data",
        "column_struct",
        "total_count",
        "tmp_table_name",
        "new_column_struct",
        "create_table_sql",
        "field_comments",
        "replace_sql",
        "source_dataset_ids",
        "is_complex",
        "bind_source_id",
        "running_way"
    ]

    def __init__(self, **kwargs):
        self.dataset_id = None
        self.result_data = None
        self.column_struct = None
        self.total_count = None
        self.tmp_table_name = None
        self.new_column_struct = None
        self.create_table_sql = None
        # 组合数据集的特殊属性
        self.replace_sql = None
        self.source_dataset_ids = None
        # 字段备注
        self.field_comments = None
        # 默认简单sql
        self.is_complex = 1
        self.bind_source_id = None
        self.is_need_procedure = 2
        self.running_way = None
        super().__init__(**kwargs)


class DatasetIndexDataModel(BaseModel):
    __slots__ = ["id", "dataset_id", "index_name", "column_list"]

    def __init__(self, **kwargs):
        self.id = None
        self.dataset_id = None
        self.index_name = None
        self.column_list = None
        super().__init__(**kwargs)


class DatasetGroupFieldModel(BaseModel):
    __slots__ = [
        "id",
        "dataset_id",
        "origin_dataset_field_id",
        "origin_col_name",
        "origin_table_name",
        "alias_name",
        "group_type",
        "origin_data_type",
        "origin_table_alias_name",
        "expression",
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.dataset_id = None
        self.origin_dataset_field_id = None
        self.origin_col_name = None
        self.origin_table_name = None
        self.alias_name = None
        self.group_type = None
        self.expression = None
        self.origin_data_type = None
        self.origin_table_alias_name = None
        super().__init__(**kwargs)


class DatasetVarModel(BaseModel):
    """
    数据集变量model
    """

    __slots__ = [
        "id",
        "name",
        "description",
        "var_type",
        "value_type",
        "dataset_id",
        "default_value",
        "default_value_type",
        "value_source",
        "value_identifier",
        "accurate_type",
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.description = None
        self.var_type = None
        self.value_type = None
        self.dataset_id = None
        self.default_value = None
        self.default_value_type = None
        self.value_source = None
        self.value_identifier = None
        self.accurate_type = 0
        super().__init__(**kwargs)

    def rules(self):
        """
        校验规则
        :return list:
        """
        rules = super().rules()
        rules.append(("id", "string", {"max": 36, "required": False}))
        rules.append(("default_value", "string", {"required": False}))
        rules.append(("dataset_id", "string", {"max": 36}))
        rules.append(("name", "string", {"max": 100}))
        rules.append(("var_type", "in_range", {"range": [e.value for e in DatasetVarVarType.__members__.values()]}))
        rules.append(("value_type", "in_range", {"range": [e.value for e in DatasetVarValueType.__members__.values()]}))
        # value_source以及value_identifier字段弃用，无需校验
        # rules.append(
        #     ("value_source", "in_range", {"range": [e.value for e in DatasetVarValueSource.__members__.values()]})
        # )
        # rules.append(("value_source", "string", {"max": 100}))
        # if self.value_source != DatasetVarValueSource.Userdefined.value and not self.value_identifier:
        #     raise UserError(message="非用户自定义类型变量，请指定变量标识符!")
        same_name_datasetvar = repository.get_one("dataset_vars", {"dataset_id": self.dataset_id, "name": self.name})
        if same_name_datasetvar:
            word = "同一个数据集变量名称不能重复!"
            if not self.id or same_name_datasetvar.get("id") != self.id:
                raise UserError(message=word)
        rules.append(
            ("accurate_type", "in_range", {"range": [e.value for e in DatasetVarAccurateType.__members__.values()]})
        )


class DatasetFieldCompareResultModel(BaseModel):
    __slots__ = [
        'compare_type',
        'compare_subtype',
        'object_id',
        'object_name',
        'child_object_name',
        'message',
        'impact_level',
        'field_data',
        'object_type',
    ]

    def __init__(self, **kwargs):
        # 对比类型
        self.compare_type = None
        # 对比子类型
        self.compare_subtype = None
        # 对象ID
        self.object_id = None
        # 影响对象名称
        self.object_name = None
        # 影响对象的类型
        self.object_type = None
        # 影响子对象名称
        self.child_object_name = None
        # 对比结果
        self.message = None
        # 影响级别
        self.impact_level = 0
        # 字段数据
        self.field_data = {}

        super().__init__(**kwargs)


class DatasetSubjectModel(BaseModel):
    """
    数据集主题表
    """

    __slots__ = ['id', 'name', 'version', 'dataset_folder_id', 'file_url', 'file_type', 'update_on']

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.version = None
        self.dataset_folder_id = None
        self.file_url = None
        self.file_type = None
        self.update_on = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("id", "string", {"max": 36}))
        rules.append(("name", "string", {"max": 254}))
        rules.append(("version", "string", {"max": 254}))
        rules.append(("dataset_folder_id", "string", {"max": 36, 'required': False}))
        rules.append(("file_url", "string", {"max": 1024}))
        rules.append(("file_type", "string", {"max": 64}))
        return rules


class DatasetSubjectTableModel(BaseModel):
    """
    数据集主题表版本数据
    """

    __slots__ = [
        'id',
        'dataset_subject_id',
        'table_name',
        'description',
        'meta_columns',
        'preview_data',
        'records',
        'dataset_subject_version',
        'dataset_id',
        'dataset_meta_columns',
    ]

    def __init__(self, **kwargs):
        self.id = None
        self.dataset_subject_id = None
        self.table_name = None
        self.description = None
        self.meta_columns = None
        self.preview_data = None
        self.records = None
        self.dataset_subject_version = None
        self.dataset_id = None
        self.dataset_meta_columns = ''
        super().__init__(**kwargs)


class DatasetSubjectTableResultModel(BaseModel):
    """
    获取主题数据表预览数据
    """

    __slots__ = [
        'id',
        'data',
        'head',
        'field',
        'count',
        'create_table_sql',
        'tmp_table_name',
        'description',
        'origin_table_name',
        'dataset_id',
    ]

    def __init__(self, **kwargs):
        self.id = ""
        self.data = []
        self.head = []
        self.field = []
        self.count = 0
        self.create_table_sql = ""
        self.tmp_table_name = ""
        self.description = None
        self.origin_table_name = None
        self.dataset_id = None
        super().__init__(**kwargs)


class DatasetInspectionQueryModel(QueryBaseModel):
    __slots__ = ['name', 'status', 'begin_date', 'end_date', 'data_source_type']

    def __init__(self, **kwargs):
        # TODO Del
        self.name = None
        self.status = None
        self.begin_date = None
        self.end_date = None
        self.data_source_type = None
        super(DatasetInspectionQueryModel, self).__init__(**kwargs)


class DatasetExternalSpaceModel(BaseModel):
    __slots__ = ["id", "name", "description", "created_on"]

    def __init__(self, **kwargs):
        self.id = None
        self.name = None
        self.description = None
        self.created_on = None
        super().__init__(**kwargs)


class DatasetExternalSubjectModel(BaseModel):
    __slots__ = [
        "id",
        "name",
        "external_space_id",
        "description",
        "tables",
        "relations",
        "table_fields",
        "table_field_relations",
        "table_code_set",
        "created_on",
        "type"
    ]

    def __init__(self, **kwargs):
        self.id = ""
        self.name = ""
        self.external_space_id = ""
        self.description = ""
        self.tables = ""
        self.relations = ""
        self.table_fields = ""
        self.table_field_relations = ""
        self.table_code_set = ""  # 字符串表示的mysql set类型, 以逗号分隔的表的code
        self.created_on = None
        self.type = None
        super().__init__(**kwargs)

    @staticmethod
    def convert_from_db(db_subject):
        subject = DatasetExternalSubjectModel(**db_subject)
        subject.tables = json.loads(subject.tables)
        subject.relations = json.loads(subject.relations)
        subject.table_fields = json.loads(subject.table_fields)
        subject.table_field_relations = (
            json.loads(subject.table_field_relations) if subject.table_field_relations else {}
        )

        return subject

    def get_dws_table(self):
        return next((table for table in self.tables if table.get('code') == self.id), None)

    def get_dws_table_name(self):
        dws_table = self.get_dws_table() or {}
        return dws_table.get('table_name')

    def get_dws_table_code(self):
        dws_table = self.get_dws_table() or {}
        return dws_table.get('code')

    def get_table_fields(self, table_name):
        return next(
            (
                table_field.get('field_list')
                for table_field in self.table_fields
                if table_field.get('table_name') == table_name
            ),
            None,
        )

    def get_tables(self):
        return [
            table
            for table in self.tables
            if (table.get('category') == 'dim') or (table.get('category') == 'dws' and table.get('code') == self.id)
        ]


class DatasetPermissionModel(BaseModel):
    __slots__ = ['id', 'name', 'dataset_id', 'user_code_field_id', 'permission_associated_field_id', 'type', 'remark']

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.name = None
        self.dataset_id = None
        self.user_code_field_id = None
        self.permission_associated_field_id = None
        self.type = None
        self.remark = None

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36, 'required': False}))
        rules.append(('name', 'string', {'max': 100, 'required': True}))
        rules.append(('type', 'string', {'max': 254, 'required': False}))
        rules.append(('dataset_id', 'string', {'max': 36, 'required': True}))
        rules.append(('user_code_field_id', 'string', {'max': 36, 'required': True}))
        rules.append(('permission_associated_field_id', 'string', {'max': 36, 'required': True}))
        return rules


class DatasetTreeAdaptHDModel(BaseModel):
    __slots__ = ['action_name', 'datasetScope', 'excludeDataSetType', 'includeDataSetType', 'DataSetGUID']

    def __init__(self, **kwargs) -> None:
        self.action_name = None
        self.datasetScope = None
        self.excludeDataSetType = None
        self.includeDataSetType = None
        self.DataSetGUID = None

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(
            ('action_name', 'in_range', {'range': ['Dataset/SelectDataset', 'Dataset/GetColumns'], 'required': True})
        )
        rules.append(('datasetScope', 'string', {'required': False}))
        # rules.append(('excludeDataSetType', 'array'))
        # rules.append(('includeDataSetType', 'array'))
        return rules


class DatasetFieldGroupModel(BaseModel):
    __table__ = 'dataset_field_group'
    __slots__ = ['id', 'dataset_id', 'group_name', 'parent_id', 'group_type', 'sort']

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.dataset_id = None
        self.group_name = None
        self.parent_id = ''
        self.group_type = 1
        self.sort = 1

        super().__init__(**kwargs)

    def check_group_name(self):
        if self.group_name:
            self.group_name = str(self.group_name).strip()

    def rules(self):
        self.check_group_name()
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36, 'required': False}))
        rules.append(('dataset_id', 'string', {'max': 36, 'required': True}))
        rules.append(('group_name', 'string', {'max': 50, 'required': True}))
        rules.append(("group_type", "in_range", {"range": [1, 2]}))
        same_name = repository.get_one(self.__table__, {'group_name': self.group_name, 'dataset_id': self.dataset_id})
        if same_name:
            word = "分组名称不能重复"
            if not self.id or same_name.get("id") != self.id:
                raise UserError(message=word)
        return rules


class DatasetFieldGroupRelationModel(BaseModel):
    __table__ = 'dataset_field_group_relation'
    __slots__ = ['id', 'group_id', 'field_id', 'sort']

    def __init__(self, **kwargs) -> None:
        self.id = None
        self.group_id = None
        self.field_id = None
        self.dataset_id = None
        self.sort = 1

        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36, 'required': False}))
        rules.append(('group_id', 'string', {'max': 36, 'required': True}))
        rules.append(('field_id', 'string', {'max': 36, 'required': True}))
        rules.append(('dataset_id', 'string', {'max': 36, 'required': True}))


class PulsarDatasetParamsModel(BaseModel):

    __slots__ = ['projectCode', 'token', 'limit', 'apikey']

    def __init__(self, **kwargs):
        self.projectCode = None
        self.token = 0
        self.limit = 100
        self.apikey = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('projectCode', 'string', {'required': True}))
        rules.append(('apikey', 'string', {'required': True}))
        rules.append(('token', 'int', {'required': True}))
        rules.append(('limit', 'int', {'required': True}))


class PulsarDashParamsModel(BaseModel):
    __slots__ = ['projectCode', 'token', 'limit', 'apikey']

    def __init__(self, **kwargs):
        self.projectCode = None
        self.token = None
        self.limit = 100
        self.apikey = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('projectCode', 'string', {'required': True}))
        rules.append(('apikey', 'string', {'required': True}))
        rules.append(('token', 'int', {'required': True}))
        rules.append(('limit', 'int', {'required': True}))


class DetailModelField(BaseModel):
    description: Optional[str]
    """类别:indicator-指标,dimension-维度,description-描述"""
    dim_type: str
    field_type: Optional[str]
    name: Optional[str]
    name_cn: Optional[str]
    parent_field: Optional[str]

    def __init__(self, **kwargs) -> None:
        self.description = None
        self.dim_type = None
        self.field_type = None
        self.name = None
        self.name_cn = None
        self.parent_field = None
        super().__init__(**kwargs)


class MultiModelNode(BaseModel):
    """模型类型"""
    category: Union[str, None]
    """模型code"""
    code: Union[str, None]

    def __init__(self, **kwargs) -> None:
        self.category = None
        self.code = None
        super().__init__(**kwargs)


class DetailModelData(BaseModel):
    """模型类型:dim-维度, dwd-明细事实, dws-汇总事实, multi_dim-多维"""
    category: str
    code: str
    """字段"""
    fields: Union[List[DetailModelField], None]
    """英文名"""
    name: str
    """多维模型结点(多维模型)"""
    nodes: Union[List[MultiModelNode], None]
    """主题域id"""
    subject_id: str
    """主题名"""
    subject_name: str
    """模型表名"""
    table_name: str

    def __init__(self, **kwargs) -> None:
        self.category = ''
        self.code = ''
        self.fields = None
        self.name = ''
        self.nodes = None
        self.subject_id = ''
        self.subject_name = ''
        self.table_name = ''
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(("category", "in_range", {"range": ['dim', 'dwd', 'dws', 'multi_dim']}))
