#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    <NAME_EMAIL> on 2018/4/13.
"""
import datetime
import json
import operator
import random
import re

from base import repository
from base.dmp_constant import DATASET_SQL_RESULT, DATASET_SQL_KEYS
from base.enums import (
    DatasetType,
    DatasetFieldType,
    OperateMode,
    DatasetConnectType,
    DatasetVersionType,
    DatasetVersionActionType,
    DatasetVersionNote,
    DatasetVersionStatus,
)
from data_source.services import data_source_service
from dataset.cache import dataset_meta_cache
from dataset.common.clean_dataset_version import CleanDatasetVersion
from dataset.models import DatasetVersionModel
from dataset.repositories import dataset_field_repository, dataset_subject_repository
from dataset.repositories import dataset_repository
from dataset.repositories import dataset_version_repository
from dataset.services.advanced_field_service import clean_dashboard_chart_cache
from dataset.services.dataset_service import get_operate_record_model
from dmplib import config
from dmplib.redis import conn as conn_redis
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.utils.logs import logger
from flow.services import flow_service
from rbac.services.data_permissions import data_permission_edit_filter

TABLE_NAME = "dataset_version"


def get_dataset_version(version_id):
    """
    获取数据集版本
    :param version_id
    :return tuple:
    """
    return dataset_version_repository.get_version(version_id)


def get_dataset_version_list(query_model):
    """
    获取数据集版本记录
    :param dataset.models.VsersionQueryModel query_model:
    :return tuple:
    """
    query_model = dataset_version_repository.get_version_list(query_model)
    return query_model


def get_dataset_version_no(
    dataset_id, version_action_type=DatasetVersionActionType.Auto.value, version_type=DatasetVersionType.HISTORY.value
):
    """
    根据数据集id获取版本号
    :param dataset_id:
    :return:
    """
    version_numbers = repository.get_data(
        TABLE_NAME,
        {'dataset_id': dataset_id, 'type': version_action_type, 'version_type': version_type},
        ['version_number'],
        multi_row=True,
    )
    return [version_number.get('version_number') for version_number in version_numbers] if version_numbers else []


def deal_redundant_data_version(dataset_id, auto=False):
    version_action_type = DatasetVersionActionType.Auto.value if auto else DatasetVersionActionType.Manual.value
    version_number_list = get_dataset_version_no(dataset_id, version_action_type=version_action_type)
    max_version_replications = (
        5
        if not config.get("DatasetConfig.max_version_replications")
        else config.get("DatasetConfig.max_version_replications")
    )
    try:
        max_version_replications = int(max_version_replications)
    except ValueError:
        raise UserError(message="最大版本数应该填写数字")
    if version_number_list and len(version_number_list) > max_version_replications:
        # 删除最老的版本
        delete_verison = repository.get_data(
            "dataset_version",
            {"type": auto, "dataset_id": dataset_id, "version_type": DatasetVersionType.HISTORY.value},
            order_by=[("created_on", "ASC")],
        )
        if delete_verison:
            repository.delete_data("dataset_version", {"id": delete_verison.get("id")})
            # 删除data
            dataset_version_repository.drop_table_data(delete_verison.get("table_name"))


def get_version_table_name(dataset_id, dataset_table_name, auto=False, snap=False):
    if snap:
        version_action_type = DatasetVersionActionType.Snap.value
    else:
        version_action_type = DatasetVersionActionType.Auto.value if auto else DatasetVersionActionType.Manual.value
    version_number_list = get_dataset_version_no(dataset_id, version_action_type=version_action_type)
    max_version_replications = (
        5
        if not config.get("DatasetConfig.max_version_replications")
        else config.get("DatasetConfig.max_version_replications")
    )
    if snap:
        max_version_replications = (
            24
            if not config.get("DatasetConfig.max_version_snap")
            else config.get("DatasetConfig.max_version_snap")
        )
    try:
        max_version_replications = int(max_version_replications)
    except ValueError:
        raise UserError(message="最大版本数应该填写数字")
    if version_number_list:
        if not snap and not auto and len(version_number_list) >= max_version_replications:
            raise UserError(message="手动备份数据集版本已经存在{}份，请删除老版本再新增版本。".format(max_version_replications))
        version_number = max(version_number_list) + 1
        # 手动版本数取决于配置，自动版本数固定是5个
        if len(version_number_list) >= max_version_replications:
            min_version_number = min(version_number_list)
            # 自动和手动的table_name命名规则不一样
            drop_table_name = "{table_name}_{version_number}".format(
                table_name=dataset_table_name, version_number=min_version_number
            )
            if auto:
                drop_table_name += "_auto"
            if snap:
                drop_table_name += "_snap"
            dataset_version_repository.drop_table_data(drop_table_name)
            repository.delete_data(
                TABLE_NAME,
                {'dataset_id': dataset_id, 'version_number': min_version_number, 'type': version_action_type},
            )
    else:
        version_number = 1

    version_table_name = "{table_name}_{version_number}".format(
        table_name=dataset_table_name, version_number=version_number
    )
    if auto:
        version_table_name += "_auto"
    if snap:
        version_table_name += "_snap"

    return version_number, version_table_name


def add_dataset_version_data(dataset_id, table_name, auto=False, version_type=DatasetVersionType.HISTORY.value):
    # 函数内部调用，生成前端一样的model
    model = DatasetVersionModel()
    model.id = seq_id()
    model.dataset_id = dataset_id
    model.table_name = table_name
    model.type = '自动'
    model.version_type = version_type
    model.version_name = datetime.datetime.now().strftime("%Y/%m/%d %p%l:%M:%S").replace("PM", "下午").replace("AM", "上午")
    return add_dataset_version(model, auto=auto)


@data_permission_edit_filter('dataset-edit')
def add_dataset_version(model, auto=False, version_type=DatasetVersionType.HISTORY.value, snap=False):
    """
    :添加数据集版本
    :param dataset.models.DatasetVersionModel model:
    :param auto:
    :param version_type:
    :param snap: 拍照
    :return :
    """
    if not hasattr(model, 'id') or (hasattr(model, 'id') and not model.id):
        model.id = seq_id()
    model.validate()

    dataset_data = repository.get_data(
        "dataset", {"id": model.dataset_id}, ["table_name", "content", "type", "connect_type", "id", "name"]
    )
    if not dataset_data:
        logger.error('数据集不存在,跳过拍照，id：{}'.format(model.dataset_id))
        return
    if dataset_data.get('connect_type') == DatasetConnectType.Directly.value:
        raise UserError(message='直连模式不支持添加数据集版本')

    from dataset.services.dataset_service import set_info_dataset
    set_info_dataset(dataset_data)

    dataset_table_name = dataset_data.get("table_name")
    if not dataset_table_name:
        if not auto:
            raise UserError(message="数据集未生成。id:{}".format(model.dataset_id))
        # 自动保存版本的时候不应该报错
        return

    # 获取版本号
    model.version_number, model.table_name = get_version_table_name(model.dataset_id, dataset_table_name, auto=auto, snap=snap)
    if not dataset_version_repository.table_exists(dataset_table_name, dataset_data.get('content')):
        if not auto:
            raise UserError(message="数据集数据未生成, id:{}".format(model.dataset_id))
        # 自动保存版本的时候不应该报错
        return
    # 获取数据集字段结构
    dataset_fields = dataset_field_repository.get_dataset_field(
        model.dataset_id, {"type": DatasetFieldType.Normal.value}
    )
    model.field_struct = json.dumps(dataset_fields)

    # 获取数据集源名称
    content = json.loads(dataset_data.get("content"))
    if dataset_data.get("type") in [DatasetType.Sql.value, DatasetType.Api.value]:
        data_source_model = data_source_service.get_data_source(content.get('data_source_id'))
        data_source_name = data_source_model.name
    elif dataset_data.get("type") == DatasetType.Excel.value:
        data_source_name = content.get("file_name")
    elif dataset_data.get("type") == DatasetType.Union.value:
        pattern = re.compile(r'\{([^\{\}]*)\}')
        dataset_names = pattern.findall(content.get("sql"))
        data_source_name = ','.join(dataset_names) if dataset_names else ''
    else:
        data_source_name = ''
    model.data_source_name = data_source_name
    # 添加dataset_content, content这个字段已经被当成描述使用了
    model.dataset_content = dataset_data.get('content')

    fields = [
        'id',
        'dataset_id',
        'version_number',
        'version_name',
        'table_name',
        'content',
        'field_struct',
        'data_source_name',
        'type',
        'version_type',
        'dataset_content',
        'status',
    ]

    # 查看当前正式版的状态
    release_version = repository.get_data(
        "dataset_version",
        {"dataset_id": model.dataset_id, "version_type": DatasetVersionType.RELEASE.value},
        fields=["status", "inspection_id"],
    )
    if release_version:
        model.status = release_version.get("status")
        fields.append("inspection_id")
        model.inspection_id = release_version.get("inspection_id")
    else:
        model.status = DatasetVersionStatus.Normal.value

    # 预发布版、正式版只保存一个
    if version_type in [DatasetVersionType.RC.value, DatasetVersionType.RELEASE.value] and repository.data_is_exists(
        "dataset_version", {"dataset_id": model.dataset_id, "version_type": version_type}
    ):
        repository.delete_data("dataset_version", {"dataset_id": model.dataset_id, "version_type": version_type})

    fields_dataset = dataset_field_repository.get_dataset_field(model.dataset_id)

    # 创建版本数据集表
    dataset_version_repository.create_table_struct(model.table_name, dataset_table_name)
    dataset_version_repository.create_table_data(model.table_name, dataset_table_name, dataset_data, fields_dataset)

    if auto:
        model.content = DatasetVersionNote.Update.value
        model.type = DatasetVersionActionType.Auto.value
    elif snap:
        model.type = DatasetVersionActionType.Snap.value
    else:
        model.type = DatasetVersionActionType.Manual.value

    # 数据集操作记录
    repository.add_data(
        'dataset_operate_record',
        get_operate_record_model(model.dataset_id, OperateMode.Save_version.value, model.data_source_name).get_dict(),
    )
    return repository.add_model(TABLE_NAME, model, fields)


@data_permission_edit_filter('dataset-edit')
def delete_dataset_version(dataset_id, version_id, table_name):
    """
    :删除数据集版本
    :return :
    """
    if not version_id:
        raise UserError(message="数据集版本ID不能为空。")
    if not table_name:
        raise UserError(message="数据集版本表名不能为空。")
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message="数据集不存在")
    if repository.data_is_exists(
        "dataset_version",
        {"id": version_id, "dataset_id": dataset_id, "version_type": DatasetVersionType.RELEASE.value},
    ):
        raise UserError(message="正式版不允许删除")
    dataset_version_repository.drop_table_data(table_name)
    repository.delete_data(TABLE_NAME, {'id': version_id})

    repository.add_data(
        'dataset_operate_record',
        get_operate_record_model(
            dataset_id, OperateMode.Delete_version.value, data_source=get_source_name(dataset)
        ).get_dict(),
    )


@data_permission_edit_filter('dataset-edit')
def replace_dataset_table(dataset_id, version_id):
    """
    替换数据集表
    :return :
    """
    from dataset.services.dataset_async_service import get_global_dataset_info

    if not dataset_id:
        raise UserError(message="数据集ID不能为空。")
    if not version_id:
        raise UserError(message="数据集版本ID不能为空。")

    # sql 直连模式下没有替换数据集功能
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message='数据集不存在')

    get_global_dataset_info(dataset)

    if dataset.get('connect_type') == DatasetConnectType.Directly.value:
        raise UserError(message='直连模式不支持替换数据集功能')

    dataset_table_name = repository.get_data_scalar("dataset", {"id": dataset_id}, "table_name")
    dataset_version = repository.get_data(
        "dataset_version", {"id": version_id}, fields=["id", "table_name", "version_type", "type", "version_number"]
    )
    if not dataset_version:
        raise UserError(message="版本数据已更新，请重新刷新页面再重试。")

    version_table_name = dataset_version.get("table_name")
    version_type = dataset_version.get("version_type") or DatasetVersionType.HISTORY.value

    if version_type == DatasetVersionType.RELEASE.value:
        raise UserError(message='当前使用版本为正式版本！')

    if not dataset_version_repository.table_exists(dataset_table_name, dataset.get('content')):
        raise UserError(message="版本数据已更新，请重新刷新页面再重试。")
    if not dataset_version_repository.table_exists(version_table_name, dataset.get('content')):
        raise UserError(message="版本数据已更新，请重新刷新页面再重试。")

    # 校验结构是否一致
    dataset_table_columns = dataset_version_repository.get_table_columns(dataset_table_name, dataset.get('content'))
    version_table_columns = dataset_version_repository.get_table_columns(version_table_name, dataset.get('content'))
    if not operator.__eq__(dataset_table_columns, version_table_columns):
        if repository.data_is_exists("dataset_subject_table", {"dataset_id": dataset_id}):
            compare_struct_update(dataset_id, version_id=version_id)
        else:
            raise UserError(code=412, message="数据集字段和版本字段不一致。")

    # 还原是将选定数据集版本切换成正式版本，原正式版本即为历史版本；
    replace_version_table(dataset_id, dataset_table_name, dataset_version, dataset)

    # 清空所有单图依赖数据集的缓存数据
    clean_dashboard_chart_cache(dataset_id)

    # 清空数据集查询结果数据缓存
    prefix = '{dmp}:{object_name}'.format(
        dmp=config.get('Cache.released_dashboard_metadata_cache_key', 'dmp'), object_name="dataset"
    )
    result_key = '{prefix}:{object_id}'.format(prefix=prefix, object_id=DATASET_SQL_RESULT + dataset_id)
    sql_key = '{prefix}:{object_id}'.format(prefix=prefix, object_id=DATASET_SQL_KEYS + dataset_id)
    cache = conn_redis()
    cache.del_data(result_key)
    cache.del_data(sql_key)
    # 清除缓存
    dataset_meta_cache.del_dataset_cache(dataset_id)

    # 触发依赖数据集流程
    trigger_depend_on_flow(dataset_id)

    repository.add_data(
        'dataset_operate_record',
        get_operate_record_model(
            dataset_id, OperateMode.Replace_version.value, data_source=get_source_name(dataset)
        ).get_dict(),
    )
    return dataset_id


def get_source_name(dataset: dict) -> str:
    # 获取数据集源名称, 不影响主流程
    try:
        content = json.loads(dataset.get("content"))
    except Exception:
        return ''
    if dataset.get("type") in [DatasetType.Sql.value, DatasetType.Api.value]:
        data_source_model = data_source_service.get_data_source(content.get('data_source_id'))
        data_source_name = data_source_model.name
    elif dataset.get("type") == DatasetType.Excel.value:
        data_source_name = content.get("file_name")
    elif dataset.get("type") == DatasetType.Union.value:
        pattern = re.compile(r'\{([^\{\}]*)\}')
        dataset_names = pattern.findall(content.get("sql"))
        data_source_name = ','.join(dataset_names) if dataset_names else ''
    else:
        data_source_name = ''
    return data_source_name


def trigger_depend_on_flow(dataset_id):
    # 这里跟flow的依赖调度有点不同，如果有问题的话，修改为调flow_service中send_depend_flow_message
    subject_dataset = dataset_subject_repository.is_subject_table(dataset_id)
    dataset_id = subject_dataset.get("id") if subject_dataset else dataset_id
    dataset_list = dataset_repository.get_dataset_relate_dataset(dataset_id)
    if dataset_list:
        for dataset in dataset_list:
            flow_service.run_flow(dataset.get("id"))


def compare_struct_update(dataset_id, version_id=None):
    # 避免相互引用的风险
    from dataset import external_query_service

    cited_fields = external_query_service.get_cited_fields_by_dataset(dataset_id)
    # 历史结构从dataset_field中取
    dataset_fields = dataset_field_repository.get_dataset_field(dataset_id, {"type": DatasetFieldType.Normal.value})
    # 应用版本，field_struct 从dataset_version中取，主题包更新从subject_table取
    if version_id:
        new_dataset_fields = get_fields_from_version(dataset_id, version_id)
    else:
        new_dataset_fields = get_fields_subject_table(dataset_id)

    rc_fields = {field.get("col_name"): field for field in new_dataset_fields}
    # 检测变更或删除字段是否有引用
    if cited_fields:
        results = inspect_structure(rc_fields, cited_fields, dataset_fields)
        # 变更的字段被引用，不允许应用当前版本
        if results:
            # todo 需要跟前端返回对应results
            raise UserError(message='变更或删除的字段被引用，不允许应用当前版本')
    # 更新dataset_field表
    update_subject_dataset_field(
        dataset_id, rc_fields, dataset_fields, new_dataset_fields, replace_version=bool(version_id)
    )
    # 删除数据集字段数据缓存
    dataset_meta_cache.del_dataset_field_cache(dataset_id)
    # 删除数据集多字段数据缓存
    dataset_meta_cache.del_multi_dataset_field_cache([dataset_field.get("id") for dataset_field in dataset_fields])
    return True


def inspect_structure(rc_fields, cited_fields, dataset_fields):
    results = []
    for field in dataset_fields:
        col_name = field.get("col_name")
        data_type = field.get("data_type")
        if col_name not in cited_fields:
            continue
        if rc_fields.get(col_name):
            if data_type == rc_fields.get(col_name).get("data_type"):
                continue

            result_data = {
                'col_name': col_name,
                'alias_name': field.get('alias_name'),
                'data_type': data_type,
                'type': '类型变更',
                'msg': '字段类型变更：{} -> {}'.format(data_type, rc_fields.get(col_name).get("data_type")),
            }
            results.append(result_data)
        else:
            result_data = {
                'col_name': col_name,
                'alias_name': field.get('alias_name'),
                'data_type': data_type,
                'type': '删除',
                'msg': '字段被删除',
            }
            results.append(result_data)
    return results


def update_subject_dataset_field(dataset_id, rc_fields, dataset_fields, new_dataset_fields, replace_version=False):
    for field in dataset_fields:
        col_name = field.get("col_name")
        data_type = field.get("data_type")

        if rc_fields.get(col_name):
            rc_field = rc_fields.get(col_name)
            if data_type == rc_field.get("data_type"):
                continue
            # 更新field type类型
            repository.update_data(
                "dataset_field",
                {"data_type": rc_field.get("data_type"), "field_group": rc_field.get("field_group")},
                {"col_name": col_name, "dataset_id": dataset_id},
            )
        else:
            # 删除类型
            repository.delete_data("dataset_field", {"col_name": col_name, "dataset_id": dataset_id})
    # 新增类型
    old_fields = {field.get("col_name"): field for field in dataset_fields}
    for field in new_dataset_fields:
        col_name = field.get("col_name")
        if not old_fields.get(col_name):
            # 如果是替换版本的时候，直接add
            if replace_version:
                repository.add_data("dataset_field", field)
                continue
            # 这里是主题包添加字段
            # 补充一些必要字段
            _field = dict()
            _field["id"] = seq_id()
            _field["dataset_id"] = dataset_id
            _field["col_name"] = col_name
            _field["data_type"] = field.get("data_type")
            _field["alias_name"] = field.get("alias_name")
            _field["rank"] = get_max_rank(dataset_id) + 1
            _field["visible"] = 1
            _field["type"] = DatasetFieldType.Normal.value
            _field["field_group"] = field.get("field_group")
            repository.add_data("dataset_field", _field)


def get_max_rank(dataset_id):
    # 这里用本来使用max(rank) 获取最大 rank,使用的这个库似乎不支持，只能手动取最大
    results = repository.get_data("dataset_field", {"dataset_id": dataset_id}, ["rank"], multi_row=True)
    return max([_.get("rank") for _ in results]) or 0


def get_fields_from_version(dataset_id, version_id):
    result = (
        repository.get_data("dataset_version", {"dataset_id": dataset_id, "id": version_id}, ["field_struct"]) or {}
    )
    if result:
        field_struct = json.loads(result.get("field_struct"))
        return field_struct
    return []


def get_fields_subject_table(dataset_id):
    result = repository.get_data("dataset_subject_table", {"dataset_id": dataset_id}, ["dataset_meta_columns"]) or {}
    if result:
        dataset_meta_columns = json.loads(result.get("dataset_meta_columns"))
        return dataset_meta_columns
    return []


def replace_version_table(dataset_id, dataset_table_name, dataset_version, dataset=None):
    dataset_fields = dataset_field_repository.get_dataset_field(dataset_id)
    version_id = dataset_version.get("id")
    version_table_name = dataset_version.get("table_name")
    replace_version_type = dataset_version.get("version_type") or DatasetVersionType.HISTORY.value
    # 应用版本时，dataset_version和dataset_current_version中记录变更
    table_tmp = version_table_name + "_tmp_" + str(random.randint(1, 99))
    # 数据集历史版本 切换成 临时版本
    dataset_version_repository.rename_table(version_table_name, table_tmp, dataset, dataset_fields)
    # 数据集正式版   切换成 数据集历史版本
    dataset_version_repository.rename_table(dataset_table_name, version_table_name, dataset, dataset_fields)
    # 数据集临时版本 切换成 数据集正式版
    dataset_version_repository.rename_table(table_tmp, dataset_table_name, dataset, dataset_fields)

    version_number = dataset_version.get("version_number")

    # 切换到预发布版本的时候，需要生成table_name
    if replace_version_type == DatasetVersionType.RC.value:
        # # 应产品需求，把预发布应用为正式时，需要备份一份原来的预发布版本
        # 创建版本数据集表
        rc_table_name = dataset_table_name + "_rc"
        dataset_version_repository.create_table_struct(rc_table_name, dataset_table_name)
        dataset_version_repository.create_table_data(rc_table_name, dataset_table_name, dataset, dataset_fields)

        old_rc_version = repository.get_data(
            "dataset_version",
            {"id": version_id},
            fields=[
                "dataset_id",
                "version_number",
                "version_name",
                "type",
                "version_type",
                "status",
                "inspection_id",
                "content",
                "dataset_content",
                "field_struct",
                "data_source_name",
                "created_on",
            ],
        )
        old_rc_version["id"] = seq_id()
        old_rc_version["table_name"] = rc_table_name
        # 防止后面被删除
        old_rc_version["version_number"] += 1
        repository.add_data("dataset_version", old_rc_version)

        version_number, new_version_table_name = get_version_table_name(
            dataset_id,
            dataset_table_name,
            auto=True if dataset_version.get("type") == DatasetVersionActionType.Auto.value else False,
        )
        dataset_version_repository.rename_table(version_table_name, new_version_table_name, dataset, dataset_fields)
        version_table_name = new_version_table_name

    # 是否存在正式版版
    if repository.data_is_exists(
        "dataset_version", {"dataset_id": dataset_id, "version_type": DatasetVersionType.RELEASE.value}
    ):
        # 正式版切换成历史版本，并更改version_number
        repository.update_data(
            "dataset_version",
            {
                "version_type": DatasetVersionType.HISTORY.value,
                "version_number": version_number,
                "table_name": version_table_name,
            },
            {"version_type": DatasetVersionType.RELEASE.value, "dataset_id": dataset_id},
        )

    # 历史版本切换成正式版，更改version_number
    repository.update_data(
        "dataset_version",
        {"version_type": DatasetVersionType.RELEASE.value, "version_number": "1", "table_name": dataset_table_name},
        {"id": version_id},
    )

    # 新增或修改dataset_current_version 表记录
    repository.delete_data("dataset_current_version", {"dataset_id": dataset_id})

    if replace_version_type == DatasetVersionType.HISTORY.value:
        # 应用的版本为历史版本时，才需要保存这个数据
        repository.add_data(
            "dataset_current_version", {"dataset_id": dataset_id, "version_id": version_id, "id": seq_id()}
        )

    # 判断是否超过最大数量
    CleanDatasetVersion(dataset_id, dataset_version.get("type")).clean()
