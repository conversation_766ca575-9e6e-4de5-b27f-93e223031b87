# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    Created by chenc04 on 2017/10/20.
"""
import json
import logging
import os
import gevent

from base import repository
from base.enums import DatasetEditMode, DBEngine, UserChannel
from components import query_sql_encoder
from components.external_api import ExternalAPI
from components.query_models import Select, QueryStructure, Limit, ModelEncoder, Object
from components.db_engine_transform import DbTransformService
from components.global_utils import validate_key_word
from components.remove_comment import remove_comment
from data_source.models import DataSourceModel
from dataset.common.sql_helper import extract_tables
from dataset.models import DatasetApiContentModel, DatasetDataModel
from dataset.services.dataset_base_service import DatasetBaseService, record_execute_time
from dataset.repositories import dataset_api_table_metadata
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib import config

from system.services import sql_adb_to_st_common

logger = logging.getLogger(__name__)


class DatasetAPIService(DatasetBaseService):
    """
    明源特殊api数据集业务类
    """

    def __init__(self, dataset_model=None, data_source_model=None):
        super().__init__(dataset_model, data_source_model)
        self._table_names = None
        self._complex_params = None

    @staticmethod
    def get_third_user_info(data_source_model):
        rv = {
            'id': '',
            'account': '',
        }
        user_id = g.userid if hasattr(g, 'userid') else ''
        if not user_id:
            return rv
        user_source_id = data_source_model.user_source_id if isinstance(data_source_model, DataSourceModel) else data_source_model.get('user_source_id')
        if not user_source_id:
            return rv
        user = repository.get_data('user', {'id': user_id}, ['account', 'user_source_id'])
        if not user:
            return rv
        # 如果用户本身来源渠道就是该渠道，不需要去查映射关系表, 直接获取渠道中用户的第三方id
        if user.get('user_source_id') == user_source_id:
            user_source_type = repository.get_data_scalar('user_source', {'id': user_source_id}, 'type')
            is_erp_user_source = user_source_type == UserChannel.Erp.value
            if is_erp_user_source:
                third_user_id = repository.get_data_scalar('external_user', {'account': user.get('account')}, 'id')
            else:
                third_user_id = repository.get_data_scalar('user_source_user', {'user_source_id': user_source_id, 'account': user.get('account')},
                                                           'user_id')
            return {
                'id': third_user_id,
                'account': user.get('account'),
            }

        # 否则查看用户是否有该渠道的映射关系
        third_user_id = repository.get_data_scalar('user_source_user_map',
                                                   {'dmp_user_id': user_id, 'user_source_id': user_source_id},
                                                   'third_user_id')
        if not third_user_id:
            return rv
        return {
            'id': third_user_id,
            'account': user.get('account')
        }

    @staticmethod
    def get_complex_params(table_names, data_source_model):
        if config.get('Function.api_sql_complex', 0) in [0, '0']:
            return {}
        complex = 1
        table_metadata_dict = DatasetAPIService.get_tables_metadata(table_names, data_source_model, from_db_only=True, flush_to_db=False)
        for table_name in table_names:
            row_count = table_metadata_dict.get(table_name, {}).get('row_count', 1)
            complex *= int(row_count / 1000 + 1)
        complex_params = {
            'tables': table_names,
            'tables_metadata': table_metadata_dict,
            'complex': complex
        }
        return complex_params

    @property
    def transform_cls(self):
        return DbTransformService

    @property
    def table_names(self):
        if self._table_names is None:
            self._table_names = self._get_table_names()
        return self._table_names

    @property
    def complex_params(self):
        return DatasetAPIService.get_complex_params(self.table_names, self.data_source_model)

    @property
    def third_user_info(self):
        return DatasetAPIService.get_third_user_info(self.data_source_model)

    @staticmethod
    def get_api(data_source_model):
        """
        获取api
        :param data_source_model:
        :return:
        """
        return ExternalAPI(
            data_source_model.conn_str.host,
            data_source_model.conn_str.access_secret,
            g.cookie if hasattr(g, 'cookie') else {},
            data_source_model.conn_str.tenant_code,
            data_source_model.conn_str.third_party_id
        )

    def get_query_structure(self, content_model, column_datas):
        """
        获取查询结构
        :param content_model:
        :param column_datas:
        :return:
        """

        query_structure = QueryStructure()

        for column_data in column_datas:
            select = Select()
            select.obj_name = content_model.table_name
            select.prop_name = column_data.get('name')
            query_structure.select.append(select)

        # 目前不支持join
        table = Object()
        table.name = content_model.table_name
        query_structure.object.append(table)

        limit = Limit()
        limit.row = self.data_limit
        query_structure.limit = limit

        return query_structure

    def get_sql_query_structure(self, sql):
        """
        根据sql语句获取查询结构，例：select a.* from (sql语句) a
        :param sql:
        :return:
        """
        query_structure = QueryStructure()
        select = Select()
        select.obj_name = "a"
        select.prop_name = "*"
        query_structure.select.append(select)
        table = Object()
        sql = self.validation_sql(sql)
        validate_key_word(sql, exclude=["if"])
        table.name = "({sql})".format(sql=sql)
        table.alias = "a"
        query_structure.object.append(table)
        limit = Limit()
        limit.row = self.data_limit
        query_structure.limit = limit
        return query_structure

    def get_result_data(self, data_source_model, biz_params, query_structure):
        result_data = self.get_api(data_source_model).get_data_list(
            biz_params, json.loads(json.dumps(query_structure, cls=ModelEncoder)),
            complex_params=self.complex_params,
            third_user_info=self.third_user_info,
        )

        if not result_data or not isinstance(result_data, dict):
            raise Exception('API数据集返回数据格式非法')

        if result_data.get('result'):
            # api会把执行sql语句放在最后一条数据中，需要去除
            data = result_data.get('data')
            if isinstance(data, list) and data and data[-1].get("real_sql"):
                data.pop()
            return result_data.get('data')
        else:
            raise UserError(message=result_data.get('msg'))

    @staticmethod
    def merge_params(data_source_params, dataset_params):
        """
        合并数据集和数据源参数
        :param data_source_params:
        :param dataset_params:
        :return:
        """
        params = []
        if dataset_params:
            params = dataset_params
        for data_source_param in data_source_params:
            data_source_param_name = data_source_param.get("name")
            is_exist = False
            for param in params:
                if param.get("name") == data_source_param_name:
                    is_exist = True
            if not is_exist:
                params.append(data_source_param)
        return params

    @staticmethod
    def get_tables_metadata(table_names, data_source_model, from_db_only=False, flush_to_db=True):
        # 获取相关表的元数据信息, 用于计算sql复杂度, 辅助功能不应影响主功能
        if config.get('Function.api_sql_complex', 0) in [0, '0']:
            return {}
        try:
            metadata_dict = dataset_api_table_metadata.get_table_metadata(table_names)
            rest_tables = list(set(table_names) - set(metadata_dict.keys()))
            if rest_tables and not from_db_only:
                rest_metadata_dict = DatasetAPIService.get_tables_metadata_from_api(rest_tables, data_source_model)
                if rest_metadata_dict and flush_to_db:
                    dataset_api_table_metadata.flush_table_metadata(rest_metadata_dict)
                    metadata_dict.update(rest_metadata_dict)
            return metadata_dict
        except Exception:
            return {}

    @staticmethod
    def get_tables_metadata_from_api(table_names, data_source_model):
        try:
            rv = DatasetAPIService.get_api(data_source_model).get_row_count(data_source_model.conn_str.params, table_names)
        except UserError:
            logger.exception("/get-row-count接口调用失败")
            return {}
        return {
            table_name: {
                'table_name': table_name,
                'row_count': row_count
            }
            for table_name, row_count in rv.get('data', {}).items()
        }

    def _get_table_names(self):
        table_names = []
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            link_datas, _ = self.deal_relation_mode_data(self.dataset_model.relation_content)
            if len(link_datas) == 1:
                return [link_datas[0].from_table_name]
            table_names.append(link_datas[0].from_table_name)
            for link_data in link_datas:
                table_names.append(link_data.to_table_name)
        else:
            dataset_content = self.load_dataset_content(self.dataset_model.content)
            sql = self.replace_dataset_sql_vars(dataset_content.get('sql'))
            table_names.extend(extract_tables(sql))

        return table_names

    def run_get_data_or_struct(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（数据集的子类需要重写）
        :return: dataset.models.DatasetDataModel
        """
        # 1、加载数据集内容
        dataset_content = self.load_dataset_content(self.dataset_model.content)
        if dataset_content.get("dataset_id"):
            dataset_id = dataset_content.get("dataset_id")
        elif getattr(self.dataset_model, "id"):
            dataset_id = getattr(self.dataset_model, "id")
        else:
            dataset_id = seq_id()

        # 判断不同模式，relation模式下走另外一套
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            link_datas, node_data = self.deal_relation_mode_data(self.dataset_model.relation_content)
            if not node_data:
                raise UserError(message="请至少拖入一个表！")
            columns = self.transfer_node_fields(node_data)

            # 1、测试运行创建表
            # （明源特殊api返回的结构是mysql字段类型, 这里与sql模式不同的原因是需要提前获取hash col_name作为as别名)
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(
                dataset_id, columns, create_table=False
            )

            # 2、根据api的sql语句获取数据和字段结构
            result_data = self.get_api_data_view(
                dataset_content, new_struct, link_datas, filter_content=self.dataset_model.filter_content
            )

        # 原来的sql模式
        else:
            # 1、根据api的sql语句获取数据和字段结构
            result_data, columns = self.get_api_data_or_columns(dataset_content)

            # 2、测试运行创建表 （明源特殊api返回的结构是mysql字段类型)
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(
                dataset_id, columns, create_table=False
            )

        # 缓存sql复杂度相关表元数据
        self.get_tables_metadata(self.table_names, self.data_source_model, from_db_only=False, flush_to_db=True)

        # run_get_data 不再计算总数，返回是为了前端不翻船
        total_count = 0
        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = dataset_id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = columns
        dataset_data_model.total_count = total_count
        dataset_data_model.tmp_table_name = tmp_table_name
        dataset_data_model.new_column_struct = new_struct
        dataset_data_model.create_table_sql = create_table_sql

        return dataset_data_model

    @record_execute_time()
    def get_api_data_or_columns(self, dataset_content):
        from gevent import monkey
        monkey.patch_socket()
        """
        请求api获取数据和结构
        :param dataset_content:
        :return:
        """
        content_model = DatasetApiContentModel(**dataset_content)
        params = self.merge_params(self.data_source_model.conn_str.params, content_model.params)

        # 高级sql模式
        if content_model.sql:
            sql = self.replace_dataset_sql_vars(content_model.sql)
            # 去注释
            sql, _ = remove_comment(sql)
            open_check = self.open_check_sr_sql()
            if open_check:
                task = gevent.spawn(self.check_sr_sql, params, sql, g.code)
            column_datas = self.get_api(self.data_source_model).get_table_columns(params, sql)
            if column_datas and column_datas.get('result'):
                column_datas = column_datas.get('data')
            else:
                if open_check:
                    gevent.kill(task)
                raise UserError(message="获取数据结构错误，错误内容：" + column_datas.get('msg'))
            query_structure = self.get_sql_query_structure(sql)
            result_data = self.get_result_data(self.data_source_model, params, query_structure)
            if open_check:
                gevent.joinall([task])
                if task.exception:
                    raise task.exception
        else:
            column_datas = self.get_api(self.data_source_model).get_table_columns(params, content_model.table_name)
            if column_datas.get('result'):
                column_datas = column_datas.get('data')
            else:
                raise UserError(message="获取数据结构错误，错误内容：" + column_datas.get('msg'))

            query_structure = self.get_query_structure(content_model, column_datas)
            result_data = self.get_result_data(self.data_source_model, params, query_structure)

        for column_data in column_datas:
            if 'col_name' not in column_data:
                column_data['col_name'] = column_data.get('name')

            if 'data_type' not in column_data:
                column_data['data_type'] = column_data.get('type')

        return result_data, column_datas

    def open_check_sr_sql(self):
        open_check = config.get("YK.check_sr_sql", 0) or 0
        sql_from = getattr(g, 'sql_from', 'viewreport') or 'viewreport'
        if sql_from and sql_from == 'testsql' and open_check and self.data_source_model.conn_str.check_sr:
            return True
        return False

    def check_sr_sql(self, params, sql, tenant):
        token = sql_adb_to_st_common.shu_xin_login()
        state, new_sql, replace_sql = sql_adb_to_st_common.request_rdb_to_st(sql, token)
        if state:
            if not self.data_source_model.conn_str.tenant_code:
                self.data_source_model.conn_str.tenant_code = tenant
            new_params = json.loads(json.dumps(params))
            # 标识是检测sr语法
            new_params.append({'value': '1', 'key': 'check_sr', 'name': 'check_sr', 'type': 'query'})
            column_datas = self.get_api(self.data_source_model).get_table_columns(new_params or params, new_sql)
            if column_datas and column_datas.get('result'):
                return column_datas
            else:
                raise UserError(message="StarRocks语法运行失败，获取数据结构错误，错误内容：" + column_datas.get('msg'))
        else:
            raise UserError(message="不是标准的sql语法，导致sql无法转换成StarRocks语法")

    @staticmethod
    def get_data_source_db_engine(data_source_params):
        """
        获取API数据源的数据引擎
        :param data_source_params:
        :return:
        """
        if not data_source_params:
            return DBEngine.RDS.value

        for data_source_param in data_source_params:
            if data_source_param.get("name") == "db_engine" and data_source_param.get("value") == DBEngine.ADS.value:
                return DBEngine.ADS.value

        return DBEngine.RDS.value

    @record_execute_time()
    def get_api_data_view(self, dataset_content, columns, link_datas, filter_content=None):
        """
        视图模式请求api获取结构
        :param dataset_content:
        :return:
        """
        content_model = DatasetApiContentModel(**dataset_content)
        params = self.merge_params(self.data_source_model.conn_str.params, content_model.params)
        query_structure = self.get_query_structure_view(columns, link_datas, filter_content=filter_content)
        # api 结构体中的过滤条件增进了参数，故进行参数替换
        for where_obj in query_structure.where:
            if where_obj:
                self.relace_where_sql_vars(where_obj, self.dataset_model.var_content, is_api=True)
        # 先返回
        result_data = self.get_result_data(self.data_source_model, params, query_structure)
        return result_data

    def run_get_data_count(self, link_datas=None):
        """
        运行数据集，产生总条数
        :return:
        """

        content = json.loads(self.dataset_model.content)
        content_model = DatasetApiContentModel(**content)
        query_structure = self.generate_sql_dataset_count_query_structure()

        web_time_out = int(os.environ.get('TIMEOUT', 10)) - 1
        params = self.data_source_model.conn_str.params + content_model.params
        try:
            result_data = self.get_api(self.data_source_model).get_data_list(
                params, json.loads(json.dumps(query_structure, cls=ModelEncoder)), web_time_out,
                complex_params=self.complex_params,
                third_user_info=self.third_user_info,
            )
        except UserError as ue:
            sql = query_sql_encoder.encode_query(query_structure)
            if ue.message.startswith("请求接口超时:"):
                raise UserError(message="获取总数时长超过{}秒，{}，sql：{}".format(str(web_time_out), "请求接口超时", sql))
            else:
                raise UserError(message="获取总数错误，{}，sql：{}".format(ue.message, sql))

        if not result_data.get('result'):
            sql = query_sql_encoder.encode_query(query_structure)
            raise UserError(message="获取总数错误，{}，sql：{}".format(result_data.get('msg'), sql))

        if result_data.get('data'):
            total_count = int(
                result_data.get('data')[0].get('total_count') if result_data and len(result_data) > 0 else 0
            )
        else:
            total_count = 0

        return total_count

    def get_dataset_content_sql(self):
        sql = super(DatasetAPIService, self).get_dataset_content_sql()
        if sql:
            sql = self.replace_dataset_sql_vars(sql)
        return sql
