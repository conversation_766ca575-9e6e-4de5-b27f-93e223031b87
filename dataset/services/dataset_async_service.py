#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    <NAME_EMAIL> on 2018/1/5.
"""
import json
import logging
import re
import os
import time
import random
from datetime import datetime

import app_celery
from app_celery import generate_excel_dataset, update_excel_index
from base import repository
from base.enums import DatasetType, OperateMode, DatasetConnectType, DatasetFieldType
from celery_app.celery import get_task_id
from components import query_sql_encoder
from components.message_queue import RabbitMQ
from components.query_structure_sql import QueryStructure, Select, ModelEncoder, Limit
from dashboard_chart.models import ChartDownloadModel
from dataset.models import DatasetOperateRecordModel
from dataset.query.result_data import DatasetQueryException
from dataset.repositories import dataset_field_repository
from dataset.repositories import dataset_repository
from dmplib import config
from dmplib.constants import LABEL_DETAIL_TABLE_NAME_SUFFIX
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from message.models import MessageModel
from message.services import message_service


from dataset.models import DatasetModel
from typing import Dict


def async_get_excel_worksheet(model: DatasetModel) -> Dict[str, str]:
    """
    获取Excel工作表
    :param model:
    :return:
    """
    if model.type == DatasetType.Excel.value:
        # 启动异步获取excel工作表
        task_id = get_task_id('dataset', DatasetType.Excel.value, g.code, seq_id())
        generate_excel_dataset.apply_async(
            kwargs={
                "task_id": task_id,
                "content": model.content,
                "code": g.code,
                "account": g.account,
                "is_get_sheet": True
            }, queue='parser')
        return {'task_id': task_id}
    else:
        raise UserError(message="只支持EXCEL数据集")


def async_preview(model):
    """
    执行异步预览
    :param model:
    :return:
    """
    if model.type == DatasetType.Excel.value:
        # 启动异步处理excel
        task_id = get_task_id('dataset', DatasetType.Excel.value, g.code, seq_id())
        generate_excel_dataset.apply_async(kwargs={
            "task_id": task_id,
            "content": model.content,
            "code": g.code,
            "account": g.account,
            "is_get_sheet": False
        }, queue='parser')
        return {'task_id': task_id}
    else:
        raise UserError(message="只支持EXCEL数据集")


def async_excel_index(dataset):
    """
    执行异步预览
    :param dataset:
    :return:
    """
    if dataset.get("type") == DatasetType.Excel.value:
        # 启动异步处理excel
        task_id = get_task_id('dataset', DatasetType.Excel.value, g.code, seq_id())
        update_excel_index.apply_async(kwargs={
            "task_id": task_id,
            "dataset": dataset,
            "code": g.code,
            "account": g.account
        }, queue='parser')
        return {'task_id': task_id}
    else:
        raise UserError(message="只支持EXCEL数据集")


def async_download(dataset_id, version_id=None):
    """
    执行异步下载数据集
    :param dataset_id:
    :param version_id:
    :return:
    """
    task_id = get_task_id('dataset', 'download', g.code, seq_id())

    dataset = dataset_repository.get_dataset(dataset_id)
    # 设置数据集的全局变量
    get_global_dataset_info(dataset)
    if not dataset:
        raise UserError(message="数据集不存在")

    if dataset.get('connect_type') == DatasetConnectType.Directly.value:
        raise UserError(message='直连模式不支持下载数据集功能')

    if dataset.get("type") == DatasetType.ExternalSubject.value:
        raise UserError(message='外部主题数据集不支持下载数据集功能')

    download_id = add_message(dataset)
    app_celery.dataset_download.apply_async(
        kwargs={
            'dataset_id': dataset_id, 'task_id': task_id, 'code': g.code,
            'download_id': download_id, 'user_id': g.userid, 'version_id': version_id
        },
        queue='download'
    )

    # 下载留痕
    repository.add_data(
        'dataset_operate_record',
        get_operate_record_model(
            dataset_id, OperateMode.Download.value, data_source=dataset.get("name") if dataset.get("name") else " "
        ).get_dict(),
    )

    return {'download_id': download_id}


def get_operate_record_model(dataset_id, operate_mode, data_source=None):
    """
    获取数据集操作记录model
    :return:
    """
    operate_record_model = DatasetOperateRecordModel()
    operate_record_model.id = seq_id()
    operate_record_model.name = data_source
    operate_record_model.dataset_id = dataset_id
    operate_record_model.operating_mode = operate_mode
    return operate_record_model


def notify_dmp_proc_download(task_id, version_id, dataset):
    # 获取查询结构
    query_struct_json, show_header_list, match_header_list, data_table_name = get_query_struct(version_id, dataset)

    # 测试查询10条数据，获取最终查询结构
    query_result = run_query_data(dataset.get("id"), query_struct_json, table_name=data_table_name)

    # 清空测试limit
    query_structure = QueryStructure(**query_result.query_structure)
    query_structure.limit = Limit()

    # 生成sql语句，调用python解析sql
    sql = query_sql_encoder.encode_query(query_structure)

    # 获取本地文件路径
    name = dataset.get('name')
    # 替换url中需要特殊处理的字符 +、空格、/、?、%、#、&、=
    file_name = re.sub(r'[#+/?%#&=*()|:<>，。、；…‘;,. ~！：【】@￥\[\]（）/"“？’$《》—]', '_', name)

    # add message
    download_id = add_message(dataset)

    # send mq download dmp-proc
    data = {
        'project_code': getattr(g, 'code'),
        'dataset_id': dataset.get("id"),
        'task_id': task_id,
        'user_id': g.userid,
        # 'external_params': getattr(g, 'external_params'),
        'download_id': download_id,
        'filename': file_name,
        'sql': sql,
        'query_struct': json.loads(json.dumps(query_structure, cls=ModelEncoder)),
        'show_header_list': show_header_list,
        'match_header_list': match_header_list,
        # 'token': query_token,
    }
    queue_name = config.get('RabbitMQ.queue_name_download', 'Download')
    rabbit_mq = RabbitMQ()
    rabbit_mq.send_message(
        queue_name, json.dumps(data, ensure_ascii=False), durable=False, headers={"_dmp_message_uuid": seq_id()}
    )
    return download_id


def get_query_struct(version_id, dataset):
    show_header_list = []
    match_header_list = []
    query_structure = QueryStructure()

    # 获取数据集表名和字段
    if version_id:
        version_data = repository.get_data("dataset_version", {"id": version_id}, ["table_name", "field_struct"])
        if not version_data:
            raise UserError(message='数据集版本不存在')
        data_table_name = version_data.get("table_name")
        fields = json.loads(version_data.get("field_struct"))
    else:
        data_table_name = dataset['table_name']
        if dataset.get('type') == DatasetType.Label.value:
            data_table_name = data_table_name + "_" + LABEL_DETAIL_TABLE_NAME_SUFFIX
        if not repository.check_data_db_table_is_exist(data_table_name, dataset):
            raise UserError(message='数据表数据未生成')
        # 获取数据集字段名
        fields = dataset_field_repository.get_dataset_field(dataset.get('id'), {'type': DatasetFieldType.Normal.value})

    for field in fields:
        if field.get("type") == DatasetFieldType.Normal.value:
            select = Select()
            select.prop_name = field.get('col_name')
            match_header_list.append(field.get('col_name'))
            show_header_list.append(field.get('alias_name') if field.get('alias_name') else field.get('col_name'))
            query_structure.select.append(select)

    limit = Limit()
    limit.row = 10
    query_structure.limit = limit

    query_structure_json = json.dumps(query_structure, cls=ModelEncoder)

    return query_structure_json, show_header_list, match_header_list, data_table_name


def run_query_data(dataset_id, query_struct_json, table_name="", is_api_schedule=False, version_id=None):
    from dataset.query.query_dataset_service import QueryDatasetService

    query_data = QueryDatasetService(
        **{
            "user_id": g.userid, "dataset_id": dataset_id, "chart_id": None, "query_structure_json": query_struct_json,
            "query_dataset_version": version_id
        }
    )
    try:
        query_result = query_data.get_query_data(table_name=table_name, is_api_schedule=is_api_schedule)
    except DatasetQueryException as dqe:
        msg = "执行错误: {} , sql：{} ".format(str(dqe), dqe.sql)
        raise UserError(message=msg)
    except Exception as e:
        msg = "内部服务错误: {}".format(str(e))
        raise UserError(message=msg)

    if query_result.code != 200:
        msg = "错误：{}，错误编码：{}，sql：{}".format(query_result.msg, query_result.code, query_result.sql)
        raise UserError(message=msg)

    return query_result


def add_message(dataset):
    # 获取本地文件路径
    name = dataset.get('name')
    # 替换url中需要特殊处理的字符 +、空格、/、?、%、#、&、=
    file_name = re.sub(r'[#+/?%#&=*()|:<>，。、；…‘;,. ~！：【】@￥\[\]（）/"“？’$《》—]', '_', name)
    # add message
    _message = {
        'source_id': file_name + '_' + datetime.now().strftime("%Y%m%d%H%M%S"),
        'user_id': g.userid,
        'source': '通知',
        'type': '个人消息',
        'title': '正在生成数据{}，请稍候...'.format(file_name),
        'url': '',
    }
    message_service.message_add(MessageModel(**_message))

    # add dashboard_chart_download_task
    download_id = seq_id()
    download_model = ChartDownloadModel(
        download_id=download_id,
        dashboard_id="",
        dashboard_chart_id="",
        download_url='',
        flow_id=dataset.get("id"),
        external_user_id="",
    )
    download_data = download_model.get_dict(
        ['download_id', 'dashboard_id', 'dashboard_chart_id', 'download_url', 'external_user_id', 'flow_id', 'status']
    )
    repository.add_data('dashboard_chart_download_task', download_data)
    return download_id


def get_global_dataset_info(dataset):
    try:
        content = json.loads(dataset.get('content', {}))
        data_source_id = content.get('data_source_id', '') or ''
        g.datasource_type = repository.get_data_scalar('data_source', {'id': data_source_id}, 'type') or ''
        g.dataset_type = dataset.get('type') or ''
    except:
        g.datasource_type = ''
        g.dataset_type = ''


def dataset_local_download(dataset_id, download_id, version_id=None):
    dataset = dataset_repository.get_dataset(dataset_id)
    name = dataset.get('name') or ''
    message = {
        'source_id': name + '_' + datetime.now().strftime("%Y%m%d%H%M%S"),
        'user_id': g.userid,
        'source': '通知',
        'type': '个人消息',
        'title': '',
        'url': '',
    }
    try:
        account = repository.get_data_scalar('user', {'id': g.userid}, 'account')
        if not account:
            raise UserError(message='找不到当前用户，请重新登录后下载')
        g.account = account
        repository.update_data("dashboard_chart_download_task", {'status': 1}, {"download_id": download_id})
        oss_url = download_dataset_to_excel(version_id, dataset_id, dataset, limit_num=None, upload_oss=True)
        repository.update_data("dashboard_chart_download_task", {'status': 2, 'download_url': oss_url}, {"download_id": download_id})
        message['title'] = '数据集下载成功'
        message['url'] = oss_url
        message_service.message_add(MessageModel(**message))
    except Exception as e:
        message['title'] = '数据下载失败，请联系管理员'
        message_service.message_add(MessageModel(**message))
        repository.update_data("dashboard_chart_download_task", {'status': 3}, {"download_id": download_id})
        raise e


def download_dataset_to_excel(version_id, dataset_id, dataset, limit_num, upload_oss=True):
    """
    下载数据集成Excel
    version_id:  数据集版本
    dataset_id:  数据集id
    dataset:     数据集信息
    """
    if version_id:
        version_data = repository.get_one('dataset_version', {'id': version_id}, ['table_name', 'field_struct'])
        if not version_data:
            raise UserError(message='没有找到数据集的版本数据')
        data_table_name = version_data.get('table_name')
        fields = json.loads(version_data.get('field_struct'))
    else:
        data_table_name = dataset['table_name']
        # 获取数据集字段名
        fields = dataset_field_repository.get_dataset_field(dataset.get('id'))
    field_list = []
    show_header_list = []
    query_structure = QueryStructure()
    for field in fields:
        if not field.get('type') == '普通':
            continue
        select = Select()
        select.prop_name = field.get('col_name')
        field_list.append(field.get('col_name'))
        show_header_list.append(field.get('alias_name') if field.get('alias_name') else field.get('col_name'))
        query_structure.select.append(select)
    file_uri = download_dataset_and_write_csv(
        dataset_id, data_table_name, query_structure, show_header_list, field_list,
        version_id, limit_num=limit_num, upload_oss=upload_oss
    )
    return file_uri


def download_dataset_and_write_csv(
        dataset_id, data_table, query_structure, show_header_list,
        field_list, version_id=None, limit_num=None, upload_oss=True
):
    """
    导出数据集数据到csv，直接返回oss或者本地文件路径
    limit_num: 是否下载全量的数据集, 没有值下载全量数据，有值下载指定数据量
    upload_oss: 是否将最后的文件上传oss, True: 返回oss地址，False: 返回文件路径
    """
    is_download_all = not bool(limit_num)
    page = 1
    if is_download_all:
        page_size = 5000
    else:
        page_size = limit_num
    limit = Limit()
    file_path = '/tmp/'
    file_name = 'dataset_result_{}{}.csv'.format(time.strftime('%Y%m%d%H%M%S'), random.randint(1000, 9999))
    all_path = os.path.join(file_path, file_name)
    try:
        while True:
            limit.offset = (page-1) * page_size
            limit.row = page_size
            query_structure.limit = limit
            query_struct_json = json.dumps(query_structure, cls=ModelEncoder)
            query_result = run_query_data(dataset_id, query_struct_json, table_name=data_table, version_id=version_id)
            result = query_result.data
            result = format_data(result, field_list)
            put_data_for_csv(result, show_header_list, all_path)
            page = page + 1
            if not result or not is_download_all:
                break
        if upload_oss:
            # 上传并返回oss地址
            oss_url = upload_file_to_oss(file_path, file_name)
            return oss_url
        else:
            # 返回文件路径
            return all_path
    except Exception as e:
        raise e
    finally:
        # 上传oss的情况下,直接删除源文件
        if upload_oss and os.path.exists(all_path):
            os.remove(all_path)


def put_data_for_csv(data, show_header_list, file_path):
    import csv
    if os.path.exists(file_path):
        with open(file_path, 'a', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            if data:
                writer.writerows(data)
    else:
        with open(file_path, 'a', encoding='utf-8-sig', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(show_header_list)
            if data:
                writer.writerows(data)


def upload_file_to_oss(local_filepath: str, file_name: str):
    from components.oss import OSSFileProxy
    upload_filepath = os.path.join(local_filepath, file_name)
    exported_file_url = OSSFileProxy().upload(
        open(upload_filepath, 'rb'), file_name=os.path.join('exports', file_name)
    )
    return exported_file_url


def format_data(data, field_list):
    result = []
    for item in data:
        row = []
        for col_name in field_list:
            value = '' if item.get(col_name) is None else item.get(col_name)
            row.append(value)
        result.append(row)
    return result
