#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
import collections
import datetime
import json
import logging
import os
from copy import deepcopy

from base import repository
from base.dmp_constant import DATASET_VARS_NAME
from base.enums import DatasetFieldGroup, DatasetFieldDataType, DatasetFieldType, DatasetConnectType
from base.enums import DatasetType
from components.dateset_generate_col_name import generate_new_col_name
from components.pular_api import PulsarApi
from components.query_structure_sql import ModelEncoder
from components.storage_setting import is_local_storage
from dataset.models import DatasetFieldModel
from dataset.query.chart_query import WhereField, ChartQuery
from dataset.cache import dataset_meta_cache, dataset_external_subject_cache
from dataset.common import advance_field_helper
from dataset.repositories import dataset_field_repository, dataset_repository
from dataset.services import dataset_var_service, dataset_field_check_service, dataset_field_group_service
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib import config
from rbac.services.data_permissions import data_permission_edit_filter
from self_service import external_service as external_self_service
from dataset.query.external_subject_query import external_subject_utils
from collections import OrderedDict, defaultdict
from typing import Any, Dict, List, Union

logger = logging.getLogger(__name__)


def get_normal_field(dataset_id):
    return dataset_field_repository.get_dataset_field(dataset_id, {'type': DatasetFieldType.Normal.value})


def get_original_external_subjects_fields(external_subject_ids: List[str]):
    """
    获取多个外部主体字段
    :param external_subject_ids:
    :return:
    """
    original_dataset_fields = []
    for external_subject_id in external_subject_ids:
        original_dataset_fields.extend(get_original_external_subject_fields(external_subject_id))
    return original_dataset_fields


def get_original_external_subject_fields(external_subject_id: str):
    """
    获取外部主题原始字段信息
    :param external_subject_id:
    :return:
    """
    if not external_subject_id:
        raise ValueError("外部主题ID不能为空！")
    original_dataset_fields = dataset_external_subject_cache.get_external_subject_dataset_fields_cache(
        external_subject_id
    )
    if not original_dataset_fields:
        external_subject = external_self_service.get_external_subject(external_subject_id)
        if not external_subject:
            raise ValueError("外部主题不存在！")
        external_subject_dataset_field_ids = []
        for table in external_subject.get("table_fields", []):
            table_name = table.get("table_name")
            for field in table.get("field_list", []):
                external_subject_dataset_field_ids.append(
                    external_subject_utils.generate_dataset_field_id(table_name, field.get("col_name", ""))
                )
        if external_subject_dataset_field_ids:
            original_dataset_fields = repository.get_list("dataset_field", {"id": external_subject_dataset_field_ids})
        dataset_external_subject_cache.set_external_subject_dataset_fields_cache(
            external_subject_id, original_dataset_fields
        )
    return original_dataset_fields or []


def get_multi_dataset_fields(dataset_field_ids):
    """
    获取多个数据集字段数据
    :param list dataset_field_ids: 数据集字段ID集合
    :return: []
    """
    return dataset_field_repository.get_dataset_field_by_ids(dataset_field_ids)


def _get_field_include_vars(dataset_include_vars: List[Any]) -> defaultdict:
    field_include_vars = defaultdict(list)
    for dataset_include_var in dataset_include_vars:
        field_include_vars[dataset_include_var.get("field_id")].append(dataset_include_var)
    return field_include_vars


def _assign_dataset_vars(dataset_id):
    """
    需要区分变量是sql直接引用变量还是报告引用变量
    :param dataset_id:
    :return:
    """
    dataset_vars = dataset_var_service.get_dataset_vars(dataset_id)
    # 需要区分变量是sql直接引用变量还是报告引用变量，0 默认，1 被数据集sql引用
    sql_var_id_list = dataset_var_service.get_dataset_sql_used_var_ids_by_dataset_id(dataset_id)
    for v in dataset_vars:
        v["is_from_sql"] = 1 if v.get("id") and v.get("id") in sql_var_id_list else 0
    return dataset_vars


def get_external_subject_dataset_field(dataset_id, conditions) -> OrderedDict:
    """
    根据dataset_id获取主题数据集主题相关字段
    :param dataset_id: 数据集ID
    :return:
    """
    conditions.update({'dataset_id': dataset_id})
    dataset_field_data = dataset_field_repository.get_dataset_field_v2(conditions)
    if not dataset_field_data:
        raise UserError(400, '错误：找不到数据集')

    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        logger.error('严重错误：根据dataset_id[%s]找不到数据集，但存在dataset_field' % dataset_id)
        raise UserError(400, '错误：找不到数据集')

    for dataset_field in dataset_field_data:
        dataset_field['alias_name'] = (
            dataset_field.get('alias_name') if dataset_field.get('alias_name') else dataset_field.get('origin_col_name')
        )

    return dataset_field_data


def get_dataset_field(dataset_id: str, is_not_category: bool = False,
                      include_dataset_vars: bool = False) -> OrderedDict:
    """
    根据dataset_id获取数据集字段
    :param dataset_id: 数据集ID
    :param is_not_category: 是否进行分类
    :param include_dataset_vars: 是否报告数据集变量
    :return:
    """
    dataset_field_data = dataset_field_repository.get_dataset_field(dataset_id)
    if not dataset_field_data:
        raise UserError(400, '错误：数据集没有字段')

    # 判断dataset 类型,api和直连需要替换origin_col_name为col_name
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        logger.error('严重错误：根据dataset_id[%s]找不到数据集，但存在dataset_field' % dataset_id)
        raise UserError(400, '错误：找不到数据集')

    if not dataset:
        raise UserError(message="数据集不存在！")

    if dataset.get('connect_type') == DatasetConnectType.Directly.value or (
            dataset.get('type') in [DatasetType.ExternalSubject.value]
    ):
        for dataset_field in dataset_field_data:
            dataset_field['alias_name'] = (
                dataset_field.get('alias_name')
                if dataset_field.get('alias_name')
                else dataset_field.get('origin_col_name')
            )

    # 旧式expression转新式expression_advance
    dataset_include_vars = dataset_var_service.batch_get_dataset_include_vars([dataset_id])
    field_include_vars = _get_field_include_vars(dataset_include_vars)
    for dataset_field in dataset_field_data:
        dataset_field["include_vars"] = list()
        if dataset_field.get('type') not in [DatasetFieldType.Customer.value, DatasetFieldType.Calculate.value]:
            continue
        dataset_field["include_vars"] = field_include_vars.get(dataset_field.get("id"))
        if dataset_field['expression_advance']:
            continue
        if not dataset_field.get('expression'):
            continue
        dataset_field['expression_advance'] = advance_field_helper.expression_convertor(dataset_field['expression'])
        # 指标模型添加可分析维度
        if dataset.get('type') == DatasetType.Indicator.value:
            dataset['analysis_dimension'] = "、".join(
                repository.get_columns("dataset_field", {"id": json.loads(dataset_field.get('relation_fields'))},
                                       'alias_name') or [])

    # 若需要获取数据集变量则一同返回 （数据集变量不属于数据集字段）
    dataset_vars = []
    if include_dataset_vars:
        dataset_vars = _assign_dataset_vars(dataset_id)

    if is_not_category:
        result = dataset_field_data
        if include_dataset_vars:
            result.extend(dataset_vars)
    else:
        if dataset_field_data:
            field_group = get_field_group(dataset_field_data)
            dimension_fields = field_group.get(DatasetFieldGroup.Dimension.value)
            field_group[DatasetFieldGroup.Dimension.value] = sort_dimension_field(dimension_fields)
            result = field_group
        else:
            result = collections.OrderedDict()
        if include_dataset_vars:
            result[DATASET_VARS_NAME] = dataset_vars

    filter_field_permission(dataset, result)
    return result


def filter_field_permission(dataset, fields):
    # 数芯数据集做按权限过滤
    if (config.get('SelfService.is_enable_indicator_permission', "0") == "1" and dataset.get('type', '') == 'INDICATOR'
            and dataset.get('external_type', '') == 'pulsar_indicator'
            and not dataset.get('is_import_table')):
        pulsar_api = PulsarApi()
        indicator_permission = pulsar_api.get_indicator_permissions(class_id=dataset['id'], user_account=g.account,
                                                                    code=g.code)
        if not indicator_permission['not_control']:
            accessed_indicators = set()
            for permission in indicator_permission.get('indicator_permissions', []):
                if permission.get('permission', 0) > 0:
                    indicator_code = permission.get('indicator_code')
                    accessed_indicators.add(indicator_code)
            for field in fields[DatasetFieldGroup.Measure.value]:
                if field['id'] not in accessed_indicators:
                    field['visible'] = 0


def get_dataset_field_no_convert(dataset_id, is_not_category=False):
    """
    根据dataset_id获取数据集字段（不转换col_name）
    :param dataset_id:
    :param is_not_category:
    :return:
    """
    dataset_field_data = dataset_field_repository.get_dataset_field(dataset_id)
    if is_not_category:
        result = dataset_field_data
    else:
        if dataset_field_data:
            field_group = get_field_group(dataset_field_data)
            dimension_fields = field_group.get(DatasetFieldGroup.Dimension.value)
            field_group[DatasetFieldGroup.Dimension.value] = sort_dimension_field(dimension_fields)
            result = field_group
        else:
            result = []
    return result


def sort_dimension_field(
        dimension_fields: List[Dict[str, Union[str, int, None]]]
) -> List[Dict[str, Union[str, int, None]]]:
    """
    排序维度字段
    :param dimension_fields:
    :return:
    """
    sort_data_type = []
    for e in DatasetFieldDataType.__members__.values():
        sort_data_type.append(e.value)

    flag = 1
    for i in range(len(dimension_fields) - 1, 0, -1):
        if flag:
            flag = 0
            for j in range(i):
                sort_data_type.index(dimension_fields[j].get('data_type'))
                if sort_data_type.index(dimension_fields[j].get('data_type')) > sort_data_type.index(
                        dimension_fields[j + 1].get('data_type')
                ):
                    dimension_fields[j], dimension_fields[j + 1] = dimension_fields[j + 1], dimension_fields[j]
                    flag = 1
        else:
            break
    return dimension_fields


def get_field_group(fields: List[Dict[str, Union[str, int, None]]]) -> OrderedDict:
    """
    获取字段并且按照维度和度量分组
    :param fields:
    :return:
    """
    # 使用有序字典，解决前端度量和维度顺序不确定的问题
    field_group = collections.OrderedDict({DatasetFieldGroup.Dimension.value: []})
    field_group[DatasetFieldGroup.Measure.value] = []
    for field in fields:
        if field['field_group'] in field_group:
            field_group[field['field_group']].append(field)
        else:
            field_group[field['field_group']] = [field]
    return field_group


def get_field_data_type_group(fields):
    """
    获取字段并且按照数据类型分组
    :param fields:
    :return:
    """
    field_group = {}
    for e in DatasetFieldDataType.__members__.values():
        field_group[e.value] = []
    for field in fields:
        if field['data_type'] in field_group:
            field_group[field['data_type']].append(field)
        else:
            field_group[field['data_type']] = [field]
    return field_group


def get_not_exist_fields(dataset_fields, old_dataset_fields, dataset_type):
    """
    获取不存在的字段ID集合
    :return:
    """
    field_ids = []
    for old_dataset_field in old_dataset_fields:
        is_exist = False
        for dataset_field in dataset_fields:
            # 忽略高级字段
            if old_dataset_field.get('type') != DatasetFieldType.Normal.value:
                continue

            if dataset_type == DatasetType.Excel.value:
                if old_dataset_field.get('alias_name') == dataset_field.get('alias_name'):
                    is_exist = True
            else:
                if old_dataset_field.get('col_name') == dataset_field.get('col_name'):
                    is_exist = True
                    break
        if not is_exist:
            field_ids.append(old_dataset_field.get("id"))
    return field_ids


def get_where_fields(dataset_field, conditions):
    where_fields = []
    # conditions 为空时
    if not conditions:
        where_field = WhereField()
        where_field.operator = None
        where_field.field = None
        where_field.field_value = None
        where_fields.append(where_field)
    else:
        for filter_data in conditions:
            where_field = WhereField()
            where_field.operator = filter_data.get("operator")
            where_field.field = dataset_field.get('col_name')
            where_field.field_value = filter_data.get("col_value")
            where_field.field_type = dataset_field.get('data_type')
            if dataset_field.get("data_type") == DatasetFieldDataType.Datetime.value:
                where_field.field_func_format = "%Y-%m-%d"
                where_field.field_func = 'date_format'
            where_fields.append(where_field)
    return where_fields


def add_inspection_rules(dataset_field):
    """
    添加巡检规则
    :param dataset_field:
    :return:
    """
    if dataset_field.get("inspection_rules"):
        try:
            inspection_rules_str = json.dumps(dataset_field.get("inspection_rules"))
        except json.JSONDecodeError as e:
            raise Exception('数据集巡检规则解析错误：' + str(e))
        inspection_wheres = get_where_fields(dataset_field, dataset_field.get("inspection_rules"))
        query = ChartQuery()
        query.where(inspection_wheres)
        dataset_field['inspection_wheres'] = query.get_where_str().replace("WHERE ", "")
        dataset_field["inspection_rules"] = inspection_rules_str


def add_dataset_field(model):
    """
    :添加数据集字段
    :date 2017/6/16
    :param model:
    :return :
    """

    data = extract_dataset_data(model)

    # model.validate()
    fields = [
        'id',
        'dataset_id',
        'alias_name',
        'rank',
        "origin_field_type",
        'origin_col_name',
        'origin_table_id',
        'origin_table_comment',
        'origin_table_name',
        'origin_table_alias_name',
        'col_name',
        'data_type',
        'visible',
        'field_group',
        'format',
        'type',
        'expression',
        'inspection_rules',
        'inspection_wheres',
        'note',
        'external_id',
        'origin_dim_type'
    ]
    reslut = repository.add_list_data('dataset_field', data, fields)

    # 设置数据集字段数据缓存
    dataset_meta_cache.set_dataset_field_cache(model.id, data)

    return reslut


def can_update_dataset_field(model: DatasetFieldModel):
    """
    :是否可以修改数据集字段类型
    :date 2017/6/16
    :param model:
    :return :
    """

    if not model.id or not model.dataset_id:
        return True

    # 检测分组字段引用
    reference, msg = dataset_field_check_service.check_group_field_reference(model.dataset_id, model.id)
    if reference:
        raise UserError(message=msg)
    return True


@data_permission_edit_filter('dataset-edit')
def update_dataset_field(model):
    """
    :编辑数据集字段类型
    :date 2017/6/16
    :param model:
    :return :
    """

    if not model.id:
        raise UserError(message='数据集表字段ID不能为空')

    # 检测分组字段引用
    reference, msg = dataset_field_check_service.check_group_field_reference(model.dataset_id, model.id)
    if reference:
        raise UserError(message=msg)

    old_dataset_field = dataset_repository.get_dataset_field(model.id)
    data = set_data({}, model)
    if model.inspection_rules:
        # 直连模式不支持添加巡检规则
        dataset = dataset_repository.get_dataset(old_dataset_field.get('dataset_id'))
        if dataset.get('connect_type') == DatasetConnectType.Directly.value:
            raise UserError(message='直连模式不支持添加巡检规则')
        data['inspection_rules'] = model.inspection_rules
        dataset_field = repository.get_data("dataset_field", {"id": model.id})
        try:
            data["inspection_rules"] = json.dumps(model.inspection_rules)
        except json.JSONDecodeError as e:
            raise Exception('数据集巡检规则解析错误：' + str(e))
        inspection_wheres = get_where_fields(dataset_field, model.inspection_rules)
        query = ChartQuery()
        query.where(inspection_wheres)
        data['inspection_wheres'] = query.get_where_str().replace("WHERE ", "")
    # 不传inspection_rules，默认为删除
    else:
        data['inspection_wheres'] = ''
        data['inspection_rules'] = ''

    result = repository.update_data('dataset_field', data, {'id': model.id})

    # 修改数据集字段元数据缓存
    dataset_field_caches = dataset_meta_cache.get_dataset_field_cache(model.dataset_id, is_no_cache_query_db=False)
    if dataset_field_caches:
        for dataset_field_cache in dataset_field_caches:
            if dataset_field_cache.get("id") == model.id:
                set_data(dataset_field_cache, model)
                break
        dataset_meta_cache.set_dataset_field_cache(model.dataset_id, dataset_field_caches)

    # 删除数据集多字段元数据缓存
    dataset_meta_cache.del_multi_dataset_field_cache([model.id])

    # 字段类型变化时需要清空字段分组关联信息
    if old_dataset_field.get('field_group') != model.field_group:
        dataset_field_group_service.delete_field_group_relation([model.id])

    return result


def set_data(data, model):
    if model.data_type:
        data['data_type'] = model.data_type
    if model.alias_name is not None:
        data['alias_name'] = model.alias_name
    if model.field_group:
        data['field_group'] = model.field_group
    if model.visible is not None:
        data['visible'] = model.visible
    if model.note is not None:
        data['note'] = model.note
    return data


def extract_dataset_data(model):
    """
    根据model提取数据集数据
    :param model:
    :return :
    """
    data = []

    if not model.field:
        raise UserError(message='数据集表字段不能为空')

    for k, item in enumerate(model.field):

        if not item.get('id'):
            item['id'] = seq_id()
        item['dataset_id'] = model.id

        if not item.get('rank'):
            item['rank'] = k + 1
        if not item.get('type'):
            item['type'] = DatasetFieldType.Normal.value
        item['external_id'] = item.get('external_id', '')
        item['origin_dim_type'] = item.get('origin_dim_type', '')
        # 增加巡检规则
        add_inspection_rules(item)

        data.append(item)
    return data


def replace_dataset_field(model, commit=True):
    """
    :替换数据集字段
    :date 2017/6/16
    :param model:
    :param commit:
    :return :
    """

    data = extract_dataset_data(model)

    # model.validate()
    fields = [
        'id',
        'dataset_id',
        'alias_name',
        'rank',
        'col_name',
        'origin_col_name',
        'origin_field_type',
        'origin_table_id',
        'origin_table_comment',
        'origin_table_name',
        'origin_table_alias_name',
        'data_type',
        'visible',
        'field_group',
        'format',
        'type',
        'expression',
        'inspection_rules',
        'inspection_wheres',
        'note',
        'external_id',
        'origin_dim_type'
    ]
    reslut = repository.replace_list_data('dataset_field', data, fields, commit=commit)

    return reslut


def get_dataset_field_values(dataset_id, field_id):
    """
    获取过滤器数据函数， 子类需要重写（后期需要废除，全部走json格式获取数据集数据）
    :return:
    """
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message='未查询到数据集!')
    dataset_field = dataset_field_repository.get_dataset_field_info(field_id)
    if not dataset_field:
        raise UserError(message='字段不存在')

    hash_col_name = dataset_field.get('col_name')
    object_name = dataset_field.get("origin_table_alias_name") or dataset_field.get("origin_table_name")
    if (
            dataset.get("type") in [DatasetType.ExternalSubject.value]
            or dataset.get('connect_type') == DatasetConnectType.Directly.value
    ):
        # 需要转换col_name为origin_col_name
        dataset_field['col_name'] = dataset_field.get('origin_col_name') or dataset_field.get('col_name')
    # 落地的情况，使用dataset中table_name作为object
    else:
        object_name = dataset.get("table_name")
    from components.query_models import QueryStructure, Select, Group, Limit

    query_structure = QueryStructure()
    query_structure.limit = Limit(row=1500)

    select_field = Select()
    if dataset_field.get('expression'):
        select_field.prop_name = hash_col_name
    else:
        if dataset.get('type') == DatasetType.Api.value:
            select_field.alias = hash_col_name
        select_field.prop_name = dataset_field.get("col_name")
        select_field.obj_name = object_name
    query_structure.select.append(select_field)
    group_field = Group()
    if dataset_field.get('expression'):
        group_field.prop_name = hash_col_name
    else:
        group_field.prop_name = dataset_field.get("col_name")
        group_field.obj_name = object_name
    query_structure.group_by.append(group_field)

    from dataset.external_query_service import get_dataset_data

    query_params = {
        "user_id": g.userid,
        "dataset_id": dataset_id,
        "chart_id": "",
        "query_structure_json": json.dumps(query_structure, cls=ModelEncoder),
    }
    result = get_dataset_data(**query_params)
    return result.get("data")


def export_dataset_field(dataset_id):
    """
    导出数据集字段信息
    :param dataset_id:
    :return:
    """
    from dataset.services.dataset_excel_service import DatasetExcelService

    # 获取数据集信息
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message='未查询到数据集!')

    # 获取数据集字段信息
    fields = get_dataset_filed_special_order(dataset_id)

    # 生成excel
    file_path, file_name = DatasetExcelService.generate_dataset_field_excel(dataset.get("name"), fields)

    # 上传对象存储
    oss_url = DatasetExcelService.upload_file_to_oss(file_path, file_name)

    # 删除临时文件
    os.remove(os.path.join(file_path, file_name))

    # 通过dmp接口添加签名
    oss_url = "{domain}/api/download/oss?url={url}".format(
        domain=config.get("Domain.dmp"), url=oss_url)

    return oss_url


def get_dataset_filed_special_order(dataset_id):
    """
    获取指定排序的字段信息
    :param dataset_id:
    :return:
    """

    def _field_sort(fields, groups):  # NOSONAR
        before = []
        after = []
        copy_fields = deepcopy(fields)
        if groups:
            for item in groups:
                children = item.get("children", [])
                for i in children:
                    for f in fields:
                        if f.get("id") == i:
                            copy_fields.remove(f)
                            f["groups"] = item.get("folderName", "")
                            before.append(f)
            after = copy_fields
        else:
            after = fields
        for field in after:
            field["groups"] = ""
        return before + after

    # 获取数据集字段信息
    fields_data = dataset_field_repository.get_dataset_field(dataset_id)
    fields_data = get_field_group(fields_data)
    fields_dim = fields_data.get("维度", [])
    fields_num = fields_data.get("度量", [])

    # 获取字段分组信息
    group = dataset_field_group_service.get_field_group_by_dataset_id(dataset_id)
    group_dim = group.get("维度", [])
    group_num = group.get("度量", [])

    # 组装排序
    dim = _field_sort(fields_dim, group_dim)
    num = _field_sort(fields_num, group_num)

    return dim + num


def import_dataset_field(params):
    """
    导入并修改字段备注
    :param params:
    :return:
    """
    from dataset.services.dataset_excel_service import DatasetExcelService

    # 参数校验
    oss_url = params.get("oss_url")
    dataset_id = params.get("dataset_id")
    if not oss_url or not dataset_id:
        raise UserError(message=f"参数错误：{params}")

    # 获取数据集信息
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message='未查询到数据集!')

    # 下载excel数据到本地
    file_path = DatasetExcelService.download_oss_file(oss_url.split("/")[-1], oss_url)

    # 解析excel获取数据集字段信息
    field_list = DatasetExcelService.read_excel_of_dataset_fields(oss_url, file_path, dataset)

    # 编辑字段信息
    edit_field = _edit_dataset_field(field_list, dataset_id)

    return {
        "total_count": len(field_list) - 1,
        "edit_count": len(edit_field),
        "edit_field": edit_field
    }


def _edit_dataset_field(field_list, dataset_id):
    """
    编辑
    :param field_list:
    :param dataset_id:
    :return:
    """
    edit_field = []
    # 获取表头索引信息
    headers_index = _get_header_index(field_list[0])

    # 编辑字段信息，调用字段编辑接口，确保字段修改校验逻辑一致
    for item in field_list[1:] if len(field_list) > 1 else []:
        field = {}
        for key, value in headers_index.items():
            v = item[value]
            field[key] = _get_field_value(key, v)
        kwargs = dataset_field_repository.get_dataset_field_by_where(
            {"dataset_id": dataset_id, "col_name": field.get("col_name")})
        # 只更新有变化的字段
        if kwargs and _need_update(kwargs, field):
            # 高级字段和分组字段不能修改data_type
            _filter_key(kwargs, field)
            kwargs.update(field)
            model = DatasetFieldModel(**kwargs)
            if model.inspection_rules:
                model.inspection_rules = json.loads(model.inspection_rules)
            # 编辑字段
            _update_dataset_field(model)
            edit_field.append(field.get("col_name"))
    return edit_field


def _update_dataset_field(model):
    """
    更新字段
    :param model:
    :return:
    """
    # 校验分组字段引用
    reference, _ = dataset_field_check_service.check_group_field_reference(model.dataset_id, model.id)
    if reference:
        raise UserError(message="因字段【{}】被分组字段引用，不能更改类型。如需更改请删除分组字段".format(
            model.alias_name))
    update_dataset_field(model)


def _need_update(kwargs, field):
    """
    不一致的才更新
    :param kwargs:
    :param field:
    :return:
    """
    for k, v in field.items():
        if v != kwargs.get(k):
            return True
    return False


def _filter_key(kwargs, field):
    """
    校验字段：1、高级字段不能修改字段类型 2、分组字段不能修改类型
    :param field:
    :return:
    """
    if kwargs.get("type") in [DatasetFieldType.Group.value, DatasetFieldType.Customer.value]:
        del field["data_type"]


def _get_field_value(key, v):  # NOSONAR
    """
    校验值
    :param key:
    :return:
    """
    visible_map = {"是": 1, "否": 0}
    if key == "visible":
        if v not in ["是", "否"]:
            raise UserError(message="可见性字段有未知的字段值")
        return visible_map.get(v, 1)
    if key == "data_type":
        if v not in ['字符串', '日期', '数值', '地址', '枚举']:
            raise UserError(message="数据类型字段有未知的字段值")
        return v
    return v


def _get_header_index(headers: list) -> dict:
    """
    获取表头索引信息
    :param headers:
    :return:
    """
    header_index = {}

    def _index(name, key):
        try:
            header_index[key] = headers.index(name)
        except:
            pass

    _index("字段名", 'col_name')
    # _index("中文别名", 'alias_name')
    _index("备注", 'note')
    _index("数据类型", 'data_type')
    _index("可见性", 'visible')

    return header_index


def get_dataset_fields_of_filling(dataset_id):
    """
    获取字段信息
    """
    dataset = dataset_field_repository.get_dataset_by_id(dataset_id)
    if dataset.get("connect_type") == "直连":
        raise UserError(message="仅支持调度数据集")
    table_name = dataset.get("table_name", "")
    dataset_fields = dataset_field_repository.get_dataset_field(dataset_id)
    try:
        content = json.loads(dataset.get("content"))
        data_source_id = content.get("data_source_id")
        g.datasource_type = repository.get_data_scalar("data_source", {"id": data_source_id}, col_name="type")
        g.dataset_type = dataset.get("type")
        g.running_way = content.get('running_way')
    except Exception as e:
        print(e)
        g.datasource_type = ''
    if is_local_storage(g.code):
        columns, _ = dataset_field_repository.get_field_of_local(table_name, dataset_id)
    else:
        columns = dataset_field_repository.get_field_of_cloud(table_name)
    columns = {i.get("col_name"): i for i in columns}
    fields = {i.get("col_name"): i for i in dataset_fields}
    for key, value in columns.items():
        value["id"] = fields.get(key, {}).get("id")
        value["alias_name"] = fields.get(key, {}).get("alias_name")
        value["data_type"] = dataset_field_repository.trans_id_data_type(value["data_type"], value)
        value["origin_field_type"] = value["data_type"]
    return list(columns.values())


def get_columns_of_schedule_dataset(dataset_id):
    """
    获取字段信息
    :param dataset_id:
    :return:
    """
    from components.db_engine_transform import get_dmp_data_type_by_db_engine

    dataset = dataset_field_repository.get_dataset_by_id(dataset_id)
    if not dataset:
        raise UserError(message="数据集不存在")
    if dataset.get("connect_type") == "直连":
        raise UserError(message="仅支持调度数据集")
    table_name = dataset.get("table_name", "")
    try:
        content = json.loads(dataset.get("content"))
        data_source_id = content.get("data_source_id")
        g.datasource_type = repository.get_data_scalar("data_source", {"id": data_source_id}, col_name="type")
        g.dataset_type = dataset.get("type")
        g.running_way = content.get('running_way')
    except Exception as e:
        print(e)
        g.datasource_type = ''
    if is_local_storage(g.code):
        columns, db_engine = dataset_field_repository.get_field_of_local(table_name, dataset_id)
    else:
        columns = dataset_field_repository.get_field_of_cloud(table_name)
        db_engine = "mysql"
    col_list = []
    dataset_fields = dataset_field_repository.get_dataset_field(dataset_id)
    fields = {i.get("col_name"): i for i in dataset_fields}
    for item in columns:
        # 数据服务中心mysql场景， 从数据服务中心会多返回一个id字段
        if item.get("col_name") not in fields:
            continue
        col_list.append({
            "name": item.get("col_name"),
            "type": item.get("data_type"),
            "comment": fields.get(item.get("col_name"), {}).get("alias_name") or item.get("comment"),
            "col_type": get_dmp_data_type_by_db_engine(item.get("data_type"), db_engine),
            "group_name": item.get('group_name', ''),
            "field_group": fields.get(item.get("col_name"), {}).get('field_group')
        })
    return col_list


def refresh_dataset_col_name(dataset_id=None):
    conditions = {}
    if dataset_id:
        conditions['dataset_id'] = dataset_id
    dataset_fields = dataset_field_repository.get_dataset_field_v2(conditions)

    for field in dataset_fields:
        new_field_name = generate_new_col_name(
            field.get('dataset_id'),
            col_name=field.get("origin_col_name") if field.get("origin_col_name") else field.get("alias_name"),
            table_name=field.get("origin_table_alias_name")
            if field.get("origin_table_alias_name")
            else field.get("table_name"),
        )

        repository.update(
            "dataset_field", {"col_name": new_field_name, 'modified_on': datetime.datetime.now()}, {"id": field.get('id')}
        )
