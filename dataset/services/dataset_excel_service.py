# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    Created by chenc04 on 2017/10/20.
"""
import datetime
import json
import os
import re
import copy
import logging
from urllib.parse import unquote

import xlrd
from xlrd import XLRDError
import xlsxwriter
import csv
from collections import Counter
from urllib.parse import urlparse
from base.enums import DatasetFileType, IndicatorType, DatasetFieldDataType, DatasetFieldGroup, DatasetType, \
    DataSourceType, DatasetStorageType, DataCenterAction
from components.oss import OSSFileProxy
from dataset.models import (
    DatasetExcelResultModel,
    DatasetExceldataModel,
    DatasetFieldModel,
    DatasetExcelContentModel,
    DatasetModel,
)
from dataset.repositories import dataset_repository, dataset_field_repository
from dataset.services.dataset_base_service import DatasetBaseService
from components.db_engine_transform import ExcelToMysqlTransformService
from dataset.services.dataset_field_service import get_field_group
from dataset.services.dataset_field_check_service import DatasetFieldCompareService
from dmplib import redis
from dmplib.utils import strings
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.utils.strings import is_number
from components.data_center_api import get_new_erp_datasource_model, request_data_center, get_data_source_info
from base import repository
from components.storage_setting import get_storage_type
from components.data_center_api import get_preview_data_of_sql


class DatasetExcelService(DatasetBaseService):
    """
    Excel数据集业务类
    """

    def __init__(self, dataset_model=None, data_source_model=None):
        super().__init__(dataset_model, data_source_model)
        self.task_prefix = 'task_'
        self.content_model = None
        self.result = None

    @property
    def transform_cls(self):
        return ExcelToMysqlTransformService

    def run_get_sheet(self, dataset_model):
        """
        获取工作表
        :return:
        """
        self.validation_dataset(dataset_model)
        file_path = self.download_oss_file(self.content_model.file_name, self.content_model.oss_url)
        if not os.path.exists(file_path):
            raise UserError(self.content_model.oss_url + "文件不存在。")
        data_info = self.read_file_sheet(self.content_model.oss_url, file_path)
        return data_info

    def run_get_data(self):
        """
        运行数据集，产生数据集数据和数据集字段
        :return:
        """
        pass

    def async_get_sheet(self, task_id, content):
        """
       异步获取excel工作表
       :param task_id:
       :param content:
       :return:
       """
        file_path = ''
        self.result = DatasetExcelResultModel()
        try:
            dataset_model = DatasetModel(**{'content': content})
            self.validation_dataset(dataset_model)
            self.result.task_id = task_id
            self.result.name = self.content_model.file_name
            self.result.status = 1
            file_path = self.download_oss_file(self.content_model.file_name, self.content_model.oss_url)
            if not os.path.exists(file_path):
                raise UserError(self.content_model.oss_url + "文件不存在。")
            data_info = self.read_file_sheet(self.content_model.oss_url, file_path)
            self.result.info = data_info
            redis.conn().set_data(task_id, self.result.get_dict())
        except Exception as e:
            error_msg = "获取excel工作表失败。" + "Err:" + str(e)
            logging.error(error_msg, exc_info=True)
            self.result.error_msg = error_msg
            self.result.status = 1
            redis.conn().set_data(task_id, self.result.get_dict())
        finally:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)

    def async_process_data(self, task_id, content):
        """
        异步处理excel数据
        :param task_id:
        :param content:
        :return:
        """
        file_path = ''
        self.result = DatasetExcelResultModel()
        try:
            dataset_model = DatasetModel(**{'content': content})

            self.validation_dataset(dataset_model)

            # 判断是否有dataset_id
            content_dict = json.loads(content)
            # 区分是追加还是编辑
            append = content_dict.get('type', '')
            old_dataset_field, num_field_location = self.get_old_filed_infos(content_dict.get("dataset_id"))

            self.result.task_id = task_id
            self.result.name = self.content_model.file_name
            self.result.status = 1

            file_path = self.download_oss_file(self.content_model.file_name, self.content_model.oss_url)
            if not os.path.exists(file_path):
                raise UserError(self.content_model.oss_url + "文件不存在。")
            file_type = os.path.splitext(file_path)[1]
            file_suffix = file_type.replace('.', '').lower()
            self.read_data(file_suffix, file_path, old_dataset_field, num_field_location, content_dict, append)
            # 默认过期时间1小时
            redis.conn().set(task_id, json.dumps(self.result.get_dict()), 3600)
        except Exception as ex:
            error_msg = "处理excel数据集失败，错误内容：" + str(ex)
            self.result.error_msg = error_msg
            self.result.status = 1
            redis.conn().set_data(task_id, self.result.get_dict())
        finally:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)

    def async_update_excel_index(self, task_id, code, dataset):
        table_name = dataset.get("table_name")
        # 查询索引信息
        indexes = repository.get_list("dataset_index", {"dataset_id": [dataset.get("id")]},
                                      fields=["index_name", "column_list", "system", "index_statement"])
        self.result = DatasetExcelResultModel()
        self.result.task_id = task_id
        self.result.status = 1

        try:
            if get_storage_type(code) == DatasetStorageType.DatasetStorageOfLocal.value:
                dataset["index_data"] = indexes
                # 获取执行索引sql
                index_sqls = self.get_index_sqls(dataset)
                data_source = get_new_erp_datasource_model()
                # 获取查询索引sql
                sql = dataset_repository.get_query_index_sql(data_source.db_type, table_name)

                # 获取当前数据库中的索引
                result = get_preview_data_of_sql(sql, data_source)
                datas = result.get("Data")
                drop_index_sqls = []

                if datas and len(datas):
                    drop_index_sqls = dataset_repository.get_drop_index_sql(datas, data_source.db_type, table_name)
                drop_index_sqls.extend(index_sqls)
                # 新增索引支持达梦
                sqls = dataset_repository.dm_get_execute_sqls(drop_index_sqls, data_source.db_type)
                sql = ''.join(sqls)
                if sql and len(sql) > 0:
                    # 数据服务中心请求执行索引
                    request_data_center(DataCenterAction.DMPDatasetExecuteSql.value, params={
                        "DataInfo": {
                            "DataSourceModel": get_data_source_info(data_source.conn_str),
                            "ExecuteSql": sql
                        }
                    })
            else:
                # 本段模式为mysql语句
                index_sqls = []
                for index in indexes:
                    index_sql = self.get_index_sql(index, "mysql", table_name)
                    if not index_sql:
                        continue
                    index_sqls.append(index_sql)
                # 更新索引
                dataset_repository.update_dataset_excel_index(table_name, index_sqls)
            # 默认过期时间1小时
            redis.conn().set(task_id, json.dumps(self.result.get_dict()), 3600)
        except Exception as ex:
            error_msg = "excel数据集创建索引，错误内容：" + str(ex)
            self.result.error_msg = error_msg
            self.result.status = 1
            redis.conn().set_data(task_id, self.result.get_dict())

    def get_index_sqls(self, dataset):
        index_sqls = []
        # 数据服务中心数据源
        data_source = get_new_erp_datasource_model()

        if not dataset.get("index_data"):
            return index_sqls

        for index in dataset.get("index_data"):
            index_sql = self.get_index_sql(index, data_source.db_type, dataset.get("table_name"))
            if not index_sql:
                continue
            index_sqls.append(index_sql)
        return index_sqls

    def get_index_sql(self, index, db_type, table_name):
        paren_left = "["
        paren_right = "]"
        if db_type.lower() == DataSourceType.Mysql.value.lower():
            paren_left = "`"
            paren_right = "`"
        elif db_type.lower() == DataSourceType.DM.value.lower():
            paren_left = "\""
            paren_right = "\""
        _cloumn_list = ['%s%s%s' % (paren_left, row, paren_right) for row in json.loads(index.get("column_list"))]
        cloumn_list = ",".join(_cloumn_list)
        if not index.get("index_name") or not cloumn_list:
            return None
        return "CREATE INDEX {left}{index_name}{right} ON {left}{table_name}{right} ({cloumn_list});".format(
            left=paren_left, index_name=index.get("index_name"), right=paren_right, cloumn_list=cloumn_list,
            table_name=table_name)

    def read_data(self, file_suffix, file_path, old_dataset_field, num_field_location, content_dict, append):
        if file_suffix in [DatasetFileType.Xls.value, DatasetFileType.Xlsx.value]:
            data_info = self.read_execl_file_data(
                file_path,
                old_dataset_field=old_dataset_field,
                num_field_location=num_field_location,
                dataset_id=content_dict.get('dataset_id'),
                append=append,
            )
            self.result.info = data_info
        elif file_suffix == DatasetFileType.Csv.value:
            data_info = self.read_csv_file_data(
                file_path,
                self.content_model.file_name,
                old_dataset_field=old_dataset_field,
                num_field_location=num_field_location,
                dataset_id=content_dict.get('dataset_id'),
                append=append,
            )
            self.result.info = data_info
        else:
            self.result.error_msg = "只支持xls和xlsx和csv文件。"
            self.result.status = 1

    def get_old_filed_infos(self, dataset_id: str):
        old_dataset_field = []
        num_field_location = {}
        if not dataset_id:
            return old_dataset_field, num_field_location
        dataset = dataset_repository.get_dataset(dataset_id)
        if dataset:
            old_dataset_field = dataset_field_repository.get_dataset_field(dataset_id, {'type': '普通'}) or []
            num_field_location = self.deal_old_dataset_field(old_dataset_field)
        return old_dataset_field, num_field_location

    @staticmethod
    def deal_old_dataset_field(old_dataset_field: list):
        num_field_location = {}
        for i, _field in enumerate(old_dataset_field):
            if _field.get('data_type') == '数值':
                alias_name = _field.get('alias_name') if _field.get('alias_name') else ''
                num_field_location["col" + str(i + 1)] = alias_name if alias_name else _field.get('col_name')
        return num_field_location

    @staticmethod
    def get_tmp_download_path():
        """
        获取下载的存放目录
        :return:
        """
        tmp_upload_folder = os.path.join(
            os.path.join(
                os.path.join(
                    os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), '../../')), "runtime"
                ),
                "tmp_download",
            ),
            seq_id(),
        )
        if not os.path.exists(tmp_upload_folder):
            os.makedirs(tmp_upload_folder)
        return tmp_upload_folder

    @staticmethod
    def download_oss_file(name, oss_url):
        """
        从oss下载文件到本地
        :return:
        """
        try:
            tmp_upload_folder = DatasetExcelService.get_tmp_download_path()
            tmp_upload_path = os.path.join(tmp_upload_folder, name)
            _url = urlparse(unquote(oss_url))
            root = _url.path if _url.path else ''
            root = root[1:] if root.startswith('/') else root
            OSSFileProxy().download_file_list(root, tmp_upload_folder)
            return tmp_upload_path
        except Exception as ex:
            error_msg = "下载" + oss_url + "文件失败，错误内容：" + str(ex)
            raise UserError(message=error_msg)

    def check_excel_table(self, excel_obj):
        if not self.content_model.select_sheets:
            sheets = excel_obj.sheet_names()
            if sheets:
                self.content_model.select_sheets.append(excel_obj.sheet_names()[0])
            else:
                raise UserError(message="excel没有工作表。")

        if self.content_model.select_sheets and len(self.content_model.select_sheets) > 10:
            raise UserError(message="工作表最多支持10个。")

    @staticmethod
    def check_single_excel_table(sheet):
        if sheet.nrows == 0:
            raise UserError(message='上传文件数据不能为空')
        elif sheet.nrows > 100000:
            raise UserError(message='上传内容不能超过十万行')

    def read_execl_file_data(
            self, file_path, old_dataset_field=None, num_field_location=None, dataset_id=None, append=None
    ):
        """
        读取文件数据
        :return:
        """
        data_info = []
        excel_obj = xlrd.open_workbook(file_path)
        # 做一些工作表检查
        self.check_excel_table(excel_obj)
        for sheet_name in self.content_model.select_sheets:
            excel_data_model = DatasetExceldataModel()
            excel_data_model.sheet_name = sheet_name
            excel_data_model.status = 1
            try:
                temp_dataset_id = dataset_id or seq_id()
                sheet = excel_obj.sheet_by_name(sheet_name)
                # 检查单个sheet内容是否合法
                self.check_single_excel_table(sheet)
                dataset_fields, datas, count = self.read_sheet_data(
                    sheet, dataset_id=temp_dataset_id, old_dataset_field=old_dataset_field
                )
                excel_data_model.data = datas
                excel_data_model.head = dataset_fields
                excel_data_model.field = get_field_group(dataset_fields)
                excel_data_model.count = count
                excel_data_model.status = 1
                # 检测数值字段中是否有数字
                error_fields = []
                error_message = ''
                # 只有追加的时候判断这些
                if append and append == 'append' and old_dataset_field:
                    error_message = self.verification_append_error_message(dataset_fields, old_dataset_field)
                if error_message:
                    excel_data_model.status = 2
                    excel_data_model.error_msg = "解析{}工作表失败，错误内容：{}".format(sheet_name, error_message)
                # 如果数据字段已经不一致了，不需要对数据再做检测, 编辑不需要校验字段
                if num_field_location and not error_message and append and append == 'append':
                    error_fields = self.verification_append_error_fields(datas, num_field_location)

                if error_fields:
                    error_msg = '{}字段要求为数值，该字段中有非数值型数据。'.format('、'.join(set(error_fields)))
                    excel_data_model.status = 2
                    excel_data_model.error_msg = "解析{}工作表失败，错误内容：{}".format(sheet_name, error_msg)
                # 没有错误，才进行模拟创建表操作
                excel_data_model = self.excel_create_and_compare(
                    error_message, error_fields, excel_data_model, dataset_id, temp_dataset_id, dataset_fields
                )
            except Exception as e:
                excel_data_model.status = 0
                excel_data_model.error_msg = str(e)
            finally:
                data_info.append(excel_data_model.get_dict())
        return data_info

    def excel_create_and_compare(
            self, error_message, error_fields, excel_data_model, dataset_id, temp_dataset_id, dataset_fields
    ):
        # 没有错误，才进行模拟创建表操作
        if not error_message and not error_fields:
            # create_table中有对dataset_fields中data_type做转换，excel不需要变化
            new_dataset_fields = copy.deepcopy(dataset_fields)
            (
                excel_data_model.create_table_sql,
                excel_data_model.id,
                excel_data_model.tmp_table_name,
            ) = self.test_create_table(temp_dataset_id, new_dataset_fields)
            if dataset_id:
                excel_data_model.check_results = DatasetFieldCompareService(dataset_id, dataset_fields).compare()
        return excel_data_model

    @staticmethod
    def get_alias_name(dataset_fields):
        return [row.get("alias_name") if row.get("alias_name") else row.get("col_name") for row in dataset_fields]

    def verification_append_error_message(self, dataset_fields, old_dataset_field):
        error_message = ""
        # 校验字段名是否一致
        if len(old_dataset_field) != len(dataset_fields):
            error_message = "数据集字段和追加文件字段不一致。"
        # 还需要考虑顺序
        new_alias_name = self.get_alias_name(dataset_fields)
        old_alias_name = self.get_alias_name(old_dataset_field)
        if new_alias_name != old_alias_name and not error_message:
            error_message = "数据集字段和追加文件字段顺序不一致。"
        for head in dataset_fields:
            is_exist = False
            for dataset_field in old_dataset_field:
                head_name = head.get("alias_name") or head.get("col_name")
                dataset_field_name = (
                    dataset_field.get("alias_name")
                    if dataset_field.get("alias_name")
                    else dataset_field.get("col_name")
                )
                if head_name == dataset_field_name:
                    is_exist = True
                    break
            if not is_exist:
                error_message = "数据集字段和追加文件字段不一致。"
        return error_message

    def verification_append_error_fields(self, datas, num_field_location):
        error_fields = []
        for data in datas:
            for field, alias_name in num_field_location.items():
                # 检测数值类型中，是否有非数值
                try:
                    if data.get(field):
                        int(data.get(field))
                except ValueError:
                    error_fields.append(alias_name if alias_name else field)
        return error_fields

    def test_create_table(self, dataset_id, dataset_fields):
        tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(
            dataset_id, dataset_fields, dataset_type=DatasetType.Excel.value
        )
        return create_table_sql, dataset_id, tmp_table_name

    @staticmethod
    def read_file_sheet(oss_url, file_path):
        """
        读取工作表数据
        :return:
        """
        try:
            data_info = []
            file_type = os.path.splitext(file_path)[1]
            file_suffix = file_type.replace('.', '').lower()
            if file_suffix == DatasetFileType.Xls.value or file_suffix == DatasetFileType.Xlsx.value:
                excel_obj = xlrd.open_workbook(file_path)
                data_info.extend(excel_obj.sheet_names())
            else:
                raise UserError(message="只支持xls和xlsx文件。")
            return data_info

        except Exception as ex:
            raise UserError(message="解析" + oss_url + "文件失败，错误内容：" + str(ex))
        finally:
            if os.path.exists(file_path):
                os.remove(file_path)

    def read_sheet_data(self, sheet, dataset_id=None, old_dataset_field=None):
        """
        读取工作表数据
        :param sheet:
        :return:
        """
        count = 0
        datas = []

        dataset_fields = self.read_head_data(
            sheet.row_values(0), dataset_id=dataset_id, old_dataset_field=old_dataset_field
        )

        for row in range(sheet.nrows):
            if row == 0:
                continue
            # 其实datas是list不需要返回的，为了后续的人更好理解，多一个返回，表示处理datas
            if row <= self.data_limit:
                datas = self.deal_single_row_value(row, sheet, datas, dataset_fields)

            count += 1

        # 补充字段数据类型
        self.add_field_data_type(dataset_fields)

        return dataset_fields, datas, count

    def deal_single_row_value(self, row: int, sheet, datas: list, dataset_fields: list) -> list:
        row_value = {}
        for i, value in enumerate(sheet.row_values(row)):
            value = self._excel_data_format(sheet, row, i)
            if (
                    value
                    and dataset_fields[i].data_type
                    and dataset_fields[i].data_type != DatasetFieldDataType.Description.value
            ):
                dataset_fields[i].data_type = self.get_data_type(value)
            if value and not dataset_fields[i].data_type:
                dataset_fields[i].data_type = self.get_data_type(value)
            # 解决数值0被忽略，默认为字符串类型的问题
            if isinstance(value, int) and value == 0:
                dataset_fields[i].data_type = self.get_data_type(value)
            row_value[dataset_fields[i].col_name] = value

        datas.append(row_value)
        return datas

    @staticmethod
    def add_field_data_type(dataset_fields: list) -> list:
        # 补充字段数据类型
        for i, dataset_field in enumerate(dataset_fields):
            if dataset_field.data_type == IndicatorType.Number.value:
                dataset_field.field_group = DatasetFieldGroup.Measure.value
            else:
                dataset_field.field_group = DatasetFieldGroup.Dimension.value
            if not dataset_field.data_type:
                dataset_field.data_type = DatasetFieldDataType.Description.value
            dataset_fields[i] = dataset_field.get_dict()
        return dataset_fields

    @staticmethod
    def read_head_data(row_values, dataset_id=None, old_dataset_field=None):
        """
        读取表头数据
        :param row_values:
        :param dataset_id:
        :param old_dataset_field:
        :return:
        """
        dataset_fields = []
        # 判断表头是否有重复列
        if len(row_values) != len(set(row_values)):
            repeat_columns = [row for row, row_count in Counter(row_values).items() if row_count > 1]
            raise UserError(message="列名{}重复，请修改。".format('、'.join(repeat_columns)))
        # old_dataset_field 从list改为dict，方便对比
        old_fields = {row.get('alias_name'): row for row in old_dataset_field} if old_dataset_field else {}
        for i, value in enumerate(row_values):
            dataset_field_model = DatasetFieldModel()
            dataset_field_model.alias_name = value
            dataset_field_model.origin_col_name = value
            # dataset_field_model.col_name = "col" + str(i + 1)
            # # 有老的数据集字段，表示编辑
            if old_fields and old_fields.get(value):
                dataset_field_model.col_name = old_fields.get(value).get('col_name')
            # 新增
            else:
                if value is not None and not isinstance(value, str):
                    raise UserError(message=f"表头字段【{value}】不是字符串")
                dataset_field_model.col_name = "{}_{}".format(
                    strings.get_first_pinyin_hanzi(value), strings.fletcher32(dataset_id + ":" + value)
                )
                # 判断col_name长度
                if len(dataset_field_model.col_name) > 64:
                    raise UserError(message='{}字段长度超过64个字符'.format(value))
            dataset_field_model.visible = 1
            dataset_field_model.rank = i + 1
            dataset_fields.append(dataset_field_model)
        return dataset_fields

    @staticmethod
    def get_data_type(value):
        if not is_number(value):

            re_data = re.findall('^(\d{2,4})[.\/\-年]{1}((\d{1,2})[.\/\-月]{1})?((\d{1,2})[日号]?)?', value.strip())

            if re_data and re_data[0][2]:
                # 这里的操作似乎是没有用的，因为根本没用到value,暂时删除，后期有问题加回来
                return DatasetFieldDataType.Datetime.value
            else:
                return DatasetFieldDataType.Description.value
        else:
            return DatasetFieldDataType.Number.value

    @staticmethod
    def _excel_data_format(sheet, row, i):
        """
        excel数据格式处理
        :param row: 行号
        :param i: 单元格号
        :return:
        """
        # ctype： 0 empty,1 string, 2 number, 3 date, 4 boolean, 5 error
        if sheet.cell(row, i).ctype == 2:
            cell = sheet.cell_value(row, i)
            tmp_col_val = int(cell) if cell % 1 == 0 else cell
        elif sheet.cell(row, i).ctype == 3 and sheet.row_values(row)[i] < 1:
            # 只有时间格式
            date_time = str(xlrd.xldate.xldate_as_datetime(sheet.row_values(row)[i], 0)).split(" ")
            tmp_col_val = date_time[1]
        elif sheet.cell(row, i).ctype == 3:
            num = str(sheet.row_values(row)[i]).split('.')
            # 只有日期，没有时间
            if num[1] == '0':
                tmp_col_val = xlrd.xldate.xldate_as_datetime(sheet.row_values(row)[i], 0).strftime('%Y-%m-%d')
            else:
                tmp_col_val = str(xlrd.xldate.xldate_as_datetime(sheet.row_values(row)[i], 0))

        else:
            tmp_col_val = sheet.row_values(row)[i]
        return tmp_col_val

    def _get_file_data_integration(self, data, count):
        """
        文件类型刷新数据集的数据整合
        :param data:
        :param count:
        :return:
        """
        result = []
        columns = []
        columns_tmp = []
        for i, c in enumerate(data):
            if i == 0:
                count = count - 1
                for cur, cur_item in enumerate(data[0]):
                    columns.append({"alias_name": cur_item, "name": 'col' + str(cur + 1)})
                    columns_tmp.append('col' + str(cur + 1))
            else:
                d, columns = self._get_file_data_format(data, columns, i)
                result.append(d)
        return result, columns, columns_tmp

    @staticmethod
    def _get_file_data_format(data, columns, i):
        d = {}
        values = data[i]
        for m, n in enumerate(values):
            columns[m]['type_date_count'] = (
                0 if not columns[m].get('type_date_count') else columns[m].get('type_date_count')
            )
            columns[m]['type_num_count'] = (
                0 if not columns[m].get('type_num_count') else columns[m].get('type_num_count')
            )
            columns[m]['type_str_count'] = (
                0 if not columns[m].get('type_str_count') else columns[m].get('type_str_count')
            )

            if not is_number(values[m]):
                # 修改values和columns
                DatasetExcelService.deal_no_number(m, n, values, columns)
            else:
                columns[m]['type_num_count'] += 1
                values[m] = float(values[m])
            d[columns[m]['name']] = values[m]
        return d, columns

    @staticmethod
    def deal_no_number(m, n, values, columns):
        re_data = re.findall("^(\d{2,4})[.\/\-年]{1}((\d{1,2})[.\/\-月]{1})?((\d{1,2})[日号]?)?", values[m].strip())
        re_time = re.findall('(\d{2}):(\d{2}):(\d{2})', n.strip())

        if re_data and re_data[0][2]:
            if re_data[0][4] and (int(re_data[0][4]) <= 31):
                day = re_data[0][4]
            else:
                day = '1'
            # 对于错误的日期目前采取自动修复方法
            month = '12' if int(re_data[0][2]) > 12 else re_data[0][2]

            fill_time = ''
            if re_time:
                fill_time = ' ' + ':'.join(re_time[0])
            values[m] = re_data[0][0] + '-' + month + '-' + day + fill_time
            columns[m]['type_date_count'] += 1
        else:
            columns[m]['type_str_count'] += 1

    @staticmethod
    def _file_data_get_field(columns, count):
        """
        文件类型获取处理过的字段名
        :param columns:
        :param count:
        :return dict
        """
        for ck, cv in enumerate(columns):
            if not ('type_num_count' in cv.keys() and 'type_str_count' in cv.keys() and 'type_date_count' in cv.keys()):
                raise UserError(message="上传文件格式有误")
            if cv['type_str_count'] == 0 and cv['type_date_count'] == 0:
                columns[ck]['data_type'] = '数值'
            elif cv['type_date_count'] / count > 0.9:
                columns[ck]['data_type'] = '日期'
            else:
                columns[ck]['data_type'] = '字符串'
        return DatasetExcelService.get_fields(columns)

    @staticmethod
    def get_fields(columns):
        """
        获取字段
        :param columns:
        :return dict
        """
        fields = []
        for description in columns:
            field = {
                'col_name': description['name'],
                'alias_name': description['alias_name'],
                'visible': 1,
                'format': '',
            }
            if description['data_type'] == IndicatorType.Datetime.value:
                field['data_type'] = DatasetFieldDataType.Datetime.value
                field['field_group'] = DatasetFieldGroup.Dimension.value
            elif description['data_type'] == IndicatorType.Number.value:
                field['field_group'] = DatasetFieldGroup.Measure.value
                field['data_type'] = DatasetFieldDataType.Number.value
            else:
                field['field_group'] = DatasetFieldGroup.Dimension.value
                field['data_type'] = DatasetFieldDataType.Description.value
            fields.append(field)
        return fields

    def validation_dataset(self, dataset_model):
        try:
            content = json.loads(dataset_model.content)
        except Exception as ex:
            raise UserError(message='数据集content解析错误，错误内容：' + ex.__str__())
        self.content_model = DatasetExcelContentModel(**content)
        self.content_model.validate()
        if not self.validation_url(self.content_model.oss_url):
            raise UserError(message=self.content_model.oss_url + "不合法。")
        if len(os.path.split(self.content_model.oss_url)) < 2:
            raise UserError(message='oss的url格式错误。')

    @staticmethod
    def validation_url(url):
        """
        校验url合法性
        :param url:
        :return:
        """
        if re.match(r'^https?:/{2}\w.+$', url):
            return True
        else:
            return False

    def read_csv_file_data(
            self, file_path, file_name, old_dataset_field=None, num_field_location=None, dataset_id=None, append=None
    ):
        """
        读取csv文件数据
        :return:
        """
        data_info = []
        excel_data_model = DatasetExceldataModel()
        excel_data_model.status = 1
        try:
            if not os.path.getsize(file_path):
                raise UserError(message='上传文件数据不能为空')
            data, count = self.open_csv_file(file_path)
            temp_dataset_id = dataset_id if dataset_id else seq_id()
            excel_data_model.sheet_name = (
                self.content_model.select_sheets[0] if self.content_model.select_sheets else file_name
            )
            dataset_fields, csv_data = self.get_csv_struct(
                data, dataset_id=temp_dataset_id, old_dataset_field=old_dataset_field
            )
            excel_data_model.data = csv_data
            excel_data_model.head = dataset_fields
            excel_data_model.field = get_field_group(dataset_fields)
            excel_data_model.count = count
            # 检测数值字段中是否有数字
            error_fields = []
            error_message = ''
            # 只有追加的时候判断这些
            if append and append == 'append' and old_dataset_field:
                # 校验字段名是否一致
                error_message = self.verification_append_error_message(dataset_fields, old_dataset_field)
            if error_message:
                excel_data_model.status = 2
                excel_data_model.error_msg = "解析文件失败，错误内容：{}".format(error_message)
            # 如果数据字段已经不一致了，不需要对数据再做检测, 编辑不需要校验字段
            if num_field_location and not error_message and append and append == 'append':
                error_fields = self.verification_append_error_fields(csv_data, num_field_location)
            if error_fields:
                error_msg = '{}字段要求为数值，该字段中有非数值型数据。'.format('、'.join(set(error_fields)))
                excel_data_model.status = 2
                excel_data_model.error_msg = "解析文件失败，错误内容：{}".format(error_msg)
            if not error_fields and not error_message:
                # create_table中有对dataset_fields中data_type做转换，excel不需要变化
                new_dataset_fields = copy.deepcopy(dataset_fields)
                (
                    excel_data_model.create_table_sql,
                    excel_data_model.id,
                    excel_data_model.tmp_table_name,
                ) = self.test_create_table(temp_dataset_id, new_dataset_fields)
                if dataset_id:
                    excel_data_model.check_results = DatasetFieldCompareService(dataset_id, dataset_fields).compare()

        except Exception as ex:
            excel_data_model.status = 0
            excel_data_model.error_msg = "解析" + file_name + "文件失败，错误内容：" + str(ex)

        finally:
            data_info.append(excel_data_model.get_dict())
            if os.path.exists(file_path):
                os.remove(file_path)
        return data_info

    def open_csv_file(self, file_path):
        data = []
        count = 0
        try:
            data, count = self.open_csv_use_diff_code(file_path, data, count, 'cp936')
        except UnicodeDecodeError:
            data, count = self.open_csv_use_diff_code(file_path, data, count, 'UTF-8')
        return data, count

    def open_csv_use_diff_code(self, file_path: str, data: list, count: int, encoding: str) -> (list, int):
        with open(file_path, 'r', encoding=encoding) as f:
            for line in f:
                #  去除第一列的 \ufeff BOM 头
                if count < self.data_limit:
                    if count == 0:
                        line = line.replace('\ufeff', '')
                    data.append(line)
                count += 1
        return data, count

    def get_csv_struct(self, data, dataset_id=None, old_dataset_field=None):
        csv_data = []
        if not data:
            return []
        line_datas = csv.reader(data)
        row = 0
        for line in line_datas:
            if row == 0:
                dataset_fields = self.read_head_data(line, dataset_id=dataset_id, old_dataset_field=old_dataset_field)
                row += 1
                continue
            row_value = {}
            for i, value in enumerate(line):
                if (
                        value
                        and dataset_fields[i].data_type
                        and dataset_fields[i].data_type != DatasetFieldDataType.Description.value
                ):
                    dataset_fields[i].data_type = self.get_data_type(value)
                if value and not dataset_fields[i].data_type:
                    dataset_fields[i].data_type = self.get_data_type(value)
                row_value[dataset_fields[i].col_name] = value
            csv_data.append(row_value)
            row += 1
        # 补充字段数据类型
        self.add_field_data_type(dataset_fields)
        return dataset_fields, csv_data

    @staticmethod
    def generate_dataset_field_excel(name, fields):
        """
        生成数据集字段信息excel
        :param name:
        :param fields:
        :return:
        """
        # 下载目录
        tmp_download_folder = DatasetExcelService.get_tmp_download_path()

        file_name = f"{name}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        tmp_download_path = os.path.join(tmp_download_folder, file_name)

        workbook = xlsxwriter.Workbook(tmp_download_path)

        # excel对sheet 名称有一定限制，如名称不符合要求，给默认值
        try:
            worksheet = workbook.add_worksheet(name)
        except:
            worksheet = workbook.add_worksheet("dmp")
        worksheet.protect()

        # 标注信息
        note_text = """
        注意：
        1、不能修改字段名、中文别名
        2、不允许新增、删除字段
                """

        def _get_note_format():
            _format = workbook.add_format({
                "fg_color": "#ffffcc",
                "align": "left",
                "border": 1
            })
            # _format.set_locked(True)
            _format.set_text_wrap()
            return _format

        worksheet.merge_range(0, 0, 0, 5, note_text, _get_note_format())
        worksheet.set_row(0, 70)

        # 表头
        center_fg_format = workbook.add_format({
            "align": "center",
            "bold": True,
            "border": 1,
            "fg_color": "#eeece1"
        })

        center_format = workbook.add_format({
            "align": "center",
            "bold": True,
            "border": 1
        })

        for i, title in enumerate(["分组", "字段名", "中文别名", "备注", "数据类型", "可见性"]):
            if i <= 2:
                _format = center_fg_format
            else:
                _format = center_format
            worksheet.write(1, i, title, _format)

        # # 设置单元格锁定, 不允许修改
        locked = workbook.add_format({
            "align": "left",
            "fg_color": "#eeece1",
            "locked": True,
            "border": 1
        })

        unlocked = workbook.add_format({
            "align": "center",
            "locked": False,
            "border": 1
        })

        # field数据
        for i, data in enumerate(fields):
            i += 2
            j = 0
            worksheet.write(i, j, data.get("groups"), locked)
            worksheet.set_column(i, j, 40)
            j += 1
            worksheet.write(i, j, data.get("col_name"), locked)
            worksheet.set_column(i, j, 40)
            j += 1
            worksheet.write(i, j, data.get("alias_name"), locked)
            worksheet.set_column(i, j, 40)
            j += 1
            worksheet.write(i, j, data.get("note") or data.get("origin_table_comment"), unlocked)
            worksheet.set_column(i, j, 40)
            j += 1
            worksheet.write(i, j, data.get("data_type"), unlocked)
            worksheet.set_column(i, j, 30)
            worksheet.data_validation(f"E{i}",
                                      {"validate": "list", "source": ['字符串', '枚举', '地址', '日期', '数值']})
            j += 1
            worksheet.write(i, j, '是' if data.get("visible") == 1 else "否", unlocked)
            worksheet.set_column(i, j, 30)
            worksheet.data_validation(f"F{i}", {"validate": "list", "source": ['是', '否']})

        # 保存
        workbook.close()
        return tmp_download_folder, file_name

    @staticmethod
    def upload_file_to_oss(file_path, file_name):
        """
        上传文件
        :param file_path:
        :param file_name:
        :return:
        """
        upload_filename = os.path.join(file_path, file_name)
        oss_file_url = OSSFileProxy().upload(
            open(upload_filename, "rb"), file_name=file_name, private=True
        )
        return oss_file_url

    @staticmethod
    def read_excel_of_dataset_fields(oss_url, file_path, dataset):
        """
        读取工作表数据
        :return:
        """
        try:
            fields_list = []
            # 读取excel
            file_type = os.path.splitext(file_path)[1]
            file_suffix = file_type.replace('.', '').lower()
            if file_suffix == DatasetFileType.Xlsx.value:
                excel_obj = xlrd.open_workbook(file_path)
                # 校验sheet_name是否和数据集名称一致
                sheet_names = excel_obj.sheet_names()
                if not sheet_names:
                    raise UserError(message="excel没有工作表")
                sheet = excel_obj.sheet_by_name(dataset.get("name"))
                # 校验表头
                headers = sheet.row_values(1)
                if "字段名" not in headers:
                    raise UserError(message="缺少必要字段：字段名")
                fields_list.append(headers)
                for i in range(2, sheet.nrows):
                    fields_list.append(sheet.row_values(i))
                return fields_list
            else:
                raise UserError(message="只支持xlsx文件。")
        except XLRDError as e:
            raise UserError(message="数据集名称不匹配") from e
        except UserError as e:
            raise e
        except Exception as ex:
            # raise UserError(message="解析" + oss_url + "文件失败，错误内容：" + str(ex))
            raise UserError(message="文件模版错误，解析失败：" + str(ex))
        finally:
            if os.path.exists(file_path):
                os.remove(file_path)
