# -*- coding: utf-8 -*-
import json
from base import repository
from base.enums import DatasetFieldGroup, DatasetFieldDataType, DatasetFieldType, DatasetType, DatasetConnectType
from components.dateset_generate_col_name import generate_new_col_name
from dataset.cache import dataset_field_meta_cache
from dataset.repositories import dataset_field_repository
from dataset.services.advanced_field_service import check_field_is_used
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id


def get_group_field(dataset_field_id):
    """
    获取分组字段
    :return:
    """
    result = dataset_field_repository.get_dataset_field_info(dataset_field_id)
    if not result:
        raise UserError("未找到该分组字段")
    result["expression"] = json.loads(result.get("expression")) if result.get("expression") else {}

    result["origin_dataset_field_id"] = result.get("expression").get("origin_dataset_field_id")
    # 转换名称，跟之前保存保持一致
    result["dataset_id"] = result.pop("id")
    result["id"] = result.pop("df.id")
    origin_dataset_field = repository.get_data(
        "dataset_field", {"id": result.get("origin_dataset_field_id")}, fields=["col_name", "data_type"]
    )
    if not origin_dataset_field:
        raise UserError("原始分组字段已被删除")
    result["origin_data_type"] = origin_dataset_field.get("data_type")
    if not result.get("origin_col_name"):
        result["origin_col_name"] = origin_dataset_field.get("col_name")
    return result


def deal_group_field_by_dataset_filed(dataset_fields, col_name):
    """
    获取分组字段
    :return:
    """
    dataset_filed = dataset_fields.get(col_name)
    if isinstance(dataset_filed.get("expression"), str):
        dataset_filed["expression"] = json.loads(dataset_filed.get("expression"))

    dataset_filed["origin_dataset_field_id"] = dataset_filed.get("expression").get("origin_dataset_field_id")
    reference_col_name = (
        dataset_filed.get("expression").get("reference_col_name")
        if dataset_filed.get("expression").get("reference_col_name")
        else dataset_filed.get("expression").get("origin_col_name")
    )
    origin_dataset_field = dataset_fields.get(reference_col_name)
    if not origin_dataset_field:
        raise UserError(message="原始分组字段已被删除")
    dataset_filed["origin_data_type"] = origin_dataset_field.get("data_type")
    if dataset_filed.get("origin_col_name"):
        dataset_filed["sql_col_name"] = dataset_filed.get("origin_col_name")
    elif dataset_filed.get("expression").get("origin_col_name"):
        dataset_filed["sql_col_name"] = dataset_filed.get("expression").get("origin_col_name")
    else:
        dataset_filed["sql_col_name"] = origin_dataset_field.get("col_name")
    return dataset_filed


def update_group_field(model):
    """
    更新分组字段
    :return:
    """
    if not model.expression:
        model.expression = {}
    # alias_name 不能重复
    if not model.id and repository.data_is_exists(
        "dataset_field", {'dataset_id': model.dataset_id, "alias_name": model.alias_name}
    ):
        raise UserError(message="{}名称重复".format(model.alias_name))
    if model.id:
        field = repository.get_data(
            "dataset_field", {'dataset_id': model.dataset_id, "alias_name": model.alias_name}, fields=["id"]
        )
        if field and field.get("id") != model.id:
            raise UserError(message="{}名称重复".format(model.alias_name))
    # 需要至少一个分组
    if not model.expression.get("groups") and not model.expression.get("range"):
        raise UserError(message="请添加至少一个分组")
    # 生成col_name
    model.col_name = generate_new_col_name(model.dataset_id, model.alias_name)
    # api和直连数据集还需获取原始表名和字段名
    origin_dataset = repository.get_data("dataset", {"id": model.dataset_id}, ["type", "connect_type"])
    origin_dataset_field = repository.get_data(
        "dataset_field", {"id": model.origin_dataset_field_id}, ["origin_col_name", "origin_table_name", "col_name"]
    )
    if (
        origin_dataset.get("type") in [DatasetType.ExternalSubject.value]
        or origin_dataset.get("connect_type") == DatasetConnectType.Directly.value
    ):
        model.expression["origin_col_name"] = origin_dataset_field.get("origin_col_name")
        model.expression["reference_col_name"] = origin_dataset_field.get("col_name")
        model.origin_table_name = origin_dataset_field.get("origin_table_name")
    else:
        model.expression["origin_col_name"] = origin_dataset_field.get("col_name")
    if model.origin_data_type not in [
        DatasetFieldDataType.Description.value,
        DatasetFieldDataType.Datetime.value,
        DatasetFieldDataType.Number.value,
    ]:
        raise UserError(message=u"分组字段目前只支持字符串、数组和时间类型")
    # 分组字段统一处理成字符，维度
    model.data_type = DatasetFieldDataType.Description.value
    model.visible = 1
    model.field_group = DatasetFieldGroup.Dimension.value
    model.type = DatasetFieldType.Group.value
    model.rank = repository.get_data_max_rank('dataset_field', 'rank', {'dataset_id': model.dataset_id})
    model.expression["origin_dataset_field_id"] = model.origin_dataset_field_id

    model.expression = json.dumps(model.expression, ensure_ascii=False)
    fields = [
        'id',
        'dataset_id',
        'alias_name',
        'col_name',
        'data_type',
        'visible',
        'field_group',
        'rank',
        'type',
        'group_type',
        'expression',
        'origin_table_alias_name',
        'origin_table_name',
    ]
    if not model.id:
        model.id = seq_id()
        repository.add_model('dataset_field', model, fields)
        # 添加数据集字段元数据缓存 、设置数据集多字段元数据缓存
        dataset_field_meta_cache.add_dataset_field_cache(model.get_dict())
    else:
        repository.update_model('dataset_field', model, {"id": model.id}, fields)
        # 修改数据集字段元数据缓存 、设置数据集多字段元数据缓存
        # 为保持缓存中dataset_field一致
        dataset_field = dataset_field_repository.get_dataset_field(model.dataset_id, condition={"id": model.id})
        dataset_field_meta_cache.update_dataset_field_cache(dataset_field[0])
    return model.id


def delete_group_field(dataset_field_id):
    """
    删除分组字段
    :return:
    """
    dataset_field = repository.get_data('dataset_field', {'id': dataset_field_id})
    if not dataset_field:
        raise UserError(message='不存在此用户字段')
    if dataset_field['type'] == DatasetFieldType.Normal.value:
        raise UserError(message='参数非法')
    # 检查当前删除字段是否已经被使用
    if check_field_is_used(dataset_field_id):
        raise UserError(message='字段已被使用，不可删除')

    # 相同记录只存一条, 添加到已删除表中
    if not repository.data_is_exists("dataset_field_delete", {"dataset_field_id": dataset_field.get("id")}):
        # 只取部分数据
        dataset_field_delete = dict()
        dataset_field_delete["dataset_id"] = dataset_field.get("dataset_id")
        dataset_field_delete["dataset_field_id"] = dataset_field.get("id")
        dataset_field_delete["col_name"] = dataset_field.get("col_name")
        dataset_field_delete["origin_col_name"] = dataset_field.get("alias_name")
        dataset_field_delete["type"] = dataset_field.get("type")
        repository.add_data('dataset_field_delete', dataset_field_delete)

    repository.delete_data("dataset_field", {"id": dataset_field_id})
    # 删除数据集字段元数据缓存 、删除数据集多字段元数据缓存
    dataset_field_meta_cache.delete_dataset_field_cache(dataset_field)
    return True
