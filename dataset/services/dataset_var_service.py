#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/3/29 10:47
# <AUTHOR> caoxl
# @File     : dataset_var_service.py
# pylint: disable=W0108
# pylint: disable=W0703
# pylint: disable=E1101
import json

from components.query_models import Var
from dataset.cache import dataset_include_vars_cache
from dataset.services.dataset_var_value_parser import DatasetVarValueParser
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from ..models import DatasetVarModel
from base import repository
from dataset.repositories import dataset_var_repository
from dashboard_chart.services import external_dashboard_service
from dataset.services.dataset_var_parser import DatasetVarParser
from dataset.services.dataset_field_check_service import DatasetVarsCompare
from keywords.external_service import (
    delete_keyword_detail,
    get_keyword_detail_by_vars,
    save_keyword_detail_by_var_id,
    get_keyword_detail_special_flag_by_vars
)
from typing import Any, List
from base.enums import DatasetEditMode


def edit_dataset_var(dataset_vars: dict = None):
    """
    编辑数据集变量
    :param dataset_vars:
    :return:
    """
    dataset_var_model = DatasetVarModel(**dataset_vars)
    dataset_var_model.validate()
    data = dataset_var_model.get_dict()
    if not dataset_var_model.id:
        # 新增
        dataset_var_model.id = seq_id()
        dataset_vars['id'] = dataset_var_model.id
        result = repository.add_model("dataset_vars", dataset_var_model)
    else:
        # 更新
        data = dataset_var_model.get_dict()
        used_var_ids = get_dataset_used_var_ids(dataset_var_model.dataset_id)
        if dataset_var_model.id in used_var_ids:
            data = dataset_var_model.get_dict(["name", "description", "default_value", "accurate_type"])
        result = repository.update_data("dataset_vars", data, {"id": dataset_var_model.id})
    # 更新关键字和变量的关系
    vars_list = [dataset_vars]
    save_keyword_detail_by_var_id(dataset_var_model.id, vars_list)
    # 删除缓存
    dataset_include_vars_cache.del_dataset_include_vars_cache([dataset_var_model.dataset_id])
    return result, data


def get_dataset_used_var_ids(dataset_id: str):
    used_var_ids = DatasetVarsCompare.get_dataset_used_vars_info(dataset_id)[0]
    dataset_data = repository.get_one('dataset', {'id': dataset_id})
    if dataset_data and dataset_data['content']:
        sql = json.loads(dataset_data['content']).get('sql')
        if sql:
            sql_var_ids = DatasetVarParser.get_var_names_from_sql_str(sql) or []
            used_var_ids.extend(sql_var_ids)
    return used_var_ids


def multi_upset_dataset_vars(dataset_id, var_data_list: list):
    """
    批量更新数据集变量
    :param dataset_id: 数据集ID
    :param var_data_list: 数据集的全部变量列表，如果不存在，则没有变量id，则新增，有则更新，如果老的变量在列表中不存在，则老的将被删除
    :return:
    """
    if not isinstance(var_data_list, list):
        raise UserError(message='变量数据必须为列表类型')

    dataset_vars = get_dataset_vars(dataset_id)
    var_data_map = {r['id']: r for r in dataset_vars}
    var_ids = [r['id'] for r in dataset_vars]
    # 新增变量
    to_insert = []
    # 更新变量
    to_update = []
    to_update_ids = []
    field_names = [
        "id",
        "name",
        "description",
        "var_type",
        "value_type",
        "dataset_id",
        "default_value",
        "default_value_type",
        "accurate_type",
        "external_content"
    ]

    var_names = set()
    for var_data in var_data_list:
        var_name = var_data['name']
        if var_name in var_names:
            raise UserError(message='变量名称【%s】重复，请修改变量名称')
        var_names.add(var_name)
        var_data.setdefault('dataset_id', dataset_id)
        if 'external_content' not in var_data:
            var_data['external_content'] = ''
        var_data.setdefault('id', seq_id())
        var_id = var_data['id']
        data = {f: var_data[f] for f in field_names}
        if var_id not in var_ids:
            to_insert.append(data)
        else:
            to_update.append(data)
            to_update_ids.append(var_id)
    # 删除变量
    to_delete = list(set(var_ids) - set(to_update_ids))
    cited_var_ids = DatasetVarsCompare.check_var_ids_is_used(to_delete, dataset_id)
    if cited_var_ids:
        var_names = ','.join([var_data_map[var_id]['name'] for var_id in cited_var_ids])
        raise UserError(message='变量【%s】在数据集中有被引用，请先解除引用关系再删除' % var_names)

    if to_update:
        for var_data in to_update:
            var_id = var_data['id']
            old_var_data = var_data_map[var_id]
            if not var_id in cited_var_ids:
                to_insert.append(var_data)
                continue
            if old_var_data['var_type'] != var_data['var_type'] or old_var_data['value_type'] != var_data['value_type']:
                raise UserError(message='变量【%s】被引用，禁止修改其字段类型和值类型' % old_var_data['name'])
            to_insert.append(var_data)

    repository.delete_data('dataset_vars', {'dataset_id': dataset_id})
    if to_insert:
        repository.add_list_data('dataset_vars', to_insert, field_names)

    # 删除缓存
    dataset_include_vars_cache.del_dataset_include_vars_cache([dataset_id])
    return True


def delete_dataset_var(var_id: str):
    """
    删除变量
    删除变量前需要判断该变量是否在字段有引用 及 该变量是否被绑定
    :param var_id:
    :return:
    """
    dataset_var = repository.get_one("dataset_vars", {"id": var_id})
    if not dataset_var:
        raise UserError(message="指定的变量不存在！")
    completely_check_dataset_var_is_used(var_id)
    # 删除缓存
    dataset_include_vars_cache.del_dataset_include_vars_cache([dataset_var.get("dataset_id")])
    delete_keyword_detail([var_id])
    return bool(repository.delete_data("dataset_vars", {"id": var_id}))


def check_dataset_var_is_used(var_id: str):
    dataset_vars = dataset_var_repository.get_dataset_var_by_ids([var_id])
    if not dataset_vars:
        return False

    dataset_id = dataset_vars[0]['dataset_id']
    check_result = DatasetVarsCompare.check_var_ids_is_used([var_id], dataset_id)
    return bool(check_result)


def get_dataset_vars(dataset_id: str) -> List[Any]:
    """
    获取数据集变量
    :param dataset_id:
    :return:
    """
    data = repository.get_list("dataset_vars", {"dataset_id": dataset_id}, order_by=[('name', 'asc')])
    # 获取变量背后绑定的关键字信息
    get_keyword_detail_by_vars(data)
    return data


def get_dataset_var_data(dataset_id: str):
    vars_data = get_dataset_vars(dataset_id)
    check_result = DatasetVarsCompare.get_dataset_used_vars_info(dataset_id)
    all_used_var_ids = check_result[0]
    for var_data in vars_data:
        var_id = var_data['id']
        var_data['is_used'] = var_id in all_used_var_ids
    return vars_data


def get_vars_by_varid(dataset_var_ids: list) -> list:
    return repository.get_list("dataset_vars", {"id": dataset_var_ids})


def batch_get_dataset_var_default_values(dataset_vars: list, dashboard_id: str) -> dict:
    result = dict()
    if not dataset_vars or not dashboard_id:
        return result
    var_list = dataset_var_repository.get_dataset_var_by_ids_and_did(dataset_vars, dashboard_id)
    # 获取变量背后绑定的关键字信息
    get_keyword_detail_by_vars(var_list)
    dataset_var_value_parser = DatasetVarValueParser()
    for dataset_var in var_list:
        default_value = dataset_var.get("default_value", "")
        loaded_default_value = json.loads(default_value) if default_value else default_value
        dataset_var["default_value"] = loaded_default_value
        dataset_var["value"] = default_value
        result[dataset_var.get("id")] = dataset_var_value_parser.parse(Var(**dataset_var))
    return result


def batch_get_dataset_var_info(dataset_vars: list, dashboard_id: str) -> dict:
    result = dict()
    if not dataset_vars or not dashboard_id:
        return result
    var_list = dataset_var_repository.get_dataset_var_by_ids_and_did(dataset_vars, dashboard_id)
    # 获取变量背后绑定的关键字信息
    get_keyword_detail_by_vars(var_list)
    dataset_var_value_parser = DatasetVarValueParser()
    for dataset_var in var_list:
        default_value = dataset_var.get("default_value", "")
        loaded_default_value = json.loads(default_value) if default_value else default_value
        dataset_var["default_value"] = loaded_default_value
        dataset_var["value"] = default_value
        dataset_var['parsed_value'] = dataset_var_value_parser.parse(Var(**dataset_var))
        result[dataset_var.get("id")] = dataset_var
    return result


def batch_get_dataset_include_vars(dataset_ids: list) -> List[Any]:
    """
    批量获取数据集引用变量情况
    :param dataset_ids:
    :return:
    """
    dataset_vars = dataset_include_vars_cache.get_dataset_include_vars_cache(dataset_ids)
    # 获取变量背后绑定的关键字信息
    get_keyword_detail_by_vars(dataset_vars)
    return dataset_vars


def batch_get_dataset_vars(dataset_ids: list):
    """
    批量获取数据集变量
    :param dataset_ids:
    :return:
    """
    return dataset_var_repository.batch_get_dataset_vars(dataset_ids)


def parse_dataset_var(dataset_var: Var, test_mode=False):
    """
    解析数据集变量
    值类型为列表时解析为 ('a', 'b', 'c') 模式
    值类型为区间时解析为 'a' AND 'b' 模式
    值类型为任意值时解析为  'a' 模式
    :param  components.query_models.Var dataset_var:
    :param  test_mode: 是否是测试模式
    :return:
    """
    try:
        dataset_var_parser = DatasetVarParser()
        dataset_var_value_parser = DatasetVarValueParser()
        dataset_var.value = dataset_var_value_parser.parse(dataset_var)
        return dataset_var_parser.parse(dataset_var, test_mode)
    except Exception as e:
        raise UserError(message="抱歉，解析变量时发生错误, 错误信息: {error}".format(error=str(e)))


def delete_vars_by_datasetid(dataset_id: str):
    """
    通过数据集ID删除变量
    前置条件：
    1. 变量不能有绑定关系
    同步操作：
    1. 删除变量
    2. 删除变量引用关系
    3. 删除变量引用缓存
    :param dataset_id:
    :return:
    """
    dataset_vars = repository.get_list("dataset_vars", {"dataset_id": dataset_id})
    from dashboard_chart.services.chart_service import METADATA_CONFIG
    is_new_jump = METADATA_CONFIG.get_is_new_jump()
    for dataset_var in dataset_vars:
        var_id = dataset_var['id']
        var_name = dataset_var['name']
        if external_dashboard_service.get_related_dashboard_by_var_id(var_id):
            raise UserError(message="变量%s被图表绑定，请先解除绑定关系!" % var_name)
        if repository.get_list('dashboard_vars_value_source_relation', {'var_id': var_id}):
            if is_new_jump and is_new_jump == 0:
                raise UserError(message='变量%s在报告变量配置中使用，请先解除绑定关系!' % var_name)
            else:
                repository.delete_data("dashboard_vars_value_source_relation", {'var_id': var_id})
    # 1. 删除变量
    repository.delete_data("dataset_vars", {"dataset_id": dataset_id})
    # 2. 删除引用关系
    repository.delete_data("dataset_field_include_vars", {"dataset_id": dataset_id})
    # 3. 删除缓存
    dataset_include_vars_cache.del_dataset_include_vars_cache([dataset_id])
    return True


def delete_var_relations_by_fieldid(field_id: str):
    """
    通过字段ID删除变量引用关系
    前置条件: 无
    同步操作:
    1. 删除变量引用关系
    2. 删除变量引用缓存
    :param field_id:
    :return:
    """
    # 1. 删除引用关系
    repository.delete_data("dataset_field_include_vars", {"field_id": field_id})
    # 2. 删除缓存
    field = repository.get_one("dataset_field", {"id": field_id})
    if field:
        dataset_include_vars_cache.del_dataset_include_vars_cache([field.get("dataset_id")])
    return True


def completely_check_dataset_var_is_used(var_id: str):
    var_data = repository.get_one('dataset_vars', {'id': var_id})
    if not var_data:
        raise UserError(message="指定的变量不存在")

    dataset_id = var_data['dataset_id']
    dataset_data = repository.get_one('dataset', {'id': dataset_id})
    if not dataset_data:
        raise UserError(message="变量所属的数据集不存在")

    dataset_content = dataset_data['content']
    if dataset_content:
        dataset_content = json.loads(dataset_content)
        sql = dataset_content.get('sql', '')
        if sql.find('${%s}' % var_id) >= 0:
            raise UserError(message="变量在数据集的sql中所使用，请先解除引用")

    if repository.get_list("dataset_field_include_vars", {"var_id": var_id}):
        raise UserError(message="该变量被高级字段引用，请先解除引用关系!")

    if external_dashboard_service.get_related_dashboard_by_var_id(var_id):
        raise UserError(message="该变量被图表绑定，请先解除绑定关系!")

    if repository.get_list('dashboard_vars_value_source_relation', {'var_id': var_id}):
        raise UserError(message='该变量在报告变量配置中使用，请先解除绑定关系!')


def get_dataset_sql_used_var_ids_by_dataset_id(dataset_id: str):
    """
    获取数据集sql中使用的变量ID
    :param dataset_id:
    :return: 变量id列表
    """
    var_ids = []
    dataset_data = repository.get_one('dataset', {'id': dataset_id})
    if not dataset_data or not dataset_data['content']:
        return var_ids
    sql = json.loads(dataset_data['content']).get('sql')
    if sql:
        sql_var_ids = DatasetVarParser.get_var_names_from_sql_str(sql) or []
        var_ids.extend(sql_var_ids)
    elif dataset_data['edit_mode'] == DatasetEditMode.Relation.value:
        sql = json.loads(dataset_data['content']).get('replace_sql')
        if not sql and dataset_data['relation_sql']:
            sql = json.loads(dataset_data['relation_sql']).get('sql')
        if sql:
            sql_var_ids = DatasetVarParser.get_var_names_from_sql_str(sql) or []
            var_ids.extend(sql_var_ids)
    # 数芯1.5 支持 关系型 引入变量，这个变量值是可以传给数芯的
    dataset_vars = repository.get_list('dataset_vars', {'dataset_id': dataset_id})
    for var in dataset_vars:
        if var.get('external_content') and var.get('id') not in var_ids:
            var_ids.append(var.get('id'))
    return var_ids
