# pylint: skip-file

from base import repository
from base.enums import DatasetPermissionType, DataSourceType, DatasetStorageType, DatasetConnectType
from components.data_center_api import get_data_by_sql, get_new_erp_datasource_model
from dmplib import config
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dataset.models import DatasetPermissionModel
from dataset.repositories import dataset_repository
from rbac import external_service
from self_service.services.external_api_service import get_permission_fields

import json
import logging
logger = logging.getLogger(__name__)
logger.setLevel(config.get("Log.level", "INFO"))

from user import external_user_service
from components.versioned_query import shield_versioned


def add_dataset_permission(dataset_permission):
    # 新增，修改数据集权限
    validate_add_dataset_permission(dataset_permission)
    dataset_permission.id = dataset_permission.id if dataset_permission.id else seq_id()
    exist_dataset_permission = repository.get_one("dataset_permission", {'id': dataset_permission.id})
    same_name_dataset_permission = repository.get_one("dataset_permission", {'name': dataset_permission.name})
    if exist_dataset_permission:  # 修改
        if same_name_dataset_permission and same_name_dataset_permission.get('id') != dataset_permission.id:
            raise UserError(message='数据集权限名称已存在，不允许修改')
        dataset_permission.type = exist_dataset_permission.get('type')
        repository.update_model("dataset_permission", dataset_permission, {'id': dataset_permission.id})
        # 刷新数据集行列权限
        from dataset.services import dataset_service

        dataset_service.refresh_dataset_row_permissions()

    else:  # 新增
        if same_name_dataset_permission:
            raise UserError(message='数据集权限名称已存在，不允许添加')
        dataset_permission.type = DatasetPermissionType.Dataset.value
        repository.add_model("dataset_permission", dataset_permission)
    return dataset_permission.id


def validate_add_dataset_permission(dataset_permission):
    # 新增修改前校验
    dataset = repository.get_one('dataset', {"id": dataset_permission.dataset_id})
    if not dataset:
        raise UserError(message='数据集不存在')
    if not repository.data_is_exists(
        'dataset_field', {"id": dataset_permission.user_code_field_id, "dataset_id": dataset_permission.dataset_id}
    ):
        raise UserError(message='用户Code字段在当前权限数据集中不存在')
    if not repository.data_is_exists(
        'dataset_field',
        {"id": dataset_permission.permission_associated_field_id, "dataset_id": dataset_permission.dataset_id},
    ):
        raise UserError(message='权限对象ID字段在当前权限数据集中不存在')


def delete_dataset_permission(dataset_permission_id):
    # 删除权限数据集
    if not repository.data_is_exists('dataset_permission', {"id": dataset_permission_id}):
        raise UserError(message='当前权限数据集不存在，请刷新页面再试')
    result, used_dataset_id = check_dataset_permission_is_used(dataset_permission_id)
    if result:
        used_dataset = repository.get_one('dataset', {'id': used_dataset_id})
        if used_dataset:
            used_dataset_name = used_dataset.get('name')
            raise UserError(message=f'已被数据集【{used_dataset_name}】引用，不能删除')
    return True, 'success', repository.delete_data('dataset_permission', {"id": dataset_permission_id})


def check_dataset_permission_is_used(dataset_permission_id):
    from dmplib.saas.project import get_project_info
    from base.enums import DatasetPermissionModel
    project = get_project_info(g.code)
    del_table = "dataset_role_filter"
    if project and project.get("dataset_permission_model") == DatasetPermissionModel.Alone.value:
        del_table = "dataset_permission_filter"
    # 检查权限数据集是否被引用
    dataset_role_filter_list = repository.get_list(del_table, None)
    for dataset_role_filter in dataset_role_filter_list:
        dataset_filter_str = dataset_role_filter.get('dataset_filter')
        if dataset_filter_str:
            dataset_filter_list = json.loads(dataset_filter_str)
            for dataset_filter in dataset_filter_list:
                if (
                    dataset_filter.get('operator') == DatasetPermissionType.Dataset.value
                    and dataset_filter.get('dataset_permission_id') == dataset_permission_id
                ):
                    return True, dataset_filter.get('dataset_id')
    return False, None


# 检查数据集权限是否被引用


def get_dataset_permission_list():
    # 获取用户权限数据集
    data = dataset_repository.get_dataset_permission_list()
    default_dataset_permission = DatasetPermissionModel(
        name='默认数据集权限', id=seq_id(), type=DatasetPermissionType.UserOrg.value
    ).get_dict()
    default_dataset_permission['dataset_name'] = '用户权限数据集'
    default_dataset_permission['dataset_table_name'] = 'user_organization'
    default_dataset_permission['user_code_field_name'] = 'user_id'
    default_dataset_permission['permission_associated_field_name'] = 'org_level'
    default_dataset_permission['org_level'] = [val.get('org_level') for val in external_service.get_user_org_level()]
    data.append(default_dataset_permission)

    # 判断是否使用API权限
    if config.get('SelfService.permission_type') == DatasetPermissionType.Api.value:
        api_dataset_permission = DatasetPermissionModel(
            name='API权限', id=seq_id(), type=DatasetPermissionType.Api.value
        ).get_dict()
        # permission_fields = [{"name": "分期", "identifier": "api.fq"}, {"name": "合同", "identifier": "api.ht"}]
        try:
            permission_fields = get_permission_fields(g.code, g.account)
        except Exception as e:
            logger.exception(str(e))
            permission_fields = []
        api_dataset_permission['permission_fields'] = permission_fields
        data.append(api_dataset_permission)

    return data


def get_dataset_permission_by_id(dataset_permission_id):
    return repository.get_data(
        "dataset_permission",
        {"id": dataset_permission_id},
        ["id", "name", "dataset_id", "user_code_field_id", "permission_associated_field_id", "type", "remark"],
    )


def get_user_dataset_permission_field_value(user_id: str, dataset_permission_id: str):
    # 获取权限数据集对应用户可查询参数
    dataset_permission = get_dataset_permission_by_id(dataset_permission_id)
    if (
        not dataset_permission
        or not dataset_permission.get('user_code_field_id')
        or not dataset_permission.get('dataset_id')
    ):  # 权限数据集被删除，查询不到数据
        return None
    dataset = repository.get_one('dataset', {"id": dataset_permission.get('dataset_id')})
    if not dataset:  # 数据集被删除，查询所有
        return None
    with shield_versioned():
        user_code_dataset_field = repository.get_one(
            'dataset_field',
            {"id": dataset_permission.get('user_code_field_id'), "dataset_id": dataset_permission.get('dataset_id')},
        )
    if not user_code_dataset_field:  # 权限数据集被删除，查询不到数据
        return None
    with shield_versioned():
        associated_dataset_field = repository.get_one(
            'dataset_field',
            {
                "id": dataset_permission.get('permission_associated_field_id'),
                "dataset_id": dataset_permission.get('dataset_id'),
            },
        )
    if not associated_dataset_field:  # 权限数据集被删除，查询不到数据
        return None

    connect_type = dataset.get('connect_type')
    if connect_type == DatasetConnectType.Directly.value:
        associated_field = associated_dataset_field.get('origin_col_name')
        user_code_field = user_code_dataset_field.get('origin_col_name')
    else:
        associated_field = associated_dataset_field.get('col_name')
        user_code_field = user_code_dataset_field.get('col_name')

    user = external_user_service.get_user_by_id(user_id)
    if not user:
        raise UserError(message='当前用户不存在，请重新登录')

    from dataset.external_query_service import get_dataset_data
    from components.query_structure_sql import ModelEncoder
    from components.query_models import QueryStructure, Select, Where, Prop
    query_structure = QueryStructure()
    select = Select()
    select.prop_name = associated_field
    select.alias = 'org_name'
    where = Where()
    where.left = Prop(prop_name=user_code_field)
    where.right = Prop(value=user.get('account'))
    where.operator = '='
    query_structure.select.append(select)
    query_structure.where.append(where)
    query_params = {
        "user_id": user_id,
        "dataset_id": dataset_permission.get('dataset_id'),
        "chart_id": "",
        "query_structure_json": json.dumps(query_structure, cls=ModelEncoder),
    }
    data = get_dataset_data(**query_params)
    return data.get('data', None)


def refresh_dataset_row_permissions_by_dataset_id(dataset_id):
    # 权限数据集被使用，数据集发生变化时，需要刷新数据集行列权限
    dataset_permission_list = repository.get_list('dataset_permission', {"dataset_id": dataset_id})
    for dataset_permission in dataset_permission_list:
        if check_dataset_permission_is_used(dataset_permission.get('id')):
            from dataset.services import dataset_service

            dataset_service.refresh_dataset_row_permissions()
            break


def relate_dataset_permission_datasets(dataset_id: str) -> list:
    result = []
    if not dataset_id:
        return result

    dataset_permission_list = repository.get_list('dataset_permission', {"dataset_id": dataset_id})
    for dataset_permission in dataset_permission_list:
        dataset_permission['type'] = 'dataset_permission'
        result.append(dataset_permission)
    return result
