# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    Created by chenc04 on 2017/10/20.
"""
import pymysql

from base import repository
from base.enums import DatasetType, DatasetEditMode
from data_source.models import ColumnQueryModel
from data_source.repositories import mysql_data_source_repository
from dataset.common import dataset_data_helper
from dataset.models import DatasetDataModel
from dataset.services.dataset_base_service import DatasetBaseService, record_execute_time
from dmplib.utils import sql_util
from dmplib.utils.errors import UserError
from dmplib.utils.sql_util import Description
from dmplib.utils.strings import seq_id
from dataset.common.sql_helper import extract_tables


class DatasetMysqlService(DatasetBaseService):
    """
    Mysql数据集业务类
    """

    def __init__(self, dataset_model, data_source_model):
        super().__init__(dataset_model, data_source_model)

    def run_get_data_or_struct(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（重写父类方法）
        :return: dataset.models.DatasetDataModel
        """

        # 1、加载数据集内容
        dataset_content = self.load_dataset_content(self.dataset_model.content)

        # 4、测试运行创建表
        if dataset_content.get("dataset_id"):
            dataset_id = dataset_content.get("dataset_id")
        elif getattr(self.dataset_model, "id"):
            dataset_id = getattr(self.dataset_model, "id")
        else:
            dataset_id = seq_id()
        self.dataset_model.id = dataset_id

        sql, meta_data = self.get_query_sql_and_metadata(dataset_id)
        sql = self.replace_vars(sql)
        total_count = self.run_get_data_count()

        # 2、根据sql语句获取数据和字段结构
        result_data, columns = self.get_data_or_columns(sql)
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            tmp_table_name, new_struct, create_table_sql = meta_data
        else:
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(dataset_id, columns)

        # 5、根据SQL匹配出所有的表名，查询表名的字段备注，用于补充字段结构里面的别名。
        field_comments = self.get_field_comment(sql)

        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = dataset_id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = columns
        dataset_data_model.tmp_table_name = tmp_table_name
        dataset_data_model.new_column_struct = new_struct
        dataset_data_model.create_table_sql = create_table_sql
        dataset_data_model.field_comments = field_comments
        dataset_data_model.total_count = total_count

        return dataset_data_model

    def get_data_source_conn(self):
        return mysql_data_source_repository.get_mysql_db(self.data_source_model)

    def _build_data_center_api_params(self, sql):
        return {
            "DataInfo": {
                "SqlText": sql,
                "DataSourceModel": {
                    "ConfigType": 1,
                    "AppLevelCode": "",
                    "DbType": "sqlserver",
                    "Server": self.data_source_model.conn_str.host,
                    "Port": self.data_source_model.conn_str.port,
                    "Database": self.data_source_model.conn_str.database,
                    "Uid": self.data_source_model.conn_str.user,
                    "Pwd": self.data_source_model.conn_str.password
                }
            }
        }

    @record_execute_time()
    def get_data_or_columns(self, query_sql):
        """
        根据sql语句获取数据和结构
        :return:
        """
        # sql语句校验
        sql = self.validation_sql(query_sql)
        sql_util.validate_key_word(sql)
        sql, data_limit = self.mysql_limit(sql, self.data_limit)
        try:
            # 根据sql语句查询数据
            with self.get_data_source_conn() as db:
                # 获取前100条数据
                if isinstance(data_limit, list):
                    tmp_result_data = db.query(sql, offset=data_limit[0], limit=data_limit[1])
                else:
                    tmp_result_data = db.query(sql, limit=data_limit)
                result_data = dataset_data_helper.data_processing(tmp_result_data)
                # 获取数据结构
                columns = [Description(*col) for col in list(db.cur.description)]
                structs = []
                for column in columns:
                    struct = {
                        "col_name": column.name,
                        "data_type": column.col_type,
                        "comment": column.alias_name,
                        "origin_field_type": column.col_type,
                    }
                    structs.append(struct)
                return result_data, structs
        except pymysql.Error as e:
            raise UserError(message='sql语句：' + sql + ' ,sql failed：' + str(e))
        except Exception as ex:
            raise UserError(message='sql语句：' + sql + ' ,运行错误：' + str(ex))

    def run_get_data_count(self, query_sql: str = ''):
        """
        运行数据集，产生总条数
        :return:
        """
        total_count = 0
        content = self.load_dataset_content(self.dataset_model.content)
        try:
            if self.dataset_model.type == DatasetType.Sql.value:
                sql = self.replace_vars(self.generate_sql_dataset_count_sql())
                with self.get_data_source_conn() as db:
                    result_data = db.query_one(sql)
                    total_count = result_data.get('total_count')
            if self.dataset_model.type == DatasetType.Label.value:
                label_id = content.get('label_id')
                data = repository.get_data('label', {'label_id': label_id}, ['cover_count'])
                total_count = data.get('cover_count')
        except pymysql.Error as e:
            raise UserError(message='sql failed：' + str(e))
        except Exception as ex:
            raise UserError(message='sql运行错误：' + str(ex))
        return total_count

    def run_get_field_values(self, field_name):
        """
        运行数据集，获取字段的所有值
        :return:
        """
        try:
            sql = self.get_query_sql()
            sql = self.validation_sql(sql)
            sql_util.validate_key_word(sql)
            group_sql = ' select {field_name} from ( {sql} ) a group by {field_name} limit {max_data_limit} '.format(
                field_name=field_name, sql=sql, max_data_limit=str(self.value_data_limit)
            )
            with self.get_data_source_conn() as db:
                result_data = db.query(group_sql)
                return result_data
        except Exception as e:
            raise UserError(message='sql failed：' + str(e))

    def get_table_columns(self, query_model):
        return mysql_data_source_repository.get_table_columns(query_model)

    def get_field_comment(self, sql):
        """
        获取字段的注释内容
        :param model:
        :param sql:
        :return:
        """
        field_comments = {}
        # table_names = sql_util.get_select_table_name(sql) 会提取出很多错误的表名，导致在这些一直在遍历这些错误的表名
        table_names = extract_tables(sql)
        for table_name in table_names:
            query_model = ColumnQueryModel(table_name=table_name)
            query_model.data_source = self.data_source_model
            query_model.data_source.conn_str_to_model()
            query_data = self.get_table_columns(query_model)
            for data in query_data.items:
                field_comments[data.get('name')] = data.get('comment')
        return field_comments
