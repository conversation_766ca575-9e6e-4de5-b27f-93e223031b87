import time
from datetime import datetime

import hug
import jwt
from loguru import logger
import json
import logging
import hashlib
import os
from typing import Union
from dmplib import config

from base import repository
from base.dmp_constant import PULSAR_DATASET_KEY
from components.redis_utils import stale_cache
from dataset.common import indicator_common
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.hug import g
from components.redis_utils import RCache
from dmplib.hug.context import DBContext
from components.pular_api import PulsarApi
from components import wait_lock
from components.query_models import QueryStructure, Select, Limit, Object
from base.enums import DatasetType, DatasetFieldDataType, DatasetFieldGroup, DatasetFieldType, DataSourceType, \
    ExternalDatasetType, IndicatorModelOpt, PulsarSyncMode, DatasetEditMode
from dmplib.utils.strings import seq_id
from dataset.repositories import indicator_repository, dataset_field_repository
from dmplib.redis import RedisCache
from dataset.cache import dataset_meta_cache
from dataset.models import (
    PulsarDatasetParamsModel, PulsarDashParamsModel, DetailModelData, DetailModelField, DatasetModel,
    DatasetDataModel
)
from dataset.services.dataset_service import DatasetBaseService
from dataset.query.indicator_model_query_data import IndicatorModelQuery
from user.services.developer_service import Developer
from dmplib.constants import ADMINISTRATORS_ID
from base.enums import FlowType, FlowStatus, FlowInstanceStatus, PulsarSyncMode, PulsarFieldType


def get_api():
    api = PulsarApi()
    return api


DATA_TYPE = {
    'varchar': "字符串",
    'char': "字符串",
    'text': "字符串",
    'integer': "数值",
    'int': "数值",
    'bigint': "数值",
    'timestamp': "日期",
    'date': "日期",
    'datetime': "日期",
    'double': "数值",
    'decimal': "数值",
    'tinyint': "数值",
    'real': "数值",
    'smallint': "数值",
    'float': "数值"
}


class PulsarDatasetService(DatasetBaseService):

    def __init__(self, dataset_model: DatasetModel=None, data_source_model=None):
        super().__init__(dataset_model, data_source_model)
        self._table_names = None

    def _get_dataset_fields(self):
        return dataset_field_repository.get_dataset_field(self.dataset_model.id)

    @staticmethod
    def get_query_structure(fields):
        query_structure = QueryStructure()
        for field in fields:
            query_structure.select.append(
                Select(
                    prop_name=field.get("col_name")
                )
            )
        query_structure.limit = Limit(
            offset=0,
            row=100
        )
        return query_structure

    def run_get_data_or_struct(self):
        # 获取字段
        fields = self._get_dataset_fields()
        # 获取查询结构
        query_structure = self.get_query_structure(fields)
        # 查询
        pulsar_api = IndicatorModelQuery(
            self.data_source_model, self.dataset_model.get_dict(), None, dataset_fields=fields,
            query_structure=query_structure
        )
        result_data, _, _ = pulsar_api.get_query_data()
        total_count = pulsar_api.get_total()

        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = self.dataset_model.id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = fields
        dataset_data_model.total_count = total_count
        dataset_data_model.tmp_table_name = self.dataset_model.table_name
        dataset_data_model.new_column_struct = fields
        dataset_data_model.create_table_sql = ''

        return dataset_data_model

def get_shuxin_data_source():
    key = f"{DataSourceType.MysoftShuXin.value}:data_source_id"
    cache = RedisCache()
    source_id = cache.get(key)
    if source_id:
        return source_id.decode()
    source_id = repository.get_data_scalar("data_source", {"type": DataSourceType.MysoftShuXin.value}, 'id')
    if not source_id:
        raise UserError(message="未添加【明源数芯】数据源")
    cache.set(key, source_id, 120)
    return source_id


def update_instance_of_time(ids):
    """
    运行中的任务超过1小时，则改为已终止
    :param ids:
    :return:
    """
    if isinstance(ids, str):
        ids = [ids]
    # 运行中的任务超过30分钟，则改为已终止
    m = int(config.get('External.indicator_sync_running_limit', 30) or 30)
    with get_db() as db:
        params = {
            'status': FlowInstanceStatus.Aborted.value,
            'flow_id': ids,
            'ori_status': FlowInstanceStatus.Running.value,
            'm': m
        }
        sql = """
        update instance set status=%(status)s, end_time=now() 
        where flow_id in %(flow_id)s and status=%(ori_status)s and  startup_time < DATE_SUB(now(), INTERVAL %(m)s minute)
        """
        db.exec_sql(sql, params=params)

        sql_flow = """
        update flow set run_status=%(status)s, end_time=now() 
        where id in %(flow_id)s and run_status=%(ori_status)s and  startup_time < DATE_SUB(now(), INTERVAL %(m)s minute)
        """

        db.exec_sql(sql_flow, params=params)


def create_flow_instance(mode: str = PulsarSyncMode.ALL.value, queue_name: str = ''):
    """
    创建实例
    :param mode:
    :param queue_name:
    :return:
    """
    from flow.models import FlowModel, FlowInstanceModel
    from flow.services import flow_instance_service

    # 识别flow类型
    # 明细
    if mode in [PulsarSyncMode.DIM.value, PulsarSyncMode.DWD.value, PulsarSyncMode.DWS.value]:
        flow_id = indicator_common.FLOW_ID_OF_DETAIL
        name = indicator_common.FLOW_DETAIL
        ids = [flow_id]
    # 指标
    elif mode == PulsarSyncMode.INDICATOR.value:
        flow_id = indicator_common.FLOW_ID_OF_INDICATOR
        name = indicator_common.FLOW_INDICATOR
        ids = [flow_id]
    # 明细 + 指标
    else:
        flow_id = indicator_common.FLOW_ID_OF_ALL
        name = indicator_common.FLOW_ALL
        # 全量场景则把三种已存在的已创建任务全部改为 已忽略
        ids = [indicator_common.FLOW_ID_OF_DETAIL, indicator_common.FLOW_ID_OF_INDICATOR, indicator_common.FLOW_ID_OF_ALL]

    flow_type = FlowType.PulsarDataset.value

    # flow不存在则创建flow
    if not repository.get_data_scalar('flow', {'id': flow_id}, col_name='id'):
        model = FlowModel(
            id=flow_id,
            name=name,
            type=flow_type,
            status=FlowStatus.Enable.value,
            run_status=FlowInstanceStatus.Created.value  # 默认已创建
        )
        fields = [
            'id',
            'name',
            'description',
            'type',
            'status',
            'run_status',
        ]
        repository.add_model('flow', model, fields)

    # 如果存在已创建的实例 则改为已忽略
    with get_db() as db:
        db.exec_sql(
            "update instance set status=%(status)s where flow_id in %(ids)s and status='已创建'",
            params={'ids': ids, 'status': FlowInstanceStatus.Ignored.value}
        )
    # 运行中的任务超过1小时，则改为已终止
    update_instance_of_time(ids)
    # 创建实例
    flow_instance = FlowInstanceModel(flow_id=flow_id, name=name, type=flow_type, queue_message=queue_name)
    return flow_instance_service.add_instance(flow_instance)


def update_instance(instance_id, flow_id=None, status=None, startup_time=False, end_time=False, msg=''):
    """
    更新实例
    :param instance_id:
    :param flow_id:
    :param status:
    :param startup_time:
    :param end_time:
    :param msg:
    :return:
    """
    data = {}
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if status:
        data['status'] = status
    if startup_time:
        data['startup_time'] = now
    if end_time:
        data['end_time'] = now
    if msg:
        data['message'] = msg
    if data:
        repository.update_data('instance', data, condition={'id': instance_id})
        if not flow_id:
            flow_id = repository.get_data_scalar('instance', {'id': instance_id}, col_name='flow_id')
        if 'message' in data:
            del data['message']
        if 'status' in data:
            data['run_status'] = data['status']
            del data['status']
        flow_id and repository.update_data('flow', data, condition={'id': flow_id})


def trans_into_item_list(class_item, root_id, child=None, _type=DatasetType.Indicator.value, item_list: list = None):
    if item_list is None:
        item_list = []
    parents = class_item.get("parent")
    dataset_id = class_item.get('id')

    item = {
        "id": dataset_id,
        "external_id": class_item.get('id'),
        "name": class_item.get('name'),
        "type": _type,
        "content": json.dumps({"data_source_id": get_shuxin_data_source()}),
        "external_type": ExternalDatasetType.PulsarIndicator.value
    }
    if child:
        child['parent_id'] = dataset_id
    item_list.append(item)
    if parents:
        trans_into_item_list(parents, root_id, item, DatasetType.Folder.value, item_list)
    else:
        item['parent_id'] = root_id
    return item_list


def get_level_code(item_id, pid, level_map):
    from dataset.services.dataset_define_service import generate_level_code

    if item_id in level_map:
        return level_map[item_id]

    level_code = generate_level_code(pid or "")
    level_map[item_id] = level_code
    return level_code


def single_indicator_class_sync(class_item: dict, root_id: str, level_map: dict, commit=True):
    """
    单个类目同步
    :param class_item:
    :param root_id:
    :param level_map:
    :param commit:
    :return:
    """
    dataset_ids = []
    # 获取类目及其父类, 按层级反转
    item_list = trans_into_item_list(class_item, root_id)
    item_list.reverse()
    for item in item_list:
        # 落地
        item['level_code'] = get_level_code(item['id'], item.get("parent_id"), level_map)
        replace_into_table({
            "dataset": {"fields": list(item.keys()), "values": [item]},
        }, commit=commit)
        # 清空缓存
        dataset_meta_cache.del_dataset_cache(item.get('id'))
        dataset_ids.append(item.get("id"))

    # 同步类目下的变量
    dataset_id = class_item.get('id')
    sync_dataset_vars(class_item.get('variables'), dataset_id)

    return dataset_ids


def sync_dataset_vars(variables, dataset_id):
    """
    同步变量
    :param variables:
    :param dataset_id:
    :return:
    """
    # 先删后插
    repository.delete_data('dataset_vars', {'dataset_id': dataset_id}, commit=False)

    if not variables:
        return

    var_type_map = {
        "枚举": 2,
        "datetime": 2,
        "string": 1
    }

    value_type_map = {
        "枚举": 2,
        "single": 2,
        "range": 3
    }

    def _get_default_value():
        value = ''
        if item.get('default_string_content'):
            value = item.get('default_string_content').get('value')
        if not value and item.get('default_datetime_content'):
            if item.get('default_datetime_content').get("dynamic_type") == "dynamic":
                value = ""
            else:
                value = item.get('default_datetime_content').get('static_value_content')
        if not value and item.get('default_datetime_content'):
            dynamic_value_content = item.get('default_datetime_content').get('dynamic_value_content', {}) or {}
            value = ""
        return json.dumps(value)

    var_list = []
    for item in variables:
        var_item = {
            "id": gen_id(dataset_id, item.get('id')),
            "name": item.get('name'),
            "description": item.get('desc'),
            "var_type": var_type_map.get(item.get('variable_type'), 1),
            "value_type": value_type_map.get(item.get('scope_type'), 2),
            "dataset_id": dataset_id,
            "default_value": _get_default_value(),
            "external_content": json.dumps(item)
        }
        var_list.append(var_item)
    if var_list:
        replace_into_table({
            "dataset_vars": {"fields": list(var_list[0].keys()), "values": var_list}
        })


def get_dataset_id_of_class(class_id: str):
    # key = f"class:{class_id}"
    # cache = RedisCache()
    # dataset_id = cache.get(key)
    # if dataset_id:
    #     return dataset_id.decode()
    dataset_id = repository.get_data_scalar("dataset", {'external_id': class_id}, 'id')
    if not dataset_id:
        return None
    # cache.set(key, dataset_id, 5)
    return dataset_id


def get_dataset_by_class_id(class_id: str):
    if not class_id:
        return None
    dataset = repository.get_data("dataset", {'external_id': class_id, "external_type": ExternalDatasetType.PulsarIndicator.value}, ['id', 'parent_id'])
    return dataset


def replace_into_table(data: dict, commit=True):
    """
    批量插入数据
    :param data:
    :param commit:
    :return:
    """
    with get_db() as db:
        for table_name, item in data.items():
            db.replace_multi_data(table_name, item.get('values'), item.get("fields"), commit)


def gen_dim_id(class_id, table_name, filed_name):
    return gen_id(class_id, table_name, filed_name)


def gen_id(*args):
    if not args:
        return seq_id()
    key = ":".join(args)
    input_name = hashlib.md5()
    input_name.update(key.encode("utf-8"))

    md5_32_str = (input_name.hexdigest()).lower()
    return f"{md5_32_str[:8]}-{md5_32_str[8:12]}-{md5_32_str[12:16]}-{md5_32_str[16:20]}-{md5_32_str[20:]}"


def single_indicator_sync(indicator: dict, dim_names: list, dim_ids: list, disable_rename_dim: dict):
    fields = []
    fields_key = set()
    fields_group = []
    fields_group_key = set()
    group_relation = []
    group_relation_key = set()
    indicator_class = indicator.get("class")
    num_relation_fields = []
    all_fields = []
    class_id = indicator_class.get("id")
    dataset_id = get_dataset_id_of_class(class_id)
    if not dataset_id:
        logger.error(f"类目{indicator_class.get('id')}未同步")
        indicator_class_sync(indicator_class.get('id'))
        dataset_id = get_dataset_id_of_class(indicator_class.get("id"))

    indicator_code = indicator.get("indicator_code")

    num_id = indicator_code
    if not indicator_code:
        return
    # 指标
    num_item = {
        "id": num_id,
        "col_name": indicator.get("name_as"),
        "alias_name": indicator.get("name_cn"),
        "origin_col_name": indicator.get("name"),
        "dataset_id": dataset_id,
        "external_id": indicator.get('indicator_code') or indicator.get('indicator_id'),
        "caliber": indicator.get("calculate_caliber"),
        "business_note": indicator.get("description"),
        "data_type": DatasetFieldDataType.Number.value,
        "field_group": DatasetFieldGroup.Measure.value,
        "type": DatasetFieldType.Normal.value,
        "origin_field_type": '',
        "visible": 1
        # "relation_fields": num_relation_fields,
    }
    all_fields.append(num_id)

    fields_key = fields_key | set(num_item.keys())

    # 维度、字段分组、字段分组关联关系
    sort_num, sort_num1 = 1, 1
    for item in indicator.get("analysis_dimension") or []:
        dimensions = item.get("dimensions") or []
        if dimensions:
            # 判断分组是否存在
            group_id = repository.get_data_scalar("dataset_field_group",
                                                  {"dataset_id": dataset_id, "group_name": item.get('name')},
                                                  'id') or seq_id()
            group_item = {
                "id": group_id,
                "dataset_id": dataset_id,
                "group_name": item.get('name'),
                "group_type": 1,
                "sort": sort_num,
                "parent_id": ''
            }
            fields_group.append(group_item)
            fields_group_key = fields_group_key | set(group_item.keys())
            replace_into_table({
                "dataset_field_group": {"fields": list(fields_group_key), "values": fields_group}
            })
            sort_num += 1
            for dim in dimensions:
                dim_id = gen_dim_id(class_id, item.get("table_name"), dim.get("name"))
                dim_obj = repository.get_data(
                    "dataset_field",
                    {"dataset_id": dataset_id, "field_group": "维度", "id": dim_id}, ['id', 'relation_fields', 'alias_name']
                ) or {}
                relation_fields = json.loads(dim_obj.get('relation_fields') or '[]') if dim_obj and dim_obj.get(
                    'relation_fields') else []

                if relation_fields is None:
                    relation_fields = []
                relation_fields.append(num_id)
                disable = 0 if dim.get("disable") else 1
                if dim_id in dim_ids:
                    alias = dim_obj.get('alias_name')
                else:
                    alias = rename_alias_name(dim_id, dim, dataset_id, dim_names)
                    dim_names.append(alias)
                    old_alias_name = dim.get("name_cn")
                    if disable:
                        if not disable_rename_dim.get(old_alias_name):
                            disable_rename_dim[old_alias_name] = []
                        disable_rename_dim.get(old_alias_name).append({
                            "id": dim_id,
                            "alias_name": alias,
                        })
                dim_item = {
                    "id": dim_id,
                    "col_name": dim.get("name_as"),
                    "alias_name": alias,
                    "origin_col_name": dim.get("name"),
                    "dataset_id": dataset_id,
                    "origin_table_name": item.get("table_name"),
                    "relation_fields": json.dumps(list(set(relation_fields))),
                    "data_type": DATA_TYPE.get(dim.get("field_type"), "字符串"),
                    "field_group": DatasetFieldGroup.Dimension.value,
                    "type": DatasetFieldType.Normal.value,
                    "visible": disable,
                    "origin_field_type": dim.get("field_type")
                }
                all_fields.append(dim_id)
                num_relation_fields.append(dim_id)
                fields.append(dim_item)
                fields_key = fields_key | set(dim_item.keys())

                relation_id = repository.get_data_scalar("dataset_field_group_relation",
                                                         {"dataset_id": dataset_id, "group_id": group_id,
                                                          "field_id": dim_id}, 'id') or seq_id()

                relation_item = {
                    "id": relation_id,
                    "field_id": dim_id,
                    "group_id": group_id,
                    "dataset_id": dataset_id,
                    "sort": sort_num1
                }
                group_relation.append(relation_item)
                sort_num1 += 1
                group_relation_key = group_relation_key | set(relation_item.keys())
                dim_ids.append(dim_id)

    # 清空缓存
    dataset_meta_cache.del_dataset_field_cache(dataset_id)
    dataset_meta_cache.del_multi_dataset_field_cache(all_fields)

    num_item['relation_fields'] = json.dumps(list(set(num_relation_fields)))
    fields.append(num_item)

    data = {
        "dataset_field": {"fields": list(fields_key), "values": fields},
        "dataset_field_group_relation": {"fields": list(group_relation_key), "values": group_relation}
    }
    replace_into_table(data)

    # 同步指标下的变量关系
    variables = []
    for item in indicator.get("variables") or []:
        # 兼容历史数据， id可能被前端使用了
        var_id = gen_id(dataset_id, item.get('id'))
        _id = gen_id('var', dataset_id, item.get('id'))
        if repository.get_data_scalar(
                'dataset_field_include_vars', {'field_id': num_id, "var_id": var_id}, col_name='id'
        ) != _id:
            _id = gen_id(num_id, dataset_id, item.get('id'))
        variables.append({
            "id": _id,
            "dataset_id": dataset_id,
            "field_id": num_id,
            "var_id": var_id
        })

    repository.delete_data("dataset_field_include_vars", {'dataset_id': dataset_id, 'field_id': num_id})
    if variables:
        replace_into_table({
            "dataset_field_include_vars": {"fields": list(variables[0].keys()), "values": variables}
        })

    # 清空变量缓存
    from dataset.cache import dataset_include_vars_cache

    dataset_include_vars_cache.del_dataset_include_vars_cache([dataset_id])

    return all_fields, dataset_id


def rename_alias_name(dim_id, dim, dataset_id, dim_names):
    # 判断映射表是否已存在该字段
    alias_name = repository.get_data_scalar("dim_field_map", {"field_id": dim_id, "origin_alias_name": dim.get("name_cn")}, col_name="alias_name")
    if alias_name:
        return alias_name

    alias_name = dim.get("name_cn") or dim.get("name")

    if not (alias_name in dim_names):
        return alias_name

    # 生成新的别名
    num = repository.get_data(
        "dim_field_map", {"dataset_id": dataset_id, "origin_alias_name": alias_name}, order_by=[("num", "desc")], fields=["num"])
    if not num:
        num = 1
    else:
        num = num.get('num') + 1
    new_alias_name = f"{alias_name}{num}"
    repository.add_data("dim_field_map", {"field_id": dim_id, "num": num, "dataset_id": dataset_id, "alias_name": new_alias_name, "origin_alias_name": alias_name})
    return new_alias_name


def indicator_sync(indicator_id=None, class_id=None):
    """
    指标同步
    :return:
    """
    indicator_list = get_api().get_all_indicator(indicator_id=indicator_id, class_id=class_id)
    all_fields = []
    dataset_ids = set()
    dim_names = []
    dim_ids = []
    disable_rename_dim = {}
    for item in indicator_list:
        fields, dataset_id = single_indicator_sync(item, dim_names, dim_ids, disable_rename_dim)
        all_fields.extend(fields)
        dataset_ids.add(dataset_id)
    disable_dim_rename(disable_rename_dim)
    # 删除多余的字段
    if all_fields and dataset_ids:
        repository.delete_data('dataset_field', {'id not': all_fields, 'dataset_id': list(dataset_ids)})

    del_field_group(dataset_ids, all_fields)


def del_field_group(dataset_ids, all_fields):
    """
    没有字段的分组删掉
    :param dataset_ids:
    :param all_fields:
    :return:
    """
    if all_fields and dataset_ids:
        repository.delete_data('dataset_field_group_relation',
                               {'field_id not': all_fields, 'dataset_id': list(dataset_ids)})
    for dataset_id in dataset_ids:
        # 没有字段的分组删掉
        with get_db() as db:
            group_ids = db.query_scalar(
                sql="select dfg.id from dataset_field_group as dfg where dfg.dataset_id =%(dataset_id)s and dfg.id not in (select group_id from dataset_field_group_relation where dataset_id = %(dataset_id)s)",
                params={'dataset_id': dataset_id}
            )
        if group_ids:
            repository.delete_data("dataset_field_group", {"id": group_ids, "dataset_id": dataset_id})


def disable_dim_rename(all_reanme_dim):
    """
    重命名指标维度字段名称：
    重名的维度中，只有一个维度字段是显示的，其他的都隐藏的，那么该字段的alias_name = col_name(不重命名，该回原本的名字)
    否则位置原本的字段逻辑
    params:all_reanme_dim{
        key:[{id:"",alias_name:""}]
    }
    key:指标字段别名(name_cn)
    value:别名相同的字段列表
    """
    for key in all_reanme_dim:
        dims = all_reanme_dim.get(key)
        if len(dims) == 1:
            dim_id = dims[0].get("id")
            alias_name = dims[0].get("alias_name")
            if alias_name != key:
                repository.update("dataset_field", {"alias_name": key}, {"id": dim_id})


def indicator_class_sync(class_id=None, history_ids: list = None):
    """
    类目同步
    :param class_id:
    :param history_ids:
    :return:
    """
    # 判断是否是第一次同步， 第一次同步需要新建根目录
    root_id = indicator_repository.get_indicator_class_root()

    error = None

    # 存在则更新， 不存在则新增
    class_list = get_api().get_all_indicator_class(class_id) or []
    current_indicator_class_ids = []
    level_code_map = {}
    for item in class_list:
        if item and item.get("id"):
            current_indicator_class_ids.append(item.get("id"))
            single_indicator_class_sync(item, root_id, level_code_map)
            try:
                if not error:
                    # 同步类目下的指标
                    indicator_sync(class_id=item.get("id"))
            except Exception as e:
                error = e

    # 全量拉去场景需要删除不存在的指标类目
    del_ids = []
    if history_ids and current_indicator_class_ids:
        not_exists = [i for i in history_ids if i not in current_indicator_class_ids]
        if not_exists:
            # 需要删除的数据集
            del_datasets = repository.get_list("dataset", {"external_id": not_exists}, ['id']) or []
            del_ids += [i.get("id") for i in del_datasets]
    if current_indicator_class_ids:
        row_list = repository.get_list(
            "dataset",
            {"external_type": ExternalDatasetType.PulsarIndicator.value, "type": DatasetType.Indicator.value}
        )
        not_exists_dataset = []
        for row in row_list:
            if not row.get('import_table_name') and row.get('external_id') not in current_indicator_class_ids:
                not_exists_dataset.append(row.get('external_id'))
        # 需要删除的数据集
        if not_exists_dataset:
            del_datasets = repository.get_list("dataset", {"external_id": not_exists_dataset, 'type': DatasetType.Indicator.value, "external_type": ExternalDatasetType.PulsarIndicator.value}, ['id']) or []
            del_ids += [i.get("id") for i in del_datasets]
    if del_ids:
        # 删除数据集
        repository.delete_data("dataset", {"id": del_ids, "external_type": ExternalDatasetType.PulsarIndicator.value})
        # 删除字段
        repository.delete_data("dataset_field", {'dataset_id': del_ids})
        # 删除缓存
        for _id in del_ids:
            dataset_meta_cache.del_dataset_cache(_id)
            dataset_meta_cache.del_dataset_field_cache(_id)

    if error:
        raise error


def model_sync(category: Union[list, str] = None, code: str = None):
    """
    模型同步
    :param category:
    :param code:
    :return:
    """
    if isinstance(category, list):
        category = ','.join(category)
    # 获取模型信息
    model_list = get_api().get_model_brief_info(category=category, code=code)
    dataset_ids, fields_ids = model_sync_func(model_list, code=code)

    repository.delete_data('dataset_field', {"dataset_id": dataset_ids, "id not": fields_ids})

    # 清空不存在的模型
    if dataset_ids:
        external_type = category.split(',') if category else ['dim', 'dwd', 'dws']
        repository.delete_data(
            "dataset", {'external_type': external_type, 'id not': dataset_ids})
        with get_db(g.code) as db:
            del_subject_ids = db.query_columns("""
            select id from dataset as d where type='folder' and external_type = 'subject' and not exists (select id from dataset where level_code like CONCAT(d.level_code,"%") and level_code!=d.level_code)
            """)
            if del_subject_ids:
                repository.delete_data(
                    "dataset", {'external_type': 'subject', 'id': del_subject_ids})


def model_sync_func(
        model_list=None, root_id=None, _type="", code: str = None
    ):
    """
    模型同步
    :param model_list:
    :param code:
    :param _type:
    :param root_id:
    :return:
    """
    # 判断是否是第一次同步， 第一次同步需要新建根目录
    if not root_id:
        root_id = indicator_repository.get_detail_model_root()

    level_code_map_folder = {}
    level_code_map_dataset = {}
    dataset_ids_of_category = []

    def model_to_dataset(model):
        if not model.code:
            return
        # 创建或更新主题域文件夹
        subject_folder_id = gen_id('subject', model.subject_id)
        subj = indicator_repository.get_dataset_of_pulsar(subject_folder_id) or {}
        subject_level_code = subj.get('level_code') or get_level_code(subject_folder_id, root_id, level_code_map_folder)
        subject_folder = {
            'id': subject_folder_id,
            'name': model.subject_name,
            'type': DatasetType.Folder.value,
            "external_type": 'subject',
            "external_id": model.subject_id,
            'parent_id': root_id,
            'level_code': subject_level_code
        }
        replace_into_table({"dataset": {"fields": list(subject_folder.keys()), "values": [subject_folder]}})
        # 创建或更新模型数据集
        model_dataset_id = gen_id(model.category, model.code)
        origin_dataset = indicator_repository.get_dataset_of_pulsar(model_dataset_id)
        level_code = ''
        if origin_dataset.get('parent_id') == subject_folder_id:
            level_code = origin_dataset.get('level_code')
            if not level_code.startswith(subject_level_code):
                level_code = ''
        if not level_code:
            level_code = get_level_code(model_dataset_id, subject_folder_id, level_code_map_dataset)
        model_dataset = {
            "id": model_dataset_id,
            "external_id": model.code,
            "name": model.name,
            "table_name": model.table_name,
            "type": DatasetType.Indicator.value,
            "content": json.dumps({"data_source_id": get_shuxin_data_source()}),
            "external_type": model.category,
            "level_code":  level_code,
            "parent_id": subject_folder_id
        }
        replace_into_table({
            "dataset": {"fields": list(model_dataset.keys()), "values": [model_dataset]}
        })
        dataset_meta_cache.del_dataset_cache(model_dataset_id)
        dataset_ids_of_category.append(subject_folder_id)
        dataset_ids_of_category.append(model_dataset_id)
        return model_dataset_id

    def model_field_to_dataset_field(field_data, ids):
        if not model_data.code:
            return
        field_model = DetailModelField(**field_data)
        field_id = gen_id(model_data.category, model_data.code, field_model.name)
        ids.append(field_id)
        field = {
            "id": field_id,
            "dataset_id": dataset_id,
            "alias_name": field_model.name_cn or field_model.name,
            "note": field_model.description,
            "origin_table_name": model_data.table_name,
            "origin_table_alias_name": model_data.table_name,
            "origin_field_type": field_model.field_type,
            "col_name": field_model.name,
            "origin_col_name": field_model.name,
            "data_type": DATA_TYPE.get(field_model.field_type, "字符串"),
            "field_group": DatasetFieldGroup.Measure.value if field_model.dim_type == "indicator" else DatasetFieldGroup.Dimension.value,
            "type": DatasetFieldType.Normal.value,
            "visible": 1,
            "group_type": _type
        }
        dataset_meta_cache.del_multi_dataset_field_cache([field.get('id')])
        return field

    fields_ids = []
    for item in model_list:
        # 同步主题域和模型
        model_data = DetailModelData(**item)
        model_data.validate()
        dataset_id = model_to_dataset(model_data)

        fields = []
        for sub_item in model_data.fields or []:
            # 同步模型字段
            field = model_field_to_dataset_field(sub_item, fields_ids)
            fields.append(field)
        if fields:
            replace_into_table({'dataset_field': {"fields": list(fields[0].keys()), "values": fields}})
            dataset_meta_cache.del_dataset_field_cache(dataset_id)

    return dataset_ids_of_category, fields_ids


def is_support_new_sync():
    """
    是否支持新同步
    :return:
    """
    # 默认开启， 走自动识别新老模式
    if not config.get("BIGDATA.support_new_sync", 1) in (1, "1"):
        return False
    http_status = get_api().check_url_exist()
    if http_status == 200:
        return True
    return False


def indicator_model_sync_all(mode=PulsarSyncMode.ALL.value):
    """
    指标模型同步
    :param mode: agg 同步支持  detail 明细  all 全量
    :return:
    """
    mode = mode or PulsarSyncMode.ALL.value
    key = f"{g.code}:indicator_model:{mode}"
    with wait_lock.WaitLocker(key, 600) as locker:
        if locker.lock():
            if mode in [
                PulsarSyncMode.DIM.value, PulsarSyncMode.DWD.value, PulsarSyncMode.DWS.value
            ]:
                model_sync(category=[PulsarSyncMode.DIM.value, PulsarSyncMode.DWD.value, PulsarSyncMode.DWS.value])
            elif mode == PulsarSyncMode.INDICATOR.value:
                # 同步之前先清空所有历史类目文件夹并获取 指标类目，以做更新和删除操作
                history_ids = clear_all_folder_class_and_get_indicator_class()
                # 同步所有指标类目和类目字段
                indicator_class_sync(history_ids=history_ids)
                # # 同步所有指标
                # indicator_sync()
            else:
                # 同步之前先清空所有历史类目文件夹并获取 指标类目，以做更新和删除操作
                history_ids = clear_all_folder_class_and_get_indicator_class()
                # 同步所有指标类目和类目字段
                indicator_class_sync(history_ids=history_ids)
                # # 同步所有指标
                # indicator_sync()
                # 同步dim、dwd、dws模型
                model_sync(["dim", "dwd", "dws"])


def get_code_list_by_shuxin_code(shuxin_code: str, tenant_code=None):
    """
    根据数芯code获取
    :param shuxin_code:
    :param tenant_code:
    :return:
    """
    conditions = {"shuxin_code": shuxin_code}
    if tenant_code:
        conditions.update({"code": tenant_code})
    data = repository.get_list("project_to_shuxin", conditions, fields=['code'], from_config_db=True) or []
    return [i.get('code') for i in data]


def get_dataset_by_id(dataset_id):
    return repository.get_data('dataset', {'id': dataset_id}, ["id", "parent_id"])


def get_child_dataset_by_id(dataset_id):
    return repository.get_list('dataset', {'parent_id': dataset_id}, ["id"])


def indicator_class_del(dataset):
    """
    删除类目
    :param dataset:
    :return:
    """
    # TODO 删除逻辑待定，未考虑引用问题
    dataset_id = dataset.get("id")
    chart = repository.get_data_scalar("dashboard_chart", {"source": dataset_id}, "source")
    if chart:
        logger.error(f"数据集{dataset_id}被引用，不能删除")
        return
    repository.delete_data("dataset_field", {'dataset_id': dataset_id}, commit=False)
    repository.delete_data("dataset_field_group", {'dataset_id': dataset_id}, commit=False)
    repository.delete_data("dataset_field_group_relation", {'dataset_id': dataset_id}, commit=False)
    repository.delete_data("dataset", {'id': dataset_id}, commit=True)
    # 清缓存
    dataset_meta_cache.del_dataset_cache(dataset_id)
    # 父级下没有文件或目录则删除
    del_parent(dataset.get('parent_id'))


def del_parent(parent_id):
    if not parent_id:
        return
    parent = get_dataset_by_id(parent_id)
    if parent:
        # 当前parent下是否还有子目录
        childs = get_child_dataset_by_id(parent.get('id'))
        if not childs:
            # 没有则删除当前parent
            repository.delete_data("dataset", {'id': parent_id}, commit=True)
            # 清缓存
            dataset_meta_cache.del_dataset_cache(parent_id)
            del_parent(parent.get('parent_id'))


def indicator_offline(indicator_id):
    """
    指标下线
    :param indicator_id:
    :return:
    """
    if not indicator_id:
        return
    # TODO 删除逻辑待定，未考虑引用问题
    dataset_field = repository.get_data("dataset_field", {"external_id": indicator_id}, fields=['id', 'dataset_id'])
    if not dataset_field:
        return
    dataset_field_id = dataset_field.get("id")
    # num = repository.get_data_scalar("dashboard_chart_num", {'num': dataset_field_id}, "num")
    # if num:
    #     logger.error("指标被引用，不可删除")
    #     raise UserError(message=f"指标被引用，不能删除")
    fields = json.loads(repository.get_data_scalar('dataset_field', {"external_id": indicator_id}, "relation_fields") or "[]")
    fields.append(dataset_field_id)
    repository.delete_data("dataset_field", {"id": fields})

    # 清空缓存
    dataset_meta_cache.del_dataset_field_cache(dataset_field.get('dataset_id'))


def model_offline(category, code):
    """
    模型下线
    :param category:
    :param code:
    :return:
    """
    if not code:
        return
    dataset_id = gen_id(category, code)
    dataset = indicator_repository.get_dataset_of_pulsar(dataset_id)
    if dataset:
        repository.delete_data("dataset_field", {"dataset_id": dataset_id})
        repository.delete_data("dataset", {'id': dataset_id})
        # 清空缓存
        dataset_meta_cache.del_dataset_field_cache(dataset_id)
        dataset_meta_cache.del_dataset_cache(dataset_id)
        # 上级目录不存在子集则删除
        del_parent(dataset.get('parent_id'))


def clear_all_folder_class_and_get_indicator_class():
    """
    清空所有类目文件夹
    :return:
    """
    # 数据集显示页面可能会出现短暂的 显示问题
    rows = repository.delete_data(
        "dataset",
        {"external_type": ExternalDatasetType.PulsarIndicator.value, "type": DatasetType.Folder.value}
    )
    logger.info(f"del rows {rows}")
    return repository.get_columns("dataset", {"type": DatasetType.Indicator.value, "external_type": ExternalDatasetType.PulsarIndicator.value}, "external_id")


def check_sync_exists(code):
    g.code = code
    key = PULSAR_DATASET_KEY.format(code=code)
    cache = RCache()
    sync_result = cache.get(key)
    if sync_result:
        res = json.loads(sync_result)
        if res.get('status') in [0, 1]:
            return True
    cache.set(key, json.dumps({'status': 0, 'msg': "已创建"}), 3600)
    return False


def indicator_model_callback(**kwargs):
    """
    指标模型同步
    :param kwargs:
    :return:
    """
    import app_celery

    opt = kwargs.get("opt", IndicatorModelOpt.All.value)
    shuxin_code = kwargs.get("shuxin_code")
    _id = kwargs.get("id") or kwargs.get("code")
    tenant_code = kwargs.get('tenant_code')
    category = kwargs.get('category')

    if not shuxin_code:
        logging.error("缺少租户信息")
        raise UserError(message='缺少租户信息')

    if tenant_code and isinstance(tenant_code, str):
        tenant_code = tenant_code.split(",")

    code_list = get_code_list_by_shuxin_code(shuxin_code, tenant_code)
    if not code_list:
        raise UserError(message=f"数芯code：{shuxin_code} 还未绑定租户")

    try:
        msg, error_code = "ok", 0
        for code in code_list:
            try:
                need_schedule = True

                g.code = code
                g.account = 'admin'

                sync_mode = PulsarSyncMode.ALL.value

                if opt in [IndicatorModelOpt.ClassUpdate.value, IndicatorModelOpt.IndicatorRelease.value]:
                    sync_mode = PulsarSyncMode.INDICATOR.value
                elif opt == IndicatorModelOpt.All.value:
                    sync_mode = PulsarSyncMode.ALL.value
                elif opt == IndicatorModelOpt.ClassDel.value:
                    if not _id:
                        return False, 'id为空', {"code": 500}
                    dataset = get_dataset_by_class_id(_id)
                    sync_mode = PulsarSyncMode.INDICATOR.value
                    if not dataset:
                        need_schedule = False
                    indicator_class_del(dataset)
                elif opt == IndicatorModelOpt.IndicatorOffline.value:
                    if not _id:
                        return False, 'id为空', {"code": 500}
                    indicator_offline(_id)
                elif opt == IndicatorModelOpt.ModelRelease.value:
                    if category not in [PulsarSyncMode.DIM.value, PulsarSyncMode.DWD.value, PulsarSyncMode.DWS.value]:
                        return False, 'category参数错误', {"code": 500}
                    sync_mode = category
                elif opt == IndicatorModelOpt.ModelOffline.value:
                    if category not in [PulsarSyncMode.DIM.value, PulsarSyncMode.DWD.value, PulsarSyncMode.DWS.value]:
                        return False, 'category参数错误', {"code": 500}
                    if not _id:
                        return False, 'id为空', {"code": 500}
                    model_offline(category, _id)
                else:
                    msg, error_code = f"未知的opt参数: {opt}", 500
                    logging.error(f"未知的opt参数")
                    return False, msg, {"code": error_code}

                if need_schedule:
                    from dataset.services.pulsar_sync_v2_service import PulsarSync

                    # 创建任务实例记录
                    instance_id = create_flow_instance(sync_mode)
                    # 丢任务前先清缓存
                    sync = PulsarSync(sync_mode)
                    # 数芯主动通知的场景需要先清缓存
                    logger.info(f"清缓存")
                    sync.clear_cache()
                    # 主动同步需要清缓存, 添加参数clear_cache=True
                    app_celery.sync_indicator_model.apply_async(
                        kwargs={
                            'code': code, 'mode': sync_mode, 'instance_id': instance_id, 'clear_cache': True
                        }, queue='celery-slow'
                    )

                db_ctx = DBContext.instance(g)
                db_ctx.close_all()
                db_ctx.stash_data = {}
            except Exception as e:
                msg = msg + f"{str(e)}; \n"
                error_code = 500

    except Exception as e:
        msg, error_code = str(e), 500
    return True, msg, {"code": error_code}


def query_meta_dataset(model: PulsarDatasetParamsModel):
    """
    获取所有的api和指标数据集
    :param model:
    :return:
    """
    with get_db() as db:
        sql = """
        select 
            `id`, `type`, `name`, `created_on` as `createdOn`, 
            `created_by` as `createdBy`, `modified_on` as `modifiedOn`, `modified_by` as `modifiedBy`
        from `dataset`
        where `type` in %(type)s
        order by `modified_on`
        """
        dataset_list = db.query(sql, params={"type": ["API", "INDICATOR"]}, offset=model.token, limit=model.limit) or []

        for dataset in dataset_list:
            fields = db.query(
                sql="""
                select 
                    `id`, `col_name` as `name`, `alias_name` as `nameCn`, `origin_col_name` as `originColName`,
                    `field_group` as `fieldGroup`,
                    case 
                        when `note` is not null then
                            `note`
                        else
                            `business_note`
                    end `comment`,
                    `data_type` as `fieldType`,
                    `external_id` as `indicatorCode`,`created_on` as `createdOn`, 
                    `created_by` as `createdBy`, `modified_on` as `modifiedOn`, `modified_by` as `modifiedBy`
                from `dataset_field`
                where 
                     `type`='普通' and `dataset_id` = %(dataset_id)s
                """,
                params={'dataset_id': dataset.get('id')}
            ) or []
            dataset['fields'] = fields
    return {'entities': dataset_list, "hasNext": not (len(dataset_list) < model.limit), "nextToken": model.token + len(dataset_list)}


@stale_cache("dash_ids", expire=5, random_time=1)
def get_dash_id_of_special_dataset(code: str):
    with get_db(code) as db:
        dash_ids = db.query_columns("select distinct(dashboard_id) from dashboard_chart as dc inner join dataset as d on d.id=dc.source and d.type in %(type)s", params={"type": ["API", "INDICATOR"]})
    return dash_ids


def get_field_of_chart_used(chart_id):
    dim_list = repository.get_columns("dashboard_chart_dim", {'dashboard_chart_id': chart_id}, col_name='dim') or []
    num_list = repository.get_columns("dashboard_chart_num", {'dashboard_chart_id': chart_id}, col_name='num') or []
    desire_list = repository.get_columns("dashboard_chart_desire", {'dashboard_chart_id': chart_id}, col_name='dataset_field_id') or []
    return list(set(dim_list + num_list + desire_list))


def query_meta_dash(model: PulsarDashParamsModel):
    """
    获取报表信息
    :param model:
    :return:
    """
    from feed.services.dashboard_feeds_service import get_dashboard_url

    # 获取所有报表（不包括自助报表）
    with get_db() as db:
        dash_ids = get_dash_id_of_special_dataset(g.code)
        if not dash_ids:
            return []
        query_dash = """
        select 
            `id`, `name`, 
            case `status`
                when 1 then
                    "已发布"
                else
                    "未发布"
            end `releaseStatus`,
            (
                 case 
                     when platform='pc' and new_layout_type=0 then
                         '大屏'
                     when platform='pc' and new_layout_type=1 then
                         '仪表板'
                     when platform='mobile' and new_layout_type=1 then
                         '新移动'
                     when platform='mobile' and new_layout_type=0 then
                         '老移动'
                     else
                         ''
                 end
             ) as type,
             `created_on` as `createdOn`, `created_by` as `createdBy`, `modified_on` as `modifiedOn`, 
             `modified_by` as `modifiedBy`, `terminal_type` as `terminalType`
        from dashboard
        where `application_type`=0 and`type` != 'FOLDER' and id in %(dash_ids)s
        order by created_on
        """
        dash_list = db.query(query_dash, params={'dash_ids': dash_ids}, offset=model.token, limit=model.limit) or []
        for dash in dash_list:
            query_charts = """
            select 
                dc.`id` as `chartId`, dc.`source` as `datasetId`, d.`type` as `datasetType`
            from `dashboard_chart` as dc inner join `dataset` as d on d.id=dc.source
            where dc.dashboard_id = %(dash_id)s and d.type in %(type)s
            """
            charts = db.query(query_charts, params={'dash_id': dash.get('id'), "type": ["API", "INDICATOR"]}) or []
            for chart in charts:
                chart['datasetFieldIds'] = get_field_of_chart_used(chart.get('chartId'))
            dash['charts'] = charts
            dash['url'] = get_dashboard_url(dash.get('id'), dash.get('terminalType')) if dash.get('releaseStatus') == '已发布' else ''
    return {'entities': dash_list, "hasNext": not (len(dash_list) < model.limit),
                "nextToken": model.token + len(dash_list)}


def redirect_indicator_asset(indicator_code):
    data_source = repository.get_data("data_source", {"type": DataSourceType.MysoftShuXin.value})
    if not data_source:
        raise UserError(message="未添加【明源数芯】数据源")
    conn_str = data_source.get("conn_str")
    conn = json.loads(conn_str)
    if not conn or not conn.get("project_code"):
        raise UserError(message="未完善【明源数芯】数据源信息")
    host = config.get("ShuXin.host", "")
    client_id = config.get("ShuXin.client_id", "")
    token = get_redirect_token(conn.get("project_code"), indicator_code)
    url = "{host}/api/sso/auth?redirect_url={host}/property-maps/inventory&token={token}&client_id={client_id}"
    url = url.format(host=host, token=token, client_id=client_id)
    hug.redirect.to(url)


def get_redirect_token(project_code, indicator_code):
    app_secret = config.get("ShuXin.client_secret", "")
    time_out = int(config.get("ShuXin.token_time_out", 3600) or 3600)
    exp = int(time.mktime(time.localtime(time.time())))
    account = g.account
    user_id = g.userid
    if Developer.is_developer_by_account(account):
        account = 'admin'
        user_id = ADMINISTRATORS_ID
    env_code = config.get("App.dmp_env_code", "") or os.environ.get('CONFIG_AGENT_CLIENT_CODE')
    payload = {
        "exp": time_out + exp,
        "source": env_code,
        "project_code": project_code,
        "user_account": account,
        "user_id": user_id,
        "tenant_code": g.code,
        "module": "indicator_asset"
    }
    if indicator_code:
        payload['indicator_code'] = indicator_code
        payload['module'] = 'indicator_detail'
    return jwt.encode(payload, app_secret, 'HS256')


def get_pulsar_dataset_status():
    """
    获取同步状态
    :return:
    """
    from celery_app.celery import celery

    flow_ids = [
        indicator_common.FLOW_ID_OF_DETAIL, indicator_common.FLOW_ID_OF_INDICATOR, indicator_common.FLOW_ID_OF_ALL
    ]

    # 调用场景是前端每10秒轮询一次，避免频繁mysql update ， 后端限制5分钟执行一次
    cache = RedisCache()
    # 租户级key
    key = f"pulsar_dataset:running_update"
    if not cache.get(key):
        for flow_id in flow_ids:
            update_instance_of_time(flow_id)
        cache.set(key, 1, 300)

    # 优先获取运行中的状态
    instance = repository.get_data(
        'instance',
        {'flow_id': flow_ids, 'status': FlowInstanceStatus.Running.value}, order_by=[('created_on', 'desc')])

    if not instance:
        instance = repository.get_data('instance', {'flow_id': flow_ids}, order_by=[('created_on', 'desc')]) or {}

    status = instance.get('status')
    msg = instance.get('message')
    queue_name = instance.get('queue_message') or 'celery-slow'
    end_time = instance.get('end_time')

    # status: 0 已创建 1 运行中  2 成功  3  失败,  超时时间设长点，避免队列阻塞导致状态丢失
    status_map = {
        FlowInstanceStatus.Created.value: 0,
        FlowInstanceStatus.Running.value: 1,
        FlowInstanceStatus.Aborted.value: 3,
        FlowInstanceStatus.Successful.value: 2,
        FlowInstanceStatus.Failed.value: 3,
        FlowInstanceStatus.Ignored.value: 3
    }
    res_status = status_map.get(status, 2)
    msg = status if status != FlowInstanceStatus.Failed.value else msg

    # 获取当前队列任务数
    try:
        queue = celery.connection().channel().queue_declare(queue_name, passive=True)
        remain_task_amount = queue.message_count
    except Exception as e:
        remain_task_amount = ''
        logger.error(e)

    if end_time:
        try:
            end_time = end_time.strftime("%m-%d %H:%M")
        except Exception as e:
            end_time = ''
            logger.exception(e)

    return res_status, msg, end_time, remain_task_amount


def record_log_pulsar_notify(request, kwargs, api_result):
    """
    记录数芯通知日志
    :param request:
    :param kwargs:
    :param api_result:
    :return:
    """
    from components.fast_logger import FastLogger

    FastLogger.ApiFastLogger(
        action="pulsar_sync_notify",
        org_code=g.code,
        api_url=request.url,
        api_param=json.dumps(kwargs, ensure_ascii=False) if kwargs else '',
        is_success=1 if api_result.get('result') else 0,
        start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        api_result=json.dumps(api_result, ensure_ascii=False)
    ).record()


if __name__ == "__main__":
    from dmplib.hug.globals import _app_ctx_stack, _AppCtxGlobals
    g = _AppCtxGlobals()
    g.code = 'mysql80'
    g.account = 'mysql80'
    _app_ctx_stack.push(g)
    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    # indicator_id = '4578d80b-8ed2-4db1-b8a3-48a87989ceca'
    indicator_class_sync(class_id='375888f7-8335-11ed-bf27-fa163e4b4cc9')
