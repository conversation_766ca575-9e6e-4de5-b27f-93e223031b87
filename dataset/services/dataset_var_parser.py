#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2019/6/5 16:21
# <AUTHOR> caoxl
# @File     : dataset_var_parser.py
# pylint:disable=W0703,E1101
import re
import json
from json import JSONDecodeError

import logging

from collections import Iterable
from components.query_models import Var
from components.utils import mysql_escape_string, is_number
from base.enums import DatasetVarVarType, DatasetVarValueType, DBEngine
from dmplib.utils.errors import UserError
from dmplib.hug import g


class DatasetVarNotDefinedError(UserError):
    def __init__(self, code=None, message='', data=None):
        super(DatasetVarNotDefinedError, self).__init__(code, message)
        self.data = data


class DatasetVarParser:

    OLD_DATASET_VAR_RE_MATCH = re.compile(r'\{:(.*?)\}')
    DATASET_VAR_RE_MATCH = re.compile(r'\$\{([^\s]*?)\}')

    # 变量解析函数映射关系
    # 后续新类型在此映射增加关系并实现函数即可
    dataset_var_parse_func_map = {
        DatasetVarValueType.List.value: 'parse_list_dataset_var',
        DatasetVarValueType.Value.value: 'parse_value_dataset_var',
        DatasetVarValueType.Section.value: 'parse_section_dataset_var',
    }

    def parse(self, dataset_var: Var, test_mode: bool):

        func_name = self.get_parse_func_name(dataset_var.value_type)
        func = getattr(self, func_name)
        return func(dataset_var.var_type, dataset_var.value, test_mode)

    def get_parse_func_name(self, value_type: int) -> str:
        func_name = self.dataset_var_parse_func_map.get(value_type)
        if not func_name or not hasattr(self, func_name):
            raise UserError(message="抱歉，相关解析方法不存在！")
        return func_name

    @staticmethod
    def _is_built_in_list_type(value):
        if isinstance(value, str):
            try:
                value = json.loads(value)
            except JSONDecodeError:
                # 解析不成功，就原样返回
                pass
        return value

    @staticmethod
    def escape_string(data):
        """
        转义
        :param data:
        :return:
        """
        try:
            if hasattr(g, 'db_engine') and g.db_engine in [DBEngine.MSSQL.value, DBEngine.SqlServer.value]:
                data = data.replace("'", "''").replace("\\", "\\\\").replace('"', '\\"')
            else:
                data = mysql_escape_string(data)
        except Exception:
            return data
        return data

    def parse_list_dataset_var(self, var_type: int, value: Iterable, test_mode: bool = False) -> str:
        """
        解析列表类型的数据集变量
        :param var_type:
        :param value:
        :param test_mode:
        :return:
        """
        # 列表类型需要解析为 IN 语句后半部分 `(a, b, c)` 形式
        value = self._is_built_in_list_type(value)
        if not isinstance(value, (list, set, tuple)):
            raise UserError(message="列表类型的变量，值必须为列表！")
        # 测试模式情况下 若列表只有一个值则将列表变为两个值以防止sql语句加()认为是单值的情况
        if test_mode and len(value) < 2:
            value.append(value[0])
        # 遇到None 转换为null
        if var_type == DatasetVarVarType.Number.value:
            for idx, v in enumerate(value):
                if v is None:
                    v = 'NULL'
                elif isinstance(v, str):
                    v = f"'{self.escape_string(v)}'"
                else:
                    v = str(v)
                value[idx] = v
            sql = " ({in_str})".format(in_str=",".join(value))
            # sql = " ({in_str})".format(in_str=",".join(map(lambda x: 'NULL' if x is None else str(x), value)))
        else:
            sql = " ({in_str})".format(
                in_str=",".join(map(lambda x: 'NULL' if x is None else "'" + self.escape_string(x) + "'", value))
            )
        return sql

    def parse_section_dataset_var(self, var_type: int, value: Iterable, test_mode: bool = False) -> str:
        """
        解析区间类型的数据集变量
        :param var_type:
        :param value:
        :param test_mode:
        :return:
        """
        # 区间类型需要解析为 BETWEEN 语句后半部分 `a AND b` 形式
        value = self._is_built_in_list_type(value)
        if not isinstance(value, (list, set, tuple)):
            raise UserError(message="区间类型的变量，值必须为列表！")
        if len(value) < 2:
            raise UserError(message="区间类型的变量，值最少需要两个元素！")
        if test_mode:
            logging.debug(msg="测试模式")
        if var_type == DatasetVarVarType.Number.value:
            def _format_value(v):
                if v is None:
                    return 'NULL'
                elif isinstance(v, str):
                    return f"'{self.escape_string(v)}'"
                return v

            sql = " {start} AND {end}".format(
                start=_format_value(value[0]), end=_format_value(value[1])
            )
        else:
            sql = " {start} AND {end}".format(
                start='NULL' if value[0] is None else "'" + self.escape_string(value[0]) + "'",
                end='NULL' if value[1] is None else "'" + self.escape_string(value[1]) + "'",
            )
        return sql

    def parse_value_dataset_var(self, var_type: int, value, test_mode: bool = False) -> str:
        """
        解析任意值类型的数据集变量
        :param var_type:
        :param value:
        :param test_mode:
        :return:
        """
        import numbers
        if test_mode:
            logging.debug(msg="测试模式")
        value = self._is_built_in_list_type(value)
        # # 任意值类型解析为简单的值
        # if (hasattr(g, 'db_engine') and g.db_engine in [DBEngine.MSSQL.value, DBEngine.SqlServer.value] and
        #         var_type == DatasetVarVarType.Number.value and not isinstance(value, list) and not is_number(value)):
        #     raise UserError(message="数值类型的变量，值必须为数值！")

        if var_type == DatasetVarVarType.Number.value and (not value or is_number(value)):
            sql = 'NULL' if value is None else value
        else:
            sql = "{value}".format(value='NULL' if value is None else "'" + self.escape_string(value) + "'")
        return sql

    @classmethod
    def get_var_names_from_sql_str(cls, sql: str):
        """
        从sql语句中正则匹配提取出数据集变量名称
        :param sql: sql语句,str字符串类型
        :return:
        """
        if not isinstance(sql, str):
            raise ValueError

        # 兼容老的变量表达式
        old_matched_vars = cls.OLD_DATASET_VAR_RE_MATCH.findall(sql)
        matched_vars = cls.DATASET_VAR_RE_MATCH.findall(sql)
        matched_vars = list(set(old_matched_vars) | set(matched_vars))
        if not all(matched_vars):
            raise UserError(message='错误,sql语句中包含空变量,类似${}')
        return matched_vars

    @classmethod
    def replace_sql_vars_by_var_value(cls, sql: str, var_id: str, value: str):
        if not isinstance(value, str):
            value = str(value)
        if value.startswith("'") and len(value) >= 2:  # 新增场景： 结构查询增加like +变量模式  前面会把SQL处理成 like %{xxx}% 此处需要把like替换成
            sql = (sql
                   .replace('LIKE %%${%s}%%' % var_id, f"LIKE '%{value[1:len(value) - 1]}%'")
                   .replace('LIKE ${%s}%%' % var_id, f"LIKE '{value[1:len(value) - 1]}%'")
                   .replace('LIKE %%${%s}' % var_id, f"LIKE '%{value[1:len(value) - 1]}'")
                   )
        sql = sql.replace('${%s}' % var_id, value)
        # 兼容老的变量表达式
        sql = sql.replace('{:%s}' % var_id, value)
        return sql

    @classmethod
    def replace_sql_vars_by_data(cls, sql: str, data: dict):
        """
        替换sql语句中的变量为变量值，结果为替换后的sql语句
        :param sql: 包含变量的原始sql语句， str类型
        :param data: 变量替换所需的替换值，dict类型，变量名称 -> 变量值
        :return: 替换变量值后的sql语句
        """
        var_ids = cls.get_var_names_from_sql_str(sql)
        no_value_vars = set(var_ids) - set(data)
        if no_value_vars:
            no_value_vars = list(sorted(list(no_value_vars)))
            raise DatasetVarNotDefinedError(
                message='错误,sql中有变量%s没有定义或未设置对应的值' % (','.join(no_value_vars)), data=no_value_vars
            )

        for var_id in var_ids:
            var_value = str(data[var_id])
            sql = cls.replace_sql_vars_by_var_value(sql, var_id, var_value)
        return sql
