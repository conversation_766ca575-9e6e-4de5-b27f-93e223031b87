# -*- coding: utf-8 -*-
"""
    Created by  <EMAIL> on 2019/05/29.
"""
# pylint:disable= R1710, W0613
import copy
import time
import json
import base64
import logging
from base.errors import UserError
from base.enums import (
    SubjectDatasetInspectionStatus,
    DataSourceType,
    InspectNodeNames,
    InspectNodeStatus,
    DatasetConnectType,
    OperateMode,
    MysoftNewERPDataFromType
)
from base.dmp_constant import DATASET_SUBJECT_INSPECTIONS_QUEUE_NAME, RUNDECK_PRODUCE_COMMON_MQ_COMMAND_TEMPLATE
from base import repository
from dmplib.utils.strings import seq_id
from components.message_queue import RabbitMQ
from components.rundeck import CommonRunDeckScheduler
from dataset.models import (
    DatasetSubjectModel,
    DatasetSubjectTableModel,
    DatasetSubjectTableResultModel,
    DatasetModel,
    DatasetInspectionQueryModel,
)
from dataset.repositories import dataset_subject_repository, dataset_repository
from flow.models import FlowInstanceModel
from flow.services import flow_service, flow_instance_service
from dataset.services.dataset_base_service import DatasetBaseService
from dataset.services import dataset_field_service
from dmplib.saas.project import get_data_db
from dmplib import config, redis
from dmplib.hug import g
from data_source.repositories import data_source_repository
from flow.services.flow_service import get_flow
from keywords.external_service import init_keyword

logger = logging.getLogger(__name__)


def generate_db_subject_inspect_result(inspection_result):
    return _build_subject_inspect_node_result(inspection_result)


def get_inspect_status(error_num, warning_num):
    if error_num > 0:
        return InspectNodeStatus.InspectNodeFailed.value
    if warning_num > 0:
        return InspectNodeStatus.InspectNodeWarning.value
    return InspectNodeStatus.InspectNodeNormally.value


def _build_subject_inspect_node_result(inspection_result):
    result = {}
    for node in inspection_result:
        item = copy.deepcopy(node)
        error_num, warning_num = _get_subject_inspect_node_counter(item)
        item["error_num"] = error_num
        item["warning_num"] = warning_num
        item["status"] = get_inspect_status(error_num, warning_num)
        result[item["node_type"]] = item
    return result


def upset_subject_inspection_node(subject_id: str, node_name: str, field_values: dict):
    """
    没有记录插入 有则更新节点
    :param subject_id:
    :param node_name:
    :param field_values:
    :return:
    """
    if not field_values.get("status"):
        raise UserError(message="请指定节点状态")
    if field_values.get("inspect_result"):
        try:
            field_values["inspect_result"] = json.dumps(field_values["inspect_result"])
        except Exception:
            field_values["inspect_result"] = ""
    field_values["subject_id"] = subject_id
    field_values["node_name"] = node_name
    inspection_node = dataset_subject_repository.get_dataset_subject_inspection_node(subject_id, node_name)
    if not inspection_node:
        if "start_time" not in field_values:
            field_values["start_time"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        return repository.add_data("dataset_subject_inspection_nodes", field_values)
    if inspection_node["status"] != InspectNodeStatus.InspectNodeInspecting.value and not inspection_node.get(
        "end_time"
    ):
        inspection_node["end_time"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    return repository.update(
        "dataset_subject_inspection_nodes", field_values, {"subject_id": subject_id, "node_name": node_name}
    )


def _get_subject_inspect_node_counter(result):
    error_num, warning_num = 0, 0
    for check_item in result.get("check_items", []):
        if check_item.get("result") == InspectNodeStatus.InspectNodeFailed.value:
            error_num += 1
        if check_item.get("result") == InspectNodeStatus.InspectNodeWarning.value:
            warning_num += 1
    return error_num, warning_num


def get_subject_tables_list(subject_id: str):
    if not subject_id or not isinstance(subject_id, str):
        raise UserError(message='参数subject_id错误')

    data = dataset_subject_repository.get_dataset_subject_tables_list([subject_id])
    subject_table_ids = [r.get('subject_table_id') for r in data or []]
    result = []
    for subject_table_id in subject_table_ids:
        result.append(get_subject_table_preview_data(subject_table_id))
    return result


def get_highdata_inspect_nodes():
    """
    获取hidhdata自身巡检节点
    :return:
    """
    return [
        InspectNodeNames.BusinessDataPreparationNode.value,
        InspectNodeNames.HighDataDataCleaningNode.value,
        InspectNodeNames.HighDataDataPushNode.value,
    ]


def get_subject_table_preview_data(subject_table_id: str):
    if not isinstance(subject_table_id, str) or not subject_table_id:
        raise UserError(message='参数subject_table_id错误,该参数应必填且为string类型')

    subject_table_data = dataset_subject_repository.get_dataset_subject_table_by_id(subject_table_id)
    if not subject_table_data:
        raise UserError(message='主题数据表记录不存在')

    dataset_model = DatasetModel(id=seq_id())
    subject_table_model = DatasetSubjectTableModel(**subject_table_data)
    return HighDataDatasetService(dataset_model).get_preview_data(subject_table_model)


def receive_message_upset_dataset_subject(**kwargs):
    """
    ERP数据集消息 通知更新数据集主题表
    """
    subject_id = kwargs.get('subject_id')
    if not subject_id:
        raise UserError(400, '缺少主题标识字段subject_id')

    data = {
        'name': kwargs.get('subject'),
        'version': str(kwargs.get('version', '')),
        'update_on': timestamp_to_datetime_str(kwargs.get('created_on')),
        'file_url': kwargs.get('file', {}).get('file_url', ''),
        'file_type': kwargs.get('file', {}).get('file_type', 'zip'),
    }
    model = DatasetSubjectModel(**data)
    model.id = subject_id
    model.validate()

    subject = dataset_subject_repository.get_dataset_subject_by_id(subject_id)
    if not subject:
        dataset_subject_repository.add_dataset_subject(model)
    else:
        dataset_subject_repository.update_dataset_subject_by_id(subject_id, data)

    # 发送MQ消息
    dataset_folder_id = (subject or {}).get('dataset_folder_id')
    if not dataset_folder_id:
        # 如果没有创建主题数据集，则解析获取预览数据
        send_process_message(subject_id, download=0)
    else:
        dataset_data = dataset_repository.get_dataset(dataset_folder_id)
        if not dataset_data:
            # 如果主题数据集被删除，则解析获取预览数据
            send_process_message(subject_id, download=0)
        else:
            run_subject_flow_by_highdata_notify(dataset_folder_id, subject_id)

    return subject_id


def run_subject_flow(dataset_id: str, subject_id: str, download: int = 1, force_update: int = 0, new_version: int = 0):
    queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
    message_extra_data = {
        'subject_id': subject_id,
        'download': download,
        'force_update': force_update,
        'new_version': new_version,
    }
    flow_service.run_flow(dataset_id, queue_name=queue_name, extra_data=message_extra_data)


def send_process_message(dataset_subject_id: str, download: int = 0, force_update: int = 0, new_version: int = 0):
    """
    发送主题数据集数据处理MQ
    :param dataset_subject_id: 主题ID
    :param download: 是否下载数据：True：下载全部数据，False：获取预览数据
    :param force_update: 是否强制更新主题数据集
    :param new_version: 是否初始化信版本
    :return:
    """
    data = {
        'subject_id': dataset_subject_id,
        'download': download,
        'force_update': force_update,
        'new_version': new_version,
        'project_code': g.code,
    }

    rabbit_mq = RabbitMQ()
    rabbit_mq.send_message(
        config.get('RabbitMQ.queue_name_work_flow', 'work_flow'),
        json.dumps(data),
        durable=False,
        headers={"_dmp_message_uuid": seq_id()},
    )



def timestamp_to_datetime_str(timestamp: str):
    try:
        timestamp = int(float(timestamp))
        time_array = time.localtime(timestamp)
        format_datetime_str = time.strftime("%Y-%m-%d %H:%M:%S", time_array)
        return format_datetime_str
    except:
        pass

    try:
        time.strptime(timestamp, "%Y-%m-%d %H:%M:%S")
        return str(timestamp)
    except:
        raise UserError(message='时间戳只接受timestamp或者%Y-%m-%d %H:%M:%S格式的日期')


class HighDataDatasetService(DatasetBaseService):
    def get_preview_data(self, model: DatasetSubjectTableModel):
        try:
            meta_columns = json.loads(model.meta_columns) if model.meta_columns else []
            preview_data = json.loads(model.preview_data) if model.preview_data else []
        except:
            raise UserError(message='预览数据表数据格式错误')

        dataset_id = self.dataset_model.id
        res_model = DatasetSubjectTableResultModel()
        res_model.id = model.id
        res_model.count = model.records
        res_model.description = model.description or model.table_name
        res_model.origin_table_name = model.table_name
        res_model.dataset_id = dataset_id

        # [{
        #         "alias_name":"UserKindGroup",
        #         "col_name":"UserKindGroup",
        #         "data_type":"字符串",
        #         "group_type":"维度"
        #     }]
        meta_fields = json.loads(model.dataset_meta_columns) if model.dataset_meta_columns else []
        for r in meta_fields:
            r['visible'] = 1
        res_model.field = dataset_field_service.get_field_group(meta_fields)
        res_model.head = meta_fields
        meta_columns = [r.get('name') for r in meta_columns]
        res_model.data = self.handle_preview_records(preview_data, col_names=meta_columns)
        return res_model.get_dict()

    @staticmethod
    def handle_preview_records(data: list, col_names: list):
        if not isinstance(data, list) or not isinstance(col_names, list):
            raise UserError(message='处理预览数据参数错误')

        records = []
        for r in data:
            record = {}
            if len(r) != len(col_names):
                raise UserError(message='表数据记录列数和数据字段数量不一致')
            for index, value in enumerate(r):
                record[col_names[index]] = value
            records.append(record)
        return records

    def run_get_field_values(self, field_name: str):
        try:
            dataset_id = self.dataset_model.id
            if not dataset_id:
                content = json.loads(self.dataset_model.content)
                subject_table_data = dataset_subject_repository.get_dataset_subject_table_by_table_name(
                    content['subject_id'], content['origin_table_name']
                )
                if subject_table_data:
                    dataset_id = subject_table_data['dataset_id']
            if not dataset_id:
                return []

            dataset_data = dataset_repository.get_dataset(dataset_id)
            if not dataset_data or not dataset_data['table_name']:
                return []

            group_sql = ' select {field_name} from {table_name} group by {field_name} limit {max_data_limit} '.format(
                field_name=field_name, table_name=dataset_data['table_name'], max_data_limit=str(self.value_data_limit)
            )
            with get_data_db() as db:
                result_data = db.query(group_sql)
            return result_data
        except Exception as e:
            raise UserError(message='获取字段值失败: %s' % str(e))


def get_dataset_subject_inspections_list(query_model: DatasetInspectionQueryModel):
    result = dataset_subject_repository.get_dataset_subject_inspections_list(query_model)
    if not result.items:
        return result
    id2name = {}
    api_ids = [i['subject_id'] for i in result.items if i['data_source_type'] == 'api']
    if api_ids:
        id_map = data_source_repository.get_data_source_name_by_ids(api_ids)
        if id_map:
            id2name.update(id_map)

    dataset_subject_ids = [i['subject_id'] for i in result.items if i['data_source_type'] == 'subject']
    if dataset_subject_ids:
        id_map = dataset_subject_repository.get_dataset_subject_name_by_ids(dataset_subject_ids)
        if id_map:
            id2name.update(id_map)
    return {"total": result.total, "items": result.items}


def get_all_subject_inspection_nodes(subject_info: dict) -> list:
    if subject_info["data_source_type"] == "subject":
        return [
            InspectNodeNames.DmpSendDataRequest.value,
            InspectNodeNames.BusinessDataPreparationNode.value,
            InspectNodeNames.HighDataDataCleaningNode.value,
            InspectNodeNames.HighDataDataPushNode.value,
            InspectNodeNames.DMPDataStorageNode.value,
            InspectNodeNames.DMPDataReportNode.value,
        ]
    else:
        return [
            InspectNodeNames.BusinessDataPreparationNode,
            InspectNodeNames.ApiDataDataCleaningNode,
            InspectNodeNames.DMPDataStorageNode.value,
            InspectNodeNames.DMPDataReportNode.value,
        ]


def get_dataset_subject_inspection_detail(subject_id: str):
    record = get_dataset_subject_inspection_by_id(subject_id)
    if not record:
        raise UserError(message='主题数据集巡检记录不存在')

    record['inspect_error'] = 0
    record['inspect_warning'] = 0
    nodes_map = {}
    all_nodes = get_all_subject_inspection_nodes(record)
    for node in dataset_subject_repository.get_dataset_subject_inspection_all_nodes([record["subject_id"]]):
        try:
            nodes_map[node["node_name"]] = json.loads(node["inspect_result"])
        except Exception:
            nodes_map[node["node_name"]] = {}
    for node in all_nodes:
        if node not in nodes_map:
            nodes_map[node] = {}
    record['inspect_result'] = nodes_map
    error_num, warning_num = compute_error_warning_num(record['inspect_result'])
    record['inspect_error'] = error_num
    record['inspect_warning'] = warning_num
    return record


def compute_error_warning_num(inspect_result: dict):
    error_num = warning_num = 0
    for node_type, node_result in inspect_result.items():
        if node_result.get('error_num'):
            error_num += node_result['error_num']
        if node_result.get('warning_num'):
            warning_num += node_result['warning_num']
    return error_num, warning_num


def update_dataset_inspection(data: dict):
    subject_id = data.get('subject_id')
    if not subject_id:
        raise UserError(message='缺少subject_id')
    subject_name = data.get('subject_name')
    if not subject_name:
        raise UserError(message='缺少subject_name')

    schedule = data.get('schedule')
    schedule_status = data.get('schedule_status')
    if schedule_status not in ('启用', '禁用'):
        raise UserError(message='参数schedule_status可选值为:启用、禁用')

    result = dataset_subject_repository.update_dataset_subject_inspection(
        subject_id, {'schedule': schedule, 'schedule_status': schedule_status}
    )
    upset_dataset_inspection_schedule(subject_id, subject_name, schedule, schedule_status == '启用')
    return result


def upset_dataset_inspection_schedule(subject_id: str, subject_name: str, schedule: str, schedule_enable: bool = False):
    def handle_message_content(msg: dict):
        handled_str = base64.b64encode(json.dumps(msg).encode('utf-8')).decode('utf-8')
        return handled_str

    project_code = getattr(g, 'code', '')
    data = {
        'job_id': 'Dmp_subject_inspection_%s_%s' % (project_code, subject_id),
        'name': subject_name,
        'schedule': schedule,
        'description': '主题数据集巡检任务',
        'schedule_enabled': schedule_enable,
        'group': project_code,
    }
    message = generate_inspection_message(subject_id, project_code)
    command = RUNDECK_PRODUCE_COMMON_MQ_COMMAND_TEMPLATE % (
        DATASET_SUBJECT_INSPECTIONS_QUEUE_NAME,
        handle_message_content(message),
    )
    data['command'] = command
    return CommonRunDeckScheduler().upset_job(**data)


def delete_dataset_subject_inspection(subject_id: str):
    dataset_subject_repository.delete_dataset_subject_inspection(subject_id)
    job_id = 'Dmp_subject_inspection_%s_%s' % (getattr(g, 'code', ''), subject_id)
    return CommonRunDeckScheduler().delete_job(job_id)


def generate_inspection_message(subject_id: str, project_code: str):
    from data_source.services import data_source_service

    task_name = 'api_inspection' if data_source_service.is_api_datasource(subject_id) else 'subject_inspection'
    return {'subject_id': subject_id, 'project_code': project_code, 'task_name': task_name}


def send_run_dataset_inspection_mq(subject_id: str, project_code: str = None):
    project_code = project_code or getattr(g, 'code', '')
    queue_name = DATASET_SUBJECT_INSPECTIONS_QUEUE_NAME
    data = generate_inspection_message(subject_id, project_code)
    rabbit_mq = RabbitMQ()
    rabbit_mq.send_message(
        queue_name, json.dumps(data), durable=False, auto_delete=False, headers={"_dmp_message_uuid": seq_id()}
    )
    return True, data


def run_dataset_inspection(subject_id: str, project_code: str = None):
    project_code = project_code or getattr(g, 'code', '')
    record = get_dataset_subject_inspection_by_id(subject_id)
    if not record:
        raise UserError(message='主题数据集巡检记录不存在')
    if record['status'] == '巡检中':
        raise UserError(message='主题数据集正在巡检中，不可重复巡检！')
    success, send_data = send_run_dataset_inspection_mq(subject_id, project_code)
    if success:
        repository.update_data(
            'dataset_subject_inspection',
            {
                'status': SubjectDatasetInspectionStatus.Inspecting.value,
                'start_time': '',
                'end_time': '',
                'subject_name': record.get("subject_name", ""),
            },
            {'subject_id': subject_id},
        )
        return True, send_data
    return False, {}


def get_dataset_subject_inspection_by_id(subject_id: str):
    """
    通过ID获取巡检信息，会补齐主题名称
    :param subject_id:
    :return:
    """
    info = dataset_subject_repository.get_dataset_subject_inspection_by_id(subject_id)
    if info:
        if info['data_source_type'] == 'api':
            data_source = data_source_repository.get_data_source_by_id(info["subject_id"])
            if data_source:
                info['subject_name'] = data_source['name']
        elif info['data_source_type'] == 'subject':
            data_subject = dataset_subject_repository.get_dataset_subject_by_id(info["subject_id"])
            if data_subject:
                info['subject_name'] = data_subject['name']
    return info


def event_add_data_source(event_name, model):
    if model.inspect_api and model.type == DataSourceType.API.value:
        return dataset_subject_repository.add_dataset_subject_inspect(
            {"subject_id": model.id, "data_source_type": "api", "subject_name": model.name}
        )
    if model.type == DataSourceType.MysoftNewERP.value:
        conn_str_dict = json.loads(model.conn_str)
        if "DataFrom" in conn_str_dict and conn_str_dict.get("DataFrom") == MysoftNewERPDataFromType.MYSOFT_ERP.value:
            # 明源erp系统类别，才初始化系统关键字
            return init_keyword(model)


def event_del_data_source(event_name, data_source_id):
    return dataset_subject_repository.del_dataset_subject_inspect_by_id(data_source_id)


def event_update_data_source(event_name, model):
    # 数据源更新事件，操作
    if model.type == DataSourceType.API.value:
        exist_info = dataset_subject_repository.get_dataset_subject_inspection_by_id(model.id)
        if exist_info and not model.inspect_api:
            return dataset_subject_repository.del_dataset_subject_inspect_by_id(model.id)
        if not exist_info and model.inspect_api:
            return dataset_subject_repository.add_dataset_subject_inspect(
                {"subject_id": model.id, "subject_name": model.name, "data_source_type": "api"}
            )


def run_subject_flow_by_highdata_notify(
    dataset_id: str, subject_id: str, download: int = 1, force_update: int = 0, new_version: int = 0
):
    queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
    message_extra_data = {
        'subject_id': subject_id,
        'download': download,
        'force_update': force_update,
        'new_version': new_version,
    }
    # dmp-proc中得到highdata生成主题包数据成功的消息会将流程实例id写入缓存中, 以延续流程第二阶段下载数据
    # 解决同个流程出现两个流程实例，后面的实例终止前面实例的问题
    subject_flow_instance_id = redis.conn().get(subject_id)
    run_flow_by_highdata_notify(
        dataset_id, queue_name=queue_name, instance_id=subject_flow_instance_id, extra_data=message_extra_data
    )


def run_flow_by_highdata_notify(
    flow_id,
    is_continue=None,
    queue_name=None,
    instance_id=None,
    check_direct=False,
    need_log=False,
    extra_data: dict = None,
):
    """
    运行流程,
        1. highdata 通知运行流程，先判断是否有运行中的流程
        2. 如果有运行中的流程，有则获取流程id, 并发送mq
        3. 没有运行中的流程则新建流程，并发送mq
    :param str flow_id:
    :param bool is_continue:
    :param str queue_name:
    :param str instance_id:
    :return:
    """
    if not flow_id:
        raise UserError(message='缺少flow_id')
    # 判断是否为直连模式
    if check_direct:
        dataset = dataset_repository.get_dataset(flow_id)
        if dataset.get('connect_type') == DatasetConnectType.Directly.value:
            raise UserError(message='直连模式只需刷新页面')
    flow = get_flow(flow_id)
    if not flow:
        raise UserError(message='该流程不存在')

    flow_instance_id = flow_instance_service.running_flow_is_exists(flow.id)

    if instance_id:
        flow_instance_data = flow_instance_service.get_instance_by_id(instance_id)
        flow_instance = FlowInstanceModel(**flow_instance_data)
    elif flow_instance_id:
        flow_instance_data = flow_instance_service.get_instance_by_id(flow_instance_id)
        flow_instance = FlowInstanceModel(**flow_instance_data)
    else:
        flow_instance = FlowInstanceModel(flow_id=flow.id, name=flow.name, type=flow.type)
        flow_instance_service.add_instance(flow_instance)

    # 发送执行消息
    data = {
        'project_code': getattr(g, 'code'),
        'flow_id': flow.id,
        'flow_instance_id': flow_instance.id,
        'test_run': "0",
    }
    data.update(extra_data or {})
    queue_name = queue_name if queue_name else config.get('RabbitMQ.queue_name_flow')
    rabbit_mq = RabbitMQ()

    try:
        rabbit_mq.send_message(queue_name, json.dumps(data), durable=False,
                                     headers={"_dmp_message_uuid": seq_id()})
        repository.update_data('flow', {'run_status': flow_instance.status}, {'id': flow.id})
        # 手动更新需要添加操作日志, 记录flow_instance.id 和 其对应状态
        if need_log:
            # pylint: disable=C0415
            from dataset.services.dataset_define_service import get_operate_record_model, get_data_source

            dataset = dataset_repository.get_dataset(flow_id)
            model = DatasetModel(**dataset)
            repository.add_data(
                'dataset_operate_record',
                get_operate_record_model(
                    flow_id,
                    OperateMode.Sync_data.value,
                    data_source=get_data_source(model),
                    instance_id=flow_instance.id,
                    run_status=flow_instance.status,
                ).get_dict(),
            )
    except Exception as e:
        repository.delete_data('instance', {'id': flow_instance.id})
        raise UserError(message=f'发送执行消息失败: {e}')
