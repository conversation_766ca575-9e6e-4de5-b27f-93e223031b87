#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Created by zhangyx02 on 26/09/2017
# pylint: skip-file
import hashlib
import json
import logging
import os
import random
import re
import traceback

import xlrd

from base import repository
from base.enums import (
    DatasetFileType,
    DatasetType,
    FlowNodeType,
    DatasetFieldType,
    OperateMode,
    FlowStatus,
    DatasetConnectType,
    DataSourceType,
    DatasetEditMode,
    FlowType,
    OperationFlowType,
    DatasetStorageType,
    DBEngine
)
from celery_app.celery import get_task_id
from celery_app.celery_base import get_unacked_id
from components import wait_lock
from components.log_setting import Debug
from components.oss import OSSFileProxy
from components.storage_setting import get_storage_type
from components.data_center_api import get_master_local_db_new_erp_data_source
from dashboard_chart.services import external_dashboard_service
from data_source.models import ColumnQueryModel, TableQueryModel
from data_source.services import data_source_service
from dataset.cache import dataset_meta_cache
from dataset.cache import dataset_result_cache
from dataset.common.sql_helper import extract_tables
from dataset.models import DatasetModel, DatasetOperateRecordModel, DatasetFlowModel, DatasetCacheFlowModel
from dataset.repositories import dataset_repository, dataset_field_repository, dataset_subject_repository
from dataset.repositories.dataset_var_repository import batch_get_dataset_vars
from dataset.services import (
    dataset_service,
    dataset_field_service,
    dataset_var_service,
    dataset_subject_service,
    dataset_permission_service,
    dataset_field_group_service,
    dataset_used_table_service,
)
from dataset.services.dataset_field_check_service import DatasetFieldCompareService
from dataset.services.dataset_union_service import DatasetUnionService
from dmplib import config
from dmplib import redis
from dmplib.constants import DATASET_TABLE_NAME_PREFIX
from dmplib.hug import g
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from flow.models import FlowNodeModel, FlowModel
from flow.repositories import flow_repository
from flow.repositories.flow_repository import get_replacement_dataset
from flow.services import flow_service, flow_instance_service
from level_sequence.models import DataSetLevelSequenceModel
from level_sequence.services import level_sequence_service
from open_data.repositories import open_data_repository
from rbac.services.data_permissions import data_permission_edit_filter
from keywords.external_service import save_keyword_detail
from manual_filling.services.filling_template_service import ManualFillingService
from keywords.services import keyword_detail_service
from dataset.common import sql_helper

debugger = Debug(__name__)
import copy


def generate_dataset_table_name(dataset_id):
    """
    生成数据集表名
    :param dataset_id:
    :return:
    """
    return DATASET_TABLE_NAME_PREFIX + "_" + hashlib.md5(dataset_id.encode("utf-8")).hexdigest()[8:-8]


def generate_level_code(parent_id=None):
    """
    根据父级组生成层级编码
    :param parent_id:
    :return:
    """
    return level_sequence_service.generate_level_code(DataSetLevelSequenceModel(level_id=parent_id))


def _get_tmp_upload_path(name):
    """
    获取临时文件路径和文件夹名称
    :param name:
    :return:
    """
    tmp_upload_folder = os.path.join(
        os.path.join(os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), "../../")), "runtime"),
        "tmp_upload",
    )
    tmp_upload_path = os.path.join(tmp_upload_folder, name)
    return tmp_upload_folder, tmp_upload_path


def upload(file, kwargs):
    """
    :上传文件
    :date 2017/6/29
    :param file:
    :param kwargs:
    :return :
    """

    if file.filename.split(".")[1] != kwargs["type"].lower():
        raise UserError(message="上传文件类型不正确")

    if not kwargs["type"].lower() in ["csv", "xls", "xlsx"]:
        raise UserError(message="上传文件类型不正确")

    result_data = {}
    tmp_upload_folder, tmp_upload_path = _get_tmp_upload_path(file.filename)
    try:
        # excel需要返回工作表名称
        if kwargs["type"].lower() == DatasetFileType.Xls.value or kwargs["type"].lower() == DatasetFileType.Xlsx.value:

            if not os.path.exists(tmp_upload_folder):
                os.makedirs(tmp_upload_folder)

            with open(tmp_upload_path, "wb+") as f:
                f.write(file.file.read())

            data = xlrd.open_workbook(tmp_upload_path)
            result_data["sheet_name"] = data.sheet_names()

        result_data["file_name"] = file.filename

        result_data["oss_file_url"] = OSSFileProxy().upload(open(tmp_upload_path, "rb"), file_name=file.filename)
    except Exception as e:
        from dmplib.utils import errors

        raise errors.ServerError('Failed to update dataset! 上传文件:%s失败: %s' % (tmp_upload_path, str(e)))
    finally:
        os.remove(tmp_upload_path)
    return result_data


@data_permission_edit_filter("dataset-edit")
def delete_dataset(dataset_id):
    """
    :删除数据集
    :date 2017/6/17
    :param str dataset_id:
    :return :
    """
    return delete_dataset_no_permission(dataset_id)


def delete_dataset_no_permission(dataset_id):
    """
    :删除数据集
    :date 2017/6/17
    :param str dataset_id:
    :return :
    """

    locker = wait_lock.WaitLocker("delete_dataset:%s" % dataset_id, 30)
    if not locker.lock():
        raise UserError(code=403, message="操作太频繁")
    try:
        dashboard_list = external_dashboard_service.get_related_dashboard_by_dataset_id(dataset_id)

        if dashboard_list:
            raise UserError(message="该数据集存在关联报告，不允许删除。")

        # 是否被当成同步用户表、组织表
        user_sync = dataset_service.relate_user_sync_datasets(dataset_id)
        if user_sync:
            raise UserError(message="该数据集是同步用户表或同步组织表，不允许删除。")

        dataset_permission = dataset_permission_service.relate_dataset_permission_datasets(dataset_id)
        if dataset_permission:
            raise UserError(message="该数据集被权限数据集所引用，不允许删除。")

        filling_data = ManualFillingService.get_template_by_dataset_id(dataset_id)
        if filling_data:
            raise UserError(message="该数据集被数据填报使用，不允许删除。")

        dataset = repository.get_data(
            "dataset",
            {"id": dataset_id},
            ["id", "table_name", "content", "level_code", "name", "type", "connect_type", "edit_mode", "cache_flow_id"],
        )

        if not dataset:
            raise UserError(message="数据集不存在")

        # 删除变量及相关数据
        dataset_var_service.delete_vars_by_datasetid(dataset_id)

        record = dataset_repository.delete_dataset(dataset)

        # 删除关键字变量关系
        keyword_detail_service.delete_keyword_detail_by_dataset_id(dataset_id)

        # 删除数据集使用的表记录
        repository.delete_data('dataset_used_table', {'dataset_id': dataset_id})

        if dataset["type"] != DatasetType.Folder.value:
            record += repository.delete_data("dataset_field", {"dataset_id": dataset_id})
            dataset_field_ids = repository.get_data(
                "dataset_field", {"dataset_id": dataset_id}, fields=["id"], multi_row=True
            )
            # 删除数据集多字段数据缓存
            dataset_meta_cache.del_multi_dataset_field_cache(dataset_field_ids)

            record += repository.delete_data("dataset_operate_record", {"dataset_id": dataset_id})

            if dataset.get("edit_mode") == DatasetEditMode.Relation.value:
                record += repository.delete_data("dataset_tables_collection", {"dataset_id": dataset_id})

            record += repository.delete_data("dataset_filter", {"dataset_id": dataset_id})

            # 删除数据集元数据所有缓存
            dataset_meta_cache.remove_dataset_cache(dataset_id)
            # 删除数据集结果缓存
            dataset_result_cache.remove_dataset_result_cache(dataset_id)

        if dataset['content']:
            subject_id = json.loads(dataset['content']).get('subject_id')
            if subject_id:
                dataset_subject_repository.delete_subject_dataset_id(
                    dataset_id, dataset["type"] == DatasetType.Folder.value
                )
                if dataset['type'] == DatasetType.Folder.value:
                    is_schedule = dataset['connect_type'] != DatasetConnectType.Push.value
                    flow_service.delete_flow(dataset_id, is_delete_scheduler=is_schedule)
                    dataset_subject_service.delete_dataset_subject_inspection(subject_id)

        if (
                dataset["type"] == DatasetType.Sql.value
                or dataset["type"] == DatasetType.Union.value
                or dataset["type"] == DatasetType.Excel.value
                or dataset["type"] == DatasetType.Api.value
        ) and dataset['connect_type'] != DatasetConnectType.Directly.value:
            record += flow_service.delete_flow(
                dataset_id, is_delete_scheduler=dataset["type"] != DatasetType.Excel.value
            )
            record += delete_dataset_version(dataset)

        if dataset.get('cache_flow_id'):
            # 删除对应的缓存调度
            flow_service.delete_flow(
                dataset.get('cache_flow_id'), is_delete_scheduler=dataset["type"] != DatasetType.Excel.value
            )
            # 删除数据集结果缓存
            dataset_result_cache.remove_dataset_result_cache(dataset_id)

        # 删除redis记录，停止异步任务
        task_id = get_task_id("dataset", dataset["type"], g.code, dataset.get("id"))
        redis.conn().del_data(task_id)
        unacked_id = get_unacked_id(dataset.get("id"))
        redis.conn().del_data(unacked_id)

        return record, dataset.get("name")
    finally:
        locker.free()


def validate_update_field(model):
    """
    判断数据结构是否变化
    :param model:
    :return:
    """
    # 更新 dataset_field
    old_dataset_field = dataset_field_repository.get_dataset_field(model.id, {"type": DatasetFieldType.Normal.value})
    # 长度不一致，结构一定是发生变化的(注意不考虑高级字段)
    if len(old_dataset_field) != len(model.field):
        raise UserError(code=500, message="【警告】sql更新会修改原有数据集结构，可能导致报告单图出错，请确认是否要修改！")
    # 现在已经不需要这个接口做判断了。。。。。判断原有dataset里有没有字段，如果已经存在
    # fields = retain_dataset_field(model.id, model.field, old_dataset_field, model.type)
    # fields 中有不存在的id,说明新增了结构变化了
    # for field in fields:
    #     if not field.get('id'):
    #         raise UserError(code=500, message='【警告】sql更新会修改原有数据集结构，可能导致报告单图出错，请确认是否要修改！')


def validate_normal_field(model):
    """
    判断普通字段的引用字段是否被删除
    :param model:
    :return:
    """
    result = dict()
    result['check_results'] = DatasetFieldCompareService(model.id, model.field).compare()
    return result


def validate_senior_field(model):
    """
    判断高级字段的引用字段是否被删除
    :param model:
    :return:
    """
    dataset_fields = dataset_field_repository.get_dataset_field(model.id)

    senior_fields = [
        dataset_field
        for dataset_field in dataset_fields
        if dataset_field.get("type")
           in [
               DatasetFieldType.Calculate.value,
               DatasetFieldType.Customer.value,
               DatasetFieldType.Indicator.value,
               DatasetFieldType.CalculateIndicator.value,
           ]
    ]
    if len(senior_fields) == 0:
        return

    model.field = retain_dataset_field(model.id, model.field, dataset_fields, model.type)

    is_exists = True
    alias_name = None
    field_name = None
    for senior_field in senior_fields:
        if not is_exists:
            break

        if senior_field.get("expression_advance"):
            # 验证高级字段新式表达式
            is_exists = is_exists_field_newstyle(model.field, senior_field.get("expression_advance"))
            if not is_exists:
                alias_name = senior_field.get("alias_name")
                field_name = get_dataset_field_name_by_colname(dataset_fields, senior_field.get("col_name"))
            continue

        if senior_field.get("expression"):
            # 验证高级字段旧式表达式
            expression = json.loads(senior_field.get("expression"))
            for item in expression:
                if not item.get("id"):
                    continue
                is_exists = is_exists_field(model.field, item.get("id"))
                if not is_exists:
                    alias_name = senior_field.get("alias_name")
                    field_name = get_dataset_field_name_by_id(dataset_fields, item.get("id"))

    if not is_exists:
        message = (
            "该数据集【{senior_name}】高级字段引用了【{field_name}】字段，目前【{field_name}】字段不存在将会影响报告数据, 是否继续保存?".format(
                senior_name=alias_name, field_name=field_name
            )
            if field_name
            else "该数据集【{senior_name}】高级字段引用字段已不存在，是否继续保存?".format(senior_name=alias_name)
        )
        raise UserError(code=490, message=message)


def is_exists_field_newstyle(retain_dataset_fields, expr_advance):
    is_exists = False

    col_names = re.findall(r'\[(.*?)\]', expr_advance)
    if len(col_names) == 0:
        return is_exists

    for rdf in retain_dataset_fields:
        if rdf.get('col_name') in col_names:
            is_exists = True
            break

    return is_exists


def is_exists_field(fields, reference_field_id):
    """
    判断引用字段id是否存在
    :param fields:
    :param reference_field_id:
    :return:
    """
    is_exists = False
    for field in fields:
        if field.get("id") == reference_field_id:
            is_exists = True
            break
    return is_exists


def get_dataset_field_name_by_colname(dataset_fields, col_name):
    """
    根据引用字段id获取字段名称
    :param dataset_fields:
    :param reference_field_id:
    :return:
    """
    name = ""
    for dataset_field in dataset_fields:
        if dataset_field.get("col_name") == col_name:
            name = dataset_field.get("alias_name") if dataset_field.get("alias_name") else dataset_field.get("col_name")
    return name


def get_dataset_field_name_by_id(dataset_fields, reference_field_id):
    """
    根据引用字段id获取字段名称
    :param dataset_fields:
    :param reference_field_id:
    :return:
    """
    name = ""
    for dataset_field in dataset_fields:
        if dataset_field.get("id") == reference_field_id:
            name = dataset_field.get("alias_name") if dataset_field.get("alias_name") else dataset_field.get("col_name")
    return name


def retain_dataset_field(dataset_id, dataset_field, old_dataset_field, dataset_type):
    """
    判断原有dataset里有没有字段，如果已经存在保留原有的id
    :return:
    """
    update_fields = []
    exist_columns = []
    # 这里需要添加 已删除数据集字段, 保证相同col_name能找回之前的ID
    has_delete_fields = repository.get_data(
        "dataset_field_delete",
        {"dataset_id": dataset_id},
        ["col_name", "dataset_field_id", "type", "origin_col_name"],
        multi_row=True,
    )
    all_old_dataset_field = old_dataset_field + (has_delete_fields if has_delete_fields else [])
    for k, request_field in enumerate(dataset_field):
        for field in all_old_dataset_field:
            request_field_id = get_request_field_id(dataset_type, field, request_field)
            if request_field_id:
                request_field["id"] = request_field_id
        request_field["inspection_rules"] = (
            request_field.get("inspection_rules") if request_field.get("inspection_rules") else ""
        )
        if request_field.get("col_name") in exist_columns:
            raise UserError(message=u"数据集保存失败，字段名{}重复".format(request_field.get("col_name")))
        exist_columns.append(request_field.get("col_name"))
        update_fields.append(request_field)
    return update_fields


def get_request_field_id(dataset_type, field, request_field):
    request_field_id = ""
    if dataset_type == DatasetType.Excel.value:
        alias_name = field.get("alias_name") if field.get("alias_name") else field.get('origin_col_name')
        if alias_name == request_field.get("alias_name") and field.get("type") == DatasetFieldType.Normal.value:
            request_field_id = field.get("id") if field.get("id") else field.get("dataset_field_id")
    else:
        if field["col_name"] == request_field["col_name"] and field.get("type") == DatasetFieldType.Normal.value:
            request_field_id = field.get("id") if field.get("id") else field.get("dataset_field_id")
    return request_field_id


def save_delete_dateset_field(dataset_id, dataset_field, old_dataset_fields):
    delete_fields = []
    _dataset_field = [row.get("col_name") for row in dataset_field]
    for field in old_dataset_fields:
        # 老的字段col_name没有在新的字段中，表示已删除
        if field.get("col_name") not in _dataset_field:
            delete_fields.append(
                {
                    "dataset_id": dataset_id,
                    "dataset_field_id": field.get("id"),
                    "col_name": field.get("col_name"),
                    "type": field.get("type"),
                    "origin_col_name": field.get("origin_col_name"),
                }
            )
    if delete_fields:
        repository.add_list_data(
            "dataset_field_delete",
            delete_fields,
            ["dataset_id", "dataset_field_id", "col_name", "type", "origin_col_name"],
        )

        # 删除字段时，如果有配置索引的话，也应该删除，避免flow建索引的时候报错
        delete_fields_columns = [row.get("col_name") for row in delete_fields]
        all_dataset_index = repository.get_data(
            "dataset_index", {"dataset_id": dataset_id}, ["id", "index_name", "column_list"], multi_row=True
        )
        delete_index = []
        update_index = []
        need_update_index = False
        # 删除的columns
        for index in all_dataset_index:
            new_column_list = json.loads(index.get("column_list"))
            remove_columns = []
            for column in new_column_list:
                if column in delete_fields_columns:
                    need_update_index = True
                    remove_columns.append(column)
            # 最后需要保留的col_names
            last_columns = list(set(new_column_list).difference(set(remove_columns)))
            if need_update_index:
                # 已经没有列了，需要删除这个索引信息
                if not last_columns:
                    delete_index.append(index.get("id"))
                else:
                    update_index.append(
                        {
                            "id": index.get("id"),
                            "dataset_id": dataset_id,
                            "index_name": index.get("index_name"),
                            "column_list": json.dumps(last_columns, ensure_ascii=False),
                        }
                    )
        if update_index:
            repository.replace_list_data("dataset_index", update_index, fields=["id", "column_list"])
        if delete_index:
            # 删除
            dataset_field_repository.delete_dataset_indexs(delete_index)
        # 删除字段时，应该把配置的行列权限也删除
        delete_fields_ids = [row.get("dataset_field_id") for row in delete_fields]
        result = repository.get_data(
            "dataset_role_filter",
            {"dataset_id": dataset_id},
            fields=["role_id", "dataset_filter", "hide_field_ids"],
            multi_row=True,
        )
        # 更新dataset_role_filter
        update_dataset_filter(result, dataset_id, delete_fields_ids)
        # 删除数据集字段和分组中的关系
        dataset_field_group_service.delete_field_group_relation(delete_fields_ids)


def update_dataset_filter(result, dataset_id, delete_fields_ids):
    # 解析所有的dataset_filter中的dateset_field_id
    for row in result:
        if row.get("dataset_filter"):
            dataset_filters = json.loads(row.get("dataset_filter"))
            need_update = False
            for dataset_filter in dataset_filters:
                if dataset_filter.get("dataset_field_id") in delete_fields_ids:
                    dataset_filters.remove(dataset_filter)
                    need_update = True
            if need_update:
                repository.update_data(
                    "dataset_role_filter",
                    {"dataset_filter": json.dumps(dataset_filters)},
                    {"dataset_id": dataset_id, "role_id": row.get("role_id")},
                )
        if row.get("hide_field_ids"):
            hide_field_ids = row.get("hide_field_ids").split(',')
            new_hide_field_ids = ""
            need_update = False
            for hide_field_id in hide_field_ids:
                if hide_field_id in delete_fields_ids:
                    need_update = True
                    hide_field_ids.remove(hide_field_id)
            if need_update:
                repository.update_data(
                    "dataset_role_filter",
                    # 去除数组的[], 与之前的结构保持一致
                    {"hide_field_ids": str(new_hide_field_ids)[1:-1]},
                    {"dataset_id": dataset_id, "role_id": row.get("role_id")},
                )


@data_permission_edit_filter("dataset-edit")
def update_cache_flow(dataset_id, model: DatasetModel):
    locker = wait_lock.WaitLocker("update_cache_flow:%s" % dataset_id, 30)
    if not locker.lock():
        raise UserError(code=403, message="操作太频繁")
    try:
        dataset = dataset_repository.get_dataset(dataset_id)
        if not dataset:
            raise UserError(message="数据集不存在")
        db_model = DatasetModel(**dataset)

        # 校验数据
        new_model = DatasetModel(**dataset)
        new_model.use_cache = model.use_cache
        new_model.cache_flow = model.cache_flow
        validate_dataset_cache(new_model)

        if new_model.use_cache:
            # 如果之前没有创建cache_flow
            if not new_model.cache_flow_id:
                new_model.cache_flow_id = seq_id()
                init_dataset_cache_flow(new_model)
            # 否则更新cache_flow的调度信息
            else:
                repository.update_data(
                    'flow', {'schedule': new_model.cache_flow.schedule}, {'id': new_model.cache_flow_id}
                )

        # 更新数据集数据
        repository.update_data(
            'dataset', {'use_cache': new_model.use_cache, 'cache_flow_id': new_model.cache_flow_id}, {'id': dataset_id}
        )

        dataset_field_id_list = repository.get_columns('dataset_field', {'dataset_id': model.id}, 'id')
        # 删除数据集多字段数据缓存
        dataset_meta_cache.del_multi_dataset_field_cache(dataset_field_id_list)
        # 删除数据集元数据所有缓存
        dataset_meta_cache.remove_dataset_cache(dataset_id)
        # 删除数据集结果缓存
        dataset_result_cache.remove_dataset_result_cache(dataset_id)

        # 更新flow数据
        if not new_model.use_cache and db_model.use_cache:
            # 关闭缓存
            flow_service.disable_flow(new_model.cache_flow_id)
        elif new_model.use_cache and not db_model.use_cache:
            # 开启缓存
            flow_service.enable_flow(new_model.cache_flow_id)
        elif new_model.use_cache:
            # 更新调度信息
            flow_service.update_flow_schedule(new_model.cache_flow_id)
    except Exception as e:
        logging.error("修改数据集调度错误", exc_info=True)
        msg = "修改数据集调度错误，错误内容：" + str(e)
        raise UserError(message=msg)
    finally:
        locker.free()

    return dataset.get("name")


@data_permission_edit_filter("dataset-edit")
def update_flow(dataset_id, new_model: DatasetModel):
    """
    : 修改数据集调度
    :param dataset_id:
    :param new_model:
    :return :
    """
    locker = wait_lock.WaitLocker("update_flow:%s" % dataset_id, 30)
    if not locker.lock():
        raise UserError(code=403, message="操作太频繁")
    try:
        db_dataset = dataset_repository.get_dataset(dataset_id)
        if not db_dataset:
            raise UserError(message="数据集不存在")
        # 判断是否使用历史版本
        if use_history_dataset_version(dataset_id):
            raise UserError(message="当前数据集使用历史版本，请使用最新版本后，再修改数据集！")

        change_content = False
        queue_name = None
        if new_model.type == DatasetType.Sql.value and new_model.flow and getattr(new_model.flow, "state_trigger",
                                                                                  "") in [1, "1"]:
            table_names = dataset_service.check_clean_status(dataset_data=new_model.get_dict())
            queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
            if table_names and not new_model.content.get("table_names"):
                new_model.content["sql_table_names"] = table_names
                change_content = True
        # 直连只支持sql、api、组合数据集
        if (
                new_model.connect_type
                and new_model.connect_type == DatasetConnectType.Directly.value
                and (new_model.type not in (DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Union.value))
        ):
            raise UserError(message="直连模式仅支持SQL、API、组合数据集")
        # 组合数据集需要更新依赖关系表
        if new_model.type == DatasetType.Union.value:
            repository.delete_data("dataset_depend", {"depend_id": new_model.id})
            if new_model.content and new_model.content.get("source_dataset_ids"):
                for source_dataset_id in new_model.content.get("source_dataset_ids"):
                    repository.add_data(
                        "dataset_depend", {"source_dataset_id": source_dataset_id, "depend_id": new_model.id}
                    )

        change_to_directly = False
        change_to_schedule = False
        to_update_fields = ["connect_type", "clear_time"]
        if not db_dataset.get("connect_type") and new_model.connect_type == DatasetConnectType.Directly.value:
            # 切换直连，需要判断是否被其他组合数据集引用
            union_dataset = has_new_union_dataset_reference(new_model.id)
            if union_dataset:
                if isinstance(union_dataset, list):
                    union_dataset = union_dataset[0]
                raise UserError(message="该数据集被组合数据集[{}]引用，不能更改为直连模式".format(union_dataset))
            change_to_directly = True
        elif db_dataset.get(
                "connect_type") == DatasetConnectType.Directly.value and new_model.connect_type != DatasetConnectType.Directly.value:
            # 直连改调度
            if has_variables(dataset_id):
                raise UserError(message="该数据集使用变量，不能更改为调度模式")
            # 关闭缓存
            new_model.use_cache = False
            new_model.cache_flow_id = ''
            to_update_fields += ["cache_flow_id", "use_cache"]
            change_to_schedule = True

        # 前端connect_type可能会null
        new_model.connect_type = new_model.connect_type if new_model.connect_type else ""

        if change_content:
            new_model.content = json.dumps(new_model.content)
            to_update_fields.append('content')
        repository.update_model("dataset", new_model, {"id": new_model.id}, to_update_fields)

        dataset_field_id_list = repository.get_columns('dataset_field', {'dataset_id': new_model.id}, 'id')
        # 删除数据集多字段数据缓存
        dataset_meta_cache.del_multi_dataset_field_cache(dataset_field_id_list)
        # 删除数据集元数据所有缓存
        dataset_meta_cache.remove_dataset_cache(dataset_id)
        # 删除数据集结果缓存
        dataset_result_cache.remove_dataset_result_cache(dataset_id)

        if change_to_directly:
            _delete_sync_flow(db_dataset, new_model, queue_name)
        elif change_to_schedule:
            _delete_cache_flow(db_dataset, new_model, queue_name)
            # 直连改调度，检查简讯是否使用该数据集，如使用则需要记录
            _change_dataset_used_subscribe(db_dataset)

        # 更新流程
        # 查询是否有正在运行的sql flow 实例，要是正在运行，先要停止运行再启动
        if (
                new_model.flow
                and db_dataset["type"] in [DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Union.value]
                and new_model.connect_type != DatasetConnectType.Directly.value
                or db_dataset["type"] == DatasetType.Excel.value
        ):
            _update_dataset_flow(new_model, db_dataset, queue_name=queue_name)

        subject = repository.get_data("dataset_subject", {"dataset_folder_id": new_model.id}, fields=["id"])
        if subject and new_model.flow:
            command = generate_subject_command(new_model.id, subject["id"])
            _update_dataset_flow(new_model, db_dataset, command=command, queue_name=queue_name,
                                 subject_id=subject["id"])

        # 数据集调度信息操作记录
        repository.add_data(
            "dataset_operate_record",
            get_operate_record_model(
                new_model.id, OperateMode.Update_flow.value, data_source=get_data_source(new_model)
            ).get_dict(),
        )

    except Exception as e:
        logging.error("修改数据集调度错误", exc_info=True)
        msg = "修改数据集调度错误，错误内容：" + str(e)
        raise UserError(message=msg)
    finally:
        locker.free()

    return db_dataset.get("name")


def use_history_dataset_version(dataset_id: str) -> bool:
    return repository.data_is_exists("dataset_current_version", condition={"dataset_id": dataset_id})


def validate_dataset(dataset: dict, model: DatasetModel):
    if not dataset:
        raise UserError(message="数据集不存在")

    # 直连只支持mysql、PostgreSQL数据集
    if (
            model.connect_type
            and model.connect_type == DatasetConnectType.Directly.value
            and (model.type not in [DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Union.value])
    ):
        raise UserError(message="目前SQL数据集直连模式仅支持MySQL、PostgreSQL、API、组合数据集")

    if not isinstance(model.field, list) or len(model.field) == 0:
        # hotfix/fix-error-apis:690a5fce073f43cfa60e0a7856a3c265
        raise UserError(message='数据集字段不存在')
    # 数据集字段id和数据集id需要匹配
    update_dataset_field_ids = [field.get("id") for field in model.field if field.get("id")]
    if update_dataset_field_ids and dataset_field_repository.validate_field_id_by_dataset_id(
            update_dataset_field_ids, model.id
    ):
        raise UserError(message='数据集字段与数据集不匹配！')

    #  如果是组合数据集，不允许选中自己
    if model.type == DatasetType.Union.value:  #
        if model.id in model.content.get("source_dataset_ids"):
            raise UserError(message='组合数据集不能选择自己！')
    validate_dataset_cache(model)


def validate_dataset_cache(model: DatasetModel):
    # 缓存模式只支持sql和api数据集
    if model.type not in [DatasetType.Sql.value] and model.use_cache:
        raise UserError(message='缓存模式只支持SQL数据集')

    # 缓存模式只能在直连状态开启
    if (
            model.type in [DatasetType.Sql.value, DatasetType.Api.value]
            and model.connect_type
            and model.connect_type != DatasetConnectType.Directly.value
            and model.use_cache
    ):
        raise UserError(message="缓存模式只能在直连状态开启")

    # 缓存开启后需要补充cache_flow信息
    if model.use_cache and not model.cache_flow.schedule:
        raise UserError(message="直连模式开启缓存需要填写调度信息")


def replace_field_and_delete(model: DatasetModel, db_fields: list):
    """
    新版本修改字段
    1.根据和现有数据集字段对比，计算出需要删除的字段
    2.删除需要删除的普通字段
    3.replace into 数据集字段信息
    """
    need_replace_field_ids = [field.get("id") for field in model.field]
    need_delete_field_ids = [db_field["id"] for db_field in db_fields if db_field["id"] not in need_replace_field_ids]
    with get_db() as db:
        if need_delete_field_ids:
            repository.delete_data(
                "dataset_field",
                {"id": need_delete_field_ids, "dataset_id": model.id, "type": DatasetFieldType.Normal.value},
                commit=False,
            )
        dataset_field_service.replace_dataset_field(model, commit=False)
        db.commit()


def _delete_cache_flow(db_dataset: dict, new_model: DatasetModel, queue_name):
    if not db_dataset.get('cache_flow_id'):
        return
    cache_flow_id = db_dataset.get('cache_flow_id')
    # 删除redis记录, 停止异步任务
    task_id = get_task_id("dataset_cache", db_dataset.get("type"), g.code, cache_flow_id)
    redis.conn().del_data(task_id)
    unacked_id = get_unacked_id(cache_flow_id)
    redis.conn().del_data(unacked_id)
    redis.conn().del_data(task_id)
    # 如果切换了缓存模式, 需要更改对应flow的状态
    data = repository.get_data("flow", {"id": cache_flow_id})
    if data:
        new_model.cache_flow = None
        flow_service.delete_flow(cache_flow_id)


def _delete_sync_flow(db_dataset: dict, new_model: DatasetModel, queue_name):
    # 删除redis记录，停止异步任务
    task_id = get_task_id("dataset", db_dataset.get("type"), g.code, db_dataset.get("id"))
    redis.conn().del_data(task_id)
    unacked_id = get_unacked_id(db_dataset.get("id"))
    redis.conn().del_data(unacked_id)
    redis.conn().del_data(task_id)
    # 调度切换直连模式,只需更改对应flow状态为禁用
    data = repository.get_data("flow", {"id": new_model.id})
    if data:
        new_model.flow = None
        flow_service.delete_flow(new_model.id)


def _change_dataset_used_subscribe(db_dataset: dict):
    """
    数据集由直连改调度，如被简讯引用则需要记录
    :param db_dataset:
    :return:
    """
    from feed.services.dashboard_feeds_service import change_dataset_connect_type_to_subscribe
    dataset_id = db_dataset.get("id")
    change_dataset_connect_type_to_subscribe(dataset_id)


@data_permission_edit_filter("dataset-edit")
def update_dataset(dataset_id, model: DatasetModel):
    """
    :修改数据集及数据集字段
    :date 2017/6/16
    :param model:
    :return :
    """
    locker = wait_lock.WaitLocker("update_dataset:%s" % dataset_id, 30)
    if not locker.lock():
        raise UserError(code=403, message="操作太频繁")
    try:
        model.validate()
        if model.clear_time and int(model.clear_time) > 1800:
            raise UserError(message='最大执行时间不能超过1800秒')

        dataset = dataset_repository.get_dataset(dataset_id)
        validate_dataset(dataset, model)
        # excel由于不生成预发布版本。导致一旦指定版本，再也无法更改保存数据集
        if (
                repository.data_is_exists("dataset_current_version", condition={"dataset_id": dataset_id})
                and dataset.get("type") != DatasetType.Excel.value
        ):
            raise UserError(message=u'指定数据版本时，不允许更改数据集！')

        # 数据集改造：支持同时修改名称+调整目录
        move_dataset(dataset_id, model.parent_id, is_same_throw_error=False)
        rename_dataset(dataset_id, model.name, model.type, model.parent_id)

        queue_name = None
        if model.type == DatasetType.Sql.value and model.flow and getattr(model.flow, "state_trigger", "") in [1, "1"]:
            dataset_service.check_clean_status(model.get_dict())
            queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')

        validate_dataset_field_name(model)
        data_source = get_data_source(model)

        # label 类型直接取 label表里的 label_tablename作为数据集结果表
        if model.type == DatasetType.Label.value:
            model.table_name = repository.get_data_scalar(
                "label", {"label_id": model.content["label_id"]}, "label_tablename"
            )

        # 组合数据集需要更新依赖关系表
        repository.delete_data("dataset_depend", {"depend_id": model.id})
        if model.type == DatasetType.Union.value and model.content and model.content.get("source_dataset_ids"):
            add_update_dataset_depend(model.id, model.content.get("source_dataset_ids"))
        # 直连和api数据集不保存版本
        # 这里不在保存版本，统一在数据集调度时生成版本
        model.content['sql_table_names'] = extract_tables(model.content.get("sql")) if model.content.get("sql") else []

        # excel需要自动绑定数据源
        if model.type == DatasetType.Excel.value and get_storage_type(
                g.code) == DatasetStorageType.DatasetStorageOfLocal.value:
            model.content['bind_source_id'] = get_master_local_db_new_erp_data_source().get('id')

        model.content = json.dumps(model.content)

        # 组合数据集编辑新增data_source_id字段
        model.content = get_union_data_source_id(model.get_dict(["content", "type"]))

        repository.update_model(
            "dataset", model, {"id": model.id},
            ["content", "connect_type", "edit_mode", "is_complex", "is_need_procedure", "type", "is_need_procedure",
             "disable_procedure", "is_import_table", "import_table_name", "import_table_type"]
        )

        # 更新数据集使用的相关表记录
        dataset_used_table_service.save_used_table(model)

        # 更新 dataset_field
        old_dataset_field = (
                dataset_field_repository.get_dataset_field(model.id, {"type": DatasetFieldType.Normal.value}) or []
        )

        # 判断原有dataset里有没有字段，如果已经存在
        model.field = retain_dataset_field(model.id, model.field, old_dataset_field, dataset.get("type"))
        # 保存删除的字段
        save_delete_dateset_field(model.id, model.field, old_dataset_field)
        # 删除数据集多字段数据缓存
        dataset_meta_cache.del_multi_dataset_field_cache(
            [dataset_field.get("id") for dataset_field in old_dataset_field]
        )
        # 删除数据集多字段数据缓存
        dataset_meta_cache.del_multi_dataset_field_cache(
            [dataset_field.get("id") for dataset_field in old_dataset_field]
        )
        # 删除并添加字段，合并成一个事务提交
        replace_field_and_delete(model, old_dataset_field)

        # API数据集中relation模式还需要保存dataset_tables_collection
        update_dataset_relation_info(model)

        # 一个数据集支持多个sql
        update_dataset_design(model)

        # 权限数据集，清除所有
        dataset_permission_service.refresh_dataset_row_permissions_by_dataset_id(model.id)
        # # 直连数据集(api/sql)可以使用变量
        # if (model.type == DatasetType.Api.value) or (
        #         model.type == DatasetType.Sql.value and model.connect_type == DatasetConnectType.Directly.value) or (
        #         model.type == DatasetType.Union.value and model.connect_type == DatasetConnectType.Directly.value):
        dataset_var_service.multi_upset_dataset_vars(model.id, model.var_content)
        # 关键字与变量关系的数据存储
        save_keyword_detail(model.id, model.var_content)

        # 删除数据集元数据所有缓存，再重新加载元数据和字段缓存， 保证version只改变一次。
        dataset_meta_cache.del_dataset_cache(model.id)
        dataset_meta_cache.del_dataset_field_cache(model.id)
        dataset_meta_cache.get_dataset_cache(model.id)
        dataset_meta_cache.get_dataset_field_cache(model.id)

        # 切换直连模式禁用同步flow
        if not dataset.get("connect_type") and model.connect_type == DatasetConnectType.Directly.value:
            _delete_sync_flow(dataset, model, queue_name)

        # 更新流程
        # 查询是否有正在运行的sql flow 实例，要是正在运行，先要停止运行再启动
        if (
                dataset["type"] in [DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Union.value]
                and model.connect_type != DatasetConnectType.Directly.value
                or dataset["type"] in [DatasetType.Excel.value]
        ):
            _update_dataset_flow(model, dataset, queue_name=queue_name)

        if dataset['type'] == DatasetType.Sql.value and dataset['use_cache'] and dataset.get('cache_flow_id'):
            flow_service.run_flow(dataset.get('cache_flow_id'))

        # 数据集操作记录, 保存原来的content和fields
        repository.add_data(
            "dataset_operate_record",
            get_operate_record_model(
                model.id,
                OperateMode.Update.value,
                data_source=data_source,
                content=dataset.get("content"),
                fields=json.dumps(old_dataset_field, ensure_ascii=False),
            ).get_dict(),
        )

    except UserError as e:
        raise UserError(message=e.message)
    except Exception as e:
        logging.error("编辑数据集错误", exc_info=True)
        msg = "编辑数据集错误，错误内容：" + str(e)
        raise UserError(message=msg)
    finally:
        locker.free()

    return dataset.get("name")


def get_union_data_source_id(data):
    if data.get('type') == DatasetType.Union.value:
        # 组合数据集编辑新增data_source_id字段
        content = json.loads(data.get("content"))
        source_dataset_ids = content.get("source_dataset_ids")
        source_datasets = repository.get_list("dataset", {'id': source_dataset_ids}, ["id"])
        if len(source_datasets) < len(source_dataset_ids):
            raise UserError(message="该组合数据集中有数据集已经被删除")

        # 组合直连模式的数据源 应该跟随数据集的数据源 否则数据源获取的就不正确
        from dataset.query.query_dataset_service import QueryDatasetService
        used_dataset_data = dataset_meta_cache.get_dataset_cache(source_datasets[0].get('id'))
        used_query_client = QueryDatasetService.get_query_client(used_dataset_data, [])
        if hasattr(used_query_client, "data_source_model") and used_query_client.data_source_model:
            content['data_source_id'] = used_query_client.data_source_model.id
        else:
            content['data_source_id'] = QueryDatasetService.get_dmp_data_source().id
        return json.dumps(content)
    else:
        return data.get("content")


def generate_subject_command(flow_id: str, subject_id: str) -> str:
    queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
    extra_data = {'download': 1, 'force_update': 0, 'new_version': 1}
    command = '%s %s %s %s %s %s %s %s' % (
        config.get('Rundeck.cmd_template'),
        g.code,
        flow_id,
        queue_name,
        subject_id,
        extra_data.get('download'),
        extra_data.get('force_update'),
        extra_data.get('new_version'),
    )
    return command


def update_dataset_cache_flow(model: DatasetModel, dataset: dict, queue_name: str):
    model.cache_flow.id = model.cache_flow_id
    model.flow.name = dataset['name']
    model.flow.nodes = [FlowNodeModel(id=repository.get_data_scalar("node", {"flow_id": model.cache_flow_id}, "id"), )]
    flow_service.update_flow_schedule(model.cache_flow_id, queue_name=queue_name)


def _update_dataset_flow(model, dataset, queue_name=None, command=None, subject_id=None):
    """
     更新数据集清洗流程
    :param dataset.models. model:
    :return:
    """
    # 更新flow , node
    model.flow.id = model.id
    model.flow.name = dataset["name"]
    model.flow.nodes = [
        FlowNodeModel(
            id=repository.get_data_scalar("node", {"flow_id": model.id}, "id"),
            name=model.flow.name,
            type=FlowNodeType.Dataset.value,
        )
    ]
    flow_instance_id = flow_instance_service.running_flow_is_exists(model.id)
    if flow_instance_id:
        flow_instance_service.stop_instance(flow_instance_id)
    # 如果一开始是直连，后来更改为调度，可能存在flow_id 找不到的情况
    if (
            dataset.get("connect_type") in [DatasetConnectType.Directly.value or DatasetConnectType.Push.value]
            and model.connect_type != DatasetConnectType.Directly.value
            and not repository.data_is_exists("flow", {"id": model.id})
    ):
        flow_service.add_flow(model.flow)

    if model.type == DatasetType.Api.value:
        task_id = get_task_id("dataset", DatasetType.Api.value, g.code, model.id)
        unacked_id = get_unacked_id(model.id)
        redis.conn().del_data(unacked_id)
        redis.conn().del_data(task_id)
        flow_service.update_flow(model.flow, is_schedule=True, queue_name=queue_name, command=command)
        if subject_id:
            dataset_subject_service.run_subject_flow(model.id, subject_id)
        else:
            flow_service.run_flow(model.id, 1, need_log=True, queue_name=queue_name)
        if model.flow.status == FlowStatus.Enable.value:
            flow_service.enable_flow(model.flow.id, command=command, queue_name=queue_name)
        else:
            flow_service.disable_flow(model.flow.id, command=command, queue_name=queue_name)
    else:
        flow_service.update_flow(model.flow, is_schedule=True, queue_name=queue_name, command=command)
        if subject_id:
            dataset_subject_service.run_subject_flow(model.id, subject_id)
        else:
            flow_service.run_flow(model.id, 1, need_log=True, queue_name=queue_name)
        if model.flow.status == FlowStatus.Enable.value:
            flow_service.enable_flow(model.flow.id, command=command, queue_name=queue_name)
        else:
            flow_service.disable_flow(model.flow.id, command=command, queue_name=queue_name)


@data_permission_edit_filter("dataset-edit")
def move_dataset(dataset_id, target_dataset_id, is_same_throw_error=True):
    """
    :移动数据集到目标文件夹下面
    :date 2017/6/19
    :param str dataset_id:
    :param str target_dataset_id:
    :param bool is_same_throw_error 文件夹相同是否抛错误
    :return :
    """

    if dataset_id == target_dataset_id:
        UserError(message="移动的数据集不能和目标文件夹一样")

    source_dataset = repository.get_data("dataset", {"id": dataset_id}, ["id", "level_code", "type"])

    if target_dataset_id:
        # 判断目标数据集是否可以移动
        target_dataset = repository.get_data("dataset", {"id": target_dataset_id}, ["id", "level_code", "type"])
        if not target_dataset:
            raise UserError(message="目标文件夹不存在")

        if target_dataset["type"] != DatasetType.Folder.value:
            raise UserError(message="目标必须是文件夹")

        if target_dataset["level_code"].find(source_dataset["level_code"]) == 0:
            if not is_same_throw_error:  # 相同的文件夹不进行后续操作和抛错误
                return
            raise UserError(message="不能移动到自身文件夹下面")

    # 更新数据集以及子集的level_code

    new_level_code = generate_level_code(target_dataset_id)

    update_sql = "update dataset set parent_id = '{}' where id = '{}';".format(target_dataset_id, dataset_id)
    return move_dataset_tree(source_dataset["level_code"], new_level_code, update_sql)


def batch_move_dataset(dataset_id_list, target_dataset_id):
    """
    :批量移动数据集到目标文件夹下面
    :date 2017/6/19
    :param str dataset_id_list:
    :param str target_dataset_id:
    :return :
    """
    if not dataset_id_list or len(dataset_id_list) == 0:
        UserError(message="请选择要移动的数据集")

    if target_dataset_id in dataset_id_list:
        UserError(message="移动的数据集不能和目标文件夹一样")

    source_dataset_list = repository.get_list(
        "dataset", {"id": dataset_id_list}, ["id", "level_code", "type"]
    )

    if target_dataset_id:
        # 判断目标数据集是否可以移动
        target_dataset = repository.get_data("dataset", {"id": target_dataset_id}, ["id", "level_code", "type"])
        if not target_dataset:
            raise UserError(message="目标文件夹不存在")

        if target_dataset["type"] != DatasetType.Folder.value:
            raise UserError(message="目标必须是文件夹")

        for source_dataset in source_dataset_list:
            if target_dataset["level_code"].find(source_dataset["level_code"]) == 0:
                raise UserError(message="不能移动到自身文件夹下面")

    for source_dataset in source_dataset_list:
        move_dataset_internal(source_dataset, target_dataset_id)


@data_permission_edit_filter("dataset-edit")
def move_dataset_internal(source_dataset, target_dataset_id):
    # 更新数据集以及子集的level_code
    new_level_code = generate_level_code(target_dataset_id)
    update_sql = "update dataset set parent_id = '{}' where id = '{}';".format(target_dataset_id,
                                                                               source_dataset['id'])
    move_dataset_tree(source_dataset["level_code"], new_level_code, update_sql)


def move_dataset_tree(source_level_code, new_level_code, update_sql):
    # 获取对应层级下的所有子级的level_code
    old_data = dataset_repository.get_move_list_by_level_code(source_level_code)
    if not old_data:
        return
    update_level_sql = [update_sql]
    for data in old_data:
        level_code = re.subn(r'^{}'.format(source_level_code), new_level_code, data.get('level_code'), flags=re.I)[0]
        sql = "update dataset set level_code = '{}' where id = '{}';".format(level_code, data.get('id'))
        update_level_sql.append(sql)
    if update_level_sql:
        exec_sql = ''.join(update_level_sql)
        repository.execute_multi_sql(sql=exec_sql)


def validate_dataset_name(model):
    """
    校验数据集名称
    :param model:
    :return:
    """
    if not model.name:
        raise UserError(message="名称不能为空。")

    name = model.name.strip()
    if "{" in name and "}" in name:
        raise UserError(message="名称不能包含{}大括号。")

    # 数据集名称暂不支持 ' 和 \
    if name.find("'") != -1 or name.find("\\") != -1 or name.find('/') != -1:
        raise UserError(message='数据集名称不能包含 ‘ 和\\ , /等特殊字符')

    if model.type == DatasetType.Folder.value:
        subject_id = (model.content or {}).get('subject_id')
        if subject_id:
            folders = dataset_repository.get_dataset_subject_folder_by_name(name)
        else:
            folders = dataset_repository.get_dataset_folder_by_name(name, model.parent_id)
        if folders and folders[0].get("id") != model.id:
            raise UserError(message="同级数据集文件夹不能重名。")
    else:
        dataset_tables = dataset_repository.get_dataset_by_name([name])
        if dataset_tables and dataset_tables[0].get("id") != model.id:
            raise UserError(message="数据集名称不能重名。")


def validate_dataset_field_name(model):
    """
    校验数据集字段名称
    :param model:
    :return:
    """
    if model.type == DatasetType.Union.value:
        if not model.field:
            raise UserError(message="数据集表字段不能为空")

        field_list = []
        for k, item in enumerate(model.field):
            field_list.append(item.get("col_name"))
        field_set = set(field_list)

        for item in field_set:
            if field_list.count(item) > 1:
                raise UserError(
                    message="数据集表字段名{col_name}不能重复，请重新编辑数据集，" "建议采用别名。".format(col_name=item))

            if "[" in item and "]" in item:
                raise UserError(
                    message="数据集表字段名{col_name}不能包含'[]'括号，请重新编辑数据集，" "建议采用别名。".format(
                        col_name=item))


def multi_add_dataset(dataset_list):
    # 判断是否是主题数据集，如果是则预处理数据，包括校验名称，level_code，生成主题文件夹ID，并填写主题数据集的parent_id
    is_subject, dataset_list = validate_dataset_subject_data(dataset_list)
    dataset_ids = []
    for dataset_data in dataset_list:
        dataset_data["type"] = dataset_data.get("type").upper()
        model = DatasetModel(**dataset_data)
        try:
            dataset_id, dataset_table_name = add_dataset(model, is_subject)
            dataset_ids.append(dataset_id)
        except Exception as ex:
            debugger.log("新增数据集错误，错误内容：%s" % str(traceback.format_exc()))
            for dataset_id in dataset_ids:
                repository.delete_data("dataset", {"id": dataset_id})
                repository.delete_data("dataset_field", {"dataset_id": dataset_id})
                repository.delete_data("dataset_operate_record", {"dataset_id": dataset_id})
                # 删除缓存
                dataset_meta_cache.del_dataset_cache(dataset_id)
            raise UserError(message="新增数据集错误，错误内容：" + str(ex))

    # 如果是主题数据集，则更新主题表或者主题数据表数据
    if dataset_list and is_subject:
        dataset_folder_model = DatasetModel(**dataset_list[0])
        is_direct = dataset_folder_model.connect_type == DatasetConnectType.Push.value
        init_dataset_flow(
            dataset_folder_model,
            queue_name=config.get('RabbitMQ.queue_name_work_flow', 'work_flow'),
            is_subject=True,
            is_direct=is_direct,
        )

    return dataset_ids


def validate_dataset_subject_data(dataset_list: list):
    if not isinstance(dataset_list, list):
        raise UserError(message='参数错误，添加多个数据集应该为列表')

    # 判断是否是主题数据集的保存
    is_subject = False
    for r in dataset_list:
        dataset_type = r.get('type', '').upper()
        if dataset_type == DatasetType.Folder.value and (r.get('content', {}) or {}).get('subject_id'):
            is_subject = True
            break
    if not is_subject:
        return is_subject, dataset_list

    # 预处理列表数据，生成主题ID以及补充主题表的parent_id
    dataset_folder_id = seq_id()
    # 确保将主题文件夹放在第一个创建
    new_dataset_list = []
    for r in dataset_list:
        r['type'] = r.get('type', '').upper()
        if r['type'] == DatasetType.Folder.value:
            r['id'] = dataset_folder_id
            new_dataset_list.insert(0, r)
        else:
            r['parent_id'] = dataset_folder_id
            new_dataset_list.append(r)

    for r in dataset_list:
        model = DatasetModel(**r)
        validate_dataset_model(model)

    return is_subject, new_dataset_list


def validate_folder_level(model):
    # 判断第几级别，最多创建5级文件夹
    if model.type == DatasetType.Folder.value and model.parent_id:
        parent_dataset = dataset_repository.get_dataset(model.parent_id)
        if len(parent_dataset["level_code"].split("-")) > 5 and model.type == DatasetType.Folder.value:
            raise UserError(message="最多创建5级文件夹")


def validate_dataset_model(model: DatasetModel):
    model.validate()
    model.name = re.sub(r"\s{2,}", " ", model.name).strip()
    validate_dataset_name(model)
    validate_dataset_field_name(model)
    validate_folder_level(model)
    validate_dataset_cache(model)


def generate_dataset_data(model: DatasetModel, is_subject: bool):
    model.parent_id = "" if model.parent_id is None else model.parent_id
    model.cache_flow_id = seq_id() if model.use_cache else ""
    data = model.get_dict(
        [
            "id",
            "name",
            "parent_id",
            "type",
            "content",
            "icon",
            "description",
            "connect_type",
            "edit_mode",
            "use_cache",
            "cache_flow_id",
            "is_complex",
            "disable_procedure",
            "is_import_table",
            "import_table_name",
            "import_table_type"
        ]
    )
    data["level_code"] = generate_level_code(model.parent_id)

    if model.type != DatasetType.Folder.value:
        # 添加table_name
        if model.type == DatasetType.Label.value:
            data["table_name"] = repository.get_data_scalar(
                "label", {"label_id": model.content["label_id"]}, "label_tablename"
            )
        else:
            data["table_name"] = generate_dataset_table_name(model.id)
        # 添加sql_table_names
        model.content['sql_table_names'] = extract_tables(model.content.get("sql")) if model.content.get("sql") else []
        data["content"] = json.dumps(model.content)
    elif is_subject:
        data['content'] = json.dumps(model.content)

    return data


def get_queue_name(model: DatasetModel):
    queue_name = None
    if model.type == DatasetType.Sql.value and model.flow and getattr(model.flow, "state_trigger", "") in [1, "1"]:
        dataset_service.check_clean_status(model.get_dict())
        queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
    return queue_name


def add_dataset(model: DatasetModel, is_subject: bool = False):
    """
    :添加数据集
    :date 2017/6/14
    :param model:
    :return :
    """
    # 文件夹自动生成id,不需要前端传
    if not getattr(model, "id") and model.type == DatasetType.Folder.value:
        model.id = seq_id()

    if model.use_cache:
        model.cache_flow_id = seq_id()

    validate_dataset_model(model)

    data = generate_dataset_data(model, is_subject)
    queue_name = get_queue_name(model)

    try:
        directly = False
        if (
                data.get("connect_type")
                and data.get("connect_type") == DatasetConnectType.Directly.value
                and data.get("content")
        ):
            # 目前SQL数据集直连模式仅支持MySQL
            if data.get("type") in [DatasetType.Sql.value, DatasetType.Api.value]:
                directly = True
                data_source_id = json.loads(data.get("content")).get("data_source_id")
                data_source = repository.get_data("data_source", {"id": data_source_id}, ["type"])
                if data_source.get("type") not in [
                    DataSourceType.Mysql.value,
                    DataSourceType.PostgreSQL.value,
                    DataSourceType.ADS.value,
                    DataSourceType.API.value,
                    DataSourceType.MysoftNewERP.value,
                    DataSourceType.Presto.value,
                    DataSourceType.MSSQL.value,
                    DataSourceType.MysoftShuXin.value,
                    DataSourceType.MysoftShuXin15.value
                ]:
                    raise UserError(message="目前SQL数据集直连模式仅支持MySQL、MSSQL、PostgreSQL、ADS、API、Presto数据源")
            # 新增组合数据集直连模式
            if data.get("type") in [DatasetType.Union.value]:
                directly = True
                # 组合数据集编辑新增data_source_id字段
                data['content'] = get_union_data_source_id(data)

        # excel需要自动绑定数据源
        if data.get("type") == DatasetType.Excel.value and get_storage_type(
                g.code) == DatasetStorageType.DatasetStorageOfLocal.value:
            content = json.loads(data.get('content'))
            content['bind_source_id'] = get_master_local_db_new_erp_data_source().get('id')
            data['content'] = json.dumps(content)

        repository.add_data("dataset", data)
        if model.type != DatasetType.Folder.value:
            dataset_field_service.add_dataset_field(model)

        # API数据集中relation模式还需要保存dataset_tables_collection
        add_dataset_relation_info(model)
        # 直连数据集(api/sql/Union)可以使用变量
        if (model.type == DatasetType.Api.value) or (
                model.type == DatasetType.Sql.value and model.connect_type == DatasetConnectType.Directly.value) or (
                model.type == DatasetType.Union.value and model.connect_type == DatasetConnectType.Directly.value):
            dataset_var_service.multi_upset_dataset_vars(model.id, model.var_content)
            # 关键字与变量关系的数据存储
            save_keyword_detail(model.id, model.var_content)
        # 组合数据集需要添加依赖关系表
        if model.type == DatasetType.Union.value and model.content and model.content.get("source_dataset_ids"):
            add_update_dataset_depend(data.get("id"), model.content.get("source_dataset_ids"))
        if is_subject:
            update_dataset_subject_data(model)

        # 一个数据集支持多语法sql
        update_dataset_design(model)

        # 只有sql数据集还有Excel数据集包含地址才需要跑流程, sql数据集直连模式不需要跑流程，api数据集不跑流程
        if (
                model.type
                in [DatasetType.Sql.value, DatasetType.Excel.value, DatasetType.Union.value, DatasetType.Api.value]
                and not directly
                and not is_subject
        ):
            init_dataset_flow(model, queue_name=queue_name)

        if model.type in [DatasetType.Sql.value] and directly and model.use_cache:
            init_dataset_cache_flow(model)
            flow_service.run_flow(model.cache_flow_id)

        repository.add_data(
            "dataset_operate_record",
            get_operate_record_model(model.id, OperateMode.Add.value, data_source=get_data_source(model)).get_dict(),
        )

        # 保存数据集使用的相关表记录
        dataset_used_table_service.save_used_table(model)
        # 设置数据集数据缓存, 使用数据库查询出来的数据, 保证数据的一致性
        cache_data = dataset_repository.get_dataset(data.get('id'))
        dataset_meta_cache.set_dataset_cache(model.id, cache_data)
    except Exception as ex:
        clear_dataset_data(model)

        logging.error("新增数据集错误", exc_info=True)
        msg = "新增数据集错误，错误内容：" + str(ex)
        raise UserError(message=msg)

    return model.id, data.get("table_name")


def generate_dataset_relation_sql(model: DatasetModel):
    content = model.content
    if isinstance(content, str):
        content = json.loads(content)
    data_source_model = data_source_service.get_data_source(content['data_source_id'])
    service_instance = dataset_service.get_dataset_service(model, data_source_model)
    query_sql = service_instance.get_query_sql(with_limit=False)
    count_sql = service_instance.generate_sql_dataset_count_sql()
    relation_sql = {'sql': query_sql, 'count_sql': count_sql}
    return json.dumps(relation_sql)


def add_dataset_relation_info(model: DatasetModel):
    # 存在过滤条件需要保存dataset_filter
    if model.filter_content:
        save_dataset_filter(model.id, model.filter_content)
    if (
            model.type in (DatasetType.Api.value, DatasetType.Sql.value, DatasetType.Union.value)
            and model.edit_mode == DatasetEditMode.Relation.value
            and model.relation_content
    ):
        data_source_type = None
        if model.type == DatasetType.Sql.value:
            data_source_type = get_data_source_type_by_model(model)
        save_relation_mode_data(model.id, model.relation_content, data_source_type)
    # 关系型 或者 非调度 组合需要计算relationSQL 否则识别不出关键字（关键字识别算法不合理）
    if (model.type in (
            DatasetType.Sql.value, DatasetType.Api.value) and model.edit_mode == DatasetEditMode.Relation.value) or (
            model.type in (
            DatasetType.Union.value) and model.edit_mode == DatasetEditMode.Relation.value and model.connect_type == DatasetConnectType.Directly.value):
        content_obj = model.content if isinstance(model.content, dict) else json.loads(model.content)
        relation_sql = generate_dataset_relation_sql(model)
        content_obj['replace_sql'] = relation_sql
        repository.update('dataset', {'relation_sql': relation_sql, 'content': json.dumps(content_obj)},
                          {'id': model.id})


def get_data_source_type_by_model(model):
    try:
        content = model.content
        if isinstance(content, str):
            content = json.loads(content)
        data_source_id = content.get("data_source_id")
        data_source = repository.get_data("data_source", {"id": data_source_id}, ["type"])
        return data_source.get("type")
    except Exception as e:
        logging.error(e)
        logging.error("获取data_source异常" + str(model))
    return None


def update_dataset_relation_info(model: DatasetModel):
    # 存在过滤条件需要保存dataset_filter
    if model.filter_content:
        save_dataset_filter(model.id, model.filter_content)
    else:
        repository.delete_data('dataset_filter', {'dataset_id': model.id})
    # 删除数据集过滤器数据缓存，重新加载一次缓存，保证version只改变一次
    dataset_meta_cache.del_dataset_filter_cache(model.id)
    dataset_meta_cache.get_dataset_filter_cache(model.id)

    # API数据集中relation模式还需要保存dataset_tables_collection
    # 关系型 或者 非调度 组合需要计算relationSQL 否则识别不出关键字（关键字识别算法不合理）
    if (
            model.type in (DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Union.value)
            and model.edit_mode == DatasetEditMode.Relation.value
    ):
        relation_sql = None
        content_obj = model.content if isinstance(model.content, dict) else json.loads(model.content)
        if model.relation_content:
            save_relation_mode_data(model.id, model.relation_content)
            # 删除数据集关联数据缓存，重新加载一次缓存，保证version只改变一次
            dataset_meta_cache.del_dataset_link_data_cache(model.id)
            dataset_meta_cache.get_dataset_link_data_cache(model.id)
            if model.type in [DatasetType.Sql.value, DatasetType.Api.value] or (
                    model.type == DatasetType.Union.value and model.connect_type == DatasetConnectType.Directly.value):
                relation_sql = generate_dataset_relation_sql(model)
                content_obj['replace_sql'] = relation_sql
        repository.update('dataset', {'relation_sql': relation_sql, 'content': json.dumps(content_obj)},
                          {'id': model.id})


def add_update_dataset_depend(dataset_id: str, source_dataset_ids: list):
    for source_dataset_id in source_dataset_ids:
        # 判断其是否为主题包数据集
        subject_dataset = is_subject_table(source_dataset_id)
        if subject_dataset:
            # 这里是调度使用(主题包)
            save_dataset_depend(subject_dataset.get("id"), dataset_id)
        save_dataset_depend(source_dataset_id, dataset_id)


def save_dataset_depend(source_dataset_id, depend_id):
    data = {"source_dataset_id": source_dataset_id, "depend_id": depend_id}
    if not repository.data_is_exists("dataset_depend", condition=data):
        repository.add_data("dataset_depend", data)


def is_subject_table(dataset_id: str):
    if repository.data_is_exists("dataset_subject_table", {"dataset_id": dataset_id}):
        result = dataset_subject_repository.get_dataset_subject(dataset_id)
        return result
    return {}


def update_dataset_subject_data(model: DatasetModel):
    content = model.content or {}
    subject_id = content.get('subject_id')
    if not subject_id:
        raise UserError(message='主题数据集的content数据格式错误，须为json格式且必须有subject_id')

    if model.type == DatasetType.Folder.value:
        repository.update_data('dataset_subject', {'dataset_folder_id': model.id}, {'id': subject_id})

    else:
        subject_table_name = content.get('origin_table_name')
        repository.update_data(
            'dataset_subject_table',
            {'dataset_id': model.id},
            {'table_name': subject_table_name, 'dataset_subject_id': subject_id},
        )


def update_dataset_design(model: DatasetModel):
    """
    更新dataset_design
    :param model:
    :return:
    """
    if not model.design:
        return repository.delete_data('dataset_design', {'dataset_id': model.id})
    # 先删再插
    repository.delete_data('dataset_design', {'dataset_id': model.id}, commit=False)
    # repository.add_data('dataset_design', {'content': model.content, 'dataset_id': model.id, "is_enable": 1}, commit=False)
    design_list = []
    for i in model.design:
        i['dataset_id'] = model.id
        design_list.append(i)
    repository.add_list_data('dataset_design', design_list,
                             ['content', 'dataset_id', 'is_enable', 'is_main', 'app_level_code', 'db_type'],
                             commit=True)


def clear_dataset_data(model: DatasetModel):
    repository.delete_data("dataset", {"id": model.id})
    repository.delete_data("dataset_field", {"dataset_id": model.id})
    repository.delete_data("dataset_operate_record", {"dataset_id": model.id})
    repository.delete_data("dataset_tables_collection", {"dataset_id": model.id})
    repository.delete_data("dataset_filter", {"dataset_id": model.id})
    # 删除流程数据
    flow_service.delete_flow(model.id, is_delete_scheduler=model.type != DatasetType.Excel.value)
    # 删除数据集数据缓存
    dataset_meta_cache.del_dataset_cache(model.id)
    # 删除数据集字段数据缓存
    dataset_meta_cache.del_dataset_field_cache(model.id)


def format_relation_table_alias_name(node_datas, data_source_type):
    if data_source_type and data_source_type == DataSourceType.PostgreSQL.value:
        if len(node_datas) == 1:
            return
        for node in node_datas:
            if node.get("alias_name"):
                node['alias_name'] = node.get("alias_name").replace(".", "_")


def save_relation_mode_data(dataset_id, relation_content, data_source_type=None):
    """
    保存relation_content中的数据到dataset_tables_collection表中
    :return:
    """
    # 判断 relation_content 类型
    if isinstance(relation_content, str):
        relation_content = json.loads(relation_content)
    node_datas, link_datas = relation_content.get('nodeDataArray'), relation_content.get('linkDataArray')
    format_relation_table_alias_name(node_datas, data_source_type)
    # 获取nodeData所有的id和表名,{'id1': 'table_name1', 'id2': 'table_name2'}
    node_dict = {row.get('id'): row.get('name') for row in node_datas}
    node_alias_dict = {row.get('id'): row.get('alias_name') for row in node_datas}
    node_table_type_dict = {row.get('id'): row.get('table_type') for row in node_datas}
    # 组装能直接写入dataset_tables_collection表的数据datas
    datas = []
    for link_data in link_datas:
        data = dict()
        data['id'] = seq_id()
        data['dataset_id'] = dataset_id
        data['from_id'] = link_data.get('from_id')
        data['from_table_name'] = node_dict.get(link_data.get('from_id'))
        data['from_alias_name'] = node_alias_dict.get(link_data.get('from_id'))
        data['to_id'] = link_data.get('to_id')
        data['to_table_name'] = node_dict.get(link_data.get('to_id'))
        data['to_alias_name'] = node_alias_dict.get(link_data.get('to_id'))
        data['join_type'] = link_data.get('join_type')
        data['join_fields'] = json.dumps(link_data.get('join_fields'))
        data['from_table_type'] = link_data.get('from_table_type') if link_data.get(
            'from_table_type') else node_table_type_dict.get(link_data.get('from_id'))
        data['to_table_type'] = link_data.get('to_table_type') if link_data.get(
            'to_table_type') else node_table_type_dict.get(link_data.get('to_id'))
        datas.append(data)
    # 单表的情况下，nodeDatas有值，linkDatas中没有值，需要写一条linkDatas的记录
    if not link_datas and node_datas:
        data = dict()
        data['id'] = seq_id()
        data['dataset_id'] = dataset_id
        data['from_id'] = node_datas[0].get("id")
        data['from_table_name'] = node_datas[0].get("name")
        data['from_alias_name'] = node_datas[0].get("alias_name")
        data['from_table_type'] = node_datas[0].get("table_type")
        datas.append(data)
    # 获取匹配字段关系,
    return dataset_repository.update_dataset_tables_collection(dataset_id, datas)


def save_dataset_filter(dataset_id, filter_content):
    """
    保存filter_content中的数据到dataset_filter表中
    :return:
    """
    if isinstance(filter_content, str):
        filter_content = json.loads(filter_content)
    for filter_data in filter_content:
        filter_data['id'] = seq_id()
        filter_data['dataset_id'] = dataset_id
        try:
            if not isinstance(filter_data.get("col_value"), str):
                filter_data['col_value'] = json.dumps(filter_data.get("col_value"))
            else:
                filter_data['col_value'] = filter_data.get("col_value")
        except Exception as e:
            raise UserError(message="筛选器的值格式错误，无法json序列化：" + str(e))
    return dataset_repository.update_dataset_filter(dataset_id, filter_content)


def get_data_source(model):
    data_source = ""
    if isinstance(model.content, str):
        model.content = json.loads(model.content)
    if model.type == DatasetType.Excel.value:
        data_source = model.content.get("file_name") if model.content else ""
    elif model.type in [DatasetType.Sql.value, DatasetType.Api.value]:
        data_source = repository.get_data_scalar("data_source", {"id": model.content.get("data_source_id")}, "name")
    elif model.type == DatasetType.Union.value:
        data_sources = []
        dataset_union_service = DatasetUnionService()
        dataset_union_service.sql = model.content.get("sql")
        dataset_union_service.parse_sql()
        for table_name in dataset_union_service.table_names:
            if not table_name:
                continue
            table_name = table_name.strip()
            find_tables = re.findall(r"\{(.*)\}", table_name, flags=re.I)
            query_table = find_tables[0] if find_tables else ""
            data_sources.append(query_table)
        data_source = ",".join(data_sources)

    return data_source


def init_dataset_cache_flow(model: DatasetModel):
    model.cache_flow.id = model.cache_flow_id
    model.cache_flow.name = model.name
    model.cache_flow.type = FlowType.DatasetCache.value
    model.cache_flow.status = FlowStatus.Enable.value if model.use_cache else FlowStatus.Disable.value
    model.cache_flow.nodes = [FlowNodeModel(name=model.name, type=FlowNodeType.DatasetCache.value)]
    flow_service.add_flow(model.cache_flow)

    if model.flow.status == FlowStatus.Enable.value:
        flow_service.update_flow_schedule(model.cache_flow_id)


def init_dataset_flow(model, queue_name=None, is_direct: bool = False, is_subject: bool = False):
    """
    初始化数据集清洗流程
    :param dataset.models. model:
    :return:
    """
    model.flow.id = model.id
    model.flow.name = model.name
    model.flow.nodes = [FlowNodeModel(name=model.name, type=FlowNodeType.Dataset.value)]
    flow_service.add_flow(model.flow)

    # 立即运行sql数据集流程
    if not is_subject:
        flow_service.run_flow(model.id, queue_name=queue_name)
    else:
        dataset_subject_service.run_subject_flow(model.id, model.content.get('subject_id'))

    # 启动调度
    if model.flow.status == FlowStatus.Enable.value and not is_direct:
        command = None
        if is_subject:
            command = generate_subject_command(model.flow.id, model.content.get('subject_id'))
        flow_service.update_flow_schedule(model.id, queue_name=queue_name, command=command)


@data_permission_edit_filter("dataset-edit")
def rename_dataset(dataset_id, name, dataset_type, parent_id=""):
    """
    :数据集重命名
    :date 2017/6/17
    :param :
    :return :
    """
    if not name:
        raise UserError(message="请输入新的名字")
    old_dataset = repository.get_data("dataset", {"id": dataset_id}, fields=["id", "name"])
    if not old_dataset:
        raise UserError(message="数据集不存在")

    # 名称没有发生变化，不做什么处理
    if old_dataset.get("name") == name:
        return 1

    dataset_model = DatasetModel()
    dataset_model.id = dataset_id
    dataset_model.name = name
    dataset_model.type = dataset_type
    dataset_model.parent_id = parent_id
    validate_dataset_name(dataset_model)

    # 更新关联数据集名称，改为提示有关联不让重命名。
    dataset_depends = dataset_repository.get_dataset_relate_dataset(dataset_model.id)
    if dataset_depends:
        dataset_names = [table.get("name") for table in dataset_depends]
        msg = "重命名无法完成，因为【{}】组合数据集正在使用".format(','.join(dataset_names))
        raise UserError(message=msg)

    open_datas = open_data_repository.query_dataset_relation(dataset_id)
    if open_datas:
        open_data_names = [table.get("name") for table in open_datas]
        msg = "重命名无法完成，因为【{}】开放数据API正在使用".format(','.join(open_data_names))
        raise UserError(message=msg)

    update_row = repository.update_data("dataset", {"name": name}, {"id": dataset_id})
    # 更新流程数据
    repository.update_data("flow", {"name": name}, {"id": dataset_id})
    repository.update_data("instance", {"name": name}, {"flow_id": dataset_id})

    return update_row


def create_dataset_folder(folder_path):
    """
    创建数据集文件夹
    :param folder_path:
    :return:
    """
    result_parent_id = ""
    folder_parent_id = ""
    if folder_path:
        folder_paths = folder_path.split("/")
        for fpath in folder_paths:
            folders = dataset_repository.get_dataset_folder_by_name(fpath, folder_parent_id)
            if not folders:
                new_folder_dataset_model = DatasetModel()
                new_folder_dataset_model.id = ""
                new_folder_dataset_model.type = DatasetType.Folder.value
                new_folder_dataset_model.name = fpath
                new_folder_dataset_model.parent_id = folder_parent_id
                new_id, table_name = add_dataset(new_folder_dataset_model)
                folder_parent_id = new_id
                result_parent_id = new_id
            else:
                folder_parent_id = folders[0].get("id")
                result_parent_id = folders[0].get("id")

    return result_parent_id


def copy_union_dataset(folder_path, dataset_name, dataset_id):
    """
    拷贝组合数据集、数据集字段、数据数据表
     :param str folder_path: 数据集文件夹路径。例如：a/b/c
    :param str dataset_name: 数据集文件名称。
    :param str dataset_id: 源数据集集id
    :return:
    """

    old_dataset = dataset_repository.get_dataset(dataset_id)
    if not old_dataset:
        raise UserError(message="创建数据集失败，数据库中没有该数据集：" + dataset_id)

    parent_id = create_dataset_folder(folder_path)

    # 高级字段需要替换expression里面用到的id值
    field_id_dict = {}
    old_dataset_fields = dataset_field_repository.get_dataset_field(dataset_id)
    for old_dataset_field in old_dataset_fields:
        old_dataset_field["old_id"] = old_dataset_field.get("id")
        old_dataset_field["id"] = seq_id()
        field_id_dict[old_dataset_field["old_id"]] = old_dataset_field["id"]
    old_dataset["field"] = old_dataset_fields

    # 如果存在高级字段，则修改高级字段中对应的字段id
    _update_advance_field_expression(old_dataset_fields, field_id_dict)

    new_dataset_model = DatasetModel(**old_dataset)
    new_dataset_model.id = seq_id()

    new_dataset_model.type = DatasetType.Union.value
    new_dataset_model.name = dataset_name
    new_dataset_model.parent_id = parent_id
    old_table_name = new_dataset_model.table_name

    sql_cols, replace_sql_cols = get_sql_cols(old_dataset_fields)
    content = {
        "count": 0,
        "sql": "select {cols} from {table_name}".format(
            cols=",".join(sql_cols), table_name="{" + old_dataset.get("name") + "}"
        ),
        "replace_sql": "select {cols} from {table_name}".format(
            cols=",".join(replace_sql_cols), table_name=old_dataset.get("table_name")
        ),
    }
    new_dataset_model.content = content

    new_dataset_id, table_name = add_dataset(new_dataset_model)

    dataset_repository.copy_dataset_data(old_table_name, table_name, replace_sql_cols)

    result_dataset_fields = []
    for old_dataset_field in old_dataset_fields:
        result_dataset_fields.append({old_dataset_field.get("old_id"): old_dataset_field.get("id")})
    result_dataset = {"dataset_id": {dataset_id: new_dataset_id}, "dataset_field_id": result_dataset_fields}

    return result_dataset


def get_sql_cols(old_dataset_fields):
    """
    获取sql语句的字段名
    :param old_dataset_fields:
    :return:
    """
    sql_cols = []
    replace_sql_cols = []
    for old_dataset_field in old_dataset_fields:
        if old_dataset_field.get("type") == DatasetFieldType.Normal.value:
            if old_dataset_field.get("alias_name"):
                sql_cols.append("[{alias_name}]".format(alias_name=old_dataset_field.get("alias_name")))
            else:
                sql_cols.append("[{col}]".format(col=old_dataset_field.get("col_name")))
            replace_sql_cols.append(old_dataset_field.get("col_name"))
    return sql_cols, replace_sql_cols


def get_operate_record_model(
        dataset_id, operate_mode, data_source=None, content="", fields="", instance_id="", run_status=None
):
    """
    获取数据集操作记录model
    :return:
    """
    operate_record_model = DatasetOperateRecordModel()
    operate_record_model.id = seq_id()
    operate_record_model.name = data_source
    operate_record_model.dataset_id = dataset_id
    operate_record_model.operating_mode = operate_mode
    operate_record_model.old_content = content
    operate_record_model.old_fields = fields
    operate_record_model.instance_id = instance_id
    operate_record_model.run_status = run_status
    return operate_record_model


def _update_advance_field_expression(old_dataset_fields, field_id_dict):
    """
    更新高级字段中expression里面的id值
    :param list old_dataset_fields: 字段数据列表
    :param dict field_id_dict: 旧字段id：新字段id 的dict数据
    :return:
    """
    for field in old_dataset_fields:
        if field.get("type") == "普通":
            continue
        try:
            expression_item_list = json.loads(field.get("expression"))
            for expression_item in expression_item_list:
                if "id" in expression_item.keys():
                    # 新旧字段id替换
                    expression_item["id"] = field_id_dict.get(expression_item.get("id"))

            field["expression"] = json.dumps(expression_item_list)
        except Exception as e:
            raise UserError(message="高级字段新增失败，错误内容：" + str(e))


def has_variables(dataset_id):
    vars = batch_get_dataset_vars([dataset_id])
    return vars


def has_union_dataset_reference(dataset_id):
    sql = ''' select source_dataset_id,depend_id from dataset_depend where source_dataset_id = %(dataset_id)s or depend_id = %(dataset_id)s limit 1'''
    data = repository.get_data_by_sql(sql, {'dataset_id': dataset_id})
    if data:
        source_dataset_id = data[0]['source_dataset_id']
        depend_id = data[0]['depend_id']
        if dataset_id == source_dataset_id:
            return get_dataset_name(depend_id)
        elif dataset_id == depend_id:
            return get_dataset_name(source_dataset_id)
    flow_id = dataset_id
    replacement_data = get_replacement_dataset(flow_id)
    if replacement_data and replacement_data.get("id"):
        flow_id = replacement_data.get("id")
    depend_flow_list = flow_repository.get_depend_flow(flow_id)
    if depend_flow_list:
        return depend_flow_list[0]['name']
    depend_dataset_list = flow_repository.get_depend_dataset(flow_id)
    if depend_dataset_list:
        return depend_dataset_list[0]['name']
    return None


def has_new_union_dataset_reference(dataset_id):
    sql = ''' select source_dataset_id,depend_id from dataset_depend where source_dataset_id = %(dataset_id)s limit 1'''
    data = repository.get_data_by_sql(sql, {'dataset_id': dataset_id})
    if data:
        source_dataset_id = data[0]['source_dataset_id']
        depend_id = data[0]['depend_id']
        if dataset_id == source_dataset_id:
            return get_dataset_name(depend_id)
    return None


def get_dataset_name(dataset_id):
    if not dataset_id:
        return ''
    return repository.get_columns('dataset', {'id': dataset_id}, 'name')


def update_operation_flow(status, just_exec=0):
    """
    开启或关闭运营数据清洗功能
    :param status:
    :param just_exec:
    :return:
    """
    # 查询是否有过flow记录
    flow = repository.get_one("flow", {"type": FlowType.OperationCollector.value}, ['id', 'status'])
    data_clean_key = 'operator_data_clean_last_time'
    cache = redis.RedisCache()
    if status == OperationFlowType.START.value:
        need_schedule = True
        flow_id = None
        if not flow:
            model = FlowModel(
                id=seq_id(),
                name="运营数据清洗",
                type=FlowType.OperationCollector.value,
                schedule=f"0 {random.randint(1, 59)} {random.randint(6, 8)} * * ?",
                status=FlowStatus.Enable.value,
                run_status="已创建",
            )
            model.nodes = [FlowNodeModel(name=model.name, type=FlowNodeType.OperationCollector.value)]
            flow_service.add_flow(model)
            flow_id = model.id
            flow = model.get_dict()
        elif flow.get("status") == FlowStatus.Disable.value:
            repository.update("flow", {"status": FlowStatus.Enable.value}, {"id": flow.get("id")})
            flow_id = flow.get("id")
        else:
            need_schedule = False
        if need_schedule:
            # 添加开启标识
            cache.set_data(key=data_clean_key, value=1)
            # flow_service.run_flow(flow_id)
            repository.update("flow", {"schedule": f"0 {random.randint(1, 59)} {random.randint(6, 8)} * * ?"},
                              {"id": flow.get("id")})
            flow_service.update_flow_schedule(flow_id)
        if just_exec == 1:
            redis.RedisCache().set_data(key=data_clean_key, value=1)
            flow_service.run_flow(flow.get('id'))
    else:
        if flow:
            repository.update("flow", {"status": FlowStatus.Disable.value}, {"id": flow.get("id")})
            # 删除标识
            cache.delete(key=data_clean_key)
            flow_service.delete_flow_schedule(flow.get("id"))


def delete_dataset_version(dataset):
    from components.data_center_api import get_new_erp_datasource_model, local_execute_sql
    from components.storage_setting import is_local_storage
    from dataset.services.dataset_async_service import get_global_dataset_info
    if dataset:
        get_global_dataset_info(dataset)
        dataset_id = dataset.get('id')
        # 获取dataset_version的所有表数据
        table_list = repository.get_columns('dataset_version', {'dataset_id': dataset_id}, 'table_name')
        repository.delete_data('dataset_version', {'dataset_id': dataset_id})
        try:
            if is_local_storage(g.code):
                datasource_model = get_new_erp_datasource_model(dataset.get('content'))
                db_type = datasource_model.db_type or datasource_model.conn_str.DbType
                delete_sql = get_drop_table(db_type, table_list)
                if delete_sql:
                    local_execute_sql(delete_sql, datasource_model.conn_str, dataset.get('content'))
            else:
                # 云端DATA库删除
                sql = get_drop_table(DBEngine.Mysql.value, table_list)
                if sql:
                    repository.execute_multi_sql('data', sql)
        except Exception as e:
            logging.error(str(e))
        return True


def get_drop_table(db_type, delete_table: list):
    drop_sql = ''
    if db_type == DBEngine.Mysql.value:
        sql = "drop table IF EXISTS {table};"
    else:
        sql = "if object_id(N'{table}', N'U') is not null drop table [{table}];"
    for table in delete_table:
        drop_sql = drop_sql + sql.format(table=table)
    return drop_sql
