#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    <NAME_EMAIL> on 2017/6/14.
"""
import datetime
import hashlib
import json
import logging
import random
import re
import traceback
from json import J<PERSON><PERSON>ecodeError
from collections import defaultdict
import time

import pymysql
import sqlparse

from base import repository
from base.dmp_constant import CHECK_HAS_ROLE_FILTER, DATASET_ROW_PERMISSION, DATASET_VARS_NAME, \
    DATASET_ROW_PERMISSION_KEYS
from base.enums import (
    DatasetType,
    DatasetFieldGroup,
    DataSourceType,
    DatasetFieldType,
    FlowNodeType,
    FlowType,
    DataBaseType,
    DatasetConnectType,
    DatasetEditMode,
    OperateMode,
    SubjectDatasetInspectionStatus,
)
from base.service import sort_tree_data
from base.dmp_constant import DATASET_FILTERABLE_TABLE_LIMIT
from components.fast_logger import FastLogger
from components.mysoft import CloudAPI
from components.global_utils import handle_g
from components.storage_setting import get_storage_type, is_local_storage
from components.query_structure_sql import QueryStructure, Select, ModelEncoder, Prop, Order
from components.message_queue import RabbitMQ
from dashboard_chart.services import external_dashboard_service
from dashboard_chart.utils.common import add_api_dataset_params
from data_source.repositories import mysql_data_source_repository
from data_source.repositories import postgresql_data_source_repository
from data_source.services import data_source_service

from data_source.services.data_source_service import trans_import_tables_shuxin
from data_source.services.data_source_service import trans_import_tables_mysoftnewerp
from data_source.services.data_source_service import get_imported_table

from dataset.common import advance_field_helper
from dataset.common import sql_helper
from dataset.models import DatasetModel, DatasetOperateRecordModel
from dataset.query.link_data import JoinField
from dataset.query.link_data import LinkData
from dataset.repositories import dataset_inspection_repository
from dataset.repositories import dataset_operate_repository
from dataset.repositories import dataset_repository, dataset_field_repository
from dataset.repositories.dataset_api_repository import APIDatasetRepository
from dataset.repositories.indicator_repository import get_indicator_model_root_id
from dataset.services import dataset_field_service, dataset_permission_service, dataset_define_service
from dataset.services import dataset_rbac_result_service, dataset_subject_service
from dataset.services.dataset_api_service import DatasetAPIService
from dataset.services.dataset_base_service import DatasetBaseService
from dataset.services.dataset_datahub_oracle_service import DatasetDataHubOracleService
from dataset.services.dataset_datahub_service import DatasetDataHubService
from dataset.services.dataset_datahub_sql_server_service import DatasetDataHubSqlServerService
from dataset.services.dataset_erp_service import ErpDatasetFactory
from dataset.services.dataset_excel_service import DatasetExcelService
from dataset.services.dataset_label_service import DatasetLabelService
from dataset.services.dataset_mysql_service import DatasetMysqlService
from dataset.services.dataset_mssql_service import DatasetMssqlService
from dataset.services.dataset_shuxin15_service import Pulsar15DatasetService
from dataset.services.indicator_service import PulsarDatasetService
from dataset.services.dataset_postgresql_service import DatasetPostgreSQLService
from dataset.services.dataset_rbac_result_service import get_dataset_filter_where_str
from dataset.services.dataset_union_service import DatasetUnionService
from dataset.services.dataset_subject_service import HighDataDatasetService
from dataset.services.dataset_ads_service import DatasetADSService
from dataset.services.dataset_mysoft_new_erp_service import DatasetMysoftNewErp
from dataset.services import advanced_field_service
from dataset.services.dataset_presto_service import DatasetPrestoService
from dataset.services import dataset_var_service
from dmplib import config
from dmplib.constants import DATASET_TABLE_NAME_PREFIX, LABEL_DETAIL_TABLE_NAME_SUFFIX
from dmplib.hug import g
from dmplib.redis import conn as conn_redis
from components.redis_utils import RCache
from dmplib.saas.project import get_data_db
from dmplib.utils.errors import UserError
from dmplib.utils.format import strtotime
from dmplib.utils.strings import seq_id
from flow.models import FlowNodeModel, ReplacementNodeContentModel
from flow.repositories import flow_repository
from flow.services import flow_instance_service
from flow.services import flow_service
from message.models import MessageModel
from message.services import message_service
from rbac.services.data_permissions import data_permission_edit_filter, data_permission_filter
from rbac.services.dataset_cache import DatasetFilterCache
from rbac.services.grant_service import clean_chart_cache
from system.system_constant import USER_SYNC
from user.services import user_service
from user.services.user_service import refresh_dataset_row_permissions
from manual_filling.services.filling_template_service import ManualFillingService
from ppt.services import ppt_service

from data_source.models import (
    TableQueryModel, ColumnQueryModel
)

from base.dmp_constant import DATA_SOURCE_DATASET_GUID

from typing import Any, Dict, List

logger = logging.getLogger(__name__)


def get_dataset_map(**kwargs):
    """

    :param kwargs:
    :return:

    """
    dataset_ids = kwargs.get('dataset_ids', '')
    if dataset_ids:
        return dataset_repository.get_dataset_map_by_ids(dataset_ids)


@data_permission_edit_filter('dataset-edit')
def data_replacement(model, heads, append=False):
    """
    数据集数据替换
    :param model:
    :param heads:
    :param append: 是否为追加数据
    :return:
    """
    error_word = ['替换', '追加'][int(bool(append))]
    if isinstance(model.content, dict):
        data_content = model.content
    else:
        try:
            data_content = json.loads(model.content)
            if not data_content.get('file_name'):
                raise UserError(message='数据{}文件名不能为空'.format(error_word))
            if not data_content.get('oss_url'):
                raise UserError(message='数据{}oss地址不能为空'.format(error_word))
        except json.JSONDecodeError as e:
            raise UserError(message='数据{}内容json格式错误：'.format(error_word) + str(e))

    dataset = dataset_repository.get_dataset(model.id)
    if not dataset:
        raise UserError(message='数据集不存在')
    if dataset.get('type') in (DatasetType.ExternalSubject.value, DatasetType.Label.value):
        raise UserError(
            message='不支持{dataset_type}数据集'.format(
                dataset_type={
                    DatasetType.Label.value: "标签",
                    DatasetType.ExternalSubject.value: "外部主题数据集",
                }.get(dataset.get('type'))
            )
        )

    # 校验字段名是否一致
    if not heads:
        raise UserError(message="{}文件字段不能为空。".format(error_word))
    dataset_fields = dataset_field_service.get_normal_field(model.id)
    if len(heads) != len(dataset_fields):
        raise UserError(message="数据集字段和{}文件字段不一致。".format(error_word))
    for head in heads:
        is_exist = False
        for dataset_field in dataset_fields:
            head_name = head.get("alias_name") or head.get("col_name")
            dataset_field_name = dataset_field.get("alias_name") or dataset_field.get("col_name")
            if head_name == dataset_field_name:
                is_exist = True
                break

        if not is_exist:
            raise UserError(message="数据集字段和{}文件字段不一致。".format(error_word))

    data_content['dataset_id'] = model.id
    data_content['table_name'] = dataset.get('table_name')

    replacement_id = dataset.get("replacement_id")
    flow_node_type = [FlowNodeType.REPLACEMENT.value, FlowNodeType.APPEND.value][int(bool(append))]
    if replacement_id:
        flow = flow_service.get_flow(replacement_id, include_nodes=True)
        for node in flow.nodes:
            node.content = ReplacementNodeContentModel(**data_content)
            node.type = flow_node_type
        flow.name = dataset.get('name') + "_" + "数据{}".format(error_word)
        flow_instance_id = flow_instance_service.running_flow_is_exists(model.id)
        if flow_instance_id:
            flow_instance_service.stop_instance(flow_instance_id)
        flow_service.update_flow(flow, is_schedule=False)
        flow_service.run_flow(flow.id, 1)
    else:
        replacement_id = seq_id()
        model.flow.id = replacement_id
        model.flow.type = FlowType.Dataset.value
        model.flow.name = dataset.get('name') + "_" + "数据{}".format(error_word)
        model.flow.nodes = [
            FlowNodeModel(
                name=model.flow.name, type=flow_node_type, content=ReplacementNodeContentModel(**data_content)
            )
        ]
        flow_service.add_flow(model.flow)
        flow_service.run_flow(model.flow.id)
        repository.update_data("dataset", {"replacement_id": replacement_id}, {"id": model.id})

    repository.add_data(
        'dataset_operate_record',
        get_operate_record_model(
            model.id, [OperateMode.Replace.value, OperateMode.Append.value][int(bool(append))], ''
        ).get_dict(),
    )
    return model.id


def get_task_data(task_id: str) -> Dict[str, int]:
    """
    获取异步任务数据
    :param task_id:
    :return:
    """
    if not task_id:
        raise UserError(message='task_id不能为空')
    dataset_base_service = DatasetBaseService({}, {})
    return dataset_base_service.get_task_data(task_id)


def generate_excel_sheet(task_id, content):
    """
    生成excel工作表
    :param task_id:
    :param content:
    :return:
    """
    dataset_excel_service = DatasetExcelService({}, {})
    dataset_excel_service.async_get_sheet(task_id, content)


def generate_excel_dataset(task_id, content):
    """
    生成excel数据集数据
    :param task_id:
    :param content:
    :return:
    """
    dataset_excel_service = DatasetExcelService({}, {})
    dataset_excel_service.async_process_data(task_id, content)


def update_excel_index(task_id, code, dataset):
    """
    生成excel数据集数据
    :param task_id:
    :param dataset:
    :return:
    """
    dataset_excel_service = DatasetExcelService({}, {})
    dataset_excel_service.async_update_excel_index(task_id, code, dataset)


def generate_api_dataset(project_code, flow_id, flow_instance_id):
    """
    生成api数据集数据
    :param project_code:
    :param flow_id:
    :param flow_instance_id:
    :return:
    """
    if not project_code:
        raise UserError(message="项目code不能为空")
    if not flow_id:
        raise UserError(message="流程id不能为空")
    if not flow_instance_id:
        raise UserError(message="流程实例id不能为空")

    dataset_api_service = DatasetAPIService({}, {})
    dataset_api_service.generate(project_code, flow_id, flow_instance_id)


def check_direct_sql(dataset=None, dataset_id=None):
    """
    检测sql数据集直连
    :return:
    """
    # 必填一项
    if not dataset and not dataset_id:
        raise UserError(message="检测sql数据集直连参数错误")
    if not dataset:
        dataset = dataset_repository.get_dataset(dataset_id)
    content = json.loads(dataset.get('content'))
    data_source_model = data_source_service.get_data_source(content.get('data_source_id'))
    if data_source_model.type == DataSourceType.PostgreSQL.value:
        try:
            with postgresql_data_source_repository.get_postgresql_db(data_source_model) as db:
                db.query(content.get('sql'))
            return ''
        except Exception as e:
            return '{}数据源连接失败:{}'.format(data_source_model.name, str(e))
    elif data_source_model.type == DataSourceType.Mysql.value:
        try:
            with mysql_data_source_repository.get_mysql_db(data_source_model) as db:
                db.query(content.get('sql'))
            return ''
        except pymysql.Error as e:
            # 一小时内只保存一条消息
            _message = {
                'source_id': data_source_model.name + datetime.datetime.now().strftime("%Y-%m-%d %H"),
                'source': '数据源',
                'type': '系统消息',
                'title': '{}数据源连接失败'.format(data_source_model.name),
                'url': '/datasource/detail/mysql/{}'.format(data_source_model.id),
            }
            message_service.message_add(MessageModel(**_message))
            return '{}数据源连接失败{}'.format(data_source_model.name, str(e))


def relate_objects_raise(dataset_id):
    # 1.获取关联报告
    dashboards = external_dashboard_service.get_related_dashboard_by_dataset_id(dataset_id)
    if dashboards:
        raise UserError(message="该数据集存在关联报告，不允许删除。")

    # 2.数据集关联数据集
    datasets = relate_dataset(dataset_id)
    if datasets:
        raise UserError(message="该数据集存在关联数据集，不允许删除。")

    # 3.简讯
    mobile_subscribe = relate_mobile_subscribe(dataset_id)
    if mobile_subscribe:
        raise UserError(message="该数据集存在关联简讯，不允许删除。")

    # 4.开发管理
    opendatas = relate_open_data(dataset_id)
    if opendatas:
        raise UserError(message="该数据集存在关联开发数据开发API，不允许删除。")

    # 5.是否被当成同步用户表、组织表
    user_sync = relate_user_sync_datasets(dataset_id)
    if user_sync:
        raise UserError(message="该数据集存在同步用户表，不允许删除。")

    # 6.是否被权限数据集引用
    dataset_permission = dataset_permission_service.relate_dataset_permission_datasets(dataset_id)
    if dataset_permission:
        raise UserError(message="该数据集被权限数据集所引用，不允许删除。")


def relate_objects(dataset_id):
    # 1.获取关联报告
    dashboards = external_dashboard_service.get_related_dashboard_by_dataset_id(dataset_id)
    # 统一处理，把dashboard_name处理成name
    for row in dashboards:
        row['type'] = 'dashboard'
        row['name'] = row.pop('dashboard_name')

    # 2.数据集关联数据集
    datasets = relate_dataset(dataset_id)

    # 3.简讯
    mobile_subscribe = relate_mobile_subscribe(dataset_id)

    # 4.开发管理
    opendatas = relate_open_data(dataset_id)

    # 5.下游调度查询
    depend_datasets = relate_depend_datasets(dataset_id)

    # 6.是否被当成同步用户表、组织表
    user_sync = relate_user_sync_datasets(dataset_id)

    # 7.是否被权限数据集引用
    dataset_permission = dataset_permission_service.relate_dataset_permission_datasets(dataset_id)

    # 8.是否被数据填报引用
    manual_filling = ManualFillingService.get_template_by_dataset_id(dataset_id)

    # 9.是否被复杂报表引用
    report_center = external_dashboard_service.get_related_report_by_dataset_id(dataset_id)

    ppt = ppt_service.get_related_ppt_by_dataset_id(dataset_id)

    # 用extend似乎速度快点
    results = (dashboards + datasets + opendatas + mobile_subscribe + depend_datasets + user_sync + dataset_permission
               + ppt + manual_filling + report_center)
    return results


def relate_depend_datasets(dataset_id):
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    mobile_subscribe = dataset_repository.get_depend_datasets_relate_dataset(dataset_id)
    for row in mobile_subscribe:
        row["type"] = "depend_dataset"
    return mobile_subscribe


def relate_mobile_subscribe(dataset_id):
    from feed.services.msg_get_data_service import clear_title_html
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    mobile_subscribe = dataset_repository.get_mobile_subscribe_relate_dataset(dataset_id)
    # 简讯动态名称显示处理
    for row in mobile_subscribe:
        row["name"] = clear_title_html(row["name"])
        row["type"] = "mobile_subscribe"
    return mobile_subscribe


def relate_open_data(dataset_id):
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    opendata_list = dataset_repository.get_open_data_relate_dataset(dataset_id)
    for row in opendata_list:
        row['type'] = 'open_data'
    return opendata_list


def relate_user_sync_datasets(dataset_id: str) -> list:
    result = []
    if not dataset_id:
        return result

    user_source_list = repository.get_list('user_source', None)
    for user_source in user_source_list:
        if user_source.get('data') and dataset_id in user_source.get("data"):
            user_source['type'] = 'user_sync'
            result.append(user_source)
    return result


def relate_dashboard(dataset_id):
    """
    数据集关联报告
    :param dataset_id:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    return external_dashboard_service.get_related_dashboard_by_dataset_id(dataset_id)


def relate_feed(dataset_id):
    """
    简讯订阅关联
    :param dataset_id:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    results = dataset_repository.get_feed_relate_dataset_id(dataset_id)
    return results


def relate_dataset(dataset_id):
    """
    数据集关联数据集
    :param dataset_id:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    dataset_list = dataset_repository.get_dataset_relate_dataset(dataset_id)
    if dataset_list:
        dataset_dict = {}
        query_level_code_list = set()
        for dataset_data in dataset_list:
            dataset_dict[dataset_data.get("id")] = dataset_data
            query_level_code_list.add(dataset_data.get("level_code")[0:5])
            flow_id = (
                dataset_data.get("replacement_id") if dataset_data.get("replacement_id") else dataset_data.get("id")
            )
            dataset_data['last_time'] = flow_service.get_flow_last_end_time(flow_id)

        level_code_dataset_dict = {}
        level_code_datasets = dataset_repository.get_dataset_by_level_code(query_level_code_list)
        for data in level_code_datasets:
            level_code_dataset_dict[data.get("level_code")] = data.get("name")

        for dataset_data in dataset_list:
            if not dataset_data.get('level_code'):
                dataset_data['folder'] = '/'
                continue
            folder_paths = []
            dataset_level_code = dataset_data.get('level_code')
            level_codes = dataset_level_code[0: len(dataset_level_code) - 5].split('-')
            prev_level_code = ''
            for level_code in level_codes:
                prev_level_code = prev_level_code + level_code + '-'
                if level_code and level_code_dataset_dict.get(prev_level_code):
                    folder_paths.append(level_code_dataset_dict.get(prev_level_code))
            dataset_data['folder'] = '/'.join(folder_paths)
            dataset_data['type'] = 'union_dataset'
        return dataset_list

    else:
        return []


def get_operate_record_list(query_model):
    """
    获取数据集操作记录
    :param dataset.models.OperateRecordQueryModel query_model:
    :return tuple:
    """
    return dataset_operate_repository.get_operate_list(query_model)


def get_flow_status(dataset_id, is_cache_flow=0):
    """
    获取数据集流程中最后一次状态
    :return:
    """
    if is_cache_flow and int(is_cache_flow):
        cache_flow_id = repository.get_data_scalar('dataset', {'id': dataset_id}, 'cache_flow_id')
        if not cache_flow_id:
            raise UserError(message='缓存流程不存在')
        return flow_repository.get_flow_status(cache_flow_id)

    return flow_repository.get_flow_status(dataset_id)


def get_content_by_dataset_id(dataset_id):
    # 获取数据集的数据源信息
    dataset = repository.get_data("dataset", {"id": dataset_id}, fields=["content"])
    if not dataset:
        raise UserError(message=u'数据集不存在')
    try:
        content = json.loads(dataset.get("content"))
    except JSONDecodeError:
        raise UserError(message=u'解析数据集content错误')
    return content


def _run_dataset_cache_flow(dataset_id: str, run_all_cache_flow: int):
    if run_all_cache_flow and int(run_all_cache_flow):
        dataset_content = repository.get_data_scalar('dataset', {'id': dataset_id}, 'content')
        try:
            content = json.loads(dataset_content)
            data_source_id = content.get('data_source_id')
        except JSONDecodeError:
            data_source_id = None
        if data_source_id:
            datasets = dataset_repository.get_same_data_source_and_use_cache_dataset(data_source_id)
            for dataset in datasets:
                cache_flow_id = dataset.get('cache_flow_id')
                if cache_flow_id:
                    flow_service.run_flow(cache_flow_id)
            return
    cache_flow_id = repository.get_data_scalar('dataset', {'id': dataset_id}, 'cache_flow_id')
    if not cache_flow_id:
        raise UserError(message='缓存流程不存在')
    return flow_service.run_flow(cache_flow_id)


def _run_dataset_sync_flow(dataset_id: str):
    dataset_data = dataset_repository.get_dataset(dataset_id)
    if not dataset_data:
        raise UserError(message="数据集不存在")
    flow_data = repository.get_data("flow", {"id": dataset_id}, fields=["state_trigger"])
    if not flow_data:
        raise UserError(message="数据集对应的流程不存在")

    queue_name = None
    message_extra_data = {}
    if dataset_data.get("type") == DatasetType.Sql.value and flow_data.get("state_trigger", "") in [1, "1"]:
        check_clean_status(dataset_data=dataset_data)
        queue_name = config.get('RabbitMQ.queue_name_work_flow', 'work_flow')
    if dataset_data.get('type') == DatasetType.Folder.value and dataset_data.get('content'):
        subject_id = json.loads(dataset_data.get('content')).get('subject_id')
        if subject_id:
            subject_inspection_record = get_subject_inspection(subject_id)
            if (
                    subject_inspection_record
                    and subject_inspection_record.get('status') == SubjectDatasetInspectionStatus.Inspecting.value
            ):
                raise UserError(code=400, message="版本正在生成中，需要等待当前版本执行完成！")
            return dataset_subject_service.run_subject_flow(dataset_id, subject_id, new_version=1)

    queue_name = queue_name or config.get('RabbitMQ.queue_name_flow_priority')

    return flow_service.run_flow(
        dataset_id, queue_name=queue_name, check_direct=True, need_log=True, extra_data=message_extra_data
    )


def run_dataset(dataset_id: str, is_cache_flow: int = 0, run_all_cache_flow: int = 0):
    """
    运行数据集
    :return:
    """
    if is_cache_flow and int(is_cache_flow):
        return _run_dataset_cache_flow(dataset_id, run_all_cache_flow)
    return _run_dataset_sync_flow(dataset_id)


def check_clean_status_by_dataset_id(dataset_id: str):
    if not dataset_id:
        raise UserError(message='缺少dataset_id参数')
    dataset_data = dataset_repository.get_dataset(dataset_id)
    if not dataset_data:
        raise UserError(message='数据集不存在')

    return check_clean_status(dataset_data)


def check_clean_status(dataset_data: dict):
    table_names = []
    content = dataset_data.get('content')
    edit_mode = dataset_data.get('edit_mode')
    if isinstance(content, str):
        content = json.loads(content)
    try:
        data_source_id = content.get("data_source_id")
        if edit_mode != DatasetEditMode.Relation.value:
            dataset_sql = content.get("sql")
            if dataset_sql:
                table_names = sql_helper.extract_tables(dataset_sql)
        else:
            table_names = get_edit_mode_table_names(dataset_data)

    except JSONDecodeError:
        raise UserError(message=u'解析数据集content错误')

    data_source = repository.get_data(
        "data_source", {"id": data_source_id}, fields=["id", "name", "code", "type", "conn_str", "is_buildin"]
    )

    if not data_source:
        raise UserError(message=u'数据源不存在')

    data_source_model = data_source_service.get_data_source(content.get('data_source_id'))

    # 根据sql获取erp数据
    if not table_names:
        raise UserError(message='没有找到对应表')

    new_table_names = []
    for table_name in table_names:
        new_table_names.append(table_name.lower())

    sql = dataset_repository.get_check_clean_status(new_table_names)

    return request_cloud_api(data_source_model, new_table_names, sql)


def get_edit_mode_table_names(dataset_data: dict):
    dataset_id = dataset_data['id']
    relation_content = dataset_data.get('relation_content')
    if not relation_content:
        relation_content = get_dataset_tables_collection(dataset_id)
    elif isinstance(relation_content, str):
        relation_content = json.loads(relation_content)

    if not relation_content:
        return []

    table_names = []
    for node in relation_content['nodeDataArray']:
        if node.get('name'):
            table_names.append(node.get('name'))
    return list(set(table_names))


def request_cloud_api(data_source_model, table_names, sql):
    conn_str = data_source_model.conn_str
    result_data = CloudAPI(conn_str.host, conn_str.access_id, conn_str.access_secret, conn_str.app_name).get_sql_list(
        sql
    )
    if result_data.get('errmsg'):
        raise UserError(message='调用ERP接口失败，错误内容：' + result_data.get('errmsg'))
    elif not result_data.get("data"):
        raise UserError(message="调用ERP接口失败，接口没有数据。")

    # 存在记录
    if result_data.get("data"):
        # 匹配是否有所有表
        exist_table_names = [
            row.get("TableName").lower() if row.get("TableName") else "" for row in result_data.get("data").get("data")
        ]
        lack_table_names = []
        for table_name in table_names:
            if table_name not in exist_table_names:
                lack_table_names.append(table_name)
        if lack_table_names:
            raise UserError(message=u'dmp_data_clean_status表中缺少{}表记录'.format('、'.join(lack_table_names)))
        return table_names
    raise UserError(message=u'dmp_data_clean_status表中缺少{}表记录'.format('、'.join(table_names)))


def generate_dataset_table_name(dataset_id):
    """
    生成数据集表名
    :param dataset_id:
    :return:
    """
    return DATASET_TABLE_NAME_PREFIX + '_' + hashlib.md5(dataset_id.encode('utf-8')).hexdigest()[8:-8]


def __convert_to_list_param(param: str):
    if isinstance(param, str):
        try:
            return json.loads(param)
        except Exception:
            param = param.strip(' ')
            if param == '':
                return []
            return param.split(',')
    if isinstance(param, list):
        return (','.join(param)).split(',')
    if isinstance(param, tuple):
        return (','.join(param)).split(',')
    raise UserError(message='非法的参数' + str(param) if param else '')


@data_permission_filter('dataset-view')
def get_dataset_tree(
        parent_id: None = '',
        exclude_types: None = None,
        start_time: None = None,
        end_time: None = None,
        with_filterable=False,
        filter_vars=False,
) -> List[Dict[str, Any]]:
    """
    :param end_time:
    :param start_time:
    :param exclude_types:
    :获取数据集树形结构
    :date 2017/6/15
    :param str parent_id :
    :parm filter_vars :
    :return list:
    """
    from dataset.repositories.indicator_repository import INDICATOR_DATASET, INDICATOR_DATASET_2, DETAIL_MODEL_DATASET

    # 指标数据置顶
    repository.update(
        'dataset',
        data={'created_on': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
              'modified_on': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
        conditions={"name": [INDICATOR_DATASET, INDICATOR_DATASET_2, DETAIL_MODEL_DATASET]}
    )

    list_data = dataset_repository.get_dataset_list_by_group_id(
        parent_id,
        exclude_types=__convert_to_list_param(exclude_types) if exclude_types else None,
        start_time=start_time,
        end_time=end_time,
        filter_vars=filter_vars
    )
    dataset_id_subject_id_mapping = get_subject_dataset_data()

    result = []
    tmp_dict = {}
    for dataset in list_data:
        if not isinstance(dataset, dict):
            continue
        try:
            dataset['content'] = json.loads(dataset['content'])
        except:
            dataset['content'] = {}

        dataset['datasource_id'] = dataset['content'].get('data_source_id', None)
        del dataset['content']
        dataset['sub'] = []
        # code格式: 0001-、0001-0001-
        level_code = dataset.get('level_code')
        if level_code:
            level_code_arr = str(level_code).rstrip('-').split('-')
            level_code_arr.pop()
            parent_code = "{}-".format('-'.join(level_code_arr)) if level_code_arr else ''
        else:
            parent_code = ''
        if parent_code not in tmp_dict:
            result.append(dataset)
            tmp_dict[level_code] = dataset
        else:
            tmp_dict.get(parent_code)['sub'].append(dataset)
            tmp_dict[level_code] = dataset

        dataset['subject_id'] = dataset_id_subject_id_mapping.get(dataset.get('id'))

    # 附加是否可作为过滤器数据集字段
    # 不可作为过滤器数据集的条件:
    # 1. 连接类型为直连类型(SQL+API)
    # 2. 关联表超过5个
    if with_filterable:
        limit = int(config.get('DatasetConfig.filterable_table_limit', DATASET_FILTERABLE_TABLE_LIMIT))
        unfilterable_dataset_ids = set()

        # 对于sql编辑模式, 取content中的sql_table_names字段
        sql_datasets = dataset_repository.get_sql_mode_direct_dataset_content(
            connect_type=DatasetConnectType.Directly.value
        )
        for ds in sql_datasets:
            try:
                content = json.loads(ds['content'])
            except JSONDecodeError:
                continue
            table_names = content.get('sql_table_names')
            if (not table_names) or (not isinstance(table_names, list)):
                continue
            cnt = len(table_names)
            if cnt > limit:
                unfilterable_dataset_ids.add(ds['id'])

        # 对于relation编辑模式, 取dataset_table_collection中关联的表的个数
        relation_datasets = dataset_repository.get_relation_mode_table_collection_count(
            connect_type=DatasetConnectType.Directly.value
        )
        for ds in relation_datasets:
            if ds['cnt'] + 1 > limit:
                unfilterable_dataset_ids.add(ds['id'])

        # 递归的对原结果集添加is_filterable字段
        recursive_add_filterable_attr(result, unfilterable_dataset_ids)

    return sort_tree_data(result)


def get_dataset_tree_for_dataset_ids(
        parent_id=None, exclude_types=None, start_time=None, end_time=None,
        with_filterable=False, filter_vars=False, token=None
):
    import jwt
    from dmplib.constants import ADMIN_ROLE_ID
    try:
        data = jwt.decode(token, config.get('JWT.secret'), algorithms="HS256")
        dataset_ids = data.get('dataset_ids') or []
    except Exception as e:
        logger.error(str(e))
        dataset_ids = []
    if dataset_ids:
        setattr(g, 'customize_roles', [ADMIN_ROLE_ID])
    data_tree = get_dataset_tree(parent_id, exclude_types, start_time, end_time, with_filterable, filter_vars)
    if dataset_ids:
        data_tree = filter_dataset_for_tree(data_tree, dataset_ids)
    return data_tree


def recursion_get_dataset_tree_for_dataset_ids(parent_id=None, is_recursion=True, data_tree=None):
    if not data_tree:
        data_tree = []
    datasets = get_dataset_tree_for_dataset_ids(parent_id)
    for dataset in datasets:
        if dataset.get("type") == DatasetType.Folder.value and is_recursion:
            data_tree = recursion_get_dataset_tree_for_dataset_ids(parent_id=dataset.get("id"), data_tree=data_tree)
        elif dataset.get("type") != DatasetType.Folder.value and dataset.get("type") != DatasetType.Excel.value:
            data_tree.append(dataset)
    return data_tree


def filter_dataset_for_tree(data_tree, filter_dataset_ids):
    level_data = []
    for tree in data_tree:
        dataset_id = tree.get('id') or ''
        dataset_type = tree.get('type') or ''
        if dataset_type != 'FOLDER' and dataset_id not in filter_dataset_ids:
            continue
        if dataset_type == 'FOLDER' and tree.get('sub'):
            tree['sub'] = filter_dataset_for_tree(tree.get('sub'), filter_dataset_ids)
        if dataset_type == 'FOLDER' and not tree.get('sub'):
            continue
        level_data.append(tree)
    return level_data


def recursive_add_filterable_attr(nodes, unfilterable_dataset_ids):
    for ds in nodes:
        if ds.get('type') == DatasetType.Folder.value:
            recursive_add_filterable_attr(ds.get('sub', []), unfilterable_dataset_ids)
            continue
        if ds.get('id') in unfilterable_dataset_ids:
            ds['is_filterable'] = False
        else:
            ds['is_filterable'] = True


def get_dataset_tree_adapt_hd(model):
    # tree 适配 higndata组件
    model.validate()

    if model.action_name == 'Dataset/SelectDataset':
        connect_type = '3'
        if str(model.datasetScope) == '1':
            connect_type = '1'
        if str(model.datasetScope) == '2':
            connect_type = '2'

        list_data = dataset_repository.get_dataset_list(connect_type=connect_type)
        return __format_dataset_result_adapt_hd(list_data)

    elif model.action_name == 'Dataset/GetColumns':
        # 查询数据集字段
        if not model.DataSetGUID:
            raise UserError(message='dataset id为空值')

        dataset_fields = dataset_field_service.get_dataset_field(model.DataSetGUID, None, False)  # noqa
        if not dataset_fields:
            dataset_fields = {}
        for k, v in dataset_fields.items():
            dataset_fields[k] = advanced_field_service.del_diagonal(v)
        return __format_columns_result_adapt_hd(dataset_fields)

    raise UserError(message='action_name值错误')


def __format_dataset_result_adapt_hd(dataset_list: dict):
    # highdata: 1-SQL、2-DB、3-Excel、4-AP
    # dmp: SQL EXCEL FOLDER UNION API
    dataset_type = {
        'SQL': '1',
        'Excel': '3',
        'API': '4',
    }
    result = []
    parent_ids = {c['parent_id'] for c in dataset_list}

    for cell in dataset_list:
        data = dict(
            ObjectGUID=cell['id'],
            ObjectName=cell['name'],
            IsDataSet='0' if cell['type'] == 'FOLDER' else '1',
            ParentGUID=cell['parent_id'],
            IsReal='1' if cell['connect_type'] == '直连' else '0',
            DataSetType=dataset_type.get(cell['type'], ''),
            CatalogLevel='',
        )
        # 要剔除出下面文件为空的文件夹
        if not ((cell['type'] == 'FOLDER') and (cell['id'] not in parent_ids)):
            result.append(data)

    return result


def __format_columns_result_adapt_hd(dataset_fields: dict):
    result = [
        dict(
            ColumnGUID=cell['id'],
            DataSetGUID=cell['dataset_id'],
            ColumnName=cell['origin_col_name'],
            DisplayName=cell['alias_name'],
            Sort=cell['rank'],
            DataType=cell['origin_field_type'],
            DataTypeText=cell['data_type'],
        )
        for cell in dataset_fields.get('度量', []) + dataset_fields.get('维度', [])
    ]
    result = sorted(result, key=lambda x: x['Sort'], reverse=False)
    return result


def get_subject_dataset_data() -> Dict[str, str]:
    dataset_subject_data = repository.get_data('dataset_subject', {}, ['dataset_folder_id', 'id'], multi_row=True)
    result = {r.get('dataset_folder_id'): r.get('id') for r in dataset_subject_data or [] if r.get('dataset_folder_id')}
    dataset_subject_table_data = repository.get_data(
        'dataset_subject_table', {}, ['dataset_id', 'dataset_subject_id'], multi_row=True
    )
    result.update(
        {
            r.get('dataset_id'): r.get('dataset_subject_id')
            for r in dataset_subject_table_data or []
            if r.get('dataset_id')
        }
    )
    return result


def _sort_dataset(list_data):
    """
    :对数据集同等级排序,文件夹优先排在前面，然后依次根据创建时间最新优先
    :date 2017/6/17
    :param list list_data 单维度数据集:
    :return list:
    """
    folder_list = []
    not_folder_list = []

    for v in list_data:
        if len(v.get('sub')) > 2:
            v['sub'] = _sort_dataset(v.get('sub'))

        if v.get('type') == DatasetType.Folder.value:
            folder_list.append(v)
        else:
            not_folder_list.append(v)

    new_list = quick_sort(folder_list, 0, len(folder_list) - 1) + quick_sort(
        not_folder_list, 0, len(not_folder_list) - 1
    )

    return new_list


def quick_sort(lists, left, right):
    if left >= right:
        return lists
    key = lists[left]
    low = left
    high = right
    while left < right:
        while left < right and strtotime(lists[right].get('created_on')) <= strtotime(key.get('created_on')):
            right -= 1
        lists[left] = lists[right]
        while left < right and strtotime(lists[left].get('created_on')) >= strtotime(key.get('created_on')):
            left += 1
        lists[right] = lists[left]
    lists[right] = key
    quick_sort(lists, low, left - 1)
    quick_sort(lists, left + 1, high)
    return lists


def get_dataset_by_ids(dataset_ids):
    """
    根据数据集ids获取数据集
    :param dataset_ids:
    :return:
    """
    if not dataset_ids:
        return []

    return repository.get_data_by_sql(
        'select id, name, modified_on, created_on ' 'from dataset where id in %(ids)s order by created_on desc',
        {'ids': dataset_ids},
    )


def get_dataset_with_folder_by_ids(dataset_ids):
    """
    根据数据集ids获取数据集及其所在目录
    :param dataset_ids:
    :return:
    """
    if not dataset_ids:
        return []

    return repository.get_data_by_sql(
        """
            SELECT
                a.id,
                a.name,
                b.id AS folder_id,
                b.name AS folder_name
            FROM
                dataset a
                LEFT JOIN dataset b ON a.parent_id = b.id
            WHERE
                a.id IN %(ids)s
            ORDER BY
                a.created_on DESC
        """,
        {'ids': dataset_ids},
    )


def get_dataset(dataset_id):  # NOSONAR
    """
    :获取数据集详情
    :date 2017/6/17
    :param str dataset_id :
    :return list:
    """
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message="数据集不存在")

    if dataset.get('content'):
        dataset['content'] = json.loads(dataset['content'])
        # 如果有data_source_id，还需查出数据源类型
        if isinstance(dataset.get('content'), dict) and dataset.get('content', {}).get("data_source_id"):
            data_source = (
                    repository.get_data(
                        'data_source', {"id": dataset.get('content', {}).get("data_source_id")}, fields=["type"]
                    )
                    or {}
            )
            dataset['content']['data_source_type'] = data_source.get("type")
        # 如果是主题包数据集
        _op_subject_dataset(dataset)
    # api 数据集还需返回relation_content
    if dataset['edit_mode'] == DatasetEditMode.Relation.value:
        dataset['relation_content'] = get_dataset_tables_collection(dataset_id)
        dataset_filters = repository.get_data('dataset_filter', {"dataset_id": dataset_id}, multi_row=True) or []
        for dataset_filter in dataset_filters:
            try:
                dataset_filter['col_value'] = json.loads(dataset_filter.get('col_value'))
            except Exception:
                dataset_filter['col_value'] = dataset_filter.get('col_value')
        dataset['filter_content'] = dataset_filters

    dataset['flow'] = flow_service.get_flow(dataset_id)
    dataset['cache_flow'] = flow_service.get_flow(dataset.get('cache_flow_id'))

    field_data = dataset_field_repository.get_dataset_field(dataset_id)
    # inspection_rules 修改
    for field in field_data:  # NOSONAR
        if field.get('inspection_rules'):
            try:
                field['inspection_rules'] = json.loads(field.get('inspection_rules'))
            except Exception:
                field['inspection_rules'] = []
        else:
            field['inspection_rules'] = []
        if field.get("relation_fields") and json.loads(field.get("relation_fields")):  # NOSONAR
            name_list = repository.get_list("dataset_field", {"id": json.loads(field.get("relation_fields"))},
                                            ['alias_name']) or []
            name_list = [i.get('alias_name') for i in name_list]
            field['relation_field_name'] = "、".join(name_list)

    # 旧式expression转新式expression_advance
    dataset_include_vars = dataset_var_service.batch_get_dataset_include_vars([dataset_id])
    field_include_vars = _get_field_include_vars(dataset_include_vars)
    for dataset_field in field_data:
        dataset_field["include_vars"] = list()
        if dataset_field.get('type') not in [DatasetFieldType.Customer.value, DatasetFieldType.Calculate.value]:
            continue
        dataset_field["include_vars"] = field_include_vars.get(dataset_field.get("id"))
        if dataset_field['expression_advance']:
            continue
        if not dataset_field.get('expression'):
            continue
        dataset_field['expression_advance'] = advance_field_helper.expression_convertor(dataset_field['expression'])

    dataset['field'] = dataset_field_service.get_field_group(field_data)

    if DatasetFieldGroup.Measure.value not in dataset['field']:
        dataset['field'][DatasetFieldGroup.Measure.value] = []

    if DatasetFieldGroup.Dimension.value not in dataset['field']:
        dataset['field'][DatasetFieldGroup.Dimension.value] = []

    # 数据集变量变量
    dataset_vars = dataset_var_service.get_dataset_var_data(dataset_id)
    dataset['field'][DATASET_VARS_NAME] = dataset_vars

    # 数据集多sql
    try:
        design = repository.get_list('dataset_design', {'dataset_id': dataset_id}) or []
        for i in design:
            i['content'] = json.loads(i['content']) if i.get('content') else {}
        dataset['design'] = design
    except Exception as e:
        logger.error(f"获取dataset_design异常：{str(e)}")

    dataset_field_service.filter_field_permission(dataset, dataset['field'])

    return dataset


def _op_subject_dataset(dataset):
    if (
            isinstance(dataset.get('content'), dict)
            and dataset.get('content', {}).get("subject_id")
            and dataset.get("version")
    ):
        version_time = datetime.datetime.strptime(
            f"{dataset['version'][1:5]}-{dataset['version'][5:7]}-{dataset['version'][7:9]} {dataset['version'][9:11]}:{dataset['version'][11:13]}:{dataset['version'][13:15]}",
            "%Y-%m-%d %H:%M:%S",
        )
        dataset["modified_on"] = version_time


def _get_field_include_vars(dataset_include_vars):
    field_include_vars = defaultdict(list)
    for dataset_include_var in dataset_include_vars:
        field_include_vars[dataset_include_var.get("field_id")].append(dataset_include_var)
    return field_include_vars


def get_dataset_tables_collection(dataset_id, return_web=True):
    view_datas = dict()
    # 获取nodeDatas
    node_datas = repository.get_data(
        'dataset_field',
        {"dataset_id": dataset_id},
        [
            "id",
            "alias_name",
            "note",
            "col_name",
            "origin_col_name",
            "data_type",
            "origin_table_id",
            "origin_table_name",
            "origin_field_type",
            "origin_table_alias_name",
            "origin_table_comment",
            "field_group",
            "external_id",
            "origin_dim_type"
        ],
        multi_row=True,
    )
    new_node_datas = {}
    # 转换成前端传下来的格式
    for dataset_field in node_datas:
        # 视图模式下，origin_table_id一定不能为空
        if not dataset_field.get("origin_table_id"):
            continue
        dataset_field["type"] = dataset_field.get("origin_field_type")
        dataset_field["name"] = dataset_field.get("origin_col_name")
        dataset_field["comment"] = dataset_field.get("note")
        dataset_field["alias_name"] = dataset_field.get("alias_name")

        if dataset_field.get('origin_table_id') not in new_node_datas.keys():
            temp_dict = dict()
            temp_dict["id"] = dataset_field.get('origin_table_id')
            temp_dict["name"] = dataset_field.get('origin_table_name')
            temp_dict["comment"] = dataset_field.get('origin_table_comment')
            temp_dict["alias_name"] = dataset_field.get('origin_table_alias_name')
            temp_dict["fields"] = [dataset_field]
        # 追加fields
        else:
            temp_dict = new_node_datas.get(dataset_field.get('origin_table_id'))
            temp_dict["fields"].append(dataset_field)
        new_node_datas[dataset_field.get('origin_table_id')] = temp_dict
    all_node_data_keys = list(new_node_datas.keys())
    # 组装nodeDatas
    view_datas['nodeDataArray'] = [v for k, v in new_node_datas.items()]
    table_type_dic = {}
    # 获取linkDatas
    link_datas = repository.get_data(
        "dataset_tables_collection",
        {"dataset_id": dataset_id},
        [
            "id",
            "dataset_id",
            "from_table_name",
            "from_id",
            "from_alias_name",
            "to_table_name",
            "to_id",
            "to_alias_name",
            "join_type",
            "join_fields",
            "from_table_type",
            "to_table_type"
        ],
        multi_row=True,
    )
    new_link_datas = []
    if return_web:
        for row in link_datas:
            # 没有to_id的情况下，不需要返回给前端
            if not row.get("to_id") and return_web:
                table_type_dic[row.get("from_id")] = row.get("from_table_type")
                continue
            row["join_fields"] = json.loads(row.get("join_fields", json.dumps([])))
            new_link_datas.append(row)
            if row.get("to_id") and row.get("to_id") not in all_node_data_keys:
                _node_data = {
                    "id": row.get("to_id"),
                    "alias_name": row.get("to_alias_name"),
                    "comment": row.get("to_alias_name"),
                    "fields": [],
                    "name": row.get("to_table_name"),
                    "table_type": row.get("to_table_type"),
                }
                view_datas['nodeDataArray'].append(_node_data)
                all_node_data_keys.append(row.get("to_id"))
            if row.get("from_id") and row.get("from_id") not in all_node_data_keys:
                _node_data = {
                    "id": row.get("from_id"),
                    "alias_name": row.get("from_alias_name"),
                    "comment": row.get("to_alias_name"),
                    "fields": [],
                    "name": row.get("from_table_name"),
                    "table_type": row.get("from_table_type"),
                }
                view_datas['nodeDataArray'].append(_node_data)
                all_node_data_keys.append(row.get("from_id"))
            table_type_dic[row.get("to_id")] = row.get("to_table_type")
            table_type_dic[row.get("from_id")] = row.get("from_table_type")
        view_datas['linkDataArray'] = new_link_datas
        # 给节点添加表类型
        for node in view_datas['nodeDataArray']:
            if not node.get('table_type'):
                node['table_type'] = table_type_dic.get(node.get('id'))
    # 不是返回前端的话，应该要处理成对象的形式
    else:
        new_link_datas = []
        for link_data in link_datas:
            join_fields = [JoinField(**(row)) for row in json.loads(link_data.get("join_fields", json.dumps([])))]
            link_data['join_fields'] = join_fields
            link_data['from_table_name'] = link_data.get("from_table_name")
            link_data['from_alias_name'] = link_data.get("from_table_name")
            link_data['to_table_name'] = link_data.get("to_table_name")
            link_data['to_alias_name'] = link_data.get("to_alias_name")
            link_data['from_table_type'] = link_data.get("from_table_type")
            link_data['to_table_type'] = link_data.get("to_table_type")
            new_link_datas.append(LinkData(**(link_data)))
        # 单表的情况下，nodeDatas有值，linkDatas中没有值，需要写一条linkDatas的记录
        if not new_link_datas and view_datas.get("nodeDataArray"):
            link_data = dict()
            link_data["from_id"] = view_datas.get("nodeDataArray")[0].get("id")
            link_data["from_table_name"] = view_datas.get("nodeDataArray")[0].get("name")
            link_data["from_table_type"] = view_datas.get("nodeDataArray")[0].get("from_table_type")
            new_link_datas.append(LinkData(**(link_data)))
        view_datas["linkDataArray"] = new_link_datas
    return view_datas


def get_dataset_link_datas(dataset_id):
    # 获取linkDatas
    link_datas = (
            repository.get_data(
                "dataset_tables_collection",
                {"dataset_id": dataset_id},
                [
                    "id",
                    "dataset_id",
                    "from_table_name",
                    "from_alias_name",
                    "from_id",
                    "to_table_name",
                    "to_alias_name",
                    "to_id",
                    "join_type",
                    "join_fields",
                ],
                multi_row=True,
            )
            or []
    )
    new_link_datas = []
    for link_data in link_datas:
        join_fields = []
        for join_field in json.loads(link_data.get('join_fields', json.dumps([]))):
            join_fields.append(JoinField(**(join_field)))
            link_data['join_fields'] = join_fields
        new_link_datas.append(LinkData(**(link_data)))
    return new_link_datas


def get_dataset_modify_time(dataset_id):
    """
    :获取数据集更新时间
    :date 2017/6/17
    :param str dataset_id : 数据集id
    :return str: 数据集更新时间
    """
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message="数据集不存在")

    if dataset.get("connect_type") in ("直连", "API"):  # 如果是直连模式，显示特定的字符
        return ""

    return dataset.get('modified_on')


def get_dataset_data_time(dataset_id):
    """
    获取数据集的业务数据更新时间
    :param dataset_id:
    :return:
    """
    if not dataset_id:
        raise ValueError(dataset_id)
    dataset = dataset_repository.get_dataset(dataset_id)
    modified_on = dataset['modified_on']
    dataset_type = dataset['type']
    if dataset_type in ('SQL', 'EXCEL'):
        # 从flow调度获取
        modified_on = ''
        flow = repository.get_data('flow', {'id': dataset_id}, ['modified_on'])
        if flow:
            modified_on = flow['modified_on']
    return modified_on


def get_dataset_result_count(dataset_id, data_source_id=""):
    """
    :获取数据集总数（仅支持api数据集 + 数芯1.5）
    :date 2017/6/17
    :param dataset_id:
    :return list:
    """
    dataset = dataset_repository.get_dataset(dataset_id)
    data_source =  data_source_service.get_data_source(data_source_id)
    is_shuxin_15 = bool(data_source and data_source.type == DataSourceType.MysoftShuXin15.value)
    if not dataset:
        raise UserError(message='找不到数据集')
    # api数据集不同步dmp模式，直接调用接口获取数据
    if dataset.get('type') == DatasetType.Api.value or is_shuxin_15:
        dataset_model = DatasetModel(**dataset)
        dataset_model.var_content = dataset_var_service.get_dataset_vars(dataset_id)
        # 视图模式下，需要组装release_content字段
        if dataset_model.edit_mode and dataset_model.edit_mode == DatasetEditMode.Relation.value:
            dataset_model.relation_content = get_dataset_tables_collection(dataset_id)
            dataset_model.filter_content = (
                    repository.get_data('dataset_filter', {"dataset_id": dataset_id}, multi_row=True) or []
            )
        result = dict()
        result["id"] = dataset_id
        content = json.loads(dataset_model.content)
        data_source_model = data_source_service.get_data_source(content.get('data_source_id'))
        # 这里查出的是总权限，没有过滤行列权限
        if is_shuxin_15:
            data_model = Pulsar15DatasetService(dataset_model, data_source_model).run_get_data_or_struct()
            total_count = data_model.total_count
        else:
            total_count = DatasetAPIService(dataset_model, data_source_model).run_get_data_count()

        # 查询过滤权限的数据
        query_structure = QueryStructure()
        select = Select()
        select.alias = 'total_count'
        select.func = 'count'
        prop = Prop()
        prop.value = 1
        select.props.append(prop)
        query_structure.select.append(select)
        query_structure_json = json.dumps(query_structure, cls=ModelEncoder)
        is_api_schedule = False
        if hasattr(g, "is_api_schedule"):
            is_api_schedule = g.is_api_schedule
        from dataset.services.dataset_async_service import run_query_data
        filter_amount_result = run_query_data(dataset_model.id, query_structure_json, is_api_schedule=is_api_schedule)
        filter_amount_count = filter_amount_result.data[0].get("total_count") if filter_amount_result.data else 0

        # field使用数据库中的
        fields = dataset_field_repository.get_dataset_field(dataset.get('id'), {'type': DatasetFieldType.Normal.value})
        result['head'] = fields
        result['count'] = total_count
        result['filter_amount'] = filter_amount_count
        return result


def get_union_replace_sql(dataset_id: str) -> str:
    dataset = dataset_repository.get_dataset(dataset_id)
    set_info_dataset(dataset)
    if not dataset:
        raise UserError(message='找不到数据集')
    if dataset.get('type') != DatasetType.Union.value:
        raise UserError(message='该数据集不是组合数据集')
    # 视图模式需要获取filter_content, relation_content
    if dataset.get("edit_mode") == DatasetEditMode.Relation.value:
        dataset['relation_content'] = get_dataset_tables_collection(dataset_id)
        dataset_filters = repository.get_data('dataset_filter', {"dataset_id": dataset_id}, multi_row=True) or []
        for dataset_filter in dataset_filters:
            try:
                dataset_filter['col_value'] = json.loads(dataset_filter.get('col_value'))
            except Exception:
                dataset_filter['col_value'] = dataset_filter.get('col_value')
        dataset['filter_content'] = dataset_filters
    dataset_model = DatasetModel(**dataset)
    union_service = DatasetUnionService(dataset_model, {})
    dataset_content = union_service.load_dataset_content(dataset.get("content"))
    running_way = dataset_content.get('running_way')
    if running_way == 'local' or (running_way is None and is_local_storage(g.code)):
        from components.data_center_api import get_new_erp_datasource_model
        data_source = get_new_erp_datasource_model(dataset_model.content)
        db_engine = data_source.db_type
    else:
        db_engine = "rds"
    union_service.db_engine = db_engine
    union_service.init_sql(dataset_content)
    return union_service.sql


def get_api_dataset_data(dataset, fields, is_api_schedule=False, offset=0, limit=100):
    # api分为两种，视图模式和sql模式
    from components.query_models import QueryStructure, Select, Limit

    query_structure = QueryStructure()
    # 分页清洗数据
    if is_api_schedule:
        query_structure.limit = Limit(row=limit, offset=offset)
    else:
        query_structure.limit = Limit(row=100)

    edit_mode = dataset['edit_mode'] == DatasetEditMode.Relation.value
    # 视图模式
    for dataset_field in fields:
        select_field = Select()
        select_field.alias = dataset_field.get("col_name")
        select_field.prop_name = dataset_field.get("origin_col_name")
        if edit_mode:
            select_field.obj_name = dataset_field.get("origin_table_alias_name") or dataset_field.get(
                "origin_table_name"
            )
        query_structure.select.append(select_field)

    # 调度模式取数添加排序字段
    if is_api_schedule and query_structure.select:
        first_select = query_structure.select[0]
        order_by = Order()
        order_by.prop_name = first_select.prop_name
        order_by.obj_name = first_select.obj_name
        order_by.method = 'asc'
        query_structure.order_by.append(order_by)

    query_structure_json = json.dumps(query_structure, cls=ModelEncoder)
    from dataset.services.dataset_async_service import run_query_data

    result = run_query_data(dataset["id"], query_structure_json, is_api_schedule=is_api_schedule)
    return result.data


@handle_g
def get_api_dataset_result_data(params: Dict[str, Any]):
    """
    :获取api数据集/数芯1.5调度结果
    :date 2021/3/16
    :param :
    :return list:
    """
    # 获取api数据集
    start = time.time()
    setattr(g, 'code', params.get("code"))
    setattr(g, 'userid', "")
    setattr(g, "cookie", None)
    setattr(g, "group_ids", [])
    setattr(g, "group_id", "")
    setattr(g, "is_api_schedule", True)
    dataset_id = params.get("dataset_id")
    data_source_id = params.get("data_source_id") or ""
    columns = params.get("columns")
    if not isinstance(columns, list):
        columns = [columns]
    tmp_table_name = params.get("tmp_table_name")
    flow_instance_id = params.get("flow_instance_id")
    dataset_name = ''
    try:
        dataset = dataset_repository.get_dataset(dataset_id)
        if not dataset:
            raise UserError(message='找不到数据集')
        dataset_name = dataset.get('name', '')
        logger.error(f"api数据集调度，数据集id: {dataset_id}, 数据集名称: {dataset_name}")
        dataset['var_content'] = dataset_var_service.get_dataset_vars(dataset_id)
        fields = dataset_field_repository.get_dataset_field(dataset.get('id'), {'type': DatasetFieldType.Normal.value})
        if dataset['edit_mode'] == DatasetEditMode.Relation.value:
            dataset['relation_content'] = get_dataset_tables_collection(dataset_id)
            dataset['filter_content'] = (
                    repository.get_data('dataset_filter', {"dataset_id": dataset_id}, multi_row=True) or []
            )
        # 获取数据集总数
        total = get_dataset_result_count(dataset_id, data_source_id=data_source_id).get("count", 0)
        msg = "接受到api数据，开始采集数据。"
        logger.error(msg)
        page_size = int(config.get("DatasetConfig.pagesize_retrieve_api_dataset", 2500))
        timeout = int(config.get("DatasetConfig.retrieve_api_dataset_timeout", 550))
        if total > 0:
            # 分页清洗数据
            page, limit = 0, page_size
            total_page = int(total / limit) + 1
            while total_page:
                # offset = page*limit if page == 0 else page*limit+1
                result = get_api_dataset_data(dataset, fields, is_api_schedule=True, offset=page * limit, limit=limit)
                # 将api数据集同步到云端
                APIDatasetRepository(dataset, columns=columns, tmp_table_name=tmp_table_name).real_time_data(
                    result=result)
                total_page -= 1
                page += 1
                if (time.time() - start) >= timeout:
                    min = round(timeout / 60)
                    raise RuntimeError(f"api数据集采集超{min}min")
        flow_status = 1
        logger.error("结束采集数据。")
    except Exception as e:
        logger.error(f"get api dataset error: {e}, {traceback.format_exc()}")
        record_api_dataset_sync_log(dataset_id, dataset_name, tmp_table_name, str(e))
        flow_status = 2
    # 通知dmp_flow
    mq = RabbitMQ()
    queue_name = f"queue_name_api_dataset_{flow_instance_id}"
    logger.info(f"queue_name: {queue_name}")
    body = {'project_code': g.code, 'flow_status': flow_status}
    res = mq.send_message(queue_name, json.dumps(body), durable=False, auto_delete=True)
    logger.info("publish msg res: %s" % res)


def get_dataset_result_data(dataset_id):
    """
    :获取数据集结果
    :date 2017/6/17
    :param dataset_id:
    :return list:
    """
    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message='找不到数据集')

    fields = dataset_field_repository.get_dataset_field(dataset.get('id'), {'type': DatasetFieldType.Normal.value})
    if dataset['edit_mode'] == DatasetEditMode.Relation.value:
        dataset['relation_content'] = get_dataset_tables_collection(dataset_id)
        dataset['filter_content'] = (
                repository.get_data('dataset_filter', {"dataset_id": dataset_id}, multi_row=True) or []
        )

    # api数据集不同步dmp模式，直接调用接口获取数据
    if dataset.get('connect_type') == DatasetConnectType.Directly.value or dataset.get(
            'type') == DatasetType.Indicator.value:
        if dataset.get('type') == DatasetType.Api.value:
            dataset['var_content'] = dataset_var_service.get_dataset_vars(dataset_id)
            result = dict()
            result["data"] = get_api_dataset_data(dataset, fields)
            # field使用数据库中的
            result['head'] = fields
            # api数据集的count从get_result_count中获取
            result['count'] = 0
            result['filter_amount'] = 0
            result['id'] = dataset_id
            return result
        # 直连模式
        else:
            dataset['var_content'] = dataset_var_service.get_dataset_vars(dataset_id)
            dataset_model = DatasetModel(**dataset)
            result = run_get_data(dataset_model)
            # # 直连模式需要替换数据
            # data_result = data_origin_col_name_transition(result.get("data"), fields, col_name='col_name')
            result['data'] = result.get("data")
            result['head'] = fields
            result['count'] = result.get("count")
            result['filter_amount'] = result.get("count")
            result['id'] = dataset_id
            return result

    else:
        set_info_dataset(dataset)
        if not dataset['table_name']:
            raise UserError(message='数据集未找到数据表')

        data_table_name = dataset['table_name']
        if dataset['type'] == DatasetType.Label.value:
            data_table_name = data_table_name + "_" + LABEL_DETAIL_TABLE_NAME_SUFFIX

        repository.check_data_db_table_is_exist(data_table_name, dataset)

        query_fields = [r.get('col_name') for r in filter(lambda r: r.get('visible'), fields)]
        result_data = {}
        if is_local_storage(g.code):
            # 本地存储模式
            data_result, filter_amount, count = get_data_result_of_local(
                dataset, dataset_id, query_fields, fields, data_table_name
            )
        else:
            data_result, filter_amount = dataset_rbac_result_service.get_dataset_table_data(
                dataset_id, fields, data_table_name, query_fields
            )
            count = dataset_repository.get_dataset_amount(dataset.get('table_name'))

        result_data['data'] = data_result
        result_data['id'] = dataset_id
        result_data['head'] = fields
        result_data['count'] = count or 0
        result_data['filter_amount'] = filter_amount
        flow_id = dataset.get("replacement_id") or dataset_id
        result_data['last_time'] = flow_service.get_flow_last_end_time(flow_id)
        result_data['connect_type'] = dataset.get('connect_type')
        return result_data


def set_info_dataset(model):
    """
    设置dataset_type  datasource_type
    """
    if isinstance(model, dict):
        model = DatasetModel(**model)
    datasource_type = ""
    running_way = ''
    if model.type in [DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Union.value]:
        try:
            content = json.loads(model.content)
        except JSONDecodeError:
            msg = "{}数据集content解析错误：{}".format(model.name, model.content)
            raise UserError(message=msg)
        running_way = content.get('running_way')
        if content.get("data_source_id"):
            data_source_model = data_source_service.get_data_source(content['data_source_id'])
            datasource_type = data_source_model.type

    setattr(g, "dataset_type", model.type)
    setattr(g, "datasource_type", datasource_type)
    setattr(g, "running_way", running_way)


def get_data_result_of_local(dataset, dataset_id, query_fields, fields, data_table_name):
    """
    从数据服务中心查询数据
    :param dataset: 数据集字典
    :param dataset_id: 数据集id
    :param query_fields: 需要查询的字段
    :param fields: 数据集全部字段
    :param data_table_name: 拍照数据集表名
    :return: data_result, filter_amount, count
    """
    dataset_model = DatasetModel(**dataset)
    # 获取where obj
    where_obj, permit_fields = dataset_rbac_result_service.get_dataset_filter_where_obj(dataset_id)
    # 生成mssql
    sql = DatasetBaseService(dataset_model, {}).generate_mssql_sql(query_fields, fields, where_obj)
    # get data from data center
    data_result, filter_amount = dataset_rbac_result_service.get_dataset_result_data_by_data_center(
        sql, fields, permit_fields, dataset_id, dataset_content=dataset_model.content)
    count = dataset_rbac_result_service.get_dataset_amount_of_data_center(data_table_name,
                                                                          content=dataset_model.content,
                                                                          dataset_id=dataset_id)
    return data_result, filter_amount, count


def get_sql_dataset_service(model, data_source_model):
    """
    获取sql类数据集业务类
    :param str data_source_type:
    :return:
    """
    if data_source_model.type == DataSourceType.Mysql.value:
        dataset_service = DatasetMysqlService(model, data_source_model)
    elif data_source_model.type == DataSourceType.MysoftERP.value:
        dataset_service = ErpDatasetFactory(model, data_source_model).create_dataset()
    elif data_source_model.type == DataSourceType.DataHub.value:
        # 区分是 SQL_Server 还是 Oracle , 默认mysql
        if data_source_model.conn_str.data_base_type == DataBaseType.SQL_Server.value:
            dataset_service = DatasetDataHubSqlServerService(model, data_source_model)
        elif data_source_model.conn_str.data_base_type == DataBaseType.Oracle.value:
            dataset_service = DatasetDataHubOracleService(model, data_source_model)
        else:
            dataset_service = DatasetDataHubService(model, data_source_model)
    elif data_source_model.type == DataSourceType.PostgreSQL.value:
        dataset_service = DatasetPostgreSQLService(model, data_source_model)
    elif data_source_model.type == DataSourceType.HighData.value:
        dataset_service = HighDataDatasetService(model, data_source_model)
    elif data_source_model.type == DataSourceType.ADS.value:
        dataset_service = DatasetADSService(model, data_source_model)
    elif data_source_model.type == DataSourceType.MysoftNewERP.value:
        dataset_service = DatasetMysoftNewErp(model, data_source_model)
    elif data_source_model.type == DataSourceType.Presto.value:
        dataset_service = DatasetPrestoService(model, data_source_model)
    elif data_source_model.type == DataSourceType.MSSQL.value:
        dataset_service = DatasetMssqlService(model, data_source_model)
    elif data_source_model.type == DataSourceType.MysoftShuXin15.value:
        dataset_service = Pulsar15DatasetService(model, data_source_model)
    else:
        raise Exception('该SQL数据集类型未实现：' + data_source_model.type)

    return dataset_service


def get_dataset_service(model, data_source_model=None):
    """
    获取数据集业务类
    :param model:
    :param data_source_model:
    :return:
    """
    if model.type == DatasetType.Sql.value:
        dataset_service = get_sql_dataset_service(model, data_source_model)
    elif model.type == DatasetType.Label.value:
        dataset_service = DatasetLabelService(model, data_source_model)
    elif model.type == DatasetType.Union.value:
        dataset_service = DatasetUnionService(model, data_source_model)
    elif model.type == DatasetType.Api.value:
        dataset_service = DatasetAPIService(model, data_source_model)
    elif model.type == DatasetType.Indicator.value:
        dataset_service = PulsarDatasetService(model, data_source_model)
    else:
        raise Exception('数据集类型未实现')
    return dataset_service


def run_get_data(model):
    """
    运行数据集，产生数据集数据和数据集字段
    :param dataset.models.DatasetModel model:
    :return:
    """
    # if model.type == DatasetType.Sql.value or model.type == DatasetType.Api.value:
    add_api_dataset_params(g, sql_from='testsql', dataset_id=model.id)
    if model.type in [DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Indicator.value]:
        try:
            content = json.loads(model.content)
        except JSONDecodeError:
            msg = "{}数据集content解析错误：{}".format(model.name, model.content)
            raise UserError(message=msg)
        if not content.get("data_source_id"):
            raise UserError(message='数据源错误，请检查')
        data_source_model = data_source_service.get_data_source(content['data_source_id'])
        if not data_source_model:
            raise UserError(message='数据源未找到。')
        g.datasource_type = data_source_model.type
        return get_dataset_service(model, data_source_model).run_get_data()
    else:
        g.dataset_type = model.type
        return get_dataset_service(model, {}).run_get_data()


def run_get_data_count(model):
    """
    运行数据集，获取总条数
    :param model:
    :return:
    """
    add_api_dataset_params(g, sql_from='testsql', dataset_id=model.id)
    if model.type == DatasetType.Sql.value or model.type == DatasetType.Api.value:
        content = json.loads(model.content)
        data_source_model = data_source_service.get_data_source(content.get('data_source_id'))
        return get_dataset_service(model, data_source_model).run_get_data_count()
    else:
        return get_dataset_service(model, {}).run_get_data_count()


def test_directly_sql(model):
    """
    :测试sql直连
    :date 2022/8/15
    :param dataset.models.DatasetModel model:
    :return:
    """
    # api数据集不同步dmp模式，直接调用接口获取数据
    if model.type == DatasetType.Sql.value and model.connect_type == DatasetConnectType.Directly.value:
        try:
            content = json.loads(model.content)
        except JSONDecodeError:
            msg = "{}数据集content解析错误：{}".format(model.name, model.content)
            raise UserError(message=msg)
        if not content.get("data_source_id"):
            raise UserError(message='数据源错误，请检查')
        data_source_model = data_source_service.get_data_source(content['data_source_id'])
        if not data_source_model:
            raise UserError(message='数据源未找到。')
        dataset_service_impl = get_dataset_service(model, data_source_model)
        dataset_service_impl.value_data_limit = 0
        field_row = dataset_field_repository.get_one_origin_field_name(model.id)
        field_name = field_row.get('origin_col_name')
        if field_row.get('origin_table_name'):
            field_name = field_row.get('col_name')
        dataset_service_impl.run_get_field_values(field_name)


@data_permission_edit_filter('dataset-edit')
def run_get_field_values(model, field_name, origin_field_name):
    """
    运行数据集，获取字段的所有值
    :param model:
    :param field_name:
    :param origin_field_name:
    :return:
    """
    # 优先使用origin_field_name
    use_field_name = origin_field_name or field_name
    if model.edit_mode == DatasetEditMode.Relation.value:
        use_field_name = field_name
    if model.type == DatasetType.Sql.value:
        content = json.loads(model.content)
        data_source_model = data_source_service.get_data_source(content.get('data_source_id'))
        dataset_service = get_sql_dataset_service(model, data_source_model)
        result = dataset_service.run_get_field_values(use_field_name)
    elif model.type == DatasetType.Union.value:
        dataset_service = DatasetUnionService(model, {})
        result = dataset_service.run_get_field_values(use_field_name)
    else:
        raise UserError(message="只支持SQL、UNION数据集。")
    # 前端只认origin_field_name，视图模式取数的时候使用的是col_name，所以这里需要转换下
    if model.edit_mode == DatasetEditMode.Relation.value:
        return [{origin_field_name or field_name: row.get(use_field_name)} for row in result]
    return result


@data_permission_edit_filter('dataset-edit')
def get_field_values(dataset_id, dataset_field_id, keyword=''):
    """
    获取数据集字段group后的值
    :param dataset_id:
    :param dataset_field_id:
    :param keyword:
    :return:
    """
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")
    if not dataset_field_id:
        raise UserError(message="数据集字段ID不能为空")

    dataset = dataset_repository.get_dataset(dataset_id)
    if not dataset:
        raise UserError(message='找不到数据集')

    dataset_field = repository.get_data("dataset_field", {"id": dataset_field_id})
    if not dataset:
        raise UserError(message='找不到数据集字段')

    # api数据集不同步dmp模式，直接调用接口获取数据
    # api数据集使用field_info
    if dataset.get('type') == DatasetType.Api.value:
        return dataset_field_service.get_dataset_field_values(dataset_id, dataset_field_id)
    if dataset.get('connect_type') != DatasetConnectType.Directly.value:
        if keyword:
            condition = " where {col_name} like '%{keyword}%'".format(
                col_name=dataset_field.get("col_name"), keyword=keyword
            )
        else:
            condition = ""
        return dataset_field_repository.get_dataset_field_values(
            dataset.get("table_name"), dataset_field.get("col_name"), condition=condition
        )
        # return dataset_field_repository.get_dataset_field_value_by_storage_type(
        #     dataset.get("table_name"), dataset_field.get("col_name"), keyword
        # )
    else:
        raise UserError(message="目前不支持直连数据集。")


def get_label_data(label_id):
    """
    获取标签数据集初始化数据
    :param label_id:
    :return:
    """
    dataset = DatasetModel()
    dataset.content = '{"label_id": "' + label_id + '"}'
    dataset.type = DatasetType.Label.value
    return run_get_data(dataset)


def get_dataset_type(dataset_id):
    """
    获取数据集的类型
    :param dataset_id:
    :return:
    """
    return dataset_repository.get_dataset_type(dataset_id)


def get_dataset_connect_type(dataset_id):
    """
    获取数据集的连接类型
    :param dataset_id:
    :return:
    """
    return dataset_repository.get_dataset_connect_type(dataset_id)


def get_dataset_data(dataset_id):
    """
    获取数据集
    :param dataset_id:
    :return:
    """
    return dataset_repository.get_dataset(dataset_id)


def dataset_table_exists(table_name):
    """
    数据集表是否存在
    :param table_name:
    :return:
    """
    return dataset_repository.dataset_table_exists(table_name)


def get_inspection_list(inspection_id: str):
    """
    如果查看的是主题包，则返回list
    :param inspection_id:
    :return:
    """
    dataset_folder = dataset_inspection_repository.dataset_folder_by_inspection_id(inspection_id)
    if not dataset_folder:
        return [get_inspection_result(inspection_id)]
    inspection_ids = dataset_inspection_repository.get_inspection_ids(inspection_id) or []
    inspection_results = []
    for inspection_id in inspection_ids:
        inspection_results.append(get_inspection_result(inspection_id))
    return inspection_results


def get_inspection_result(inspection_id):
    """
    获取数据集巡检结果
    :param inspection_id:
    :return:
    """
    inspection = dataset_inspection_repository.get_inspection(inspection_id) or {}
    table_name = inspection.get('table_name')
    use_dataset_inspection_details = False
    # 新版不查data库，直接从dataset_inspection_details中获取
    if repository.data_is_exists("dataset_inspection_details", {"inspection_id": inspection_id}):
        use_dataset_inspection_details = True
    if not use_dataset_inspection_details and not table_name:
        return {"title": inspection, "data": []}
    if use_dataset_inspection_details:
        _inspection_data = dataset_inspection_repository.get_inspection_details(inspection_id)
        # 简单替换下 inspection_message 到 unusual_count (之前的unusual_count为数值，不太符合现在要求，先临时更改，
        # 后期让前端使用inspection_message替代unusual_count字段)
        for inspection_detail in _inspection_data:
            if not inspection_detail.get("unusual_count") and inspection_detail.get("inspection_message"):
                inspection_detail["unusual_count"] = inspection_detail.get("inspection_message")
    else:
        _inspection_data = dataset_inspection_repository.get_inspection_data(table_name)
    # 去除巡检数为0的记录
    inspection_data = []
    for index, item in enumerate(_inspection_data):
        if item.get('unusual_count') and item.get('unusual_count') not in ['0', u'0']:
            # inspection_rule 需要转成list
            if item.get('inspection_rule'):
                item['inspection_rule'] = [json.loads(item.pop('inspection_rule'))]
            # 字段不统一
            if item.get('inspection_rules'):
                # 默认的巡检是需要json.loads的
                try:
                    item['inspection_rule'] = [json.loads(item.get('inspection_rules'))]
                except Exception:
                    item['inspection_rule'] = [item.get('inspection_rules')]
            inspection_data.append(item)
    data = {"title": inspection, "data": inspection_data}
    return data


def get_version_inspection_result(inspection_id):
    """
    获取数据集版本的巡检结果
    :param dataset_id: 数据集dataset_id
    :param version_id: 数据集版本version_id
    :return:
    """
    inspection = dataset_inspection_repository.get_version_inspection(inspection_id)
    if not inspection:
        return {}

    inspection_id = inspection.get('id')
    inspection_details = dataset_inspection_repository.get_inspection_details(inspection_id)
    # 简单替换下 inspection_message 到 unusual_count (之前的unusual_count为数值，不太符合现在要求，先临时更改，
    # 后期让前端使用inspection_message替代unusual_count字段)
    for inspection_detail in inspection_details:
        if not inspection_detail.get("unusual_count") and inspection_detail.get("inspection_message"):
            inspection_detail["unusual_count"] = inspection_detail.get("inspection_message")
    inspection['inspection_details'] = inspection_details
    return inspection


def get_operate_record_model(dataset_id, operate_mode, data_source=None):
    """
    获取数据集操作记录model
    :return:
    """
    operate_record_model = DatasetOperateRecordModel()
    operate_record_model.id = seq_id()
    operate_record_model.name = data_source
    operate_record_model.dataset_id = dataset_id
    operate_record_model.operating_mode = operate_mode
    return operate_record_model


def union_depend_update():
    """
    组合数据集关联数据升级
    :return:
    """
    union_datasets = repository.get_data(
        "dataset", {"type": DatasetType.Union.value}, fields=["id", "content"], multi_row=True
    )
    for union_dataset in union_datasets:
        if not union_dataset.get("content"):
            continue
        content = json.loads(union_dataset.get("content"))
        sql = content.get("replace_sql")
        table_names = get_table_names(sql)
        for table_name in table_names:
            table_name = table_name.strip().split(" ")[0]
            source_dataset_id = repository.get_data_scalar("dataset", {"table_name": table_name.strip()}, "id")
            if not source_dataset_id:
                continue
            dataset_depend = repository.get_data(
                "dataset_depend", {"source_dataset_id": source_dataset_id, "depend_id": union_dataset.get("id")}
            )
            if not dataset_depend:
                repository.add_data(
                    "dataset_depend", {"source_dataset_id": source_dataset_id, "depend_id": union_dataset.get("id")}
                )


def get_table_names(sql):
    """
    根据sql获取所有表名
    :param sql:
    :return:
    """
    table_names = []
    sql_format = sqlparse.format(sql, encoding='UTF-8', reindent=True, keyword_case='upper')
    subsections = sql_format.split('\n')
    for subsection in subsections:
        if 'FROM' in subsection:
            if sql_format.upper().count('SELECT') > 1:
                table_names.append(subsection.replace("FROM", "").replace(")", ""))
            else:
                table_names.extend(re.findall("FROM(.*)", subsection, flags=re.I))
        if 'JOIN' in subsection:
            table_names.extend(re.findall("JOIN(.*)ON", subsection, flags=re.I))
    return table_names


def validation_sql(sql):
    """
    验证sql语句
    :param sql:
    :return:
    """
    if not sql or not sql.strip():
        raise Exception('sql语句不能为空')
    sql_list = sql.strip().split(';')
    if '' in sql_list:
        sql_list.remove('')
    # 判断sql是否单条还是多条，根据分号
    if len(sql_list) > 1:
        raise Exception('仅支持执行单条sql语句')
    return sql.strip().strip(';')


def refresh_cache_by_dataset_id(dataset_id):
    if dataset_id:
        DatasetFilterCache(dataset_id).refresh()

        refresh_dataset_row_permissions()

        # 删除单图关联数据集缓存数据
        clean_chart_cache(dataset_id)
        # 删除 缓存

        refresh_dataset_row_permission_cache(dataset_id)


def refresh_dataset_row_permission_cache(dataset_id):
    cache = RCache()
    key2 = '%s:%s' % (CHECK_HAS_ROLE_FILTER, dataset_id)
    cache.del_data(key2)
    cached_key = get_cache_dataset_row_permission_keys(dataset_id)
    cached_keys = cache.lrange(cached_key, 0, -1)
    for k in cached_keys:
        cache.del_data(k)
    cache.del_data(cached_key)
    # keys = cache._connection.keys(pattern=key)
    # if keys:
    #     new_keys = [key.decode() for key in keys]
    #     cache._connection.delete(*new_keys)


def get_dataset_row_permission_str(dataset_id):
    if not dataset_has_role_filter(dataset_id):
        return ''
    role_ids = user_service.get_cur_role_id()
    token = hashlib.md5()
    token.update(str(role_ids).encode('utf-8'))
    key = '%s:%s:%s' % (DATASET_ROW_PERMISSION, dataset_id, token.hexdigest())
    # 查找对应的role
    cache = conn_redis()
    data = cache.get_data(key)
    if data is None:
        where_str, permit_fields = get_dataset_filter_where_str(dataset_id, role_ids=role_ids)
        cache.set_data(key, str([where_str, permit_fields]))
        cached_keys = get_cache_dataset_row_permission_keys(dataset_id)
        cache.lpush(cached_keys, key)
        return str([where_str, permit_fields])
    return data


def get_all_dataset_row_permission_key(dataset_id):
    user_id = g.userid if hasattr(g, 'userid') else None
    if not user_id:
        return ['%s:%s' % (DATASET_ROW_PERMISSION, dataset_id)]
    role_ids = user_service.get_cur_role_id()
    if not role_ids:
        return ['%s:%s' % (DATASET_ROW_PERMISSION, dataset_id)]
    return ['%s:%s:%s' % (DATASET_ROW_PERMISSION, dataset_id, role_id) for role_id in role_ids]


def get_cache_dataset_row_permission_keys(dataset_id):
    return DATASET_ROW_PERMISSION_KEYS.format(dataset_id=dataset_id)


def dataset_has_role_filter(dataset_id):
    # 考虑到90%的数据集都没有设置行列权限，这里先设置一层缓存过滤
    key = '%s:%s' % (CHECK_HAS_ROLE_FILTER, dataset_id)
    cache = conn_redis()
    data = cache.get_data(key)
    if data is None:
        data = repository.data_is_exists('dataset_role_filter', {'dataset_id': dataset_id}) or 0
        cache.set_data(key, data)
    return True if data else False


def get_dataset_index(dataset_id):
    index_datas = repository.get_data(
        "dataset_index", {"dataset_id": dataset_id, "system": 0}, ["id", "index_name", "column_list"], multi_row=True
    )
    # 调整结构
    results = {"dataset_id": dataset_id, "index_datas": []}
    if not index_datas:
        return results

    # 获取 dataset_fields 信息，用于前端显示字段
    dataset_fields = (
            repository.get_data("dataset_field", {"dataset_id": dataset_id}, ["col_name", "alias_name"], multi_row=True)
            or []
    )
    dataset_fields_dict = {
        dataset_field.get("col_name"): dataset_field.get("alias_name") for dataset_field in dataset_fields
    }

    for row in index_datas:
        try:
            row["column_list"] = json.loads(row.get("column_list")) if row.get("column_list") else []
        except Exception:
            row["column_list"] = []
        row["alias_list"] = [dataset_fields_dict.get(single) for single in row.get("column_list")]
        results["index_datas"].append(row)
    return results


def delete_dataset_index(index_data_id):
    # 获取数据库已有的index_name
    old_index_data = repository.get_data(
        "dataset_index", {"id": index_data_id}, fields=["id", "index_name"], multi_row=True
    )
    # 删除索引
    affect_row = repository.delete_data("dataset_index", {"id": index_data_id})

    # 获取索引
    index_data = repository.get_data(
        "dataset_index", {"dataset_id": index_data_id}, fields=["id", "index_name"], multi_row=True
    )
    return affect_row


def update_dataset_index(index_data):
    # 获取数据库已有的index_name
    old_index_data = repository.get_data(
        "dataset_index", {"dataset_id": index_data.dataset_id}, fields=["id", "index_name"], multi_row=True
    )
    old_index_dict = {single.get("id"): single.get("index_name") for single in old_index_data}
    # old_index_names = [single.get("index_name") for single in old_index_data]
    # 验证index_datas中是否有重名
    if index_data.index_name in old_index_dict.values() and (
            not index_data.id or old_index_dict.get(index_data.id) != index_data.index_name
    ):
        # 比对id是否一样
        raise UserError(message=u'索引名称不允许重复，请修改！')

    if not index_data.id:
        index_data.id = seq_id()

    # 不落地的数据集，不能设置索引
    dataset = repository.get_data("dataset", {"id": index_data.dataset_id},
                                  ["id", "type", "table_name", "connect_type", "content"])
    if not dataset:
        raise UserError(message=u'数据集不存在！')
    if dataset.get("type") == DatasetType.Api.value:
        raise UserError(message=u'API数据集不支持添加索引！')
    if dataset.get("type") == DatasetType.ExternalSubject.value:
        raise UserError(message=u'外部主题数据集不支持添加索引！')
    if dataset.get("connect_type") == DatasetConnectType.Directly.value:
        raise UserError(message=u'直连数据集不支持添加索引！')
    dataset_content = json.loads(dataset.get("content"))
    validate_index_datas(dataset_content, index_data)
    fields = ['id', 'dataset_id', 'index_name', 'column_list']
    index_data.column_list = json.dumps(index_data.column_list, ensure_ascii=False)
    TABLE_NAME = "dataset_index"
    repository.delete_data(TABLE_NAME, {"id": index_data.id})
    repository.add_model(TABLE_NAME, index_data, fields)

    # excel立即执行添加索引
    if dataset.get("type") == DatasetType.Excel.value:
        from dataset.services.dataset_async_service import async_excel_index
        dataset["delete_index"] = json.dumps(old_index_data)
        dataset["index_data"] = json.dumps([
            {"id": index_data.id, "dataset_id": index_data.dataset_id, "column_list": index_data.column_list,
             "index_name": index_data.index_name}])
        return async_excel_index(dataset)
    return index_data.id


def update_dataset_indexes(index_datas, is_excel=False, dataset_id=None):
    index_data = index_datas[0] if index_datas else None
    if not index_data and is_excel is False:
        raise UserError(message=u'索引参数格式错误,应为数组')
    dataset_id = index_data.dataset_id if not dataset_id else dataset_id
    # 获取数据库已有的index_name
    old_index_datas = repository.get_data(
        "dataset_index", {"dataset_id": dataset_id}, fields=["id", "index_name"], multi_row=True
    )
    old_index_dict = {single.get("id"): single.get("index_name") for single in old_index_datas}
    # 验证index_datas中是否有重名

    for index_data in index_datas:
        if index_data.index_name in old_index_dict.values() and (
                not index_data.id or old_index_dict.get(index_data.id) != index_data.index_name
        ):
            # 比对id是否一样
            raise UserError(message=u'索引名称不允许重复，请修改！')

        if not index_data.id:
            index_data.id = seq_id()

    # 不落地的数据集，不能设置索引
    dataset = repository.get_data("dataset", {"id": dataset_id},
                                  ["id", "type", "table_name", "connect_type", "content"])
    if not dataset:
        raise UserError(message=u'数据集不存在！')
    if dataset.get("type") == DatasetType.Api.value:
        raise UserError(message=u'API数据集不支持添加索引！')
    if dataset.get("type") == DatasetType.ExternalSubject.value:
        raise UserError(message=u'外部主题数据集不支持添加索引！')
    if dataset.get("connect_type") == DatasetConnectType.Directly.value:
        raise UserError(message=u'直连数据集不支持添加索引！')
    dataset_content = json.loads(dataset.get("content"))
    validate_index_datas(dataset_content, index_datas)
    fields = ['id', 'dataset_id', 'index_name', 'column_list']

    # 索引要删除
    if dataset.get("type") == DatasetType.Excel.value:
        for old_index_data in old_index_datas:
            table_name = "dataset_index"
            repository.delete_data(table_name, {"id": old_index_data.get("id")})

    for index_data in index_datas:
        index_data.column_list = json.dumps(index_data.column_list, ensure_ascii=False)
        index_data.dataset_id = dataset_id
        table_name = "dataset_index"
        repository.delete_data(table_name, {"id": index_data.id})
        repository.add_model(table_name, index_data, fields)

    # excel立即执行添加索引
    if dataset.get("type") == DatasetType.Excel.value:
        from dataset.services.dataset_async_service import async_excel_index
        return async_excel_index(dataset)
    return dataset_id


def validate_index_datas(dataset_content, index_datas):
    """
    验证索引正确性
    :param dataset_content:
    :param index_datas:
    :return:
    """
    # 没有索引就不需要检验
    if not index_datas or len(index_datas) == 0:
        return
    create_table_sql = dataset_content.get("create_table_sql")
    tmp_table_name = dataset_content.get("tmp_table_name")
    if not tmp_table_name:
        return True
    # 替换临时表，为了不跟flow创建的表冲突
    new_tmp_table_name = tmp_table_name + str(random.randint(1, 10))
    # 老的数据集，可能没有create_table_sql没有办法做校验
    if not create_table_sql:
        return True
    # 替换create_table_sql中临时表
    create_table_sql = create_table_sql.replace(tmp_table_name, new_tmp_table_name)
    with get_data_db() as db:
        # 检查临时表是否存在，不做处理。
        exist_sql = "SELECT table_name FROM information_schema.TABLES WHERE table_name = %(table_name)s"
        if db.query(exist_sql, {"table_name": new_tmp_table_name}):
            return True

        try:
            # 1.创建临时表
            db.exec_sql(create_table_sql)

            single_indexes = []
            for index_data in index_datas:
                # 拼接索引语
                _cloumn_list = ['`%s`' % row for row in index_data.column_list]
                cloumn_list = ",".join(_cloumn_list)
                single_indexes.append("CREATE INDEX `{index_name}` ON `{table_name}` ({cloumn_list});".format(
                    index_name=index_data.index_name, cloumn_list=cloumn_list, table_name=new_tmp_table_name
                ))
            # 2.执行索引语句
            for single_index in single_indexes:
                db.exec_sql(single_index)
        except Exception as e:
            raise UserError(message=u'建立索引失败，错误原因：%s' % str(e))
        finally:
            # 4.删除临时表
            drop_sql = "drop table if exists {table_name}".format(table_name=new_tmp_table_name)
            db.exec_sql(drop_sql)


def create_dataset_data_index(dataset_fields):
    if not isinstance(dataset_fields, dict):
        raise UserError(message=u'自动创建索引失败，dataset_fields参数有误，请输入dict。当前输入：%s' % dataset_fields)
    # 不落地的数据库不创建索引
    if not dataset_fields:
        return True
    datasets = dataset_repository.get_ground_dataset(list(dataset_fields.keys()))
    datasets = {row.get("id"): row for row in datasets}
    for dataset_id, fields in dataset_fields.items():
        if dataset_id in datasets.keys():
            deal_single_index(dataset_id, fields, datasets)
    return True


def deal_single_index(dataset_id, fields, datasets):
    logger.info("begin deal_single_index")
    field_types = dataset_repository.get_field_type(datasets.get(dataset_id).get("table_name"))
    # 表还未生成
    if not field_types:
        return
    exist_indexs = dataset_repository.get_exist_by_table_name(datasets.get(dataset_id).get("table_name"))
    for field in fields:
        index_name = "system_index_{column}".format(column=field)
        if index_name in exist_indexs:
            continue
        field_type = field_types.get(field)
        # varchar 超过255 需要使用FULLTEXT
        if field_type and isinstance(field_type, str):
            length = re.findall('char\((.*?)\)', field_type)
        else:
            continue
        if (length and int(length[0]) > 255) or field_type.find("text") != -1:
            # 不建全文索引
            continue
        single_index = """ALTER TABLE `{table_name}` ADD {index_type} `{index_name}` ({column});""".format(
            table_name=datasets.get(dataset_id).get("table_name"),
            index_type='INDEX',
            index_name=index_name,
            column=field,
        )
        # 这里默认使用单列索引，需要根据column判断是否存在
        if repository.data_is_exists(
                "dataset_index", {"dataset_id": dataset_id, "system": 1, "index_statement": single_index}
        ):
            continue
        data = {
            "id": seq_id(),
            "dataset_id": dataset_id,
            "index_name": index_name,
            "column_list": json.dumps([field]),
            "system": 1,
            "index_statement": single_index,
        }
        repository.add_data('dataset_index', data)


def get_dataset_name_by_ids(field_ids):
    result = dataset_field_repository.get_dataset_field_name_by_ids(field_ids)
    return [_.get("alias_name") if _.get("alias_name") else _.get("col_name") for _ in result]


def get_dataset_transferred_sql(dataset_model: DatasetModel):
    """
    有变量的数据集，获取转换后的sql语句
    :param dataset_model:
    :return:
    """
    api_service = DatasetAPIService(dataset_model)
    transferred_sql = api_service.get_dataset_content_sql()
    return transferred_sql


def get_datasets_of_api_datasource(api_datasource_id: str) -> list:
    result = []
    datasets = dataset_repository.get_dataset_by_type(DatasetType.Folder.Api)
    for dataset in datasets:
        if not dataset.get("content"):
            continue
        content = json.loads(dataset.get("content"))
        if content.get("data_source_id") != api_datasource_id:
            continue
        result.append(dataset)
    return result


def get_subject_inspection(subject_id):
    fields = ['subject_id', 'subject_name', 'start_time', 'end_time', 'status']
    conditions = {'subject_id': subject_id}
    return repository.get_data("dataset_subject_inspection", conditions, fields)


def copy_dataset(dataset_id):
    from dataset.common.copy_dataset_helper import CopyDatasetHelper
    helper = CopyDatasetHelper(dataset_id)
    if helper.check_is_import_table():
        raise UserError(message="引入表不支持复制")
    return helper.copy_dataset()


def copy_datasets(dataset_ids: list, folder_id: str):
    if not dataset_ids or not folder_id:
        raise UserError(message='数据集或者目录为空')
    # 判断目录是否存在
    folder_info = repository.get_one('dataset', {'type': DatasetType.Folder.value, 'id': folder_id}, ['id', 'type'])
    if not folder_info:
        raise UserError(message='指定的目录不存在')
    from dataset.common.copy_dataset_helper import CopyDatasetHelper
    data = {'dataset_map': {}, 'field_id_map': {}, 'col_name_map': {}, 'var_map': {}}
    new_dataset_ids = []
    try:
        for dataset_id in dataset_ids:
            helper = CopyDatasetHelper(dataset_id, folder_id)
            if helper.check_is_import_table():  # 引入的数据库表不支持进行复制，复制也是用之前的
                continue
            helper.copy_dataset()
            data['dataset_map'].update({dataset_id: helper.new_dataset_id})
            data['field_id_map'].update(helper.fields_map)
            data['col_name_map'].update(helper.col_name_map)
            data['var_map'].update(helper.vars_map)
            new_dataset_ids.append(helper.new_dataset_id)
    except Exception as e:
        from dataset.services.dataset_define_service import delete_dataset
        for dataset_id in new_dataset_ids:
            delete_dataset(dataset_id)
        raise UserError(message=str(e))
    return data


def record_api_dataset_sync_log(dataset_id, dataset_name, error_data_id, error):
    log_dict = {
        'module_type': FastLogger.ModuleType.DATASET,
        'biz_type': FastLogger.BizType.DATASET_SYNC,
        'biz_id': dataset_id,
        'biz_name': dataset_name,
        'error_type': f'API{FastLogger.ErrorType.DATASET_SYNC_ERROR}',
        'error_msg': error,
        'error_traceback': traceback.format_exc(),
        'error_data_id': error_data_id
    }
    FastLogger.BizErrorFastLogger(**log_dict).record()


def get_indicator_model_dataset_tree_for_dataset_ids(parent_id=None, exclude_types=None, start_time=None, end_time=None,
                                                     with_filterable=False, filter_vars=False, token=None):
    import jwt
    from dmplib.constants import ADMIN_ROLE_ID
    dataset_ids = []
    customize_roles = []
    try:
        data = jwt.decode(token, config.get('JWT.secret'), algorithms="HS256")
        dataset_ids = data.get('dataset_ids') or []
        customize_roles = data.get('customize_roles')
    except Exception as e:
        logger.error(str(e))
    if customize_roles:
        setattr(g, 'customize_roles', customize_roles)
    elif dataset_ids:
        setattr(g, 'customize_roles', [ADMIN_ROLE_ID])
    data_tree = get_indicator_model_dataset_tree(parent_id, exclude_types, start_time, end_time, with_filterable,
                                                 filter_vars)
    if dataset_ids:
        data_tree = filter_dataset_for_tree(data_tree, dataset_ids)
    return data_tree


def get_tree_tables(request, **kwargs):
    token = request.cookies.get('token')
    id = kwargs.get('id')

    def to_tree(result, tmp_dict, parent_id, id, table):
        if parent_id not in tmp_dict:
            result.append(table)
            tmp_dict[id] = table
        else:
            parent_dict = tmp_dict.get(parent_id)
            parent_dict['sub'].append(table)
            tmp_dict[id] = table

    # 前端选择“数据集”数据源
    if id and id == DATA_SOURCE_DATASET_GUID:
        return get_indicator_model_dataset_tree_for_dataset_ids(
            kwargs.get('parent_id'),
            kwargs.get('exclude_types'),
            start_time=kwargs.get('begin_time', None),
            end_time=kwargs.get('end_time', None),
            filter_vars=kwargs.get('filter_vars', False),
            token=token
        )
    else:
        cookie = request.cookies
        g.cookie = cookie
        model = TableQueryModel(**kwargs)

        table_list = data_source_service.get_tables(model).get_result_dict()

        if table_list['items']:
            used_table = get_imported_table(model.id)
            tables = None
            if model.data_source.type == DataSourceType.MysoftNewERP.value and model.data_source.app_code == '1401':
                tables = trans_import_tables_mysoftnewerp(table_list.get('items', []), used_table)
            elif model.data_source.type == DataSourceType.MysoftShuXin15.value:
                tables = trans_import_tables_shuxin(table_list.get('items', []), used_table, filter_view=False)
            else:
                return table_list
            result = []
            tmp_dict = {}
            try:
                tables.sort(key=lambda s: (s["level_type"], s["parent_id"]))
                for table in tables:
                    if not isinstance(table, dict):
                        continue
                    table['sub'] = []
                    table['type'] = table['level_type'].upper() if table['level_type'] else None

                    to_tree(result, tmp_dict, table['parent_id'], table['id'], table)
            except Exception as e:
                logger.error(str(e))
            return result
        return table_list


@data_permission_filter('dataset-view')
def get_indicator_model_dataset_tree(parent_id: None = '',
                                     exclude_types: None = None,
                                     start_time: None = None,
                                     end_time: None = None,
                                     with_filterable=False,
                                     filter_vars=False) -> List[Dict[str, Any]]:
    """
    :parm external_type: 扩展类型，dataset(只查数见数据集),model(明细数据集),indicator(指标数据集)
    """

    def to_tree(result, tmp_dict, parent_code, dataset):
        if parent_code not in tmp_dict:
            result.append(dataset)
            tmp_dict[level_code] = dataset
        else:
            parent_dict = tmp_dict.get(parent_code)
            parent_dict['sub'].append(dataset)
            tmp_dict[level_code] = dataset

    indicator_root_id, model_root_id = get_indicator_model_root_id()
    list_data = dataset_repository.get_dataset_list_by_group_id(
        parent_id,
        exclude_types=__convert_to_list_param(exclude_types) if exclude_types else None,
        start_time=start_time,
        end_time=end_time,
        filter_vars=filter_vars
    )
    dataset_id_subject_id_mapping = get_subject_dataset_data()
    external_type_model = ['dim', 'dwd', 'dws', 'subject']
    external_type_indicator = ['pulsar_indicator']
    result = []
    result_model = []
    result_indicator = []
    tmp_dict_model = {}
    tmp_dict_indicator = {}
    tmp_dict = {}
    for dataset in list_data:
        if not isinstance(dataset, dict):
            continue
        try:
            dataset['content'] = json.loads(dataset['content'])
        except:
            dataset['content'] = {}

        dataset['datasource_id'] = dataset['content'].get('data_source_id', None)
        del dataset['content']
        dataset['sub'] = []
        # code格式: 0001-、0001-0001-
        level_code = dataset.get('level_code')
        if level_code:
            level_code_arr = str(level_code).rstrip('-').split('-')
            level_code_arr.pop()
            parent_code = "{}-".format('-'.join(level_code_arr)) if level_code_arr else ''
        else:
            parent_code = ''
        if (dataset['external_type'] and dataset['external_type'] in external_type_model) or dataset[
            'id'] == model_root_id:
            if dataset['id'] == model_root_id:
                dataset['sub'] = result_model
                result.append(dataset)
            else:
                to_tree(result_model, tmp_dict_model, parent_code, dataset)
        elif (dataset['external_type'] and dataset['external_type'] in external_type_indicator and not dataset.get(
                'is_import_table')) or dataset[
            'id'] == indicator_root_id:
            if dataset['id'] == indicator_root_id:
                dataset['sub'] = result_indicator
                result.append(dataset)
            else:
                to_tree(result_indicator, tmp_dict_indicator, parent_code, dataset)
        else:
            to_tree(result, tmp_dict, parent_code, dataset)
        dataset['subject_id'] = dataset_id_subject_id_mapping.get(dataset.get('id'))

    return sort_tree_data(result)


def get_model_dataset_tree(exclude_types: None = None) -> List[Dict[str, Any]]:
    """
    无权限获取数据集列表包含文件夹
    """

    def to_tree(result, tmp_dict, parent_code, dataset):
        if parent_code not in tmp_dict:
            result.append(dataset)
            tmp_dict[level_code] = dataset
        else:
            parent_dict = tmp_dict.get(parent_code)
            parent_dict['sub'].append(dataset)
            tmp_dict[level_code] = dataset

    indicator_root_id, model_root_id = get_indicator_model_root_id()
    list_data = dataset_repository.get_dataset_list_by_group_id(
        exclude_types=__convert_to_list_param(exclude_types) if exclude_types else None,
    )
    dataset_id_subject_id_mapping = get_subject_dataset_data()
    external_type_model = ['dim', 'dwd', 'dws', 'subject']
    external_type_indicator = ['pulsar_indicator']
    result = []
    result_model = []
    result_indicator = []
    tmp_dict_model = {}
    tmp_dict_indicator = {}
    tmp_dict = {}
    for dataset in list_data:
        if not isinstance(dataset, dict):
            continue
        try:
            dataset['content'] = json.loads(dataset['content'])
        except:
            dataset['content'] = {}

        dataset['datasource_id'] = dataset['content'].get('data_source_id', None)
        del dataset['content']
        dataset['sub'] = []
        # code格式: 0001-、0001-0001-
        level_code = dataset.get('level_code')
        if level_code:
            level_code_arr = str(level_code).rstrip('-').split('-')
            level_code_arr.pop()
            parent_code = "{}-".format('-'.join(level_code_arr)) if level_code_arr else ''
        else:
            parent_code = ''
        if (dataset['external_type'] and dataset['external_type'] in external_type_model) or dataset[
            'id'] == model_root_id:
            if dataset['id'] == model_root_id:
                dataset['sub'] = result_model
                result.append(dataset)
            else:
                to_tree(result_model, tmp_dict_model, parent_code, dataset)
        elif (dataset['external_type'] and dataset['external_type'] in external_type_indicator) or dataset[
            'id'] == indicator_root_id:
            if dataset['id'] == indicator_root_id:
                dataset['sub'] = result_indicator
                result.append(dataset)
            else:
                to_tree(result_indicator, tmp_dict_indicator, parent_code, dataset)
        else:
            to_tree(result, tmp_dict, parent_code, dataset)
        dataset['subject_id'] = dataset_id_subject_id_mapping.get(dataset.get('id'))

    return sort_tree_data(result)


def get_datasets_by_parentid(parent_ids: None = ''):
    if parent_ids:
        # 获取数据集level_code
        level_codes = dataset_repository.get_dataset_level_codes(parent_ids)
        if level_codes and len(level_codes) > 0:
            return dataset_repository.get_datasets_by_level_code(level_codes)
    return None


def indicator_search_by_key(key):
    return dataset_repository.indicator_search(key)


@data_permission_edit_filter('dataset-edit')
def check_can_edit(dataset_id):
    return dataset_id


@data_permission_edit_filter('dataset-view')
def check_can_view(dataset_id):
    return dataset_id


def run_flows(is_all=True, is_mysoft_shuxin=True, dataset_id=None, dataset_ids=None):
    datasets = []
    if dataset_ids:
        datasets = dataset_repository.get_dataset_map_by_ids(dataset_ids)
    elif dataset_id:
        datasets.append(repository.get_one("dataset", {'id': dataset_id, 'type': ['SQL', 'API', 'UNION']}))

    if is_all:
        # 获取所有调度数据集
        datasource = {}
        if is_mysoft_shuxin:
            datasource = repository.get_one("data_source", {'type': DataSourceType.MysoftShuXin15.value})
        else:
            datasource = repository.get_one("data_source", {'type': DataSourceType.MysoftNewERP.value})
        if datasource:
            datasets = dataset_repository.get_dataset_connect_type_by_source(datasource.get("id"), connect_type='2')
    for dataset in datasets:
        flow_id = dataset.get('id')
        # 调用清洗
        flow_service.run_flow(flow_id)
        print(f"数据集：{dataset.get('name')}[{dataset.get('id')}] 开始清洗")

    return datasets


def delete_unused_system_dataset():
    """
    删除未被使用的系统级数据集
    """
    try:
        datasets = dataset_repository.get_datasets("1", exclude_types=['EXCEL', 'LABEL', 'FOLDER', 'TEMPLATE',
                                                                       'EXTERNAL_SUBJECT',
                                                                       'INDICATOR'])
    except Exception as e:
        logger.error(str(e))

    delete_dataset = []

    if datasets:
        for dataset in datasets:
            dataset_id = dataset.get("id")
            results = relate_objects(dataset_id)
            if not results or len(results) < 1:
                try:
                    dataset_define_service.delete_dataset_no_permission(dataset_id)
                except Exception as e:
                    logger.error(f'租户code[{g.code}]删除数据集[{dataset_id}]报错,详细信息：' + str(e))
                    continue
                delete_dataset.append({"id": dataset_id, "name": dataset.get("name")})

    return delete_dataset


def delete_dataset_tmp_folder():
    """
    删除系统分发数据集文件夹空文件夹
    """
    try:
        folders = dataset_repository.get_tmp_folder()
    except Exception as e:
        logger.error(str(e))

    delete_folders = []
    i = 0
    for folder in folders:
        folder_id = folder.get("id")
        try:
            dataset_define_service.delete_dataset_no_permission(folder_id)
        except Exception as e:
            i += 1
            logger.error(f"租户code[{g.code}]删除文件夹[{folder_id}]报错，详细信息：" + str(e))
            continue
        delete_folders.append({"id": folder_id, "name": folder.get("name")})

    if len(folders) > 0 and i == 0:  # 存在目录,同时删除没有报错
        delete_folders.append(delete_dataset_tmp_folder())

    return delete_folders

def get_shuxin15_table_param(dataset_sql, data_source_id):
    """
    获取sql中使用的表的变量，然后进行合并返回前端
    """
    table_names = sql_helper.extract_tables(dataset_sql)
    var_list = []
    var_name_list = []
    for table_name in table_names:
        table_name = table_name.rstrip(',')
        if not table_name:
            continue
        columns = data_source_service.get_table_columns(
            ColumnQueryModel(**{'id': data_source_id, 'table_name': table_name})).get_dict(['var_content'])
        for var in columns.get('var_content', []):
            if var.get('name') in var_name_list:
                continue
            var_name_list.append(var.get('name'))
            var_list.append(var)
    return var_list


