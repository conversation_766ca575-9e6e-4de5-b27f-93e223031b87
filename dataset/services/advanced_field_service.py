#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
    class
    <NAME_EMAIL> on 2018/1/16.
"""
import hashlib
import json
import re
from json import JSONDecodeError

from base import repository
from base.enums import (
    DatasetFieldType,
    DatasetFieldOperator,
    DatasetFieldDataType,
    DatasetFieldGroup,
    DatasetFieldCompareSubType,
)
from components.dateset_generate_col_name import generate_new_col_name
from components.query_structure_sql import Var
from dashboard_chart.repositories import chart_repository
from dataset.cache import dataset_field_meta_cache
from dataset.cache import dataset_include_vars_cache
from dataset.repositories import advanced_field_repository
from dataset.services import dataset_var_service, dataset_var_parser, dataset_field_group_service
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.redis import conn as conn_redis
from dmplib.hug import g
from rbac.services.data_permissions import data_permission_edit_filter
from dataset.models import DatasetFieldModel
from dataset.common import advance_field_helper
from components.query_models import QueryStructure, Select, Limit, ModelEncoder
from dataset.query.result_data import status_code, DatasetQueryException
from self_service import external_query_service
from self_service.repositories import external_subject_repository


from typing import Dict, List, Union


def clean_dashboard_chart_cache(dataset_id):
    """
    根据dataset_id批量清除报告单图缓存数据
    :param dataset_id:
    :return:
    """
    chart_id_list = chart_repository.get_chart_id_list_by_dataset_id(dataset_id)
    redis_cache = conn_redis()
    if chart_id_list:
        for chart_id in chart_id_list:
            redis_cache.del_data(chart_id.get("id"))


def validate_expression(expression: str, dataset_id: str, external_subject_ids: List[str] = None):
    """
    高级字段表达式预执行，验证表达式是否有效
    :param expression: 表达式
    :param dataset_id: 数据集ID
    :param external_subject_ids: 外部主题数据集(多个)
    :return:
    """
    try:
        query_structure = QueryStructure()

        select = Select()
        select.prop_raw = expression
        select.alias = "exp"
        query_structure.select.append(select)

        limit = Limit()
        limit.row = 10
        query_structure.limit = limit

        from dataset.query.query_dataset_service import QueryDatasetService  # pylint: disable=C0415

        query_data = QueryDatasetService(
            **{
                "user_id": g.userid,
                "dataset_id": dataset_id,
                "chart_id": None,
                "external_subject_ids": external_subject_ids,
                "query_structure_json": json.dumps(query_structure, cls=ModelEncoder),
            }
        )

        result = query_data.get_query_data()

        if result.code == 200:
            return True, ""

        elif result.code == 404:
            return False, "字段不存在"

        else:
            return False, status_code[result.code]

    except DatasetQueryException as dqe:
        msg = "执行错误: {} , sql：{}".format(str(dqe), dqe.sql)
        raise UserError(message=msg) from dqe
    except Exception as e:
        msg = "内部服务错误: {}".format(str(e))
        raise UserError(message=msg) from e


def check_col_name(dataset_id, col_name_gened):
    """
    校验高级字段col_name是否重名：判断字段是否已存在于dataset_field
    :param dataset_id: 数据集ID
    :param col_name_gened: 生成的col_name
    :return:
    """
    dataset_field_col_name = repository.get_data(
        "dataset_field", {"dataset_id": dataset_id, "col_name": col_name_gened}, ["id"]
    )
    if dataset_field_col_name:
        return False
    return True


def is_calc(exp):
    """
    判断是否为计算高级：表达式是否包含计算类型方法
    :param exp: 表达式
    :return:
    """
    calc_types = [
        DatasetFieldOperator.Avg.value,
        DatasetFieldOperator.Count.value,
        DatasetFieldOperator.Sum.value,
        DatasetFieldOperator.Min.value,
        DatasetFieldOperator.Max.value,
        DatasetFieldOperator.MaxBy.value,
        DatasetFieldOperator.MinBy.value,
    ]
    funcs = re.findall(r"([_a-zA-Z]+?)\(", exp)
    for func in funcs:
        if func.upper() in calc_types:
            return True
    return False


def get_new_col_name(model: DatasetFieldModel):
    # 高级字段col_name生成规则与普通字段一致,不再随机生成
    col_name_gened = "A" + "_" + generate_new_col_name(model.dataset_id, model.alias_name)
    # 需要校验col_name是否重名
    is_dumplicated = check_col_name(model.dataset_id, col_name_gened)
    if not is_dumplicated:
        msg = "字段名错误: 高级字段[%s]已经存在" % model.alias_name
        raise UserError(message=msg)
    return col_name_gened


@data_permission_edit_filter("dataset-edit")
def save_dataset_field(model: DatasetFieldModel, external_subject_id: str = None, origin_table_name=None):
    """
    新增或修改高级字段
    :param model:
    :return: col_name
    """
    model.data_type = model.data_type if model.data_type else DatasetFieldDataType.Number.value
    model.field_group = model.field_group if model.field_group else DatasetFieldGroup.Measure.value
    model.visible = 1

    # 兼容旧版高级字段表达式
    if not model.expression_advance:
        # 旧式expression转新式(不做定制化函数转换，如TODAY()=>CURDATE())
        model.expression_advance = advance_field_helper.expression_convertor(model.expression, is_custm_op_cvt=False)

    # 先替换高级字段中的数据集字段，再处理变量，解决变量中包含[]高级字段中引用字段的占位符导致的bug问题
    replaced_field_exp = advance_field_helper.expression_real_name(model.expression_advance, model.dataset_id)
    # 判断是否带有变量, 存在变量则需要替换变量值
    dataset_field_include_vars, expression_advance_var = handle_expression_advance_vars(model, replaced_field_exp)

    # 测试运行
    external_subject_ids = [external_subject_id] if external_subject_id else []
    is_valid, msg = validate_expression(
        expression_advance_var if expression_advance_var else replaced_field_exp, model.dataset_id, external_subject_ids
    )
    if not is_valid:
        raise UserError(message=msg)

    model.type = (
        DatasetFieldType.Calculate.value if is_calc(model.expression_advance) else DatasetFieldType.Customer.value
    )

    # 新增
    if not model.id:
        return add_advanced_field(model, dataset_field_include_vars, external_subject_id, origin_table_name)
    # 修改
    else:
        return edit_advanced_field(model, dataset_field_include_vars)


# 新增高级字段时，将新增的高级字段保存到主题表中
def add_field_fix_external_subject(external_subject_id, table_name, col_name):
    external_subject = external_subject_repository.get_subject_detail(external_subject_id)
    all_table_fields = json.loads(external_subject['table_fields'])
    current_table = {}
    for table_field in all_table_fields:
        if table_field['table_name'].lower() == table_name.lower():
            current_table = table_field

    if current_table is None or current_table['field_list'] is None:
        return

    is_exists = False
    for field in current_table['field_list']:
        if field['col_name'].lower() == col_name.lower():
            is_exists = True

    if not is_exists:
        current_table['field_list'].append(
            {"col_name": col_name, "type": DatasetFieldCompareSubType.AdvancedField.value}
        )

    external_subject_repository.update_external_subject(
        {'table_fields': json.dumps(all_table_fields)}, {'id': external_subject_id}
    )


# 删除高级字段时，主题表也要做同步修改
def delete_field_fix_external_subject(external_subject_id, table_name, col_name):
    external_subject = external_subject_repository.get_subject_detail(external_subject_id)
    all_table_fields = json.loads(external_subject['table_fields'])
    current_table = {}
    for table_field in all_table_fields:
        if table_field['table_name'].lower() == table_name.lower():
            current_table = table_field

    if current_table is None or current_table['field_list'] is None:
        return

    new_field_list = []
    for field in current_table['field_list']:
        if field['col_name'].lower() != col_name.lower():
            new_field_list.append(field)

    current_table['field_list'] = new_field_list

    external_subject_repository.update_external_subject(
        {'table_fields': json.dumps(all_table_fields)}, {'id': external_subject_id}
    )


def add_advanced_field(
    model: DatasetFieldModel, dataset_field_include_vars: [] = None, external_subject_id=None, origin_table_name=None
):
    """
    新增高级字段
    :param model:
    :param dataset_field_include_vars:
    :return: col_name
    """
    dataset_field_id = repository.get_data(
        "dataset_field_delete",
        {"dataset_id": model.dataset_id, "origin_col_name": model.alias_name},
        fields=["dataset_field_id"],
    )

    model.col_name = get_new_col_name(model)
    newid = seq_id()
    if external_subject_id is not None and origin_table_name is not None:
        newid = external_query_service.generate_dataset_field_id(origin_table_name, model.col_name)
    model.id = dataset_field_id.get("dataset_field_id") if dataset_field_id else newid
    model.rank = repository.get_data_max_rank("dataset_field", "rank", {"dataset_id": model.dataset_id})
    # 为include_dataset_var 赋值field_id  添加时id后生成
    for dataset_field_include_var in dataset_field_include_vars:
        dataset_field_include_var["field_id"] = model.id

    model.validate()

    res_id = advanced_field_repository.insert_advanced_field(model, dataset_field_include_vars)
    if res_id != model.id:
        raise UserError(message="新增高级字段错误")

    # 将新增的高级字段保存到主题表中
    if external_subject_id is not None and origin_table_name is not None:
        add_field_fix_external_subject(external_subject_id, origin_table_name, model.col_name)

    # 添加数据集字段元数据缓存 、设置数据集多字段元数据缓存
    dataset_field_meta_cache.add_dataset_field_cache(model.get_dict())
    # 删除数据集字段和变量关系缓存
    dataset_include_vars_cache.del_dataset_include_vars_cache([model.dataset_id])
    return model.col_name


def edit_advanced_field(model: DatasetFieldModel, dataset_field_include_vars: [] = None):
    """
    修改高级字段
    :param model:
    :param dataset_field_include_vars:
    :return: col_name
    """
    old_field = repository.get_data("dataset_field", {"id": model.id}, ["alias_name", "col_name"])
    if not old_field:
        msg = "修改高级字段失败，字段[%s]丢失" % model.alias_name
        raise UserError(message=msg)

    if old_field.get("alias_name") == model.alias_name:
        # 当alias_name不变时，需要考虑老的高级字段col_name不变
        model.col_name = old_field.get("col_name")
    else:
        model.col_name = get_new_col_name(model)

    model.validate()

    # 检查字段type类型是否有变化，目前不支持type变化的修改
    field_type = repository.get_data_scalar("dataset_field", {"id": model.id}, "type")
    if field_type == DatasetFieldType.Calculate.value and model.type != field_type:
        # 当前类型为计算高级（带聚合）时，不支持带聚合函数和不带聚合函数的转换
        raise UserError(message="暂不支持带聚合函数和不带聚合函数的转换")
    res_id = advanced_field_repository.update_advanced_field(model, dataset_field_include_vars)
    if res_id != model.id:
        raise UserError(message="修改高级字段错误")
    # 修改数据集字段元数据缓存 、设置数据集多字段元数据缓存
    dataset_field_meta_cache.update_dataset_field_cache(model.get_dict())
    # 删除数据集字段和变量关系缓存
    dataset_include_vars_cache.del_dataset_include_vars_cache([model.dataset_id])
    return model.col_name


def handle_expression_advance_vars(model: DatasetFieldModel, replaced_field_exp: str):
    """
    处理高级字段中带有变量的表达式
    :return:
    """
    dataset_field_include_vars = []
    expression_advance_var = replaced_field_exp
    if expression_advance_var:
        var_params = dataset_var_parser.DatasetVarParser.get_var_names_from_sql_str(expression_advance_var)
        # 同一个变量若被引用多次，只能插入一条引用记录
        include_vars_dict = dict(
            (var_param, {"id": seq_id(), "dataset_id": model.dataset_id, "field_id": model.id, "var_id": var_param})
            for var_param in var_params
        )
        dataset_field_include_vars = list(include_vars_dict.values())
        dataset_var_models = []
        dataset_vars = dataset_var_service.batch_get_dataset_vars([model.dataset_id])
        for dataset_var in dataset_vars:
            v = Var(**dataset_var)
            v.var_id = dataset_var.get("id")
            v.value = dataset_var.get("value") if "value" in dataset_var else dataset_var.get("default_value")
            dataset_var_models.append(v)
        expression_advance_var = advance_field_helper.transform_var(expression_advance_var, dataset_var_models, True)
    return dataset_field_include_vars, expression_advance_var


def delete_dataset_field(dataset_field_id, external_subject_id=None, origin_table_name=None):
    """
    删除用户自定义字段
    :param dataset_field_id: dataset_field表字段id
    :return:
    """
    dataset_field = repository.get_data("dataset_field", {"id": dataset_field_id})
    if not dataset_field:
        msg = "数据集字段不存在：" + dataset_field_id
        raise UserError(message=msg)
    if dataset_field["type"] == DatasetFieldType.Normal.value:
        raise UserError(message="数据集字段类型错误")
    # 检查当前删除字段是否已经被使用
    if check_field_is_used(dataset_field_id):
        raise UserError(message="字段已被使用，不可删除")

    dataset_field_delete = dict()
    # 相同记录只存一条, 添加到已删除表中
    if not repository.data_is_exists("dataset_field_delete", {"dataset_field_id": dataset_field.get("id")}):
        # 只取部分数据
        dataset_field_delete["dataset_id"] = dataset_field.get("dataset_id")
        dataset_field_delete["dataset_field_id"] = dataset_field.get("id")
        dataset_field_delete["col_name"] = dataset_field.get("col_name")
        dataset_field_delete["origin_col_name"] = dataset_field.get("alias_name")
        dataset_field_delete["type"] = dataset_field.get("type")

    res_id = advanced_field_repository.delete_advanced_field(
        dataset_field.get("dataset_id"), dataset_field.get("id"), dataset_field_delete=dataset_field_delete
    )
    if res_id != dataset_field.get("id"):
        raise UserError(message="删除高级字段错误")

    # 删除高级字段时，主题表也要做同步修改
    if external_subject_id is not None and origin_table_name is not None:
        delete_field_fix_external_subject(external_subject_id, origin_table_name, dataset_field.get("col_name"))

    # 删除数据集字段元数据缓存 、删除数据集多字段元数据缓存
    dataset_field_meta_cache.delete_dataset_field_cache(dataset_field)
    # 删除数据集字段和变量关系缓存
    dataset_include_vars_cache.del_dataset_include_vars_cache([dataset_field.get("dataset_id")])
    # 删除数据集字段和分组中的关系
    dataset_field_group_service.delete_field_group_relation([dataset_field_id])


def check_repeat_operator(operators):
    """
    检查连续操作符
    :param operators:
    :return:
    """
    pre_op = ""
    for operator in operators:
        if operator == "+" and pre_op == "+":
            raise UserError(message="不支持连续操作符")
        pre_op = operator


def check_field_is_used(dataset_field_id):
    # 检查维度表是否存在此字段
    if repository.data_is_exists("dashboard_chart_dim", {"dim": dataset_field_id}):
        return True
    # 检查度量表是否存在此字段
    if repository.data_is_exists("dashboard_chart_num", {"num": dataset_field_id}):
        return True
    # 检查筛选表是否存在此字段
    if repository.data_is_exists("dashboard_chart_filter", {"dataset_field_id": dataset_field_id}):
        return True
    # 检查指标维度是否存在此字段
    if repository.data_is_exists("dashboard_indicator_dim", {"field_id": dataset_field_id}):
        return True
    # 检查指标度量是否存在此字段
    if repository.data_is_exists("dashboard_indicator_num", {"field_id": dataset_field_id}):
        return True
    # 检查指标筛选是否存在此字段
    if repository.data_is_exists("dashboard_indicator_filter", {"field_id": dataset_field_id}):
        return True
    return False


def dataset_field_input_operater(check_list, operator_list, item_key, item, pre_op):
    """
    处理高级字段输入框内容
    :param check_list:
    :param operator_list:
    :param item_key:
    :param item:
    :param pre_op:
    :return:
    """
    if item_key > 0 and pre_op:
        if not re.compile(r"[<><=>=<>,=+\-*/()]", re.IGNORECASE).search(pre_op):
            raise UserError(message="高级字段输入必须配合+,-,*,/内的操作符")
    elif item_key > 0 and not pre_op:
        raise UserError(message="高级字段输入必须配合操作符")

    value = hashlib.md5(item["value"].encode("utf-8")).hexdigest()[8:-8]
    operator_list.append(value)
    check_list.append("1")


def format_advanced_fields(expression_dict):
    """
    格式化高级字段表达式
    :param expression_dict:
    :return:
    """
    for item in expression_dict:
        if "op" in item and item.get("op").upper() == DatasetFieldOperator.Input.value:
            # 解决单引号导致拼接sql错误的问题，强制在单引号前面加入斜杠
            item["value"] = inverted_commas(item.get("value"))
    return json.dumps(expression_dict)


def inverted_commas(str_text):
    """
    转换字符串里面的单引号
    :param str_text:
    :return:
    """

    if not str_text:
        return ""

    new_s = ""
    for v in str_text:
        if v in ["'", "\\"]:
            new_s += "\\" + v
        else:
            new_s += v
    return new_s


def del_diagonal(dataset_fields: List[Dict[str, Union[str, int, None]]]) -> List[Dict[str, Union[str, int, None]]]:
    """
    去除高级字段输入框里面的双斜杆
    :param dataset_fields:
    :return:
    """
    for dataset_field in dataset_fields:
        if dataset_field.get("type") != DatasetFieldType.Normal.value and dataset_field.get("expression"):
            try:
                expression_dict = json.loads(dataset_field.get("expression"))
            except JSONDecodeError as e:
                msg = "高级字段表达式json序列化错误：" + str(dataset_field)
                raise UserError(message=msg) from e
            for item in expression_dict:
                if "op" in item and item.get("op").upper() == DatasetFieldOperator.Input.value:
                    item["value"] = get_item_value(item.get("value"), "\\'", "'", "'")
                    item["value"] = get_item_value(item.get("value"), "\\\\", "\\", "\\\\")
            dataset_field["expression"] = json.dumps(expression_dict)
    return dataset_fields


def get_item_value(item_value, old_str, new_str, find_str):
    return item_value.replace(old_str, new_str) if item_value and item_value.find(find_str) > -1 else item_value
