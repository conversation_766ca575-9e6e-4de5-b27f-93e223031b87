# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    Created by chenc04 on 2020/08/03.
"""
import os

from components.data_hub import CollectAPI
from components.db_engine_transform import DbTransformService
from dataset.models import DatasetDataModel
from base.enums import DatasetEditMode
from dataset.services.dataset_base_service import DatasetBaseService, record_execute_time
from dmplib.utils import sql_util
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id


class DatasetDataHubService(DatasetBaseService):
    """
    datahub-mysql数据集业务类
    """

    def __init__(self, dataset_model=None, data_source_model=None):
        super().__init__(dataset_model, data_source_model)

    @property
    def transform_cls(self):
        return DbTransformService

    def get_api(self, data_source_model):
        """
        获取api
        :param data_source_model:
        :return:
        """
        return CollectAPI(data_source_model.conn_str.host, data_source_model.conn_str.access_secret)

    def get_result_data(self, data_source_model, sql):
        web_time_out = int(os.environ.get('TIMEOUT', 10)) - 1
        result_data = self.get_api(data_source_model).get_sql_list(
            sql, timeout=web_time_out, db_code=data_source_model.conn_str.db_code
        )

        if result_data.get('errmsg'):
            raise UserError(
                message='调用datahub接口失败，错误内容：{errmsg},sql: {sql}'.format(sql=sql, errmsg=result_data.get('errmsg'))
            )
        elif not result_data.get("data"):
            raise UserError(message="调用datahub接口失败，接口没有数据。")
        else:
            return result_data.get('data')

    def get_total_count(self, count_result):
        if count_result.get("data"):
            if count_result.get("data")[0].get("TOTAL_COUNT"):
                total_count = count_result.get("data")[0].get("TOTAL_COUNT")
            elif count_result.get("data")[0].get("total_count"):
                total_count = count_result.get("data")[0].get("total_count")
            else:
                total_count = 0
        else:
            total_count = 0
        return total_count

    def run_get_data_or_struct(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（重写父类方法）
        :return: dataset.models.DatasetDataModel
        """
        # 1、 加载数据集内容
        dataset_content = self.load_dataset_content(self.dataset_model.content)
        dataset_id = dataset_content.get("dataset_id") if dataset_content.get("dataset_id") else seq_id()

        sql, meta_data = self.get_query_sql_and_metadata(dataset_id)
        sql = self.validation_sql(sql, can_order_by=False)
        sql_util.validate_key_word(sql)

        # 2、根据sql语句获取总数
        count_sql = " select count(1) as total_count  from ( " + sql + " ) s "
        count_result = self.get_result_data(self.data_source_model, count_sql)

        total_count = self.get_total_count(count_result)

        # 3、根据sql语句获取数据和字段结构
        if total_count and int(total_count) > self.data_limit:
            sql = " select * from ( {sql} ) s limit {limit} ".format(sql=sql, limit=self.data_limit)
        result_data, columns = self.get_data_or_columns(sql)

        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            tmp_table_name, new_struct, create_table_sql = meta_data
        else:
            # 4、测试运行创建表
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(dataset_id, columns)

        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = dataset_id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = columns
        dataset_data_model.total_count = total_count
        dataset_data_model.tmp_table_name = tmp_table_name
        dataset_data_model.new_column_struct = new_struct
        dataset_data_model.create_table_sql = create_table_sql

        return dataset_data_model

    @record_execute_time()
    def get_data_or_columns(self, sql):
        """
        根据sql语句获取数据和结构
        :return:
        """
        result_data = self.get_result_data(self.data_source_model, sql)
        return result_data.get("data"), result_data.get("struct")

    def run_get_data_count(self):
        """
        运行数据集，产生数据集数据和数据集字段
        :return:
        """
        count_sql = self.generate_sql_dataset_count_sql()
        count_result = self.get_result_data(self.data_source_model, count_sql)
        total_count = self.get_total_count(count_result)
        return total_count

    def run_get_field_values(self, field_name):
        """
        运行数据集，获取字段的所有值
        :return:
        """
        sql = self.validation_sql(self.get_query_sql(), can_order_by=False)
        sql_util.validate_key_word(sql)
        group_sql = ' select {field_name} from ( {sql} ) a limit {max_data_limit} group by {field_name} '.format(
            field_name=field_name, sql=sql, max_data_limit=str(self.value_data_limit)
        )
        result_data = self.get_result_data(self.data_source_model, group_sql)
        return result_data.get("data")
