# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    Created by chenc04 on 2017/10/20.
"""
import json
import re
from collections import defaultdict

import pymysql

from base import repository
from base.enums import DatasetFieldType, SqlComplexOrNot, DatasetStorageType, DatasetEditMode, MysoftNewERPDataBaseType, DBEngine, DatasetType, DataSourceType, SqlNeedProcedure
from components.storage_setting import get_storage_type, is_local_storage
from components.redis_utils import stale_cache
from components.data_center_api import get_local_data_bind_source_id
from dataset.common import sql_helper
from dataset.models import DatasetDataModel, DatasetModel
from dataset.repositories import dataset_field_repository
from dataset.repositories import dataset_repository
from dataset.services.dataset_base_service import DatasetBaseService, record_execute_time
from dmplib.utils import sql_util
from dmplib.hug import g
from dmplib.utils.errors import UserError
from dmplib.utils.sql_util import Description
from dmplib.utils.strings import seq_id
from data_source.models import DataSourceModel
from data_source.services.data_source_service import load_data_source_conn_str


class DatasetUnionService(DatasetBaseService):
    """
    联合数据集业务类
    """

    def __init__(self, dataset_model=None, data_source_model=None):
        super().__init__(dataset_model, data_source_model)
        self.old_sql = ""
        self.sql = ""
        self.table_names = []
        self.dataset_tables = []
        self.dataset_id = ""
        # 源数据集ID
        self.source_dataset_ids = []
        self.dataset_fields = []
        self.new_struct = []
        self.tmp_table_name = ""
        self.create_table_sql = ""
        self.bind_source_id = ""
        self.running_way = None

    def init_sql(self, content):
        """
        获取执行sql语句
        :return:
        """
        if content.get("dataset_id"):
            dataset_id = content.get("dataset_id")
        elif getattr(self.dataset_model, "id"):
            dataset_id = getattr(self.dataset_model, "id")
        else:
            dataset_id = seq_id()
        self.dataset_id = dataset_id
        # 支持视图模式
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            self.dataset_model.id = self.dataset_id
            self.transfer_filter_content()
            self.sql, meta_data = self.get_relation_edit_mode_sql(self.dataset_id, with_limit=False)
            self.tmp_table_name, self.new_struct, self.create_table_sql = meta_data
            self.old_sql = self.sql
            self.__recover_new_struct()
        else:
            self.old_sql = content.get("sql")
            self.sql = content.get("sql")
        # 校验sql
        self.sql = self.validation_sql(self.sql)
        sql_util.validate_key_word(self.sql)

        # 解析替换sql
        self.table_names = sql_helper.extract_tables(self.sql, is_alias=True)
        self.dataset_tables = sql_helper.parse_table_alias_names(self.table_names)
        self.dataset_tables = self.get_dataset_metadata()
        self.validate_sql_fields(self.sql, self.dataset_tables)
        self.sql, self.source_dataset_ids = sql_helper.parse_custom_sql(self.sql, self.dataset_tables)
        self.sql = self.replace_vars(self.sql)

    @staticmethod
    @stale_cache('union_source', expire=60)
    def __get_source_by_id(content):
        try:
            source_id = json.loads(content).get("data_source_id")
        except Exception as e:
            raise UserError(message=f"数据集content错误：{str(e)}")
        source = repository.get_data('data_source', {'id': source_id})
        if not source:
            raise UserError(message=f"数据源不存在")
        return source

    def __dataset_storage_way(self, dataset_id):
        """
        数据集存储方式
        :param dataset_id:
        :return:
        """
        dataset = dataset_repository.get_dataset(dataset_id)
        model = DatasetModel(**dataset)
        if model.type == DatasetType.Sql.value:
            data_source = self.__get_source_by_id(model.content)
            if data_source.get('type') == DataSourceType.MysoftNewERP.value:
                return get_local_data_bind_source_id(data_source)
            return ''
        elif model.type == DatasetType.Union.value and get_storage_type(g.code) == DatasetStorageType.DatasetStorageOfLocal.value:
            try:
                bind_source_id = json.loads(model.content).get("bind_source_id")
            except Exception as e:
                raise UserError(message=f"数据集content错误：{str(e)}")
            if bind_source_id:
                return bind_source_id
            else:
                return get_local_data_bind_source_id(None)
        else:  # excel union数据集 暂时以当前存储模式为准(不考虑切换的场景)
            if get_storage_type(g.code) == DatasetStorageType.DatasetStorageOfLocal.value:
                return get_local_data_bind_source_id(None)
            return ''

    def __check_data_source(self):
        dataset_way = set()
        for source_dataset_id in self.source_dataset_ids:
            # 组合数据集云端和本地不能一起使用
            dataset_way.add(self.__dataset_storage_way(source_dataset_id))
            # if len(dataset_way) > 1:
            #     raise UserError(message="云上数据集和云下数据集不能组合使用、op和saas数据集不能组合使用")
        return dataset_way.pop() if dataset_way else ''

    def __recover_new_struct(self):
        recover = re.compile(r"^\{(.*?)\}$")

        def __get_origin_name(i, key):
            m = recover.match(i.get(key, ''))
            if m and m.groups():
                i[key] = m.groups()[0]

        for item in self.new_struct:
            __get_origin_name(item, "origin_table_name")
            __get_origin_name(item, 'origin_table_alias_name')
            __get_origin_name(item, "name")
            __get_origin_name(item, "table_name")

    @staticmethod
    def __transfer_table_name(name):
        if not name or (name and name.startswith('{') and name.endswith('}')):
            return name
        return "{" + name + "}"

    @staticmethod
    def __transfer_field_name(name):
        if not name or (name and name.startswith('[') and name.endswith(']')):
            return name
        return "[" + name + "]"

    def transfer_filter_content(self):
        """
        转换filter_content中的字段和表名转换符
        """
        for item in self.dataset_model.filter_content:
            item["table_name"] = self.__transfer_table_name(item["table_name"])
            item['col_name'] = self.__transfer_field_name(item["col_name"])

    @staticmethod
    def transfer_node_fields(node_datas):
        """
        视图模式请求api获取结构
        :param node_datas:
        :return:
        """
        # 所有的columns
        columns = []
        for node in node_datas:
            for field in node.get("fields"):
                field["table_name"] = DatasetUnionService.__transfer_table_name(node.get("name"))
                field["origin_table_id"] = node.get("id")
                field["origin_table_name"] = DatasetUnionService.__transfer_table_name(node.get("name"))
                field["origin_table_comment"] = node.get("comment")
                field["origin_table_alias_name"] = DatasetUnionService.__transfer_table_name(node.get("alias_name"))
                # if 'col_name' not in field:
                field['col_name'] = field.get('col_name') or field.get('name')
                field['name'] = DatasetUnionService.__transfer_field_name(field.get('name'))
                field['data_type'] = field.get('type')
                field["origin_field_type"] = field.get("type")
                columns.append(field)
        return columns

    @staticmethod
    def transfer_link_data(link_datas):
        for item in link_datas:
            item.from_table_name = DatasetUnionService.__transfer_table_name(item.from_table_name) if item.from_table_name else item.from_table_name
            item.from_alias_name = ""
            item.to_table_name= DatasetUnionService.__transfer_table_name(item.to_table_name) if item.to_table_name else item.to_table_name
            item.to_alias_name= ""
            for i in item.join_fields:
                i.left = DatasetUnionService.__transfer_field_name(i.left)
                i.right = DatasetUnionService.__transfer_field_name(i.right)
        return link_datas

    def validate_sql_fields(self, sql: str, dataset_metadata: list):
        def get_no_table_fields(fields):
            no_table_fields = []
            for r in fields:
                if not r:
                    continue

                field = r.strip().split(' ')[0]
                if field.find('.') < 0:
                    no_table_fields.append(field.replace('[', '').replace(']', '').strip())
            return no_table_fields

        if len(dataset_metadata) < 2:
            return

        try:
            fields = sql_helper.get_select_fields(sql)
        except Exception as e:
            raise UserError(message='解析SQL失败，SQL存在语法错误：%s' % str(e))

        if not fields or '*' in fields:
            return

        no_table_fields = get_no_table_fields(fields)
        if not no_table_fields:
            return

        res = check_field_in_multi_table(no_table_fields, dataset_metadata)
        if not res:
            return

        msg_list = ['字段【%s】在数据集【%s】中都存在；' % (k, ','.join(v)) for k, v in res.items()]
        raise UserError(message='SQL语法错误，在查询SQL中：\n%s' % '\n'.join(msg_list))

    def get_dataset_metadata(self):
        """
        获取数据集元数据
        :return:
        """
        query_dataset_names = [table.get("sql_table_name") for table in self.dataset_tables]
        # 这里可能有自关联的场景，
        query_dataset_names = list(set(query_dataset_names))
        query_dataset_metadata = dataset_repository.get_dataset_by_name(query_dataset_names)
        if not query_dataset_metadata or len(query_dataset_metadata) != len(query_dataset_names):
            raise UserError(
                message='{sql} 中存在无效表名 {table_names}'.format(sql=self.sql, table_names=','.join(query_dataset_names))
            )
        sort_query_dataset_metadata = self.sort_dict_list(query_dataset_metadata, 'name')
        for dataset_metadata in sort_query_dataset_metadata:
            for table in self.dataset_tables:
                if dataset_metadata.get("name") == table.get("sql_table_name"):
                    dataset_metadata["sql_table_name"] = table.get("sql_table_name")
                    dataset_metadata["sql_table_alias_name"] = table.get("sql_table_alias_name")

            dataset_field_metadata = dataset_field_repository.get_dataset_field(
                dataset_metadata.get("id"), {"type": DatasetFieldType.Normal.value}
            )
            if not dataset_field_metadata:
                raise UserError(message='{sql_table_name}表没有字段'.format(sql_table_name=dataset_metadata.get("name")))
            dataset_metadata["dataset_fields"] = self.sort_dict_list(dataset_field_metadata, 'alias_name')
        return sort_query_dataset_metadata

    def get_erp_datasource_of_data_center(self):
        """
        获取数据服务中心执行数据源
        :return:
        """
        data = repository.get_data("data_source", {'id': self.bind_source_id})
        model = DataSourceModel(**data)
        load_data_source_conn_str(model)
        return model

    def run_get_data_or_struct_local(self):
        """
        本地存储模式，获取数据拍照
        :return:
        """
        dataset_content = self.load_dataset_content(self.dataset_model.content)

        # 2、获取数据服务中心指定的数据源
        self.data_source_model = self.get_erp_datasource_of_data_center()

        if self.data_source_model.db_type.lower() == MysoftNewERPDataBaseType.Mysql.value:
            self.db_engine = DBEngine.RDS.value
        elif self.data_source_model.db_type.lower() == MysoftNewERPDataBaseType.DM.value:
            self.db_engine = DBEngine.DM.value
        else:
            self.db_engine = DBEngine.MSSQL.value

        self.init_sql(dataset_content)

        # 3、根据sql语句获取数据和字段结构
        result_data, struct, total_count, is_complex, is_need_procedure = self.get_erp_data(self.sql, is_need_column_struct=True)

        # 4、测试运行创建表
        dataset_id = self.dataset_id
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            tmp_table_name, new_struct, create_table_sql = self.tmp_table_name, self.new_struct, self.create_table_sql
        else:
            if self.dataset_model.type == "UNION" or is_local_storage():
                create_table = False
            else:
                create_table = True
            # 3、测试运行创建表
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(dataset_id, struct, create_table=create_table)

        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = dataset_id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = struct
        dataset_data_model.total_count = total_count
        dataset_data_model.tmp_table_name = tmp_table_name
        dataset_data_model.new_column_struct = new_struct
        dataset_data_model.create_table_sql = create_table_sql
        dataset_data_model.replace_sql = self.sql
        dataset_data_model.source_dataset_ids = self.source_dataset_ids
        dataset_data_model.is_complex = SqlComplexOrNot.Complex.value if is_complex else SqlComplexOrNot.Sample.value
        dataset_data_model.bind_source_id = self.bind_source_id
        dataset_data_model.is_need_procedure = is_need_procedure if is_need_procedure is not None else 0
        dataset_data_model.running_way = self.running_way

        return dataset_data_model

    @staticmethod
    @stale_cache('union_depend', expire=60)
    def _get_depend_dataset(dataset_id):
        return repository.get_data('dataset', {'id': dataset_id}, fields=['type', 'content'])

    def get_running_way(self, source_dataset_ids=None, pre_way=None):
        """
        获取组合数据集引用的数据集绑定数据源
        sql、api数据集， 如果数据源是云端则走云端，否则本地
        组合数据集：新创建的则 获取组合数据集状态，历史的则根据当前是本地还是云端
        excel数据集：当前是本地还是云端
        :return:
        """
        pre_way = pre_way or None
        way = None
        source_dataset_ids = source_dataset_ids or self.source_dataset_ids or []
        for depend_dataset_id in source_dataset_ids:
            depend_dataset = self._get_depend_dataset(depend_dataset_id)
            if not depend_dataset:
                continue
            if depend_dataset.get('type') in [DatasetType.Sql.value, DatasetType.Api.value]:
                data_source = self.__get_source_by_id(depend_dataset.get('content'))
                if data_source.get('type') == DataSourceType.MysoftNewERP.value:
                    conn_str = data_source.get('conn_str')
                    conn_str = json.loads(data_source.get('conn_str')) if conn_str else {}
                    erp_api_info_id = conn_str.get('erp_api_info_id')
                    is_master_local_db = conn_str.get('is_master_local_db')
                    if is_master_local_db in ['1', 1] or not conn_str:
                        way = 'local'
                    else:
                        way = f"local_{erp_api_info_id}" if erp_api_info_id else 'local'
                else:
                    way = 'cloud'
            elif depend_dataset.get('type') == DatasetType.Union.value:
                content = depend_dataset.get('content')
                content = json.loads(content) if content else {}
                way = content.get('running_way')
                if not way:
                    sub_source_dataset_ids = content.get('source_dataset_ids') or []
                    way = self.get_running_way(source_dataset_ids=sub_source_dataset_ids, pre_way=pre_way)
            else:
                # 本地模式不判断excel数据集
                if get_storage_type(g.code) == DatasetStorageType.DatasetStorageOfCloud.value:
                    way = DatasetStorageType.DatasetStorageOfCloud.value
            if pre_way and pre_way != way:
                raise UserError(message="数据集落地库不同源，不能组合")
            pre_way = way
        way = way or get_storage_type(g.code)
        if way and way.startswith('local'):
            way = 'local'
            # local模式需要获取bind_source_id
            self.bind_source_id = self.__check_data_source()
        setattr(g, "running_way", way)
        return way

    def run_get_data_or_struct(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（重写父类方法）
        :return: dataset.models.DatasetDataModel
        """
        # 1、加载数据集内容
        dataset_content = self.load_dataset_content(self.dataset_model.content)
        self.init_sql(dataset_content)

        way = self.get_running_way()

        if way == 'local':
            self.running_way = way
            return self.run_get_data_or_struct_local()

        # 2、根据sql语句获取数据和字段结构
        sql, data_limit = self.mysql_limit(self.sql, self.data_limit)
        result_data, columns = self.get_result_data(sql, data_limit)

        dataset_id = self.dataset_id
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            tmp_table_name, new_struct, create_table_sql = self.tmp_table_name, self.new_struct, self.create_table_sql
        else:
            # 3、测试运行创建表
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(dataset_id, columns)

        # 4、根据sql语句获取总数
        total_count = self.run_get_data_count(sql=self.sql)

        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = dataset_id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = columns
        dataset_data_model.total_count = total_count
        dataset_data_model.tmp_table_name = tmp_table_name
        dataset_data_model.new_column_struct = new_struct
        dataset_data_model.create_table_sql = create_table_sql
        dataset_data_model.replace_sql = self.sql
        dataset_data_model.source_dataset_ids = self.source_dataset_ids
        dataset_data_model.running_way = way

        return dataset_data_model

    @record_execute_time()
    def get_result_data(self, sql, data_limit):
        """
        根据sql查询data库数据
        :param sql:
        :param data_limit:
        :return:
        """
        try:
            with repository.get_data_db() as db:
                if isinstance(data_limit, list):
                    tmp_result_data = db.query(sql, offset=data_limit[0], limit=data_limit[1])
                else:
                    tmp_result_data = db.query(sql, limit=data_limit)
                result_data = DatasetBaseService.data_processing(tmp_result_data)
                columns = [Description(*col) for col in list(db.cur.description)]
                structs = []
                for column in columns:
                    struct = {
                        "col_name": column.name,
                        "data_type": column.col_type,
                        "comment": column.alias_name if column.alias_name else self.get_alias_name(column.name),
                    }
                    structs.append(struct)
            return result_data, structs
        except pymysql.Error as e:
            raise UserError(message='sql语句：' + self.old_sql + ' \n\n 替换后sql语句：' + self.sql + ' \n\n 运行错误：' + str(e))
        except UserError as ue:
            raise UserError(message=ue.message)
        except Exception as ex:
            raise UserError(message='sql语句：' + self.old_sql + '\n\n 替换后sql语句：' + self.sql + ' \n\n 运行错误：' + str(ex))

    def get_alias_name(self, col_name):
        for dataset in self.dataset_tables:
            dataset_fields = dataset.get("dataset_fields")
            for dataset_field in dataset_fields:
                if dataset.get('sql_table_alias_name') + '.' + dataset_field.get('col_name') == col_name:
                    return dataset_field.get('alias_name')
                if dataset_field.get('col_name') == col_name:
                    return dataset_field.get('alias_name')
        return ''

    def run_get_data_count(self, sql=None):
        """
        运行数据集，产生总条数
        :return:
        """
        try:
            dataset_content = self.load_dataset_content(self.dataset_model.content)
            self.init_sql(dataset_content)
            sql = self.sql
            way = self.get_running_way()
            if way == DatasetStorageType.DatasetStorageOfLocal.value:
                self.data_source_model = self.get_erp_datasource_of_data_center()
                if self.data_source_model.db_type.lower() == MysoftNewERPDataBaseType.Mysql.value:
                    self.db_engine = DBEngine.RDS.value
                else:
                    self.db_engine = DBEngine.MSSQL.value
                self.init_sql(dataset_content)
                sql = self.sql
                self.data_source_model = self.get_erp_datasource_of_data_center()
                _, _, total_count, _, _ = self.get_erp_data(sql, is_need_column_struct=True)
            else:
                sql = " select count(1) as total_count from ( " + sql + " ) s "
                with repository.get_data_db() as db:
                    result_data = db.query_one(sql)
                    total_count = result_data.get('total_count') if result_data.get('total_count') else 0
        except pymysql.Error as e:
            raise UserError(message='sql failed：' + str(e))
        except UserError as ue:
            raise UserError(message=ue.message)
        except Exception as ex:
            raise UserError(message='sql运行错误：' + str(ex))
        return total_count

    def run_get_field_values(self, field_name):
        """
        运行数据集，获取字段的所有值
        :return:
        """
        try:
            content = self.load_dataset_content(self.dataset_model.content)
            self.init_sql(content)
            group_sql = ' select {field_name} from ( {sql} ) a group by {field_name} limit {max_data_limit} '.format(
                field_name=field_name, sql=self.sql, max_data_limit=str(self.value_data_limit)
            )
            with repository.get_data_db() as db:
                result_data = db.query(group_sql)
                return result_data
        except Exception as e:
            raise UserError(message='sql failed：' + str(e))

    def query_table(self):
        query_to_table = {}
        query_table_names = []
        for table_name in self.table_names:
            table_name = table_name.strip()
            find_tables = re.findall("\{(.*)\}", table_name, flags=re.I)
            query_table = find_tables[0] if find_tables else ''
            query_table_names.append(query_table)
            query_to_table[query_table] = table_name
        return query_to_table, query_table_names

    def replace_sql(self):
        query_to_table, query_table_names = self.query_table()
        query_table_names = []

        dataset_tables = dataset_repository.get_dataset_by_name(query_table_names)
        if not dataset_tables:
            raise UserError(
                message='{sql} 中存在无效表名 {table_names}'.format(sql=self.sql, table_names=','.join(self.table_names))
            )

        dataset_field_dict = {}

        sort_dataset_tables = self.sort_dict_list(dataset_tables, 'name')
        for dataset in sort_dataset_tables:
            self.source_dataset_ids.append(dataset.get("id"))

            dataset_fields = dataset_field_repository.get_dataset_field(
                dataset.get("id"), {"type": DatasetFieldType.Normal.value}
            )
            if not dataset_fields:
                raise UserError(message='{sql_table_name}表没有表字段'.format(sql_table_name=dataset.get("name")))
            dataset_field_dict[dataset.get("id")] = dataset_fields

            # 替换表名
            if "{%s}" % dataset.get("name") in self.sql:
                self.sql = self.sql.replace("{%s}" % dataset.get("name"), dataset.get("table_name"))
            table_name = query_to_table.get(dataset.get("name"))
            temp_table_name = table_name.strip().split()
            if temp_table_name[0] == dataset.get("name") or (
                temp_table_name[0].replace('{', '').replace('}', '') == dataset.get("name")
            ):
                dataset["sql_table_name"] = temp_table_name[0]
            if len(temp_table_name) == 2:
                dataset["sql_table_alias_name"] = temp_table_name[1]
            # 解决表别名带有as关键字
            elif len(temp_table_name) > 2 and temp_table_name[1] == 'as':
                dataset["sql_table_alias_name"] = temp_table_name[2]
            else:
                dataset["sql_table_alias_name"] = ""

            # 替换表带别名的别名字段
            sort_dataset_fields = self.sort_dict_list(dataset_fields, 'alias_name')
            for dataset_field in sort_dataset_fields:
                self.replace_alias_col_name(
                    dataset.get("table_name"),
                    dataset.get("sql_table_name"),
                    dataset.get("sql_table_alias_name"),
                    dataset_field.get("alias_name"),
                    dataset_field.get("col_name"),
                )
            dataset['dataset_fields'] = sort_dataset_fields

        self.dataset_tables = sort_dataset_tables

        # 替换别名字段
        for dataset in sort_dataset_tables:
            dataset_fields = dataset_field_dict.get(dataset.get("id"))
            sort_dataset_fields = self.sort_dict_list(dataset_fields, 'alias_name')
            for dataset_field in sort_dataset_fields:
                if (
                    dataset_field.get("alias_name")
                    and '[{alias_name}]'.format(alias_name=dataset_field.get("alias_name")) in self.sql
                ):
                    self.sql = self.sql.replace(
                        '[{alias_name}]'.format(alias_name=dataset_field.get("alias_name")),
                        dataset_field.get("col_name"),
                    )
        # 替换字段名字段
        for dataset in sort_dataset_tables:
            dataset_fields = dataset_field_dict.get(dataset.get("id"))
            sort_dataset_fields = self.sort_dict_list(dataset_fields, 'alias_name')
            for dataset_field in sort_dataset_fields:
                replace_col_name = '[{col_name}]'.format(col_name=dataset_field.get("col_name"))
                if replace_col_name in self.sql:
                    self.sql = self.sql.replace(replace_col_name, dataset_field.get("col_name"))

    def replace_alias_col_name(self, table_name, sql_table_name, sql_table_alias_name, alias_name, col_name):
        """
        替换字段名
        :param table_name: 数据集表名
        :param sql_table_name: SQL语句中的表名
        :param sql_table_alias_name: SQL语句中的表别名
        :param alias_name: 字段别名
        :param col_name: 字段名
        :return:
        """
        if sql_table_alias_name:

            table_alias_col_alias = '{sql_table_alias_name}.[{alias_name}]'.format(
                sql_table_alias_name=sql_table_alias_name, alias_name=alias_name
            )

            table_alias_col_name = '{sql_table_alias_name}.[{col_name}]'.format(
                sql_table_alias_name=sql_table_alias_name, col_name=col_name
            )

            right_col_name = '{sql_table_alias_name}.{col_name}'.format(
                sql_table_alias_name=sql_table_alias_name, col_name=col_name
            )

            if alias_name and table_alias_col_alias in self.sql:
                self.sql = self.sql.replace(table_alias_col_alias, right_col_name)

            if table_alias_col_name in self.sql:
                self.sql = self.sql.replace(table_alias_col_name, right_col_name)

        else:
            sql_table_name_alias_name = '{sql_table_name}.[{alias_name}]'.format(
                sql_table_name=sql_table_name, alias_name=alias_name
            )
            sql_table_name_col_name = '{sql_table_name}.[{col_name}]'.format(
                sql_table_name=sql_table_name, col_name=col_name
            )
            table_name_alias_name = '{table_name}.[{alias_name}]'.format(table_name=table_name, alias_name=alias_name)
            table_name_col_name = '{table_name}.[{col_name}]'.format(table_name=table_name, col_name=col_name)
            right_col_name = '{table_name}.{col_name}'.format(table_name=table_name, col_name=col_name)
            if alias_name and sql_table_name_alias_name in self.sql:
                self.sql = self.sql.replace(table_name_alias_name, right_col_name)

            if alias_name and table_name_alias_name in self.sql:
                self.sql = self.sql.replace(table_name_alias_name, right_col_name)

            if sql_table_name_col_name in self.sql:
                self.sql = self.sql.replace(sql_table_name_col_name, right_col_name)

            if table_name_col_name in self.sql:
                self.sql = self.sql.replace(table_name_col_name, right_col_name)

    @staticmethod
    def sort_dict_list(sort_list, sort_name):
        """
        根据排序名字符长度从大道小排序
        :param sort_list:
        :param sort_name:
        :return:
        """
        flag = 1
        for i in range(len(sort_list) - 1, 0, -1):
            if flag:
                flag = 0
                for j in range(i):
                    if sort_list[j].get(sort_name) and len(sort_list[j].get(sort_name)) < len(
                        sort_list[j + 1].get(sort_name)
                    ):
                        sort_list[j], sort_list[j + 1] = sort_list[j + 1], sort_list[j]
                        flag = 1
            else:
                break
        return sort_list

    def parse_sql(self):
        self.table_names = sql_helper.parse_sql(self.sql)


def check_field_in_multi_table(fields, dataset_metadata):
    alias_names = []
    alias_names_mapping = defaultdict(list)
    for dataset in dataset_metadata:
        for r in dataset['dataset_fields']:
            if not r.get('alias_name'):
                continue

            alias_name = r["alias_name"]
            alias_names.append(alias_name)
            alias_names_mapping[alias_name].append(dataset['sql_table_name'])

    results = {}
    for field_name in fields:
        if alias_names.count(field_name) < 2:
            continue

        results[field_name] = alias_names_mapping[field_name]
    return results
