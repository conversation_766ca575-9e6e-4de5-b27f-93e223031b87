# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    Created by chenc04 on 2017/10/20.
"""
import csv
import functools
import json
import os
import re
import logging
import time
from datetime import date, datetime
import xlwt
from decimal import Decimal
from base import repository
from base.enums import (
    DatasetType,
    DatasetFileType,
    DatasetFieldType,
    DatasetEditMode,
    DataSourceType,
    DBEngine,
    DatasetStorageType, SqlWhereOperator,
)
from components.oss import OSSFileProxy
from components.remove_comment import remove_comment
from components.query_models import Prop, Object, Var, Where
from components.data_center_api import get_preview_data_of_sql, get_new_erp_datasource_model
from components.storage_setting import is_local_storage
from dataset.models import DatasetExcelExportModel
from dataset.query import query_structure_helper
from dataset.query.link_data import JoinField, LinkData
from components.query_models import Select, QueryStructure, Limit
from components.query_structure_sql import encode_query
from dataset.query.query_structure_helper import transform_object, transform_filter_where, transform_filter_content, \
    transform_filter_where_json
from dataset.repositories import dataset_field_repository
from dataset.repositories import dataset_repository
from dataset.services import dataset_field_service, dataset_var_service
from dataset.services import dataset_rbac_result_service, dataset_field_check_service
from dataset.services import dataset_var_parser
from data_source.models import DataSourceModel
from data_source.services.data_source_service import load_data_source_conn_str
from dmplib import redis
from dmplib.constants import LABEL_DETAIL_TABLE_NAME_SUFFIX
from dmplib.db.errors import ColumnNotFoundError, TableNotFoundError
from components.global_utils import validate_key_word
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from dmplib.hug import g
from components import db_engine_transform

from typing import Any, Dict, Optional, Callable
from keywords.external_service import get_keyword_result_format_for_var

logger = logging.getLogger(__name__)


class DatasetBaseService:
    """
    数据集基类
    """

    def __init__(
            self, dataset_model: Optional[Dict[Any, Any]] = None, data_source_model: Optional[Dict[Any, Any]] = None
    ) -> None:
        self.dataset_model = dataset_model
        self.data_source_model = data_source_model
        self.data_limit = 100
        self.max_data_limit = 100000
        self.value_data_limit = 1500
        self.db_engine = DBEngine.RDS.value
        self.execute_time = 0
        if hasattr(self.dataset_model, 'id') and not self.dataset_model.id:
            self.dataset_model.id = seq_id()

    @property
    def transform_cls(self):
        return db_engine_transform.DbTransformService

    def create_tmp_table(
            self, dataset_id: str, origin_columns: list, dataset_type=DatasetType.Sql.value, create_table=True
    ):
        # 获取原数据集字段,其中excel使用的是alias_name
        dataset_id = dataset_id or self.dataset_model.id
        dataset_type = dataset_type or self.dataset_model.type
        if dataset_type == DatasetType.Excel.value:
            old_col_names = dataset_field_repository.get_alias_name_and_origin_col_name_and_origin_table_alias_name(
                dataset_id)
        else:
            old_col_names = dataset_field_repository.get_col_name_and_origin_col_name_and_origin_table_alias_name(
                dataset_id)
        return self.transform_cls(dataset_id).create_tmp_table(origin_columns, old_col_names, create_table)

    def get_erp_datasource_of_data_center(self):
        """
        获取数据服务中心执行数据源
        :return:
        """
        return get_new_erp_datasource_model(self.dataset_model.content)

    def get_erp_data(self, sql, is_need_column_struct=True):
        """
        数据服务中心数据集预览接口
        :param sql:
        :param is_need_column_struct:
        :return:
        """

        sql = self.replace_vars(sql)
        start = time.time()
        disable_procedure = self.dataset_model.disable_procedure if hasattr(self.dataset_model,
                                                                            'disable_procedure') else 0
        rs = get_preview_data_of_sql(sql, self.data_source_model, is_need_column_struct,
                                     disable_procedure=disable_procedure)
        end = time.time()
        self.execute_time = round(end - start, 2)
        data = []
        if "Data" in rs:
            data = rs.get("Data")
        # 返回字段结构
        if "ColumnDataType" in rs:
            struct = rs.get("ColumnDataType")
            for item in struct:
                item['col_type'] = item.get("data_type")
                if self.dataset_model.type == DatasetType.Union.value:
                    item["comment"] = self.get_alias_name(item.get("col_name"))
                item["origin_field_type"] = item.get("data_type")
            return data, struct, rs.get("DataCount", 0), rs.get("IsComplexSql", 1), rs.get("IsNeedProcedure")

    def run_get_data(self):
        """
        运行数据集，产生数据集数据和数据集字段（数据集的子类需要重写）
        :return:
        """
        try:
            # 1、加载数据集内容
            dataset_content = self.load_dataset_content(self.dataset_model.content)

            # 支持本地存储和云端存储
            dataset_data_model = self.run_get_data_or_struct()

            # 3、获取数据集字段
            _dataset_fileds = dataset_field_repository.get_dataset_field(dataset_data_model.dataset_id)
            dataset_fileds = self.update_dataset_fileds(_dataset_fileds)
            # 根据SQL匹配出所有的表名，查询表名的字段备注，用于补充字段结构里面的别名。
            field_comments = dataset_data_model.field_comments if dataset_data_model.field_comments else {}
            fileds = self.get_fields(
                dataset_data_model.new_column_struct, dataset_fileds, field_comments=field_comments
            )

            # 4、数据字段名转换新字段名
            new_data = self.data_col_name_transition(
                dataset_data_model.result_data, dataset_data_model.new_column_struct
            )

        except dataset_var_parser.DatasetVarNotDefinedError as e:
            result = {'check_var_results': e.data}
            return result

        # 5、组装数据格式返回给前端
        result = {
            "id": dataset_data_model.dataset_id,
            "data": new_data,
            "head": fileds,
            "field": dataset_field_service.get_field_group(fileds),
            "create_table_sql": dataset_data_model.create_table_sql,
            "tmp_table_name": dataset_data_model.tmp_table_name,
            "count": dataset_data_model.total_count,
            # 组合数据集的属性
            'replace_sql': dataset_data_model.replace_sql,
            'source_dataset_ids': dataset_data_model.source_dataset_ids,
            'is_complex': dataset_data_model.is_complex,
            'execute_time': self.execute_time,
            'bind_source_id': getattr(dataset_data_model, 'bind_source_id', ''),
            'is_need_procedure': getattr(dataset_data_model, 'is_need_procedure', 2),
            'running_way': getattr(dataset_data_model, 'running_way', None),
            'org_column_struct': dataset_data_model.column_struct
        }
        # 有dataset_id才需要进行比对，比对单图和高级字段引用是否被删除和修改
        dataset_id = dataset_content.get('dataset_id') if dataset_content.get('dataset_id') else self.dataset_model.id
        if dataset_id:
            dataset_vars = None
            if (
                    self.dataset_model.type == DatasetType.Api.value
                    and self.dataset_model.edit_mode == DatasetEditMode.Sql.value
            ):
                dataset_vars = self.dataset_model.var_content
            result['check_results'] = dataset_field_check_service.DatasetFieldCompareService(
                dataset_id, fileds, dataset_vars
            ).compare()
        else:
            result['check_results'] = []

        return result

    def run_get_data_or_struct(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（数据集的子类需要重写）
        :return: dataset.models.DatasetDataModel
        """
        pass

    def run_get_data_or_struct_local(self):
        """
        本地存储模式根据sql语句获取数据和结构，测试运行创建临时表（数据集的子类需要重写）
        :return: dataset.models.DatasetDataModel
        """
        pass

    def run_get_data_count(self):
        """
        运行数据集，产生总条数（数据集的子类需要重写）
        :return:
        """
        pass

    def run_get_field_values(self, field_name):
        """
        运行数据集，获取字段的所有值（数据集的子类需要重写）
        :return:
        """
        pass

    @staticmethod
    def load_dataset_content(content):
        if isinstance(content, dict):
            return content
        try:
            return json.loads(content)
        except json.JSONDecodeError as e:
            raise Exception('数据集内容解析错误：' + str(e))

    @staticmethod
    def validation_sql(sql, can_order_by=True):
        """
        验证sql语句
        :param sql:
        :return:
        """
        if not sql or not sql.strip():
            raise UserError(message='请选择表')

        # 去掉注释，缩进sql, 并返回;号条数
        sql, semicolon_num = DatasetBaseService.remove_comments(sql)

        sql_list = sql.strip().split(';')
        if '' in sql_list:
            sql_list.remove('')
        # 判断sql是否单条还是多条，根据分号
        if semicolon_num > 1:
            raise UserError(message='仅支持执行单条sql语句')

        # 判断sql里是否有order by

        pattern = 'from.*?order\s+by\s'

        sql_regex = re.compile(pattern, re.IGNORECASE)
        # 暂时 支持 select * 写法，后期可能会屏蔽，保留代码
        # 检测select字段中是否有 * 号
        # parsed = sqlparse.parse(sql)
        # for token in parsed[0].tokens:
        #     if isinstance(token, IdentifierList):
        #         for identifier in token.get_identifiers():
        #             if identifier.value == '*':
        #                 raise UserError(message='不支持select * ，请指定字段名。')
        #     if token.value == '*':
        #         raise UserError(message='不支持select * ，请指定字段名。')
        #     if token.ttype is Keyword:  # from
        #         break
        if sql_regex.findall(sql) and not can_order_by:
            raise UserError(message='编辑的sql会当做子查询，sql语句里不允许存在order by，若需要数据排序，请在单图里实现')

        # 去除结尾的;  因为有拼接操作
        return sql.strip().strip(';')

    def create_table(self, sql):
        result = self.api.get_sql_list(sql, is_download="0")
        # self.create_table(result)
        self.columns = [self.replace_parenthesis(struct.get("col_name")) for struct in result.get("data").get("struct")]

    def mysql_limit(self, sql, data_limit):
        # limit 100 数据
        try:
            # (limit\s+[\d|,|\s]+)\s*$
            # (limit\s+[\d|,|\s]+)[\s|\)]*$
            # (limit\s+[\d|,|\s]+[;|\)]*)\s*$
            # (limit\s+[\d|,|\s]+[\s|\)|;]*)\s*$
            limit_str = re.findall(r'(limit\s+[\d|,|\s]+[\s|\)|;]*)\s*$', sql, flags=re.I)
            if limit_str:
                limit_num = re.subn(r'limit', ' ', limit_str[0], flags=re.I)[0].strip()
                limit_num = limit_num.replace(';', '')
                limit_num_split = limit_num.split(",")
                if len(limit_num_split) > 1:
                    if int(limit_num_split[1]) < data_limit:
                        data_limit = int(limit_num_split[1])
                    data_limit = [int(limit_num_split[0]), data_limit]
                else:
                    if int(limit_num) < data_limit:
                        data_limit = limit_num
                sql = re.subn(limit_str[0] + '$', ' ', sql, flags=re.I)[0]
        except Exception:
            sql = sql.strip().strip(';')
        return sql, data_limit

    @staticmethod
    def remove_comments(sql):
        """
        去除sql注释
        :return: sql
        """
        sql, semicolon_num = remove_comment(sql)
        # 下面的正则无法解决引号内注释问题
        # sql = re.sub(re.compile("/\*.*?\*/", re.DOTALL), "", sql)  # 去除类似  /*注释 */
        # sql = re.sub(re.compile("\-\-[^\n]*"), "", sql)  # 去除类似  --注释
        return sql, semicolon_num

    def get_task_data(self, task_id: str) -> Dict[str, int]:
        """
        获取数据集数据
        :param task_id:
        :return:
        """
        data = redis.conn().get_data(task_id)
        if data:
            redis.conn().del_data(task_id)
            return data
        else:
            return {"data": [], "head": [], "field": [], "count": 0, "invalid_line": 0, "invalid_count": 0, "status": 0}

    def export_dataset(self, task_id, dataset_id, version_id=None, file_type=None):
        """
        导出数据集数据
        :param task_id:
        :param dataset_id:
        :param version_id:
        :return:
        """
        excel_export_model = DatasetExcelExportModel()
        excel_export_model.status = 1
        tmp_upload_path = ''
        name = ''
        try:
            dataset = dataset_repository.get_dataset(dataset_id)
            if not dataset:
                raise UserError(message="数据集不存在")

            # 获取数据集表名和字段
            if version_id:
                version_data = repository.get_data(
                    "dataset_version", {"id": version_id}, ["table_name", "field_struct"]
                )
                data_table_name = version_data.get("table_name")
                fields = json.loads(version_data.get("field_struct"))
            else:
                data_table_name = dataset['table_name']
                if dataset.get('type') == DatasetType.Label.value:
                    data_table_name = data_table_name + "_" + LABEL_DETAIL_TABLE_NAME_SUFFIX
                if not repository.check_data_db_table_is_exist(data_table_name, dataset):
                    raise UserError(message='数据表数据未生成')
                # 获取数据集字段名
                fields = dataset_field_repository.get_dataset_field(
                    dataset.get('id'), {'type': DatasetFieldType.Normal.value}
                )
            query_fields = []
            for field in fields:
                query_fields.append(field.get('col_name'))

            # 获取数据集权限过滤后数据
            data_result, filter_amount = dataset_rbac_result_service.get_dataset_table_data(
                dataset_id, fields, data_table_name, query_fields, limit=100000
            )

            # 获取本地文件路径
            name = dataset.get('name')
            # 替换url中需要特殊处理的字符 +、空格、/、?、%、#、&、=
            name = re.sub(r'[#+/?%#&=*()|:<>，。、；…‘;,. ~！：【】@￥\[\]（）/"“？’$《》—]', '_', name)

            file_suffix = DatasetFileType.Csv.value if file_type == 'csv' else DatasetFileType.Xls.value
            file_name = name + '.' + file_suffix

            tmp_upload_folder = os.path.join(
                os.path.join(
                    os.path.join(
                        os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), '../../')), "runtime"
                    ),
                    "tmp_download",
                ),
                seq_id(),
            )
            if not os.path.exists(tmp_upload_folder):
                os.makedirs(tmp_upload_folder)
            tmp_upload_path = os.path.join(tmp_upload_folder, file_name)

            if file_type == 'csv':
                self.write_csv(tmp_upload_path, fields, data_result)
            else:
                self.write_excel(name, tmp_upload_path, fields, data_result)

            # 上传oss
            oss_file_url = OSSFileProxy().upload(open(tmp_upload_path, 'rb'), root='download_file', file_name=file_name)

            excel_export_model.oss_url = oss_file_url
            excel_export_model.name = name

        except Exception as ex:
            if not isinstance(ex, (ColumnNotFoundError, TableNotFoundError)):
                logger.exception(ex)
            excel_export_model.status = 1
            excel_export_model.error_msg = "导出" + name + "数据集失败，错误内容：" + str(ex)
        finally:
            redis.conn().set_data(task_id, excel_export_model.get_dict())
            if os.path.exists(tmp_upload_path):
                os.remove(tmp_upload_path)

    @staticmethod
    def write_excel(name, file_path, fields, data_result):
        """
        excel
        :param file_path:
        :param name:
        :param fields:
        :param data_result:
        :return:
        """
        cols = []
        file = xlwt.Workbook()
        # excel对sheet 名称有一定限制，如名称不符合要求，给默认值
        try:
            table = file.add_sheet(name)
        except:
            table = file.add_sheet("dmp")
        # 表头
        for i, field in enumerate(fields):
            table.write(0, i, field.get('alias_name') if field.get('alias_name') else field.get('col_name'))
            table.col(i).width = 6666
            cols.append(field.get('col_name'))
        # 数据
        for r, data in enumerate(data_result):
            r += 1
            for c, col in enumerate(cols):
                new_data = data.get(col)
                if isinstance(new_data, datetime):
                    new_data = new_data.strftime('%Y-%m-%d %H:%M:%S')
                if isinstance(new_data, date):
                    new_data = new_data.strftime('%Y-%m-%d')
                table.write(r, c, new_data)
        # 保存
        file.save(file_path)

    @staticmethod
    def write_csv(file_path, fields, data_result):
        """
        csv
        :param file_path:
        :param fields:
        :param data_result:
        :return:
        """
        cols = []
        headers = []
        for field in fields:
            headers.append(field.get('alias_name') if field.get('alias_name') else field.get('col_name'))
            cols.append(field.get('col_name'))
        with open(file_path, 'w', encoding='utf-8-sig', newline='') as csv_file:
            spam_writer = csv.writer(csv_file)
            spam_writer.writerow(headers)
            data_list = []
            for data in data_result:
                for col in cols:
                    new_data = data.get(col)
                    if isinstance(new_data, datetime):
                        new_data = new_data.strftime('%Y-%m-%d %H:%M:%S')
                    if isinstance(new_data, date):
                        new_data = new_data.strftime('%Y-%m-%d')
                    data_list.append(new_data)
                spam_writer.writerow(data_list)
                data_list = []

    @staticmethod
    def replace_parenthesis(col_name):
        if col_name:
            return col_name.replace('(', '_').replace(')', '_')
        else:
            return col_name

    def set_field_data(self, data_list):
        """
        设置字段名：带有小括号的字段名替换下划线
        :param data_list:
        :return:
        """
        for data in data_list:
            for k, v in data.items():
                new_col_name = self.replace_parenthesis(k)
                if k != new_col_name:
                    data[new_col_name] = data.pop(k)
        return data_list

    @staticmethod
    def update_dataset_fileds(dataset_fileds):
        """
        更改dataset_fileds结构
        :param dataset_fileds:
        [{'col_name': 'col1', 'alias_name': '别名'， ‘inspection_rules’： [{"operator": "!=", "col_value": "0"}]}]
        :return:
        {'col1': {'alias_name': '别名'， ‘inspection_rules’： [{"operator": "!=", "col_value": "0"}]}}
        """
        _dict = {}
        dataset_fileds = dataset_fileds or []
        for field in dataset_fileds:
            _dict[field.get('col_name')] = field
        return _dict

    def get_fields(self, columns, dataset_fileds, field_comments={}):
        """
        获取数据集字段
        :param columns:
        :param dataset_fileds:
        :param field_comments:
        :return dict
        """
        # 区分视图模式和sql模式
        relation = self.dataset_model.edit_mode == DatasetEditMode.Relation.value or False
        fields = []
        alias_name_keys_count = {}

        for description in columns:
            if description.get("comment"):
                alias_name = description.get("comment")
            elif field_comments.get(description.get("old_col_name")):
                alias_name = field_comments.get(description.get("old_col_name"))
            else:
                alias_name = description.get("old_col_name")
            field = {
                "origin_col_name": description.get("origin_col_name"),
                "origin_table_id": description.get("origin_table_id"),
                "origin_table_comment": description.get("origin_table_comment"),
                "origin_table_name": description.get("origin_table_name"),
                "origin_table_alias_name": description.get("origin_table_alias_name"),
                "col_name": description.get("col_name"),
                "origin_field_type": description.get("origin_field_type"),
                "alias_name": description.get("alias_name")
                if (description.get("alias_name") and relation)
                else alias_name,
                "visible": 1,
                "format": "",
                "note": description.get("note") or field_comments.get(description.get("origin_col_name")),
                "external_id": description.get("code", '') or description.get("external_id", ''),
                "origin_dim_type": description.get("dim_type", '') or description.get("origin_dim_type", '')
            }
            #  存在重名的中文字段 后面（1）来标记
            alias_name = field.get('alias_name')
            alias_name_keys_count[alias_name] = alias_name_keys_count[alias_name] + 1 if alias_name in alias_name_keys_count else 0
            if alias_name_keys_count[alias_name] > 0:
                field['alias_name'] = f'{alias_name}({alias_name_keys_count[alias_name]})'
            col_type = (
                description.get("data_type")[0: description.get("data_type").find("(")]
                if description.get("data_type") and description.get("data_type").find("(") != -1
                else description.get("data_type")
            )

            field["field_group"] = db_engine_transform.get_dmp_field_group(col_type)
            field["data_type"] = db_engine_transform.get_dmp_data_type(col_type)

            _field = dataset_fileds.get(description.get("col_name"))
            if _field:
                # 添加巡检规则
                field = self.add_inspection_rules(field, _field, relation)
            fields.append(field)
        return fields

    @staticmethod
    def add_inspection_rules(field, _field, relation):

        # 添加巡检规则
        try:
            field["inspection_rules"] = json.loads(_field.get("inspection_rules"))
        except:
            field["inspection_rules"] = []
        # 替换别名/可见/字段类型
        keys = ["visible", "data_type", "field_group", "note"]
        # 非视图模式下，需要使用数据库保存的alias_name
        if not relation:
            keys.append("alias_name")
        for k in keys:
            field[k] = _field.get(k) if _field.get(k) is not None else field.get(k)

        return field

    @staticmethod
    def data_col_name_transition(datas, new_struct):
        """
        数据字段名转换（字段名从新生成后，原有数据中的key需要替换为新的字段名）
        :param datas:
        :param new_struct:
        :return:
        """
        data_keys = []
        if datas and len(datas) > 0:
            data_keys = datas[0].keys()
        new_data = []
        for data in datas:
            new_dict = {}
            for struct in new_struct:
                if struct.get('old_col_name') in data_keys:
                    new_dict[struct.get('col_name')] = data.get(struct.get('old_col_name'))
                # 不需要转换的情况
                elif struct.get('col_name') in data_keys:
                    new_dict = data
            new_data.append(new_dict)
        return new_data

    @staticmethod
    def deal_relation_mode_data(relation_content):
        """
        处理relation_content中的数据
        :return:
        """
        if isinstance(relation_content, str):
            try:
                relation_content = json.loads(relation_content)
            except json.JSONDecodeError as e:
                raise Exception('数据集内容解析错误：' + str(e))
        node_data = relation_content.get("nodeDataArray")
        # 为了匹配link_data中的table_name
        nodedata_dict = {row.get('id'): row.get('name') for row in node_data}
        node_alias_data_dict = {row.get('id'): row.get('alias_name') for row in node_data}
        # link_datas变成数组
        new_link_datas = []
        for link_data in relation_content.get('linkDataArray'):
            join_fields = []
            if link_data.get('join_fields'):
                for join_field in link_data.get('join_fields'):
                    if isinstance(join_field, dict):
                        join_fields.append(JoinField(**(join_field)))
            link_data['join_fields'] = join_fields
            link_data['from_table_name'] = nodedata_dict.get(link_data.get('from_id'))
            link_data['from_alias_name'] = node_alias_data_dict.get(link_data.get('from_id'))
            link_data['to_table_name'] = nodedata_dict.get(link_data.get('to_id'))
            link_data['to_alias_name'] = node_alias_data_dict.get(link_data.get('to_id'))
            new_link_datas.append(LinkData(**(link_data)))
        # 单表的情况下，nodeDatas有值，link_datas中没有值，需要写一条link_datas的记录
        if not new_link_datas and node_data:
            link_data = dict()
            link_data["from_id"] = node_data[0].get("id")
            link_data["from_table_name"] = node_data[0].get("name")
            new_link_datas.append(LinkData(**(link_data)))
        return new_link_datas, relation_content.get("nodeDataArray")

    @staticmethod
    def transfer_node_fields(node_datas):
        """
        视图模式请求api获取结构
        :param node_datas:
        :return:
        """
        # 所有的columns
        columns = []
        for node in node_datas:
            for field in node.get("fields"):
                field["table_name"] = node.get("name")
                field["origin_table_id"] = node.get("id")
                field["origin_table_name"] = node.get("name")
                field["origin_table_comment"] = node.get("comment")
                field["origin_table_alias_name"] = node.get("alias_name")
                if 'col_name' not in field:
                    field['col_name'] = field.get('name')
                if 'data_type' not in field:
                    field['data_type'] = field.get('type')
                field["origin_field_type"] = field.get("type")
                field["code"] = field.get("code")
                columns.append(field)
        return columns

    def get_query_structure_view(self, column_datas, link_datas, filter_content=None, with_limit=True):
        """
        获取查询结构
        :param column_datas:
        :param link_datas:
        :param filter_content:
        :return:
        """
        query_structure = QueryStructure()

        for column_data in column_datas:
            select = Select()
            select.obj_name = (
                column_data.get("origin_table_alias_name")
                if column_data.get("origin_table_alias_name")
                else column_data.get('table_name')
            )
            select.prop_name = column_data.get('name')
            select.alias = column_data.get("col_name")
            query_structure.select.append(select)

        table = transform_object(link_datas)
        query_structure.object = table

        if with_limit:
            limit = Limit()
            limit.row = self.data_limit
            query_structure.limit = limit

        # 加入数据集过滤器
        if filter_content:
            if isinstance(filter_content, str):
                filter_content = json.loads(filter_content)
            filter_content = transform_filter_content(filter_content, link_datas)
            where = transform_filter_where(filter_content)
            query_structure.where.append(where)
        return query_structure

    @staticmethod
    def transfer_link_data(link_datas):
        return link_datas

    def get_relation_edit_mode_sql(self, dataset_id, with_limit=True):
        link_datas, node_data = transfer_link_data_new(self.dataset_model.relation_content)
        if not node_data:
            raise UserError(message="请至少拖入一个表！")
        columns = self.transfer_node_fields(node_data)

        # 组合数据集视图模式使用
        link_datas = self.transfer_link_data(link_datas)

        # 2、测试运行创建表
        # （明源特殊api返回的结构是mysql字段类型, 这里与sql模式不同的原因是需要提前获取hash col_name作为as别名)
        if self.dataset_model.type == "UNION" or self.data_source_model.type == DataSourceType.MysoftShuXin15.value or is_local_storage():
            create_table = False
        else:
            create_table = True
        tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(dataset_id, columns,
                                                                             create_table=create_table)

        query_structure = self.get_query_structure_view(
            new_struct, link_datas, self.dataset_model.filter_content, with_limit=with_limit
        )
        sql = self.encode_query(query_structure)
        return sql, (tmp_table_name, new_struct, create_table_sql)

    def get_query_sql(self, dataset_id: str = '', with_limit=True):
        return self._get_query_sql_or_metadata(dataset_id, with_limit=with_limit)[0]

    def get_query_sql_and_metadata(self, dataset_id: str = '', with_limit=True):
        return self._get_query_sql_or_metadata(dataset_id, with_limit=with_limit)

    def _get_query_sql_or_metadata(self, dataset_id: str = '', with_limit=True):
        if not (
                self.dataset_model.type == DatasetType.Sql.value or self.dataset_model.type == DatasetType.Union.value or self.dataset_model.type == DatasetType.Api.value):
            raise UserError(message='错误，该数据集不是SQL、Union、API数据集')

        meta_data = tuple()
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            sql, meta_data = self.get_relation_edit_mode_sql(dataset_id or self.dataset_model.id, with_limit=with_limit)
            if self.db_engine in (DBEngine.MSSQL.value,):
                limit_index = sql.find('LIMIT')
                if limit_index != -1:
                    sql = sql[:limit_index]
        else:
            if isinstance(self.dataset_model.content, dict):
                sql = self.dataset_model.content.get('sql')
            else:
                sql = json.loads(self.dataset_model.content).get('sql')

        return (sql, meta_data)

    @staticmethod
    def data_processing(datas):
        """
        数据加工，解决前端无法显示Decimal的问题
        :param datas:
        :return:
        """
        result_data = []
        for item in datas:
            tmp_items = {}
            for item_key, item_value in item.items():
                item_value = (
                    item_value.normalize()
                    if isinstance(item_value, Decimal) and str(item_value) == '0E-10'
                    else item_value
                )
                # Decimal 0 不处理的话前端显示None
                if item_value in [Decimal(0)]:
                    item_value = 0
                tmp_items[item_key] = str(item_value) if isinstance(item_value, Decimal) else item_value
            result_data.append(tmp_items)
        return result_data

    def encode_query(self, query_structure: QueryStructure):
        return encode_query(query_structure, self.db_engine)

    def generate_sql_dataset_count_sql(self):
        if not self.dataset_model.type != DatasetType.Sql:
            raise UserError(message='只支持SQL数据集调用')
        query_structure = self.generate_sql_dataset_count_query_structure()
        return self.encode_query(query_structure)

    def get_dataset_content_sql(self):
        content = self.load_dataset_content(self.dataset_model.content)
        sql = content.get('sql')
        return sql

    def generate_sql_dataset_count_query_structure(self):
        if self.dataset_model.type not in (DatasetType.Sql.value, DatasetType.Api.value, DatasetType.Union.value):
            raise UserError(message='视图模式目前只支持SQL、API、组合数据集')

        query_structure = QueryStructure()
        select = Select()
        select.alias = 'total_count'
        select.func = 'count'
        prop = Prop()
        prop.value = 1
        select.props.append(prop)
        query_structure.select.append(select)

        link_datas = []
        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            if self.data_source_model and self.data_source_model.type == DataSourceType.API.value:
                link_datas, node_data = self.deal_relation_mode_data(self.dataset_model.relation_content)
            else:
                link_datas, node_data = transfer_link_data_new(self.dataset_model.relation_content)
            table = transform_object(link_datas)
            query_structure.object = table
        else:
            content = self.load_dataset_content(self.dataset_model.content)
            table = Object()
            sql = self.get_dataset_content_sql()
            if sql:
                sql = self.validation_sql(sql)
                validate_key_word(sql, exclude=["if"])
                table.name = "({sql})".format(sql=sql)
                table.alias = "a"
            else:
                table.name = content.get('table_name', '')
            query_structure.object.append(table)

        # 加入数据集过滤器
        if self.dataset_model.filter_content:
            if isinstance(self.dataset_model.filter_content, str):
                filter_content = json.loads(self.dataset_model.filter_content)
                filter_content = transform_filter_content(filter_content, link_datas)
                where = transform_filter_where(filter_content)
            else:
                filter_content = transform_filter_content(self.dataset_model.filter_content, link_datas)
                where = transform_filter_where(filter_content)

            query_structure.where.append(where)
        return query_structure

    def replace_dataset_sql_vars(self, sql: str = '', var_content: list = None):
        var_content = var_content or self.dataset_model.var_content or []
        origin_sql = sql
        if not origin_sql:
            content = self.dataset_model.content
            if isinstance(content, str):
                content = json.loads(content)
            origin_sql = content.get('sql')

        return self.replace_dataset_sql_var_id_as_values(origin_sql, var_content)

    @staticmethod
    def replace_dataset_sql_var_id_as_values(sql, var_data_list: list):
        var_ids = dataset_var_parser.DatasetVarParser.get_var_names_from_sql_str(sql)
        var_values = {}
        for var in var_data_list:
            if var['id'] not in var_ids:
                continue
            if not var.get('value') and var.get('default_value_type', 2) == 1:
                var['default_value'] = get_keyword_result_format_for_var(var)
            var.setdefault('value', var.get("default_value", ""))
            if var.get("value") and isinstance(var.get("value"), list) and var.get("value_type") == 2:  # 单值
                var['value'] = var.get("value")[0]
            dataset_var_model = Var(**var)
            replaced_sql_value = dataset_var_service.parse_dataset_var(dataset_var_model)
            var_values[var['id']] = replaced_sql_value

        replaced_sql = dataset_var_parser.DatasetVarParser.replace_sql_vars_by_data(sql, var_values)
        return replaced_sql

    def replace_vars(self, sql):
        var_content = self.dataset_model.var_content or []
        if len(var_content) > 0:
            return self.replace_dataset_sql_vars(sql, var_content)
        return sql

    def transform_object_of_mssql_table_name(self, table_name):
        return [Object(name=table_name, alias="", join_typ="", ref_clause="")]

    def generate_mssql_sql(self, query_fields, fields, where_obj):
        query_structure = QueryStructure()
        fields = [item for item in fields if item.get("col_name") in query_fields]
        for column_data in fields:
            select = Select()
            if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
                select.obj_name = self.dataset_model.table_name
            else:
                select.obj_name = (
                    column_data.get("origin_table_alias_name")
                    if column_data.get("origin_table_alias_name")
                    else column_data.get('table_name')
                )
            select.prop_name = column_data.get('col_name')
            # select.alias = column_data.get("col_name")
            query_structure.select.append(select)

        query_structure.object = self.transform_object_of_mssql_table_name(self.dataset_model.table_name)
        # 加入数据集过滤器
        if where_obj:
            query_structure.where = where_obj
        query_structure.limit = None

        sql = self.encode_query(query_structure)
        return sql

    @staticmethod
    def relace_where_sql_vars(where_obj, vars_data_list, is_api=False):
        if where_obj and where_obj.conditions:
            for condition in where_obj.conditions:
                DatasetBaseService.relace_where_sql_vars(condition, vars_data_list, is_api)
        if where_obj.left:
            left_value_type = getattr(where_obj.left, 'value_type')
            if left_value_type == '变量':
                where_obj.left.value \
                    = DatasetBaseService.replace_dataset_sql_var_id_as_values(where_obj.left.value, vars_data_list)
                if is_api and where_obj.left.value.startswith("'"):
                    where_obj.left.value = where_obj.left.value[1:len(where_obj.left.value) - 1]
        if not where_obj.right or not where_obj.right.value or where_obj.right.value_type != '变量':
            return
        value = DatasetBaseService.replace_dataset_sql_var_id_as_values(where_obj.right.value, vars_data_list)
        # 数据集的结构参数 如果是like 则需要对文本型值进行处理 变量存储的是 %{xxx}% ,原始替换后为 %'xx'% ,需要改写成 '%xx%'
        if where_obj.operator.upper() == SqlWhereOperator.Like.value or where_obj.operator.upper() == SqlWhereOperator.Nlike.value:
            if value.startswith("%'") and value.endswith("'%"):
                value = value[2:len(value) - 2]
                value = "'%%%s%%'" % value
            elif value.startswith("'") and value.endswith("'%"):
                value = value[1:len(value) - 2]
                value = "'%s%%'" % value
            elif value.startswith("%'") and value.endswith("'"):
                value = value[2:len(value) - 1]
                value = "'%%%s'" % value
        where_obj.right.value = value
        if not is_api:  # 替换between和in 场景 只能是api需要适配，其他不需要
            return
            # 结构体请求的字符串都会被api强制加个 单引号 变量里面也会给加单引号会导致 请求报错，所以，结构体请求需要对字符串的单引号去除
        if value.startswith("'"):
            where_obj.right.value = value[1:len(value) - 1]
        convert_flag = False
        if where_obj.operator.upper() == SqlWhereOperator.Between.value:  # between 拆解成 >= and <=
            where_obj_convert = query_structure_helper.convert_between_where(where_obj)
            where_obj.conditions = where_obj_convert.conditions
            convert_flag = True
        elif where_obj.operator.upper() == SqlWhereOperator.In.value:
            where_obj_convert = query_structure_helper.convert_in_where_2(where_obj)
            where_obj.conditions = where_obj_convert.conditions
            convert_flag = True
        elif where_obj.operator.upper() == SqlWhereOperator.Notin.value:
            where_obj_convert = query_structure_helper.convert_in_where_2(where_obj)
            where_obj.conditions = where_obj_convert.conditions
            convert_flag = True
        if convert_flag:  # 转换过的需要重置
            where_obj.operator = ""
            where_obj.left = Prop()
            where_obj.right = Prop()


def transfer_link_data_new(relation_content):
    """
    处理relation_content中的数据
    :return:
    """
    if isinstance(relation_content, str):
        try:
            relation_content = json.loads(relation_content)
        except json.JSONDecodeError as e:
            raise UserError(message='数据集内容解析错误：' + str(e))

    node_data = relation_content.get("nodeDataArray")
    # 为了匹配link_data中的table_name
    nodedata_dict = {row.get('id'): row.get('name') for row in node_data}
    node_alias_data_dict = {row.get('id'): row.get('alias_name') for row in node_data}
    # link_datas变成数组
    new_link_datas = []
    for r in relation_content.get('linkDataArray'):
        link_data = {}
        link_data.update(r)
        join_fields = []
        if r.get('join_fields'):
            for join_field in r.get('join_fields'):
                join_fields.append(JoinField(**(join_field)))

        link_data['join_fields'] = join_fields
        link_data['from_table_name'] = nodedata_dict.get(r.get('from_id'))
        link_data['from_alias_name'] = node_alias_data_dict.get(r.get('from_id'))
        link_data['to_table_name'] = nodedata_dict.get(r.get('to_id'))
        link_data['to_alias_name'] = node_alias_data_dict.get(r.get('to_id'))
        new_link_datas.append(LinkData(**(link_data)))
    # 单表的情况下，nodeDatas有值，link_datas中没有值，需要写一条link_datas的记录
    if not new_link_datas and node_data:
        link_data = dict()
        link_data["from_id"] = node_data[0].get("id")
        link_data["from_table_name"] = node_data[0].get("name")
        new_link_datas.append(LinkData(**(link_data)))
    return new_link_datas, node_data


def record_execute_time():
    def wrapper1(func):
        def wrapper2(*args, **kwargs):
            start = time.time()
            data_or_struct = func(*args, **kwargs)
            end = time.time()
            if len(args) > 0:
                dataset_model = args[0]
                if hasattr(dataset_model, "execute_time"):
                    dataset_model.execute_time = round(end - start, 2)
            return data_or_struct

        return wrapper2

    return wrapper1
