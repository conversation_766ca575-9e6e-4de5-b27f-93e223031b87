# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    class
    Created by guozh on 2018/05/03.
"""
from sqlalchemy import text

from base.enums import DatasetEditMode, DBEngine
from dataset.models import DatasetDataModel
from dataset.common import dataset_data_helper
from dataset.services.dataset_base_service import record_execute_time
from dataset.services.dataset_datahub_service import DatasetBaseService
from components.db_engine_transform import MssqlToMysqlTransformService
from dmplib.utils import sql_util
from dmplib.utils.strings import seq_id
from data_source.repositories.mssql_data_source_repository import get_result_data, get_mssql_db
from components.orm import OrmDb, MssqlDescription


class DatasetMssqlService(DatasetBaseService):
    """
    mssql 数据集业务类
    """

    def __init__(self, dataset_model=None, data_source_model=None):
        super().__init__(dataset_model, data_source_model)
        self.db_engine = DBEngine.MSSQL.value

    @property
    def transform_cls(self):
        return MssqlToMysqlTransformService

    def run_get_data_or_struct(self):
        """
        根据sql语句获取数据和结构，测试运行创建临时表（重写父类方法）
        :return: dataset.models.DatasetDataModel
        """
        # 1、加载数据集内容
        dataset_content = self.load_dataset_content(self.dataset_model.content)

        # 2、测试运行创建表
        if dataset_content.get("dataset_id"):
            dataset_id = dataset_content.get("dataset_id")
        elif getattr(self.dataset_model, "id"):
            dataset_id = getattr(self.dataset_model, "id")
        else:
            dataset_id = seq_id()
        self.dataset_model.id = dataset_id

        sql, meta_data = self.get_query_sql_and_metadata(dataset_id)
        sql = self.replace_vars(sql)
        sql = self.validation_sql(sql)
        sql_util.validate_key_word(sql)

        # 3、根据sql语句获取总数
        count_sql = " select count(1) as total_count  from ( " + sql + " ) s "
        count_result = self.get_result_data(self.data_source_model, count_sql)
        total_count = count_result[0].get('total_count', 0) if count_result else 0

        # 4、根据sql语句获取数据和字段结构
        if total_count and int(total_count) > self.data_limit:
            sql = " select top {limit} * from ( {sql} ) s ".format(sql=sql, limit=self.data_limit)
        result_data, columns = self.get_data_or_columns(sql)

        if self.dataset_model.edit_mode == DatasetEditMode.Relation.value:
            tmp_table_name, new_struct, create_table_sql = meta_data
        else:
            # 5、测试运行创建表
            tmp_table_name, new_struct, create_table_sql = self.create_tmp_table(dataset_id, columns)

        dataset_data_model = DatasetDataModel()
        dataset_data_model.dataset_id = dataset_id
        dataset_data_model.result_data = result_data
        dataset_data_model.column_struct = columns
        dataset_data_model.total_count = total_count
        dataset_data_model.tmp_table_name = tmp_table_name
        dataset_data_model.new_column_struct = new_struct
        dataset_data_model.create_table_sql = create_table_sql
        return dataset_data_model

    @record_execute_time()
    def get_data_or_columns(self, sql):
        """
        根据sql语句获取数据和结构
        :return:
        """
        with get_mssql_db(self.data_source_model) as db:
            cursor_result = db.execute(text(sql))

            # 获取数据结构
            columns = [MssqlDescription(*col) for col in list(cursor_result.cursor.description)]
            structs = []
            for column in columns:
                struct = {
                    "col_name": column.name,
                    "data_type": column.col_type,
                    "comment": column.alias_name,
                    "origin_field_type": column.col_type,
                }
                structs.append(struct)
            result_data = OrmDb.data_format(cursor_result)
            result_data = dataset_data_helper.data_processing(result_data)
            return result_data, structs

    def run_get_data_count(self):
        """
        运行数据集，产生数据集数据和数据集字段
        :return:
        """
        count_sql = self.generate_sql_dataset_count_sql()
        count_result = self.get_result_data(self.data_source_model, count_sql)
        total_count = count_result[0].get('total_count', 0) if count_result else 0
        return total_count

    def run_get_field_values(self, field_name):

        """
        运行数据集，获取字段的所有值
        :return:
        """
        sql = self.validation_sql(self.get_query_sql(), can_order_by=False)
        sql_util.validate_key_word(sql)
        group_sql = " select top {max_data_limit}  {field_name} from ( {sql} ) a group by {field_name}  ".format(
            field_name=field_name, sql=sql, max_data_limit=str(self.value_data_limit)
        )
        result_data = self.get_result_data(self.data_source_model, group_sql)
        return result_data

    @staticmethod
    def get_result_data(data_source_model, sql):
        return get_result_data(data_source_model, sql)
