#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

from base import repository
from base.enums import DatasetVersionType, MysoftNewERPDataBaseType, DataCenterAction
from components.utils import split_array
from dmplib import config
from dmplib.saas.project import get_db, get_data_db
from dmplib.utils.errors import UserError
from dmplib.hug import g
import logging
from components.data_center_api import get_new_erp_datasource_model, get_data_by_sql


class CleanDatasetVersion:
    """
    清理数据集多余版本
    """

    def __init__(self, dataset_id, version_action_type):
        self.dataset_id = dataset_id
        self.version_action_type = version_action_type

    def clean(self):
        dataset_history_version = repository.get_data_order_by(
            "dataset_version",
            {
                "type": self.version_action_type,
                "dataset_id": self.dataset_id,
                "version_type": DatasetVersionType.HISTORY.value,
            },
            multi_row=True,
            order_by=[("created_on", "ASC")],
        )
        max_version_replications = (
            5
            if not config.get("DatasetConfig.max_version_replications")
            else config.get("DatasetConfig.max_version_replications")
        )
        try:
            max_version_replications = int(max_version_replications)
        except ValueError:
            raise UserError(message="最大版本数应该填写数字")
        # 保留天数版本
        max_day_replications = (
            2
            if not config.get("DatasetConfig.max_day_replications")
            else config.get("DatasetConfig.max_day_replications")
        )
        try:
            max_day_replications = int(max_day_replications)
        except ValueError:
            raise UserError(message="数据集版本保留天数应该填写数字")

        # 还没有达到删除条件
        if not dataset_history_version or (len(dataset_history_version) < max_version_replications):
            return

        # 剔除符合按天保留数据集
        delete_versions = self.filter_delete_version(
            dataset_history_version, max_version_replications, max_day_replications
        )
        if not delete_versions:
            return

        # 使用事务处理
        with get_db() as db, get_data_db() as data_db:
            try:
                delete_ids = []
                # 删除记录
                delete_sql = "delete from dataset_version where id in %(ids)s"
                for version in delete_versions:
                    # 判断是否符合按天保留
                    delete_ids.append(version.get("id"))
                    self.delete_version_data(data_db, version)
                db.exec_sql(delete_sql, {"ids": delete_ids})
                data_db.commit()
            except Exception as e:
                db.rollback()
                data_db.rollback()
                raise UserError(message="删除数据集历史版本错误:" + str(e))

    @staticmethod
    def delete_version_data(data_db, version):
        # 删除data库，数据
        drop_sql = " DROP TABLE IF EXISTS {table_name} ;  ".format(table_name=version.get("table_name"))
        data_db.exec_sql(drop_sql, commit=False)

    def filter_delete_version(self, dataset_history_version, max_version_replications: int, max_day_replications: int):
        # 最大版本数之前的需要删除（按最大版本数规则）
        need_delete_versions = dataset_history_version[:-max_version_replications]

        # 0表示不开启按天保留
        if max_day_replications <= 0:
            return need_delete_versions

        # 符合按天保留（不包含已经在最大版本数的）
        exist_days = [
            exist_version["created_on"].strftime('%Y-%m-%d')
            for exist_version in dataset_history_version[-max_version_replications:]
        ]
        # 过滤之后仍需要删除的版本
        delete_versions = []
        # 倒序输出(确保同一天内，保存的是最后一次)
        for version in need_delete_versions[::-1]:
            version_created_on = version["created_on"]
            version_created_on_day = version_created_on.strftime('%Y-%m-%d')
            if version_created_on_day not in exist_days and self.conform_day_reserved(
                    max_day_replications, version_created_on
            ):
                exist_days.append(version_created_on_day)
                continue
            delete_versions.append(version)

        return delete_versions

    def conform_day_reserved(self, max_day_replications: int, created_on: datetime) -> bool:
        """
        是否符合按天保留(按天保留的话，只保留最后一个版本)
        :return:
        """
        delta = (self.get_today_max() - created_on).days
        # 跟产品讨论，保留3天，表示前3天的，所以不包括今天，需要+1
        if delta < max_day_replications + 1:
            return True
        return False

    @staticmethod
    def get_today_max():
        """
        获取今天的最大时间 例如： 2020-08-12 23：59：59
        :return:
        """
        today = datetime.datetime.now()
        return datetime.datetime.strptime(datetime.datetime.strftime(today, '%Y-%m-%d 23:59:59'), '%Y-%m-%d %H:%M:%S')


class CleanDatasetHistoryTable:
    def __init__(self, project_code):
        g.code = project_code
        self.project_code = project_code
        self.data_center_model = None
        self.data_center_type = None
        self.all_dataset_table = []
        self.all_datacenter_table = []
        self.all_data_table = []
        self.all_dataset_clean_table = []
        self._init_dataset_table()

    def _init_dataset_table(self):
        self.all_dataset_table = repository.get_columns('dataset_version', {}, 'table_name') or []
        logging.info('dataset_version中一共有{}张表'.format(len(self.all_dataset_table)))

    def _init_datacenter_data(self):
        self.data_center_model = get_new_erp_datasource_model()
        self.data_center_type = self.data_center_model.db_type

    def _init_cloud_data(self):
        with get_data_db() as data_db:
            sql = "select table_name as `name` from information_schema.tables where table_schema = database() and table_name like 'dataset_%'"
            self.all_data_table = data_db.query_columns(sql) or []
            logging.info('云端DATA库中一共查询有{}张表'.format(len(self.all_data_table)))
        self.data_center_type = MysoftNewERPDataBaseType.Mysql.value

    def clean_datacenter_table(self):
        logging.info('开始清理数据服务中心垃圾表')
        self._init_datacenter_data()
        # 检查并获取数据服务中心所有的表
        self._get_datacenter_table()
        # 处理垃圾表
        return self._clean_datacenter_table()

    def clean_cloud_data_table(self):
        logging.info('开始清理云端DATA库中的垃圾表')
        self._init_cloud_data()
        return self._clean_data_table(self.all_data_table, self.all_dataset_table)

    def clean_local_data_table(self):
        logging.info('开始清理数据服务中心垃圾表')
        self._init_datacenter_data()
        # 获取需要清理的相关表
        self._get_clean_table()
        # 清理相关垃圾表
        return self._clean_local_table()

    def clean_new_cloud_data_table(self):
        logging.info('开始清理云端DATA库中的垃圾表')
        # 获取需要清理的垃圾表
        self._get_clean_table('cloud')
        self.data_center_type = MysoftNewERPDataBaseType.Mysql.value
        return self._clean_data_table(self.all_dataset_clean_table, self.all_dataset_table)

    def _get_clean_table(self, table_env='local'):
        # 获取所有产生的表数据
        now = datetime.datetime.now()
        yesterday = (now - datetime.timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
        table_list = repository.get_column('dataset_clean_table', {'table_env': table_env, 'created_on <=': yesterday},
                                           ['table_name'], limit=200000) or []
        logging.info('dataset_clean_table中一共查询有{}张表需要对比处理'.format(len(table_list)))
        self.all_dataset_clean_table = table_list

    def _clean_local_table(self):
        if self.all_dataset_clean_table:
            delete_table = list(set(self.all_dataset_clean_table).difference(self.all_dataset_table))
            logging.info('本地模式下一共需要删除的表有{}张'.format(len(delete_table)))
            drop_sql = self._get_drop_table(delete_table)
            if drop_sql:
                for sql in drop_sql:
                    self.local_execute_sql(sql)
                repository.delete_data('dataset_clean_table', {'table_name': delete_table})
                return len(delete_table)
        return 0

    def _get_datacenter_table(self):
        if self.data_center_type == MysoftNewERPDataBaseType.Mysql.value:
            sql = "select table_name as `name` from information_schema.tables where table_schema = database() and table_name like 'dataset_%';"
        else:
            sql = "select [name] from sysObjects where xtype='U' and [name] like 'dataset_%';"
        result = get_data_by_sql(sql, self.data_center_model)
        data = result.get('Data') or []
        logging.info('数据服务中心一共查询有{}张表'.format(len(data)))
        if data:
            self.all_datacenter_table = [item.get('name') for item in data]

    def _clean_datacenter_table(self):
        if self.all_datacenter_table:
            delete_table = list(set(self.all_datacenter_table).difference(self.all_dataset_table))
            delete_table = self._check_table_name(delete_table)
            logging.info('数据服务中心一共需要删除的表有{}张'.format(len(delete_table)))
            drop_sql = self._get_drop_table(delete_table)
            if drop_sql:
                for sql in drop_sql:
                    self.local_execute_sql(sql)
                return len(delete_table)
        return 0

    def _clean_data_table(self, clean_table: list, all_table: list):
        if clean_table:
            delete_table = list(set(clean_table).difference(all_table))
            logging.info('云端一共需要删除的表有{}张'.format(len(delete_table)))
            drop_sql = self._get_drop_table(delete_table)
            if drop_sql:
                for sql in drop_sql:
                    repository.execute_multi_sql('data', sql)
                repository.delete_data('dataset_clean_table', {'table_name': delete_table})
            return len(delete_table)
        return 0

    @staticmethod
    def _check_table_name(table_name: list):
        delete_table_name = []
        if table_name:
            for table in table_name:
                table_split = table.split('_')
                last_split = table_split[-1]
                if len(table_split) == 2 and last_split.islower():
                    delete_table_name.append(table)
                if len(table_split) > 2 and last_split.lower() in ['rc', 'snap', 'tmp']:
                    delete_table_name.append(table)
                if table.find('dataset_tmp_') > -1:
                    delete_table_name.append(table)
        return delete_table_name

    def _get_drop_table(self, delete_table: list):
        if not delete_table:
            return
        if self.data_center_type == MysoftNewERPDataBaseType.Mysql.value:
            sql = "drop table IF EXISTS {table};"
        else:
            sql = "if object_id(N'{table}', N'U') is not null drop table [{table}];"

        sub_arr = split_array(delete_table, 1000)

        drop_sql_list = []
        for sub in sub_arr:
            drop_sql = ''
            for table in sub:
                drop_sql = drop_sql + sql.format(table=table)
            drop_sql_list.append(drop_sql)
        return drop_sql_list

    @staticmethod
    def local_execute_sql(sql):
        from components.data_center_api import request_data_center, get_data_source_info
        data_source = get_new_erp_datasource_model()
        return request_data_center(
            DataCenterAction.DMPDatasetExecuteSql.value,
            params={
                "DataInfo": {
                    "DataSourceModel": get_data_source_info(data_source.conn_str),
                    "ExecuteSql": sql,
                }
            })

    @staticmethod
    def clean_project_data():
        from datetime import datetime, timedelta
        times = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        logging.info('清理租户{}:instance表'.format(g.code))
        repository.delete_data('instance', {'created_on<': times})
        logging.info('清理完成，已删除{}之前的数据'.format(times))

        logging.info('清理租户{}:activity表'.format(g.code))
        repository.delete_data('activity', {'created_on<': times})
        logging.info('清理完成，已删除{}之前的数据'.format(times))

        times = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        logging.info('清理租户{}:dataset_inspection表'.format(g.code))
        repository.delete_data('dataset_inspection', {'created_on<': times})
        logging.info('清理完成，已删除{}之前的数据'.format(times))

        times = (datetime.now() - timedelta(days=60)).strftime("%Y-%m-%d")
        logging.info('清理租户{}:dataset_operate_record表'.format(g.code))
        repository.delete_data('dataset_operate_record', {'modified_on<': times})
        logging.info('清理完成，已删除{}之前的数据'.format(times))

        times = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        logging.info('清理租户{}:dataset_field_delete表'.format(g.code))
        repository.delete_data('dataset_field_delete', {'modified_on<': times})
        logging.info('清理完成，已删除{}之前的数据'.format(times))

