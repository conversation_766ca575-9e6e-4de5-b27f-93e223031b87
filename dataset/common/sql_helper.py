#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    sql语句辅助类（提供sql解析、转换处理等）
    <NAME_EMAIL> on 2018/9/18.
"""
import re
import time
import json

import sqlparse
import sql_metadata
from pypinyin import lazy_pinyin

from base.enums import DatasetFieldType
from components import deal_dataset_transition
from dmplib.utils.strings import _get_random_chars
from dataset.common import advance_field_helper
from dataset.services import dataset_group_field


def clean(sql_str):
    # remove the /* */ comments
    q = re.sub(r"/\*[^*]*\*+(?:[^*/][^*]*\*+)*/", "", sql_str)
    # remove whole line -- and # comments
    lines = [line for line in q.splitlines() if not re.match("^\s*(--|#)", line)]
    # remove trailing -- and # comments
    q = " ".join([re.split("--|#", line)[0] for line in lines])
    q = ' '.join(q.split())
    return q


# def extract_tables(sql, is_alias=False):
#     """
#     获取表名
#     :param sql:
#     :param is_alias:
#     :return:
#     """
#     sql = clean(sql)
#     # 处理中文开头的表名
#     chinese_char = set(re.findall(r'[\u4e00-\u9fa5]+', sql))
#     chinese_map = {i: "".join(lazy_pinyin(i, errors="ignore")) for i in chinese_char}
#     for k, v in chinese_map.items():
#         sql = sql.replace(k, v)
#     sql = sql.replace("{", "`{").replace("}", "}`")
#     tables = set(sql_metadata.Parser(sql).tables or [])
#     tables_aliases = {}
#     if is_alias:
#         tables_aliases = sql_metadata.Parser(sql).tables_aliases
#         tables_aliases = {v:k for k, v in tables_aliases.items()}
#     replace_tables = []
#     for table in tables:
#         ta = table.replace("`{", "{").replace("}`", "}")
#         if is_alias and tables_aliases and tables_aliases.get(table):
#             ta = f"{table} {tables_aliases.get(table)}"
#         for k, v in chinese_map.items():
#             if v in table:
#                 ta = ta.replace(v, k)
#         replace_tables.append(ta)
#     return list(set(replace_tables)) or list(tables)


def extract_tables(org_sql, is_alias=False):
    """
    获取表名
    :param sql:
    :param is_alias:
    :return:
    """
    try:
        org_sql = clean(org_sql)
        # 处理中文开头的表名
        chinese_char = set(re.findall(r'[\u4e00-\u9fa5]+', org_sql))
        chinese_map = {i: "".join(lazy_pinyin(i, errors="ignore")) + _get_random_chars(6) for i in chinese_char}
        for k, v in chinese_map.items():
            org_sql = org_sql.replace(k, v)
        replace_tables = []
        for sql in org_sql.split(";"):
            # 前两个两个替换是将oracle和DM的替换成普通的语法
            sql = sql.replace("\"{", "{").replace("}\"", "}").replace("{", "`{").replace("}", "}`").replace("'", "`").replace('"', "`")
            parser = sql_metadata.Parser(sql)
            parser._query_type = "SELECT"
            tables = set(parser.tables or [])
            tables_aliases = {}
            if is_alias:
                tables_aliases = parser.tables_aliases
                tables_aliases = {v:k for k, v in tables_aliases.items()}
            for table in tables:
                ta = table.replace("`{", "{").replace("}`", "}")
                if is_alias and tables_aliases and tables_aliases.get(table):
                    ta = f"{table} {tables_aliases.get(table)}"
                for k, v in chinese_map.items():
                    if v in table:
                        ta = ta.replace(v, k)
                replace_tables.append(ta)
        return list(set(replace_tables)) or list(tables)
    except Exception as e:
        print(e)
        return ["error"]


def get_table_names(sql, is_alias=False):
    """
    解析sql表名称
    :param sql:
    :param is_alias: 是否包含别名（默认否）
    :return:
    """
    sql = clean(sql)
    table_names = parse_sql(sql, is_alias=is_alias)

    table_names = filter(None, table_names)
    table_names = [i for i in table_names if i.strip() and i.strip() not in ['(', ")"]]
    table_names = list(set(table_names))

    # 再用正则匹配一次, 匹配数据服务中心使用的表名
    dc_table_names = re.findall(r'\bdata_wide\w+', sql) or []

    table_names = table_names + dc_table_names

    table_names = [i.replace("`", "").replace('"', "").replace("[", "").replace("]", "") for i in table_names]

    return list(set(table_names))


def parse_sql(sql, is_alias=False):
    table_names = []
    sql_format = sqlparse.format(sql, encoding='UTF-8', reindent=True, keyword_case='upper')
    subsections = sql_format.split('\n')
    for subsection in subsections:
        if 'FROM' in subsection:
            if sql_format.upper().count('SELECT') > 1:
                table_names.append(subsection.replace("FROM", "").replace(")", ""))
            else:
                table_names.extend(re.findall("FROM(.*)", subsection, flags=re.I))
        if 'JOIN' in subsection:
            table_names.extend(re.findall("JOIN(.*)ON", subsection, flags=re.I))

    # 检验表名是否有字符串小写被upper成大写了
    return check_table_name_upper(table_names, sql, is_alias)


def check_table_name_upper(table_names: list, sql: str, is_alias: bool) -> list:
    # 检验表名是否有字符串小写被upper成大写了
    for index, table_name in enumerate(table_names):
        table_names[index] = table_name.strip().rstrip(";")
        if table_name not in sql and table_name.lower() in sql:
            table_names[index] = table_name.lower().rstrip(";")
        if table_name.isspace():
            table_names[index] = None
        if not is_alias and table_name.strip() and table_name.find(" ") > -1:
            table_names[index] = table_name.strip().split()[0].rstrip(";")
    return table_names


def parse_table_alias_names(table_names):
    """
    解析表名和表别名
    :param table_names:
    :return:
    """
    table_alias_names = []
    for table_name in table_names:
        if table_name:
            origin_table_name = re.findall(r'\{(.*)\}', table_name)
            table_name = table_name.replace(origin_table_name[0], 'a') if origin_table_name else table_name
            temp_table_name = table_name.strip().split()
            if len(temp_table_name) == 2:
                sql_table_alias_name = temp_table_name[1]
            # 解决表别名带有as关键字
            elif len(temp_table_name) > 2 and temp_table_name[1].lower() == 'as':
                sql_table_alias_name = temp_table_name[2]
            else:
                sql_table_alias_name = ""

            sql_table_name = origin_table_name[0] if origin_table_name else temp_table_name[0]

            table_alias_names.append({"sql_table_name": sql_table_name, "sql_table_alias_name": sql_table_alias_name})
    return table_alias_names


def parse_custom_sql(sql, dataset_matedata):
    """
    解析自定义的sql
    :param sql: sql语句
    :param dataset_matedata: 数据集元数据 [
                                            {
                                                "name":"数据集名",
                                                "table_name":"dataset_xxxx",
                                                "sql_table_name":"数据集名",
                                                "sql_table_alias_name":"a",
                                                "dataset_fields":[
                                                    {
                                                        "col_name":"hash_123",
                                                        "alias_name":"hash"
                                                    }
                                                ]
                                            }
                                          ]
    :return: sql
    """
    source_dataset_ids = []
    for dataset in dataset_matedata:
        source_dataset_ids.append(dataset.get("id"))
        for dataset_field in dataset.get("dataset_fields"):

            # 1、{数据集名或别名或表名}.[字段名或别名] 替换
            col_name = get_col_name(dataset, dataset_field)
            new_col_name = "{}.{}".format(dataset.get("table_name"), col_name)
            alias_name = dataset_field.get("alias_name") if dataset_field.get("alias_name") else ""

            old_col_name = '{' + dataset.get("sql_table_name") + '}.[' + alias_name + ']'
            sql = replace_sql(sql, old_col_name, new_col_name)
            old_col_name_1 = '`{' + dataset.get("sql_table_name") + '}`.`[' + alias_name + ']`'
            sql = replace_sql(sql, old_col_name_1, new_col_name)
            old_col_name = '{' + dataset.get("sql_table_name") + '}.[' + col_name + ']'
            sql = replace_sql(sql, old_col_name, new_col_name)
            old_col_name_1 = '`{' + dataset.get("sql_table_name") + '}`.`[' + col_name + ']`'
            sql = replace_sql(sql, old_col_name_1, new_col_name)

            if dataset.get("sql_table_alias_name"):
                new_col_name = "{}.{}".format(dataset.get("sql_table_alias_name"), col_name)
                old_col_name = dataset.get("sql_table_alias_name") + '.[' + alias_name + ']'
                sql = replace_sql(sql, old_col_name, new_col_name)
                old_col_name = dataset.get("sql_table_alias_name") + '.[' + col_name + ']'
                sql = replace_sql(sql, old_col_name, new_col_name)

    for dataset in dataset_matedata:
        for dataset_field in dataset.get("dataset_fields"):

            # 1、{数据集名或别名或表名}.[字段名或别名] 替换
            col_name = get_col_name(dataset, dataset_field)

            # 2、[字段名或别名] 替换
            old_col_name = "[{}]".format(dataset_field.get("alias_name"))
            sql = replace_sql(sql, old_col_name, col_name)

            old_col_name = "[{}]".format(col_name)
            sql = replace_sql(sql, old_col_name, col_name)

        # 3、{数据集名或表名} 替换
        old_table_name = '{' + dataset.get("name") + '}'
        sql = replace_sql(sql, old_table_name, dataset.get("table_name"))

    return sql, source_dataset_ids


def replace_sql(sql, old, new):
    """
    替换sql语句
    :param sql:
    :param old:
    :param new:
    :return:
    """
    if old in sql:
        sql = sql.replace(old, new)
    return sql


def get_col_name(dataset, dataset_field):
    """
    获取普通、高级、分组字段名（sql语句使用）
    :param dataset:
    :param dataset_field:
    :return:
    """
    # 高级字段
    if dataset_field.get("type") in [
        DatasetFieldType.Customer.value,
        DatasetFieldType.Calculate.value,
        DatasetFieldType.Indicator.value,
        DatasetFieldType.CalculateIndicator.value,
    ]:
        if dataset_field.get("expression_advance"):
            new_col_name = advance_field_helper.expression_real_name(
                dataset_field.get("expression_advance"), dataset.get("id")
            )
        else:
            expression_advance = advance_field_helper.expression_convertor(dataset_field.get("expression"))
            new_col_name = advance_field_helper.expression_real_name(expression_advance, dataset.get("id"))
        new_col_name = "{} AS {}".format(new_col_name, dataset_field.get("col_name"))
    # 分组字段
    elif dataset_field.get("type") == DatasetFieldType.Group.value:
        all_dataset_fields = {row.get("col_name"): row for row in dataset.get("dataset_fields")}
        new_dataset_field = dataset_group_field.deal_group_field_by_dataset_filed(
            all_dataset_fields, dataset_field.get("col_name")
        )
        new_col_name = deal_dataset_transition.deal_exexpression_to_sql(dataset, new_dataset_field)
        new_col_name = "{} AS {}".format(new_col_name, dataset_field.get("col_name"))
    # 普通字段
    else:
        new_col_name = dataset_field.get("col_name")
    return new_col_name


def deal_where_index_end(subsections):
    where_index = -1
    where_index_end = -1
    extract_where = ""
    for i, subsection in enumerate(subsections):
        if subsection.startswith("WHERE"):
            where_index = i
            extract_where = "{} {}".format(extract_where, subsection.strip())
        if where_index != -1 and i > where_index and subsection.startswith(" "):
            extract_where = "{} {}".format(extract_where, subsection.strip())
        if where_index != -1 and i > where_index and not subsection.startswith(" "):
            where_index_end = i
            break
    where_index_end = len(subsections) if where_index_end == -1 else where_index_end
    return where_index, extract_where, where_index_end


def sql_append_where(sql, where_str):
    """
    sql语句追加where
    :param sql:
    :param where_str:
    :return:
    """
    sql_format = sqlparse.format(sql, encoding='UTF-8', reindent=True, keyword_case='upper')
    subsections = sql_format.split('\n')

    # 提取where
    where_index, extract_where, where_index_end = deal_where_index_end(subsections)

    # 存在where
    if extract_where:
        new_extract_where = extract_where.replace("WHERE", "", 1)
        new_extract_where = " WHERE ({}) AND ({}) ".format(new_extract_where.strip(), where_str)
        new_subsections = []
        is_append = False
        for i, subsection in enumerate(subsections):
            if (where_index < i <= where_index_end or len(subsections) - 1 == i) and not is_append:
                new_subsections.append(new_extract_where)
                is_append = True
            if where_index > i or i >= where_index_end:
                new_subsections.append(subsection)
        new_sql = " ".join([subsection.strip() for subsection in new_subsections])
    # 不存在where
    else:
        new_sql = deal_no_where(where_index, subsections, where_str)
    return new_sql


def deal_no_where(where_index: int, subsections: list, where_str: str) -> str:
    # 不存在where
    keywords = ["GROUP", "ORDER", "HAVING", "LIMIT"]
    for i, subsection in enumerate(subsections):
        is_append = False
        for keyword in keywords:
            if subsection.startswith(keyword):
                is_append = True
        if is_append:
            where_index = i
    if where_index > -1:
        subsections.insert(where_index, "WHERE ({})".format(where_str))
    else:
        subsections.append("WHERE ({})".format(where_str))
    new_sql = " ".join([subsection.strip() for subsection in subsections])
    return new_sql


def mysql_limit(sql, data_limit):
    """
    追加limit
    :param sql:
    :param data_limit:
    :return:
    """
    try:
        limit_str = re.findall(r'(limit\s+[\d|(offset)|,|\s]+[\s|\)|;]*)\s*$', sql, flags=re.I)
        if limit_str and limit_str[0]:
            limit_num = re.subn(r'limit', ' ', limit_str[0], flags=re.I)[0].strip()
            limit_num = limit_num.replace(';', '')
            new_limit_str = deal_limit_num(limit_num, data_limit)
            sql = re.subn(limit_str[0] + '$', new_limit_str, sql, flags=re.I)[0]
        elif re.findall(r'limit', sql, flags=re.I):
            sql = sql.replace(';', '')
        else:
            sql = sql.replace(';', '') + ' LIMIT ' + str(data_limit)
    except Exception:
        pass

    return sql


def deal_limit_num(limit_num, data_limit):
    if "," in limit_num:
        limit_num_split = limit_num.split(",")
        if int(limit_num_split[1]) < data_limit:
            data_limit = int(limit_num_split[1])
        new_limit_str = ' LIMIT ' + str(limit_num_split[0]) + ' , ' + str(data_limit)
    elif "OFFSET" in limit_num:
        limit_num_split = limit_num.split("OFFSET")
        if int(limit_num_split[0]) < data_limit:
            data_limit = int(limit_num_split[0])
        new_limit_str = ' LIMIT ' + str(data_limit) + ' OFFSET ' + str(limit_num_split[1])
    else:
        if int(limit_num) < data_limit:
            data_limit = limit_num
        # 缩进问题，和flow保持一致
        new_limit_str = ' LIMIT ' + str(data_limit)
    return new_limit_str


def find_mysql_limit(sql):
    """
    查找sql中的limit
    :param sql:
    :return:
    """
    limit_str_list = []
    sql_format = sqlparse.format(sql, encoding='UTF-8', reindent=True, keyword_case='upper')
    subsections = sql_format.split('\n')
    for i, subsection in enumerate(subsections):
        if subsection and (subsection.find("LIMIT") > -1 or subsection.find("OFFSET") > -1):
            limit_str_list.append(subsection)
    return limit_str_list


def get_select_fields(sql: str):
    if len(sqlparse.split(sql)) != 1:
        raise ValueError('只支持单条SQL语句解析')

    tokens = sqlparse.parse(sql)[0].tokens
    tmp_value = ''
    identifier_list = []
    for token in tokens:
        if isinstance(token, sqlparse.sql.IdentifierList):
            tmp_tokens = list(token.get_identifiers())
            if tmp_tokens:
                tmp_tokens[0].value = tmp_value + tmp_tokens[0].value
                identifier_list.extend(tmp_tokens)

        elif isinstance(token, sqlparse.sql.Identifier):
            token.value = tmp_value + token.value
            identifier_list.append(token)

        elif token.normalized == 'FROM':
            break

        if token.is_whitespace or token.value == ',':
            tmp_value = ''
        else:
            tmp_value += token.value
    return deal_sql_field(identifier_list, sql)


def deal_sql_field(identifier_list: list, sql: str) -> list:
    identifier_list_token = sqlparse.sql.IdentifierList(identifier_list) if identifier_list else None

    if identifier_list_token is None:
        pattern = r'select[\s]+[*][\s]+from'
        if not re.match(pattern, sql, re.I | re.M):
            return []
        return ['*']

    fields = [r.value for r in identifier_list_token.get_identifiers()]
    return fields


def mo_parse(sql):
    """
    sql parse
    :param sql:
    :return:
    """
    sql = clean(sql)
    chinese_char = set(re.findall(r'[\u4e00-\u9fa5]+', sql))
    chinese_map = {i: "".join(lazy_pinyin(i)) for i in chinese_char}
    for k, v in chinese_map.items():
        sql = sql.replace(k, v)

    from mo_sql_parsing import parse
    struct = parse(sql)
    print(struct)
    return []


if __name__ == '__main__':
    sql = """
select 1 a from `data_wide_s_oppgjrecord`
union all 
select 1 a from `data_wide_s_opportunity`
union all 
select 1 a from `data_wide_mdm_myuser`
union all 
select 1 a from `data_wide_s_salehsdata`
union all 
select 1 a from `data_wide_s_room`
union all 
select 1 a from `data_wide_mdm_myuserbuprojectright`
union all 
select 1 a from `data_wide_mdm_myuserbusinessunitmapping`
union all 
select 1 a from `data_wide_dws_s_receivable`
"""
    # start = time.time()
    # tables = extract_tables(sql, True)
    # print("extract_tables: ", tables,  time.time()-start)
    # # start = time.time()
    # # tables = mo_parse(sql)
    # # print("get_table_names: ", tables, time.time() - start)
    start = time.time()
    tables = get_table_names(sql, False)
    print("get_table_names: ", tables, time.time() - start)