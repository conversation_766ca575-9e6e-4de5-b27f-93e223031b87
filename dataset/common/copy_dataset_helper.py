#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    数据集复制帮助类
"""
import json

from base import repository
from base.enums import DatasetType, DatasetConnectType
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from dmplib.saas.project import get_db
from dmplib.utils.strings import uniqid
from components.dateset_generate_col_name import generate_new_col_name
from exports.services.export_dataset_service import query_dataset_data
from dataset.services.dataset_define_service import generate_level_code
from dataset.services.dataset_service import generate_dataset_table_name
from flow.services.flow_service import update_flow_schedule, run_flow


class CopyDatasetHelper(object):
    def __init__(self, dataset_id, folder_id=None):
        self.dataset_info = None
        self.dataset_name = None
        self.dataset_field_list = None
        self.dataset_id = dataset_id
        self.new_dataset_id = None
        self.connect_type = None
        self.dataset_type = None
        self.cache_flow_id = None
        self.folder_id = folder_id
        self.fields_map = {}
        self.col_name_map = {}
        self.vars_map = {}
        self.db = get_db()
        self.load_dataset_info()
        self.source_id = None

    def load_dataset_info(self):
        self.dataset_info = query_dataset_data('', self.dataset_id)
        if not self.dataset_info:
            raise UserError(message="没有查询到对应的数据集")
        del self.dataset_info['dataset_folders']
        del self.dataset_info['data_source']
        del self.dataset_info['dataset_id']
        self.new_dataset_id = seq_id()

    def copy_dataset(self):
        try:
            # 处理变量
            self.dataset_vars()
            # 处理关键字变量使用关系
            self.keyword_details()
            # 处理字段
            self.dataset_fields()
            # 处理dataset数据
            dataset = self.dataset()
            # 处理缓存调度
            self.cache_flow()
            # 其他表处理
            self.other_object()
            # commit
            self.db.commit()
            # 处理调度任务
            self.run_flow()
            return dataset
        except Exception as e:
            self.db.rollback()
            raise UserError(message=str(e))

    def other_object(self):
        for key, value in self.dataset_info.items():
            if hasattr(self, key):
                getattr(self, key)()

    def dataset(self):
        table = 'dataset'
        dataset = self.dataset_info.get('dataset')
        if not dataset:
            raise UserError(message="复制的数据集不能为空")
        dataset['id'] = self.new_dataset_id
        dataset_name = '{}_副本'.format(dataset['name'])
        parent_id = dataset.get('parent_id') if not self.folder_id else self.folder_id
        dataset['parent_id'] = parent_id
        dataset['name'] = self.dataset_name = self._check_dataset_name(dataset_name)
        dataset['table_name'] = generate_dataset_table_name(self.new_dataset_id)
        dataset['level_code'] = generate_level_code(parent_id)
        dataset['source_id'] = dataset['source_id'] if dataset['source_id'] else self.dataset_id
        dataset['content'] = self._fix_dataset_sql(dataset['content'])
        dataset['is_lock'] = 0
        if dataset['relation_sql']:
            for key, value in self.col_name_map.items():
                dataset['relation_sql'] = dataset['relation_sql'].replace(key, value)
            for key, value in self.vars_map.items():
                dataset['relation_sql'] = dataset['relation_sql'].replace(key, value)
        self.connect_type = dataset.get('connect_type')
        self.dataset_type = dataset.get('type')
        repository.add_data(table, dataset, commit=False)
        del self.dataset_info['dataset']
        return dataset

    def _fix_dataset_sql(self, content):
        if not content:
            return ''
        data = json.loads(content)
        tmp_table_name = uniqid("dataset_tmp_")
        if data.get('create_table_sql'):
            for key, value in self.col_name_map.items():
                data['create_table_sql'] = data['create_table_sql'].replace(key, value)
        if data.get('tmp_table_name'):
            data['create_table_sql'] = data['create_table_sql'].replace(data['tmp_table_name'], tmp_table_name)
            data['tmp_table_name'] = tmp_table_name
        if data.get('sql') and self.vars_map:
            for key, value in self.vars_map.items():
                data['sql'] = data['sql'].replace(key, value)
        if data.get('replace_sql') and self.vars_map:
            for key, value in self.vars_map.items():
                data['replace_sql'] = data['replace_sql'].replace(key, value)
        return json.dumps(data, ensure_ascii=False)

    @staticmethod
    def _check_dataset_name(dataset_name):
        while True:
            where = {'name': dataset_name, 'type!=': 'FOLDER'}
            data = repository.get_one('dataset', where)
            if not data:
                break
            dataset_name += '_副本'
        return dataset_name

    def dataset_fields(self):
        table = 'dataset_field'
        self.dataset_field_list = self.dataset_info.get('dataset_fields')
        if self.dataset_field_list:
            for field in self.dataset_field_list:
                old_field_id = field['id']
                field['id'] = self.fields_map[old_field_id] = seq_id()
                field['dataset_id'] = self.new_dataset_id
                table_name = field.get("origin_table_name") if field.get("origin_table_name") else field.get("origin_table_alias_name")
                if self.dataset_info.get('dataset', {}).get('type') == DatasetType.Union.value:
                    table_name = '{' + table_name + '}' if table_name else ''
                col_name = field['origin_col_name'] if field['origin_col_name'] else field['col_name']
                new_col_name = generate_new_col_name(self.new_dataset_id, col_name, table_name)
                self.col_name_map[field['col_name']] = new_col_name
                field['col_name'] = new_col_name
            self._fix_dataset_field()
            fields = list(self.dataset_field_list[0].keys())
            repository.add_list_data(table, self.dataset_field_list, fields, commit=False)
        del self.dataset_info['dataset_fields']

    def _fix_dataset_field(self):
        map_arr = [self.col_name_map, self.vars_map, self.fields_map]
        # 处理高级字段字段名
        for field in [item for item in self.dataset_field_list if item.get("type") != "普通"]:
            for map_info in map_arr:
                for k, v in map_info.items():
                    if isinstance(field.get("expression_advance"), str) and k in field.get("expression_advance"):
                        field["expression_advance"] = field.get("expression_advance").replace(k, v)
                    if isinstance(field.get("expression"), str) and k in field.get("expression"):
                        field["expression"] = field.get("expression").replace(k, v)

    def dataset_depend(self):
        table = 'dataset_depend'
        dataset_depend = self.dataset_info.get('dataset_depend')
        if dataset_depend:
            for depend in dataset_depend:
                depend['depend_id'] = self.new_dataset_id
            fields = list(dataset_depend[0].keys())
            repository.add_list_data(table, dataset_depend, fields, commit=False)

    def dataset_index(self):
        table = 'dataset_index'
        dataset_index = self.dataset_info.get('dataset_index')
        if dataset_index:
            for field_index in dataset_index:
                field_index['id'] = seq_id()
                field_index['dataset_id'] = self.new_dataset_id
                # 处理索引
                for k, v in self.col_name_map.items():
                    if isinstance(field_index.get('column_list'), str) and k in field_index.get('column_list'):
                        field_index['column_list'] = field_index.get('column_list').replace(k, v)
            fields = list(dataset_index[0].keys())
            repository.add_list_data(table, dataset_index, fields, commit=False)

    def dataset_tables_collection(self):
        table = 'dataset_tables_collection'
        dataset_tables_collection = self.dataset_info.get('dataset_tables_collection')
        if dataset_tables_collection:
            for collection in dataset_tables_collection:
                collection['id'] = seq_id()
                collection['dataset_id'] = self.new_dataset_id
            fields = list(dataset_tables_collection[0].keys())
            repository.add_list_data(table, dataset_tables_collection, fields, commit=False)

    def dataset_field_delete(self):
        table = 'dataset_field_delete'
        dataset_field_delete = self.dataset_info.get('dataset_field_delete')
        if dataset_field_delete:
            for delete in dataset_field_delete:
                delete['dataset_id'] = self.new_dataset_id
                new_field_id = self.fields_map.get(delete.get('dataset_field_id'))
                delete['dataset_field_id'] = new_field_id if new_field_id else delete['dataset_field_id']
            fields = list(dataset_field_delete[0].keys())
            repository.add_list_data(table, dataset_field_delete, fields, commit=False)

    def dataset_filter(self):
        table = 'dataset_filter'
        dataset_filter = self.dataset_info.get('dataset_filter')
        if dataset_filter:
            for item in dataset_filter:
                item['id'] = seq_id()
                item['dataset_id'] = self.new_dataset_id
                for key, value in self.vars_map.items():
                    item['json_value'] = item['json_value'].replace(key, value)
            fields = list(dataset_filter[0].keys())
            repository.add_list_data(table, dataset_filter, fields, commit=False)

    def dataset_vars(self):
        table = 'dataset_vars'
        dataset_vars = self.dataset_info.get('dataset_vars')
        if dataset_vars:
            for var in dataset_vars:
                old_var_id = var['id']
                var['dataset_id'] = self.new_dataset_id
                var['id'] = self.vars_map[old_var_id] = seq_id()
            fields = list(dataset_vars[0].keys())
            repository.add_list_data(table, dataset_vars, fields, commit=False)
        del self.dataset_info['dataset_vars']

    def keyword_details(self):
        table = 'keyword_details'
        keyword_details = self.dataset_info.get('keyword_details') or []
        if keyword_details:
            datas = []
            for keyword_detail in keyword_details:
                new_var_id =  self.vars_map.get(keyword_detail['var_id'], '')
                if not new_var_id:
                    continue
                keyword_detail['dataset_id'] = self.new_dataset_id
                keyword_detail['var_id'] = new_var_id
                keyword_detail.pop('id', None)
                keyword_detail.pop('modified_on', None)
                datas.append(keyword_detail)
            if datas:
                fields = list(datas[0].keys())
                repository.add_list_data(table, datas, fields, commit=False)

    def dataset_field_include_vars(self):
        table = 'dataset_field_include_vars'
        dataset_field_include_vars = self.dataset_info.get('dataset_field_include_vars')
        if dataset_field_include_vars:
            for include_var in dataset_field_include_vars:
                include_var['id'] = seq_id()
                include_var['dataset_id'] = self.new_dataset_id
                new_field_id = self.fields_map.get(include_var.get('field_id'))
                include_var['field_id'] = new_field_id if new_field_id else include_var['field_id']
                new_var_id = self.vars_map.get(include_var.get('var_id'))
                include_var['var_id'] = new_var_id if new_var_id else include_var['var_id']
            fields = list(dataset_field_include_vars[0].keys())
            repository.add_list_data(table, dataset_field_include_vars, fields, commit=False)

    def flow(self):
        table = 'flow'
        flow = self.dataset_info.get('flow')
        if flow:
            flow['id'] = self.new_dataset_id
            flow['name'] = self.dataset_name
            repository.add_data(table, flow, commit=False)

    def nodes(self):
        table = 'node'
        nodes = self.dataset_info.get('nodes')
        if nodes:
            for node in nodes:
                node['id'] = seq_id()
                node['flow_id'] = self.new_dataset_id
                node['name'] = self.dataset_name
            fields = list(nodes[0].keys())
            repository.add_list_data(table, nodes, fields, commit=False)

    def cache_flow(self):
        table = 'flow'
        flow = self.dataset_info.get('cache_flow')
        if flow:
            flow['id'] = self.cache_flow_id = seq_id()
            flow['name'] = self.dataset_name
            repository.add_data(table, flow, commit=False)
        del self.dataset_info['cache_flow']

    def cache_nodes(self):
        table = 'node'
        nodes = self.dataset_info.get('cache_nodes')
        if nodes:
            for node in nodes:
                node['id'] = seq_id()
                node['name'] = self.dataset_name
                node['flow_id'] = self.cache_flow_id
            fields = list(nodes[0].keys())
            repository.add_list_data(table, nodes, fields, commit=False)

    def run_flow(self):
        # 定时数据集任务
        if (
            self.dataset_info.get('flow')
            and self.dataset_type in [DatasetType.Sql.value, DatasetType.Excel.value, DatasetType.Union.value, DatasetType.Api.value]
            and self.connect_type != DatasetConnectType.Directly.value
        ):
            run_flow(self.new_dataset_id)
            update_flow_schedule(self.new_dataset_id)
        # 缓存数据集定时任务
        if self.cache_flow_id and self.connect_type == DatasetConnectType.Directly.value:
            update_flow_schedule(self.cache_flow_id)

    def check_is_import_table(self):
        if self.dataset_info.get('dataset').get('is_import_table'):
            return True
        else:
            return False