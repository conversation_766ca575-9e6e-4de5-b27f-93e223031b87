目录结构：
- base 
- - enums.py 业务类型枚举
- - enums.py 基础表单Model
- - repository.py 通用仓储
- components 第三方组件
- confd 配置工具核心文件
- dmplib 工具包
- nginx ngingx配置文件
- runtime dmp运行时文件存储目录，包括日志文件
- .env dmp配置文件
- .env.example dmp配置文件模板
- app.py dmp入口文件
- build.xml sonar检测文件配置，添加新的业务模块后需要在source中添加模块目录
- requirement.txt dmp需要安装的Python扩展
- [业务模块] 业务模块名称，如flow
- - api_route.py 业务模块API列表
- - models.py 该业务模块下所有Model
- - services 业务逻辑文件夹
- - repositories 仓储实现文件夹