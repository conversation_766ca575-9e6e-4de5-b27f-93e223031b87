#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time     : 2020/8/3 20:20
# <AUTHOR> caoxl
# @File     : warning_service.py

import json
import logging

from base import repository
from dmplib import config
from base.enums import FlowStatus
from components.rundeck import RundeckScheduler
from dmplib.hug import g
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from indicator_new.common.utils import record_exists, get_indicator_name_by_id, get_result_verbose_count_by_result_id
from indicator_new.models import IndicatorWarningConfigItemModel, IndicatorWarningConfigModel
from indicator_new.repositories.warning_repositories import get_indicator_warning_result
from ..repositories import indicator_repositories


logger = logging.getLogger(__name__)


def get_indicator_warning(config_id: str):
    warning_conf = repository.get_data("dashboard_indicator_warning_config", {"id": config_id})
    if not warning_conf:
        return False
    warning_items = indicator_repositories.get_warning_items_by_config_id(config_id)
    warning_conf["items"] = warning_items or []
    return warning_conf


def get_warning_results_verbose(result_id: str, page=0, page_size=0):
    page_params = (page, page_size) if (page and page_size) else None
    fields = ['result_id', 'indicator_id', 'result']
    warning_result = get_indicator_warning_result(result_id)
    warning_result_verbose = repository.get_list(
        'dashboard_indicator_warning_result_item',
        {"result_id": result_id},
        fields=fields,
        page=page_params,
        order_by=[('created_on', 'desc')],
    )
    if warning_result_verbose:
        for item in warning_result_verbose:
            # result verbose is long text, change to json
            item['result'] = json.loads(item['result']) if item['result'] else {}

    if warning_result:
        total = get_result_verbose_count_by_result_id(result_id)
        try:
            warning_result['global_messages'] = json.loads(warning_result['global_messages'])
        except:
            warning_result['global_messages'] = []
        warning_result['results'] = [item['result'] for item in warning_result_verbose if warning_result_verbose]
        warning_result['page'] = page if page else 1
        warning_result['page_size'] = page_size if page_size else total
        warning_result['total'] = total
    return warning_result


def get_command(config_id, queue_name='celery'):
    """
    获取rundeck执行celery的command命令
    :param config_id:
    :return:
    """
    celery_task_name = "app_celery.async_indicator_new_warning"
    cmd_template_feed = config.get(
        "Rundeck.cmd_template_celery", "export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py"
    )
    command = "%s %s %s %s %s" % (cmd_template_feed, g.code, celery_task_name, config_id, queue_name)
    return command


def add_indicator_warning(model):
    """
    新增指标预警配置
    :param rundeck:
    :param model:
    :return:
    """
    model.validate()
    warning_run(model)
    if model.items:
        for item_model in model.items:
            item_model.config_id = model.id
            item_model.indicator_id = model.indicator_id
            add_indicator_warning_item(item_model)
    fields = model.to_dict().keys()
    return repository.add_model(model.__table__, model, fields)


def add_indicator_warning_item(model):
    """
    新增指标预警配置条目
    :param model:
    :return:
    """
    model.validate()
    fields = list(model.__slots__)
    return repository.add_model(model.__table__, model, fields)


def update_indicator_warning(model):
    """
    修改指标预警配置
    :param model:
    :return:
    """
    if model.items:
        for item in model.items:
            warning_item_model = IndicatorWarningConfigItemModel(**item)
            update_indicator_warning_item(warning_item_model)
    fields = model.to_dict().keys()
    return repository.update_model(model.__table__, model.to_dict(), fields)


def update_indicator_warning_item(model):
    """
    修改指标预警配置条目
    :param model:
    :return:
    """
    if not model.id:
        model.id = seq_id()
    fields = list(model.__slots__)
    return repository.update_model(model.__table__, model, {'id': model.id}, fields)


def get_warning_config_model_from_params(**params):
    items = params.get('items')
    if "items" in params:
        del params["items"]
    warning_config_model = IndicatorWarningConfigModel(**params)
    warning_config_model.indicator_name = get_indicator_name_by_id(warning_config_model.indicator_id)
    if items:
        warning_config_model.items = [IndicatorWarningConfigItemModel(**item) for item in items]

    return warning_config_model


def add_warning_model_transaction(tables, model):
    with get_db() as db:
        for table in tables:
            condition = "indicator_id"
            db.delete(table, {condition: model.indicator_id}, commit=True)
        return add_indicator_warning(model)


def update_warning_config_transaction(model):
    if not record_exists("dashboard_indicator_warning_config", {'indicator_id': model.indicator_id}):
        return add_indicator_warning(model)
    tables = ["dashboard_indicator_warning_config_item", "dashboard_indicator_warning_config"]
    return add_warning_model_transaction(tables, model)


def get_warning_config_by_id(indicator_id: str):
    return repository.get_data("dashboard_indicator_warning_config", {'indicator_id': indicator_id})


def get_warning_config_items_by_id(config_id: str):
    return repository.get_list('dashboard_indicator_warning_config_item', {'config_id': config_id})


def get_warning_verbose(indicator_id: str):
    warning_config = get_warning_config_by_id(indicator_id)
    if not warning_config:
        return []
    warning_config_items = get_warning_config_items_by_id(warning_config['id'])
    warning_config_model = IndicatorWarningConfigModel(**warning_config)
    warning_config_model.indicator_name = get_indicator_name_by_id(warning_config_model.indicator_id)
    if warning_config_items:
        warning_config_model.items = []
        for item in warning_config_items:
            warning_config_item_model = IndicatorWarningConfigItemModel(**item)
            warning_config_model.items.append(warning_config_item_model)
    return warning_config_model


def indicator_warning_enable(**kwargs):
    indicator_id = kwargs.get('indicator_id', '')
    is_enable = kwargs.get('is_enable', 0)
    warning_config = get_warning_config_by_id(indicator_id)
    if not warning_config:
        raise UserError(code=404, message="该指标尚未配置预警")
    warning_config['is_enable'] = int(is_enable) if is_enable else 0
    warning_config_model = get_warning_config_model_from_params(**warning_config)
    warning_config_items = get_warning_config_items_by_id(warning_config_model.id)
    warning_config_model.items = (
        [IndicatorWarningConfigItemModel(**item) for item in warning_config_items] if warning_config_items else []
    )
    update_warning_model(warning_config_model)
    warning_run(warning_config_model)
    return warning_config_model


def update_warning_model(warning_config_model):
    config_fields = warning_config_model.to_dict().keys()
    return repository.update_model(
        'dashboard_indicator_warning_config',
        warning_config_model,
        {"indicator_id": warning_config_model.indicator_id},
        fields=config_fields,
    )


def warning_run(warning_config_model):
    warning_config_model.name = warning_config_model.id
    warning_config_model.description = ""
    try:
        if warning_config_model.is_enable and warning_config_model.schedule:
            warning_config_model.status = FlowStatus.Enable.value
            # 注册定时任务
            scheduler = RundeckScheduler(warning_config_model)
            if scheduler.job_is_exists():
                scheduler.delete_job()
            scheduler.add_job(command=get_command(warning_config_model.id))
        else:
            warning_config_model.status = FlowStatus.Disable.value
            # 删除定时任务
            if record_exists('dashboard_indicator_warning_config', {'id': warning_config_model.id}):
                RundeckScheduler(warning_config_model).delete_job()
    except Exception as e:
        # pylint: disable=W1203
        logger.error(f"rundeck error, ErrMsg: {e}")
        warning_config_model.is_enable = 0
    finally:
        del warning_config_model.name
        del warning_config_model.status
        del warning_config_model.description
