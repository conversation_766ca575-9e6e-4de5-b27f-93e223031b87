#!/usr/bin/env python
# -*- coding: utf-8 -*-
# pylint: skip-file

import logging

from dmplib.hug import APIWrapper
from dmplib.utils.errors import UserError
from design_assets.services import design_assets_service

logger = logging.getLogger(__name__)
api = APIWrapper(__name__)


@api.admin_route.get('/export_dashboard_as_asset')
def export_dashboard_as_asset(response, **kwargs):
    """
    导出报告为设计资产
    id: dashboard_id
    """
    folder_id = kwargs.get('folder_id', '')
    _type = kwargs.get('type', '')
    if not folder_id:
        raise UserError(message='缺少文件夹id!')
    design_assets_service.export_dashboard_as_asset(response, folder_id=folder_id, download_type=_type)


@api.admin_route.get('/list')
def assets_list(**kwargs):
    """
    设计资产list接口
    """
    data = design_assets_service.get_assets_list(kwargs)
    return True, 'ok', data


@api.admin_route.get('/get_search_values')
def get_search_values():
    """
    获取可以用来过滤的条件值
    return：
    {
        "result": true,
        "msg": "ok",
        "data": [
            {
                "apply_scene": [
                    "产业开发",
                    "资产管理"
                ],
                "design_style": [],
                "color": []
            }
        ]
    }
    """
    data = design_assets_service.get_search_values()
    return True, 'ok', data


@api.admin_route.post('/install')
def asset_install(**kwargs):
    """
    导出报告为设计资产的模板资产
    id: dashboard_id
    """
    asset_id = kwargs.get('asset_id', '')
    apply_dashboard_id = kwargs.get('apply_dashboard_id', '')
    result = design_assets_service.DesignAssetTemplateInstaller(
        asset_id=asset_id,
        apply_dashboard_id=apply_dashboard_id
    ).install()
    return True, 'ok', result


@api.admin_route.post('/install_status')
def install_status(**kwargs):
    """
    数据集导入状态查询
    """
    import_dataset_ids = kwargs.get('import_dataset_ids', [])
    if not import_dataset_ids:
        raise UserError(message='没有数据集id!')
    result = design_assets_service.get_import_dataset_status(import_dataset_ids)
    return True, 'ok', result


@api.admin_route.post('/delete_local_cache')
def delete_local_cache(**kwargs):
    """
    删除所有的本地的缓存
    """
    result = design_assets_service.DesignAssetPreload.delete_all_local_cache()
    return True, 'ok', result
