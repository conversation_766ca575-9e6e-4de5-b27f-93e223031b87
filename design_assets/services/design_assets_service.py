#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import base64
import datetime
import os
import json
import shutil
# pylint:disable=R0201,W0123
import tempfile
import traceback
from hashlib import md5
from typing import List, Dict
from urllib.parse import unquote

import zipfile
from loguru import logger

from dmplib.hug import g
from base import repository
from components.oss import OSSFileProxy
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from design_assets.repositories import design_assets_repository
from dmplib.utils.strings import seq_id
from design_assets.constants import ASSET_LOCAL_PATH, DATASET_STORE_FOLDER_ID, DATASET_STORE_FOLDER_NAME
from components.utils import (
    debugger_and_logger,
    hash_k,
    remove_temporary_files,
    set_temporary_log,
    get_temporary_log_url,
    spawn_background_task
)
from design_assets.models import AssetConfigModel

local_debug_logger = debugger_and_logger(name=__name__, prefix='[设计资产]')

SUPPORTED_TYPES = ['模板', '素材', '设计规范', '规范', '组合图', '地图']


def export_template(dashboard_id, asset_type, need_export_dataset=True, extra=None):
    import app_celery
    from exports.repositories import export_repository
    from dashboard_chart.services import metadata_service

    patch_chart_theme_config(asset_type, dashboard_id)

    # 1. 模板获取元数据
    local_debug_logger(f'开始导出模板：{dashboard_id}')
    _, meta = metadata_service.get_screens_preview_metadata_v2(dashboard_id=dashboard_id) or {}  # type: dict
    meta.pop('installed_component', None)
    dashboard_layout = meta.get('dashboard', {}).get('layout', {})
    dashboard_name = meta.get('dashboard', {}).get('name', '')
    dashboard_cover = meta.get('dashboard', {}).get('cover') or ''
    local_debug_logger(f'获取元数据成功')

    # 2. 获取数据集的导出包
    excel_zip_name = ''
    has_dataset = False
    meta_json_file = tempfile.NamedTemporaryFile(suffix='.json').name
    config_json_file = tempfile.NamedTemporaryFile(suffix='.json').name
    base64_file = tempfile.NamedTemporaryFile(suffix='.txt').name
    sql_text_file = tempfile.NamedTemporaryFile(suffix='.txt').name
    dataset_file = tempfile.NamedTemporaryFile(suffix='.zip').name
    assets_zip = tempfile.NamedTemporaryFile(suffix='.zip').name
    dataset_ids = design_assets_repository.get_template_relation_dataset_ids(dashboard_id)
    if not need_export_dataset:
        dataset_ids = []
    local_debug_logger({'相关的模板数据集id': dataset_ids})

    if need_export_dataset and dataset_ids:
        # 2.1 走excel数据集导出流程
        export_id = seq_id()
        export_repository.insert_export(
            {
                'id': export_id,
                'dataset_ids': ','.join(dataset_ids),
                'title': f'模板({dashboard_name})数据集导出',
                'description': f'模板({dashboard_name})数据集导出(设计资产)',
                'source_user_id': g.userid,
                'is_export_excel_data': 1,
            }
        )
        try:
            kwargs = {'project_code': g.code, 'export_id': export_id, 'account': g.account, 'userid': g.userid}
            # 同步导出
            app_celery.export_dashboard(**kwargs)
            export_data = export_repository.get_export_by_id(export_id)
            excel_zip_url = export_data.get('url', '')
            raw_data = OSSFileProxy().get_object(excel_zip_url, is_url=True)
            excel_zip_name = os.path.basename(unquote(excel_zip_url))
            write_to_tmp_file(dataset_file, content=raw_data.resp.response.content)
            has_dataset = True
        except Exception as e:
            local_debug_logger(f'导出失败: {str(e)}')
            logger.error(f'导出失败： {traceback.format_exc()}')
            export_repository.delete_exports(export_id, False)
            raise UserError(message='导出数据集zip失败')
    else:
        local_debug_logger(f'没有数据集！')

    # 3. 打包成zip下载
    meta_file_name = f'template-{dashboard_id}.json'
    config_json = AssetConfigModel(
        **{
            'asset': meta_file_name,
            'name': dashboard_name,
            'dataset_zip': excel_zip_name,
            'dataset_ids': dataset_ids,
            'asset_type': asset_type,
            'layout': dashboard_layout
        }
    ).get_dict()
    write_to_tmp_file(meta_json_file, content=json.dumps(meta, ensure_ascii=False))
    write_to_tmp_file(config_json_file, content=json.dumps(config_json, ensure_ascii=False, indent=2))

    # 压缩整个模板包
    with zipfile.ZipFile(assets_zip, 'w', compression=zipfile.ZIP_DEFLATED) as zf:
        zf.write(config_json_file, arcname='config.json')
        zf.write(meta_json_file, arcname=meta_file_name)
        if has_dataset:
            zf.write(dataset_file, arcname=excel_zip_name)

    # # # 4. 上传oss返回
    # file_name = f'assets-{dashboard_name}-{dashboard_id}.zip'
    # meta_upload_data = upload_asset_oss(file_path=assets_zip, file_name=file_name)

    # # 5. 上传base64的的数据
    with open(assets_zip, 'rb') as f:
        base64_content = base64.b64encode(f.read())
    #     write_to_tmp_file(base64_file, content=base64_content)
    # base64_file_name = file_name.replace('.zip', '-base64.txt')
    # meta_base64_upload_data = upload_asset_oss(file_path=base64_file, file_name=base64_file_name)
    #
    # 6.SQL生成
    sql_data = {
        'dashboard_id': dashboard_id, 'name': dashboard_name,
        'logo': dashboard_cover,
        'preview_url': f'https://dmp.mypaas.com.cn/dataview/share/{dashboard_id}?code=ued',
        'asset_type': asset_type, 'raw_data': base64_content.decode(),
    }
    extra = extra or {}
    sql_data.update(extra)
    # sql_text = build_asset_table_sql(sql_data)
    # write_to_tmp_file(sql_text_file, content=sql_text)
    # sql_text_file_name = file_name.replace('.zip', '-sql-text.txt')
    # sql_upload_data = upload_asset_oss(file_path=sql_text_file, file_name=sql_text_file_name)

    # 7. 移除临时文件
    remove_temporary_files(*[meta_json_file, dataset_file, assets_zip, config_json_file, base64_file, sql_text_file])

    return json.dumps(sql_data, ensure_ascii=False)

    # return {
    #     f'《{dashboard_name}》压缩包下载': meta_upload_data.get('url', ''),
    #     f'《{dashboard_name}》压缩包base64下载': base64_content.decode(),
    #     f'《{dashboard_name}》sql下载': sql_text,
    # }


def patch_chart_theme_config(asset_type, dashboard_id):
    import re
    if asset_type in ['模板', '设计规范']:
        dashboard = repository.get_one("dashboard", {'id': dashboard_id}) or {}
        if dashboard:
            theme = dashboard.get('theme') or ''
            new_theme = re.sub(r'"themeKey":"\w+"', '"themeKey":"customColor"', theme)
            print(new_theme)
            repository.update('dashboard', {'theme': new_theme}, conditions={'id': dashboard.get('id')})


def export_material(dashboard):
    """素材的导出"""
    dashboard_id = dashboard.get('id', '') or ''
    dashboard_parent_id = dashboard.get('parent_id', '') or ''
    dashboard_name = dashboard.get('name', '') or ''
    dashboard_cover = dashboard.get('cover', '') or ''
    parent_folder_name = repository.get_one('dashboard', conditions={'id': dashboard_parent_id}, fields=['name']) or {}
    asset_type = '素材'
    extra = {
        'dashboard_id': dashboard_id,
        'name': dashboard_name,
        'logo': dashboard_cover,
        'preview_url': '',
        'material_type': parent_folder_name.get('name', ''),  # 分类名字取父级的文件夹名字
        'asset_type': asset_type
    }
    sql_text = export_template(dashboard_id, asset_type, need_export_dataset=False, extra=extra)
    return sql_text


# def build_asset_table_sql(data):
#     """生成资产表的更新SQL"""
#     fields = list(data.keys())
#     values = [data[k] for k in fields]
#     values = ['%r' % v for v in values]
#     sql = 'REPLACE INTO `{table}`({cols}) VALUES ({values});'.format(
#         table='design_assets', cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
#     )
#     return sql


def write_to_tmp_file(tmp_path, content):
    """数据写到临时文件"""
    if not isinstance(content, bytes):
        content = bytes(content, encoding='utf-8')
    with open(tmp_path, 'w+b') as temp:
        temp.write(content)


def upload_asset_oss(file_path, file_name):
    """
    上传资产文件到oss
    """
    from upload.services.upload_service import upload_file
    upload_location = f'design-assets/{file_name}'
    with open(file_path, 'rb') as f:
        upload_data = upload_file(**{
            'file': f,
            'file_name': file_name,
            'upload_location': upload_location
        })
        return upload_data


@spawn_background_task
def _export_dashboard_as_asset(folder_id, download_type, uuid):
    """
    导出报告为设计资产的模板资产
    """
    result = []
    try:
        if download_type in ['模板', '设计规范', '组合图', '地图']:
            dashboards = design_assets_repository.get_folder_dashboard(folder_id)
            if not dashboards:
                raise UserError(message=f'{download_type}文件下的报告为空！')
            for dashboard in dashboards:
                dashboard_id = dashboard.get('id', '')
                sql_text = export_template(dashboard_id, download_type)
                result.append(sql_text)
        elif download_type in ['素材']:
            dashboards = design_assets_repository.get_second_folder_dashboard(folder_id)
            if not dashboards:
                raise UserError(message=f'{download_type}文件下的报告为空！')
            for dashboard in dashboards:
                sql_text = export_material(dashboard)
                result.append(sql_text)

        set_temporary_log(uuid=uuid, data='\n'.join(result))
    except:
        set_temporary_log(uuid=uuid, data=traceback.format_exc())
    return result


def export_dashboard_as_asset(response, folder_id, download_type):
    if download_type not in SUPPORTED_TYPES:
        raise UserError(message=f'不支持的导出类型：{download_type}')

    uuid = seq_id()
    set_temporary_log(uuid=uuid, data='正在导出中...')
    _export_dashboard_as_asset(folder_id, download_type, uuid)
    tmp_log_url = get_temporary_log_url(uuid, raw=1)
    tmp_html = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>设计资产模板/素材信息下载页面</title>
    </head>
    <body>
        <h1>{download_type} 下载链接</h1>
        <ul>
           <li>导出的文件夹id：{folder_id} </li>
           <li>导出时间： {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} </li>
           <li> <a href="{tmp_log_url}" >导出信息查询地址</a> </li>
        </ul>
    </body>
    </html>
    """
    response.body = tmp_html
    response.set_header('Content-Type', 'text/html; charset=utf-8')


def get_assets_list(kwargs):
    datas = design_assets_repository.get_assets_list_data(kwargs)
    for data in datas:
        if data.get('asset_type') == '模板':
            data['layout'] = load_asset_layout(data.get('id', ''))
        else:
            data['layout'] = {}
    return datas


def load_asset_layout(asset_id):
    """
    获取模板样式
    """
    try:
        preloader = DesignAssetPreload(asset_id)
        preloader.load()
        config_json = preloader.load_asset_config()
        return config_json.get('layout', {})
    except UserError as ue:
        logger.error(f'获取layout失败: {str(ue.message)}')
        return {}
    except Exception as e:
        logger.error(f'获取layout失败: {str(e)}')
        return {}


def get_import_dataset_status(ids):
    """
    获取数据集是否导入状态
    """
    if not ids:
        return {}
    else:
        datas = design_assets_repository.get_dataset_status(ids)
        return {data.get('id'): data.get('run_status') == '已成功' for data in datas}


def get_search_values():
    datas = design_assets_repository.get_search_values()
    # 格式化返回
    for data in datas:
        for key, val in data.items():
            condition_str = data[key]
            data[key] = condition_str.split(',') if condition_str else []
    return datas


def load_asset_json(path):
    try:
        with open(path, 'r') as f:
            return json.loads(f.read())
    except Exception as e:
        raise UserError(message=f'解析资产json出错，文件：{os.path.basename(path)}，错误：{str(e)}')


# 设计资产预加载到本地
class DesignAssetPreload:
    def __init__(self, asset_id):
        self.asset_id = asset_id
        self.record = {}
        self.local_path = os.path.join(ASSET_LOCAL_PATH, str(self.asset_id))
        self.local_config_path = os.path.join(self.local_path, 'config.json')

    @staticmethod
    def delete_all_local_cache():
        try:
            shutil.rmtree(ASSET_LOCAL_PATH)
            # local_debug_logger(f'删除本地缓存成功: {ASSET_LOCAL_PATH}')
        except:
            pass

    def _collect_record_data(self):
        record = repository.get_one(
            'design_assets', conditions={'id': self.asset_id}, from_config_db=True
        ) or {}
        if not record:
            raise UserError(message=f'模板数据<{self.asset_id}>不存在！')
        self.record = record

    def _template_init_local_file(self):
        # 模板存到本地
        self._init_local_asset_folder()
        asset_zip = tempfile.NamedTemporaryFile(suffix='.zip').name
        try:
            raw_data = self.record.get('raw_data', '')
            raw = base64.b64decode(raw_data.strip())
            with open(asset_zip, 'w+b') as temp:
                temp.write(raw)
            with zipfile.ZipFile(asset_zip, 'r') as zip_ref:
                zip_ref.extractall(self.local_path)
        except Exception as e:
            msg = f'模板缓存到本地错误： {self.asset_id}, 检查数据是否正确, 原因：{str(e)}'
            logger.error(msg)
            raise UserError(message=msg)
        finally:
            try:
                os.unlink(self.local_path)
            except:
                pass

    def _init_local_asset_folder(self):
        # 1. 删除本地目录
        try:
            shutil.rmtree(self.local_path)
        except:
            pass

        # 2.重新创建本地目录
        try:
            os.makedirs(self.local_path)
        except:
            pass

        if not os.path.exists(self.local_path):
            raise UserError(message='本地设计资产文件创建失败！')

    def _init_local_file(self):
        asset_type = self.record.get('asset_type')
        if asset_type in SUPPORTED_TYPES:
            self._template_init_local_file()
        else:
            raise UserError(message=f'资产定义的未知类型下载到本地：{asset_type}')

    # def _material_init_local_file(self):
    #     try:
    #         raw_data = self.record.get('raw_data', '')
    #         material = base64.b64decode(raw_data.encode()).decode()
    #         material = json.loads(material)
    #         model = AssetConfigModel(
    #             asset=material,
    #             name=self.record.get('name', ''),
    #             asset_type=self.record.get('asset_type'),
    #         )
    #         self._init_local_asset_folder()
    #         with open(self.local_config_path, 'w') as f:
    #             f.write(json.dumps(model.get_dict(), ensure_ascii=False, indent=2))
    #     except Exception as e:
    #         logger.error(f'缓存素材<{self.record.get("name")}>失败: {traceback.format_exc()}')
    #         raise UserError(message=f'缓存素材<{self.record.get("name")}>失败: {str(e)}')

    def _config_is_existed(self):
        return os.path.exists(self.local_config_path)

    def load(self):
        if self._config_is_existed():
            return
        else:
            local_debug_logger(f'开始模板资源<{self.asset_id}>缓存到本地')
            self._collect_record_data()
            self._init_local_file()
            local_debug_logger(f'模板资源缓存<{self.asset_id}>到本地成功')

    def load_asset_config(self):
        return load_asset_json(self.local_config_path)


class DesignAssetTemplateInstaller:
    def __init__(self, asset_id, apply_dashboard_id):
        self.asset_id = asset_id
        self.apply_dashboard_id = apply_dashboard_id
        self.apply_dashboard = {}
        self.preloader = DesignAssetPreload(self.asset_id)
        self.preloader.load()
        self.local_path = self.preloader.local_path
        self.config = self.preloader.load_asset_config()
        self.result = {}
        self.record = {}
        self.__args_check()

    def __args_check(self):
        if not self.asset_id:
            raise UserError(message='缺少模板资产id！')
        if not self.apply_dashboard_id:
            raise UserError(message='缺少应用报告id！')

    # def __loads(self, path):
    #     return load_asset_json(path)

    def _record_check(self):
        record = repository.get_one('design_assets', conditions={'id': self.asset_id}, from_config_db=True) or {}
        if not record:
            raise UserError(message='模板id不存在！')
        self.record = record
        # if  record.get('asset_type') != '模板':
        #     raise UserError(message='展示不支持模板以外的安装！')

    # def _local_asset_file_check(self):
    #     if not os.path.exists(self.local_config_path):
    #         raise UserError(message=f'设计资产本地文件未找到!')
    #     self.config = self.__loads(self.local_config_path)

    def _asset_install(self):
        asset_type = self.config.get('asset_type')
        if asset_type in SUPPORTED_TYPES:
            self._template_asset_install()
        # elif asset_type == '素材':
        #     self._material_asset_install()
        else:
            raise UserError(message=f'资产定义的类型不支持安装：{asset_type}')

    def _material_asset_install(self):
        self.result['asset'] = self.config.get('asset') or {}

    def __replace_dashboard_meta(self, pre_meta):
        """
        替换报告其他信息
        """
        apply_dashboard = repository.get_one('dashboard', conditions={'id': self.apply_dashboard_id}) or {}
        if not apply_dashboard:
            raise UserError(message=f'应用报告不存在: {self.apply_dashboard_id}')
        self.apply_dashboard = apply_dashboard
        pre_dashboard_info = pre_meta.get('dashboard', {})
        pre_first_report = pre_meta.get('first_report', {})
        # 要处理的报告信息字段
        keys = ['name', 'level_code', 'biz_code', 'parent_id']
        for key in keys:
            if key in apply_dashboard:
                pre_dashboard_info[key] = apply_dashboard[key]
                pre_first_report[key] = apply_dashboard[key]
        return pre_meta

    def __template_metadata_copy(self, pre_meta):
        from dashboard_chart.services.dashboard_service import MetadataSubject, get_replace_map

        new_dashboard_id = self.apply_dashboard_id
        # 1. 处理报告的其他信息：
        pre_meta = self.__replace_dashboard_meta(pre_meta)
        # 2. 处理id
        #### id的处理 ####
        meta_sub = MetadataSubject(metadata=pre_meta, edit_source="copy")
        pre_dashboard_info = meta_sub.metadata_storage().metadata_dashboard()
        dashboard_id = pre_dashboard_info.get('id', '')
        if not dashboard_id:
            raise UserError(message='模板元数据没有id')
        models, errors = meta_sub.do_edit()
        replace_map = {dashboard_id: new_dashboard_id}
        global_params_redirect_map = {}
        get_replace_map(models, replace_map, new_dashboard_id, global_params_redirect_map)
        meta_json = json.dumps(pre_meta)
        for old_str, new_str in replace_map.items():
            meta_json = meta_json.replace(old_str, new_str)
        #### id的处理 ####
        return json.loads(meta_json)

    def __get_need_import_datasets(self, ids):
        if not ids:
            return []
        existed_ids = repository.get_list('dataset', conditions={'id': ids}, fields=['id']) or []
        existed_ids = [existed_id.get('id') for existed_id in existed_ids if existed_id.get('id')]
        return [_id for _id in ids if _id not in existed_ids]

    def _template_asset_install(self):
        """
        安装模板设计资产
        """
        meta_path = os.path.join(self.local_path, self.config.get('asset', ''))
        dataset_zip_path = os.path.join(self.local_path, self.config.get('dataset_zip', ''))
        local_debug_logger(f'开始模板资源安装，元数据文件：{meta_path}, 数据集文件：{dataset_zip_path}')
        if not os.path.exists(meta_path):
            raise UserError(message=f'模板元数据文件资源有缺失！文件：{os.path.basename(meta_path)}')
        if not os.path.exists(dataset_zip_path):
            raise UserError(message=f'模板数据集文件资源有缺失！文件：{os.path.basename(dataset_zip_path)}')

        meta = load_asset_json(meta_path)
        dataset_ids = self.config.get('dataset_ids') or []

        # 1. 处理元数据中id替换
        renew_meta = self.__template_metadata_copy(pre_meta=meta)
        metadata = self.__metadata_patch_deal(renew_meta)
        self.result['asset'] = metadata
        local_debug_logger(f'完成元数据的id处理替换')

        # 2.1. 处理数据集的导入
        need_import_dataset_ids = self.__get_need_import_datasets(dataset_ids)
        self.result['import_dataset_ids'] = need_import_dataset_ids
        # 2.2. 导入数据集
        asset_name = self.record.get('name', '')
        AssetDatasetImporter(need_import_dataset_ids, dataset_zip_path, asset_name).do_import()

    def __metadata_patch_deal(self, metadata):
        """
        处理元数据中的一些其他信息
        """
        # 1. 大屏类型
        application_type = self.apply_dashboard.get('application_type') or 0
        dashboard = metadata.get('dashboard') or {}
        first_report = metadata.get('first_report') or {}
        dashboard['application_type'] = application_type
        first_report['application_type'] = application_type

        # 资产类型
        charts = first_report.get('charts') or []
        for chart in charts:
            chart['asset_id'] = self.asset_id

        return metadata

    def install(self):

        # 1. 检查表记录
        local_debug_logger(f'开始检查表记录是否存在：{self.asset_id}')
        self._record_check()
        # # 2. 检查本地资源文件
        # local_debug_logger('开始检查本地资源环境')
        # self._local_asset_file_check()
        # 3. 资源安装
        local_debug_logger('开始资源安装')
        self._asset_install()
        local_debug_logger('资源安装完成')

        return self.result


class AssetDatasetImporter:
    def __init__(self, import_dataset_ids, dataset_zip_path, asset_name):
        self.import_dataset_ids = import_dataset_ids
        self.dataset_zip_path = dataset_zip_path
        self.asset_name = asset_name  # 属于哪个报告

    def _create_dataset_folder(self, folder_id, folder_name, parent_id):
        folder = repository.get_one('dataset', conditions={'id': folder_id})
        if folder:
            local_debug_logger(f'<{folder_name}> 已经存在不需要再次创建')
        else:
            # 添加一个数据集文件夹
            from dataset.services.dataset_define_service import add_dataset, DatasetModel
            local_debug_logger(f'开始创建数据集文件夹 <{folder_name}>')
            kwargs = {
                'id': folder_id,
                'name': folder_name,
                'parent_id': parent_id,
                'type': 'FOLDER',
            }
            model = DatasetModel(**kwargs)
            add_dataset(model)

    def __load_datasets_from_zip(self):
        json_file_name = os.path.basename(self.dataset_zip_path).replace('.zip', '.json')
        try:
            with zipfile.ZipFile(self.dataset_zip_path, 'r') as zip_ref:
                with zip_ref.open(json_file_name) as f:
                    return json.loads(f.read()).get('datasets') or {}
        except Exception as e:
            raise UserError(message=f'读取数据集文件失败！原因：{str(e)}')

    def __upload_dataset_zip_file(self, asset_dataset_zip):
        from upload.services.upload_service import upload_file
        file_name = f'assets-dataset/{os.path.basename(asset_dataset_zip)}'
        upload_location = f'design-assets/{file_name}'

        key = md5(upload_location.encode()).hexdigest()
        cache = RedisCache(key_prefix='asset-dataset-cache-')
        cache_url = cache.get(key)
        if cache_url:
            return cache_url.decode() if isinstance(cache_url, bytes) else cache_url
        else:
            with open(asset_dataset_zip, 'rb') as f:
                meta_upload_data = upload_file(**{
                    'file': f,
                    'file_name': file_name,
                    'upload_location': upload_location
                })
                cache_url = meta_upload_data.get('url', '')
                cache.set(key, cache_url, 3 * 60 * 60)
                return cache_url

    def _import_datasets(self, parent_id):
        from imports.services.dashboard_import_data_services import dashboard_import_data
        tmpl_all_datasets = self.__load_datasets_from_zip()
        datasets = [dataset.get('dataset') for dataset in tmpl_all_datasets if dataset.get('dataset')]
        need_datasets = [dataset for dataset in datasets if dataset.get('id') in self.import_dataset_ids]
        local_debug_logger(f'从数据集包中加载数据集个数：{len(datasets)}, 需要导入的个数：{len(need_datasets)}')
        dataset_oss_url = self.__upload_dataset_zip_file(self.dataset_zip_path)
        local_debug_logger(f'从数据集包上传的oss地址是：{dataset_oss_url}')
        import_kwargs = {
            "oss_url": dataset_oss_url,
            "pick_data": {
                "dashboards": [],
                "datasets": need_datasets,
                "applications": [],
                "data_source": {}
            },
            "target_dataset_folder_id": parent_id,
            "include_dashboard_folder": False,
            "include_dataset_folder": False,
            "force_update": True
        }
        dashboard_import_data(**import_kwargs)

    def do_import(self):
        dataset_ids = self.import_dataset_ids
        if not dataset_ids:
            local_debug_logger('没有数据集需要导入')
        else:
            local_debug_logger(f'需要导入的数据集id: {dataset_ids}')
            # 创建最外层的主要目录
            self._create_dataset_folder(DATASET_STORE_FOLDER_ID, DATASET_STORE_FOLDER_NAME, parent_id='')
            # 创建模板数据集目录
            md5_hex = hash_k(self.asset_name)
            template_folder_id = '{}-{}-{}-{}-{}'.format(
                md5_hex[:8], md5_hex[8:12], md5_hex[12:16], md5_hex[16:20], md5_hex[20:]
            )
            self._create_dataset_folder(
                template_folder_id, folder_name=self.asset_name,
                parent_id=DATASET_STORE_FOLDER_ID
            )
            local_debug_logger('开始导入数据集流程')
            self._import_datasets(template_folder_id)


# 项目启动自动删除缓存
DesignAssetPreload.delete_all_local_cache()
