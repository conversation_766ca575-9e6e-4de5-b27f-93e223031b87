#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/5/12.
"""
from dmplib.saas.project import get_db


def get_label_col_list_by_label_id(label_id):
    """
    获取标签明细列
    :param str label_id:
    :return:
    """
    sql = 'SELECT lc.indicator_id,i.`name` AS indicator_name ,i.odps_table,i.odps_field,lc.rank,type ' 'FROM label_list_col AS lc ' 'INNER JOIN indicator AS i ON lc.indicator_id=i.id ' 'WHERE lc.label_id=%(label_id)s ' 'ORDER BY lc.rank '
    with get_db() as db:
        return db.query(sql, {'label_id': label_id})
